/* 消息提示样式 */
.alert {
    margin-bottom: 15px;
    padding: 10px;
    border-radius: 4px;
    transition: opacity 0.5s ease-in-out;
}

.alert.fade-out {
    opacity: 0;
}

/* 警告类型消息 */
.alert-warning {
    background-color: #fff3cd;
    color: #856404;
    border: 1px solid #ffeeba;
}

/* 错误类型消息 */
.alert-danger {
    background-color: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

/* 成功类型消息 */
.alert-success {
    background-color: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

/* 信息类型消息 */
.alert-info {
    background-color: #d1ecf1;
    color: #0c5460;
    border: 1px solid #bee5eb;
}

/* 验证错误样式 */
.alert ul {
    margin: 5px 0 0 20px;
    padding: 0;
}

.alert h4 {
    margin-top: 0;
    margin-bottom: 5px;
    font-size: 16px;
    font-weight: bold;
} 