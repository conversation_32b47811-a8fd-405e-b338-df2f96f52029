// 博客分类编辑页面功能
var BlogCateEdit = {
    // 初始化SEO关键词组件
    initSeoKeywords: function() {
        if (frame_obj && typeof frame_obj.seo_keyword_form_submit === 'function') {
            frame_obj.seo_keyword_form_submit();
            this.displayInitialKeywords();
        }
    },

    // 显示初始关键词
    displayInitialKeywords: function() {
        var keywords = $("#Category_SeoKeyword_en").val();
        console.log('初始关键词:', keywords);
        if (keywords && keywords.length > 0) {
            var keywordArray = keywords.split(',');
            var $container = $(".option_selected[data-type='seo_keyword'] .select_list");
            $container.empty();

            keywordArray.forEach(function(keyword) {
                if (keyword && keyword.trim().length > 0) {
                    var $option = $('<span class="btn_attr_choice" data-type="seo_keyword">' +
                        '<b>' + keyword + '</b>' +
                        '<input type="hidden" name="seoKeywords[]" value="' + keyword + '">' +
                        '<i></i>' +
                        '</span>');
                    $container.append($option);
                }
            });

            // 隐藏placeholder
            $(".option_selected[data-type='seo_keyword'] .placeholder").hide();
        }
    },

    // 打开关键词编辑弹窗
    openKeywordEditDialog: function() {
        var $editKeywordForm = $('#edit_keyword_form');
        var $editKeywordList = $editKeywordForm.find('.edit_keyword_list');
        
        // 清空并重新填充关键词列表
        $editKeywordList.empty();
        
        // 获取当前关键词
        var currentKeywords = $("#Category_SeoKeyword_en").val().split(',');
        console.log('打开弹窗时的关键词:', currentKeywords);
        
        // 添加关键词输入框
        currentKeywords.forEach(function(keyword) {
            if (keyword.trim()) {
                var $input = $('<div class="rows clean">' +
                    '<div class="input">' +
                    '<input type="text" class="box_input" value="' + keyword.trim() + '" maxlength="255">' +
                    '</div>' +
                    '</div>');
                $editKeywordList.append($input);
            }
        });
        
        // 添加一个空的输入框用于添加新关键词
        var $newInput = $('<div class="rows clean">' +
            '<div class="input">' +
            '<input type="text" class="box_input" value="" maxlength="255" placeholder="输入新关键词">' +
            '</div>' +
            '</div>');
        $editKeywordList.append($newInput);
    },

    // 处理关键词修改表单提交
    handleKeywordSubmit: function(e) {
        e.preventDefault();
        
        // 获取所有关键词输入框的值
        var keywords = [];
        var uniqueKeywords = {};
        
        $('#edit_keyword_form .edit_keyword_list .box_input').each(function() {
            var keyword = $(this).val().trim();
            console.log('输入框中的关键词:', keyword);
            if (keyword && !uniqueKeywords[keyword.toLowerCase()]) {
                uniqueKeywords[keyword.toLowerCase()] = true;
                keywords.push(keyword);
            }
        });
        
        console.log('修改后的关键词数组:', keywords);
        
        // 更新关键词显示
        var $selectList = $('.option_selected[data-type="seo_keyword"] .select_list');
        $selectList.empty();
        
        keywords.forEach(function(keyword) {
            var $keywordHtml = $('<span class="btn_attr_choice" data-type="seo_keyword">' +
                '<b>' + keyword + '</b>' +
                '<input type="hidden" name="seoKeywords[]" value="' + keyword + '">' +
                '<i></i>' +
                '</span>');
            $selectList.append($keywordHtml);
        });
        
        // 更新隐藏字段
        var keywordsString = keywords.join(',');
        console.log('更新到隐藏字段的值:', keywordsString);
        $('#Category_SeoKeyword_en').val(keywordsString);
        
        // 关闭弹窗
        $('#fixed_right').css('right', '-350px');
        $('#fixed_right_div_mask').remove();
        $('#fixed_right>.global_container').hide();
        
        // 如果有关键词，隐藏placeholder
        if (keywords.length > 0) {
            $('.option_selected[data-type="seo_keyword"] .placeholder').hide();
        } else {
            $('.option_selected[data-type="seo_keyword"] .placeholder').show();
        }
    },

    // 处理取消按钮点击
    handleKeywordCancel: function() {
        $('#fixed_right').css('right', '-350px');
        $('#fixed_right_div_mask').remove();
        $('#fixed_right>.global_container').hide();
    },

    // 处理删除关键词按钮点击
    handleKeywordDelete: function() {
        $(this).closest('.btn_attr_choice').remove();
        
        // 更新隐藏字段
        var keywords = [];
        $('.option_selected[data-type="seo_keyword"] .select_list .btn_attr_choice b').each(function() {
            keywords.push($(this).text());
        });
        
        var keywordsString = keywords.join(',');
        console.log('删除后的关键词:', keywordsString);
        $('#Category_SeoKeyword_en').val(keywordsString);
        
        // 如果没有关键词了，显示placeholder
        if (keywords.length === 0) {
            $('.option_selected[data-type="seo_keyword"] .placeholder').show();
        }
    },

    // 处理表单提交
    handleFormSubmit: function() {
        // 收集所有关键词
        var keywords = [];
        $('.option_selected[data-type="seo_keyword"] .select_list .btn_attr_choice b').each(function() {
            keywords.push($(this).text());
        });
        
        // 更新隐藏字段
        var keywordsString = keywords.join(',');
        console.log('提交前的关键词:', keywordsString);
        $('#Category_SeoKeyword_en').val(keywordsString);
        
        // 提交表单
        console.log('提交表单时的Category.SeoKeyword_en值:', $('#Category_SeoKeyword_en').val());
        $('#edit_form').submit();
    },

    // 初始化字数计数器
    initWordCounter: function() {
        $('[number_limit]').each(function() {
            var $this = $(this);
            var $counter = $this.closest('.number_limit_relative').find('.number_limit span');

            // 初始显示字数
            $counter.text($this.val().length);

            // 输入时更新字数
            $this.on('input', function() {
                $counter.text($this.val().length);
            });

            // 触发一次input事件，确保初始化后正确显示字数
            $this.trigger('input');
        });
    },

    // 初始化所有功能
    init: function() {
        this.initSeoKeywords();
        this.initWordCounter();

        // 绑定事件处理函数
        frame_obj.fixed_right($('#edit_keyword'), '.fixed_edit_keyword', this.openKeywordEditDialog.bind(this));
        $('#edit_keyword_form .btn_submit').on('click', this.handleKeywordSubmit.bind(this));
        $('#edit_keyword_form .btn_cancel').on('click', this.handleKeywordCancel.bind(this));
        $('.option_selected[data-type="seo_keyword"]').on('click', '.btn_attr_choice i', this.handleKeywordDelete.bind(this));
        $('.btn_submit').click(this.handleFormSubmit.bind(this));
    }
};

// 页面加载完成后初始化
$(document).ready(function() {
    BlogCateEdit.init();
}); 