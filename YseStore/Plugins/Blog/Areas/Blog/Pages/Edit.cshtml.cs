using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.Extensions.Logging;
using YseStore.Common.Const;
using YseStore.Common.Helper;
using YseStore.IService.Blog;
using YseStore.Model.Entities.Blog;

namespace Blog.Areas.Blog.Pages
{
    public class EditModel : PageModel
    {
        private readonly IBlogNewService _blogService;
        private readonly IBlogCategoryService _categoryService;
        private readonly IBlogNewContentService _contentService;
        private readonly IBlogNewTagsService _tagService;
        private readonly IBlogSeoService _blogSeoService;
        private readonly ILogger<EditModel> _logger;

        [TempData] public string StatusMessage { get; set; }

        [BindProperty] public BlogNew BlogPost { get; set; }

        [BindProperty] public BlogNewContent BlogContent { get; set; }

        [BindProperty] public string TagString { get; set; }

        [FromQuery(Name = "id")] public int? Id { get; set; }

        public Dictionary<short, string> Categories { get; set; }
        public List<SelectListItem> CategoryOptions { get; set; }
        public List<string> AvailableTags { get; set; }
        public string UrlBase { get; set; }

        public EditModel(
            IBlogNewService blogService,
            IBlogCategoryService categoryService,
            IBlogNewContentService contentService,
            IBlogNewTagsService tagService,
            YseStore.IService.Blog.IBlogSeoService blogSeoService,
            ILogger<EditModel> logger)
        {
            _blogService = blogService;
            _categoryService = categoryService;
            _contentService = contentService;
            _tagService = tagService;
            _blogSeoService = blogSeoService;
            _logger = logger;
        }

        public async Task<IActionResult> OnGetAsync()
        {
            UrlBase = AppSettingsConstVars.WebSiteUrl + "/blog/";
            // 加载分类列表
            await LoadCategories();

            // 加载可用标签
            await LoadTags();

            // 如果是编辑现有博客
            if (Id.HasValue)
            {
                // 获取博客基本信息
                BlogPost = await _blogService.GetBlogById(Id.Value);
                if (BlogPost == null)
                {
                    StatusMessage = "未找到指定的博客文章";
                    return RedirectToPage("/Index", new { area = "Blog" });
                }

                // 获取博客内容
                BlogContent = await _contentService.GetContentByArticleId(Id.Value);
                if (BlogContent == null)
                {
                    BlogContent = new BlogNewContent { AId = Id.Value };
                }

                // 如果有标签，获取标签名称
                if (!string.IsNullOrEmpty(BlogPost.Tag))
                {
                    // 去除首尾的 | 并按 | 分割字符串
                    var tagIdStrings = BlogPost.Tag.Trim('|')
                        .Split(new[] { '|' }, StringSplitOptions.RemoveEmptyEntries);

                    List<int> tagIds = tagIdStrings
                        .Where(idStr => int.TryParse(idStr, out _))
                        .Select(int.Parse)
                        .ToList();

                    if (tagIds.Count > 0)
                    {
                        List<string> tagNames = await _tagService.GetTagNamesByIds(tagIds);
                        TagString = string.Join(",", tagNames);
                    }
                }
            }
            else
            {
                // 新建博客
                BlogPost = new BlogNew
                {
                    AccTime = DateTimeHelper.ConvertToUnixTimestamp(DateTime.Now),
                    IsDraft = true,
                    ModifyAt = DateTimeHelper.ConvertToUnixTimestamp(DateTime.Now),
                    ViewCount = 0,
                    MyOrder = 0
                };

                BlogContent = new BlogNewContent
                {
                    UsedMobile = false
                };
            }

            return Page();
        }

        /// <summary>
        /// 保存草稿
        /// </summary>
        public async Task<IActionResult> OnPostSaveDraftAsync()
        {
            try
            {
                _logger.LogInformation("OnPostSaveDraftAsync 开始执行，Id: {Id}", Id);

                // 根据路由参数判断是新增还是修改
                var isUpdate = Id.HasValue && Id.Value > 0;

                if (BlogPost.AccTime == null)
                {
                    BlogPost.AccTime = DateTimeHelper.ConvertToUnixTimestamp(DateTime.Now);
                }

                BlogPost.MyOrder = 1;
                BlogPost.IsDraft = true;
                BlogPost.IsHot = false;

                // 确保AId被正确设置
                if (isUpdate)
                {
                    BlogPost.AId = Id.Value;
                }
                else
                {
                    BlogPost.AId = 0;
                }

                var result = await SaveBlog();

                // 如果保存成功，重定向到博客首页
                if (result is PageResult)
                {
                    return RedirectToPage("/Index", new { area = "Blog" });
                }

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "OnPostSaveDraftAsync 发生异常: {Message}", ex.Message);
                StatusMessage = $"保存草稿失败: {ex.Message}";

                try
                {
                    await LoadCategories();
                    await LoadTags();
                }
                catch (Exception loadEx)
                {
                    _logger.LogError(loadEx, "加载分类和标签失败: {Message}", loadEx.Message);
                }

                return Page();
            }
        }

        /// <summary>
        /// 保存并发布
        /// </summary>
        public async Task<IActionResult> OnPostPublishAsync()
        {
            try
            {
                // 记录详细的调试信息
                _logger.LogInformation("OnPostPublishAsync 开始执行，Id: {Id}", Id);

                BlogPost.IsHot = false;
                BlogPost.MyOrder = 1;

                // 根据路由参数判断是新增还是修改
                var isUpdate = Id.HasValue && Id.Value > 0;
                _logger.LogInformation("isUpdate: {IsUpdate}", isUpdate);

                // 确保AId被正确设置
                if (isUpdate)
                {
                    BlogPost.AId = Id.Value;
                }
                else
                {
                    BlogPost.AId = 0;
                }

                // 如果是新增操作，需要确保Id保持为null
                if (!isUpdate)
                {
                    Id = null;
                }

                BlogPost.IsDraft = false;
                if (BlogPost.AccTime == null)
                {
                    BlogPost.AccTime = DateTimeHelper.ConvertToUnixTimestamp(DateTime.Now);
                }

                // 为操作添加更明确的状态消息
                if (isUpdate)
                {
                    // 这是修改操作
                    StatusMessage = "正在保存修改...";
                }
                else
                {
                    // 这是新增操作
                    StatusMessage = "正在发布新博客...";
                }

                _logger.LogInformation("准备调用SaveBlog，BlogPost.Title: {Title}", BlogPost?.Title);
                var result = await SaveBlog();
                _logger.LogInformation("SaveBlog 执行完成，result类型: {ResultType}", result?.GetType().Name);

                // 如果保存成功，重定向到博客首页
                if (result is PageResult)
                {
                    return RedirectToPage("/Index", new { area = "Blog" });
                }

                return result;
            }
            catch (Exception ex)
            {
                // 记录详细的异常信息
                _logger.LogError(ex, "OnPostPublishAsync 发生异常: {Message}", ex.Message);

                StatusMessage = $"发布失败: {ex.Message}";

                try
                {
                    await LoadCategories();
                    await LoadTags();
                }
                catch (Exception loadEx)
                {
                    _logger.LogError(loadEx, "加载分类和标签失败: {Message}", loadEx.Message);
                    StatusMessage += $" (加载数据失败: {loadEx.Message})";
                }

                return Page();
            }
        }

        /// <summary>
        /// 定时发布
        /// </summary>
        public async Task<IActionResult> OnPostTimingPublishAsync()
        {
            // 根据路由参数判断是新增还是修改
            var isUpdate = Id.HasValue && Id.Value > 0;

            // 确保AId被正确设置
            if (isUpdate)
            {
                BlogPost.AId = Id.Value;
            }
            else
            {
                BlogPost.AId = 0;
            }

            // 检查ModelState
            if (!ModelState.IsValid)
            {
                // 如果是新增操作，需要确保Id保持为null
                if (!isUpdate)
                {
                    Id = null;
                }

                StatusMessage = "提交的表单数据无效，请检查并重新提交";
                await LoadCategories();
                await LoadTags();
                return Page();
            }

            BlogPost.IsDraft = false;
            return await SaveBlog();
        }

        private async Task<IActionResult> SaveBlog()
        {
            try
            {
                // 检查BlogPost是否为null
                if (BlogPost == null)
                {
                    StatusMessage = "表单数据绑定失败，请重试";
                    await LoadCategories();
                    await LoadTags();
                    return Page();
                }

                // 处理标签
                List<string> tagNames = null;
                if (!string.IsNullOrEmpty(TagString))
                {
                    tagNames = _tagService.ParseTagString(TagString);
                }

                // 根据路由参数判断是新增还是修改
                var isUpdate = Id.HasValue && Id.Value > 0;

                // 确保AId被正确设置
                if (isUpdate)
                {
                    BlogPost.AId = Id.Value;
                }
                else
                {
                    BlogPost.AId = 0;
                }

                await ProcessPageUrl(isUpdate);

                if (!isUpdate)
                {
                    long blogId = await _blogService.AddBlog(BlogPost, BlogContent, tagNames);

                    if (blogId > 0)
                    {
                        // 更新博客PageUrl缓存
                        if (!string.IsNullOrEmpty(BlogPost.PageUrl))
                        {
                            _blogSeoService.UpdateBlogPageUrlCache(BlogPost.PageUrl, (int)blogId);
                        }

                        StatusMessage = "新增博客成功";
                        return RedirectToPage("/Edit", new { id = blogId, area = "Blog" });
                    }
                    else
                    {
                        _logger.LogError("新增博客失败，返回ID为0");
                        StatusMessage = "新增博客失败";
                    }
                }
                else
                {
                    bool success = await _blogService.UpdateBlog(BlogPost, BlogContent, tagNames);

                    if (success)
                    {
                        // 更新博客PageUrl缓存
                        if (!string.IsNullOrEmpty(BlogPost.PageUrl))
                        {
                            _blogSeoService.UpdateBlogPageUrlCache(BlogPost.PageUrl, BlogPost.AId);
                        }

                        StatusMessage = "更新博客成功";
                    }
                    else
                    {
                        _logger.LogError("更新博客失败");
                        StatusMessage = "更新博客失败";
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "SaveBlog 发生异常: {Message}", ex.Message);
                StatusMessage = $"保存失败: {ex.Message}";

                try
                {
                    await LoadCategories();
                    await LoadTags();
                }
                catch (Exception loadEx)
                {
                    _logger.LogError(loadEx, "加载分类和标签时发生异常: {Message}", loadEx.Message);
                }

                return Page();
            }

            await LoadCategories();
            await LoadTags();
            return Page();
        }

        private async Task LoadCategories()
        {
            var categoryList = await _categoryService.Query();
            Categories = categoryList.ToDictionary(c => c.CateId, c => c.Category);

            CategoryOptions = categoryList.Select(c => new SelectListItem
            {
                Value = c.CateId.ToString(),
                Text = c.Category
            }).ToList();
            if (!CategoryOptions.Any())
            {
                // 添加"未分类"选项
                CategoryOptions.Insert(0, new SelectListItem { Value = "", Text = "未分类" });
            }
        }

        private async Task LoadTags()
        {
            var tags = await _tagService.GetAllTags();
            AvailableTags = tags.Select(t => t.Name).ToList();
        }

        /// <summary>
        /// 处理博客PageUrl的唯一性
        /// </summary>
        /// <param name="isUpdate">是否为更新操作</param>
        private async Task ProcessPageUrl(bool isUpdate)
        {
            try
            {
                // 如果PageUrl为空，使用标题生成
                if (string.IsNullOrEmpty(BlogPost.PageUrl) && !string.IsNullOrEmpty(BlogPost.Title))
                {
                    BlogPost.PageUrl =
                        await _blogSeoService.GenerateUniqueBlogPageUrlAsync(BlogPost.Title,
                            isUpdate ? BlogPost.AId : 0);
                }
                // 如果PageUrl不为空，确保其唯一性
                else if (!string.IsNullOrEmpty(BlogPost.PageUrl))
                {
                    BlogPost.PageUrl =
                        await _blogSeoService.GenerateUniqueBlogPageUrlAsync(BlogPost.PageUrl,
                            isUpdate ? BlogPost.AId : 0);
                }
                // 如果标题和PageUrl都为空，生成默认URL
                else
                {
                    BlogPost.PageUrl =
                        await _blogSeoService.GenerateUniqueBlogPageUrlAsync($"blog-{DateTime.Now.Ticks}",
                            isUpdate ? BlogPost.AId : 0);
                }
            }
            catch (Exception ex)
            {
                // 如果生成PageUrl失败，使用时间戳作为备用方案
                BlogPost.PageUrl = $"blog-{DateTime.Now.Ticks}";
                StatusMessage += $" (PageUrl生成警告: {ex.Message})";
            }
        }
    }
}