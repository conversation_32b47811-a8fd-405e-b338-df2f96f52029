@model Blog.Areas.Blog.Pages.BlogCateModel
@{
    var totalCount = Model.Categories?.dataCount ?? 0;
    var totalPages = Model.Categories?.pageCount ?? 0;
}

<div id="blog-table-container">
    <!-- 添加隐藏的防伪令牌字段 -->
    @Html.AntiForgeryToken()
    
    <table border="0" cellpadding="5" cellspacing="0" class="r_con_table">
        <colgroup>
            <col style="width:1%;">
            <col style="width:8%;">
            <col style="width:30%;">
            <col style="width:30%;">
            <col style="width:15%;">
        </colgroup>
        <thead>
        <tr>
            <td width="20" class="myorder"></td>
            <td width="40" class="pos">
                <ul class="table_menu_button global_menu_button">
                    <li>
                        <div class="btn_checkbox "><em class="button"></em><input type="checkbox" name="select_all"
                                                                                  value=""></div>
                    </li>
                    <li class="open">已选择<span></span>个</li>
                    <li>
                        <a class="del" href="#" onclick="submitBatchDelete(); return false;">删除</a>
                    </li>
                </ul>
            </td>
            <td width="50%">分类名称</td>
            <td width="30%">博客数量</td>
            <td width="200" class="operation tar"></td>
        </tr>
        </thead>
        <tbody data-listidx="0">
        @if (Model.Categories != null && Model.Categories.data != null && Model.Categories.data.Any())
        {
            foreach (var category in Model.Categories.data)
            {
                <tr data-id="@category.CateId" style="cursor: pointer;">
                    <td class="myorder move_myorder" data="move_myorder"><i class="icon_myorder"></i></td>
                    <td>
                        <div class="btn_checkbox "><em class="button"></em><input type="checkbox" name="select"
                                                                                  value="@category.CateId"></div>
                    </td>
                    <td>@category.Category</td>
                    <td>@Model.GetBlogCount(category.CateId)</td>
                    <td class="operation tar">
                        <a href="/Blog/CateEdit?id=@category.CateId" class="icon_edit oper_icon button_tips">修改</a>
                        <a href="#" onclick="deleteCategory(@category.CateId); return false;"
                           class="oper_icon icon_del del button_tips" data-id="@category.CateId">删除</a>
                    </td>
                </tr>
            }
        }
        else
        {
            <tr>
                <td colspan="5" class="text-center">暂无数据</td>
            </tr>
        }
        </tbody>
    </table>
</div>

<div class="scroll_sticky">
    <div class="scroll_sticky_content">
        <div></div>
    </div>
</div>
<div id="turn_page" data-current="@Model.PageIndex" data-count="@totalPages">
    <div class="total_page">共 @totalCount 条</div>
</div>

<script>
    // 全局变量存储当前要删除的ID或批量删除的ID数组
    var currentDeleteId = null;
    var batchDeleteIds = [];

    // 删除单个分类
    function deleteCategory(id) {
        // 存储当前要删除的ID
        currentDeleteId = id;
        
        // 显示确认弹窗
        global_obj.win_alert({
            title: "删除确认",
            subtitle: "确定要删除这个分类吗？",
            confirmBtn: "删除",
            cancelBtn: "取消"
        }, null, 'confirm');
        
        return false;
    }

    // 批量删除
    function submitBatchDelete() {
        var selectedCategories = document.querySelectorAll('input[name="select"]:checked');
        if (selectedCategories.length === 0) {
            global_obj.win_alert({
                title: "提示",
                subtitle: "请至少选择一个分类",
                confirmBtn: "确定"
            });
            return;
        }

        // 收集所有选中的ID
        batchDeleteIds = Array.from(selectedCategories).map(function(checkbox) {
            return checkbox.value;
        });
        
        // 显示确认弹窗
        global_obj.win_alert({
            title: "批量删除确认",
            subtitle: "确定要删除选中的 " + batchDeleteIds.length + " 个分类吗？",
            confirmBtn: "删除",
            cancelBtn: "取消"
        }, null, 'confirm');
        
        return false;
    }

    function updateSelectedCount() {
        var count = document.querySelectorAll('input[name=select]:checked').length;
        var countSpan = document.querySelector('.table_menu_button .open span');
        if (countSpan) {
            countSpan.textContent = count;
        }
    }

    // 初始化功能
    document.addEventListener('DOMContentLoaded', function () {
        // 全选/取消全选功能
        var selectAllCheckbox = document.querySelector('input[name=select_all]');
        if (selectAllCheckbox) {
            selectAllCheckbox.addEventListener('change', function () {
                var isChecked = this.checked;
                document.querySelectorAll('input[name=select]').forEach(function (checkbox) {
                    checkbox.checked = isChecked;
                });
                updateSelectedCount();
            });
        }

        // 单选更新计数
        var checkboxes = document.querySelectorAll('input[name=select]');
        checkboxes.forEach(function (checkbox) {
            checkbox.addEventListener('change', function () {
                updateSelectedCount();
            });
        });

        // 确保删除按钮正确绑定点击事件
        var deleteButtons = document.querySelectorAll('.icon_del');
        deleteButtons.forEach(function (button) {
            button.addEventListener('click', function (e) {
                var id = this.getAttribute('data-id');
                if (id) {
                    // 防止事件冒泡
                    e.preventDefault();
                    e.stopPropagation();
                    deleteCategory(id);
                }
            });
        });

        updateSelectedCount();

        // 监听确认按钮点击事件
        document.addEventListener('click', function(e) {
            // 检查点击的是否是确认按钮
            if (e.target && (e.target.classList.contains('btn_sure') || 
                            e.target.classList.contains('confirm') || 
                            e.target.classList.contains('btn_warn'))) {
                
                // 获取当前的搜索关键词，用于保持搜索状态
                var keyword = "@Model.Keyword";
                var pageIndex = "@Model.PageIndex";
                
                // 构建URL参数
                var params = "PageIndex=" + pageIndex;
                if (keyword) {
                    params += "&Keyword=" + encodeURIComponent(keyword);
                }
                
                // 单个删除操作
                if (currentDeleteId) {
                    window.location.href = '/Blog/Cate?handler=DeleteCategory&id=' + currentDeleteId + '&' + params;
                    currentDeleteId = null;
                }
                
                // 批量删除操作
                if (batchDeleteIds.length > 0) {
                    window.location.href = '/Blog/Cate?handler=DeleteBatch&ids=' + batchDeleteIds.join(',') + '&' + params;
                    batchDeleteIds = [];
                }
            }
        });

        // 初始化拖拽排序功能
        if (typeof frame_obj !== 'undefined' && typeof frame_obj.dragsort === 'function') {
            frame_obj.dragsort(
                $('#blog .r_con_table tbody'),  // 容器
                '',  // 拖动触发区域，可以为空
                'tr',  // 拖动的项目
                'a, td[data!=move_myorder]',  // 排除的元素
                '<tr class="placeHolder"></tr>',  // 占位符
                '',  // 回调参数（此处可能不需要）
                function() {  // 完成拖拽后的回调函数
                    // 在这里获取排序后的顺序并提交到后端
                    let categoryIds = [];
                    $('#blog .r_con_table tbody tr').each(function(index) {
                        let categoryId = $(this).data('id');
                        if(categoryId) {
                            categoryIds.push({
                                categoryId: categoryId,
                                targetOrder: index
                            });
                        }
                    });
                    
                    // 发送数据到后端
                    $.ajax({
                        url: '/Blog/Cate?handler=UpdateOrder',
                        type: 'POST',
                        data: { orderData: JSON.stringify(categoryIds) },
                        headers: {
                            "RequestVerificationToken": $('input:hidden[name="__RequestVerificationToken"]').val()
                        },
                        success: function(result) {
                            if(result.success) {
                                // 刷新整个页面
                                window.location.reload();
                            }
                        },
                        error: function() {
                        
                        }
                    });
                }
            );
        }
    });
    
    // 封装表格功能初始化函数，用于刷新后重新绑定事件
    function initializeTableFunctions() {
        // 全选/取消全选功能
        var selectAllCheckbox = document.querySelector('input[name=select_all]');
        if (selectAllCheckbox) {
            selectAllCheckbox.addEventListener('change', function () {
                var isChecked = this.checked;
                document.querySelectorAll('input[name=select]').forEach(function (checkbox) {
                    checkbox.checked = isChecked;
                });
                updateSelectedCount();
            });
        }

        // 单选更新计数
        var checkboxes = document.querySelectorAll('input[name=select]');
        checkboxes.forEach(function (checkbox) {
            checkbox.addEventListener('change', function () {
                updateSelectedCount();
            });
        });

        // 确保删除按钮正确绑定点击事件
        var deleteButtons = document.querySelectorAll('.icon_del');
        deleteButtons.forEach(function (button) {
            button.addEventListener('click', function (e) {
                var id = this.getAttribute('data-id');
                if (id) {
                    // 防止事件冒泡
                    e.preventDefault();
                    e.stopPropagation();
                    deleteCategory(id);
                }
            });
        });

        updateSelectedCount();
        
        // 重新初始化拖拽排序功能
        if (typeof frame_obj !== 'undefined' && typeof frame_obj.dragsort === 'function') {
            frame_obj.dragsort(
                $('#blog .r_con_table tbody'),  // 容器
                '',  // 拖动触发区域，可以为空
                'tr',  // 拖动的项目
                'a, td[data!=move_myorder]',  // 排除的元素
                '<tr class="placeHolder"></tr>',  // 占位符
                '',  // 回调参数（此处可能不需要）
                function() {  // 完成拖拽后的回调函数
                    // 在这里获取排序后的顺序并提交到后端
                    let categoryIds = [];
                    $('#blog .r_con_table tbody tr').each(function(index) {
                        let categoryId = $(this).data('id');
                        if(categoryId) {
                            categoryIds.push({
                                categoryId: categoryId,
                                targetOrder: index
                            });
                        }
                    });
                    
                    // 发送数据到后端
                    $.ajax({
                        url: '/Blog/Cate?handler=UpdateOrder',
                        type: 'POST',
                        data: { orderData: JSON.stringify(categoryIds) },
                        headers: {
                            "RequestVerificationToken": $('input:hidden[name="__RequestVerificationToken"]').val()
                        },
                        success: function(result) {
                            if(result.success) {
                                // 刷新整个页面
                                window.location.reload();
                            }
                        },
                        error: function() {
                     
                        }
                    });
                }
            );
        }
    }
    
</script>