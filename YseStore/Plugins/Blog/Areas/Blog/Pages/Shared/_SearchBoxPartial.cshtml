@model YseStore.Plugins.Blog.Models.SearchBoxModel

<div class="search_box @Model.ExtraClasses">
    <form id="@Model.FormId" 
          hx-get="@Model.Endpoint" 
          hx-target="@Model.Target" 
          hx-trigger="@Model.Trigger"
          hx-indicator="#@(Model.FormId)-indicator">
        <div class="k_input">
            <input type="text" class="form_input" name="@Model.KeywordName" value="@Model.KeywordValue" size="15" autocomplete="off" placeholder="@Model.Placeholder">
            <button type="submit" class="search_btn iconfont">&#xe600;</button>
        </div>
        <span id="@(Model.FormId)-indicator" class="htmx-indicator" style="position: absolute; right: 8px; top: 7px;">
            <i class="fas fa-spinner fa-spin"></i>
        </span>
        <div class="clear"></div>
    </form>
</div> 