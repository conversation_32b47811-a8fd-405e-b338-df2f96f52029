@model Microsoft.AspNetCore.Mvc.ModelBinding.ModelStateDictionary

@if (!Model.IsValid)
{
    <div class="alert alert-danger" style="margin-bottom: 15px; padding: 10px; background-color: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; border-radius: 4px;">
        <h4>模型验证错误：</h4>
        <ul>
            @foreach (var key in Model.Keys)
            {
                var state = Model[key];
                if (state.Errors.Count > 0)
                {
                    <li>字段 <strong>@key</strong>: @string.Join(", ", state.Errors.Select(e => e.ErrorMessage))</li>
                }
            }
        </ul>
    </div>
} 