using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Mvc.ApplicationModels;
using Microsoft.Extensions.FileProviders;
using PluginsBase;
using YseStore.IService;
using YseStore.Service;
//using WebApplicationPlugins;


[assembly: WebApplicationPlugin(typeof(Plugin))]

public sealed class Plugin : WebApplicationPlugin
{
    public override void ConfigureWebApplicationBuilder(WebApplicationBuilder builder, PluginSettings settings)
    {
        // 注册博客分类服务
        // builder.Services.AddScoped<IBlogCategoryService, BlogCategoryService>();

        builder.Services.AddRazorPages()
              //.AddRazorPagesOptions(options =>
              //{
              //    options.Conventions.AddPageRoute("/Index", "/blog/{*path}"); // ҳӳ䵽 /blog
              //})
            .AddApplicationPart(typeof(Plugin).Assembly);

        // The module file provider is a composite of the root and this file provider
        var moduleFileProvider = new CompositeFileProvider(
            builder.Environment.WebRootFileProvider,
            new PhysicalFileProvider(Path.Combine(settings.ContentRootPath, "wwwroot"))
        );

        // Look at static files in this content root as well
        builder.Services.AddOptions<StaticFileOptions>()
            .PostConfigure(o =>
            {
                o.FileProvider = o.FileProvider is not null ?
                    new CompositeFileProvider(o.FileProvider, moduleFileProvider) :
                    moduleFileProvider;
            });
    }

    public override void ConfigureWebApplication(WebApplication app, PluginSettings settings)
    {
       app.MapFallbackToAreaPage("/Index", "Blog").WithOrder(100);

        //app.Map("/blog", b =>
        //{
        //    b.UseStaticFiles();
        //    b.UseRouting();
        //    b.UseEndpoints(endpoints =>
        //    {
        //        endpoints.MapFallbackToAreaPage("/Index", "Blog");
        //       // endpoints.MapRazorPages();
        //    });
        //});
    }
}
