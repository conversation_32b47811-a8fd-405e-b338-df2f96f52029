namespace YseStoreControls.Pages.Components.Drawer;

public class MxDrawer : MComponent
{
    public Dictionary<string, string> Items { get; set; } = [];


    public MxDrawer()
    {
        Subscribe<OpenDrawer>(OpenDrawerHandler);
        Subscribe<CloseDrawer>(CloseDrawerHandler);
    }


    public async Task OpenDrawerHandler(OpenDrawer data)
    {
        var uuid = Guid.NewGuid().ToString("N");
        Items.Add(uuid, data.Name);
        Client.ExecuteJs($$"""
                window.showdrawer=function()
                {
                    const el = document.getElementById('{{uuid}}');
                    const myObj = new bootstrap.Offcanvas(el, {
                        keyboard: true,  
                        backdrop: 'true',
                        scroll:false
                    });
                    myObj.show();
                    $("body").trigger("resize");
                }
            window.showdrawer();
            $('body').prepend('<div id="fixed_right_div_mask" data-id="{{uuid}}" x-on:click="const targetId = $event.currentTarget.getAttribute(`data-id`);const targetEl = document.getElementById(targetId);if (targetEl) targetEl.remove();"></div>');
            $('#fixed_right_div_mask').show();
            """);
        await Task.CompletedTask;
    }

    public async Task CloseDrawerHandler(CloseDrawer data)
    {
        // Task.Delay(3000).Wait();
        Client.ExecuteJs(
            $$"""
            window.closedrawer=function()
            {
                const dwel = document.getElementById('{{data.Id}}');
                const myObj = bootstrap.Offcanvas.getInstance(dwel);
                myObj.hide();
                $(dwel).hide();
                $('#fixed_right_div_mask').remove();
                setTimeout(() => {
                    dwel.remove();
                }, 2000);
            }
            window.closedrawer();
        """
            );
        //Items.Remove(data.Id);
        //  await Task.CompletedTask;
    }
}
