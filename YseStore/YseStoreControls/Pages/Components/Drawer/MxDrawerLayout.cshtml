@model YseStoreControls.Pages.Components.Drawer.MxDrawerLayout
@{
    var cls = "";
    var style = "";
    var bodystyle = "";
    var ariahidden = "false";
    if (Model.HasSlot("actions"))
    {
        bodystyle = "max-height:calc(100% - 131px);";
    }
    else
    {
        bodystyle = "max-height:calc(100% - 61px);";
    }
    if (Model.IsShow)
    {
        cls = "show";

        ariahidden = "false";
    }

}
<div>

    <div id="@Model.Id" style="@style" aria-hidden="@ariahidden" role="dialog" tabindex="-1">
        <div id="fixed_right" style="right: 0px; width: 400px;">
            <div class="global_container fixed_add_url_box" data-width="400" style="display: block; height: 645px;">
                <div class="top_title"><span>@Model.Slot("title")</span> <a data-id="@Model.Id" href="javascript:;" class="close" x-on:click="const targetId = $event.currentTarget.getAttribute(`data-id`);const targetEl = document.getElementById(targetId);if (targetEl) targetEl.remove();$('#fixed_right_div_mask').remove();"></a></div>
                <div style="@bodystyle">
                    <div>
                        @Model.Slot()
                    </div>
                    @if (Model.HasSlot("actions"))
                    {
                        <div class="rows clean box_button box_submit">
                            <div class="input">
                                @Model.Slot("actions")
                            </div>
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>
</div>