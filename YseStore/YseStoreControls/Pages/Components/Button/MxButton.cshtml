@model YseStoreControls.Pages.Components.Button.MxButton 
@{
    var typeClass1 = Model.Primary ? "btn_add" : "";
    var typeClass2 = Model.Info ? "btn_add" : "";
    var typeClass3 = Model.Plain ? "filter_btn" : "";
    var typeClass4 = Model.RoundPrimary ? "btn_global btn_submit" : "";
    var typeClass5 = Model.RoundInfo ? "btn_global btn_cancel" : "";
    var typeClass6 = Model.Search ? "search_btn" : "";
    
    var btnType = $"{typeClass1}  {typeClass2} {typeClass3} {typeClass4} {typeClass5} {typeClass6}";
 
}

<button on:click="@Model.Click"
        type="@(Model.Submit ? "submit" : "button")"
        key="@Model.Key"
        class="@($"btn {btnType} {Model.Class} [&.m-request>.bx-loader]:block")">
    @Model.Slot()
</button>