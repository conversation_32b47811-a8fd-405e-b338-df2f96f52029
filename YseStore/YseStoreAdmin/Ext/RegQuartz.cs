using GZY.Quartz.MUI.Config;
using GZY.Quartz.MUI.EFContext;
using GZY.Quartz.MUI.Extensions;
using Microsoft.EntityFrameworkCore;
using Mxweb.Configuration;

namespace YseStoreAdmin.Ext
{
    public static class RegQuartz
    {
        public static IServiceCollection RegisterQuartz(this IServiceCollection services,string conn)
        {
            var optionsBuilder = new DbContextOptionsBuilder<QuarzEFContext>();
            optionsBuilder.UseMySql(conn
                , ServerVersion.Create(Version.Parse("8.0.42"), Pomelo.EntityFrameworkCore.MySql.Infrastructure.ServerType.MySql));
            
            services.AddQuartzUI(optionsBuilder.Options, new QuartzMUIOptions() { ShowConsoleLog = false, DefaultApiTimeOut = 10 });
            services.AddQuartzClassJobs();
            return services;
        }
        
    }
}
