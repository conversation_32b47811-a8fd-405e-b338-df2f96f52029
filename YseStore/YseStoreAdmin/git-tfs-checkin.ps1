# 脚本: git-tfs-checkin.ps1
# 用途: 将Git代码提交回TFS

param(
    [Parameter(Mandatory=$false)]
    [string]$CommitMessage = ""
)

Write-Host "=== Starting to check in code to TFS ===" -ForegroundColor Green

# 设置Git仓库路径 - 请将此路径修改为实际Git仓库路径
$gitRepoPath = "D:\Code\TfsCode\YseStore\YseStore"

# 如果没有提供提交消息，则请求用户输入
if ([string]::IsNullOrEmpty($CommitMessage)) {
    $CommitMessage = Read-Host "Please enter commit message"
}

# 保存当前工作目录路径
$currentDir = Get-Location

try {
    # 切换到Git仓库目录
    Write-Host "Changing directory to: $gitRepoPath" -ForegroundColor Cyan
    Set-Location -Path $gitRepoPath
    
    # 解决所有权问题 (可选)
    Write-Host "Configuring Git to accept current repository ownership" -ForegroundColor Cyan
    git config --global --add safe.directory $gitRepoPath
    
    # 执行git tfs checkin命令
    Write-Host "Executing: git tfs checkin -m '$CommitMessage'" -ForegroundColor Cyan
    git tfs checkin -m "$CommitMessage"
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host "Successfully checked in code to TFS" -ForegroundColor Green
    } else {
        Write-Host "Failed to check in code to TFS, error code: $LASTEXITCODE" -ForegroundColor Red
    }
} catch {
    Write-Host "Error occurred: $_" -ForegroundColor Red
} finally {
    # 恢复工作目录
    Set-Location $currentDir
}

Write-Host "=== Operation completed ===" -ForegroundColor Green