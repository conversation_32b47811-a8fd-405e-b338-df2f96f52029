using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using System.ComponentModel.DataAnnotations;
using System.Text.Json.Serialization;
using YseStore.Common.Helper;

namespace YseStoreAdmin.Parameters.Sales
{
    public class CouponRequest
    {
        [Required(ErrorMessage = "优惠券编号不能为空")]
        [BindProperty(Name = "SalesCoupon[CouponNumber]")] // 指定参数名映射
        public string CouponNumber { get; set; }
        /// <summary>
        /// fixed 固定时间  receive领取时间
        /// </summary>
        public string ValidityType { get; set; }

        [DisplayFormat(DataFormatString = "yyyy-MM-dd HH:mm:ss")]
        public DateTime? StartTime { get; set; }

        [DisplayFormat(DataFormatString = "yyyy-MM-dd HH:mm:ss")]
        public DateTime? EndTime { get; set; }
        /// <summary>
        /// 领取时间
        /// </summary>
        public int? Duration { get; set; }

        /// <summary>
        /// 领取时间类型day天 hour小时
        /// </summary>
        public string TimeType { get; set; }
        /// <summary>
        /// all所有人 group客户组  member_tag客户标签  individual个别客户
        /// </summary>
        [Required(ErrorMessage = "请选择适用范围")]
        public string UseCustomer { get; set; }

        /// <summary>
        /// // 客户标签选中值
        /// </summary>
        [BindProperty(Name = "user_tagsOption[]")]
        //[FromForm(Name = "user_tagsOption[]")] // 客户标签选中值
        public List<string>? UserTagsOptions { get; set; }
        /// <summary>
        /// 个别客户选中值  
        /// </summary>
        [BindProperty(Name = "customerOption[]")]
        public List<string>? CustomerOption { get; set; }

        /// <summary>
        /// 客户组选中值  user_groupOption
        /// </summary>
        [BindProperty(Name = "user_groupOption[]")]
        public List<string>? UserGroupOption { get; set; }
        /// <summary>
        /// 适用产品类型 0全部 1指定产品 2指定分类
        /// </summary>

        public int UseProducts { get; set; }
        /// <summary>
        /// 指定产品选中值
        /// </summary>
        [BindProperty(Name = "productsOption[]")]
        public List<string>? ProductsOption { get; set; }


        /// <summary>
        /// 指定分类选中值
        /// </summary>
        [BindProperty(Name = "products_categoryCurrent[]")]
        public List<string>? ProductsCategoryCurrent { get; set; }



        public string? UseProductsValue { get; set; }
        /// <summary>
        /// 优惠券类型 0 折扣券 1 现金券
        /// </summary>

        public int CouponType { get; set; }
        /// <summary>
        /// 减免金额
        /// </summary>
        //[Required(ErrorMessage = "优惠值必填")]
        //[Range(0.01, double.MaxValue, ErrorMessage = "优惠值必须大于0")]
        public decimal? CouponTypeValue { get; set; }
        /// <summary>
        /// 折扣
        /// </summary>

        //[DisplayFormat(DataFormatString = "{0:N2}", ApplyFormatInEditMode = true)]
        //[Range(0.01, double.MaxValue, ErrorMessage = "优惠值必须大于0")]
        public decimal? DisCountCouponTypeValue { get; set; }


        /// <summary>
        /// 最高抵扣金额 0 不限制 1 限制
        /// </summary>
        public int? IsMaxAmount { get; set; }
        /// <summary>
        /// 最高抵扣金额值
        /// </summary>
        public decimal? MaxAmount { get; set; }

        /// <summary>
        /// 使用条件 0最低消费金额  1最低购买数量
        /// </summary>
        public int ConditionType { get; set; }

        /// <summary>
        /// 最低消费金额
        /// </summary>
        public decimal? ConditionPrice { get; set; }
        /// <summary>
        /// 最低购买数量
        /// </summary>
        public int? ConditionQty { get; set; }


        /// <summary>
        /// 发放量
        /// </summary>
        [Required(ErrorMessage = "发放量必填")]
        [Range(1, int.MaxValue, ErrorMessage = "发放量必须大于0")]
        public int? Qty { get; set; }

        /// <summary>
        ///不限数量  0 不限制 1 限制
        /// </summary>
        public int? UnLmQty { get; set; }

        [Required(ErrorMessage = "每人使用次数必填")]
        [Range(1, int.MaxValue, ErrorMessage = "每人使用次数必须大于0")]
        public int? UseNum { get; set; }

        /// <summary>
        ///不限次数  0 不限制 1 限制
        /// </summary>
        public int? UnLmUseNum { get; set; }

        /// <summary>
        /// 允许与满减活动（减金额、打折）、会员折扣叠加使用
        /// </summary>
        public int? IsAllowDiscount { get; set; }

        /// <summary>
        /// 允许与限时促销、批发叠加使用
        /// </summary>
        public int? IsAllowPromotion { get; set; }

        /// <summary>
        /// 取消订单后自动返还  0 不自动 1 自动
        /// </summary>
        public int? OrderCancelReturn { get; set; }




        public string DeliveryMethod { get; set; }
        public int? CId { get; set; } = 0;
        /// <summary>
        /// 赠送方式 register完成会员注册 member登录成功 review客户评论成功  confirmReceipt客户确认收货  orderCompleted订单状态已完成
        /// </summary>
        public string? GiftMethod { get; set; }


        /// <summary>
        /// 在产品详情页显示该优惠券，客户点击后可以领取   0 不可以 1 可以
        /// </summary>
        public int? IsDetailShow { get; set; }




    }

    // 嵌套的SalesCoupon模型
    public class SalesCouponModel
    {
        [Required]
        [StringLength(20)]
        public string CouponNumber { get; set; }
    }


    #region 检测使用过优惠券
    public class CheckOperationUsedCouponRequest
    {
        public string ValidityType { get; set; }
        public string CouponNumber { get; set; }
        [DisplayFormat(DataFormatString = "yyyy-MM-dd HH:mm:ss")]
        public DateTime? EndTime { get; set; }

        public string? IsOver { get; set; }

        public int? CId { get; set;}
    }

    public class CheckResult
    {
        public string Tips { get; set; }
        public List<string> OperationNames { get; set; }
        public string ShoppingCartRecall { get; set; }
    }
    // 配置类
    public class CartRecallConfig
    {

        public IsItem IsItem { get; set; }
        [JsonProperty("IsCart")]
        public CartRecallSetting IsCart { get; set; }
    }
    public class IsItem
    {
        public bool IsUsed { get; set; }
    }
    public class CartRecallSetting
    {
        public bool IsUsed { get; set; }
        public bool UsedCoupon { get; set; }
        public string Coupon { get; set; }
        public int HowLong { get; set; }
    }
    #endregion


    public class DiscountAddProductRequest
    {

        [BindProperty(Name = "ProId[]")]
        public List<int>? ProIds { get; set; }


        [BindProperty(Name = "item")]
        public int? Item { get; set; }
    }


    #region 保存满减活动
    public class DiscountRequest
    {
        [Required(ErrorMessage = "活动名称不能为空")]
        public string Name { get; set; }

        [DisplayFormat(DataFormatString = "yyyy-MM-dd HH:mm:ss")]
        public DateTime? StartTime { get; set; }

        [DisplayFormat(DataFormatString = "yyyy-MM-dd HH:mm:ss")]
        public DateTime? EndTime { get; set; }

        /// <summary>
        /// 适用产品类型 0全部 1指定产品 2指定分类
        /// </summary>

        public int UseProducts { get; set; }

        /// <summary>
        /// 指定产品选中值
        /// </summary>
        [BindProperty(Name = "productsOption[]")]
        public List<string>? ProductsOption { get; set; }
        /// <summary>
        /// 指定分类选中值
        /// </summary>
        [BindProperty(Name = "products_categoryCurrent[]")]
        public List<string>? ProductsCategoryCurrent { get; set; }
        public string? UseProductsValue { get; set; }
        /// <summary>
        /// 使用条件  0满额减/送  1满件减/送
        /// </summary>
        public int ActCondition { get; set; }
        /// <summary>
        /// 活动类型0减金额  1打折 2赠品
        /// </summary>
        public int ActType { get; set; }
        /// <summary>
        /// 优惠规则 0阶梯优惠 1循环优惠
        /// </summary>
        public int ActRule { get; set; }
        //RulePro[]
        /// <summary>
        /// 主键
        /// </summary>
        public int FId { get; set; }
        /// <summary>
        /// 优惠类型
        /// </summary>
        public int Type { get; set; }


    }
    #endregion

    #region 更新促销活动
    public class SaleUpdateModel
    {
        public int Id { get; set; }
        public string Name { get; set; }
        /// <summary>
        /// 限购规则 0不限购 1限购
        /// </summary>
        [JsonPropertyName("limitType")]
        public int LimitType { get; set; }
        /// <summary>
        /// 限购规则限购数量
        /// </summary>
        [JsonPropertyName("limitCount")]
        public int? LimitCount { get; set; }
        /// <summary>
        /// 优惠类型 0固定折扣 1现金减价 2一口价
        /// </summary>
        [JsonPropertyName("offerType")]
        public int OfferType { get; set; }
        /// <summary>
        /// 促销价
        /// </summary>
        public List<string>? SalePrice { get; set; }
        /// <summary>
        /// 优惠金额
        /// </summary>
        [JsonPropertyName("discountPrice")]
        public List<string>? DiscountPrice { get; set; }
        /// <summary>
        /// 折扣
        /// </summary>
        [JsonPropertyName("discount")]
        public List<string>? Discount { get; set; }
        public List<int> Item { get; set; }
        /// <summary>
        /// 使用产品类型 0全部 1指定产品 2指定分类
        /// </summary>
        [JsonPropertyName("useProductsType")]
        public int UseProductsType { get; set; }
        [JsonPropertyName("useProductsDataAry")]
        public List<List<string>> UseProductsDataAry { get; set; }
        /// <summary>
        /// 指定分类选中值
        /// </summary>
        public List<int>? ProductsCategoryCurrent { get; set; }
        /// <summary>
        /// 最低产品价格
        /// </summary>
        [JsonPropertyName("conditionPrice")]
        public List<string>? ConditionPrice { get; set; }
        //[DisplayFormat(DataFormatString = "yyyy-MM-dd HH:mm:ss")]
        public string? StartTime { get; set; }
        //[DisplayFormat(DataFormatString = "yyyy-MM-dd HH:mm:ss")]
        public string? EndTime { get; set; }
        /// <summary>
        /// 排序
        /// </summary>
        [JsonPropertyName("order_type")]
        public string OrderType { get; set; }
        /// <summary>
        /// 适用客户 all所有人tourist游客 member会员
        /// </summary>
        public string ApplicableCustomers { get; set; }

        public Dictionary<string,string>  Scope { get; set; }

        public Dictionary<string, Dictionary<string, decimal>>? childDiscount { get; set; }

        public Dictionary<string, Dictionary<string, decimal>>? childDiscountPrice { get; set; }
        public Dictionary<string, Dictionary<string, decimal>>? childSalePrice { get; set; }

        

    }
    #endregion

    #region
    public class ReadProductModel
    {
        public int Id { get; set; }
    }
    #endregion

    public class FlashSalesSettingRequest
    {
        /// <summary>
        /// 活动项目
        /// </summary>
        [BindProperty(Name = "Activities[]")]
        public List<string>? Activities { get; set; }

        [BindProperty(Name = "DefaultStatus[BgColor]")]
        public string DefaultStatusBgColor{get; set; }
        [BindProperty(Name = "DefaultStatus[TitleColor]")]
        public string DefaultStatusTitleColor { get; set; }
        [BindProperty(Name = "DefaultStatus[CountdownTxtColor]")]
        public string DefaultStatusCountdownTxtColor { get; set; }
        [BindProperty(Name = "SelectedStatus[BgColor]")]
        public string SelectedStatusBgColor { get; set; }
        [BindProperty(Name = "SelectedStatus[TitleColor]")]
        public string SelectedStatusTitleColor { get; set; }
        [BindProperty(Name = "SelectedStatus[CountdownTxtColor]")]
        public string SelectedStatusCountdownTxtColor { get; set; }
        [BindProperty(Name = "SelectedStatus[CountdownBgColor]")]
        public string SelectedStatusCountdownBgColor { get; set; }
    }

    public class FlashSalesSetting
    {
        public List<string>? Activities { get; set; }
        public SetStatus SelectedStatus { get; set; }
        public SetStatus DefaultStatus { get; set; }
    }
    
    public class SetStatus
    {
        public string BgColor { get; set; }
        public string TitleColor { get; set; }
        public string CountdownTxtColor { get; set; }
        public string CountdownBgColor { get; set; }
    }


    #region 弹窗公告参数
    public class OperationActivitiesRequest
    {
        [Required(ErrorMessage = "活动名称不能为空")]
        public string Name { get; set; }

        [DisplayFormat(DataFormatString = "yyyy-MM-dd HH:mm:ss")]
        public DateTime StartTime { get; set; }

        [DisplayFormat(DataFormatString = "yyyy-MM-dd HH:mm:ss")]
        public DateTime EndTime { get; set; }
        /// <summary>
        /// 优惠券编码
        /// </summary>
        public string? Coupon { get; set; }
        /// <summary>
        /// 进入店铺几秒显示
        /// </summary>

        public int ShowTime { get; set; }

        /// <summary>
        /// 显示页面
        /// </summary>
        public List<string>? Pages { get; set; }

        public int Frequency { get; set; }
        /// <summary>
        /// 标题
        /// </summary>
        [BindProperty(Name = "Data[en][Title]")]
        public string? Title { get; set; }

        /// <summary>
        /// 链接文案
        /// </summary>
        [BindProperty(Name = "Data[en][SubTitle]")]
        public string? SubTitle { get; set; }
        [BindProperty(Name = "Data[en][Content]")]
        public string? Content { get; set; }
        [BindProperty(Name = "Data[en][BtnText]")]
        public string? BtnText { get; set; }
        [BindProperty(Name = "Data[en][Pic]")]
        public string? Pic { get; set; }
        [BindProperty(Name = "Data[en][PicMobile]")]
        public string? PicMobile { get; set; }
        [BindProperty(Name = "Data[en][Link]")]
        public string? Link { get; set; }
        [BindProperty(Name = "Data[en][YesBtn]")]
        public string? YesBtn { get; set; }

        [BindProperty(Name = "Data[en][NoBtn]")]
        public string? NoBtn { get; set; }
        #region 弹窗公告样式

        public string? BgColor { get; set; }
        public string? FontColor { get; set; }
        public string? TitleColor { get; set; }
        public string? ContentColor { get; set; }
        public string? BtnTextColor { get; set; }
        public string? BtnTextBgColor { get; set; }
        #endregion

        public string? Category { get; set; }

        public string? Mode { get; set; }

        public int OId { get; set; }=0;

        public string?  Client { get; set; }
    }
    #endregion
    public class EnDataModel
    { 
        public OperationActivitiesModelData en { get; set; }
    }
    public class OperationActivitiesModelData
    {
        public string? Title { get; set; }
        public string? SubTitle { get; set; }
        
        public string? Content { get; set; }
        public string? BtnText { get; set; }
        public string? Pic { get; set; }
        public string? PicMobile { get; set; }
        public string? Link { get; set; }
        public string? YesBtn { get; set; }
        public string? NoBtn { get; set; }

        public string? PicSize { get; set; }
        public string? PicMobileSize { get; set; }

        public string? BgColor { get; set; }
        public string? FontColor { get; set; }
        public string? TitleColor { get; set; }
        public string? ContentColor { get; set; }
        public string? BtnTextColor { get; set; }
        public string? BtnTextBgColor { get; set; }
    }

   
}
