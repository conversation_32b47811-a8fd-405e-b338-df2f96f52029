{"classPrefix": "Common\\helps", "classFullName": "Common\\helps\\HelpsManage", "classBrief": "HelpsManage 提供了可以在应用程序中使用B端操作函数整理的相关功能。", "classExtends": "无", "availableVersion": "5.0010+", "sourceCode": "http://git.shop5.ueeshop.com/developer/ueeshop5.0/common/helps/HelpsManage.php", "params": [], "methods": [{"methodName": "checkVersion", "methodBrief": "判断当前系统的版本权限", "methodIsDefinedIn": "--", "methodType": "公共 静态 方法", "methodPrefix": "public static", "methodParams": [{"methodParamName": "version", "methodParamDefault": null, "methodParamType": "array", "methodParamBrief": "当前系统的版本。 具体规则如下：\r\n● 0 UEESHOP 标准版。\r\n● 1 UEESHOP 高级版。\r\n● 2 UEESHOP 专业版。\r\n● 100 UEESHOP 定制版。\r\n● 10 Shoptago 免费版。\r\n● 11 Shoptago 标准版。\r\n● 12 Shoptago 高级版。\r\n● 13 Shoptago 专业版。"}], "methodReturn": {"methodReturnType": "boolean", "methodReturnBrief": "返回判断后是否允许，true或者false"}}, {"methodName": "formDataFilterCdn", "methodBrief": "通过递归批量替换成CDN地址", "methodIsDefinedIn": "--", "methodType": "公共 静态 方法", "methodPrefix": "public static"}, {"methodName": "formDataFilterCdnExt", "methodBrief": "通过递归批量把参数的数值替换成CDN地址", "methodIsDefinedIn": "--", "methodType": "公共 静态 方法", "methodPrefix": "public static", "methodParams": [{"methodParamName": "ary", "methodParamDefault": null, "methodParamType": "string", "methodParamBrief": "需要替换CDN地址的规则。 具体规则如下：\r\n● $_GET GET数据。\r\n● $_POST POST数据。\r\n● $_REQUEST REQUEST数据。"}]}, {"methodName": "checkPermit", "methodBrief": "权限检测", "methodIsDefinedIn": "--", "methodType": "公共 静态 方法", "methodPrefix": "public static", "methodParams": [{"methodParamName": "version", "methodParamDefault": "''", "methodParamType": "string", "methodParamBrief": "需要检查该权限的名称"}], "methodReturn": {"methodReturnType": "integer", "methodReturnBrief": "返回判断后是否允许，1或者0"}}, {"methodName": "noPermit", "methodBrief": "没有权限的输出方式", "methodIsDefinedIn": "--", "methodType": "公共 静态 方法", "methodPrefix": "public static", "methodParams": [{"methodParamName": "type", "methodParamDefault": null, "methodParamType": "string", "methodParamBrief": "需要设置没有权限的输出结果。 具体规则如下：\r\n● 0 返回false。\r\n● 1 直接exit终止，输出没有权限的提示字样。"}], "methodReturn": {"methodReturnType": "boolean || string", "methodReturnBrief": "返回false 或者 直接exit终止"}}, {"methodName": "checkAuth", "methodBrief": "左侧栏目的权限检测", "methodIsDefinedIn": "--", "methodType": "公共 静态 方法", "methodPrefix": "public static", "methodParams": [{"methodParamName": "module", "methodParamDefault": "[]", "methodParamType": "array", "methodParamBrief": "需要检查该栏目权限的数组"}], "methodReturn": {"methodReturnType": "integer", "methodReturnBrief": "返回判断后是否允许，1或者0"}}, {"methodName": "operationLog", "methodBrief": "记录B端各种各类的操作日志", "methodIsDefinedIn": "--", "methodType": "公共 静态 方法", "methodPrefix": "public static", "methodParams": [{"methodParamName": "Logs", "methodParamDefault": null, "methodParamType": "array", "methodParamBrief": "记录的标题"}, {"methodParamName": "LogDesc", "methodParamDefault": "''", "methodParamType": "string", "methodParamBrief": "记录的详细操作名称"}, {"methodParamName": "<PERSON><PERSON><PERSON>", "methodParamDefault": "''", "methodParamType": "string", "methodParamBrief": "记录的模块标识"}, {"methodParamName": "Action", "methodParamDefault": "''", "methodParamType": "string", "methodParamBrief": "记录的子模块标识"}]}, {"methodName": "configOperaction", "methodBrief": "自动新增或者修改公共配置数据表的内容", "methodIsDefinedIn": "--", "methodType": "公共 静态 方法", "methodPrefix": "public static", "methodParams": [{"methodParamName": "cfg", "methodParamDefault": null, "methodParamType": "array", "methodParamBrief": "需要循环记录的数据信息数组"}, {"methodParamName": "global", "methodParamDefault": null, "methodParamType": "array", "methodParamBrief": "需要保存到公共配置数据表的GroupId字段内容"}]}, {"methodName": "getApi<PERSON>ey", "methodBrief": "获取应用到第三方接口的API key", "methodIsDefinedIn": "--", "methodType": "公共 静态 方法", "methodPrefix": "public static"}, {"methodName": "thisWebFile", "methodBrief": "判断文件是否属于当前网站的文件", "methodIsDefinedIn": "--", "methodType": "公共 静态 方法", "methodPrefix": "public static", "methodParams": [{"methodParamName": "file", "methodParamDefault": null, "methodParamType": "string", "methodParamBrief": "文件路径"}], "methodReturn": {"methodReturnType": "boolean", "methodReturnBrief": "返回判断后是不是当前网站的文件，true或者false"}}, {"methodName": "nameCredentialStatus", "methodBrief": "读取实名认证状态", "methodIsDefinedIn": "--", "methodType": "公共 静态 方法", "methodPrefix": "public static", "methodReturn": {"methodReturnType": "integer", "methodReturnBrief": "返回是否认证成功，1或者0"}}, {"methodName": "multiImg", "methodBrief": "下拉和文本框的混合功能", "methodIsDefinedIn": "--", "methodType": "公共 静态 方法", "methodPrefix": "public static", "methodParams": [{"methodParamName": "boxId", "methodParamDefault": null, "methodParamType": "string", "methodParamBrief": "图片框ID"}, {"methodParamName": "inputName", "methodParamDefault": null, "methodParamType": "string", "methodParamBrief": "保存图片的字段"}, {"methodParamName": "picpath", "methodParamDefault": "''", "methodParamType": "string", "methodParamBrief": "图片路径"}, {"methodParamName": "imgSize", "methodParamDefault": "''", "methodParamType": "string", "methodParamBrief": "用于多图片上传"}, {"methodParamName": "isMove", "methodParamDefault": 0, "methodParamType": "integer", "methodParamBrief": "是否带有拖动功能"}], "methodReturn": {"methodReturnType": "string", "methodReturnBrief": "返回该功能的HTML格式字符串"}}, {"methodName": "multiImgItem", "methodBrief": "多个图片上传框功能", "methodIsDefinedIn": "--", "methodType": "公共 静态 方法", "methodPrefix": "public static", "methodParams": [{"methodParamName": "input_name", "methodParamDefault": null, "methodParamType": "string", "methodParamBrief": "字段名"}, {"methodParamName": "picpath", "methodParamDefault": "''", "methodParamType": "string", "methodParamBrief": "图片链接"}, {"methodParamName": "num", "methodParamDefault": null, "methodParamType": "integer", "methodParamBrief": "排序数字"}, {"methodParamName": "s_picpath", "methodParamDefault": "''", "methodParamType": "string", "methodParamBrief": "缩略图链接"}, {"methodParamName": "is_move", "methodParamDefault": 0, "methodParamType": "integer", "methodParamBrief": "是否可以移动"}, {"methodParamName": "type", "methodParamDefault": "''", "methodParamType": "string", "methodParamBrief": "上传框的类型。具体规则如下：\r\n● '' 图片。\r\n● video 视频。"}, {"methodParamName": "module", "methodParamDefault": "''", "methodParamType": "string", "methodParamBrief": "模块"}], "methodReturn": {"methodReturnType": "string", "methodReturnBrief": "返回该功能的HTML格式字符串"}}, {"methodName": "formEdit", "methodBrief": "多语言版本的表单文本框输出", "methodIsDefinedIn": "--", "methodType": "公共 静态 方法", "methodPrefix": "public static", "methodParams": [{"methodParamName": "row", "methodParamDefault": null, "methodParamType": "array", "methodParamBrief": "表单数据数组"}, {"methodParamName": "type", "methodParamDefault": "'text'", "methodParamType": "string", "methodParamBrief": "文本框的输出类型，具体规则如下：\r\n● text 单行文本框。\r\n● textarea 多行文本框。\r\n● editor 编辑器。\r\n● editor_simple 简便编辑器。"}, {"methodParamName": "name", "methodParamDefault": null, "methodParamType": "string", "methodParamBrief": "字段名称"}, {"methodParamName": "size", "methodParamDefault": 0, "methodParamType": "integer", "methodParamBrief": "文本框的尺寸"}, {"methodParamName": "max", "methodParamDefault": 0, "methodParamType": "integer", "methodParamBrief": "文本框可输入的最大字符数"}, {"methodParamName": "attr", "methodParamDefault": "''", "methodParamType": "string", "methodParamBrief": "可扩容的参数"}, {"methodParamName": "changeName", "methodParamDefault": "''", "methodParamType": "string", "methodParamBrief": "可替换的字段名称"}, {"methodParamName": "changeAry", "methodParamDefault": "[]", "methodParamType": "array", "methodParamBrief": "可替换的表单数据数组"}, {"methodParamName": "full", "methodParamDefault": 0, "methodParamType": "integer", "methodParamBrief": "是否需要宽度拉满显示"}], "methodReturn": {"methodReturnType": "string", "methodReturnBrief": "返回该功能的HTML格式字符串"}}, {"methodName": "editor", "methodBrief": "编辑器的多行文本框输出", "methodIsDefinedIn": "--", "methodType": "公共 静态 方法", "methodPrefix": "public static", "methodParams": [{"methodParamName": "name", "methodParamDefault": null, "methodParamType": "string", "methodParamBrief": "文本框名称"}, {"methodParamName": "content", "methodParamDefault": "''", "methodParamType": "string", "methodParamBrief": "文本框内容"}, {"methodParamName": "imgbank", "methodParamDefault": "true", "methodParamType": "boolean", "methodParamBrief": "已弃用"}, {"methodParamName": "change", "methodParamDefault": 0, "methodParamType": "integer", "methodParamBrief": "参数data-change的设置数值"}], "methodReturn": {"methodReturnType": "string", "methodReturnBrief": "返回该功能的HTML格式字符串"}}, {"methodName": "ckEditor", "methodBrief": "Ckeditor编辑器生成", "methodIsDefinedIn": "--", "methodType": "公共 静态 方法", "methodPrefix": "public static", "methodParams": [{"methodParamName": "name", "methodParamDefault": null, "methodParamType": "string", "methodParamBrief": "文本框名称"}], "methodReturn": {"methodReturnType": "string", "methodReturnBrief": "返回该功能的HTML格式字符串"}}, {"methodName": "ckEditorSimple", "methodBrief": "CkeditorSimple编辑器生成", "methodIsDefinedIn": "--", "methodType": "公共 静态 方法", "methodPrefix": "public static", "methodParams": [{"methodParamName": "name", "methodParamDefault": null, "methodParamType": "string", "methodParamBrief": "文本框名称"}, {"methodParamName": "width", "methodParamDefault": "''", "methodParamType": "string", "methodParamBrief": "编辑器可视宽度"}, {"methodParamName": "height", "methodParamDefault": 300, "methodParamType": "integer", "methodParamBrief": "编辑器可视高度"}], "methodReturn": {"methodReturnType": "string", "methodReturnBrief": "返回该功能的HTML格式字符串"}}, {"methodName": "unitFormEdit", "methodBrief": "多语言版本的表单文本框输出（带有限制最大字符长度显示和提示的效果）", "methodIsDefinedIn": "--", "methodType": "公共 静态 方法", "methodPrefix": "public static", "methodParams": [{"methodParamName": "row", "methodParamDefault": null, "methodParamType": "array", "methodParamBrief": "表单数据数组"}, {"methodParamName": "type", "methodParamDefault": "'text'", "methodParamType": "string", "methodParamBrief": "文本框的输出类型，具体规则如下：\r\n● text 单行文本框。\r\n● textarea 多行文本框。\r\n● editor 编辑器。\r\n● editor_simple 简便编辑器。"}, {"methodParamName": "name", "methodParamDefault": null, "methodParamType": "string", "methodParamBrief": "字段名称"}, {"methodParamName": "size", "methodParamDefault": 0, "methodParamType": "integer", "methodParamBrief": "文本框的尺寸"}, {"methodParamName": "max", "methodParamDefault": 0, "methodParamType": "integer", "methodParamBrief": "文本框可输入的最大字符数"}, {"methodParamName": "attr", "methodParamDefault": "''", "methodParamType": "string", "methodParamBrief": "可扩容的参数"}, {"methodParamName": "changeName", "methodParamDefault": "''", "methodParamType": "string", "methodParamBrief": "可替换的字段名称"}, {"methodParamName": "changeAry", "methodParamDefault": "[]", "methodParamType": "array", "methodParamBrief": "可替换的表单数据数组"}, {"methodParamName": "full", "methodParamDefault": 0, "methodParamType": "integer", "methodParamBrief": "是否需要宽度拉满显示"}], "methodReturn": {"methodReturnType": "string", "methodReturnBrief": "返回该功能的HTML格式字符串"}}, {"methodName": "guideIndexBox", "methodBrief": "没有数据时候的引导页 以及开关型的应用", "methodIsDefinedIn": "--", "methodType": "公共 静态 方法", "methodPrefix": "public static", "methodParams": [{"methodParamName": "tit", "methodParamDefault": null, "methodParamType": "string", "methodParamBrief": "大标题"}, {"methodParamName": "desc", "methodParamDefault": "''", "methodParamType": "string", "methodParamBrief": "小标题"}, {"methodParamName": "btnHtml", "methodParamDefault": 300, "methodParamType": "string || array", "methodParamBrief": "按钮HTML格式"}, {"methodParamName": "img", "methodParamDefault": 300, "methodParamType": "string", "methodParamBrief": "图片路径"}, {"methodParamName": "boxClass", "methodParamDefault": 300, "methodParamType": "string", "methodParamBrief": "容器拓展类名"}], "methodReturn": {"methodReturnType": "string", "methodReturnBrief": "返回该功能的HTML格式字符串"}}, {"methodName": "boxDropDouble", "methodBrief": "下拉和文本框的单选和多选的混合功能", "methodIsDefinedIn": "--", "methodType": "公共 静态 方法", "methodPrefix": "public static", "methodParams": [{"methodParamName": "SelectName", "methodParamDefault": null, "methodParamType": "string", "methodParamBrief": "下拉名称"}, {"methodParamName": "InputName", "methodParamDefault": null, "methodParamType": "string", "methodParamBrief": "文本名称"}, {"methodParamName": "DataAry", "methodParamDefault": "[]", "methodParamType": "array", "methodParamBrief": "产品属性"}, {"methodParamName": "Value", "methodParamDefault": "[]", "methodParamType": "array", "methodParamBrief": "数值，具体规则如下：\r\n● 单选:[Select]=>下拉值,[Input]=>文本值,[Type]=>类型值。\r\n● 多选:[Name]=>名称,[Value]=>数值,[Type]=>类型。"}, {"methodParamName": "Type", "methodParamDefault": 0, "methodParamType": "integer", "methodParamBrief": "产品类型 (0:下拉+文本，1:下拉)"}, {"methodParamName": "InputAttr", "methodParamDefault": null, "methodParamType": "string", "methodParamBrief": "文本额外参数"}, {"methodParamName": "isCheckbox", "methodParamDefault": 0, "methodParamType": "integer", "methodParamBrief": "是否为多选"}, {"methodParamName": "isMore", "methodParamDefault": 0, "methodParamType": "integer", "methodParamBrief": "显示加载更多"}, {"methodParamName": "placeholder", "methodParamDefault": "''", "methodParamType": "string", "methodParamBrief": "默认形式的文字提示"}, {"methodParamName": "TopType", "methodParamDefault": "''", "methodParamType": "string", "methodParamBrief": "默认内容的类型"}, {"methodParamName": "isShowAdd", "methodParamDefault": 1, "methodParamType": "integer", "methodParamBrief": "默认显示添加选项（针对下拉）"}], "methodReturn": {"methodReturnType": "string", "methodReturnBrief": "返回该功能的HTML格式字符串"}}, {"methodName": "boxDropDoubleOption", "methodBrief": "boxDropDouble的选项生成", "methodIsDefinedIn": "--", "methodType": "公共 静态 方法", "methodPrefix": "public static", "methodParams": [{"methodParamName": "Name", "methodParamDefault": null, "methodParamType": "string", "methodParamBrief": "选项的标题"}, {"methodParamName": "Value", "methodParamDefault": null, "methodParamType": "string", "methodParamBrief": "选项的数值"}, {"methodParamName": "Type", "methodParamDefault": null, "methodParamType": "string", "methodParamBrief": "选项的相应类型"}, {"methodParamName": "Table", "methodParamDefault": null, "methodParamType": "string", "methodParamBrief": "选项的相应数据表名称"}, {"methodParamName": "Checkbox", "methodParamDefault": 0, "methodParamType": "integer", "methodParamBrief": "是否为多选或者单选，具体规则如下：\r\n● 0 单选。\r\n● 1 多选。"}, {"methodParamName": "TopType", "methodParamDefault": "''", "methodParamType": "string", "methodParamBrief": "是否为一级单选的选项，具体规则如下：\r\n● 0 否。\r\n● 1 是。"}, {"methodParamName": "Next", "methodParamDefault": 0, "methodParamType": "integer", "methodParamBrief": "该选项是否有下一级，具体规则如下：\r\n● 0 没有下一级。\r\n● 1 有下一级。"}, {"methodParamName": "IconTxt", "methodParamDefault": "''", "methodParamType": "string", "methodParamBrief": "输入在选项的名称前面的icon html格式的字符串内容"}], "methodReturn": {"methodReturnType": "string", "methodReturnBrief": "返回该功能的HTML格式字符串"}}, {"methodName": "setPageUrl", "methodBrief": "封装自定义地址保存", "methodIsDefinedIn": "--", "methodType": "公共 静态 方法", "methodPrefix": "public static", "methodParams": [{"methodParamName": "table", "methodParamDefault": null, "methodParamType": "string", "methodParamBrief": "相关的数据库表名称"}, {"methodParamName": "ID", "methodParamDefault": null, "methodParamType": "integer", "methodParamBrief": "相关的主键ID"}, {"methodParamName": "Name", "methodParamDefault": "''", "methodParamType": "string", "methodParamBrief": "自定义地址的地址名称，选填，如果填写了会自动覆盖数据中的地址名称"}]}, {"methodName": "getUrl", "methodBrief": "后端获取相关对象的前端地址", "methodIsDefinedIn": "--", "methodType": "公共 静态 方法", "methodPrefix": "public static", "methodParams": [{"methodParamName": "row", "methodParamDefault": null, "methodParamType": "array", "methodParamBrief": "相关对象的数据"}, {"methodParamName": "field", "methodParamDefault": "'products_category'", "methodParamType": "string", "methodParamBrief": "相关的数据库表名称"}, {"methodParamName": "lang", "methodParamDefault": 300, "methodParamType": "integer", "methodParamBrief": "相关对象的语言版"}], "methodReturn": {"methodReturnType": "string", "methodReturnBrief": "返回针对后端url，点击在时候如果有正式域名直接跳到正式域名"}}, {"methodName": "salesRightSearchForm", "methodBrief": "右侧公共弹窗的搜索框生成", "methodIsDefinedIn": "--", "methodType": "公共 静态 方法", "methodPrefix": "public static", "methodParams": [{"methodParamName": "m", "methodParamDefault": null, "methodParamType": "string", "methodParamBrief": "用于搜索后跳转的模块参数"}, {"methodParamName": "a", "methodParamDefault": null, "methodParamType": "string", "methodParamBrief": "用于搜索后跳转的功能参数"}, {"methodParamName": "d", "methodParamDefault": null, "methodParamType": "string", "methodParamBrief": "用于搜索后跳转的操作参数"}, {"methodParamName": "ext", "methodParamDefault": "''", "methodParamType": "string", "methodParamBrief": "用于扩展的隐藏域字符串内容"}], "methodReturn": {"methodReturnType": "string", "methodReturnBrief": "返回该功能的HTML格式字符串"}}, {"methodName": "fixedRightProductsChoiceForm", "methodBrief": "右侧产品选择弹窗生成", "methodIsDefinedIn": "--", "methodType": "公共 静态 方法", "methodPrefix": "public static", "methodParams": [{"methodParamName": "m", "methodParamDefault": null, "methodParamType": "string", "methodParamBrief": "用于搜索后跳转的模块参数"}, {"methodParamName": "a", "methodParamDefault": null, "methodParamType": "string", "methodParamBrief": "用于搜索后跳转的功能参数"}, {"methodParamName": "d", "methodParamDefault": null, "methodParamType": "string", "methodParamBrief": "用于搜索后跳转的操作参数"}, {"methodParamName": "hideAry", "methodParamDefault": "[]", "methodParamType": "array", "methodParamBrief": "隐藏域"}, {"methodParamName": "allSelect", "methodParamDefault": 0, "methodParamType": "integer", "methodParamBrief": "是否全选"}, {"methodParamName": "ext", "methodParamDefault": "''", "methodParamType": "string", "methodParamBrief": "用于扩展的隐藏域字符串内容"}, {"methodParamName": "filterData", "methodParamDefault": "[]'", "methodParamType": "array", "methodParamBrief": "筛选数据"}, {"methodParamName": "IsNoData", "methodParamDefault": 0, "methodParamType": "integer", "methodParamBrief": "是否显示暂没数据"}, {"methodParamName": "IsHiddenCategoryFilter", "methodParamDefault": 0, "methodParamType": "integer", "methodParamBrief": "是否显示分类筛选"}], "methodReturn": {"methodReturnType": "string", "methodReturnBrief": "返回该功能的HTML格式字符串"}}, {"methodName": "iconvPrice", "methodBrief": "显示转换汇率后的价格", "methodIsDefinedIn": "--", "methodType": "公共 静态 方法", "methodPrefix": "public static", "methodParams": [{"methodParamName": "Price", "methodParamDefault": null, "methodParamType": "float", "methodParamBrief": "源浮点数"}, {"methodParamName": "Method", "methodParamDefault": 0, "methodParamType": "integer", "methodParamBrief": "定义应该从结果中返回的数据规则。 具体规则如下：\r\n● 0 符号+价格。\r\n● 1 符号。\r\n● 2 价格。"}, {"methodParamName": "<PERSON><PERSON><PERSON><PERSON>", "methodParamDefault": "''", "methodParamType": "string", "methodParamBrief": "货币缩写"}], "methodReturn": {"methodReturnType": "string", "methodReturnBrief": "返回由$Method参数所定义的字符串"}}, {"methodName": "rangePrice", "methodBrief": "显示当前产品的最优惠价格（包含限时促销）", "methodIsDefinedIn": "--", "methodType": "公共 静态 方法", "methodPrefix": "public static", "methodParams": [{"methodParamName": "Price", "methodParamDefault": null, "methodParamType": "float", "methodParamBrief": "源浮点数"}, {"methodParamName": "Method", "methodParamDefault": 0, "methodParamType": "integer", "methodParamBrief": "定义应该从结果中返回的数据规则。 具体规则如下：\r\n● 1 价格。\r\n● 0 符号+价格。"}], "methodReturn": {"methodReturnType": "string", "methodReturnBrief": "返回由$Method参数所定义的字符串"}}, {"methodName": "shoppingCartRecall", "methodBrief": "系统各类相关的召回执行函数（包含购物车召回和弃单召回）", "methodIsDefinedIn": "--", "methodType": "公共 静态 方法", "methodPrefix": "public static", "methodParams": [{"methodParamName": "Type", "methodParamDefault": null, "methodParamType": "string", "methodParamBrief": "相关召回的执行类型"}, {"methodParamName": "ID", "methodParamDefault": 0, "methodParamType": "integer", "methodParamBrief": "相关召回的召回ID（单个）"}, {"methodParamName": "<PERSON><PERSON>", "methodParamDefault": "[]", "methodParamType": "array", "methodParamBrief": "相关召回的召回ID（多个）"}], "methodReturn": {"methodReturnType": "array", "methodReturnBrief": "返回召回后的结果数组"}}, {"methodName": "checkTmpFile", "methodBrief": "检查图片是否在临时文件夹", "methodIsDefinedIn": "--", "methodType": "公共 静态 方法", "methodPrefix": "public static", "methodParams": [{"methodParamName": "file", "methodParamDefault": null, "methodParamType": "string", "methodParamBrief": "需要检查的文件路径"}], "methodReturn": {"methodReturnType": "string", "methodReturnBrief": "如果文件存在就返回文件路径，否则返回空白字符串"}}, {"methodName": "language", "methodBrief": "批量替换后台图片CDN功能", "methodIsDefinedIn": "--", "methodType": "公共 静态 方法", "methodPrefix": "public static", "methodParams": [{"methodParamName": "html", "methodParamDefault": null, "methodParamType": "string", "methodParamBrief": "需要替换的html字符串内容"}], "methodReturn": {"methodReturnType": "string", "methodReturnBrief": "返回已被替换的html字符串内容"}}, {"methodName": "getSalesId", "methodBrief": "获取业务员ID", "methodIsDefinedIn": "--", "methodType": "公共 静态 方法", "methodPrefix": "public static"}, {"methodName": "updateScreening", "methodBrief": "更新条件筛选数据", "methodIsDefinedIn": "--", "methodType": "公共 静态 方法", "methodPrefix": "public static", "methodParams": [{"methodParamName": "ProId", "methodParamDefault": 0, "methodParamType": "string", "methodParamBrief": "产品ID"}, {"methodParamName": "CateId", "methodParamDefault": 0, "methodParamType": "string", "methodParamBrief": "产品分类ID"}, {"methodParamName": "Type", "methodParamDefault": "Products", "methodParamType": "string", "methodParamBrief": "定义处理的类型规则。 具体规则如下：\r\n● Products 产品编辑分类变化 \r\n● ProcutsCategory 删除产品分类"}]}, {"methodName": "showPriceFormat", "methodBrief": "显示后台货币相应的价格格式", "methodIsDefinedIn": "--", "methodType": "公共 静态 方法", "methodPrefix": "public static", "methodParams": [{"methodParamName": "Price", "methodParamDefault": null, "methodParamType": "float", "methodParamBrief": "源浮点数"}], "methodReturn": {"methodReturnType": "string", "methodReturnBrief": "返回带价格格式的字符串"}}, {"methodName": "globalSelectBoxHtml", "methodBrief": "新版普通的下拉效果", "methodIsDefinedIn": "--", "methodType": "公共 静态 方法", "methodPrefix": "public static", "methodParams": [{"methodParamName": "selectData", "methodParamDefault": "[]", "methodParamType": "array", "methodParamBrief": "下拉数据"}, {"methodParamName": "name", "methodParamDefault": "''", "methodParamType": "float", "methodParamBrief": "下拉的name值"}, {"methodParamName": "selected", "methodParamDefault": null, "methodParamType": "''", "methodParamBrief": "下拉的默认选项"}, {"methodParamName": "value", "methodParamDefault": null, "methodParamType": "''", "methodParamBrief": "下拉的默认数值"}], "methodReturn": {"methodReturnType": "string", "methodReturnBrief": "返回该功能的HTML格式字符串"}}, {"methodName": "get<PERSON><PERSON><PERSON><PERSON><PERSON>", "methodBrief": "判断浏览器名称和版本", "methodIsDefinedIn": "--", "methodType": "公共 静态 方法", "methodPrefix": "public static", "methodReturn": {"methodReturnType": "string", "methodReturnBrief": "返回浏览器的名称"}}, {"methodName": "getOS", "methodBrief": "获取客户端操作系统信息包括win10", "methodIsDefinedIn": "--", "methodType": "公共 静态 方法", "methodPrefix": "public static", "methodReturn": {"methodReturnType": "string", "methodReturnBrief": "返回客户端操作系统的名称"}}, {"methodName": "isTrial", "methodBrief": "后台判断版本是否为试用版", "methodIsDefinedIn": "--", "methodType": "公共 静态 方法", "methodPrefix": "public static", "methodReturn": {"methodReturnType": "boolean", "methodReturnBrief": "返回判断后是否允许，true或者false"}}, {"methodName": "get_filter_data", "methodBrief": "获取添加产品筛选数据", "methodIsDefinedIn": "--", "methodType": "公共 静态 方法", "methodPrefix": "public static", "methodParams": [{"methodParamName": "cateId", "methodParamDefault": 0, "methodParamType": "integer", "methodParamBrief": "产品分类ID"}, {"methodParamName": "tagId", "methodParamDefault": "''", "methodParamType": "string", "methodParamBrief": "产品标签ID"}], "methodReturn": {"methodReturnType": "string", "methodReturnBrief": "返回判断后是否允许，true或者false"}}]}