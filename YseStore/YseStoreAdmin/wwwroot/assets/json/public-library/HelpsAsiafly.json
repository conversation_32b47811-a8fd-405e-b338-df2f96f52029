{"classPrefix": "Common\\helps", "classFullName": "Common\\helps\\HelpsAsiafly", "classBrief": "HelpsAsiafly 提供了U闪达功能的方法。", "classExtends": "无", "availableVersion": "5.0010+", "sourceCode": "http://git.shop5.ueeshop.com/developer/ueeshop5.0/common/helps/HelpsAsiafly.php", "params": [], "methods": [{"methodName": "curl", "methodBrief": "CURL请求", "methodIsDefinedIn": "--", "methodType": "公共 静态 方法", "methodPrefix": "public static", "methodParams": [{"methodParamInjection": "", "methodParamPrefix": "", "methodParamName": "url", "methodParamDefault": null, "methodParamType": "string", "methodParamBrief": "请求地址"}, {"methodParamInjection": "", "methodParamPrefix": "", "methodParamName": "formData", "methodParamDefault": "[]", "methodParamType": "array", "methodParamBrief": "POST请求参数"}, {"methodParamInjection": "", "methodParamPrefix": "", "methodParamName": "setOpt", "methodParamDefault": "[]", "methodParamType": "array", "methodParamBrief": "拓展CURL配置数组"}, {"methodParamInjection": "", "methodParamPrefix": "", "methodParamName": "log", "methodParamDefault": "true", "methodParamType": "boolean", "methodParamBrief": "是否记录日志"}], "methodReturn": {"methodReturnType": "array", "methodReturnBrief": "返回响应数据"}, "methodThrows": null, "methodExample": ""}, {"methodName": "getToken", "methodBrief": "获取token", "methodIsDefinedIn": "--", "methodType": "公共 静态 方法", "methodPrefix": "public static", "methodParams": [], "methodReturn": {"methodReturnType": "string", "methodReturnBrief": "token字符串"}, "methodThrows": null, "methodExample": ""}, {"methodName": "apply", "methodBrief": "开户", "methodIsDefinedIn": "--", "methodType": "公共 静态 方法", "methodPrefix": "public static", "methodParams": [{"methodParamInjection": "", "methodParamPrefix": "", "methodParamName": "data", "methodParamDefault": null, "methodParamType": "array", "methodParamBrief": "开户数据"}], "methodReturn": {"methodReturnType": "array", "methodReturnBrief": "开户响应数据"}, "methodThrows": null, "methodExample": ""}, {"methodName": "activate", "methodBrief": "激活", "methodIsDefinedIn": "--", "methodType": "公共 静态 方法", "methodPrefix": "public static", "methodParams": [{"methodParamInjection": "", "methodParamPrefix": "", "methodParamName": "data", "methodParamDefault": null, "methodParamType": "array", "methodParamBrief": "激活数据"}], "methodReturn": {"methodReturnType": "array", "methodReturnBrief": "激活响应数据"}, "methodThrows": null, "methodExample": ""}, {"methodName": "getExpressList", "methodBrief": "运费试算", "methodIsDefinedIn": "--", "methodType": "公共 静态 方法", "methodPrefix": "public static", "methodParams": [{"methodParamInjection": "", "methodParamPrefix": "", "methodParamName": "OrderId", "methodParamDefault": null, "methodParamType": "integer", "methodParamBrief": "订单ID"}, {"methodParamInjection": "", "methodParamPrefix": "", "methodParamName": "WId", "methodParamDefault": null, "methodParamType": "integer", "methodParamBrief": "包裹ID"}], "methodReturn": {"methodReturnType": "array", "methodReturnBrief": "获取亚翔供应链物流列表"}, "methodThrows": null, "methodExample": ""}, {"methodName": "getShippingPrice", "methodBrief": "获取亚翔系物流产品价格", "methodIsDefinedIn": "--", "methodType": "公共 静态 方法", "methodPrefix": "public static", "methodParams": [{"methodParamInjection": "", "methodParamPrefix": "", "methodParamName": "data", "methodParamDefault": null, "methodParamType": "array", "methodParamBrief": "包含最快时效，最慢时效，货物重量，国家代码，物流产品类型对应重量的数组"}, {"methodParamInjection": "", "methodParamPrefix": "", "methodParamName": "isMiddlePrice", "methodParamDefault": "true", "methodParamType": "boolean", "methodParamBrief": "是否取中位价格"}, {"methodParamInjection": "", "methodParamPrefix": "", "methodParamName": "OrderRate", "methodParamDefault": 0, "methodParamType": "float", "methodParamBrief": "已经生成订单，价格需要使用订单汇率，没有生成订单前，使用Session的汇率"}, {"methodParamInjection": "", "methodParamPrefix": "", "methodParamName": "ProductCode", "methodParamDefault": "''", "methodParamType": "string", "methodParamBrief": "是否返回指定的产品代码的"}], "methodReturn": {"methodReturnType": "array", "methodReturnBrief": "中位价格数组 / 名称价格数组"}, "methodThrows": null, "methodExample": ""}, {"methodName": "getOrdersPackagePrice", "methodBrief": "订单拉取包裹物流价格", "methodIsDefinedIn": "--", "methodType": "公共 静态 方法", "methodPrefix": "public static", "methodParams": [{"methodParamInjection": "", "methodParamPrefix": "", "methodParamName": "OrderId", "methodParamDefault": null, "methodParamType": "integer", "methodParamBrief": "订单ID"}, {"methodParamInjection": "", "methodParamPrefix": "", "methodParamName": "WId", "methodParamDefault": null, "methodParamType": "integer", "methodParamBrief": "包裹ID"}, {"methodParamInjection": "", "methodParamPrefix": "", "methodParamName": "ProductCode", "methodParamDefault": null, "methodParamType": "string", "methodParamBrief": "亚翔产品物流代码"}], "methodReturn": {"methodReturnType": "array", "methodReturnBrief": "包裹物流价格列表"}, "methodThrows": null, "methodExample": ""}, {"methodName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "methodBrief": "货币转换（某些渠道不支持此货币，需要转成其他货货币申报，亚翔说这个申报不影响客户前后台的货币，只是申报而已，我也不懂）", "methodIsDefinedIn": "--", "methodType": "公共 静态 方法", "methodPrefix": "public static", "methodParams": [{"methodParamInjection": "", "methodParamPrefix": "", "methodParamName": "price", "methodParamDefault": 0, "methodParamType": "float", "methodParamBrief": "价格"}, {"methodParamInjection": "", "methodParamPrefix": "", "methodParamName": "type", "methodParamDefault": "'Currency'", "methodParamType": "string", "methodParamBrief": "类型：【Currency | Price】"}], "methodReturn": {"methodReturnType": "string|float", "methodReturnBrief": "货币 | 价格"}, "methodThrows": null, "methodExample": ""}, {"methodName": "hsCode", "methodBrief": "海关hscode 类型有多种，需要提交时候处理过滤，保存在我们数据库的不处理，避免数据丢失，在接口这里处理", "methodIsDefinedIn": "--", "methodType": "公共 静态 方法", "methodPrefix": "public static", "methodParams": [{"methodParamInjection": "", "methodParamPrefix": "", "methodParamName": "hscode", "methodParamDefault": null, "methodParamType": "string", "methodParamBrief": "价格"}], "methodReturn": {"methodReturnType": "string", "methodReturnBrief": "替换后的数据"}, "methodThrows": null, "methodExample": ""}, {"methodName": "getTable", "methodBrief": "根据提供的参数，获取标签PDF文件链接，客户单号", "methodIsDefinedIn": "--", "methodType": "公共 静态 方法", "methodPrefix": "public static", "methodParams": [{"methodParamInjection": "", "methodParamPrefix": "", "methodParamName": "OrderId", "methodParamDefault": null, "methodParamType": "integer", "methodParamBrief": "订单Id"}, {"methodParamInjection": "", "methodParamPrefix": "", "methodParamName": "WId", "methodParamDefault": null, "methodParamType": "integer", "methodParamBrief": "包裹ID"}], "methodReturn": {"methodReturnType": "array", "methodReturnBrief": "获取订单信息提交接口返回数据"}, "methodThrows": null, "methodExample": ""}, {"methodName": "httpCopy", "methodBrief": "从远程服务器上下载文件到本地服务器", "methodIsDefinedIn": "--", "methodType": "公共 静态 方法", "methodPrefix": "public static", "methodParams": [{"methodParamInjection": "", "methodParamPrefix": "", "methodParamName": "filepath", "methodParamDefault": null, "methodParamType": "string", "methodParamBrief": "远程服务器文件路径"}, {"methodParamInjection": "", "methodParamPrefix": "", "methodParamName": "BusinessNo", "methodParamDefault": null, "methodParamType": "string", "methodParamBrief": "亚翔客户单号"}], "methodReturn": {"methodReturnType": "mixed", "methodReturnBrief": "下载文件到本地服务器 或者 失败状态"}, "methodThrows": null, "methodExample": ""}, {"methodName": "downFile", "methodBrief": "下载文件", "methodIsDefinedIn": "--", "methodType": "公共 静态 方法", "methodPrefix": "public static", "methodParams": [{"methodParamInjection": "", "methodParamPrefix": "", "methodParamName": "filepath", "methodParamDefault": null, "methodParamType": "string", "methodParamBrief": "目标文件路径"}, {"methodParamInjection": "", "methodParamPrefix": "", "methodParamName": "saveName", "methodParamDefault": "''", "methodParamType": "string", "methodParamBrief": "保存名称"}], "methodReturn": null, "methodThrows": null, "methodExample": ""}]}