{"classPrefix": "Common\\helps", "classFullName": "Common\\helps\\HelpsHtml", "classBrief": "HelpsHtml 提供了对视图输出的相关功能", "classExtends": "无", "availableVersion": "5.0010+", "sourceCode": "http://git.shop5.ueeshop.com/developer/ueeshop5.0/common/helps/HelpsHtml.php", "params": [], "methods": [{"methodName": "turnPage", "methodBrief": "返回后台翻页功能的HTML", "methodIsDefinedIn": "--", "methodType": "公共 静态 方法", "methodPrefix": "public static", "methodParams": [{"methodParamInjection": "", "methodParamPrefix": "", "methodParamName": "pagination", "methodParamDefault": null, "methodParamType": "object", "methodParamBrief": "Yii框架Pagination类返回的对象"}], "methodReturn": {"methodReturnType": "string", "methodReturnBrief": "返回的HTML"}, "methodExample": null}, {"methodName": "noTableData", "methodBrief": "返回后台内页表格空白提示的HTML", "methodIsDefinedIn": "--", "methodType": "公共 静态 方法", "methodPrefix": "public static", "methodParams": [{"methodParamInjection": "", "methodParamPrefix": "", "methodParamName": "isAdd", "methodParamDefault": "0", "methodParamType": "integer", "methodParamBrief": "是否显示添加按钮"}, {"methodParamInjection": "", "methodParamPrefix": "", "methodParamName": "addUrl", "methodParamDefault": "'javascript:;'", "methodParamType": "string", "methodParamBrief": "添加按钮的地址"}, {"methodParamInjection": "", "methodParamPrefix": "", "methodParamName": "msg", "methodParamDefault": "' '", "methodParamType": "string", "methodParamBrief": "提示语的文案"}, {"methodParamInjection": "", "methodParamPrefix": "", "methodParamName": "btn_msg", "methodParamDefault": "' '", "methodParamType": "string", "methodParamBrief": "添加按钮的文案"}, {"methodParamInjection": "", "methodParamPrefix": "", "methodParamName": "target", "methodParamDefault": "' '", "methodParamType": "string", "methodParamBrief": "额外属性（如新窗口打开）"}], "methodReturn": {"methodReturnType": "string", "methodReturnBrief": "返回的HTML"}, "methodExample": null}, {"methodName": "pluginsAppHeader", "methodBrief": "返回后台应用公共头部的HTML", "methodIsDefinedIn": "--", "methodType": "公共 静态 方法", "methodPrefix": "public static", "methodParams": [{"methodParamInjection": "", "methodParamPrefix": "", "methodParamName": "app", "methodParamDefault": null, "methodParamType": "string", "methodParamBrief": "应用名称"}, {"methodParamInjection": "", "methodParamPrefix": "", "methodParamName": "edit", "methodParamDefault": "0", "methodParamType": "string", "methodParamBrief": "编辑模式:<br/>0 : 返回我的应用<br/>1 : 返回上一页"}, {"methodParamInjection": "", "methodParamPrefix": "", "methodParamName": "link", "methodParamDefault": "' '", "methodParamType": "string", "methodParamBrief": "自定义返回链接"}, {"methodParamInjection": "", "methodParamPrefix": "", "methodParamName": "type", "methodParamDefault": "0", "methodParamType": "string", "methodParamBrief": "类型:<br/>0 : 大标题带简介<br/>1 : 小标题没简介"}], "methodReturn": {"methodReturnType": "string", "methodReturnBrief": "返回的HTML"}, "methodExample": null}, {"methodName": "newPluginsAppHeader", "methodBrief": "返回后台应用公共头部的HTML（新版）", "methodIsDefinedIn": "--", "methodType": "公共 静态 方法", "methodPrefix": "public static", "methodParams": [{"methodParamInjection": "", "methodParamPrefix": "", "methodParamName": "config", "methodParamDefault": null, "methodParamType": "array", "methodParamBrief": "配置数组 可传入:<br/>● version 版本 1<br/>● app 应用名称 gtranslate<br/>● edit 编辑模式 0 : 带图标返回我的应用 1 : 没图标返回上一页<br/>● link 自定义返回链接"}], "methodReturn": {"methodReturnType": "string", "methodReturnBrief": "返回的HTML"}, "methodExample": null}, {"methodName": "PluginsAppHeaderSubordinate", "methodBrief": "返回后台应用公共头部带二级面包屑的HTML", "methodIsDefinedIn": "--", "methodType": "公共 静态 方法", "methodPrefix": "public static", "methodParams": [{"methodParamInjection": "", "methodParamPrefix": "", "methodParamName": "config", "methodParamDefault": null, "methodParamType": "array", "methodParamBrief": "配置的数组 可传入:<br/>● app 应用名称 gtranslate<br/>● edit 编辑模式 0 : 返回我的应用 1 : 返回上一页<br/>● link 自定义返回链接<br/>● icon 是否显示图标 0 : 不显示 1 : 显示<br/>● type 显示图片类型 0 : 小图标带简介 1 : 大图标没简介<br/>● sub 面包屑导航下级数据数组"}], "methodReturn": {"methodReturnType": "string", "methodReturnBrief": "返回的HTML"}, "methodExample": null}, {"methodName": "btnCheckbox", "methodBrief": "返回后台勾选按钮的HTML", "methodIsDefinedIn": "--", "methodType": "公共 静态 方法", "methodPrefix": "public static", "methodParams": [{"methodParamInjection": "", "methodParamPrefix": "", "methodParamName": "name", "methodParamDefault": null, "methodParamType": "string", "methodParamBrief": "input的name"}, {"methodParamInjection": "", "methodParamPrefix": "", "methodParamName": "value", "methodParamDefault": "' '", "methodParamType": "string", "methodParamBrief": "input的value"}, {"methodParamInjection": "", "methodParamPrefix": "", "methodParamName": "class", "methodParamDefault": "' '", "methodParamType": "string", "methodParamBrief": "类名"}, {"methodParamInjection": "", "methodParamPrefix": "", "methodParamName": "disabled", "methodParamDefault": "0", "methodParamType": "integer", "methodParamBrief": "input的disabled（是否不可用的）"}, {"methodParamInjection": "", "methodParamPrefix": "", "methodParamName": "checked", "methodParamDefault": "0", "methodParamType": "integer", "methodParamBrief": "input的checked（是否选中）"}], "methodReturn": {"methodReturnType": "string", "methodReturnBrief": "返回的HTML"}, "methodExample": null}, {"methodName": "seoBoxHtml", "methodBrief": "返回后台公共SEO编辑模块的HTML", "methodIsDefinedIn": "--", "methodType": "公共 静态 方法", "methodPrefix": "public static", "methodParams": [{"methodParamInjection": "", "methodParamPrefix": "", "methodParamName": "row", "methodParamDefault": null, "methodParamType": "array", "methodParamBrief": "SEO数据"}], "methodReturn": {"methodReturnType": "string", "methodReturnBrief": "返回的HTML"}, "methodExample": null}, {"methodName": "fixedRightSeoBoxHtml", "methodBrief": "返回后台右侧弹窗内的SEO关键词编辑表单的HTML", "methodIsDefinedIn": "--", "methodType": "公共 静态 方法", "methodPrefix": "public static", "methodParams": [{"methodParamInjection": "", "methodParamPrefix": "", "methodParamName": "<PERSON><PERSON>", "methodParamDefault": null, "methodParamType": "array", "methodParamBrief": "需要编辑的SEO数据"}], "methodReturn": {"methodReturnType": "string", "methodReturnBrief": "返回的HTML"}, "methodExample": null}, {"methodName": "boxOptionButton", "methodBrief": "返回后台多功能选项按钮的HTML", "methodIsDefinedIn": "--", "methodType": "公共 静态 方法", "methodPrefix": "public static", "methodParams": [{"methodParamInjection": "", "methodParamPrefix": "", "methodParamName": "name", "methodParamDefault": null, "methodParamType": "string", "methodParamBrief": "显示名称和input的name"}, {"methodParamInjection": "", "methodParamPrefix": "", "methodParamName": "value", "methodParamDefault": null, "methodParamType": "string", "methodParamBrief": "input的value"}, {"methodParamInjection": "", "methodParamPrefix": "", "methodParamName": "type", "methodParamDefault": null, "methodParamType": "string", "methodParamBrief": "类型，一般用左标识"}, {"methodParamInjection": "", "methodParamPrefix": "", "methodParamName": "isCurrent", "methodParamDefault": "0", "methodParamType": "integer", "methodParamBrief": "是否勾选选中:<br/>0 : 不勾选 1 : 勾选"}, {"methodParamInjection": "", "methodParamPrefix": "", "methodParamName": "isFixed", "methodParamDefault": "0", "methodParamType": "integer", "methodParamBrief": "是否为固定选项:<br/>0 : 不固定 1 : 固定"}, {"methodParamInjection": "", "methodParamPrefix": "", "methodParamName": "isDelete", "methodParamDefault": "0", "methodParamType": "integer", "methodParamBrief": "是否能删除选项:<br/>0 : 不能删除 1 : 能删除"}, {"methodParamInjection": "", "methodParamPrefix": "", "methodParamName": "icon_txt", "methodParamDefault": "' '", "methodParamType": "string", "methodParamBrief": "图标文案"}], "methodReturn": {"methodReturnType": "string", "methodReturnBrief": "返回的HTML"}, "methodExample": null}, {"methodName": "tableScrollSticky", "methodBrief": "返回后台列表表格底部滚动条的HTML", "methodIsDefinedIn": "--", "methodType": "公共 静态 方法", "methodPrefix": "public static", "methodParams": null, "methodReturn": {"methodReturnType": "string", "methodReturnBrief": "返回的HTML"}, "methodExample": null}, {"methodName": "fixedBoxPopup", "methodBrief": "返回后台固定中部页面弹窗的HTML", "methodIsDefinedIn": "--", "methodType": "公共 静态 方法", "methodPrefix": "public static", "methodParams": [{"methodParamInjection": "", "methodParamPrefix": "", "methodParamName": "config", "methodParamDefault": "[]", "methodParamType": "array", "methodParamBrief": "配置的数组 可传入:<br/>● class 类名<br/>● width 宽度 720<br/>● title 标题<br/>● content 内容"}], "methodReturn": {"methodReturnType": "string", "methodReturnBrief": "返回的HTML"}, "methodExample": null}, {"methodName": "filterSelect", "methodBrief": "返回后台筛选下拉的HTML，一般用于搜索栏右旁", "methodIsDefinedIn": "--", "methodType": "公共 静态 方法", "methodPrefix": "public static", "methodParams": [{"methodParamInjection": "", "methodParamPrefix": "", "methodParamName": "config", "methodParamDefault": "[]", "methodParamType": "array", "methodParamBrief": "配置的数组 可传入:<br/>● type 类型 radio : 单选 checkbox : 多选<br/>● inputName input的name<br/>● showName 显示的名称<br/>● optionList 选项的数据数组<br/>● checkedList 选中选项的数据数组"}], "methodReturn": {"methodReturnType": "string", "methodReturnBrief": "返回的HTML"}, "methodExample": null}]}