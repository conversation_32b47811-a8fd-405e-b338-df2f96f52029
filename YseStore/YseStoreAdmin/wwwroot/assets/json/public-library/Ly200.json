{"classPrefix": "Common\\helps", "classFullName": "Common\\helps\\Ly200", "classBrief": "Ly200 提供了可以在应用程序中使用的Mailchimp相关邮件功能。", "classExtends": "无", "availableVersion": "5.0010+", "sourceCode": "http://git.shop5.ueeshop.com/developer/ueeshop5.0/common/helps/Ly200.php", "params": [], "methods": [{"methodName": "getDomain", "methodBrief": "获取网站域名", "methodIsDefinedIn": "--", "methodType": "公共 静态 方法", "methodPrefix": "public static", "methodParams": [{"methodParamInjection": "", "methodParamPrefix": "", "methodParamName": "protocol", "methodParamDefault": 1, "methodParamType": "integer", "methodParamBrief": "是否携带上协议"}, {"methodParamInjection": "", "methodParamPrefix": "", "methodParamName": "type", "methodParamDefault": 0, "methodParamType": "integer", "methodParamBrief": "有以下取值情况：<br>● 0：返回获取网站的正式域名 <br>● 1：返回获取网站的临时域名 <br>● 2：当前打开的域名，不以域名绑定的域名为标准，主要目的是为了PayPal商家授权的时候，请求的链接是网站的实际域名"}], "methodReturn": {"methodReturnType": "string", "methodReturnBrief": "网站域名"}, "methodThrows": null, "methodExample": ""}, {"methodName": "ip", "methodBrief": "IP地址转换为地理地址", "methodIsDefinedIn": "--", "methodType": "公共 静态 方法", "methodPrefix": "public static", "methodParams": [{"methodParamInjection": "", "methodParamPrefix": "", "methodParamName": "ip", "methodParamDefault": null, "methodParamType": "string|array", "methodParamBrief": "IP字符串 或者 IP 数组"}, {"methodParamInjection": "", "methodParamPrefix": "", "methodParamName": "onlyCountry", "methodParamDefault": "false", "methodParamType": "boolean", "methodParamBrief": "仅显示国家"}], "methodReturn": {"methodReturnType": "string|array", "methodReturnBrief": "物理地址字符串 或者 数组"}, "methodThrows": null, "methodExample": ""}, {"methodName": "getIp", "methodBrief": "获取浏览者IP", "methodIsDefinedIn": "--", "methodType": "公共 静态 方法", "methodPrefix": "public static", "methodParams": [{"methodParamInjection": "", "methodParamPrefix": "", "methodParamName": "istry", "methodParamDefault": 0, "methodParamType": "integer", "methodParamBrief": "是否代理"}], "methodReturn": {"methodReturnType": "string", "methodReturnBrief": "IP4"}, "methodThrows": null, "methodExample": ""}, {"methodName": "getUeeshopWebGetData", "methodBrief": "获取统计数据", "methodIsDefinedIn": "--", "methodType": "公共 静态 方法", "methodPrefix": "public static", "methodParams": [], "methodReturn": {"methodReturnType": "array", "methodReturnBrief": "统计数据"}, "methodThrows": null, "methodExample": ""}, {"methodName": "e<PERSON><PERSON>", "methodBrief": "将数据转成Json，并输出", "methodIsDefinedIn": "--", "methodType": "公共 静态 方法", "methodPrefix": "public static", "methodParams": [{"methodParamInjection": "", "methodParamPrefix": "", "methodParamName": "msg", "methodParamDefault": "''", "methodParamType": "string|array", "methodParamBrief": "返回的数据"}, {"methodParamInjection": "", "methodParamPrefix": "", "methodParamName": "ret", "methodParamDefault": 0, "methodParamType": "integer", "methodParamBrief": "返回的状态"}, {"methodParamInjection": "", "methodParamPrefix": "", "methodParamName": "exit", "methodParamDefault": 1, "methodParamType": "integer", "methodParamBrief": "是否直接退出程序"}], "methodReturn": null, "methodThrows": null, "methodExample": ""}, {"methodName": "password", "methodBrief": "密码加密", "methodIsDefinedIn": "--", "methodType": "公共 静态 方法", "methodPrefix": "public static", "methodParams": [{"methodParamInjection": "", "methodParamPrefix": "", "methodParamName": "password", "methodParamDefault": null, "methodParamType": "string", "methodParamBrief": "源密码"}], "methodReturn": {"methodReturnType": "string", "methodReturnBrief": "加密后的密码"}, "methodThrows": null, "methodExample": ""}, {"methodName": "api", "methodBrief": "调用 API", "methodIsDefinedIn": "--", "methodType": "公共 静态 方法", "methodPrefix": "public static", "methodParams": [{"methodParamInjection": "", "methodParamPrefix": "", "methodParamName": "data", "methodParamDefault": null, "methodParamType": "array", "methodParamBrief": "请求参数"}, {"methodParamInjection": "", "methodParamPrefix": "", "methodParamName": "key", "methodParamDefault": null, "methodParamType": "string", "methodParamBrief": "密钥"}, {"methodParamInjection": "", "methodParamPrefix": "", "methodParamName": "url", "methodParamDefault": null, "methodParamType": "string", "methodParamBrief": "请求地址"}], "methodReturn": {"methodReturnType": "mixed", "methodReturnBrief": "返回结果如果是数组，则表示成功，非数组，则是错误的提示语"}, "methodThrows": null, "methodExample": ""}, {"methodName": "sign", "methodBrief": "获取签名", "methodIsDefinedIn": "--", "methodType": "公共 静态 方法", "methodPrefix": "public static", "methodParams": [{"methodParamInjection": "", "methodParamPrefix": "", "methodParamName": "data", "methodParamDefault": null, "methodParamType": "array", "methodParamBrief": "请求参数"}, {"methodParamInjection": "", "methodParamPrefix": "", "methodParamName": "key", "methodParamDefault": null, "methodParamType": "string", "methodParamBrief": "密钥"}], "methodReturn": {"methodReturnType": "string", "methodReturnBrief": "签名"}, "methodThrows": null, "methodExample": ""}, {"methodName": "appkey", "methodBrief": "获取APP的秘钥", "methodIsDefinedIn": "--", "methodType": "公共 静态 方法", "methodPrefix": "public static", "methodParams": [{"methodParamInjection": "", "methodParamPrefix": "", "methodParamName": "ApiName", "methodParamDefault": "''", "methodParamType": "string", "methodParamBrief": "API名称"}], "methodReturn": {"methodReturnType": "string", "methodReturnBrief": "秘钥"}, "methodThrows": null, "methodExample": ""}, {"methodName": "curl", "methodBrief": "请求curl", "methodIsDefinedIn": "--", "methodType": "公共 静态 方法", "methodPrefix": "public static", "methodParams": [{"methodParamInjection": "", "methodParamPrefix": "", "methodParamName": "url", "methodParamDefault": "''", "methodParamType": "string", "methodParamBrief": "请求地址"}, {"methodParamInjection": "", "methodParamPrefix": "", "methodParamName": "post", "methodParamDefault": "''", "methodParamType": "string|array", "methodParamBrief": "请求参数"}, {"methodParamInjection": "", "methodParamPrefix": "", "methodParamName": "referer", "methodParamDefault": "''", "methodParamType": "string", "methodParamBrief": "来源Url"}, {"methodParamInjection": "", "methodParamPrefix": "", "methodParamName": "curl_opt", "methodParamDefault": "[]", "methodParamType": "array", "methodParamBrief": "CURL的选项"}, {"methodParamInjection": "", "methodParamPrefix": "", "methodParamName": "return_cookie", "methodParamDefault": "false", "methodParamType": "boolean", "methodParamBrief": "是否返回<PERSON>ie"}], "methodReturn": {"methodReturnType": "mixed", "methodReturnBrief": "CURL响应数据"}, "methodThrows": null, "methodExample": ""}, {"methodName": "queryString", "methodBrief": "过滤Url参数", "methodIsDefinedIn": "--", "methodType": "公共 静态 方法", "methodPrefix": "public static", "methodParams": [{"methodParamInjection": "", "methodParamPrefix": "", "methodParamName": "un", "methodParamDefault": "''", "methodParamType": "string|array", "methodParamBrief": "需要过滤的参数"}], "methodReturn": {"methodReturnType": "string", "methodReturnBrief": "过滤后拼接的Url"}, "methodThrows": null, "methodExample": ""}, {"methodName": "getQueryString", "methodBrief": "重新组织伪静态参数", "methodIsDefinedIn": "--", "methodType": "公共 静态 方法", "methodPrefix": "public static", "methodParams": [{"methodParamInjection": "", "methodParamPrefix": "", "methodParamName": "str", "methodParamDefault": null, "methodParamType": "string", "methodParamBrief": "源Url"}], "methodReturn": {"methodReturnType": "string", "methodReturnBrief": "结果Url"}, "methodThrows": null, "methodExample": ""}, {"methodName": "ueeshopWebGetData", "methodBrief": "获取网站统计文件中的数据", "methodIsDefinedIn": "--", "methodType": "公共 静态 方法", "methodPrefix": "public static", "methodParams": [], "methodReturn": {"methodReturnType": "array", "methodReturnBrief": "统计数据"}, "methodThrows": null, "methodExample": ""}, {"methodName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "methodBrief": "生成缓存文件路径", "methodIsDefinedIn": "--", "methodType": "公共 静态 方法", "methodPrefix": "public static", "methodParams": [{"methodParamInjection": "", "methodParamPrefix": "", "methodParamName": "theme", "methodParamDefault": "''", "methodParamType": "string", "methodParamBrief": "风格txxx"}, {"methodParamInjection": "", "methodParamPrefix": "", "methodParamName": "root", "methodParamDefault": "true", "methodParamType": "boolean", "methodParamBrief": "是否显示绝对路径"}, {"methodParamInjection": "", "methodParamPrefix": "", "methodParamName": "isLang", "methodParamDefault": "true", "methodParamType": "boolean", "methodParamBrief": "是否显示语言"}], "methodReturn": {"methodReturnType": "string", "methodReturnBrief": "结果Url"}, "methodThrows": null, "methodExample": ""}, {"methodName": "getSizeImg", "methodBrief": "输入尺寸和路径，显示对应尺寸的缩略图路径", "methodIsDefinedIn": "--", "methodType": "公共 静态 方法", "methodPrefix": "public static", "methodParams": [{"methodParamInjection": "", "methodParamPrefix": "", "methodParamName": "filepath", "methodParamDefault": null, "methodParamType": "string", "methodParamBrief": "文件路径"}, {"methodParamInjection": "", "methodParamPrefix": "", "methodParamName": "size", "methodParamDefault": null, "methodParamType": "string", "methodParamBrief": "尺寸，宽x高"}], "methodReturn": {"methodReturnType": "string", "methodReturnBrief": "对应尺寸的缩略图路径"}, "methodThrows": null, "methodExample": ""}, {"methodName": "strToUrl", "methodBrief": "字符串转换成合法的url路径", "methodIsDefinedIn": "--", "methodType": "公共 静态 方法", "methodPrefix": "public static", "methodParams": [{"methodParamInjection": "", "methodParamPrefix": "", "methodParamName": "str", "methodParamDefault": null, "methodParamType": "string", "methodParamBrief": "原字符串"}], "methodReturn": {"methodReturnType": "string", "methodReturnBrief": "合法Url字符串"}, "methodThrows": null, "methodExample": ""}, {"methodName": "getUrl", "methodBrief": "根据数据记录 和 类型 获取网站的对应的Url", "methodIsDefinedIn": "--", "methodType": "公共 静态 方法", "methodPrefix": "public static", "methodParams": [{"methodParamInjection": "", "methodParamPrefix": "", "methodParamName": "row", "methodParamDefault": null, "methodParamType": "array", "methodParamBrief": "数据记录"}, {"methodParamInjection": "", "methodParamPrefix": "", "methodParamName": "type", "methodParamDefault": "'products_category'", "methodParamType": "string", "methodParamBrief": "类型"}, {"methodParamInjection": "", "methodParamPrefix": "", "methodParamName": "lang", "methodParamDefault": "''", "methodParamType": "string", "methodParamBrief": "语言"}], "methodReturn": {"methodReturnType": "string", "methodReturnBrief": "对应的Url"}, "methodThrows": null, "methodExample": ""}, {"methodName": "getNavData", "methodBrief": "获取导航数据【头部、底部】", "methodIsDefinedIn": "--", "methodType": "公共 静态 方法", "methodPrefix": "public static", "methodParams": [{"methodParamInjection": "", "methodParamPrefix": "", "methodParamName": "type", "methodParamDefault": "'nav'", "methodParamType": "string", "methodParamBrief": "类型,有以下取值 <br>● nav 头部导航 <br>● foot_nav 底部导航"}], "methodReturn": {"methodReturnType": "array", "methodReturnBrief": "返回导航数据数组"}, "methodThrows": null, "methodExample": ""}, {"methodName": "getNavDataAry", "methodBrief": "把导航数组划分成需要参数", "methodIsDefinedIn": "--", "methodType": "公共 静态 方法", "methodPrefix": "public static", "methodParams": [{"methodParamInjection": "", "methodParamPrefix": "", "methodParamName": "row", "methodParamDefault": null, "methodParamType": "array", "methodParamBrief": "导航数组数据"}], "methodReturn": {"methodReturnType": "array", "methodReturnBrief": "返回导航数据数组"}, "methodThrows": null, "methodExample": ""}, {"methodName": "isMobileClient", "methodBrief": "是否用手机访问", "methodIsDefinedIn": "--", "methodType": "公共 静态 方法", "methodPrefix": "public static", "methodParams": [{"methodParamInjection": "", "methodParamPrefix": "", "methodParamName": "type", "methodParamDefault": 0, "methodParamType": "integer", "methodParamBrief": "有以下取值: <br>● 0: 只允许手机打开手机版 <br>● 1: 电脑允许打开手机版"}], "methodReturn": {"methodReturnType": "integer", "methodReturnBrief": "返回以下: <br>● 0 非移动端访问 <br>● 1 移动端访问"}, "methodThrows": null, "methodExample": ""}, {"methodName": "sendMail", "methodBrief": "邮件发送", "methodIsDefinedIn": "--", "methodType": "公共 静态 方法", "methodPrefix": "public static", "methodParams": [{"methodParamInjection": "", "methodParamPrefix": "", "methodParamName": "toEmail", "methodParamDefault": null, "methodParamType": "array|string", "methodParamBrief": "收件人邮箱(数组)"}, {"methodParamInjection": "", "methodParamPrefix": "", "methodParamName": "Msubject", "methodParamDefault": null, "methodParamType": "string", "methodParamBrief": "标题"}, {"methodParamInjection": "", "methodParamPrefix": "", "methodParamName": "<PERSON><PERSON>", "methodParamDefault": null, "methodParamType": "string", "methodParamBrief": "内容"}, {"methodParamInjection": "", "methodParamPrefix": "", "methodParamName": "type", "methodParamDefault": 0, "methodParamType": "integer", "methodParamBrief": "有以下取值: <br>● 0: 默认 <br>● 1: 群发 <br>● 2: 禁用smtp"}], "methodReturn": {"methodReturnType": "mixed", "methodReturnBrief": "以实际的调用的邮件应用返回数据为准"}, "methodThrows": null, "methodExample": ""}, {"methodName": "loadCdnContents", "methodBrief": "加载CDN链接", "methodIsDefinedIn": "--", "methodType": "公共 静态 方法", "methodPrefix": "public static", "methodParams": [{"methodParamInjection": "", "methodParamPrefix": "", "methodParamName": "html", "methodParamDefault": "''", "methodParamType": "string", "methodParamBrief": "源字符串"}, {"methodParamInjection": "", "methodParamPrefix": "", "methodParamName": "type", "methodParamDefault": 0, "methodParamType": "integer", "methodParamBrief": "替换类型 <br>● 0: 替换html 直接输出 <br> ● 1: 替换文件链接 直接返回 <br>● 2: 替换文件链接 返回内容"}, {"methodParamInjection": "", "methodParamPrefix": "", "methodParamName": "cdnHttp", "methodParamDefault": "false", "methodParamType": "boolean", "methodParamBrief": "CDN链接是否带协议"}], "methodReturn": {"methodReturnType": "string", "methodReturnBrief": "加载CDN链接之后的字符串"}, "methodThrows": null, "methodExample": ""}, {"methodName": "checkCdnPicture", "methodBrief": "获取图片的CDN链接", "methodIsDefinedIn": "--", "methodType": "公共 静态 方法", "methodPrefix": "public static", "methodParams": [{"methodParamInjection": "", "methodParamPrefix": "", "methodParamName": "url", "methodParamDefault": null, "methodParamType": "string", "methodParamBrief": "图片链接"}, {"methodParamInjection": "", "methodParamPrefix": "", "methodParamName": "domain", "methodParamDefault": "false", "methodParamType": "boolean", "methodParamBrief": "没有CDN的时候 是否加上域名"}, {"methodParamInjection": "", "methodParamPrefix": "", "methodParamName": "http", "methodParamDefault": "false", "methodParamType": "boolean", "methodParamBrief": "使用CDN的时候 是否加上协议"}], "methodReturn": {"methodReturnType": "string", "methodReturnBrief": "图片的CDN链接"}, "methodThrows": null, "methodExample": ""}, {"methodName": "checkUrlFile", "methodBrief": "返回URL编译后的文件名称", "methodIsDefinedIn": "--", "methodType": "公共 静态 方法", "methodPrefix": "public static", "methodParams": [{"methodParamInjection": "", "methodParamPrefix": "", "methodParamName": "url", "methodParamDefault": "''", "methodParamType": "string", "methodParamBrief": "源Url"}, {"methodParamInjection": "", "methodParamPrefix": "", "methodParamName": "domain", "methodParamDefault": "false", "methodParamType": "boolean", "methodParamBrief": "是否包含域名"}, {"methodParamInjection": "", "methodParamPrefix": "", "methodParamName": "http", "methodParamDefault": "false", "methodParamType": "boolean", "methodParamBrief": "是否加上协议"}], "methodReturn": {"methodReturnType": "string", "methodReturnBrief": "文件名称"}, "methodThrows": null, "methodExample": ""}, {"methodName": "sendSms", "methodBrief": "发送短信", "methodIsDefinedIn": "--", "methodType": "公共 静态 方法", "methodPrefix": "public static", "methodParams": [{"methodParamInjection": "", "methodParamPrefix": "", "methodParamName": "mobilephone", "methodParamDefault": null, "methodParamType": "string", "methodParamBrief": "接收短信的手机号码"}, {"methodParamInjection": "", "methodParamPrefix": "", "methodParamName": "sms", "methodParamDefault": null, "methodParamType": "array", "methodParamBrief": "短信模板为：【%s，订单号：%s】, 此处应该为数组, 即 <br>● ['您的网站有新的订单', '16120609460686']"}, {"methodParamInjection": "", "methodParamPrefix": "", "methodParamName": "TemplateId", "methodParamDefault": 1, "methodParamType": "integer", "methodParamBrief": "指定模板Id"}], "methodReturn": {"methodReturnType": "mixed", "methodReturnBrief": "调用短信接口返回的数据"}, "methodThrows": null, "methodExample": ""}, {"methodName": "thirdApiLog", "methodBrief": "发送短信", "methodIsDefinedIn": "--", "methodType": "公共 静态 方法", "methodPrefix": "public static", "methodParams": [{"methodParamInjection": "", "methodParamPrefix": "", "methodParamName": "Data", "methodParamDefault": null, "methodParamType": "array", "methodParamBrief": "接收的数据 <br>● TId 需要修改的日志记录主键 <br>● Status 接口调用状态 【0 未处理 1 连接失败 2 失败 3 成功】 <br>● ReturnData 返回的数据 Json 格式 <br>● Category 接口类型 <br>● ClassName 接口调用方法 <br>● SubmitData 提交的数据 Json 格式"}, {"methodParamInjection": "", "methodParamPrefix": "", "methodParamName": "Type", "methodParamDefault": "false", "methodParamType": "boolean", "methodParamBrief": "是否是更新"}], "methodReturn": {"methodReturnType": "void|integer", "methodReturnBrief": "仅添加有返回值，返回主键"}, "methodThrows": null, "methodExample": ""}, {"methodName": "getCountryWhere", "methodBrief": "获取可用国家的条件", "methodIsDefinedIn": "--", "methodType": "公共 静态 方法", "methodPrefix": "public static", "methodParams": [], "methodReturn": {"methodReturnType": "array", "methodReturnBrief": "用于Sql的条件"}, "methodThrows": null, "methodExample": ""}, {"methodName": "formSelect", "methodBrief": "生成下拉表单", "methodIsDefinedIn": "--", "methodType": "公共 静态 方法", "methodPrefix": "public static", "methodParams": [{"methodParamInjection": "", "methodParamPrefix": "", "methodParamName": "data", "methodParamDefault": null, "methodParamType": "array", "methodParamBrief": "选项的数据"}, {"methodParamInjection": "", "methodParamPrefix": "", "methodParamName": "name", "methodParamDefault": null, "methodParamType": "string", "methodParamBrief": "Select 的名称"}, {"methodParamInjection": "", "methodParamPrefix": "", "methodParamName": "selected<PERSON><PERSON><PERSON>", "methodParamDefault": "''", "methodParamType": "string", "methodParamBrief": "当前选中的选项值"}, {"methodParamInjection": "", "methodParamPrefix": "", "methodParamName": "field", "methodParamDefault": "''", "methodParamType": "string", "methodParamBrief": "选项文本内容对应的字段，为空时文本值取数组 $data 的值"}, {"methodParamInjection": "", "methodParamPrefix": "", "methodParamName": "key", "methodParamDefault": "''", "methodParamType": "string", "methodParamBrief": "选项value的取值，为空时value取数组 $data 的键"}, {"methodParamInjection": "", "methodParamPrefix": "", "methodParamName": "index", "methodParamDefault": "''", "methodParamType": "string", "methodParamBrief": "第一个选项的文本内容，一般是用于输出 -- 请选择 -- 之类提示"}, {"methodParamInjection": "", "methodParamPrefix": "", "methodParamName": "attr", "methodParamDefault": "''", "methodParamType": "string", "methodParamBrief": "Select 的自定义属性"}], "methodReturn": {"methodReturnType": "string", "methodReturnBrief": "生成下拉表单Html"}, "methodThrows": null, "methodExample": ""}, {"methodName": "getLanguagePack", "methodBrief": "获取语言包信息", "methodIsDefinedIn": "--", "methodType": "公共 静态 方法", "methodPrefix": "public static", "methodParams": [{"methodParamInjection": "", "methodParamPrefix": "", "methodParamName": "lang", "methodParamDefault": "''", "methodParamType": "string", "methodParamBrief": "语言"}], "methodReturn": {"methodReturnType": "array", "methodReturnBrief": "语言数组"}, "methodThrows": null, "methodExample": ""}]}