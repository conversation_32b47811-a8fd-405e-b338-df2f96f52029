{"classPrefix": "Common\\helps", "classFullName": "Common\\helps\\HelpsString", "classBrief": "HelpsString 提供了字符的相关处理", "classExtends": "无", "availableVersion": "5.0010+", "sourceCode": "http://git.shop5.ueeshop.com/developer/ueeshop5.0/common/helps/HelpsString.php", "params": [], "methods": [{"methodName": "aryFormat", "methodBrief": "根据 对应的 格式，将数组或者字符串格式化。并返回对应的字符串。主要用于Mysql查询", "methodIsDefinedIn": "--", "methodType": "公共 静态 方法", "methodPrefix": "public static", "methodParams": [{"methodParamInjection": "", "methodParamPrefix": "", "methodParamName": "data", "methodParamDefault": null, "methodParamType": "array | string", "methodParamBrief": "源数组 | 源字符串"}, {"methodParamInjection": "", "methodParamPrefix": "", "methodParamName": "return", "methodParamDefault": 0, "methodParamType": "integer", "methodParamBrief": "定义应该从结果中返回的数据规则。 具体规则如下：\r\n● 0 字符串。\r\n● 1 数组。\r\n● 2 in查询语句\r\n● 3 or查询语句\r\n● 4 返回第一个值"}, {"methodParamInjection": "", "methodParamPrefix": "", "methodParamName": "unset", "methodParamDefault": "''", "methodParamType": "string", "methodParamBrief": "从源数组 | 源字符串 中过滤字符串，配合$explode_char 可将过滤字符串转为过滤数组"}, {"methodParamInjection": "", "methodParamPrefix": "", "methodParamName": "explode_char", "methodParamDefault": "','", "methodParamType": "string", "methodParamBrief": "仅 $data 为源字符串，或者 $unset 不为空值时有效，用于对 源字符串 和 $unset 分割字符串转为数组"}, {"methodParamInjection": "", "methodParamPrefix": "", "methodParamName": "implode_char", "methodParamDefault": "','", "methodParamType": "string | array", "methodParamBrief": "仅 $return 为1时有效，用于对结果数组转化为字符串的分割子字符串"}], "methodReturn": {"methodReturnType": "string", "methodReturnBrief": "根据 对应的 格式，将数组或者字符串格式化。并返回对应的字符串"}, "methodExample": null}, {"methodName": "aryFormatExt", "methodBrief": "判断字符串是否为空", "methodIsDefinedIn": "--", "methodType": "公共 静态 方法", "methodPrefix": "public static", "methodParams": [{"methodParamInjection": "", "methodParamPrefix": "", "methodParamName": "v", "methodParamDefault": null, "methodParamType": "string", "methodParamBrief": "需要判断的字符串"}], "methodReturn": {"methodReturnType": "boolean", "methodReturnBrief": "返回的布尔值"}, "methodExample": null}, {"methodName": "strCode", "methodBrief": "把字符串或数组的值进行PHP函数处理", "methodIsDefinedIn": "--", "methodType": "公共 静态 方法", "methodPrefix": "public static", "methodParams": [{"methodParamInjection": "", "methodParamPrefix": "", "methodParamName": "data", "methodParamDefault": null, "methodParamType": "string | array", "methodParamBrief": "需要处理的字符串或数组"}, {"methodParamInjection": "", "methodParamPrefix": "", "methodParamName": "fun", "methodParamDefault": "htmlspecialchars", "methodParamType": "string", "methodParamBrief": "PHP处理函数"}], "methodReturn": {"methodReturnType": "string | array", "methodReturnBrief": "被处理后的字符串或数组"}, "methodExample": null}, {"methodName": "randCode", "methodBrief": "生成随机数字", "methodIsDefinedIn": "--", "methodType": "公共 静态 方法", "methodPrefix": "public static", "methodParams": [{"methodParamInjection": "", "methodParamPrefix": "", "methodParamName": "length", "methodParamDefault": "10", "methodParamType": "integer", "methodParamBrief": "随机数字的长度"}], "methodReturn": {"methodReturnType": "string", "methodReturnBrief": "生成后的随机数字"}, "methodExample": null}, {"methodName": "keywordsFilter", "methodBrief": "过滤敏感词", "methodIsDefinedIn": "--", "methodType": "公共 静态 方法", "methodPrefix": "public static", "methodParams": [{"methodParamInjection": "", "methodParamPrefix": "", "methodParamName": "ary", "methodParamDefault": "[]", "methodParamType": "array", "methodParamBrief": "源数据（被检查的数据）"}], "methodReturn": null, "methodExample": null}, {"methodName": "attrDecode", "methodBrief": "格式化产品属性内容里面的双引号", "methodIsDefinedIn": "--", "methodType": "公共 静态 方法", "methodPrefix": "public static", "methodParams": [{"methodParamInjection": "", "methodParamPrefix": "", "methodParamName": "str", "methodParamDefault": null, "methodParamType": "string", "methodParamBrief": "属性内容数据"}], "methodReturn": {"methodReturnType": "string", "methodReturnBrief": "被处理后的属性内容数据"}, "methodExample": null}, {"methodName": "strColor", "methodBrief": "颜色标签", "methodIsDefinedIn": "--", "methodType": "公共 静态 方法", "methodPrefix": "public static", "methodParams": [{"methodParamInjection": "", "methodParamPrefix": "", "methodParamName": "str", "methodParamDefault": "' '", "methodParamType": "string", "methodParamBrief": "内容"}, {"methodParamInjection": "", "methodParamPrefix": "", "methodParamName": "key", "methodParamDefault": 0, "methodParamType": "integer", "methodParamBrief": "状态码"}, {"methodParamInjection": "", "methodParamPrefix": "", "methodParamName": "return_type", "methodParamDefault": 0, "methodParamType": "integer", "methodParamBrief": "返回类型<br/>0 : 返回HTML<br/>1 : 返回类名"}], "methodReturn": {"methodReturnType": "string", "methodReturnBrief": "返回的HTML或类名"}, "methodExample": null}, {"methodName": "strCrypt", "methodBrief": "字符串加解密", "methodIsDefinedIn": "--", "methodType": "公共 静态 方法", "methodPrefix": "public static", "methodParams": [{"methodParamInjection": "", "methodParamPrefix": "", "methodParamName": "str", "methodParamDefault": null, "methodParamType": "string", "methodParamBrief": "需要处理的内容数据"}, {"methodParamInjection": "", "methodParamPrefix": "", "methodParamName": "action", "methodParamDefault": "encrypt", "methodParamType": "string", "methodParamBrief": "处理类型:<br/>encrypt : 加密<br/>decrypt : 解密"}, {"methodParamInjection": "", "methodParamPrefix": "", "methodParamName": "key", "methodParamDefault": "www-ly200-com", "methodParamType": "string", "methodParamBrief": "加密标识"}], "methodReturn": {"methodReturnType": "string", "methodReturnBrief": "返回加解密后的字符串"}, "methodExample": null}, {"methodName": "strStr", "methodBrief": "查找字符串在另一字符串中的是否第一次出现", "methodIsDefinedIn": "--", "methodType": "公共 静态 方法", "methodPrefix": "public static", "methodParams": [{"methodParamInjection": "", "methodParamPrefix": "", "methodParamName": "str", "methodParamDefault": null, "methodParamType": "string", "methodParamBrief": "查找字符串"}, {"methodParamInjection": "", "methodParamPrefix": "", "methodParamName": "array", "methodParamDefault": null, "methodParamType": "array", "methodParamBrief": "另一字符串数组"}], "methodReturn": {"methodReturnType": "boolean", "methodReturnBrief": "返回的布尔值"}, "methodExample": null}, {"methodName": "format", "methodBrief": "格式化文本", "methodIsDefinedIn": "--", "methodType": "公共 静态 方法", "methodPrefix": "public static", "methodParams": [{"methodParamInjection": "", "methodParamPrefix": "", "methodParamName": "str", "methodParamDefault": null, "methodParamType": "string", "methodParamBrief": "需要被格式化的文本"}], "methodReturn": {"methodReturnType": "string", "methodReturnBrief": "被格式化后的文本"}, "methodExample": null}, {"methodName": "randomUser", "methodBrief": "生成随机用户名", "methodIsDefinedIn": "--", "methodType": "公共 静态 方法", "methodPrefix": "public static", "methodParams": null, "methodReturn": {"methodReturnType": "string", "methodReturnBrief": "随机的用户名"}, "methodExample": null}, {"methodName": "aryDelMin", "methodBrief": "把数据中小于$min的数据删除", "methodIsDefinedIn": "--", "methodType": "公共 静态 方法", "methodPrefix": "public static", "methodParams": [{"methodParamInjection": "", "methodParamPrefix": "", "methodParamName": "ary", "methodParamDefault": null, "methodParamType": "array", "methodParamBrief": "需要处理的数组"}, {"methodParamInjection": "", "methodParamPrefix": "", "methodParamName": "min", "methodParamDefault": "0", "methodParamType": "integer", "methodParamBrief": "界限值"}, {"methodParamInjection": "", "methodParamPrefix": "", "methodParamName": "sort", "methodParamDefault": "0", "methodParamType": "integer", "methodParamBrief": "排序类型:<br/>0 : sort（从小到大）<br/>1 : rsort（从大到小）"}], "methodReturn": {"methodReturnType": "array", "methodReturnBrief": "处理后的数组"}, "methodExample": null}, {"methodName": "filter<PERSON><PERSON>ji", "methodBrief": "过滤emoji表情", "methodIsDefinedIn": "--", "methodType": "公共 静态 方法", "methodPrefix": "public static", "methodParams": [{"methodParamInjection": "", "methodParamPrefix": "", "methodParamName": "str", "methodParamDefault": null, "methodParamType": "string", "methodParamBrief": "需要处理的数据"}], "methodReturn": {"methodReturnType": "string", "methodReturnBrief": "处理后的数据"}, "methodExample": null}, {"methodName": "ripTags", "methodBrief": "过滤HTML标签格式（一般用在手机版编辑器输出）", "methodIsDefinedIn": "--", "methodType": "公共 静态 方法", "methodPrefix": "public static", "methodParams": [{"methodParamInjection": "", "methodParamPrefix": "", "methodParamName": "string", "methodParamDefault": null, "methodParamType": "string", "methodParamBrief": "需要处理的内容"}, {"methodParamInjection": "", "methodParamPrefix": "", "methodParamName": "repStr", "methodParamDefault": "' '", "methodParamType": "string", "methodParamBrief": "需过滤后替换的字符串"}], "methodReturn": {"methodReturnType": "string", "methodReturnBrief": "处理后的数据"}, "methodExample": null}, {"methodName": "str<PERSON><PERSON>", "methodBrief": "输出固定长度内容（适用中文）", "methodIsDefinedIn": "--", "methodType": "公共 静态 方法", "methodPrefix": "public static", "methodParams": [{"methodParamInjection": "", "methodParamPrefix": "", "methodParamName": "str", "methodParamDefault": null, "methodParamType": "string", "methodParamBrief": "需要处理的内容"}, {"methodParamInjection": "", "methodParamPrefix": "", "methodParamName": "length", "methodParamDefault": null, "methodParamType": "integer", "methodParamBrief": "显示的长度"}, {"methodParamInjection": "", "methodParamPrefix": "", "methodParamName": "start", "methodParamDefault": "0", "methodParamType": "integer", "methodParamBrief": "开始计算的位置"}, {"methodParamInjection": "", "methodParamPrefix": "", "methodParamName": "replace", "methodParamDefault": "' '", "methodParamType": "string", "methodParamBrief": "超出需要替换的字符串"}], "methodReturn": {"methodReturnType": "string", "methodReturnBrief": "处理后的数据"}, "methodExample": null}, {"methodName": "strContentCrypt", "methodBrief": "内容加压解压", "methodIsDefinedIn": "--", "methodType": "公共 静态 方法", "methodPrefix": "public static", "methodParams": [{"methodParamInjection": "", "methodParamPrefix": "", "methodParamName": "str", "methodParamDefault": null, "methodParamType": "string", "methodParamBrief": "需要处理的内容"}, {"methodParamInjection": "", "methodParamPrefix": "", "methodParamName": "action", "methodParamDefault": "decrypt", "methodParamType": "string", "methodParamBrief": "处理的类型<br/>encode : 加压<br/>decrypt : 解压"}], "methodReturn": {"methodReturnType": "string", "methodReturnBrief": "处理后的数据"}, "methodExample": null}]}