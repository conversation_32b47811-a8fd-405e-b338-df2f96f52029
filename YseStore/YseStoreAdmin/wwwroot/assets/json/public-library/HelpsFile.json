{"classPrefix": "Common\\helps", "classFullName": "Common\\helps\\HelpsFile", "classBrief": "HelpsFile 提供了文件操作相关方法。", "classExtends": "无", "availableVersion": "5.0010+", "sourceCode": "http://git.shop5.ueeshop.com/developer/ueeshop5.0/common/helps/HelpsFile.php", "params": [], "methods": [{"methodName": "mkDir", "methodBrief": "建立目录", "methodIsDefinedIn": "--", "methodType": "公共 静态 方法", "methodPrefix": "public static", "methodParams": [{"methodParamInjection": "", "methodParamPrefix": "", "methodParamName": "dir", "methodParamDefault": null, "methodParamType": "string", "methodParamBrief": "目录路径"}], "methodReturn": {"methodReturnType": "string", "methodReturnBrief": "目录路径"}, "methodExample": null}, {"methodName": "delDir", "methodBrief": "删除文件夹", "methodIsDefinedIn": "--", "methodType": "公共 静态 方法", "methodPrefix": "public static", "methodParams": [{"methodParamInjection": "", "methodParamPrefix": "", "methodParamName": "dir", "methodParamDefault": null, "methodParamType": "string", "methodParamBrief": "目录路径"}], "methodReturn": {"methodReturnType": "boolean", "methodReturnBrief": "操作状态"}, "methodExample": null}, {"methodName": "writeFile", "methodBrief": "写文件", "methodIsDefinedIn": "--", "methodType": "公共 静态 方法", "methodPrefix": "public static", "methodParams": [{"methodParamInjection": "", "methodParamPrefix": "", "methodParamName": "saveDir", "methodParamDefault": null, "methodParamType": "string", "methodParamBrief": "写入目录"}, {"methodParamInjection": "", "methodParamPrefix": "", "methodParamName": "saveName", "methodParamDefault": null, "methodParamType": "string", "methodParamBrief": "文件名称"}, {"methodParamInjection": "", "methodParamPrefix": "", "methodParamName": "contents", "methodParamDefault": null, "methodParamType": "string", "methodParamBrief": "写入内容"}, {"methodParamInjection": "", "methodParamPrefix": "", "methodParamName": "efbbbf", "methodParamDefault": "0", "methodParamType": "string", "methodParamBrief": "EFBBBF编码"}], "methodReturn": {"methodReturnType": "boolean", "methodReturnBrief": "文件路径"}, "methodExample": null}, {"methodName": "delFile", "methodBrief": "删除文件", "methodIsDefinedIn": "--", "methodType": "公共 静态 方法", "methodPrefix": "public static", "methodParams": [{"methodParamInjection": "", "methodParamPrefix": "", "methodParamName": "file", "methodParamDefault": null, "methodParamType": "string", "methodParamBrief": "文件路径"}], "methodReturn": null, "methodExample": null}, {"methodName": "checkCache", "methodBrief": "检查缓存是否过期", "methodIsDefinedIn": "--", "methodType": "公共 静态 方法", "methodPrefix": "public static", "methodParams": [{"methodParamInjection": "", "methodParamPrefix": "", "methodParamName": "filepath", "methodParamDefault": null, "methodParamType": "string", "methodParamBrief": "缓存文件路径"}, {"methodParamInjection": "", "methodParamPrefix": "", "methodParamName": "isThemes", "methodParamDefault": "1", "methodParamType": "integer", "methodParamBrief": "是否是风格"}, {"methodParamInjection": "", "methodParamPrefix": "", "methodParamName": "cacheTimeout", "methodParamDefault": "0", "methodParamType": "integer", "methodParamBrief": "缓存时间/秒"}], "methodReturn": {"methodReturnType": "boolean", "methodReturnBrief": "缓存状态"}, "methodExample": null}, {"methodName": "getExtName", "methodBrief": "返回文件后辍名（小写）", "methodIsDefinedIn": "--", "methodType": "公共 静态 方法", "methodPrefix": "public static", "methodParams": [{"methodParamInjection": "", "methodParamPrefix": "", "methodParamName": "file", "methodParamDefault": "''", "methodParamType": "string", "methodParamBrief": "文件路径"}, {"methodParamInjection": "", "methodParamPrefix": "", "methodParamName": "excision", "methodParamDefault": "''", "methodParamType": "string", "methodParamBrief": "截取指定的字符串之前的字符串"}], "methodReturn": {"methodReturnType": "string", "methodReturnBrief": "文件后辍名"}, "methodExample": null}, {"methodName": "getBaseName", "methodBrief": "返回文件名", "methodIsDefinedIn": "--", "methodType": "公共 静态 方法", "methodPrefix": "public static", "methodParams": [{"methodParamInjection": "", "methodParamPrefix": "", "methodParamName": "file", "methodParamDefault": "''", "methodParamType": "string", "methodParamBrief": "文件路径"}], "methodReturn": {"methodReturnType": "string", "methodReturnBrief": "文件名"}, "methodExample": null}, {"methodName": "getDirName", "methodBrief": "返回文件目录路径", "methodIsDefinedIn": "--", "methodType": "公共 静态 方法", "methodPrefix": "public static", "methodParams": [{"methodParamInjection": "", "methodParamPrefix": "", "methodParamName": "file", "methodParamDefault": "''", "methodParamType": "string", "methodParamBrief": "文件路径"}], "methodReturn": {"methodReturnType": "string", "methodReturnBrief": "文件目录路径"}, "methodExample": null}, {"methodName": "getFileName", "methodBrief": "返回不包含后缀的文件名", "methodIsDefinedIn": "--", "methodType": "公共 静态 方法", "methodPrefix": "public static", "methodParams": [{"methodParamInjection": "", "methodParamPrefix": "", "methodParamName": "file", "methodParamDefault": "''", "methodParamType": "string", "methodParamBrief": "文件路径"}], "methodReturn": {"methodReturnType": "string", "methodReturnBrief": "不包含后缀的文件名"}, "methodExample": null}, {"methodName": "formatCsv", "methodBrief": "格式化csv数据，去掉数据中存在的英文逗号对分隔的影响", "methodIsDefinedIn": "--", "methodType": "公共 静态 方法", "methodPrefix": "public static", "methodParams": [{"methodParamInjection": "", "methodParamPrefix": "&", "methodParamName": "data", "methodParamDefault": null, "methodParamType": "array", "methodParamBrief": "需要格式化的数据"}], "methodReturn": {"methodReturnType": "array", "methodReturnBrief": "格式化后的数据"}, "methodExample": null}, {"methodName": "appendCsv", "methodBrief": "生成/追加的CSV文件", "methodIsDefinedIn": "--", "methodType": "公共 静态 方法", "methodPrefix": "public static", "methodParams": [{"methodParamInjection": "", "methodParamPrefix": "", "methodParamName": "filedir", "methodParamDefault": null, "methodParamType": "string", "methodParamBrief": "需要存放的文件夹路径"}, {"methodParamInjection": "", "methodParamPrefix": "", "methodParamName": "fileName", "methodParamDefault": null, "methodParamType": "string", "methodParamBrief": "文件名称"}, {"methodParamInjection": "", "methodParamPrefix": "", "methodParamName": "headArr", "methodParamDefault": null, "methodParamType": "array", "methodParamBrief": "表头"}, {"methodParamInjection": "", "methodParamPrefix": "", "methodParamName": "data", "methodParamDefault": null, "methodParamType": "array", "methodParamBrief": "数据，二维数组每个数组就是一行"}], "methodReturn": {"methodReturnType": "string|boolean", "methodReturnBrief": "$filePath CSV文件相对路径"}, "methodExample": null}, {"methodName": "downFile", "methodBrief": "下载文件", "methodIsDefinedIn": "--", "methodType": "公共 静态 方法", "methodPrefix": "public static", "methodParams": [{"methodParamInjection": "", "methodParamPrefix": "", "methodParamName": "filepath", "methodParamDefault": null, "methodParamType": "string", "methodParamBrief": "文件路径"}, {"methodParamInjection": "", "methodParamPrefix": "", "methodParamName": "saveName", "methodParamDefault": "''", "methodParamType": "string", "methodParamBrief": "下载时的文件名称"}], "methodReturn": null, "methodExample": null}, {"methodName": "fileUpload", "methodBrief": "上传文件", "methodIsDefinedIn": "--", "methodType": "公共 静态 方法", "methodPrefix": "public static", "methodParams": [{"methodParamInjection": "", "methodParamPrefix": "", "methodParamName": "up_file_name", "methodParamDefault": null, "methodParamType": "string", "methodParamBrief": "文件资源"}, {"methodParamInjection": "", "methodParamPrefix": "", "methodParamName": "save_dir", "methodParamDefault": null, "methodParamType": "string", "methodParamBrief": "保存目录"}, {"methodParamInjection": "", "methodParamPrefix": "", "methodParamName": "is_ckeditor", "methodParamDefault": "0", "methodParamType": "integer", "methodParamBrief": "是否是编辑框上传"}, {"methodParamInjection": "", "methodParamPrefix": "", "methodParamName": "is_html", "methodParamDefault": "1", "methodParamType": "integer", "methodParamBrief": "是否上传.html格式文件"}], "methodReturn": {"methodReturnType": "string", "methodReturnBrief": "上传后的文件路径"}, "methodExample": null}, {"methodName": "fileUploadSwf", "methodBrief": "上传图片", "methodIsDefinedIn": "--", "methodType": "公共 静态 方法", "methodPrefix": "public static", "methodParams": [{"methodParamInjection": "", "methodParamPrefix": "", "methodParamName": "save_dir", "methodParamDefault": null, "methodParamType": "string", "methodParamBrief": "保存目录"}, {"methodParamInjection": "", "methodParamPrefix": "", "methodParamName": "resize_ary", "methodParamDefault": "''", "methodParamType": "string", "methodParamBrief": "缩略图尺寸"}, {"methodParamInjection": "", "methodParamPrefix": "", "methodParamName": "AddPhoto", "methodParamDefault": "true", "methodParamType": "boolean", "methodParamBrief": "是否加入图片银行"}, {"methodParamInjection": "", "methodParamPrefix": "", "methodParamName": "is_water", "methodParamDefault": "0", "methodParamType": "integer", "methodParamBrief": "是否加水印"}], "methodReturn": {"methodReturnType": "string", "methodReturnBrief": "json格式的文件数据"}, "methodExample": null}, {"methodName": "createfileName", "methodBrief": "生成图片和文件的名称", "methodIsDefinedIn": "--", "methodType": "公共 静态 方法", "methodPrefix": "public static", "methodParams": [{"methodParamInjection": "", "methodParamPrefix": "", "methodParamName": "name", "methodParamDefault": null, "methodParamType": "string", "methodParamBrief": "名称"}, {"methodParamInjection": "", "methodParamPrefix": "", "methodParamName": "path", "methodParamDefault": null, "methodParamType": "string", "methodParamBrief": "路径"}], "methodReturn": {"methodReturnType": "string", "methodReturnBrief": "文件名称"}, "methodExample": null}, {"methodName": "photoTmpUpload", "methodBrief": "图片上传文件处理", "methodIsDefinedIn": "--", "methodType": "公共 静态 方法", "methodPrefix": "public static", "methodParams": [{"methodParamInjection": "", "methodParamPrefix": "", "methodParamName": "img", "methodParamDefault": null, "methodParamType": "string", "methodParamBrief": "图片路径"}, {"methodParamInjection": "", "methodParamPrefix": "", "methodParamName": "save_dir", "methodParamDefault": null, "methodParamType": "string", "methodParamBrief": "保存目录"}], "methodReturn": {"methodReturnType": "string", "methodReturnBrief": "新图片路径"}, "methodExample": null}, {"methodName": "photoAddItem", "methodBrief": "把图片加入图片银行", "methodIsDefinedIn": "--", "methodType": "公共 静态 方法", "methodPrefix": "public static", "methodParams": [{"methodParamInjection": "", "methodParamPrefix": "", "methodParamName": "imgPath", "methodParamDefault": null, "methodParamType": "string", "methodParamBrief": "图片路径"}, {"methodParamInjection": "", "methodParamPrefix": "", "methodParamName": "name", "methodParamDefault": "''", "methodParamType": "string", "methodParamBrief": "图片名称"}, {"methodParamInjection": "", "methodParamPrefix": "", "methodParamName": "isSystem", "methodParamDefault": "0", "methodParamType": "integer", "methodParamBrief": "系统图片类型，等于 0 即该图片又图片银行管理上传，非系统图片"}, {"methodParamInjection": "", "methodParamPrefix": "", "methodParamName": "cateId", "methodParamDefault": "0", "methodParamType": "integer", "methodParamBrief": "图片银行分类ID号"}], "methodReturn": {"methodReturnType": "string", "methodReturnBrief": "新图片路径"}, "methodExample": null}, {"methodName": "fileAddItem", "methodBrief": "文件管理，记录文件", "methodIsDefinedIn": "--", "methodType": "公共 静态 方法", "methodPrefix": "public static", "methodParams": [{"methodParamInjection": "", "methodParamPrefix": "", "methodParamName": "path", "methodParamDefault": null, "methodParamType": "string", "methodParamBrief": "文件路径"}, {"methodParamInjection": "", "methodParamPrefix": "", "methodParamName": "name", "methodParamDefault": "''", "methodParamType": "string", "methodParamBrief": "文件名称"}], "methodReturn": {"methodReturnType": "string", "methodReturnBrief": "新文件路径"}, "methodExample": null}, {"methodName": "getCSVFile", "methodBrief": "读取CSV文件", "methodIsDefinedIn": "--", "methodType": "公共 静态 方法", "methodPrefix": "public static", "methodParams": [{"methodParamInjection": "", "methodParamPrefix": "", "methodParamName": "path", "methodParamDefault": null, "methodParamType": "string", "methodParamBrief": "文件路径"}], "methodReturn": {"methodReturnType": "array", "methodReturnBrief": "读取到的数据"}, "methodExample": null}, {"methodName": "ImageCenterCrop", "methodBrief": "等比例裁剪居中图片", "methodIsDefinedIn": "--", "methodType": "公共 静态 方法", "methodPrefix": "public static", "methodParams": [{"methodParamInjection": "", "methodParamPrefix": "", "methodParamName": "source", "methodParamDefault": null, "methodParamType": "string", "methodParamBrief": "图片路径"}, {"methodParamInjection": "", "methodParamPrefix": "", "methodParamName": "width", "methodParamDefault": null, "methodParamType": "integer", "methodParamBrief": "生成的图片宽度"}, {"methodParamInjection": "", "methodParamPrefix": "", "methodParamName": "height", "methodParamDefault": null, "methodParamType": "integer", "methodParamBrief": "生成的图片高度"}], "methodReturn": {"methodReturnType": "string", "methodReturnBrief": "新图片路径"}, "methodExample": null}, {"methodName": "getImageSize", "methodBrief": "获取图片的宽度和高度", "methodIsDefinedIn": "--", "methodType": "公共 静态 方法", "methodPrefix": "public static", "methodParams": [{"methodParamInjection": "", "methodParamPrefix": "", "methodParamName": "path", "methodParamDefault": null, "methodParamType": "array", "methodParamBrief": "图片路径，数组的形式"}, {"methodParamInjection": "", "methodParamPrefix": "", "methodParamName": "type", "methodParamDefault": "photo", "methodParamType": "string", "methodParamBrief": "图片类型，可传入:<br>● photo 图片管理<br>● products 产品图片"}], "methodReturn": {"methodReturnType": "array", "methodReturnBrief": "图片的宽度和高度"}, "methodExample": null}]}