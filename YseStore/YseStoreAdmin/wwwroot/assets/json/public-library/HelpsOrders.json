{"classPrefix": "Common\\helps", "classFullName": "Common\\helps\\HelpsOrders", "classBrief": "HelpsOrders 提供了订单相关处理函数。", "classExtends": "无", "availableVersion": "5.0010+", "sourceCode": "http://git.shop5.ueeshop.com/developer/ueeshop5.0/common/helps/HelpsOrders.php", "params": [], "methods": [{"methodName": "ordersProductsUpdate", "methodBrief": "更新订单里面的产品库存", "methodIsDefinedIn": "--", "methodType": "公共 静态 方法", "methodPrefix": "public static", "methodParams": [{"methodParamInjection": "", "methodParamPrefix": "", "methodParamName": "OrderStatus", "methodParamDefault": null, "methodParamType": "integer", "methodParamBrief": "订单状态"}, {"methodParamInjection": "", "methodParamPrefix": "", "methodParamName": "ordersRow", "methodParamDefault": null, "methodParamType": "array", "methodParamBrief": "需要处理的订单数据"}, {"methodParamInjection": "", "methodParamPrefix": "", "methodParamName": "type", "methodParamDefault": "0", "methodParamType": "integer", "methodParamBrief": "处理类型 1:返还库存 0:减库存"}], "methodReturn": null, "methodExample": ""}, {"methodName": "ordersUserUpdate", "methodBrief": "处理会员数据 消费金额，会员等级", "methodIsDefinedIn": "--", "methodType": "公共 静态 方法", "methodPrefix": "public static", "methodParams": [{"methodParamInjection": "", "methodParamPrefix": "", "methodParamName": "OrderId", "methodParamDefault": null, "methodParamType": "integer", "methodParamBrief": "订单ID"}, {"methodParamInjection": "", "methodParamPrefix": "", "methodParamName": "type", "methodParamDefault": "'plus'", "methodParamType": "string", "methodParamBrief": "处理类型 plus:增加累计金额 less:扣除累计金额"}, {"methodParamInjection": "", "methodParamPrefix": "", "methodParamName": "refundType", "methodParamDefault": "'all'", "methodParamType": "string", "methodParamBrief": "退款类型 all:全款 partially:部分退款"}], "methodReturn": null, "methodExample": ""}, {"methodName": "ordersPrice", "methodBrief": "获取订单总价格", "methodIsDefinedIn": "--", "methodType": "公共 静态 方法", "methodPrefix": "public static", "methodParams": [{"methodParamInjection": "", "methodParamPrefix": "", "methodParamName": "Data", "methodParamDefault": null, "methodParamType": "array", "methodParamBrief": "订单数据"}, {"methodParamInjection": "", "methodParamPrefix": "", "methodParamName": "IsFee", "methodParamDefault": "0", "methodParamType": "integer", "methodParamBrief": "计算手续费 0 不计算 1 计算"}, {"methodParamInjection": "", "methodParamPrefix": "", "methodParamName": "IsManage", "methodParamDefault": "0", "methodParamType": "integer", "methodParamBrief": "后台货币 0 不计算 1 计算"}, {"methodParamInjection": "", "methodParamPrefix": "", "methodParamName": "replaceData", "methodParamDefault": "[]", "methodParamType": "array", "methodParamBrief": "替换参数 可传入:<br />● shippingPrice 运费<br />● lineItems 商品数据"}], "methodReturn": {"methodReturnType": "float", "methodReturnBrief": "订单总价格"}, "methodExample": ""}, {"methodName": "ordersProductPrice", "methodBrief": "获取订单产品总价格格式显示", "methodIsDefinedIn": "--", "methodType": "公共 静态 方法", "methodPrefix": "public static", "methodParams": [{"methodParamInjection": "", "methodParamPrefix": "", "methodParamName": "Data", "methodParamDefault": null, "methodParamType": "array", "methodParamBrief": "订单数据"}, {"methodParamInjection": "", "methodParamPrefix": "", "methodParamName": "Method", "methodParamDefault": "0", "methodParamType": "integer", "methodParamBrief": "显示格式 0 符号+价格 1 价格"}, {"methodParamInjection": "", "methodParamPrefix": "", "methodParamName": "IsManage", "methodParamDefault": "0", "methodParamType": "integer", "methodParamBrief": "后台货币 0 不计算 1 计算"}, {"methodParamInjection": "", "methodParamPrefix": "", "methodParamName": "replaceData", "methodParamDefault": "[]", "methodParamType": "array", "methodParamBrief": "替换参数 可传入:<br />● itemId 商品ID<br />● quantity 商品数量"}], "methodReturn": {"methodReturnType": "string|float", "methodReturnBrief": "订单产品总价格"}, "methodExample": ""}, {"methodName": "ordersProductPrice", "methodBrief": "根据订单号返回订单相关信息", "methodIsDefinedIn": "--", "methodType": "公共 静态 方法", "methodPrefix": "public static", "methodParams": [{"methodParamInjection": "", "methodParamPrefix": "", "methodParamName": "OId", "methodParamDefault": null, "methodParamType": "string", "methodParamBrief": "订单号"}], "methodReturn": {"methodReturnType": "array", "methodReturnBrief": "订单相关信息"}, "methodExample": ""}, {"methodName": "ordersShippingPrice", "methodBrief": "订单包裹价格格式显示", "methodIsDefinedIn": "--", "methodType": "公共 静态 方法", "methodPrefix": "public static", "methodParams": [{"methodParamInjection": "", "methodParamPrefix": "", "methodParamName": "Data", "methodParamDefault": null, "methodParamType": "array", "methodParamBrief": "订单数据"}, {"methodParamInjection": "", "methodParamPrefix": "", "methodParamName": "Method", "methodParamDefault": "0", "methodParamType": "integer", "methodParamBrief": "显示格式 0 符号+价格 1 价格"}, {"methodParamInjection": "", "methodParamPrefix": "", "methodParamName": "IsManage", "methodParamDefault": "0", "methodParamType": "integer", "methodParamBrief": "后台货币 0 不计算 1 计算"}], "methodReturn": {"methodReturnType": "string|float", "methodReturnBrief": "订单包裹价格"}, "methodExample": ""}, {"methodName": "getOrdersPackage", "methodBrief": "获取订单包裹信息", "methodIsDefinedIn": "--", "methodType": "公共 静态 方法", "methodPrefix": "public static", "methodParams": [{"methodParamInjection": "", "methodParamPrefix": "", "methodParamName": "OrderId", "methodParamDefault": null, "methodParamType": "integer", "methodParamBrief": "订单ID"}, {"methodParamInjection": "", "methodParamPrefix": "", "methodParamName": "Data", "methodParamDefault": "[]", "methodParamType": "array", "methodParamBrief": "订单数据"}], "methodReturn": {"methodReturnType": "array", "methodReturnBrief": "订单包裹信息<br />● Parent 主包裹信息<br />● Sub 拆分包裹信息[以Parent分类]<br />● Valid 主包裹的有效包裹<br />● ProInfo 包裹产品信息<br />● Count 包裹数量<br />● OvId 海外仓ID数组"}, "methodExample": ""}, {"methodName": "getPackageProinfo", "methodBrief": "获取订单产品信息", "methodIsDefinedIn": "--", "methodType": "公共 静态 方法", "methodPrefix": "public static", "methodParams": [{"methodParamInjection": "", "methodParamPrefix": "", "methodParamName": "OrderId", "methodParamDefault": null, "methodParamType": "integer", "methodParamBrief": "订单ID"}], "methodReturn": {"methodReturnType": "array", "methodReturnBrief": "订单产品信息,以LId为键"}, "methodExample": ""}, {"methodName": "writeOrderLog", "methodBrief": "生成订单日志", "methodIsDefinedIn": "--", "methodType": "公共 静态 方法", "methodPrefix": "public static", "methodParams": [{"methodParamInjection": "", "methodParamPrefix": "", "methodParamName": "IsAdmin", "methodParamDefault": null, "methodParamType": "integer", "methodParamBrief": "是否为管理员 0:买家 1:管理员 2:API 3:APP"}, {"methodParamInjection": "", "methodParamPrefix": "", "methodParamName": "UserId", "methodParamDefault": null, "methodParamType": "integer", "methodParamBrief": "会员ID或管理员ID"}, {"methodParamInjection": "", "methodParamPrefix": "", "methodParamName": "UserName", "methodParamDefault": null, "methodParamType": "string", "methodParamBrief": "会员名字或管理员名字"}, {"methodParamInjection": "", "methodParamPrefix": "", "methodParamName": "OrderId", "methodParamDefault": null, "methodParamType": "integer", "methodParamBrief": "订单ID"}, {"methodParamInjection": "", "methodParamPrefix": "", "methodParamName": "isTemp", "methodParamDefault": "0", "methodParamType": "integer", "methodParamBrief": "是否为临时订单"}, {"methodParamInjection": "", "methodParamPrefix": "", "methodParamName": "Log", "methodParamDefault": "''", "methodParamType": "string", "methodParamBrief": "记录日志标题"}, {"methodParamInjection": "", "methodParamPrefix": "", "methodParamName": "Logdata", "methodParamDefault": "''", "methodParamType": "string", "methodParamBrief": "记录日志数据"}, {"methodParamInjection": "", "methodParamPrefix": "", "methodParamName": "TId", "methodParamDefault": "''", "methodParamType": "integer", "methodParamBrief": "支付记录关联ID 表:orders_transaction_log"}], "methodReturn": null, "methodExample": ""}, {"methodName": "writeOrderLogData", "methodBrief": "获取需要显示的日志数据", "methodIsDefinedIn": "--", "methodType": "公共 静态 方法", "methodPrefix": "public static", "methodParams": [{"methodParamInjection": "", "methodParamPrefix": "", "methodParamName": "type", "methodParamDefault": null, "methodParamType": "string", "methodParamBrief": "日志类型 可传入：<br />● create 创建<br />● initiate 发起付款<br />● cancel 取消付款<br />● pending 待处理<br />● paid 付款<br />● shipped 发货<br />● changeShipped 更改包裹信息<br />● cancelDelivery 取消发货<br />● completed 完成<br />● refund 退款<br />● refundFail 退款(失败)<br />● voided 作废<br />● recallOrderReturn 弃单召回(买家进入网站)<br />● ordersUpdate 修改订单信息"}, {"methodParamInjection": "", "methodParamPrefix": "", "methodParamName": "data", "methodParamDefault": "[]", "methodParamType": "array", "methodParamBrief": "日志数据 可传入：<br />● name 操作人<br />● paymentMethod 支付方式<br />● paymentAmount 支付金额<br />● currency 货币<br />● person 负责人<br />● paymentStatus 支付状态<br />● operationType 操作类型<br />● errorMessage 错误信息<br />● proCount 产品数量<br />● deliveryMethod 物流方式<br />● trackingNumber 运单号<br />● shippingTime 发货时间<br />● remarks 备注<br />● totalAmount 订单总金额<br />● refundStatus 退款状态<br />● reason 原因<br />● discountAmount 优惠金额<br />● discountPrice 折扣价格<br />● tax 税费<br />● payAdditionalFee 手续费<br />● proData 产品信息 "}], "methodReturn": {"methodReturnType": "array", "methodReturnBrief": "日志数据"}, "methodExample": ""}, {"methodName": "ordersDetailPrice", "methodBrief": "订单各种价格格式显示", "methodIsDefinedIn": "--", "methodType": "公共 静态 方法", "methodPrefix": "public static", "methodParams": [{"methodParamInjection": "", "methodParamPrefix": "", "methodParamName": "Data", "methodParamDefault": null, "methodParamType": "array", "methodParamBrief": "订单数据"}, {"methodParamInjection": "", "methodParamPrefix": "", "methodParamName": "Method", "methodParamDefault": "0", "methodParamType": "integer", "methodParamBrief": "显示格式 0 符号+价格 1 价格"}, {"methodParamInjection": "", "methodParamPrefix": "", "methodParamName": "IsManage", "methodParamDefault": "0", "methodParamType": "integer", "methodParamBrief": "后台货币 0 不计算 1 计算"}, {"methodParamInjection": "", "methodParamPrefix": "", "methodParamName": "replaceData", "methodParamDefault": "[]", "methodParamType": "array", "methodParamBrief": "替换参数 可传入：<br />● shippingPrice 运费<br />● lineItems 商品数据"}], "methodReturn": {"methodReturnType": "array", "methodReturnBrief": "订单各种价格<br />● ProductPrice 产品价格<br />● DiscountPrice 订单折扣金额<br />● CouponPrice 优惠券折扣金额<br />● ShippingPrice 运费<br />● FeePrice 手续费<br />● TaxPrice 税费<br />● TotalPrice 订单总价格"}, "methodExample": ""}, {"methodName": "trackingNumberUpload", "methodBrief": "上传订单物流单号到支付接口上", "methodIsDefinedIn": "--", "methodType": "公共 静态 方法", "methodPrefix": "public static", "methodParams": [{"methodParamInjection": "", "methodParamPrefix": "", "methodParamName": "type", "methodParamDefault": null, "methodParamType": "string", "methodParamBrief": "执行类型 shipped:发货 cancelled:取消发货"}, {"methodParamInjection": "", "methodParamPrefix": "", "methodParamName": "orderId", "methodParamDefault": null, "methodParamType": "integer", "methodParamBrief": "订单ID"}, {"methodParamInjection": "", "methodParamPrefix": "", "methodParamName": "widAry", "methodParamDefault": "[]", "methodParamType": "array", "methodParamBrief": "包裹ID"}, {"methodParamInjection": "", "methodParamPrefix": "", "methodParamName": "oldAry", "methodParamDefault": "[]", "methodParamType": "array", "methodParamBrief": "旧运单号数据 (更新运单号)"}], "methodReturn": null, "methodExample": ""}, {"methodName": "ordersSms", "methodBrief": "订单短信通知", "methodIsDefinedIn": "--", "methodType": "公共 静态 方法", "methodPrefix": "public static", "methodParams": [{"methodParamInjection": "", "methodParamPrefix": "", "methodParamName": "OId", "methodParamDefault": null, "methodParamType": "string", "methodParamBrief": "订单号"}], "methodReturn": null, "methodExample": ""}, {"methodName": "echoOrdersLog", "methodBrief": "订单日志输出", "methodIsDefinedIn": "--", "methodType": "公共 静态 方法", "methodPrefix": "public static", "methodParams": [{"methodParamInjection": "", "methodParamPrefix": "", "methodParamName": "rows", "methodParamDefault": null, "methodParamType": "array", "methodParamBrief": "日志记录数据"}, {"methodParamInjection": "", "methodParamPrefix": "", "methodParamName": "type", "methodParamDefault": null, "methodParamType": "string", "methodParamBrief": "输出类型 Log:日志 Name:名字"}], "methodReturn": {"methodReturnType": "string", "methodReturnBrief": "日志内容"}, "methodExample": ""}, {"methodName": "ordersShippingMethod", "methodBrief": "计算运费", "methodIsDefinedIn": "--", "methodType": "公共 静态 方法", "methodPrefix": "public static", "methodParams": [{"methodParamInjection": "", "methodParamPrefix": "", "methodParamName": "SId", "methodParamDefault": null, "methodParamType": "array", "methodParamBrief": "物流ID"}, {"methodParamInjection": "", "methodParamPrefix": "", "methodParamName": "CId", "methodParamDefault": null, "methodParamType": "integer", "methodParamBrief": "国家ID"}, {"methodParamInjection": "", "methodParamPrefix": "", "methodParamName": "type", "methodParamDefault": null, "methodParamType": "array", "methodParamBrief": "物流类型"}, {"methodParamInjection": "", "methodParamPrefix": "", "methodParamName": "proInfoAry", "methodParamDefault": null, "methodParamType": "array", "methodParamBrief": "产品信息"}, {"methodParamInjection": "", "methodParamPrefix": "", "methodParamName": "discount_percentage", "methodParamDefault": "1", "methodParamType": "integer", "methodParamBrief": "优惠券 和 满减 折扣总价格"}], "methodReturn": {"methodReturnType": "array", "methodReturnBrief": "运费相关信息"}, "methodExample": ""}, {"methodName": "updateRefundSuccess", "methodBrief": "退款成功后的操作<br />删除包裹产品数据、重新入库、更新订单信息", "methodIsDefinedIn": "--", "methodType": "公共 静态 方法", "methodPrefix": "public static", "methodParams": [{"methodParamInjection": "", "methodParamPrefix": "", "methodParamName": "OrderId", "methodParamDefault": null, "methodParamType": "integer", "methodParamBrief": "订单ID"}, {"methodParamInjection": "", "methodParamPrefix": "", "methodParamName": "deleteProData", "methodParamDefault": null, "methodParamType": "array", "methodParamBrief": "删除产品的删除ID号"}, {"methodParamInjection": "", "methodParamPrefix": "", "methodParamName": "refundType", "methodParamDefault": null, "methodParamType": "integer", "methodParamBrief": "退款类型 0:部分退款 1:全额退款"}, {"methodParamInjection": "", "methodParamPrefix": "", "methodParamName": "PassOrderStatus", "methodParamDefault": null, "methodParamType": "integer", "methodParamBrief": "没退款之前的订单状态"}, {"methodParamInjection": "", "methodParamPrefix": "", "methodParamName": "isCancel", "methodParamDefault": "0", "methodParamType": "integer", "methodParamBrief": "是否为取消订单"}], "methodReturn": null, "methodExample": ""}, {"methodName": "disCountTitle", "methodBrief": "获取折扣标题文案", "methodIsDefinedIn": "--", "methodType": "公共 静态 方法", "methodPrefix": "public static", "methodParams": [{"methodParamInjection": "", "methodParamPrefix": "", "methodParamName": "DiscountType", "methodParamDefault": "''", "methodParamType": "string", "methodParamBrief": "折扣类型 默认:订单折扣 user:会员折扣 recall:弃单召回折扣"}], "methodReturn": {"methodReturnType": "string", "methodReturnBrief": "折扣标题文案"}, "methodExample": ""}]}