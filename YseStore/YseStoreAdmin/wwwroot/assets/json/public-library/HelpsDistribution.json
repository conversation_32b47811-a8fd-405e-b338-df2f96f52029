{"classPrefix": "Common\\helps", "classFullName": "Common\\helps\\HelpsDistribution", "classBrief": "HelpsDistribution 提供了分销功能相关方法。", "classExtends": "无", "availableVersion": "5.0010+", "sourceCode": "http://git.shop5.ueeshop.com/developer/ueeshop5.0/common/helps/HelpsDistribution.php", "params": [], "methods": [{"methodName": "DISTOrderReceived", "methodBrief": "订单完成，处理分销余额", "methodIsDefinedIn": "--", "methodType": "公共 静态 方法", "methodPrefix": "public static", "methodParams": [{"methodParamInjection": "", "methodParamPrefix": "", "methodParamName": "OrderId", "methodParamDefault": null, "methodParamType": "integer", "methodParamBrief": "订单ID"}], "methodReturn": null, "methodExample": null}, {"methodName": "DISTWithdraw", "methodBrief": "提现操作", "methodIsDefinedIn": "--", "methodType": "公共 静态 方法", "methodPrefix": "public static", "methodParams": [{"methodParamInjection": "", "methodParamPrefix": "", "methodParamName": "Data", "methodParamDefault": null, "methodParamType": "array", "methodParamBrief": "提现相关信息"}], "methodReturn": {"methodReturnType": "string", "methodReturnBrief": "提现结果"}, "methodExample": null}, {"methodName": "DISTOrder<PERSON><PERSON>nt", "methodBrief": "获取未返佣的相关数据", "methodIsDefinedIn": "--", "methodType": "公共 静态 方法", "methodPrefix": "public static", "methodParams": [{"methodParamInjection": "", "methodParamPrefix": "", "methodParamName": "OrderId", "methodParamDefault": null, "methodParamType": "integer", "methodParamBrief": "订单ID"}], "methodReturn": {"methodReturnType": "array", "methodReturnBrief": "未返佣的相关数据"}, "methodExample": null}, {"methodName": "DISTDealProductsPrice", "methodBrief": "获取产品价格 （ 产品总价 - 折扣 - 优惠券 ）", "methodIsDefinedIn": "--", "methodType": "公共 静态 方法", "methodPrefix": "public static", "methodParams": [{"methodParamInjection": "", "methodParamPrefix": "", "methodParamName": "Data", "methodParamDefault": null, "methodParamType": "array", "methodParamBrief": "订单数据"}], "methodReturn": {"methodReturnType": "float", "methodReturnBrief": "产品价格"}, "methodExample": null}]}