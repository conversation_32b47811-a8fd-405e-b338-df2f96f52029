{"classPrefix": "Common\\helps", "classFullName": "Common\\helps\\Ip", "classBrief": "Ip 提供了可以在应用程序中使用IP的相关功能。", "classExtends": "无", "availableVersion": "5.0010+", "sourceCode": "http://git.shop5.ueeshop.com/developer/ueeshop5.0/common/helps/Ip.php", "params": [{"paramName": "ip", "paramBrief": "域名", "paramIsDefinedIn": "--", "paramType": "string", "paramPrefix": "private static", "paramPrefixTranslate": "私有 静态 属性", "paramDefalut": null}, {"paramName": "fp", "paramBrief": "IP库文件指针资源", "paramIsDefinedIn": "--", "paramType": "resource", "paramPrefix": "private static", "paramPrefixTranslate": "私有 静态 属性", "paramDefalut": null}, {"paramName": "offset", "paramBrief": "从二进制字符串对数据进行解包后的数组", "paramIsDefinedIn": "--", "paramType": "array", "paramPrefix": "private static", "paramPrefixTranslate": "私有 静态 属性", "paramDefalut": null}, {"paramName": "index", "paramBrief": "读取IP库文件后的内容", "paramIsDefinedIn": "--", "paramType": "string", "paramPrefix": "private static", "paramPrefixTranslate": "私有 静态 属性", "paramDefalut": null}], "methods": [{"methodName": "find", "methodBrief": "调用查询IP库的相关信息", "methodIsDefinedIn": "--", "methodType": "公共 静态 方法", "methodPrefix": "public static", "methodReturn": {"methodReturnType": "array", "methodReturnBrief": "返回IP库查询后的地址详细信息"}}, {"methodName": "init", "methodBrief": "初始化ip库的调用", "methodIsDefinedIn": "--", "methodType": "公共 静态 方法", "methodPrefix": "public static"}, {"methodName": "__destruct", "methodBrief": "析构函数", "methodIsDefinedIn": "--", "methodType": "公共 静态 方法", "methodPrefix": "public static"}, {"methodName": "updateiplocation", "methodBrief": "区分直辖市/自治区/省", "methodIsDefinedIn": "--", "methodType": "公共 静态 方法", "methodPrefix": "public static", "methodParams": [{"methodParamName": "row", "methodParamDefault": null, "methodParamType": "string", "methodParamBrief": "三级地区信息，包含国家省份城市"}], "methodReturn": {"methodReturnType": "array", "methodReturnBrief": "返回区分后的信息数组"}}]}