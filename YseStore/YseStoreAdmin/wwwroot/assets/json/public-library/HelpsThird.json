{"classPrefix": "Common\\helps", "classFullName": "Common\\helps\\HelpsThird", "classBrief": "HelpsThird 提供了第三方代码相关方法。", "classExtends": "无", "availableVersion": "5.0010+", "sourceCode": "http://git.shop5.ueeshop.com/developer/ueeshop5.0/common/helps/HelpsThird.php", "params": [], "methods": [{"methodName": "code", "methodBrief": "输出第三方代码", "methodIsDefinedIn": "--", "methodType": "公共 静态 方法", "methodPrefix": "public static", "methodParams": [{"methodParamInjection": "", "methodParamPrefix": "", "methodParamName": "source", "methodParamDefault": null, "methodParamType": "array", "methodParamBrief": "来源数组"}, {"methodParamInjection": "", "methodParamPrefix": "", "methodParamName": "isMeta", "methodParamDefault": "0", "methodParamType": "integer", "methodParamBrief": "源在Meta输出"}, {"methodParamInjection": "", "methodParamPrefix": "", "methodParamName": "isBody", "methodParamDefault": "0", "methodParamType": "integer", "methodParamBrief": "源在Body输出"}], "methodReturn": {"methodReturnType": "string", "methodReturnBrief": "第三方代码"}, "methodExample": null}, {"methodName": "convertUnderline", "methodBrief": "将 下划线|中划线 命名转换为 驼峰式 命名", "methodIsDefinedIn": "--", "methodType": "公共 静态 方法", "methodPrefix": "public static", "methodParams": [{"methodParamInjection": "", "methodParamPrefix": "", "methodParamName": "str", "methodParamDefault": null, "methodParamType": "string", "methodParamBrief": "来源字符串"}, {"methodParamInjection": "", "methodParamPrefix": "", "methodParamName": "ucfirst", "methodParamDefault": "true", "methodParamType": "boolean", "methodParamBrief": "首字母是否小写"}], "methodReturn": {"methodReturnType": "string", "methodReturnBrief": "目标字符串"}, "methodExample": null}, {"methodName": "thirdCode", "methodBrief": "自定义代码 应用 代码字符串", "methodIsDefinedIn": "--", "methodType": "公共 静态 方法", "methodPrefix": "public static", "methodParams": [{"methodParamInjection": "", "methodParamPrefix": "", "methodParamName": "isMeta", "methodParamDefault": null, "methodParamType": "integer", "methodParamBrief": "是否是head标签里面"}, {"methodParamInjection": "", "methodParamPrefix": "", "methodParamName": "isBody", "methodParamDefault": null, "methodParamType": "integer", "methodParamBrief": "是否是body标签里面"}], "methodReturn": {"methodReturnType": "string", "methodReturnBrief": "代码字符串"}, "methodExample": null}, {"methodName": "luckyOrangeCode", "methodBrief": "Lucky Orange应用 代码字符串", "methodIsDefinedIn": "--", "methodType": "公共 静态 方法", "methodPrefix": "public static", "methodParams": [{"methodParamInjection": "", "methodParamPrefix": "", "methodParamName": "isManage", "methodParamDefault": null, "methodParamType": "integer", "methodParamBrief": "是否在B端显示"}, {"methodParamInjection": "", "methodParamPrefix": "", "methodParamName": "isBody", "methodParamDefault": null, "methodParamType": "integer", "methodParamBrief": "本方法中无用，请勿删除"}], "methodReturn": {"methodReturnType": "string", "methodReturnBrief": "代码字符串"}, "methodExample": null}]}