.home_container {max-width: 1382px; margin: 0 auto;}

/************************** layout start **************************/
.greeting {font-size: 16px; line-height: 40px; color: #7d8d9e;}
.dashboard {
    --numValColor: #1f2328;
    display: flex; flex-direction: row;
}
.dashboard .left {width: 26.05%;} /*360*/ /*1382*/
.dashboard .right {width: 72.36%;} /*1000*/
.left+.right {margin-left: 1.448%} /*20*/
/*************************** layout end ***************************/

/************************** pannel start **************************/
.pannel {box-sizing: border-box; padding: 24px; background-color: #fff; border-radius: 5px; font-size: 14px; color: #404852; overflow: hidden;}
.pannel .head{position: relative; line-height: 18px; color: #7d8d9e;}
.pannel .content {font-size: 16px; line-height: 18px;}
.pannel+.pannel {margin-top: 20px;}
.left .pannel {height: 190px}
.right .pannel {height: 400px;}
.left .pannel .content {margin-top: 40px;}

.pannel .head .detail a {color: #7d8d9e;}
.pannel .head .detail a:hover {color: var(--primaryColor);}
.pannel .head .detail {display: block; position: absolute; top: 0; right: 0; font-size: 14px; line-height: 16px;}

.pannel .content .row {margin-bottom: 40px;}
.pannel .content .val {margin: 0 10px;}
.pannel.today-sales .content .val {font-size: 22px; color: var(--numValColor);}
.pannel.todo .content .item {position: relative; display: block; padding: 0 20px; color: #404852;}
.pannel.todo .item::before {content: ''; position: absolute; top: 50%; display: inline-block; width: 20px; height: 20px; margin: -10px 0 0;}
.pannel.todo .item::before {left: 0; background: url('../images/frame/icon_dashboard_todo.png') no-repeat 0 0;}
.pannel.todo .item.unpay::before {background-position-y: -20px;}
.pannel.todo .item .icon {position: absolute; right: 0; color: #7d8d9e; font-size: 14px;}
.pannel.todo .val {margin-right: 5px; color: var(--numValColor);}
.pannel.products {height: 400px; padding: 24px 0;}
.pannel.products .content {margin-top: 0;}

.right .pannel .head {display: flex; justify-content: space-between;}
.right .pannel .content {display: flex;}
/*************************** pannel end ***************************/


/************************** module start **************************/
.tabs .tab-header {position: relative; display: flex; color: #404852; font-size: 14px;}
.tabs .tab-header::after {content: ''; position: absolute; bottom: 0; width: 100%; height: 1px; background-color: #edf0f5;}
.tabs .nav {margin: 0 24px; padding: 0 0 15px; position: relative; cursor: pointer;}
.tabs .nav.active {color: var(--primaryColor);}
.tabs .nav.active::after {content: ''; position: absolute; left: 0; bottom: 0; display: inline; width: 100%; height: 3px; background-color: var(--primaryColor);}
.tabs .tab-content {padding: 0 24px 24px;}
.tabs .tab-content .tabpanel {display: none; opacity: 0; transition: all 0.3s;}
.tabs .tab-content .tabpanel.active {display: block; opacity: 1;}

.product-header {display: none; justify-content: space-between; padding: 20px 0; font-size: 12px; color: #7d8d9e;}
.product-list {display: none; height: 260px; padding-right: 5px; overflow-y: scroll;}
.product-list::-webkit-scrollbar {width: 4px; height: 4px; background-color: #fff; box-shadow: unset;}
.product-list::-webkit-scrollbar-track {background-color: #fff; box-shadow: unset;}
.product-list::-webkit-scrollbar-thumb {border-radius: 10px; box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.1); background-color: #f7f7f7;}
.product-list .item, .item .info {display: flex; line-height: 32px;}
.product-list .item .info {width: 75%; text-align: left;}
.product-list .info .pic {display: flex; justify-content: center; align-items: center; width: 33px; height: 33px; overflow: hidden; text-align: center;}
.product-list .info img {max-width: 100%; max-height: 100%;}
.product-list .info .title {width: 200px; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;}
.product-list .info .title a {font-size: 14px; color: #404852;}
.product-list .info .pic+.title {margin-left: 10px;}
.product-list .item .num {width: 25%; text-align: right;}
.product-list .item .num .val {margin-right: 0; font-weight: normal; color: var(--numValColor);}
.product-list .item + .item {margin-top: 14px;}
.tab-content .no_data {display: block; background-color: #fff; background-image: url('../images/mta/icon_no_data.png'); text-align: center; font-size: 12px; color: #7d8d9e;}

.datepicker {display: inline-block; position: relative; cursor: pointer; margin-bottom: 5px;}
.datepicker .text {position: relative; display: block; padding-right: 22px; color: #404852; font-size: 22px;}
.datepicker .text .icon {position: absolute; right: 0; color: #404852; font-size: 15px; transition: all 0.4s;}
.datepicker:hover:not(.disabled) .text .icon {transform: rotate(-180deg);}
.datepicker.disabled {cursor: no-drop; opacity: 0.3;}
/*
.datepicker .options {
    display: none;
    position: absolute;
    top: 50%;
    left: 0;
    z-index: 1;
    opacity: 0;
    z-index: 1;
    width: 110px;
    padding: 10px 0;
    border-radius: 5px;
    background-color: #fff;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.2);
    transition: all 0.3s;
    color: #404852;
    cursor: unset;
}
.datepicker .options label {display: block; box-sizing: border-box; width: 100%; padding: 0 25px; cursor: pointer; font-size: 14px; line-height: 28px; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;}
.datepicker .options label:hover {background-color: #ecf5ff;}
.datepicker .options input[type=radio] {display: none; opacity: 0;}
*/
.datepicker .pane {display: none; opacity: 0; position: absolute; top: 100%; left: 0; z-index: 10; margin: 10px 0 0; padding: 20px 30px 30px; background-color: #fff; border-radius: 5px; box-shadow: 0 0 10px rgba(0, 0, 0, 0.2); transition: all 0.3s;}
.datepicker.active .pane {display: block; opacity: 1;}
.datepicker .item+.item {margin-top: 15px;}
.datepicker .item.btn_group {margin-top: 25px;}
.datepicker .label {font-size: 14px; line-height: 36px; color: #1f2328;}
.datepicker .compare .input {display: none;}
.datepicker select {position: relative; z-index: 1; width: 250px; padding: 5px 20px 5px 10px; background-color: transparent; line-height: 20px; border: 1px solid #ccdced; border-radius: 4px; cursor: pointer; appearance: none; -moz-appearance: none; -webkit-appearance: none;}
.datepicker select::-ms-expand {display: none;}
.datepicker select+.icon {position: absolute; z-index: -1; color: #8f9dac; margin: 1px 0 0 -21px; font-size: 10px; transform: scale(0.8); -webkit-transform: scale(0.8);}
.datepicker select.active+.icon {transform: scale(0.8) rotate(-180deg);}
.datepicker input[type=checkbox] {display: none; opacity: 0;}
.datepicker .checkbox input[type=checkbox]+label{position: relative; padding-left: 25px; cursor: pointer;}
.datepicker .checkbox input[type=checkbox]+label::before {content: ''; position: absolute; top: 0; left: 0; width: 16px; height: 16px; border: 1px solid #ccdced; border-radius: 2px; background-color: #fff;}
.datepicker .checkbox input[type=checkbox]:checked+label::before {border-color: var(--primaryColor); background: var(--primaryColor) url('../images/frame/icon_checkbox_checked.png') no-repeat center;}
.datepicker .btn_global {border-radius: 5px; height: 36px; line-height: 36px;}
.datepicker .btn_global+.btn_global {margin-left: 5px;}
.datepicker .btn_global:disabled {background-color: #ccc; border-color: #ccc; cursor: no-drop;}
.datepicker .custom_date {display: none;}
.datepicker .custom_date input.date {
    box-sizing: border-box;
    width: 100%;
    padding: 5px 25px 5px 10px;
    background: url('../images/frame/icon_date.png') no-repeat 95% center transparent;
    border: 1px solid #ccdced;
    border-radius: 4px;
}
.datepicker .custom_date input.date.error {border-color: #f00;}

.report {width: 450px}
.pannel .content .report + .report {margin-left: 40px;}
.report .title {font-size: 14px; margin-bottom: 35px;}
.report .title .val {display: inline-block; margin: 0; padding: 10px 0; font-size: 22px; color: var(--numValColor);}
.report.conver .title {margin-bottom: 30px;}

.conversion .operate {display: flex; padding: 0 20px; background-color: #f7f7f7; font-size: 14px; line-height: 37px;}
.conversion .operate.header {background-color: #fff; font-size: 12px; color: #7d8d9e; line-height: 20px;}
.conversion .operate.header .rate {text-align: center;}
.conversion .operate+.operate {margin-top: 10px;}
.conversion .action {width: 36.586%;} /*150*/
.conversion .num {width: 48.784%;} /*200*/
.conversion .num .val {margin: 0; font-weight: normal; color: var(--numValColor);}
.conversion .rate {width: 14.635%; position: relative;} /*60*/
.conversion .rate .val {position: absolute; width: 60px; margin: 0; text-align: center; font-weight: normal; font-size: 14px; line-height: 30px; color: #fff; z-index: 1; transform: translateY(-70%);}
.conversion .rate .val::before, .conversion .rate .val::after {content: ''; position: absolute; left: 0; z-index: -1;}
.conversion .rate .val::before {top: 0;  width: 60px; height: 30px; background-color: var(--primaryColor);}
.conversion .rate .val::after {bottom: -10px; border-style: solid; border-width: 10px 30px 0 30px; border-color: var(--primaryColor) transparent transparent transparent;}
.conversion[data-checkout-mode="single"] .operate {line-height: 46px;}
.conversion[data-checkout-mode="single"] .operate.header {line-height: 20px;}
.conversion[data-checkout-mode="single"] .operate.addpayment {display: none;}

.report .chart {width: 100%; height: 250px;}
.highcharts-legend-item.highcharts-pie-series .highcharts-point {display: none; opacity: 0;}

/*加载*/
.loader {display: block; background-color: rgba(255, 255, 255, 0.75); z-index: 10; display: flex;}
.loader .inner {display: flex; flex-direction: column; justify-content: center; align-items: center; margin: 0 auto;}
.loader .text {margin: 5px; font-size: 14px; color: #7d8d9e;}
.loader .loading {
    width: 50px;
    height: 50px;
    background-color: transparent;
    border-top: 3px var(--primaryColor) solid;
    border-right: 3px var(--primaryColor) solid;
    border-bottom: 3px #cbcbca solid;
    border-left: 3px #cbcbca solid;
    border-radius: 50%;
    animation: rotation .5s infinite linear;
}
/*************************** module end ***************************/



