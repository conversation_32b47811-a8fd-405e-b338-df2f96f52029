

/*************************** 多语言 start ***************************/
body.en .app_main .app_item .box_view>p{line-height:16px; margin-top:0;}
body.en .app_main .app_item .box_view .btn_install{margin-top:8px;}

body.en .my_app_list .app_category_list>li .info>p{line-height:17px; margin-top:10px;}
/*************************** 多语言 start ***************************/

/*************************** 应用主页 start ***************************/
#app{padding:0 0 40px;}
.no_app{position: relative;}
.no_plugins{height: 198px; margin: auto;position: absolute;top: 0;bottom: 0;left: 0;right: 0;text-align: center;}
.no_plugins .title{height: 74px;line-height: 74px;font-size: 40px;color: #000;}
.no_plugins .subtitle{height: 42px;line-height: 42px;font-size: 20px;color: #555;}
.no_plugins .go_app_store{display: inline-block;height: 52px;line-height: 52px;background: var(--primaryColor);border-radius: 3px;margin-top: 29px;padding: 0 45px;font-size: 18px;color: #fff;}

.app_header{height:80px; background:#ccc no-repeat center;position: relative;background-size: cover;}
.app_header h3{height:38px; line-height:38px; padding:40px 0 35px; text-align:center; font-size:34px; color:#222;}
.app_header .btn_app_menu{display: none;vertical-align: top;width:38px; height:38px; background:url(../images/plugins/btn_menu.png) no-repeat center var(--primaryColor); margin-left: 15px; border-radius:5px;}
.app_header .category_menu{position: absolute; bottom: 0; width:100%; margin:auto; padding:30px 0; text-align: center; font-size: 0;}
.app_header .category_menu a{display: inline-block;height: 34px;line-height: 34px;padding: 0 26px;color: #222;font-size: 16px;}
.app_header .category_menu a.current{background: var(--primaryColor);border-radius: 50px; color: #fff;}
/* header浮动样式 */
.app_header .category_menu.fixed {position: fixed; bottom: unset; z-index: 10; background-color:#fff; color:#1f2328; box-shadow: inset 0px 1px 3px #e6e8ea; animation: headerFixed 0.35s ease-in;}
.app_header .category_menu.fixed a {color:#1f2328;}
.app_header .category_menu.fixed a.current {color: #fff;}
.app_header .category_menu.fixed::before {content: ""; position: absolute; bottom: 1px; left: 0; width: 100%; height: 1px; box-shadow: 0px 2px 2px rgba(0,0,0,.2);}
.app_header .app_search{width: 500px;margin:0 auto;position: relative;}
.app_header .app_search .search_input{width: 100%;height: 38px; padding:0 46px 0 15px;box-sizing: border-box;border-radius: 19px;border:0}
.app_header .app_search .search_btn{position: absolute;width: 46px;height: 38px;top: 0;right: 0;text-indent:99px; cursor:pointer; background:url(../images/plugins/btn_search.png) no-repeat center; border:0;background-color: #fff;border-radius: 0 19px 19px 0;background-size: 38%;}
.nodata_app_bg{background-color: #fff;}
@keyframes headerFixed {
    from {transform: translateY(-10%); opacity: 0;}
    to {transform: translateY(0); opacity: 1;}
}

/*列表样式*/
.app_main{padding:40px 0;display: none;}
.app_main .category_box{display: none;}
.app_main .app_item{width: 100%; height:135px; padding-right: 80px; padding-left: 135px; margin: 0 0 20px 0; background-repeat:no-repeat; border-radius:5px; position:relative; float:left; transition:all .4s; -webkit-transition:all .4s;background-color: #fff;box-sizing:border-box;-webkit-box-sizing:border-box;-moz-box-sizing:border-box;}
.app_main .app_item .btn_box{width: 80px;height: 36px;margin-top: 51px;margin-right: -80px;text-align: center; line-height: 36px;display: inline-block;vertical-align: top;}
.app_main .app_item .btn_box .btn_view{width: 50px;margin-right: 7px;float: right;color:var(--primaryColor);}
.app_main .app_item .btn_box .box_used{margin-right: 10px;float: right;}
.app_main .app_item .btn_install{background: var(--primaryColor);border-radius: 5px;color: white;margin-right: 10px;color: var(--primaryColor);}
.app_main .app_item .installed{color: #aaa;}
.app_main .app_item .box_view{margin-left: 35px; border-radius:5px; }
.app_main .app_item .box_view strong{height: 21px;line-height: 21px;margin-top: 30px;font-size: 16px;color: #000;display: block;}
.app_main .app_item .box_view strong span{display: inline-block;vertical-align: top;height: 19px;line-height: 19px;border:1px solid #888;border-radius: 3px; margin-left: 12px;padding: 0 6px;font-size: 12px;color: #888;}
.app_main .app_item .box_view>p{margin-top:10px; line-height:20px; font-size:12px; color:#666; cursor:default;}
.app_main .app_item:hover{transform:translateY(-5px); -webkit-transform:translateY(-5px);}
.app_main .app_item .import_progress .progress{ background-color:var(--primaryColor);}
.app_main .app_item .import_progress .progress_percent>span{color:var(--primaryColor);}
.app_main li.app_item{padding-right: 126px;}
.app_main li.app_item .btn_box{width: 126px;margin-right: -126px;}

.app_main .app_item_next{text-align:center;padding: 0;background: none;}
.app_main .app_item_next>strong{line-height:49px; margin-top:36px; font-size:28px; display:block;}
.app_main .app_item_next>span{margin-top:8px; display:block;}
.app_main .app_item_next:hover{transform:translateY(0px); -webkit-transform:translateY(0px);}

.pop_app_menu form{width:800px; border:0;}
.pop_app_menu .app_category_list{padding:30px 25px 50px;}
.pop_app_menu #app_category_list_box{position: relative;}
.pop_app_menu .app_category_list .item{width:165px; margin: 0px 9px 20px;position: absolute;}
.pop_app_menu .app_category_list .item dl{margin-top:16px;}
.pop_app_menu .app_category_list .item dl dt{height:31px; line-height:31px; overflow: hidden; margin-bottom:4px; font-size:16px;text-overflow: ellipsis;white-space: nowrap;}
.pop_app_menu .app_category_list .item dl dd{height:24px; line-height:24px;}
.pop_app_menu .app_category_list .item dl dd>a{font-size:14px;}
.pop_app_menu .app_category_list .item dl:first-child{margin-top:0;}

.pop_app_install form{width:650px; border:0;}
.pop_app_install form .t{height:0; border:0;}
.pop_app_install form .t h2{position:absolute; top:0; right:0;}
.pop_app_install .install_header{height:140px; text-align:center; background:url(../images/plugins/bg_ueeshop.png) no-repeat center 63px var(--primaryColor);}
.pop_app_install .install_header>h3{height:28px; line-height:28px; margin-top:24px; font-size:20px; color:#fff; display:inline-block;}
.pop_app_install .install_bodyer{padding:18px 90px;}
.pop_app_install .install_bodyer .title{height:39px; line-height:39px; font-size:18px; color:#666;}
.pop_app_install .install_bodyer .title>strong{font-size:20px; color:#333;}
.pop_app_install .install_bodyer .infomation{line-height:25px; font-size:14px; color:#666;}
.pop_app_install .install_bodyer .button{text-align:center; border:0;}
.pop_app_install .install_bodyer .button .btn_submit{height:32px; line-height:32px; color:#fff; background-color:var(--primaryColor);}

.pop_app_oauth .download{height: 32px;line-height: 32px;display: inline-block;padding: 0 13px 0 35px;background: url(../images/shipping/icon_download.png) no-repeat 14px center;border:1px solid var(--primaryColor);color: var(--primaryColor);font-size: 14px;text-decoration: none;border-radius: 17px;margin-top: 10px;}
.pop_app_oauth form{width:800px; border:0;}
.pop_app_oauth form .t{height:85px; line-height:85px; border:0; border-bottom:1px #ebebeb solid;}
.pop_app_oauth form .t h1{text-indent:25px;}
.pop_app_oauth form .t.success{background-image:url(../images/frame/icon_success.png); background-repeat:no-repeat; background-position:25px center;}
.pop_app_oauth form .t.success h1{text-indent:63px;}
.pop_app_oauth .success_bodyer{min-height: 200px;padding:35px 25px;}
.pop_app_oauth .success_bodyer.loading{background: url(..//images/global/loading.gif) no-repeat center;}
.pop_app_oauth .success_bodyer .infomation{width:403px; float:left;}
.pop_app_oauth .success_bodyer .data_list{margin-bottom:15px; display:none;}
.pop_app_oauth .success_bodyer .data_list.fixed_height{position: relative; max-height: 150px;overflow-y: auto; }
.pop_app_oauth .success_bodyer .content{width:350px; line-height:20px; margin-bottom:15px; font-size:12px; color:#999;}
.pop_app_oauth .success_bodyer .button{height: auto;margin:10px 0 20px;padding: 0;text-align:left; border:0;}
.pop_app_oauth .success_bodyer .button .btn_global{margin:0; color:#fff; background-color:var(--primaryColor); border:0; display:none;}
.pop_app_oauth .success_bodyer .button .help{display:none;line-height:32px; margin-left:10px; color:#aaa;}
.pop_app_oauth .success_bodyer .picture{width:316px; padding-left:30px; border-left:1px #ebebeb solid; float:left;}
.pop_app_oauth .success_bodyer .picture>img{max-width:100%; max-height:100%;}
.pop_app_oauth .success_bodyer #btn_paypal_marketing{border:0;}
.pop_app_oauth .success_bodyer #btn_paypal_marketing .xcomponent-element{height:32px; background:#fff;}
.pop_app_oauth .rows>label{line-height:1; padding-top:10px; padding-bottom:5px;}
.pop_app_oauth .rows .box_input{width:340px;}
.pop_app_oauth .rows .box_input_small{width:240px;margin-bottom: 10px;}
.pop_app_oauth .rows:first-child>label{padding-top:0;}

.pop_app_oauth .facebook_flow_container .store_id{width:540px; line-height:20px; font-size:14px; color:#a2a2a2;}
.pop_app_oauth .facebook_flow_container .button{margin-top:15px;}
.pop_app_oauth .facebook_flow_container .button>div{padding-bottom: 15px;color: #999;font-size: 12px;}
.pop_app_oauth .facebook_flow_container button{height:38px; line-height:38px; overflow:hidden; margin-right:6px; padding:0 18px; font-size:13px; font-weight:bold; color:#fff; background-color:#05a579; border-color:#05a579; border-radius:3px; transition:200ms cubic-bezier(.08,.52,.52,1) background-color, 200ms cubic-bezier(.08,.52,.52,1) box-shadow, 200ms cubic-bezier(.08,.52,.52,1) transform;}
.pop_app_oauth .facebook_flow_container button:hover{background-color:#365899; border-color:#365899;}
.pop_app_oauth .facebook_flow_container button:active{background-color:#29487d; border-color:#29487d;}
.pop_app_oauth #btn_expand{position: absolute;height: 28px;line-height: 28px; left:270px;top:20px;}
.pop_app_oauth .rows .icon_delete_1{margin-top: 6px;margin-left: 9px;}

.pop_app_switch form{width:800px; border:0;}
.pop_app_switch form .t{height:85px; line-height:85px; border:0; border-bottom:1px #ebebeb solid;}
.pop_app_switch form .t h1{text-indent:25px;}
.pop_app_switch .switch_bodyer{padding:25px;}
.pop_app_switch .switch_bodyer .info_title{ font-size:16px; margin-bottom:10px;}
.pop_app_switch .switch_bodyer .infomation{padding-bottom:10px;}
.pop_app_switch .switch_bodyer .data_list>li{width:50%; height:32px; line-height:32px; padding:5px 0; float:left;}
.pop_app_switch .switch_bodyer .data_list>li .number_box{margin-left:10px; display:none;}
.pop_app_switch .switch_bodyer .data_list>li .unit_input, .pop_app_switch .switch_bodyer .data_list>li .box_input{height:30px; line-height:30px;}
.pop_app_switch .switch_bodyer .button{padding:10px 0 20px; text-align:left; border:0;}
.pop_app_switch .switch_bodyer .button .btn_global{margin:0; color:#fff; background-color:var(--primaryColor); border:0; display:none;}

.pop_app_cooperate .cooperate_box{padding: 32px 43px;}
.pop_app_cooperate .cooperate_box .cooperate_title{line-height: 36px; font-size: 20px;}
.pop_app_cooperate .cooperate_box .cooperate_logo_box{padding-top: 36px; text-align: center;}
.pop_app_cooperate .cooperate_box .cooperate_logo_box .logo{display: inline-block; width: 66px; height: 66px; border-radius: 35px; margin: 0 42px; background-repeat: no-repeat; background-position: center; vertical-align: middle; background-size: 66px auto;overflow: hidden;}
.pop_app_cooperate .cooperate_box .cooperate_logo_box .logo span{ display: block; width: 100%; height: 100%; background-size: contain; background-position: center; background-repeat: no-repeat; }
.pop_app_cooperate .cooperate_box .cooperate_logo_box .icon{display: inline-block; width: 48px; height: 48px; vertical-align: middle; background: url(../images/plugins/cooperate_icon.png) no-repeat center / 100% auto;}
.pop_app_cooperate .cooperate_box .cooperate_logo_box .ueeshop{background-image: url(../images/plugins/cooperate_ueeshop.png);}
.pop_app_cooperate .cooperate_box .cooperate_logo_box .tongdun{background-image: url(../images/plugins/icon_item_tongdun.jpg);}
.pop_app_cooperate .cooperate_box .cooperate_bodyer{margin-top: 32px; background: #f7f9fb; padding: 22px 32px;}
.pop_app_cooperate .cooperate_box .cooperate_bodyer .title{line-height: 32px; font-size: 16px;}
.pop_app_cooperate .cooperate_box .cooperate_bodyer .infomation{padding: 12px 0 45px; line-height: 32px; color: #7d8d9e;}
.pop_app_cooperate .cooperate_box .cooperate_btn{padding-top: 32px; text-align: center; font-size: 0;}
.pop_app_cooperate .cooperate_box .cooperate_btn .btn_submit{margin-right: 21px;}
.pop_app_cooperate .cooperate_box .cooperate_btn .btn_global{height: 38px; line-height: 38px; padding: 0 23px;}

.app_main_box{max-width: 1304px;margin: 0 auto;padding: 0 20px; color: #1f2328;}
.app_index_title{margin-top: 65px;margin-bottom: 20px;font-size: 30px;}
.app_index_tips{margin:15px 0 25px;font-size: 16px;}
.app_index_plugins_box{display: flex;flex-wrap: wrap;}
.app_index_plugins{width: 23%;margin-right: 2%;margin-bottom: 26px;padding: 30px 45px;background: #fff;box-sizing: border-box;float: left;border-radius: 5px;transition: all 0.3s;}
.app_index_plugins:hover{transform: translateY(-5px);}
.app_index_plugins .plugins_icon{display: block;width: 70px;height: 70px;background-size: contain;border-radius: 35px;}
.app_index_plugins .plugins_name{margin-top: 20px;font-size:18px;height: 50px;line-height: 25px;}
.app_index_plugins .plugins_brief{ line-height: 20px;margin-top: 10px;font-size: 14px;color: #7d8d9e; overflow: hidden;}
.app_index_plugins .plugins_bottom{margin-top: 35px;}
.app_index_plugins .plugins_tips {line-height: 19px; margin-top: 6px; font-size: 12px; color: #7d8d9e;}
.app_index_plugins .plugins_tips>a {margin-left: 8px; color: var(--primaryColor);}
.app_index_plugins .plugins_version{font-size: 14px;color: #aaa;float: left;line-height: 30px;}
.app_index_plugins .plugins_btn{float: left;}
.app_index_plugins .plugins_btn.btn_unused{background: #b0b2b1; border-color: #b0b2b1; cursor: no-drop;}
.app_index_plugins[data-type="cod_page"] .global_app_tips a { color: #f68f44; }
.app_index_scenes_box{padding-bottom: 60px;}
.app_index_scenes_box a{margin-left: 1.8%;display: block;float: left;transition: all 0.3s;position: relative;}
.app_index_scenes_box a.ml{margin-left: 0;}
.app_index_scenes_box a.m1{width: 23.2%;}
.app_index_scenes_box a.m2{width: 48.1%;}
.app_index_scenes_box a.m3{width: 73.1%;}
.app_index_scenes_box a:hover{transform: translateY(-5px);}
.app_index_scenes_box img{display: block;max-width: 100%;}
.app_index_scenes_box .scenes_name{position: absolute;line-height: 30px;font-size: 22px;font-weight: bold;text-align: center;}
.app_index_scenes_box a.m1 .scenes_name{width: 100%;top:36px;left:0;}
.app_index_scenes_box a.m2 .scenes_name{width: 41.5%;top:50%;right:0;transform: translateY(-50%);text-align: left;}
.app_index_scenes_box a.m1 .scenes_name.scenes_more{top:50%;transform: translateY(-50%);font-size: 26px;color:#666666;}
.app_index_scenes_box a.m1 .scenes_name.scenes_more .scenes_more1{ display: block;margin-bottom: 6px;}
.app_index_scenes_box a.m1 .scenes_name.scenes_more .scenes_more2{ display: block;font-size: 18px; font-weight: normal;}
.app_index_scenes_box .scenes_fastproducts{color:#3d5173;}
.app_index_scenes_box .scenes_include{color:#ffffff;}
.app_index_scenes_box .scenes_review{color:#ffffff;}
.app_index_scenes_box .scenes_login{color:#ffffff;}
.app_index_scenes_box .scenes_volume{color:#3c405f;}
.app_index_scenes_box .scenes_drainage{color:#464755;}
.app_index_scenes_box .scenes_google{color:#555555;}
.app_index_scenes_box .scenes_facebook{color:#fbd686;}
.app_index_scenes_box .scenes_service{color:#773429;}
.app_index_scenes_box .scenes_langcurrency{color:#1c3963;}
.app_index_scenes_box .scenes_wind{color:#ffffff;}
.app_index_scenes_box .scenes_speed{color:#2d4d58;}
.app_index_scenes_box .scenes_orders{color:#3f609b;}
@media screen and (max-width: 1280px) {
	.app_index_scenes_box .scenes_name{line-height: 24px; font-size: 16px;}
	.app_index_scenes_box a.m1 .scenes_name{top:20px;}
}

#box_plugins_tips {position: fixed; left: calc(50% - 201px); top: 250px; z-index: 10001; display: none; width: 402px; padding: 10px; text-align: center; background-color: #fff; border-radius: 4px; box-sizing: border-box; -webkit-box-sizing: border-box;}
#box_plugins_tips .btn_tips_close {position: absolute; top: 10px; right: 11px; display: block; width: 30px; height: 30px; background: url(../images/frame/icon_delete_0.png) no-repeat center / 100%;}
#box_plugins_tips .tips_title {margin-top: 42px;}
#box_plugins_tips .tips_title, #box_plugins_tips .tips_content {line-height: 30px; font-size: 16px; color: #000;}
#box_plugins_tips .btn_tips_know {display: inline-block; height: 32px; line-height: 32px; margin-top: 43px; margin-bottom: 20px; color: #fff; background-color: var(--primaryColor);}
/*************************** 应用主页 start ***************************/

/*************************** 应用详细公共样式 start ***************************/
.plugins_app_box .plugins_app_title .box_explain{min-height:26px; line-height:26px; font-size:14px;}
/*************************** 应用详细公共样式 end ***************************/

/*************************** 我的应用主页 start ***************************/
.my_app_list{padding: 30px 0;}
.my_app_list .app_category_title{height:51px; line-height:51px; margin-top:29px; font-size:18px; color:#000;}
.my_app_list .app_category_title:first-child{margin-top:0;}
.my_app .app_more{margin:45px 0; text-align: center;font-size: 20px;}
.my_app .app_more a{color:var(--primaryColor);text-decoration: underline;}

#app .app_list{float: left;width: 43%;height: 200px;margin:10px 1.5%;padding:1.5%;border:1px solid #ccc;border-radius: 2px;}

#review #progress{padding:0 20px 20px;}
#review #progress>p{margin:5px 0;}

#app .my_app h1{font-size: 30px;}
#app .my_app .search_form{margin-top:4px;}
#app .my_app .search_form .form_input{background-color: #fff;}
#app .my_app_list{margin-top: 25px;padding:0 30px;background: #fff;border-radius: 5px;}
#app .my_app_list .app_plugins_item{padding: 20px 0;border-top: 1px solid #edf0f5;}
#app .my_app_list ul.app_category_list:first-child .app_plugins_item:first-child{border-top:0;}
#app .my_app_list .app_plugins_item .plugins_icon{width: 60px;height: 60px;background-size: contain;border-radius: 30px;}
#app .my_app_list .app_plugins_item .plugins_name{height: 60px;line-height: 60px;margin-left: 20px;font-size: 16px;}
#app .my_app_list .app_plugins_item .plugins_open{width: 50px;margin-top: 18px;position: relative;}
#app .my_app_list .app_plugins_item .plugins_open .plugins_confirm{width: 100%;height: 100%;position: absolute;left: 0;top: 0;z-index: 1;cursor: pointer;}
#app .my_app_list .app_plugins_item .plugins_txt{height: 60px;line-height: 60px;margin-right: 20px;}
#app .my_app_list .app_plugins_item .plugins_txt a{color: var(--primaryColor);}
#app .my_app_list .app_plugins_item .import_progress{width: 200px;margin-top:22px;margin-right: 20px;display: none;}
#app .my_app_list .app_plugins_item .import_progress .progress_bar{width:150px; height:6px;margin-top: 6px; overflow:hidden; background-color:#eee; border-radius:20px;float: left;}
#app .my_app_list .app_plugins_item .import_progress .progress{width:0; height:6px; overflow:hidden; background-color:#07bb8b; border-radius:20px; transition:width .8s; -moz-transition:width .8s; -webkit-transition:width .8s;}
#app .my_app_list .app_plugins_item .import_progress .progress_percent{font-size:12px; color:#aaa;float: right;}
#app .my_app_list .app_plugins_item .import_progress .progress_percent>span{color:#07bb8b;}
/*************************** 我的应用主页 end ***************************/

/************************** 第三方服务市场 start **************************/
.third_market .app_main_box {max-width: 1230px; padding: 0 5%;}
.third_market .market_list {display: flex; flex-direction: row; justify-content: flex-start; flex-wrap: wrap;}
.third_market .market_item {width: 31.9%; margin: 0 1.4% 25px 0; padding: 0; float: unset; background-color: #fff; border-radius: 5px; text-align: left; font-size: 14px; line-height: 24px;}
.market_item .info {padding: 20px;}
.market_item .info .pic {display: flex; align-items: center; height: 55px; max-width: 200px; margin-bottom: 20px; overflow: hidden;}
.market_item .info .pic img {max-width: 200px; height: 55px;}
.market_item .info .title {margin-bottom: 20px; font-size: 18px; line-height: 20px; color: #555;}
.market_item .info .link {font-size: 16px; line-height: 32px; color: #b4b4b4;}
.market_item .info .desc {height: 72px; color: #969696; word-break: break-all; overflow: hidden; display: -webkit-box; text-overflow: ellipsis; -webkit-box-orient: vertical; -webkit-line-clamp: 3;}
.market_item .bottom {padding: 20px; position: relative; display: flex; flex-direction: row; align-items: center; justify-content: space-between;}
.market_item .info+.bottom::before {content: ''; position: absolute; top: 0; left: 0; width: 100%; height: 1px; background-color: #edf0f5;}
.market_item .bottom .brief {width: calc(100% - 100px); color: var(--primaryColor); word-break: break-all; overflow: hidden; display: -webkit-box; text-overflow: ellipsis; -webkit-box-orient: vertical; -webkit-line-clamp: 2;}
.market_item .bottom .btn_submit {display: block;}
.third_market .statement {width: 720px; margin: 0 auto; padding: 100px 0 0; font-size: 12px; line-height: 24px; color: #b3b3b3; text-align: center;}
.third_market .pop_form {width: auto; top: 50%; left: 50%; transform: translate(-50%, -50%);}
.third_market form {box-sizing: border-box; width: 360px; padding: 20px;}
.third_market form .item {display: flex; flex-direction: row; justify-content: flex-start; width: 100%; font-size: 14px; line-height: 28px;}
.third_market form .item+.item {margin-top: 20px;}
.third_market form .head {position: relative; width: 100%; font-size: 18px;}
.third_market form .head .btn_close {position: absolute; top: 0; right: 0; width: 20px; height: 20px; display: block; transform: rotate(45deg);}
.third_market form .head .btn_close::before {content: ''; display: block; width: 20px; height: 1px; background-color: #bbb; position: absolute; top: 10px; left: 0;}
.third_market form .head .btn_close::after {content: ''; display: block; width: 1px; height: 20px; background-color: #bbb; position: absolute; top: 0; left: 10px;}
.third_market form .item .label {width: 30%;text-align: right; padding-right: 10px; line-height: 32px;}
.third_market form .item .input {width: 70%; text-align: left;}
.third_market form .item input {box-sizing: border-box; padding: 5px 10px; border: 1px solid #ddd; border-radius: 4px;}
.third_market form .submit_btn {margin: 0 auto; display: block; width: 120px; height: unset; color: #fff; border: 0px; background-color: #0cb083;}
.third_market form .submit_btn:disabled {background-color: #aaa;}
.third_market form .tips {margin-top: 18px; text-align: center; font-size: 12px; color: #b4b4b4;}
@media screen and (max-width: 1280px) {
	.market_item .info .pic img {max-width: 160px;}
}
/*************************** 第三方服务市场 end ***************************/

/*************************** 分销 Start ***************************/
.w_1400 #mta_box .nav>div, #mta_box .nav>dl, #mta_box .nav ul{margin-right:60px;}
.w_1400 #mta_box .nav dl dd a{margin:0 5px;}
.w_1400 #mta_box .nav ul li .form_input{width:170px;}

.distribution .inside_table{padding-top: 0;}
.distribution #mta .inside_table{padding: 0 0 20px;background: #fff;}
.distribution .box_select{width: 250px;}
.distribution .mta_menu{margin: 0 30px;}
.distribution .r_con_column{padding: 0px 30px 10px;}
.distribution .list_menu{height: 33px;line-height: 33px;padding-bottom: 10px;}
.distribution .search_form{margin-top: 0;}
.distribution .global_container{padding: 20px 24px;}

#withdraw_search label{width:12%; text-align:right; padding-right:2%;}
#withdraw_search .input{display:inline-block;}
#withdraw_search .input .form_input, #withdraw_search .input select{margin-bottom:5px; padding:0 5px; background-color:#fff; border:1px #ddd solid; border-radius:5px; display:inline-block;}
#withdraw_search .input .form_input{height:30px; line-height:30px;}
#withdraw_search .input select{height:32px; line-height:32px; vertical-align:top;}
#withdraw_search .input .btn_ok{display:inline-block; float:none;padding: 0 10px;height: 32px;border-radius: 5px;}
#withdraw_search .total{display:inline-block;}
#withdraw_search .total>li{margin-left:30px; display:inline-block;}

.box_data_list>li .item .count{margin-left:5px; font-size:14px; color:#555;}
/*************************** 分销 End ***************************/


/*************************** 买家秀 Start ***************************/
#gallery .bg_no_table_data {min-height: 420px;}
#gallery .bg_no_table_data .btn_add_item {width: 150px; height: 40px; line-height: 38px; margin: 25px auto; padding: 0; font-size: 16px;}
#gallery .header {margin: 0; padding: 10px 25px;}
#gallery .header .inner {display: flex; flex-direction: row; justify-content: space-between; align-items: center;}
#gallery .header .tips {padding: 9px 10px; border: 1px solid #ffdec6; background-color: #fff9f5; border-radius: 5px;}
#gallery .header .tips .global_app_tips {margin: 0;}
#gallery .header .tips .global_app_tips a {position: relative; color: #f68f44;}
#gallery .header .tips .global_app_tips a:after {content: ''; display: block; position: absolute; left: 0; bottom: 0; width: 100%; height: 1px; background-color: #f68f44;}
#gallery .header .btn_add_item {height: 34px; padding: 10px 17px; background-color: var(--primaryColor); color: #fff; border-radius: 5px; border-color: var(--primaryColor); line-height: 14px;}

#gallery table.r_con_table td:nth-child(1) {width: 3.42%;}
#gallery table.r_con_table td:nth-child(2) {width: 11.46%;}
#gallery table.r_con_table td:nth-child(3) {width: 22.41%;}
#gallery table.r_con_table td:nth-child(4) {width: auto;}
#gallery table.r_con_table td:nth-child(5) {width: 5%;}

#gallery tbody tr .icon_myorder {background-image: url(../images/view/icon_view.png); background-position: -102px -96px;}
#gallery tbody tr:hover .icon_myorder {background-position: -42px -186px;}

#gallery td img {max-width: 70px; max-height: 70px; border-radius: 4px;}
#gallery .pro_info {white-space: unset;}
#gallery .pro_info .plist {min-height: 70px ;position: relative; padding-left: 85px; display: flex; align-items: center;}
#gallery .pro_info img {position: absolute; top: 0; left: 0;}

#gallery .operation .btn.edit {display: block; width: 30px; height: 30px; margin: 0; padding: 0; border-radius: 36px; line-height: 30px; background: #eaf7ff url(../images/frame/icon_item_operation.png) no-repeat center 0; transition: all 0.3s ease-out;}
#gallery tr:hover .operation .btn.edit {background-color: #fff;}
#gallery .operation .btn.edit.button_tips {font-size: 0; color: transparent;}
#gallery  tr:hover .operation .btn.edit:hover {background: var(--primaryColor) url(../images/frame/icon_item_operation_hover.png) no-repeat center 0; cursor: pointer;}
#gallery .operation .btn.edit.disabled, #gallery .operation .btn.edit[disabled] {background: #f3f3f3 url(../images/frame/icon_item_operation_disable.png) no-repeat center 0;}

#gallery .gallery_form {width: 100%; font-size: 12px; line-height: 14px;}
#gallery .gallery_form .rows {margin: 0 0 35px;}
#gallery .gallery_form label {margin-bottom: 7px; font-size: 14px; line-height: 16px;}
#gallery .gallery_form .tips {margin: 0 0 15px; padding: 0;}
#gallery .gallery_form .multi_img, #gallery .gallery_form .multi_img .img {margin: 0; padding: 0;}
#gallery .gallery_form .box_drop_double {margin: 2px 0;}
#gallery .gallery_form .box_drop_double dd .drop_menu .drop_add{display: none;}
#gallery .move_next_top.disabled,
#gallery .move_prev_top.disabled { color: #bfbfbf; cursor: no-drop; }
/*************************** 买家秀 End ***************************/


/*************************** Facebook专页店铺 Start ***************************/
#index_set{margin:20px; padding-left:440px;}
#index_set .themes_box{position: relative;float: left;margin-left: -440px;width: 400px;border: 5px solid #fff;}
#index_set .themes_box .themes_pic{width:400px;}
#index_set .themes_box .abs_item{position:absolute; cursor:pointer;}
#index_set .themes_box .abs_item:hover:before, #index_set .themes_box .abs_item.cur:before{position: absolute;content: '';top: -4px;bottom: -4px;;left: -4px;;right: -4px;border:3px solid var(--primaryColor);}

.set_products_btn{height:31px;}
.set_products_btn .set_add{margin-top:0; margin-left:5px;}

.set_products_list{margin:20px 0; position:relative;}
.set_products_list:after{content: '';display: block;width: 100%;clear:both;}
.set_products_list .set_products_item{width:130px; margin:5px; background-color:#f5f5f5; border:1px transparent solid; border-radius:5px; position:relative; float:left;}
.set_products_list .set_products_item .edit_box{position: absolute;right: 5px;top: 5px;}
.set_products_list .set_products_item .edit_box a{display: block;width: 23px;height: 23px;margin-bottom: 5px;border-radius: 11.5px;}
.set_products_list .set_products_item .edit_box .main{background: #ccc url(../images/products/icon_star.png) no-repeat 0 0;}
.set_products_list .set_products_item .edit_box .main.p_main{background-color: #fec921;}
.set_products_list .set_products_item .edit_box .del{display: none;background: url(../images/frame/icon_delete_1.png) no-repeat center center;}
.set_products_list .set_products_item .edit_box .del:hover{background-image: url(../images/frame/icon_delete_1_current.png);}
.set_products_list .set_products_item .pic{height:120px; overflow:hidden; margin:0 auto; padding:10px; text-align:center; cursor:move; border-bottom:none;}
.set_products_list .set_products_item .pic img{max-width:100%; max-height:100%; display:inline-block; vertical-align:middle;}
.set_products_list .set_products_item .pic span{height: 100%; display:inline-block; vertical-align:middle;}
.set_products_list .set_products_item .content{width:90%; height:20px; line-height:20px; overflow:hidden; margin:0 auto; padding-bottom:5px; text-align:center; font-size:12px;}
.set_products_list .set_products_item .content>a{width:100%; overflow:hidden; display:block; white-space:nowrap; text-overflow:ellipsis; -webkit-text-overflow:ellipsis;}
.set_products_list .set_products_item:hover .edit_box .del{display: block;}
.set_products_list .set_products_item.placeHolder{background-color:#fff; border:1px #ddd dashed;}

.fixed_add_products .list_menu{height:auto; line-height:normal; padding:0; position:inherit!important; border:0; box-shadow:none;}
.fixed_add_products .search_form{margin-top: 0;margin-bottom: 28px;float: none;width: auto;padding-right: 36px;}
.fixed_add_products .search_form .search_btn{float: right;margin-right: -36px;}
.fixed_add_products .search_form .k_input{width: 100%;position: relative;}
.fixed_add_products .search_form .k_input .more{float: none;position: absolute;right: 0;top: 0;}
.fixed_add_products .search_form .k_input .form_input{float: none;width: 95%;}

.add_products_list{text-align: center;}
.w_1400 .add_products_list .add_products_item{margin: 0 10px 20px;}
.add_products_list .add_products_item{position: relative;width:133px; display: inline-block; vertical-align: top; margin: 0px 7px 14px;border-radius: 4px;border:1px solid #f1f1f1;}
.add_products_list .add_products_item .set_products_item{display: none;}
.add_products_list .add_products_item .img{border-bottom:none; padding:10px;height:115px; margin:0 auto; text-align:center;cursor:pointer;}
.add_products_list .add_products_item .img span{display:inline-block; height:100%; vertical-align:middle;}
.add_products_list .add_products_item .img img{vertical-align:middle; max-width:100%; max-height:100%;}
.add_products_list .add_products_item .img input{display:none;}
.add_products_list .add_products_item .img_mask{display:none; background: rgba(0,0,0,0.3) url(../images/photo/icon_item_hover.png) no-repeat 103px 7px; position:absolute; top: 0;left: 0; width: 100%;height: 100%; z-index:3;border-radius: 4px;}
.add_products_list .add_products_item .name{text-align:center; margin:0 auto; overflow:hidden; height:20px; line-height:20px; overflow:hidden; font-size:12px; white-space:nowrap; text-overflow:ellipsis;padding: 0 5px 5px;}
.add_products_list .add_products_item.cur .img_mask,.add_products_list .add_products_item:hover .img_mask{display:block;}
.add_products_list .add_products_item.cur .img_mask{background-image: url(../images/photo/icon_item_cur.png);}
/*************************** Facebook专页店铺 End ***************************/

/*************************** Paypal纠纷 Start ***************************/
.mb20{margin-bottom:20px;}
.ml5{margin-left:5px;}
.mr5{margin-right:5px;}

#dis .ratio_edit a{ margin-left: 5px; display: inline-block; color: var(--primaryColor); }
#dis .ratio_edit,
#dis .ratio_edit a{ font-size: 14px; }

#paypal_dispute .plugins_app_menu { margin-bottom: 10px; }
#paypal_dispute .search_box .k_input .search_btn { padding: 0; }
#paypal_dispute .list_menu .status_box { display: none; margin-top: 6px; margin-left: 20px; padding: 7px 30px 7px 10px; height: 21px; line-height: 21px;border: 1px solid #ffdec6; border-radius: 5px; background: #fff9f5; }
#paypal_dispute .list_menu .status_box.success { border-color: #d6ebde; background-color: #eaf8ec; }
#paypal_dispute .list_menu .status_box .status_tips { display: inline-block; height: 21px; font-size: 14px; color: #1f2328; }
#paypal_dispute .list_menu .status_box .status_tips a { margin-left: 24px; color: #0baf4d; }
#paypal_dispute .list_menu .status_box .iconfont { display: inline-block; vertical-align: text-top; margin-right: 5px; width: 21px; height: 21px; line-height: 21px; color: #f68f44; font-size: 20px; text-align: center; }
#paypal_dispute .list_menu .status_box .iconfont.icon-choose1 { background: #2eba65; color: #fff; border-radius: 50%; }
#paypal_dispute .list_menu .status_box .iconfont.icon_menu_close { background: #c00; color: #fff; border-radius: 50%; font-size: 10px; }
#paypal_dispute .box_table { padding-top: 10px; }

.dispute_status_list{height:136px; overflow:hidden;}
.dispute_status_list .item{width:33%; text-align:center; position:relative; float:left;}
.dispute_status_list .item>strong{height:25px; line-height:25px; font-size:14px; font-weight:bold; color:#333;}
.dispute_status_list .item .border{width:100%; position:absolute; top:47px; right:50%; border-bottom:3px #d7d7d7 solid;}
.dispute_status_list .item .choice{width:100%; height:28px; position:absolute; top:34px; left:0; z-index:2;}
.dispute_status_list .item .choice>i{width:28px; height:28px; margin:0 auto; background-color:#d7d7d7; border-radius:50px; display:block;}
.dispute_status_list .item .time{margin-top:47px; font-size:12px; color:#a0a4a3;}
.dispute_status_list .item .block_box{width:50%; height:28px; background-color:#edf0f2; position:absolute; top:34px; z-index:1; display:none;}
.dispute_status_list .item .block_left{left:0;}
.dispute_status_list .item .block_right{right:0;}
.dispute_status_list .item.current>strong{color:var(--primaryColor);}
.dispute_status_list .item.current .border{border-color:var(--primaryColor);}
.dispute_status_list .item.current .choice>i{background:url(../images/frame/icon_checkbox_current.png) no-repeat center var(--primaryColor);}
.dispute_status_list .item_1 .block_left{display:block;}
.dispute_status_list .item_3 .block_right{display:block;}
.dispute_status_list.claim .item{width:20%;}
.dispute_status_list.claim .item_3 .block_right{display:none;}

.dispute_parent_box{width:50%; float:left;}
.dispute_parent_box:first-child{width:50%;}
.dispute_parent_top_box{width:100%;}
.dispute_parent_top_box>.ml5{margin-left:0;}
.dispute_parent_top_box>.mr5{margin-right:0;}
.dispute_parent_top_box .global_container:first-child{margin-top:10px;}

.dispute_number{line-height:26px; margin-top:8px; padding-bottom:14px;}
.dispute_number>strong{display:inline-block; font-size:16px; color:#000;}
.dispute_number>span{display:inline-block; margin-left:10px; font-size:12px; color:#999;}

.box_dispute_info .global_container{padding:15px; border:1px #eee solid;}

.dispute_info .list>li{padding:6px 0; font-size:14px; color:#666;}
.dispute_info .list>li>strong{width:100px; float:left;}
.dispute_info .list>li>span{width:240px; line-height:20px; margin-left:0; display:inline-block; vertical-align:middle;}
.dispute_info .list>li>span.web_price{width:auto; font-size:12px; color:#999;}
.dispute_info .coupon_code{margin-left:105px; line-height:20px;}
.dispute_info .box_button{margin-top:10px;}
.dispute_info .box_button .btn_next, .dispute_info .box_button .btn_cancel{min-width:104px; margin-bottom:8px; margin-right:10px; padding:0 10px; text-align:center; float:left; display:block;}
.dispute_info .box_button .btn_next{color:#fff; background-color:#0cb083; border-color:#0cb083;}
.dispute_info .box_button .btn_cancel{border-color:#e6e6e8;}
.dispute_info .box_button .btn_refund{background-color:#fff; border-color:#e6e6e8;}
.dispute_info .box_button .btn_refund:hover{text-decoration:none; background-color:#f4f4f4;}
.dispute_info .box_button .btn_loading{font-size:0; background:url(../images/global/loading_small.gif) no-repeat center #f3f3f3; cursor:no-drop;}

.fixed_evidence .file_list .file_item { display: flex; align-items: center; justify-content: space-between; margin-top: 10px; }
.fixed_evidence .file_list .file_item:first-child { margin-top: 0; }
.fixed_evidence .file_list .evidence_file_remove { display: block; width: 34px; height: 34px; text-align: center; line-height: 34px; }
.fixed_evidence .file_list .evidence_file_remove::before { content: "\e60b"; width: 34px; height: 34px; color: var(--GlobalAssistColor); font-family: "iconfont"!important; font-size: 16px; font-style: normal; -webkit-font-smoothing: antialiased; -moz-osx-font-smoothing: grayscale; }
.fixed_evidence .box_package { margin-bottom: 10px; padding: 20px; background-color: #fafafa; }
.fixed_evidence .box_package .box_select,
.fixed_evidence .box_package input { background-color: #fff; }
.fixed_evidence .box_package .rows:nth-child(2) { margin-bottom: 15px; }
.fixed_evidence .box_package .btn_package_remove { display: none; border: 1px #ccdced solid; border-radius: 5px; width: 34px; height: 34px; text-align: center; line-height: 34px; }
.fixed_evidence .box_package .btn_package_remove::before { content: "\e60b"; width: 34px; height: 34px; color: var(--GlobalAssistColor); font-family: "iconfont"!important; font-size: 16px; font-style: normal; -webkit-font-smoothing: antialiased; -moz-osx-font-smoothing: grayscale; }
.fixed_evidence .box_package:nth-child(n+2) .btn_package_remove { display: block; }
.fixed_evidence .btn_package_add { display: block; height: 20px; margin-top: 15px; color: var(--primaryColor); line-height: 20px; }
.fixed_evidence .btn_package_add:before { content: "\e6ba"; display: inline-block; vertical-align: top; width: 20px; height: 20px; margin-right: 7px; text-align: center; font-size: 10px; font-family: "iconfont"!important; font-style: normal; color: #fff; -webkit-font-smoothing: antialiased; -moz-osx-font-smoothing: grayscale; background-color: var(--primaryColor); border-radius: 50px; }

#messages{margin-right:-15px; margin-left:-15px; padding-bottom:50px;}
#messages .row { display: flex; margin-top: 15px; }
#messages .row .col_box{display:inline-block; vertical-align:top; padding:0 15px;}
#messages .row .col_left{width:70px;}
#messages .row .col_right{width:670px;}
#messages .row .avatar{display:block; width:58px; height:58px; line-height:55px; background-color:rgba(50,50,50,0); border:2px #cdcdcd solid; border-radius:50%; text-align:center;}
#messages .row .avatar.isMe{border-color:#333; color:#333;}
#messages .row .message_bubble{border:1px #cdcdcd solid; border-radius:4px; -moz-border-radius:4px; -khtml-border-radius:4px; -webkit-border-radius:4px; padding:20px; position:relative; box-shadow:0 0 3px 1px rgba(205,205,205,.5); -webkit-box-shadow:0 0 3px 1px rgba(205,205,205,.5);}
#messages .row .message_bubble>p{padding:4px 0 14px; color:#333;}
#messages .row .message_bubble>p>a{text-decoration:underline;}
#messages .row .message_bubble .message_tips{border-bottom:1px #ccc solid; margin-bottom:20px;}
#messages .row .message_bubble .message_body{font-size:14px; line-height:1.6;}
#messages .row .message_bubble .message_footer{padding-bottom:10px; font-size:12px;}
#messages .row .message_bubble .offer_detail{line-height:1.6; border-bottom:1px #ccc solid; margin-bottom:20px; padding-top:0;}
#messages .row .message_bubble:before{width:14px; height:14px; background:#fff; border:1px solid #cdcdcd; border-top:0; border-right:0; position:absolute; top:22px; left:-8px; content:''; transform:rotate(45deg); -o-transform:rotate(45deg); -ms-transform:rotate(45deg); -moz-transform:rotate(45deg); -webkit-transform:rotate(45deg); box-shadow:-4px 4px 5px -2px rgba(205,205,205,.5); -webkit-box-shadow:-4px 4px 5px -2px rgba(205,205,205,.5);}
#messages .row .form_control{width:644px; border:1px #ccc solid; border-radius:4px; padding:6px 12px; font-size:14px; line-height:1.6; color:#333; box-shadow:inset 0 1px 1px rgba(0,0,0,0.075);}
#messages .compose{margin-bottom:30px;}
#messages .message_file_link.loading { color: #aaa; cursor: no-drop; }

.right_list{margin-top:10px;}
.right_list .rows{padding:8px 0;}
.right_list .rows>span{line-height:20px; padding-right:10px; color:#555;}

.order_details .right_list .rows>strong, .order_details .right_list .rows>span{padding-right:10px;}
.order_details .right_list .rows>strong{width:82px; float:left; color:#000}
.order_details .right_list .rows>span{line-height:20px; margin-left:92px; color:#555; display:inherit;}
.order_details .right_list .rows>span>a{text-decoration:underline;}

.box_fixed_download_tips .box_middle .title { font-size: 0; }
.box_fixed_download_tips .box_middle .content { padding: 0 40px 40px; text-align: center; }
.box_fixed_download_tips .box_middle .tips_content { width: 70%; margin: 0 auto; padding: 10px 0 50px; font-size: 20px; line-height: 1.5; }
.box_fixed_download_tips .box_middle .tips_button { display: flex; justify-content: center; gap: 20px; }
.box_fixed_download_tips .box_middle .tips_button .btn_global { min-width: 120px; height: 40px; padding: 0 30px; line-height: 38px; }

/*************************** Paypal纠纷 End **************************/

/*************************** 评论 start ***************************/
#custom_comments .list_title{margin: 10px 30px;}
#custom_comments .edit_box, #custom_comments .edit_box *{line-height: 24px;}
#custom_comments .input img{max-width: 500px;}
#custom_comments .r_con_table .side_by_side a{ margin-left: 6px; }
#custom_comments .r_con_table .side_by_side a:first-child{ margin-left: 0; }

#custom_comments .list_menu_button>li>a.save{display: none;background: var(--primaryColor);padding: 0 25px;color: #fff;}
#custom_comments .r_con_table .box_input{border-color: transparent;}
#custom_comments .r_con_table .box_input.cur{border-color: #ddd;}
#custom_comments .r_con_table .bat_edit{display: none;}
#custom_comments .r_con_table .bat_edit_item{padding: 20px 10px;}
#custom_comments .r_con_table .star{ z-index: 0; }

#custom_comments .r_con_table .menu_img{position: relative; margin-top: 5px;}
#custom_comments .r_con_table .menu_img .menu_img_txt{display: inline-block;padding-right: 15px;position: relative;cursor: pointer;}
#custom_comments .r_con_table .menu_img .menu_img_txt i{width: 9px;height: 6px;position: absolute;right: 0;top:8px;background-image: url(../images/orders/icon_arrow1.png);transition: all 0.3s;}
#custom_comments .r_con_table .menu_img:hover .menu_img_txt i{transform: rotate(180deg);}
#custom_comments .r_con_table .menu_img:hover .menu_img_container{opacity: 1; z-index: 2;}
#custom_comments .r_con_table .menu_img .menu_img_container{position: absolute; min-width: 265px; padding:12px;background-color: #fff; left: 0;top: 100%;z-index: -1;opacity: 0; box-shadow: 0 0 10px rgba(0,0,0,0.1);border-radius: 5px; }
#custom_comments .r_con_table .menu_img .menu_img_container .menu_img_item{display: inline-block;position: relative; width: 56px;height: 56px; margin-top: 10px; margin-right: 10px; border-radius: 5px;overflow: hidden;vertical-align: middle;}
#custom_comments .r_con_table .menu_img .menu_img_container .menu_img_item img{max-width: 100%;max-height: 100%;}
#custom_comments .r_con_table .menu_img .menu_img_container .menu_img_item .icon_multi_view{display: none; position: absolute; width: 100%;height: 100%; top:0;left:0;background-color: rgba(0,0,0,.5); background-repeat: no-repeat;background-position: center; z-index: 1;}
#custom_comments .r_con_table .menu_img .menu_img_container .menu_img_item .icon-video2{ font-size: 20px; color: #fff; position: absolute; right: 0; top: 0; left: 0; bottom: 0; pointer-events: none; z-index: 1; background: rgb(0, 0, 0, .6); display: flex; align-items: center; justify-content: center;}
#custom_comments .r_con_table .menu_img .menu_img_container .menu_img_item:hover .icon_multi_view{display: block;}
#custom_comments .r_con_table .menu_img .menu_img_container .menu_img_item:nth-child(-n+4){margin-top: 0;}
#custom_comments .r_con_table .menu_img .menu_img_container .menu_img_item:nth-child(4n){margin-right: 0;}

#custom_comments .review_box{border-bottom: 1px solid #e9e9e9;padding-bottom: 28px;padding-top: 16px;}
#custom_comments .review_box .del{display: none;margin-top: 13px;}
#custom_comments .review_box .star_box{height: 30px;line-height: 30px;}
#custom_comments .review_box .agree{display: inline-block;height: 30px;line-height: 30px;margin-left: 20px;padding-left: 27px;color: #999;font-size: 14px;background: url(../images/products/icon_good.png) no-repeat left center;}
#custom_comments .review_box .msg{line-height: 20px;font-size: 14px;padding-bottom: 11px;padding-top: 16px;padding-right: 30px;}
#custom_comments .review_box .r_img{height: 60px;margin: 14px 0;}
#custom_comments .review_box .r_img .list{float: left;width: 58px;height: 58px;margin-right: 7px;border:1px solid #dddddd;text-align: center;vertical-align: middle;font-size: 0;}
#custom_comments .review_box .r_img .list img{vertical-align: middle;max-width: 100%;max-height: 100%;}
#custom_comments .review_box .r_img .list:after{display: inline-block;vertical-align: middle;height: 100%;content: '';}
#custom_comments .review_box .info{padding-top: 12px;height: 20px;line-height: 20px;font-size: 12px;}
#custom_comments .review_box .info span{padding-left: 20px;color: #999;}
#custom_comments .review_box:hover .del{display: block;}
#custom_comments .review_box .r_img .list a{position: relative; display: flex; width: 100%; height: 100%; align-items: center; justify-content: center;}
#custom_comments .review_box .r_img .list .video_box .icon-video2{font-size: 20px; color: #fff; position: absolute; right: 0; top: 0; left: 0; bottom: 0; pointer-events: none; z-index: 1; background: rgb(0, 0, 0, .6); display: flex; align-items: center; justify-content: center;}

#custom_comments .form_remark_log{margin-top:30px; background-color:#f8f8f8; border:1px #cbcecd solid; border-radius:4px;}
#custom_comments .form_remark_log .form_box{margin:8px; height:32px; line-height:32px;}
#custom_comments .form_remark_log .remark_left{width:100%; margin-left:-55px; float:left;}
#custom_comments .form_remark_log .remark_left>div{margin-left:55px;}
#custom_comments .form_remark_log .box_input{width:100%; height:32px; line-height:32px; padding:0; border:0; vertical-align:top;}
#custom_comments .form_remark_log .box_input:focus{box-shadow:none;}
#custom_comments .form_remark_log .btn_save{width:50px; height:32px; color:#fff; cursor:pointer; border-radius:4px; border:0; float:right;}

#custom_comments .btn_download{color:#fff; background-color:var(--primaryColor);}
#custom_comments .btn-success{background: var(--primaryColor);border-color: var(--primaryColor);}
#custom_comments .fileinput-button:hover, #custom_comments .fileinput-button:focus, #custom_comments .fileinput-button:active, #custom_comments .fileinput-button.active{color:#fff; background-color:#449d44; border-color:#449d44;}

#fixed_right .review_star{display: block;padding-top: 5px;}
#fixed_right .review_star span{display: inline-block;vertical-align: middle;width: 18px;height: 18px;background: url(../images/global/star.png) no-repeat;cursor: pointer;}
#fixed_right .review_star a{vertical-align: middle;margin-left: 15px;}
#fixed_right .review_star .star_0{background-position: -100px -40px;}
#fixed_right .review_star .star_1{background-position: 0 -40px;}

#fixed_right .fixed_collect_reviews { background-color: #fff; }
#fixed_right .fixed_collect_reviews .step_list { padding-bottom: 30px; }
#fixed_right .fixed_collect_reviews .step_item { border-radius: 4px; overflow: hidden; margin-top: 20px; padding: 20px; background-color: #f8f9fb; }
#fixed_right .fixed_collect_reviews .step_item>strong { color: #1f2328; line-height: 2; }
#fixed_right .fixed_collect_reviews .step_item>p { margin-top: 6px; font-size: 12px; color: #7d8d9e; line-height: 1.75; }
#fixed_right .fixed_collect_reviews .step_item .rows { margin: 0; font-size: 14px; line-height: 30px; }
#fixed_right .fixed_collect_reviews .step_item .rows label { display: block; font-size: 14px; color: #1f2328; line-height: 30px; }
#fixed_right .fixed_collect_reviews .step_item .copy { display: block; padding-left: 17px; color: var(--primaryColor); font-size: 12px; position: relative; }
#fixed_right .fixed_collect_reviews .step_item .copy::before { content: ''; display: block; position: absolute; left: 0; top: 8px; width: 15px; height: 15px; background: url(../images/plugins/app/erp_copy.png) no-repeat 0 1px; }
#fixed_right .fixed_collect_reviews .step_item .access_key { word-break: break-all; font-size: 12px; color: #7d8d9e; line-height: 1.5; }
#fixed_right .fixed_collect_reviews .step_item .btn_install_collect { display: inline-block; border-radius: 5px; height: 30px; margin-top: 10px; padding: 0 18px; text-decoration: none; font-size: 12px; color: #fff; line-height: 30px; background-color: var(--primaryColor); }
#fixed_right .fixed_collect_reviews .step_menu { position: fixed; bottom: 0; left: 20px; width: 305px; padding: 10px 0; background-color: #fff; }
#fixed_right .fixed_collect_reviews .step_menu .close { display: inline-block; vertical-align: top; border-radius: 5px; }
#fixed_right .fixed_collect_reviews .step_menu .tips { display: inline-block; vertical-align: top; height: 30px; line-height: 30px; margin-left: 10px; padding: 0; font-size: 12px; color: #404852; }
#fixed_right .fixed_collect_reviews .step_menu .tips>a { color: var(--primaryColor); }

#progress_container{display: none;}
#progress_container table{margin: 10px 0;}
#progress_container .r_con_table tr.error *{color: #c00;}
#progress_container .r_con_table tr td{white-space: normal;}

#progress_loading{width:32px; height:32px; line-height:32px; margin:15px auto 30px; text-align:center; font-size:16px; color:#000; background:url(../images/global/loading.gif) no-repeat left center; display:block;}
#progress_loading.completed{width:auto; background:none;}
#progress_loading.completed .btn_cancel{width:90px; margin:5px auto 20px; display:block;}

#custom_comments .comments_ctrl .list_menu_button {float: right;}
#custom_comments .comments_ctrl .search_form {float: left;}

/* 亚马逊采集评论 */
#custom_comments .list_menu_button>li>a.add{margin-right: 10px;}
#custom_comments .list_menu_button>li>a.quick_more{height: 32px; line-height: 32px; padding:0 13px 0 15px; color:#434a53;  border: 1px solid var(--GlobalBtnSimpleBorderColor); background-color:transparent;}
#custom_comments .list_menu_button>li>a.quick_more>em{width:8px; height:5px; margin-left:11px; margin-top:13px; background:url(../images/frame/icon_arrow_down_small_blue.png) no-repeat center; position:relative; display:inline-block; vertical-align:top; transition:all .4s; -webkit-transition:all .4s; transform:rotate(0); -webkit-transform:rotate(0);}
#custom_comments .list_menu_button>li:hover>a.quick_more{background-color: #f4f5f6;}
#custom_comments .list_menu_button>li:hover>a.quick_more>em{transform:rotate(180deg); -webkit-transform:rotate(180deg);}
#custom_comments .list_menu_button>li .quick_more_menu{width:97px; padding:7px 0; margin-top: 6px; text-align:center; background-color:#fff; position:absolute; top:21px; left:0; z-index:1; display:none; opacity:0; filter:alpha(opacity=0);}
#custom_comments .list_menu_button>li .quick_more_menu>a{width:inherit; height:27px; line-height:27px; text-decoration:none; font-size:12px; color:#666; display:block;}
#custom_comments .list_menu_button>li:hover .quick_more_menu{display:block;}

#custom_comments button.warning{background-color: #f68f44; font-size: 12px; cursor: unset;}

#crawl_amazon .container {width: 350px;}
#crawl_amazon form, #crawl_amazon form .item {display: flex; flex-direction: column;}
#crawl_amazon form .item .input {display: inherit;}
#crawl_amazon form .item+.item {margin-top: 36px;}
#crawl_amazon form .global_select_box {width: 100%;}
#crawl_amazon form .global_select_box .select_ul {padding: 8px 0;}
#crawl_amazon form .global_select_box .item {padding: 0 20px; line-height: 32px; font-size: 12px;}
#crawl_amazon form .global_select_box .item+.item {margin-top: 0px;}
#crawl_amazon form .item label, #crawl_amazon form .item .label {margin-bottom: 8px;}
#crawl_amazon form textarea {width: 100%; padding: 5px; border-radius: 5px;}
#crawl_amazon form textarea:not([readonly]):focus {border-color: var(--primaryColor);}
#crawl_amazon .processing.statement {padding: 20px; border-radius: 5px; font-size: 14px; background-color: #f68f44; color: #fff;}
#crawl_amazon .footer button+button {margin-left: 20px;}
/*************************** 评论 end ***************************/

/*************************** 购物车召回 start ***************************/
#shopping_cart_recall{padding:0 50px;}
#shopping_cart_recall .recall_title{height:90px; background:url(../images/plugins/shopping_cart_recall_logo.png) no-repeat left center; margin:20px 0 30px; padding:0; padding-left:111px;}
#shopping_cart_recall .recall_title>h1{height:57px; line-height:57px; padding:0; font-size:30px; color:#000;}
#shopping_cart_recall .recall_title .box_explain{height:26px; line-height:26px; font-size:14px;}
#shopping_cart_recall .recall_set_list{padding-bottom:50px;}
#shopping_cart_recall .recall_set_list .item{overflow:hidden; background-color:#fafafa; border-radius:4px; margin-bottom:20px; padding:15px 20px;}
#shopping_cart_recall .recall_set_list .item .item_left{max-width:70%; line-height:36px; color:#555;}
#shopping_cart_recall .recall_set_list .item .item_right{padding-top:22px;}
#shopping_cart_recall .recall_set_list .item .title{display:block; font-size:18px; color:#000;}
#shopping_cart_recall .recall_set_list .item .used_tips{display:block; line-height:20px;}
#shopping_cart_recall .recall_set_list .item .btn_recall_cart_edit{display:none; min-width:60px; height:27px; line-height:26px; margin-right:10px; padding:0 20px; color:#555;}
#shopping_cart_recall .overview_list .rows{margin-top:15px;}
#shopping_cart_recall .overview_list .rows>strong{display:block; height:30px; line-height:30px; font-size:14px; color:#555;}
#shopping_cart_recall .overview_list .rows>span{display:block; height:48px; line-height:48px; font-size:25px; color:#000;}
#shopping_cart_recall .overview_list .rows:first-child{margin-top:0;}
#shopping_cart_recall .global_app_tips{color:#f68f44;}
.fixed_recall_cart .box_type .input_radio_box{display:block; margin-top:15px;}
.fixed_recall_cart .box_type .input_radio_box:first-child{margin-top:8px;}

/*************************** 购物车召回 end ***************************/

/*************************** 协议 start ***************************/
#agreement .global_container{margin-top:10px; position:relative;}
#agreement .box_use_agreement{position:absolute; top:29px; right:20px;}

.fixed_btn_submit .center_container_1000{margin-top:0; margin-bottom:0;}

/*************************** 协议 end ***************************/

/*************************** 产品采集 start ***************************/
#product_collection .r_con_table tbody td.img.pic_box{width:85px; height:85px; text-align:center; display:block;}
#product_collection .r_con_table tbody td.img.pic_box>img{overflow:hidden; border-radius:4px;}
#product_collection .crawl_form{width: 700px;margin: 16px auto 0 auto;font-size: 0;border-radius: 25px;overflow: hidden;}
#product_collection .crawl_form .crawl_input{width: 590px;height: 50px;line-height: 50px;padding: 0 20px;background: #fff;border: 0;box-sizing: border-box;}
#product_collection .crawl_form .crawl_button{width: 110px;height: 50px;line-height: 50px;background: var(--primaryColor);border: 0;color: #fff;font-size: 16px;}
/*************************** 产品采集 end ***************************/

/*************************** 定制属性 start ***************************/
#custom_attributes .rows{margin-bottom: 31px;}
#custom_attributes .rows.switchery_rows{margin-bottom: 0;}
#custom_attributes .rows.switchery_rows .input{margin-top: 14px;}
#custom_attributes .global_container{overflow:visible;}
#custom_attributes .select_box{display:none; position:relative; margin-bottom: 10px;}
#custom_attributes .select_box .btn_option_clear{background:url(../images/products/icon_square_delete.png) no-repeat left; padding-left:19px; position:absolute; top:5px; right:0; color:#7d8d9e;}
#custom_attributes .apply_box{margin-bottom:0;}
#custom_attributes .box_drop_double, #custom_attributes .box_select{max-width:100%;}
#custom_attributes .maxcount_box{display:none;}
#custom_attributes .option_box{display:none;}
#custom_attributes .option_box .option_row{border-top:1px #edf0f5 solid; padding-top:17px;}
#custom_attributes .option_box .option_row:first-child{border:0; padding-top:0;}
#custom_attributes .extend_info{width:100%;  border-radius:5px; margin-bottom:0px; box-sizing: border-box;}
#custom_attributes .extend_info .info .title{line-height:20px; margin-bottom:5px; font-size:12px;color:#404952}
#custom_attributes .extend_info .info .value{line-height:14px; margin-bottom:12px; font-size:12px;color:#7d8d9e}
#custom_attributes .extend_info .btn_extend_remove{margin-left:11px;}
#custom_attributes .extend_info .btn_extend_add{display:none;}
#custom_attributes .extend_info.add{background-color:transparent; padding:0;}
#custom_attributes .extend_info.add .info{display:none;}
#custom_attributes .extend_info.add .btn_extend_add{display:block;}
#custom_attributes .btn_attr_add{background-color:var(--primaryColor); margin-top:20px; color:#fff;}
#custom_attributes .btn_attr_add:before, #custom_attributes .btn_attr_add:after{background-color:#fff;}
#custom_attributes .placeHolder{height:76px; overflow:hidden; border:1px #aaa dashed; background-color:#fff;}

#custom_attributes .r_con_table {margin-top: 10px;}

.attribute_container{position:relative;}
.attribute_container .attribute_menu{position:absolute; top:20px; right:20px;}
.attribute_container .attribute_menu>button{display:inline-block; vertical-align:top; width:36px; height:36px; background-repeat:no-repeat; background-position:center; background-color:#f4f5f6; border:0; border-radius:50%; margin-left:10px;}
.attribute_container .attribute_menu>button.move_up{background-image:url(../images/plugins/app/custom_attributes_move.png); transform:rotate(180deg); -webkit-transform:rotate(180deg);}
.attribute_container .attribute_menu>button.move_down{background-image:url(../images/plugins/app/custom_attributes_move.png);}
.attribute_container .attribute_menu>button.copy{background-image:url(../images/plugins/app/custom_attributes_copy.png);}
.attribute_container .attribute_menu>button.delete{background-image:url(../images/plugins/app/custom_attributes_delete.png);}
.attribute_container .attribute_menu>button.shrink{background-image:url(../images/plugins/app/custom_attributes_shrink.png);}
.attribute_container .attribute_title {width: calc(100% - 184px); min-height: 36px; line-height: 36px; overflow: hidden; font-size: 20px; color: #000;}
.attribute_container .attribute_body{padding:20px 0 0;}
.attribute_container.show .attribute_menu>button.shrink{transform:rotate(180deg); -webkit-transform:rotate(180deg);}
.attribute_container.move{height:36px; overflow:hidden!important;}
.attribute_container .global_app_tips.obvious{ display: none; margin: 0 0 10px; padding: 9px; }
.attribute_container .global_app_tips.obvious a{ color: #F68F44; }


.default_attribute_container{display:none;}

.custom_list .btn_option{display:block; width:34px; height:34px; line-height:38px; border:1px #ddd solid; border-radius:5px; margin-top:30px; text-align:center;}
.custom_list .btn_option>i{display:inline-block; width:14px; height:14px; background-repeat:no-repeat; background-position:center;}
.custom_list .btn_option_add{border-top-right-radius:0; border-bottom-right-radius:0;}
.custom_list .btn_option_add>i{background-image:url(../images/products/icon_square_add.png);}
.custom_list .btn_option_remove{border-left-width:0; border-top-left-radius:0; border-bottom-left-radius:0;}
.custom_list .btn_option_remove>i{background-image:url(../images/products/icon_square_delete.png);}
.custom_list .button.hide_add .btn_option_add{display:none;}
.custom_list .button.hide_add .btn_option_remove{border-left-width:1px; border-radius:5px;}
.custom_list .button.hide_remove .btn_option_add{border-radius:5px;}
.custom_list .button.hide_remove .btn_option_remove{display:none;}

.btn_extend{color:var(--primaryColor);}

.fixed_extend .btn_option_add, .fixed_extend .btn_option_remove{margin:0;}
.fixed_extend .extend_box>label{float:left;}
.fixed_extend .extend_box>label:first-child{display: none;}
.fixed_extend .extend_box>label.float{width:111px; margin-right:98px; float:right;}
.fixed_extend .extend_box .extend_row .rows{margin-right:10px; margin-bottom:10px;}
.fixed_extend .extend_check_box>label.float{display:none;}
.fixed_extend .extend_check_box .extend_max_row{display:none;}
.fixed_extend .extend_check_box>label:first-child{display: block;}
.fixed_extend .extend_check_box>label:nth-child(3){display: none;}

/* [new] */
#custom_attributes .input_radio_side_box{width: calc(100% / 3 - 17px);margin-right: 25px;margin-bottom: 30px;}
#custom_attributes .input_radio_side_box:nth-child(3){margin-right: 0;}
#custom_attributes .input_radio_side_box .input_radio{width: calc(100% - 2px);}
#custom_attributes .option_box {margin-bottom: 9px;}
#custom_attributes .option_box .option_row{margin-bottom: 30px;padding-top: 20px;}
#custom_attributes .option_box .multi_img{margin-top: 0;}
#custom_attributes .option_box .pro_multi_img .img{width:auto; height:auto;}
#custom_attributes .option_box .pro_multi_img .img .preview_pic{width:50px; height:50px;}
#custom_attributes .option_box .pro_multi_img .img .upload_btn{width:48px; height:48px; background-image: url(../images/frame/bg_multi_img_add.png); background-color: var(--GlobalPicUploadBgColor); border: 1px var(--primaryColor) dashed; background-size:12px; border-radius:5px;}
#custom_attributes .option_box .pro_multi_img .img .preview_pic>a{width:48px; height:48px; overflow:hidden; border:1px #ddd solid; border-radius:5px; padding:0;}
#custom_attributes .option_box .pro_multi_img .img .pic_btn{border-radius:5px;}
#custom_attributes .option_box .pro_multi_img .img .pic_btn>a.del{width:16px; height:16px;}
#custom_attributes .option_box .pro_multi_img .img .pic_btn>a .icon_multi_delete{background-size:100%;}
#custom_attributes .option_box .pro_multi_img .myorder, #custom_attributes .option_box .pro_multi_img .video_edit{display:none;}
#custom_attributes .option_row .box_input,#custom_attributes .option_row .unit_input{height: 48px;line-height: 48px;}
#custom_attributes .custom_list {padding: 20px 20px 0 20px; border: 1px solid #edf0f5;}
#custom_attributes .custom_list .btn_option{margin-top: 36px;}
#custom_attributes .custom_list .rows label {font-size: 12px;}
#custom_attributes .pro_multi_img .img.isfile, #custom_attributes .pro_multi_img .img.show_btn, #custom_attributes .pro_multi_img .video{margin:0;}
#custom_attributes .global_form .rows label span{color:#7d8d9e;}
/*************************** 定制属性 end ***************************/

/*************************** facebook start ***************************/
#facebook_flow{min-height: 77px;}
#facebook_flow.loading{background: url(../images/global/loading.gif) no-repeat center;}
/*************************** facebook end ***************************/

/*************************** 水印 start ***************************/
.watermark_set .watermark_tab{border-collapse:collapse; background:#fff; margin-top:3px;}
.watermark_set .watermark_tab .item{border:1px solid #e8e8e8; cursor:pointer; text-align:center; line-height:120%;}
.watermark_set .watermark_tab .item>div{position:relative; width:60px; padding:20px 10px;}
.watermark_set .watermark_tab .item .filter{position:absolute; left:0; top:0; width:100%; height:100%; background:#000; filter:alpha(opacity=70); -moz-opacity:0.7; opacity:0.7; display:none;}
.watermark_set .watermark_tab .item .check{position:absolute; left:0; top:0; width:100%; height:100%; background:url(../images/frame/selected_icon.png) no-repeat center center; display:none;}
.watermark_set .watermark_tab .item_on .filter, .watermark_set .watermark_tab .item_on .check{display:block;}
.watermark_set .preview{width:249px; height:249px; overflow:hidden; background:url(../images/frame/demo.jpg) no-repeat;}
.watermark_set .preview .watermark_tab{background:none;}
.watermark_set .preview .watermark_tab .item{width:83px; height:83px; padding:0; border:0;}
.watermark_set .preview .watermark_tab .item img{max-width:83px; max-height:83px; text-align:center;}
.watermark_set .watermark_position{float: left;width: 100px;height: 100px;padding: 9px;border:1px dashed transparent;border-radius: 4px;margin-right: 10px;background: url(../images/set/watermark_position_1.png) no-repeat center center;cursor: pointer;}
.watermark_set .watermark_position_5{background-image: url(../images/set/watermark_position_5.png);}
.watermark_set .watermark_position_9{background-image: url(../images/set/watermark_position_9.png);}
.watermark_set .watermark_position.cur{border-color: var(--primaryColor);}
.watermark_set #slider_box #slider{width:200px; height:10px; margin:2px 6px 0 7px;}
.watermark_set #slider_box span{font-size:11px; color:#777;}
.watermark_set .themes_progress .input_button{text-align: center;}
.watermark_set .themes_progress .input_button .btn_global{display: none;width: 128px;}
.watermark_set .rows li{line-height: 28px;background: #fafafa;border-right: 3px;margin-top: 20px;padding: 11px 100px 11px 20px;position: relative;color: #888;font-size: 14px;}
.watermark_set .rows li:first-child{margin-top: 10px;}
.watermark_set .rows li strong{display: block;color: #000;}
.watermark_set .rows li .rcon{height: 20px;position: absolute;top: 0;right: 20px;bottom: 0;margin:auto;color: #000;}
.watermark_set .rows li em{color: #f5222d;}
/*************************** 水印 end ***************************/

/*************************** sitemap start ***************************/
.sitemap .inside_table{padding: 0;}
.sitemap .plugins_app_menu{width: 100%;position: absolute;top: 0;left: 0;}
.sitemap .guide_index_box .sec_tit{font-size: 20px;color: #000;}
.sitemap .guide_index_box .guide_con .desc{margin-top: 10px;margin-bottom: 30px;}
.sitemap .guide_index_box .guide_con .desc p{margin-bottom: 20px;font-size: 18px;color: #000;}
.sitemap .guide_index_box .guide_con .btn{padding: 0 50px;}
.sitemap .input_button .btn_global{display: none;min-width: 128px;}
.sitemap .r_con_table .update{display: block; width: 42px; height: 42px;border-radius: 50%;}
.sitemap .r_con_table tr:hover .update{background: #fafffe url(../images/plugins/icon_update.png);}
.sitemap .circle_container_close{display: none;}
.sitemap #btn_progress_continue{display: none;}
.sitemap #btn_progress_keep{display: none;}
.sitemap #box_circle_container .btn_progress_completed{margin-top:15px;}
/*************************** sitemap end ***************************/

/*****************************language start***********/
#lang_set .payment_used .switchery.checked{cursor: no-drop;}
/*****************************language start***********/

#googlefeed_inside .rows>label>span a{color: var(--primaryColor);}
#googlefeed_inside .rows .turn_right{background-image: url(../images/frame/icon_turn_right.png);}


/************************** 亚马逊采集 Start [on 20210806 by Eddie] **************************/
#amazon_collection .inside_table {padding: 0;}
#amazon_collection .flex_row {display: flex; flex-direction: row;}
#amazon_collection .flex_column {display: flex; flex-direction: column;}
#amazon_collection .flex_row > *, #amazon_collection .flex_column > * {display: inherit;}

.table_list {box-sizing: border-box; position: relative; display: inherit; border: 0; border-radius: 5px; font-size: 14px; line-height: 16px;}
.table_list > .thead {padding: 20px 25px; border-top-left-radius: 5px; border-top-right-radius: 5px;}
.table_list > .thead .btn_submit {padding: 7px 23px; border-radius: 5px; line-height: normal;}
.table_list > .thead .search {flex: 1;}
.table_list > .thead .search .input {padding: 10px; border: 1px solid #ccdced; border-radius: 5px;}
.table_list > .thead .search .input:active, .table_list > .thead .search .input:focus, .table_list > .thead .search .input:focus-within {border-color: var(--primaryColor); box-shadow: 0 0 5px 1px rgb(11 175 77 / 30%);}
.table_list > .thead .search input {width: 320px; border: 0; font-size: 12px;}
.table_list > .thead .search .item+.item {margin-left: 10px;}
.table_list > .thead .search .btn_submit {padding: 7px 13px;}

.table_list > .thead .search button.warning {padding: 7px 13px; background-color: #f68f44; border-color: #f68f44; border-radius: 5px; color: #fff; font-size: 12px; cursor: unset;}

.table_list > .tbody, .table_list > .tbody table {margin: 0; padding: 0px;}
.table_list tbody td {font-size: 14px;}
.table_list tbody .pic {width: 80px; height: 80px; position: relative; display: block; background-color: #f0f0f0; border-radius: 4px; text-align: center; overflow: hidden;}
.table_list tbody .pic img {width: 100%; height: 100%; position: absolute; top: 0; left: 0;}
.table_list tbody .title {display: -webkit-box; overflow: hidden; text-overflow: ellipsis; line-clamp: 2;-webkit-line-clamp: 2; -webkit-box-orient: vertical;}
.table_list tbody .operate .btn {display: block; width: 30px; height: 30px; margin: 0; padding: 0; border-radius: 36px; background-color: #fff; line-height: 30px; font-size: 0; color: transparent;}
.table_list tbody .operate .btn.publish {background: #eaf7ff url(../images/frame/icon_item_operation.png) no-repeat center -180px; transition: all 0.3s ease-out;}
.table_list tbody .operate .btn.publish:hover {background: var(--primaryColor) url(../images/frame/icon_item_operation_hover.png) no-repeat center -180px; cursor: pointer;}
.table_list tbody .operate .btn.publish.disabled, .table_list tbody .operate .btn.publish[disabled] {background: #f3f3f3 url(../images/frame/icon_item_operation_disable.png) no-repeat center -180px; cursor: default;}

.table_list > .tfoot {padding: 20px 25px; border-bottom-left-radius: 5px; border-bottom-right-radius: 5px;}
.table_list > .tfoot #turn_page {padding: 20px 0;}

/* 右侧弹窗 */
#amazon_collection .crawl_form .radio_group {position: relative; display: flex; flex-direction: row; justify-content: flex-start; align-items: center; font-size: 12px;}
#amazon_collection .crawl_form .radio_group > span+span {margin-left: 12px;}
#amazon_collection .crawl_form .radio_group input[type=radio] {opacity: 0; position: absolute; top: 0; left: 0; z-index: -999;}
#amazon_collection .crawl_form .radio_group_item {display: flex; flex-direction: row; justify-content: flex-start; align-items: center; margin: 0; padding: 8px 12px; border-radius: 30px; background-color: #f4f5f6; border: 1px solid #f4f5f6; cursor: pointer;}
#amazon_collection .crawl_form .radio_group_item i {box-sizing: border-box; display: inline-block; width: 14px; height: 14px; border-radius: 50%; background-color: #fff; border: 1px solid #ccdced;}
#amazon_collection .crawl_form .radio_group_item i+span {margin-left: 10px;}
#amazon_collection .crawl_form .radio_group input[type=radio]:checked+.radio_group_item {background-color: #f6fdff; border-color: var(--primaryColor);}
#amazon_collection .crawl_form .radio_group input[type=radio]:checked+.radio_group_item i {width: 15px; height: 15px; background-color: transparent; border: 5px solid var(--primaryColor);}

#amazon_collection .crawl_form .processing.statement, #crawl_form .processing.statement{margin-bottom: 30px; padding: 20px; border-radius: 5px; font-size: 14px; background-color: #f68f44; color: #fff;}

#crawl_amazon .footer .btn_cancel[disabled], #crawl_amazon .footer .btn_cancel.disabled {background-color: #ccc!important; border-color: #ccc; color: #fff; cursor: no-drop;}

#crawl_form .btn_cancel.btn_know{ text-align: center; color: #fff; background-color: var(--primaryColor); border: 1px solid var(--primaryColor);}

#fixed_right .fixed_collect_products .step_list { padding-bottom: 30px; }
#fixed_right .fixed_collect_products .step_item { border-radius: 4px; overflow: hidden; margin-top: 20px; padding: 20px; background-color: #f8f9fb; }
#fixed_right .fixed_collect_products .step_item>strong { color: #1f2328; line-height: 2; }
#fixed_right .fixed_collect_products .step_item>p { margin-top: 6px; font-size: 12px; color: #7d8d9e; line-height: 1.75; }
#fixed_right .fixed_collect_products .step_item .rows { margin: 0; font-size: 14px; line-height: 30px; }
#fixed_right .fixed_collect_products .step_item .rows label { display: block; font-size: 14px; color: #1f2328; line-height: 30px; }
#fixed_right .fixed_collect_products .step_item .copy { display: block; padding-left: 17px; color: var(--primaryColor); font-size: 12px; position: relative; }
#fixed_right .fixed_collect_products .step_item .copy::before { content: ''; display: block; position: absolute; left: 0; top: 8px; width: 15px; height: 15px; background: url(../images/plugins/app/erp_copy.png) no-repeat 0 1px; }
#fixed_right .fixed_collect_products .step_item .access_key { word-break: break-all; font-size: 12px; color: #7d8d9e; line-height: 1.5; }
#fixed_right .fixed_collect_products .step_item .btn_install_collect { display: inline-block; border-radius: 5px; height: 30px; margin-top: 10px; padding: 0 18px; text-decoration: none; font-size: 12px; color: #fff; line-height: 30px; background-color: var(--primaryColor); }
#fixed_right .fixed_collect_products .step_menu { position: fixed; bottom: 0; left: 20px; width: 305px; padding: 10px 0; background-color: #fff; }
#fixed_right .fixed_collect_products .step_menu .close { display: inline-block; vertical-align: top; border-radius: 5px; }
#fixed_right .fixed_collect_products .step_menu .tips { display: inline-block; vertical-align: top; height: 30px; line-height: 30px; margin-left: 10px; padding: 0; font-size: 12px; color: #404852; }
#fixed_right .fixed_collect_products .step_menu .tips>a { color: var(--primaryColor); }

/*************************** 亚马逊采集 End [on 20210806 by Eddie] ***************************/

#products_development_form .error_tips{ display: block; margin-top: 5px; font-size:12px; color: #e41a23; }

.fixed_edit_level .input_radio_side_box{ width: 100%; }
.fixed_edit_level .box_type_menu .item.hide{ display: none; }

/***************************	实时订单	Start	***************************/
#real_time_order .guide_index_box .guide_con{ width: 100%; }
#real_time_order .guide_index_box .guide_con .guide_top_title{ margin-bottom: 35px; font-size: 24px; color: #2a2a2a; }
#real_time_order .guide_index_box .guide_con .order_box{ padding: 25px 23px 35px; width: 100%; border-radius: 5px; background-color: #f5f5f5; overflow: hidden; }
#real_time_order .guide_index_box .guide_con .order_box .order_item{ margin-bottom: 30px; }
#real_time_order .guide_index_box .guide_con .order_box .order_item .label{ position: relative;; display: inline-block; margin-bottom: 8px; font-size: 0; cursor: pointer;; }
#real_time_order .guide_index_box .guide_con .order_box .order_item .label>*{ margin-right: 10px; display: inline-block; vertical-align: middle; font-size: 16px; }
#real_time_order .guide_index_box .guide_con .order_box .order_item .label input{ display: none; }
#real_time_order .guide_index_box .guide_con .order_box .order_item .label:before{ content:'';display: inline-block;width: 11px;height: 11px;content: '';margin-right: 4px;background-color: #fff;border: 1px solid #ccdced;border-radius: 16px;vertical-align: middle; }
#real_time_order .guide_index_box .guide_con .order_box .order_item.cur .label:before{ border-color: var(--primaryColor); }
#real_time_order .guide_index_box .guide_con .order_box .order_item.cur .label:after{ content:''; width: 7px; height: 7px; border-radius: 100%; background-color: var(--primaryColor); position: absolute; top: 50%; transform: translateY(-50%); left: 3px; }
#real_time_order .guide_index_box .guide_con .order_box .order_item:last-child{ margin-bottom: 0; }
#real_time_order .guide_index_box .guide_con .order_box .order_item .img_box img{ max-width: 100%; }
#real_time_order_form .btn_global{ margin-top: 37px; }
/***************************	实时订单	 END	***************************/

#reject_box{ display: flex; width: 100%; height: 100%; position: fixed; z-index: 100003; background: rgb(0, 0, 0, .6); top: 0; left: 0; }
#reject_box .box{ padding: 40px 30px; width: 550px; border-radius: 10px; background-color: #fff; position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); }
#reject_box .box .main_title{ margin-bottom: 20px; text-align: center; font-size: 24px; }
#reject_box .box .main_subtitle{ text-align: center; font-size: 16px; }
#reject_box .box>.close_btn{ position: absolute; top: 10px; right: 10px; cursor: pointer; font-size: 20px; }
#reject_box .box>.close_btn i{ font-size: inherit; }
#reject_box .box .rows:last-child{ margin-bottom: 0; text-align: center; }
#reject_box .box .rows .input_box_textarea{ padding: 18px; font-size: 14px; width: 100%; box-sizing: border-box; min-height: 180px; border-radius: 10px; margin-top: 20px; border: 1px solid #ccdced; border-radius: 10px; }
#reject_box .box .rows .global_btn{ display: inline-block; height: 38px; line-height: 38px; padding: 0 37px; margin: 0 5px; background: #fff; color: #000; cursor: pointer; font-size: 14px; border: 1px solid #ced4da; border-radius: 38px }
#reject_box .box .rows .global_btn.reject_btn{ border-color: #D1241B; color: #fff; background-color: #D1241B; }

