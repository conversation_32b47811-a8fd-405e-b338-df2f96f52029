

/*************************** 全局 start ***************************/
/* w_1200 Start */
.w_1200 #chat .fixed{width:39%; float:left;}
.w_1200 #chat .style_box{width:59.9%; margin:0; margin-left:39.9%; position:absolute; left:0; top:0;}
.w_1200 #chat .style_box .box{width:128px;}

.w_1200 #config .rows .input .not_input{width:280px;}
.w_1200 #config .rows .input .not_input .input_name{width:215px;}
.w_1200 #config .rows .input .not_input .input_url{width:231px;}
.w_1200 #config .rows .input .long{width:320px;}
/* w_1200 End */

/* w_1400 Start */
.w_1400 #chat .style_box .box{width:150px;}
/* w_1400 End */

.top_menu_title{line-height:60px; padding-bottom:20px; font-size:26px; color:#676a6f;}

.config_table a.share_del{width:24px; height:24px; overflow:hidden; margin-top:6px; display:inline-block; vertical-align:top;}
.config_table .box_input{width: 99%;background: #fff;}
.config_table .share_str{display: inline-block;margin-top: 2.5px;margin-left: 8px;line-height: 25px;}

a.edit{line-height: 20px;display: inline-block;}
a.del{line-height: 20px;}

/* 设置首页 */
#config .list_menu{padding: 0;}
#config .config_center_wrapper{ width: 96%; margin: 0 auto; max-width: 1257px; }
#config .r_con_table{margin-left: 0;margin-right: 0;}
#config .index h1{ font-size: 22px; }
#config .index .list_item{display: block;width: 235px;margin-left: 20px;margin-top: 20px;padding: 30px 20px;background: #fff;box-sizing: border-box;float: left;position: relative;border-radius: 5px;}
#config .index .list_item:nth-child(5n+1){margin-left: 0;}
#config .index .list_item:hover{background: #eaf7ff;transform: translateY(-5px);}
#config .index .list_item .list_icon{display: block;margin-bottom: 22px;width: 40px;height: 40px;line-height: 40px;background: url(../images/set/list_icon.png) no-repeat;position: relative;text-align: center;}
#config .index .list_item .list_icon.list_icon_basis{background-position: 0 0;}
#config .index .list_item .list_icon.list_icon_shopping-set{background-position: 0 -40px;}
#config .index .list_item .list_icon.list_icon_user_reg_set{background-position: 0 -80px;}
#config .index .list_item .list_icon.list_icon_share{background-position: 0 -120px;}
#config .index .list_item .list_icon.list_icon_review{background-position: 0 -160px;}
#config .index .list_item .list_icon.list_icon_email{background-position: 0 -200px;}
#config .index .list_item .list_icon.list_icon_manager{background-position: 0 -240px;}
#config .index .list_item .list_icon.list_icon_print{background-position: 0 -280px;}
#config .index .list_item .list_icon.list_icon_logs{ background-position: 0 -720px;}

#config .index .list_item .list_icon.list_icon_photo{background-position: 0 -640px;}
#config .index .list_item .list_icon.list_icon_country{background-position: 0 -400px;}
#config .index .list_item .list_icon.list_icon_agreement{background-position: 0 -440px;}
#config .index .list_item .list_icon.list_icon_payment{background-position: 0 -480px;}
#config .index .list_item .list_icon.list_icon_shipping{background-position: 0 -520px;}
#config .index .list_item .list_icon.list_icon_taxes{background-position: 0 -560px;}
#config .index .list_item .list_icon.list_icon_language{background-position: 0 -600px;}
#config .index .list_item .list_icon.list_icon_user-set{ background-position: 0 -680px; }
#config .index .list_item .list_icon.list_icon_commission{background-image: unset;}
#config .index .list_item .list_icon.list_icon_commission::before{content: "\e796";font-family: "iconfont" !important;font-size: 32px;color: var(--GlobalAssistColor);}
#config .index .list_item .list_name{font-size: 18px;color: #000;}
#config .index .list_item .list_brief {height: 44px; line-height: 22px; overflow: hidden; margin-top: 15px; font-size: 14px; color: var(--GlobalAssistColor);}
.en #config .index .list_item .list_brief{height: 66px;}
@media screen and (max-width: 1540px) {
	#config .index .list_item{width: 23%;}
	#config .index .list_item:nth-child(5n+1){margin-left: 2%;}
	#config .index .list_item:nth-child(4n+1){margin-left: 0;}
}
@media (max-width:1280px) {
	#config .index .list_item{width: 31%;}
	#config .index .list_item:nth-child(4n+1){margin-left: 20px;}
	#config .index .list_item:nth-child(3n+1){margin-left: 0;}
}
#config .set_edit{ background-color: var(--primaryColor);}
#config .watermark_position{background: url(../images/set/watermark_position_1.png) no-repeat center center;}
#config .watermark_position_5{background-image: url(../images/set/watermark_position_5.png);}
#config .watermark_position_9{background-image: url(../images/set/watermark_position_9.png);}
#config .watermark_position.cur{border-color: var(--primaryColor);}


/* 购物设置 */
.shopping_edit_form .input_radio_side_box{width: 345px;min-height: unset;margin-bottom: 0;}
.shopping_edit_form .input_radio_side_box .input_radio{width: 341px;}
.shopping_edit_form .orders_sms_rows .input_checkbox{margin-top:11px;}
.shopping_edit_form .box_textarea{height: 140px;}
.shopping_set .global_container{overflow: visible;}
.shopping_set .global_container .rows:last-child{margin-bottom: 10px;}
.shopping_set .r_con_table thead tr td{white-space: nowrap;}
.shopping_set .icon-question{margin-right: -6px;margin-top: -2px;}
.shopping_set .sales_container .switchery_rows{display: flex;justify-content: space-between;padding: 20px;background-color: #f4f5f6;border-radius: 5px;margin-top: 3px;color: #7d8d9e;font-size: 12px;align-items: center;}
.shopping_set .sales_container .price_rules{display: flex;padding: 20px;background-color: #f4f5f6;border-radius: 5px;margin-top: 3px;color: #7d8d9e;font-size: 12px;line-height: 22px;flex-wrap: wrap;}
.shopping_set .sales_container .price_rules div{width: 100%;}
.shopping_set .sales_container .price_rules a{color: #7d8d9e;}
.shopping_set .sales_container .price_rules .icon{font-size: 14px;color: var(--GlobalMainColor);padding-left: 3px;padding-right: 3px;}
.shopping_set .sales_container .title_flex{display: flex;justify-content: space-between;align-items: center;}
.shopping_set .consultation_only .config_top_title{margin-bottom: 0;}

/* 会员设置 */
#config .user_set{ width: 750px; }
#config .user_set .global_container .config_top_title{margin:0px 0 10px;padding:10px 0;display: block;}
#config .user_set .global_container .config_top_title .return{font-size:20px; color:#000;}
#config .user_set .global_container .text_tips{ font-size: 14px; color: #7f8c9f; }
#config .user_set .global_container .edit_email{ font-size: 14px; color: var(--primaryColor); }
#config .user_set .global_container .config_top_title .config_sub_title{padding: 5px 0;}
#config .user_set .table_item { display: flex; justify-content: space-between; border-radius: 3px; margin-bottom: 10px; padding: 25px 20px; background-color: #f8f9fb; line-height: 18px; }
#config .user_set .table_item .text_info { flex: 1; max-width: 80%; font-size: 14px; color: #000; }
#config .user_set .table_item .global_app_tips { margin: 0; margin-right: 20px; }
#config .user_set .table_item .global_app_tips.hide { display: none; }
#config .user_set .table_item[data-type="review_rejected"] .edit_email { margin-right: 20px; }
#config .user_set .table_item.hide { display: none; }
#config .user_set .field_txt{line-height: 36px;}
#config .user_set .btn_field_add{background-color: var(--primaryColor);color: #fff;padding: 0 18px;height: 36px;line-height: 36px;border-radius: 5px;font-size: 14px;float: right;}
#config .user_set #field_list{margin-top: 20px;}
#config .user_set #field_list .field_item{position: relative; margin-bottom: 15px;padding:18px;background-color: #f8f9fb;border-radius: 5px;}
#config .user_set #field_list .field_item em{width: 6px;height: 100%;position: absolute;left: 5px;top: 0;cursor: move;}
#config .user_set #field_list .field_item em::after{content: '';width: 6px;height: 14px;position: absolute;top: 50%;left: 13px;background-image: url(../images/view/icon_view.png);background-position: -102px -98px;transform: translateY(-50%);}
#config .user_set #field_list .field_item:hover{background-color: #ecf5ff;}
#config .user_set #field_list .field_item .field_name{margin-left: 16px;font-size: 14px;color: #555;display: inline-block;max-width: 88%;}
#config .user_set #field_list .field_item .field_opt{float: right;font-size: 12px;}
#config .user_set #field_list .field_item .field_opt.field_del_btn{margin-left: 8px;color:#7d8d9e}
#config .user_set #field_list .field_item .field_opt.field_edit_btn{color:#89D0FB}
#config .user_set .global_form .rows .input.hide{ display: none; }

.verification_email_box>.rows { margin: 0; }

.fixed_field .field_box.loading{width: 100%;height: 100%; background:url(../images/frame/loading_oth.gif) no-repeat center;}
.fixed_field .field_content_row{overflow: hidden;}
.fixed_field .field_content_row .btn_option{display:block; width:34px; height:34px; line-height:38px; border:1px #ddd solid; border-radius:5px;text-align:center;}
.fixed_field .field_content_row .btn_option>i{display:inline-block; width:14px; height:14px; background-repeat:no-repeat; background-position:center;}
.fixed_field .field_content_row .btn_option_add{border-top-right-radius:0; border-bottom-right-radius:0;}
.fixed_field .field_content_row .btn_option_add>i{background-image:url(../images/products/icon_square_add.png);}
.fixed_field .field_content_row .btn_option_remove{border-left-width:0; border-top-left-radius:0; border-bottom-left-radius:0;}
.fixed_field .field_content_row .btn_option_remove>i{background-image:url(../images/products/icon_square_delete.png);}
.fixed_field .field_content_row .button.hide_add .btn_option_add{display:none;}
.fixed_field .field_content_row .button.hide_add .btn_option_remove{border-left-width:1px; border-radius:5px;}
.fixed_field .field_content_row .button.hide_remove .btn_option_add{border-radius:5px;}
.fixed_field .field_content_row .button.hide_remove .btn_option_remove{display:none;}
.fixed_field .field_content_row .rows{margin-bottom: 20px;}
.fixed_field .field_content_row .rows label{margin-right: 10px;}
.fixed_field .field_content_row .rows label,
.fixed_field .field_content_row .rows .input,
.fixed_field .field_content_row .button{float: left;}

/* 社交媒体 */
#share_edit_form .share_div{width: 25px;height: 25px;display: inline-block;vertical-align: top;margin-top: 2.5px;line-height: 0;background: url(../images/frame/icon_share.png) no-repeat 0 0;}
#share_edit_form .share_twitter{background-position: 0 -25px;}
#share_edit_form .share_pinterest{background-position: 0 -50px;}
#share_edit_form .share_youtube{background-position: 0 -75px;}
#share_edit_form .share_google{background-position: 0 -100px;}
#share_edit_form .share_vk{background-position: 0 -125px;}
#share_edit_form .share_instagram{background-position: 0 -150px;}
#share_edit_form .share_linkedin{background-position: 0 -175px;}
/* 产品评论 & 邮件设置 */
#config .email_config_box{padding: 15px 20px 25px; line-height:28px; position:relative; font-size:14px; color:#555;}
#config .email_config_box strong{display:block; font-size:20px; color:#000;}
#config .email_config_box .btn_submit{position:absolute; top:33px; right:20px; bottom:0;}
#config .email_config_box .email_tips { display: block; margin-top: 20px; line-height: 30px; color: #404852;*color: var(--GlobalAssistColor); font-size: 14px;}
#config .email_config_box .email_tips.number_hide .EmailNumber{ display: none; }
#config .email_config_box .email_tips span{ color: #7d8d9e; }
#config .email_config_box .email_tips span .insufficient{ color: #f5222d; }
#config .email_config_box .email_tips span .lack{ color: #f68f44; }
#config .email_config_box .email_tips a {margin-left: 10px; color: var(--primaryColor); text-decoration: none; border-bottom: 1px solid var(--primaryColor);}
#config .config_table_body_set .email_module {margin-top: 18px;}
#config .config_table_body_set .email_module:first-child {margin-top: 0;}
#config .config_table_body_set .module_title {height: 40px; line-height: 40px; font-size: 16px;}
#config .config_table_body_set .table_item {padding: 0 20px; background: #f8f9fb; border: 0; border-radius: 5px; box-sizing: border-box; -webkit-box-sizing: border-box;}
#config .config_table_body_set .table_item tr td {height: 53px; line-height: 53px; padding: 0;}
#config .config_table_body_set .table_item tr td>span {font-size: 14px;}
#config .config_table_body_set .table_item tr td .desc {font-size: 12px; color: #aaa;}
#config .config_table_body_set .table_item tr td>a {line-height: 53px;}
#config .config_table_body_set .table_item tr td .img {margin-top: 17px;}
#config .config_table_body_set .table_item:hover, #config .config_table_body_set .table_item:hover tbody tr {background: #f8f8fb;}
#config .email_config_box .email_tips .global_app_tips {margin-top: -8px; margin-bottom: 10px; padding: 10px; }
#config .email_config_box .email_tips .global_app_tips a {color: #f68f44; border-color: #f68f44;}
#config .email_config_box .global_app_tips{ padding: 9px 20px 9px 12px; display: inline-block; border-radius: 5px; color: #1f2328; border: 1px solid #ffdec6; border-radius: 5px; background-color: #fff9f5; }
#config .email_config_box .global_app_tips a{ display: inline-block; color: #f68f44; position: relative; }
#config .email_config_box .global_app_tips a:before{ content:''; width: 100%; height: 1px; background-color: #f68f44; position: absolute; bottom: 0; left: 0; }
#config .email_config_box .global_app_tips.insufficient{ border:1px solid #ffdfdc; background-color: #fff9f8; }
#config .email_config_box .global_app_tips.lack{ border:1px solid #ffdec6; background-color: #fff9f5; }
#config .email_config_box .r_con_table tr td .non_user { height: 24px; line-height: 24px; margin: 4px 8px 0 0; padding: 0 8px; font-size: 12px; background-color: #eaf7ff; border-radius: 4px; float: left; }
#config .email_config_box .email_merchant_title { line-height: 30px; }
#config .email_config_box .email_merchant_title .global_app_tips{ margin-top: 0; margin-right: 10px; padding: 6px 20px 7px 12px; vertical-align: middle; }
#config .email_config_box .email_merchant_title .manage_email_add{ display: inline-block; vertical-align: middle; }
#config .email_config_box .email_merchant_title .btn_submit{ margin-left: 10px; min-width: unset; padding: 0 18px; line-height: 34px; height: 34px; border-radius: 5px; position: static; }
#config .email_config_box .email_merchant_box { margin-top: 25px; }
#config .email_config_box .email_merchant_box .table_item { display: flex; align-items: center; justify-content: space-between; padding-top: 17px; padding-bottom: 17px; }
#config .email_config_box .email_merchant_box .table_item.hide { display: none; }
#config .email_config_box .email_merchant_box .item_name { flex: 1; }
#config .email_config_box .email_merchant_box .item_name .merchant_title { display: block; line-height: 1.5; }
#config .email_config_box .email_merchant_box .item_name .merchant_note { display: block; font-size: 12px; color: #7e8da0; line-height: 2; }
#config .email_config_box .email_merchant_box .global_app_tips { border: 0; margin: 0; background: none; }
#config .email_config_box .email_merchant_box .global_app_tips.hide { display: none; }
#config .email_config_box .email_config_table table tr td{ font-size: 12px; color: #1f2328; }
#config .email_config_box .email_config_table .operation{ text-align: right; }
#managerAddForm .input{ display: flex; }
#managerAddForm .input .box_input,
#managerAddForm .input .global_select_box { flex: 1; }
#managerAddForm .permit_list .item{ margin-top: 10px; padding: 24px 25px; width: 100%; line-height: 15px; background-color: #f7f9fb; box-sizing: border-box; font-size: 14px; color: #1f2328; }
#managerAddForm .permit_list .item:first-child{ margin-top: 0; }

#config .customer_notice .table_item:hover,
#config .customer_notice .table_item:hover tbody tr,
#config .email_config_box .table_item:hover,
#config .email_config_box .table_item:hover tbody tr { background-color: #EDF5FF; }

#merchant_edit_form .global_app_tips { display: inline-block; border: 1px solid #ffdec6; border-radius: 5px; width: 100%; padding: 9px 20px 9px 12px; color: #1f2328; background-color: #fff9f5; -webkit-box-sizing: border-box; box-sizing: border-box; }
#merchant_edit_form .global_app_tips a { color: #FF7300; }
#merchant_edit_form .bg_no_table_data { height: 250px !important; }
#merchant_edit_form .bg_no_table_data a { text-decoration: underline; color: var(--primaryColor); }
#merchant_edit_form .r_con_table .email { font-size: 14px; line-height: 1.8; }
#merchant_edit_form .r_con_table .name { color: #777; }
#merchant_edit_form .r_con_table tbody tr:last-child td { border-bottom: 1px solid #edf0f5; }

/* 邮件 */
#config .email .set_edit{display: inline-block;padding:0 16px;margin-bottom: 10px;height: 32px;line-height: 32px;border-radius: 5px;background-color: var(--primaryColor);color: #fff;font-size: 14px;text-decoration: none;}
#config .email .email_config_table a.edit{opacity: 0;}
#config .email .email_config_table .img img{max-height: 12px;}
#config .email .email_config_table:hover a.edit{opacity: 1;}
#config .email input[readonly]{background-color: #efefef;cursor: default;}
#send_person_form .switch_box{padding: 20px; background-color: #f7f9fb;}
#send_person_form .switch_box .title{display: block; padding: 13px 0;margin: 0;}
#send_person_form .switch_box .title .switch_btn{color: #4BA0FC;}
#send_person_form .switch_box ul{display: none;}
#send_person_form .switch_box li{ margin-top: 16px;padding: 27px 20px; border-radius: 5px;background-color: #fff;}
#send_person_form .switch_box li:first-child{ margin-top: 0; }
#send_person_form .switch_box li .global_app_tips{ margin-top: 24px; padding: 9px 32px 9px 12px; line-height: 18px; color: #1f2328; border: 1px solid #ffdec6; border-radius: 5px; background-color: #fff9f5; }
#send_person_form .switch_box li .global_app_tips a{ display: inline-block; color: #f68f44; position: relative; }
#send_person_form .switch_box li .global_app_tips a:before{ content:''; width: 100%; height: 1px; background-color: #f68f44; position: absolute; bottom: 0; left: 0; }
#send_person_form .switch_box .box_type_menu.fl{padding-top: 0;}
#send_person_form .switch_box .box_type_menu .item.disable{cursor: not-allowed;}
#send_person_form .switch_box .box_type_menu span{margin-right: 0; padding-left: 0; padding-right: 0; background-color: transparent; border: none;font-size: 16px;}
#send_person_form .switch_box .box_type_menu span::before{vertical-align: middle; margin-right: 15px;}
#send_person_form .switch_box .box_type_menu span img{vertical-align: middle;}
#send_person_form .switch_box .status{ margin-left: 17px; width: 56px; height: 30px; line-height: 30px; text-align: center;}
#send_person_form .switch_box .status span{display: inline-block; padding: 0 9px; border-radius: 5px; background: #eaf7ff; border-radius: 3px; color: #1f2328; font-size: 12px;}
#send_person_form .switch_box .operation{margin-left: 35px;width: 48px; height: 30px; line-height: 30px; text-align: center;}
#send_person_form .switch_box .operation a{text-decoration: underline; color: #4BA0FC; font-size: 12px;}
#send_person_form .switch_box .box_type_menu .item.disable::before { width: 6px; height: 6px; border: 5px solid #edf0f5; border-radius: 16px;}
#send_person_form .customize_box{ margin-top: 20px; display: none; }
#send_person_form .customize_box input{ box-sizing: border-box; max-width: 100%; }
#send_person_form .sendcloud_box input{ box-sizing: border-box; max-width: 100%; }

#recipients .list_menu { display: flex; align-items: center; justify-content: space-between; padding: 0 24px; }
#recipients .list_menu h1 { font-size: 24px; }
#recipients .list_menu:after { content: unset; }
#recipients .list_menu_button>li>a.add { margin: 0; }
#recipients .r_con_table .field_item { display: inline-block; vertical-align: top; border-radius: 30px; margin-right: 5px; margin-bottom: 5px; padding: 0 15px; font-size: 12px; background-color: #eaf7ff; line-height: 30px; }
#recipients .choose_list .choose_item { display: flex; align-items: center; margin-top: 15px; cursor: pointer; line-height: 28px; }
#recipients .choose_list .choose_item .btn_checkbox { display: flex; align-content: center; flex-direction: row; align-items: center; width: auto; }
#recipients .choose_list .choose_item .btn_checkbox .button { margin-right: 10px; }
#recipients .choose_list .choose_item .btn_radio, #recipients .choose_list .choose_item .btn_checkbox { margin-right: 12px; }

.simulate_select_box{width: 346px;min-height: 50px;position: relative;}
.simulate_select_box .select{display: flex; padding: 9px 15px 0 9px;min-height: 30px;line-height: 30px;cursor: pointer;position: relative;flex-wrap: wrap;border:1px solid var(--GlobalBorderColor);border-radius: 5px;}
.simulate_select_box .select.focus{border-color: var(--primaryColor);outline: none!important;box-shadow: 0 0 5px rgba(7,187,139,0.3);}
.simulate_select_box .select:after{content: '';width: 9px;height: 6px;background: url(../images/frame/icon_select.png) no-repeat left center;position: absolute;top: 0;right: 9px;bottom: 0;margin:auto;transition: all 0.3s ease-in-out;}
.simulate_select_box .select.focus:after{transform: rotate(-180deg);}
.simulate_select_box .placeholder{font-size: 12px;color: var(--GlobalAssistColor);padding-bottom: 9px;}
.simulate_select_box .selected{height: 30px;line-height: 30px; padding: 0 18px;margin-right: 9px;margin-bottom: 9px; background-color: #daf2fe;border-radius: 30px;font-size: 12px;color:var(--GlobalTextColor);position: relative;}
.simulate_select_box .selected i{width: 18px;height: 100%;background-repeat: no-repeat;background-position: -8px -65px;background-color: #daf2fe;border-left: 0;border-top-right-radius: 50px;border-bottom-right-radius: 50px;position: absolute;top: 0;right: 0px;z-index: 0;cursor: pointer;display: block;transition: all 0.3s;-moz-transition: all 0.3s;-webkit-transition: all 0.3s;}
.simulate_select_box .selected:hover i{background-image: url(../images/frame/icon_close.png);right: -7px;}
.simulate_select_box .option_box{display: none;width: 346px;position: absolute;top: calc(100% + 2px);left: -1px;background-color: #fff;border-radius: 5px;z-index: 1;}
.simulate_select_box .global_app_tips{margin: 10px;}
.simulate_select_box .option_list{max-height: 320px;}
.simulate_select_box .option_list .item{line-height: 32px;color: var(--GlobalTextColor);padding: 0 18px;cursor: pointer;}
.simulate_select_box .option_list .item span{color: var(--GlobalAssistColor);}
.simulate_select_box .option_list .current, .simulate_select_box .option_list .current span{color: var(--GlobalMainColor);cursor: default;}
.simulate_select_box .option_list .disabled, .simulate_select_box .option_list .disabled span{color: #cccccc;cursor: no-drop;}
.simulate_select_box .no_data{display: none; padding: 22px;line-height: 22px;text-align: center; font-size: 14px;color:var(--GlobalAssistColor);}
.simulate_select_box .btn_load_more{display: block;line-height: 30px;text-align: center;color: var(--GlobalTextColor);}
.simulate_select_box .foot{display: flex;justify-content: space-between;padding: 0 20px;line-height: 40px;margin-top: 10px; font-size: 14px;border-top: 1px solid var(--GlobalDividingLineColor);}
.simulate_select_box .foot .btn_refresh{padding-left: 20px;color: var(--GlobalTextColor);background: url(../images/frame/icon_refresh1.png) no-repeat left center;}
.simulate_select_box .foot .btn_refresh .ing{display: none;}
.simulate_select_box .foot .btn_refresh.refreshing{background-image: url(../images/frame/icon_refresh_gif.gif);background-size: 12px 12px;}
.simulate_select_box .foot .btn_refresh.refreshing .df{display: none;}
.simulate_select_box .foot .btn_refresh.refreshing .ing{display: block;}
.simulate_select_box .foot .btn_set{color:var(--GlobalMainColor);}

/* 管理员 */
#config .manager .r_con_table td.operation{ text-align: right; }
#config .manager .r_con_table td.operation a{ float: none; display: inline-block; vertical-align: middle; }
#config .manager .box_select{width: 226px;}
#config .manager.index .return_title span{ font-size: 22px; color: #000000; }
#config .manager.index .bread_nav{ margin-top: 10px; }
#config .manager.index .global_container{ padding: 10px 0; }
#config .manager.center_container_1000{ margin: 0 auto; }
#config .manager.list .return_title{ margin: 0 auto; padding: 10px 25px; }
#config .manager.list .return_title a{ padding: 0; }
#config .manager .input_radio_box{ margin-left: 24px; width: 343px; }
#config .manager .input_radio_box:first-child{ margin-left: 0; }
#config .manager .fixed_btn_submit{ margin-bottom: 0; }
#config .manager .btn_permit{height:32px; line-height:32px; padding:0 20px; text-decoration:none; font-size:14px; border:0; border-radius:5px; display:inline-block; vertical-align:top;}
#config .manager input[readonly]{  cursor: no-drop; background-color: #f4f5f7; color: #404852 }


#config .manager .config_box{ font-size: 0;}
#config .manager .config_box>div{ margin-left: 14px; width: 348px; }
#config .manager .config_box>div:first-child{ margin-left: 0; }
#config .manager .config_box .config_list{ display: inline-block; vertical-align: top; margin-right: 14px; margin-bottom: 14px; width: 348px; padding: 24px 25px; border-radius: 5px;background: #f8f9fb;box-sizing: border-box; }
#config .manager .config_box .config_list:nth-child(2n){ margin-right: 0; }
#config .manager .config_box .config_list.current>.info_name:after, #config .manager .config_box .config_list.current>.info_name:before{border-top: 0;border-bottom: 6px solid #fff;top: auto;bottom: 5px;}
#config .manager .config_box .config_list.current>.info_name:before{border-bottom-color: #7c8c9b;bottom: 7px;}
#config .manager .config_box .config_list.current .second_box{display: block;}
#config .manager .config_box .config_list .info_name{ line-height: 16px;font-size: 14px;color: #1f2328;position: relative;}
#config .manager .config_box .config_list .info_name .input_checkbox{ margin-top: 0; margin-right: 5px;width: 14px; height: 14px;background-color: #fff;}
#config .manager .config_box .config_list>.info_name em{margin-left: 4px;color: var(--GlobalAssistColor);}
#config .manager .config_box .config_list>.info_name i{width: 25px;height: 100%;position: absolute;top: 0;right: 0;cursor: pointer;z-index: 1;}
#config .manager .config_box .config_list>.info_name:after, #config .manager .config_box .config_list>.info_name:before{border: 6px solid transparent;border-top: 6px solid #f8f9fb;width: 0;height: 0;position: absolute;top: 5px;right: 0;content: ' ';}
#config .manager .config_box .config_list>.info_name:before{border-top-color: #7c8c9b;top: 7px;}
#config .manager .config_box .float_left .config_list:first-child>.info_name:after,
#config .manager .config_box .float_left .config_list:first-child>.info_name:before{display: none;}
#config .manager .config_box .config_list .second_box{display: none;background-color: #f8f9fb;margin-top: 20px;}
#config .manager .config_box .config_list .second_box>.info_name{margin-top: 10px;width: 100%;display: flex;flex-wrap: wrap;align-items: center;}
#config .manager .config_box .config_list .second_box>.info_name .input_checkbox_box{ display: inline-block; vertical-align: middle; width: auto; font-size: 12px; }
#config .manager .config_box .config_list .second_box>.info_name>.input_checkbox_box{ margin-top: 5px; }
#config .manager .config_box .config_list .second_box>.info_name .second_content{ margin-top: 5px; display: flex; flex-wrap: wrap;align-items: center; }
#config .manager .config_box .config_list .second_box>.info_name .second_content .th_i{margin: 3px 5px;display: flex;align-items: center;flex-wrap: wrap;}
#config .manager .config_box .config_list[data-menu="plugins"] .second_box>.info_name .second_content .th_i{width: 100%;}
#config .manager .config_box .config_list .second_box>.info_name .second_content .th_i label{ font-size: 12px; color: #1f2328;line-height: 16px;}
#config .manager .config_box .config_list .four_box{display: flex;flex-wrap: wrap;align-items: center;width: 100%;margin-left: 20px;padding: 5px 0;}
#config .manager .config_box .config_list .four_box .four_i{display: flex;margin: 3px 5px;}
#config .manager .config_box .config_list .second_box>.info_name:first-child{margin-top: 0;}
#config .manager .box_button .input_checkbox_box{display: inline-block;margin-top: 5px;margin-right: 20px;font-size: 14px;color: #1f2328;}
#config .manager .box_button .input_checkbox_box .input_checkbox{background-color: #fff;}
#config .manager .premit_box .PermitHead{ height: 30px; }
#config .manager .premit_box .PermitHead .input_checkbox{ vertical-align: middle; }
/*************************** 全局 end ***************************/

#smtp_ver_form .loading{width: 50px;height: 50px;margin:auto auto 10px;background: url(../images/global/loading.gif) no-repeat center;}
#smtp_ver_form .box_progress .tips{height: auto}

#smtp_ver_form .box_progress .tips_link{margin:12px 0;}
#smtp_ver_form .box_progress .tips_link a{color: #04bc76;}

#email_edit_form .sys_remark{line-height:26px;}
#email_edit_form .sys_remark strong{font-weight:bold;}
#email_edit_form .tpl_tips{display:none;}
#mail_preview_box{display: none;width: 750px;background: #fff;position: fixed;top: 10%;right: 0;bottom: 10%;left: 0;margin:auto;padding: 30px 20px;border-radius: 5px;box-shadow: 0 0 10px rgba(0,0,0,0.2);z-index: 10001;}
#mail_preview_box .btn_close{width: 26px;height: 26px;background-color: #ccc;position: absolute;top: 8px;right: 8px;border-radius: 26px;}
#mail_preview_box .btn_close:before{content: '';width: 23px;height: 23px;position: absolute;top: 0;right: 0;bottom: 0;left: 0;margin:auto;background: url(../images/frame/icon_close.png) no-repeat left top;}

#language_edit_form .box_select{width:100px;}
#language .r_con_table td .default { font-size: 12px; color: var(--GlobalTitleColor); background-color: #e4f6f1; padding: 4px 8px; line-height: normal; border-radius: 3px; margin-left: 10px; }

/*水印升级*/
.themes_progress .input_button{text-align:center;}
.themes_progress .input_button .btn_global{display:none; width:128px;}
.themes_progress .input_button .btn_proceed{background:var(--primaryColor); border-color:var(--primaryColor); color:#fff;}

/*邮箱设置*/
#config #email_config_form .box_option_list{max-width: none;}
#config #email_config_form .box_option_list .change_smtp_item.current{color:#fff;background-color: var(--primaryColor);}

#config .country .mb0{margin-bottom: 0;}
#config .country .plugins_app_menu{padding-top: 10px;margin-bottom: 10px;}
#config .country .list_menu{padding: 0 30px;}
#config .country .country_list td{vertical-align: middle;padding-left: 30px;padding-right: 30px;font-size: 14px;}
#config .country .country_list tbody td{padding: 30px;}
#config .country .country_list td .icon_flag_big{vertical-align: middle;margin-right: 15px;}
#config .country .country_list td .name{display: inline-block;vertical-align: middle;position: relative;font-size: 14px;}
#config .country .country_list td .tags_box{width:max-content; position: absolute;top: -2px;left: 100%;}
#config .country .country_list td .c_tags{height:24px; line-height:24px; margin:0 0 5px 10px;padding:0 8px; font-size:12px; background-color:#e4f6f1; border-radius:4px; display:block;}
#config .country .country_name{white-space: normal;}
#config .country .country_name .flag_img{display: inline-block;vertical-align: middle;width: 60px;margin-right: 15px;}
#config .country .country_name img{max-width: 60px;max-height: 38px;}
#config .country_edit{padding: 30px;}
#config .country_edit .country_name{line-height: 38px;font-size: 22px;color: #555;}
#config .country_edit .country_name .icon_flag_big{vertical-align: middle;margin-right: 10px;}
#config .country_edit .vat{display: inline-block;vertical-align: top;line-height: 20px;}
#config .country_edit .rows{clear: unset;}
#config .country_edit .rows.clean{clear: both;}
#config .country_edit .dis_box{line-height: 28px;border-bottom:1px solid #edf0f5;padding-bottom: 7px;font-size: 14px;color: #555;}
#config .country_edit .list{width: 48.5%;}
#config .country_edit .box_select{max-width: unset;}
#config .country_edit .unit_input{width: 100%;}
#config .states_title{line-height: 76px;background: #fff;margin-top: 10px;border-radius: 5px 5px 0 0; font-size: 20px;color: #000;text-indent: 30px;}
#config .states_edit{margin: 0;}
#config .states_edit .icon_del{display: inline-block;width: 18px;height: 18px;background: url(../images/frame/icon_operation.png) no-repeat -18px center;}
#config .states_edit .icon_edit{display: inline-block;width: 18px;height: 18px;margin-right: 10px;background: url(../images/frame/icon_operation.png) no-repeat 0 center;}
#config .states_edit td{padding-left: 30px;padding-right: 30px;font-size: 14px;}
#config .states_edit .my_order{position: relative;transition: 0.3s all ease-out;}
#config .states_edit .my_order i{display: none;position: absolute;left: 10px;top: 0;bottom: 0;margin:auto;width: 20px;background: url(../images/set/icon_myorder_move.png) no-repeat center center;cursor: move;}
#config .states_edit tr:hover i{display: block;}
#config .states_edit tr:hover .my_order{text-indent: 10px;}
#config .states_add{padding: 30px 20px;background: #fff;border-radius: 0 0 5px 5px;}
/*************************** 基本设置 end ***************************/


/*************************** 风格模板 start ***************************/
#index_set{ margin:20px;padding-left: 440px; }
#index_set .themes_box{position: relative;float: left;margin-left: -440px;width: 400px;border: 5px solid #fff;}
#index_set .themes_box .themes_pic{width: 400px;}
#index_set .themes_box .abs_item{position: absolute;cursor: pointer;}
#index_set .themes_box .abs_item:hover:before,#index_set .themes_box .abs_item.cur:before{position: absolute;content: '';top: -4px;bottom: -4px;;left: -4px;;right: -4px;border:3px solid var(--primaryColor);}
#index_set .show_type .ty_list{width: 118px;height: 34px;padding-top: 84px;background: url(../images/set/icon_adshow_type.png) no-repeat left 19px;float: left;margin-right: 20px;font-size: 14px;text-align: center;color: #999;border:1px solid #cbcecd;border-radius: 5px;cursor: pointer;}
#index_set .show_type .ty_list_2{background-position: -140px 19px;}
#index_set .show_type .ty_list_3{background-position: -280px 19px;}
#index_set .show_type .ty_list.cur{background-image: url(../images/set/icon_adshow_type_cur.png);color: var(--primaryColor);border-color: var(--primaryColor);}



.icon_terminal_pc{width:30px; height:23px; background:url(../images/mta/icon_terminal_pc.png) no-repeat center top; display:inline-block; vertical-align:top;}
.icon_terminal_mobile{width:30px; height:23px; background:url(../images/mta/icon_terminal_mobile.png) no-repeat center top; display:inline-block; vertical-align:top;}
#themes .box_drop_down_menu{margin-left:20px; padding:0;display: inline-block;vertical-align: middle;}
#themes .box_terminal .more>i{margin-top:5px;}
#themes .box_terminal .more_menu{min-width:110px;top: 39px;display: none;}
#themes .box_terminal .more_menu .item{height:33px; line-height:33px; padding:0 15px;}
#themes .box_terminal .more_menu .item>em{margin-top:9px; margin-right:5px; background-size:65%;}
#themes .box_terminal .more_menu .item>em.icon_terminal_pc{background-size:55%;}
#themes .box_terminal .more_menu .item>em.icon_terminal_mobile{margin-top:7px; background-size:35%;}
#themes .box_cycle .more_menu{min-width:110px;}
#themes .rows .adpic_row{padding:10px; position:relative;}
#themes .rows .adpic_row:hover{cursor:pointer; background:#f1f8f5;}
#themes .rows .adpic_row .l_img{width:36%; height:213px; margin-top:0; background:#f6f6f6; border-radius:5px;}
#themes .rows .adpic_row .l_img .img .upload_btn{background: url(../images/frame/bg_index_set_pic.png) no-repeat center 69px;}
#themes .rows .adpic_row .l_img .img .upload_btn,#themes .rows .adpic_row .l_img .img,#themes .rows .adpic_row .l_img .img .preview_pic{width: 100%;height: 100%;margin: auto;}
#themes .rows .adpic_row .l_img .img .pic_btn{width: 100%;height: 100%;}
#themes .rows .adpic_row .l_img .img .pic_btn a{margin-top: 88px;}
#themes .rows .adpic_row .l_img .img.isfile:hover .pic_btn{display: block;z-index: 2;}
#themes .rows .adpic_row .l_img .img .size{position: absolute;top: 133px;left: 0;right: 0;line-height: 24px;font-size: 14px;color: #aaa;text-align: center;padding: 0 20px;}
#themes .rows .adpic_row .l_img .img.isfile:hover .size{z-index: 2;color: #fff;top: 115px;}
#themes .rows .adpic_row .l_img .img .preview_pic{background: none;}
#themes .rows .adpic_row .l_img .img .preview_pic>a{cursor:default;width: 100%;height: 100%;padding: 0;border:none;position: relative;z-index: 1;background: #f6f6f6;}
#themes .rows .adpic_row .l_img .img .preview_pic>a.zoom{cursor:pointer;}
#themes .rows .adpic_row .upload_file_multi .img{margin-bottom:0;}
#themes .rows .adpic_row .upload_file_multi .img .preview_pic{width:82px; height:82px;}
#themes .rows .adpic_row .upload_file_multi .img .upload_btn{width:82px; height:82px; background-size:100%;}
#themes .rows .adpic_row .upload_file_multi .img .preview_pic>a{width:70px; height:70px;}
#themes .rows .adpic_row .ad_info input{background-color:#fff;}
#themes .rows .adpic_row .ad_info .unit_input{margin-top:10px;}
#themes .rows .adpic_row .ad_info .unit_input:first-child{margin:0;}
#themes .rows .adpic_row .ad_view{width:60%; margin-left:36%;}
#themes .rows .adpic_row .ad_view input{width:90%; max-width:333px; background-color:#fff;}
#themes .rows .adpic_row .ad_view .view_item{margin-top:10px; margin-left:40px;}
#themes .rows .adpic_row .ad_view .view_item b{line-height:28px; display: block;}
#themes .rows .adpic_row .ad_view .view_item:first-child{margin-top:0;}
#themes .rows .adpic_row .drag_bg{height:39px; overflow:hidden; margin:auto; position:absolute; top:0; bottom:0; right:2%; display:none;}
#themes .rows .adpic_row:hover .drag_bg{display:block;}
#themes .config_table thead td{padding:19px 10px;}

#themes .themes_nav_container{overflow:visible;}
#themes .themes_nav_container .box_drop_double{width:345px;}
#themes .themes_nav_container .target_rows .box_select{width:60px;}

#themes_nav{background:#fff;}
#themes_nav .nav_form_left{width:60%;}
#themes_nav .nav_form_left .custom_row{padding-left:20px;}
#themes_nav .nav_form_right{width:40%;}
#themes_nav .module_nav_table .del{margin-left:5px;}
#themes_nav .module_nav_table .del>img{vertical-align:middle;}
#themes_nav .module_nav_table .add_custom_item, #themes_nav .module_nav_table .add_item{margin-left:20px;}
#themes_nav .module_nav_table .select_input{padding-left:30px;}
#themes_nav .myorder_list{padding:1px 0 20px 0;}
#themes_nav .myorder_list li{height:36px; line-height:36px; border:1px transparent solid;}
#themes_nav .myorder_list li>div{background: url(../images/products/menu_dt_cur.png) no-repeat 15px center; cursor:move; text-indent:40px; border-bottom:1px solid #eee;}
#themes_nav .myorder_list .placeHolder{border:1px #1584D5 dashed; background-color:#fff;}
#themes_nav #nav_custom_hide, #themes_nav #nav_hide{display:none;}
#themes_nav .r_con_form .rows_hd_blank{display:none;}
.themes_nav_form .nav_oth{margin-top:8px;}

.themes_content{padding-right: 479px; margin:20px 10px;}
.themes_set{padding-bottom: 10px;padding-top: 5px;}
.themes_set .themes{line-height: 34px;font-size: 15px;color: #757676;text-transform: uppercase;text-indent: 10px;}
.themes_set .use{float: right;padding: 0 17px;height: 32px;line-height: 32px;border-radius: 5px;background: var(--primaryColor);border:1px solid var(--primaryColor);font-size: 14px;text-align: center;color: #fff;}
.themes_set .view{float: right;padding: 0 17px;height: 32px;line-height: 32px;border-radius: 5px;border:1px solid transparent;color: var(--primaryColor);font-size: 14px;text-align: center;}
.themes_current{max-width: 510px;margin:auto;background: #fff;border:5px solid #fff;}
.themes_current .themes_img{overflow: hidden;}
.themes_current img{max-width: 100%;}

.themes_themes{float: right;width: 479px;margin-right: -489px;height: 100%;overflow-y: auto;}
.themes_themes #themes_box{position: relative;}
.themes_themes .item{position: absolute;width: 145px;margin:0 0 12px 0px;border-radius:5px;}
.themes_themes .item .img{overflow:hidden; cursor:pointer; display:table-cell; vertical-align:middle; text-align:center;}
.themes_themes .item .img img{max-width:100%; max-height:100%; vertical-align:middle;}
.themes_themes .item .img_mask{display:none; background:rgba(0,0,0,0.55); position:absolute; top:0; left:0; width: 100%;height: 100%; z-index:3;}
.themes_themes .item .img_mask:before{content: '';position: absolute;top: 0;bottom: 0;left: 0;right: 0;margin:auto;width: 40px;height: 40px;background: url(../images/photo/icon_big_item.png) no-repeat center center;}
.themes_themes .item .info{display: none !important;width:100%; height:35px; line-height:35px; background:#000; color:#fff; overflow:hidden; position:absolute; left:0; bottom:0; z-index:4; filter:alpha(opacity=40); -moz-opacity:0.4; opacity:0.4}
.themes_themes .item .info span{margin-left:10px;}
.themes_themes .item .info .btn{width:30px;}
.themes_themes .item .info .btn>a{margin-right:10px;}
.themes_themes .current .img_mask,.themes_themes .item:hover .img_mask{display:block;}
.themes_themes .current .img_mask:before{background-image: url(../images/photo/icon_big_item_cur.png);}


#themes_products_detail .themes{background:#fff;}
#themes_products_detail .themes .item{margin:20px 0 0 20px; position:relative; border:3px #e8e8e8 solid; border-radius:5px;}
#themes_products_detail .themes .item .img{width:210px; height:280px; overflow:hidden; cursor:pointer; display:table-cell; vertical-align:middle; text-align:center;}
#themes_products_detail .themes .item .img img{max-width:100%; max-height:100%; vertical-align:middle;}
#themes_products_detail .themes .item .img_mask{display:none; background:url(../images/photo/r.png) no-repeat center; position:absolute; top:0; left:0; width:210px; height:280px; z-index:3;}
#themes_products_detail .themes .item .info{width:100%; height:35px; line-height:35px; background:#000; color:#fff; overflow:hidden; display:none; position:absolute; left:0; bottom:0; z-index:4; filter:alpha(opacity=40); -moz-opacity:0.4; opacity:0.4}
#themes_products_detail .themes .item .info span{margin-left:10px;}
#themes_products_detail .themes .item .info .btn{width:30px;}
#themes_products_detail .themes .item .info .btn>a{margin-right:10px;}
#themes_products_detail .themes .current{border:3px #54a28f solid;}
#themes_products_detail .themes .current .img_mask{display:block;}
#themes_products_detail .module_nav_table .del{margin-left:5px;}
#themes_products_detail .module_nav_table .del>img{vertical-align:middle;}
#themes_products_detail .module_nav_table .add_custom_item, #module .module_nav_table .add_item{margin-left:20px;}
#themes_products_detail .module_nav_table .select_input{padding-left:30px;}
#themes_products_detail .add_item{color:red; cursor:pointer;}
#themes_products_detail .rows .input .help_item{margin-bottom:5px;}
#themes_products_detail .rows .input .btn_option{width:28px; height:28px; line-height:28px; font-size:20px; text-align:center; text-decoration:none; vertical-align:top; background-color:#fff; border:1px #ddd solid; border-radius:5px; display:inline-block;}

#themes_style .module_style_table{background:#e7f7f3; width:100%;}
#themes_style .module_style_table td{background:#fff;}
#themes_style .module_style_table thead{color:#60666a;}
#themes_style .module_style_table tbody td{height:40px; line-height:40px;}
#themes_style .module_style_table tbody td div{display:table-cell;}
#themes_style .module_style_table tbody td .form_input{height:28px; line-height:28px; border:1px solid #ddd; background:#fff; border-radius:5px; padding:0 5px; outline:0;}
#themes_style .module_style_table tbody td input{width:50px;}
#themes_style .r_con_form .rows{background:#e7f7f3;}
#themes_style .r_con_form .btn_box{width:100%; background:#fff;}
#themes_style .r_con_form .btn_box>label{display:none;}
#themes_style .r_con_form .btn_box>.input{width:100%; text-align:center; float:none;}
#themes_style .btn_ok, #themes_style .btn_cancel{display:inline-block; float:none;}
#themes_style .color_rows{float: left;clear: none;margin-right: 15px;}
#themes_style .tit{clear:both;font-size: 16px;color: #000;padding-top: 20px;}
/*************************** 风格模板 end ***************************/

/*************************** 税费管理 start ***************************/
#tax .plugins_app_menu{padding-top: 10px;margin-bottom: 0;border: none;}
.en .plugins_app_menu li a{padding: 0 25px;}
#tax .r_con_table{margin-left: 0;margin-right: 0;}
#tax .r_con_table td{font-size: 14px;padding-left: 30px;padding-right: 30px;}
#tax .r_con_table tbody td{padding: 30px;vertical-align: middle;}
#tax .r_con_table tbody td b{line-height: 34px;}
#tax .r_con_table td .icon_flag_big{vertical-align: middle;margin-right: 15px;}
#tax .r_con_table td .name{display: inline-block;vertical-align: middle;position: relative;font-size: 14px;}
#tax .r_con_table td .tags_box{width:max-content; position: absolute;top: -2px;left: 100%;}
#tax .r_con_table td .c_tags{height:24px; line-height:24px; margin:0 0 5px 10px;padding:0 8px; font-size:12px; background-color:#e4f6f1; border-radius:4px; display:block;}
#tax .country_name{white-space: normal;}
#tax .country_name .flag_img{display: inline-block;vertical-align: middle;width: 60px;margin-right: 15px;}
#tax .country_name img{max-width: 60px;max-height: 38px;}
#tax .tax_left{width: 900px;}
#tax .tax_right{width: 380px;}
#tax .tax_right .global_container{margin-top: 0;}
#tax .tax_right .top_title{line-height: 38px;font-size: 20px;color: #000;}
#tax .tax_right .sub_title{line-height: 22px;margin-top: 16px;margin-bottom: 25px;font-size: 16px;color: #555;}
#tax .tax_right .btn{margin-bottom: 10px;font-size: 14px;color: #888;}
#country .country_menu_list a{ color: var(--primaryColor);}
#country .country_menu_list dt i{background: url(../images/set/icon_myorder_move.png) no-repeat left center;}

@media (max-width:1500px) {
	#tax .tax_left, #tax .tax_right{width: auto;float: none;}
	#tax .tax_right{margin-bottom: 10px;}
	#tax .tax_right .global_container{padding: 20px 30px;}
}
#tax .states_box .global_container{padding: 30px;}
#tax .states_box .rows.fr{margin-bottom: 0;}
#tax .states_box .country_name{line-height: 38px;margin-right:160px;padding: 2px 0px 10px;font-size: 22px;color: #555;}
#tax .states_box .country_name .icon_flag_big{vertical-align: middle;margin-right: 10px;}
#tax .states_box .states_title{line-height: 38px;background: #fff;padding: 0 30px 15px;font-size: 14px;color: #888;}
#tax .states_box .states_edit{margin: 0;}
#tax .states_box .box_tax_set {width: 50%;}
#tax .states_box .box_tax_set .float_rows {width: 110px; margin-left: 30px; margin-bottom: 0;}
#tax .states_box .box_tax_set .float_rows.input_tax {margin-left: 0;}
#tax .states_box .box_tax_set .float_rows .unit_input {display: flex;}
.box_tax_bat_edit .unit_input{display: flex;}
.box_tax_bat_edit .unit_input .box_input{flex: 1;}
/*************************** 税费管理 end ***************************/


/*************************** 运费管理 start ***************************/
.center_container_786 {max-width: 786px; margin: 0 auto;}
.center_container_816 {max-width: 816px; margin: 0 auto;}
.center_container_1690 {max-width: 1690px; margin: 0 auto;}

#shipping .global_container { overflow: inherit; padding: 20px 24px; }
#shipping .r_con_wrap .big_title {line-height: 42px;}
#shipping .global_form .rows {margin-bottom: 10px;}
#shipping .updata_form .big_title {margin-bottom: 28px;}

#shipping .bg_between {display: none; margin-top: 20px; padding: 19px 25px 25px; background-color: #f8f9fb;}
#shipping .bg_between .unit_input {display: flex; justify-content: space-between;}
#shipping .bg_between .unit_input>b.last {width: 34px; border-radius: 0;}
#shipping .bg_between .unit_input .box_input {width: 100%; border-radius: 0;}
#shipping .bg_between .unit_input .input_item{width: 50%;}
#shipping .bg_between .unit_input .input_item .error_tips{display: none; line-height: 26px;}
#shipping .bg_between .unit_input .box_select_down { height: 34px; line-height: 34px; }
#shipping .bg_between .unit_input .box_select_down .head::after { top: 14px; }

#shipping .guide_index_box{padding: 7%;background: #fff;border-radius: 5px;}
#shipping .inside_container .open_asiafly{height: 29px;line-height: 29px;border: 1px solid var(--primaryColor);border-radius: 50px; margin-top: 4px;margin-right: 10px;padding: 0 25px;font-size: 14px;color: var(--primaryColor);}
#shipping .inside_container .add_shipping{margin-top: 4px;font-size: 14px;}
#shipping .area_edit_box .trans3{transition:all .3s;}
#shipping .area_edit_box .set_add{margin-top: 20px;margin-left: 20px;}
#shipping .area_edit_box .set_add.hide{display: none;}

/*亚翔激活 start*/
#shipping .asiafly_box{padding: 40px 50px 50px;}
#shipping .asiafly_box .name{line-height: 58px;font-size: 40px;color: #000;}
#shipping .asiafly_box .ratio{line-height: 38px;margin-top: 8px;font-size: 20px;color: #f5222d;}
#shipping .asiafly_box .desc{line-height: 32px;font-size: 14px;color: #888;}
#shipping .asiafly_box .title{line-height: 32px;margin-top: 10px;font-size: 14px;color: #000;}
#shipping .asiafly_box .edit_box{margin-top: 10px;text-align: center;}
#shipping .asiafly_box .edit_box a{display: none;height: 38px;line-height: 38px;padding: 0 42px;font-size: 18px;border:1px solid var(--primaryColor);border-radius: 50px;text-align: center;color: var(--primaryColor);}
#shipping .asiafly_box .edit_box a.close{display: inline-block;height: auto;line-height: normal;border: none;margin-top: 17px;padding: 0;font-size: 14px;color: #888;}
#shipping .asiafly_box .edit_box a.cur{background: var(--primaryColor);color: #fff;}
#shipping .asiafly_box .edit_box a.show{display: block;}
#shipping .asiafly_tips li{padding-top: 15px;padding-bottom: 12px;border-top: 1px solid #f1f1f1;}
#shipping .asiafly_tips li:first-child{padding-top: 10px;padding-bottom: 24px;border-top: none;}
#shipping .asiafly_tips li .title{line-height: 42px;font-size: 20px;color: #000;}
#shipping .asiafly_tips li .desc{line-height: 28px;font-size: 14px;color: #888;}
#shipping .asiafly_tips li a{line-height: 32px;background: var(--primaryColor);border-radius: 50px;margin-top: 25px; margin-left: 50px; padding: 0 25px;font-size: 14px;color: #fff;}
.asiafly_account_review_box table tr{line-height: 44px;}
.asiafly_account_review_box table tr td{padding-right: 28px;font-size: 16px;color: #000;}
.asiafly_shipping_open .global_tips strong{font-size: 14px;}
.asiafly_shipping_open .global_tips input{height: 28px;line-height: 28px;background: none;border-color: var(--primaryColor);margin-top: 16px;padding: 0 30px;color: var(--primaryColor);}
.asiafly_shipping_open .btn_submit{height: 40px;line-height: 38px;margin-top: 10px;padding: 0 75px;font-size: 16px;}
.asiafly_shipping_open .center{text-align: center;}
/*亚翔激活 end*/
.box_area_choose_range .delivery_box {display: flex; justify-content: space-between;}
.box_area_choose_range .input_radio_side_box {width: 300px;}
#shipping .inside_container .inside_menu {margin-right: 24px;}
#shipping .list_menu {height: 67px; padding: 0 24px;}
#shipping .list_menu .global_app_tips {margin-top: 22px; float: left;}
#shipping .list_menu .list_menu_button {height: auto; padding: 20px 0;}
#shipping .list_menu .list_menu_button .add {min-width: 56px; height: 27px; line-height: 27px; margin: 0; padding: 0 24px;}
#shipping .config_table_body {padding: 0;}
#shipping .config_table_body .global_container {overflow: visible; margin-top: 19px; padding: 0;}
#shipping .shipping_head {display: flex; justify-content: space-between; min-height: 103px; padding: 0 24px;}
#shipping .shipping_head .shipping_title {padding: 25px 0;}
#shipping .shipping_head .shipping_title .box_title {min-height: 30px; line-height: 30px; font-size: 0; color: var(--GlobalTitleColor);}
#shipping .shipping_head .shipping_title .box_title>strong {font-size: 18px;}
#shipping .shipping_head .shipping_title .box_title .status {display: inline-block; vertical-align: top; height: 22px; line-height: 22px; background-color: #fbeded; margin-top: 4px; margin-left: 12px; padding: 0 11px; font-size: 12px; color: #f35958; border-radius: 11px; white-space: nowrap; transition: all .3s;}
#shipping .shipping_head .shipping_title .box_title .status.ing {background-color: #EDF5FF; color: #4BA0FC;}
#shipping .shipping_head .shipping_title .info {height: 24px; line-height: 24px; color: #7d8c9f;}
#shipping .shipping_head .shipping_title .info>li {display: inline-block; vertical-align: top; margin-left: 24px;}
#shipping .shipping_head .shipping_title .info>li:first-child {margin-left: 0;}
#shipping .shipping_head .shipping_edit {padding: 38px 0; font-size: 0;white-space: nowrap;}
#shipping .shipping_head .shipping_edit .add {display: inline-block; min-width: 56px; height: 27px; line-height: 27px; margin-left: 12px; padding: 0 24px; text-align: center; font-size: 12px; color: #fff; background: var(--primaryColor); border-radius: 14px;}
#shipping .shipping_head .shipping_edit .btn_more {display: inline-block; vertical-align: top; position: relative;}
#shipping .shipping_head .shipping_edit .btn_more>dd {display: none; position: absolute; top: 30px; left: -13px; z-index: 1; opacity: 0; filter: alpha(opacity=0); width: 70px; padding: 7px 0; text-align: center; background-color: #fff;}
#shipping .shipping_head .shipping_edit .btn_more>dd>a {display: block; width: inherit; height: 27px; line-height: 27px; font-size: 12px; color: #666;}
#shipping .shipping_head .shipping_edit .btn_more .icon_more {display: inline-block; vertical-align: middle; position: relative; width: 27px; height: 27px; line-height: 27px; margin-left: 7px; text-align: center; font-size: 0; color: var(--primaryColor); font-family: "iconfont" !important; font-style: normal; -webkit-font-smoothing: antialiased; -moz-osx-font-smoothing: grayscale; background-image:  none; background-color: #eaf7ff; border-radius: 36px; transition: all 0.3s ease-out;}
#shipping .shipping_head .shipping_edit .btn_more .icon_more:before {content: "\e73a"; font-size: 14px;}
#shipping .shipping_head .shipping_edit .btn_more .icon_more:hover {background-color: var(--primaryColor); color: #fff;}
#shipping .shipping_head .shipping_edit .btn_more:hover>dd {display: block; top: 39px;}

#shipping .warehouse_add_btn{ margin-top: 20px; margin-right: 24px; min-width: 56px; height: 27px; line-height: 27px; padding: 0 24px; background-color: var(--primaryColor); color: #fff; }

#shipping .inside_table {padding: 0;}
#shipping .customize {margin-top: 22px;}
#shipping .warehouse_title {display: flex; align-items: center; justify-content: space-between; line-height: 70px; padding: 0 24px; font-size: 20px; color: #000;}
#shipping .warehouse_title.no-flex { justify-content: left; }
#shipping .warehouse_title.no-flex .global_app_tips { margin-top: 3px; margin-left: 20px; }
#shipping .r_con_table {margin: 0;}
#shipping .r_con_table tbody td.name>strong {line-height: 21px;}
#shipping .r_con_table tbody td.list>p {line-height: 23px;}
#shipping .r_con_table tbody td.list>p.explain {color: var(--GlobalAssistColor);}
#shipping .r_con_table tbody td.operation a:first-child {margin: 0;}
#shipping .r_con_table tbody .box_move { position: relative; }
#shipping .r_con_table tbody .box_move .btn_move { position: absolute; top: 0; right: 0; bottom: 0; left: 0; cursor: move; background: url(../images/frame/icon_myorder.png) no-repeat center; }

#shipping .mock_table{margin: 10px 0;border-top: 1px #edf0f5 solid;background-color: #fff;}
#shipping .mock_table .tr{display: flex;border-bottom: 1px #edf0f5 solid;justify-content: space-between;}
#shipping .mock_table .tr .td{box-sizing: border-box;}
#shipping .mock_table .tr .td:first-child{padding-left: 24px;}
#shipping .mock_table .tr .td:last-child{padding-right: 24px;}
#shipping .mock_table .thead .tr{background-color: #f7f9fb;font-weight: bold;}
#shipping .mock_table .thead .td{padding: 12px 20px;line-height: 34px;color: #404852;}
#shipping .mock_table .tbody .tr:hover{background-color: #ecf5ff;}
#shipping .mock_table .tbody .td{display: flex;padding: 10px 20px;line-height: 24px;align-items: center;}
#shipping .mock_table .c_select{width: 6%;}
#shipping .mock_table .c_order{width: 5%;min-width: 51px;}
#shipping .mock_table .c_logistics{width: 30%;}
#shipping .mock_table .c_related{width: 20%;}
#shipping .mock_table .operation{width: 80px;}
#shipping .mock_table .c_warehouse{flex: 1;position: relative;}
#shipping .mock_table .tbody .c_name{padding-left: 30px;}
#shipping .mock_table .tbody .second_box, #shipping .mock_table .tbody .third_box{display: none;}
#shipping .mock_table .tbody .second_box .c_name{padding-left: 60px;}
#shipping .mock_table .tbody .third_box .c_name{padding-left: 90px;}
#shipping .mock_table .tbody .td.operation a{text-decoration: none;color: var(--primaryColor);}
#shipping .mock_table .tr .td .oper_icon{display:inline-block; vertical-align:middle; width:30px; height:30px; line-height:30px; margin-left:7px; border-radius:36px; background-image: none; background-color: #eaf7ff; text-align:center; font-size:0 !important; transition:all 0.3s ease-out; position:relative; font-family: "iconfont" !important; font-size: 16px; font-style: normal; -webkit-font-smoothing: antialiased; -moz-osx-font-smoothing: grayscale;}
#shipping .mock_table .tr .td .oper_icon:before{ font-size: 14px; transition: all 0.3s ease-out; position: absolute; top: 50%; left: 50%; transform: translate(-50%,-50%); }
#shipping .mock_table .tr .td .oper_icon:hover:before{ color: #fff; }
#shipping .mock_table .tr:hover .td .oper_icon{background-color:#fff;}
#shipping .mock_table .tr .td .oper_icon:hover{ background-color:var(--primaryColor);}
#shipping .mock_table .tr .td .oper_icon.disabled {cursor: no-drop; background-color: #f3f3f3;}
#shipping .mock_table .tr .td .oper_icon.disabled:before{ color: #c6c6c6; }
#shipping .mock_table .tr .td .icon_edit:before{ content: "\e6d3"; font-size: 15px; }
#shipping .mock_table .tr .td .icon_transfer::before {content: "\e71e";}
.pos .global_menu_button.left_10{left: 10px;}
#shipping .mock_table .thead .current .td{color: #f7f9fb;}
.r_con_table .tr .td .icon_sub::before, .r_con_table tr td .icon_sub::before{content: "\e669";font-size: 16px;}
#shipping .mock_table .c_name .nav_ext {width: 20px;height: 20px;background: #49c378;position: absolute;left: 0px;top: 0px;bottom: 0;margin: auto; border-radius: 10px;cursor: pointer;z-index: 1;}
#shipping .mock_table .second_box .c_name .nav_ext{left: 30px;}
#shipping .mock_table .c_name .nav_ext:before {width: 11px;height: 1px;background: #fff;position: absolute;left: 50%;top: 50%;content: '';transform: translateX(-50%);z-index: 1;}
#shipping .mock_table .c_name .nav_ext.current:after {width: 1px;height: 11px;background: #fff;position: absolute;left: 50%;top: 50%;content: '';transform: translateY(-50%);z-index: 2;}

.related_products {position: relative; }
.related_products .products_txt {display: inline-block;padding-right: 15px;position: relative;cursor: pointer;}
.related_products .products_txt i {width: 9px;height: 6px;position: absolute;right: 0;top:7px;background-image: url(../images/orders/icon_arrow1.png);transition: all 0.3s;}
.related_products.current .products_txt i {transform: rotate(180deg);}
.related_products .products_container {display: none;position: absolute;left: 0;top: 100%;z-index: 1;}
.related_products .products_box {width: 310px;max-height: 400px;margin: 7px 0;padding: 20px;background-color: #fff;box-sizing: border-box;box-shadow: 0 0 10px 0 rgba(0, 0, 0, 0.2);border-radius: 5px;overflow-y: auto;}
.related_products .products_box::-webkit-scrollbar {width: 5px; background: #fff;border-radius: 5px;}
.related_products .products_box::-webkit-scrollbar-thumb {background: rgba(0, 0, 0, 0.1);border-radius: 5px;}
.related_products .products_box::-webkit-scrollbar-thumb:hover {background: rgba(0, 0, 0, 0.3);}
.related_products .products_box .products_list_line {height: 1px;margin: 15px 0;background-color: #edf0f5;}
.related_products .products_box .products_list .list_title {font-size: 14px;}
.related_products .products_box .products_list .list_item {margin-top: 20px;}
.related_products .products_box .products_list .list_item:first-child {margin-top: 0; }
.related_products .products_box .products_list .list_item .item_img {position: relative; width: 50px; height: 50px; background-color: #f0f0f0; border-radius: 4px;}
.related_products .products_box .products_list .list_item .item_img i {width: 22px;height: 22px;line-height: 22px;background-color: #e4e5e7;font-size: 12px;color: #404852;position: absolute;right: -8px;top: -8px;border-radius: 22px;text-align: center;}
.related_products .products_box .products_list .list_item .item_img img {max-width: 100%;max-height: 100%;border-radius: 4px;overflow: hidden;}
.related_products .products_box .products_list .list_item .item_info {width: 200px;}
.related_products .products_box .products_list .list_item .item_info .info_name {line-height: normal;margin-bottom: 3px;}
.related_products .products_box .products_list .list_item .item_info .info_attr {font-size: 12px;color: #7d8d9e;}
.related_products .products_box .products_list .list_item .item_info .custom_attr p {font-size: 12px;color: #7d8d9e;}
.related_products .products_box .products_list .list_item .item_info .custom_attr img {max-width: 50px;height: 50px;border-radius: 4px;overflow: hidden;}
.related_products .products_box .products_list {margin-top: 20px; }
.related_products .products_box .products_list:first-child {margin-top: 0; }
.related_products .products_box .products_load_more {display: block; margin-top: 30px; text-align: center; font-size: 14px; color: #000;}

.drop_country {position: relative;}
.drop_country .country_txt {display: inline-block; position: relative; height: 23px; line-height: 23px; padding-right: 15px; color: #7d8c9f; cursor: pointer;}
.drop_country .country_txt::after {content: ""; display: inline-block; vertical-align: top; width: 9px; height: 23px; margin-left: 6px; background: url(../images/orders/icon_arrow1.png) no-repeat center; transition: all .3s;}
.drop_country .country_txt.no_drop {cursor: default;}
.drop_country .country_txt.no_drop::after {display: none;}
.drop_country .country_container {display: none; position: absolute; left: 0; top: 100%; z-index: 1;}
.drop_country .country_box {width: 506px; max-height: 506px; overflow-y: auto; margin: 7px 0; padding: 12px 18px; background-color: #fff; box-sizing: border-box; box-shadow: 0 0 10px 0 rgba(0, 0, 0, 0.2); border-radius: 5px;}
.drop_country .country_box::-webkit-scrollbar {width: 5px; background: #fff; border-radius: 5px;}
.drop_country .country_box::-webkit-scrollbar-thumb {background: rgba(0, 0, 0, .1); border-radius: 5px;}
.drop_country .country_box::-webkit-scrollbar-thumb:hover {background: rgba(0, 0, 0, .3);}
.drop_country .country_box .country_list {display: flex; flex-wrap: wrap;}
.drop_country .country_box .country_list .list_item {width: 155px; line-height: 19px; margin-top: 12px; margin-bottom: 0!important; padding-right: 20px; box-sizing: border-box; -webkit-box-sizing: border-box;}
.drop_country .country_box .country_list .list_item:nth-child(-n+3) {margin-top: 0;}
.drop_country .country_box .country_list .list_item:nth-child(n+3) {padding-right: 0;}
.drop_country.current .country_txt::after {transform: rotate(180deg);}
.drop_country .country_box .range_itme{display: flex;}
.drop_country .country_box .range_tit{min-width: 60px;max-width: 80px; padding: 4px 15px 4px 0;line-height: 18px;}
.drop_country .country_box .range_con{flex: 1;}
.drop_country .country_box .range_con p{padding: 4px 0;line-height: 18px;font-size: 12px;}

/* 右侧弹窗 Start */
.freight_rate_edit .unit_input {display: flex; justify-content: space-between;}
.freight_rate_edit .unit_input .box_input {width: 100%;}
/* 右侧弹窗 End */

#shipping .shipping_template_box{display: inline-block;border: 1px solid #dddddd;border-radius: 50px; padding: 0;margin: 4px 10px 0 0;font-size: 14px;}
#shipping .shipping_template_box .box_drop_down_menu{padding: 0 15px 0 20px;border-right: 1px solid #dddddd;}
#shipping .shipping_template_box .box_drop_down_menu .more_menu{min-width: 100%;left: 0;}
#shipping .shipping_template_box .edit_btn{width: 52px;height: 31px;background: #f5f5f5 url(../images/set/icon_set_btn.png) no-repeat center center;border-radius: 0 50px 50px 0;}
#shipping .download{height: 32px;line-height: 32px;display: inline-block;padding: 0 13px 0 35px;background: url(../images/shipping/icon_download.png) no-repeat 14px center;border:1px solid var(--primaryColor);color: var(--primaryColor);font-size: 14px;text-decoration: none;border-radius: 17px;margin-top: 10px;}
#shipping input[name=MinWeight]{border-right: 1px solid #ddd;}
#shipping .weightarea_box {display: flex; overflow: hidden; justify-content: space-between;}
#shipping .weightarea_box .item {position: relative; float: left; width: 100%; height: 158px; border:1px solid #cbcecd; margin-left: 25px; border-radius: 5px; box-sizing: border-box; cursor: pointer;}
#shipping .weightarea_box .item:first-child {margin-left: 0;}
#shipping .weightarea_box .item.cur,#shipping .weightarea_box .item:hover {background-color: #EDF5FF;border-color: var(--primaryColor);}
#shipping .weightarea_box .item i{display: block;width: 70px;height: 70px;background: #f2f6fa url(../images/shipping/icon_weightarea.png) no-repeat -29px -11px; border-radius: 70px; margin: 25px auto 21px;transition: all 0.3s ease-out;}
#shipping .weightarea_box .item.cur i,#shipping .weightarea_box .item:hover i{background-color: unset; background-image: url(../images/shipping/icon_weightarea_cur.png);}
#shipping .weightarea_box .item_1 i{background-position: -157px -11px;}
#shipping .weightarea_box .item_2 i{background-position: -285px -11px;}
#shipping .weightarea_box .item_3 i{background-position: -415px -11px;}
#shipping .weightarea_box .item_4 i{background-position: -541px -11px;}
#shipping .weightarea_box .item_5 i{background-position: -669px -11px;}
#shipping .weightarea_box .item input{display: none;}
#shipping .weightarea_box .item .tool_tips_ico{position: absolute;width: 100%;height: 100%;top: 0;left: 0;background: none;padding: 0;margin: 0;cursor: pointer;}
#shipping .weightarea_box .item .name{margin: 0 auto;line-height: 20px;max-height: 40px;overflow: hidden;color: #555;font-size: 14px;text-align: center;transition: all 0.3s ease-out;}
#shipping .weightarea_box .item.cur .name,#shipping .weightarea_box .item:hover .name{color: var(--primaryColor);}
#shipping .weightarea_box .item.disabled {cursor: no-drop;}
#shipping .weightarea_box .item.disabled i {background-image: url(../images/shipping/icon_weightarea_disabled.png); background-color: #f2f6fa;}
#shipping .weightarea_box .item.disabled:hover {background-color: unset; border-color: #cbcecd;}
#shipping .weightarea_box .item.disabled:hover .name {color: #555;}
#shipping .shipping_area_title{height:30px; line-height:30px; margin-bottom:5px; padding:0 12px; font-weight:bold; background-color:#fff; border:1px #E8E8E8 solid;}

/*地区部分*/
#shipping .big_title {margin-bottom: 0;}

#shipping .box_area {position: relative;}
#shipping .box_area .btn_add_area {margin-left: 10px; color: #fff; background-color: var(--primaryColor);}
#shipping .box_area .content {min-height: 92px; max-height: 276px; overflow-y: auto; margin-top: 28px; padding: 10px 4px 0px 10px; border:1px solid #dce1e4; border-radius: 5px;}
#shipping .box_area .content[data-range="zipCode"]{border: none;max-height: 500px;}
#shipping .box_area .content::-webkit-scrollbar {width: 5px; background: #fff; border-radius: 5px;}
#shipping .box_area .content::-webkit-scrollbar-thumb {background: rgba(0, 0, 0, .1); border-radius: 5px;}
#shipping .box_area .content::-webkit-scrollbar-thumb:hover {background: rgba(0, 0, 0, .3);}
#shipping .box_area .item {height: 36px; line-height: 36px; margin-right: 10px; margin-bottom: 10px; padding-right: 10px; float: left; background: #EDF5FF; border-radius: 5px;}
#shipping .box_area .item.add_country_btn {width: 120px; margin: 24px auto 0; padding: 0 20px;  float:none; text-align: center; cursor: pointer; color: #fff; background: var(--primaryColor); border-radius: 50px;}
#shipping .box_area .item input {display: none;}
#shipping .box_area .img {width: 16px; height: 16px; margin:10px; float: left; font-size: 0;}
#shipping .box_area .img img {max-width: 16px; max-height: 16px; vertical-align: top;}
#shipping .box_area .icon_flag {vertical-align: top;}
#shipping .box_area .name {float: left; font-size: 14px; color: #555;}
#shipping .box_area .area_box {display: none; width: 36px; height: 36px; float: left; cursor: pointer; background: no-repeat center center;}
#shipping .box_area .area_box.del {background-image: url(../images/frame/icon_close.png);}
#shipping .box_area .box_button {position: absolute; top: 25px; right: 24px; font-size: 0;}
#shipping .box_area .btn_import_area {padding: 0 23px; background-color: #fff;}
#shipping .box_area_import .area_down_box {display: none;}
#shipping .box_area .dl{border:1px solid #dce1e4;margin-top: 20px;border-radius: 3px;}
#shipping .box_area .dl:first-child{margin-top: 0;}
#shipping .box_area .dt{display: flex; background-color: #f4f6f7;line-height: 40px;}
#shipping .box_area .dd{display: flex;align-items: center;}
#shipping .box_area .range_tit{width: 150px;padding-left: 20px;}
#shipping .box_area .range_con{flex: 1;}
#shipping .box_area .dd .range_con{padding-top: 20px;}
#shipping .box_area .range_item{display: flex;padding: 0px 26px 20px 0;align-items: center;}
#shipping .box_area .range_item .box_select{width: 110px;margin-right: 20px;}
#shipping .box_area .range_item .range_value{flex: 1;}
#shipping .box_area .range_item .value_item[data-value-type="NumberRange"]{border: 1px #ccdced solid;border-radius: 5px;display: flex;width: max-content;align-items: center;}
#shipping .box_area .range_item .value_item[data-value-type="NumberRange"] input{height: 32px;width: 90px;padding: 0 10px;border: 1px solid transparent;border-radius: 5px;font-size: 12px;}
#shipping .box_area .range_oper{display: flex;border:1px solid #dce1e4;border-radius: 5px;}
#shipping .box_area .range_oper a{width: 34px;height: 34px;line-height: 34px;text-align: center;color: #b0b0b0;}
#shipping .box_area .range_oper a span{font-size: 14px;}
#shipping .box_area .range_oper a.range_add{display: none;}
#shipping .box_area .range_con[data-max-item="1"] .range_oper a.range_del{display: none;}
#shipping .box_area .range_item:last-child .range_oper .range_add{display: block;}
#shipping .box_area .range_item:last-child .range_oper .range_del{border-left: 1px solid #dce1e4;}

#shipping .box_area .range_item .value_item[data-value-type="Specify"]{display: flex;flex-wrap: wrap; padding-left: 4px;padding-top: 4px;border: 1px #ccdced solid;border-radius: 5px;margin-right: 35px;max-width: 305px;}
#shipping .box_area .range_item .value_item[data-value-type="Specify"] .enter_input{height: 26px;border: 0;padding-left: 4px;padding-right: 8px;min-width: 50px;flex:1;margin-bottom: 4px;font-size: 12px;}
#shipping .box_area .code_item{line-height:18px; margin-right:8px; margin-bottom:4px; padding:4px 18px; color:#555; cursor:default; background-color:#daeffe; border-radius:16px; position:relative; transition:all 0.3s; -moz-transition:all 0.3s; -webkit-transition:all 0.3s;}
#shipping .box_area .code_item .cname{font-size:12px;color: #555;word-break: break-word;}
#shipping .box_area .code_item .cdel{width:18px; height:26px; background-color:#daeffe; border-left:0; border-top-right-radius:16px; border-bottom-right-radius:16px; position:absolute; top:0;bottom: 0; right:-1px;margin:auto; z-index:0; cursor:pointer; display:block; transition:all 0.3s; -moz-transition:all 0.3s; -webkit-transition:all 0.3s;}
#shipping .box_area .code_item .cdel span{font-size: 0px;line-height: 26px;}
#shipping .box_area .code_item:hover .cdel{right:-7px; z-index:1;}
#shipping .box_area .code_item:hover .cdel span{font-size: 8px;}
.zipcode_intersect_tips{height: max-content;min-height: 310px; top: 0 !important;right: 0;bottom: 0;left: 0;margin: auto;}
.zipcode_intersect_tips .win_content{position: static;transform: translate(0);}
.zipcode_intersect_tips .ext_html{max-height: 320px;overflow: auto;margin-top: 20px;display: flex;justify-content: center;}
.zipcode_intersect_tips .ext_html::-webkit-scrollbar {width: 5px; background: #fff;border-radius: 5px;}
.zipcode_intersect_tips .ext_html::-webkit-scrollbar-thumb {background: rgba(0, 0, 0, 0.1);border-radius: 5px;}
.zipcode_intersect_tips .ext_html::-webkit-scrollbar-thumb:hover {background: rgba(0, 0, 0, 0.3);}
.zipcode_intersect_tips .cenbox{padding: 20px 20px 0;width: max-content;}
.zipcode_intersect_tips .cenbox .list{padding-bottom: 20px;}
.zipcode_intersect_tips .cenbox .lleft{font-weight: bold;font-size: 12px;color: #555;line-height: 24px;padding-right: 10px;vertical-align: top;white-space:nowrap;text-align: right;padding-top: 15px;}
.zipcode_intersect_tips .cenbox .lright{line-height: 24px;font-size: 12px;color: #888;vertical-align: top;padding-top: 15px;text-align: left;}

#shipping #area_import_form .express_down_box .global_select_box{z-index: 10;}
#shipping #area_import_form .select_tips {display: none; line-height: 18px; margin: 10px 0; color: var(--GlobalTipColor);}
#shipping #area_import_form .select_tips::before {content: ""; display: inline-block; vertical-align: top; width: 18px; height: 18px; margin-right: 6px; background: url(../images/frame/app_tips_icon.png) no-repeat center;}

#shipping .box_info .rows {margin-top: 28px; margin-top: 23px;}
#shipping .box_info .rows .box_input {width: 50%;}

#shipping .box_free .rows {margin-top: 28px;}
#shipping .box_free .free {display: flex;align-items: center;height: 40px;}
#shipping .box_free .box_type_menu_content .item {color: #7d8c9f;}
#shipping .box_free .input_checkbox_box.disabled .input_checkbox{background-color: var(--primaryColor);border-color: var(--primaryColor);}
#shipping .box_free .input_checkbox_box.disabled .input_checkbox:before{background-color: var(--primaryColor);}

#shipping .box_freight {position: relative;}
#shipping .box_freight .rows {margin-top: 28px;}
#shipping .box_freight .box_button {position: absolute; top: 25px; right: 24px; font-size: 0;}
#shipping .box_freight .btn_add_list {margin-left: 10px; color: #fff; background-color: var(--primaryColor);}
#shipping .box_freight .btn_import {padding: 0 23px; background-color: #fff;}
#shipping .box_freight .r_con_table {margin-top: 28px;}
#shipping .box_freight .box_qty {display: flex;}
#shipping .box_freight .box_qty .rows {width: 240px; margin-left: 24px;}
#shipping .box_freight .box_qty .rows .input>.box_input {width: 100%; box-sizing: border-box; -webkit-box-sizing: border-box;}
#shipping .box_freight .box_qty .rows .input .unit_input>b {min-width: 30px;}
#shipping .box_freight .box_qty .rows .input .unit_input>b.last {min-width: 34px;}
#shipping .box_freight .box_qty .rows:first-child {margin-left: 0;}

#shipping .box_affix .rows {margin-top: 28px;}

#shipping #fixed_right {z-index: 9999;}

.fixed_btn_submit .btn_continue {width: auto; padding: 0 25px;}

/* 右侧弹窗 Start */
#freight_edit_form .box_calculation {display: flex;}
#freight_edit_form .box_calculation .input_radio_box {width: 252px; margin-left: 10px;}
#freight_edit_form .box_calculation .input_radio_box:first-child {margin-left: 0;}
#freight_edit_form .calculation_content .content {display: none;}
#freight_edit_form .box_between {display: flex;}
#freight_edit_form .box_between .rows {width: 188px; margin-left: 24px; margin-bottom: 30px; box-sizing: border-box; -webkit-box-sizing: border-box;}
#freight_edit_form .box_between .rows>label {height: 37px; line-height: 37px;}
#freight_edit_form .box_between .rows .input {position: relative;}
#freight_edit_form .box_between .rows .input .unit_input {display: flex;}
#freight_edit_form .box_between .rows .input .unit_input .box_input.has_error{ border: 1px solid red; }
#freight_edit_form .box_between .rows .input .unit_input>b {min-width: 30px;}
#freight_edit_form .box_between .rows .input .unit_input>b.last {min-width: 34px;}
#freight_edit_form .box_between .rows .input .unit {position: absolute; top: 0; right: -36px; height: 36px; line-height: 36px; color: var(--GlobalAssistColor);}
#freight_edit_form .box_between .rows:first-child {margin-left: 0;}
#freight_edit_form .box_between .btn_option_remove {display: block; width: 34px; height: 34px; line-height: 34px; margin-top: 37px; margin-left: 24px; border: 1px solid #ccdced; border-radius: 5px; text-align: center;}
#freight_edit_form .box_between .btn_option_remove::before {content: "\e60b"; width: 34px; height: 34px; color: var(--GlobalAssistColor); font-family: "iconfont"!important; font-size: 16px; font-style: normal; -webkit-font-smoothing: antialiased; -moz-osx-font-smoothing: grayscale;}
#freight_edit_form .box_between .box_range {width: 400px;}
#freight_edit_form .box_range {display: flex;}
#freight_edit_form .box_range>em {width: 24px; height: 36px; line-height: 36px; margin-top: 37px; text-align: center;}
#freight_edit_form .box_range .rows {width: 188px; margin-bottom: 30px; margin-left: 0; box-sizing: border-box; -webkit-box-sizing: border-box;}
#freight_edit_form .box_range .rows>label {height: 37px; line-height: 37px;}
#freight_edit_form .box_range .rows .input {position: relative;}
#freight_edit_form .box_range .rows .input .unit_input {display: flex;}
#freight_edit_form .box_range .rows .input .unit_input>b {min-width: 30px;}
#freight_edit_form .box_range .rows .input .unit_input>b.last {min-width: 34px;}
#freight_edit_form .box_range .rows .input .unit {position: absolute; top: 0; right: -36px; height: 36px; line-height: 36px; color: var(--GlobalAssistColor);}
#freight_edit_form .no_title .rows>label {display: none;}
#freight_edit_form .no_title .box_range>em, #freight_edit_form .no_title .btn_option_remove {margin-top: 0;}
#freight_edit_form .btn_add_node {display: block; height: 20px; line-height: 20px; color: var(--primaryColor);}
#freight_edit_form .btn_add_node:before {content: "\e6ba"; display: inline-block; vertical-align: top; width: 20px; height: 20px; margin-right: 7px; text-align: center; font-size: 10px; font-family: "iconfont"!important; font-style: normal; color: #fff; -webkit-font-smoothing: antialiased; -moz-osx-font-smoothing: grayscale; background-color: var(--primaryColor); border-radius: 50px;}
#freight_edit_form .global_app_tips {display: inline-block; vertical-align: top; width: 400px; margin-top: 40px; padding: 10px 18px; color: var(--GlobalTitleColor); box-sizing: border-box; -webkit-box-sizing: border-box;}
#freight_edit_form .content[data-type=each] .box_between .btn_option_remove {margin-left: 60px;}

#freight_import_form .box_list_freight {padding-bottom: 30px;}
#freight_import_form .box_list_freight .item {margin-top: 29px;}
#freight_import_form .box_list_freight .item .title {height: 35px; line-height: 35px; color: var(--GlobalTitleColor);}
#freight_import_form .box_list_freight .item .content {padding: 30px 25px; background-color: #f8f9fb;}
#freight_import_form .box_list_freight .item .content .content_node {height: 9px; line-height: 9px; margin-top: 40px; margin-bottom: 10px; font-size: 12px; color: var(--GlobalAssistColor);}
#freight_import_form .box_list_freight .item .content .content_node:first-child {margin-top: 0;}
#freight_import_form .box_list_freight .item .content .content_name {height: 33px; line-height: 33px; margin-top: 30px; font-size: 12px; color: var(--GlobalTitleColor);}
#freight_import_form .box_list_freight .item .content .content_name:first-child {margin-top: 9px;}
#freight_import_form .box_list_freight .item .content .content_item {position: relative;}
#freight_import_form .box_list_freight .item .content .content_item .unit {position: absolute; top: 0; left: 317px; height: 36px; line-height: 36px; font-size: 12px; color: var(--GlobalAssistColor);}
#freight_import_form .box_list_freight .item .content .unit_input {display: flex; width: 305px; font-size: 12px;}
#freight_import_form .box_list_freight .item .content .unit_input>b {min-width: 30px;}
#freight_import_form .box_list_freight .item .content .unit_input .box_input {width: 100%;}
#freight_import_form .box_list_freight .item:first-child {margin-top: 20px;}
#freight_import_form .box_list_freight .item[data-type=additional] .content .content_item .unit {top: 33px;}
/* 右侧弹窗 End */

#shipping .rows_hd{height: 62px;line-height: 62px;margin: 15px 0 10px;font-size: 16px;border-top: 1px solid #e8e8e8;}
#shipping .country_area_title{height:40px; line-height:40px; margin-top:5px; font-size:14px; position:relative;}
#shipping .country_area_title .down{width:40px; height:40px; background: url(../images/shipping/icon_country_down.png) no-repeat center center; position:absolute; top:0; right:0;}
#shipping .country_area_title .down.cur{transform:rotate(180deg); -moz-transform:rotate(180deg); -webkit-transform:rotate(180deg);}
.country_area_box .continent_area .continent{height:16px;padding:22px; border-bottom: 1px solid #edf0f5; border-radius:5px;position: relative; font-size:14px;cursor: pointer;}
.country_area_box .continent_area .continent .input_checkbox_box{margin:0;}
.country_area_box .continent_area .continent .input_checkbox_box .input_checkbox{margin-right: 15px;}
.country_area_box .continent_area .continent .down{width: 0;height: 0;border-style:solid;border-width: 5px 0 5px 5px;border-color: transparent;border-left-color: #888888;margin:auto;position: absolute;left: 0;top: 0;bottom: 0;transition: 0.3s all;}
.country_area_box .continent_area .continent .down.cur{transform:rotate(90deg);-moz-transform:rotate(90deg);-webkit-transform:rotate(90deg);}
.country_area_box .continent_area .country_item{display:none;border-bottom: 1px solid #edf0f5;}

.country_area_box .country_item{padding-left: 22px;}
.country_area_box .country_item .input_checkbox_box{display: block;height: 42px;line-height: 42px;overflow: hidden;border-top: 1px solid #edf0f5;padding-left: 55px;position: relative;}
.country_area_box .country_item .input_checkbox_box:first-child{border: none;}
.country_area_box .country_item .input_checkbox_box .input_checkbox{margin:auto;position: absolute;top: 0;bottom: 0;left: 22px;}
.country_area_box .country_item .input_checkbox_box .img{display: none;width: 16px;height: 16px;line-height: 0;vertical-align: top;margin:21px 5px 21px 0;font-size: 0;}
.country_area_box .country_item .input_checkbox_box .img img{max-width: 16px;max-height: 16px;vertical-align: top;}
.country_area_box .country_item .input_checkbox_box.disabled{color:#ccc; cursor:no-drop;}
.country_area_box .country_item .input_checkbox_box.disabled .input_checkbox{border-color:#ccc;}
.country_area_box .country_item .input_checkbox_box.disabled .input_checkbox:before{background:none; display:none;}

.country_area_box .country_item_sec{display: flex;user-select: none;}
.country_area_box .country_item_sec .input_checkbox_box{flex: 1;}
.country_area_box .country_item_sec .states_count{line-height: 42px;padding: 0 15px 0 10px;position: relative;cursor: pointer;}
.country_area_box .country_item_sec .states_count .down{width: 0;height: 0;border-style:solid;border-width: 5px 0 5px 5px;border-color: transparent;border-left-color: #888888;margin:auto;position: absolute;right: 0;top: 0;bottom: 0;transition: 0.3s all;}
.country_area_box .country_item_sec.cur .states_count .down{transform:rotate(90deg);-moz-transform:rotate(90deg);-webkit-transform:rotate(90deg);}
.country_area_box .country_item_third{display: none; padding-left: 22px;user-select: none;}
.country_area_box .country_item_third .input_checkbox_box{height: 42px;line-height: 42px;}
.country_area_box .global_form .rows:last-child{margin-bottom: 5px;}

#shipping .row{margin-bottom:4px;}
#shipping .row .d_del{width:24px; height:24px; overflow:hidden; margin-top:6px; margin-left:10px; display:inline-block; vertical-align:top;}
#shipping table td{padding-right: 20px; white-space: normal;}
#shipping table td .rows{margin-bottom: 0;}
#shipping table td span{margin-bottom:6px;}
#shipping #InsArea td span{margin-bottom:6px; margin-right:10px;}

#shipping .shipping_area{width:100%; zoom:1; position:relative;}
#shipping .shipping_area:after{clear:both; display:block; content:'';}
#shipping .shipping_area .disable{filter:alpha(opacity=50); -moz-opacity:0.5; opacity:0.5;}

#shipping .shipping_area .nav_list .list_btn{border-bottom: 1px solid #eee;margin-bottom: 20px;}
#shipping .shipping_area .nav_list .list_btn a{float: left;height: 33px;line-height: 33px;padding:0 20px;border:1px solid #eeeeee;border-radius: 5px 5px 0 0;color: #aaa;background: #f4f4f4;text-decoration: none;margin-bottom: -1px;}
#shipping .shipping_area .nav_list .list_btn a.cur{background: #fff;border-bottom-color: #fff;color: #000;}
#shipping .shipping_area .nav_list .list_btn a.add{border-color: #fff;background: none;border-bottom-color: #eee;color: var(--primaryColor);}
#shipping .shipping_area .shipping_area_list .area_hd{padding:15px 0 8px;}
#shipping .shipping_area .shipping_area_list .area_hd>h4{height:24px; line-height:24px;}
#shipping .shipping_area .shipping_area_list .area_list{padding:0;}
#shipping .shipping_area .shipping_area_list .area_list>li{position: relative;padding:15px 180px 15px 25px;margin-bottom: 10px;background: #f2f8f6;}
#shipping .shipping_area .shipping_area_list .area_list>li>h5{line-height:32px; overflow:hidden;color: #000;font-size: 14px;}
#shipping .shipping_area .shipping_area_list .area_list>li .desc{line-height: 20px;max-height: 60px;overflow: hidden;font-size: 12px;color: #999;}
#shipping .shipping_area .shipping_area_list .area_list>li .operate{position: absolute;right: 0;top: 0;bottom: 0;margin:auto;height: 48px;}
#shipping .shipping_area .shipping_area_list .area_list>li .operate a{display: block;height: 24px;line-height: 24px;padding: 0 25px;}
#shipping .shipping_area .shipping_area_list .sub_click{width:74%; background:#f7f7f7; padding-left:2%; cursor:move;}
#shipping .shipping_area .shipping_area_list .cur{color:#87AABE; font-weight:bold;}
#shipping .shipping_area .shipping_area_list .menu_one:hover, #shipping .shipping_area .shipping_area_list .sub:hover, #shipping .shipping_area .shipping_area_list  .sub:hover .sub_click{background-color:#e6ebee; color:#444; text-decoration:none;}
#shipping .shipping_area .shipping_area_list .sub h5{ height:28px; overflow:hidden;}
#shipping .shipping_area .shipping_area_list .sub:hover h5{width:125px;}
#shipping .shipping_area .shipping_area_list .menu_one:hover .menu_view, #shipping .shipping_area .shipping_area_list .sub:hover .menu_view{display:inline;}
#shipping .shipping_area .shipping_area_list .placeHolder{border:1px #1584D5 dashed; background:#fff;}
#shipping .shipping_area .shipping_area_list .config_table tr td{padding:10px;}
#shipping .shipping_area .edit_form{overflow-y:auto;}
#shipping .shipping_area .r_con_form .rows>label{width:25%;}
#shipping .shipping_area .r_con_form .rows .input{width:60%; overflow:inherit;}

#shipping .add_unit{position: relative;width:25px; height:25px;  overflow:hidden;  text-align:center; text-decoration:none; font-size:0px; color:var(--primaryColor);font-weight: bold; border-radius:50%; display:inline-block; vertical-align:top;border: 1px solid var(--primaryColor);margin-top: 2px;}
#shipping .add_unit:before{position: absolute;top: 0;bottom: 0;right: 0;left: 0;margin:auto;width: 3px;height: 13px;background: var(--primaryColor);content: '';}
#shipping .add_unit:after{position: absolute;top: 0;bottom: 0;right: 0;left: 0;margin:auto;width: 13px;height: 3px;background: var(--primaryColor);content: '';}
#shipping .add_unit{margin-left:8px; transition:transform 80ms ease; transform:rotate(0); -webkit-transform:rotate(0);}
#shipping .input_unit{overflow:inherit;}
#shipping .box_select{max-width: 50%;}

#WeightBetween, #VolumeBetween, #ExtWeight, #ExtWeightArea, #WeightArea, #Quantity, #VolumeArea{display:none;}

#shipping .input.visible{overflow: visible;}

#method_api_box .unit_input{margin-bottom:6px;}
#method_api_box .rows p{padding: 5px 0;line-height: 22px;}

.box_warehouse_tips {max-width: 80%; line-height: 20px; margin-top: 28px; font-size: 14px;}
.box_warehouse_tips::before {content: ""; display: inline-block; vertical-align: top; width: 18px; height: 18px; margin-top: 1px; background: url(../images/frame/app_tips_icon.png) no-repeat center;}
.box_warehouse_tips>span {margin-left: 6px; color: var(--GlobalTitleColor);}
.box_warehouse_tips .btn_warehouse_reset {margin-left: 6px; text-decoration: underline; color: #f68f44;}

#shipping .list_menu_button .add {min-width: 54px; height: 30px; line-height: 30px; margin-top: 2px; text-align: center; border-radius: 50px;}

/* 右侧弹窗 Start */
#fixed_right .shipping_overseas_edit .edit_overseas_list{margin-top:29px;}
#fixed_right .shipping_overseas_edit .rows label{padding:0;}
#fixed_right .shipping_overseas_edit .rows .tab_box_btn{padding:0;}
#fixed_right .shipping_overseas_edit .item{padding:7px 0;}
#fixed_right .shipping_overseas_edit .item .box_input{height:38px; line-height:38px; float:left;}
#fixed_right .shipping_overseas_edit .item .product_count{width:95px; line-height:40px; overflow:hidden; margin-left:13px; color:#999; float:left;}
#fixed_right .shipping_overseas_edit .item .overseas_delete{margin-top:8px; margin-left:13px; float:left;}
#fixed_right .shipping_overseas_edit .box_button{margin-top:17px;}
#fixed_right .shipping_overseas_edit .box_button .btn_global{margin-right:8px; border:0!important;}

#fixed_right .edit_template_list{margin-top:29px;}
#fixed_right .edit_template_list .rows{background: #f4f4f4; border-radius: 5px; margin-bottom: 8px; padding: 20px;}
#fixed_right .edit_template_list .product_count{line-height: 24px;padding: 3px 0;}
#fixed_right .edit_template_list .product_count em{text-decoration: underline;}
.shipping_template_edit .shipping_list_box .item{height: 32px;line-height: 32px;overflow: hidden;font-size: 14px;color: #555;}
.shipping_template_edit .shipping_list_box .input_checkbox_box span{display: inline-block;vertical-align: top;margin-top: 8px;}

#fixed_right .warehouse_add_box .logistics_list .input_checkbox_box{display:block; padding:8px 0; color:#555;}
#fixed_right .warehouse_add_box .logistics_list .input_checkbox_box:hover{background-color:#f3fcfb;}

#fixed_right .warehouse_reset_box {padding: 20px 24px;}
#fixed_right .warehouse_reset_box .t_tit {height: 24px; line-height: 24px; margin-top: 5px;}
#fixed_right .warehouse_reset_box .box_type_menu {width: 100%;}
#fixed_right .warehouse_reset_box .box_type_menu span {display: block; height: 36px; line-height: 36px; margin-top: 20px; margin-right: 0; font-size: 14px;}
#fixed_right .warehouse_reset_box .box_type_menu span::before {margin-right: 8px; vertical-align: text-bottom;}
#fixed_right .warehouse_reset_box #box_circle_container {display: none; margin-top: 120px;}
#fixed_right .warehouse_reset_box .rows.box_submit {padding: 10px 24px;}
#fixed_right .warehouse_reset_box .input_button .btn_submit {border: 0;}
/* 右侧弹窗 End */
/*************************** 运费管理 end ***************************/


/*************************** 图片管理 start ***************************/
.upload{height:32px; line-height:32px; padding:9px 0; overflow:hidden; float:left; max-width:550px;}
.upload>h3{float:left; margin-left:11px; margin-right:11px; font-weight:bold;}
.upload .up_input{position:relative; float:left; margin-right:5px; margin-bottom: 10px;}
.upload .up_input>object{float:left;}
.upload .up_input .loading{width:89px; height:32px; background-color:#08b98b; border-color:#08b98b; border-radius:5px; position:absolute; top:0; left:0; text-align:center;}
.upload .upload_file_tips {display: inline-block; height: 32px; line-height: 32px; padding: 0; padding-left: 6px; font-size: 12px; color: #f68f44;}
.upload .upload_file_tips::before {content: ""; display: inline-block; vertical-align: top; width: 18px; height: 18px; margin-top: 7px; margin-right: 6px; background: url(../images/frame/app_tips_icon.png) no-repeat;}

.photo_choice_box{padding: 0;}
.photo_choice_box .inside_container{padding: 0;}
.photo_choice_box .inside_table{padding-top: 0;padding-bottom:0; background-color:transparent;margin-top: 0;}

.file_choice_box{padding: 0;}
.file_choice_box .inside_container{padding: 0;}
.file_choice_box .inside_table{padding-top: 0;padding-bottom:0; background-color:transparent;margin-top: 0;}

#photo .icon {width: 1em; height: 1em; overflow: hidden; vertical-align: 0em; fill: currentColor;}

#photo .r_con_table .break-all {word-break: break-all; word-wrap: break-word;}

#photo .inside_container { padding-top: 20px; padding-bottom: 0; border: 0;}

#photo .photo_menu_button{position: relative;float: none;padding-top: 0px;}
#photo .photo_menu_button .input_checkbox_box{display: inline-block; margin-top: 7px;margin-right: 20px;}
#photo .photo_list{padding: 10px 0 0; box-sizing: border-box;}
#photo .photo_list::-webkit-scrollbar{width: 5px;background: #fff;border-radius: 5px;}
#photo .photo_list::-webkit-scrollbar-thumb{background: rgba(0, 0, 0, 0.1);border-radius: 5px;}
#photo .photo_list::-webkit-scrollbar-thumb:hover{background: rgba(0, 0, 0, 0.3);}
#photo .photo_list .photo_list_box{display: block; height: 100%; font-size: 0;flex-wrap: wrap;justify-content: center; position: relative; overflow-y: auto;box-sizing: border-box; padding-bottom: 70px;}
#photo .photo_list .photo_list_box:after{content: '';display: inline-block;width: 100%;height: 0;clear: both;}
#photo .photo_list .item{display: inline-block; vertical-align: top;width: 110px;box-sizing: border-box; position: relative;border-radius: 4px;border: 1px solid #eeeeee;margin: 4px;padding: 14px;box-shadow: 2px 2px 5px rgba(0, 0, 0, 0.05);}
#photo .photo_list .item .img{display: flex;height:80px;justify-content: center;align-items: center;overflow: hidden;}
#photo .photo_list .item .img img{vertical-align:middle; max-width:100%;}
#photo .photo_list .item .img input{display:none;}
#photo .photo_list .item .img_mask{display:none; width: 25px; background: rgba(0,0,0,0.5); position:absolute; top: 0;left: 0; width: 100%;height: 100%; z-index:3;border-radius: 5px; cursor: pointer; }
#photo .photo_list .item .img_mask .zoom{width: 25px;height: 25px;position: absolute; top: 10px; left: 10px; transition: all 0.1s ease-out; font-family: "iconfont" !important; font-size: 16px; font-style: normal; -webkit-font-smoothing: antialiased; -moz-osx-font-smoothing: grayscale;}
#photo .photo_list .item .img_mask .zoom:before{ content: "\e609"; color: #fff; font-size: 22px; position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%);  }
#photo .photo_list .item .img_mask .icon_list{ width: 25px; position: absolute; top: 10px; right: 13px; }
#photo .photo_list .item .img_mask .icon_list>*{ margin-top: 9px; display: block; width: 25px; height: 25px; border: 2px solid #bfc0c0; border-radius: 100%; background-color: #5f6061; color: #abacad; position: relative; box-sizing: border-box; }
#photo .photo_list .item .img_mask .icon_list>*:first-child{ margin-top: 0; }
#photo .photo_list .item .img_mask .icon_list>*:before{ position: absolute; top: 50%; left: 50%; transform: translate(-50%,-50%); }
#photo .photo_list .item .img_mask .icon_list>.view:before{ width: 13px; height: 10px; line-height: 10px;font-size: 14px; }
#photo .photo_list .item.cur .img_mask .icon_list .status{ color: #fff; border-color: #08b98b; background: #08b98b; }
#photo .photo_list .item .img_mask .icon_topleft { display: none; width: 25px; position: absolute; top: 10px; left: 45px; }
#photo .photo_list .item .img_mask .icon_topleft>* { margin-top: 9px; display: block; width: 25px; height: 25px; font-size: 23px; color: #fff; position: relative; box-sizing: border-box; }
#photo .photo_list .item .img_mask .icon_topleft>*:first-child { margin-top: 0; }
#photo .photo_list .item .img_mask .icon_topleft>*:before { position: absolute; top: 50%; left: 50%; transform: translate(-50%,-50%); }
#photo .photo_list .item .img_view{width:24px; height:24px; overflow:hidden; position:absolute; top: 0;right: 0;bottom: 0;left: 0;margin: auto; z-index:4; display:none;background: url(../images/frame/icon_multi_view.png) no-repeat center;}
#photo .photo_list .item .img_tags{width:18px; height:24px; overflow:hidden; position:absolute; top: 11px;left: 12px; z-index:4; display:none;background: url(../images/frame/icon_tags.png) no-repeat center;}
#photo .photo_list .item .name{text-align:center;margin-top: 14px; overflow:hidden; height:22px; line-height:22px; overflow:hidden; font-size:12px; white-space:nowrap; text-overflow:ellipsis;}
#photo .photo_list .item .name>a{display:block;white-space:nowrap; text-overflow:ellipsis;overflow: hidden;}
#photo .photo_list .item .zoom{position:absolute; right:3px; bottom:3px; z-index:4; outline:0;}
#photo .photo_list .item .refresh{position:absolute; right:32px; bottom:3px; z-index:4; outline:0;}
#photo .photo_list .cur .img_mask,#photo .photo_list .item:hover .img_mask, #photo .photo_list .cur .img_view, #photo .photo_list .item:hover .img_view, #photo .photo_list .cur .img_tags, #photo .photo_list .item:hover .img_tags{display:block;}
#photo .list_foot{height:30px; overflow:hidden; padding:5px 170px;}
#photo .list_foot .btn_ok{padding:0 25px;}
#photo .list_foot .btn_cancel{padding:0 25px;}
#photo #PicDetail{margin-top: 10px;}
#photo .photo_multi_img .item{display: flex;align-items: center;border-radius: 5px;margin-bottom: 12px; background-color: #f7f9fb;}
#photo .photo_multi_img .item a{display: flex;width:60px; height:60px; margin: 13px;margin-right: 0;align-items: center;justify-content: center;}
#photo .photo_multi_img .item a img{max-width:100%; max-height:100%;border-radius: 5px;}
#photo .photo_multi_img .item span{display: block; width: 42px;height: 42px; background: url(../images/products/icon_square_delete.png) no-repeat center;font-size: 0;order: 3;margin-left: 5px;margin-right: 5px;border-radius: 42px;cursor: pointer;}
#photo .photo_multi_img .item .icon {margin: 22px; margin-right: 12px; font-size: 40px;}
#photo .photo_multi_img .item .box_input{flex: 1; padding: 0 10px; height:34px; line-height:34px;margin-left: 20px;background: #fff;}
#photo .turn_page_box{ width: calc( 100% - 390px ); height: 60px; line-height: 60px; position: absolute; bottom: 0; text-align: right; background: #fff; z-index: 10; border-top: 1px solid #efefef; }
#photo .turn_page_box #turn_page{ text-align: center; padding: 0; }

#photo .photo_list .photo_list_box::-webkit-scrollbar,
#photo_list_form .photo_choose_box::-webkit-scrollbar {width: 5px; background: #fff;border-radius: 5px;}
#photo .photo_list .photo_list_box::-webkit-scrollbar-thumb,
#photo_list_form .photo_choose_box::-webkit-scrollbar-thumb {background: rgba(0, 0, 0, 0.1);border-radius: 5px;}
#photo .photo_list .photo_list_box::-webkit-scrollbar-thumb:hover,
#photo_list_form .photo_choose_box::-webkit-scrollbar-thumb:hover {background: rgba(0, 0, 0, 0.3);}

#photo_list_form{ display: flex; flex-direction: row; align-items: flex-start; height: 100%; }
#photo_list_form .photo_list_box{ flex: 1; }
#photo_list_form .photo_choose_box{display: block; flex: none; margin: 5px 10px 0; padding: 15px; font-size: 0; box-sizing: border-box; width: 350px; height: calc( 100% - 10px ); border-radius: 5px; background-color: #fff; overflow-y: auto; }
#photo_list_form .photo_choose_box .item{ margin: 3px; }
#photo .photo_list .photo_choose_box .item{ display: inline-block; vertical-align: top; padding: 8px; width: 70px; }
#photo .photo_list .photo_choose_box .item .img{ height: 52px; }
#photo .photo_list .photo_choose_box .item .name{ display: none; }
#photo .photo_list .photo_choose_box .item .icon_list{ display: none; }
#photo .photo_list .photo_choose_box .item .img_mask{ display: none; }
#photo .photo_list .photo_choose_box .item .close_btn { cursor: pointer;; position: absolute; top: -8px; right: -8px; font-size: 12px; background: #000;color: #fff;border-radius: 50%;width: 16px;height: 16px;line-height: 16px;box-sizing: border-box; }
#photo .photo_list .photo_choose_box .item .close_btn i{ font-size: inherit; display: block; position: absolute; top: 50%; left: 50%; transform: translate(-50% , -50%) scale(0.5); }

#photo.auto_box_choice .inside_table{padding:0;}

#photo.photo_choice_gallery .photo_list .item .img_mask .icon_list { display: none; }
#photo.photo_choice_gallery .photo_list .item .img_mask .icon_topleft { display: block; right: 10px; left: auto; }
#photo.photo_choice_gallery .photo_list .photo_choose_box { display: none; }
#photo.photo_choice_gallery .photo_list .cur .img_mask { display: none; }
#photo.photo_choice_gallery .photo_list .item:hover .img_mask,
#photo.photo_choice_gallery .photo_list .item:hover .img_view,
#photo.photo_choice_gallery .photo_list .item:hover .img_tags { display: block; }
#photo.photo_choice_gallery .turn_page_box { width: 100%; }

#file .file_menu_button{position: relative;float: none;padding-top: 0px;}
#file .file_menu_button .input_checkbox_box{display: inline-block; margin-top: 7px;margin-right: 20px;}
#file .file_list{padding: 20px 0; min-height: 300px;}
#file .file_list::-webkit-scrollbar{width: 5px;background: #fff;border-radius: 5px;}
#file .file_list::-webkit-scrollbar-thumb{background: rgba(0, 0, 0, 0.1);border-radius: 5px;}
#file .file_list::-webkit-scrollbar-thumb:hover{background: rgba(0, 0, 0, 0.3);}
#file .file_list .file_list_box{display: flex;flex-wrap: wrap;justify-content: center;}
#file .file_list .file_list_box:after{content: '';display: inline-block;width: 100%;height: 0;clear: both;}
#file .file_list .item{width: 154px;box-sizing: border-box; position: relative;border-radius: 4px;border: 1px solid #eeeeee;margin: 5px;padding: 14px;box-shadow: 2px 2px 5px rgba(0, 0, 0, 0.05);}
#file .file_list .item .img{display: flex;height:124px;justify-content: center;align-items: center;overflow: hidden;}
#file .file_list .item .img img{vertical-align:middle; max-width:100%;}
#file .file_list .item .img input{display:none;}
#file .file_list .item .img_mask{display:none; width: 25px; background: rgba(0,0,0,0.5); position:absolute; top: 0;left: 0; width: 100%;height: 100%; z-index:3;border-radius: 5px; cursor: pointer; }
#file .file_list .item .img_mask .zoom{width: 60px;height: 60px;background: url(../images/frame/icon_multi_view.png) no-repeat center center;position: absolute;top: 0;right: 0;bottom: 0;left: 0;margin: auto;transition: all 0.1s ease-out;}
#file .file_list .item .img_mask .zoom:hover{transform: scale(1.2);}
#file .file_list .item .img_mask .icon_list{ width: 25px; position: absolute; top: 10px; right: 13px; }
#file .file_list .item .img_mask .icon_list>*{ margin-top: 9px; display: block; width: 25px; height: 25px; border: 2px solid #bfc0c0; border-radius: 100%; background-color: #5f6061; color: #abacad; position: relative; box-sizing: border-box; }
#file .file_list .item .img_mask .icon_list>*:first-child{ margin-top: 0; }
#file .file_list .item .img_mask .icon_list>*:before{ position: absolute; top: 50%; left: 50%; transform: translate(-50%,-50%); }
#file .file_list .item .img_mask .icon_list>.view:before{ width: 13px; height: 10px; line-height: 10px;font-size: 14px; }
#file .file_list .item.cur .img_mask .icon_list .status{ color: #fff; border-color: #08b98b; background: #08b98b; }
#file .file_list .item .img_view{width:24px; height:24px; overflow:hidden; position:absolute; top: 0;right: 0;bottom: 0;left: 0;margin: auto; z-index:4; display:none;background: url(../images/frame/icon_multi_view.png) no-repeat center;}
#file .file_list .item .img_tags{width:18px; height:24px; overflow:hidden; position:absolute; top: 11px;left: 12px; z-index:4; display:none;background: url(../images/frame/icon_tags.png) no-repeat center;}
#file .file_list .item .name{text-align:center;margin-top: 14px; overflow:hidden; height:22px; line-height:22px; overflow:hidden; font-size:12px; white-space:nowrap; text-overflow:ellipsis;}
#file .file_list .item .name>a{display:block;white-space:nowrap; text-overflow:ellipsis;overflow: hidden;}
#file .file_list .item .zoom{position:absolute; right:3px; bottom:3px; z-index:4; outline:0;}
#file .file_list .item .refresh{position:absolute; right:32px; bottom:3px; z-index:4; outline:0;}



#file .file_list.all_file_box .file_list_box { border-top: 1px solid #edf0f5; }
#file .file_list.all_file_box .file_list_box .thead{ width: 100%; background-color: #f7f9fb; }
#file .file_list.all_file_box .file_list_box .thead .item_name{ box-sizing: border-box; padding: 20px; color: #404852; font-weight: bold; }

#file .file_list.all_file_box{ background-color: #fff; }
#file .file_list.all_file_box .file_list_box .tbody{ width: 100%; }
#file .file_list.all_file_box .file_list_box .tbody .file_item{ border-top: 1px solid #edf0f5; display: flex; align-items: center; justify-content: center; flex-wrap: wrap; cursor: pointer;; }
#file .file_list.all_file_box .file_list_box .tbody .file_item:hover{ background-color: #ecf5ff; }
#file .file_list.all_file_box .file_list_box .tbody .file_item:first-child{ border-top: 0; }
#file .file_list.all_file_box .file_list_box .tbody .file_item .item_name { box-sizing: border-box; padding: 18px 20px; color: #404852; }
#file .file_list.all_file_box .file_list_box .tbody .file_item .item_name:first-child{ text-align: center; }
#file .file_list.all_file_box .file_list_box .tbody .file_item .item_name .name{ text-overflow: ellipsis; display: -webkit-box; -webkit-box-orient: vertical; -webkit-line-clamp: 1; overflow: hidden; }
#file .file_list.all_file_box .file_list_box .tbody .file_item .item_name .icon{ width: 30px; height: 30px; }
#file .file_list.all_file_box .file_list_box .tbody .file_item .item_name .ext{ margin-top: 5px; color: #888888; font-size: 12px; }
#file .file_list.all_file_box .file_list_box .tbody .file_item .item_name input{ display: none; }
#file .file_list.all_file_box .file_list_box .tbody .file_item .item_name.checkbox:before{ display: inline-block; width: 14px; height: 14px; content: ''; margin-right: 4px; background-color: #fff; border: 1px solid #ccdced; border-radius: 16px; vertical-align: text-top; }
#file .file_list.all_file_box .file_list_box .tbody .file_item.cur .item_name.checkbox:before{ width: 6px; height: 6px; border: 5px solid var(--primaryColor); border-radius: 16px; }
#file .file_list.all_file_box .file_list_box .tbody .file_item:last-child{ border-bottom: 1px solid #edf0f5; }



#file .file_list .cur .img_mask,#file .file_list .item:hover .img_mask, #file .file_list .cur .img_view, #file .file_list .item:hover .img_view, #file .file_list .cur .img_tags, #file .file_list .item:hover .img_tags{display:block;}
#file .list_foot{height:30px; overflow:hidden; padding:5px 170px;}
#file .list_foot .btn_ok{padding:0 25px;}
#file .list_foot .btn_cancel{padding:0 25px;}
#file #PicDetail{margin-top: 10px;}
#file .file_multi_img .item{display: flex;align-items: center;border-radius: 5px;margin-bottom: 12px; background-color: #f7f9fb;}
#file .file_multi_img .item a{display: flex;width:60px; height:60px; margin: 13px;margin-right: 0;align-items: center;justify-content: center;}
#file .file_multi_img .item a img{max-width:100%; max-height:100%;border-radius: 5px;}
#file .file_multi_img .item span{display: block; width: 42px;height: 42px; background: url(../images/products/icon_square_delete.png) no-repeat center;font-size: 0;order: 3;margin-left: 5px;margin-right: 5px;border-radius: 42px;cursor: pointer;}
#file .file_multi_img .item .icon {margin: 22px; margin-right: 12px; font-size: 40px;}
#file .file_multi_img .item .box_input{flex: 1; padding: 0 10px; height:34px; line-height:34px;margin-left: 20px;background: #fff;}

#file.auto_box_choice .inside_table{padding:0;}
#file .upload{max-width: 650px;}

.photo_index .return_title{margin: 0 0 15px; padding-top: 0;padding-bottom: 0;}
.photo_index .inside_container>h1{padding: 0;}
.photo_index td.wrap{word-wrap: break-word;white-space: normal;}
.photo_index td .pic_name{font-size: 14px;max-width: 500px;}
.photo_index td .pic_suffix{color: #888;}
.photo_index td .icon {font-size: 30px;}
.photo_index td .tags_item{display: inline-block;min-height: 28px;line-height: 1.8;padding: 7px 17px;background-color: #daf2fe;border-radius: 28px;margin: 3px 7px 3px 0;}
.photo_index td .pic_box{position: relative;}
.photo_index td .pic_box a{display: none;position: absolute;top: 0;right: 0;bottom: 0;left: 0;background: rgba(0,0,0,0.5) url(../images/frame/icon_multi_view.png) no-repeat center;border-radius: 4px;}
.photo_index td .pic_box:hover a{display: block;}
.photo_index .bg_no_table_data{*height: calc(100% - 73px) !important; background: #fff;border-radius: 5px;}
.photo_index .btn_copy{display: inline-block;margin-top: 5px; background: url(../images/products/icon_copy.png) no-repeat left center;padding-left: 21px;color: var(--primaryColor);}

.box_photo_edit>form {padding: 15px 0;}
.box_photo_edit .global_form .rows label {line-height: 24px;}
.box_photo_edit .global_form .rows .note_tips {display: block; line-height: 23px; color: var(--GlobalAssistColor);}
.box_photo_edit .global_form .box_tags label {line-height: 34px;}

.box_photo_edit .fileinput-button {margin-top: 9px; padding: 0 23px; background-color: var(--primaryColor);}
.box_photo_edit .fileinput-button::before, .box_photo_edit .fileinput-button::after {display: none;}
.box_photo_edit .fileupload-buttonbar {padding-bottom: 10px; border-bottom: 1px #edf0f5 solid;}
.box_photo_edit .fileupload-progress {line-height: 0; padding-top: 10px;}
.box_photo_edit .fileupload-progress.in {line-height: 34px;}
/*************************** 图片管理 end ***************************/



/*分享图标*/
#share_list .share_btn{width:60px; height:60px; margin-left:10px; margin-bottom:10px; background-image:url(../images/frame/share.png); background-repeat:no-repeat; background-color:transparent; border:1px #fff solid; cursor:move; border-radius:50%; overflow:hidden;}
#share_list .placeHolder{border:1px #aaa dashed; border-radius:50%; background:#fff;}
.share_facebook{background-position:0 0;}
.share_google{background-position:-60px -120px;}
.share_twitter{background-position:-60px 0;}
.share_vk{background-position:-120px -120px;}
.share_linkedin{background-position:-120px 0;}
.share_googleplus{background-position:-180px 0;}
.share_digg{background-position:-120px -60px;}
.share_reddit{background-position:-180px -60px;}
.share_stumbleupon{background-position:0 -120px;}
.share_delicious{background-position:-60px -60px;}
.share_pinterest{background-position:0 -60px;}


/*************************** 平台授权 start ***************************/
#authorization .pop_form .button .btn_ok, #authorization .pop_form .button .btn_cancel{float:none;}
#authorization .pop_form_amazon .r_con_form .rows label{width:200px;}
#authorization .pop_form_amazon .r_con_form .rows .input{width:500px;}
#authorization .pop_form_amazon .r_con_form .rows .input a.blue{color:#09F; text-decoration:underline;}
#authorization .pop_form_amazon .r_con_form .rows .input .bg_gray{background:#ccc; cursor:no-drop;}

#authorization .open_api tbody tr td center{ padding-bottom:12px;}
#authorization .open_api tbody tr td center p{font-size:24px; padding:12px 0;}
#authorization .open_api center .btn_ok{display:inline-block; float:none; min-width:60px;}
/*************************** 平台授权 end ***************************/



/*************************** 支付设置 start ***************************/
#payment .inside_container{ padding-top: 20px; }
#payment .pay_type{overflow:hidden;}
#payment .pay_type a{float:left;margin-right: 20px;font-size: 14px;color: #666;padding-bottom: 14px;border-bottom: 3px solid transparent;text-decoration: none;}
#payment .pay_type a.cur{border-color: var(--primaryColor);color: var(--primaryColor);}
#payment .pay_list .item{width: 30%;margin: 40px 0 40px 5%;float: left;}
#payment .pay_list .item.fir{margin-left: 0;}
#payment .pay_list .pic{height: 32px;margin-bottom: 6px;vertical-align: middle;font-size: 0;}
#payment .pay_list .pic img{max-width: 100%;max-height: 100%;vertical-align: middle;}
#payment .pay_list .pic span{display: inline-block;height: 100%;vertical-align: middle;}
#payment .pay_list .name{height: 34px;line-height: 34px;overflow: hidden;color: #444;font-size: 14px;}
#payment .pay_list .desc{font-size: 12px;color: #999;line-height: 20px;height: 80px;overflow: hidden;}
#payment .pay_list .get{display: block;background: var(--primaryColor);border-radius: 5px;color: #fff;font-size: 14px;width: 60px;height: 28px;line-height: 28px;text-align: center;text-decoration: none;margin-top: 12px;}
#payment .pay_list .line{border-bottom: 1px solid #f1f1f1;}
#payment .global_container .tit{line-height: 40px;font-size: 20px;color: #1f2328;}
#payment .global_container .desc{line-height: 22px;padding: 5px 0;font-size: 14px;color: var(--GlobalAssistColor);}
#payment .global_container .btn_tutorials{margin-left: 20px;margin-top: 20px;margin-right: 20px;padding: 0 25px;float: right;}
#payment .payment_list_container { margin: 0; padding: 0; overflow: inherit; }
#payment .bg_no_table_data .content{top: 80px;}
#payment .payment_head {padding: 0 30px;}
#payment .payment_head .search_box {margin: 20px 0;}
#payment .payment_head .search_box_selected { margin-top: 0; margin-bottom: 10px; }
#payment .payment_head .list_menu_button { margin: 20px 0; padding: 0; }
#payment .payment_head .set_account_button { color: var(--primaryColor); background-color: var(--GlobalBtnSecBgColor); }
#payment .payment_menu { display: flex; align-items: center; justify-content: space-between; padding: 0 30px; background-color: #f9fbfa; border-top: 1px solid #edf0f5; border-bottom: 1px solid #edf0f5; color: #555; font-size: 14px; line-height: 45px; font-weight: bold;}
#payment .payment_menu .payment_col { margin-right: 2%; }
#payment .payment_menu .action { flex: 1; width: 13%; }
#payment .payment_box{margin-bottom: 30px;}
#payment .payment_box .payment_name{ font-size:20px; margin-bottom: 15px;}
#payment .payment_box .payment_item{ position: relative; padding:30px; background:#fff;}
#payment .payment_box .payment_item.first{ margin-top:15px;}
#payment .payment_box .payment_item .payment_icon{ width:70px; height:70px; float:left;}
#payment .payment_box .payment_item .payment_icon.showtype_0{ background:url(../images/set/payment/showtype0.png) center no-repeat;}
#payment .payment_box .payment_item .payment_icon.showtype_1{ background:url(../images/set/payment/showtype1.png) center no-repeat;}
#payment .payment_box .payment_item .payment_icon.showtype_2{ background:url(../images/set/payment/showtype2.png) center no-repeat;}
#payment .payment_box .payment_item .payment_icon.showtype_3{ background:url(../images/set/payment/showtype3.png) center no-repeat;}
#payment .payment_box .payment_item .payment_icon.showtype_4{ width: 125px; background:url(../images/set/payment/method/CashOnDelivery.png) center no-repeat;}
#payment .payment_box .payment_item .payment_icon.showtype_5{ width: 125px; background:url(../images/set/payment/method/GooglePay.png) center no-repeat;}
#payment .payment_box .payment_item .payment_icon.showtype_7{ width: 125px; background:url(../images/set/payment/method/OceanPayment.png) no-repeat center center / 100%; }
#payment .payment_box .payment_item .payment_icon.showtype_OceanpaymentAggregatePayment{ width: 125px; background:url(../images/set/payment/method/OceanPayment.png) no-repeat center center / 100%; }
/* #payment .payment_box .payment_item .payment_icon.showtype_DlocalAggregatePayment{ width: 125px; background:url(../images/set/payment/method/OceanPayment.png) no-repeat center center / 100%; } */
#payment .payment_box .payment_item.merge_item{position: relative;}
#payment .payment_box .payment_item.merge_item::before{content: '';position: absolute;top: 0;display: block;width: calc(100% - 60px);height: 1px;background-color: #f1f1f1;}
#payment .payment_box .payment_item .payment_tips{ height:70px; line-height:70px; margin-left:40px; font-size:12px; color:#888; float:left;}
#payment .payment_box .payment_item .paypal_tips{margin-left:40px; padding-top:10px; float:left;}
#payment .payment_box .payment_item .paypal_tips>strong{line-height:27px; font-size:16px; color:#555;}
#payment .payment_box .payment_item .paypal_tips>p{line-height:25px; font-size:12px;}
#payment .payment_box .payment_item .paypal_tips>p>span{display:inline-block; color:#888;}
#payment .payment_box .payment_item .paypal_tips>p>a{display:inline-block; margin-left:12px; padding-right:17px; position:relative; color:var(--primaryColor);}
#payment .payment_box .payment_item .paypal_tips>p>a::after{display:block; content:""; width:8px; height:5px; background-image:url(../images/products/icon_menu_arrow.png); background-position:right; background-repeat:no-repeat; position:absolute; top:10px; right:0; transition:all .3s;}
#payment .payment_box .payment_item .paypal_tips>p>a.hover::after{transform:rotate(180deg);}
#payment .payment_box .payment_item .paypal_info{display:none; border-top:1px #f1f1f1 solid; margin-top:30px; padding:23px 0; padding-left:111px;}
#payment .payment_box .payment_item .paypal_info>strong{line-height:29px; font-size:16px; color:#555;}
#payment .payment_box .payment_item .paypal_info>ul>li{line-height:23px; margin-top:7px; padding-left:20px; position:relative; font-size:12px; color:#888;}
#payment .payment_box .payment_item .paypal_info>ul>li::before{display:block; content:""; width:5px; height:5px;  background-color:var(--primaryColor); border-radius:50%; position:absolute; top:9px; left:0;}
#payment .payment_box .payment_item .paypal_info>a{ display: block; margin-top: 15px; font-size: 12px; color: #07bb8a; }
#payment .payment_box .payment_item .paypal_info>p{line-height:21px; margin-top:25px; font-size:12px; color:#aaa;}
#payment .payment_box .payment_item .paypal_try {display: flex; justify-content: space-between; margin-top: 10px; padding: 15px; background-color: #f2f2f2; border-radius: 5px;}
#payment .payment_box .payment_item .paypal_try>strong {line-height: 30px;}
#payment .payment_box .payment_item .paypal_try>strong>span {color: var(--primaryColor);}
#payment .payment_box .payment_item .paypal_try .btn_try_activate {margin: 0;}
#payment .payment_box .payment_item .paypal_notice { display: flex; justify-content: space-between; border: 1px solid #4BA0FC; border-radius: 5px; margin-top: 10px; padding: 20px; background-color: #EAF8EC; line-height: 30px; }
#payment .payment_box .payment_item .paypal_notice>strong { flex: 1; }
#payment .payment_box .payment_item .paypal_notice .box_button { width: 20%; text-align: right; }
#payment .payment_box .payment_item .paypal_notice .box_button>a { margin: 0 6px; }
#payment .payment_box .payment_item .paypal_notice .box_button #btn_learn_more { color: var(--primaryColor); }
#payment .payment_box .payment_item .paypal_notice .box_button #btn_remind_again { color: #7E8DA0; }
#payment .payment_box .paypal_notice_bg { margin-top: -10px; padding-top: 0; }
#payment .payment_box .payment_item .paypal_loans { display: flex; justify-content: space-between; position: relative; z-index: 99; margin-top: -30px; padding: 20px; background-color: #F8F9FB; line-height: 30px; }
#payment .payment_box .payment_item .paypal_loans .box_text { flex: 1; }
#payment .payment_box .payment_item .paypal_loans .content { margin-top: 5px; color: #7E8DA0; }
#payment .payment_box .payment_item .paypal_loans .box_button { width: 20%; text-align: right; }
#payment .payment_box .payment_item .paypal_loans .box_button>a { margin: 0 6px; }
#payment .payment_box .payment_item .paypal_loans .box_button #btn_online { color: var(--primaryColor); }
#payment .payment_box .payment_item .paypal_loans.paypal_loans_up { margin-top: 30px; }
#payment .payment_box .payment_item .btn_global{ margin-left:10px; margin-top:16px; padding:0 25px; float:right;}

#payment .payment_box .payment_item .oper_icon {display:inline-block; vertical-align:middle; width:30px; height:30px; line-height:30px; margin-left:7px; border-radius:36px; background-image: none; background-color: #eaf7ff; text-align:center; font-size:0 !important; transition:all 0.3s ease-out; position:relative; font-family: "iconfont" !important; font-size: 16px; font-style: normal; -webkit-font-smoothing: antialiased; -moz-osx-font-smoothing: grayscale; color:var(--primaryColor);}
#payment .payment_box .payment_item .oper_icon:before {font-size: 18px; transition: all 0.3s ease-out; position: absolute; top: 50%; left: 50%; transform: translate(-50%,-50%);}
#payment .payment_box .payment_item .oper_icon:hover {background-color:var(--primaryColor);}
#payment .payment_box .payment_item .oper_icon:hover:before {color: #fff;}
#payment .payment_box .payment_item .oper_icon.disabled {cursor: no-drop; background-color: #f3f3f3;}
#payment .payment_box .payment_item .oper_icon.disabled:before {color: #c6c6c6; }
#payment .payment_box .payment_item .icon_submit:before {content: "\e6a8"; font-size: 14px;}
#payment .payment_box .payment_item .icon_cancel:before {content: "\e6e8";}
#payment .payment_box .payment_item .icon_set:before { content: "\e6a5"; font-size: 14px; }
#payment .payment_box .payment_item .icon_disable:before { content: "\e61d"; font-size: 14px; }
#payment .payment_box .payment_item .icon_delete:before { content: "\e60b"; font-size: 14px; }
#payment .payment_box .payment_item .icon_detail:before { content: "\e72c"; font-size: 20px; }

#payment .payment_box .payment_list_item { display: flex; align-items: center; justify-content: space-between; position: relative; padding: 30px 30px 31px; }
#payment .payment_box .payment_list_item .payment_param { overflow: hidden; margin-right: 2%; }
#payment .payment_box .payment_list_item .payment_param .payment_h1 { display: flex; height: 70px; line-height: 26px; font-size: 14px; color: var(--GlobalTextColor); align-items: center; flex-wrap: wrap; }
#payment .payment_box .payment_list_item .payment_param .supply {width: 100%; line-height: 18px; font-size: 12px; color: var(--GlobalAssistColor); margin-top: 2px; }
#payment .payment_box .payment_list_item .payment_param .payment_img { max-width: 150px; height: 70px; }
#payment .payment_box .payment_list_item .payment_param .payment_img img { max-width: 100%; max-height: 100%; }
#payment .payment_box .payment_list_item .payment_param .payment_title { font-size: 12px; color: #888; margin-top: 15px; }
#payment .payment_box .payment_list_item .payment_param .payment_value { font-size: 16px; color: #000; padding-top: 10px; }
#payment .payment_box .payment_list_item .payment_param.payment_provider { width: 17%; }
#payment .payment_box .payment_list_item .payment_param.payment_card  { display: flex; flex-wrap: wrap; gap: 7px; width: 55%; font-size: 0; }
#payment .payment_box .payment_list_item .payment_param.payment_card img { max-width: 100px; max-height: 24px; margin-top: 7px; border: 1px solid transparent; }
#payment .payment_box .payment_list_item .payment_param.payment_status { width: 9%; }
#payment .payment_box .payment_list_item .payment_param.payment_method { display: flex; flex-wrap: wrap; gap: 10px; width: 66%; font-size: 0; }
#payment .payment_box .payment_list_item .payment_param.payment_method .method_item { display: flex; align-items: center; border-radius: 5px; overflow: hidden; padding: 5px; color: #404852; background-color: #EDFCF8; }
#payment .payment_box .payment_list_item .payment_param.payment_method .method_item .item_logo { display: flex; flex-wrap: wrap; align-content: center; justify-content: center; height: 15px; margin-right: 5px; background-color: #fff; }
#payment .payment_box .payment_list_item .payment_param.payment_method .method_item .item_logo>img { max-width: 100%; max-height: 100%; }
#payment .payment_box .payment_list_item .payment_param.payment_method .method_item .item_name { font-size: 12px; }
#payment .payment_box .payment_list_item .payment_param.payment_logo { width: 17%; }
#payment .payment_box .payment_list_item .payment_param.payment_logo_offline { width: 36%; }
#payment .payment_box .payment_list_item .payment_param.payment_names { width: 15%; }
#payment .payment_box .payment_list_item .payment_param.payment_names_offline { width: 36%; }
#payment .payment_box .payment_list_item .payment_param.country { width: 20%; }
#payment .payment_box .payment_list_item .payment_param.currency { width: 16%; }
#payment .payment_box .payment_list_item .operation { justify-content: flex-end; flex: 1; width: 10%; }
#payment .payment_box .payment_list_item .payment_param.provider,
#payment .payment_box .payment_list_item .payment_param.country,
#payment .payment_box .payment_list_item .operation { display: flex; align-items: center; }
#payment .payment_box .payment_list_item .payment_status_btn { display: inline-block; padding: 6px 10px; font-size: 12px; border-radius: 25px; color: #999; background-color: #f3f3f3; }
#payment .payment_box .payment_list_item .payment_status_btn.status_used { color: var(--GlobalMainColor); background-color: #eaf8ec; }
#payment .payment_box .payment_list_item::after { display: block; content:''; position: absolute; bottom: 0; left: 0; width: 100%; height: 1px; background-color:#f1f1f1; }

#payment .payment_box .bg_no_table_fixed {padding: 250px 0;}

#payment .payment_list_1 .payment_menu .payment_col { width: 17%; }
#payment .payment_list_1 .payment_menu .payment_col:nth-child(2) { width: 55%; }
#payment .payment_list_1 .payment_menu .payment_col:nth-child(3) { width: 9%; }
#payment .payment_list_2 .payment_menu .payment_col { width: 36%; }
#payment .payment_list_2 .payment_menu .payment_col:nth-child(2) { width: 36%; }
#payment .payment_list_2 .payment_menu .payment_col:nth-child(3) { width: 9%; }
#payment .payment_list_3 .payment_menu .payment_col { width: 17%; }
#payment .payment_list_3 .payment_menu .payment_col:nth-child(2) { width: 66%; }
#payment .payment_list_3[data-provider="0"] .payment_list_item { align-items: flex-start; }
#payment .payment_list_3[data-provider="1"] .payment_menu .payment_col { width: 17%; }
#payment .payment_list_3[data-provider="1"] .payment_menu .payment_col:nth-child(2) { width: 15%; }
#payment .payment_list_3[data-provider="1"] .payment_menu .payment_col:nth-child(3) { width: 20%; }
#payment .payment_list_3[data-provider="1"] .payment_menu .payment_col:nth-child(4) { width: 16%; }
#payment .payment_list_3[data-provider="1"] .payment_menu .payment_col:nth-child(5) { width: 9%; }

#payment .payment_list_3[data-provider-name="custom"] .payment_menu .payment_col { width: 20%; }
#payment .payment_list_3[data-provider-name="custom"] .payment_menu .payment_col:nth-child(2) { width: 30%; }
#payment .payment_list_3[data-provider-name="custom"] .payment_menu .payment_col:nth-child(3) { width: 20%; }
#payment .payment_list_3[data-provider-name="custom"] .payment_box .payment_list_item .payment_param.payment_logo { width: 20%; }
#payment .payment_list_3[data-provider-name="custom"] .payment_box .payment_list_item .payment_param.payment_names { width: 30%; }
#payment .payment_list_3[data-provider-name="custom"] .payment_box .payment_list_item .payment_param.payment_status { width: 20%; }

/* [New] 支付设置排版 */
#payment .payment_box .payment_row {display: flex; flex-direction: row; justify-content: space-between; align-items: center; position: relative; padding: 30px; text-align: center; background-color: #fff;}
#payment .payment_box .payment_row.theader { padding: 30px 30px 0; color: #888; font-size: 12px; line-height: 45px; }
#payment .payment_box .payment_row.info {justify-content: flex-start; width: 100%; padding: 0; background-color: transparent; text-align: left;}
#payment .payment_box .payment_row .action {width: 100px; padding-left: 10px;}
#payment .payment_box .payment_item+.payment_row:not(.theader)::before,
#payment .payment_box .payment_row:not(.theader)+.payment_row::before {content: ''; position: absolute; top: 0; display: block; width: calc(100% - 60px); height: 1px; background-color: #f1f1f1;}
#payment .payment_box .payment_once_row { padding-top: 0; }
#payment .payment_box .merge::after { content: ''; position: absolute; top: 0; z-index: 1; display: block; width: calc(23% - 100px); height: 1px; background-color: #fff; }
#payment .payment_box .merge .payment_col:nth-child(1) { visibility: hidden; }
#payment .payment_box[data-type="0"] { position: relative; }
#payment .payment_box[data-type="0"] .abstation_tips { padding: 30px 30px 0; background-color: #fff; }
#payment .payment_box[data-type="0"] .abstation_tips .global_app_tips { position: relative; z-index: 99; width: max-content; margin: 0; }
#payment .payment_box[data-type="0"] .abstation_tips .global_app_tips>a { color: var(--primaryColor); }
#payment .payment_box[data-type="0"] .abstation_mark { position: absolute; top: 41px; left: 0; z-index: 1; width: 100%; height: calc(100% - 41px); background-color: rgba(255, 255, 255, .5); }
#payment .payment_box[data-type="2"] .payment_row.theader,
#payment .payment_box[data-type="3"] .payment_row.theader { border: solid #EDF0F5; border-width: 1px 0 1px; padding-top: 0; color: #555; background-color: #F9FBFA; }
#payment .payment_box[data-type="1"] .payment_item::after { display: block; content: ''; position: absolute; bottom: 0; width: calc(100% - 60px); height: 1px; background-color: #f1f1f1; }
.payment_row .payment_col:nth-child(1) {width: 18%;}
.payment_row .payment_col:nth-child(2) {width: 21%;}
.payment_row .payment_col:nth-child(3) {width: 17%;}
.payment_row .payment_col:nth-child(4) {width: 10%;}
.payment_row .payment_col:nth-child(5) {width: 26%;}
.payment_row .payment_col+.payment_col {margin-left: 2%;}
.payment_row .payment_logo {max-width: 150px; height: 70px;display: flex;align-items: center;}
.payment_row .payment_logo img {max-width: 100%; max-height: 100%;}
.payment_row .payment_value {font-size: 16px; line-height: 30px; color: #000;}
.payment_row .payment_title {font-size: 12px; line-height: 16px; color: #888; margin-top: 15px;}
.payment_row .credit_cards {display: flex; flex-direction: row; justify-content: flex-start; align-items: center; flex-wrap: wrap; gap: 7px; }
.payment_row .credit_cards img {max-width: 100px; max-height: 24px; border: 1px solid transparent;}

#payment .center_container_1440 .payment_box .payment_item .payment_param{ width:14%; margin-right:2%;}
#payment .center_container_1440 .payment_box .payment_item .payment_param_half{float: left;width: 7%;margin-right: 1%;}
#payment .center_container_1440 .payment_box .payment_item .payment_param.payment_card{ width:270px;}

#payment .payment_limit .input_checkbox_box{user-select: none;}
#payment .payment_limit .input_checkbox_box .input_checkbox{vertical-align: middle;margin-top: -2px;}
#payment .payment_limit .unit_input .box_input[readonly]{background-color: #f4f5f7;}
#payment .order_list { display: flex; flex-direction: column; flex-wrap: nowrap; justify-content: center; align-items: center; position: relative; border:1px solid #ddd; border-radius: 5px; height: 88px; padding: 10px 0; text-align: center; margin-top: 10px; background-color: #fff; }
#payment .order_list i { position: absolute; top: 0; bottom: 0; left: 15px; margin: auto; }
#payment .order_list img { vertical-align: middle; max-width: 70%; max-height: 70%; }
#payment .order_list .title { font-size: 18px; }
#payment .order_list .provider { margin-top: 8px; font-size: 12px; color: #7E8DA0; }
#payment .order_list .credit_cards { display: flex; flex-wrap: wrap; flex-direction: row; justify-content: center; align-items: center; margin-top: 8px; }
#payment .order_list .credit_cards .card { margin-left: 3px;}
#payment .order_list .credit_cards img { max-width: 100px; max-height: 20px; border: 1px solid transparent; }
#payment .order_list:hover { cursor: move; }
#payment .order_list.placeHolder { border: 1px #aaa dashed; }
#payment .aggregate_box{ height: 400px; overflow-y: auto; }
#payment .card_rows{ padding:12px 0; border-bottom:1px solid #edf0f5;}
#payment .card_rows .card_rows_checkbox{ margin-top:4px; float:left;}
#payment .card_rows .card_rows_img{ width:100px; height:24px; margin-left:30px; float:left; font-size:0;}
#payment .card_rows .card_rows_img.text_center{ text-align: center; margin-left: 0; }
#payment .card_rows .card_rows_img img{ max-width: 100%; max-height: 100%; border:1px solid transparent;}
#payment .card_rows .card_rows_name{ margin-top:4px; margin-left:15px; float:left; color:#555;}

#payment .use_button_box li {background:#fafafa; border-right:3px; margin-top:20px; padding:0 20px; position:relative; font-size:14px; color:#888;}
#payment .use_button_box li:first-child {margin-top: 10px;}
#payment .use_button_box li .box_content {min-height: 70px; padding: 21px 0; box-sizing: border-box; -webkit-box-sizing: border-box;}
#payment .use_button_box li .box_content>strong {display:block; padding-right: 60px; line-height:28px; color:#000;}
#payment .use_button_box li .box_content>span { display: block; margin-top: 8px; padding-right: 60px; font-size: 12px; line-height: 19px; }
#payment .use_button_box li .rcon {height:20px; position:absolute; top:25px; right:20px; margin:auto; color:#000;}
#payment .use_button_box li .switchery {vertical-align:top;}
#payment .use_button_box li .editor { display: none; padding-bottom: 20px; }
#payment .use_button_box li .editor.show { display: block; }
#payment .use_button_box li em {color:#f5222d;}

#payment .box_credit_card_payment { display: none; margin-top: 15px; }
#payment .box_credit_card_payment strong { height: 30px; color: #000; line-height: 30px; }
#payment .box_credit_card_payment .item { overflow: hidden; border-radius: 5px; margin: 10px 0; background-color: #fff; }
#payment .box_credit_card_payment .item .input_radio_box { display: inline-block; vertical-align: top; width: inherit; margin: 0; padding-right: 0; background-color: unset; }
#payment .box_credit_card_payment .item .input_radio_box>strong { margin-bottom: 17px; padding-right: 0; }
#payment .box_credit_card_payment .item .input_radio_box .input_radio { border: 0; margin: 0; }
#payment .box_credit_card_payment .item .input_radio_box:hover .input_radio { box-shadow: none; }
#payment .box_credit_card_payment .item .input_radio_box:hover>strong,
#payment .box_credit_card_payment .item .input_radio_box.checked>strong { color: var(--primaryColor); }
#payment .box_credit_card_payment .item .input_radio_box.disabled { cursor: no-drop; }
#payment .box_credit_card_payment .item .input_radio_box.disabled .input_radio { display: none; background-color: unset; }
#payment .box_credit_card_payment .item .btn_preview { margin-top: 22px; }
#payment .box_credit_card_payment .item .global_app_tips { margin: -10px 20px 17px; }
#payment .box_credit_card_payment .box_credit_card_3ds { display: none; }
#payment .box_credit_card_payment .box_credit_card_3ds li { background-color: #fff; }
#payment .box_credit_card_payment .box_credit_card_3ds.show { display: block; }
#payment .box_credit_card_payment .box_card_support { margin-top: 10px; padding: 20px; background-color: #fff; }
#payment .box_credit_card_payment.show { display: block; }

#payment #item_express_credit_card { display: none; }
#payment #item_express_credit_card.show { display: block; }
#payment #item_applepay_express { display: none; }
#payment #item_applepay_express.show { display: block; }
#payment #item_googlepay_express { display: none; }
#payment #item_googlepay_express.show { display: block; }

#payment .global_app_tips.ppcp_tips { display: none; }
#payment .global_app_tips.ppcp_tips>span { margin: 0; padding: 0; }
#payment .global_app_tips.ppcp_tips a { color: #f68f44; }
#payment .global_app_tips.ppcp_tips.show { display: flex; }

#payment .box_extend_content { display: none; width: 100%; margin-top: 20px; }
#payment .box_extend_content .global_app_tips { display: block; margin-top: 0; }
#payment .box_extend_content .global_app_tips a { color: #f68f44; }
#payment .table_local_payment { margin: 20px 0 30px; }
#payment .table_local_payment .r_con_table { margin: 0;}
#payment .table_local_payment td:last-child { text-align: center; }
#payment .table_local_payment thead td { padding-top: 0; padding-bottom: 0; }
#payment .table_local_payment tbody td { vertical-align: top; }
#payment .table_local_payment tbody .payment_method_item { display: flex; align-items: center; margin-top: 18px; font-size: 14px; color: #404852; }
#payment .table_local_payment tbody .payment_method_item img { height: 23px; margin-right: 10px; }
#payment .table_local_payment tbody .payment_method_item:first-child { margin-top: 0;}
#payment .table_local_payment tbody .payment_method_item[data-method=sofort] { margin-left: -14px; }

#payment .table_excheckout { margin-top: 20px; }
#payment .table_excheckout .set_item { display: flex; align-items: center; justify-content: space-between; position: relative; margin-bottom: 10px; padding: 20px; color: #000; background-color: #fafafa; }
#payment .table_excheckout .set_item.flex_none { display: block; }
#payment .table_excheckout .set_item.flex_none>strong { display: block; margin-bottom: 20px; }
#payment .table_excheckout .set_second_item { display: flex; align-items: center; justify-content: space-between; position: relative; margin-top: 10px; color: #000; }

#payment .box_tutorials {margin-top: 2px; margin-bottom: 31px;}
#payment .box_tutorials .boxs {padding: 20px; border-radius: 5px; background-color: #f8f9fb;}
#payment .box_tutorials .tit {line-height: 32px; font-size: 16px; color: #1f2328;}
#payment .box_tutorials .info {line-height: 19px; font-size: 12px; color: var(--GlobalAssistColor);}
#payment .box_tutorials .list {margin-top: 14px; padding-bottom: 14px; border-bottom: 1px solid #edf0f5;}
#payment .box_tutorials .list:last-child {border-bottom: unset;}
#payment .box_tutorials .item {line-height: 24px; color: #1f2328; font-size: 14px;}
#payment .box_tutorials .item.desc {color: #404852;}
#payment .box_tutorials .btn_view {margin-top: 12px; float: right; color: #fff; background-color: var(--primaryColor); border: 1px solid var(--primaryColor);}

#payment .account_info .box_input[readonly] { background-color: #f4f5f7; }
#payment .global_app_tips a { color: #FF7300; }

.fixed_btn_submit .pay_del { display: inline-block; vertical-align: top; }

.provider_logo { height: 44px; }

#payment[data-method="Paypal"] .btn_preview { display: inline-block; vertical-align: top; margin-left: 10px; font-size: 12px; color: var(--primaryColor); }
#payment[data-method="Paypal"] .btn_how_get { display: inline-block; vertical-align: top; margin-left: 10px; font-size: 12px; color: var(--primaryColor); line-height: 30px; }
#payment[data-method="Paypal"] .box_preview .content { display: flex; flex-wrap: wrap; justify-content: center; align-items: center; }
#payment[data-method="Paypal"] .box_preview .close { top: 22px; right: 27px; }
#payment[data-method="Paypal"] .box_preview .box_middle { padding-bottom: 50px; }

#payment[data-method="Paypal"] .btn_preview_tab { display: inline-block; vertical-align: top; margin-left: 10px; font-size: 12px; color: var(--primaryColor); }
#payment[data-method="Paypal"] .box_preview_tab .title { margin-bottom: 10px; }
#payment[data-method="Paypal"] .box_preview_tab .content { display: flex; flex-wrap: wrap; justify-content: center; align-items: center; }
#payment[data-method="Paypal"] .box_preview_tab .content .tab_head { display: flex; align-items: center; justify-content: center; border-bottom: 1px #E3E3E3 solid; width: 100%; font-size: 16px; line-height: 36px; }
#payment[data-method="Paypal"] .box_preview_tab .content .tab_head>li { margin: 0 10px; cursor: default; }
#payment[data-method="Paypal"] .box_preview_tab .content .tab_head>li:hover,
#payment[data-method="Paypal"] .box_preview_tab .content .tab_head>li.current { border-bottom: 1px var(--primaryColor) solid; margin-bottom: -1px; color: var(--primaryColor); }
#payment[data-method="Paypal"] .box_preview_tab .content .tab_body>li { display: none; }
#payment[data-method="Paypal"] .box_preview_tab .close { top: 22px; right: 27px; }
#payment[data-method="Paypal"] .box_preview_tab .box_middle { padding-bottom: 50px; }

#payment[data-method="Paypal"] .btn_preview_later_tab { display: inline-block; vertical-align: top; margin-left: 10px; font-size: 12px; color: var(--primaryColor); }
#payment[data-method="Paypal"] .box_preview_later_tab .title { margin-bottom: 10px; }
#payment[data-method="Paypal"] .box_preview_later_tab .content { display: flex; flex-wrap: wrap; justify-content: center; align-items: center; }
#payment[data-method="Paypal"] .box_preview_later_tab .content .tab_head { display: flex; align-items: center; justify-content: center; border-bottom: 1px #E3E3E3 solid; width: 100%; font-size: 16px; line-height: 36px; }
#payment[data-method="Paypal"] .box_preview_later_tab .content .tab_head>li { margin: 0 10px; cursor: default; }
#payment[data-method="Paypal"] .box_preview_later_tab .content .tab_head>li:hover,
#payment[data-method="Paypal"] .box_preview_later_tab .content .tab_head>li.current { border-bottom: 1px var(--primaryColor) solid; margin-bottom: -1px; color: var(--primaryColor); }
#payment[data-method="Paypal"] .box_preview_later_tab .content .tab_body>li { display: none; }
#payment[data-method="Paypal"] .box_preview_later_tab .close { top: 22px; right: 27px; }
#payment[data-method="Paypal"] .box_preview_later_tab .box_middle { padding-bottom: 50px; }

.w_1200 #payment[data-method="Paypal"] .left_container { margin-left: -450px; }
.w_1200 #payment[data-method="Paypal"] .left_container .left_container_side { margin-left: 450px; }
.w_1200 #payment[data-method="Paypal"] .right_container { width: 440px; }
.w_1200 #payment[data-method="Paypal"] .global_container .big_title { display: flex; align-items: center; justify-content: space-between; margin-bottom: 0; }
.w_1200 #payment[data-method="Paypal"] .global_container[data-type="excheckout"] .big_title,
.w_1200 #payment[data-method="Paypal"] .global_container[data-type="later"] .big_title { justify-content: left; }

.w_1200 #payment[data-system-type="system"] .global_container .big_title { display: flex; align-items: center; justify-content: space-between; margin-bottom: 0; }
.w_1200 #payment[data-system-type="system"] .left_container { margin-left: -450px; }
.w_1200 #payment[data-system-type="system"] .left_container .left_container_side { margin-left: 450px; }
.w_1200 #payment[data-system-type="system"] .right_container { width: 440px; }
.w_1200 #payment[data-system-type="system"] .right_container .big_title { margin-bottom: 10px; }
.w_1200 #payment[data-system-type="system"] .right_container .text { padding-bottom: 10px; line-height: 26px; }

#payment_edit_form .rows>label{position:relative;}
#payment_edit_form .payment_logo { max-width: 150px; }

.global_form .rows .required {font-size: 12px; color: var(--GlobalAssistColor);}
.global_form .float_rows {clear: none; width: 48%;}
.global_form .float_rows .unit_input {display: block;}
.global_form .float_rows .box_input {width: calc(100% - 22px);}
.global_form .long_rows .box_input {width: calc(100% - 22px);}
.global_form .submit_tips {margin-top: 10px; color: #e41a23;}
.global_form .submit_tips::before {content: ""; display: inline-block; vertical-align: top; width: 18px; height: 18px; margin-top: 1px; margin-right: 6px; background-image: url(../images/frame/icon_red_tips.png); background-repeat: no-repeat;}


.payment_line{ height:1px; margin-bottom:30px; background:#f1f1f1;}
.payment_label_fr{position:absolute; left:368px;}
.payment_sort{ float:right; margin-top:3px;}
.payment_theader{ padding:0 30px; background:#f9fbfa; border-top:1px solid #edf0f5; border-bottom:1px solid #edf0f5;}
.payment_theader .payment_td{ width:14%; height:47px; line-height:47px; margin-right:2%; float:left; font-size:14px; color:#555; font-weight:bold;}
.payment_theader .payment_td.payment_card{ width:17%;}
.payment_theader .payment_td:nth-child(7){ margin:0; text-align:right;}
.payment_theader .payment_td_half{float: left;width: 7%;margin-right: 1%;}

#filter_choice_list { display: inline-block; vertical-align: top; position: relative; }
#filter_choice_list > button { display: block; border: 0; min-height: 44px; padding: 0 5px; font-size:  12px; color: var(--primaryColor); background-color: transparent; line-height: 44px; }
#filter_choice_list > button > i { display: inline-block; vertical-align: top; width: 8px; height: 5px; margin-top: 19px; margin-left: 4px; background: url(../images/products/icon_menu_arrow.png) no-repeat; transition: all .2s; -webkit-transition: all .2s; }
#filter_choice_list > button:hover > i { transform: rotate(180deg); -webkit-transform: rotate(180deg); }
#filter_choice_list .choice_box { display: none; position: absolute; left: -10px; z-index: 1; opacity: 0; width: max-content; background: transparent; }
#filter_choice_list .choice_box .drop_down { border-radius: 5px; overflow-y: auto; max-height: 390px; padding: 7px 0; background-color: #fff; }
#filter_choice_list .choice_box .drop_down .item { display: block; height: 26px; padding: 0 18px; text-decoration: none; font-size: 12px; color: #666; cursor: pointer; line-height: 26px; }
#filter_choice_list .choice_box .drop_down .item.current > span { color: var(--primaryColor); }
#filter_choice_list .choice_box .drop_down::-webkit-scrollbar { border-radius: 5px; width: 5px; background: #fff; }
#filter_choice_list .choice_box .drop_down::-webkit-scrollbar-thumb { border-radius: 5px; background: rgba(0, 0, 0, 0.1); }
#filter_choice_list .choice_box .drop_down::-webkit-scrollbar-thumb:hover { background: rgba(0, 0, 0, 0.3); }

.box_partner{text-align:center;}
.btn_paypal{height:46px; line-height:46px; background-color:var(--primaryColor); padding:0 50px; color:#fff; transition:all .15s ease-out;}
.btn_paypal_sign{display:none; margin-right: 15px;}
.btn_paypal_activate{margin-right: 15px;}
.btn_paypal_delete{display:block;}
.paypal_loading{display:inline-block; width:46px; height:46px; background:url(../images/global/loading_oth.gif) #fff center no-repeat;}

.box_paypal{text-align:center;}
.box_paypal_tab_menu{display:inline-block; height:28px; overflow:hidden; border-radius:5px; margin:10px auto 20px; font-size:0;}
.box_paypal_tab_menu>span{display:inline-block; height:28px; line-height:28px; background:#f1f1f1; padding:0 11px; font-size:12px; cursor:pointer; color:#555;}
.box_paypal_tab_menu>span.checked{background:var(--primaryColor); color:#fff;}
.box_paypal_tab>div{display:none;}
.box_paypal_tab>div.show{display:block;}
.box_paypal_tab .merchant_paypal{padding:0 0 20px;}
.box_paypal_tab .merchant_paypal .use_button_box{text-align:left;}
.box_paypal_tab .personal_paypal{text-align:left;}

.payment_version_tips{width:100%; height:100%; position:absolute; top:33%; left:0; z-index:9001; text-align:center;}
.payment_version_tips>p{line-height:52px; font-size:26px; color:#000;}
.payment_version_tips .btn_global{height:46px; line-height:46px; margin-top:20px; padding:0 60px; font-size:18px;}

#fixed_right .paypal_try_box .box_email>label {font-size: 16px;}
#fixed_right .paypal_try_box .instruction {padding-top: 30px;}
#fixed_right .paypal_try_box .instruction>div {padding: 20px 0;}
#fixed_right .paypal_try_box .instruction .box_title {line-height: 36px;}
#fixed_right .paypal_try_box .instruction .box_content {line-height: 20px;}
#fixed_right .paypal_try_box .instruction .box_list {padding-top: 15px;}
#fixed_right .paypal_try_box .instruction .box_list li {position: relative; margin-left: 8px; padding-bottom: 40px; padding-left: 30px; border-left: 1px solid #333;}
#fixed_right .paypal_try_box .instruction .box_list li::before {content: ""; display: block; position: absolute; top: 0; left: -9px; width: 16px; height: 16px; line-height: 16px; text-align: center; font-size: 14px; color: #333; background-color: #fff; border: 1px solid #333; border-radius: 50%;}
#fixed_right .paypal_try_box .instruction .box_list li:nth-child(1)::before {content: "1";}
#fixed_right .paypal_try_box .instruction .box_list li:nth-child(2)::before {content: "2";}
#fixed_right .paypal_try_box .instruction .box_list li:nth-child(3)::before {content: "3";}
#fixed_right .paypal_try_box .instruction .box_list li:nth-child(4)::before {content: "4";}
#fixed_right .paypal_try_box .instruction .box_list li:last-child {border: 0;}

.fixed_search_filter .filter_country .filter_title::after,
.fixed_search_filter .filter_country.current .filter_title::after { transform: rotate(270deg); }
.fixed_search_filter .filter_country .filter_option { display: block; }
.fixed_search_filter .filter_country .filter_clean { display: none; }
.fixed_search_filter .filter_country .filter_clean.show { display: block; }

.filter_country_area_box .search_form { margin-top: 0; margin-bottom: 18px; float: none; width: auto; padding-right: 70px; }
.filter_country_area_box .search_menu .k_input .more{display: none;}
.filter_country_area_box .search_menu .search_form{width: 100%;}
.filter_country_area_box .search_menu .search_form form{display: flex;}
.filter_country_area_box .search_menu .k_input{flex: 1;display: flex;position: relative;}
.filter_country_area_box .search_menu .k_input .more{display: none;}
.filter_country_area_box .search_menu .k_input .form_input{flex: 1;}
.filter_country_area_box .search_menu .k_input .search_btn {position: absolute; top: 0; right: 0; width: 30px; height:28px; line-height:28px; margin:2px;padding:0; overflow:hidden; text-align: center; font-size: 14px; color: #fff; font-family: "iconfont" !important; font-style: normal; -webkit-font-smoothing: antialiased; -moz-osx-font-smoothing: grayscale; cursor:pointer; background: var(--GlobalMainColor); border:0;border-radius: 5px;}
.filter_country_area_box .search_menu .new_filter_btn {display: none;}
.filter_country_area_box .select_all_box{display: inline-block;margin: 5.5px 20px 0 0;}
.filter_country_area_box .select_all_box .input_checkbox_box .input_checkbox{width: 15px;height: 15px;}
.filter_country_area_box .fixed_right_products_choice_jsppane{height: calc(100vh - 204px);overflow-y: auto;outline: none;}
.filter_country_area_box .fixed_right_products_choice_jsppane.loading{background-image: url(../images/frame/loading_oth.gif);background-position: center;background-repeat: no-repeat;}
.filter_country_area_box .fixed_right_products_choice_jsppane::-webkit-scrollbar{width: 5px; background: #fff;border-radius: 5px;}
.filter_country_area_box .fixed_right_products_choice_jsppane::-webkit-scrollbar-thumb{background: rgba(0, 0, 0, 0.1);border-radius: 5px;}
.filter_country_area_box .fixed_right_products_choice_jsppane::-webkit-scrollbar-thumb:hover{background: rgba(0, 0, 0, 0.3);}
.filter_country_area_box .search_box_selected{margin-top: 0;margin-bottom: 8px;}
.filter_country_area_box .search_box_selected .btn_item_choice>b{max-width: 270px;}

.provider_account_box .input_radio_side_box { width: 100%; }
.provider_account_box .input_radio_side_box:last-child { margin-bottom: 0; }
.provider_account_box .account_info { border-radius: 5px; padding: 20px; font-size: 14px; color: #1f2328; background-color: #f4f5f6; line-height: 36px; }
.provider_account_box .account_info .item { margin-top: 10px; }
.provider_account_box .account_info .item .box_input { background-color: #fff; }
.provider_account_box .account_info .item:first-child { margin-top: 0; }
.provider_account_box .unified_tab.hide,
.provider_account_box .indep_tab.hide { display: none; }

.box_paypal_onboarding .title .btn_choose { margin-left: 15px; font-size: 14px; color: var(--primaryColor); }
.box_paypal_onboarding .onboarding_box { display: flex; justify-content: space-between; }
.box_paypal_onboarding .input_radio_side_box { width: 300px; }

/*************************** 支付设置 end ***************************/


/*************************** 汇率设置 start ***************************/
#exchange .rows .tips{line-height: 32px;}
/*************************** 汇率设置 end ***************************/


/*************************** 第三方代码 start ***************************/
#third .gary{font-size: 12px;color: #888;}
/*************************** 第三方代码 end ***************************/


/*************************** 管理员 start ***************************/
/* 右侧弹窗 Start */
#fixed_right .fixed_permit .PermitHead{border-bottom:1px solid #e8e8e8; height:28px; font-weight:bold; color:#555; text-indent:5px;}
#fixed_right .fixed_permit .module{text-indent:5px; color:#888; font-size:12px; border-bottom:1px solid #e8e8e8; margin-right:5px; line-height:32px;}
#fixed_right .fixed_permit .module:hover{background:#e7f7f3;}
#fixed_right .fixed_permit dl dt, #fixed_right .fixed_permit dl dd{float:left; line-height:normal; padding:6px 0;}
#fixed_right .fixed_permit dl dt{margin-left:30px;}
#fixed_right .fixed_permit dl dd{width:73%; margin-left:10px;}
#fixed_right .fixed_permit dl dd>input{margin-left:10px;}
#fixed_right .fixed_permit input{vertical-align: middle;}
/* 右侧弹窗 End */
/*************************** 管理员 end ***************************/


/*************************** 系统日志 start ***************************/
#logs{padding:0;}
/*************************** 系统日志 end ***************************/


/*************************** 协议 start ***************************/
#agreement .global_container{margin-top:24px;padding: 15px 24px 2px 24px;position:relative;}
#agreement .global_container:first-child{margin-top: 15px;}
#agreement .big_title{margin-bottom: 4px;}
#agreement .box_use_agreement{position:absolute; top:26px; right:20px;}
#agreement .box_explain:not(.normall_margin){margin-bottom: 25px;}
.fixed_btn_submit .center_container_1000{margin-top:0; margin-bottom:0;}
.agreement.center_container{margin-bottom: 0;}
/*************************** 协议 end ***************************/


.global_container .config_top_title{margin:0px 0 10px;padding:10px 0;display: block;}
.global_container .config_top_title .return{font-size:20px; color:#000;}
.global_container .config_top_title .subreturn{font-size: 12px;color: #7e8da0;margin-top: 10px;}
.global_container .config_top_title .config_sub_title{padding: 5px 0;}

/*附加信息收集*/
.address_additional .config_top_title{display: flex;justify-content: space-between;align-items: center;}
.address_additional .btn_add_additional{line-height: 32px;font-size: 14px;color: #fff;background-color: #4BA0FC;border-radius: 4px;padding: 0 25px;}
.address_additional .r_con_table tr td:first-child{word-break: break-word;}

/*基础设置 start */
.basis_edit_form .global_container .rows{ clear: unset;}
.basis_edit_form .global_container .half_rows{width: 48.5%;}
.basis_edit_form .support_box .config_top_title{margin:0;}
.basis_edit_form .support_box .support_checkbox{margin-top:13px;}
.basis_edit_form .store {padding-bottom: 1px;}
.basis_edit_form .normal, .basis_edit_form .contact {padding-bottom: 0;}
.basis_edit_form .review {padding-bottom: 10px;}
.basis_edit_form .global_app_tips a { color: #FF7300; }
/*基础设置 end */


/* 其他设置 */
.o_return_title{margin:0;padding:0 25px;position: relative;}
.o_return_title::before{content: "\e779";font-family: 'iconfont';position: absolute;top: 0;bottom: 0;left: 0;margin: auto;}


/* 店铺语言 Start */
#language .global_container.no_padding{padding: 0;}
#language .global_container.no_padding .language_padding{padding: 20px;padding-bottom: 0;}
#language .global_container.no_padding .language_box{ padding: 0 20px; }
#language .global_container .config_top_title{position: relative;}
#language .global_container .config_top_title .tips{margin-top: 8px;color: #aaa;font-size: 12px;}
#language .global_container .inside_table{padding: 0;}
#language .global_container .rows.box_background{background-color: #f7f9fb;padding: 30px 20px;margin-bottom: 15px;}
#language .global_container .rows.box_background label{max-width: 364px;position: relative;}
#language .global_container .rows.box_background .box_select{height: 42px;background-color: #fff;}
#language .global_container .rows.box_background .box_select select{height: 42px;}
#language .global_container .box_background .lang_jump_btn{position: absolute;top: 0;right: 0;}
#language .bg_no_table_data .content>p{font-size: 16px;}
#language .bg_no_table_data .content .btn_add_item{height: 32px;line-height: 30px;font-size: 14px;}
#language .list_menu_button{position: absolute;right: 0;top: 0;}
#language .global_form .rows .input .title{font-size: 14px;color: #555;}
#language .r_con_table td .status{line-height: 22px;font-size: 12px;}
#language .r_con_table td .status0{background-color: #f3f3f3;color: #888;}
#language .r_con_table td .status1{background-color: #eaf8ec;color: #25a736;}
#language .r_con_table td .status_tips{display: inline-block;font-size: 12px;color: #aaa;}
#language .r_con_table td .flag_icon{ margin-right: 3px; width: 32px; height: 20px; display: inline-block; vertical-align: middle; position: relative; }
#language .r_con_table td .flag_icon .icon_flag_big{ position: absolute; top: 0; left: 0; transform: scale(.5) translate(-50%, -50%); }
#language .switch_box .switch_item{ box-sizing: border-box; padding: 30px 20px; width: 100%; background-color: #f7f9fb;margin-bottom: 15px; }
#language .switch_box .switch_item .content_item_title{ line-height: 18px; font-size: 14px; }
#language .switch_box .switch_item .content_item_tips{ margin-top: 10px; color: #7d8d9e; font-size: 12px; }
#language .switch_box .switch_item .btn_checkbox{ margin-top: 2px; }
#language .switch_box .switch_item .content{ margin-left: 10px; }
.fixed_language .type_box .type_select{margin-top: 12px;padding: 12px 20px;border: 1px solid #ddd;text-align: center;cursor: pointer;border-radius: 5px;}
.fixed_language .type_box .type_select:first-child{margin-top: 0;}
.fixed_language .type_box .type_select.current{border-color: var(--primaryColor);}
.fixed_language .type_box .type_select .title{font-size: 14px;color: #555;}
.fixed_language .type_box .type_select .brief{font-size: 12px;color: #aaa;}
.fixed_language .type_box .type_select input{display: none;}
.fixed_language_view .input_radio_side_box,
.fixed_language .input_radio_side_box{ margin-top: 0; margin-bottom: 12px; width: 100%; box-sizing: border-box; }
.fixed_language .global_app_tips{ padding: 12px 10px; width: 100%; border: 1px solid #ffdec6; border-radius: 5px; background-color: #fff9f5; box-sizing: border-box; }
.fixed_language .global_app_tips span{ font-size: 12px; color: #1f2328; }
.fixed_language .global_app_tips a{ margin-left: 5px; font-size: 12px; color: #f68f44; text-decoration: underline; }
.fixed_language .app_box .app_select{margin-top: 12px;padding: 20px 20px 20px 55px;border: 1px solid #ddd;cursor: pointer;border-radius: 5px;}
.fixed_language .app_box .app_select:first-child{margin-top: 0;}
.fixed_language .app_box .app_select.current{border-color: var(--primaryColor);background-color: #f5fdff;}
.fixed_language .app_box .app_select.current:before,
.fixed_language .app_box .app_select.current:after{ top: 50%; transform: translateY(-50%); }
.fixed_language .app_box .app_select .image{width: 50px;height: 50px;background-size: contain;}
.fixed_language .app_box .app_select .title{width: 155px;margin-top: 10px;font-size: 14px;color: #555;}
.fixed_language .app_box .app_select input{display: none;}
.fixed_language .app_box{ margin-bottom: 0; }
.fixed_language .app_box:visible { margin-bottom: 28px; }
.fixed_language .rows .tips{ font-size: 12px; color: #7f7f7f; }
.fixed_language .global_tips span a{display: inline-block;height: 24px;line-height: 24px;margin: 0;padding: 0 18px;background-color: #fff;color: #555;text-decoration: none;border-radius: 5px;font-size: 12px;}

#config .fixed_language .icon_language{ margin-left: -16px; position: relative; vertical-align: top; }
#config .fixed_language .icon_language .icon_flag_big{ transform: scale(.5); }
#config .fixed_language .rows .unit_input{ width: 100%; }
#config .fixed_language .rows .unit_input>b{ width: 50px; height: 34px; }
#config .fixed_language .rows .unit_input>b .icon_flag_big{ margin-left: -16px; transform: scale(.5) translateY(-3px); }
#config .fixed_language .rows .unit_input .box_drop_double{ width: calc( 100% - 50px ); display: inline-block; vertical-align: top; }
#config .fixed_language .rows .unit_input .drop_down{ width: calc( 100% + 50px ); transform: translateX(-50px); }
#config .fixed_language .rows .unit_input .box_input{ border-top-left-radius: 0; border-bottom-left-radius: 0; }
#config .fixed_language .input_radio_side_box:last-child{ margin-bottom: 0; }
/* 店铺语言 End */
/* 语言包修改 satart */
.langpack{position: relative;}
.langpack .global_container{padding: 24px 20px;}
.langpack .inside_container{padding-top: 0;}
.langpack .inside_container .inside_menu{margin: 0 0 0 0 ;}
.langpack .langpack_content{padding-top: 20px;}
.langpack .global_container .langpack_content_box{border-top: solid 1px #edf0f5;}

/* 语言包修改 end*/

/* 货币 Start */
.currency {position: relative;}
.currency .btn_add_currency {position: absolute; top: 20px; right: 20px; min-width: 72px; height: 34px; line-height: 32px; font-size: 14px; color: #fff; background-color: #4BA0FC; border-radius: 4px;}
.currency .currency_container {position: relative;}
.currency .currency_container .scroll_sticky {position: absolute; top: 0; right: 0; bottom: -12px; border: 0;}
.currency .currency_container .scroll_sticky_content {width: auto; height: 100%;}
.currency .currency_list {max-height: 413px; overflow-y: scroll; margin: 10px 0; border-top: 1px #edf0f5 solid; border-bottom: 1px #edf0f5 solid;}
.currency .currency_list .r_con_table {margin: 0; border: 0;}
.currency .currency_list .r_con_table tbody tr:last-child td {border-bottom: 0;}
.currency .currency_list .r_con_table.fixed thead {position: sticky; position: -webkit-sticky; top: 0; z-index: 1; box-shadow: 0.1rem 0 0 0 #edf0f5;}
.currency .currency_list::-webkit-scrollbar {width: 5px; background: #fff;border-radius: 5px;}
.currency .currency_list::-webkit-scrollbar-thumb {background: rgba(0, 0, 0, 0.1);border-radius: 5px;}
.currency .currency_list::-webkit-scrollbar-thumb:hover {background: rgba(0, 0, 0, 0.3);}
.currency .box_currency_select .rows {margin: 28px 0 0;}
.currency .box_currency_select .rows>label {height: 36px; line-height: 36px;}
.currency .box_currency_select .rows:first-child .box_select {width: 180px;}
.currency .box_currency_select .select_tips {line-height: 18px; margin: 10px 0; color: var(--GlobalTipColor);}
.currency .box_currency_select .select_tips::before {content: ""; display: inline-block; vertical-align: top; width: 18px; height: 18px; margin-right: 6px; background: url(../images/frame/app_tips_icon.png) no-repeat center;}

.fixed_add_currency .box_currency_hide .box_input:read-only {color: var(--GlobalTextColor); cursor: no-drop; background-color: #f8f9fb;}
.fixed_add_currency .box_currency_method .input_radio_box{ margin-top: 10px; margin-bottom: 0; width: 100%; }
.fixed_add_currency .box_currency_method .input_radio_box:first-child{ margin-top: 0; }
.fixed_add_currency .box_currency_method .tips{ margin-top: 10px; padding: 0; color: #7d8d9e; font-size: 12px; line-height: 12px; }
.fixed_add_currency .box_currency_method .box_proportion { border-right: 3px; overflow: hidden; margin-top: 10px; padding: 15px; color: var(--GlobalTextColor); background-color: #fafafa; }
.fixed_add_currency .box_currency_method .box_proportion .btn_checkbox { display: inline-block; vertical-align: top; margin-top: 8px; }
.fixed_add_currency .box_currency_method .box_proportion .unit_input { margin: 0 6px; }
.fixed_add_currency .box_currency_method .box_proportion .unit_input .box_input { border-top-right-radius: 0; border-bottom-right-radius: 0; }
.fixed_add_currency .box_currency_method .box_proportion .unit_input .box_input[readonly] { cursor: no-drop; background-color: #f4f5f7; }
.fixed_add_currency .box_currency_method .box_proportion .unit_input,
.fixed_add_currency .box_currency_method .box_proportion .unit_input .box_input { height: 30px; line-height: 30px; }
.fixed_add_currency .box_currency_method .box_proportion .unit_input>b.last { line-height: 30px; }
.fixed_add_currency .box_currency_method .box_proportion .error_tips { margin-top: 10px; }
.fixed_add_currency .box_rate .usd_text {height: 36px; line-height: 36px; color: var(--GlobalTextColor);}
.fixed_add_currency .box_rate .box_exchange_rate {display: inline-block; vertical-align: top;}
.fixed_add_currency .box_payment_method {padding: 16px; background-color: #f7f9fb;}
.fixed_add_currency .box_payment_method ul {max-height: 300px; overflow-y: auto; margin-top: 3px;}
.fixed_add_currency .box_payment_method ul::-webkit-scrollbar {width: 5px; background: #f7f9fb; border-radius: 5px;}
.fixed_add_currency .box_payment_method ul::-webkit-scrollbar-thumb {background: rgba(0, 0, 0, 0.1);border-radius: 5px;}
.fixed_add_currency .box_payment_method ul::-webkit-scrollbar-thumb:hover {background: rgba(0, 0, 0, 0.3);}
.fixed_add_currency .box_payment_method li {line-height: 30px; font-size: 12px;}
.fixed_add_currency .box_payment_method li::before {content: "\e616"; display: inline-block; vertical-align: top; width: 19px; height: 19px; line-height: 20px; margin-top: 5px; margin-right: 7px; text-align: center; font-family: "iconfont" !important; font-style: normal; font-size: 16px; color: #fff; -webkit-font-smoothing: antialiased; -moz-osx-font-smoothing: grayscale; background-color: var(--GlobalMainColor); border-radius: 50px;}
.fixed_add_currency .box_rate, .fixed_add_currency .box_payment_method {display: none;}



#button_float_tips {z-index: 2;}

/* 货币 End */

/* 订单自动化程序 Start */
.order_automation {position: relative;}
.order_automation .config_top_title {margin: 0;}
.order_automation .config_top_title .box_explain {margin-bottom: 2px;}
.order_automation .btn_set_order_auto {position: absolute; top: 34px; right: 20px; min-width: 72px; height: 34px; line-height: 32px; font-size: 14px; color: #fff; background-color: #4BA0FC; border-radius: 4px;}
.order_automation .rows {display: none; margin: 0; margin-top: 20px;}
.order_automation .rows.show {display: block;}

.fixed_order_auto .input_checkbox_side_box {width: 100%; margin-top: 0; margin-bottom: 12px; box-sizing: border-box;}

.order_automation .unit_input .box_select_down .head::after{ top: 13px; }

/* 订单自动化程序 End */


/*  新规放UI css Start  */
.demo_component{width:calc(100% - 210px);position:fixed;top:86px;bottom:20px;overflow:scroll;height:calc(100% - 106px);overflow-x:hidden;background-color: #fff;margin-right: 20px;}
.demo_component::-webkit-scrollbar{width: 6px;background-color: #fff;border-radius: 5px;}
.demo_component::-webkit-scrollbar-thumb{background: rgba(144,147,153,.3);border-radius: 5px; }
.demo_component::-webkit-scrollbar-thumb:hover{background: rgba(144,147,153,.3);}
.demo_wrap_1800{width: auto;margin: 0 auto;}
.demo_component_nav{position:fixed;top:86px;bottom:20px;height: calc(100% - 106px);overflow-x: hidden;transition:padding-top .3s;width:250px;box-sizing:border-box;overflow:hidden;}
.demo_side_nav{height:100%;overflow-x:auto;overflow-y:scroll;}
.demo_side_nav::-webkit-scrollbar{width: 6px;background-color: #fff;border-radius: 5px;width: 0;}
.demo_side_nav::-webkit-scrollbar-thumb{background: rgba(144,147,153,.3);border-radius: 5px; }
.demo_side_nav::-webkit-scrollbar-thumb:hover{background: rgba(144,147,153,.3);}
.demo_side_nav:hover::-webkit-scrollbar{width: 6px;}
.demo_side_nav>ul{padding-top: 50px;padding-bottom: 50px;}
.demo_side_nav ul>li.demo_nav_item{padding: 0 20px;box-sizing: border-box;}
.demo_side_nav ul>li.demo_nav_item .demo_nav_group .demo_nav_item{padding: 0 0;}
.demo_nav_item>a{margin-top:15px;font-size:16px;color:#333;line-height:40px;height:40px;margin:0;padding:0;text-decoration:none;display:block;position:relative;transition:.15s ease-out;font-weight:700;}
.demo_nav_item .demo_nav_group .demo_nav_group_title{font-size:12px;color:#999;line-height:26px;margin-top:15px;}
.demo_nav_item .demo_pure_menu_list .demo_nav_item a{display:block;height:40px;color:#444;line-height:40px;font-size:14px;overflow:hidden;white-space:nowrap;text-overflow:ellipsis;font-weight:400;}
.demo_nav_item .demo_pure_menu_list .demo_nav_item a:hover{color:var(--GlobalLinkHoverColor);}
.demo_nav_item .demo_pure_menu_list .demo_nav_item a.active{color:var(--GlobalLinkHoverColor);}
.demo_component_content{padding-left:270px;height:100%;}
.demo_component_content .content{padding-top: 50px;padding: 71px 0;}
.content_title{font-size:20px;font-weight:600;margin-top:63px;padding-bottom: 5px;}
.mian_color_title{margin-top:0;}
.content_subtitle{margin-top:0px;margin-bottom:15px;color:#000;}
.demo_color_box{display:inline-flex;justify-content:center;flex-direction:column;flex-wrap:wrap;width:270px;height:88px;color:#ffffff;border-radius:4px;font-size:16px;line-height:30px;box-sizing:border-box;padding:20px;margin:5px 0;}
.demo_color_box .value{width:100%;font-size:14px;line-height:1;}
.demo_row{margin-left:-10px;margin-right:-10px;display:flex; align-items: center;margin-top: 15px;}
.demo_col{display:inline-flex;padding-left:10px;padding-right:10px;}
.demo_col_desc{flex:1;font-size:14px;color:#404852;}
.demo_flex_wrap{flex-wrap:wrap;}
.demo_col_title{font-size:22px;line-height:46px;}
.demo_table{width:auto;border-collapse:collapse;border-spacing:0;color:#000;margin:5px 0;}
.demo_table thead tr th,.demo_table tbody tr td{text-align:left;}
.demo_table thead tr th{padding:6px 0;color:#555555;font-size:14px;line-height:24px;font-weight:unset;background-color:#eeeeee;border-left-color:#fff;border-left-width:2px;border-left-style:solid;}
.demo_table thead tr th:first-child{border-left-width:0;}
.demo_table thead tr .cell,.demo_table tbody tr .cell{padding:0 20px;}
.demo_table tbody tr{border-bottom-width:1px;border-bottom-style:solid;border-bottom-color:#eee;}
.demo_table tbody tr td{padding:20px 0;}
.demo_table tbody tr td .line div{border-top-width:1px;border-top-style:solid;height:0;border-top-color:#404852;}
.demo_table tbody tr td .line div.dashed{border-top-style:dashed;}
.demo_btm{width:170px;height:50px;border:solid 1px #000;}
.demo_title_color{color:#1f2328;}
.demo_content_color{color:#404852;}
.demo_assist_color{color:var(--GlobalAssistColor);}
/*  新规放UI css End  */

/*  系统日志 css Start  */
#config .logs.index{margin: auto;max-width: 1280px;}
#config .logs.index .bread_nav{ margin-top: 10px; }
#config .logs.index .desc{color: var(--GlobalAssistColor);}
#config .logs.index .global_container{padding: 20px 0;}
#config .logs.index .list_menu{padding: 0 20px;}
.fixed_view_detail .view_list{margin-top: 10px;padding: 18px 24px;border-radius: 3px;background-color: #f8f9fb;color: var(--GlobalTextColor);line-height: 20px;}
.fixed_view_detail .view_list .tit{margin-bottom: 10px;font-weight: bold;}
.fixed_view_detail .view_list .sub{margin-bottom: 10px;font-size: 12px;line-height: 22px;}
.fixed_view_detail .view_list .sub span{display: inline-block;line-height: 22px;padding: 0 11px;border-radius: 22px;background-color: #EDF5FF;color: var(--GlobalMainColor);margin-left: 8px;}
.fixed_view_detail .view_list .sub .sdel{background-color: #f3f3f3;color: #888;}
.fixed_view_detail .view_list .sub .sdis{background-color: #fbeded;color: #f35958;}
.fixed_view_detail .view_list .tsub{color: var(--GlobalTitleColor);}
.fixed_view_detail .view_list .desc{color: var(--GlobalAssistColor);font-size: 12px;margin-bottom: 10px;}
.fixed_view_detail .view_list .con{font-size: 12px;}
.fixed_view_detail .btn_cancel{background-color: var(--GlobalMainColor);color: #fff;}
/*  系统日志 css End  */

#public_library{ background-color: #fff; position: fixed; width: 100%; height: 100%; top: 0; left: 0; }
#public_library .menu{ padding-top: 30px; padding-left: 4.2%; padding-right: 2.1%; width: 20.57%; height: 100%; background-color: #f6f6f7; border-right: 1px solid #eaeaea; box-sizing: border-box; }
#public_library .menu .pic_box{ text-align: left; }
#public_library .menu .pic_box img{ max-width: 100%; }
#public_library .menu .item_list{ margin-top: 45px; }
#public_library .menu .item_list .item{ margin-top: 30px; }
#public_library .menu .item_list .item .top_title.current{ font-weight: bold; }
#public_library .menu .item_list .item:first-child{ margin-top: 0; }
#public_library .menu .item_list .item .top_title{ font-size: 18px; color: #222222; position: relative; cursor: pointer; transition: .4s; }
#public_library .menu .item_list .item *{ transition: .4s; }
#public_library .menu .item_list .item .sub .sec_item.hasThird a{ display: block; font-size: 14px;position: relative; }
#public_library .menu .item_list .item .sub .sec_item.hasThird a.current{ color: var(--primaryColor); }
#public_library .menu .item_list .item .sub .sec_item.hasThird a:before{ content:""; width: 4px; height: 14px; background-color:  var(--primaryColor);position: absolute; top: 50%; left: -12px; transform: translateY(-50%); opacity: 0; transition: .4s; }
#public_library .menu .item_list .item .sub .sec_item.hasThird a.current:before{ opacity: 1; }
#public_library .menu .item_list .item .sub .sec_item:not(.hasThird) a.current{ display: block; padding-left: 12px; position: relative; color: #4BA0FC; }
#public_library .menu .item_list .item .sub .sec_item:not(.hasThird) a.current:before{ content: ''; width: 3px; height: 14px; background-color: #4BA0FC; position: absolute; top: 50%; transform: translateY(-50%); left: 0; }
#public_library .menu .item_list .item .top_title em{ content:'';display: inline-block; width:0; height:0; border-width:6px 6px 0 6px; border-style:solid; border-color: #aaaaaa transparent transparent transparent ; position:absolute; top: 50%; transform: translateY(-50%); right: 0; }
#public_library .menu .item_list .item .top_title.current em{ transform: translateY(-50%) rotate(180deg); border-color: #4BA0FC transparent transparent transparent ; }
#public_library .menu .item_list .item.active .sub{ margin-top: 15px; }
#public_library .menu .item_list .item .sub{ *margin-top: 15px; height: 0; overflow: hidden; }
#public_library .menu .item_list .item .sub .sec_item{ line-height: 40px; }
#public_library .menu .item_list .item .sub .sec_item .sec_tit>a{font-size: 14px;}
#public_library .menu .item_list .item .third{ padding-left: 24px; overflow: hidden; height: 0; }
#public_library .menu .item_list .item .sub .sec_item.hasThird .third .third_height a{ font-size: 14px; }
#public_library .content{ padding: 5% 5.2%; width: 79.43%; height: 100%; box-sizing: border-box; overflow-y: auto; }
#public_library .content .text{ display: none; }
#public_library .content .text img{ max-width: 100%; max-height: 100%; }

#public_library .content .text h1:first-child{ font-weight: bold; }
#public_library .content .text h1{ font-size: 36px; color: #000000;}
#public_library .content .text h2{ font-size: 24px; color: #222222;}
#public_library .content .text h3{ font-size: 16px; color: #5a6b81;}
#public_library .content .text h3.black{ color: #000; }
#public_library .content .text h4{ font-size: 14px; color: #5a6b81;}
#public_library .content .text h5{ font-size: 14px; color: #404852;}
#public_library .content .text .box{ padding: 0 26px; width: 100%; line-height: 54px; border-radius: 5px; background-color: #f3faf5; box-sizing: border-box; position: relative; color: #030303; }
#public_library .content .text .box:before{ content:''; display: block; width: 5px; height: 100%; background-color: #4BA0FC; border-top-left-radius: 5px; border-bottom-left-radius: 5px; position: absolute; top: 0; left: 0; }
#public_library .content .text .box span{ color: #993366; }
#public_library .content .text ul{ padding-left: 16px; }
#public_library .content .text ul li{ margin: 15px 0; list-style: auto; line-height: 24px; font-size: 14px; color: #222222; }
#public_library .content .text .table_box table{ width: 100%; }
#public_library .content .text .table_box table thead{ line-height: 58px; background-color: #f7f9fb; border-top: 1px solid #edf0f5; border-bottom: 1px solid #edf0f5; }
#public_library .content .text .table_box table thead tr td{ padding: 0 20px 0 60px; font-size: 14px; color: #1f2328; font-weight: bold; box-sizing: border-box; }
#public_library .content .text .table_box .color_bg{ background-color: #f7f9fb; }
#public_library .content .text .table_box .font_color{ color: #4BA0FC; }
#public_library .content .text .table_box.all_line tr,
#public_library .content .text .table_box.all_line tr td{ border: 1px solid #edf0f5; }
#public_library .content .text .table_box table tbody tr{ line-height: 22px; color: #1f2328; font-size: 14px; border-bottom: 1px solid #edf0f5; }
#public_library .content .text .table_box table tbody tr:hover{ background-color: #ecf5ff; }
#public_library .content .text .table_box table tbody tr td{ padding: 18px 20px 17px 60px; font-size: 14px; color: #1f2328; box-sizing: border-box;  }
#public_library .content .text .show_table table{ width: 100%; }
#public_library .content .text .show_table table tr:first-child{ border-top: 1px solid #edf0f5; }
#public_library .content .text .show_table table tr{ border-bottom: 1px solid #edf0f5; }
#public_library .content .text .show_table table tr:hover{ background-color: #ecf5ff; }
#public_library .content .text .show_table table tr td{ padding: 28px 31px; line-height: 22px; font-size: 14px; color: #1f2328; vertical-align: top; }
#public_library .content .text .example_code{ padding: 30px; width: 100%; border: 1px solid #eaeefb; background-color: #fafafa; box-sizing: border-box; font-size: 12px; line-height: 24px; letter-spacing: 0.3px; ; }

#shipping #fixed_right .country_area_box .search_menu .k_input .more{display: none;}
#shipping #fixed_right .country_area_box .search_menu .search_form{width: 100%;}
#shipping #fixed_right .country_area_box .search_menu .search_form form{display: flex;}
#shipping #fixed_right .country_area_box .search_menu .k_input{flex: 1;display: flex;position: relative;}
#shipping #fixed_right .country_area_box .search_menu .k_input .more{display: none;}
#shipping #fixed_right .country_area_box .search_menu .k_input .form_input{flex: 1;}
#shipping #fixed_right .country_area_box .search_menu .k_input .search_btn {position: absolute; top: 0; right: 0; width: 30px; height:28px; line-height:28px; margin:2px;padding:0; overflow:hidden; text-align: center; font-size: 14px; color: #fff; font-family: "iconfont" !important; font-style: normal; -webkit-font-smoothing: antialiased; -moz-osx-font-smoothing: grayscale; cursor:pointer; background: var(--GlobalMainColor); border:0;border-radius: 5px;}
#shipping #fixed_right .country_area_box .search_menu .new_filter_btn {display: none;}
#shipping #fixed_right .country_area_box .select_all_box{display: inline-block;margin: 5.5px 20px 0 0;}
#shipping #fixed_right .country_area_box .select_all_box .input_checkbox_box .input_checkbox{width: 15px;height: 15px;}
#shipping #fixed_right .country_area_box .fixed_right_products_choice_jsppane{height: calc(100vh - 204px);overflow-y: auto;outline: none;}
#shipping #fixed_right .country_area_box .fixed_right_products_choice_jsppane.loading{background-image: url(../images/frame/loading_oth.gif);background-position: center;background-repeat: no-repeat;}
#shipping #fixed_right .country_area_box .fixed_right_products_choice_jsppane::-webkit-scrollbar{width: 5px; background: #fff;border-radius: 5px;}
#shipping #fixed_right .country_area_box .fixed_right_products_choice_jsppane::-webkit-scrollbar-thumb{background: rgba(0, 0, 0, 0.1);border-radius: 5px;}
#shipping #fixed_right .country_area_box .fixed_right_products_choice_jsppane::-webkit-scrollbar-thumb:hover{background: rgba(0, 0, 0, 0.3);}
#shipping #fixed_right .country_area_box .search_box_selected{margin-top: 0;margin-bottom: 8px;}
#shipping #fixed_right .country_area_box .search_box_selected .btn_item_choice>b{max-width: 270px;}
#shipping #fixed_right .search_form{margin-top: 0;margin-bottom:18px;float: none;width: auto;padding-right: 70px;}
#shipping #fixed_right .search_form .search_btn{float: right;margin-left: 0;}
#shipping #fixed_right .search_form .k_input{width: 100%;position: relative;}
#shipping #fixed_right .search_form .k_input .more{float: none;position: absolute;right: 0;top: 0;}
#shipping #fixed_right .search_form .k_input .form_input{float: none;width: 95%;}

#config .email .toggle_language{position: absolute;top:0;right: 0;}
#config .email .toggle_language .toggle_title{display: inline-block;font-size: 14px;line-height: 1; vertical-align: middle;}
#config .email .toggle_language .box_select{display: inline-block;max-width: 140px;line-height: 1;vertical-align: middle;}

#shipping .tips_error {margin: 8px 0 0 0;}
#shipping .tips_error .global_app_tips {display: inline-block; padding: 8px 20px 8px 12px;border-radius: 5px;color: #1f2328;border: 1px solid #ffdec6;border-radius: 5px;background-color: #fff9f5;margin-top: 5px;}
#shipping .tips_error .global_app_tips a {display: inline-block;color: var(--primaryColor);position: relative;}
#shipping .tips_error .global_app_tips span {display: inline-block;vertical-align: middle;font-size: 12px;}
#shipping .tool_icon{position: relative;margin-left: 5px;}
#shipping .tool_icon_box{display: none; position: absolute;width: auto;height: auto; left: 18px;bottom: 0;}
#shipping .tool_icon_box .tool_icon_content{background-color: #fff;padding: 16px 20px;border-radius: 5px;box-shadow: 0 0 10px rgba(0, 0, 0, 0.2);white-space: nowrap;font-size: 12px;line-height: normal;color: #333;}
#shipping .tool_icon:hover .tool_icon_box{display: block;}

/*  佣金 Start  */
#commission .head{display: grid;grid-column-gap:20px;grid-template-columns: 1fr 1fr;}
#commission .head .tit{font-size: 16px;line-height: 30px;margin-bottom: 5px;}
#commission .head .price{display: flex;height: 40px;align-items: center;}
#commission .head .price span{font-size: 22px;padding-right: 10px;}
#commission .head a{padding: 0 15px;line-height: 24px;font-size: 12px;min-width: 36px;text-align: center;border: 1px solid var(--primaryColor);border-radius: 17px;margin-left: 10px;}
#commission .head a.comm_payment{background: var(--primaryColor);color: #fff;}
#commission .head a.comm_status{color: var(--primaryColor);background-color: #EDF5FF;}
#commission .billing_details{padding: 0;}
#commission .billing_details .comm_tit{font-size: 16px;line-height: 38px;padding: 10px 20px 0;display: flex;align-items: center;}
#commission .billing_details .comm_tit span{flex: 1;}
#commission .billing_details .comm_tit .btn_request_invoice{height: 34px;line-height: 34px;font-size: 14px;color: #fff;background-color: #4BA0FC;border-radius: 4px;padding: 0 25px;}
#commission .r_con_table tr td:first-child{padding-left: 20px;}
#commission .r_con_table tr td:last-child{padding-right: 20px;}
#commission #turn_page{padding-top: 5px;padding-bottom: 15px;padding-right: 20px;}
/*  佣金 End  */
/* 图片 Start */
.photo-tiles {
	padding: 10px 20px 20px;
}
.photo-tiles-list {
	display: grid;
	grid-template-columns: repeat(auto-fill, minmax(260px, 1fr));
	gap: 10px;
	justify-items: start;
}
	.photo-tiles-list .photo-tiles-item {
		width: 260px;
		max-width: 260px;
		display: block;
		margin: 0 auto 15px;
		padding: 20px 20px;
		background: #fff;
		box-sizing: border-box;
		float: left;
		position: relative;
		border-radius: 5px;
		word-break: break-all;
		box-shadow: 0px 0px 5px 0px rgba(0, 0, 0, .1), 0px 0px 0px 0px rgba(0, 0, 0, .1), 1px 0px 6px 0px rgba(0, 0, 0, .1), 0px 5px 15px 0px rgba(0, 0, 0, .1);
	}
		.photo-tiles-list .photo-tiles-item img {
			width: 100%;
			height: auto;
			aspect-ratio: 1 / 1;
			object-fit: cover;
		}
		.photo-tiles-list .photo-tiles-item.trans {
			transition: all 0.2s ease-out;
		}
			.photo-tiles-list .photo-tiles-item.trans:hover {
				background: #eaf7ff;
				transform: translateY(-5px);
			}
.photo-tiles-item .photo-info {
	margin-top: 10px;
}
.photo-tiles-item .photo-info span{
	font-weight:bold;
}
.photo-tiles-item .photo-info .btn_copy{
	float:right;
}
.photo-tiles-item .prlightbox {
	cursor: pointer;
}
.photo-list.active, .photo-grid.active {
	background: #fff;
}
/* 图片 Start */
/*! PhotoSwipe main CSS by Dmitry Semenov | photoswipe.com | MIT license */
.pswp {
	display: none;
	position: absolute;
	width: 100%;
	height: 100%;
	left: 0;
	top: 0;
	overflow: hidden;
	-ms-touch-action: none;
	touch-action: none;
	z-index: 1500;
	-webkit-text-size-adjust: 100%;
	-webkit-backface-visibility: hidden;
	outline: none
}

.pswp * {
	-webkit-box-sizing: border-box;
	box-sizing: border-box
}

.pswp img {
	max-width: none
}

.pswp--animate_opacity {
	opacity: 0.001;
	will-change: opacity;
	-webkit-transition: opacity 333ms cubic-bezier(0.4, 0, 0.22, 1);
	transition: opacity 333ms cubic-bezier(0.4, 0, 0.22, 1)
}

.pswp--open {
	display: block
}

.pswp--zoom-allowed .pswp__img {
	cursor: -webkit-zoom-in;
	cursor: -moz-zoom-in;
	cursor: zoom-in
}

.pswp--zoomed-in .pswp__img {
	cursor: -webkit-grab;
	cursor: -moz-grab;
	cursor: grab
}

.pswp--dragging .pswp__img {
	cursor: -webkit-grabbing;
	cursor: -moz-grabbing;
	cursor: grabbing
}

.pswp__bg {
	position: absolute;
	left: 0;
	top: 0;
	width: 100%;
	height: 100%;
	background: #000;
	opacity: 1 !important;
	-webkit-transform: translateZ(0);
	transform: translateZ(0);
	-webkit-backface-visibility: hidden;
	will-change: opacity
}

.pswp__scroll-wrap {
	position: absolute;
	left: 0;
	top: 0;
	width: 100%;
	height: 100%;
	overflow: hidden
}

.pswp__container, .pswp__zoom-wrap {
	-ms-touch-action: none;
	touch-action: none;
	position: absolute;
	left: 0;
	right: 0;
	top: 0;
	bottom: 0
}

.pswp__container, .pswp__img {
	-webkit-user-select: none;
	-moz-user-select: none;
	-ms-user-select: none;
	user-select: none;
	-webkit-tap-highlight-color: transparent;
	-webkit-touch-callout: none
}

.pswp__zoom-wrap {
	position: absolute;
	width: 100%;
	-webkit-transform-origin: left top;
	-ms-transform-origin: left top;
	transform-origin: left top;
	-webkit-transition: -webkit-transform 333ms cubic-bezier(0.4, 0, 0.22, 1);
	transition: transform 333ms cubic-bezier(0.4, 0, 0.22, 1)
}

.pswp__bg {
	will-change: opacity;
	-webkit-transition: opacity 333ms cubic-bezier(0.4, 0, 0.22, 1);
	transition: opacity 333ms cubic-bezier(0.4, 0, 0.22, 1)
}

.pswp--animated-in .pswp__bg, .pswp--animated-in .pswp__zoom-wrap {
	-webkit-transition: none;
	transition: none
}

.pswp__container, .pswp__zoom-wrap {
	-webkit-backface-visibility: hidden
}

.pswp__item {
	position: absolute;
	left: 0;
	right: 0;
	top: 0;
	bottom: 0;
	overflow: hidden
}

.pswp__img {
	position: absolute;
	width: auto;
	height: auto;
	top: 0;
	left: 0
}

.pswp__img--placeholder {
	-webkit-backface-visibility: hidden
}

.pswp__img--placeholder--blank {
	background: #222
}

.pswp--ie .pswp__img {
	width: 100% !important;
	height: auto !important;
	left: 0;
	top: 0
}

.pswp__error-msg {
	position: absolute;
	left: 0;
	top: 50%;
	width: 100%;
	text-align: center;
	font-size: 14px;
	line-height: 16px;
	margin-top: -8px;
	color: #CCC
}

	.pswp__error-msg a {
		color: #CCC;
		text-decoration: underline
	}
/*! PhotoSwipe Default UI CSS by Dmitry Semenov | photoswipe.com | MIT license */

.pswp__button {
	width: 44px;
	height: 44px;
	position: relative;
	background: none;
	cursor: pointer;
	overflow: visible;
	-webkit-appearance: none;
	display: block;
	border: 0;
	padding: 0;
	margin: 0;
	float: right;
	opacity: 0.75;
	-webkit-transition: opacity 0.2s;
	transition: opacity 0.2s;
	-webkit-box-shadow: none;
	box-shadow: none
}

	.pswp__button:focus, .pswp__button:hover {
		opacity: 1
	}

	.pswp__button:active {
		outline: none;
		opacity: 0.9
	}

	.pswp__button::-moz-focus-inner {
		padding: 0;
		border: 0
	}

.pswp__ui--over-close .pswp__button--close {
	opacity: 1
}

.pswp__button, .pswp__button--arrow--left:before, .pswp__button--arrow--right:before {
	background: url(../images/set/default-skin.png) 0 0 no-repeat;
	background-size: 264px 88px;
	width: 44px;
	height: 44px
}

@media (-webkit-min-device-pixel-ratio: 1.1), (-webkit-min-device-pixel-ratio: 1.09375), (min-resolution: 105dpi), (min-resolution: 1.1dppx) {
	.pswp--svg .pswp__button, .pswp--svg .pswp__button--arrow--left:before, .pswp--svg .pswp__button--arrow--right:before {
		background-image: url(../images/set/default-skin.png)
	}

	.pswp--svg .pswp__button--arrow--left, .pswp--svg .pswp__button--arrow--right {
		background: none
	}
}

.pswp__button--close {
	background-position: 0 -44px
}

.pswp__button--share {
	background-position: -44px -44px
}

.pswp__button--fs {
	display: none
}

.pswp--supports-fs .pswp__button--fs {
	display: block
}

.pswp--fs .pswp__button--fs {
	background-position: -44px 0
}

.pswp__button--zoom {
	display: none;
	background-position: -88px 0
}

.pswp--zoom-allowed .pswp__button--zoom {
	display: block
}

.pswp--zoomed-in .pswp__button--zoom {
	background-position: -132px 0
}

.pswp--touch .pswp__button--arrow--left, .pswp--touch .pswp__button--arrow--right {
	visibility: hidden
}

.pswp__button--arrow--left, .pswp__button--arrow--right {
	background: none;
	top: 50%;
	margin-top: -50px;
	width: 70px;
	height: 100px;
	position: absolute
}

.pswp__button--arrow--left {
	left: 0
}

.pswp__button--arrow--right {
	right: 0
}

	.pswp__button--arrow--left:before, .pswp__button--arrow--right:before {
		content: '';
		top: 35px;
		background-color: rgba(0,0,0,0.3);
		height: 30px;
		width: 32px;
		position: absolute
	}

.pswp__button--arrow--left:before {
	left: 6px;
	background-position: -138px -44px
}

.pswp__button--arrow--right:before {
	right: 6px;
	background-position: -94px -44px
}

.pswp__counter, .pswp__share-modal {
	-webkit-user-select: none;
	-moz-user-select: none;
	-ms-user-select: none;
	user-select: none
}

.pswp__share-modal {
	display: block;
	background: rgba(0,0,0,0.5);
	width: 100%;
	height: 100%;
	top: 0;
	left: 0;
	padding: 10px;
	position: absolute;
	z-index: 1600;
	opacity: 0;
	-webkit-transition: opacity 0.25s ease-out;
	transition: opacity 0.25s ease-out;
	-webkit-backface-visibility: hidden;
	will-change: opacity
}

.pswp__share-modal--hidden {
	display: none
}

.pswp__share-tooltip {
	z-index: 1620;
	position: absolute;
	background: #FFF;
	top: 56px;
	border-radius: 2px;
	display: block;
	width: auto;
	right: 44px;
	-webkit-box-shadow: 0 2px 5px rgba(0,0,0,0.25);
	box-shadow: 0 2px 5px rgba(0,0,0,0.25);
	-webkit-transform: translateY(6px);
	-ms-transform: translateY(6px);
	transform: translateY(6px);
	-webkit-transition: -webkit-transform 0.25s;
	transition: transform 0.25s;
	-webkit-backface-visibility: hidden;
	will-change: transform
}

	.pswp__share-tooltip a {
		display: block;
		padding: 8px 12px;
		color: #000;
		text-decoration: none;
		font-size: 14px;
		line-height: 18px
	}

		.pswp__share-tooltip a:hover {
			text-decoration: none;
			color: #000
		}

		.pswp__share-tooltip a:first-child {
			border-radius: 2px 2px 0 0
		}

		.pswp__share-tooltip a:last-child {
			border-radius: 0 0 2px 2px
		}

.pswp__share-modal--fade-in {
	opacity: 1
}

	.pswp__share-modal--fade-in .pswp__share-tooltip {
		-webkit-transform: translateY(0);
		-ms-transform: translateY(0);
		transform: translateY(0)
	}

.pswp--touch .pswp__share-tooltip a {
	padding: 16px 12px
}

a.pswp__share--facebook:before {
	content: '';
	display: block;
	width: 0;
	height: 0;
	position: absolute;
	top: -12px;
	right: 15px;
	border: 6px solid transparent;
	border-bottom-color: #FFF;
	-webkit-pointer-events: none;
	-moz-pointer-events: none;
	pointer-events: none
}

a.pswp__share--facebook:hover {
	background: #3E5C9A;
	color: #FFF
}

	a.pswp__share--facebook:hover:before {
		border-bottom-color: #3E5C9A
	}

a.pswp__share--twitter:hover {
	background: #55ACEE;
	color: #FFF
}

a.pswp__share--pinterest:hover {
	background: #CCC;
	color: #CE272D
}

a.pswp__share--download:hover {
	background: #DDD
}

.pswp__counter {
	position: absolute;
	left: 0;
	top: 0;
	height: 44px;
	font-size: 13px;
	line-height: 44px;
	color: #FFF;
	opacity: 0.75;
	padding: 0 10px
}

.pswp__caption {
	position: absolute;
	left: 0;
	bottom: 0;
	width: 100%;
	min-height: 44px
}

	.pswp__caption small {
		font-size: 11px;
		color: #BBB
	}

.pswp__caption__center {
	text-align: left;
	max-width: 420px;
	margin: 0 auto;
	font-size: 13px;
	padding: 10px;
	line-height: 20px;
	color: #CCC
}

.pswp__caption--empty {
	display: none
}

.pswp__caption--fake {
	visibility: hidden
}

.pswp__preloader {
	width: 44px;
	height: 44px;
	position: absolute;
	top: 0;
	left: 50%;
	margin-left: -22px;
	opacity: 0;
	-webkit-transition: opacity 0.25s ease-out;
	transition: opacity 0.25s ease-out;
	will-change: opacity;
	direction: ltr
}

.pswp__preloader__icn {
	width: 20px;
	height: 20px;
	margin: 12px
}

.pswp__preloader--active {
	opacity: 1
}

	.pswp__preloader--active .pswp__preloader__icn {
		background: url(../images/ajax-loader.gif) 0 0 no-repeat
	}

.pswp--css_animation .pswp__preloader--active {
	opacity: 1
}

	.pswp--css_animation .pswp__preloader--active .pswp__preloader__icn {
		-webkit-animation: clockwise 500ms linear infinite;
		animation: clockwise 500ms linear infinite
	}

	.pswp--css_animation .pswp__preloader--active .pswp__preloader__donut {
		-webkit-animation: donut-rotate 1000ms cubic-bezier(0.4, 0, 0.22, 1) infinite;
		animation: donut-rotate 1000ms cubic-bezier(0.4, 0, 0.22, 1) infinite
	}

.pswp--css_animation .pswp__preloader__icn {
	background: none;
	opacity: 0.75;
	width: 14px;
	height: 14px;
	position: absolute;
	left: 15px;
	top: 15px;
	margin: 0
}

.pswp--css_animation .pswp__preloader__cut {
	position: relative;
	width: 7px;
	height: 14px;
	overflow: hidden
}

.pswp--css_animation .pswp__preloader__donut {
	-webkit-box-sizing: border-box;
	box-sizing: border-box;
	width: 14px;
	height: 14px;
	border: 2px solid #FFF;
	border-radius: 50%;
	border-left-color: transparent;
	border-bottom-color: transparent;
	position: absolute;
	top: 0;
	left: 0;
	background: none;
	margin: 0
}

@media screen and (max-width: 1024px) {
	.pswp__preloader {
		position: relative;
		left: auto;
		top: auto;
		margin: 0;
		float: right
	}
}

@-webkit-keyframes clockwise {
	0% {
		-webkit-transform: rotate(0deg);
		transform: rotate(0deg)
	}

	100% {
		-webkit-transform: rotate(360deg);
		transform: rotate(360deg)
	}
}

@keyframes clockwise {
	0% {
		-webkit-transform: rotate(0deg);
		transform: rotate(0deg)
	}

	100% {
		-webkit-transform: rotate(360deg);
		transform: rotate(360deg)
	}
}

@-webkit-keyframes donut-rotate {
	0% {
		-webkit-transform: rotate(0);
		transform: rotate(0)
	}

	50% {
		-webkit-transform: rotate(-140deg);
		transform: rotate(-140deg)
	}

	100% {
		-webkit-transform: rotate(0);
		transform: rotate(0)
	}
}

@keyframes donut-rotate {
	0% {
		-webkit-transform: rotate(0);
		transform: rotate(0)
	}

	50% {
		-webkit-transform: rotate(-140deg);
		transform: rotate(-140deg)
	}

	100% {
		-webkit-transform: rotate(0);
		transform: rotate(0)
	}
}

.pswp__ui {
	-webkit-font-smoothing: auto;
	visibility: visible;
	opacity: 1;
	z-index: 1550
}

.pswp__top-bar {
	position: absolute;
	left: 0;
	top: 0;
	height: 44px;
	width: 100%
}

.pswp__caption, .pswp__top-bar, .pswp--has_mouse .pswp__button--arrow--left, .pswp--has_mouse .pswp__button--arrow--right {
	-webkit-backface-visibility: hidden;
	will-change: opacity;
	-webkit-transition: opacity 333ms cubic-bezier(0.4, 0, 0.22, 1);
	transition: opacity 333ms cubic-bezier(0.4, 0, 0.22, 1)
}

.pswp--has_mouse .pswp__button--arrow--left, .pswp--has_mouse .pswp__button--arrow--right {
	visibility: visible
}

.pswp__top-bar, .pswp__caption {
	background-color: rgba(0,0,0,0.5)
}

.pswp__ui--fit .pswp__top-bar, .pswp__ui--fit .pswp__caption {
	background-color: rgba(0,0,0,0.3)
}

.pswp__ui--idle .pswp__top-bar {
	opacity: 0
}

.pswp__ui--idle .pswp__button--arrow--left, .pswp__ui--idle .pswp__button--arrow--right {
	opacity: 0
}

.pswp__ui--hidden .pswp__top-bar, .pswp__ui--hidden .pswp__caption, .pswp__ui--hidden .pswp__button--arrow--left, .pswp__ui--hidden .pswp__button--arrow--right {
	opacity: 0.001
}

.pswp__ui--one-slide .pswp__button--arrow--left, .pswp__ui--one-slide .pswp__button--arrow--right, .pswp__ui--one-slide .pswp__counter {
	display: none
}

.pswp__element--disabled {
	display: none !important
}

.pswp--minimal--dark .pswp__top-bar {
	background: none
}
