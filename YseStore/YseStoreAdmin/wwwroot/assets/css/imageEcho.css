/**
 * 产品图片回显功能的CSS样式修复
 * 确保图片正确显示和布局
 */

/* 确保图片容器正确显示 */
#PicDetail.pro_multi_img {
    display: grid !important;
    grid-auto-flow: row dense;
    grid-gap: 18px;
    grid-template-columns: repeat(6, 1fr);
    margin: 0;
    grid-template-rows: repeat(2, 1fr);
}

/* 当只有一个上传按钮时的特殊样式 */
#PicDetail.pro_multi_img:has(.img.show_btn:only-child),
#PicDetail.pro_multi_img .img.show_btn:only-child {
    grid-column: 1 / 3;
    grid-row: 1 / 2;
}

/* 没有图片时的容器样式 */
#PicDetail.pro_multi_img:not(:has(.img.isfile)) {
    grid-template-columns: repeat(3, 1fr);
    grid-template-rows: 1fr;
}

/* 确保已上传的图片项正确显示 */
#PicDetail.pro_multi_img .img.isfile {
    width: auto !important;
    height: auto !important;
    overflow: visible !important;
    margin: 0 !important;
}

/* 确保上传按钮项正确显示 */
#PicDetail.pro_multi_img .img.show_btn {
    width: auto !important;
    height: auto !important;
    overflow: visible !important;
    margin: 0 !important;
}

/* 第一张图片占据更大的空间 */
#PicDetail.pro_multi_img .img:not(.show_btn):first-child {
    grid-row: 1/3;
    grid-column: 1/3;
}

/* 图片预览区域样式 */
#PicDetail.pro_multi_img .img .preview_pic {
    width: 100%;
    height: 0;
    background-color: #f8f9fb;
    border-radius: 5px;
    padding-top: 100%;
    position: relative;
}

/* 图片链接样式 */
#PicDetail.pro_multi_img .img .preview_pic > a {
    width: 100%;
    height: 100%;
    padding: 0;
    border: 0;
    position: absolute;
    left: 0;
    top: 0;
    box-sizing: border-box;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
    border-radius: 5px;
}

/* 图片样式 */
#PicDetail.pro_multi_img .img .preview_pic img {
    max-width: 100%;
    max-height: 100%;
    object-fit: cover;
    border-radius: 5px;
}

/* 上传按钮容器样式 - 使用源代码的样式 */
#PicDetail.pro_multi_img .img.show_btn .preview_pic {
    background-color: var(--GlobalPicUploadBgColor, #f6fdff) !important;
    border: 1px var(--primaryColor, #4BA0FC) dashed !important;
    box-sizing: border-box !important;
    position: relative;
}

/* 上传按钮样式 - 使用源代码的正确样式 */
#PicDetail.pro_multi_img .img.show_btn .upload_btn {
    width: 100% !important;
    height: 100% !important;
    background: none !important;
    text-indent: 0 !important;
    opacity: 0 !important;
    position: absolute !important;
    left: 0 !important;
    top: 0 !important;
    z-index: 1 !important;
    border: none !important;
    cursor: pointer !important;
}

/* 上传文本样式 - 使用源代码的样式 */
#PicDetail.pro_multi_img .img.show_btn .upload_txt {
    display: block !important;
    width: 100% !important;
    position: absolute !important;
    left: 0 !important;
    top: 50% !important;
    text-align: center !important;
    transform: translateY(-50%) !important;
    line-height: 20px !important;
    cursor: pointer !important;
    color: var(--GlobalBtnSimpleColor, #7d8d9e) !important;
    z-index: 2 !important;
    pointer-events: none !important;
}

#PicDetail.pro_multi_img .img.show_btn .upload_txt p:first-child {
    color: var(--primaryColor, #4BA0FC) !important;
    font-weight: normal !important;
}

#PicDetail.pro_multi_img .img.show_btn .upload_txt p {
    margin: 2px 0 !important;
    font-size: 12px !important;
}

/* 图片操作按钮样式 */
#PicDetail.pro_multi_img .img .pic_btn {
    border-radius: 5px;
    z-index: 2;
}

/* 已上传图片的按钮样式（隐藏） */
#PicDetail.pro_multi_img .img.isfile .upload_btn {
    display: none !important;
}

/* 已上传图片隐藏上传文本 */
#PicDetail.pro_multi_img .img.isfile .upload_txt {
    display: none !important;
}

/* 确保图片容器在有内容时显示 */
#PicDetail.pro_multi_img:not(:empty) {
    display: grid !important;
}

/* 修复可能的布局问题 */
.multi_img.upload_file_multi.pro_multi_img {
    clear: both;
}

.multi_img.upload_file_multi.pro_multi_img:after {
    display: block;
    height: 0;
    clear: both;
    content: '';
}

/* 响应式调整 */
@media (max-width: 1200px) {
    #PicDetail.pro_multi_img {
        grid-template-columns: repeat(4, 1fr);
    }
}

@media (max-width: 768px) {
    #PicDetail.pro_multi_img {
        grid-template-columns: repeat(3, 1fr);
    }
}

@media (max-width: 480px) {
    #PicDetail.pro_multi_img {
        grid-template-columns: repeat(2, 1fr);
    }
}

/* 确保图片加载时的样式 */
#PicDetail.pro_multi_img .img.isfile .preview_pic img {
    transition: opacity 0.3s ease;
}

#PicDetail.pro_multi_img .img.isfile .preview_pic img:not([src]) {
    opacity: 0;
}

#PicDetail.pro_multi_img .img.isfile .preview_pic img[src] {
    opacity: 1;
}

/* 修复可能的z-index问题 */
#PicDetail.pro_multi_img .img .preview_pic {
    position: relative;
    z-index: 1;
}

#PicDetail.pro_multi_img .img .pic_btn {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 3;
}

/* 确保隐藏字段不影响布局 */
#PicDetail.pro_multi_img .img input[type="hidden"] {
    display: none !important;
}

/* 调试样式 - 可以在开发时启用 */
/*
#PicDetail.pro_multi_img .img {
    border: 1px solid red;
}

#PicDetail.pro_multi_img .img.isfile {
    border: 1px solid green;
}

#PicDetail.pro_multi_img .img.show_btn {
    border: 1px solid blue;
}
*/
