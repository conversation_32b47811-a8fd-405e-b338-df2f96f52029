 /* 可视化编辑容器 Start */
#visual .h1{height:30px;line-height: 30px;font-size: 20px;color: #1f2328;}
#visual .inside_table{padding-top: 10px;}
#visual .inside_table .add{background-image: none;font-size: 14px;padding: 0 20px;}
#visual .inside_table table{width: 100%;margin: 10px 0;}
#visual .inside_table table thead td{padding: 14px 30px;}
#visual .inside_table table td{padding:30px;font-size: 14px;}
#visual .inside_table table td a{font-size: 14px;margin-right: 10px;}
#visual .inside_table table td a:nth-child(2){color: #aaa;}
#visual .inside_container .inside_menu{border-bottom: none;padding-bottom: 10px;}
#visual .inside_container .inside_menu>li.fr{float: right;}
#visual .inside_container .title{font-size: 20px;}
#visual .inside_container .edit_btn{padding: 0 17px;height: 32px;line-height: 32px;border-radius: 5px;background: var(--primaryColor);border: 1px solid var(--primaryColor);font-size: 14px;text-align: center;color: #fff;}

#visual .box_tab{line-height: 60px;border-bottom: 1px solid #f1f1f1;}
#visual .box_tab a{margin: 0 30px;}
#visual .box_tab a.current{color: var(--primaryColor);}
#visual .container{width: 1200px;background: #fff;margin: auto;position: relative;margin-top: 24px;border-radius: 5px;}
#visual .container:first-child{margin-top: 4px;}
#visual .container img{max-width: 100%;}
#visual .container .box_themes{padding: 24px 30px 0 30px;}
#visual .container .box_themes .h1 a{padding-left: 32px;float: right;font-size: 14px;color: var(--primaryColor);position: relative;}
#visual .container .box_themes .h1 a::after{content: '';width: 30px;height: 30px;position: absolute;top: 0;left: 0;background-image: url(../images/view/icon_view.png);background-position: 0 -180px;}
#visual .container .box_list{margin-top: 46px;margin-left: 50px;padding-right: 490px;position: relative;}
#visual .container .view_box{width: 100%;max-width: 936px;position: relative;}
#visual .view_pc{padding: 22px 20px 16px 20px;background: #fff; position: relative;border-radius: 20px;border: 1px solid #c5ccd3;border-bottom: 0;border-bottom-left-radius: 0;border-bottom-right-radius: 0;}
#visual .view_pc .img{height: 100%;height:300px;overflow: hidden;}
#visual .view_mobile{width: 162px;height: 250px; overflow: hidden;background: #fff;border:1px solid #c5ccd3;border-bottom: 0;border-radius: 22px;padding: 19px 13px 16px 13px;position: absolute;right: -111px;bottom: 0;border-bottom-left-radius: 0;border-bottom-right-radius: 0;}
#visual .view_mobile .img{width: 100%;height: 100%;overflow: hidden;}
#visual .container .btn_box{width: 220px;height: 188px;margin-top: 53px;margin-right: 50px;position: absolute;top: 0;right: 0;bottom: 0;text-align: center;}
#visual .container .btn_box .edit_btn{display: block;width: 220px;height: 42px;line-height: 42px;background: var(--primaryColor);border-radius: 4px;font-size: 16px;color: #fff;}
#visual .container .btn_box .switch_style_btn{margin-top: 12px;background-color: #F5F7FA;color: var(--primaryColor);}
#visual .container .btn_box dd{padding:6px 0;line-height: 26px;font-size: 12px;color: #aaa;}
#visual .container .btn_box .title{font-size: 14px;margin-bottom: 21px;color: #1f2328;}
#visual .container .btn_box .title strong{font-weight: bold;font-size: 16px;}
#visual .container .btn_box .title span{display: inline-block;margin-top: 9px;color: #7d8d9e;font-size: 12px;}
#visual .terminal_list{opacity: 1; width: 162px;height: 30px; background:#fff; border:1px solid #dddddd;border-radius: 16px;margin: 15px auto 5px;}
#visual .terminal_list a{width: 82px;height: 32px; background: url(../images/view/icon_terminal.png) no-repeat center center; border-radius: 16px;margin:-1px 0;}
#visual .terminal_list a.pc{background-position: center top; margin-left: -1px;float: left;}
#visual .terminal_list a.mobile{background-position: center bottom;margin-right: -1px;float: right;}
#visual .terminal_list a.cur{background-color: var(--primaryColor);background-image: url(../images/view/icon_terminal_cur.png);}
#visual .box_drafts .inside_container{padding-top: 20px;padding-bottom: 22px;}
#visual .box_drafts .inside_container>h1{font-size: 20px;color: #1f2328;}
#visual .box_drafts .r_con_table{margin: 0;}
#visual .box_drafts .r_con_table td{padding: 26px 30px;}
#visual .box_drafts .r_con_table td.title{font-size: 14px;color: #1f2328;}
#visual .box_drafts .r_con_table td.title strong{font-weight: bold;font-size: 16px;}
#visual .box_drafts .r_con_table td.title em{font-size: 14px;}
#visual .box_drafts .r_con_table td.title span{display: inline-block;margin-top: 6px;color: #7d8d9e;font-size: 12px;}
#visual .box_drafts .r_con_table td .oper_icon{opacity: 1;}
#visual .box_drafts .r_con_table tbody tr:hover, #visual .box_drafts .r_con_table tbody tr.clicked{background-color: #ecf5ff;}
#visual .box_drafts .bg_no_table_data .content{*height: 37px;bottom: 0;margin: auto;top: 0 !important;}

#visual .search_menu{height: 60px;}
#visual .search_menu .k_input .more{display: none;}
#visual .search_menu .k_input .form_input{width: 218px;}
#visual .add_visual_curtom_jsppane{width: 310px;height: calc(100% - 110px);overflow-y: auto;outline: none;}
#visual .add_visual_curtom_jsppane::-webkit-scrollbar{width: 5px; background: #fff;border-radius: 5px;}
#visual .add_visual_curtom_jsppane::-webkit-scrollbar-thumb{background: rgba(0, 0, 0, 0.1);border-radius: 5px;}
#visual .add_visual_curtom_jsppane::-webkit-scrollbar-thumb:hover{background: rgba(0, 0, 0, 0.3);}
#visual .add_visual_curtom_list .curtom_list_item{padding:15px;cursor: pointer;}
#visual .add_visual_curtom_list .curtom_list_item:hover{background: #e9f2ff;}
#visual .add_visual_curtom_list .curtom_list_item .item_img{width: 46px;height: 46px;border-radius: 4px;overflow: hidden;}
#visual .add_visual_curtom_list .curtom_list_item .item_img img{max-width: 100%;max-height: 100%;}
#visual .add_visual_curtom_list .curtom_list_item .item_name{width: 190px;height: 46px;line-height: 23px;margin-left: 12px;overflow: hidden;font-size: 14px;color: #555;}
#visual .add_visual_curtom_list .curtom_list_item .item_arrow{position: relative;}
#visual .add_visual_curtom_list .curtom_list_item .item_arrow i{position: absolute;right: 0;top: 15px;}
#visual .add_visual_curtom_list .curtom_list_item .item_arrow i:after, #visual .add_visual_curtom_list .curtom_list_item .item_arrow i:before{border: 8px solid transparent;border-left: 8px solid #fff;width: 0;height: 0;position: absolute;top: 0;right: 0;content: '';}
#visual .add_visual_curtom_list .curtom_list_item .item_arrow i:before{border-left-color: #ccc;right: -2px;}
#visual .add_visual_curtom_list .curtom_list_item:hover .item_arrow i:after, #visual .add_visual_curtom_list .curtom_list_item:hover .item_arrow i:before{border: 8px solid transparent;border-left: 8px solid #effaf7;width: 0;height: 0;position: absolute;top: 0;right: 0;content: '';}
#visual .add_visual_curtom_list .curtom_list_item:hover .item_arrow i:before{border-left-color: var(--primaryColor);right: -2px;}
#visual .add_visual_curtom_list .load_more{display: block;margin-top: 10px;text-align: center;font-size: 14px;color: #000;}
#visual .add_visual_curtom_list .load_more:hover{color: var(--primaryColor);}

#template_list{display: none;overflow-y: auto;background: #fff;margin:auto;position: fixed;top: 0;right: 0;bottom: 0;left: 0;z-index: 9999;text-align: center;visibility: visible;}
#template_list .close{width: 44px;height: 44px;background: url(../images/frame/icon_win_close.png) no-repeat center center;position: fixed;top: 16px;right: 20px;z-index: 10003;}
#template_list .terminal_box{background: #fff;padding-bottom: 15px;position: fixed;top: 0;right: 0;left: 0;z-index: 10002;}
#template_list .top_title{height: 56px;line-height: 56px;padding-top: 60px;font-size: 36px;}
#template_list .sub_title{display: none;height: 26px;line-height: 26px;font-size: 16px;color: #888;}
#template_list.visual_edit .head_fixed{height: 128px;}
#template_list.visual_edit .mobile .head_fixed{height: 107px;}
#template_list .head_fixed{height: 230px;}
#template_list .mobile .head_fixed{height: 209px;}
#template_list.small_head .head_fixed, #template_list.small_head .mobile .head_fixed{height: 62px;}
#template_list.small_head .terminal_list{display: none;opacity: 0;}
#template_list.small_head .top_title{display: none;}
#template_list.small_head .sub_title{padding-top: 15px;}
#template_list .category{padding-top: 15px;}
#template_list .category a{display: inline-block;height: 30px;line-height: 30px;border:1px solid transparent; border-radius: 16px; margin: 0 4px;padding: 0 28px;font-size: 16px;color: #888;}
#template_list .category a.cur{border-color: var(--primaryColor);color: var(--primaryColor);}
#template_list .tem_box{display: none;width: 80%;overflow: hidden;margin:auto;}
#template_list .tem_box .rows{overflow: hidden;text-align: center;}
#template_list .tem_box .list{width: 15.7vw;overflow: hidden;margin: 4vw 2.5vw 0;position: relative; display: inline-block;box-sizing:border-box;-webkit-box-sizing:border-box;}
#template_list .tem_box .list .img{height: 23.3vw;background: url(../images/global/loading.gif) no-repeat center center;border: 1px solid  #e8e8e8;padding: 9px;position: relative;}
#template_list .tem_box .list .img:before{content:'';width: 100%;height: 9px;background: #fff;border-top: 1px solid  #e8e8e8;position: absolute;top: -1px;right: 0;left: 0;}
#template_list .tem_box .list .img:after{content:'';width: 100%;height: 9px;background: #fff;border-bottom: 1px solid  #e8e8e8;position: absolute;right: 0;bottom: -1px;left: 0;}
#template_list .tem_box .list .img.cur, #template_list .tem_box .list .img.cur:before, #template_list .tem_box .list .img.cur:after{border-color: var(--primaryColor);}
#template_list .tem_box .list .img span{width: 100%;height: 100%;background: rgba(0,0,0,0.6);position: absolute;top: 0;left: 0;z-index: 1;opacity: 0;transition: all 0.3s;}
#template_list .tem_box .list .img.cur span{opacity: 1;background: rgba(0,0,0,0.6) url(../images/photo/icon_big_item_cur.png) no-repeat top  12px right 12px/8%}
#template_list .tem_box .list .img label{ display:block; padding:5px 10px; background:#333; position:absolute; right:0; bottom:0; z-index:1000; color:#f1f1f1; font-size:14px;}
#template_list .tem_box .list img{max-width: 100%;}
#template_list .tem_box .edit_box{height: 24px;line-height: 24px;background: #fff;padding: 0 0 8px;position: relative;z-index: 1;}
#template_list .tem_box .edit_box .edit_btn{ background: var(--primaryColor);border:1px solid var(--primaryColor);}
#template_list .tem_box .edit_box .edit_responsive{margin-left:5px;padding: 0 8px;background-color: #e4f6f1;border-radius: 5px;}
#template_list .tem_box .eidt_btn_items{position: absolute;width: 100%; top:46%;left:50%;transform: translate(-50%,-50%);z-index: 10;}
#template_list .tem_box .eidt_btn_items .edit_btn{display: block; width: 43.5%; height: 38px;line-height: 38px;margin:0 auto; background: var(--primaryColor);border:1px solid var(--primaryColor);border-radius: 19px;font-size: 16px;color: #fff;text-align: center;opacity: 0;transition: all 0.3s;}
#template_list .tem_box .list:hover .edit_btn, #template_list .tem_box .list:hover .img span{opacity: 1;}
#template_list .tem_box .list:hover .edit_btn{animation:btnMove .2s 1 0s linear forwards; -webkit-animation:btnMove .2s 1 0s linear forwards;}
#template_list .tem_box .list:hover .view_btn{opacity: 1;}
#template_list .tem_box .list .edit_btn:hover{box-shadow: inset 0px 0px 8px rgba(85,85,85,.3);}
#template_list .tem_box .list .view_btn{width:30px; height:19px; background-image:url(../images/view/icon_preview.png); background-position:center; position:absolute; left:20px; top:20px; opacity:0; z-index:2;}
#template_list .tem_box .loading{height: 44px;line-height: 44px;margin-top: 20px;margin-bottom: 15px;text-align: center;}
#template_list .tem_box .loading span{display: inline-block;font-size: 16px;color: #999;}
#template_list .tem_box .loading.cur span{background: url(../images/global/loading.gif) no-repeat 5px center;padding-left: 44px;}

#template_list .tem_box.mobile{width: 88vw;}
#template_list .tem_box.mobile .list{width: 20vw;margin: 2.86vw 1vw 0;}
#template_list .tem_box.mobile .list .img{height: 30vw;}

#plugins_visual{width:100vw; height:100vh; background:#ececec; position:fixed; top:0; left:0; z-index:9999;opacity: 0;}
#plugins_visual .visual_btn{display: block;width: 160px;height: 36px;line-height: 36px;margin: 0 auto;background: var(--primaryColor);color: #fff;font-size: 14px;text-align: center;border-radius: 18px;}
#plugins_visual .loading_mask{ width:100%; height:100%; background:url(../images/global/loading_oth.gif) #fff center no-repeat; position:absolute; top:0; left:0;}
#plugins_visual .top_bar{ height:60px; line-height:60px; position:relative; text-align:center;background-color: #293039;}
#plugins_visual .top_bar .go_home{ height:60px; line-height:60px; padding:0 25px 0 50px; float:left; font-size:14px; position:relative; cursor:pointer;background-color: #293039;color: #abadaf;}
#plugins_visual .top_bar .go_home i{width: 10px;height: 16px;position: absolute;left: 27px;top: 22px;background-image: url(../images/view/icon_view.png);background-position: -70px -67px;}
#plugins_visual .top_bar .go_close{width: 110px; height:32px; line-height:32px; margin:15px 20px 0 20px; background:#888; float:left; font-size:14px; color:#fff; border-radius:16px; position:relative;  cursor:pointer;}
#plugins_visual .top_bar .go_pages{ padding:0 60px 0 35px; float:left; font-size:14px; color:#555; position:relative; border-right:1px solid #eaeaea; border-left:1px solid #eaeaea; cursor:pointer; position:relative;}
#plugins_visual .top_bar .go_pages .pages_name span{ color:var(--primaryColor);}
#plugins_visual .top_bar .go_pages .pages_name span i{ width:16px; height:16px; position:absolute; top:24px; right:25px;}
#plugins_visual .top_bar .go_pages .pages_name span i:after,
#plugins_visual .top_bar .go_pages .pages_name span i:before{ border:8px solid transparent; border-top:8px solid #f6f7f7; width:0; height:0; position:absolute; top:0; left:0; content:'';}
#plugins_visual .top_bar .go_pages .pages_name span i:before{ border-top-color:var(--primaryColor); top:2px;}
#plugins_visual .top_bar .go_pages .pages_list{ display:none; width:100%; background:#f6f7f7; position:absolute; left:-1px; top:100%; z-index:1; border:1px solid #eaeaea;}
#plugins_visual .top_bar .go_pages:hover .pages_list{ display:block;}
#plugins_visual .top_bar .go_pages:hover .pages_list a{display: block;border-top: 1px solid #eaeaea;}
#plugins_visual .top_bar .go_pages:hover .pages_list a:first-child{border-top: 0;}
#plugins_visual .top_bar .go_pages:hover .pages_list a:hover{color: #0cb083;}
#plugins_visual .top_bar .go_btn{ height:36px; line-height:36px; margin-top:12px; margin-right:16px; padding:0 30px; float:right; cursor:pointer;border-radius: 5px;position: relative;color: #abadaf;}
#plugins_visual .top_bar .go_btn.go_save,
#plugins_visual .top_bar .go_btn.go_publish{padding:0 28px 0 52px;background-color: var(--primaryColor);color: #fff;}
#plugins_visual .top_bar .go_btn.go_save:after{width: 15px;height: 15px;position: absolute;left: 27px;top: 11px;background-image: url(../images/view/icon_view.png);background-position: -98px -67px;content: '';}
#plugins_visual .top_bar .go_btn.go_publish::after{width: 15px;height: 15px;position: absolute;left: 27px;top: 11px;background-image: url(../images/view/icon_view.png);background-position: -7px -97px;content: '';}
#plugins_visual .top_bar .go_btn.go_style{padding-left: 52px;margin-right: 0;}
#plugins_visual .top_bar .go_btn.go_style::after{width: 16px;height: 16px;position: absolute;left: 27px;top: 10px;background-image: url(../images/view/icon_view.png);background-position: -37px -97px;content: '';}
#plugins_visual .top_bar .go_btn.go_view{margin-right: 0;}
#plugins_visual .top_bar .go_btn.go_view::after{width: 24px;height: 16px;position: absolute;left: 0;top: 10px;background-image: url(../images/view/icon_view.png);background-position: -64px -188px;content: '';}
#plugins_visual .top_bar .go_btn.go_preview{ position:relative;}
#plugins_visual .top_bar .go_btn.go_preview .qrcode{ width:150px; height:150px; margin-left:-75px; background:#fff; border:1px solid #ececec; position:absolute; left:50%; top:44px; z-index:1;}
#plugins_visual .top_bar .go_btn.go_mode{padding: 0 40px;margin-right: 20px;}
#plugins_visual .top_bar .go_btn.go_restore{border-color: #888888;color: #888888;background: transparent;}
#plugins_visual .top_bar .go_btn.go_preview .qrcode img{ max-width:100%; max-height:100%;}
#plugins_visual .top_bar .go_client{ width:164px; height:60px; margin-left:-82px; position:absolute; top:0; left:50%;}
#plugins_visual .top_bar .go_screen{width: 180px;height: 100%; font-size: 0;position: absolute;left: 50%;top: 0;transform: translate(-50%);}
#plugins_visual .top_bar .go_screen i{display: inline-block;width: 30px;height: 30px;margin: 15px 15px 0 15px;background-image: url(../images/view/icon_view.png);background-repeat: no-repeat;cursor: pointer;}
#plugins_visual .top_bar .go_screen i:nth-child(1){background-position: 0 -30px;}
#plugins_visual .top_bar .go_screen i:nth-child(1):hover,
#plugins_visual .top_bar .go_screen i:nth-child(1).current{background-position: -30px -30px;}
#plugins_visual .top_bar .go_screen i:nth-child(2){background-position: -60px -30px;}
#plugins_visual .top_bar .go_screen i:nth-child(2):hover,
#plugins_visual .top_bar .go_screen i:nth-child(2).current{background-position: -90px -30px;}
#plugins_visual .top_bar .go_screen i:nth-child(3){background-position: 0 -60px;}
#plugins_visual .top_bar .go_screen i:nth-child(3):hover,
#plugins_visual .top_bar .go_screen i:nth-child(3).current{background-position: -30px -60px;}
#plugins_visual .top_bar .go_size{ width:164px; height:60px; margin-left:-82px; position:absolute; top:0; left:50%;}
#plugins_visual .top_bar .go_size .size_list{opacity: 1; width: 162px;height: 30px; background:#fff; border:1px solid #dddddd;border-radius: 16px;margin: 15px auto 5px;}
#plugins_visual .top_bar .go_size .size_list a{width: 81px;height: 32px;line-height: 32px;border-radius: 16px;margin:-1px 0;float: left;}
#plugins_visual .top_bar .go_size a.cur{background-color: #0cb083;color: #fff;}

/* 页面选择 */
#plugins_visual .top_bar .go_select{float: left;position: relative;min-width: 164px; height:36px;line-height: 36px;margin:12px 0 0 8px; padding:0 50px 0 16px;background-color: #1f242b; box-sizing: border-box;color:#abadaf;font-size: 14px;border-radius: 5px;text-align: left;}
#plugins_visual .top_bar .go_select .page_show_item em{position: absolute;right:16px;top:50%; width: 9px;height: 6px;background: url(../images/frame/icon_arrow_down_small_gary.png) no-repeat center;transform:rotate(0deg) translateY(-50%);transition: all 0.3s;}
#plugins_visual .top_bar .go_select .show_page_item{color:#fff;}
#plugins_visual .top_bar .go_select .page_select_box {position: absolute;left: 0; width: 100%;height: 0; background-color: #fff;border-radius: 5px;z-index: 1;text-align: left;overflow:hidden;opacity: 0;transition: all .3s;box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.2);max-height: 476px;overflow-y: auto;}
#plugins_visual .top_bar .go_select .page_select_box::-webkit-scrollbar{width: 5px; background: #fff;border-radius: 5px;}
#plugins_visual .top_bar .go_select .page_select_box::-webkit-scrollbar-thumb{background: rgba(0, 0, 0, 0.1);border-radius: 5px;}
#plugins_visual .top_bar .go_select .page_select_box::-webkit-scrollbar-thumb:hover{background: rgba(0, 0, 0, 0.3);}

#plugins_visual .top_bar .go_select .page_select_box ul li{line-height: 42px;}
#plugins_visual .top_bar .go_select .page_select_box ul li .select_item{display: block;background: url(../images/view/icon_pages.png) no-repeat left 15px center; color:#555555; padding:0px 15px 0 50px;}
#plugins_visual .top_bar .go_select .page_select_box ul li.select_index .select_item{background-position: 15px 0;}
#plugins_visual .top_bar .go_select .page_select_box ul li.select_list .select_item{background-position: 15px -42px;}
#plugins_visual .top_bar .go_select .page_select_box ul li.select_goods .select_item{background-position: 15px -84px;}
#plugins_visual .top_bar .go_select .page_select_box ul li.select_article .select_item{background-position: 15px -126px;}
#plugins_visual .top_bar .go_select .page_select_box ul li.item_line{margin-top: 5px;padding-top: 5px; border-top:1px solid #f0f0f0;}
#plugins_visual .top_bar .go_select .page_select_box ul li .select_item:hover{background-color: #f1f8f5;}
#plugins_visual .top_bar .go_select .page_select_box ul li .select_son_box{padding:0px 15px 0 50px;}
#plugins_visual .top_bar .go_select .page_select_box ul li .select_son_box .select_son_item{font-size: 12px;}
#plugins_visual .top_bar .go_select .page_select_box ul li .select_son_box .select_son_item a{display: block;line-height: 36px; color:#555;text-overflow: ellipsis;white-space: nowrap;overflow: hidden;}
#plugins_visual .top_bar .go_select .page_select_box ul li .select_son_box .select_son_item a:hover{color:#0baf4d;}
#plugins_visual .top_bar .go_select:hover .page_select_box{height: auto;opacity: 1;}
#plugins_visual .top_bar .go_select:hover .page_show_item em{transform:rotate(180deg) translateY(50%);}

#plugins_visual .main_bar{ width:calc(100% - 415px); height:calc(100vh - 60px); background:#ececec; position:relative;transition: all 0.2s ease-in-out;}
#plugins_visual .main_bar.full{width: 100%;}
#plugins_visual .main_bar.mweb .iframe_container{width: 414px;height: calc(100% - 30px);margin-top: 15px;}
#plugins_visual .main_bar .iframe_container{width: 100%;height: 100%;margin: 0 auto;transition: all 0.2s ease-in-out;}
#plugins_visual .main_bar iframe{ width:100%; height:100%;}
#plugins_visual .tool_bar{width: 430px;height: calc(100vh - 60px); position:fixed; right:0; top:60px;background-color: #e8ebee;transition: all 0.2s ease-in-out;}
#plugins_visual .tool_bar.full{right: -430px;}
#plugins_visual .tool_bar .tool_bar_menu{padding: 10px 0;background-color: #fff;text-align: center;border-left: 1px solid #dcdfe5;border-bottom: 1px solid #dcdfe5;}
#plugins_visual .tool_bar .tool_bar_menu ul{display: inline-block;padding: 2px;background-color: #eceef0;border-radius: 20px;}
#plugins_visual .tool_bar .tool_bar_menu ul li{display: inline-block;padding: 0 18px;height: 32px;line-height: 32px;cursor: pointer;border-radius: 16px;font-size: 12px;color: #404852;}
#plugins_visual .tool_bar .tool_bar_menu ul li.current{background-color: var(--primaryColor);color: #fff;}
#plugins_visual .tool_bar .nav_bar{width: 150px;height: calc(100vh - 60px - 57px);padding-top: 6px;padding-left: 5px;box-sizing: border-box;border-left: 1px solid #e0e3e8;float: left;}
#plugins_visual .tool_bar .nav_bar .nav_bar_tab{display: none;height: 100%;overflow: auto;overflow-x: hidden;}
#plugins_visual .tool_bar .nav_bar .nav_bar_tab::-webkit-scrollbar{width: 5px; background: #fff;border-radius: 5px;}
#plugins_visual .tool_bar .nav_bar .nav_bar_tab::-webkit-scrollbar-thumb{background: rgba(0, 0, 0, 0.1);border-radius: 5px;}
#plugins_visual .tool_bar .nav_bar .nav_bar_tab::-webkit-scrollbar-thumb:hover{background: rgba(0, 0, 0, 0.3);}
#plugins_visual .tool_bar .nav_bar .nav_bar_tab.current{display: block;}
#plugins_visual .tool_bar .nav_bar .placeHolderItem{width: 108px;height: 50px;margin-bottom: 6px;background-color: #f5f7f9;border-radius: 5px;}
#plugins_visual .tool_bar .nav_bar .modeItem{width: 138px;height: 50px;margin-bottom: 6px;background-color: #f5f7f9;border-radius: 5px;position: relative;opacity: 1 !important;}
#plugins_visual .tool_bar .nav_bar .modeItem.current{background-color: #fff;}
#plugins_visual .tool_bar .nav_bar .modeItem#addModeBtn .char{color: var(--primaryColor);}
#plugins_visual .tool_bar .nav_bar .modeItem em:first-child{width: 30px;height: 100%;position: absolute;left: 0;top: 0;cursor: pointer;}
#plugins_visual .tool_bar .nav_bar .modeItem em:first-child::after{background-image: url(../images/view/icon_view.png);content: '';}
#plugins_visual .tool_bar .nav_bar .modeItem .ding::after{width: 12px;height: 16px;position: absolute;left: 10px;top: 17px;background-position: -69px -97px;}
#plugins_visual .tool_bar .nav_bar .modeItem em.move{cursor: move;}
#plugins_visual .tool_bar .nav_bar .modeItem em.move::after{width: 6px;height: 14px;position: absolute;left: 13px;top: 18px;background-position: -102px -98px;}
#plugins_visual .tool_bar .nav_bar .modeItem em.add::after{width: 12px;height: 12px;position: absolute;left: 10px;top: 19px;background-position: -9px -129px;}
#plugins_visual .tool_bar .nav_bar .modeItem em.color::after{width: 18px;height: 17px;position: absolute;left: 7px;top: 17px;background-position: -36px -127px;}
#plugins_visual .tool_bar .nav_bar .modeItem em.text::after{width: 18px;height: 18px;position: absolute;left: 7px;top: 16px;background-position: -66px -126px;}
#plugins_visual .tool_bar .nav_bar .modeItem em.products::after{width: 18px;height: 19px;position: absolute;left: 7px;top: 14px;background-position: -96px -126px;}
#plugins_visual .tool_bar .nav_bar .modeItem .char{width: 80px;height: 50px;line-height: 50px;margin-left: 32px;cursor: pointer;text-overflow: ellipsis;white-space: nowrap; overflow: hidden;font-size: 12px;color: #1f2328;}
#plugins_visual .tool_bar .nav_bar .modeItem .del{display: none;width: 22px;height: 100%;position: absolute;right: 0;top: 0;cursor: pointer;}
#plugins_visual .tool_bar .nav_bar .modeItem .del::after{width: 16px;height: 16px;background-image: url(../images/view/icon_view.png);content: '';position: absolute;right: 5px;top: 17px;background-position: -7px -157px;}
#plugins_visual .tool_bar .nav_bar .modeItem:hover .del{display: block;}
#plugins_visual .tool_bar .nav_bar .modeItem .show{display: none;}
#plugins_visual .tool_bar .set_bar{width: 274px;height: calc(100vh - 60px - 57px - 12px);margin-top: 6px;margin-right: 6px;background-color: #fff;float: right;border-radius: 5px;overflow: hidden;}
#plugins_visual .tool_bar .set_bar .set_item{display: none;height: 100%;background-color: #fff;padding: 12px 10px;position: relative;overflow-y: auto;overflow-x: hidden;box-sizing: border-box;}
#plugins_visual .tool_bar .set_bar .set_item::-webkit-scrollbar{width: 5px; background: #fff;border-radius: 5px;}
#plugins_visual .tool_bar .set_bar .set_item::-webkit-scrollbar-thumb{background: rgba(0, 0, 0, 0.1);border-radius: 5px;}
#plugins_visual .tool_bar .set_bar .set_item::-webkit-scrollbar-thumb:hover{background: rgba(0, 0, 0, 0.3);}
#plugins_visual .tool_bar .set_bar .set_item.current{ display:block;}
#plugins_visual .tool_bar .set_bar .set_item .name{ line-height:25px; color:#555555;font-size: 12px;}
#plugins_visual .tool_bar .set_bar .set_item .tips{ padding:0; font-size:12px; color:#7d8d9e;}
#plugins_visual .tool_bar .set_bar .set_item .tips a{ color: var(--primaryColor); text-decoration: underline;  }
#plugins_visual .tool_bar .set_bar .set_item .blank_line{ height:1px; margin:10px 0; background:#f5f5f5;}
#plugins_visual .tool_bar .set_bar .set_item .plugins_select{ width:100%; height:36px; padding:0 10px; background:url(../images/frame/icon_select.png) right center no-repeat; background-size:auto 5px; border:1px solid #ccdced; box-sizing:border-box; border-radius:3px; appearance:none; -moz-appearance:none; -webkit-appearance:none;}
#plugins_visual .tool_bar .set_bar .set_item .plugins_input{ width:100%; height:36px; line-height:36px; padding:0 10px; font-size:12px; color:#404852; border:1px solid #ccdced; border-radius:5px; box-sizing:border-box;}
#plugins_visual .tool_bar .set_bar .set_item .plugins_textarea{ width:100%; height:90px; line-height:20px; padding:10px; font-size:12px; color:#404852; border:1px solid #ccdced; border-radius:5px; box-sizing:border-box;}
#plugins_visual .tool_bar .set_bar .set_item .plugins_input:focus, #plugins_visual .tool_bar .set_bar .set_item .plugins_textarea:focus{border-color:var(--primaryColor); outline:none!important; box-shadow:0 0 5px rgba(7,187,139,0.3);}
#plugins_visual .tool_bar .set_bar .set_item.edit .input_checkbox_box{ display:inline-block; margin-top:5px;}
#plugins_visual .tool_bar .set_bar .set_item.edit .font_size .progress{ width:176px; height:2px; margin-top:17px; background:#eeeeee; float:left; position:relative; margin-left:8px; cursor:pointer;}
#plugins_visual .tool_bar .set_bar .set_item.edit .font_size .progress .bar{ width:0; height:2px; position:absolute; top:0; left:0; background:var(--primaryColor);}
#plugins_visual .tool_bar .set_bar .set_item.edit .font_size .progress .bar i{ width:12px; height:12px; background:var(--primaryColor); position:absolute; top:-5px; right:-5px; display:block; border-radius:6px;}
#plugins_visual .tool_bar .set_bar .set_item.edit .font_size .text{ width:48px; height:36px; line-height:36px; text-align:center; float:right;}
#plugins_visual .tool_bar .set_bar .set_item.edit .font_style{ font-size:0; letter-spacing:-3px;}
#plugins_visual .tool_bar .set_bar .set_item.edit .font_style i{ width:20px; height:20px; margin-right:20px; background-position:center; background-repeat:no-repeat; display:inline-block; cursor:pointer;}
#plugins_visual .tool_bar .set_bar .set_item.edit .font_style i.italic{ background-image:url(../images/view/icon_font_i.png);}
#plugins_visual .tool_bar .set_bar .set_item.edit .font_style i.italic.current{ background-image:url(../images/view/icon_font_i_current.png);}
#plugins_visual .tool_bar .set_bar .set_item.edit .font_style i.underline{ background-image:url(../images/view/icon_font_u.png);}
#plugins_visual .tool_bar .set_bar .set_item.edit .font_style i.underline.current{ background-image:url(../images/view/icon_font_u_current.png);}
#plugins_visual .tool_bar .set_bar .set_item.edit .font_style i.bold{ background-image:url(../images/view/icon_font_b.png);}
#plugins_visual .tool_bar .set_bar .set_item.edit .font_style i.bold.current{ background-image:url(../images/view/icon_font_b_current.png);}
#plugins_visual .tool_bar .set_bar .set_item.edit .font_style .color_style{ width:22px; height:22px; border:2px solid #e5e5e5; float:right; text-indent:999px; border-radius:3px; cursor:pointer;}
#plugins_visual .tool_bar .set_bar .set_item.edit .gallery{ display:inline-block; height:20px; line-height:20px; margin-top:12px; padding-left:16px; font-size:14px; color:var(--primaryColor); position:relative;}
#plugins_visual .tool_bar .set_bar .set_item.edit .gallery:before{ width:8px; height:2px; background:var(--primaryColor); position:absolute; top:9px; left:4px; content:'';}
#plugins_visual .tool_bar .set_bar .set_item.edit .gallery:after{ width:2px; height:8px; background:var(--primaryColor); position:absolute; top:6px; left:7px; content:'';}
#plugins_visual .tool_bar .set_bar .set_item.edit .icon_con{ height:25px; line-height: 25px; padding-left: 25px; background-position:left center; background-repeat:no-repeat;font-size: 12px;}
#plugins_visual .tool_bar .set_bar .set_item.edit .icon_con.icon_web{ background-image:url(../images/view/icon_web.png);}
#plugins_visual .tool_bar .set_bar .set_item.edit .icon_con.icon_mweb{ background-image:url(../images/view/icon_mweb.png);}
#plugins_visual .multi_img{margin-top: 0;}
#plugins_visual .tool_bar .set_bar .set_item.edit .multi_img .img{ width:100%; height:110px;margin-bottom: 3px;}
#plugins_visual .tool_bar .set_bar .set_item.edit .multi_img .img .preview_pic{ width:100%; height:100%;}
#plugins_visual .tool_bar .set_bar .set_item.edit .multi_img .img .upload_btn{ width:100%; height:100%; background-image:url(../images/view/bg_multi_img_big.png);background-size: contain;}
#plugins_visual .tool_bar .set_bar .set_item.edit .multi_img .img .preview_pic>a{ width:100%; height:100%; box-sizing:border-box;border-radius: 3px;}
#plugins_visual .tool_bar .set_bar .set_item.edit .slide_contents .title{padding:8px 0 8px 10px; margin: 0 0 0px; font-size:14px; color:#000000; position:relative;font-weight: bold;border-radius: 5px;}
#plugins_visual .tool_bar .set_bar .set_item.edit .slide_contents .box{position:relative;margin-bottom: 18px; padding:0 10px;}
#plugins_visual .tool_bar .set_bar .set_item.edit .slide_contents_style .box{ position: relative; ;margin-bottom: 18px; padding:0 10px; }
#plugins_visual .tool_bar .set_bar .set_item.edit .slide_contents .box .visual_drop_box{position: absolute;right: 5px;top: -28px;}
#plugins_visual .tool_bar .set_bar .set_item.edit .slide_contents .box .visual_Button_box{overflow:hidden;padding:5px 0px;}
#plugins_visual .tool_bar .set_bar .set_item.edit .slide_contents .box .visual_Button_box .name{float: left;width: calc(100% - 40px);height: auto;line-height: 18px;}
#plugins_visual .tool_bar .set_bar .set_item.edit .slide_contents .box .visual_Button_box .visual_button_btn{float: left;width: 40px;margin:0}
#plugins_visual .tool_bar .set_bar .set_item.edit .loc_box .slide_contents{padding-top: 20px;}
#plugins_visual .tool_bar .set_bar .set_item.edit .loc_box .slide_contents:first-child{padding-top: 8px;}
#plugins_visual .tool_bar .set_bar .set_item.edit .slide_contents:first-child .title{margin-top: 0;}
#plugins_visual .tool_bar .set_bar .set_item.edit .loc_box{ display:none;}
#plugins_visual .tool_bar .set_bar .set_item.edit .loc_box .slide_contents{position: relative;}
#plugins_visual .tool_bar .set_bar .set_item.edit .loc_box .slide_contents.has_title::before{content:'';display:block;width:auto;height:1px;background-color:#f0f0f0;margin-left:-50px;margin-right:-50px;position: absolute;top: 0;left: 0;right: 0;}
#plugins_visual .tool_bar .set_bar .set_item.edit .loc_box .slide_contents.has_title:nth-child(1):before{content:unset;}
#plugins_visual .tool_bar .set_bar .set_item.edit .loc_box .slide_contents.has_title:last-child:after{content:'';display:block;width:auto;height:1px;background-color:#f0f0f0;margin-left:-50px;margin-right:-50px;position: absolute;top: unset;left: 0;right: 0;bottom: -18px;}

#plugins_visual .tool_bar .set_bar .set_item.edit .loc_box .banner_item_list .placeHolderBannerItem{width: 241px;height: 80px;margin-bottom: 10px;background-color: #f5f7f9;border-radius: 5px;}
#plugins_visual .tool_bar .set_bar .set_item.edit .loc_box .banner_item_list .banner_item{display: none; width: 241px;height: 80px;margin-bottom: 10px;background-color: #f5f7f9;border-radius: 5px;position: relative;}
#plugins_visual .tool_bar .set_bar .set_item.edit .loc_box .banner_item_list .banner_item.is_show{display: block;}
#plugins_visual .tool_bar .set_bar .set_item.edit .loc_box .banner_item_list .banner_item .item_move{width: 40px;height: 100%;position: absolute;left: 0;top: 0;cursor: move;}
#plugins_visual .tool_bar .set_bar .set_item.edit .loc_box .banner_item_list .banner_item .item_move::after{content: '';width: 6px;height: 14px;position: absolute;left: 18px;top: 33px;background-image: url(../images/view/icon_view.png);background-position: -102px -98px;}
#plugins_visual .tool_bar .set_bar .set_item.edit .loc_box .banner_item_list .banner_item .item_img{max-width: 120px;height: 50px;margin-top: 15px;margin-left: 38px;border-radius: 5px;overflow: hidden;}
#plugins_visual .tool_bar .set_bar .set_item.edit .loc_box .banner_item_list .banner_item .item_img img{max-width: 100%;max-height: 100%;}
#plugins_visual .tool_bar .set_bar .set_item.edit .loc_box .banner_item_list .banner_item .item_edit{display: none;height: 50px;line-height: 50px;margin-top: 15px;font-size: 12px;color: var(--primaryColor);cursor: pointer;}
#plugins_visual .tool_bar .set_bar .set_item.edit .loc_box .banner_item_list .banner_item .item_del{display: none;width: 30px;height: 50px;margin: 15px 15px 0 10px;cursor: pointer;background: url(../images/view/mode_del_btn.png) center no-repeat;}
#plugins_visual .tool_bar .set_bar .set_item.edit .loc_box .banner_item_list .banner_item:hover .item_edit,
#plugins_visual .tool_bar .set_bar .set_item.edit .loc_box .banner_item_list .banner_item:hover .item_del{display: block;}
#plugins_visual .tool_bar .set_bar .set_item.edit .loc_box .banner_item_add{width: 100%;height: 80px;line-height: 50px;padding: 15px 42px;background-color: #f5f7f9;border-radius: 5px;box-sizing: border-box;position: relative;font-size: 12px;color: var(--primaryColor);cursor: pointer;}
#plugins_visual .tool_bar .set_bar .set_item.edit .loc_box .banner_item_add::after{content: '';width: 12px;height: 12px;position: absolute;left: 15px;top: 34px;background-image: url(../images/view/icon_view.png);background-position: -9px -129px;}
#plugins_visual .tool_bar .set_bar .set_item.edit .loc_box .effect_item_list .effect_item{width: 100%;height: 80px;margin-bottom: 10px;padding: 15px 0 15px 100px;background-color: #f5f7f9;border-radius: 5px;box-sizing: border-box;position: relative;cursor: pointer;}
#plugins_visual .tool_bar .set_bar .set_item.edit .loc_box .effect_item_list .effect_item .item_img{width: 70px;height: 50px;position: absolute;left: 13px;top: 17px;background-image: url(../images/view/icon_effect.png);}
#plugins_visual .tool_bar .set_bar .set_item.edit .loc_box .effect_item_list .effect_item .item_txt{height: 50px;line-height: 50px;font-size: 12px;color: #222;}
#plugins_visual .tool_bar .set_bar .set_item.edit .loc_box .effect_item_list .effect_item.icon_effect0 .item_img{background-position: 0 0;}
#plugins_visual .tool_bar .set_bar .set_item.edit .loc_box .effect_item_list .effect_item.icon_effect1 .item_img{background-position: -70px 0;}
#plugins_visual .tool_bar .set_bar .set_item.edit .loc_box .effect_item_list .effect_item.icon_effect2 .item_img{background-position: -140px 0;}
#plugins_visual .tool_bar .set_bar .set_item.edit .loc_box .effect_item_list .effect_item.icon_effect3 .item_img{background-position: 0 -50px;}
#plugins_visual .tool_bar .set_bar .set_item.edit .loc_box .effect_item_list .effect_item.icon_effect4 .item_img{background-position: -70px -50px;}
#plugins_visual .tool_bar .set_bar .set_item.edit .loc_box .effect_item_list .effect_item.icon_effect5 .item_img{background-position: -140px -50px;}
#plugins_visual .tool_bar .set_bar .set_item.edit .loc_box .effect_item_list .effect_item.current::after{content: '';width: 24px;height: 24px;position: absolute;right: 20px;top: 28px;background-image: url(../images/view/icon_view.png);background-position: -63px -153px;}
#plugins_visual .tool_bar .set_bar .set_item.edit .loc_box .banner_contents{display: none;}
#plugins_visual .tool_bar .set_bar .set_item.edit .loc_box .banner_back{margin-bottom: 25px;padding: 0 15px 18px 15px;font-size: 12px;color: #888;border-bottom: 1px solid #eceef1;cursor: pointer;position: relative;}
#plugins_visual .tool_bar .set_bar .set_item.edit .loc_box .banner_back::after{content: '';width: 7px;height: 12px;position: absolute;left: 0;top: 3px;background-image: url(../images/view/icon_view.png);background-position: -102px -159px;}
#plugins_visual .tool_bar .set_bar .set_item.edit .rows .lang_tab .tab_title{ height:30px; line-height:30px; color:#111; float:left;}
#plugins_visual .tool_bar .set_bar .set_item.edit .rows .lang_tab .tab_box{ float:right;}
#plugins_visual .tool_bar .set_bar .set_item.edit .rows .lang_tab .tab_box .tab_box_row dd{ right:3px; left:auto;}
#plugins_visual .tool_bar .set_bar .set_item.edit .products_contents_box:after{display: block;clear: both;content: '';}
#plugins_visual .tool_bar .set_bar .set_item.edit .products_contents_box .pro_item_list{width: 48px; height: 48px;margin-right: 10px;margin-top: 10px;background: #fff;float: left;border: 1px solid #eee;border-radius: 3px;text-align: center;position: relative;}
#plugins_visual .tool_bar .set_bar .set_item.edit .products_contents_box .pro_item_list i{display: none;width: 16px;height: 16px;background: url(../images/view/iocn_products_del.png) no-repeat;position: absolute;top: -8px;right: -8px;}
#plugins_visual .tool_bar .set_bar .set_item.edit .products_contents_box .pro_item_list img{max-width: 100%;max-height: 100%;}
#plugins_visual .tool_bar .set_bar .set_item.edit .products_contents_box .pro_item_list .soldout{width: 100%;height: 100%;background:rgba(0, 0, 0, 0.5);position: absolute;left: 0;top: 0;color: #fff;font-size: 12px;line-height:48px;}
#plugins_visual .tool_bar .set_bar .set_item.edit .products_category_tab_box{padding: 0 1px;}
#plugins_visual .tool_bar .set_bar .set_item.edit .products_category_tab_list{margin-top: 10px;}
#plugins_visual .tool_bar .set_bar .set_item.edit .products_contents_add{height: 36px;line-height: 36px;margin-top: 12px;text-align: center;font-size: 14px;color:var(--primaryColor);border: 1px solid var(--primaryColor);border-radius: 18px;cursor: pointer;}
#plugins_visual .tool_bar .set_bar .set_item.edit .nav_item{border:1px solid #ccdced;text-align: center;border-radius: 3px;}
#plugins_visual .tool_bar .set_bar .set_item.edit .nav_item .nav_txt_tips{padding: 30px 0 34px 0;background-color: #f4f5f6;border-bottom: 1px solid #ccdced;font-size: 12px;border-radius: 3px 3px 0 0;}
#plugins_visual .tool_bar .set_bar .set_item.edit .nav_item .nav_set_btn{font-size: 0;}
#plugins_visual .tool_bar .set_bar .set_item.edit .nav_item .nav_set_btn a{display: inline-block;width: 50%;height: 40px;line-height: 40px;font-size: 12px;box-sizing: border-box;overflow: hidden;}
#plugins_visual .tool_bar .set_bar .set_item.edit .nav_item .nav_set_btn a[name="edit"]{display: none;}
#plugins_visual .tool_bar .set_bar .set_item.edit .nav_item .nav_set_btn a[name="del"]{display: none;border-left: 1px solid #ccdced;}
#plugins_visual .tool_bar .set_bar .set_item.edit .nav_item .nav_set_btn a:hover{color: var(--primaryColor);}
#plugins_visual .tool_bar .set_bar .set_item.edit .nav_item .nav_set_btn.bind a[name="add"]{display: none;}
#plugins_visual .tool_bar .set_bar .set_item.edit .nav_item .nav_set_btn.bind a[name="edit"],
#plugins_visual .tool_bar .set_bar .set_item.edit .nav_item .nav_set_btn.bind a[name="del"]{display: inline-block;}

#plugins_visual .tool_bar .set_bar .set_item.edit .drop_item{margin-bottom: 12px; border:1px solid #ddd;text-align: center;}
#plugins_visual .tool_bar .set_bar .set_item.edit .drop_item .drop_txt_tips{padding: 30px 0;background-color: #f6f7f7;border-bottom: 1px solid #ddd;}
#plugins_visual .tool_bar .set_bar .set_item.edit .drop_item .drop_set_btn{font-size: 0;}
#plugins_visual .tool_bar .set_bar .set_item.edit .drop_item .drop_set_btn a{display: inline-block;width: 50%;height: 40px;line-height: 40px;font-size: 14px;box-sizing: border-box;overflow: hidden;}
#plugins_visual .tool_bar .set_bar .set_item.edit .drop_item .drop_set_btn a[name="del"]{display: none;}
#plugins_visual .tool_bar .set_bar .set_item.edit .drop_item .drop_set_btn a:hover{color: var(--primaryColor);}
#plugins_visual .tool_bar .set_bar .set_item.edit .drop_item .drop_set_btn.bind a[name="add"]{display: none;}
#plugins_visual .tool_bar .set_bar .set_item.edit .drop_item .drop_set_btn.bind a[name="del"]{display: inline-block;}

#plugins_visual .tool_bar .set_bar .set_item.mode{padding: 0;}
#plugins_visual .tool_bar .set_bar .set_item.mode .mode_cate{margin: 20px 14px 0 14px;position: relative;z-index: 1;}
#plugins_visual .tool_bar .set_bar .set_item.mode .mode_cate .cate_select{position: relative;}
#plugins_visual .tool_bar .set_bar .set_item.mode .mode_cate .cate_title{height: 32px;line-height: 32px;padding: 0 30px 0 20px;background-color: #f3f3f3;border-radius: 5px;cursor: pointer;position: relative;}
#plugins_visual .tool_bar .set_bar .set_item.mode .mode_cate .cate_title::after{content: '';width: 9px;height: 6px;background-image: url(../images/view/icon_view.png);position: absolute;right: 12px;top: 13px;background-position: -41px -162px;transition: all 0.2s ease-in-out;}
#plugins_visual .tool_bar .set_bar .set_item.mode .mode_cate .cate_title.current::after{transform: rotate(-180deg);}
#plugins_visual .tool_bar .set_bar .set_item.mode .mode_cate .cate_option{display: none;width: 100%;height: calc(100vh - 191px);padding-top: 10px;position: absolute;left: 0;top: 22px;opacity: 0;overflow: auto;}
#plugins_visual .tool_bar .set_bar .set_item.mode .mode_cate .cate_option::-webkit-scrollbar{width: 5px;background: #fff;border-radius: 5px;}
#plugins_visual .tool_bar .set_bar .set_item.mode .mode_cate .cate_option::-webkit-scrollbar-thumb{background: rgba(0, 0, 0, 0.1);border-radius: 5px;}
#plugins_visual .tool_bar .set_bar .set_item.mode .mode_cate .cate_option::-webkit-scrollbar-thumb:hover{background: rgba(0, 0, 0, 0.3);}
#plugins_visual .tool_bar .set_bar .set_item.mode .mode_cate .cate_option ul{background-color: #fff;padding: 5px;border-radius: 5px;box-shadow: 0 0 10px 1px rgba(0, 0, 0, 0.2);}
#plugins_visual .tool_bar .set_bar .set_item.mode .mode_cate .cate_option ul::after{display: block;content: '';clear: both;}
#plugins_visual .tool_bar .set_bar .set_item.mode .mode_cate .cate_option ul li{width: calc((100% - (2 * 4px)) / 3);height: 90px;margin-left: 4px;margin-top: 4px;background-color: #f5f7f9;padding-top: 65px;box-sizing: border-box;font-size: 12px;color: #555;text-align: center;float: left;position: relative;cursor: pointer;}
#plugins_visual .tool_bar .set_bar .set_item.mode .mode_cate .cate_option ul li:nth-child(3n+1){margin-left: 0;}
#plugins_visual .tool_bar .set_bar .set_item.mode .mode_cate .cate_option ul li:nth-child(-n+3){margin-top: 0;}
#plugins_visual .tool_bar .set_bar .set_item.mode .mode_cate .cate_option ul li::after{width: 40px;height: 40px;background-image: url(../images/view/icon_cate.png);content: '';position: absolute;left: 18px;top: 18px;}
#plugins_visual .tool_bar .set_bar .set_item.mode .mode_cate .cate_option ul li.cate_option_poster::after{background-position: 0 0;}
#plugins_visual .tool_bar .set_bar .set_item.mode .mode_cate .cate_option ul li.cate_option_service::after{background-position: -40px 0;}
#plugins_visual .tool_bar .set_bar .set_item.mode .mode_cate .cate_option ul li.cate_option_special::after{background-position: -80px 0;}
#plugins_visual .tool_bar .set_bar .set_item.mode .mode_cate .cate_option ul li.cate_option_about::after{background-position: 0 -40px;}
#plugins_visual .tool_bar .set_bar .set_item.mode .mode_cate .cate_option ul li.cate_option_review::after{background-position: -40px -40px;}
#plugins_visual .tool_bar .set_bar .set_item.mode .mode_cate .cate_option ul li.cate_option_team::after{background-position: -80px -40px;}
#plugins_visual .tool_bar .set_bar .set_item.mode .mode_cate .cate_option ul li.cate_option_gallery::after{background-position: 0 -80px;}
#plugins_visual .tool_bar .set_bar .set_item.mode .mode_cate .cate_option ul li.cate_option_description::after{background-position: -40px -80px;}
#plugins_visual .tool_bar .set_bar .set_item.mode .mode_cate .cate_option ul li.cate_option_products::after{background-position: -80px -80px;}
#plugins_visual .tool_bar .set_bar .set_item.mode .mode_cate .cate_option ul li.cate_option_blog::after{background-position: 0 -120px;}
#plugins_visual .tool_bar .set_bar .set_item.mode .mode_cate .cate_option ul li.cate_option_product_reviews::after{ background-position: -40px -120px; }
#plugins_visual .tool_bar .set_bar .set_item.mode .mode_cate .cate_option ul li.cate_option_product_maylike::after{ background-position: -80px -120px; }
#plugins_visual .tool_bar .set_bar .set_item.mode .mode_cate .cate_option ul li.cate_option_video::after{background-position: 0 -160px;}
#plugins_visual .tool_bar .set_bar .set_item.mode .mode_cate .cate_option ul li.cate_option_newsletter::after{background-position: -40px -160px;}
#plugins_visual .tool_bar .set_bar .set_item.mode .mode_cate .cate_option ul li.cate_option_customize::after{ background-position: 42px -160px; }
#plugins_visual .tool_bar .set_bar .set_item.mode .mode_cate .cate_option ul li.cate_option_brands::after{background-position: 0 -200px;}
#plugins_visual .tool_bar .set_bar .set_item.mode .mode_cate .cate_option ul li.cate_option_order_tracking::after{background-position: -40px -200px;}
#plugins_visual .tool_bar .set_bar .set_item.mode .mode_cate_contents{height: calc(100% - 52px);padding: 14px;overflow-y: auto;box-sizing: border-box;}
#plugins_visual .tool_bar .set_bar .set_item.mode .mode_cate_contents::-webkit-scrollbar{width: 5px;background: #fff;border-radius: 5px;}
#plugins_visual .tool_bar .set_bar .set_item.mode .mode_cate_contents::-webkit-scrollbar-thumb{background: rgba(0, 0, 0, 0.1);border-radius: 5px;}
#plugins_visual .tool_bar .set_bar .set_item.mode .mode_cate_contents::-webkit-scrollbar-thumb:hover{background: rgba(0, 0, 0, 0.3);}
#plugins_visual .tool_bar .set_bar .set_item.mode .mode_cate_contents .moduleList{display: none;}
#plugins_visual .tool_bar .set_bar .set_item.mode .mode_cate_contents .moduleList.current{display: block;}
#plugins_visual .tool_bar .set_bar .set_item.mode .mode_cate_contents .moduleList .modeItem{margin-top: 20px;cursor: pointer;position: relative;box-shadow: 0 0 10px 0 rgba(0,0,0,0.1);border: 2px solid transparent;padding: 7px;}
#plugins_visual .tool_bar .set_bar .set_item.mode .mode_cate_contents .moduleList .modeItem .mode_type_desc{color:#000;line-height: 30px;margin-bottom: 3px;}
#plugins_visual .tool_bar .set_bar .set_item.mode .mode_cate_contents .moduleList .modeItem .mode_limit_tips{position:absolute;top:0;left:0;width: 100%;height: 100%;background-color: rgba(0, 0, 0, 0.5);text-align: center;line-height: 1;font-size:12px;color:#fff;}
#plugins_visual .tool_bar .set_bar .set_item.mode .mode_cate_contents .moduleList .modeItem.mode_pack .mode_limit_tips{top:auto;bottom:3%;left:50%;width: 96%;height: 77%;transform: translateX(-50%);}
#plugins_visual .tool_bar .set_bar .set_item.mode .mode_cate_contents .moduleList .modeItem .mode_limit_tips span{width: 100%; position: absolute;left:50%;top:50%;transform: translate(-50%,-50%);}
#plugins_visual .tool_bar .set_bar .set_item.mode .mode_cate_contents .moduleList .modeItem:first-child{margin-top: 0;}
#plugins_visual .tool_bar .set_bar .set_item.mode .mode_cate_contents .moduleList .modeItem:hover{border: 2px solid var(--primaryColor);}
#plugins_visual .tool_bar .set_bar .set_item.mode .mode_cate_contents .moduleList .modeItem:hover::after{width: 24px;height: 24px;content: '';position: absolute;top: -12px;left: -12px;background-image: url(../images/view/icon_view.png);background-position: -63px -153px;}
#plugins_visual .tool_bar .set_bar .set_item.mode .mode_cate_contents .moduleList .modeItem.noIsInPages:hover{cursor: not-allowed;}
#plugins_visual .tool_bar .set_bar .set_item.mode .mode_cate_contents .moduleList .modeItem.noIsInPages:hover::after{display: none;}
#plugins_visual .tool_bar .set_bar .set_item.mode .mode_cate_contents .moduleList .modeItem img{display: block;max-width: 100%;}
#plugins_visual .tool_bar .set_bar .set_item.mode .mode_cate_contents .moduleList .guide_index_box{ margin-top: 0; padding: 0; text-align: left; }
#plugins_visual .tool_bar .set_bar .set_item.mode .mode_cate_contents .moduleList .guide_index_box .guide_con{ position: static; top: 0; transform: translateY(0); }
#plugins_visual .tool_bar .set_bar .set_item.mode .mode_cate_contents .moduleList .guide_index_box .guide_con .global_app_tips{ margin-left: 0; }
#plugins_visual .tool_bar .set_bar .set_item.mode #addModeBtn{border-color: var(--primaryColor);}
#plugins_visual .tool_bar .set_bar .set_item.mode #addModeBtn span{color: var(--primaryColor);}
#plugins_visual .tool_bar .set_bar .set_item.mode .mode_menu #addModeBtn{border: 1px solid var(--primaryColor);}
#plugins_visual .tool_bar .set_bar .set_item.mode .mode_menu #addModeBtn span{color: var(--primaryColor);}
#plugins_visual .tool_bar .set_bar .set_item.mode .mode_menu .modeList .modeItem:hover .move{background-image: url(../images/view/move_icon_uee.png);}
#plugins_visual .tool_bar .set_bar .set_item.mode .mode_cate .mode_cate_box li.current{background: var(--primaryColor);}
#plugins_visual .tool_bar .set_bar .set_item.mode .mode_cate .mode_cate_box li:hover{background: var(--primaryColor);}
#plugins_visual .tool_bar .set_bar .set_item.mode .modeList .modeItem:hover .move{background-image: url(../images/view/move_icon_uee.png);}
#plugins_visual .tool_bar .set_bar .set_item.site .item_site_tab{display: none;}
#plugins_visual .tool_bar .set_bar .set_item.site .item_site_tab.current{display: block;}
#plugins_visual .tool_bar .set_bar .set_item.site .item_site_tab .container_box {position: relative;padding-top: 20px;}
#plugins_visual .tool_bar .set_bar .set_item.site .item_site_tab .container_box:first-child{padding-top: 8px;}
#plugins_visual .tool_bar .set_bar .set_item.site .item_site_tab .container_box .title{padding:8px 0 8px 10px; margin: 0 0 0px; font-size:14px; color:#000000; position:relative;font-weight: bold;border-radius: 5px;}
#plugins_visual .tool_bar .set_bar .set_item.site .item_site_tab .container_box .color_tip{padding:0 0 8px 10px; margin: 0 0 10px; font-size:12px; color:#555555;}
#plugins_visual .tool_bar .set_bar .set_item.site .item_site_tab .container_box:first-child .title{margin-top: 0;}
#plugins_visual .tool_bar .set_bar .set_item.site .item_site_tab .container_box.has_title::before{content:'';display:block;width:auto;height:1px;background-color:#f0f0f0;margin-left:-50px;margin-right:-50px;position:absolute;top:0;left:0;right:0;}
#plugins_visual .tool_bar .set_bar .set_item.site .item_site_tab .container_box.has_title:first-child::before{content: unset;}
#plugins_visual .tool_bar .set_bar .set_item.site .item_site_tab .container_box.has_title:last-child::after{content:'';display:block;width:auto;height:1px;background-color:#f0f0f0;margin-left:-50px;margin-right:-50px;position:absolute;top:unset;left:0;right:0;bottom:0px;}

#plugins_visual .tool_bar .set_bar .set_item.site .item_site_tab .container_box .sub_box{margin-top: 12px;padding: 0px 10px; border-radius: 5px;margin-bottom: 18px;}
#plugins_visual .tool_bar .set_bar .set_item.site .item_site_tab .container_box .sub_box .sub_title{font-size: 12px;color: #555;margin-bottom: 18px;}
#plugins_visual .tool_bar .set_bar .set_item.site .color_rows{height: 22px;line-height: 22px;margin-bottom: 18px;}
#plugins_visual .tool_bar .set_bar .set_item.site .color_rows:last-child{margin-bottom: 0;}
#plugins_visual .tool_bar .set_bar .set_item.site .color_rows span{display: inline-block;margin-left: 12px;font-size: 12px;color: #555;vertical-align: top;}
#plugins_visual .tool_bar .set_bar .set_item.site .color_rows .color_style{ width:46px; height:22px; border:1px solid #e5e5e5; text-indent:999px; border-radius:5px; cursor:pointer;box-sizing: border-box;vertical-align: top;}
#plugins_visual .tool_bar .set_bar .set_item.site .font_rows .name{margin-top: 12px;}
#plugins_visual .tool_bar .set_bar .set_item.site .font_rows:first-child .name{margin-top: 0;}
#plugins_visual .tool_bar .set_bar .set_item.site .switch_rows:first-child{padding-top: 2px;}
#plugins_visual .tool_bar .set_bar .set_item.site .switch_rows {padding-top: 25px ;padding-bottom: 5px;}
#plugins_visual .tool_bar .set_bar .set_item.site .switch_rows .switch_name{float: left;}
#plugins_visual .tool_bar .set_bar .set_item.site .switch_rows .switch_box{float: right;}
#plugins_visual .tool_bar .set_bar .set_item.site .switch_rows .switchery_small{width: 40px;}
#plugins_visual .tool_bar .set_bar .set_item.edit .slide_contents .global_select_box {height: auto;}
#plugins_visual .tool_bar .set_bar .set_item.edit .slide_contents .global_select_box .select_ul li{font-size: 12px;}



#gallery_pop form{ width:1345px;}
#gallery_pop .r_con_form{ height:calc(100vh - 100px - 63px - 71px); max-height:none; background:#f2f3f3;}
#gallery_pop .top_bar{ padding:0 60px;}
#gallery_pop .top_bar .cate{ display:inline-block; line-height:75px; font-size:20px; color:#000;}
#gallery_pop .top_bar .more{ display:inline-block; margin-left:16px; padding-right:20px; font-size:12px; color:#888; position:relative; cursor:pointer;}
#gallery_pop .top_bar .more:hover .more_box{ display:block;}
#gallery_pop .top_bar .more i{ width:10px; height:10px; position:absolute; top:4px; right:4px;}
#gallery_pop .top_bar .more i:after,
#gallery_pop .top_bar .more i:before{ border:5px solid transparent; border-top:5px solid #f2f3f3; width:0; height:0; position:absolute; top:0; left:0; content:'';}
#gallery_pop .top_bar .more i:before{ border-top-color:#cacaca; top:2px;}
#gallery_pop .top_bar .more .more_box{ display:none; width:100px; position:absolute; left:0; top:100%; z-index:1;}
#gallery_pop .top_bar .more .more_box .list{ background:#fff; margin-top:10px; padding:6px 0; border-radius:5px; box-shadow:0 0 10px 0 rgba(0,0,0,0.2);}
#gallery_pop .top_bar .more .more_box .list .item{ height:30px; line-height:30px; font-size:12px; color:#555; text-indent:25px; overflow:hidden;}
#gallery_pop .top_bar .more .more_box .list .item:hover{ background:#f1f8f5;}
#gallery_pop .mid_bar{ height:calc(100vh - 100px - 63px - 71px - 75px); padding:0 60px; overflow:auto;}
#gallery_pop .mid_bar .item{ width:385px; height:255px; margin-bottom:20px; margin-left:26px; float:left; position:relative; text-align:center;}
#gallery_pop .mid_bar .item:hover i{ display:block;}
#gallery_pop .mid_bar .item.current i{ display:block;}
#gallery_pop .mid_bar .item i{ display:none; width:100%; height:100%; background:url(../images/view/icon_check.png) rgba(0,0,0,0.5) center no-repeat; position:absolute; top:0; left:0; cursor:pointer;}
#gallery_pop .mid_bar .item img{ max-width:100%; max-height:100%;}
.pop_products_page_type .r_con_form{padding: 30px 0;}
.pop_products_page_type .p_item{width: 20%;height: 150px;line-height: 150px;margin: 0 5%;float: left;border: 2px solid #ccc;text-align: center;cursor: pointer;}
.pop_products_page_type .p_item:hover{border: 2px solid #0cb083;}
.pop_products_page_type .p_item.current{border: 2px solid #0cb083;}

/* 导航部分 Start */
#nav h1{padding-top: 20px;padding-bottom: 5px;font-size: 22px;color: #000;}
#nav h1 a{line-height: 29px;font-size: 12px;}
#nav .nav_main_box .nav_second_box{display: none;margin-left: 10px;border-left: 1px solid #e0e0e0;}
#nav .nav_main_box .nav_third_box{display: none;margin-left: 30px;border-left: 1px solid #e0e0e0;}
#nav .nav_main_box .nav_four_box{display: none;margin-left: 30px;border-left: 1px solid #e0e0e0;}
#nav .nav_main_box .nav_dra_item, #nav .nav_main_box .nav_dra_second_item{margin-top: 10px;}
#nav .nav_main_box .nav_dra_item.placeHolder, #nav .nav_main_box .nav_dra_second_item.placeHolder{background-color:#e9f2ff; border:1px var(--primaryColor) dashed;border-radius: 5px;}
#nav .nav_main_box .nav_item{display: flex;width: 100%;margin-top: 10px;padding: 18px;background: #fff;border-radius: 5px;box-sizing: border-box;border: 1px solid transparent;position: relative;align-items: center;word-break: break-all;min-height: 62px;}
#nav .nav_main_box .nav_item:hover{background: #e9f2ff;border: 1px solid var(--primaryColor);}
#nav .nav_main_box .nav_item.second, #nav .nav_main_box .nav_item.third, #nav .nav_main_box .nav_item.fourth{width: calc(100% - 20px);margin-left: 20px;}
#nav .nav_main_box .nav_item.second:after, #nav .nav_main_box .nav_item.third:after, #nav .nav_main_box .nav_item.fourth:after{width: 20px;height: 1px;background: #e0e0e0;position: absolute;left: -20px;top: 30px;content: '';}
#nav .nav_main_box .nav_item .nav_ext{width: 20px;height: 20px;background: #49c378;position: absolute;left: -10px;top: 0;bottom: 0;margin:auto;border-radius: 10px;cursor: pointer;z-index: 1;}
#nav .nav_main_box .nav_item .nav_ext:before{width: 11px;height: 1px;background: #fff;position: absolute;left: 50%;top: 50%;content: '';transform: translateX(-50%);z-index: 1;}
#nav .nav_main_box .nav_item .nav_ext.current:after{width: 1px;height: 11px;background: #fff;position: absolute;left: 50%;top: 50%;content: '';transform: translateY(-50%);z-index: 2;}
#nav .nav_main_box .nav_item .nav_myorder{width: 14px;height: 14px;background: url(../images/view/icon_nav.png) no-repeat;background-position: 0 0;cursor: move;}
#nav .nav_main_box .nav_item .nav_myorder:hover{background-position: 0 -20px;}
#nav .nav_main_box .nav_item .nav_name{font-size: 14px;color: #000; width: 75%;flex: 1;padding-right: 20px;padding-left: 5px;word-break: break-all;}
#nav .nav_main_box .nav_item .nav_name span{display: inline-block;height: 24px;line-height: 24px;padding: 0 13px;margin-left: 9px;color: var(--GlobalTitleColor);background-color: #eaf7ff;font-size: 12px;border-radius: 3px;}
#nav .nav_main_box .nav_item .nav_set{width: 20px;height: 20px;margin-right: 15px;cursor: pointer;order: 1;}
#nav .nav_main_box .nav_item .nav_set::after{content: "\e6d3";font-size: 15px;font-family: "iconfont" !important;color:#555555;}
#nav .nav_main_box .nav_item .nav_set:hover::after{color:#4BA0FC;}
#nav .nav_main_box .nav_item .nav_hidden{width: 20px;height: 20px;margin-right: 15px;cursor: pointer;order: 1;}
#nav .nav_main_box .nav_item .nav_hidden::after{content: "\e609";font-size: 20px;font-family: "iconfont" !important;color:#555555;}
#nav .nav_main_box .nav_item .nav_hidden.yes::after{content: "\e625";}
#nav .nav_main_box .nav_item .nav_del{order: 2;}
#nav .nav_main_box .nav_item .nav_del:after{content: "\e60b" !important; font-size: 16px;font-family:"iconfont" !important}
#nav .nav_main_box .nav_item .nav_preview{order: 1;}
#nav .nav_main_box .nav_item .nav_preview:after{content: "\e609" !important; font-size: 16px;}
#nav .nav_main_box .nav_item .nav_preview_hid{order: 1;}
#nav .nav_main_box .nav_item .nav_preview_hid:after{content: "\e625" !important; font-size: 16px;}
#nav .nav_main_box .nav_item .nav_del:hover{background-position: -40px -20px;}
#nav .nav_main_box .nav_item .nav_add_sub{line-height: 20px;margin-right: 15px;font-size: 12px;cursor: pointer;color: #555;}
#nav .nav_main_box .nav_item .nav_add_sub:hover{color: var(--primaryColor);}
#nav .menu_add{padding: 0 20px;}
#nav .rows .adpic_row:hover{ background:#f1f8f5;}

#nav .nav_btn{height: 30px;line-height: 30px;background-color: #ffffff;padding:0 15px;margin-left: 12px; border-radius: 15px; color:#404852;text-align: center;}

.box_create_nav .box_middle { padding: 0; }
.box_create_nav .box_middle .title { margin: 0; padding: 13px 15px; }
.box_create_nav .box_middle .content { height: 75vh; }

#create_nav_form { border-bottom-left-radius: 5px; border-bottom-right-radius: 5px; overflow: hidden; height: 100%; background-color: #edf0f2; }
#create_nav_form .nav_container { display: flex; height: calc(100% - 88px); padding: 16px; }
#create_nav_form .left_menu { border-radius: 5px; overflow: hidden; width: 300px; background-color: #fff; }
#create_nav_form .left_menu .menu_head { border-bottom: 1px solid #ededed; padding: 0 12px; font-size: 16px; line-height: 59px; }
#create_nav_form .left_menu .menu_list { overflow-y: auto; height: calc(100% - 60px); }
#create_nav_form .left_menu .menu_list::-webkit-scrollbar { width: 5px; border-radius: 5px; background-color: #fff; }
#create_nav_form .left_menu .menu_list::-webkit-scrollbar-thumb { border-radius: 5px; background-color: rgba(0, 0, 0, .1); }
#create_nav_form .left_menu .menu_list::-webkit-scrollbar-thumb:hover { background-color: rgba(0, 0, 0, .3); }
#create_nav_form .left_menu .menu_item { height: 44px; padding: 0 12px; line-height: 44px; }
#create_nav_form .left_menu .menu_item .collapse { position: relative; border: 1px solid #b3b3b3; border-radius: 3px; overflow: hidden; float: left; width: 15px; height: 15px; margin-top: 13px; margin-right: 12px; cursor: pointer; }
#create_nav_form .left_menu .menu_item .collapse::before { content: ''; position: absolute; top: 7px; left: 4px; z-index: 1; width: 7px; height: 1px; background-color: #b3b3b3; }
#create_nav_form .left_menu .menu_item .collapse::after { content: ''; position: absolute; top: 4px; left: 7px; z-index: 2; width: 1px; height: 7px; background-color: #b3b3b3; }
#create_nav_form .left_menu .menu_item .collapse.spread::after { display: none; }
#create_nav_form .left_menu .menu_item .collapse.disabled { visibility: hidden; }
#create_nav_form .left_menu .menu_item .name { overflow: hidden; float: left; width: calc(100% - 66px); white-space: nowrap; -webkit-text-overflow: ellipsis; text-overflow: ellipsis; }
#create_nav_form .left_menu .menu_item .btn_create_menu { float: right; margin-left: 6px; color: var(--primaryColor); white-space: nowrap; }
#create_nav_form .left_menu .menu_item:hover { background-color: #F5F7FA; }
#create_nav_form .left_menu .menu_sub_list { display: none; margin-left: 10px; }
#create_nav_form .left_menu .menu_sub_list.loading { padding-bottom: 40px; background: url(../images/frame/loading_oth.gif) no-repeat center bottom; }
#create_nav_form .left_menu .btn_menu_more { display: block; position: relative; border-top: 1px solid #edf0f5; height: 28px; padding: 5px 10px 0; text-align: center; text-decoration: none; cursor: pointer; line-height: 28px; }
#create_nav_form .right_content { flex: 1; margin-left: 24px; }
#create_nav_form .right_content .nav_table { height: 100%; }
#create_nav_form .right_content .nav_head { display: flex; align-items: center; justify-content: space-between; padding: 0 20px 16px; }
#create_nav_form .right_content .nav_head .nav_th { position: relative; font-size: 16px; font-weight: 600; line-height: 37px; }
#create_nav_form .right_content .nav_head .nav_th>em { position: absolute; bottom: -5px; display: block; font-size: 12px; color: #999; line-height: 1; }
#create_nav_form .right_content .nav_head .nav_th:nth-child(1),
#create_nav_form .right_content .nav_head .nav_th:nth-child(2),
#create_nav_form .right_content .nav_head .nav_th:nth-child(3) { width: 25%; }
#create_nav_form .right_content .nav_head .nav_th:nth-child(4) { width: 9%; }
#create_nav_form .right_content .nav_head .nav_th:nth-child(5) { width: 12%; }
#create_nav_form .right_content .nav_head .nav_th:nth-child(6) { width: 4%; }
#create_nav_form .right_content .nav_body { height: calc(100% - 55px); overflow-y: auto; }
#create_nav_form .right_content .nav_body::-webkit-scrollbar { width: 5px; border-radius: 5px; background-color: #edf0f2; }
#create_nav_form .right_content .nav_body::-webkit-scrollbar-thumb { border-radius: 5px; background-color: rgba(0, 0, 0, .1); }
#create_nav_form .right_content .nav_body::-webkit-scrollbar-thumb:hover { background-color: rgba(0, 0, 0, .3); }
#create_nav_form .right_content .nav_item { display: flex; align-items: center; justify-content: space-between; border-radius: 5px; overflow: hidden; margin-top: 8px; padding: 18px; background-color: #fff; }
#create_nav_form .right_content .nav_item .item_cell>.box_input,
#create_nav_form .right_content .nav_item .item_cell>.box_select { width: 75%; }
#create_nav_form .right_content .nav_item .item_cell .btn_menu_del { display: block; width: 20px; height: 20px; background: url(../images/view/icon_nav.png) no-repeat; background-position: -40px 0; }
#create_nav_form .right_content .nav_item .item_cell .btn_menu_del:hover { background-position: -40px -20px; }
#create_nav_form .right_content .nav_item .item_cell:nth-child(1),
#create_nav_form .right_content .nav_item .item_cell:nth-child(2),
#create_nav_form .right_content .nav_item .item_cell:nth-child(3) { width: 23%; padding-right: 2%; }
#create_nav_form .right_content .nav_item .item_cell:nth-child(4) { width: 9%; }
#create_nav_form .right_content .nav_item .item_cell:nth-child(5) { width: 12%; }
#create_nav_form .right_content .nav_item .item_cell:nth-child(6) { width: 4%; }
#create_nav_form .right_content .nav_item .item_link>span { font-size: 12px; }
#create_nav_form .right_content .nav_item .item_link.hidden { visibility: hidden; }
#create_nav_form .right_content .nav_item .item_icon { position: relative; }
#create_nav_form .right_content .nav_item .item_icon .fixed_icon { width: 36px; height: 36px; }
#create_nav_form .right_content .nav_item .item_icon .multi_img { position: absolute; top: -7px; left: 0; margin-top: 0; }
#create_nav_form .right_content .nav_item .item_icon .multi_img .img { margin: 0; }
#create_nav_form .right_content .nav_item .item_icon .multi_img .img .preview_pic { width: 50px; height: 50px; }
#create_nav_form .right_content .nav_item .item_icon .multi_img .img .upload_btn { width: 50px; height: 50px; background-size: 100%; }
#create_nav_form .right_content .nav_item .item_icon .multi_img .img .preview_pic>a { width: 44px; height: 44px; padding: 2px; }
#create_nav_form .right_content .nav_item .item_icon .multi_img .img .pic_btn>a.del { width: 18px; height: 18px; }
#create_nav_form .right_content .nav_item .item_icon .multi_img .img .pic_btn>a.del>i { background-size: 100%; }
#create_nav_form .right_content .nav_item:first-child { margin-top: 0; }

#create_nav_form .right_content .not_icon .nav_head .nav_th:nth-child(1),
#create_nav_form .right_content .not_icon .nav_head .nav_th:nth-child(2),
#create_nav_form .right_content .not_icon .nav_head .nav_th:nth-child(3) { width: 28%; }
#create_nav_form .right_content .not_icon .nav_head .nav_th:nth-child(4) { display: none; width: 0%; }
#create_nav_form .right_content .not_icon .nav_item .item_cell:nth-child(1),
#create_nav_form .right_content .not_icon .nav_item .item_cell:nth-child(2),
#create_nav_form .right_content .not_icon .nav_item .item_cell:nth-child(3) { width: 26%; padding-right: 2%; }
#create_nav_form .right_content .not_icon .nav_item .item_cell:nth-child(4) { display: none; width: 0%; }

#create_nav_form .right_content .not_icon_sub .nav_head .nav_th:nth-child(1),
#create_nav_form .right_content .not_icon_sub .nav_head .nav_th:nth-child(2),
#create_nav_form .right_content .not_icon_sub .nav_head .nav_th:nth-child(3) { width: 32%; }
#create_nav_form .right_content .not_icon_sub .nav_head .nav_th:nth-child(4),
#create_nav_form .right_content .not_icon_sub .nav_head .nav_th:nth-child(5) { display: none; width: 0%; }
#create_nav_form .right_content .not_icon_sub .nav_item .item_cell:nth-child(1),
#create_nav_form .right_content .not_icon_sub .nav_item .item_cell:nth-child(2),
#create_nav_form .right_content .not_icon_sub .nav_item .item_cell:nth-child(3) { width: 30%; padding-right: 2%; }
#create_nav_form .right_content .not_icon_sub .nav_item .item_cell:nth-child(4),
#create_nav_form .right_content .not_icon_sub .nav_item .item_cell:nth-child(5) { display: none; width: 0%; }

#create_nav_form .button { padding: 13px 20px; text-align: right; background-color: #fff; }
#create_nav_form .button .btn_disabled { border-color: #f2f2f2; }
#create_nav_form .no_data { margin: 22% 0 0; padding: 100px 0; font-size: 14px; color: #7d8d9e; background: url(../images/mta/icon_no_data_gray.png) no-repeat center top; background-size: 12%; }

/* 导航部分 End */

/* 选择模块 Start */
.img_center{ text-align:center;}
.img_center img{ vertical-align:middle;}
.img_center span{height:100%;display:inline-block; vertical-align:middle;}
#modeSelect{overflow-y: auto;background: #fff;margin:auto;position: fixed;top: 0;right: 0;bottom: 0;left: 0;z-index: 10001;text-align: center;opacity: 0;pointer-events:none;}
#modeSelect.hover{opacity: 1;pointer-events:visible;}
#modeSelect .close{width: 44px;height: 44px;background: url(../images/frame/icon_win_close.png) no-repeat center center;position: fixed;top: 16px;right: 20px;z-index: 10003;}
#modeSelect .cont{width: 100%;height: 100%;padding: 3vh;box-sizing:border-box;position: relative;}
#modeSelect .cont .title{text-align: center;font-size: 36px;color: #555555;margin-bottom: 15px;}
#modeSelect .cont .typeList{text-align: center;font-size: 0;}
#modeSelect .cont .typeList>a{display: inline-block;font-size: 16px;color: #888888;height: 30px;line-height: 30px;border-radius: 15px;border:1px solid transparent; padding: 0 30px;margin-left: 10px;}
#modeSelect .cont .typeList>a:first-child{margin-left: 0;}
#modeSelect .cont .typeList>a.hover,#modeSelect .cont .typeList>a:hover{border-color: var(--primaryColor);color: var(--primaryColor);}
#modeSelect .cont .moduleBox{width:84.9vw; margin: 60px auto 0;}
#modeSelect .cont .moduleBox .moduleList{display: none;height: 698px;overflow: hidden;}
#modeSelect .cont .moduleBox .moduleList.hover{display: block;}
#modeSelect .cont .moduleBox .moduleList .modeItem{float: left;width: 26.3vw;height: 17vw;margin:0 2.6vw 3.1vw 0;border:1px solid #e8e8e8;cursor: pointer;position: relative;}
#modeSelect .cont .moduleBox .moduleList .modeItem:nth-child(3n){margin-right: 0;}
#modeSelect .cont .moduleBox .moduleList .modeItem:after{position: absolute;content: '';left: 0;right: 0;top: 0;bottom: 0;background: rgba(0,0,0,0.5);opacity: 0;transition:all 0.4s ease-out;-moz-transition:all 0.4s ease-out;-ms-transition:all 0.4s ease-out;-o-transition:all 0.4s ease-out;-webkit-transition:all 0.4s ease-out;pointer-events:none;z-index: 1;}
#modeSelect .cont .moduleBox .moduleList .modeItem:before{position: absolute;content: '';width: 36px;height: 36px;background-color: rgba(0,0,0,0.2);border:1px solid rgba(255,255,255,0.6);background-image: url(../images/view/module_select_icon.png); background-repeat: no-repeat;background-position: center center;display: none;left: 50%;top: 50%;transform:translate(-50%,-50%);border-radius: 20px;z-index: 2;}
#modeSelect .cont .moduleBox .moduleList .modeItem:hover:before,#modeSelect .cont .moduleBox .moduleList .modeItem.hover:before{display: block;}
#modeSelect .cont .moduleBox .moduleList .modeItem.hover:before{background-color:var(--primaryColor)}
#modeSelect .cont .moduleBox .moduleList .modeItem:hover:after,#modeSelect .cont .moduleBox .moduleList .modeItem.hover:after{opacity: 1}
#modeSelect .cont .moduleBox .moduleList .modeItem .img{overflow: hidden;width: 100%;height: 100%;}
#modeSelect .cont .moduleBox .moduleList .modeItem .img img{max-width: 100%;max-height: 100%;}
/* 选择模块 End */

@keyframes SlideUp{
    0%{transform:translateY(30px); opacity:0;}
    50%{transform:translateY(15px); opacity:.5;}
    100%{transform:translateY(0); opacity:1;}
}

@keyframes btnMove{
	0%{transform:translateY(0); opacity:0;}
	50%{transform:translateY(10px); opacity:.5;}
	100%{transform:translateY(20px); opacity:1;}
}

/* 页面 start */
#page .r_con_table{min-width:93%;}
#page .inside_container.center_container_1000{margin: 0 auto;}
#page .inside_container>h1{padding: 0;}
#page_inside .myorder_rows .box_select{width:60px;}
#page_inside .global_container{overflow: visible;}
#page_inside .mobile_description.hide{ display: none; }
/* 页面 end 

/*域名绑定 start*/
.domain_binding_index .inside_table{padding: 24px;min-height: 500px;}
.domain_binding_index .big_title{height: 34px;line-height: 34px;font-size: 24px;margin-bottom: 24px;}
.domain_binding_index .big_title .g_btn { margin-left: 15px; }
.domain_binding_index .add{font-size: 12px;height: 34px;padding: 5px 20px;}
.domain_binding_index .r_con_table tbody td{padding-top: 30px;padding-bottom: 30px;}
.domain_binding_index .r_con_table .operation a{color: var(--GlobalMainColor);display: inline-block;margin-left: 24px;font-size: 14px;}
#domain_binding .center_container_1000{margin-top: 0;}
#domain_binding .return_title{margin: 0;}
#domain_binding .r_con_table span.status{padding: 0 20px;height: 28px;line-height: 28px;border-radius: 28px;}
#domain_binding .r_con_table span.status.connect{background-color: #F5F7FA;color: var(--GlobalMainColor);}
#domain_binding .r_con_table span.status.pend{background-color: #fbeded;color: #f35958;}
#domain_binding .r_con_table tr td .non_domain { border-radius: 4px; height: 24px; margin: 4px 8px 0 0; padding: 0 8px; float: left; font-size: 12px; background-color: #eaf7ff; line-height: 24px; }
#domain_binding .domain_form .rows{margin:10px 30px;}
#domain_binding .domain_form .rows.center{text-align: center;}
#domain_binding .domain_form .rows label.title{font-size: 30px;color: #000;text-align: center;line-height: 60px;}
#domain_binding .domain_form .rows .subtitle{line-height: 24px;margin-bottom: 13px;font-size: 14px;color: #7e8da0;}
#domain_binding .domain_form .rows .box_input{width: 70%;height: 48px;line-height: 48px;margin:0px auto;text-align: center;font-size: 16px;}
#domain_binding .domain_form .rows .btn_global{height: 34px;line-height: 34px;padding: 0 48px;font-size: 16px;}
#domain_binding .domain_form .bind_rows label{font-size: 20px;line-height: 30px;}
#domain_binding .domain_form .bind_rows .input{padding: 10px 0;line-height: 20px;font-size: 14px;color: #7e8da0;margin-bottom: 5px;}
#domain_binding .domain_form .rows .input.tips{display: none;line-height: 20px;padding: 20px 5px 5px;text-align: center;font-size: 16px;}
#domain_binding .domain_form .global_app_tips{color:#f68f44;font-size: 14px;margin-top: 0;}
#domain_binding .binding_inside_table.binding_add{padding: 104px 0 150px;}
#domain_binding .binding_inside_table.binding_ver{padding: 24px;min-height: 500px;}
.fixed_btn_submit .btn_prev{margin-left: 0;}

#domain_binding .btn_copy{display: inline-block;padding-left: 10px;color: var(--GlobalMainColor);font-size: 14px;}
#domain_binding .btn_copy::before{content: "\e618";font-family: "iconfont" !important;padding-right: 5px;}
#domain_binding .step_box{display: flex;justify-content: center;padding: 30px 0;}
#domain_binding .step_box .list{width: 330px;text-align: center;}
#domain_binding .step_box .list .num{position: relative;}
#domain_binding .step_box .list .num::before{content: '';position: absolute;top: 0;right: 50%;bottom: 0;left: 0;margin:auto;height: 1px;background-color: #d7d7d7;z-index: 0;}
#domain_binding .step_box .list .num::after{content: '';position: absolute;top: 0;right: 0;bottom: 0;left: 50%;margin:auto;height: 1px;background-color: #d7d7d7;z-index: 0;}
#domain_binding .step_box .list .num span{display: block;width: 26px;height: 26px;line-height: 26px;border:1px solid #c5ccd3;color: #c5ccd3;border-radius: 26px;font-size: 14px;background-color: #fff;margin: auto;position: relative;z-index: 1;}
#domain_binding .step_box .list .tit{padding: 5px 0;line-height: 26px;font-size: 14px;color: var(--GlobalTextColor);}
#domain_binding .step_box .list:first-child .num::before{display: none;}
#domain_binding .step_box .list:last-child .num::after{display: none;}
#domain_binding .step_box .current .num span{color: var(--GlobalMainColor);border-color: var(--GlobalMainColor);}
#domain_binding .step_box .current .tit{color: var(--GlobalMainColor);}

.fixed_change_domain .domain_list { padding: 10px 0; }
.fixed_change_domain .domain_list .input_radio_box { padding: 8px 0; color: #1f2328; }
/*域名绑定 end*/

/*引导部分样式*/
#global_guide_box.free_mode{position: fixed;top: 0;right: 0;bottom: 0;left: 0;z-index: 1101;background: rgba(0, 0, 0, 0.7);}
#global_guide_box.free_mode span{height: 30px;line-height: 30px;margin: 0 auto 65px;position: absolute;right: 0;bottom: 50%;left: 0;text-align: center;font-size: 24px;color: #fff;}
#global_guide_box.free_mode .gui_next{width: 160px;line-height: 42px;background: var(--primaryColor);text-align: center;font-size: 16px;color: #fefefe;border-radius: 4px;cursor: pointer;position: absolute;right: 0;bottom: 36%;left: 0;margin:0 auto;}
#global_guide_box.free_mode em{width: 80px;height: 80px;background: #0cb083 url(../images/frame/icon_mouse.png) no-repeat center center;border-radius: 100%;margin:auto;position: absolute;top: 0;right: 0;bottom: 0;left: 0;-webkit-animation: cd-pulse 2s infinite;-moz-animation: cd-pulse 2s infinite;animation: cd-pulse 2s infinite;-webkit-animation-delay: .5s;-moz-animation-delay: .5s;animation-delay: .5s;}
#global_guide_box.free_mode.s1{top: 81px;}
#global_guide_box.free_mode .dashed_box{position: absolute;left: 0;top: -81px;width: 165px;height: 75px;border:3px dashed #000;transition: all 0s;}
#global_guide_box.free_mode .dashed_box:after{position: absolute;left: 168px;top: -3px;height: 81px;width: 99999px;background: rgba(0, 0, 0, 0.7);content: '';}
#global_guide_box.free_mode.s1 span{bottom:auto;top: 40px;left: 35px;margin:0;text-align: left;}
#global_guide_box.free_mode.s1 .gui_next{margin:0;left: 190px;top: 100px;bottom: auto;}
#global_guide_box.free_mode .step_1{opacity: 0;visibility: hidden;}
#global_guide_box.free_mode.s1 .step_1{opacity: 1;visibility: visible;}
#global_guide_box.free_mode.s1 .step_0{opacity: 0;visibility: hidden;}
#global_guide_box.free_mode.s2 .step_1,#global_guide_box.free_mode.s2 .step_0{opacity: 0;visibility: hidden;}
#global_guide_box.free_mode.hide{display: none;}
#global_guide_box.free_mode .step_2{position: absolute;left: 0;right: 0;top: 0;bottom: 0;opacity: 0;visibility: hidden;}
#global_guide_box.free_mode.s2 .step_2{opacity: 1;visibility: visible;}
#global_guide_box.free_mode .step_2 img{position: absolute;left: 50%;top: 50%;transform:translate(-50%,-50%);cursor: pointer;}


@-webkit-keyframes cd-pulse {
	0% { box-shadow: 0 0 0 0 #0cb083; }
	100% { box-shadow: 0 0 0 45px transparent; }
}
@-moz-keyframes cd-pulse {
	0% { box-shadow: 0 0 0 0 #0cb083; }
	100% { box-shadow: 0 0 0 45px transparent; }
}
@keyframes cd-pulse {
	0% { box-shadow: 0 0 0 0 #0cb083; }
	100% { box-shadow: 0 0 0 45px transparent; }
}

.plugin_edit_box{background: var(--primaryColor)}
.no_data{line-height:25px; margin:75px 0; padding:80px 0; text-align:center; font-size: 12px; color:#888888; background:url(../images/mta/icon_no_data.png) no-repeat center top;}
.fixed_no_data{line-height:25px; padding:80px 0; text-align:center; font-size: 12px; color:#888888; background:url(../images/view/visual_no_data.png) no-repeat center top; position: absolute; top: 30%; left: 50%; transform: translateX(-50%);}
.fixed_loading{display: none;}
.products_choice{visibility: visible;z-index: 10001;}
.photo_choice{visibility: visible;z-index: 10001;}

.view_blanktip_box{position: fixed;top:60px;right:0;bottom: 0;left: 0;background-color: rgba(0, 0, 0, 0.5);}
.fial_box{position: absolute;left: 50%;top: 50%;width: 540px;height: 310px; transform: translate(-50%,-50%);background-color: #fff; text-align: center;z-index: 10001;border-radius: 5px;}
.fial_box .fial_item{position: absolute;left: 0;top: 50%;width: 100%; transform: translateY(-50%);}
.fial_box .fail_title{line-height: 20px;margin-bottom: 30px; color:#1f2328;font-size: 18px;}
.fial_box .fail_btn_retry{ background-color: #0baf4d;color:#fff;}
.fial_box .btn_global{min-width: 104px; height: 36px;line-height: 36px; margin:0 6px;}
.fial_box .fail_btn_close{border:1px solid #ced4da;box-sizing: border-box; background-color: #fff;color:#000000;}

#nav_edit_form .rows label span{font-size: 12px;color:#7e8da0}
#nav_edit_form .box_type_menu{display: block;margin-top:6px;}
#nav_edit_form .box_type_menu span{display: block;width: 100%;margin-bottom: 12px;box-sizing: border-box;}
#nav_edit_form .erro_tips{color:red;margin-top: 6px;}

.global_app_tips{display: block; margin-top: 20px; color:#f68f44}
.global_app_tips.hide{display: none;}
.global_app_tips em{margin-top: 1px;display: inline-block;vertical-align: top;}
.global_app_tips .tips{line-height: 20px; padding:0;display: inline-block;vertical-align: top;}

#visual .guide_index_box{padding: 2% 0;position: relative;text-align: right;margin-bottom: 15px;}
#visual .guide_index_box img{max-width: 50%;}
#visual .guide_index_box .guide_con{text-align: left;}
#visual .guide_index_box .guide_con .tit{line-height: 58px;font-size: 40px;color: #000;}
#visual .guide_index_box .guide_con .desc{line-height: 30px;margin-top: 8px;margin-bottom: 54px;font-size: 16px;color: #555;}
#visual .guide_index_box .guide_con .btn{display: inline-block;height: 50px;line-height: 50px;padding: 0 35px;border-radius: 50px;font-size: 18px;color: #fff;vertical-align: middle;}
#visual .guide_index_box .guide_con .btn.disabled{ background-color: #ccc; border-color: #ccc; cursor: no-drop;  }
#visual .guide_index_box .guide_con .global_app_tips{ margin-top: 0; margin-left: 10px; display: inline-block; vertical-align: middle; color: #f68f44;  }

#visual .gloabl_visual_tips{ position: relative; padding: 12px 21px 12px 36px; line-height: 20px; border:1px solid #ffdec6; border-radius: 5px; background-color: #fff9f5; box-sizing: border-box; font-size: 12px; color: #555555; }
#visual .gloabl_visual_tips em{ display: inline-block;width: 16px;height: 16px;margin-right: 6px;background: url(../images/frame/app_tips_icon.png) no-repeat center / contain; vertical-align: bottom; position: absolute; top: 13px; left: 11px; }

#page_inside .article_menu{min-height:35px; margin-bottom:15px; margin-left:-5px;}
#page_inside .article_menu_item{display:inline-block; vertical-align:top; position:relative;}
#page_inside .article_menu_item>button{display:block; min-height:35px; line-height:35px; background-color:transparent; border:0; padding:0 5px; color:var(--primaryColor);}
#page_inside .article_menu_item>button>em{display:inline-block; vertical-align:top; width:15px; height:15px; background-repeat:no-repeat; margin-top:10px; margin-right:6px;}
#page_inside .article_menu_item>button>i{display:inline-block; vertical-align:top; width:8px; height:5px; background:url(../images/products/icon_menu_arrow.png) no-repeat; margin-top:15px; margin-left:8px; transition:all .2s; -webkit-transition:all .2s;}
#page_inside .article_menu_item>button.btn_menu_copy>em{background-image:url(../images/products/icon_menu_copy.png);}
#page_inside .article_menu_item>button.btn_menu_view>em{width:18px; background-image:url(../images/products/icon_menu_view.png);}
#page_inside .article_menu_item>button.btn_menu_app>em{background-image:url(../images/products/icon_menu_app.png);}
#page_inside .article_menu_item>button.btn_menu_app:hover>i{transform:rotate(180deg); -webkit-transform:rotate(180deg);}
#page_inside .article_menu_item .box_my_app{display:none; background:transparent; position:absolute; left:18px; z-index:1; opacity:0;}
#page_inside .article_menu_item .drop_down{background-color:#fff; border-radius:5px; padding:7px 0;}
#page_inside .article_menu_item .drop_down .item{display:block; height:32px; line-height:32px; padding:0 18px; text-decoration:none; font-size:12px; color:#666; cursor:pointer;}


#www_redirect .global_container{padding:30px}
#www_redirect .content_top{overflow: hidden;}
#www_redirect .content_top .content_title{float: left;font-size: 20px;color:#1f2328}
#www_redirect .switchery{margin:3px 0 0 0;}
#www_redirect .content_info{margin:30px 0}
#www_redirect .content_info .content_item{background-color: #f7f9fb;padding:30px 20px;margin-bottom: 18px;}
#www_redirect .content_info .content_item_title{float: left; color:#1f2328;font-size: 14px;}

#visual .container_first{margin-top: 12px;}
#visual .container_version{width: 1200px;max-width: 100%;margin: auto;position: relative;margin-top: 15px;border-radius: 5px;background-color: transparent;margin-top: 15px;overflow: hidden;}
#visual .container_version .box_version_top{margin-bottom: 20px;}
#visual .container_version .box_version_top .visual_version_title{font-size: 20px;color:#1f2328;margin-top:4px;line-height: 20px;}
#visual .container_version .box_version_top .visual_version_span{padding:0 15px; height: 20px;line-height: 20px;background-color: var(--GlobalSyncColor);border-radius: 10px;margin-left: 10px;margin-top:4px; color:#fff; text-align: center;}
#visual .container_version .box_version_top .set_version_btn{height: 28px;line-height: 28px;background-color: var(--GlobalBtnMainBgColor);border-radius: 16px;color:#fff; text-align: center;cursor: pointer;}
#visual .container_version .box_version_top .set_version_btn a{padding: 0 18px;color:#fff;display: block;}
#visual .container_version .box_version_banner .go_to_new{display: block;width: 100%;position: relative;}
#visual .container_version .box_version_banner .banner_info{position: absolute;width: 100%; top:50%;left: 50%;transform: translate(-50%,-50%);text-align: center;}
#visual .container_version .box_version_banner .banner_info .banner_info_title{display: inline-block;line-height: 38px;font-size: 24px;color:#ffffff;vertical-align:top; }
#visual .container_version .box_version_banner .banner_info .banner_info_btn{display: inline-block;padding:0 32px;margin-left: 18px; height: 38px;line-height: 38px;background-color: #ff9f15;border-radius: 19px; vertical-align: center;font-size: 16px;color:#fff;text-align: center;}
#visual .container{ max-width: 100%; }

#url_redirect  #error_form .rows{ padding: 0 24px; margin-top: 10px; }
.fixed_set_www_box .switchery{margin:3px 0 0 0;}
.fixed_set_www_box .content_info{margin:30px 0}
.fixed_set_www_box .content_info .content_item{background-color: #f7f9fb;padding:30px 20px;margin-bottom: 18px;}
.fixed_set_www_box .content_info .content_item_title{float: left; color:#1f2328;font-size: 14px;}

#fixed_right.loading>div{ opacity: 0; }

#url_redirect .global_app_tips{margin: 0 0 0 15px;padding: 6px 9px;color: #555555;}
#url_redirect .top_title { padding-bottom: 10px; font-size: 24px; color: var(--GlobalTitleColor); }
#url_redirect_form .rows { padding: 0 24px; margin-top: 10px; margin-bottom: 20px; }
#url_redirect_form .table_item { border-radius: 3px; margin-top: 15px; padding: 25px 20px; background-color: #f8f9fb; }
#url_redirect_form .table_item .item_head { display: flex; justify-content: space-between; align-items: center;}
#url_redirect_form .table_item .item_content {font-size: 14px; color: #7f8c9f; }
#url_redirect_form .table_item .item_content>strong { display: flex; padding-right: 40px; color: #000; line-height: 28px; align-items: center;}
#url_redirect_form .table_item .item_content>span { display: block; margin-top: 8px; font-size: 12px; line-height: 19px; }
#url_redirect_form .table_item .item_content>span>a { text-decoration: underline; color: var(--primaryColor); }
#url_redirect_form .table_item .item_body { border-radius: 3px; margin-top: 15px; padding: 20px; background-color: #fff; }
#url_redirect_form .table_item .item_body .box_type_menu>span { border: 0; background: none; }
#url_redirect_form .table_item:first-child { margin-top: 0; }
#url_redirect_form .box_url_table { display: flex; align-items: center; justify-content: space-between; }
#url_redirect_form .box_url_table .table_menu a { display: block; border-radius: 5px; height: 34px; margin-right: 24px; padding: 0 18px; text-decoration: none; font-size: 12px; color: #fff; background-repeat: no-repeat; background-position: 13px center; background-color: var(--primaryColor); line-height: 34px; }
/* 重定向 */
.fixed_add_url_box .error_tips{display: none;font-size: 12px;line-height: 22px;color: #ff1003;padding-top: 3px;}


/**************************** tkd start ****************************/
#tkd{padding-left: 60px;padding-right: 60px;}
#tkd .inside_title{padding-bottom: 20px;}
#tkd .inside_title h1{line-height: 40px;font-size: 24px;color: var(--GlobalTitleColor);}
#tkd .inside_title h2{line-height: 36px;font-size: 14px;position: relative;color: var(--GlobalAssistColor);}
#tkd .inside_container{padding-top: 10px;}
#tkd .r_con_table{margin-top: 0;}
#tkd .r_con_table.new tbody td, #tkd .r_con_table.new tbody td *{font-size: 12px;}
#tkd .tkd_menu { display: flex; justify-content: space-between; align-items: center; }
#tkd .top_menu{ padding: 0 15px; box-sizing: border-box; display: flex; align-items: left; justify-content: left; }
#tkd .top_menu li{ margin-left: 25px; }
#tkd .top_menu li:first-child{ margin-left: 0; }
#tkd .top_menu li a{ display: inline-block; line-height: 32px; border-bottom: 3px solid transparent; font-size: 14px; padding-bottom: 5px;}
#tkd .top_menu li a.current{ color: var(--primaryColor); border-color: var(--primaryColor); }
#tkd .view_seo { margin-right: 19px; margin-bottom: 8px; padding-left: 24px; font-size: 12px; color: #555; background: url(../images/set/icon_view.png) no-repeat left center; }
#tkd .inside_table{ padding-top: 0; }
#tkd .inside_table *[data-placeholder]::placeholder{color: #aaa;}
#tkd .inside_table textarea{ width: 100%; line-height: 20px; overflow-y: visible; background: none; border-color: transparent; border-radius: 5px; box-sizing: border-box; -webkit-box-sizing: border-box; padding: 10px; min-height: 72px;}
#tkd .r_con_table textarea.cur {border-color: #ccdced;}
.tkd_fixed_submit{ display: none; height: 0;padding: 0 20px;box-sizing: border-box;}
.tkd_fixed_submit .center_container_full{padding: 0 40px;}
#tkd .box_option_list { position: relative; }
#tkd .box_option_list:not(.cur):before{ content:''; width: 100%; height: 100%; position: absolute; top: 0; left: 0; z-index: 10; }
#tkd .box_option_list:not(.cur) .option_selected{ border-color: transparent; }
#tkd .box_option_list .option_selected .box_input{ background-color: transparent; }

#tkd .number_limit_relative{ padding-bottom: 20px; }
#tkd .number_limit_relative .number_limit{ display: none; font-size: 12px; bottom: unset; top: calc( 100% - 20px ); left: 0; }
#tkd .number_limit_relative .number_limit span{ font-size: 12px; }
#tkd .title{ margin-bottom: 20px; min-height: 72px; box-sizing: border-box; padding: 10px;}
#tkd .box_option_list .option_selected{min-height: 56px;display: flex;flex-wrap: wrap;padding-top: 10px;padding-bottom: 4px;}
#tkd .box_option_list .option_selected .box_input{width: 100% !important;display: block !important;padding: 0;position: static !important;height: 20px;}
#tkd .box_option_list .btn_attr_choice, #tkd .box_option_list .btn_attr_choice>i{height: 24px;;height: 24px;}
/**************************** tkd end ****************************/