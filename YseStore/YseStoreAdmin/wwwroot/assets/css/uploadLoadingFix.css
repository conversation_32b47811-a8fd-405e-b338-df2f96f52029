/**
 * 上传加载状态修复样式
 * 解决上传图片时加载状态出现两次的问题
 */

/* 确保同一容器内只显示一个loading元素 */
.upload_file_box .loading:not(:first-child) {
    display: none !important;
}

.show_btn .loading:not(:first-child) {
    display: none !important;
}

#PicDetail .loading:not(:first-child) {
    display: none !important;
}

/* 优化loading元素的显示效果 */
.upload_file_box .loading {
    background: #f8f9fb url(../images/frame/loading_oth.gif) no-repeat center !important;
    position: absolute !important;
    width: 100% !important;
    height: 100% !important;
    left: 0 !important;
    top: 0 !important;
    z-index: 999 !important;
    border-radius: 5px !important;
}

.show_btn .loading {
    background: #f8f9fb url(../images/frame/loading_oth.gif) no-repeat center !important;
    position: absolute !important;
    width: 100% !important;
    height: 100% !important;
    left: 0 !important;
    top: 0 !important;
    z-index: 999 !important;
    border-radius: 5px !important;
}

/* 防止loading元素重叠 */
.upload_file_box {
    position: relative !important;
}

.show_btn {
    position: relative !important;
}

/* 确保loading动画正确显示 */
.upload_file_box .loading,
.show_btn .loading {
    background-size: 32px 32px !important;
    background-position: center center !important;
    background-repeat: no-repeat !important;
}

/* 拖拽状态下的loading样式 */
.upload_file_box.dragenter .loading {
    background-color: rgba(75, 160, 252, 0.1) !important;
    border: 1px var(--primaryColor) dashed !important;
    box-sizing: border-box !important;
}

/* 隐藏可能重复的loading元素 */
.upload_file_box .loading + .loading {
    display: none !important;
}

.show_btn .loading + .loading {
    display: none !important;
}

/* 确保loading元素在正确的层级 */
.upload_file_box .loading {
    z-index: 1000 !important;
}

.show_btn .loading {
    z-index: 1000 !important;
}

/* 防止loading元素影响其他元素 */
.upload_file_box .loading ~ * {
    pointer-events: none !important;
}

.show_btn .loading ~ * {
    pointer-events: none !important;
}

/* 上传完成后确保loading被移除 */
.upload_file_box:not(.uploading) .loading {
    display: none !important;
}

.show_btn:not(.uploading) .loading {
    display: none !important;
}

/* 优化loading动画性能 */
.upload_file_box .loading,
.show_btn .loading {
    will-change: opacity !important;
    transition: opacity 0.2s ease-in-out !important;
}

/* 确保loading元素不会被其他样式覆盖 */
.global_upload_pic .upload_file_box .loading {
    background: #f8f9fb url(../images/frame/loading_oth.gif) no-repeat center !important;
    position: absolute !important;
    width: 100% !important;
    height: 100% !important;
    left: 0 !important;
    top: 0 !important;
    z-index: 1000 !important;
}

/* 修复可能的样式冲突 */
.pro_multi_img .img .loading {
    background: #f8f9fb url(../images/frame/loading_oth.gif) no-repeat center !important;
    position: absolute !important;
    width: 100% !important;
    height: 100% !important;
    left: 0 !important;
    top: 0 !important;
    z-index: 1000 !important;
    border-radius: 5px !important;
}

/* 确保只有第一个loading元素可见 */
.upload_file_box .loading:nth-child(n+2) {
    display: none !important;
}

.show_btn .loading:nth-child(n+2) {
    display: none !important;
}

#PicDetail .loading:nth-child(n+2) {
    display: none !important;
}
