/**
 * 上传按钮样式修复
 * 确保与源代码样式完全一致
 */

/* 重置并应用正确的上传按钮样式 */
#PicDetail.pro_multi_img .img.show_btn .preview_pic {
    background-color: #f6fdff !important;
    border: 1px #4BA0FC dashed !important;
    box-sizing: border-box !important;
    position: relative !important;
    width: 100% !important;
    height: 0 !important;
    padding-top: 100% !important;
    border-radius: 5px !important;
}

/* 当没有图片时隐藏整个上传按钮容器、按钮和文字 */
#PicDetail.pro_multi_img:not(:has(.img.isfile)) .img.show_btn .upload_box.preview_pic {
    display: none !important;
}

#PicDetail.pro_multi_img:not(:has(.img.isfile)) .img.show_btn .upload_btn {
    display: none !important;
}

#PicDetail.pro_multi_img:not(:has(.img.isfile)) .img.show_btn .upload_txt {
    display: none !important;
}

/* 当有图片时显示上传按钮容器和透明按钮 */
#PicDetail.pro_multi_img:has(.img.isfile) .img.show_btn .upload_box.preview_pic {
    display: block !important;
}

#PicDetail.pro_multi_img:has(.img.isfile) .img.show_btn .upload_btn {
    width: 100% !important;
    height: 100% !important;
    background: none !important;
    text-indent: 0 !important;
    opacity: 0 !important;
    position: absolute !important;
    left: 0 !important;
    top: 0 !important;
    z-index: 1 !important;
    border: none !important;
    cursor: pointer !important;
    border-radius: 5px !important;
}

/* 上传文本 - 使用源代码的样式 */
#PicDetail.pro_multi_img .img.show_btn .upload_txt {
    display: block !important;
    width: 100% !important;
    position: absolute !important;
    left: 0 !important;
    top: 50% !important;
    text-align: center !important;
    transform: translateY(-50%) !important;
    line-height: 20px !important;
    cursor: pointer !important;
    color: #7d8d9e !important;
    z-index: 2 !important;
    pointer-events: none !important;
}

#PicDetail.pro_multi_img .img.show_btn .upload_txt p {
    margin: 2px 0 !important;
    font-size: 12px !important;
    line-height: 1.2 !important;
}

#PicDetail.pro_multi_img .img.show_btn .upload_txt p:first-child {
    color: #4BA0FC !important;
    font-weight: normal !important;
}

/* 确保已上传图片不显示上传相关元素 */
#PicDetail.pro_multi_img .img.isfile .upload_btn {
    display: none !important;
}

#PicDetail.pro_multi_img .img.isfile .upload_txt {
    display: none !important;
}

/* 确保图片项有正确的尺寸 */
#PicDetail.pro_multi_img .img {
    width: auto !important;
    height: auto !important;
    overflow: visible !important;
    margin: 0 !important;
}

/* 确保容器正确显示 */
#PicDetail.pro_multi_img {
    display: grid !important;
}

/* 当只有上传按钮时的特殊布局 */
#PicDetail.pro_multi_img:not(:has(.img.isfile)) {
    grid-template-columns: repeat(3, 1fr) !important;
    grid-template-rows: 1fr !important;
}

#PicDetail.pro_multi_img:not(:has(.img.isfile)) .img.show_btn:first-child {
    grid-column: 1 / 3 !important;
    grid-row: 1 / 2 !important;
}

/* 有图片时的布局 */
#PicDetail.pro_multi_img:has(.img.isfile) {
    grid-template-columns: repeat(6, 1fr) !important;
    grid-template-rows: repeat(2, 1fr) !important;
}

/* 第一张图片占据更大空间 */
#PicDetail.pro_multi_img .img.isfile:first-child {
    grid-row: 1/3 !important;
    grid-column: 1/3 !important;
}

/* 调试模式 - 可以临时启用来检查布局 */
/*
.debug-mode #PicDetail.pro_multi_img .img.show_btn {
    border: 2px solid blue !important;
}

.debug-mode #PicDetail.pro_multi_img .img.isfile {
    border: 2px solid green !important;
}

.debug-mode #PicDetail.pro_multi_img .img.show_btn .preview_pic {
    background-color: rgba(75, 160, 252, 0.1) !important;
}
*/
