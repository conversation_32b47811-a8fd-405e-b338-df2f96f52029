

#wholesale .list_menu_button .global_tips {height: 34px; line-height: 34px; margin: 0; padding: 0 20px 0 38px;}
#wholesale .list_menu_button .global_tips:after {width: 18px; height: 18px; top: 8px; left: 12px; background-size: 18px;}
#wholesale .inside_table .global_app_tips{ align-items: center; font-size: 12px;}


#edit_wholesale_form .unit_text{ margin-right: 5px; }


.r_con_table tbody td.img .pic_box{width:80px; height:80px; text-align:center; display:block;border-radius: 4px;background-color: #f0f0f0;}
.r_con_table td.operation{ text-align: right; }
#fixed_right .fixed_edit_wholesale .btn_option_add, #fixed_right .fixed_edit_wholesale .btn_option_remove{margin:0;}
#fixed_right .fixed_edit_wholesale .wholesale_box>label{float:left;}
#fixed_right .fixed_edit_wholesale .wholesale_box>label.float{width:106px; margin-right:105px; float:right;}
#fixed_right .fixed_edit_wholesale .wholesale_list { padding-top: 20px; border: 1px solid #F5F6F8; }
#fixed_right .fixed_edit_wholesale .wholesale_list .wholesale_row{ margin-bottom:15px; }
#fixed_right .fixed_edit_wholesale .wholesale_list .wholesale_row .wholesale_row_sybmol{ color: #333; }
#fixed_right .fixed_edit_wholesale .wholesale_list .wholesale_row .error_tips{ margin-top: 5px; padding: 0 10px; color: red; }

#fixed_right .fixed_edit_wholesale .wholesale_list .rows{ margin-bottom: 0; margin-right:10px; padding: 0 10px;}
#fixed_right .fixed_edit_wholesale .wholesale_list .rows:first-child{ width: 25%; }
#fixed_right .fixed_edit_wholesale .wholesale_list .btn_option{display:block; width:34px; height:34px; line-height:38px; border:1px #ddd solid; border-radius:5px; text-align:center;}
#fixed_right .fixed_edit_wholesale .wholesale_list .btn_option>i{display:inline-block; width:14px; height:14px; background-repeat:no-repeat; background-position:center;}
#fixed_right .fixed_edit_wholesale .wholesale_list .btn_option_add{border-top-right-radius:0; border-bottom-right-radius:0;}
#fixed_right .fixed_edit_wholesale .wholesale_list .btn_option_add>i{background-image:url(../../images/products/icon_square_add.png);}
#fixed_right .fixed_edit_wholesale .wholesale_list .btn_option_remove{border-left-width:0; border-top-left-radius:0; border-bottom-left-radius:0;}
#fixed_right .fixed_edit_wholesale .wholesale_list .btn_option_remove>i{background-image:url(../../images/products/icon_square_delete.png);}
#fixed_right .fixed_edit_wholesale .wholesale_list .button.hide_add .btn_option_add{display:none;}
#fixed_right .fixed_edit_wholesale .wholesale_list .button.hide_add .btn_option_remove{border-left-width:1px; border-radius:5px;}
#fixed_right .fixed_edit_wholesale .wholesale_list .button.hide_remove .btn_option_add{border-radius:5px;}
#fixed_right .fixed_edit_wholesale .wholesale_list .button.hide_remove .btn_option_remove{display:none;}
#fixed_right .fixed_edit_wholesale #btn_wholesale_fixed_show{display:none;}
#fixed_right .fixed_edit_wholesale .input_radio_side_box{ width: 100%; margin-bottom: 10px;}
#fixed_right .fixed_edit_wholesale .input_radio_side_box:last-child{ margin-bottom: 0; }
#fixed_right .fixed_edit_wholesale .input_radio_side_box>strong{margin-top: 10px;margin-bottom: 10px;}
#fixed_right .fixed_edit_wholesale .input_radio_side_box::before{top: 17px;left: 20px;}
#fixed_right .fixed_edit_wholesale .input_radio_side_box::after{top: 22px;left: 25px;}

#fixed_right .fixed_edit_wholesale .wholesale_list .wholesale_row .rows{ padding-right: 0; }
#fixed_right .fixed_edit_wholesale .wholesale_list .wholesale_row .rows:first-child input{ width: 50px; }
#fixed_right .fixed_edit_wholesale .wholesale_list .wholesale_row .rows:nth-child(2) input{ width: 60px; }

#fixed_right .fixed_edit_wholesale .wholesale_symbol_tips{ font-size: 12px; letter-spacing: 0; }

#fixed_right .box_submit{ z-index: 1; }

#fixed_right .fixed_edit_wholesale .label_list{ width: 100%; display: flex; align-items: center; justify-content: left; flex-wrap: wrap; background-color: #F5F6F8; }
#fixed_right .fixed_edit_wholesale .label_list label{ width: 22%; padding: 0 20px; line-height: 35px; font-size: 12px; }
#fixed_right .fixed_edit_wholesale .label_list .row{ padding: 0 20px; }
#fixed_right .fixed_edit_wholesale .label_list .row .unit_input>b{ display: none; }

#fixed_right .fixed_edit_wholesale .batch_box{ display: none; font-size: 12px; }

#fixed_right .fixed_edit_wholesale .batch_item .input {position: relative;}
#fixed_right .fixed_edit_wholesale .batch_item .switchery { display: block; }
#fixed_right .fixed_edit_wholesale .batch_item .input .batch_item_overlay {display: none; background-color: rgba(255,255,255,.3); position: absolute; top: 0; right: 0; bottom: 0; left: 0; z-index: 1;}
#fixed_right .fixed_edit_wholesale .batch_item .input .batch_item_overlay.show {display: block;}

#fixed_right .fixed_edit_wholesale .batch_edit>.batch_item{ padding-bottom: 30px; margin-bottom: 30px; border-bottom: 1px solid #ccdced; }
#fixed_right .fixed_edit_wholesale .batch_edit>.batch_item.last{ border-bottom: 0; }
#fixed_right .fixed_edit_wholesale .batch_edit>.batch_item.wholesale_set_box{ padding-bottom: 0; border-bottom: 0; }
#fixed_right .fixed_edit_wholesale .batch_edit .batch_item strong{ margin: 10px 0; display: inline-block; }

#fixed_right .fixed_mixed_batch .rows:not(.mixed_item){ margin-top: 25px; background-color: transparent; }
#fixed_right .fixed_mixed_batch .mixed_item{ margin-top: 25px; margin-bottom: 0; padding: 20px 24px; border-radius: 5px; box-sizing: border-box; width: 100%; background-color: #F7F8FA; }
#fixed_right .fixed_mixed_batch .mixed_item .item_title{ margin-bottom: 4px; }
#fixed_right .fixed_mixed_batch .mixed_item .item_title .input_checkbox_box{ display: inline-block; vertical-align: middle; }
#fixed_right .fixed_mixed_batch .mixed_item:first-child{ margin-top: 12px; }
#fixed_right .fixed_mixed_batch .mixed_item label { margin-bottom: 5px; display: flex; align-items: center; justify-content: space-between;}
#fixed_right .fixed_mixed_batch .mixed_item .input {position: relative;}
#fixed_right .fixed_mixed_batch .mixed_item .input .batch_item_overlay {display: none; background-color: rgba(255,255,255,.3); position: absolute; top: 0; right: 0; bottom: 0; left: 0; z-index: 2;}
#fixed_right .fixed_mixed_batch .mixed_item .input .batch_item_overlay.show {display: block;}
#fixed_right .fixed_mixed_batch .input_radio_side_box {width: 100%;}
#fixed_right .fixed_mixed_batch .input_radio_side_box:last-child {margin-bottom: 0;}
#fixed_right .fixed_mixed_batch .input_radio_side_box .fixed_box {height: 17px;}
#fixed_right .fixed_mixed_batch .input_radio_side_box .fixed_input {display: none; position: absolute; top: 54px; left: 48px; z-index: 1; width: 273px; margin: 0; padding: 0;}
#fixed_right .fixed_mixed_batch .input_radio_side_box .fixed_input .box_input {background-color: #fff;}
#fixed_right .fixed_mixed_batch .input_radio_side_box .fixed_input .unit_input {display: flex; width: 100%;}
#fixed_right .fixed_mixed_batch .input_radio_side_box .fixed_input .unit_input .box_input {flex: 1;}
#fixed_right .fixed_mixed_batch .input_radio_side_box.checked .fixed_box {height: 36px; margin: 7px 0 21px;}
#fixed_right .fixed_mixed_batch .input_radio_side_box.checked .fixed_input {display: block;}


#wholesaler .list_menu_button .global_tips{ height: 34px; line-height: 34px; margin: 0; padding: 0 20px 0 38px; background-color: #fff9f5; border-color: #ffdec6; }
#wholesaler .list_menu_button .global_tips:after {width: 18px; height: 18px; top: 8px; left: 12px; background-size: 18px; background: url(../../images/frame/app_tips_icon.png) no-repeat center;}
#wholesaler .list_menu_button .global_tips span{  color: #1f2328; font-size: 12px; }
#wholesaler .list_menu_button .add_btn{ background-color: var(--primaryColor); color: #fff; }

#wholesale .menu_tab,
#wholesaler .menu_tab{ width: 100%; border-radius: 5px; box-sizing: border-box; padding: 6px; background-color: #fff; font-size: 0; }
#wholesale .menu_tab a,
#wholesaler .menu_tab a{ display: inline-block; line-height: 38px; padding: 0 50px; border-radius: 25px; font-size: 16px; color: #000000;  }
#wholesale .menu_tab a.current,
#wholesaler .menu_tab a.current{ background-color: #edf0f2; }

#wholesaler .r_con_table thead td>a{color:#60666a;}
#wholesaler .r_con_table tr td.img{ text-align:left;}
#wholesaler .r_con_table tr td.img img{max-width: 68px;max-height: 68px;border-radius: 4px;} 
#wholesaler .r_con_table tr td .no_check_email{background: url(../../images/frame/icon_win_warning.png) no-repeat left center;padding-left: 22px;margin-top: 4px;background-size: 16px;}
#wholesaler .r_con_table tr td .non_user{height:24px; line-height:24px; margin:4px 8px 0 0; padding:0 8px; font-size:12px; background-color:#e4f6f1; border-radius:4px; float:left;}
#wholesaler .r_con_table tr.lock *{color: #aaa !important;}

body .box_filter .filter_list .filter_option .filter_scope .input{ width: 120px; }

#wholesaler_set_form{  }
#wholesaler_set_form .input_radio_box { display: block; width: 100%; }
 
#wholesale.add_wholesale .add_products .btn_box { display: flex; align-items: center; justify-content: space-between; }
#wholesale.add_wholesale .add_products .btn_box .big_title{ margin-bottom: 0; }
#wholesale.add_wholesale .add_products .btn_box a{ display: inline-block; padding: 0 25px; height: 28px; line-height: 28px; border: 1px solid var(--primaryColor); background-color: var(--primaryColor); border-radius: 15px; color: #fff; font-size: 14px; text-align: center; }
#wholesale.add_wholesale .add_products .table_box .pic_box{ position: relative; width: 80px; height: 80px; border-radius: 5px; overflow: hidden; }
#wholesale.add_wholesale .add_products .table_box .pic_box,
#wholesale.add_wholesale .add_products .table_box .box_name{ display: inline-block; vertical-align: middle; }
#wholesale.add_wholesale .add_products .table_box .box_name{ width: calc( 100% - 110px ); margin-left: 30px; }
#wholesale.add_wholesale .add_products .table_box .pic_box img{ position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); max-width: 100%; max-height: 100%; }


#wholesale.add_wholesale .setting_box .half_row .rows.float .box_input{ width: calc( 100% - 31px ); }
#wholesale.add_wholesale .setting_box .half_row .rows.float:nth-child(2){ margin-right: 0; }
#wholesale.add_wholesale .setting_box .half_row .rows.float .wholesale_symbol_tips{ margin-top: 5px; font-size: 12px; }
#wholesale.add_wholesale .setting_box .half_row:not(.bottom_box) .rows.float{ width: calc( 50% - 10px ); }
#wholesale.add_wholesale .setting_box .half_row:not(.bottom_box) .rows.float:nth-child(2){ margin-right: 0; }

#wholesale.add_wholesale .setting_box .half_row.bottom_box .rows.float{ width: calc( ( 100% / 3 ) - 20px ); }
#wholesale.add_wholesale .setting_box .half_row.bottom_box .rows.float .box_type_menu span{ margin-bottom: 10px; }
#wholesale.add_wholesale .setting_box .half_row.bottom_box .rows.float:nth-child(3){ margin-right: 0; }

#wholesale.add_wholesale .setting_box .label_list{ margin-bottom: 0; width: 100%; display: flex; align-items: center; justify-content: left; flex-wrap: wrap; background-color: #F5F6F8; }
#wholesale.add_wholesale .setting_box .label_list label{ width: 15%; padding: 0 20px; line-height: 35px; font-size: 12px; }
#wholesale.add_wholesale .setting_box .label_list .row{ padding: 0 20px; }
#wholesale.add_wholesale .setting_box .label_list .row .unit_input>b{ display: none; }

#wholesale.add_wholesale .setting_box .batch_item .input {position: relative;}
#wholesale.add_wholesale .setting_box .batch_item .switchery { display: block; }
#wholesale.add_wholesale .setting_box .batch_item .input .batch_item_overlay {display: none; background-color: rgba(255,255,255,.3); position: absolute; top: 0; right: 0; bottom: 0; left: 0; z-index: 1;}
#wholesale.add_wholesale .setting_box .batch_item .input .batch_item_overlay.show {display: block;}

#wholesale.add_wholesale .setting_box .wholesale_list{ padding-top: 20px; border: 1px solid #F5F6F8; }
#wholesale.add_wholesale .setting_box .wholesale_list .wholesale_row{ margin-bottom: 20px; }
#wholesale.add_wholesale .setting_box .wholesale_list .wholesale_row .rows{ margin-bottom: 0; padding: 0 20px; }
#wholesale.add_wholesale .setting_box .wholesale_list .wholesale_row .rows .unit_text{ margin-right: 5px; }
#wholesale.add_wholesale .setting_box .wholesale_list .wholesale_row .error_tips{ margin-top: 5px; padding: 0 20px; color: red; }
#fixed_right .fixed_edit_wholesale .btn_option_add, #fixed_right .fixed_edit_wholesale .btn_option_remove{margin:0;}

#wholesale.add_wholesale .setting_box .btn_option{display:block; width:34px; height:34px; line-height:38px; border:1px #ddd solid; border-radius:5px; text-align:center;}
#wholesale.add_wholesale .setting_box .btn_option>i{display:inline-block; width:14px; height:14px; background-repeat:no-repeat; background-position:center;}
#wholesale.add_wholesale .setting_box .btn_option_add{border-top-right-radius:0; border-bottom-right-radius:0;}
#wholesale.add_wholesale .setting_box .btn_option_add>i{background-image:url(../../images/products/icon_square_add.png);}
#wholesale.add_wholesale .setting_box .btn_option_remove{border-left-width:0; border-top-left-radius:0; border-bottom-left-radius:0;}
#wholesale.add_wholesale .setting_box .btn_option_remove>i{background-image:url(../../images/products/icon_square_delete.png);}
#wholesale.add_wholesale .setting_box .button.hide_add .btn_option_add{display:none;}
#wholesale.add_wholesale .setting_box .button.hide_add .btn_option_remove{border-left-width:1px; border-radius:5px;}
#wholesale.add_wholesale .setting_box .button.hide_remove .btn_option_add{border-radius:5px;}
#wholesale.add_wholesale .setting_box .button.hide_remove .btn_option_remove{display:none;}

#wholesale.add_wholesale .setting_box .sale_method_box{ height: 36px; box-sizing: border-box; padding: 2px 0; }
#wholesale.add_wholesale .setting_box .sale_method_box .item{ display: inline-block; vertical-align: middle ; }
#wholesale.add_wholesale .setting_box .sale_method_box .batch_box{ margin-left: 20px; display: none; vertical-align: middle; font-size: 14px; color: #1f2328; }
#wholesale.add_wholesale .setting_box .sale_method_box .batch_box .unit_input{ display: inline-block; vertical-align: middle; }

#wholesale .inside_table .list_menu .global_app_tips{ margin: 8px 0; padding: 6.5px 10px; border: 1px solid #ffdec6; background-color: #fff9f5; border-radius: 5px; }
#wholesale .inside_table .list_menu .global_app_tips a{ color: #f68f44; }

#fixed_right_products_choice_form .fixed_right_products_choice_jsppane{ position: relative; }
#fixed_right_products_choice_form .bg_no_table_data{ position: static; }
#fixed_right_products_choice_form .bg_no_table_data .content{ font-size: 12px; position: absolute; top: 40%!important; transform: translateY(-40%); left: 0;}
#fixed_right_products_choice_form .bg_no_table_data .content .table_title{ font-size: 16px; color: #000; }
#fixed_right_products_choice_form .bg_no_table_data .content a{ color: var(--primaryColor); text-decoration: underline; }
