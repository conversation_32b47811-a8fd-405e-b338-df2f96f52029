.w_1200 #sale_sticker_edit .left_container{margin-left: -328px;}
.w_1200 #sale_sticker_edit .left_container .left_container_side{margin-left: 328px;}
.w_1200 #sale_sticker_edit .right_container{margin-left: 20px;width: 308px;}
#sale_sticker_edit .center_container_1043{width: 96%;max-width:1043px; margin:0 auto 20px;}
#sale_sticker_edit .global_container{margin-top: 20px;overflow: unset;}
#sale_sticker_edit .box_drop_double{max-width: unset;}
#sale_sticker_edit .style_item{position: relative;display: inline-block; width: 78px;height: 78px;margin-top: 8px; margin-right: 18px; background-color: #f8f9fb; border: 1px solid var(--GlobalBorderColor);vertical-align: top;border-radius: 5px;cursor: pointer;}
#sale_sticker_edit .style_item .style_info{position: absolute;top:50%;left: 50%;transform: translate(-50%,-50%);border:1px solid #70849a;}
#sale_sticker_edit .style_item .style_info.style_oval{width: 44px;height: 22px;border-radius: 11px;}
#sale_sticker_edit .style_item .style_info.style_round{width: 34px;height: 34px;border-radius: 50%;}
#sale_sticker_edit .style_item .style_info.style_square{width: 38px;height: 22px;}
#sale_sticker_edit .style_item input[type='radio']{display: none;}
#sale_sticker_edit .style_item:hover{width: 76px;height: 76px;border:2px solid #0cb04f}
#sale_sticker_edit .style_item.cur{width: 76px;height: 76px;border:2px solid #0cb04f}
#sale_sticker_edit .style_item.cur::before{position:absolute;content: '\e616'; width: 25px;height: 25px;line-height: 25px; border-radius: 50%;background-color: #07bb8a;font-family: "iconfont" !important;text-align: center;font-size: 16px;color:#fff;left: -12px;top:-12px;}
#sale_sticker_edit .color_row_box{margin-top: 10px;}
#sale_sticker_edit .rows .color_rows{display: inline-block; height: 22px;line-height: 22px;margin-right: 16px;vertical-align: top;}
#sale_sticker_edit .rows .color_rows:last-child{margin-bottom: 0;}
#sale_sticker_edit .rows .color_rows span{display: inline-block;margin-left: 2px;font-size: 12px;color: #404852;vertical-align: top;}
#sale_sticker_edit .rows .color_rows .color_style{ width:46px; height:22px; border:1px solid #e5e5e5; text-indent:999px; border-radius:5px; cursor:pointer;box-sizing: border-box;vertical-align: top;}
#sale_sticker_edit .box_type_menu{margin-bottom: 10px;}
#sale_sticker_edit .rows.type_box .input_radio_side_box{width: 210px;margin:0 20px 18px 0;}
#sale_sticker_edit .rows.type_box .input_radio_side_box:nth-child(3n){margin:0 0 18px 0;}
#sale_sticker_edit .time_rows .time_info{background-color: #f8f9fb;padding:28px;margin-top: 3px;}
#sale_sticker_edit .time_rows .time_info .input_time{background-color: #fff;}
#sale_sticker_edit .use_group_box , #sale_sticker_edit .time_rows , #sale_sticker_edit .specify_rows{display: none;}
yl
#sale_sticker_edit.r_con_wrap .right_container .big_title{margin-bottom: 12px;}
#sale_sticker_edit .right_container .global_container{padding:24px;}
#sale_sticker_edit .right_container .preview_row{position: relative; width: 260px;height: 260px;background:url(../../images/frame/preview_icon.jpg) no-repeat center;}
#sale_sticker_edit .right_container .preview_row .preview_sticker{display: none; position: absolute; top:10px;left: 10px;background-color: #000;color:#fff;font-size: 12px; text-align: center; height: 26px;line-height: 26px;overflow: hidden;}
#sale_sticker_edit .right_container .preview_row .preview_sticker.show{display: block;}
#sale_sticker_edit .right_container .preview_row .preview_sticker[data='oval']{padding:0 12px; border-radius: 13px;}
#sale_sticker_edit .right_container .preview_row .preview_sticker[data='round']{width: 46px;height: 46px;line-height: 46px;border-radius: 50%;}
#sale_sticker_edit .right_container .preview_row .preview_sticker[data='square']{padding:0 12px;}