

.w_1400 #products_inside .center_container_1200{margin:0 auto;}
.w_1400 #category_inside .center_container_1200{margin:0 auto;}
.w_1200 #products_inside .freight_box .box_select{width: 100%;}

/*************************** 产品列表页 start ***************************/
.sort_box{display: inline-block;height: 19px;position: relative;padding-right: 12px;color: #555;}
.sort_box:before{content: '';width: 0;height: 0;border-width: 0 4px 6px 4px;border-style: solid;border-color: transparent;border-bottom-color: #555; position: absolute;right: 0;top: 2px;}
.sort_box:after{content: '';width: 0;height: 0;border-width: 6px 4px 0px 4px;border-style: solid;border-color: transparent;border-top-color: #555; position: absolute;right: 0;bottom: 2px;}
.sort_box.desc:before{display: none;}
.sort_box.asc:after{display: none;}
.en .list_menu_button>li .more_menu{width: 150px;}
.list_menu_button>li{font-size:0;}
.list_menu_button>li .more_menu{width: 105px;}
.list_menu_button>li>a.facebook{padding:0 28px 0 50px; background-image:url(../../images/frame/icon_facebook.png); background-position:24px center;}
.list_menu_button>li>a.facebook_release, .list_menu_button>li>a.facebook_delete{padding:0 20.46px 0 38px; background-position:15px center; display:none;}
.list_menu_button>li>a.facebook_release{margin:0; background-image:url(../../images/frame/icon_release.png); border-top-right-radius:0; border-bottom-right-radius:0;}
.list_menu_button>li>a.facebook_delete{background-image:url(../../images/frame/icon_del.png); border-top-left-radius:0; border-bottom-left-radius:0;}
.list_menu_button>li .facebook_border{width:1px; height:14px; background-color:#d7d7d7; position:absolute; top:9px; left:79px; display:none;}
.list_menu_button>li:hover>a.facebook{display:none!important;}
.list_menu_button>li:hover>a.facebook_release, .list_menu_button>li:hover>a.facebook_delete, .list_menu_button>li:hover .facebook_border{display:inline-block; vertical-align:top;}
.list_menu_button>li.loading>a.facebook{font-size:0; background:url(../../images/global/loading_small.gif) no-repeat center #f3f3f3;}
.list_menu_button>li.loading:hover>a.facebook{display:block!important;}
.list_menu_button>li.loading:hover>a.facebook_release, .list_menu_button>li.loading:hover>a.facebook_delete, .list_menu_button>li.loading:hover .facebook_border{display:none!important;}
.list_menu_button>li>a.shopify_product_list_sync,
.list_menu_button>li>a.shopline_product_list_sync{background-color:var(--primaryColor); color:#fff;}
.list_menu_button>li>a.shopifycsvupload_product_list_sync,
.list_menu_button>li>a.shoplinecsvupload_product_list_sync{background-color:var(--primaryColor); color:#fff;}
.list_menu_button>li>a.xshoppycsvupload_product_list_sync{background-color:var(--primaryColor); color:#fff;}

.r_con_table tbody td.img .pic_box{width:85px; height:85px; text-align:center; display:block;}
.r_con_table tbody td.img .pic_box>img{overflow:hidden; border-radius:4px;}
#products .r_con_table tbody td .btn_view{display:block; width:21px; height:14px; background:url(../../images/products/btn_view.png) no-repeat; border:0; margin-top:15px; margin-left:-2px; transition:all .2s; -webkit-transition:all .2s; opacity:0;}
#products .r_con_table tbody td .btn_view:hover{background-image:url(../../images/products/btn_view_current.png);}
#products .r_con_table tbody tr:hover td .btn_view{opacity:1;}
#products .r_con_table tbody td.info .name{font-size:13px;}
#products .r_con_table tbody td.info .classify{line-height:24px; padding-bottom:5px;}
#products .r_con_table tbody td.info .other_box{height:24px; line-height:24px; margin:4px 8px 8px 0; padding:0 8px; font-size:12px; background-color:#e4f6f1; border-radius:4px; display:block;}
#products .r_con_table tbody td.info .loading{width:14px; height:24px; margin:4px 8px 8px 0; background:url(../../images/global/loading_small.gif) no-repeat left center; display:block;}
#products .r_con_table tbody td.price_input .form_input{height:36px; line-height:36px; margin-top:-9px; margin-left:5px; padding:0 5px;}
#products .r_con_table tbody td.price_input .cur{line-height: 36px;}
#products .r_con_table tbody td.myorder_select .box_select{width:50px; margin-top:-9px;}

#category_select_hide, #myorder_select_hide{display:none;}
/*************************** 产品列表页 end ***************************/

/*************************** 产品编辑页 start ***************************/
#products_inside .center_container_1200{margin:0 20px;}

#products_inside .global_container{overflow:inherit; position:relative;}
#products_inside .box_textarea{height:56px;}
#products_inside .border_t{margin-top:10px; border-top:1px #f6f6f6 solid;}
#products_inside .big_notes{line-height:23px; margin-bottom:10px;font-size: 12px;}
#products_inside .right_container .big_title{cursor:pointer;}
#products_inside .right_container .big_title.no_pointer{cursor:default;}
#products_inside .attr_container .big_title{margin-bottom:12px;}
#products_inside .description_container .big_title{margin-bottom:0;}
#products_inside .description_container .box_basic_more{top:60px;}

#products_inside .product_menu{margin-bottom:15px; margin-left:-5px;}
#products_inside .product_menu_item{display:inline-block; vertical-align:top; position:relative;}
#products_inside .product_menu_item>button{display:block; min-height:35px; line-height:35px; background-color:transparent; border:0; padding:0 5px; color:var(--primaryColor);}
#products_inside .product_menu_item>button>em{display:inline-block; vertical-align:top; width:15px; height:15px; background-repeat:no-repeat; margin-top:10px; margin-right:6px;}
#products_inside .product_menu_item>button>i{display:inline-block; vertical-align:top; width:8px; height:5px; background:url(../../images/products/icon_menu_arrow.png) no-repeat; margin-top:15px; margin-left:8px; transition:all .2s; -webkit-transition:all .2s;}
#products_inside .product_menu_item>button.btn_menu_copy>em{background-image:url(../../images/products/icon_menu_copy.png);}
#products_inside .product_menu_item>button.btn_menu_view>em{width:18px; background-image:url(../../images/products/icon_menu_view.png);}
#products_inside .product_menu_item>button.btn_menu_app>em{background-image:url(../../images/products/icon_menu_app.png);}
#products_inside .product_menu_item>button.btn_menu_app:hover>i{transform:rotate(180deg); -webkit-transform:rotate(180deg);}
#products_inside .product_menu_item .box_my_app{display:none; background:transparent; position:absolute; left:18px; z-index:1; opacity:0;}
#products_inside .product_menu_item .drop_down{background-color:#fff; border-radius:5px; padding:7px 0;}
#products_inside .product_menu_item .drop_down .item{display:block; height:32px; line-height:32px; padding:0 18px; text-decoration:none; font-size:12px; color:#666; cursor:pointer;}

#products_inside .price_box{width:177px; margin-right:20px; float:left; clear:none;}
#products_inside .twice_box{width:375px; margin-left:10px; float:left; clear:none;}
#products_inside .twice_box .box_input{width:90%; padding:0 5%;}
#products_inside .twice_box .box_select{max-width:373px;}
#products_inside .twice_box .unit_input{width:100%;}
#products_inside .twice_box .unit_input .box_input{width:86%; padding:0 5%;}
#products_inside .twice_box.twice_first{margin-right:10px; margin-left:0;}

#products_inside .box_basic_more{position:absolute; top:20px; right:11px;}
#products_inside .box_basic_more dt>a{width:36px; height:24px; padding:9px; display:block;}
#products_inside .box_basic_more dt>a>i{width:inherit; height:inherit; background:url(../../images/products/btn_basic_more_gray.png) no-repeat center center; border-radius:3px; display:block;}
.en #products_inside .box_basic_more dd{width: 140px;left: -41px;background: #fff;}
#products_inside .box_basic_more dd{width:100px; overflow:hidden; padding:7px 0; background-color:#fff; border-radius:4px; position:absolute; top:42px; left:-35px; display:none; z-index:2;}
#products_inside .box_basic_more dd .input_checkbox_box{height:32px; line-height:32px; text-decoration:none; font-size:0; display:block;}
#products_inside .box_basic_more dd .input_checkbox_box>span{font-size:12px; display:inline-block; vertical-align:top;}
#products_inside .box_basic_more dd .input_checkbox_box .input_checkbox{width:12px; height:12px; margin:9px 10px 0 15px; vertical-align:top;}
#products_inside .box_basic_more dd .btn_open_attr>em{width:16px; height:15px; margin:8px 10px 0 15px; position:relative; display:inline-block; vertical-align:top;}
#products_inside .box_basic_more dd .add_attr>em:before{width:1px; height:15px; margin:auto; background:var(--primaryColor); position:absolute; top:0; bottom:0; left:7px; content:'';}
#products_inside .box_basic_more dd .add_attr>em:after{width:15px; height:1px; margin:auto; background:var(--primaryColor); position:absolute; top:0; bottom:0; left:0; content:'';}
#products_inside .box_basic_more dd .edit_attr>em{background:url(../../images/shipping/icon_edit.png) no-repeat center;}
#products_inside .box_basic_more dd .myorder_attr>em{background:url(../../images/frame/icon_myorder.png) no-repeat top;}
#products_inside .box_attr_basic_more{top:6px; right:0;}
#products_inside .box_attr_basic_more dt>a{width:24px; height:16px; padding:0; padding-bottom:8px;}
#products_inside .box_attr_basic_more dt>a>i{background-image:url(../../images/products/btn_basic_more_gray.png);}
#products_inside .box_attr_basic_more dd{width:70px; top:24px; left:-26px; z-index:9;}
#products_inside .box_attr_basic_more dd .input_checkbox_box{height:28px; line-height:28px; text-align:center;}
#products_inside .box_seo_basic_more{top:7px; right:13px;}
#products_inside .box_seo_basic_more dt>a{width:24px; height:16px; padding:0; padding-bottom:8px;}
#products_inside .box_seo_basic_more dt>a>i{background-image:url(../../images/products/btn_basic_more.png); background-color:#ccc; background-size:80%;}
#products_inside .box_seo_basic_more dd{width:70px; top:20px; left:-26px; z-index:9;}
#products_inside .box_seo_basic_more dd .input_checkbox_box{height:28px; line-height:28px; text-align:center;}
#products_inside .box_attr_add_basic_more{top:72px;}

#products_inside .input_unit{max-width:100px;}

#products_inside .d_line{padding-top:30px; border-bottom:1px #f1f1f1 solid;}
#products_inside .d_list{margin-right:10px; float:left;}
#products_inside .d_tit{height:32px; line-height:32px; padding-top:10px; font-size:14px; display:block;}
#products_inside .d_empty{height:5px; display:block;}
#products_inside .d_del{margin-top:48px; margin-left:10px;}
#products_inside .d_del.d_del_empty{margin-top:10px;}
#products_inside #add_wholesale{margin-top:10px;}

#products_inside .sold_status{width:100%;}
#products_inside .sold_status_box{margin-left:28px;}
#products_inside .sold_status_box .switchery{margin-top:9px; vertical-align:top;}
#products_inside .sold_status_box .sold_in_title{line-height:38px; display:inline-block; vertical-align:top;}
#products_inside .sold_status_box .sold_in_time{margin-left:5px;}

.icon_apply{height:23px; line-height:23px; overflow:hidden; border-radius:50px; padding:0 13px; position:absolute; top:3px; right:35px; z-index:1;}
.icon_apply.icon_apply_product{background-color:#eaf7f4; color:var(--primaryColor);}
.icon_apply.icon_apply_category{background-color:#ecf1f9; color:#3d78e0;}
.icon_apply.icon_apply_categories{background-color:#faf3e8; color:#f27326;}
.icon_apply.icon_apply_website{background-color:#faeded; color:#f7303a;}

.box_text .rows{position:relative; clear:both;}
.box_text .rows .attr_delete{position:absolute; top:3px; right:0; display:none;}
.box_text .rows label .icon_apply{right:0; transition:right .5s; -webkit-transition:right .5s;}
.box_text .rows label .switchery{margin-top:6px; margin-right:39px;}
.box_text .rows .unit_input{width:100%;}
.box_text .rows:hover .icon_apply{right:35px;}
.box_text .rows:hover .attr_delete{display:block;}

.box_editor_attribute .rows{position:relative;}
.box_editor_attribute .rows .attr_delete{position:absolute; top:23px; right:0; display:none;}
.box_editor_attribute .rows:hover .attr_delete{display:block;}

.description_box .head{border-bottom: 1px solid #f6f6f6; padding-bottom:5px;}
.description_box .tab_box{margin-top:5px; margin-right:54px; float:right;}
.description_box .input>ul>li{margin-top:10px;}

.description_tab_box{display:inline-block; vertical-align:top; margin-top:5px; position:relative; z-index:5;}
.description_tab_box:hover{z-index:6;}
.description_tab_box .description_tab_row{height:30px; line-height:31px; font-size:12px; position:relative;}
.description_tab_box .description_tab_row .description_tab_title{padding:0 20px 0 10px; position:relative; font-size:14px; color:#000;}
.description_tab_box .description_tab_row .description_tab_title i{display:inline-block; vertical-align:top; width:8px; height:5px; background:url(../../images/frame/icon_arrow_down_small_gray.png) no-repeat center; margin:auto; position:absolute; top:0; bottom:0; right:5px; transition: all .4s; -webkit-transition:all .4s; transform:rotate(0); -webkit-transform:rotate(0);}
.description_tab_box .description_tab_row .drop_down{display:none; min-width:100%; background:#fff; padding:7px 0; position:absolute; top:30px; left:0;}
.description_tab_box .description_tab_row:hover .description_tab_title i{transform:rotate(180deg); -webkit-transform:rotate(180deg);}
.description_tab_box .description_tab_row .item{display:block; line-height:27px; padding:0 15px; text-align:left; text-decoration:none; white-space:nowrap; font-size:12px; color:#888;}
.description_tab_box.close .description_tab_row .description_tab_title i{display:none;}
.description_tab_apply{display:inline-block; vertical-align:top; width:200px; margin-top:5px; position:relative;}
.description_tab_apply .icon_apply{left:0; right:auto;}

.box_attr{position:relative;}
.box_attr .rows .input{overflow:inherit; position:relative;}
.box_attr label{min-height: 30px;padding-top:0!important; padding-right:40px;}
.box_attr label>span{margin-left:8px; color:#0bb183;}

.box_attr_list .btn_attr_choice{height:24px; line-height:24px; margin-right:5px; margin-bottom:5px; vertical-align:top;}
.box_attr_list .btn_attr_choice>i{height:24px; background-position:-7px -23px;}
.box_attr_list .btn_attr_choice.placeHolder{background-color:#fff; border:1px #aaa dashed;}
.box_attr_list .box_input{height:24px; line-height:24px; border:0; display:inline-block; vertical-align:top;}
.box_attr_list .select_all{margin-top:3px; color:#b0b0b0; display:inline-block;}
.box_attr_list .attr_selected, .box_attr_list .attr_not_yet{overflow:hidden; padding:6px 10px 1px; border:1px #cbcecd solid; border-radius:4px; position:relative;}
.box_attr_list .attr_selected{min-height:29px; cursor:text;}
.box_attr_list .attr_selected .box_input{width:20px; background-color:#fff; z-index:2; display:none;}
.box_attr_list .attr_selected .box_input:focus{box-shadow:none;}
.box_attr_list .attr_selected .select_list{display:inline-block; vertical-align:top;}
.box_attr_list .attr_selected .placeholder{line-height:36px; position:absolute; top:0; left:10px; z-index:1;}
.box_attr_list .attr_selected .btn_attr_choice:before{width:6px; height:10px; background:url(../../images/frame/icon_myorder.png) no-repeat left top; position:absolute; top:7px; left:7px; display:none; content:'';}
.box_attr_list .attr_selected .btn_attr_choice:hover:before{display:block;}
.box_attr_list .attr_not_yet{background-color:#f8f8f8; border-top:0; display:none;}
.box_attr_list .attr_not_yet .btn_attr_choice{background-color:#f8f8f8;}
.box_attr_list .attr_not_yet .btn_attr_choice>i{background-color:#f8f8f8; background-position:-7px -23px;}
.box_attr_list .selected_focus{border-color:#07bb8b; outline:none!important; box-shadow:0 0 5px rgba(7,187,139,.3);}
#add_attribute, #edit_attribute{margin-right:10px;}
#add_attribute{height:34px; line-height:34px; padding:0 27px; font-size:14px;}
#myorder_attribute{height:34px; line-height:34px; padding:0 27px; font-size:14px; color:#555;}
#add_combination{height:30px; line-height:30px; padding:0 22px; font-size:14px;}
#control_combination{display:inline-block; width:30px; height:30px; background:#f5f5f5; border-radius:50%; text-align:center; margin-left:10px;}
#control_combination>i{display:inline-block; vertical-align:top; margin-top:6px;}

.attribute_select_list{width:300px; background-color:#fff; position:absolute; top:37px; left:0; z-index:2; box-shadow:#ddd 0 0 10px 2px;}
.attribute_select_list>li>a{width:inherit; line-height:20px; padding:5px 25px; text-decoration:none; display:block;}
.attribute_select_list>li>a:hover{background-color:#f5f5f5;}

.tags_row{margin-top:15px;}

.global_form .classify_rows{margin:0;}
.global_form .classify_hide{display:none;}
.classify .rows{margin-right:10px; margin-bottom:15px;}
.classify .rows .box_select{width:246px;}
.classify .btn_option{display:block; width:34px; height:34px; line-height:38px; border:1px #ddd solid; border-radius:5px; text-align:center;}
.classify .btn_option>i{display:inline-block; width:14px; height:14px; background-repeat:no-repeat; background-position:center;}
.classify .btn_option_add{border-top-right-radius:0; border-bottom-right-radius:0;}
.classify .btn_option_add>i{background-image:url(../../images/products/icon_square_add.png);}
.classify .btn_option_remove{border-left-width:0; border-top-left-radius:0; border-bottom-left-radius:0;}
.classify .btn_option_remove>i{background-image:url(../../images/products/icon_square_delete.png);}
.classify .button.hide_add .btn_option_add{display:none;}
.classify .button.hide_add .btn_option_remove{border-left-width:1px; border-radius:5px;}
.classify .button.hide_remove .btn_option_add{border-radius:5px;}
.classify .button.hide_remove .btn_option_remove{display:none;}

.seo_box{padding-bottom:20px; display:none;}
.seo_box .rows{position:relative;}
.seo_box .keys_row, .seo_box .custom_row{margin-bottom: 0;}
.seo_box .box_input{width:90%;}
.seo_box .unit_input .box_input{width:auto;}
#edit_seo_list{height:40px; line-height:40px; position:absolute; top:0; right:0; font-size:14px;}
.seo_info_box{display:none;}
.seo_info_box>div{margin-bottom:10px;}
.seo_info_box .keyword{line-height:18px; font-size:12px; word-break:break-all;}
.seo_info_box .description{line-height:18px; font-size:12px; word-break:break-all;}
.seo_info_box .url{line-height:18px; text-decoration:underline; font-size:12px;}
.seo_info_box .btn_copy{background: url(../../images/products/icon_copy.png) no-repeat left center;padding-left: 21px;color: var(--primaryColor);}
.freight_box{position:relative;}
.freeshipping_box{position:absolute; top:-41px; right:0;}
.freeshipping_box .switchery{margin:0;}
.box_volume_weight{margin-top:2px; vertical-align:top;}

.cubage_input{margin-left:10px;}
.cubage_input>b{padding:0 4px;}
.cubage_input .box_input{padding:0 5px;}
.cubage_input:first-child{margin-left:0;}

/* 产品属性 Start */
#attribute_ext_box{margin-top:20px; display:none;}
#attribute_ext_box .box_multi_tab{overflow:inherit;}
#attribute_ext{background-color:#fff; border-top:1px #edf0f5 solid; border-radius:4px;min-height:150px; position:relative;}
#attribute_ext.tab_show{border-top-left-radius:0;}
.relation_box{width:100%;}
.relation_box .title{text-align:center; background:#f7f7f7;}
.relation_box td{padding:15px 10px; font-size:12px; border-bottom:1px #f6f6f6 solid;}
.relation_box td .box_input{height:28px; line-height:28px;}
.relation_box td .form_input{width:40px; height:22px; line-height:22px;}
.relation_box td .sku_input{width:130px;}
.relation_box td .input_price{width:55px;}
.relation_box td .attr_picture{width:40px; height:40px; background:url(../../images/products/bg_upload_pic.png) no-repeat; cursor:pointer; transition:all .2s;}
.relation_box td .attr_picture>img{max-width:100%; max-height:100%;}
.relation_box td .attr_picture.saved{background:transparent none scroll no-repeat center center / contain; border-radius:3px;}
.relation_box td .attr_picture_hover{background-image:url(../../images/products/bg_upload_pic_hover.png);}
.relation_box td .btn_delete{display:block; width:18px; height:18px;}
.relation_box td .btn_delete>i{display:inline-block; vertical-align:top;}
.relation_box td.attr_name{text-align:left; color:#555;}
.relation_box thead tr{border-bottom:1px #edf0f5 solid;}
.relation_box thead td{height:39px; line-height:39px; background-color:#f9fbfa; border:0; padding:0; text-indent:10px; font-size:14px;}
.relation_box tbody{display:none;}
.relation_box tbody.show{display:table-row-group;}
.relation_box .group_title td{text-align:left; font-weight:bold;}
.relation_box .spacing{padding:10px;}
.relation_box .spacing img{left:0; top:0;}
.relation_box .synchronize_btn{padding:0 10px; line-height:18px; text-decoration:none; color:#fff; border-radius:3px; background-color:#888; display:inline-block;}
.open_attr_price{position:absolute; top:27px; right:15px;}
.open_attr_price span{vertical-align:middle; margin-right:10px;}
.box_combination{display:none; text-align:right;}
.box_combination span{vertical-align:middle; margin-right:10px; font-size:14px;}
.box_combination .input{line-height:30px;}
.box_combination.fixed{margin:0; position:absolute; top:83px; right:20px;}
#attribute_unit_box .relation_box thead td{text-indent:20px;}
#attribute_unit_box .relation_box tbody{display:table-row-group;}
#attribute_unit_box .relation_box tbody td{padding:15px 20px;}
#attribute_unit_box .relation_box tbody .box_input{width:calc(100% - 22px); height:34px; line-height:34px; margin:5px 0;}

.box_attribute{display:none;}
.box_cart_attribute{margin-top:20px;}

.box_attribute_tab_menu{display:none; height:50px; border-bottom:1px #ddd solid; margin:0 auto 5px; font-size:0;}
.box_attribute_tab_menu>span{display:inline-block; height:48px; line-height:48px; border-bottom:3px transparent solid; padding:0 12px; font-size:14px; cursor:pointer; color:#555;}
.box_attribute_tab_menu>span.checked{border-color:var(--primaryColor); color:var(--primaryColor);}

.product_line{height:1px; background-color:#f1f1f1; margin-bottom:30px;}

.batch_box{position:relative;}
.batch_box .btn_batch{width:inherit; height:inherit; display:block;}
.batch_box .batch_edit{background-color:#fff; border:1px #ccc solid; position:absolute; right:-1px; top:36px; z-index:9;}
.batch_box .batch_edit>em{border-width:0 5px 5px 5px; border-color:transparent transparent #ccc transparent; border-style:solid; display:block; position:absolute; top:-5px; right:30px; z-index:10;}
.batch_box .batch_edit>i{border-width:0 5px 5px 5px; border-color:transparent transparent #fff transparent; border-style:solid; display:block; position:absolute; top:-3px; right:30px; z-index:11;}
.batch_box .batch_edit .rows{width:200px;}
.batch_box .batch_edit .rows>label{height:28px; line-height:28px; padding:0; padding-left:10px; text-align:left; font-size:12px; background-color:#f9fbfa; display:block;}
.batch_box .batch_edit .rows .input{text-align:left; padding-left:10px;}
.batch_box .batch_edit .rows .input>p{line-height:16px; padding:4px 0;}
.batch_box .batch_edit .rows .input>p>input{margin-top:2px; display:inline-block; vertical-align:top;}
.batch_box .batch_edit .button{line-height:30px; padding-bottom:10px;}
.batch_box .batch_edit .btn{height:26px; line-height:26px; padding:0 15px; font-size:12px; color:#fff; border:0; border-radius:3px; display:inline-block;}
.batch_box .batch_edit .btn_batch_submit{margin-right:8px; background-color:#55a290;}
.batch_box .batch_edit .btn_batch_cancel{background-color:#82C0B2;}
/* 产品属性 End */

/* 图片弹窗 Start */
.box_popover{box-sizing:border-box; line-height:1.5; position:absolute; top:0; left:0; z-index:1000; font-size:14px;}
.box_popover_hidden{display:none;}
.box_popover_arrow{display:block; width:8.48528137px; height:8.48528137px; background:transparent; border-width:4.24264069px; border-style:solid; position:absolute; transform:rotate(45deg);}
.box_popover_right{padding-left:10px;}
.box_popover_right .box_popover_arrow{border-top-color:transparent; border-right-color:transparent; border-bottom-color:#fff; border-left-color:#fff; bottom:3px; left:6px; box-shadow:-3px 3px 7px rgba(0,0,0,.07); transform:translateY(-50%) rotate(45deg);}
.box_popover_right .box_popover_content{background-color:#fff; background-clip:padding-box; border-radius:2px; padding:15px; box-shadow:0 2px 8px rgba(0,0,0,.15); box-shadow:0 0 8px rgba(0,0,0,.15) \9;}
.box_popover_right .box_popover_picture{display:flex; flex-wrap:wrap; max-width:542px; max-height:280px; overflow:auto; justify-content:start;}
.box_popover_right .box_popover_picture .popover_img{width:86px; height:86px; margin-right:6px; margin-bottom:6px; background:transparent none scroll no-repeat center center / contain; border:1px #eee solid; cursor:pointer;}
.box_popover_right .box_popover_picture .popover_img:nth-child(4n+4){margin-right:0;}
.box_popover_right .box_popover_picture .popover_img_selected{border-color:var(--primaryColor); text-align:center; position:relative;}
.box_popover_right .box_popover_picture .popover_img_selected:before{width:30px; height:30px; background:url(../../images/frame/icon_success.png) no-repeat #fff; border-radius:50%; position:absolute; top:28px; left:28px; z-index:1; content:'';}
.box_popover_right .box_popover_picture .popover_img_empty{width:200px; padding:10px 0; text-align:center;}
.box_popover_right .box_popover_picture .popover_img_empty>p{margin-bottom:10px;}
.popover_enter_animate{animation-duration:.2s; animation-fill-mode:both; animation-play-state:paused; transform:scale(0); opacity:0; animation-timing-function:cubic-bezier(.08,.82,.17,1);}
.popover_leave_animate{animation-duration:.2s; animation-fill-mode:both; animation-play-state:paused; animation-timing-function:cubic-bezier(.78,.14,.15,.86);}
.popover_enter_active{animation-name:PopoverEnter; animation-play-state:running;}
.popover_leave_active{animation-name:PopoverLeave; animation-play-state:running; pointer-events:none;}
@keyframes PopoverEnter {
	0%{transform:scale(0.8); opacity:0;}
	100%{transform:scale(1); opacity:1;}
}
@keyframes PopoverLeave {
	0%{transform:scale(1);}
	100%{transform:scale(0.8); opacity:0;}
}
/* 图片弹窗 End */

/* 右侧弹窗 Start */
#fixed_right .fixed_video .box_button{margin-top:35px;}
#fixed_right .fixed_video .box_button .btn_global{margin-right:8px; border:0!important;}

#fixed_right .fixed_add_attribute .input{position:relative; overflow:inherit;}
#fixed_right .fixed_add_attribute .input>.box_input{width:90%;}
#fixed_right .fixed_add_attribute #check_attribute_name input{width:96%;}
#fixed_right .fixed_add_attribute .attribute_select_list{width:332px; background-color:#fff; position:absolute; top:37px; left:0; z-index:10; display:none; box-shadow:#ddd 0 0 10px 2px;}
#fixed_right .fixed_add_attribute .attribute_select_list>li>a{width:inherit; line-height:16px; padding:8px 25px; text-decoration:none; display:block;}
#fixed_right .fixed_add_attribute .attribute_select_list>li>a:hover{background-color:#f5f5f5;}
#fixed_right .fixed_add_attribute .box_button{margin-top:35px;}
#fixed_right .fixed_add_attribute .box_button .btn_global{margin-right:8px; border:0!important;}
#fixed_right .fixed_add_attribute .apply_box{margin-bottom:0;}
#fixed_right .fixed_add_attribute .type_box{margin-bottom:0;}
#fixed_right .fixed_add_attribute .input_radio_box{display:inline-block; width:257px; min-height:93px; overflow:hidden; margin:0; margin-bottom:20px; position:relative; text-align:center;}
#fixed_right .fixed_add_attribute .input_radio_box>strong{display:block; line-height:30px; margin-top:14px;}
#fixed_right .fixed_add_attribute .input_radio_box>p{line-height:20px; margin-top:5px; margin-bottom:17px; padding:0 27px;}
#fixed_right .fixed_add_attribute .input_radio_box .input_radio{display:block; width:255px; height:calc(100% - 5px); background:none; margin:0; position:absolute; transition:all .2s; -webkit-transition:all .2s;}
#fixed_right .fixed_add_attribute .input_radio_box:hover .input_radio, #fixed_right .fixed_add_attribute .input_radio_box.checked .input_radio{width:253px; border-width:2px;}
#fixed_right .fixed_add_attribute .input_radio_box.checked>strong{color:var(--primaryColor);}
#fixed_right .fixed_add_attribute .input_radio_box.checked .input_radio:before{margin:5px;}
#fixed_right .fixed_add_attribute .input_radio_box.disabled .input_radio{border-color:#ddd; border-style:dashed; cursor:no-drop;}
#fixed_right .fixed_add_attribute .input_radio_box.mr20{margin-right:20px;}
#fixed_right .fixed_add_attribute .screen_box .box_explain{margin-top:5px; margin-left:20px;}

#fixed_right .fixed_edit_attribute .edit_tips{font-size:14px;}
#fixed_right .fixed_edit_attribute .choice_type{margin-top:20px;}
#fixed_right .fixed_edit_attribute .edit_attr_list{margin-top:20px;}
#fixed_right .fixed_edit_attribute .rows label{padding:0;}
#fixed_right .fixed_edit_attribute .rows .tab_box_btn{margin:0; padding:0;}
#fixed_right .fixed_edit_attribute .item{margin-top:15px; padding-left:28px; position:relative; border:1px #ddd solid; border-radius:5px; cursor:move;}
#fixed_right .fixed_edit_attribute .item>strong{display:inline-block; height:38px; line-height:40px;}
#fixed_right .fixed_edit_attribute .item .myorder{line-height:18px; padding:11px 10px 11px 2px; position:absolute; top:0; left:10px; vertical-align:top;}
#fixed_right .fixed_edit_attribute .item .myorder .icon_myorder{background-image:url(../../images/frame/icon_myorder_fixed.png); transition:all .3s; -webkit-transition:all .3s;}
#fixed_right .fixed_edit_attribute .item:hover .myorder .icon_myorder{background-image:url(../../images/frame/icon_myorder.png);}
#fixed_right .fixed_edit_attribute .item.placeHolder{background-color:#fff; border:1px #aaa dashed;}
#fixed_right .fixed_edit_attribute .edit_editor_list{margin-top:20px;}
#fixed_right .fixed_edit_attribute .edit_editor_list .product_count{line-height:36px;}
#fixed_right .fixed_edit_attribute .edit_editor_list .attr_delete{margin-top:6px; margin-left:13px;}
#fixed_right .fixed_edit_attribute .box_button{margin-top:17px;}
#fixed_right .fixed_edit_attribute .box_button .btn_global{border:0!important; margin-right:8px;}

#fixed_right .fixed_edit_attribute_option_edit .attribute_info{background-color:#f6f7f7; margin:0 -20px; padding:8px 20px; position:relative;}
#fixed_right .fixed_edit_attribute_option_edit .attribute_info .attribute_title{height:30px; line-height:30px; font-size:16px; color:#000;}
#fixed_right .fixed_edit_attribute_option_edit .global_tips{margin:20px 0; padding:6px 20px;}
#fixed_right .fixed_edit_attribute_option_edit .global_tips strong{font-size:14px;}
#fixed_right .fixed_edit_attribute_option_edit .edit_attr_list{margin-top:29px;}
#fixed_right .fixed_edit_attribute_option_edit .rows label{padding:0;}
#fixed_right .fixed_edit_attribute_option_edit .rows .tab_box_btn{padding:0;}
#fixed_right .fixed_edit_attribute_option_edit .item{padding:7px 0;}
#fixed_right .fixed_edit_attribute_option_edit .item .box_input{height:38px; line-height:38px; float:left;}
#fixed_right .fixed_edit_attribute_option_edit .item .default_name{width:95px; line-height:40px; overflow:hidden; margin-left:13px; float:left; white-space:nowrap; text-overflow:ellipsis; -webkit-text-overflow:ellipsis;}
#fixed_right .fixed_edit_attribute_option_edit .box_button{margin-top:17px;}
#fixed_right .fixed_edit_attribute_option_edit .box_button .btn_global{margin-right:8px; border:0!important;}
#fixed_right .fixed_edit_attribute_option_edit .bg_no_table_data{padding-top:50%; display:none;}

#fixed_right .fixed_edit_attribute_delete .attribute_info{background-color:#f6f7f7; margin:0 -20px; padding:8px 20px; position:relative;}
#fixed_right .fixed_edit_attribute_delete .attribute_info .attribute_title{height:30px; line-height:30px; font-size:16px; color:#000;}
#fixed_right .fixed_edit_attribute_delete .attribute_info .attribute_related{height:26px; line-height:26px; color:#555;}
#fixed_right .fixed_edit_attribute_delete .attribute_info .icon_apply{top:auto; right:20px; bottom:8px;}
#fixed_right .fixed_edit_attribute_delete .global_tips{margin:20px 0; padding:6px 20px;}
#fixed_right .fixed_edit_attribute_delete .global_tips strong{font-size:14px;}
#fixed_right .fixed_edit_attribute_delete .box_button{padding-top:20px;}
#fixed_right .fixed_edit_attribute_delete .box_button input{display:block; margin:0 auto;}
#fixed_right .fixed_edit_attribute_delete .box_button .btn_submit{min-width:140px; height:36px; line-height:36px;}
#fixed_right .fixed_edit_attribute_delete .box_button .btn_cancel{background-color:transparent; border:0; margin-top:8px;}

#fixed_right .fixed_associate_pic .edit_tips{line-height:16px; font-size:14px;}
#fixed_right .fixed_associate_pic .switchery_pictures{margin-top:20px;}
#fixed_right .fixed_associate_pic .edit_picture_list{margin-top:20px;}
#fixed_right .fixed_associate_pic .item{padding:5px 0; position:relative;}
#fixed_right .fixed_associate_pic .item .title{line-height:30px; overflow:hidden;}
#fixed_right .fixed_associate_pic .item .multi_img{margin:0; padding:0; border-bottom:1px #e3e3e3 solid; clear:none;}
#fixed_right .fixed_associate_pic .item .multi_img .img .preview_pic{width:60px; height:60px;}
#fixed_right .fixed_associate_pic .item .multi_img .img .upload_btn{width:60px; height:60px; background-size:100%; border:0;}
#fixed_right .fixed_associate_pic .item .multi_img .img .preview_pic>a{width:56px; height:56px; padding:1px;}
#fixed_right .fixed_associate_pic .box_button{margin-top:17px;}
#fixed_right .fixed_associate_pic .box_button .btn_global{margin-right:8px; border:0!important;}
#fixed_right .fixed_associate_pic .bg_no_table_data{padding-top:50%; display:none;}

#fixed_right .fixed_add_combination .add_content .rows{margin:10px 0 25px;}
#fixed_right .fixed_add_combination .add_content .rows .box_input{width:90%;}
/* 右侧弹窗 End */
/*************************** 产品编辑页 end ***************************/

/*************************** 数据同步 start ***************************/
#fixed_right .box_input.bg_gray{background: #ccc;cursor: no-drop;}
.r_con_sync .inside_table{padding:0;}
.r_con_sync .inside_menu_table{padding-top:20px;}
.r_con_sync .account_list_box{padding: 9px 0 0;background: #fff;}
.r_con_sync .account_list{padding: 0 30px 5px 5px;}
.r_con_sync .account_list .more_menu{min-width: auto;line-height: 27px;}
.r_con_sync .box_drop_down_menu .more_menu .item{padding: 0 92px 0 15px;}
.r_con_sync .box_drop_down_menu .more_menu a{padding: 0 5px;display: inline-block;}
.r_con_sync .box_drop_down_menu .more_menu .opation a{padding: 0;}
.r_con_sync .list_menu_button a{padding: 0 25px;}
.r_con_sync .list_menu_button .del{padding: 0 16px 0 35px;}
.r_con_sync .list_menu_button .add_store{background: var(--primaryColor);color:#fff;}
.r_con_sync .list_menu_button .add_store:hover{background: var(--primaryColor);color:#fff;}
.r_con_sync .dashboard{margin-bottom:10px;}
.r_con_sync .dashboard .div-btn{position:relative; margin:0 15px 5px 5px;}
.r_con_sync .dashboard .div-btn .btn_ok{float:none; margin:0; position:relative;}
.r_con_sync .dashboard .div-btn:hover .btn_ok{background:#4a907f;}
.r_con_sync .dashboard .div-btn .btn_ok span{display:inline-block; margin-left:5px; width:0; height:0; border-top:6px solid; vertical-align:middle; border-right:4px solid transparent; border-left:4px solid transparent; box-sizing:border-box;}
.r_con_sync .dashboard .div-btn ul{ position:absolute; top:90%; left:0; z-index:1000; display:none; min-width:160px; padding:5px 0; list-style:none; margin-top:2px; text-align:left; background-color:#fff; -webkit-background-clip:padding-box; background-clip:padding-box; border:1px solid rgba(0,0,0,.15); border-radius:4px; -webkit-box-shadow:0 6px 12px rgba(0,0,0,.175); box-shadow: 0 6px 12px rgba(0,0,0,.175);}
.r_con_sync .dashboard .div-btn ul>li{display:block; padding:3px 20px 5px; clear:both; font-weight:400; line-height:18px; white-space:nowrap; cursor:pointer;}
.r_con_sync .dashboard .div-btn ul>li:hover{color:#262626;text-decoration:none;background-color:#f5f5f5;}
.r_con_sync .pop_form .btn_ok, .r_con_sync .pop_form .btn_cancel{float:none;}
.r_con_sync .pop_form .button button{display:inline-block;}
.r_con_sync .sync_progress .tips_contents{overflow-y:auto; background:#fff; width:540px;height: 310px; margin:0 auto; padding:40px 0 0; border-radius:4px; line-height:30px; text-align:center;box-sizing: border-box;position: relative;transform: translateY(-50%);}
.r_con_sync .inside_menu_table.shopify_table{padding-top: 0;}
.shopify_table .plugins_app_menu{margin-bottom: 10px;}
.sync_progress .progress_close{position: absolute;top:15px;right:15px; width: 20px;height: 20px;background: url(../../images/frame/icon_win_close.png) no-repeat center;cursor: pointer;}
.sync_progress #box_circle_container{position: absolute;left: 50%;top: 50%;transform: translate(-50%, -50%);width: 100%;}
.sync_progress #box_circle_container .circle_progress_text span{ display: block; font-size: 14px; color: #7d8d9e; }

.r_con_sync a.green{color:green;}
.r_con_sync .global_form .rows .input .category_list{padding:8px 3px 0 3px; line-height:24px; min-height:24px; color:#990000;}
.r_con_sync .global_form .rows .input>label{display:inline-block; margin-right:10px; min-height:18px; padding:2px 0; text-align:left; width:auto;}
.r_con_sync .global_form .rows .input>label input{vertical-align:middle;}
.r_con_sync .global_form .rows .input .property-table{background-color:#fbfbfb; box-sizing:border-box; padding:10px 10px 15px; min-height:20px;}
.r_con_sync .global_form .rows .input .property-table .property-form{padding-bottom:10px;}
.r_con_sync .global_form .rows .input .property-table .property-form .form-item{clear:both; margin-bottom:5px;}
.r_con_sync .global_form .rows .input .property-table .property-form .form-item em{font-style:inherit; display:block; width:150px; height:auto; float:left; padding:4px 0; padding-right:15px; margin-bottom:10px; text-align:right; line-height:21px; position:relative;}
.r_con_sync .global_form .rows .input .property-table .property-form .form-item .form-control{ display:block; margin-left:165px; line-height:20px;}
.r_con_sync .global_form .rows .input .property-table .property-form .form-item .form-control>label{display:inline-block; margin-right:10px; min-height:18px; padding:2px 0; text-align:left; width:auto;}
.r_con_sync .global_form .rows .input .property-table .property-form .form-item .form-control>label input{vertical-align:middle;}
.r_con_sync .global_form .rows .input .property-table .custom-property{border-top:1px dashed #ccc; padding-top:10px; padding-left:75px;}
.r_con_sync .global_form .rows .input .property-table .custom-property .custom-property-item{line-height:30px; margin-bottom:8px;}
.r_con_sync .global_form .rows .input .WholeSaleBox{padding-bottom:5px; padding-top:8px;}
.r_con_sync .global_form .rows .input .sku-property-list>label{display:inline-block; margin-right:15px; min-height:18px; padding:2px 0; text-align:left; width:auto;}
.r_con_sync .global_form .rows .input .sku-property-list>label input{vertical-align:middle; margin:0 3px 3px 0; line-height:normal; cursor:pointer;}
.r_con_sync .global_form .rows .input .color-property .property-title{display:inline-block; width:12px; height:12px; border:1px solid #e4e4e4; overflow:hidden; text-indent:-9999px; padding:0; margin:-2px 0;}
.r_con_sync .global_form .rows .input .turn_on_mobile_detail{line-height:35px;}
.r_con_sync .global_form .rows .input .custom_weight{padding-top:12px;}
.r_con_sync .global_form .rows .input .custom_weight>div{padding:3px 0;}
.r_con_sync .sku-table th, .sku-table td{padding:0 10px 0 10px;}
.r_con_sync .sku-custom-property{padding:10px 0;}
.r_con_sync .sku-custom-property-list{margin:10px 0; width:auto; border:1px solid #e8e8e8;}
.r_con_sync .sku-custom-property-list th{background-color:#f2f2f2; padding-top:10px; padding-bottom:10px; text-align:left;}

.r_con_sync .global_form .rows .input .detail_textarea{height:150px; border-radius:4px; -ms-border-radius:4px; -moz-border-radius:4px; -webkit-border-radius:4px; display:inline-block; vertical-align:top;}
.r_con_sync .global_form .rows .input .detail_textarea textarea{ padding:5px 10px; width:720px; height:140px;}
.r_con_sync .global_form .rows .input.upload_file .upload_box, .r_con_sync .global_form .rows .input .upload_file .upload_box, .r_con_sync .global_form .rows .input.upload_file .upload_box a{ width:80px; height:80px;}
.r_con_sync .global_form .rows .input.upload_file .upload_box a.zoom{width:20px; height:20px;}

.copy_products_box #btn_expand{width:140px; height:36px; line-height:36px; float:right;}
.copy_products_box .classify{margin-right:160px;}
.copy_products_box .classify .box_select{width:100%; max-width:100%;}
.copy_products_box .expand_list select{margin-right:10px;}
.copy_products_box .expand_list{padding-top:6px; clear:both;}
.copy_products_box .expand_list>li{margin-top:5px; margin-right:160px; position:relative;}
.copy_products_box .expand_list>li>a{margin-top:6px; margin-left:9px; position:absolute; top:0; right:-35px;}
.copy_products_box .expand_list>li .box_select{width:100%; max-width:100%; display:inline-block; vertical-align:top;}
.copy_products_box .expand_list div{margin-bottom:6px;}
/*************************** 数据同步 end ***************************/
/*************************** shopify start ***************************/
.win_alert .ext_html{margin-top: 23px;line-height: 15px;}
.csvupload_products_box .explode_box .box_item{padding: 0;border: 0;}
#upload_edit_form .pro_download_btn{position: absolute;top:50%;right: 20px;background-color: #0cb083;border-radius: 15px;color:#fff; transform: translateY(-50%);}
.progress_container_right{display:none;}
.progress_container_right .r_con_table{margin:0;}
.progress_container_right .r_con_table .loading{width:14px; height:20px; background:url(../../images/global/loading_small.gif) no-repeat left center; display:block;}
.progress_container_right .r_con_table .completed{width:14px; height:20px; background:url(../../images/set/current.png) no-repeat left center; display:block;}
.progress_container_right .r_con_table tr .name{max-width:440px; overflow:hidden; white-space:nowrap; text-overflow:ellipsis; -webkit-text-overflow:ellipsis;}
.progress_container_right .r_con_table tr.error *{color:#c00;}
.csvupload_products_box .progress_container_right .r_con_table{border: 0;}
.csvupload_products_box .progress_container_right .r_con_table thead tr{border-top: 1px solid #edf0f5;border-bottom: 1px solid #edf0f5;}
.csvupload_products_box .progress_container_right .r_con_table thead tr td{height: auto;line-height: normal;padding: 15px;}
.csvupload_products_box .progress_container_right .r_con_table tbody tr:first-child td{padding-top: 20px;}
.csvupload_products_box .progress_container_right .r_con_table tbody tr td{padding: 10px;border: 0;}
.csvupload_products_box .progress_container_right .r_con_table tbody tr td .icon{display: block;width: 10px;height: 10px;margin: 0 auto;background-color: #ddd;border-radius: 5px;}
.csvupload_products_box .progress_container_right .r_con_table tbody tr td .icon.on{background-color: #4bcb5c;}
.csvupload_products_box .progress_container_right .r_con_table tbody tr td .img{display: inline-block;width: 50px;height: 50px;background-color: #ddd;border-radius: 5px;overflow: hidden;}
.csvupload_products_box .progress_container_right .r_con_table tbody tr td .img img{max-width: 100%;max-height: 100%;}
.csvupload_products_box .progress_container_right .r_con_table tbody tr td .name{display: inline-block;width: 200px;height: 50px;line-height: 25px;margin-left: 10px;white-space: normal;}

#box_circle_container #btn_progress_keep,#box_circle_container #btn_progress_continue{display: none;}
#box_circle_container .progress_completed_btn{display: none; min-width: 105px; height: 38px;line-height: 38px;margin-top: 30px; background-color: #09af4b;color:#fff;}
#box_circle_container .progress_completed_btn.show{display:inline-block;}
/*************************** shopify end ***************************/

/*************************** Shopify采集 start ***************************/
.r_con_sync .collection_table{padding:20px 30px 10px 30px;}
.r_con_sync .collection_table .search_form{float:left;}
.r_con_sync .collection_table .btn_crawl{height:33px; line-height:33px; overflow:hidden; background:var(--primaryColor); border:0; border-radius:5px; margin-top:8px; padding:0 26px; float:right; color:#fff; cursor:pointer;}
.r_con_sync .collection_table .btn_crawl[disabled]{ background-color: #ccc; cursor: no-drop;  }
.r_con_sync .collection_table .btn_crawl[disabled]{ background-color: #ccc; cursor: no-drop;  }
.r_con_sync .collection_table .list_menu_button a{padding:0 16px 0 35px;}
.r_con_sync .collection_table .global_app_tips{ margin-top: 14px; margin-right: 10px; color: #f68f44;  }

.box_crawl .box_crawl_program{margin:0; position:relative;}
.box_crawl .box_textarea{height:110px; background-color:#f3f3f3; border:0;}
.box_crawl .btn_crawl_start{height:32px; line-height:32px; overflow:hidden; background:var(--primaryColor); border:0; border-radius:3px; padding:0 16px; position:absolute; right:21px; bottom:15px; font-size:12px; color:#fff; cursor:pointer;}
.box_crawl .btn_crawl_start[disabled]{background-color:#ccc; cursor:no-drop;}
.box_crawl .global_tips{display:none; width:calc(100% - 10px); margin:15px 0; padding:8px;}
.box_crawl .global_tips strong{font-size:12px;}
.box_crawl .bg_no_table_data{display:none; height:300px!important;}
.box_crawl .bg_no_table_data .content{background-size:100%; padding-top:200px; top:0!important;}
.box_crawl .products_show_list{display:none; width:310px; height:calc(100vh - 390px); overflow-y:auto; outline:0;}
.box_crawl .products_show_list .products_show_list_item{padding:10px 0; cursor:pointer; transition:all .5s; opacity:0;}
.box_crawl .products_show_list .products_show_list_item:hover, .box_crawl .products_show_list .products_show_list_item.current{background:#e9f2ff;}
.box_crawl .products_show_list .products_show_list_item .btn_checkbox{margin-right:10px; margin-top:15px; float:left;}
.box_crawl .products_show_list .products_show_list_item .item_img{width:46px; height:46px; border-radius:4px; overflow:hidden; background-color:#f0f0f0;}
.box_crawl .products_show_list .products_show_list_item .item_img img{max-width:100%; max-height:100%;}
.box_crawl .products_show_list .products_show_list_item .item_name{width:240px; height:46px; line-height:23px; margin-left:12px; overflow:hidden; font-size:12px; color:#555;}
.box_crawl .products_show_list .products_show_list_item.show{opacity:1;}
.box_crawl .products_show_list::-webkit-scrollbar{width:5px; background:#fff; border-radius:5px;}
.box_crawl .products_show_list::-webkit-scrollbar-thumb{background:rgba(0,0,0,.1); border-radius:5px;}
.box_crawl .products_show_list::-webkit-scrollbar-thumb:hover{background:rgba(0,0,0,.3);}
.box_crawl .products_show_list.show{display:block;}
.csvupload_products_box .input_checkbox_box .input_checkbox{margin-top: 2px;}
.csvupload_products_box .input_checkbox_box {margin-bottom: 15px;display: inline-block;}
/*************************** Shopify采集 end ***************************/

/* tongtool */
#sync_tongtool .delivery_box{margin-top: 60px;}
#sync_tongtool .delivery_box .rows label{display: block; margin-bottom: 15px; color:#1f2328;}
#sync_tongtool .delivery_box .rows .input_checkbox_box{font-size: 12px;color:#404852}
#sync_tongtool .delivery_box .rows .input_checkbox_box .input_checkbox{margin-top: 3px;}
#sync_tongtool .delivery_box .rows.type_submit{margin-top: 56px; text-align: center;}
#sync_tongtool .delivery_box .rows.type_submit .tongtool_product_sync_btn{max-width: 105px; height: 38px;line-height: 38px;}
#sync_tongtool .delivery_box .middle_title{ font-size: 20px; color: #1f2328; text-align: center; }
#sync_tongtool .delivery_box.delivery_orders_form{ margin-top: 0px;padding-bottom: 50px; }
#sync_tongtool .delivery_box.delivery_orders_form .rows.type_submit{ margin-top: 30px; }
#sync_tongtool .fixed_box_popup .box_middle{text-align: center;}
#sync_tongtool .fixed_box_popup .box_middle .title{margin-top: 50px;}
#sync_tongtool .delivery_box.delivery_orders_form .rows label{display: inline-block;margin-bottom: 0;}
#sync_tongtool .delivery_box.delivery_orders_form .rows .input{display: inline-block;vertical-align: bottom;margin-left: 12px; }
#sync_tongtool .delivery_orders_form .btn_global.btn_submit{ padding: 0 35px; width: auto; height: 38px; line-height: 38px; border-radius: 5px; font-size: 14px; color: #fff; }

#products_sync .global_app_tips.obvious{padding: 13px 20px 15px;margin-bottom: 17px;}
#products_sync .white_mask{position: relative;}
#products_sync .white_mask::after{content: '';position: absolute;top: 0;right: 0;bottom: 0;left: 0;background-color: rgba(255, 255, 255, 0.4);cursor: no-drop;z-index: 10;}