#facebook_report .global_container {padding: 30px;}
#facebook_report .account_intro {position: relative;}
#facebook_report .account_title {line-height: 40px; font-size: 20px; color: var(--GlobalTitleColor);}
#facebook_report .account_tips {line-height: 31px; font-size: 14px; color:#7d8d9e;}
#facebook_report .go_set {position: absolute; top: 20px; right: 0; font-size: 14px; color: #fff; background-color: #0baf4d;}
#facebook_report .go_set.top_position {top:0;}
#facebook_report .account_list {margin-top: 30px;}

#facebook_report .content_top {overflow: hidden;}
#facebook_report .content_top .content_title {float: left; font-size: 20px; color: var(--GlobalTitleColor)}
#facebook_report .content_top .btn_help {line-height: 26px; float: right; font-size: 12px;}
#facebook_report .content_top .btn_help>span {color: var(--GlobalMainColor);}
#facebook_report .content_info {margin: 30px 0;}
#facebook_report .content_info .content_item {position: relative; padding: 30px; margin-top: 20px; background-color: #f7f9fb; border-radius: 4px;}
#facebook_report .content_info .content_item:first-child {margin-top: 0;}
#facebook_report .content_info .content_item_title {margin-bottom: 14px; font-size: 16px; color: var(--GlobalTitleColor);}
#facebook_report .content_info .content_item_tips {width: 80%; font-size: 14px; color:#7d8d9e;}
#facebook_report .content_info .content_item_name {margin-bottom: 18px; font-size: 16px; color: var(--GlobalTitleColor);}
#facebook_report .content_info .content_item_name .content_item_edit {float: right; font-size: 14px; color: var(--primaryColor);}
#facebook_report .content_info .go_set {top: 34px; right: 30px;}
#facebook_report .content_info .link_box {display: inline-block; line-height: 38px; overflow: hidden; font-size: 0; border: 1px solid var(--GlobalBorderColor); border-radius: 5px;}
#facebook_report .content_info .link_box .link {display: inline-block; vertical-align: top; width: 445px; height: 38px; padding: 0 10px; font-size: 12px; color: var(--GlobalTitleColor); border: 0;}
#facebook_report .content_info .link_box .btn_copy {display: inline-block; vertical-align: top; height: 38px; padding: 0 11px; color: var(--GlobalMainColor); background-color: #fff; border: 0;}
#facebook_report .content_info .link_box .btn_copy::before {content: "\e618"; margin-right: 7px; font-family: "iconfont" !important; font-size: 14px; font-style: normal; -webkit-font-smoothing: antialiased; -moz-osx-font-smoothing: grayscale;}

.fixed_ad_account .rows {margin-bottom: 30px;}
.fixed_ad_account .rows>label {height: 33px; line-height: 33px;}
.fixed_ad_account .rows[data-type=name] .input {display: flex;}
.fixed_ad_account .rows[data-type=name] .input .box_input {flex: 1;}
