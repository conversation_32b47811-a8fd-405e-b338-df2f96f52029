.plugins_app_menu{ padding: 0 25px; height: 51px; line-height: 51px; }
.plugins_app_menu li{ margin-left: 50px; line-height: 50px; }
.plugins_app_menu li:first-child{ margin-left: 0; }
.plugins_app_menu li a{ padding: 0; font-size: 16px; }
.plugins_app_menu li a.current{ border-bottom: 2px solid var(--primaryColor); }

.distribution .search_form{ float: left; }

#dis .uname{color: #7F7F7F;}
#dis .btn_copy, #dis_view .btn_copy{display: inline-block;background: url(../../images/products/icon_copy.png) no-repeat left center;padding-left: 21px;color: var(--primaryColor);}
.fixed_btn_submit{ right: 0; left: unset; }

#dis .follow_email { margin-top: 8px; margin-left: 20px; }
#dis .follow_email .global_app_tips{ margin: 0; display: block; }
#dis .follow_email .global_app_tips .tips{ color: #f68f44; }
#dis.config .inside_table{ padding-bottom: 0; background-color: transparent; }
#dis.config .plugins_app_menu{ margin-bottom: 0; border-radius: 5px; }
#dis.config .config_box{ margin-top: 20px; padding: 0 24px; width: 100%; border-radius: 5px; background: #fff; box-sizing: border-box; }
#dis.config .config_box .rows{ padding: 40px 0; border-bottom: 1px solid #f1f1f1; position: relative; display: flex; align-items: center; justify-content: space-between; }
#dis.config .config_box .rows .config_btn{ width: 90px; height: 30px; line-height: 30px; border-radius: 35px; background: var(--primaryColor); font-size: 14px; color: #ffffff; display: block; position: absolute; top: 50%; transform: translateY(-50%); right: 0; text-align: center; }
#dis.config .config_box .rows:last-child{ border-bottom: 0; }
#dis.config .config_box .rows .title{ height: 17px; line-height: 17px; font-size: 18px; }
#dis.config .config_box .rows .sub{ margin-top: 8px; line-height: 28px; font-size: 12px; color: #7e8da0; }
#dis.config .config_box .rows .sub .global_app_tips{ display: block; }
#dis.config .config_box .rows .sub .global_app_tips>*{ display: inline-block; vertical-align: middle; color: #f68f44; }
#dis.config .top_title{ font-size: 20px; color: #1f2328; }
#dis.config .top_title .error_tips{ margin-left: 15px; display: inline-block; vertical-align: middle; font-size: 14px; color: red; }
#dis.config #config_page_form .rows.half{ width: 46.5%; }
#dis.config #config_page_form label{ min-height: 30px; }
#dis.config #config_page_form .box_input{ width: 100%; box-sizing: border-box; }
#dis.config #config_page_form .list_box .global_container.box{ margin-right: 1.6%; width: 49.2%; box-sizing: border-box; }
#dis.config #config_page_form .list_box .global_container.box:nth-child(2n){ margin-right: 0; }
#dis.config #config_page_form .list_box .multi_img .img .preview_pic{ width: 60px; height: 60px; }
#dis.config #config_page_form .list_box .multi_img .img .upload_btn{ width: 60px; height: 60px; background-size: contain; }
#dis.config #config_page_form .list_box .multi_img .img .preview_pic>a{ width: 48px; height: 48px; background-size: contain; }
#dis.config #config_page_form .list_box .global_container{ padding: 20px 24px; }
#dis.config #config_page_form .list_box .box_textarea{ width: 100%; height: 70px; box-sizing: border-box; }
#dis .btn_copy.hide{display: none;}
#dis .align_center{ text-align: center; }
#dis .align_left{ text-align: left; }
#dis .align_right{ text-align: right; }
#dis .icon-tongguo:before,
#dis .icon-jujue:before{ font-size: 12px; }
#dis .inside_menu_left{ padding: 0 24px 20px; }
#dis .inside_menu_left .inside_menu{ margin: 0; }
#dis .list_menu.noMinHeight{ min-height: unset; }
#dis .plugins_app_menu .menu_tips{ display: inline-block; margin-left: 4px; padding: 1px 10px; line-height: normal; font-size: 12px; border-radius: 15px; background-color: #F5222D; color: #fff; }

.fixed_edit_level{ position: relative; }
.fixed_edit_level .rows label{ margin-bottom: 5px; }
.fixed_edit_level .rows .unit_input{ width: 100%; display: flex; }
.fixed_edit_level .rows .unit_input input{ width: calc( 100% - 36px ); flex: 1; }
.fixed_edit_level .global_app_tips{ padding: 9px 11px; border: 1px solid #ffdec6; border-radius: 5px; background-color: #fff9f5; }

#dis_view .global_container{padding: 30px;}
#dis_view .w_1200 .right_container{ width: 385px; }
#dis_view .bg_no_table_data{ max-height: 220px; }
#dis_view .big_title{margin-bottom: 10px;}
#dis_view .base_info_v2 .name{ margin-bottom: 10px;  font-size: 14px; color: #7d8d9e; }
#dis_view .base_info_v2 .email{ font-size: 16px; }
#dis_view .info_list{display: flex; justify-content: space-between;}
#dis_view .info_list .info_item{ padding: 35px 25px; width: calc((100% - 20px) / 2); border-radius: 5px; background-color: #f7f7f7; box-sizing: border-box; text-align: center;}
#dis_view .info_list .info_item .title{ line-height: 12px; font-size: 14px; color: #404852; }
#dis_view .info_list .info_item .text{ margin-top: 17px; line-height: 18px; font-size: 24px; color: #1f2328; font-weight: bold; }
#dis_view .switchery_big_title{display: flex;align-items: center;margin-bottom: 0;line-height: 26px;}
#dis_view .switchery_big_title .full{flex: 1;}
#dis_view .share_link{margin-bottom: 12px;color: #aaa;font-size: 12px;}
#dis_view.plugins_app_box:after{height: 0;}
#dis_view .orders_info_v2 .view_all{ display: block; margin: 30px auto 0; width: 110px; height: 32px; line-height: 32px; border: 1px solid #ced4da; border-radius: 35px; font-size: 12px; color: #404852; text-align: center; }
#dis_view .operation_btn_menu{ margin-bottom: 24px; }
#dis_view .operation_btn_menu>a{ margin-left: 15px; display: inline-block; vertical-align: top; color: var(--primaryColor); font-size: 14px; }
#dis_view .operation_btn_menu>a:before{ margin-right: 4px; }
#dis_view .operation_btn_menu>a:first-child{ margin-left: 0px; }
#dis_view .email .status{margin-left: 5px;display: inline-block;height: 22px;line-height: 22px;background-color: #fbeded;padding: 0 11px;font-size: 12px;color: #f35958;border-radius: 11px; white-space: nowrap;}
#dis_view .email .status.ing{background-color:#eaf8ec; color:#0baf4d;}
#dis_view .email .status.end{background-color:#f3f3f3; color:#888;}
#dis_view .email .status.process{background-color:#f3f3f3; color:#22abff;}
#dis_view .app_form_message{ margin-top: 40px; padding: 30px 0 20px; border-top: 1px solid #f5f5f5; }
#dis_view .app_form_message .vItem{ margin-top: 20px; display: flex; flex-wrap: wrap; align-items: center; justify-content: space-between; }
#dis_view .app_form_message .vItem:first-child{ margin-top: 0; }
#dis_view .app_form_message .vItem .log_title{ width: 30%; text-align: left; color: #7e8da0; }
#dis_view .app_form_message .vItem .log_value{ width: 55%; text-align: right; color: #333; word-break: break-all; }
#dis_view .app_form_message .vItem .pic_box{ display: inline-block; vertical-align: middle; width: 40px; height: 40px; border: 1px solid #eeefff; position: relative; }
#dis_view .app_form_message .vItem .pic_box img{ max-width: 100%; max-height: 100%; position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); }
#dis_view .app_form_message .vItem .log_value .file_item{ margin-top: 10px; display: block; font-size: 14px; color: #333; }
#dis_view .app_form_message .vItem .log_value .file_item:first-child{ margin-top: 0; }
#dis_view .empty_link{ font-size: 24px; font-weight: normal; line-height: 24px; }

#rule_page_form .rows:last-child{ margin-bottom: 0; }
#rule_page_form .input_radio_box{ margin-bottom: 0; width: 48%; }
#rule_page_form .global_container{ padding: 30px 24px; }
#rule_page_form .btn_field_add { text-align: center; color: #fff; background-color: var(--primaryColor); border: 1px solid var(--primaryColor); }
#rule_page_form .top_title{ font-size: 18px; }

#rule_page_form .field_list{ margin-top: 10px; }
#rule_page_form .field_list .field_item { display: flex; align-items: center; justify-content: space-between; position: relative; border-radius: 5px; margin-bottom: 15px; padding:18px; background-color: #f8f9fb; }
#rule_page_form .field_list .field_item em{width: 6px;height: 100%;position: absolute;left: 5px;top: 0;cursor: move;}
#rule_page_form .field_list .field_item em::after{content: '';width: 6px;height: 14px;position: absolute;top: 50%;left: 13px;background-image: url(../../images/view/icon_view.png);background-position: -102px -98px;transform: translateY(-50%);}
#rule_page_form .field_list .field_item:hover{background-color: #ecf5ff;}
#rule_page_form .field_list .field_item .field_name { display: inline-block; width: 80%; margin-left: 16px; }
#rule_page_form .field_list .field_item .field_opt{float: right;font-size: 12px;}
#rule_page_form .field_list .field_item .field_opt.field_del_btn{margin-left: 8px;color:#7d8d9e}
#rule_page_form .field_list .field_item .field_opt.field_edit_btn{color:#0baf4e}

#field_form .field_content_row{overflow: hidden;}
#field_form .field_content_row .btn_option{display:block; width:34px; height:34px; line-height:38px; border:1px #ddd solid; border-radius:5px;text-align:center;}
#field_form .field_content_row .btn_option>i{display:inline-block; width:14px; height:14px; background-repeat:no-repeat; background-position:center;}
#field_form .field_content_row .btn_option_add{border-top-right-radius:0; border-bottom-right-radius:0;}
#field_form .field_content_row .btn_option_add>i{background-image:url(../../images/products/icon_square_add.png);}
#field_form .field_content_row .btn_option_remove{border-left-width:0; border-top-left-radius:0; border-bottom-left-radius:0;}
#field_form .field_content_row .btn_option_remove>i{background-image:url(../../images/products/icon_square_delete.png);}
#field_form .field_content_row .button.hide_add .btn_option_add{display:none;}
#field_form .field_content_row .button.hide_add .btn_option_remove{border-left-width:1px; border-radius:5px;}
#field_form .field_content_row .button.hide_remove .btn_option_add{border-radius:5px;}
#field_form .field_content_row .button.hide_remove .btn_option_remove{display:none;}
#field_form  .field_content_row .rows label{ line-height: 36px; margin-right: 10px; }
#field_form  .field_content_row .rows label,
#field_form  .field_content_row .rows .input,
#field_form  .field_content_row .button{ float: left; }

.distribution_email_title { padding: 10px 0; font-size: 20px; color: #000; line-height: 30px; }
.distribution_email_box .table_item {padding: 0 20px; background: #f8f9fb; border: 0; border-radius: 5px; box-sizing: border-box; -webkit-box-sizing: border-box;}
.distribution_email_box .table_item tr td {height: 53px; line-height: 53px; padding: 0;}
.distribution_email_box .table_item tr td>span {font-size: 14px;}
.distribution_email_box .table_item tr td .desc {font-size: 12px; color: #aaa;}
.distribution_email_box .table_item tr td>a {line-height: 53px;}
.distribution_email_box .table_item tr td .img {margin-top: 17px;}
.distribution_email_box .table_item tr td a.edit{ opacity: 0; color: var(--primaryColor); }
.distribution_email_box .table_item .global_app_tips { display: inline-block; margin-right: 10px; }
.distribution_email_box .table_item .global_app_tips.hide { display: none; }
.distribution_email_box .table_item:hover tr td a.edit { opacity: 1; }

.fixed_add_distribution .error_tips{ margin-top: 5px; color: #F83228; }
