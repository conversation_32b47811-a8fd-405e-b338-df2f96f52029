#cod_page_edit_form .box_select{max-width: 462px;}
#cod_page_edit_form .input_checkbox_box{margin-right: 38px;}
#cod_page_edit_form .set .input_checkbox_box{display: block; width: 100%; margin-right: 0; }
#cod_page_edit_form .set .input_checkbox_box strong{color:#555;margin-left: 5px; display: inline-block; vertical-align: middle;}
#cod_page_edit_form .set .input_checkbox_box{ display: inline-block; vertical-align: middle;}
#cod_page_edit_form .set .input_checkbox_box .input_checkbox{ display: inline-block; vertical-align: middle;}
#cod_page_edit_form .set .input_checkbox_box.readonly .input_checkbox{ border-color: #afe3ad; }
#cod_page_edit_form .set .input_checkbox_box.readonly .input_checkbox:before{ background-color: #afe3ad; }
#cod_page_edit_form .set .input_checkbox_box.readonly:hover .input_checkbox{ box-shadow: 0 0 0 #fff; }
#cod_page_edit_form .btn_cancel{margin-left: 10px;}
#cod_page_edit_form .r_con_table tbody tr:hover{ background: #fff; }
#cod_page_edit_form .big_title{ margin-bottom: 0;}
#cod_page_edit_form .big_title .set{ padding: 0 16px 0 27px; font-size: 14px; color: #07bb8a; position: relative; background: url(../../ico/set1.png) no-repeat center left; }
#cod_page_edit_form .big_title .set>span{ cursor: pointer; }
#cod_page_edit_form .big_title .set:after{ content: ''; width: 8px; height: 5px; position: absolute; top: 50%; right: 0; transform: translateY(-50%) rotate(180deg); background: url(../../ico/arrow_up_blue.png) no-repeat center center; }
#cod_page_edit_form .big_title .set:hover .switchBox{ display: block; }
#cod_page_edit_form .big_title .set .switchBox{ display: none; width: 190px; border-radius: 5px; position: absolute; top: 40px; right: 0; padding: 22px 20px; background: #fff; box-shadow: 0 0 7px #e8e8e8; z-index: 10; font-size: 0;; }
#cod_page_edit_form .big_title .set .switchBox strong{ font-size: 12px;;}
#cod_page_edit_form .big_title .set .switchBox .smail_title{ line-height: 32px; font-size: 12px; color: #555; }
#cod_page_edit_form .r_con_table thead td{ padding: 12px 30px; }
#cod_page_edit_form .r_con_table.new tbody td{ padding: 17px 30px; }
#cod_page_edit_form .r_con_table .review_box .readonly{ cursor: no-drop; }
#cod_page_edit_form .r_con_table .review_box .readonly .switchery_state_on{ background-color: #afe3ad; }
#cod_page_edit_form .r_con_table .review_box .readonly .switchery_toggler{box-shadow: none;}
#cod_page_edit_form .fixed_btn_submit{ padding-right: 50px; }
#cod_page_edit_form .fixed_btn_submit .input{ margin-right: 60px; }
#cod_page_edit_form .input_checkbox_box.readonly{ cursor: no-drop; }
#cod_page_edit_form .big_title .go_set{ padding: 0 34px; height: 34px; line-height: 34px; border-radius: 35px; background-color: var(--GlobalBtnSecColor); color: #fff; font-size: 14px; }

#cod_page .plugins_app_title .global_app_tips a { color: #f68f44; }
