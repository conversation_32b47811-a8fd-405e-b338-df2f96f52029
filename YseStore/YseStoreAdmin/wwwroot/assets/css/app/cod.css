#cod .inside_table .r_con_table td.img .pro_info {display: flex; align-items: center;}
#cod .inside_table .r_con_table td.img .pro_info .pic_box {width: 84px; height: 84px; overflow: hidden; border-radius: 3px;}
#cod .inside_table .r_con_table td.img .pro_info .name {flex: 1; line-height: 23px; margin-left: 20px; color: #404852;}
#cod .inside_table .r_con_table td.img .pro_info .name>a { margin: 0; }

#cod .global_container {padding: 30px;}
#cod .big_title {height: 34px; line-height: 34px; margin: 0; padding: 10px 0 20px;}
#cod .big_title .btn_choose {height: 34px; line-height: 34px; float: right; color: #fff; background-color: var(--primaryColor); border-radius: 4px;}
#cod .rows label {line-height: 34px;}
#cod .tags_option_list {margin-bottom: 0;}
#cod .tags_option_list .input {padding: 5px 0 12px;}
#cod .tags_option_list .input_checkbox_box {margin-left: 30px;}
#cod .tags_option_list .input_checkbox_box:first-child {margin-left: 0;}
#cod .option_list {margin-bottom: 0;}
#cod .option_list .btn_attr_choice {margin-top: 12px; margin-bottom: 12px;}
#cod .product_table {display: none; width: calc(100% + 60px); margin: 10px -30px 0;}
#cod .product_table .pro_info {display: flex; align-items: center;}
#cod .product_table .pro_info .pic_box {width: 84px; height: 84px; overflow: hidden; border-radius: 3px;}
#cod .product_table .pro_info .name { flex: 1; line-height: 23px; margin-left: 20px; color: #404852; word-break: break-all; word-wrap: break-word; white-space: pre-wrap; }
#cod .product_table .pro_info .name>a { margin: 0; }

#fixed_right .global_app_tips {display: block; margin-top: 0;}
#fixed_right .global_app_tips a { color: #f68f44; }
#fixed_right .choose_list {margin-top: 18px; padding: 0 20px;}
#fixed_right .choose_list .choose_item {display: flex; align-items: center; line-height: 28px; margin-top: 7px; cursor: pointer;}
#fixed_right .choose_list .choose_item .btn_radio, #fixed_right .choose_list .choose_item .btn_checkbox {margin-right: 12px;}
#fixed_right .bg_no_table_data {height: 400px !important;}

#fixed_right .box_submit .select_all_box { display: inline-block; margin: 5.5px 20px 0 0; font-size: 14px; }
#fixed_right .box_submit .select_all_box .input_checkbox_box .input_checkbox { width: 15px; height: 15px; }

.plugins_app_box .btn_unused { background: #b0b2b1; border-color: #b0b2b1; cursor: no-drop; }
.plugins_app_box .global_app_tips,
.plugins_app_box .guide_index_box .guide_con .global_app_tips { display: block; margin: 18px 0; color: #1f2328; }
.plugins_app_box .global_app_tips a { color: #f68f44; }

.plugins_app_box .inside_table.prohibit_page::after { content: ''; position: absolute; width: 100%; height: 100%; top:0; left: 0; z-index: 100; background: rgba(255, 255, 255, 0.4); }
