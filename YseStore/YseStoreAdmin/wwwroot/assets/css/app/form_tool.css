#form_tool .bg_no_table_data .btn_add_item {width: 150px; height: 40px; line-height: 38px; margin: 25px auto; padding: 0; font-size: 16px;}
#form_tool .list_menu{margin-bottom: 10px;}
#form_tool .r_con_table.new tbody td, #form_tool .r_con_table.new tbody td *{font-size: 12px;word-break: break-all;}
#form_tool .r_con_table.new tbody td .content_status{display: inline-block; height: 22px; padding: 0 14px; border-radius: 8px;}
#form_tool .r_con_table.new tbody td .content_status.unread{background-color: #fbeded; color: #f35958;}
#form_tool .r_con_table.new tbody td .content_status.read{background-color: #eaf8ec; color: #0baf4d;}

#form_tool .r_con_table tr td .icon_edit{margin-left: 30px;}
#form_tool .r_con_table .field_item{display: inline-block;padding:2px 8px;margin-right: 5px;margin-bottom: 5px; line-height: 22px; background-color: #eaf7ff;border-radius: 5px;  vertical-align: top;}
#form_tool .r_con_table .data_view_btn{ text-decoration: underline;color:var(--GlobalLinkColor); padding-left: 10px;}
.box_form_tool_edit .delivery_box {display: flex; justify-content: space-between;}
.box_form_tool_edit .delivery_box[data-num="1"]{justify-content: center;}
.box_form_tool_edit .input_radio_side_box{min-height: 135px;}
.form_tool_edit .rows label span{font-size: 12px;color:#7d8d9e;}
.form_tool_edit .btn_field_add{background-color: var(--primaryColor);color: #fff;margin:25px 0 10px}
.form_tool_edit .btn_field_add:before, .form_tool_edit .btn_field_add:after{background-color: #fff;}
.field_content_row{overflow: hidden;}
.field_content_row .btn_option{display:block; width:34px; height:34px; line-height:38px; border:1px #ddd solid; border-radius:5px;text-align:center;}
.field_content_row .btn_option>i{display:inline-block; width:14px; height:14px; background-repeat:no-repeat; background-position:center;}
.field_content_row .btn_option_add{border-top-right-radius:0; border-bottom-right-radius:0;}
.field_content_row .btn_option_add>i{background-image:url(../../images/products/icon_square_add.png);}
.field_content_row .btn_option_remove{border-left-width:0; border-top-left-radius:0; border-bottom-left-radius:0;}
.field_content_row .btn_option_remove>i{background-image:url(../../images/products/icon_square_delete.png);}
.field_content_row .button.hide_add .btn_option_add{display:none;}
.field_content_row .button.hide_add .btn_option_remove{border-left-width:1px; border-radius:5px;}
.field_content_row .button.hide_remove .btn_option_add{border-radius:5px;}
.field_content_row .button.hide_remove .btn_option_remove{display:none;}
.global_form .field_content_row .rows{margin-bottom: 20px;}
.global_form .field_content_row .rows label{margin-right: 10px;}
.global_form .field_content_row .rows label,.global_form .field_content_row .rows .input,.field_content_row .button{float: left;}
#form_tool .r_con_table .unread_count{display: inline-block; height: 15px; line-height: 15px; margin-left: 5px; padding: 0 6px; font-size: 12px; color: #fff; text-align: center; border-radius: 8px; background-color: #f35958;}

.form_tool_edit .field_item { display: flex; align-items: center; justify-content: space-between; position: relative; border-radius: 5px; margin-bottom: 15px; padding:18px; background-color: #f8f9fb; }
.form_tool_edit .field_item em{width: 6px;height: 100%;position: absolute;left: 5px;top: 0;cursor: move;}
.form_tool_edit .field_item em::after{content: '';width: 6px;height: 14px;position: absolute;top: 50%;left: 13px;background-image: url(../../images/view/icon_view.png);background-position: -102px -98px;transform: translateY(-50%);}
.form_tool_edit .field_item:hover{background-color: #ecf5ff;}
.form_tool_edit .field_item .field_name { display: inline-block; width: 80%; margin-left: 16px; }
.form_tool_edit .field_item .field_opt{float: right;font-size: 12px;}
.form_tool_edit .field_item .field_opt.field_del_btn{margin-left: 8px;color:#7d8d9e}
.form_tool_edit .field_item .field_opt.field_edit_btn{color:#68B0FD}

.fixed_field_sort .set_field_item{position: relative; margin-bottom: 15px;padding:12px;background-color: #f4f5f7;border-radius: 5px;}
.fixed_field_sort .set_field_item em{width: 6px;height: 100%;position: absolute;left: 5px;top: 0;cursor: move;}
.fixed_field_sort .set_field_item em::after{content: '';width: 6px;height: 14px;position: absolute;top: 50%;left: 13px;background-image: url(../../images/view/icon_view.png);background-position: -102px -98px;transform: translateY(-50%);}
.fixed_field_sort .set_field_item:hover{background-color: #ecf5ff;}
.fixed_field_sort .set_field_item .field_name{margin-left: 20px;display: inline-block; width: calc(100% - 55px); color:#1f2328}
.fixed_field_sort .set_field_item:hover .field_name{color:var(--primaryColor);}
.fixed_field_sort .set_field_item:hover em::after{background-position: -42px -188px;}
.fixed_field_sort .set_field_item .input_checkbox_box{float: right;}
#field_form .global_select_box{height: auto;}

.form_tool_data_view .data_list .data_item{padding:20px 0;border-bottom: 1px solid #edf0f5;}
.form_tool_data_view .data_list .data_item .data_item_title{ width: 30%;}
.form_tool_data_view .data_list .data_item .data_item_value{ width: 70%;padding:0 18px;box-sizing: border-box;word-break: break-all;}
.form_tool_data_view .data_list .data_item .data_item_value .field_img .field_img_item{display: inline-block;position: relative; width: 56px;height: 56px;margin-right: 10px; border-radius: 5px;overflow: hidden;vertical-align: middle;}
.form_tool_data_view .data_list .data_item .data_item_value .field_img .field_img_item img{max-width: 100%;max-height: 100%;}
.form_tool_data_view .data_list .data_item .data_item_value .field_img .field_img_item .icon_multi_view{display: none; position: absolute; width: 100%;height: 100%; top:0;left:0;background-color: rgba(0,0,0,.5); background-repeat: no-repeat;background-position: center;}
.form_tool_data_view .data_list .data_item .data_item_value .field_img .field_img_item:hover .icon_multi_view{display: block;}
.form_tool_data_view .data_list .data_item .data_item_value .value_left{width: 100px;}
.form_tool_data_view .data_list .data_item .data_item_value .value_left img{max-width: 100%;}
.form_tool_data_view .data_list .data_item .data_item_value .value_right{width: calc( 100% - 112px );}
.form_tool_data_view .data_list .data_item .data_item_value .value_right .value_right_name{margin-bottom: 20px; line-height: 18px; color:#363636}
.form_tool_data_view .data_list .data_item .data_item_value .value_right .value_right_price .Price_1{color:#1a1a1a;font-size: 16px;}
.form_tool_data_view .data_list .data_item .data_item_value .value_right .value_right_price del{color:#999999;font-size: 12px;}
.form_tool_data_view .data_list .data_item .data_item_value .file_item{padding: 15px 20px; margin-top: 10px; display: block; background-color: #F7F7F7; position: relative; }
.form_tool_data_view .data_list .data_item .data_item_value .file_item:first-child{ margin-top: 0; }
.form_tool_data_view .data_list .data_item .data_item_value .file_item>span{ display: flex; align-items: center; justify-content: space-between; }
.form_tool_data_view .data_list .data_item .data_item_value .file_item>span .icon{ margin-right: 10px; width: 32px; height: 32px; flex: none; }
.form_tool_data_view .data_list .data_item .data_item_value .file_item>span .item{ flex: 1; }
.form_tool_data_view .data_list .data_item .data_item_value .file_item>span .item>span{ display: block; font-size: 12px; }
.form_tool_data_view .data_list .data_item .data_item_value .file_item>span .item .file_name{ margin-bottom: 5px; }

.field_edit_form .global_container{margin-bottom: 25px;}
.global_container.close_hidden{overflow: unset;margin-bottom: 100px;}
.global_container.close_hidden .bg_no_table_data{ max-height: 280px; }
.global_form .rows .tips,.global_form .rows .page_tips{font-size: 12px;color:#7d8d9e}
.global_form .rows .copy{height: 36px;line-height: 36px;color: #fff;background-color: var(--GlobalMainColor);border-radius: 5px;font-size: 14px;padding: 0 20px;margin-left: 8px;border: none;}
.global_form .rows .use_products_box{margin-top: 20px;}

.field_edit_form .color_row_box{margin-top: 10px;}
.field_edit_form .rows .color_rows{height: 22px;line-height: 22px;margin-bottom: 18px;}
.field_edit_form .rows .color_rows:last-child{margin-bottom: 0;}
.field_edit_form .rows .color_rows span{display: inline-block;margin-left: 10px;font-size: 12px;color: #404852;vertical-align: top;}
.field_edit_form .rows .color_rows .color_style{ width:46px; height:22px; border:1px solid #e5e5e5; text-indent:999px; border-radius:5px; cursor:pointer;box-sizing: border-box;vertical-align: top;}
.field_edit_form .rows .box_drop_double{ width: 350px; }

.form_tool_data .field_img{position: relative;}
.form_tool_data .field_img .field_img_txt{display: inline-block;padding-right: 15px;position: relative;cursor: pointer;}
.form_tool_data .field_img .field_img_txt i{width: 9px;height: 6px;position: absolute;right: 0;top:10px;background-image: url(../../images/orders/icon_arrow1.png);transition: all 0.3s;}
.form_tool_data .field_img:hover .field_img_txt i{transform: rotate(180deg);}
.form_tool_data .field_img:hover .field_img_container{opacity: 1;z-index: 1;}
.form_tool_data .field_img .field_img_container{position: absolute;width: 188px; padding:12px;background-color: #fff; left: 0;top: 100%;z-index: -1;opacity: 0; box-shadow: 0 0 12px 0px rgba(0,0,0,.2);}
.form_tool_data .field_img .field_img_container .field_img_item{display: inline-block;position: relative; width: 56px;height: 56px;margin-right: 10px; border-radius: 5px;overflow: hidden;vertical-align: middle;}
.form_tool_data .field_img .field_img_container .field_img_item img{max-width: 100%;max-height: 100%;}
.form_tool_data .field_img .field_img_container .field_img_item .icon_multi_view{display: none; position: absolute; width: 100%;height: 100%; top:0;left:0;background-color: rgba(0,0,0,.5); background-repeat: no-repeat;background-position: center;}
.form_tool_data .field_img .field_img_container .field_img_item:hover .icon_multi_view{display: block;}
.form_tool_data .field_img .field_img_container .field_img_item:last-child{margin-right: 0;}

.edit_table .icon_copy{ color:#0baf4d }
.edit_table .operation{ text-align: right; }

#form_tool.form_tool_data .field_file{position: relative;}
#form_tool.form_tool_data .field_file .field_file_txt{display: inline-block;padding-right: 15px;position: relative;cursor: pointer;}
#form_tool.form_tool_data .field_file .field_file_txt i{width: 9px;height: 6px;position: absolute;right: 0;top:10px;background-image: url(../../images/orders/icon_arrow1.png);transition: all 0.3s;}
#form_tool.form_tool_data .field_file:hover .field_file_txt i{transform: rotate(180deg);}
#form_tool.form_tool_data .field_file .field_file_container{ overflow-y: auto; position: absolute;width: 290px; padding:12px;background-color: #fff; left: 0;top: 100%;z-index: -1;opacity: 0;box-shadow: 0 0 12px 0px rgba(0,0,0,.2);}
#form_tool.form_tool_data .field_file .field_file_container>a:first-child{ margin-top: 0; }
#form_tool.form_tool_data .field_file .field_file_container>a{ margin-top: 12px; padding: 15px 20px; border-radius: 5px;  box-sizing: border-box; display: block; background-color: #F7F7F7; }
#form_tool.form_tool_data .field_file .field_file_container>a>span{ display: flex; align-items: center; justify-content: center; }
#form_tool.form_tool_data .field_file .field_file_container svg{ margin-right: 20px; width: 32px; height: 32px; display: inline-block; vertical-align: middle; }
#form_tool.form_tool_data .field_file .field_file_container i{ font-size: 16px; color: #555; }
#form_tool.form_tool_data .field_file .field_file_container .item { display: inline-block; vertical-align: middle; flex: 1; }
#form_tool.form_tool_data .field_file .field_file_container .item .file_icon{ display: block; }
#form_tool.form_tool_data tbody tr:last-of-type .field_file .field_file_container{top: auto;bottom:100%;box-shadow: 0 0 12px 0px rgba(0,0,0,.2);}
#form_tool.form_tool_data .field_file:hover .field_file_container{opacity: 1;z-index: 2;}
#form_tool.form_tool_data .field_file .field_file_container .field_file_item{display: inline-block;position: relative; width: 56px;height: 56px;margin-right: 10px; border-radius: 5px;overflow: hidden;vertical-align: middle;}
#form_tool.form_tool_data .field_file .field_file_container .field_file_item img{max-width: 100%;max-height: 100%;}
#form_tool.form_tool_data .field_file .field_file_container .field_file_item .icon_multi_view{display: none; position: absolute; width: 100%;height: 100%; top:0;left:0;background-color: rgba(0,0,0,.5); background-repeat: no-repeat;background-position: center;}
#form_tool.form_tool_data .field_file .field_file_container .field_file_item:hover .icon_multi_view{display: block;}
#form_tool.form_tool_data .field_file .field_file_container .field_file_item:last-child{margin-right: 0;}

.global_app_tips.obvious{ display: none; margin: 0 0 10px; padding: 9px; }
.global_app_tips.obvious a{ color: #F68F44; }

#email_notice_form .list_item { display: flex; justify-content: space-between; border-radius: 5px; width: 100%; margin-top: 25px; margin-bottom: 0; padding: 20px 24px; background-color: #F7F8FA; box-sizing: border-box; }
#email_notice_form .list_item .left { flex: 1; }
#email_notice_form .list_item .global_app_tips.hide { display: none; }
#email_notice_form .list_item:hover { background-color: #f1f8f5; }
#email_notice_form .box_submit .btn_cancel { border: 1px solid var(--primaryColor); color: #fff; background-color: var(--primaryColor); }
