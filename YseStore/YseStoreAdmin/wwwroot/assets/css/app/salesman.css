/********************************** 没有权限页面 start ***********************************/
.permission_tips_box{position: relative; text-align: center; padding: 10% 0;}
.permission_tips_box .permission_tips{ position: relative; top: 0; right: 0; bottom: 0; left: 0; min-width: 350px; margin: auto; font-size: 22px;}
.permission_tips_box .permission_tips .txt{padding: 12px 0;}
/********************************** 没有权限页面 end ***********************************/

.plugins_app_menu{height: 62px; padding: 0 30px;}
.plugins_app_menu li{height: 62px; }
.plugins_app_box .plugins_app_menu li a{line-height: 30px; margin: 16px 0; padding: 0 40px; border-radius: 5px; }
.plugins_app_box .plugins_app_menu li .current{background-color: #EDF5FF;}
.fixed_assign .rows label, .fixed_right_add_salesman .rows label{display: block; padding: 8px 0; color: #000;}
.fixed_right_add_salesman .bg_no_table_data .content{padding-top: 200px; background-size: 300px auto;}
.fixed_assign .bg_no_table_data .content{padding-top: 165px; background-size: 250px auto;}
.box_drop_double dt.box_checkbox_list{min-height: 32px;}
.btn_attr_choice.current, .btn_item_choice.current{background-color: #daf2fe;}
.btn_attr_choice.current>i, .btn_item_choice.current>i{background-color: #daf2fe;}
#salesman .operation .del, #salesman .operation .assign, #salesman .operation .views{overflow: hidden; display: block; width: 42px; height: 42px; line-height: 42px; text-align: center; text-indent: 100%; margin: 0 auto; background-color: #fff; background-repeat: no-repeat; background-position: center; border-radius: 50%; opacity: 0; transition:all .4s; -webkit-transition:all .4s; transform:rotate(0); -webkit-transform:rotate(0);}
#salesman .operation .del{background-image: url(../../images/frame/icon_circle_delete_current.png);}
#salesman .operation .assign{background-image: url(../../images/frame/icon_circle_assign_current.png);}
#salesman .operation .views{background-image: url(../../images/mta/icon_view.png); margin-left: 10px;margin-right: 10px;}
#salesman tr:hover .operation .del, #salesman tr:hover .operation .assign, #salesman tr:hover .operation .views{opacity: 1;}
#salesman .operation .del:hover, #salesman .operation .assign:hover, #salesman .operation .views:hover{text-indent: 0; background-position: -42px center;}
#salesman .search_form{ margin-top: 0; }
#salesman table .link{white-space: nowrap;}
#salesman table .copy_link {display: inline-block; padding-left: 20px; line-height: 22px; margin-top: 4px; background: url(../../images/products/icon_copy.png) no-repeat left center; color: var(--GlobalLinkColor);}

#salesman_add .box_drop_double dd .drop_menu .item>span.item_name { width: 330px; }
#salesman .list_menu_button { padding: 0; }
#salesman .r_con_table thead td a { color: #404852; }
#salesman.salesman_views .head { display: flex; flex-direction: row; justify-content: flex-start; color: #7d8d9e; line-height: 32px; }
#salesman.salesman_views .head .dateselector .inner  { background-color: #fff; }
#salesman.salesman_views .global_container { overflow: unset; margin-top: 20px; }
#salesman.salesman_views .global_container.salesman_orders_box { padding: 20px 0; }
#salesman.salesman_views .global_container.salesman_orders_box .big_title { padding: 0 20px}
#salesman.salesman_views .box_data { display: flex; justify-content: space-between; align-items: flex-start; height: 125px; }
#salesman.salesman_views .box_data .data_item { width: calc(100% / 2 - 10px); padding: 30px; background-color: #f7f7f7; box-sizing: border-box; }
#salesman.salesman_views .box_data .data_item .data_item_title { margin-bottom: 15px; font-size: 14px; color: #404852; }
#salesman.salesman_views .box_data .data_item .data_item_stats { font-weight: bold; color:#1f2328; font-size: 24px; }
#salesman.salesman_views .salesman_orders_box table,
#salesman.salesman_views .salesman_orders_box .bg_no_table_data { display: none; }
#salesman.salesman_views .salesman_orders_box .loader_content { min-height: 450px; }
body .loader_content { position: relative; }
body #salesman .loader { height: 100% !important; }

#btn_salesman_fixed_show { display: none; }