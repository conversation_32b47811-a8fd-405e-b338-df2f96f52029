#aliexpress_sync_form .group_box{position: relative; color: #333; cursor:default;}
#aliexpress_sync_form .group_box .select_value{position: relative; height: 36px; line-height: 34px; width: 100%; padding: 0 11px; border: 1px #ccdced solid; box-sizing: border-box; border-radius: 5px; background: #fff;}
#aliexpress_sync_form .group_box .select_value::after{content: '';position: absolute;  display: block; width: 0; height: 0;  right: 8px; top: 15px; border: 5px solid transparent; border-top-color: #888;}
#aliexpress_sync_form .group_box .loader{position: absolute; top: 36px; left: 0; height: 150px; width: 100%; z-index: 1; background: #fff; border: 1px solid #888; box-sizing: border-box; display: none;}
#aliexpress_sync_form .group_box ul{overflow: auto; position: absolute; width: 100%; max-height: 400px; border: 1px solid #bbb; border-radius: 10px; box-sizing: border-box; background: #fff; box-shadow: 0 5px 10px #aaa;}
#aliexpress_sync_form .group_box ul li{padding: 1px 11px;}
#aliexpress_sync_form .group_box ul li:hover{background: #1e90ff; color: #fff;}
#aliexpress_sync_form .group_box ul li.group:hover{background: none; color: #333;}
#aliexpress_sync_form .group_box ul li.group{font-weight: bold;}
#aliexpress_sync_form .group_box ul li.sub_group{padding-left: 22px;}

.error_tips { margin-bottom: 20px; color: #333; }
.error_tips .global_app_tips{ padding: 10px 15px; border: 1px solid #F68F44; border-radius: 5px; background-color: #FFFBF4; }
.error_tips .global_app_tips a{ color: #F68F44; }

#fixed_right .item_container { margin-bottom: 15px; padding: 25px; background-color: #F8F9FB; }
#fixed_right .item_name{ display: block; margin-top: 10px; font-size: 14px; color: #000; }
