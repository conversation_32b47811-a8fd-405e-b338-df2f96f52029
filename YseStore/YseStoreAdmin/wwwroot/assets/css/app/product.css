#products_switch .app_list_menu .list_menu_button .set {color: #404852; background-color: #fff; border: 1px #ced4da solid; box-sizing: border-box; -webkit-box-sizing: border-box;}
#products_switch .mobile_description.hide{ display: none; }

.box_related_info {position: relative;}
.box_related_info .related_txt {display: inline-block; padding-right: 15px; position: relative; cursor: pointer;}
.box_related_info .related_txt i {width: 9px; height: 6px; position: absolute; right: 0; top:7px; background-image: url(../../images/orders/icon_arrow1.png); transition: all 0.3s;}
.box_related_info.current .related_txt i {transform: rotate(180deg);}
.box_related_info .related_container {display: none; position: absolute; left: 0; top: 100%; z-index: 1;}
.box_related_info .related_box {max-width: 310px; max-height: 400px; overflow-y: auto; margin: 7px 0; padding: 20px; background-color: #fff; box-sizing: border-box; box-shadow: 0 0 10px 0 rgba(0, 0, 0, 0.2); border-radius: 5px;}
.box_related_info .related_box::-webkit-scrollbar {width: 5px; background-color: #fff; border-radius: 5px;}
.box_related_info .related_box::-webkit-scrollbar-thumb {background-color: rgba(0,0,0,.1); border-radius: 5px;}
.box_related_info .related_box::-webkit-scrollbar-thumb:hover {background-color: rgba(0, 0, 0, 0.3);}
.box_related_info .related_box .related_list .item {display: flex; height: 50px; overflow: hidden; margin-top: 20px;}
.box_related_info .related_box .related_list .item .item_img {width: 50px; height: 50px; overflow: hidden; text-align: center; border-radius: 4px;}
.box_related_info .related_box .related_list .item .item_img img {max-width: 100%; max-height: 100%; border-radius: 4px; overflow: hidden;}
.box_related_info .related_box .related_list .item .item_info {width: 200px; margin-left: 20px;}
.box_related_info .related_box .related_list .item .item_info .info_name {line-height: normal; white-space: normal;}
.box_related_info .related_box .related_list .item:first-child {margin-top: 0;}
.box_related_info .related_box .related_list .related_list_load_more {display: block; margin-top: 10px; text-align: center; font-size: 14px; color: #000; transition: all .2s;}
.box_related_info .related_box .related_list.loading {padding-bottom: 30px; background-image: url(../../images/frame/loading_oth.gif); background-position: bottom; background-repeat: no-repeat;}
.box_related_info .related_box .related_list[data-type=categories] .item {height: auto;}
.box_related_info .related_box .related_list[data-type=categories] .item .item_info {width: 150px; margin-left: 0;}

.box_switch_edit .rows {margin-bottom: 28px;}
.box_switch_edit .rows>label {display: block; line-height: 24px;}
.box_switch_edit .rows .input {margin: 6px 0;}
.box_switch_edit .rows .input_button {margin: 16px 0 5px; text-align: center; font-size: 0;}
.box_switch_edit .rows .input_button .btn_global {margin: 0 5px;}

#edit_form .box_drop_double {max-width: 100%;}
#edit_form .use_products_box .input.has_error .box_drop_double dt.box_checkbox_list {border-color: #e41a23;}
#edit_form .box_content {margin-bottom: 10px;}

.box_switch_set .box_set_list .set_item {position: relative; margin-top: 20px; padding: 8px; cursor: pointer; box-shadow: 0 0 10px 0 rgba(0,0,0,0.1); border: 2px solid transparent;}
.box_switch_set .box_set_list .set_item input {display: none;}
.box_switch_set .box_set_list .set_item img {display: block; max-width: 100%;}
.box_switch_set .box_set_list .set_item .set_item_title {line-height: 24px; margin-bottom: 3px; color:#000;}
.box_switch_set .box_set_list .set_item:first-child {margin-top: 0;}
.box_switch_set .box_set_list .set_item:hover, .box_switch_set .box_set_list .set_item.current {border: 2px var(--primaryColor) solid;}
.box_switch_set .box_set_list .set_item:hover::after, .box_switch_set .box_set_list .set_item.current::after {width: 24px; height: 24px; content: ''; position: absolute; top: -12px; left: -12px; background-image: url(../../images/view/icon_view.png); background-position: -63px -153px;}