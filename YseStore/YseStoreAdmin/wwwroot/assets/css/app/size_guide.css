#size_guide .blank5{margin: 0px auto;clear: both;height: 5px;font-size: 1px;overflow: hidden;}
#size_guide .global_container{overflow: unset;}
#size_guide .product_menu{min-height:35px; margin-bottom:15px;}
#size_guide .product_menu_item{display:inline-block; vertical-align:top; position:relative;}
#size_guide .product_menu_item>button{display:block; min-height:35px; line-height:35px; background-color:transparent; border:0; padding:0 5px; color:var(--primaryColor);}
#size_guide .product_menu_item>button>em{display:inline-block; vertical-align:top; width:18px; height:18px; margin-right:3px; position: relative;}
#size_guide .product_menu_item>button.btn_menu_view>em::before{content: "\e609"; position: absolute;top:0;left: 0;width: 100%;height: 100%;font-size: 18px; font-family: "iconfont" !important;}
#size_guide .product_menu_item.disabled>button{color:#888}
#size_guide .use_group_box{display: none;}
#size_guide .box_type_menu{margin-bottom: 10px;}
#size_guide .attr_select_box .box_drop_double{max-width:unset ;}
#size_guide .attr_select_box .box_drop_double dt .box_select{max-width: unset;background: unset;}
#size_guide .attr_select_box .box_drop_double .box_input{height: 38px;}
#size_guide .option_btn{display: inline-block; height: 30px;margin-left: 10px; border:1px solid #ccdced;background-color: #fff;color:#555555;text-align: center;line-height: 30px;border-radius: 15px;padding:0 28px;font-size: 14px;vertical-align: middle;}
#size_guide .option_btn.add_btn{background-color: #0baf4d;color:#fff;}
#size_guide .global_container.guide_box{padding:20px 0;}
#size_guide .guide_box .big_title{padding:0 20px 28px; margin-bottom: 0;}
#size_guide .guide_box .guide_table{border-top:1px solid #f1f1f1;}
#size_guide .guide_box .bg_no_table_data{height: 240px!important;}
#size_guide .guide_box .guide_operation_btn{display: flex;align-items: center;height: 40px;justify-content: space-between;}
#size_guide .guide_box .inside_container{padding: 0;text-align: right;}
#size_guide .guide_box .inside_container .inside_menu{margin:13px 26px}
#size_guide .guide_show_box .r_con_table tbody[data-unit=IN]{display: none;}

#size_guide .measurement_box{width: 100%;margin-top: 18px;}
#size_guide .measurement_pic{width: 382px;float: right;}
#size_guide .measurement_pic .multi_img{margin-top: 0;}
#size_guide .measurement_pic .multi_img .img{width: 382px;float: none;margin: auto;}
#size_guide .measurement_pic .multi_img .img .preview_pic{width: 382px;height: 240px;margin:auto;}
#size_guide .measurement_pic .multi_img .img .upload_btn{width: 380px;height: 238px; background: #f8f9fb url(../../images/frame/bg_multi_img_add.png) no-repeat center center;border: 1px dashed #ccdced;border-radius: 5px;}
#size_guide .measurement_pic .multi_img .img .preview_pic>a{width: 370px;height: 228px;}
#size_guide .measurement_info{width: 545px;float: left;min-height: 240px;}
#size_guide .measurement_info .measurement_item{padding:25px;margin-bottom: 10px; background-color: #f8f9fb;display: flex;justify-content: space-between;align-items: center;}
#size_guide .measurement_info .measurement_item .measurement_item_message{width: 80%;color:#404852}
#size_guide .measurement_info .measurement_item .measurement_item_message .item_title{margin-bottom:15px; font-size: 14px;font-weight: bold;}
#size_guide .measurement_info .measurement_item .measurement_item_message .item_desc{line-height: 20px;}
#size_guide .measurement_info .measurement_item .measurement_item_operation{width: 20%;}
#size_guide .measurement_box .measurement_item .measurement_item_operation .oper_icon{display:inline-block; vertical-align:middle; width:30px; height:30px; line-height:30px; margin-left:7px; border-radius:36px; background-image: none; background-color: #eaf7ff; text-align:center; font-size:0 !important; transition:all 0.3s ease-out; position:relative; font-family: "iconfont" !important; font-size: 16px; font-style: normal; -webkit-font-smoothing: antialiased; -moz-osx-font-smoothing: grayscale;color:var(--primaryColor);}
#size_guide .measurement_box .measurement_item .measurement_item_operation .oper_icon:before{ font-size: 14px; transition: all 0.3s ease-out; position: absolute; top: 50%; left: 50%; transform: translate(-50%,-50%); }
#size_guide .measurement_box .measurement_item .measurement_item_operation .oper_icon:hover:before{ color: #fff; }
#size_guide .measurement_box .measurement_item:hover td .oper_icon{background-color:#fff;}
#size_guide .measurement_box .measurement_item .measurement_item_operation .oper_icon:hover{ background-color:var(--primaryColor);}
#size_guide .measurement_box .measurement_item .measurement_item_operation .oper_icon.disabled {cursor: no-drop; background-color: #f3f3f3;}
#size_guide .measurement_box .measurement_item .measurement_item_operation .oper_icon.disabled:before{ color: #c6c6c6; }
#size_guide .measurement_box .measurement_item .measurement_item_operation .icon_edit:before{ content: "\e6d3"; font-size: 15px; }
#size_guide .measurement_box .measurement_item .measurement_item_operation .icon_del:before{ content: "\e60b"; }
#size_guide .measurement_box .bg_no_table_data{background-color: #f8f9fb;height: 240px!important;}
.fixed_guide_add_measurement #add_measurement_form .full_textarea{height: 98px;}
.fixed_guide_field_set .field_content_row{overflow: hidden;position: relative;padding:20px 24px 20px 54px;margin-bottom: 10px;background-color: #f8f9fb;}
.fixed_guide_field_set .field_content_row .field_myorder{width: 6px;height: 100%;position: absolute;left: 5px;top: 0;cursor: move;}
.fixed_guide_field_set .field_content_row .field_myorder::after{content: '';width: 6px;height: 14px;position: absolute;top: 50%;left: 23px;background-image: url(../../images/view/icon_view.png);background-position: -102px -98px;transform: translateY(-50%);}
.fixed_guide_field_set .field_content_row .button{float: left;}
.fixed_guide_field_set .field_content_row .btn_field{display:block; width:34px; height:34px; line-height:38px; border:1px #ccdced solid; border-radius:5px;text-align:center;}
.fixed_guide_field_set .field_content_row .btn_field>i{display:inline-block; width:14px; height:14px; background-repeat:no-repeat; background-position:center;}
.fixed_guide_field_set .field_content_row .btn_field_remove>i{background-image:url(../../images/products/icon_square_delete.png);}
.fixed_guide_field_set .field_content_row .button.hide_remove .btn_field_add{border-radius:5px;}
.fixed_guide_field_set .field_content_row .button.hide_remove .btn_field_remove{display:none;}
.fixed_guide_field_set .field_content_row .rows .box_input {border:1px solid #ccdced;background-color: #fff;}
.fixed_guide_field_set .global_form .rows{margin-bottom: 0;}
.fixed_guide_field_set .field_add_btn .btn_add_item{height: 33px;line-height: 33px;padding: 0;font-size: 14px;background-color: transparent;}
.fixed_guide_field_set .field_add_btn .btn_add_item::before {content: "+"; display: inline-block; vertical-align: top; width: 16px; height: 16px; line-height: 1; overflow: hidden; margin-top: 10px; margin-right: 6px; text-align: center; font-size: 14px; color: #fff; background-color: var(--primaryColor); border-radius: 50px;}
.fixed_guide_add_data .fixed_unit_box .inside_container{padding: 0; }
.fixed_guide_add_data .fixed_unit_box .inside_container .inside_menu{margin:0}
.fixed_guide_add_data .fixed_data_box .unit_data_box[data-unit=IN]{display: none;}
.fixed_guide_add_data .fixed_data_box .unit_data_box .box_input[readonly]{ background-color: #F4F5F7; cursor: no-drop; }
.fixed_guide_import .input_radio_side_box{width: 100%;}

.guide_view_wrapper{position:fixed;width: 780px;height: 90%; background-color: #fff; top:50%;left: 50%;border-radius: 5px;padding:0 30px 20px;transform: translate(-50%,-50%);z-index: 10001;overflow-y: auto;}
.guide_view_wrapper::-webkit-scrollbar{width: 5px; background: #fff;border-radius: 5px;}
.guide_view_wrapper::-webkit-scrollbar-thumb{background: rgba(0, 0, 0, 0.1);border-radius: 5px;}
.guide_view_wrapper::-webkit-scrollbar-thumb:hover{background: rgba(0, 0, 0, 0.3);}
.guide_view_wrapper .guide_title{margin: 70px 0 20px; font-size: 20px;font-weight: bold; color:#222222; text-align: center;}
.guide_view_wrapper .guide_desc{line-height: 20px; font-size:12px; color:#888888;}
.guide_view_wrapper .guide_close{position: absolute;top:20px;right: 20px;cursor: pointer;}
.guide_view_wrapper .guide_close::after{content: "\e664";color:#666666;font-size: 24px;font-family: "iconfont" !important;}
.guide_view_wrapper .guide_measurement{margin-top: 45px;}
.guide_view_wrapper .guide_measurement .guide_measurement_title{margin-bottom: 48px; font-size: 20px;font-weight: bold; color:#222222; text-align: center;}
.guide_view_wrapper .guide_measurement .guide_measurement_content{display: flex;justify-content: space-between;align-items: center; }
.guide_view_wrapper .guide_measurement .guide_measurement_content .guide_measurement_info{flex: 1;}
.guide_view_wrapper .guide_measurement .guide_measurement_content .guide_measurement_info .guide_measurement_item{margin-bottom: 25px;}
.guide_view_wrapper .guide_measurement .guide_measurement_content .guide_measurement_info .guide_measurement_item .guide_measurement_item_title{margin-bottom: 12px; font-size: 14px;color:#000;font-weight: bold;}
.guide_view_wrapper .guide_measurement .guide_measurement_content .guide_measurement_info .guide_measurement_item .guide_measurement_item_title em{display: inline-block;width: 24px;height: 24px;line-height: 24px; margin-right: 6px; background-color: #000;color:#fff; border-radius: 50%;text-align: center;}
.guide_view_wrapper .guide_measurement .guide_measurement_content .guide_measurement_info .guide_measurement_item .guide_measurement_item_desc{font-size: 12px;color:#666666;line-height: 20px;}
.guide_view_wrapper .guide_measurement .guide_measurement_content .guide_measurement_img{width: 310px;margin-left: 36px;}
.guide_view_wrapper .guide_measurement .guide_measurement_content .guide_measurement_img img{max-width: 100%;}
.guide_view_wrapper .guide_table{margin-bottom:12px;}
.guide_view_wrapper .guide_table .guide_table_switch{margin:8px 0;font-size: 0;text-align: right;}
.guide_view_wrapper .guide_table .guide_table_switch .table_switch_item{display: inline-block;width: 58px;height: 30px;line-height: 30px; background-color: #fff;border:1px solid #000;color:#000000;font-size: 14px; vertical-align: middle;text-align: center;box-sizing: border-box;}
.guide_view_wrapper .guide_table .guide_table_switch .table_switch_item.cur{color:#fff;background-color: #000;}
.guide_view_wrapper .guide_table .guide_table_info table{width: 100%;text-align: center;border:1px solid #e1e1e1}
.guide_view_wrapper .guide_table .guide_table_info table thead td{font-size: 14px;color:#222222;font-weight: bold;}
.guide_view_wrapper .guide_table .guide_table_info table td{padding:16px 0;}
.guide_view_wrapper .guide_table .guide_table_info table tbody[unit=IN]{display: none;}

.box_guide_edit .guide_box{display: flex;justify-content: space-between;align-items: flex-start;flex-wrap: wrap;}
.box_guide_edit .guide_box .input_radio_side_box{vertical-align: top;width: 360px;min-height: 115px;}

.r_con_wrap .guide_global_form .big_title .tips{display: inline-block; font-size: 12px;color:#7d8d9e;}
#size_guide .box_select{max-width:unset;background: unset;}
#size_guide .scope_box .box_drop_double{max-width: unset;} 

#field_set_form .checkbox_select_box{}
#field_set_form .checkbox_select_box .select_row{ margin-bottom: 10px; padding: 20px 30px 20px 35px; width: 100%; box-sizing: border-box;border-radius: 0; border: 0; height: auto; line-height: auto; background-color: #F8F9FB; position: relative; }
#field_set_form .checkbox_select_box .select_row:before{ position: absolute; top: 26px; left: 10px; }
#field_set_form .checkbox_select_box .select_row .title{ font-size: 14px; }
#field_set_form .checkbox_select_box .select_row .unit{ font-size: 14px; color: #7e8da0; }
#field_set_form .checkbox_select_box .select_row .box_select{ margin: 0 5px; display: inline-block; vertical-align: middle; width: auto; height: 30px; background-color: #fff; }
#field_set_form .checkbox_select_box .select_row .box_select select{ display: inline-block; vertical-align: top; height: 30px; }
