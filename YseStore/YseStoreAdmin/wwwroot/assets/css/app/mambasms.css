.mambasms_items_box{width: 984px; margin: 0 auto;}
.mambasms_items_box .global_container{margin: 0 auto 20px; padding: 30px;}
.mambasms_items_box .global_container .mambasms_list{display: flex; align-items: center; justify-content: space-between;}
.mambasms_items_box .global_container .mambasms_list .left{ width: 80%;}
.mambasms_items_box .global_container .mambasms_list .left .tit{line-height: 36px; font-size: 20px; color: #1f2328;}
.mambasms_items_box .global_container .mambasms_list .left .desc{line-height: 19px; font-size: 12px; color: #7d8d9e;margin-top: 10px;}
.mambasms_items_box .global_container .mambasms_list .rig_btn{height: 34px; line-height: 34px; padding: 0 22px; color: #fff; background: #0baf4d; border-radius: 3px;}

.authorization_container{width: 984px; margin: 0 auto;}
.authorization_container .global_container{padding: 30px;overflow: visible; box-sizing: border-box;}
.authorization_container .author_title{font-size: 20px; color: var(--GlobalTitleColor); line-height: 42px;}
.authorization_container .sec_title{line-height: 21px; font-size: 14px; color: var(--GlobalAssistColor); margin: 10px 0 20px;}
.authorization_container .author_subtitle{font-size: 12px; color: #7F7F7F;}
.authorization_container .acc_list{padding: 19px 24px;background-color: #f7f9fb;border-radius: 3px;margin: 20px 0 11px;display: flex;align-items: center;flex-wrap: wrap;}
.authorization_container .acc_list .left{color: var(--GlobalTextColor);flex:1;}
.authorization_container .acc_list .rig_btn{margin-left: 10px;line-height: 36px;padding: 0 22px;border-radius: 4px;background-color: var(--GlobalMainColor);color: #fff;}
.content_list{padding: 24px; background-color: #f7f9fb;border-radius: 3px;margin-bottom: 11px;}
.content_list .sender_tips{font-size: 12px; color: var(--GlobalAssistColor); height: 30px; line-height: 30px;}
.content_list .sender_tips a{color: var(--GlobalMainColor);}

.step_box{display: flex;justify-content: center;padding: 30px 0;}
.step_box .list{width: 330px;text-align: center;}
.step_box .list .num{position: relative;}
.step_box .list .num::before{content: '';position: absolute;top: 0;right: 50%;bottom: 0;left: 0;margin:auto;height: 1px;background-color: #d7d7d7;z-index: 0;}
.step_box .list .num::after{content: '';position: absolute;top: 0;right: 0;bottom: 0;left: 50%;margin:auto;height: 1px;background-color: #d7d7d7;z-index: 0;}
.step_box .list .num span{display: block;width: 26px;height: 26px;line-height: 26px;border:1px solid #c5ccd3;color: #c5ccd3;border-radius: 26px;font-size: 14px;background-color: #fff;margin: auto;position: relative;z-index: 1;}
.step_box .list .tit{padding: 5px 0;line-height: 26px;font-size: 14px;color: var(--GlobalTextColor);}
.step_box .list:first-child .num::before{display: none;}
.step_box .list:last-child .num::after{display: none;}
.step_box .current .num span{color: var(--GlobalMainColor);border-color: var(--GlobalMainColor);}
.step_box .current .tit{color: var(--GlobalMainColor);}

.oauth_desc_box .oauth_desc_list{display: flex; justify-content: space-between; margin: 20px 0 11px;}
.oauth_desc_box .oauth_desc_list .item{width: 100%; background: #f8f9fb; padding: 20px 28px 24px; border-radius: 5px; box-sizing: border-box;}
.oauth_desc_box .oauth_desc_list .item .item_title{line-height: 32px; font-size: 14px; color: #404853;}
.oauth_desc_box .oauth_desc_list .item .item_desc{line-height: 24px; font-size: 14px; color: #7d8d9d;}

.simulate_select_box{width: 346px; position: relative;}
.simulate_select_box .select{display: flex; padding: 0px 10px; min-height: 36px;line-height: 36px;cursor: pointer;position: relative;flex-wrap: wrap;border:1px solid var(--GlobalBorderColor);border-radius: 5px;}
.simulate_select_box .select.focus{border-color: var(--primaryColor);outline: none!important;box-shadow: 0 0 5px rgba(7,187,139,0.3);}
.simulate_select_box .select:after{content: '';width: 9px;height: 6px;background: url(../../images/frame/icon_select.png) no-repeat left center;position: absolute;top: 0;right: 9px;bottom: 0;margin:auto;transition: all 0.3s ease-in-out;}
.simulate_select_box .select.focus:after{transform: rotate(-180deg);}
.simulate_select_box .select.has_error{border-color: #f00;}
.simulate_select_box .placeholder{font-size: 12px;color: var(--GlobalAssistColor);}
.simulate_select_box .selected{font-size: 12px;}
.simulate_select_box .option_box{display: none;width: 346px;position: absolute;top: calc(100% + 2px);left: -1px;background-color: #fff;border-radius: 5px;z-index: 1; padding-top: 10px;}
.simulate_select_box .global_app_tips{margin: 10px;}
.simulate_select_box .option_list{max-height: 320px;}
.simulate_select_box .option_list .item{line-height: 36px;color: var(--GlobalTextColor);padding: 0 18px;cursor: pointer;}
.simulate_select_box .option_list .item span{color: var(--GlobalAssistColor);}
.simulate_select_box .option_list .current, .simulate_select_box .option_list .current span{color: var(--GlobalMainColor);cursor: default;}
.simulate_select_box .option_list .disabled, .simulate_select_box .option_list .disabled span{color: #cccccc;cursor: no-drop;}
.simulate_select_box .no_data{display: none; padding: 22px;line-height: 22px;text-align: center; font-size: 14px;color:var(--GlobalAssistColor);}
.simulate_select_box .btn_load_more{display: block;line-height: 30px;text-align: center;color: var(--GlobalTextColor);}
.simulate_select_box .foot{display: flex;justify-content: space-between;padding: 0 20px;line-height: 40px;margin-top: 10px; font-size: 14px;border-top: 1px solid var(--GlobalDividingLineColor);}
.simulate_select_box .foot .btn_refresh{padding-left: 20px;color: var(--GlobalTextColor);background: url(../../images/frame/icon_refresh1.png) no-repeat left center;}
.simulate_select_box .foot .btn_refresh .ing{display: none;}
.simulate_select_box .foot .btn_refresh.refreshing{background-image: url(../../images/frame/icon_refresh_gif.gif);background-size: 12px 12px;}
.simulate_select_box .foot .btn_refresh.refreshing .df{display: none;}
.simulate_select_box .foot .btn_refresh.refreshing .ing{display: block;}
.simulate_select_box .foot .btn_set{color:var(--GlobalMainColor);}

#mambasms .top_title{font-size: 20px;line-height: 32px;color: var(--GlobalTitleColor); margin-bottom: 14px; text-indent: 3px;}
#mambasms .list_menu .g_btn_main{padding: 0 18px;}
#mambasms .list_menu .remaining_emails{line-height: 36px; padding-right: 20px;}
#mambasms .list_menu .remaining_emails span{color: #0baf4d;}
#mambasms .list_menu_content {min-height: 51px; line-height: 51px; padding: 0 24px; background-color: #fff; z-index: 2;}
#mambasms .inside_table{padding: 20px 0;}

.mambasms_pannel .global_container{margin-top: 24px; padding: 19px 24px 30px 24px; overflow: unset;}
.mambasms_pannel .use_group_box{margin-top: 20px;}
.mambasms_pannel .global_container .rows:last-child{margin-bottom: 0;}
#mambasms .mambasms_pannel {width: 1250px; margin: 0 auto 20px;}

#mambasms table > thead {background-color: #f4f6f9;}
#mambasms table > thead th {font-weight: bold; padding: 0 15px;}
#mambasms table > tbody td:first-child {padding-left: 15px;}
#mambasms table > tbody td:last-child {padding-right: 15px;}
#mambasms table > tbody .actions {text-align: left;}

/* 表头:全选按钮 */
#mambasms table > thead th .list_menu_button .open {line-height: 32px;}
#mambasms .table_menu_button li {display: flex; flex-direction: row; justify-content: flex-start; align-items: center;}

.mail_subject {width: 470px; overflow: hidden; white-space: nowrap; text-overflow: ellipsis;}
.send_group{color: #7F7F7F;}

#mambasms .campaign_info {width: 0 auto;}
#mambasms .campaign_info .inner{display:flex; flex-direction:row; justify-content:flex-start; }
#mambasms .campaign_info .inner .left {width: 860px; margin-right: 10px;}
#mambasms .campaign_info .inner .right {width: 380px;}
#mambasms .campaign_info .big_title {margin-bottom: 5px;}
#mambasms .campaign_info .right .item {margin-bottom: 30px;}
#mambasms .campaign_info .right .item:last-child {margin-bottom: 0;}

.win_alert .win_tips .email_need{color: #0baf4d;}
.win_alert .win_tips span{padding: 0 8px;}