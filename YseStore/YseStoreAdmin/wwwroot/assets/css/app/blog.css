.blog .plugins_app_menu{margin-bottom: 10px;}
#blog_inside .set_add{margin-top:0;}
#blog_inside .blog_nav .d_del{width:24px; height:24px; overflow:hidden; margin-top:8px; margin-left:10px; display:inline-block; vertical-align:top;}
#blog_inside .seo_box .box_input{width:90%;}

#blog .global_app_tips{ margin-top: 0; margin-bottom: 10px; }
#blog .global_app_tips a{ color: #f68f44; }
#blog .blog_top_title{ display: flex; align-items: center; justify-content: space-between; }
#blog .version_btn.btn_global{ margin-top: 50px; color: #fff; background-color: var(--primaryColor); z-index: 1; }
#blog .sync_btn.btn_global{ margin-top: 50px; padding: 0 15px; margin-right: 15px; color: #000; background-color: #fff; border: 1px solid #000; z-index: 1; }
#blog .img_btn{ margin-bottom: 15px; display: block; width: 100%; height: 90px; background: url('../../images/plugins/app/blog/blog_new_img.jpg') no-repeat center center / cover; position: relative; }
#blog .img_btn .text_info{ position: absolute; top: 0; left: 0; bottom: 0; right: 0; margin: 0 auto; width: 100%; line-height: 90px; text-align: center; font-size: 36px; color: #ffffff; overflow: hidden; }
#blog .img_btn .text_info span{ margin-left: 30px; display: inline-block; vertical-align: text-bottom; padding: 0 42px; height: 42px; line-height: 42px; background-color: #ffffff; border-radius: 35px; font-size: 24px; color: #666666; }

#blog_inside .fixed_btn_submit{ left: 180px; }
#blog_inside .review_box{border-bottom: 1px solid #e9e9e9;padding-bottom: 28px;padding-top: 16px;}
#blog_inside .review_box .star_box{height: 30px;line-height: 30px;}
#blog_inside .review_box .agree{display: inline-block;height: 30px;line-height: 30px;margin-left: 20px;padding-left: 27px;color: #999;font-size: 14px;background: url(../../images/products/icon_good.png) no-repeat left center;}
#blog_inside .review_box .msg{line-height: 20px;font-size: 14px;padding-bottom: 11px;padding-top: 16px;}
#blog_inside .review_box .r_img{height: 60px;margin: 14px 0;}
#blog_inside .review_box .r_img .list{float: left;width: 58px;height: 58px;margin-right: 7px;border:1px solid #dddddd;text-align: center;vertical-align: middle;font-size: 0;}
#blog_inside .review_box .r_img .list img{vertical-align: middle;max-width: 100%;max-height: 100%;}
#blog_inside .review_box .r_img .list:after{display: inline-block;vertical-align: middle;height: 100%;content: '';}
#blog_inside .review_box .info{padding-top: 12px;height: 20px;line-height: 20px;font-size: 12px;}
#blog_inside .review_box .info span{padding-left: 20px;color: #999;}

#blog_inside .form_remark_log{margin-top:30px; background-color:#f8f8f8; border:1px #cbcecd solid; border-radius:5px;}
#blog_inside .form_remark_log .form_box{margin:8px; height:32px; line-height:32px;}
#blog_inside .form_remark_log .remark_left{width:100%; margin-left:-55px; float:left;}
#blog_inside .form_remark_log .remark_left>div{margin-left:55px;}
#blog_inside .form_remark_log .box_input{width:100%; height:32px; line-height:32px; padding:0; border:0; vertical-align:top;}
#blog_inside .form_remark_log .box_input:focus{box-shadow:none;}
#blog_inside .form_remark_log .btn_save{width:50px; height:32px; color:#fff; cursor:pointer; background-color:#07bb8a; border-radius:5px; border:0; float:right;}

#blog_inside .blog_menu{min-height:35px; margin-bottom:15px; margin-left:-5px;}
#blog_inside .blog_menu_item{display:inline-block; vertical-align:top; position:relative;}
#blog_inside .blog_menu_item>button{display:block; min-height:35px; line-height:35px; background-color:transparent; border:0; padding:0 5px; color:var(--primaryColor);}
#blog_inside .blog_menu_item>button>em{display:inline-block; vertical-align:top; width:15px; height:15px; background-repeat:no-repeat; margin-top:10px; margin-right:6px;}
#blog_inside .blog_menu_item>button.btn_menu_view>em{width:18px; background-image:url(../../images/products/icon_menu_view.png);}

#blog .blog_menu{min-height:35px; margin-bottom:15px; margin-left:-5px;}
#blog .blog_menu_item{display:inline-block; vertical-align:top; position:relative;}
#blog .blog_menu_item>button{display:block; min-height:35px; line-height:35px; background-color:transparent; border:0; padding:0 5px; color:var(--primaryColor);}
#blog .blog_menu_item>button>em{display:inline-block; vertical-align:top; width:15px; height:15px; background-repeat:no-repeat; margin-top:10px; margin-right:6px;}
#blog .blog_menu_item>button.btn_menu_view>em{width:18px; background-image:url(../../images/products/icon_menu_view.png);}

#btn_progress_keep{ display: none; }
#box_circle_container .version_btn{ display: none; margin: 15px 10px 0; vertical-align: middle; color: #fff; background-color: var(--primaryColor); border: 1px solid var(--primaryColor); }
#box_circle_container .cancel_btn{ display: none; margin: 15px 10px 0; vertical-align: middle; }