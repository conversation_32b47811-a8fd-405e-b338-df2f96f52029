#faq .search_box .k_input .search_btn{ padding: 0; }
#faq .list_menu_button.fr>li>a.faq_edit{ background-color: var(--primaryColor); color: #fff; }
#faq .list_menu_button.fr>li>a.faq_edit:hover{ color: #fff;; }

#add_faq{ padding: 0 16px; width: auto; height: 34px; line-height: 34px; border-radius: 5px; background-color: var(--primaryColor); color: #fff; font-size: 14px; }

#faq_inside .inside{ max-width: 828px; margin: 0 auto; }
#faq_inside .global_container:first-child{ margin-top: 10px; }
#faq_inside .global_container{ margin-top: 24px; overflow: unset;  }
#faq_inside .input_radio_side_box{ width: 48.5%; }
#faq_inside .input_checkbox_box{ margin-left: 30px; }
#faq_inside .input_checkbox_box:first-child{ margin-left: 0; }
#faq_inside .use_group_box{ margin-top: 20px; display: none; }
#faq_inside .box_type_menu .item b.change{ color: var(--primaryColor); }


#right_form textarea{ height: 100px; }

#faq_box .oper_icon{content: ""; margin-left: 12px; display: inline-block; width: 30px; height: 30px; font-family: "iconfont" !important; background-image: none!important; font-size: 16px; font-style: normal; -webkit-font-smoothing: antialiased; -moz-osx-font-smoothing: grayscale; position: relative; background-color: #eaf7ff; color: var(--primaryColor); border-radius: 35px; font-size: 0; vertical-align: middle; }
#faq_box .oper_icon:first-child{ margin-left: 0; }
#faq_box .icon_del:before{ content: "\e60b"; }
#faq_box .faq_item{ margin-top: 20px; padding: 12px 12px 12px 20px; width: 100%; background-color: #f8f9fb; box-sizing: border-box; position: relative;}
#faq_box .faq_item:hover{ background-color: #ecf5ff; }
#faq_box .faq_item:hover .oper_icon{ background-color: #fff; }
#faq_box .oper_icon:before{ color: var(--primaryColor); transition: all 0.3s ease-out;position: absolute;top: 50%;left: 50%;transform: translate(-50%,-50%); font-size: 16px; }
#faq_box .icon_edit:before{ content: "\e6d3"; font-size: 15px ; }
#faq_box .faq_item .oper_icon:hover{ background-color: var(--primaryColor); }
#faq_box .oper_icon:hover:before,
#faq_box .icon_del:hover:before{ color: #fff; }
#faq_box .faq_item{ font-size: 0; }
#faq_box .faq_item:first-child{ margin-top: 0; }
#faq_box .faq_item .info{ width: calc( 100% - 195px );}
#faq_box .faq_item>div{ display: inline-block; vertical-align: middle; }
#faq_box .faq_item .info>div{ word-break: break-all; display: -webkit-box; -webkit-line-clamp: 1; -webkit-box-orient: vertical; overflow: hidden; }
#faq_box .faq_item .info .question{ font-size: 12px; color: #404852; font-weight: bold;  }
#faq_box .faq_item .info .answer{ margin-top: 12px; font-size: 12px; color: #404852; }
#faq_box .faq_item .operation{ width: 170px; text-align: right; position: absolute; top: 50%; transform: translateY(-50%); right: 20px; }
#faq_box .faq_item .move{ width: 25px; }

.box_promote{border: 1px solid #f59145; border-radius: 5px;background-color: #fef9f5;padding: 24px;margin-bottom: 24px;}
.box_promote .ttit{line-height: 28px;font-size: 20px;color: var(--GlobalTitleColor);}
.box_promote .ttit_small{ display: inline-block; margin-top: 16px; line-height: 12px; color: #7d8d9e; }
.box_promote .stit{line-height: 28px;font-size: 14px;color: var(--GlobalTitleColor);margin-top: 16px;}
.box_promote .desc a{color: #f88e42;text-decoration: underline;}
.box_promote .copy_code{ margin-left: 20px; font-family: "iconfont" !important;font-size: 12px;font-style: normal;-webkit-font-smoothing: antialiased;-moz-osx-font-smoothing: grayscale; color: #f68f42; }
.box_promote .copy_code:before{ content: "\e618"; margin-right: 6px; width: 13px; height: 13px;  }

.flex_promote .box_title{line-height: 42px;font-size: 24px;margin-bottom: 14px;color: var(--GlobalTitleColor)}
.flex_promote .desc{line-height: 24px;font-size: 14px;color: var(--GlobalTextColor)}
.flex_promote .link_box{display: flex; padding-top: 46px; padding-bottom: 50px;}
.flex_promote .link_box .link{flex: 1;height: 38px;line-height: 38px;padding: 0 10px;border-radius: 5px;border: 1px solid var(--GlobalBorderColor);color: var(--GlobalAssistColor);font-size: 12px;}
.flex_promote .link_box .copy{height: 40px;line-height: 40px;color: #fff;background-color: var(--GlobalMainColor);border-radius: 5px;font-size: 14px;padding: 0 20px;margin-left: 8px;border: none;}

.hidden_address{ opacity: 0; pointer-events: none; width: 1px; height: 1px; }
.r_con_table td.operation{ text-align: right; }