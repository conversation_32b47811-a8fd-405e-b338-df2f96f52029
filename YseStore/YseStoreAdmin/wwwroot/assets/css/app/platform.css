

.search_form{ float: left; }
.platform .r_con_table .pro_info{min-height: 60px;padding-left: 80px;position: relative;}
.platform .r_con_table .pro_info img{max-width: 60px;max-height: 60px;position: absolute;top: 0;left: 0;border-radius: 5px;}
#fixed_right .fixed_edit_platform{background-color:#fff!important;}
#fixed_right .fixed_edit_platform .platform_list{padding-bottom:50px;}
#fixed_right .fixed_edit_platform .platform_list>li{margin-bottom:10px; border-radius:4px;position: relative;}
#fixed_right .fixed_edit_platform .platform_list>li .item , .global_form .rows{ margin-bottom: 24px; }
#fixed_right .fixed_edit_platform.platform_add .platform_list>li label{ display: block; margin-bottom: 9px; font-size: 14px; }
#fixed_right .fixed_edit_platform.platform_edit .platform_list>li label{ line-height:19px ; display: block; margin-bottom: 9px; font-size: 14px; }
#fixed_right .fixed_edit_platform .platform_list>li .icon{height:40px; line-height:40px; margin-top:-4px; padding: 0 0 0 65px;background-repeat:no-repeat; background-position:left center;}
#fixed_right .fixed_edit_platform .platform_list>li .icon dt{margin-top:5px;}
#fixed_right .fixed_edit_platform .platform_list>li .box_textarea{background-color:#fff;}
#fixed_right .fixed_edit_platform .platform_list>li .del{position: absolute;width:24px; height:24px; line-height:24px; overflow:hidden; top: 14px;right: 27px; text-indent:99px; background:url(../../images/frame/icon_delete_1.png) no-repeat center; float:right; display:block;}
#fixed_right .fixed_edit_platform .platform_list>li .del:hover{background-image:url(../../images/frame/icon_delete_1_current.png);}
#fixed_right .fixed_edit_platform .platform_list>li .lang_txt{margin-top:6px;}
#fixed_right .fixed_edit_platform .platform_list>li .lang_txt .unit_input{display: block;}
#fixed_right .fixed_edit_platform .platform_list>li .lang_txt:first-child{margin-top:0;}
#fixed_right .fixed_edit_platform .platform_list>li .box_select{ margin-bottom:10px; background-color:#fff;}
#fixed_right .fixed_edit_platform .platform_list>li .box_select select{ vertical-align: top; }
#fixed_right .fixed_edit_platform .platform_list>li .box_select.PlatformTypeSec{display: none;width: 140px !important;margin-left: 10px;}
#fixed_right .fixed_edit_platform .platform_list>li .box_textarea{min-width:100%; box-sizing: border-box; height:150px;}
#fixed_right .fixed_edit_platform .platform_list>li.only .box_textarea{width:271px!important;}
#fixed_right .fixed_edit_platform .platform_list>li .box_input{ width: 100%;box-sizing: border-box; }
#fixed_right .fixed_edit_platform .platform_list>li .color_list .color_item{ margin-top: 12px; font-size: 0; }
#fixed_right .fixed_edit_platform .platform_list>li .color_list .color_item:first-child{ margin-top: 0; }
#fixed_right .fixed_edit_platform .platform_list>li .color_style{ display: inline; vertical-align: middle; width: 46px; height: 22px; border: 1px solid #e5e5e5; text-indent: 999px; border-radius: 5px; cursor: pointer; box-sizing: border-box; }
#fixed_right .fixed_edit_platform .platform_list>li .color_list .color_item span{ display: inline-block; vertical-align: middle; margin-left: 12px; font-size: 14px; }
#fixed_right .fixed_edit_platform .platform_list .button{margin-top:10px;}
#fixed_right .fixed_edit_platform .platform_list .btn_option{display:block; width:34px; height:34px; line-height:38px; background-color:#fff; border:1px #ddd solid; border-radius:5px; text-align:center;}
#fixed_right .fixed_edit_platform .platform_list .btn_option>i{display:inline-block; width:14px; height:14px; background-repeat:no-repeat; background-position:center;}
#fixed_right .fixed_edit_platform .platform_list .btn_option_add>i{background-image:url(../../images/products/icon_square_add.png);}
#fixed_right .fixed_edit_platform .platform_list .btn_option_remove>i{background-image:url(../../images/products/icon_square_delete.png);}
#fixed_right .fixed_edit_platform #btn_platform_fixed_show{display:none;}
#fixed_right .fixed_edit_platform .platform_line{ margin-bottom: 24px; width: 100%; height: 1px; background: #edf0f5; }
#fixed_right .fixed_edit_platform .box_button .btn_submit {background-color: var(--primaryColor);}

#fixed_right .edit_list .edit_item{ padding: 20px 0; border-bottom: 1px solid #edf0f5; }
#fixed_right .edit_list .edit_item>label{ margin-bottom: 0!important;}
#fixed_right .edit_list .edit_item .show_item{ float: left; font-size: 14px; box-sizing: border-box; padding: 10px 14px; line-height: 10px; height: 30px; background: #000; color: #fff; border-radius: 5px; }
#fixed_right .edit_list .edit_item .view{ float: right; width: 70px; }
#fixed_right .edit_list .edit_item .view .grey_bg{ float: left; width: 30px; height: 30px; background-color: #f4f4f4; border-radius: 100%; position: relative; top: 0; right: 0; }
#fixed_right .edit_list .edit_item .view .del{ margin-right: 10px; }
#fixed_right .edit_list .edit_item .view .del:hover{ background:#f4f4f4 url(../../images/frame/icon_delete_1.png) no-repeat center;  }
#fixed_right .edit_list .edit_item.current .view .show:after, #fixed_right .edit_list .edit_item.current .view .show:before{ border: 7px solid transparent; border-bottom: 7px solid #f4f4f4; width: 0; height: 0; position: absolute; top: 6px; left: 50%; transform: translateX(-50%); content: ''; }
#fixed_right .edit_list .edit_item.current .view .show:before{ border-bottom-color: #a4a4a4; top: 4px;}
#fixed_right .edit_list .edit_item .view .show:after, #fixed_right .edit_list .edit_item .view .show:before{ border: 7px solid transparent; border-top: 7px solid #f4f4f4; width: 0; height: 0; position: absolute; bottom: 6px; left: 50%; transform: translateX(-50%); content: ''; }
#fixed_right .edit_list .edit_item .view .show:before{ border-top-color: #a4a4a4; bottom: 4px;}
#fixed_right .edit_list .edit_item .current_show { height: 0; overflow: hidden; transition:all 0.4s; }
#fixed_right .edit_list .edit_item .current_show .showlist{ padding-top: 27px; }


#ColorShow{ display: inline-block; padding: 10px 14px; min-width: 115px; min-height: 30px; line-height: 10px;box-sizing: border-box; border-radius: 5px; background-color: #000; color: #fff;  }

.plugins_app_box .r_con_table .shop_info strong{display:block; margin-bottom:5px; color:#555;}
.plugins_app_box .r_con_table .shop_info p{margin-bottom:15px; color:#aaa;}
.shop_info{ width: 670px; }
.shop_info .color_list{ font-size: 0; }
.shop_info .color_list .color_item{ display: inline-block; box-sizing: border-box; margin-bottom: 10px; margin-right: 10px; height: 30px; line-height: 10px; padding: 10px 14px; border-radius: 5px; }

@media screen and (max-width: 1280px){
	.shop_info{ width: 300px; }
}