/**
 * 图片预览显示修复样式
 * 专门解决首次上传图片后预览图显示为空白的问题
 * 确保upload_box preview_pic容器始终可见
 */

/* 强制显示已上传图片的预览容器 */
#PicDetail.pro_multi_img .img.isfile .upload_box.preview_pic {
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
}

/* 确保已上传图片的预览容器有正确的样式 */
#PicDetail.pro_multi_img .img.isfile .preview_pic {
    width: 100% !important;
    height: 0 !important;
    padding-top: 100% !important;
    position: relative !important;
    background-color: #f8f9fb !important;
    border-radius: 5px !important;
}

/* 确保已上传图片的链接容器正确显示 */
#PicDetail.pro_multi_img .img.isfile .preview_pic > a {
    width: 100% !important;
    height: 100% !important;
    padding: 0 !important;
    border: 0 !important;
    position: absolute !important;
    left: 0 !important;
    top: 0 !important;
    box-sizing: border-box !important;
    display: block !important;
}

/* 确保已上传的图片正确显示 */
#PicDetail.pro_multi_img .img.isfile .preview_pic img {
    width: 100% !important;
    height: 100% !important;
    object-fit: cover !important;
    border-radius: 5px !important;
    display: block !important;
}

/* 隐藏已上传图片项的上传按钮和文字 */
#PicDetail.pro_multi_img .img.isfile .upload_btn {
    display: none !important;
}

#PicDetail.pro_multi_img .img.isfile .upload_txt {
    display: none !important;
}

/* 确保上传按钮项的样式正确 */
#PicDetail.pro_multi_img .img.show_btn .preview_pic {
    background-color: var(--GlobalPicUploadBgColor, #f6fdff) !important;
    border: 1px var(--primaryColor, #4BA0FC) dashed !important;
    box-sizing: border-box !important;
    display: block !important;
}

/* 确保上传按钮正确显示 */
#PicDetail.pro_multi_img .img.show_btn .upload_btn {
    width: 100% !important;
    height: 100% !important;
    background: none !important;
    text-indent: 0 !important;
    opacity: 0 !important;
    position: absolute !important;
    left: 0 !important;
    top: 0 !important;
    z-index: 1 !important;
    border: none !important;
    cursor: pointer !important;
    display: block !important;
}

/* 确保上传文字正确显示 */
#PicDetail.pro_multi_img .img.show_btn .upload_txt {
    display: block !important;
    width: 100% !important;
    position: absolute !important;
    left: 0 !important;
    top: 50% !important;
    text-align: center !important;
    transform: translateY(-50%) !important;
    line-height: 20px !important;
    cursor: pointer !important;
    color: var(--GlobalBtnSimpleColor, #7d8d9e) !important;
    z-index: 2 !important;
    pointer-events: none !important;
}

#PicDetail.pro_multi_img .img.show_btn .upload_txt p {
    margin: 2px 0 !important;
    font-size: 12px !important;
    line-height: 1.2 !important;
}

#PicDetail.pro_multi_img .img.show_btn .upload_txt p:first-child {
    color: var(--primaryColor, #4BA0FC) !important;
    font-weight: normal !important;
}

/* 确保容器本身可见 */
#PicDetail.pro_multi_img {
    display: grid !important;
    visibility: visible !important;
}

/* 修复可能的隐藏问题 */
.upload_box.preview_pic[style*="display: none"] {
    display: block !important;
}

.upload_box.preview_pic[style*="visibility: hidden"] {
    visibility: visible !important;
}

/* 确保图片加载时的过渡效果 */
#PicDetail.pro_multi_img .img.isfile .preview_pic img {
    transition: opacity 0.3s ease-in-out;
}

#PicDetail.pro_multi_img .img.isfile .preview_pic img[src=""] {
    opacity: 0;
}

#PicDetail.pro_multi_img .img.isfile .preview_pic img:not([src=""]) {
    opacity: 1;
}

/* 防止CSS冲突的重要样式 */
#PicDetail .img.isfile .upload_box {
    display: block !important;
}

#PicDetail .img.isfile .preview_pic {
    display: block !important;
}

/* 确保在所有情况下预览容器都可见 */
#PicDetail.pro_multi_img .img .upload_box.preview_pic {
    min-height: 1px !important;
}

/* 修复可能的z-index问题 */
#PicDetail.pro_multi_img .img .preview_pic {
    z-index: auto !important;
}

#PicDetail.pro_multi_img .img .preview_pic > a {
    z-index: auto !important;
}

#PicDetail.pro_multi_img .img .preview_pic img {
    z-index: auto !important;
}
