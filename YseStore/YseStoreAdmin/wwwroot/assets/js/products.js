var products_obj = {
    //************************************************* 数据储存 Start *************************************************
    config_init: {
        option_max: 9999999, //规格数量上限
    },
    //************************************************* 数据储存 End *************************************************

    //************************************************* 数据储存 Start *************************************************
    data_init: {
        image_data: {"all": [], "more": [], "mainAttr": "", "videoItem": ""}, // 主图信息
        attr_model: 1, // 多规格模式 0:单规格组合 1:多规格
        specification: 0, // 0:单规格 1:多规格 2:多规格加价
        attr_data: {}, // 属性和选项数据
        option_data: {}, // 选项详细数据
        ext_attr_data: {0: {}, 1: {}}, // 多规格数据
        ext_save_data: {}, // 多规格保存数据
        batch_save_data: {}, // 批量处理数据
        warehouse_id: 0, // 当前已选的仓库ID
        warehouse_data: {}, // 仓库数据
        warehouse_name_data: {}, // 仓库名称数据
        warehouse_set_data: [], // 仓库设置数据
        relate_method: '', // 关联主图关联方式
        local_upload_img: {}, // 记录本地上传的顺序
        popover_img: [] // 记录关联主图提交的顺序
    },
    //************************************************* 数据储存 End *************************************************

    //************************************************* 函数事件 Start *************************************************
    function_init: {
        // 禁止相关文字
        prohibit_input: function ($Obj) {
            $Obj.off().on('keydown', function (e) {
                var $Value = $(this).val(),
                    key = window.event ? e.keyCode : e.which;
                if ((key == 189 && e.shiftKey == true) || key == 219 || key == 221) {
                    //禁止输出"_[]"
                    return false;
                }
            }).on('keyup', function (e) {
                var $Value = $(this).val();
                $Value = $Value.replace(/([_])/g, "").replace(/([\[])/g, "").replace(/([\]])/g, "");
                $(this).val($Value);
            });
        },

        // 属性选项内容 组合和分解
        attr_option_json: function ($Text) {
            var $Type = (typeof (arguments[1]) == 'undefined') ? 0 : 1, //0:组合 1:分解
                $Obj = new Object, $Ary = new Object, $Result = '';
            if ($Type == 1) {
                //分解 字符串转变对象
                $Result = new Object;
                $Obj = $Text.split('#;');
                $($Obj).each(function (index, element) {
                    if (element) {
                        $Ary = element.split('#:');
                        $Result[$Ary[0]] = $Ary[1];
                    }
                });
            } else {
                //组合 对象转变字符串
                for (k in $Text) {
                    $Result += k + '#:' + $Text[k] + '#;';
                }
            }
            return $Result;
        },

        // 产品属性组合总数
        attr_combination_count: function (position) {
            let $total = 1, $count = 1;
            $(".box_cart_attribute .box_button_choice").each(function (index, element) {
                $count = parseInt($(element).find(".btn_attr_choice").length);
                $(element).data("position") == position && ($count += 1);
                $count > 0 && ($total *= $count);
            });
            return $total;
        },

        // 产品属性选项
        attr_load: function () {
            let checkFun = function (opt) {
                    // 数量上限
                    let result = true;
                    if (products_obj.data_init.attr_model == 1) {
                        // 仅有多规格才会限制
                        let $count = products_obj.function_init.attr_combination_count(opt.position);
                        if ($count > products_obj.config_init.option_max) {
                            global_obj.win_alert_auto_close(lang_obj.manage.products.tips.option_max.replace("{{max}}", products_obj.config_init.option_max), 'fail', 1000, '8%');
                            result = false;
                        }
                    }
                    return result;
                }, enterFun = function (opt) {
                    // 属性对象
                    products_obj.data_init.attr_data[opt.position - 1]["Options"][products_obj.data_init.attr_data[opt.position - 1]["Options"].length] = opt.option;
                    // 选项对象
                    if (products_obj.data_init.option_data[opt.attrName] === undefined) {
                        products_obj.data_init.option_data[opt.attrName] = {};
                    }
                    if (products_obj.data_init.option_data[opt.attrName][opt.option] === undefined) {
                        products_obj.data_init.option_data[opt.attrName][opt.option] = {
                            "color": "",
                            "main": "",
                            "picture": []
                        };
                    }
                    let json = global_obj.json_decode_data(products_obj.data_init.option_data[opt.attrName]);
                    $(".box_cart_attribute .box_button_choice[data-position=\"" + opt.position + "\"] .attr_data").val(json);
                    // 重新组合规格搭配 (多规格)
                    let $extAry = {};
                    // 多规格
                    let $count = products_obj.function_init.attr_combination_count();
                    if ($count <= products_obj.config_init.option_max) {
                        $autoAry = products_obj.function_init.auto_combination();
                        for (let k in products_obj.data_init.warehouse_data) {
                            let id = products_obj.data_init.warehouse_data[k];
                            $index = 0;
                            for (let i in $autoAry) {
                                for (let j in $autoAry[i]) {
                                    if (products_obj.data_init.specification == 2 && $index > products_obj.config_init.option_max) {
                                        // 多规格加价
                                        break;
                                    }
                                    let $attrName = i,
                                        $title = $autoAry[i][j],
                                        $optionName = global_obj.json_decode_data($title);
                                    if (typeof ($extAry[id]) == 'undefined') {
                                        $extAry[id] = [];
                                    }
                                    $extAry[id][$index] = {
                                        "AttrName": $attrName,
                                        "Combination": $optionName,
                                        "Data": $optionName,
                                        "Title": $title.join(" / "),
                                        "ProId": "",
                                        "OldPrice": "",
                                        "Price": "",
                                        "CostPrice": "",
                                        "SKU": "",
                                        "Stock": "",
                                        "VariantsId": "",
                                        "Weight": "",
                                        "WeightUnit": "kg",
                                        "PicPath": ""
                                    };
                                    if (typeof (products_obj.data_init.ext_save_data[id]) == 'undefined') {
                                        // 记录到保存数据(仓库)
                                        products_obj.data_init.ext_save_data[id] = {};
                                    }
                                    if (typeof (products_obj.data_init.ext_save_data[id][$attrName]) == 'undefined') {
                                        // 记录到保存数据(属性)
                                        products_obj.data_init.ext_save_data[id][$attrName] = {};
                                    }
                                    if (typeof (products_obj.data_init.ext_save_data[id][$attrName][$optionName]) == 'undefined') {
                                        // 记录到保存数据(选项)
                                        products_obj.data_init.ext_save_data[id][$attrName][$optionName] = $extAry[id][$index];
                                    } else {
                                        $extAry[id][$index] = products_obj.data_init.ext_save_data[id][$attrName][$optionName];
                                    }
                                    ++$index;
                                }
                            }
                        }
                        products_obj.data_init.ext_attr_data[1] = $extAry;
                    }
                    // 重新组合规格搭配 (多规格加价)
                    $index = 0;
                    $extAry = [];
                    // 多规格加价
                    $autoAry = products_obj.function_init.auto_combination("separate");
                    for (let i in $autoAry) {
                        for (let j in $autoAry[i]) {
                            let $attrName = i,
                                $title = $autoAry[i][j],
                                $optionName = global_obj.json_decode_data($title);
                            $extAry[$index] = {
                                "AttrName": $attrName,
                                "Combination": $optionName,
                                "Data": $optionName,
                                "Title": $title[0],
                                "ProId": "",
                                "OldPrice": "",
                                "Price": "",
                                "CostPrice": "",
                                "SKU": "",
                                "Stock": "",
                                "VariantsId": "",
                                "Weight": "",
                                "WeightUnit": "kg",
                                "PicPath": ""
                            };
                            if (typeof (products_obj.data_init.ext_save_data[$attrName]) == 'undefined') {
                                // 记录到保存数据(属性)
                                products_obj.data_init.ext_save_data[$attrName] = {};
                            }
                            if (typeof (products_obj.data_init.ext_save_data[$attrName][$optionName]) == 'undefined') {
                                // 记录到保存数据(选项)
                                products_obj.data_init.ext_save_data[$attrName][$optionName] = $extAry[$index];
                            } else {
                                $extAry[$index] = products_obj.data_init.ext_save_data[$attrName][$optionName];
                            }
                            ++$index;
                        }
                    }
                    for (let k in products_obj.data_init.warehouse_data) {
                        let id = products_obj.data_init.warehouse_data[k];
                        if (id > 0) {
                            let $attrName = global_obj.json_decode_data(["Ship From"]);
                            $title = products_obj.data_init.warehouse_name_data[id],
                                $optionName = global_obj.json_decode_data([$title]);
                            $extAry[$index] = {
                                "AttrName": $attrName,
                                "Combination": $optionName,
                                "Data": $optionName,
                                "Title": $title,
                                "ProId": "",
                                "OldPrice": "",
                                "Price": "",
                                "CostPrice": "",
                                "SKU": "",
                                "Stock": "",
                                "VariantsId": "",
                                "Weight": "",
                                "WeightUnit": "kg",
                                "PicPath": ""
                            };
                            if (typeof (products_obj.data_init.ext_save_data[$attrName]) == 'undefined') {
                                // 记录到保存数据(属性)
                                products_obj.data_init.ext_save_data[$attrName] = {};
                            }
                            if (typeof (products_obj.data_init.ext_save_data[$attrName][$optionName]) == 'undefined') {
                                // 记录到保存数据(选项)
                                products_obj.data_init.ext_save_data[$attrName][$optionName] = $extAry[$index];
                            } else {
                                $extAry[$index] = products_obj.data_init.ext_save_data[$attrName][$optionName];
                            }
                            ++$index;
                        }
                    }
                    products_obj.data_init.ext_attr_data[0] = $extAry;
                    // 规格显示
                    products_obj.function_init.attr_load();
                    products_obj.function_init.attr_price_show();
                    if (opt.obj.find(".input").hasClass("has_error")) {
                        opt.obj.find(".input").removeClass("has_error").find(".error_tips").remove();
                        opt.obj.find(".attr_selected").removeAttr("style");
                    }
                },
                removeFun = function (opt) {
                    let $this = opt.this,
                        $Obj = opt.obj,
                        $number = $this.parent().index(),
                        $AttrIndex = parseInt($Obj.data("position")) - 1,
                        $extAry = {};
                    products_obj.data_init.attr_data[$AttrIndex].Options.splice($number, 1);
                    // products_obj.data_init.attr_data.splice($number, 1);
                    let $attr = $Obj.find(".attr_title").val(),
                        $option = $this.prev().val();
                    delete products_obj.data_init.option_data[$attr][$option];
                    // 选项数据储存
                    let json = global_obj.json_decode_data(products_obj.data_init.option_data[$attr]);
                    $(".box_cart_attribute .box_button_choice[data-position=\"" + $Obj.data("position") + "\"] .attr_data").val(json);
                    // 重新组合规格搭配 (多规格)
                    $index = 0;
                    let $count = products_obj.function_init.attr_combination_count();
                    if ($count <= products_obj.config_init.option_max) {
                        $autoAry = products_obj.function_init.auto_combination();
                        for (let k in products_obj.data_init.warehouse_data) {
                            let id = products_obj.data_init.warehouse_data[k];
                            $index = 0;
                            for (let i in $autoAry) {
                                for (let j in $autoAry[i]) {
                                    let $attrName = i,
                                        $title = $autoAry[i][j],
                                        $optionName = global_obj.json_decode_data($title);
                                    if (typeof ($extAry[id]) == 'undefined') {
                                        $extAry[id] = {};
                                    }
                                    $extAry[id][$index] = {
                                        "AttrName": $attrName,
                                        "Combination": $optionName,
                                        "Data": $optionName,
                                        "Title": $title.join(" / "),
                                        "ProId": "",
                                        "OldPrice": "",
                                        "Price": "",
                                        "CostPrice": "",
                                        "SKU": "",
                                        "Stock": "",
                                        "VariantsId": "",
                                        "Weight": "",
                                        "WeightUnit": "kg",
                                        "PicPath": ""
                                    };
                                    if (typeof (products_obj.data_init.ext_save_data[id]) == 'undefined') {
                                        // 记录到保存数据(仓库)
                                        products_obj.data_init.ext_save_data[id] = {};
                                    }
                                    if (typeof (products_obj.data_init.ext_save_data[id][$attrName]) == 'undefined') {
                                        // 记录到保存数据(属性)
                                        products_obj.data_init.ext_save_data[id][$attrName] = {};
                                    }
                                    if (typeof (products_obj.data_init.ext_save_data[id][$attrName][$optionName]) == 'undefined') {
                                        // 记录到保存数据(选项)
                                        products_obj.data_init.ext_save_data[id][$attrName][$optionName] = $extAry[id][$index];
                                    }
                                    ++$index;
                                }
                            }
                        }
                        products_obj.data_init.ext_attr_data[1] = $extAry;
                    }
                    // 重新组合规格搭配 (多规格加价)
                    $index = 0;
                    $extAry = [];
                    $autoAry = products_obj.function_init.auto_combination("separate");
                    for (let i in $autoAry) {
                        for (let j in $autoAry[i]) {
                            let $attrName = i,
                                $title = $autoAry[i][j],
                                $optionName = global_obj.json_decode_data($title);
                            $extAry[$index] = {
                                "AttrName": $attrName,
                                "Combination": $optionName,
                                "Data": $optionName,
                                "Title": $title[0],
                                "ProId": "",
                                "OldPrice": "",
                                "Price": "",
                                "CostPrice": "",
                                "SKU": "",
                                "Stock": "",
                                "VariantsId": "",
                                "Weight": "",
                                "WeightUnit": "kg",
                                "PicPath": ""
                            };
                            if (typeof (products_obj.data_init.ext_save_data[$attrName]) == 'undefined') {
                                // 记录到保存数据(属性)
                                products_obj.data_init.ext_save_data[$attrName] = {};
                            }
                            if (typeof (products_obj.data_init.ext_save_data[$attrName][$optionName]) == 'undefined') {
                                // 记录到保存数据(选项)
                                products_obj.data_init.ext_save_data[$attrName][$optionName] = $extAry[$index];
                            }
                            ++$index;
                        }
                    }
                    for (let k in products_obj.data_init.warehouse_data) {
                        let id = products_obj.data_init.warehouse_data[k];
                        if (id > 0) {
                            let $attrName = global_obj.json_decode_data(["Ship From"]);
                            $title = products_obj.data_init.warehouse_name_data[id],
                                $optionName = global_obj.json_decode_data([$title]);
                            $extAry[$index] = {
                                "AttrName": $attrName,
                                "Combination": $optionName,
                                "Data": $optionName,
                                "Title": $title,
                                "ProId": "",
                                "OldPrice": "",
                                "Price": "",
                                "CostPrice": "",
                                "SKU": "",
                                "Stock": "",
                                "VariantsId": "",
                                "Weight": "",
                                "WeightUnit": "kg",
                                "PicPath": ""
                            };
                            if (typeof (products_obj.data_init.ext_save_data[$attrName]) == 'undefined') {
                                // 记录到保存数据(属性)
                                products_obj.data_init.ext_save_data[$attrName] = {};
                            }
                            if (typeof (products_obj.data_init.ext_save_data[$attrName][$optionName]) == 'undefined') {
                                // 记录到保存数据(选项)
                                products_obj.data_init.ext_save_data[$attrName][$optionName] = $extAry[$index];
                            } else {
                                $extAry[$index] = products_obj.data_init.ext_save_data[$attrName][$optionName];
                            }
                            ++$index;
                        }
                    }
                    products_obj.data_init.ext_attr_data[0] = $extAry;
                    // 规格显示
                    products_obj.function_init.attr_price_show();
                    // 产品规格处理
                    $("#attribute_ext_box thead .btn_checkbox").removeClass("current indeterminate").find("input").prop("checked", false); //最后去掉全选
                    $(".attribute_ext thead tr").removeClass("current");
                    $(".attribute_ext thead .global_menu_button .open").addClass('no_select');
                }

            frame_obj.buttonChoice({
                InputName: "AttrOption",
                loadColor: 0,
                onCheck: checkFun,
                onEnter: enterFun,
                onRemove: removeFun
            });

            frame_obj.multi_lang_show_all('#edit_form');

            // 属性排序按钮(超过两个属性才显示"属性排序"按钮)
            if ($(".box_cart_attribute .box_button_choice").length > 1) {
                $("#myorder_attribute").show();
            } else {
                $("#myorder_attribute").hide();
            }

            // 下拉效果
            $('.box_attr_basic_more').hover(function () {
                $(this).find('.drop_down').show().stop(true).animate({'top': 24, 'opacity': 1}, 250);
            }, function () {
                $(this).find('.drop_down').stop(true).animate({'top': 14, 'opacity': 0}, 100, function () {
                    $(this).hide();
                });
            });

            // 修改属性选项
            frame_obj.fixed_right($(".btn_option_edit"), ".fixed_edit_attribute_option_edit", function (obj) {
                let $Box = $(".fixed_edit_attribute_option_edit"),
                    $Obj = obj.parents(".box_button_choice"),
                    $AttrName = $Obj.children("input").val(),
                    $AttrId = $Obj.data("id"),
                    $Position = $Obj.data("position"),
                    $Data = new Object, $Html = "", $Num = 0;
                $Box.find(".edit_attr_list").html("");
                $Box.find("input[name=AttrId]").val($AttrId);
                $Html += '<div class="rows clean">';
                $Html += '<label>' + lang_obj.manage.global.name + '</label>';
                $Html += '<div class="input">';
                $Html += '<div class="item clean">';
                $Html += '<input type="text" class="box_input fl" name="AttrName" value="' + global_obj.htmlspecialchars($AttrName) + '" size="30" maxlength="255" autocomplete="off" data-position="' + $Position + '" notnull />';
                $Html += '</div>';
                $Html += '</div>';
                $Html += '</div>';
                $Obj.find('.attr_selected .btn_attr_choice').each(function (index, element) {
                    $Data[index] = $(this).find('.attr_current').val();
                    ++$Num;
                });
                if ($Num > 0) {
                    $Html += '<div class="rows">';
                    $Html += '<label>' + lang_obj.global.option + '</label>';
                    $Html += '<div class="input option_list">';
                    for (k in $Data) {
                        $Html += '<div class="item clean" data-position="' + k + '">';
                        $Html += '<span class="myorder"><span class="icon_myorder"></span></span>';
                        $Html += '<input type="text" class="box_input fl attr_option_value" value="' + global_obj.htmlspecialchars($Data[k]) + '" size="30" maxlength="255" autocomplete="off" notnull />';
                        $Html += '<div class="default_name fl" title="' + global_obj.htmlspecialchars($Data[k]) + '">' + global_obj.htmlspecialchars($Data[k]) + '</div>';
                        $Html += '</div>';
                    }
                    ;
                    $Html += '</div>';
                    $Html += '</div>';
                }
                $Box.find('.edit_attr_list').html($Html);
                $Box.find('.edit_tips, .edit_attr_list, .box_button').show();
                $Box.find('.bg_no_table_data').hide();
                products_obj.function_init.prohibit_input($Box.find("input[name=AttrName]"));
                products_obj.function_init.prohibit_input($Box.find(".attr_option_value"));
                frame_obj.dragsort($Box.find('.option_list'), '', '.myorder', 'input', '<div class="item placeHolder"></div>');
            });

            // 修改多图
            frame_obj.fixed_right($(".btn_picture_edit"), ".fixed_edit_attribute_picture_edit", function (obj) {
                let $box = $(".fixed_edit_attribute_picture_edit"),
                    $obj = obj.parents(".box_button_choice"),
                    $attrName = $obj.children("input").val(),
                    $attrId = $obj.data("id"),
                    $position = $obj.data("position"),
                    $data = new Object, $html = "", i = 0,
                    $picPathAry = [], $trObj = {};
                $box.find(".edit_attr_list").html("");
                $box.find("input[name=AttrId]").val($attrId);
                $obj.find('.attr_selected .btn_attr_choice').each(function (index, element) {
                    $data[index] = $(this).find('.attr_current').val();
                    ++i;
                });
                if (i > 0) {
                    $html += '<table border="0" cellpadding="5" cellspacing="0" class="relation_box" data-attr="' + global_obj.htmlspecialchars($attrName) + '" data-position="' + $position + '">';
                    $html += '<colgroup>';
                    $html += '<col style="width:130px; min-width:130px;"></col>';
                    $html += '<col style="width:280px; min-width:280px;"></col>';
                    $html += '</colgroup>';
                    $html += '<thead>';
                    $html += '<tr>';
                    $html += '<td>' + lang_obj.global.option + '</td>';
                    $html += '<td>' + lang_obj.global.picture + '</td>';
                    $html += '</tr>';
                    $html += '</thead>';
                    $html += '<tbody>';
                    for (k in $data) {
                        i = 0;
                        $picPathAry = products_obj.data_init.option_data[$attrName][$data[k]].picture;
                        $html += '<tr data-value="' + global_obj.htmlspecialchars($data[k]) + '">';
                        $html += '<td>' + global_obj.htmlspecialchars($data[k]) + '</td>';
                        $html += '<td>';
                        $html += '<div class="multi_img upload_file_multi pro_multi_img" id="PictureDetail_' + k + '">';
                        if ($picPathAry) {
                            for (k2 in $picPathAry) {
                                $html += frame_obj.multi_img_item("PicPath[]", i, 0);
                                ++i;
                            }
                        }
                        $html += frame_obj.multi_img_item("PicPath[]", i, 0);
                        $html += '</div>';
                        $html += '</td>';
                        $html += '</tr>';
                    }
                    $html += '</tbody>';
                    $html += '</table>';
                }
                $box.find('.edit_attr_list').html($html);
                $box.find('.edit_tips, .edit_attr_list, .box_button').show();
                $box.find('.bg_no_table_data').hide();
                // 图片上传
                products_obj.function_init.side_picture_upload();
                // 显示图片
                for (k in $data) {
                    $trObj = $box.find("tbody tr[data-value=\"" + $.quotationMarksTransferred($data[k]) + "\"]");
                    $picPathAry = products_obj.data_init.option_data[$attrName][$data[k]].picture;
                    if ($picPathAry) {
                        for (k2 in $picPathAry) {
                            let $sObj = $trObj.find(".img[num=" + k2 + "]"),
                                $picPath = $picPathAry[k2];
                            $sObj.find(".pic_btn .zoom").attr("href", $picPath);
                            $sObj.find(".preview_pic a").remove();
                            $sObj.find(".preview_pic").append(frame_obj.upload_img_detail($picPath)).children(".upload_btn").hide().parent().parent().addClass("isfile").removeClass("show_btn");
                            $sObj.find(".preview_pic").children("input[type=hidden]").val($picPath).attr("save", 1).trigger("change");
                        }
                    }
                }
            });

            products_obj.function_init.option_type_model();
            products_obj.function_init.option_filter();
            products_obj.function_init.associate_main_image();
        },

        // 选项类型事件
        option_type_model: function () {
            let callback = function (opt) {
                let $attr = $(opt.el).parents(".box_button_choice").find(".attr_title").val(),
                    $option = $(opt.el).parent().find(".attr_current").val();
                products_obj.data_init.option_data[$attr][$option].color = '#' + opt.hes;
                // 选项数据储存
                let json = global_obj.json_decode_data(products_obj.data_init.option_data[$attr]);
                $(".box_cart_attribute .box_button_choice[data-attr=\"" + $.quotationMarksTransferred($attr) + "\"] .attr_data").val(json);
            }
            frame_obj.buttonChoice({
                loadInit: 0,
                loadColr: 1,
                onColor: callback
            });

            let pirture = function () {
                let $id = $(this).parents(".box_button_choice").data("id"),
                    $attr = $(this).parents(".box_button_choice").find(".attr_title").val(),
                    $option = $(this).parent().find(".attr_current").val();
                products_obj.function_init.option_picture_upload({
                    obj: $(this),
                    id: $id,
                    attr: $attr,
                    option: $option
                });
                return false;
            }

            // 选择图片(选项类型：图文)
            $(".box_button_choice[data-type=picture] .choice_list .btn_attr_choice>em").off().on("click", pirture);

            // 获取相关数据
            $(".box_button_choice").each(function (index, element) {
                let $type = $(this).attr("data-type"),
                    $attr = $(this).find(".attr_title").val();
                if ($type == "text") return true;
                $(this).find(".btn_attr_choice>em").each(function (index, element) {
                    let $option = $(this).parent().find(".attr_current").val();
                    if ($type == "color") {
                        // 颜色
                        if (products_obj.data_init.option_data[$attr][$option].color) {
                            $(this).attr("style", "background-color:" + products_obj.data_init.option_data[$attr][$option].color);
                        } else {
                            $(this).removeAttr("style");
                        }
                    } else {
                        // 图文
                        if (products_obj.data_init.option_data[$attr][$option].main) {
                            $(this).attr("style", "background-image:url(" + products_obj.data_init.option_data[$attr][$option].main + ")");
                        } else {
                            $(this).removeAttr("style");
                        }
                    }
                });
            });
        },

        // 规格筛选事件
        option_filter: function () {
            let html = ''
            let repeatAry = {}
            $(products_obj.data_init.attr_data).each((index, element) => {
                $(element.Options).each((index2, element2) => {
                    if (typeof repeatAry[element2] == "undefined") {
                        repeatAry[element2] = 1
                    } else {
                        repeatAry[element2] += 1
                    }
                })
            })
            html += '<div class="filter_grid">'
            $(products_obj.data_init.attr_data).each((index, element) => {
                html += '<ul>'
                html += '<li class="title">' + element.Name + '</li>'
                $(element.Options).each((index2, element2) => {
                    html += '<li><a href="javascript:;" class="btn_warehouse" data-number="' + index + '" data-attr="' + global_obj.htmlspecialchars(element.Name.toString()) + '" data-option="' + global_obj.htmlspecialchars(element2.toString()) + '">' + element2 + '</a></li>'
                })
                html += '</ul>'
            })
            html += '</div>'
            html += '<a href="javascript:;" class="clear_option_filter">清除</a>'
            $(".option_filter .inside_title span").html(lang_obj.manage.products.choose_specifications)
            $(".option_filter .inside_body .box_filter").html(html)
        },

        // 自动搭配选项组合
        auto_combination: function () {
            let $type = (typeof (arguments[0]) == 'undefined') ? 'merge' : 'separate',
                $result = [],
                $optionAry = [],
                $attrName = '';
            if ($type == "merge") {
                // 组合
                let $attrNameAry = [];
                $(products_obj.data_init.attr_data).each(function (index, element) {
                    $attrNameAry[index] = element.Name;
                });
                $attrName = global_obj.json_decode_data($attrNameAry);
                let checked_data = [], key_ary = new Array, checked_data_len = 0;
                $(products_obj.data_init.attr_data).each(function (index, element) {
                    checked_data[index] = element.Options;
                });
                for (k in checked_data) {
                    key_ary.push(k);
                    checked_data_len += 1;
                }

                function CartAttr($arr, $num) {
                    let _arr = [];
                    if ($num == 0) {
                        for (j in checked_data[$num]) {
                            $arr.push([checked_data[$num][j]]);
                        }
                    } else {
                        let $index = 0;
                        for (i in $arr) {
                            let $length = $arr[i].length, $_ary = [];
                            if (checked_data[$num]) {
                                for (j in checked_data[$num]) {
                                    let $_ary = [];
                                    for (k in $arr[i]) {
                                        $_ary[k] = $arr[i][k];
                                    }
                                    $_ary[$length] = checked_data[$num][j];
                                    _arr[$index] = $_ary;
                                    ++$index;
                                }
                            }
                        }
                        _arr.length && ($arr = _arr);
                    }
                    ++$num;
                    if ($num < checked_data_len) {
                        CartAttr($arr, $num);
                    } else {
                        $optionAry = $arr;
                    }
                }

                CartAttr($optionAry, 0);
                $result[$attrName] = $optionAry;
            } else {
                // 分开
                let $index = 0;
                $(products_obj.data_init.attr_data).each(function (index, element) {
                    if (element.Options) {
                        $index = 0;
                        $attrName = global_obj.json_decode_data([element.Name]);
                        $result[$attrName] = [];
                        for (i in element.Options) {
                            $result[$attrName][$index] = [element.Options[i]];
                            ++$index;
                        }
                    }
                });
                if (products_obj.data_init.warehouse_data && products_obj.data_init.warehouse_data[0] != 0) {
                    $attrName = global_obj.json_decode_data(["Ship From"]);
                    // 检查是否已经存在Ship From属性，避免重复生成
                    if (!$result[$attrName]) {
                        $result[$attrName] = [];
                        let warehouseIndex = 0;
                        for (let k in products_obj.data_init.warehouse_data) {
                            let warehouseId = products_obj.data_init.warehouse_data[k];
                            let warehouseName = products_obj.data_init.warehouse_name_data[warehouseId];
                            if (warehouseName) {
                                $result[$attrName][warehouseIndex] = [warehouseName];
                                warehouseIndex++;
                            }
                        }
                    }
                }
            }
            return $result;
        },

        //规格图片事件
        combination_picture: function (data) {
            //规格图片上传
            let $Type = data.Type,
                $Position = data.Position,
                $top = data.Top,
                $left = data.Left,
                $Html = "", $ListHtml = "", $ImgData = [], $Height = 0, $PicPath = "", $Path = "", $VideoIndex,
                $obj = {};
            $.each(products_obj.data_init.image_data.all, function (index, element) {
                let $Path = element;
                $Path && ($Path = $Path.replace('?video', ''));
                if ($Path) {
                    $ImgData[index] = $Path;
                    if (products_obj.data_init.image_data.videoItem == $Path) {
                        $VideoIndex = index;
                    }
                }
            });
            if ($Type == 'extend') {
                // 产品规格
                let $extend = data.Extend,
                    $sExtend = $.quotationMarksTransferred($extend),
                    $attrName = data.AttrName,
                    $sAttrName = $.quotationMarksTransferred($attrName);
                $sWid = data.Wid;
                $obj = $(".box_popover[data-wid=\"" + $sWid + "\"][data-attr=\"" + $sAttrName + "\"][data-extend=\"" + $sExtend + "\"]");
                products_obj.function_init.clean_combination_picture();
                if (products_obj.data_init.ext_save_data[$sWid][$attrName][$extend]) {
                    //已有保存数据
                    $PicPath = products_obj.data_init.ext_save_data[$sWid][$attrName][$extend].PicPath;
                }
                if ($ImgData.length) {
                    //有图片数据
                    if ($ImgData.length > 8) {
                        //有三行
                        $Height = 310;
                    } else if ($ImgData.length > 4) {
                        //有两行
                        $Height = 218;
                    } else {
                        //仅有一行
                        $Height = 124;
                    }
                    $top -= $Height - 40;
                    $($ImgData).each(function (index, element) {
                        if (element) {
                            let fileName = element.substr(element.lastIndexOf("/") + 1);
                            let name = fileName.substr(0, fileName.lastIndexOf("-"));
                            name = name ? name : (index + 1);
                            $ListHtml += '<div class="popover_img' + (index == $VideoIndex ? ' popover_video' : '') + ($PicPath && element.indexOf($PicPath) !== -1 ? " popover_img_selected" : "") + '" style="background-image:url(\'' + global_obj.htmlspecialchars(element) + '\');"><span>' + name + '</span></div>';
                        }
                    });
                } else {
                    //没有图片数据
                    $Height = 109;
                    $top -= $Height - 40;
                    $ListHtml += '<div class="popover_img_empty"><p>' + lang_obj.manage.products.tips.please_upload_pictures + '</p></div>';
                }
                if (!$obj.length) {
                    $Html += '<div class="box_popover box_popover_right" data-wid="' + $sWid + '" data-attr="' + global_obj.attr_htmlspecialchars($attrName) + '" data-extend="' + global_obj.attr_htmlspecialchars($extend) + '" data-choose-type="radio">';
                    $Html += '<div class="box_popover_arrow"></div>';
                    $Html += '<div class="box_popover_content">';
                    $Html += '<div class="box_popover_picture">';
                    $Html += $ListHtml;
                    $Html += '</div>';
                    $Html += '</div>';
                    $Html += '</div>';
                    $(".attribute_ext").append($Html);
                    $obj = $(".box_popover[data-wid=\"" + $sWid + "\"][data-attr=\"" + $sAttrName + "\"][data-extend=\"" + $sExtend + "\"]"); //重新获取一次
                } else {
                    $obj.find(".box_popover_picture").html($ListHtml);
                }
                $obj.css({"top": $top, "left": $left});
                $(".attribute_ext .group[data-wid=\"" + $sWid + "\"][data-attr=\"" + $sAttrName + "\"][data-extend=\"" + $sExtend + "\"] .attr_picture").addClass("attr_picture_hover");
            } else if ($Type == 'batch') {
                // 批量处理
                $obj = $(".box_popover[data-type=\"batch\"]");
                $top = 210;
                $left = 20;
                if (products_obj.data_init.batch_save_data) {
                    // 已有保存数据
                    $PicPath = products_obj.data_init.batch_save_data.PicPath;
                }
                if ($ImgData.length) {
                    // 有图片数据
                    $($ImgData).each(function (index, element) {
                        if (element) {
                            let fileName = element.substr(element.lastIndexOf("/") + 1);
                            let name = fileName.substr(0, fileName.lastIndexOf("-"));
                            name = name ? name : (index + 1);
                            $ListHtml += '<div class="popover_img' + ($PicPath && element.indexOf($PicPath) !== -1 ? " popover_img_selected" : "") + '" style="background-image:url(\'' + global_obj.htmlspecialchars(element) + '\');"><span>' + name + '</span></div>';
                        }
                    });
                } else {
                    // 没有图片数据
                    $ListHtml += '<div class="popover_img_empty"><p>' + lang_obj.manage.products.tips.please_upload_pictures + '</p></div>';
                }
                if (!$obj.length) {
                    $Html += '<div class="box_popover box_popover_bottom" data-type="batch" data-extend="" data-choose-type="radio">';
                    $Html += '<div class="box_popover_arrow"></div>';
                    $Html += '<div class="box_popover_content">';
                    $Html += '<div class="box_popover_picture">';
                    $Html += $ListHtml;
                    $Html += '</div>';
                    $Html += '</div>';
                    $Html += '</div>';
                    $(".fixed_batch_edit .batch_content").append($Html);
                    $obj = $(".box_popover[data-type=\"batch\"]"); //重新获取一次
                } else {
                    $obj.find(".box_popover_picture").html($ListHtml);
                }
                $obj.css({"top": $top, "left": $left});
            } else if ($Type == 'main') {
                // 关联主图
                let $extend = data.Extend,
                    $sExtend = $.quotationMarksTransferred($extend),
                    $attrName = data.AttrName,
                    $sAttrName = $.quotationMarksTransferred($attrName),
                    $picPathAry = [],
                    $width = 420,
                    $chooseType = data.chooseType || 'radio';
                $obj = $(".box_popover[data-type=\"main\"][data-attr=\"" + $sAttrName + "\"][data-extend=\"" + $sExtend + "\"]");
                products_obj.function_init.clean_combination_picture();
                if (products_obj.data_init.option_data[$attrName][$extend]) {
                    // 已有保存数据
                    $picPathAry = products_obj.data_init.option_data[$attrName][$extend].picture;
                }
                if ($ImgData.length) {
                    // 有图片数据
                    if ($ImgData.length > 8) {
                        // 有三行
                        $Height = 310;
                    } else if ($ImgData.length > 4) {
                        // 有两行
                        $Height = 274; //218;
                    } else {
                        // 仅有一行
                        $Height = 157; //124;
                    }
                    $top -= $Height - 40;
                    $($ImgData).each(function (index, element) {
                        if (element) {
                            let fileName = element.substr(element.lastIndexOf("/") + 1);
                            let name = fileName.substr(0, fileName.lastIndexOf("-"));
                            name = name ? name : (index + 1);
                            $ListHtml += '<div class="popover_img' + (index == $VideoIndex ? ' popover_video' : '') + ($.inArray(element, $picPathAry) != -1 ? " popover_img_selected" : "") + '" style="background-image:url(\'' + global_obj.htmlspecialchars(element) + '\');"><span>' + name + '</span></div>';
                        }
                    });
                } else {
                    // 没有图片数据
                    $Height = 109;
                    $top -= $Height - 40;
                    $width = 0;
                    $ListHtml += '<div class="popover_img_empty"><p>' + lang_obj.manage.products.tips.please_upload_pictures + '</p></div>';
                }
                if (!$obj.length) {
                    $Html += '<div class="box_popover box_popover_right" data-type="main" data-attr="' + global_obj.attr_htmlspecialchars($attrName) + '" data-extend="' + global_obj.attr_htmlspecialchars($extend) + '">';
                    $Html += '<div class="box_popover_arrow"></div>';
                    $Html += '<div class="box_popover_content">';
                    $Html += '<div class="box_popover_picture">' + $ListHtml + '</div>';
                    $Html += '<div class="box_popover_button">';
                    $Html += '<input type="button" class="btn_global btn_submit btn_popover_sure" value="确定" /></input>';
                    $Html += '</div>';
                    $Html += '</div>';
                    $Html += '</div>';
                    $(".box_main_associate").append($Html);
                    $obj = $(".box_popover[data-type=\"main\"][data-attr=\"" + $sAttrName + "\"][data-extend=\"" + $sExtend + "\"]"); //重新获取一次
                } else {
                    $obj.find(".box_popover_picture").html($ListHtml);
                }
                $obj.attr('data-choose-type', $chooseType);
                let boxWidth = $("#products_inside").width(); // $(".box_main_associate").outerWidth(true);
                let popoverWidth = 420;
                if (boxWidth > 800 && ($left + popoverWidth) > boxWidth) {
                    $left = $left - popoverWidth - 60;
                    $obj.addClass("box_popover_left").removeClass("box_popover_right");
                } else {
                    $obj.addClass("box_popover_right").removeClass("box_popover_left");
                }
                $obj.css({"top": $top, "left": $left});
                $width > 0 && $obj.css({"width": $width});
                $(".box_main_associate .group[data-type=\"main\"][data-attr=\"" + $sAttrName + "\"][data-extend=\"" + $sExtend + "\"] .attr_picture").addClass("attr_picture_hover");
            }
            $obj.removeClass("box_popover_hidden").addClass("popover_enter_animate popover_enter_active");
            setTimeout(function () {
                $obj.removeClass("popover_enter_animate popover_enter_active");
            }, 200);
        },

        // 清除图片弹出框
        clean_combination_picture: function (e) {
            let $where = typeof (e) != "undefined" ? $(e.target).parents('#box_popover').length == 0 : 1,
                $obj = $('.box_popover:visible'),
                $extend = '';
            if (typeof (e) != "undefined" && $(e.target).parents('.box_popover').length && $(e.target).parents('.box_popover').attr('data-choose-type') == 'checkbox') {
                return false
            }
            if ($obj.length) {
                if ($where) {
                    $obj.each(function (index, element) {
                        $extend = global_obj.json_decode_data($(element).data("extend"));
                        $(".attribute_ext .group[data-extend=\"" + $.quotationMarksTransferred($extend) + "\"] .attr_picture").removeClass("attr_picture_hover");
                        $(".box_main_associate .group[data-extend=\"" + $.quotationMarksTransferred($extend) + "\"] .attr_picture").removeClass("attr_picture_hover");
                    });
                    $obj.addClass("popover_leave_animate popover_leave_active");
                    setTimeout(function () {
                        $obj.removeClass("popover_leave_animate popover_leave_active").addClass('box_popover_hidden');
                    }, 200);
                }
            }
        },

        // 依据产品分类，显示产品属性选项
        attr_category_select: function (value) {
            var $Status = 0,
                $ProId = $('#ProId').val();
            $.post('/api/Product/GetProductAttr?id=' + $ProId, '', function (data) {
                if (data.ret == 1) {
                    let $Html = '', $Attr = {}, $AttrCount = 0, i = 0,
                        $extAry = {}, $autoAry = {}, $index = 0;
                    if (data.msg.Attr) {
                        // 属性管理HTML
                        $Attr = data.msg.Attr;
                        for (k in $Attr) {
                            i = 0;
                            $IsOverseas = ($Attr[k]['AttrId'] == 'Overseas' ? 1 : 0); // 是否为海外仓选项
                            if ($IsOverseas == 1) {
                                for (i in $Attr[k]['Option']) {
                                    // 统计海外仓对象的数量
                                    $AttrCount += 1;
                                }
                            } else {
                                $AttrCount = $Attr[k]['Options'].length;
                            }
                            $Html += '<div class="box_button_choice clean" data-id="' + $Attr[k].AttrId + '" data-position="' + $Attr[k].Position + '" data-attr="' + global_obj.htmlspecialchars($Attr[k].Name) + '" data-type="' + $Attr[k].Type + '">';
                            $Html += '<div class="rows clean">';
                            $Html += '<label>';
                            $Html += '<strong>' + $Attr[k].Name + '</strong>';
                            $Html += '<dl class="box_basic_more box_attr_basic_more">';
                            $Html += '<dt><a href="javascript:;" class="btn_basic_more"><i></i></a></dt>';
                            $Html += '<dd class="drop_down">';
                            $Html += '<a href="javascript:;" class="item input_checkbox_box btn_option_edit"><span>' + lang_obj.manage.global.edit + '</span></a>';
                            // $Html += 						'<a href="javascript:;" class="item input_checkbox_box btn_picture_edit"><span>' + lang_obj.manage.products.main_picture + '</span></a>';
                            $Html += '<a href="javascript:;" class="item input_checkbox_box btn_attribute_delete"><span>' + lang_obj.global.del + '</span></a>';
                            $Html += '</dd>';
                            $Html += '</dl>';
                            $Html += '</label>';
                            $Html += '<div class="input">';
                            $Html += '<div class="choice_list">';
                            $Html += '<div class="attr_selected">';
                            $Html += '<div class="select_list">';
                            if ($Attr[k].Options) {
                                for (k2 in $Attr[k].Options) {
                                    $Html += frame_obj.buttonChoiceOption($Attr[k].Options[k2], "AttrOption", $Attr[k].Position, 1);
                                    ++i;
                                }
                            }
                            $Html += '</div>';
                            $Html += '<input type="text" class="box_input" name="_Attr" value="" size="30" maxlength="255" />';
                            $Html += '<span class="placeholder' + (i > 0 ? ' hide' : '') + '">' + lang_obj.manage.products.placeholder + '</span>';
                            $Html += '</div>';
                            $Html += '</div>';
                            $Html += '</div>';
                            $Html += '</div>';
                            $Html += '<input type="hidden" name="AttrTitle[]" value="' + global_obj.htmlspecialchars($Attr[k].Name) + '" class="attr_title" />';
                            $Html += '<input type="hidden" name="AttrType[]" value="' + $Attr[k].Type + '" class="attr_type" />';
                            $Html += '<input type="hidden" name="AttrData[]" value="' + global_obj.htmlspecialchars($Attr[k].Data) + '" class="attr_data" />';
                            $Html += '</div>';
                        }
                        $('.box_cart_attribute').html($Html)

                        // 配置数据
                        products_obj.data_init.attr_model = (data.msg.IsCombination == 2 ? 0 : 1);
                        if (data.msg.IsCombination == 0 && data.msg.Combinatin && typeof data.msg.Combinatin[0] !== 'undefined') {
                            products_obj.data_init.attr_model = 0;
                        }
                        products_obj.data_init.attr_data = data.msg.Attr;
                        if (typeof data.msg.Option === "object" && data.msg.Option.length == 0) {
                            // 把空数组转成空对象
                            data.msg.Option = {};
                        }
                        products_obj.data_init.option_data = data.msg.Option;

                        products_obj.data_init.defautl_settings = data.msg.IsDefautlSettings;

                        if (products_obj.data_init.specification == 1 || products_obj.data_init.specification == 0) {
                            // 多规格
                            $extAry = [];
                            if (products_obj.data_init.specification == 1) {
                                products_obj.data_init.ext_attr_data[1] = $.fn.deepCopy(data.msg.Combinatin || [])
                                products_obj.data_init.ext_save_data = $.fn.deepCopy(data.msg.SaveCombination || [])
                            }
                            $autoAry = products_obj.function_init.auto_combination("separate");
                            for (let i in $autoAry) {
                                for (let j in $autoAry[i]) {
                                    let $attrName = i,
                                        $title = $autoAry[i][j],
                                        $optionName = global_obj.json_decode_data($title);
                                    $extAry[$index] = {
                                        "AttrName": $attrName,
                                        "Combination": $optionName,
                                        "Data": $optionName,
                                        "Title": $title[0],
                                        "ProId": "",
                                        "OldPrice": "",
                                        "Price": "",
                                        "CostPrice": "",
                                        "SKU": "",
                                        "Stock": "",
                                        "VariantsId": "",
                                        "Weight": "",
                                        "WeightUnit": "kg",
                                        "PicPath": ""
                                    };
                                    if (typeof data.msg.Combinatin[0] != 'undefined' && typeof data.msg.Combinatin[0][$index] != 'undefined' && data.msg.Combinatin[0][$index].AttrName == $attrName && data.msg.Combinatin[0][$index].Data == $optionName) {
                                        // 调用存有数据表里的数据
                                        $extAry[$index] = data.msg.Combinatin[0][$index];
                                    }
                                    if (typeof (products_obj.data_init.ext_save_data[$attrName]) == 'undefined') {
                                        // 记录到保存数据(属性)
                                        products_obj.data_init.ext_save_data[$attrName] = {};
                                    }
                                    if (typeof (products_obj.data_init.ext_save_data[$attrName][$optionName]) == 'undefined') {
                                        // 记录到保存数据(选项)
                                        products_obj.data_init.ext_save_data[$attrName][$optionName] = $extAry[$index];
                                    }
                                    ++$index;
                                }
                            }
                            if (products_obj.data_init.specification > 0) {
                                for (let k in products_obj.data_init.warehouse_data) {
                                    let id = products_obj.data_init.warehouse_data[k];
                                    if (id > 0) {
                                        let $attrName = global_obj.json_decode_data(["Ship From"]);
                                        $title = products_obj.data_init.warehouse_name_data[id],
                                            $optionName = global_obj.json_decode_data([$title]);
                                        $extAry[$index] = {
                                            "AttrName": $attrName,
                                            "Combination": $optionName,
                                            "Data": $optionName,
                                            "Title": $title,
                                            "ProId": "",
                                            "OldPrice": "",
                                            "Price": "",
                                            "CostPrice": "",
                                            "SKU": "",
                                            "Stock": "",
                                            "VariantsId": "",
                                            "Weight": "",
                                            "WeightUnit": "kg",
                                            "PicPath": ""
                                        };
                                        if (typeof (products_obj.data_init.ext_save_data[$attrName]) == 'undefined') {
                                            // 记录到保存数据(属性)
                                            products_obj.data_init.ext_save_data[$attrName] = {};
                                        }
                                        if (typeof (products_obj.data_init.ext_save_data[$attrName][$optionName]) == 'undefined') {
                                            // 记录到保存数据(选项)
                                            products_obj.data_init.ext_save_data[$attrName][$optionName] = $extAry[$index];
                                        } else {
                                            $extAry[$index] = products_obj.data_init.ext_save_data[$attrName][$optionName];
                                        }
                                        ++$index;
                                    }
                                }
                            }
                            products_obj.data_init.ext_attr_data[0] = $.fn.deepCopy($extAry)
                        }
                        if (products_obj.data_init.specification == 2 || products_obj.data_init.specification == 0) {
                            // 多规格加价
                            $extAry = [];
                            if (products_obj.data_init.specification == 2) {
                                products_obj.data_init.ext_attr_data[0] = $.fn.deepCopy(data.msg.Combinatin[0] || [])
                                products_obj.data_init.ext_save_data = $.fn.deepCopy(data.msg.SaveCombination[0] || [])
                            }
                            let $count = products_obj.function_init.attr_combination_count();
                            if ($.isEmptyObject(products_obj.data_init.ext_attr_data[1]) === true && $count <= products_obj.config_init.option_max) {
                                $autoAry = products_obj.function_init.auto_combination();
                                // 只为第一个仓库生成基础属性组合，避免重复
                                let firstWarehouseId = products_obj.data_init.warehouse_data[0] || 0;
                                $index = 0;
                                for (let i in $autoAry) {
                                    for (let j in $autoAry[i]) {
                                        let $attrName = i,
                                            $title = $autoAry[i][j],
                                            $optionName = global_obj.json_decode_data($title);
                                        if (typeof ($extAry[firstWarehouseId]) == 'undefined') {
                                            $extAry[firstWarehouseId] = {};
                                        }
                                        $extAry[firstWarehouseId][$index] = {
                                            "AttrName": $attrName,
                                            "Combination": $optionName,
                                            "Data": $optionName,
                                            "Title": $title.join(" / "),
                                            "ProId": "",
                                            "OldPrice": "",
                                            "Price": "",
                                            "CostPrice": "",
                                            "SKU": "",
                                            "Stock": "",
                                            "VariantsId": "",
                                            "Weight": "",
                                            "WeightUnit": "kg",
                                            "PicPath": ""
                                        };
                                        if (typeof data.msg.Combinatin[firstWarehouseId] != 'undefined' && typeof data.msg.Combinatin[firstWarehouseId][$index] != 'undefined' && data.msg.Combinatin[firstWarehouseId][$index].AttrName == $attrName && data.msg.Combinatin[firstWarehouseId][$index].Data == $optionName) {
                                            // 调用存有数据表里的数据
                                            $extAry[firstWarehouseId][$index] = data.msg.Combinatin[firstWarehouseId][$index];
                                        }
                                        if (typeof (products_obj.data_init.ext_save_data[firstWarehouseId]) == 'undefined') {
                                            // 记录到保存数据(仓库)
                                            products_obj.data_init.ext_save_data[firstWarehouseId] = {};
                                        }
                                        if (typeof (products_obj.data_init.ext_save_data[firstWarehouseId][$attrName]) == 'undefined') {
                                            // 记录到保存数据(属性)
                                            products_obj.data_init.ext_save_data[firstWarehouseId][$attrName] = {};
                                        }
                                        if (typeof (products_obj.data_init.ext_save_data[firstWarehouseId][$attrName][$optionName]) == 'undefined') {
                                            // 记录到保存数据(选项)
                                            products_obj.data_init.ext_save_data[firstWarehouseId][$attrName][$optionName] = $extAry[firstWarehouseId][$index];
                                        }
                                        ++$index;
                                    }
                                }
                                products_obj.data_init.ext_attr_data[1] = $.fn.deepCopy($extAry)
                            }
                        }
                        // 仓库设置
                        if (data.msg.WarehouseSet) {
                            products_obj.data_init.warehouse_set_data = data.msg.WarehouseSet;
                        }

                        // 配置显示
                        products_obj.function_init.attr_load();
                        products_obj.function_init.attr_price_show(); //加载完才触发
                        if (data.msg.IsCombination == 0 && $.isEmptyObject(products_obj.data_init.warehouse_name_data) === false) {
                            // 单规格(仓库)
                            $('.box_attribute_tab_menu .item:eq(0)').click();
                        }
                    }
                    if (data.msg.mainAttr != "") {
                        products_obj.data_init.image_data.mainAttr = data.msg.mainAttr;
                    }
                    products_obj.function_init.attr_load();
                }
                $Status += 1;
                if ($Status > 0) {
                    $('.fixed_btn_submit .btn_submit').removeClass('btn_disabled');
                }
                return false;
            }, 'json');
        },

        //产品属性价格显示
        attr_price_show: function () {
            // 没有属性数据，规格设置显示没有数据
            let extDataSize = 0;
            if (products_obj.data_init.attr_model == 1) {
                // 多规格
                for (let k in products_obj.data_init.ext_attr_data[1]) {
                    if (products_obj.data_init.ext_attr_data[1][k]) {
                        for (let k2 in products_obj.data_init.ext_attr_data[1][k]) {
                            extDataSize += 1;
                        }
                    }
                }
            } else {
                // 多规格加价
                for (let k in products_obj.data_init.ext_attr_data[0]) {
                    if (products_obj.data_init.ext_attr_data[0][k] && products_obj.data_init.ext_attr_data[0][k].AttrName != "[\"Ship From\"]") {
                        extDataSize += 1;
                    }
                }
            }
            if (extDataSize == 0) {
                $(".spec_no_data").show();
                $(".box_multi_tab, .box_warehouse").hide();
            } else {
                $(".spec_no_data").hide();
                $(".box_multi_tab").show();
                // if (products_obj.data_init.attr_model == 1) {
                // 	$(".box_warehouse").show();
                // } else {
                // 	$(".box_warehouse").hide();
                // }
                $(".box_warehouse").show();
            }
            // 没有属性数据
            let attrCount = $(".box_cart_attribute .rows").length;
            if (attrCount < 1) {
                $(".attr_no_data").show();
                $("#add_attribute").hide();
                if (parseInt($('#edit_form input[name=ProId]').val()) == 0) {
                    $("#attribute_ext_box").hide();
                }
            } else {
                $(".attr_no_data").hide();
                $("#add_attribute").show();
                if (parseInt($('#edit_form input[name=ProId]').val()) == 0) {
                    $("#attribute_ext_box").show();
                }
            }
            // 添加规格
            if (extDataSize > 0 && attrCount > 0) {
                $(".box_combination .input").show();
            } else {
                $(".box_combination .input").hide();
            }

            // 生成表格内容
            $(".attribute_ext tbody").html(""); // 清除所有表格内容
            let Html = products_obj.function_init.attr_price_row_show();
            $(Html).each(function (index, element) {
                if (index == 1) {
                    // 多规格
                    for (let id in element) {
                        $("#AttrId_" + index + " tbody[data-id=\"" + id + "\"]").append(element[id]).find('tr').addClass('group');
                    }
                } else {
                    // 多规格加价
                    $("#AttrId_" + index + " tbody").append(element).find('tr').addClass('group');
                }
            });
            products_obj.function_init.check_default_option(products_obj.data_init.defautl_settings); //检查是否添加了默认选项
            products_obj.function_init.input_show_notnull();

            frame_obj.check_amount($('.attribute_ext .relation_box'));

            // 列表表格滚动栏效果
            if ($(".box_table").length) {
                $(".box_table table tr").each(function (index, element) {
                    let $prevColumnWidth = 0;
                    $(element).find('td').each(function (key, value) {
                        if ($(value).hasClass('flex_item')) {
                            let position = 'left';
                            if ($(value).hasClass('last')) {
                                position = 'right';
                                $prevColumnWidth = 0;
                            }
                            if ($(value).hasClass('sec_last')) {
                                position = 'right';
                                $prevColumnWidth = $(element).find('td').last().outerWidth(true);
                            }
                            if (key == 1) {
                                $prevColumnWidth = 62;
                            }
                            $(value).css(position, $prevColumnWidth);
                        }
                        $prevColumnWidth += parseFloat($(value).outerWidth(true));
                    })
                })

                // 滚动条
                let $boxWidth = $(".box_table").width(),
                    $wrapWidth = $('.r_con_wrap').width()
                $tableWidth = $(".box_table table").width();
                if ($tableWidth > $wrapWidth) {
                    $(".box_table table").addClass("fixed");
                }
                $boxWidth >= $tableWidth && ($tableWidth -= 10); // 隐藏滚动条
                $(".scroll_sticky_content>div").css({"width": $tableWidth, "height": 1});
            }
        },

        // tbody显示 -> 回显PriorityShippingOvId
        attr_price_row_show: function () {
            let html = [], _html, html_attr, val_ary, _ary, $PIC, $IMG, $PV, $OV, html_tmp,
                $Html_1 = {},
                $Html_0 = "",
                html_t = $('#attribute_tmp .column tbody').html(),
                obj = products_obj.data_init.ext_attr_data[1];
            // 多规格
            if (products_obj.data_init.attr_model == 1) {
                html_tmp = $('#tmp_1').html();
                for (let id in obj) {
                    if (id > 0 && $.isEmptyObject(products_obj.data_init.warehouse_name_data) === false && !global_obj.in_array(id, products_obj.data_init.warehouse_set_data)) {
                        continue;
                    }
                    let wHtml = "";
                    for (let index in obj[id]) {
                        let _data = obj[id][index];
                        _html = html_tmp;
                        $extend = global_obj.json_encode_data(_data.Data);
                        val_ary = {
                            "SKU": _data.SKU,
                            "OldPrice": _data.OldPrice,
                            "Price": _data.Price,
                            "CostPrice": _data.CostPrice,
                            "Stock": _data.Stock,
                            "Weight": _data.Weight,
                            "WeightUnit": _data.WeightUnit,
                            "VariantsId": _data.VariantsId,
                            "PriorityShippingOvId": _data.PriorityShippingOvId || 0
                        };
                        $PIC = "class=\"attr_picture attr_picture_empty\"";
                        $IMG = "";
                        if (products_obj.data_init.ext_save_data[id][_data.AttrName][_data.Data]) {
                            // 已有相关数据
                            _ary = products_obj.data_init.ext_save_data[id][_data.AttrName][_data.Data];
                            val_ary = {
                                "SKU": _ary.SKU,
                                "OldPrice": _ary.OldPrice,
                                "Price": _ary.Price,
                                "CostPrice": _ary.CostPrice,
                                "Stock": _ary.Stock,
                                "Weight": _ary.Weight,
                                "WeightUnit": _ary.WeightUnit,
                                "VariantsId": _ary.VariantsId,
                                "PriorityShippingOvId": _ary.PriorityShippingOvId || 0
                            };
                            if (_ary.PicPath) {
                                $PIC = "class=\"attr_picture attr_picture_empty saved\" style=\"background-image:url('" + _ary.PicPath + "');\"";
                                $IMG = _ary.PicPath;
                            }
                        }
                        $OV = val_ary["OldPrice"] != "" && parseFloat(val_ary["OldPrice"]) > -1 ? parseFloat(val_ary["OldPrice"]).toFixed(2) : '';
                        $PV = val_ary["Price"] != "" && parseFloat(val_ary["Price"]) > -1 ? parseFloat(val_ary["Price"]).toFixed(2) : '';
                        $CV = val_ary["CostPrice"] != "" && parseFloat(val_ary["CostPrice"]) > -1 ? parseFloat(val_ary["CostPrice"]).toFixed(2) : '';
                        html_attr = ' data-id="' + val_ary['VariantsId'] + '" data-position="' + index + '" data-attr="' + global_obj.htmlspecialchars(_data.AttrName) + '" data-extend="' + global_obj.htmlspecialchars(_data.Data) + '" data-wid="' + id + '"';
                        $($extend).each(function (index2, element2) {
                            html_attr += " data-attr-" + index2 + "=\"" + global_obj.htmlspecialchars(element2) + "\"";
                        });
                        if (typeof val_ary["SKU"] != 'string') val_ary["SKU"] = val_ary["SKU"].toString();
                        let _IsDefault = _data.IsDefault == undefined ? 0 : _data.IsDefault;

                        // 生成优先发货仓库选项
                        let priorityShippingOptions = '';
                        let warehouseKeys = Object.keys(products_obj.data_init.warehouse_name_data);
                        let firstWarehouseId = null;
                        let isFirstOption = true;



                        // 过滤掉无效的仓库ID（如0或空值），找到第一个有效的仓库ID
                        for (let warehouseId in products_obj.data_init.warehouse_name_data) {
                            if (warehouseId && warehouseId != '0' && warehouseId != 0) {
                                if (firstWarehouseId === null) {
                                    firstWarehouseId = warehouseId;
                                }

                                let warehouseName = products_obj.data_init.warehouse_name_data[warehouseId];
                                let selected = '';

                                // 如果有保存的优先发货仓库ID，则选中对应选项，否则默认选择第一个有效仓库
                                if (val_ary["PriorityShippingOvId"] && val_ary["PriorityShippingOvId"] != '0' && val_ary["PriorityShippingOvId"] != 0) {
                                    // 确保类型一致的比较
                                    if (parseInt(val_ary["PriorityShippingOvId"]) == parseInt(warehouseId)) {
                                        selected = ' selected';
                                    }
                                } else if (isFirstOption) {
                                    selected = ' selected';
                                    isFirstOption = false;
                                }

                                priorityShippingOptions += '<option value="' + warehouseId + '"' + selected + '>' + global_obj.htmlspecialchars(warehouseName) + '</option>';
                            }
                        }

                        // 如果库存为空，使用默认库存值
                        let stockValue = val_ary["Stock"];
                        if (!stockValue || stockValue === '' || stockValue === '0') {
                            let defaultInventory = $('input[name="DefaultInventory"]').val() || '5000';
                            stockValue = defaultInventory;
                        }

                        wHtml += _html.replace(/XXX/g, global_obj.htmlspecialchars(_data.Data))
                            .replace('Name', global_obj.htmlspecialchars(_data.Title))
                            .replace('%SKUV%', global_obj.htmlspecialchars(val_ary["SKU"]))
                            .replace('%OV%', $OV)
                            .replace('%PV%', $PV)
                            .replace('%CV%', $CV)
                            .replace('%SV%', stockValue)
                            .replace('%WV%', val_ary["Weight"])
                            .replace('attr_txt=""', html_attr)
                            .replace(/data-pic=""/g, $PIC)
                            .replace(/%IMG%/g, $IMG)
                            .replace(/%VID%/g, val_ary["VariantsId"])
                            .replace(/%Attr%/g, global_obj.htmlspecialchars(_data.AttrName))
                            .replace(/%Options%/g, global_obj.htmlspecialchars(_data.Data))
                            .replace(/%OvId%/g, "_" + id)
                            .replace(/%Position%/g, index)
                            .replace(/%IsDefault%/g, _IsDefault)
                            .replace(/%WUV%/g, val_ary["WeightUnit"])
                            .replace(/%PRIORITY_SHIPPING_OPTIONS%/g, priorityShippingOptions);
                    }
                    $Html_1[id] = wHtml;
                }
            }
            // 多规格加价
            if (products_obj.data_init.attr_model == 0) {
                $Html_0 += html_t.replace('XXX', 0).replace('Column', lang_obj.manage.products.group_attr);
                html_tmp = $('#tmp_0').html();
                let $attrName = '', $optionName = '', $title = '';
                obj = products_obj.data_init.ext_attr_data[0];
                let warehouseNameData = {}
                for (let k in products_obj.data_init.warehouse_name_data) {
                    warehouseNameData[products_obj.data_init.warehouse_name_data[k]] = k
                }

                // 先清理重复的Ship From项
                if (obj && Array.isArray(obj)) {
                    let cleanedObj = [];
                    let shipFromItems = new Set();

                    for (let index in obj) {
                        let element = obj[index];
                        if (element.AttrName === '["Ship From"]') {
                            let shipFromKey = element.Title + '|' + element.Data;
                            if (!shipFromItems.has(shipFromKey)) {
                                shipFromItems.add(shipFromKey);
                                cleanedObj.push(element);
                            }
                        } else {
                            cleanedObj.push(element);
                        }
                    }
                    obj = cleanedObj;
                }

                // 用于去重的集合，记录已处理的属性和选项组合
                let processedItems = new Set();

                for (let index in obj) {
                    let element = obj[index];
                    _html = html_tmp;
                    $attrName = element.AttrName;
                    $title = element.Title;
                    $optionName = element.Data;

                    // 创建唯一标识符用于去重
                    let itemKey = $attrName + '|' + $optionName;

                    // 如果已经处理过这个组合，跳过
                    if (processedItems.has(itemKey)) {
                        continue;
                    }

                    if ($attrName == '["Ship From"]' && $.isEmptyObject(warehouseNameData) === false) {
                        let id = warehouseNameData[element.Title] || 0
                        if (!global_obj.in_array(id, products_obj.data_init.warehouse_set_data)) {
                            continue;
                        }
                    }

                    // 标记为已处理
                    processedItems.add(itemKey);
                    $attrTitle = global_obj.json_encode_data($attrName);
                    val_ary = {"OldPrice": 0, "Price": 0, "CostPrice": 0, "VariantsId": 0, "PriorityShippingOvId": 0};
                    if (products_obj.data_init.ext_save_data[$attrName][$optionName]) {
                        //已有相关数据
                        _ary = products_obj.data_init.ext_save_data[$attrName][$optionName];
                        val_ary = {
                            "OldPrice": _ary.OldPrice,
                            "Price": _ary.Price,
                            "CostPrice": _ary.CostPrice,
                            "VariantsId": _ary.VariantsId,
                            "PriorityShippingOvId": _ary.PriorityShippingOvId || 0
                        };
                    }
                    $PIC = "class=\"attr_picture attr_picture_empty\"";
                    $IMG = "";
                    $OV = val_ary["OldPrice"] != "" && parseFloat(val_ary["OldPrice"]) > -1 ? parseFloat(val_ary["OldPrice"]).toFixed(2) : '';
                    $PV = val_ary["Price"] != "" && parseFloat(val_ary["Price"]) > -1 ? parseFloat(val_ary["Price"]).toFixed(2) : '';
                    $CV = val_ary["CostPrice"] != "" && parseFloat(val_ary["CostPrice"]) > -1 ? parseFloat(val_ary["CostPrice"]).toFixed(2) : '';
                    html_attr = ' data-id="' + val_ary['VariantsId'] + '" data-position="' + index + '" data-attr="' + global_obj.htmlspecialchars($attrName) + '" data-extend="' + global_obj.htmlspecialchars($optionName) + '"';
                    $Html_0 += _html.replace(/XXX/g, global_obj.htmlspecialchars($optionName))
                        .replace('Name', $attrTitle[0] + ':<br />' + $title)
                        .replace('%OV%', $OV)
                        .replace('%PV%', $PV)
                        .replace('%CV%', $CV)
                        .replace('attr_txt=""', html_attr)
                        .replace(/data-pic=""/g, $PIC)
                        .replace(/%IMG%/g, $IMG)
                        .replace(/%VID%/g, val_ary["VariantsId"])
                        .replace(/%Attr%/g, global_obj.htmlspecialchars($attrName))
                        .replace(/%Options%/g, global_obj.htmlspecialchars($optionName))
                        .replace(/%Position%/g, index);
                }
                ;
            }
            html = [$Html_0, $Html_1];
            return html;
        },

        input_show_notnull: function () {
            let type = products_obj.data_init.specification;
            if (type == 0) {
                // 单规格
                $('#AttrId_0').find('input').prop('disabled', false);
                $("#AttrId_0 input[data-type=\"price\"]").removeAttr("notnull").removeClass("null");
                $('#AttrId_1').find('input').prop('disabled', false);
                $("#AttrId_1 input[data-type=\"price\"]").removeAttr("notnull").removeClass("null");
                if (products_obj.data_init.warehouse_id == 0) {
                    // 显示默认
                    $("#AttrId_1 tbody[data-id=0] input").prop("disabled", false);
                } else {
                    // 显示仓库
                    $("#AttrId_1 tbody[data-id=0] input").prop("disabled", true);
                }
            } else if (type == 1) {
                // 多规格
                $('#AttrId_0').find('input').prop('disabled', true);
                $('#AttrId_1').find('input').prop('disabled', false);
                $("#AttrId_1 input[data-type=\"price\"]").attr('notnull', 'notnull');

                // 检查是否设置了仓库
                let hasWarehouseSet = products_obj.data_init.warehouse_set_data &&
                                    products_obj.data_init.warehouse_set_data.length > 0 &&
                                    !products_obj.data_init.warehouse_set_data.every(id => id == 0);

                if (!hasWarehouseSet || products_obj.data_init.warehouse_id == 0) {
                    // 没有设置仓库或显示默认 - 确保data-id="0"的表格可编辑
                    $("#AttrId_1 tbody[data-id=0] input[data-type=\"price\"]").attr("notnull", "notnull").addClass("null");
                    $("#AttrId_1 tbody[data-id=0] input").prop("disabled", false);
                } else {
                    // 已设置仓库且不显示默认 - 禁用data-id="0"的表格
                    $("#AttrId_1 tbody[data-id=0] input[data-type=\"price\"]").removeAttr("notnull").removeClass("null");
                    $("#AttrId_1 tbody[data-id=0] input").prop("disabled", true);
                }
            } else {
                // 多规格加价
                $('#AttrId_0').find('input').prop('disabled', false);
                $('#AttrId_1').find('input').prop('disabled', true);
                if ($("#attribute_unit_box tr[data-id=0]").is(":hidden")) {
                    // 基础设置只能固定显示默认数据
                    $("#attribute_unit_box tr[data-id=0]").show().siblings().hide();
                }
            }
            $("#attribute_unit_box tr[data-type=\"warehouse\"][data-disabled=0] input").prop('disabled', false);
            $("#attribute_unit_box tr[data-type=\"warehouse\"][data-disabled=1] input").prop('disabled', true);
        },

        sync_product_attribute: function ($CateId) {
            var $Sync = $('#sync_product_hidden').val(),
                $ProId = $('#sync_product_id').val();
            $.post('./?do_action=products.get_sync_attributes', {
                'Sync': $Sync,
                'ProId': $ProId,
                'CateId': $CateId
            }, function (result) {
                if (result.ret == 1) {
                    if (result.msg.Attr) {
                        var $VId = '';
                        for (k in result.msg.Attr) {
                            for (k2 in result.msg.Attr[k]) {
                                $VId = result.msg.Attr[k][k2];
                                products_obj.data_init.attr_data[$VId] = new Object;
                                products_obj.data_init.attr_data[$VId]['Info'] = {
                                    "Cart": "1",
                                    "Color": "0",
                                    "Column": k,
                                    "Desc": "0",
                                    "Name": k2,
                                    "VId": $VId
                                };
                                products_obj.data_init.attr_data[$VId]['Data'] = ["", "0", "0.00", "0", "0"];
                            }
                        }
                        $('.box_cart_attribute .box_button_choice').each(function () {
                            var $ID = $(this).data('id'),
                                $Title = $(this).children('input:eq(0)').val(),
                                $NewID = result.msg.Attr[$Title][0];
                            $(this).attr('data-id', $NewID);
                            $(this).children('input:eq(0)').attr('name', $(this).children('input:eq(0)').attr('name').replace($ID, $NewID));
                            $(this).children('input:eq(1)').attr('name', $(this).children('input:eq(1)').attr('name').replace($ID, $NewID));
                            $(this).find('.btn_attr_choice').each(function () {
                                var $VId = $(this).find('.attr_current').val(),
                                    $Target = $(this).find('input[name=AttrOption\\[\\]]'),
                                    $JSON = products_obj.function_init.attr_option_json($Target.val(), 1),
                                    $Name = $JSON['Name'],
                                    $NewVId = result.msg.Attr[$Title][$Name];
                                $(this).children('.attr_current').val($(this).children('.attr_current').val().replace($VId, $NewVId));
                                $Target.val($Target.val().replace($VId, $NewVId).replace($ID, $NewID));
                            });
                        });
                    }
                    if (result.msg.Ext) {
                        products_obj.data_init.ext_attr_data[1] = result.msg.Ext;
                    }
                    if (result.msg.IsCombination == 1) {
                        if (!$('.open_attr_price .switchery>input').is(':checked')) {
                            //第一次打开
                            $('.open_attr_price .switchery').click();
                        } else {
                            //执行两次，刷新ID值
                            $('.open_attr_price .switchery').click();
                            $('.open_attr_price .switchery').click();
                        }
                    }
                    $('.description_tab_box .drop_down .item:eq(0)').click(); //默认勾选"详细介绍"
                    $('.fixed_btn_submit .btn_submit').removeClass('btn_disabled');
                }
            }, 'json');
        },

        // 产品主图上传
        main_picture_upload: function () {
            frame_obj.mouse_click($('.upload_menu li[data-type=gallery]'), 'pro', function () {
                // 确保PicDetail容器存在且可见
                let $picDetail = $('#PicDetail');
                if ($picDetail.length === 0) {
                    console.error('PicDetail容器不存在');
                    return;
                }
               
                // 显示容器
                $picDetail.show();

                // 获取当前最后一个图片项的num，如果没有则从0开始
                let $lastImg = $picDetail.find('.img:last');
                let $num = $lastImg.length > 0 ? $lastImg.attr('num') : '0';

                // 如果没有可用的上传项，创建一个
                if ($picDetail.find('.img.show_btn').length === 0) {
                    let newNum = parseInt($num) + 1;
                    let html = frame_obj.multi_img_item("PicPath[]", newNum, 1, 'products');
                    $picDetail.append(html);
                    $num = newNum.toString();
                }



                // 修复参数格式：使用正确的选择器格式
                frame_obj.photo_choice_init('PicDetail .img[num=' + $num + ']', 'products', 99, 'do_action=products.products_img_del&Model=products', 1, "frame_obj.upload_pro_img_init(1, '#PicDetail', 1);products_obj.function_init.main_picture_upload();");
            });
            // 先清除绑定拖动事件
            $('.global_container[data-name=pic_info] .pro_multi_img').dragsort('destroy');
            // 图片拖动
            frame_obj.dragsort($('.global_container[data-name=pic_info] .pro_multi_img'), '', 'dl:not(".show_btn")', 'a[class!=myorder]', '<dl class="img placeHolder"><dt class="preview_pic"><a href="javascript:;"></a></dt></dl>', '', function () {
                products_obj.function_init.read_all_image_data();
            });
            // 产品主图删除点击事件
            frame_obj.mouse_click($('.global_container[data-name=pic_info] .pro_multi_img .pic_btn .del'), 'proDel', function ($this) {
                var $obj = $this.parents('.img'),
                    $obj_par = $obj.parent(),
                    $num = parseInt($obj.attr('num')),
                    $path = $obj.find('input[name=PicPath\\[\\]]').val();
                $obj.remove();
                frame_obj.upload_pro_img_init(1);
                var $i = 0;
                $obj_par.find('.img').each(function () {
                    $(this).attr('num', $i);
                    ++$i;
                });
                if ($(".view_more_image").length) $(".view_more_image").click();
                products_obj.function_init.show_btn_init();
                products_obj.function_init.read_all_image_data();
            });
            products_obj.function_init.show_btn_init();
            frame_obj.imageAltDdit();
        },

        side_picture_upload: function () {
            $(".fixed_edit_attribute_picture_edit .relation_box").on("click", ".upload_btn", function () {
                let $id = $(this).parents(".multi_img").attr("id"),
                    $num = $(this).parents('.img').attr('num');
                frame_obj.photo_choice_init($id + " .img[num;" + $num + "]", "products", 99, "", 1, "");
            });
            $('.fixed_edit_attribute_picture_edit .relation_box').on("click", ".del", function () {
                let $obj = $(this).parents('.img'),
                    $obj_par = $obj.parent(),
                    i = 0;
                $obj.remove();
                frame_obj.upload_pro_img_init(1);
                $obj_par.find('.img').each(function () {
                    $(this).attr('num', i);
                    ++i;
                });
            });
        },

        // 属性选项图片上传
        option_picture_upload: function (data) {
            // Html内容
            let $obj = data.obj,
                $html = "",
                $picHtml = $("#icon_picture_html").html(),
                $target = {};
            if ($(".picupload").length > 0) {
                $(".picupload").remove();
            }
            $html += '<div class="picupload" data-id="' + data.id + '" data-attr="' + global_obj.htmlspecialchars(data.attr) + '" data-option="' + global_obj.htmlspecialchars(data.option) + '">';
            $html += '<div class="picupload_title">' + lang_obj.global.picture + '</div>';
            $html += '<div class="picupload_close"></div>';
            $html += '<div class="picupload_picture">' + $picHtml + '</div>';
            $html += '<div class="picupload_submit"></div>';
            $html += '</div>';
            $html = $html.replace("XXXDetail", "IconDetail").replace("XXXPath", "IconPath");
            $target = $($html);
            $target.appendTo(document.body);

            // 定位
            let $positon = $obj.offset(),
                $top = $positon.top + $obj[0].offsetHeight,
                $left = $positon.left,
                $m = document.compatMode == "CSS1Compat",
                viewPort = {
                    l: window.pageXOffset || ($m ? document.documentElement.scrollLeft : document.body.scrollLeft),
                    w: window.innerWidth || ($m ? document.documentElement.clientWidth : document.body.clientWidth)
                },
                $targetW = $target.width();
            if ($left + $targetW > viewPort.l + viewPort.w) {
                $left -= $targetW;
            }
            $target.css({
                position: "absolute",
                left: $left + "px",
                top: $top + "px"
            });
            if ($target.height() + parseInt($target.css("top")) > $(window).height()) {
                $target.css("top", parseInt($target.css("top")) - $target.height());
            }
            $target.show();

            // 隐藏元素
            let hide = function (ev) {
                ev.data.target.remove();
                $('html').off('mousedown', hide);
            }
            $('html, .picupload_close').mousedown({
                target: $target
            }, hide);
            $target.mousedown(function (ev) {
                ev.stopPropagation();
            });
            $("#righter").off("mousedown").on("mousedown", ".pop_form", function (ev) {
                ev.stopPropagation();
            });

            // 图片上传
            let delPicture = function () {
                // 移除配图信息
                if (products_obj.data_init.option_data[data.attr][data.option]) {
                    products_obj.data_init.option_data[data.attr][data.option].main = "";
                }
                $(".box_button_choice[data-id=\"" + data.id + "\"] .attr_current[value=\"" + data.option + "\"]").prev().prev().removeAttr("style");
                // 选项数据储存
                let json = global_obj.json_decode_data(products_obj.data_init.option_data[data.attr]);
                $(".box_cart_attribute .box_button_choice[data-attr=\"" + $.quotationMarksTransferred(data.attr) + "\"] .attr_data").val(json);
            }
            frame_obj.upload_img_init(delPicture);
            frame_obj.mouse_click($("#IconDetail .upload_btn"), 'pro', function ($this) {
                frame_obj.photo_choice_init("IconDetail", "products", 1, "", 1, "products_obj.function_init.option_picture_upload_result();");
            });

            // 获取数据
            let $picPath = "";
            if (products_obj.data_init.option_data[data.attr][data.option]) {
                $picPath = products_obj.data_init.option_data[data.attr][data.option].main;
            }
            if ($picPath) {
                $target.find(".pic_btn .zoom").attr("href", $picPath);
                $target.find(".preview_pic a").remove();
                $target.find(".preview_pic").append(frame_obj.upload_img_detail($picPath)).children(".upload_btn").hide().parent().parent().addClass("isfile").removeClass("show_btn");
                $target.find(".preview_pic").children("input[type=hidden]").val($picPath).attr("save", 1).trigger("change");
            }
        },

        // 属性选项图片上传返回数据
        option_picture_upload_result: function () {
            let $path = $("#IconDetail input[name=\"IconPath\"]").val(),
                $id = $(".picupload").data("id"),
                $attr = $(".picupload").data("attr"),
                $option = $(".picupload").data("option");
            if ($path === undefined) {
                // 没有获取成功，以图片管理的父级来获取
                $path = parent.$("#IconDetail input[name=\"IconPath\"]").val();
                $id = parent.$(".picupload").data("id");
                $attr = parent.$(".picupload").data("attr");
                $option = parent.$(".picupload").data("option");
                parent.$(".box_button_choice[data-id=\"" + $id + "\"] .attr_current[value=\"" + $option + "\"]").prev().prev().attr("style", "background-image:url(" + $path + ")");
                parent.products_obj.data_init.option_data[$attr][$option].main = $path;
                // 选项数据储存
                let json = global_obj.json_decode_data(parent.products_obj.data_init.option_data[$attr]);
                parent.$(".box_cart_attribute .box_button_choice[data-attr=\"" + $.quotationMarksTransferred($attr) + "\"] .attr_data").val(json);
                parent.$(".picupload").remove();
                // 移除遮罩层和图片弹窗
                parent.global_obj.div_mask(1);
                parent.$(".pop_form").remove();
            } else {
                $(".box_button_choice[data-id=\"" + $id + "\"] .attr_current[value=\"" + $option + "\"]").prev().prev().attr("style", "background-image:url(" + $path + ")");
                products_obj.data_init.option_data[$attr][$option].main = $path;
                // 选项数据储存
                let json = global_obj.json_decode_data(products_obj.data_init.option_data[$attr]);
                $(".box_cart_attribute .box_button_choice[data-attr=\"" + $.quotationMarksTransferred($attr) + "\"] .attr_data").val(json);
                $(".picupload").remove();
                // 移除遮罩层和图片弹窗
                global_obj.div_mask(1);
                $(".pop_form").remove();
            }
        },

        batch_delete_combination: function (listObj) {
            listObj.each(function (index, element) {
                let $obj = $(this).parents("tr.group"),
                    $Position = $obj.data("position"),
                    $attrName = global_obj.json_decode_data($obj.data("attr")),
                    $extend = $obj.data("extend"),
                    $optionName = global_obj.json_decode_data($extend),
                    $wid = $obj.data("wid"),
                    $Table = $obj.parent(),
                    $Count = 0, $OptionParent = [], $OptionObj = [], $OptionIndex = "";
                if ($extend) {
                    $($extend).each(function (index, element) {
                        // 检查是否影响相关的选项数据
                        $Count = $(".attribute_ext .group[data-attr-" + index + "=\"" + $.quotationMarksTransferred(element) + "\"]").length;
                        if ($Count == 1) {
                            // 该选项的最后一个选项组合，自动删掉该选项
                            let $Value = element;
                            $(products_obj.data_init.attr_data[index].Options).each(function (index, element) {
                                if ($Value == element) {
                                    $OptionIndex = index;
                                }
                            });
                            $OptionObj = $(".box_cart_attribute .box_button_choice:eq(" + index + ") .attr_current:eq(" + $OptionIndex + ")").parent();
                            $OptionParent = $OptionObj.parents(".attr_selected");
                            $OptionObj.remove();
                            $OptionObj = $(".box_cart_attribute .box_button_choice:eq(" + index + ")"); // 重新定义
                            if ($OptionObj.find(".btn_attr_choice").length < 1) {
                                // 没有选项，显示placeholder
                                $OptionParent.find(".placeholder").removeClass("hide");
                            }
                            products_obj.data_init.attr_data[index].Options.splice($OptionIndex, 1);
                            // 去掉选项
                            let $attr = products_obj.data_init.attr_data[index].Name;
                            $attr = global_obj.json_decode_data([$attr]);
                            let $value = global_obj.json_decode_data([$Value]);
                            for (let index in products_obj.data_init.ext_attr_data[0]) {
                                let element = products_obj.data_init.ext_attr_data[0][index];
                                if (element.AttrName == $attr && element.Data == $value) {
                                    products_obj.data_init.ext_attr_data[0].splice(index, 1);
                                }
                            }
                            delete products_obj.data_init.ext_save_data[$attr][$value];
                        }
                    });
                }
                // 删除相关信息
                if ($obj.length) {
                    $(this).parents("tr.group").remove();
                    products_obj.data_init.ext_attr_data[1][$wid].splice($Position, 1);
                    delete products_obj.data_init.ext_save_data[$wid][$attrName][$optionName];
                    $Table.find(".group").each(function (index, element) {
                        $(this).attr("data-position", index);
                    });
                }
            });
        },

        // 安全删除仓库组合数据，不影响属性选项
        safe_delete_warehouse_combination: function (listObj, warehouseId) {
            listObj.each(function (index, element) {
                let $obj = $(this).parents("tr.group"),
                    $Position = $obj.data("position"),
                    $attrName = global_obj.json_decode_data($obj.data("attr")),
                    $extend = $obj.data("extend"),
                    $optionName = global_obj.json_decode_data($extend),
                    $wid = $obj.data("wid"),
                    $Table = $obj.parent();

                // 只删除组合行和相关数据，不删除属性选项
                if ($obj.length) {
                    $(this).parents("tr.group").remove();

                    // 清理扩展属性数据
                    if (products_obj.data_init.ext_attr_data[1] && products_obj.data_init.ext_attr_data[1][$wid]) {
                        products_obj.data_init.ext_attr_data[1][$wid].splice($Position, 1);
                    }

                    // 清理保存数据
                    if (products_obj.data_init.ext_save_data[$wid] &&
                        products_obj.data_init.ext_save_data[$wid][$attrName] &&
                        products_obj.data_init.ext_save_data[$wid][$attrName][$optionName]) {
                        delete products_obj.data_init.ext_save_data[$wid][$attrName][$optionName];
                    }

                    // 重新设置位置索引
                    $Table.find(".group").each(function (index, element) {
                        $(this).attr("data-position", index);
                    });
                }
            });
        },

        //字长判断
        check_char_length: function (e, max) {
            e.change(function (event) {
                let curLength = e.val().length;
                if (curLength > max) {
                    e.val($.trim(e.val()).substr(0, max)).trigger('change');
                    global_obj.win_alert_auto_close(lang_obj.manage.products.tips.text_input_max.replace("{{max}}", max), "fail", 1000, "8%");
                    return;
                }
            }).keyup(function () {
                e.trigger("change");
            });
        },

        // 产品图片加入
        put_prod_img: function (imgpath, alt) {
            if (!imgpath) return;
            let picNum = $('.global_container[data-name=pic_info] .pro_multi_img .img').length - 1;
            let obj = $('#PicDetail').find('.img[num=' + picNum + ']');
            if (obj.find('input[type=hidden]').attr('save') == 0 && !obj.hasClass('video')) {
                obj.find('.pic_btn .zoom').attr('href', imgpath);
                obj.find('.preview_pic').append(frame_obj.upload_img_detail(imgpath)).children('.upload_btn').hide().parent().parent().addClass('isfile').removeClass('show_btn');
                obj.find('.preview_pic').children('input[type=hidden]').val(imgpath).attr('save', 1);
                obj.find('input[name^=Alt]').val(alt)
            }
           
            let html = frame_obj.multi_img_item("PicPath[]", ++picNum, 1, 'products');
            $('#PicDetail').append(html);

            frame_obj.upload_pro_img_init(1, '#PicDetail', 1);

            products_obj.function_init.main_picture_upload();
            products_obj.function_init.show_btn_init();
            products_obj.function_init.read_all_image_data();
        },

        // 产品图片加入 (本地上传)
        pre_put_prod_img: function () {
            let picNum = $('.global_container[data-name=pic_info] .pro_multi_img .img').length - 1
            let obj = $('#PicDetail').find('.img[num=' + picNum + ']')
            if (obj.find('input[type=hidden]').attr('save') == 0 && !obj.hasClass('video')) {
                let $icon = $('#PicDetail').attr('data-load-icon')
                obj.find('.preview_pic').append(frame_obj.upload_img_detail($icon)).children('.upload_btn').hide().parent().parent().addClass('isfile').removeClass('show_btn')
            }
            let html = frame_obj.multi_img_item("PicPath[]", (picNum + 1), 1, 'products')
            $('#PicDetail').append(html)
            frame_obj.upload_pro_img_init(1, '#PicDetail', 1)
            return picNum
        },

        // 产品图片更新
        update_prod_img: function (picNum, imgpath) {
            if (!imgpath) return;
            
            let obj = $('#PicDetail').find('.img[num=' + picNum + ']')
            if (obj.find('input[type=hidden]').attr('save') == 0 && !obj.hasClass('video')) {
                obj.find('.pic_btn .zoom').attr('href', imgpath)
                obj.find('.preview_pic img').attr('src', imgpath)
                obj.find('.preview_pic .upload_btn').hide().parent().parent().addClass('isfile').removeClass('show_btn')
                obj.find('.preview_pic').children('input[type=hidden]').val(imgpath).attr('save', 1)
            }
            products_obj.function_init.main_picture_upload()
            products_obj.function_init.show_btn_init()
            products_obj.function_init.read_all_image_data()
        },

        // 初始化产品上传按钮
        show_btn_init: function () {
            let txtHtml = '<dd class="upload_txt"><p>' + lang_obj.manage.products.upload.upload + '</p><p>' + lang_obj.manage.products.upload.upload_local + '</p></dd>';
            $('.global_container[data-name=pic_info] .pro_multi_img .img .upload_txt').remove();
            $('.global_container[data-name=pic_info] .pro_multi_img .img.show_btn').append(txtHtml);
            let picNum = $('.global_container[data-name=pic_info] .pro_multi_img .img').length - 1;
            if (picNum > 0) {
                $('.global_container[data-name=pic_info] .pro_multi_img').show();
                $('.upload_file_box').hide();
                $('.upload_select_menu').show()
            } else {
                $('.upload_file_box').show();
                $('.global_container[data-name=pic_info] .pro_multi_img').hide();
                $('.upload_select_menu').hide()
            }
        },

        // 获取产品主图数据
        view_more_image: function () {
            let $id = $("#ProId").val();
            $.post("/api/Product/GetProductImages/", {"id": $id}, function (result) {
                if (result.ret == 1) {
                    products_obj.data_init.image_data.all = result.msg.imageData;
                    products_obj.data_init.image_data.more = result.msg.moreImageData;
                    products_obj.data_init.image_data.moreAlt = result.msg.moreAltData;
                    products_obj.data_init.image_data.videoItem = result.msg.videoItem;

                    if (result.msg.moreImageData && result.msg.moreImageData.length > 0) {
                        $(".upload_file_box").after('<div class="view_more_image"><span>' + lang_obj.manage.products.tips.more_image.replace("{{count}}", result.msg.moreImageData.length) + '</span></div>');
                        let html = "";
                        $.each(result.msg.moreImageData, function (index, element) {
                            if (result.msg.videoItem == element) {
                                if (element.indexOf('x-oss-process=video') != -1) {
                                    element += "&video";
                                } else {
                                    element += "?video";
                                }
                            }
                            html += '<input type="hidden" name="PicPath[]" value="' + element + '" />';
                        });
                        $(".view_more_image").append(html);
                    }

                    if (result.msg.altData && result.msg.altData.length > 0) {
                        $.each($(result.msg.altData), function (index, value) {
                            $('#PicDetail.pro_multi_img .img[num=' + index + '] .pic_btn').find('[type=hidden]').val(value);
                        })
                    }
                }
            }, "json");
        },

        // 更新关联主图的主属性选项
        associate_main_image: function () {
            let $mainObj = $("#box_main_image .box_main_attr"),
                html = "";
            html += "<option value=\"\" data-id=\"0\">" + lang_obj.global.selected + "</option>";
            $(products_obj.data_init.attr_data).each(function (index, element) {
                html += "<option value=\"" + element.Name + "\" data-id=\"" + element.AttrId + "\">" + element.Name + "</option>";
            });
            $mainObj.find("select").html(html);
            let $boxObj = $mainObj.find(".box_select");
            let $selectObj = $mainObj.find("select");
            if (products_obj.data_init.image_data.mainAttr != "") {
                $boxObj.find("option[value=\"" + products_obj.data_init.image_data.mainAttr + "\"]").prop("selected", true);
                $selectObj.trigger("change");
            }
        },

        // 关联主图的拖动排序效果
        associate_main_image_move: function (obj, attrName, extend, clean) {
            // 先清除绑定拖动事件
            clean === true && obj.find('.upload_file_multi').dragsort('destroy');
            // 图片拖动
            frame_obj.dragsort(obj.find('.upload_file_multi'), '', 'dl:not(".show_btn")', 'a[class!=myorder]', '<dl class="img placeHolder"><dt class="preview_pic"><a href="javascript:;"></a></dt></dl>', '', function () {
                products_obj.function_init.write_option_picture_data(obj, attrName, extend);
            });
        },

        // 记录关联主图的数据记录
        write_option_picture_data: function (obj, attrName, extend) {
            let $picAry = [];
            obj.find(".img").each(function (index, element) {
                let $picPath = $(element).find("input[type=hidden]").val();
                if ($picPath) {
                    $picAry[index] = $picPath;
                }
            });
            products_obj.data_init.option_data[attrName][extend].picture = $picAry;
            let json = global_obj.json_decode_data(products_obj.data_init.option_data[attrName]);
            $(".box_cart_attribute .box_button_choice[data-attr=\"" + attrName + "\"] .attr_data").val(json);
        },

        read_all_image_data: function () {
            let pathAry = [];
            $(".global_container[data-name=pic_info] .pro_multi_img .img").each(function (index, element) {
                let path = $(element).find("input[type=hidden]").val();
                pathAry[index] = path;
            });
            products_obj.data_init.image_data.all = pathAry;
        },

        /**
         * 检查有没有已经是勾选了默认属性
         * 情况1：一开始进来 没有操作过 默认第一个属性是默认选项
         * 情况2：手动取消了默认选项 这种情况下 就不显示默认标签
         */
        check_default_option: function (IsDefautlSettings) {
            let _visibleTrList = $('#AttrId_1 tbody:visible tr');
            let _addVisibleTrList = $('#AttrId_1 tbody[data-id=0]');
            if (_addVisibleTrList.css('display') == 'table-row-group') {
                _visibleTrList = $('#AttrId_1 tbody[data-id=0] tr');
            } else { //检查在多仓库的情况下 只能加一个默认选项
                let _siblingsTbodyTr = _visibleTrList.parent().siblings("tbody:not([data-id=0])").find('tr'),
                    _siblingsCheckedOption = 0;
                _siblingsTbodyTr.each(function () {
                    if ($(this).find(".attr_checkbox input[name*='[IsDefault]']").val() == 1) {
                        _siblingsCheckedOption += 1;
                    }
                })
                if (_siblingsCheckedOption != 0) return false;
            }
            if (_visibleTrList.length) {
                let _checkedOption = _switchOption = 0,
                    _labelHtml = '<span class="attr_default">' + lang_obj.manage.products.default_option + '</span>';
                _visibleTrList.each((index, element) => {
                    if ($(element).find(".attr_checkbox input[name*='[IsDefault]']").val() == 1) {
                        _checkedOption += 1;
                    }
                    if (!$(element).attr('data-id')) {
                        _switchOption += 1;
                    }
                })
                if (_checkedOption == 0 && !IsDefautlSettings) {
                    let _firstTrParent = $(_visibleTrList[0]);
                    _firstTrParent.find(".attr_checkbox input[name*='[IsDefault]']").val(1);
                    _firstTrParent.find('.attr_name').append(_labelHtml);
                } else if (_checkedOption != 0 && IsDefautlSettings) {
                    _visibleTrList.each((index, element) => {
                        if ($(element).find(".attr_checkbox input[name*='[IsDefault]']").val() == 1 && $(element).find('.attr_name .attr_default').length == 0) {
                            $(element).find('.attr_name').append(_labelHtml);
                        }
                    })
                } else if (_switchOption && IsDefautlSettings) {
                    let _firstTrParent = $(_visibleTrList[0]);
                    _firstTrParent.find(".attr_checkbox input[name*='[IsDefault]']").val(1);
                    _firstTrParent.find('.attr_name').append(_labelHtml);
                }

            }
        },

        /**
         * 取消勾选默认属性选项
         */
        cancel_default_option: function () {
            $('#attribute_ext_box').on('click', '.attr_default', function () {
                $(this).parents('tr').find(".attr_checkbox input[name*='[IsDefault]']").val(0);
                $(this).remove();
            })
        },



        /**
         * 确保为所有选中的仓库创建DOM tbody
         * @param {Array} warehouseIds 仓库ID数组
         */
        ensureAllWarehouseTbodies: function(warehouseIds) {


            // 只在多规格模式下执行
            if (products_obj.data_init.attr_model !== 1) {
                return;
            }

            const defaultTbody = $('#AttrId_1 tbody[data-id="0"]');
            if (defaultTbody.length === 0) {
                return;
            }

            warehouseIds.forEach(function(warehouseId) {
                if (warehouseId === 0) return; // 跳过默认仓库

                let targetTbody = $(`#AttrId_1 tbody[data-id="${warehouseId}"]`);

                if (targetTbody.length === 0) {
                    // 创建新的tbody
                    const newTbody = defaultTbody.clone(true);
                    newTbody.attr('data-id', warehouseId);
                    newTbody.hide(); // 默认隐藏

                    // 更新表单字段名称
                    newTbody.find('input, select').each(function() {
                        const element = $(this);
                        const name = element.attr('name');
                        if (name && name.startsWith('variants[')) {
                            const newName = name.replace(/variants\[_0\]/, `variants[_${warehouseId}]`);
                            element.attr('name', newName);
                        }
                    });

                    defaultTbody.after(newTbody);

                    // 设置复制的数据到新创建的DOM中
                    products_obj.function_init.fillWarehouseTbodyData(warehouseId, newTbody);
                }
            });

        },

        /**
         * 填充仓库tbody的数据
         * @param {number} warehouseId 仓库ID
         * @param {jQuery} tbody 表体元素
         */
        fillWarehouseTbodyData: function(warehouseId, tbody) {
            // 获取该仓库的数据
            const warehouseData = products_obj.data_init.ext_attr_data[1][warehouseId];
            if (!warehouseData || warehouseData.length === 0) {
                return;
            }

            // 遍历每一行数据
            warehouseData.forEach(function(rowData, index) {
                const attrName = rowData.AttrName;
                const optionName = rowData.Data;

                // 获取保存的数据
                let savedData = rowData;
                if (products_obj.data_init.ext_save_data[warehouseId] &&
                    products_obj.data_init.ext_save_data[warehouseId][attrName] &&
                    products_obj.data_init.ext_save_data[warehouseId][attrName][optionName]) {
                    savedData = products_obj.data_init.ext_save_data[warehouseId][attrName][optionName];
                }

                // 找到对应的行
                const row = tbody.find('tr').eq(index);
                if (row.length === 0) {
                    return;
                }

                // 填充输入框数据
                const fields = ['SKU', 'OldPrice', 'Price', 'CostPrice', 'Stock', 'Weight'];
                fields.forEach(function(field) {
                    const input = row.find(`input[name*="[${field}]"]`);
                    if (input.length > 0 && savedData[field]) {
                        input.val(savedData[field]);
                    }
                });

                // 特殊处理WeightUnit下拉框
                if (savedData.WeightUnit) {
                    const weightUnitInput = row.find('input[name*="[WeightUnit]"]');
                    if (weightUnitInput.length > 0) {
                        weightUnitInput.val(savedData.WeightUnit);
                    }
                }

                // 特殊处理PriorityShippingOvId下拉框
                const prioritySelect = row.find('select[name*="[PriorityShippingOvId]"]');

                if (prioritySelect.length > 0 && savedData.PriorityShippingOvId) {
                    prioritySelect.val(savedData.PriorityShippingOvId);
                    // 触发change事件确保UI更新
                    prioritySelect.trigger('change');
                }
            });

        }
    },
    //************************************************* 函数事件 End *************************************************

    // 产品列表
    products_init: function () {
        // 翻译器加载
        frame_obj.translation_init();

        frame_obj.del_init($('#products .r_con_table')); // 删除提示
        frame_obj.select_all($('input[name=select_all]'), $('input[name=select]'), $('.table_menu_button').find('.del, .sold_in, .sold_out, .facebook, .batch_price')); // 批量操作
        frame_obj.del_bat($('.table_menu_button .del'), $('input[name=select]'), '/manage/products/products/delete-batch'); // 批量删除
        // 批量上架
        frame_obj.del_bat($('.table_menu_button .sold_in'), $('input[name=select]'), '', function (id_list) {
            global_obj.win_alert(lang_obj.global.sold_in_confirm, function () {
                $.get('/manage/products/products/sold-batch', {id: id_list, sold: 0}, function (data) {
                    if (data.ret == 1) {
                        window.location.reload();
                    }
                }, 'json');
            }, 'confirm');
            return false;
        }, lang_obj.global.dat_select);
        // 批量下架
        frame_obj.del_bat($('.table_menu_button .sold_out'), $('input[name=select]'), '', function (id_list) {
            global_obj.win_alert(lang_obj.global.sold_out_confirm, function () {
                $.get('/manage/products/products/sold-batch', {id: id_list, sold: 1}, function (data) {
                    if (data.ret == 1) {
                        window.location.reload();
                    }
                }, 'json');
            }, 'confirm');
            return false;
        }, lang_obj.global.dat_select);
        // 批量发布到Facebook店铺
        frame_obj.del_bat($('.table_menu_button .facebook_release'), $('input[name=select]'), '', function (id_list) {
            global_obj.win_alert(lang_obj.global.facebook_release_confirm, function () {
                facebook_store_obj.products_upload(id_list);
            }, 'confirm');
            return false;
        }, lang_obj.global.dat_select);
        // 批量从Facebook店铺删除
        frame_obj.del_bat($('.table_menu_button .facebook_delete'), $('input[name=select]'), '', function (id_list) {
            global_obj.win_alert(lang_obj.global.facebook_delete_confirm, function () {
                facebook_store_obj.products_delete(id_list);
            }, 'confirm');
            return false;
        }, lang_obj.global.dat_select);
        frame_obj.select_all($('.table_menu_button input[name=custom_all]'), $('.table_menu_button input[class=custom_list][disabled!=disabled]')); // 批量操作
        frame_obj.submit_form_init($('.table_menu_button .ico form'), './?m=products&a=products');
        $('#products .r_con_table .copy').click(function () {
            var $this = $(this);
            global_obj.win_alert(lang_obj.global.copy_confirm, function () {
                window.location = $this.attr('href');
            }, 'confirm');
            return false;
        });

        $('#products .inside_menu').insideMenu();

        // 批量修改价格
        $('.table_menu_button .batch_price').on('click', function () {
            var id_list = '';
            $('input[name=select]').each(function (index, element) {
                id_list += $(element).get(0).checked ? $(element).val() + '-' : '';
            });
            if (id_list) {
                id_list = id_list.substring(0, id_list.length - 1);
                window.location = '/Products/BatchPrice?list=' + id_list;
            } else {
                global_obj.win_alert(lang_obj.global.dat_select, function () {
                }, 'confirm');
            }
        });

        // 勾选事件：默认执行
        $('.inside_table .r_con_table input:checkbox:not(.null)').each(function () { //重新默认全部取消勾选
            if ($(this).is(':checked') === true) $(this).prev().click();
        });

        // 预览
        $('#products .r_con_table .btn_view').click(function () {
            var $Url = $(this).parents('tr').data('url');
            window.open($Url);
            return false;
        });

        // 搜索筛选
        frame_obj.filterRight({
            "onSubmit": function ($obj) {
                // 分类
                let cateId = $obj.find("input[name=cateId]").val();
                // 标签
                let tagId = [];
                $obj.find("input[name=\"products_tagsCurrent[]\"]").each(function (index, element) {
                    tagId[index] = $(element).val();
                });
                tagId = tagId.join(",");
                // 价格
                let minPrice = $.trim($obj.find("input[name=MinPrice]").val())
                let maxPrice = $.trim($obj.find("input[name=MaxPrice]").val())

                $('.search_box input[name=cateId]').val(cateId)
                $('.search_box input[name=tagId]').val(tagId)
                $('.search_box input[name=minPrice]').val(minPrice)
                $('.search_box input[name=maxPrice]').val(maxPrice)
            }
        });

        /********************************** 产品导出 (Start) *****************************/
        // 导出右边弹窗
        frame_obj.fixed_right($('.more_feat .export'), '.fixed_products_export');

        // 显示邮箱输入框
        var exportForm = $('#products_export_form')
        exportForm.find('.export_range .input_radio_box').click(function () {
            let count = $(this).parents('.item').data('count')
            if (count > shop_config.exportCount.product) {
                exportForm.find('.email_box').show().find('input').attr('notnull', '')
                $('#exportService').val('external')
            } else {
                exportForm.find('.email_box').hide().find('input').removeAttr('notnull')
                $('#exportService').val('website')
            }
        });

        frame_obj.submit_form_init(exportForm, '', function () {
            if (global_obj.check_form(exportForm.find('*[notnull]'), exportForm.find('*[format]'))) {
                return false;
            }

            let id_list = '',
                Type = exportForm.find('input[name=Type]:checked').val();
            if (Type == 0) {
                $('.r_con_table input[name=select]').each(function (index, element) {
                    id_list += $(element).get(0).checked ? $(element).val() + '-' : '';
                });
                if (id_list) {
                    id_list = id_list.substring(0, id_list.length - 1);
                    $('#id_list').val(id_list);
                } else {
                    global_obj.win_alert_auto_close(lang_obj.global.dat_select, 'fail', 1000, '8%');
                    return false;
                }
            }

            $('#fixed_right .btn_cancel').click()
            let exportService = $('#exportService').val()
            if (exportService == 'website') {
                global_obj.win_loading();
                frame_obj.circle_progress_bar({
                    percent: 0,
                    processingText: lang_obj.manage.global.export_loading
                });
            }
        }, '', function (data) {
            if (data.ret == 2) {
                global_obj.win_alert({
                    title: data.msg,
                    confirmBtn: lang_obj.global.ok2,
                });
            } else if (data.ret == 1) {
                let txt = lang_obj.manage.products.export_finish;
                txt = txt.replace('%num%', data.msg.count);
                frame_obj.circle_progress_bar({
                    "percent": 100,
                    "comfirmButton": 1,
                    "cancelButton": 1,
                    "completedText": txt,
                    "comfirmButtonText": lang_obj.global.download,
                    "cancelButtonText": lang_obj.global.cancel,
                });
                // 点击事件
                $('.win_alert .btn_progress_completed .btn_cancel').off('click').on('click', function () {
                    $('.win_alert .close').trigger('click');
                });
                $('.win_alert .btn_progress_completed .btn_confirm').off('click').on('click', function () {
                    window.location = '/manage/products/products/export-down?name=' + data.msg.name;
                    $('.win_alert .close').trigger('click');
                });
            } else if (data.ret == -1) {
                // 选择已勾选的产品导出，却没有勾选任何产品
                global_obj.win_alert_auto_close(lang_obj.global.dat_select, 'fail', 1000, '8%');
            } else if (data.ret == -2) {
                global_obj.win_alert_auto_close(data.msg, 'fail', 1000, '8%');
            } else if (data.ret == -3) {
                global_obj.win_alert({
                    title: data.msg,
                    confirmBtn: lang_obj.global.ok2,
                }, function () {
                    $('.more_feat .export').click()
                });
            }
        });
        /********************************** 产品导出 (End) *****************************/
        /********************************** 产品导入 (Start) *****************************/

        frame_obj.fixed_right($('.more_feat .import'), '.upload_products_box', () => {
            let $formObj = $('#upload_edit_form')
            let $step = $formObj.find('.step_box .current').attr('data-number') || 1
            if ($step == 1) {
                // 第一步
                $formObj.find('.btn_step_next').show()
            } else if ($step == 2) {
                // 第二步
                $formObj.find('.btn_step_prev, .btn_step_next').show()
            } else {
                // 第三步
            }
            $formObj.find('.btn_submit').prop('disabled', true)
        })

        $('#upload_edit_form')
            .on('click', '.btn_step_next', () => {
                // 下一步
                let $formObj = $('#upload_edit_form')
                let $step = $formObj.find('.step_box .current').attr('data-number') || 1
                if ($step == 1) {
                    // 跳到第二步
                    $formObj.find('.step_box .list[data-number="2"]').addClass('current').siblings().removeClass('current')
                    $formObj.find('.step_item[data-number="2"]').addClass('current').siblings().removeClass('current')
                    $formObj.find('.btn_step_prev, .btn_step_next').show()
                } else {
                    // 跳到第三步
                    $formObj.find('.step_box .list[data-number="3"]').addClass('current').siblings().removeClass('current')
                    $formObj.find('.step_item[data-number="3"]').addClass('current').siblings().removeClass('current')
                    $formObj.find('.btn_step_next').hide()
                    $formObj.find('.btn_submit').show()
                }
            })
            .on('click', '.btn_step_prev', () => {
                // 上一步
                let $formObj = $('#upload_edit_form')
                let $step = $formObj.find('.step_box .current').attr('data-number') || 1
                if ($step == 3) {
                    // 跳到第二步
                    $formObj.find('.step_box .list[data-number="2"]').addClass('current').siblings().removeClass('current')
                    $formObj.find('.step_item[data-number="2"]').addClass('current').siblings().removeClass('current')
                    $formObj.find('.btn_step_prev, .btn_step_next').show()
                    $formObj.find('.btn_submit').hide()
                } else if ($step == 2) {
                    // 跳到第一步
                    $formObj.find('.step_box .list[data-number="1"]').addClass('current').siblings().removeClass('current')
                    $formObj.find('.step_item[data-number="1"]').addClass('current').siblings().removeClass('current')
                    $formObj.find('.btn_step_prev').hide()
                }
            })
            .on('click', '.btn_upload_photo', () => {
                // 上传图片
                let url = ''
                let noMask = 1
                $.post('/manage/account/upload-ueeshop-url', '', function (result) {
                    if (result.ret == 1) {
                        url = result.msg.url
                        frame_obj.photo_upload_iframe_init(url, noMask)
                        $('#div_mask').css('z-index', 10002)
                    }
                }, 'json')
            })
            .on('click', '.btn_photo_gallery', () => {
                // 从图片库中选择
                frame_obj.photo_choice_init('', 'gallery')
                let $choiceObj = $('#photo_choice_edit_form')
                $choiceObj.find('.button').hide()
                $choiceObj.find('.t>h1').text(lang_obj.manage.photo.gallery)
                $('#div_mask').css('z-index', 10002)
            })

        $('#upload_edit_form').fileupload({
            url: '/api/ProductFileUpload/uploadFiles',
            acceptFileTypes: /^.*\/(csv|zip|vnd\.ms-excel)$/i, //csv xlsx xls
            // callback: function(filepath, count) {
            // 	$('#excel_path').val(filepath);
            // 	if (filepath) $("#upload_edit_form .btn_submit").removeAttr("disabled");
            // }
            done: function (e, data) {
                const response = data.result;
                const filePath = response?.filePath || '';
                const surplus = response?.surplus || '';
                const name = response?.name || '';
                if (!filePath) {
                    // 没有返回图片路径
                    return false;
                }
                //回显路径地址
                document.querySelector("#excel_path").value = filePath;
                if (filePath) $("#upload_edit_form .btn_submit").removeAttr("disabled");
            }
        });
        $('#upload_edit_form').fileupload(
            'option',
            'redirect',
            window.location.href.replace(/\/[^\/]*$/, '/cors/result.html?%s')
        );

        frame_obj.submit_form_init($('#upload_edit_form'), '', function () {
            let $obj = $("#upload_edit_form"),
                $number = parseInt($obj.find(".form_container input[name=Number]").val());
            $('#upload_edit_form').find('.form_container').hide();
            $('#upload_edit_form').find('#box_circle_container').show();
            if ($number == 0) {
                frame_obj.circle_progress_bar({
                    "percent": 0,
                    "processingText": lang_obj.manage.products.upload.import_loading
                });
            }
            return true;
        }, '', function (result) {
            if (result.ret == 1) {
                // 进度条
                frame_obj.circle_progress_bar({
                    "percent": 0,
                    "processingText": lang_obj.manage.products.upload.import_pending
                });

                $('#upload_edit_form').find('.container_close_tips').show()

                frame_obj.importMonitoring('/manage/products/products/import-check', {
                    "data": [
                        {
                            "name": result.msg.name,
                            "taskId": result.msg.taskId,
                            "id": result.msg.id
                        }
                    ],
                }, 5000);

            } else {
                global_obj.win_alert(result.msg);
                $('#upload_edit_form').find('.form_container').show();
                $('#upload_edit_form').find('.progress_container_right').hide();
                $("#upload_edit_form #box_circle_container").hide();
            }
        });

        $(".box_import_completed .btn_import_fail").click(function () {
            let url = $(this).attr("data-url");
            if (url) {
                window.open(url);
            }
            return false;
        });
        /********************************** 产品导入 (End) *****************************/

        /********************************** 指定分类批量改价 (Start) **********************************/
        frame_obj.fixed_right($('.more_feat .batch-price'), '.batch_price_by_cate', function () {
            //重置表单
            document.getElementById('batchPriceForm').reset()

            var form = $('#batchPriceForm')
            var
                priceTypeEle = form.find('.price_type'),
                calcMethodEle = form.find('.price_method'),
                priceTypeTxt = form.find('.price_type_text'),
                calcVal = form.find('.calc_value')
            ;

            frame_obj.check_amount(form)

            ////绑定事件监听
            //提交表单
            form.find('.btn_submit').click(function (e) {
                e.preventDefault()//阻止默认(表单提交)事件

                var errStack = []
                var formData = {}
                //表单验证
                form.serializeArray().forEach(function (row) {
                    formData[row.name] = row.value
                    switch (row.name) {
                        case 'cate_id':
                            if (row.value == '') errStack.push(row.name)
                            break
                        case 'calc_value':
                            var _v = parseFloat(row.value)
                            if (_v <= 0) errStack.push(row.name)
                            break
                    }
                })
                if (errStack.length > 0) {
                    var errItem = errStack.shift()
                    global_obj.win_alert_auto_close(form.find('label[rel="' + errItem + '"]').data('tip'), 'fail', 1000, '8%')
                    return
                }

                //锁定 - 禁用表单
                form.find('input').attr('disabled', true)
                form.find('select').attr('disabled', true)
                form.find('.price_method').attr('disabled', true)

                //提交
                $.ajax({
                    url: form.attr('action'),
                    type: 'POST',
                    async: true,
                    data: formData,
                    dataType: 'json',
                    success: function (res) {
                        if (res.ret == 1) {
                            //操作成功刷新当前页
                            location.reload()
                        }
                    },
                    error: function (res) {

                    },
                    complete: function () {
                        //解除锁定
                        form.find('input').removeAttr('disabled')
                        form.find('select').removeAttr('disabled')
                        form.find('.price_method').removeAttr('disabled')
                    }
                })
            })

            //监听事件
            //价格类型
            priceTypeEle.find('select[name="price_type"]').change(function () {
                var selected = $(this).find('option:selected')
                priceTypeTxt.text(selected.text())
            })
            //改价方式
            calcMethodEle.find('.input_radio_box').click(function () {
                var checked = $(this).find('input[type="radio"]').val()
                if (checked == 'percent') {
                    calcVal.find('.price').hide()
                    calcVal.find('.percent').show()
                    let price = parseFloat(calcVal.find('input').val());
                    price = isNaN(price) ? 0 : price;
                    calcVal.find('input').attr('decimal', 1).val(price.toFixed(1))
                } else {
                    calcVal.find('.price').show()
                    calcVal.find('.percent').hide()
                    calcVal.find('input').attr('decimal', 2)
                }
            })


            //默认选中第一项
            priceTypeEle.find('select[name="price_type"]').val('shop_price').change() //商城价
            calcMethodEle.find('.input_radio_box')[1].click() //价格

        });
        /********************************** 指定分类批量改价 (End) **********************************/

        /********************************** 批量处理标签 (Start) **********************************/
        frame_obj.fixed_right($('.bat_add_tags, .bat_del_tags'), '.box_products_tags_bat', '', function (obj) {
            // 添加标签
            let $PId = "", $Type = "insert";
            if (obj.hasClass('bat_del_tags')) $Type = 'delete';
            let id_list = '';
            $('.r_con_table input[name=select]').each(function (index, element) {
                id_list += $(element).get(0).checked ? $(element).val() + '-' : '';
            });
            if (id_list) {
                id_list = id_list.substring(0, id_list.length - 1);
                $PId = id_list;
            } else {
                global_obj.win_alert(lang_obj.global.dat_select);
                return false;
            }
            $('.box_products_tags_bat input[name=PId]').val($PId);
            $('.box_products_tags_bat input[name=Type]').val($Type);
            $.get('/manage/products/products/tags?Type=' + $Type, '', function (data) {
                $('.box_products_tags_bat .box_option_list').html(data.msg);
                $('.insert_delete').each(function () {
                    if ($(this).is('input')) {
                        $(this).val($(this).attr('data-' + $Type));
                    } else {
                        $(this).text($(this).attr('data-' + $Type));
                    }
                });
                // 多功能选项框
                frame_obj.box_option_list();
                frame_obj.box_option_button_choice();
            }, 'json');
        });

        frame_obj.submit_object_init($('#products_tags_edit_form'), '', '', '', function (data) {
            global_obj.win_alert_auto_close(data.msg, '', 400, '8%');
            setTimeout(function () {
                location.href = location.href;
            }, 400);
        });

        frame_obj.fixed_right($('.bat_del_tags'), '.box_products_tags_del_bat', '', function (obj) {
            // 删除标签
            let id_list = '';
            $('.r_con_table input[name=select]').each(function (index, element) {
                id_list += $(element).get(0).checked ? $(element).val() + '-' : '';
            });
            if (id_list) {
                id_list = id_list.substring(0, id_list.length - 1);
                $PId = id_list;
            } else {
                global_obj.win_alert(lang_obj.global.dat_select);
                return false;
            }
            $('.all_tags_search_form input[name=Keyword]').val('');
            $('.box_products_tags_del_bat input[name=PId]').val($PId);
            $.get('/manage/products/products/tags-del-list', '', function (data) {
                $('.box_products_tags_del_bat .all_tags_list').html(data.msg);
            }, 'json');
        });

        frame_obj.submit_form_init($('.all_tags_search_form'), '', '', '', function (data) {
            $('.box_products_tags_del_bat .all_tags_list').html(data.msg);
        });

        frame_obj.submit_form_init($('#products_tags_del_form'), '', '', '', function (data) {
            global_obj.win_alert_auto_close(data.msg, '', 400, '8%');
            setTimeout(function () {
                location.href = location.href;
            }, 400);
        });
        /********************************** 批量处理标签 (End) **********************************/

        products_obj.copy_box_init();

    },

    importData: {
        "failData": [],
        "tipsData": [],
        "successCount": 0,
        "failCount": 0,
        "excelFile": "",
        "completedFun": () => {
            $('#fixed_right_div_mask, .upload_products_box .close').click(() => {
                //硬性绑定页面刷新事件
                let Url = '/manage/products/products'
                let State = {title: '', url: Url}
                window.history.replaceState(State, '', Url)
                window.location.reload()
                return false
            });
            frame_obj.circle_progress_bar({"percent": 100});
            $("#upload_edit_form .box_import_completed").show();
            $("#upload_edit_form #box_circle_container").hide();

            let successCount = products_obj.importData.successCount
            if (successCount) {
                $(".box_import_completed .item_done").show().find(".item_title>span").text(successCount)
            }

            let failCount = products_obj.importData.failCount
            if (failCount) {
                $(".box_import_completed .item_fail").show().find(".item_title>span").text(failCount)
                $(".box_import_completed .fail_list").show()
            }

            let post = {
                "excelFile": products_obj.importData.excelFile,
                "data": products_obj.importData.failData,
                "tips": products_obj.importData.tipsData
            };
            $.post("/manage/products/products/import-fail-file", post, function (result) {
                if (result.ret == 1) {
                    $(".box_import_completed .btn_import_fail").attr("data-url", result.msg.downloadUrl);
                    if (typeof result.msg.tips !== undefined) {
                        $(result.msg.tips).each(function (index, element) {
                            $(".box_import_completed .item_fail .list_content").append('<div class="list_item">' + element.positionY + element.positionX + '，' + element.text + '</div>');
                        });
                    }
                }
            }, "json");
        }
    },

    //批量修改价格
    batch_price_init: function () {
        frame_obj.check_amount($('.r_con_table'));
        $('.r_con_table .mod_price_input').keyup(function () {
            var $save = 0;
            $('.r_con_table .mod_price_input').each(function () {
                if ($(this).val() != '') $save++;
            });
            if ($save) {
                $('.row_save_box').removeClass('none');
                $(window).resize();
            } else {
                $('.row_save_box').addClass('none');
                $(window).resize();
            }
        });
        frame_obj.submit_object_init($('#edit_form'), location.href);
    },

    //产品编辑详情
    products_edit_init: function () {
        // 翻译器加载
        frame_obj.translation_init();

        // 预览
        $('.product_menu .btn_menu_view').click(function () {
            var $Url = $(this).parents('.product_menu').data('url');
            window.open($Url);
        });

        // 我的应用(下拉)
        $('.product_menu_item').hover(function () {
            $(this).find('.box_my_app').show().stop(true).animate({'top': 35, 'opacity': 1}, 250);
        }, function () {
            $(this).find('.box_my_app').stop(true).animate({'top': 25, 'opacity': 0}, 100, function () {
                $(this).hide();
            });
        });

        // 切换产品
        let $proId = $("#ProId").val();
        frame_obj.global_switch("/manage/products/products/switch?id=" + $proId + (product_data.switch_url ? product_data.switch_url : ''));

        // 更多选项(下拉)
        $('.box_basic_more').hover(function () {
            var $Top = 42;
            $(this).hasClass('box_seo_basic_more') && ($Top = 20);
            $(this).find('.drop_down').show().stop(true).animate({'top': $Top, 'opacity': 1}, 250);
        }, function () {
            var $Top = 32;
            $(this).hasClass('box_seo_basic_more') && ($Top = 10);
            $(this).find('.drop_down').stop(true).animate({'top': $Top, 'opacity': 0}, 100, function () {
                $(this).hide();
            });
        });

        // 图片上传
        products_obj.function_init.main_picture_upload();
        $('#PicDetail.multi_img input[name=PicPath\\[\\]]').each(function () {
            if ($(this).attr('save') == 1) {
                $(this).parent().find('img').attr('src', $(this).attr('data-value')); //直接替换成缩略图
            }
        });
        // 上传按钮
        $('.global_container[data-name=pic_info] .pro_multi_img').on('click', '.img .upload_box .upload_btn', function () {
            $('.upload_file_box input[type=file]').trigger('click');
        });
        // 拖动上传
        $('.upload_file_box').on('fileuploadstart', function () {
            $(this).append('<div class="loading"></div>').removeClass('dragenter static');
            $('.show_btn').append('<div class="loading"></div>');
        });
        $('.upload_file_box').on('fileuploadstop', function () {
            $(this).children('.loading').remove();
        });
        $('.upload_file_box, #PicDetail').on('dragenter', function () {
            let picNum = $('.global_container[data-name=pic_info] .pro_multi_img .img').length - 1;
            if (picNum <= 0) $('.upload_file_box').addClass('static');
            $('.upload_file_box').addClass('dragenter');
        });
        $('.upload_file_box').on('dragleave', function () {
            $(this).removeClass('dragenter');
        });
        $(window).on('dragend', function (e) {
            e.preventDefault();
            $('.upload_file_box').trigger('dragleave');
        });
        // 图片上传
        $('.upload_file_box').fileupload({
            // url: '/manage/action/file-upload?size=products',
            url: '/api/ProductFileUpload/uploadImages',
            acceptFileTypes: /^image\/(gif|jpe?g|png|webp|x-icon|jp2)$/i,
            disableImageResize: false,
            imageMaxWidth: 99999,
            imageMaxHeight: 99999,
            imageForceResize: false,
            maxFileSize: 10485760,
            maxHeight: 5000,
            messages: {
                maxFileSize: lang_obj.manage.photo.size_limit,
                acceptFileTypes: lang_obj.manage.photo.type_error,
                maxHeight: lang_obj.manage.photo.height_limit
            },
            dropZone: $('.upload_file_box'),
            progress: function (e, data) {
                let imgName = data.files[0].name
                if (typeof products_obj.data_init.local_upload_img[imgName] === 'undefined') {
                    let num = products_obj.function_init.pre_put_prod_img(imgName)
                    products_obj.data_init.local_upload_img[imgName] = num
                }
            },
            done: function (event, data) {
                if (typeof data.result == 'undefined') return
                $('#PicDetail').find(".loading").remove()
                
                // 处理不同的API返回格式
                let imgpath, imgName;
                
                // 检查是否是files数组格式
                if (data.result.files && data.result.files.length > 0) {
                    imgpath = data.result.files[0].url || data.result.files[0].thumbnailUrl;
                    imgName = data.result.files[0].fileName || data.result.files[0].name;
                } 
                // 兼容现有的filePath格式
                else if (data.result.filePath) {
                    imgpath = data.result.filePath;
                    imgName = data.result.name;
                }
                
                if ($(".view_more_image").length) {
                    $(".view_more_image").trigger('click')
                }
                
                let num = products_obj.data_init.local_upload_img[imgName]
                products_obj.function_init.update_prod_img(num, imgpath)
                delete products_obj.data_init.local_upload_img[imgName]
            },
            processfail: function (e, data) {
                $('.upload_file_box').trigger('dragleave');
                let files = data.files;
                let error = "";
                $.each(files, function (index, element) {
                    if (typeof element.error !== "undefined") {
                        error += element.error;
                    }
                });
                error == "" && (error = lang_obj.manage.photo.type_error);
                global_obj.win_alert_auto_close(error, 'await', 1000, '8%', 0);
            }
        }).on('fileuploadadd', function (e, data) {
            $.fn.fileUploadAdd(data);
        });

        // 显示余下图片
        products_obj.function_init.view_more_image();
        $(".global_container").on("click", ".view_more_image", function () {
            if (products_obj.data_init.image_data.more) {
                $.each(products_obj.data_init.image_data.more, function (index, element) {
                    products_obj.function_init.put_prod_img(element, products_obj.data_init.image_data.moreAlt[index]);
                    if (products_obj.data_init.image_data.videoItem && products_obj.data_init.image_data.videoItem == element) {
                        let _imgLogo = '?video';
                        if (element.indexOf('x-oss-process=video') != -1) {
                            _imgLogo = '&video';
                        }
                        $(".global_container[data-name=pic_info] .pro_multi_img .img.isfile:last").addClass("video").find("input[type=hidden]").val(element + _imgLogo).parents('.video').find('.alt_edit').hide().siblings('.video_seo_btn').show();
                        products_obj.products_video_init();
                    }
                });
            }
            $(this).remove();
        });

        // 产品主图勾选效果
        $('.global_container[data-name="pic_info"]').delegate('.input_checkbox_box, .btn_checkbox', 'click', function () {
            let $checked = 0
            let $mainObj = $('.global_container[data-name="pic_info"]')
            let $listObj = $mainObj.find('.pro_multi_img')
            let $checkedCount = $listObj.find('.input_checkbox_box.checked').length
            let $count = $listObj.find('.input_checkbox_box').length
            if ($count == 0) {
                return false;
            }
            if ($(this).hasClass('btn_checkbox')) {
                // 全选
                if ($(this).hasClass('current')) {
                    // 勾选
                    $checked = 1;
                    $checkedCount += 1;
                } else {
                    // 取消勾选
                    $checkedCount -= 1;
                }
                if ($('.view_more_image').length) {
                    $('.view_more_image').trigger('click')
                }
                if ($checked == 1) {
                    $listObj.find('.img.isfile').each(function (index, element) {
                        $(element).addClass('isshow')
                        $(element).find('.input_checkbox_box').addClass('checked').find('input').attr('checked', true)
                    })
                } else {
                    $listObj.find('.img.isfile').each(function (index, element) {
                        $(element).removeClass('isshow')
                        $(element).find('.input_checkbox_box').removeClass('checked').find('input').attr('checked', false)
                    })
                }
                $checkedCount = $listObj.find(".input_checkbox_box.checked").length;
                if ($checked == 1) {
                    $mainObj.find(".upload_select_menu .btn_checkbox").removeClass("indeterminate");
                }
            } else {
                // 部分勾选
                if ($(this).hasClass("checked")) {
                    // 取消勾选
                    $checkedCount -= 1;
                    $(this).parents('.img.isfile').removeClass('isshow')
                } else {
                    // 勾选
                    $checked = 1;
                    $checkedCount += 1;
                    $(this).parents('.img.isfile').addClass('isshow')
                }
                if ($checkedCount == $count) {
                    // 全选
                    $mainObj.find('.upload_select_menu .btn_checkbox').removeClass('indeterminate').addClass('current').find('input').attr('checked', true);
                } else if ($checkedCount == 0) {
                    // 没有勾选
                    $mainObj.find('.upload_select_menu .btn_checkbox').removeClass('current indeterminate').find('input').attr('checked', false);
                } else {
                    $mainObj.find('.upload_select_menu .btn_checkbox').removeClass('current').addClass('indeterminate').find('input').attr('checked', false);
                }
            }
            if ($checkedCount > 0) {
                $('.upload_select_menu').addClass('current')
                $('.upload_select_menu .open').removeClass('no_select').text(`已选择${$checkedCount}个`)
            } else {
                $('.upload_select_menu').removeClass('current')
                $('.upload_select_menu .open').addClass('no_select').text('全选')
            }
        }).delegate('.batch_delete_pictrue', 'click', function () {
            // 批量删除产品主图
            let $listObj = $(".global_container[data-name=pic_info] .pro_multi_img .input_checkbox_box.checked")
            if ($listObj.length) {
                let params = {
                    'title': lang_obj.global.del_confirm,
                    'confirmBtn': lang_obj.global.del,
                    'confirmBtnClass': 'btn_warn'
                };
                global_obj.win_alert(params, function () {
                    // 删除图片
                    $listObj.each(function () {
                        $(this).parents('.img').remove()
                    })
                    frame_obj.upload_pro_img_init(1)
                    let $i = 0
                    $('.global_container[data-name=pic_info] .pro_multi_img').find('.img').each(function () {
                        $(this).attr('num', $i)
                        ++$i
                    })
                    if ($('.view_more_image').length) {
                        $('.view_more_image').trigger('click')
                    }
                    products_obj.function_init.show_btn_init()
                    products_obj.function_init.read_all_image_data()
                    // 最后去掉全选
                    $('.upload_select_menu .btn_checkbox').removeClass('current indeterminate').find('input').attr('checked', false)
                    $('.upload_select_menu').removeClass('current')
                    $('.upload_select_menu .open').addClass('no_select').text('全选')
                    // 提示成功
                    global_obj.win_alert_auto_close(lang_obj.manage.global.del_success, '', 1000, '8%')
                }, 'confirm')
            } else {
                global_obj.win_alert(lang_obj.global.del_dat_select)
            }
            return false
        })

        // 视频事件
        frame_obj.fixed_right($('.upload_menu li[data-type=video]'), '.fixed_video', function () {
            $('.fixed_video .box_textarea').removeAttr('style');
            let videoObj = $('#PicDetail .img.video');
            let moreObj = $('.view_more_image');
            let _VideoType = $('#VideoType').val();
            if (videoObj.length) {  // 编辑
                if (_VideoType == 'local') {
                    let _obj = $('.fixed_video .multi_file'),
                        _filepath = $('#VideoUrl').val();
                    _obj.find('.file_btn .zoom').attr('href', _filepath);
                    _obj.find('.preview_file a').remove();
                    frame_obj.generate_file_cover(_obj, _filepath);//生成封面图
                    _obj.find('.preview_file').children('input[type=hidden][name=FilePath]').val(_filepath).attr('save', 1).trigger('change');
                }
                //封面图
                let videoImage = videoObj.find('.preview_pic input[type=hidden]').val();
                $('.fixed_video .multi_img .img').addClass('isfile').find('.preview_pic input[type=hidden]').val(videoImage);
                $('.fixed_video .multi_img .img .zoom').attr('href', videoImage);
                let fixedVideoObj = $('.fixed_video .multi_img .preview_pic input:hidden');
                if (!fixedVideoObj.next('a').length) {
                    fixedVideoObj.parent().append(frame_obj.upload_img_detail(fixedVideoObj.val())).children('.upload_btn').hide();
                }
            } else if (moreObj.length) {
                let videoImage = "";
                moreObj.find("input[type=hidden]").each(function (index, element) {
                    if ($(element).val().indexOf("?video") || $(element).val().indexOf("&video")) {
                        videoImage = $(element).val();
                    }
                });
                if (_VideoType == 'local') {
                    let _obj = $('.fixed_video .multi_file'),
                        _filepath = $('#VideoUrl').val();
                    _obj.find('.file_btn .zoom').attr('href', _filepath);
                    _obj.find('.preview_file a').remove();
                    frame_obj.generate_file_cover(_obj, _filepath);//生成封面图
                    _obj.find('.preview_file').children('input[type=hidden][name=FilePath]').val(_filepath).attr('save', 1).trigger('change');
                }
                if (videoImage) {
                    $('.fixed_video .multi_img .img').addClass('isfile').find('.preview_pic input[type=hidden]').val(videoImage);
                    $('.fixed_video .multi_img .img .zoom').attr('href', videoImage);
                    let fixedVideoObj = $('.fixed_video .multi_img .preview_pic input:hidden');
                    if (!fixedVideoObj.next('a').length) {
                        fixedVideoObj.parent().append(frame_obj.upload_img_detail(fixedVideoObj.val())).children('.upload_btn').hide();
                    }
                }
            } else {  // 添加
                $('.fixed_video .box_type_menu .item').eq(0).click();
                $('.fixed_video .box_textarea').val('');
            }
        });

        // 视频数据提交
        $('#video_form').submit(function () {
            return false
        })
        $('#video_form').on('click', '.btn_submit', function () {
            let $formObj = $('#video_form')
            if (global_obj.check_form($formObj.find('*[notnull]'))) return false;
            let imgVaule = $formObj.find('input[name=PicPath]').val();
            let _videoTypeObj = $('#video_form .video_source .box_type_menu').find('.item.checked');
            let _videoCheckVal = _videoTypeObj.find('input[name=VideoType]').val();
            if (_videoCheckVal == 'local') {
                let _thirdVideoPath = $('#video_form input[name=FilePath]').val();
                if (!_thirdVideoPath) {
                    global_obj.win_alert_auto_close(lang_obj.manage.file.no_file_video, 'await', 1000, '8%')
                    return false;
                }
                var videoUrl = $formObj.find('input[name=FilePath]').val();
                if (!imgVaule) {
                    imgVaule = $formObj.find('#localVideo .preview_file a img').attr('src');
                }
            } else {
                if (!imgVaule) {
                    global_obj.win_alert_auto_close(lang_obj.manage.photo.no_picture, 'await', 1000, '8%')
                    return false;
                }
                var videoUrl = $formObj.find('textarea[name=VideoUrl]').val();
            }
            $('#VideoUrl').val(videoUrl);
            $('#VideoType').val(_videoCheckVal);
            let videoObj = $('#PicDetail .img.video');
            if (videoObj.length) {  // 编辑
                videoObj.find('.preview_pic input[type=hidden]').val(imgVaule);
                videoObj.find('.preview_pic a img').attr('src', imgVaule);
                videoObj.find('.pic_btn .icon_multi_view').parent().attr('href', imgVaule);
            } else {  // 添加
                products_obj.function_init.put_prod_img(imgVaule);
            }
            products_obj.data_init.image_data.videoItem = imgVaule;
            let _imgValue = imgVaule + '?video';
            if (imgVaule.indexOf('x-oss-process=video/snapshot') != -1) {
            // if (imgVaule.indexOf('x-oss-process=video') != -1) {
                _imgValue = imgVaule + '&video';
            }
            $('#PicDetail .preview_pic').find("input[value='" + imgVaule + "']").parents('dl.img').find('.pic_btn .alt_edit').remove();
            $('#PicDetail .preview_pic').find("input[value='" + imgVaule + "']").val(_imgValue).parents('dl.img').addClass('video');
            if (_videoCheckVal == 'third') {
                $('#PicDetail .preview_pic').find("input[value='" + _imgValue + "']").parents('dl.img').find('.video_seo_btn').show();
            } else {
                $('#PicDetail .preview_pic').find("input[value='" + _imgValue + "']").parents('dl.img').find('.video_seo_btn').hide();
            }
            $('#fixed_right .btn_cancel').click();
            return false;
        });

        //填写的视频为youtube的话 就是请求对应的图片。
        let _doing = true;
        $('#video_form textarea[name=VideoUrl]').keyup(function () {
            let _this = $(this),
                _value = _this.val(),
                _PicValue = $('#video_form input[name=PicPath]').val();
            $IsIFrame = _value.indexOf('iframe') ? true : false;
            if ($IsIFrame) {
                _value = global_obj.htmlspecialchars_decode(_value);
                let reg = /src=["\'](.*?)["\']/;
                let Ary = _value.match(reg);
                if (Ary && Ary[1]) {
                    _value = Ary[1];
                }
            }
            var regExp = /^.*((youtu.be\/)|(v\/)|(\/u\/\w\/)|(embed\/)|(watch\?)|(live\/)|(shorts\/))\??v?=?([^#\&\?]*).*/;
            var match = _value.match(regExp);
            _return = (match && match.length == 11) ? match[7] : false;
            if (_return == false) {
                if (match && match.length == 9) {
                    _return = match[8] ? match[8] : false;
                } else if (match && match.length == 10) {
                    _return = match[9] ? match[9] : false;
                }
            }
            setTimeout(() => {
                //是否能获取到youtube视频的ID
                if (_return && !_PicValue && _doing) {
                    _doing = false;
                    $('#fixed_right .fixed_video .loading_mask').show();
                    $.post('/manage/products/products/curl-youtube-pic', {id: _return}, function (result) {
                        if (result) {
                            _doing = true;
                            if (result.ret == 1) {
                                let obj = $('#Video');
                                obj.find('.pic_btn .zoom').attr('href', result.msg);
                                obj.find('.preview_pic a').remove();
                                obj.find('.preview_pic').append(frame_obj.upload_img_detail(result.msg)).children('.upload_btn').hide().parent().parent().addClass('isfile').removeClass('show_btn');
                                obj.find('.preview_pic').children('input[type=hidden]').val(result.msg).attr('save', 1).trigger('change');
                            }
                            $('#fixed_right .fixed_video .loading_mask').hide();
                        }
                    }, 'json')
                }
            }, 1000);
        })

        $('body').on('click', '#fixed_right .fixed_video .multi_img .img .upload_btn', function () {
            if ($('#photo_choice_edit_form').length > 0) {
                $('#fixed_right').css('z-index', '-1');
                $('#photo_choice_edit_form h2 , #photo_choice_edit_form .btn_global').on('click', function () {
                    $('#fixed_right').css('z-index', '10001');
                })
            }
        })

        // 视频图片上传
        frame_obj.mouse_click($('#Video .upload_btn, #Video .pic_btn .edit'), 'video', function ($this) { //产品主图点击事件
            frame_obj.photo_choice_init('Video', '', 1, '', 1, "$('#fixed_right',window.parent.document).css('z-index','10001')");
        });

        $('body').on('click', '#fixed_right .fixed_video .multi_file .file .upload_btn', function () {
            if ($('#file_choice_edit_form').length > 0) {
                $('#fixed_right').css('z-index', '-1');
                $('#file_choice_edit_form h2 , #file_choice_edit_form .btn_global').on('click', function () {
                    $('#fixed_right').css('z-index', '10001');
                })
            }
        })
        // 本地视频上传
        frame_obj.mouse_click($('#localVideo .upload_btn, #localVideo .pic_btn .edit'), 'localVideo', function ($this) {
            frame_obj.file_choice_init('localVideo', '', 1, 'mp4', '', 1, "$('#fixed_right',window.parent.document).css('z-index','10001')");
        });

        // 本地视频删除点击事件
        frame_obj.mouse_click($('.multi_file .file_btn .del'), 'Del', function ($this) {
            var $obj = $this.parents('.file');
            $obj.removeClass('isfile').removeClass('show_btn').parent().append($obj);
            $obj.find('.file_btn .zoom').attr('href', 'javascript:;');
            $obj.find('.preview_file .upload_btn').show();
            $obj.find('.preview_file a').remove();
            $obj.find('.preview_file input:hidden').val('').attr('save', 0).trigger('change');
        });


        //******************************** 销售信息 Start ********************************
        if ($('#products_inside .price_box').length) {
            $('#products_inside .price_box').each(function () {
                var $Input = $(this).find('.unit_input'),
                    $Width = $(this).outerWidth(true),
                    $b_w = $Input.find('b').length ? $Input.find('b')[0].getBoundingClientRect().width : 0,
                    $i_w = 0;
                $i_w = $Input.find('.box_input').outerWidth(true) - $Input.find('.box_input').outerWidth();
                $Input.find('.box_input').css('width', $Width - $b_w - $i_w - 22);
            });
        }
        //******************************** 销售信息 End ********************************

        // 简短介绍字长限制
        products_obj.function_init.check_char_length($(".box_briefdescription .box_textarea"), 500);

        // 勾选按钮
        frame_obj.switchery_checkbox(function (obj) {
            if (obj.find('input[name=SoldOut]').length) {
                // 上下架
                $('#sold_out_div').css('display', 'none');
            } else if (obj.find('input[name=IsFreeShipping]').length) {
                // 免运费
                $('.box_volume_weight').css('display', 'none');
                $('.cubage_box').fadeOut();
            } else if (obj.find('input[name=IsColor]').length) {
                // 设置为主图
                var $Form = $('#edit_picture_form');
                $Form.find('.edit_picture_list .item').each(function () {
                    $(this).find('.img[num!=0]').show();
                });
            } else if (obj.find('input[name=batch_checked]').length) {
                // 批量处理选项开关(开启)
                obj.parents(".batch_item").find(".batch_item_overlay").removeClass("show");
            } else if (obj.find('input[name=needLogistics]').length) {
                // 是否需要物流
                $('.logistics_box').fadeIn();
            } else if (obj.find('input[name=UsedMobile]').length) {
                // 是否开启移动端详细描述
                $('.mobile_description').fadeIn();
            }
        }, function (obj) {
            if (obj.find('input[name=SoldOut]').length) {
                // 上下架
                $('#sold_out_div').css('display', '');
            } else if (obj.find('input[name=IsFreeShipping]').length) {
                // 免运费
                $('.box_volume_weight').css('display', 'inline-block');
                if ($('.box_volume_weight .input_checkbox_box').hasClass('checked')) {
                    $('.cubage_box').fadeIn();
                } else {
                    $('.cubage_box').fadeOut();
                }
            } else if (obj.find('input[name=IsColor]').length) {
                // 设置为主图
                var $Form = $('#edit_picture_form');
                $Form.find('.edit_picture_list .item').each(function () {
                    $(this).find('.img[num!=0]').hide();
                });
            } else if (obj.find('input[name=batch_checked]').length) {
                // 批量处理选项开关(关闭)
                obj.parents(".batch_item").find(".switchery, .input_checkbox_box").removeClass("checked").find("input").removeAttr("checked");
                obj.parents(".batch_item").find(".attr_picture").removeClass("saved").removeAttr("style").next().val("");
                obj.parents(".batch_item").find(".box_input").val("");
                obj.parents(".batch_item").find(".batch_item_overlay").addClass("show");
                products_obj.data_init.batch_save_data.PicPath = ""; //清空
            } else if (obj.find('input[name=needLogistics]').length) {
                // 是否需要物流
                $('.logistics_box').fadeOut();
            } else if (obj.find('input[name=UsedMobile]').length) {
                // 是否开启移动端详细描述
                $('.mobile_description').fadeOut();
            }
        });

        $(".specification_container .input_radio_box").click(function () {
            // 规格模式
            let value = parseInt($(this).find("input").val());
            products_obj.data_init.specification = value;
            if (value == 1) {
                // 多规格
                // 先验证组合数量限制
                let $count = products_obj.function_init.attr_combination_count()
                if ($count > products_obj.config_init.option_max) {
                    global_obj.win_alert_auto_close(lang_obj.manage.products.tips.option_max.replace("{{max}}", products_obj.config_init.option_max), 'fail', 1000, '8%')
                    return false
                }

                $(".attr_container .big_title").hide()
                $(".box_attribute_tab_menu").hide()
                $(".box_attribute_tab_menu").find(".item").show()
                $(".box_attribute_tab_menu").find(".item[data-type='info'], .item[data-type='basic']").hide()
                $("#box_main_image").show()

                $('#attribute_unit_box').hide()
                $('.box_attribute, .box_combination, #add_combination, .option_filter').show()
                if ($(".spec_no_data:visible").length) {
                    $(".box_warehouse").hide()
                } else {
                    $(".box_warehouse").show()
                }
                $('.warehouse_filter').removeClass('readonly')

                if (parseInt($('#edit_form input[name=ProId]').val()) > 0) {
                    // 产品编辑
                    $('.box_attribute_tab_menu').show()
                    let item = $('.box_attribute_tab_menu .item.checked')
                    if (item.data("type") == "info" || item.data("type") == "basic") {
                        // 不能停留在"规格信息"或者"基础设置"上
                        $('.box_attribute_tab_menu .item:eq(2)').click()
                    } else {
                        $('.box_attribute_tab_menu .item.checked').click()
                    }
                }
                $('#AttrId_0').hide().removeClass("show").find('input').prop('disabled', true)
                $('#AttrId_1').show().addClass("show").find('input').prop('disabled', false)
                products_obj.data_init.attr_model = 1
                products_obj.function_init.attr_price_show()
            } else if (value == 2) {
                // 多规格加价
                $(".attr_container .big_title").hide()
                $(".box_attribute_tab_menu").hide()
                $(".box_attribute_tab_menu .item").show()
                $(".box_attribute_tab_menu .item[data-type='info']").hide()
                $("#box_main_image").show()

                $('#attribute_unit_box').show()
                $('#attribute_unit_box input[name=Stock]').removeAttr('notnull')
                $("#AttrId_1 input[data-type=\"price\"]").attr('notnull', 'notnull')
                $('.box_attribute, .warehouse_filter').show()
                $(".box_combination, #add_combination, .option_filter").hide()
                $('.warehouse_filter').addClass('readonly')

                if (parseInt($('#edit_form input[name=ProId]').val()) > 0) {
                    // 产品编辑
                    $('.box_attribute_tab_menu').show()
                    let item = $('.box_attribute_tab_menu .item.checked')
                    if (item.data("type") == "info") {
                        // 不能停留在"规格信息"上
                        $('.box_attribute_tab_menu .item:eq(1)').click()
                    } else {
                        $('.box_attribute_tab_menu .item.checked').click()
                    }
                }
                $('#AttrId_0').show().addClass("show").find('input').prop('disabled', false)
                $('#AttrId_1').hide().removeClass("show").find('input').prop('disabled', true)
                products_obj.data_init.attr_model = 0
                products_obj.function_init.attr_price_show()
            } else {
                // 单规格
                $(".attr_container .big_title, #attribute_unit_box").show()
                $("#box_attribute_tab_menu, .box_attribute, .option_filter").hide()
                $('#attribute_unit_box input[name=Stock]').attr('notnull', 'notnull')
                $("#AttrId_1 input[data-type=\"price\"]").removeAttr("notnull").removeClass("null")
                $('.box_attribute, .box_combination, #attribute_ext_box, .box_warehouse, #box_main_image').hide()
                $('.warehouse_filter').removeClass('readonly')

                $(".box_attribute_tab_menu .item[data-type='info']").click()
            }
            // 批量处理 默认隐藏，去掉勾选状态
            $(".attribute_ext thead tr").removeClass("current");
            $(".attribute_ext thead .global_menu_button .open").addClass('no_select');
            $(".attribute_ext thead .btn_checkbox.current").click();
            $(".box_cart_attribute").attr("data-type", value);
            $(".attr_container").attr("data-type", value);
        });

        $('.box_volume_weight .input_checkbox_box').click(function () {
            var $Obj = $(this);
            if ($Obj.hasClass('checked')) {
                $('.cubage_box').fadeOut();
            } else {
                $('.cubage_box').fadeIn();
            }
        });

        // 多规格属性模式(开启)
        if ($("#IsCombination").val() > 0) {
            let $model = $("#IsCombination").val();
            $(".specification_container .input_radio_box:eq(" + $model + ")").click();
        } else {
            $(".specification_container .input_radio_box:eq(0)").click();
        }

        $('.box_attribute_tab_menu .item').click(function () {
            var $Type = $(this).data('type');
            $(this).addClass('checked').siblings().removeClass('checked');
            if ($Type == 'info') {
                // 基本信息
                if ($.isEmptyObject(products_obj.data_init.warehouse_name_data) === false) {
                    // 有仓库
                    $("#attribute_unit_box, #attribute_ext_box, .box_warehouse, .box_attribute_tab_menu").show();
                    $(".attr_container .big_title, .box_attribute, .box_combination, .box_multi_tab, .option_filter, .spec_no_data").hide();
                    $(".box_attribute_tab_menu .item[data-type='info']").show().siblings().hide();
                    let $id = $(".warehouse_filter .btn_warehouse.current").attr("data-id");
                    if ($("#attribute_unit_box tbody tr:visible").attr("data-id") != $id) {
                        // 恢复显示指定数据
                        $("#attribute_unit_box tr[data-id=" + $id + "]").show().siblings().hide();
                    }
                } else {
                    // 没有仓库
                    $(".attr_container .big_title, #attribute_unit_box").show();
                    $("#attribute_ext_box, .box_attribute_tab_menu, .box_combination").hide();
                }
            } else if ($Type == 'basic') {
                // 基础设置
                $("#attribute_unit_box").show();
                $('#attribute_ext_box, .box_combination, .box_attribute, #box_main_image').hide();
            } else if ($Type == 'price') {
                // 属性价格管理
                $('#attribute_ext_box, .box_combination').show();
                $('#attribute_unit_box, .box_attribute, #box_main_image').hide();
            } else if ($Type == 'option') {
                // 属性选项管理
                $('#attribute_unit_box, #attribute_ext_box, .box_combination, #box_main_image').hide();
                $('.box_attribute').show();
            } else {
                // 关联主图
                $('#attribute_unit_box, #attribute_ext_box, .box_combination, .box_attribute').hide();
                $('#box_main_image').show();
            }
        });
        if (parseInt($('#edit_form input[name=ProId]').val()) > 0) {
            let type = products_obj.data_init.specification;
            if (type == 1) {
                // 多规格
                $('.box_attribute_tab_menu .item:eq(2)').click();
            } else if (type == 2) {
                // 多规格加价
                $('.box_attribute_tab_menu .item:eq(1)').click();
            } else {
                // 单规格
                $('.box_attribute_tab_menu .item:eq(0)').click();
            }
        }

        // 规格筛选
        $('.option_filter .inside_body').on('click', 'li>a', function () {
            let $this = $(this)
            let $filterObj = $('.option_filter')
            let $show = ($('#AttrId_1').hasClass('show') ? 1 : 0)
            let $tbodyObj = $(`#AttrId_ ${$show} tbody:visible`)
            // 高亮
            $this.parent().siblings().find('a').removeClass('current')
            if ($this.hasClass('current')) {
                $this.removeClass('current')
            } else {
                $this.addClass('current')
            }
            // 全部取消勾选
            $tbodyObj.find('.input_checkbox_box').each((index, element) => {
                $(element).removeClass('checked').find('input').attr('checked', false)
            })
            // 获取已勾选的选项
            let optionFind = ''
            let optionAry = []
            $filterObj.find('a.current').each((index, element) => {
                let $number = $(element).data('number')
                let $option = $(element).data('option')
                optionFind += `[data-attr-${$number}="${$.quotationMarksTransferred($option)}"]`
                optionAry.push('<b>' + $(element).data('option') + '</b>')
            })
            // 勾选相应的规格
            if (optionFind) {
                $filterObj.addClass('lock') // 锁住(筛选选项不能变动)
                $tbodyObj.find(optionFind + ' .input_checkbox_box').trigger('click')
                $tbodyObj.find('tr').hide()
                $tbodyObj.find(optionFind).removeAttr('style')
                $filterObj.find('.inside_title>span').html(optionAry.join('<i>, </i>')) // 显示选中名字
                $filterObj.removeClass('lock') // 解锁
            } else {
                let $extObj = $('.attribute_ext')
                $extObj.find('thead .btn_checkbox').removeClass('current indeterminate')
                $extObj.find('thead tr').removeClass('current')
                $extObj.find('thead .global_menu_button .open').addClass('no_select')
                $tbodyObj.find('tr').removeAttr('style')
                $filterObj.find('.inside_title>span').html(lang_obj.manage.products.choose_specifications)
            }
            return false
        }).on('click', '.clear_option_filter', function () {
            let $filterObj = $('.option_filter')
            let $show = ($('#AttrId_1').hasClass('show') ? 1 : 0)
            let $tbodyObj = $(`#AttrId_ ${$show} tbody:visible`)
            let $extObj = $('.attribute_ext')
            // 全部取消勾选
            $tbodyObj.find('.input_checkbox_box').each((index, element) => {
                $(element).removeClass('checked').find('input').attr('checked', false)
            })
            // 清除所有
            $extObj.find('thead .btn_checkbox').removeClass('current indeterminate')
            $extObj.find('thead tr').removeClass('current')
            $extObj.find('thead .global_menu_button .open').addClass('no_select')
            $tbodyObj.find('tr').removeAttr('style')
            $filterObj.find('a.current').removeClass('current')
            $filterObj.find('.inside_title>span').html(lang_obj.manage.products.choose_specifications)
        })

        // 仓库信息
        $(".warehouse_filter").on("mouseenter", function () {
            let $this = $(this)
            let $count = $this.find(".inside_body li").not(".hide").length
            if ($count == 0 || $this.hasClass('readonly')) {
                $(this).find(".inside_body").addClass("hide")
            } else {
                $(this).find(".inside_body").removeClass("hide")
            }
            return false
        });
        $(".box_warehouse .btn_warehouse").click(function () {
            let $id = $(this).data("id");
            $(this).addClass("current");
            $(this).parent().siblings().find(".btn_warehouse").removeClass("current");
            $(".warehouse_filter .inside_title>span").html($(this).text());
            products_obj.data_init.warehouse_id = $id;
            // 多规格
            $("#AttrId_1 tbody[data-id=\"" + $id + "\"]").show().siblings("tbody").hide();
            products_obj.function_init.input_show_notnull();
            // 批量处理 默认隐藏，去掉勾选状态
            $(".attribute_ext thead .btn_checkbox.current").click(); // 全选
            $(".attribute_ext thead .btn_checkbox.indeterminate").click().click(); // 部分勾选
            // 单规格
            $("#attribute_unit_box tbody tr[data-id=\"" + $id + "\"]").show().siblings("tr").hide();
            products_obj.function_init.check_default_option(products_obj.data_init.defautl_settings);
        });
        if ($(".box_warehouse .btn_warehouse").length > 0) {
            $(".box_warehouse .btn_warehouse").each(function (index, element) {
                products_obj.data_init.warehouse_data[index] = $(element).data("id");
                products_obj.data_init.warehouse_name_data[$(element).data("id")] = $(element).text();
            });
            
            let showCount = $(".warehouse_filter li[data-type='warehouse']:not('.hide')").length;
            if (showCount > 0) {
                // 默认选择第一个仓库选项
                $(".box_warehouse li[data-type='warehouse']:not('.hide')>.btn_warehouse:eq(0)").click();
            } else {
                $(".box_warehouse .btn_warehouse:eq(0)").click();
            }
        } else {
            products_obj.data_init.warehouse_data[0] = 0;
        }
        $(".box_warehouse .inside_menu").addClass("inside_menu_mini");
        $(window).resize(function () {
            $(".box_warehouse .inside_menu").addClass("inside_menu_mini");
        });

        // 仓库设置
        frame_obj.fixed_right($('.btn_warehouse_set'), '.fixed_warehouse_set', function ($this) {
            // 添加属性
            let $obj = $('.fixed_warehouse_set');
            // 清空内容
            /*
			$obj.find('.box_input').val('');
			$obj.find(".rows[data-type=\"type\"]").show();
			$obj.find(".rows[data-type=\"type\"] .input_radio_box:eq(0)").click();
			products_obj.function_init.prohibit_input($Obj.find("input[name=AttrName]"));
			*/
        });

        // 状态选项
        $('select[name=SoldOut]').change(function () {
            var $value = $(this).val();
            if ($value == 1) {
                // 下架
                $('#sold_out_div').show();
                $('#arrival_notice_div').hide();
            } else if ($value == 2) {
                // 脱销
                $('#sold_out_div').hide();
                $('#arrival_notice_div').show();
            } else {
                // 上架
                $('#sold_out_div, #arrival_notice_div').hide();
            }
        });

        $('.right_container .big_title').on('click', function () {
            var $This = $(this);
            if ($This.find('i').length) {
                if ($This.next().is(':hidden')) {
                    $This.find('i').addClass('current');
                    $This.next().slideDown();
                } else {
                    $This.find('i').removeClass('current');
                    $This.next().slideUp();
                }
            }
            $(window).resize();
        });
        $(window).resize(function () {
            frame_obj.multi_lang_show_all('#edit_form');
        });
        // 多语言事件
        frame_obj.multi_lang_show_all('#edit_form');
        frame_obj.multi_lang_show_item('#edit_form');

        //处理智能分类
        $('.classify_rows .btn_attr_choice.disabled').append('<div class="button_tips opacity">' + lang_obj.manage.products.del_intel + '</div>');

        //******************************** SEO Start ********************************
        //SEO编辑
        $('#edit_seo_list').click(function () {
            var $This = $(this),
                $Seo = $('.seo_box'),
                $SeoInfo = $('.seo_info_box'),
                $PageUrl = $Seo.find('.box_textarea[name=PageUrl]'),
                $Ary = new Array('title', 'description', 'keyword', 'url'),
                $Obj = '', $Val = '', $i = 0, $j = 0;
            if ($This.attr('data-save') == 0) { //编辑
                $This.text(lang_obj.global.pack_up).attr('data-save', 1);
                $SeoInfo.hide();
                $Seo.show();
            } else { //收起
                $SeoInfo.show();
                $Seo.hide();
                $This.text(lang_obj.global.edit).attr('data-save', 0);
                for (i in $Ary) { //注入内容
                    $Obj = $Seo.find('div[data-name=' + $Ary[i] + ']').find('input:eq(0), textarea:eq(0)');
                    $Val = $.trim($Obj.val());
                    if ($Ary[i] == 'url') { //自定义地址
                        $Val = $Val.replace($Obj.attr('data-domain'), '').replace('.html', '');
                        $Val = $Obj.attr('data-domain') + $Val;
                    }
                    if ($Ary[i] == 'keyword') { //关键词
                        $Val = '';
                        $Obj = $Seo.find('div[data-name=' + $Ary[i] + '] .option_selected input[name=keysName\\[\\]]');
                        $Obj.each(function () {
                            $Val += ($j ? ',' : '') + $(this).val();
                            ++$j;
                        });
                    }
                    $SeoInfo.find('.' + $Ary[i]).text($Val);
                    if ($Val == '') {
                        $SeoInfo.find('.' + $Ary[i]).hide();
                    } else {
                        ++$i;
                        $SeoInfo.find('.' + $Ary[i]).show();
                    }
                }
                if ($i > 0) {
                    $SeoInfo.find('.blank20').show();
                } else {
                    $SeoInfo.find('.blank20').hide();
                }
            }
            frame_obj.seo_url_show();
        });

        //SEO相关事件
        $("[name=Name_en]").on("keyup", function () {
            var $Value = $.trim($(this).val());
            $(".seo_info_box").show();
            if (!parseInt($('#edit_form input[name=ProId]').val())) {
                $(".seo_info_box .title").text($Value);
                $(".seo_box").find('input[name=SeoTitle_en]').val($Value);
            }
            if ($Value) {
                $(".seo_info_box .title").show();
            } else {
                $(".seo_info_box .title").hide();
            }
        });

        const _seoVal = $(".seo_box").find('textarea[name=SeoDescription_en]').val();
        $("[name=BriefDescription_en]").on("keyup", function () {
            var $Value = $.trim($(this).val());
            if (_seoVal == '') {
                $(".seo_info_box").show();
                $(".seo_info_box .description").text($Value);
                $(".seo_box").find('textarea[name=SeoDescription_en]').val($Value);
                if ($Value) {
                    $(".seo_info_box .description").show();
                } else {
                    $(".seo_info_box .description").hide();
                }
            }
        });

        $('[name=PageUrl]').on('keyup', function (e) {
            var $Key = window.event ? e.keyCode : e.which,
                $Value = $.trim($(this).val());
            if ($Key == 8 && $Value == '') {
                //退格键 (不允许为空)
                $(this).val($('.global_container[data-name=basic_info] .multi_lang:eq(0) .lang_txt:eq(0) input').val().replace(/\s+/g, '-'));
            }
        });

        // 复制链接
        var clipboard = new ClipboardJS('.seo_info_box .btn_copy');
        clipboard.on('success', function (e) {
            alert(lang_obj.global.copy_complete);
        });

        //修改 SEO 关键词
        frame_obj.fixed_right($('#edit_keyword'), '.fixed_edit_keyword', function ($this) {
            var $ProId = $('#ProId').val(),
                $AddProId = $('#AddProId').val();
            frame_obj.seo_edit_keyword({
                'do_action': '/manage/action/seo-keyword-select',
                'Type': 'products',
                'ProId': $ProId,
                'AddProId': $AddProId
            });
        });

        frame_obj.seo_keyword_form_submit();
        //******************************** SEO End ********************************


        //******************************** 属性事件 Start ********************************
        frame_obj.fixed_right($('.box_basic_more .add_attr, #add_attribute, #add_attribute_oth'), '.fixed_add_attribute', function ($this) {
            // 添加属性
            let $Obj = $('.fixed_add_attribute');
            // 清空内容
            $Obj.find('.box_input').val('');
            $Obj.find(".rows[data-type=\"type\"]").show();
            $Obj.find(".rows[data-type=\"type\"] .input_radio_box:eq(0)").click();
            products_obj.function_init.prohibit_input($Obj.find("input[name=AttrName]"));
        });

        frame_obj.fixed_right($("#myorder_attribute"), ".fixed_edit_attribute", function ($this) {
            // 属性排序
            var $Obj = $(".fixed_edit_attribute"),
                $Title = "", $Position = 0, $Html = "", $Count = 0;
            $Obj.find(".edit_attr_list").html("");
            $Count = $(".box_cart_attribute .box_button_choice").length;
            if ($Count > 0) {
                $Html += '<div class="rows">';
                $Html += '<label>' + lang_obj.manage.global.name + '</label>';
                $Html += '<div class="input">';
                $(".box_cart_attribute .box_button_choice").each(function (index, element) {
                    $Title = $.trim($(this).find(".attr_title").val());
                    $Position = parseInt($(this).data("position"));
                    $Html += '<div class="item clean" data-id="' + $(this).attr('data-id') + '" data-position="' + $Position + '">';
                    $Html += '<span class="myorder"><span class="icon_myorder"></span></span>';
                    $Html += '<strong>' + $Title + '</strong>';
                    $Html += '<input type="hidden" value="' + global_obj.htmlspecialchars($Title) + '" />';
                    $Html += '</div>';
                });
                $Html += '</div>';
                $Html += '</div>';
            }
            if ($Html) {
                $Obj.find('.edit_attr_list').html($Html);
                $Obj.find('.bg_no_table_data').hide();
                frame_obj.dragsort($Obj.find('.edit_attr_list .input'), '', '', '', '<div class="item placeHolder"></div>');
            } else {
                $Obj.find('.bg_no_table_data').show();
            }
        });

        frame_obj.fixed_right($("#add_combination"), ".fixed_add_combination", function ($this) {
            // 添加规格
            var $Obj = $(".fixed_add_combination"),
                $Title = "", $Html = "";
            $Obj.find(".add_content").html("");
            $(".box_cart_attribute .box_button_choice").each(function (index, element) {
                $Title = $.trim($(this).find(".attr_title").val());
                $Html += '<div class="rows">';
                $Html += '<label>' + $Title + '</label>';
                $Html += '<div class="input">';
                $Html += '<div class="item clean">';
                $Html += '<input type="text" class="box_input fl" value="" size="30" maxlength="255" autocomplete="off" data-position="' + (parseInt(index) + 1) + '" notnull />';
                $Html += '</div>';
                $Html += '</div>';
                $Html += '</div>';
            });
            if ($Html) {
                $Obj.find('.add_content').html($Html);
                products_obj.function_init.prohibit_input($Obj.find('.add_content input.box_input'));
                $Obj.find('.bg_no_table_data').hide();
            } else {
                $Obj.find('.bg_no_table_data').show();
            }
        });

        frame_obj.fixed_right($('.batch_edit'), '.fixed_batch_edit', function ($this) {
            // 批量处理(产品规格)
            let $Obj = $('.fixed_batch_edit'),
                $number = ($("#AttrId_1").hasClass("show") ? 1 : 0),
                $extObj = $("#AttrId_" + $number);
            $checkedObj = $extObj.find(".input_checkbox_box.checked"),
                $checkedCount = $checkedObj.length;
            $Obj.find(".batch_item").each(function (index, element) {
                $(element).find(".switchery, .input_checkbox_box").removeClass("checked").find("input").removeAttr("checked");
                $(element).find(".attr_picture").removeClass("saved").removeAttr("style").next().val("");
                $(element).find(".box_input").val("");
                $(element).find(".batch_item_overlay").addClass("show");
                products_obj.data_init.batch_save_data = {}; // 清空
            });
            if ($checkedCount > 0) {
                $Obj.find('.bg_no_table_data').hide();
            } else {
                $Obj.find('.bg_no_table_data').show();
            }
            $Obj.find(".batch_model[data-number=" + $number + "]").show().siblings(".batch_model").hide();

            $.each($Obj.find(".batch_model[data-number=" + $number + "]").find('.box_type_menu'), function (k, v) {
                v = $(v);
                v.find('.item').eq(0).click();
            })
            $.each($Obj.find(".batch_model[data-number=" + $number + "]").find('.item_adjustment'), function (k, v) {
                v = $(v);
                v.find('input').val('');
                v.find('select').find('option').eq(0).prop('selected', true);
                v.find('b').eq(0).show().siblings('.last').hide();
            })

            $Obj.find(".batch_model[data-number=" + $number + "]").find('.item_adjustment').find('input[name="amount"]').val('');

            frame_obj.check_amount($Obj); // 检查价格
        }, function () {
            let $number = ($("#AttrId_1").hasClass("show") ? 1 : 0),
                $extObj = $("#AttrId_" + $number);
            $checkedObj = $extObj.find(".input_checkbox_box.checked"),
                $checkedCount = $checkedObj.length;
            if ($checkedCount == 0) {
                global_obj.win_alert(lang_obj.global.dat_select);
                return false;
            }
        });

        $('.fixed_batch_edit').on('click', '.box_type_menu .item', function () {
            let _value = $(this).find('input').val(),
                _parentObj = $(this).parents('.batch_item');
            _parentObj.find('.item_' + _value).show().siblings().hide();
        })
        $('.fixed_batch_edit').on('change', 'select', function () {
            _Value = $(this).val(),
                _Obj = $(this).parents('.item_content');
            if (_Value == 'increase' || _Value == 'reduce') {
                _Obj.find('b.symbol').show().siblings('b').hide();
            } else {
                _Obj.find('b.per').show().siblings('b').hide();
            }
        })

        products_obj.function_init.attr_load(); // 加载产品属性
        window.setTimeout(function () {
            // 延时加载
            if ($('#sync_product_hidden').length) return false; // 针对同步产品编辑
            products_obj.function_init.attr_category_select(); // 默认加载
        }, 1000);
        $(document).click(function (e) {
            // 属性框去掉着色
            if ($(e.target).attr('class') != 'attr_selected selected_focus') {
                $('.attr_selected').removeClass('selected_focus').next('.attr_not_yet').slideUp();
            }
            products_obj.function_init.clean_combination_picture(e);
        });

        var $PicPosition = $("#PicDetail").offset().top;
        $(".box_cart_attribute").delegate(".btn_attribute_delete", "click", function () {
            // 删除规格属性
            let $Obj = $(this).parents(".box_button_choice"),
                $Position = parseInt($Obj.data("position")),
                $number = $Position - 1,
                $index = 0,
                $autoAry = {},
                $extAry = {};
            $Obj.remove();
            products_obj.data_init.attr_data.splice($number, 1);
            // 重新组合规格搭配 (多规格)
            $index = 0;
            let $count = products_obj.function_init.attr_combination_count();
            if ($count <= products_obj.config_init.option_max) {
                $autoAry = products_obj.function_init.auto_combination();
                for (let k in products_obj.data_init.warehouse_data) {
                    let id = products_obj.data_init.warehouse_data[k];
                    $index = 0;
                    for (let i in $autoAry) {
                        for (let j in $autoAry[i]) {
                            let $attrName = i,
                                $title = $autoAry[i][j],
                                $optionName = global_obj.json_decode_data($title);
                            if (typeof ($extAry[id]) == 'undefined') {
                                $extAry[id] = [];
                            }
                            $extAry[id][$index] = {
                                "AttrName": $attrName,
                                "Combination": $optionName,
                                "Data": $optionName,
                                "Title": $title.join(" / "),
                                "ProId": "",
                                "OldPrice": "",
                                "Price": "",
                                "CostPrice": "",
                                "SKU": "",
                                "Stock": "",
                                "VariantsId": "",
                                "Weight": "",
                                "WeightUnit": "kg",
                                "PicPath": ""
                            };
                            if (typeof (products_obj.data_init.ext_save_data[id]) == 'undefined') {
                                // 记录到保存数据(仓库)
                                products_obj.data_init.ext_save_data[id] = {};
                            }
                            if (typeof (products_obj.data_init.ext_save_data[id][$attrName]) == 'undefined') {
                                // 记录到保存数据(属性)
                                products_obj.data_init.ext_save_data[id][$attrName] = {};
                            }
                            if (typeof (products_obj.data_init.ext_save_data[id][$attrName][$optionName]) == 'undefined') {
                                // 记录到保存数据(选项)
                                products_obj.data_init.ext_save_data[id][$attrName][$optionName] = $extAry[id][$index];
                            }
                            ++$index;
                        }
                    }
                }
                products_obj.data_init.ext_attr_data[1] = $extAry;
            }
            // 重新组合规格搭配 (多规格加价)
            $index = 0;
            $extAry = [];
            $autoAry = products_obj.function_init.auto_combination("separate");
            for (let i in $autoAry) {
                for (let j in $autoAry[i]) {
                    let $attrName = i,
                        $title = $autoAry[i][j],
                        $optionName = global_obj.json_decode_data($title);
                    $extAry[$index] = {
                        "AttrName": $attrName,
                        "Combination": $optionName,
                        "Data": $optionName,
                        "Title": $title[0],
                        "ProId": "",
                        "OldPrice": "",
                        "Price": "",
                        "CostPrice": "",
                        "SKU": "",
                        "Stock": "",
                        "VariantsId": "",
                        "Weight": "",
                        "WeightUnit": "kg",
                        "PicPath": ""
                    };
                    if (typeof (products_obj.data_init.ext_save_data[$attrName]) == 'undefined') {
                        // 记录到保存数据(属性)
                        products_obj.data_init.ext_save_data[$attrName] = {};
                    }
                    if (typeof (products_obj.data_init.ext_save_data[$attrName][$optionName]) == 'undefined') {
                        // 记录到保存数据(选项)
                        products_obj.data_init.ext_save_data[$attrName][$optionName] = $extAry[$index];
                    }
                    ++$index;
                }
            }
            for (let k in products_obj.data_init.warehouse_data) {
                let id = products_obj.data_init.warehouse_data[k];
                if (id > 0) {
                    let $attrName = global_obj.json_decode_data(["Ship From"]);
                    $title = products_obj.data_init.warehouse_name_data[id],
                        $optionName = global_obj.json_decode_data([$title]);
                    $extAry[$index] = {
                        "AttrName": $attrName,
                        "Combination": $optionName,
                        "Data": $optionName,
                        "Title": $title,
                        "ProId": "",
                        "OldPrice": "",
                        "Price": "",
                        "CostPrice": "",
                        "SKU": "",
                        "Stock": "",
                        "VariantsId": "",
                        "Weight": "",
                        "WeightUnit": "kg",
                        "PicPath": ""
                    };
                    if (typeof (products_obj.data_init.ext_save_data[$attrName]) == 'undefined') {
                        // 记录到保存数据(属性)
                        products_obj.data_init.ext_save_data[$attrName] = {};
                    }
                    if (typeof (products_obj.data_init.ext_save_data[$attrName][$optionName]) == 'undefined') {
                        // 记录到保存数据(选项)
                        products_obj.data_init.ext_save_data[$attrName][$optionName] = $extAry[$index];
                    } else {
                        $extAry[$index] = products_obj.data_init.ext_save_data[$attrName][$optionName];
                    }
                    ++$index;
                }
            }
            products_obj.data_init.ext_attr_data[0] = $extAry;
            // 重新整理顺序
            let $AttrSaveAry = [], $Title = "";
            $(".box_cart_attribute .box_button_choice").each(function (index, element) {
                $Title = $(this).find("strong").text();
                $AttrSaveAry[$Title] = $(this).html();
            });
            $(".box_cart_attribute .box_button_choice").remove(); // 先清空
            $(products_obj.data_init.attr_data).each(function (index, element) {
                let position = parseInt(index) + 1;
                let re = new RegExp("AttrOption\\[" + element.Position + "\\]", "g");
                $(".box_cart_attribute").append('<div class="box_button_choice clean" data-id="' + element.AttrId + '" data-position="' + position + '" data-attr="' + global_obj.htmlspecialchars(element.Name) + '" data-type="' + element.Type + '">' + $AttrSaveAry[element.Name].replace(re, "AttrOption[" + position + "]") + '</div>');
                products_obj.data_init.attr_data[index].Position = position;
            });
            products_obj.function_init.attr_load();
            products_obj.function_init.attr_price_show();
        })

        $('#attribute_ext_box').delegate('.attr_picture', 'click', function () {
            // 选项组合图片上传
            let $ProId = parseInt($('#ProId').val()),
                $obj = $(this).parents(".group"),
                $position = parseInt($obj.data('position')),
                $attrName = global_obj.json_decode_data($obj.data("attr")),
                $extend = global_obj.json_decode_data($obj.data("extend")),
                $wid = parseInt($obj.data("wid")),
                $Top = $(this).position().top,
                $Left = $(this).position().left + 50;
            products_obj.function_init.combination_picture({
                "Type": "extend",
                "Position": $position,
                "AttrName": $attrName,
                "Extend": $extend,
                "Wid": $wid,
                "Top": $Top,
                "Left": $Left
            });
            return false;
        }).delegate(".btn_delete", "click", function () {
            // 删除选项组合 (已废弃)
            /*
			var $Obj = $(this).parents("tr.group"),
				$Position = $Obj.data("position"),
				$extend = $Obj.data('extend'), //删掉第一个字符
				$Table = $Obj.parent(),
				$Count = 0, $OptionParent = [], $OptionObj = [], $OptionIndex = "";
			if ($extend) {
				$extend = $extend.split("_");
				$($extend).each(function(index, element) {
					//检查是否影响相关的选项数据
					$Count = $(".attribute_ext .group[data-attr-" + index + "=\"" + $.quotationMarksTransferred(element) + "\"]").length;
					if ($Count == 1) {
						//该选项的最后一个选项组合，自动删掉该选项
						var $Value = element;
						$(products_obj.data_init.attr_data[index].Options).each(function(index, element) {
							if ($Value == element) {
								$OptionIndex = index;
							}
						});
						$OptionObj = $(".box_cart_attribute .box_attr:eq(" + index + ") .attr_current:eq(" + $OptionIndex + ")").parent();
						$OptionParent = $OptionObj.parents(".attr_selected");
						$OptionObj.remove();
						$OptionObj = $(".box_cart_attribute .box_attr:eq(" + index + ")"); //重新定义
						if ($OptionObj.find(".btn_attr_choice").length < 1) {
							//没有选项，显示placeholder
							$OptionParent.find(".placeholder").removeClass("hide");
						}
						products_obj.data_init.attr_data[index].Options.splice($OptionIndex, 1);
					}
				});
			}
			$(this).parents("tr.group").remove();
			products_obj.data_init.ext_attr_data[1].splice($Position, 1);
			$Table.find(".group").each(function(index, element) {
				$(this).attr("data-position", index);
			});
			*/
        }).delegate(".box_input", "change", function () {
            // 文本改动
            let $obj = $(this).parents(".group"),
                $attrName = global_obj.json_decode_data($obj.data("attr")),
                $extend = global_obj.json_decode_data($obj.data("extend")),
                $wid = parseInt($obj.data("wid")),
                $data = [];
            $obj.find('.box_input').each(function () {
                $data[$(this).data('type')] = $(this).val();
            });
            if (products_obj.data_init.attr_model == 1) {
                // 多规格
                products_obj.data_init.ext_save_data[$wid][$attrName][$extend].SKU = $data.sku;
                products_obj.data_init.ext_save_data[$wid][$attrName][$extend].OldPrice = $data.oldprice;
                products_obj.data_init.ext_save_data[$wid][$attrName][$extend].Price = $data.price;
                products_obj.data_init.ext_save_data[$wid][$attrName][$extend].CostPrice = $data.cost_price;
                products_obj.data_init.ext_save_data[$wid][$attrName][$extend].Stock = $data.stock;
                products_obj.data_init.ext_save_data[$wid][$attrName][$extend].Weight = $data.weight;
            } else {
                // 多规格加价
                products_obj.data_init.ext_save_data[$attrName][$extend].SKU = $data.sku;
                products_obj.data_init.ext_save_data[$attrName][$extend].OldPrice = $data.oldprice;
                products_obj.data_init.ext_save_data[$attrName][$extend].Price = $data.price;
                products_obj.data_init.ext_save_data[$attrName][$extend].CostPrice = $data.cost_price;
                products_obj.data_init.ext_save_data[$attrName][$extend].Stock = $data.stock;
                products_obj.data_init.ext_save_data[$attrName][$extend].Weight = $data.weight;
            }
        }).delegate(".popover_img", "click", function () {
            // 选中图片
            let $Img = $(this).attr("style"),
                $attrName = $(this).parents(".box_popover").data("attr"),
                $sAttrName = global_obj.json_decode_data($attrName),
                $htmlAttrName = $.quotationMarksTransferred($sAttrName),
                $extend = $(this).parents(".box_popover").data("extend"),
                $sExtend = global_obj.json_decode_data($extend),
                $htmlExtend = $.quotationMarksTransferred($sExtend),
                $wid = parseInt($(this).parents(".box_popover").data("wid")),
                $ImgSrc = $Img.replace("background-image:url('", "").replace("');", "");
            if (!$(this).hasClass("popover_img_selected")) {
                // 未选中
                $(this).addClass("popover_img_selected");
                $(".attribute_ext .group[data-wid=\"" + $wid + "\"][data-attr=\"" + $htmlAttrName + "\"][data-extend=\"" + $htmlExtend + "\"] .attr_picture").attr("style", $Img).addClass("saved").next().val($ImgSrc);
                products_obj.data_init.ext_save_data[$wid][$sAttrName][$sExtend].PicPath = $ImgSrc;
            } else {
                // 已选中
                $(this).removeClass("popover_img_selected");
                $(".attribute_ext .group[data-wid=\"" + $wid + "\"][data-attr=\"" + $htmlAttrName + "\"][data-extend=\"" + $htmlExtend + "\"] .attr_picture").removeAttr("style").removeClass("saved").next().val("");
                products_obj.data_init.ext_save_data[$wid][$sAttrName][$sExtend].PicPath = "";
            }
        }).delegate("#btn_picture_upload", "click", function () {
            // 请先上传图片
            // $("body, html").animate({scrollTop:500}, 500);
        }).delegate(".input_checkbox_box, .btn_checkbox", "click", function () {
            // 批量勾选
            var $Checked = 0,
                $mainObj = $(this).parents(".attribute_ext"),
                $tbodyObj = $mainObj.find("tbody:visible"),
                $tbodyAllObj = $mainObj.find("tbody"),
                $CheckedCount = $tbodyObj.find(".input_checkbox_box.checked").length,
                $Count = $tbodyObj.find(".input_checkbox_box").length;
            if ($Count == 0) {
                return false;
            }
            if ($(this).hasClass("btn_checkbox")) {
                if ($(this).hasClass("current")) {
                    // 勾选
                    $Checked = 1;
                    $CheckedCount += 1;
                } else {
                    // 取消勾选
                    $CheckedCount -= 1;
                }
            } else {
                if ($(this).hasClass("checked")) {
                    // 取消勾选
                    $CheckedCount -= 1;
                } else {
                    // 勾选
                    $Checked = 1;
                    $CheckedCount += 1;
                }
            }
            if ($(this).parents("thead").length) {
                // 全选
                if ($Checked == 1) {
                    $tbodyObj.find(".input_checkbox_box").each(function (index, element) {
                        $(element).addClass("checked").find("input").attr("checked", true);
                    });
                } else {
                    $tbodyAllObj.find(".input_checkbox_box").each(function (index, element) {
                        $(element).removeClass("checked").find("input").attr("checked", false);
                    });
                }
                $CheckedCount = $tbodyObj.find(".input_checkbox_box.checked").length;
                $tr = $(this).parents('table').find('.prod_select:first').parents('tr');
                if ($Checked == 1) {
                    $mainObj.find("thead .btn_checkbox").removeClass("indeterminate");
                }
            } else {
                // 部分勾选
                if ($CheckedCount == $Count) {
                    // 全选
                    $mainObj.find("thead .btn_checkbox").removeClass("indeterminate").addClass("current").find("input").attr("checked", true);
                } else if ($CheckedCount == 0) {
                    // 没有勾选
                    $mainObj.find("thead .btn_checkbox").removeClass("current indeterminate").find("input").attr("checked", false);
                } else {
                    $mainObj.find("thead .btn_checkbox").removeClass("current").addClass("indeterminate").find("input").attr("checked", false);
                }
            }
            if ($CheckedCount > 0) {
                $(".attribute_ext thead tr").addClass("current");
                $(".attribute_ext thead .global_menu_button .open").removeClass('no_select');
                $(".attribute_ext thead .global_menu_button .open>span").text($CheckedCount);
            } else {
                $(".attribute_ext thead tr").removeClass("current");
                $(".attribute_ext thead .global_menu_button .open").addClass('no_select');
            }
            // 规格筛选 (归元)
            let $filterObj = $('.option_filter')
            if (!$filterObj.hasClass('lock')) {
                // 没锁住
                $filterObj.find('.inside_title>span').html(lang_obj.manage.products.choose_specifications)
                $filterObj.find('a.current').removeClass('current')
                let $show = ($('#AttrId_1').hasClass('show') ? 1 : 0)
                let $tbodyObj = $(`#AttrId_ ${$show} tbody:visible`)
                $tbodyObj.find('tr').removeAttr('style')
            }
        }).delegate(".global_menu_button .open", "click", function () {
            let $obj = $(".attribute_ext thead tr");
            if ($obj.hasClass("current")) {
                $obj.removeClass("current");
                $(this).removeClass('no_select');
            } else {
                $obj.addClass("current");
                $(this).addClass('no_select');
            }
        });

        $('.batch_delete').click(function () {
            // 批量删除产品规格
            let $listObj = $("#AttrId_1 tbody:visible .input_checkbox_box.checked");
            if ($listObj.length) {
                let params = {
                    'title': lang_obj.global.del_confirm,
                    'confirmBtn': lang_obj.global.del,
                    'confirmBtnClass': 'btn_warn'
                };
                global_obj.win_alert(params, function () {
                    products_obj.function_init.batch_delete_combination($listObj);
                    // 最后去掉全选
                    $("#attribute_ext_box thead .btn_checkbox").removeClass("current indeterminate").find("input").attr("checked", false);
                    $(".attribute_ext thead tr").removeClass("current");
                    $(".attribute_ext thead .global_menu_button .open").addClass('no_select');
                    // 提示成功
                    global_obj.win_alert_auto_close(lang_obj.manage.global.del_success, '', 1000, '8%');
                }, 'confirm');
            } else {
                global_obj.win_alert(lang_obj.global.del_dat_select);
            }
            return false;
        });

        /*** 设置为默认选项 **/
        $('#attribute_ext_box').on('click', '.set_default', () => {
            let _listObj = $("#AttrId_1 tbody:visible .input_checkbox_box.checked"),
                _listLength = _listObj.length;
            if (_listLength == 0) { // 没选择
                global_obj.win_alert(lang_obj.manage.products.please_select);
            } else if (_listLength > 0) { //多选
                _listObj = _listObj.last();
                // 无论任何时候，选择了多少个属性，都默认将最后勾选的规格选项设置为默认选项
                let _parentId = _listObj.parents('tr').attr('data-id'),
                    _attrDefaultId = 0;
                _parentId = _parentId ? _parentId : -1;
                $('#AttrId_1 tbody tr').each(function () {
                    if ($(this).find('.attr_name .attr_default').length) {
                        _attrDefaultId = $(this).attr('data-id');
                    }
                })
                if (_parentId == _attrDefaultId) return false; //当选中已经是默认的选项不做提示
                let _parentObj = _listObj.parent(),
                    _labelHtml = '<span class="attr_default">' + lang_obj.manage.products.default_option + '</span>';
                _parentObj.find("input[name*='[IsDefault]']").val(1);
                // 贴标签
                if (_parentObj.parent().find('.attr_name').find('.attr_default').length == 0) {
                    _parentObj.parent().find('.attr_name').append(_labelHtml);
                }
                //将其他选项取消
                _parentObj.parent().siblings().find('.attr_checkbox').find("input[name*='[IsDefault]']").val(0);
                _parentObj.parent().siblings().find('.attr_name').find('.attr_default').remove();
                //如果存在多仓库的话 要检查其他仓库是否已经存在勾选
                let _visibleTrList = $('#AttrId_1 tbody:visible tr');
                let _siblingsTbodyTr = _visibleTrList.parent().siblings("tbody:not([data-id=0])").find('tr');
                _siblingsTbodyTr.each(function () {
                    if ($(this).find(".attr_checkbox input[name*='[IsDefault]']").val() == 1) {
                        $(this).find('.attr_checkbox').find("input[name*='[IsDefault]']").val(0);
                        $(this).find('.attr_name').find('.attr_default').remove();
                    }
                })
                //手动取消默认选项
                products_obj.function_init.cancel_default_option();
            } else {
                global_obj.win_alert(lang_obj.manage.products.please_select);
            }
        })

        products_obj.function_init.cancel_default_option();

        $(".fixed_batch_edit").delegate(".attr_picture", "click", function () {
            // 触发图片栏
            var $Top = $(this).position().top,
                $Left = $(this).position().left;
            products_obj.function_init.combination_picture({"Type": "batch", "Top": $Top, "Left": $Left});
            return false;
        }).delegate(".popover_img", "click", function () {
            // 选中图片
            var $Img = $(this).attr("style"),
                $Extend = $(this).parents(".box_popover").data("extend"),
                $sExtend = $Extend.substr(1); //清掉第一个字符
            $ImgSrc = $Img.replace("background-image:url('", "").replace("');", "");
            if (!$(this).hasClass("popover_img_selected")) {
                $(this).addClass("popover_img_selected");
                $(".fixed_batch_edit .batch_model[data-number=1] .batch_item:eq(0) .attr_picture").attr("style", $Img).addClass("saved").next().val($ImgSrc);
                products_obj.data_init.batch_save_data.PicPath = $ImgSrc;
            }
            ;
        });


        //******************************** 属性事件 End ********************************


        //******************************** 仓库事件 Start ********************************
        $(".fixed_warehouse_set").on("click", ".warehouse_item", function () {
            // 点击产品
            let $obj = $(".fixed_warehouse_set"),
                $allObj = $obj.find(".select_all_box .input_checkbox_box");
            if ($(this).hasClass("current")) {
                $(this).removeClass("current");
                $(this).find(".btn_checkbox").removeClass("current");
                $(this).find(".btn_checkbox input").prop("checked", false);
            } else {
                $(this).addClass("current");
                $(this).find(".btn_checkbox").addClass("current");
                $(this).find(".btn_checkbox input").prop("checked", true);
            }
            if ($obj.find(".warehouse_item.current").length > 0) {
                // 已有勾选产品
                if ($obj.find(".warehouse_item").length == $obj.find(".warehouse_item.current").length) {
                    // 全选
                    $allObj.addClass("checked").find("input").prop("checked", true);
                }
            } else {
                // 没有勾选产品
                $allObj.removeClass("checked").find("input").prop("checked", false);
            }
        }).on("click", ".warehouse_item .btn_checkbox", function () {
            $(this).parent().click();
            return false;
        }).on("click", ".select_all_box .input_checkbox_box", function () {
            let $this = $(this),
                $obj = $(".fixed_warehouse_set"),
                $item = $obj.find(".warehouse_item");
            if ($this.hasClass("checked")) {
                $item.removeClass("current");
                $item.find(".btn_checkbox").removeClass("current");
                $item.find(".btn_checkbox input").prop("checked", false);
            } else {
                $item.addClass("current");
                $item.find(".btn_checkbox").addClass("current");
                $item.find(".btn_checkbox input").prop("checked", true);
            }
        });
        //******************************** 仓库事件 End ********************************


        //******************************** 关联主图 Start ********************************
        frame_obj.box_type_menu(function (obj) {
            let value = obj.find('input[name="RelateMethod"]').val()
            if (value == 'multiple-single') {
                // 多个属性关联单张主图
                $('#box_main_image .box_main_attr, #box_main_image .box_main_associate').hide()
            } else {
                // 单个属性关联单张主图、单个属性关联多张主图
                $('#box_main_image .box_main_attr, #box_main_image .box_main_associate').show()
            }
            if (value == 'single-single') {
                // 单个属性关联单张主图
                $('#box_main_image .relation_box .upload_file_multi').each(function (index, element) {
                    $(element).find('.img:gt(0)').addClass('hide')
                })
            } else if (value == 'single-multiple') {
                // 单个属性关联多张主图
                $('#box_main_image .relation_box .upload_file_multi').each(function (index, element) {
                    $(element).find('.img').removeClass('hide')
                })
            }
            products_obj.data_init.relate_method = value

            //视频来源切换
            if (obj.find('input[name="VideoType"]').length) {
                let _videoType = obj.find('input[name="VideoType"]').val(),
                    _videoParents = obj.parents('#video_form'),
                    _videoItem = _videoParents.find(`.rows[data-videotype='${_videoType}']`);
                _videoParents.find('.rows').each(function () {
                    if ($(this).attr('data-videotype')) {
                        $(this).hide();
                    }
                })
                _videoItem.show();
                let _coverTips = _videoParents.find('.cover_img .tips');
                if (_videoType == 'local') {
                    _coverTips.show()
                    $('textarea[name=VideoUrl]').removeAttr('notnull');
                } else {
                    _coverTips.hide()
                    $('textarea[name=VideoUrl]').attr('notnull', 'notnull');
                }
            }

        });
        $('#box_main_image input[name="RelateMethod"]:checked').parent().trigger('click')

        $("#box_main_image").on("change", ".box_main_attr select", function () {
            // 选择主属性
            let title = $(this).val();
            let html = "";
            let i = 0;
            let position = parseInt($(this).find("option:selected").index()) - 1;
            if (typeof products_obj.data_init.attr_data[position] !== "undefined" && typeof products_obj.data_init.option_data[title] !== "undefined") {
                $.each(products_obj.data_init.attr_data[position].Options, function (index, element) {
                    let data = products_obj.data_init.option_data[title][element];
                    let picPathAry = data.picture;
                    let j = 0;
                    html += '<tr class="group" data-attr="' + global_obj.htmlspecialchars(title) + '" data-option="' + global_obj.htmlspecialchars(element) + '">';
                    html += '<td class="attr_name">' + element + '</td>';
                    html += '<td class="attr_picture">';
                    html += '<div class="multi_img upload_file_multi" id="PictureDetail_' + i + '">';
                    if (picPathAry) {
                        for (k2 in picPathAry) {
                            html += frame_obj.multi_img_item("PicPath[]", j, 1);
                            ++j;
                        }
                    }
                    html += frame_obj.multi_img_item("PicPath[]", j, 1);
                    html += '</div>';
                    html += '</td>';
                    html += '</tr>';
                    ++i;
                });
            }
            $("#box_main_image .box_main_associate tbody").html(html);
            if (html == "") {
                $("#box_main_image .box_main_associate").removeClass("show");
            } else {
                $("#box_main_image .box_main_associate").addClass("show");
            }
            // 显示图片
            if (typeof products_obj.data_init.option_data[title] !== "undefined") {
                $.each(products_obj.data_init.option_data[title], function (index, element) {
                    $trObj = $("#box_main_image .box_main_associate tbody tr[data-option=\"" + $.quotationMarksTransferred(index) + "\"]");
                    picPathAry = element.picture;
                    if (picPathAry) {
                        for (k2 in picPathAry) {
                            let $sObj = $trObj.find(".img[num=" + k2 + "]"),
                                $picPath = picPathAry[k2];
                            $sObj.find(".pic_btn .zoom").attr("href", $picPath);
                            $sObj.find(".preview_pic a").remove();
                            $sObj.find(".preview_pic").append(frame_obj.upload_img_detail($picPath)).children(".upload_btn").hide().parent().parent().addClass("isfile").removeClass("show_btn");
                            $sObj.find(".preview_pic").children("input[type=hidden]").val($picPath).attr("save", 1).prop("disabled", true).trigger("change");
                            $trObj.find("input[type=hidden]").prop("disabled", true);
                        }
                    }
                });
            }
            // 记录选中ID
            products_obj.data_init.image_data.mainAttr = title;
            // 重新触发一次关联方式
            $('#box_main_image input[name="RelateMethod"]:checked').parent().trigger('click')
            // 排序效果
            $('.box_main_associate .group').each((index, element) => {
                let $this = $(element)
                let $tdObj = $this.find('.attr_picture')
                let $attrName = $this.data('attr')
                let $extend = $this.data('option')
                let $sExtend = global_obj.json_decode_data($extend)
                products_obj.function_init.associate_main_image_move($tdObj, $attrName, $sExtend, false)
            })
        }).on("click", ".upload_btn", function () {
            // 关联主图上传
            let $obj = $(this).parents(".group"),
                $position = parseInt($obj.data('position')),
                $attrName = $obj.data("attr").toString(),
                $extend = $obj.data("option").toString(),
                $Top = $(this).parents(".img").position().top,
                $Left = $(this).parents(".img").position().left + 86,
                $chooseType = $('#box_main_image input[name="RelateMethod"]:checked').val() == 'single-multiple' ? 'checkbox' : 'radio';
            products_obj.function_init.combination_picture({
                "Type": "main",
                "Position": $position,
                "AttrName": $attrName,
                "Extend": $extend,
                "Top": $Top,
                "Left": $Left,
                "chooseType": $chooseType
            });
            products_obj.data_init.popover_img = []
            $obj.find(".attr_picture .img").each(function (index, element) {
                let img = $(element).find("input[type=hidden]").val()
                if (img) products_obj.data_init.popover_img[index] = img
            })
            return false;
        }).on("click", ".popover_img", function () {
            // 选中图片
            let $Img = $(this).attr("style")
            let $ImgSrc = $Img.replace("background-image:url('", "").replace("');", "")
            if ($(this).parents(".box_popover").attr("data-choose-type") == "checkbox") {
                // 多选
                if (!$(this).hasClass("popover_img_selected")) {
                    // 未选中
                    $(this).addClass("popover_img_selected")
                    products_obj.data_init.popover_img.push($ImgSrc)
                } else {
                    // 已选中
                    $(this).removeClass("popover_img_selected")
                    let index = products_obj.data_init.popover_img.indexOf($ImgSrc)
                    products_obj.data_init.popover_img.splice(index, 1)
                }
            } else {
                // 单选
                let $attrName = $(this).parents(".box_popover").data("attr"),
                    $sAttrName = global_obj.json_decode_data($attrName),
                    $htmlAttrName = $.quotationMarksTransferred($sAttrName),
                    $extend = $(this).parents(".box_popover").data("extend"),
                    $sExtend = global_obj.json_decode_data($extend),
                    $htmlExtend = $.quotationMarksTransferred($sExtend),
                    $tdObj = $(".box_main_associate .group[data-attr=\"" + $htmlAttrName + "\"][data-option=\"" + $htmlExtend + "\"] .attr_picture"),
                    $imgObj = $tdObj.find(".img:last"),
                    $count = $tdObj.find(".img").length;
                if (!$(this).hasClass("popover_img_selected")) {
                    // 未选中
                    $(this).addClass("popover_img_selected");
                    $imgObj.find(".pic_btn .zoom").attr("href", $ImgSrc);
                    $imgObj.find(".preview_pic a").remove();
                    $imgObj.find(".preview_pic").append(frame_obj.upload_img_detail($ImgSrc)).children(".upload_btn").hide().parent().parent().addClass("isfile").removeClass("show_btn");
                    $imgObj.find(".preview_pic").children("input[type=hidden]").val($ImgSrc).attr("save", 1).prop("disabled", true).trigger("change");
                    $tdObj.find(".multi_img").append(frame_obj.multi_img_item("PicPath[]", $count, 1));
                    if (products_obj.data_init.relate_method == 'single-single') {
                        // 单个属性关联单张主图
                        $tdObj.find(".multi_img").prepend($imgObj)
                        $tdObj.find(".multi_img").each(function (index, element) {
                            $(element).find('.img:gt(0)').addClass('hide')
                        })
                    }
                    products_obj.function_init.associate_main_image_move($tdObj, $attrName, $sExtend, true)
                } else {
                    // 已选中
                    $(this).removeClass("popover_img_selected");
                    $tdObj.find(".img").each(function (index, element) {
                        if ($(element).find("input[type=hidden]").val() == $ImgSrc) {
                            $(element).remove();
                            return false;
                        }
                    });
                }
                let i = 0;
                $tdObj.find(".img").each(function () {
                    $(this).attr("num", i);
                    ++i;
                });
                products_obj.function_init.write_option_picture_data($tdObj, $attrName, $sExtend)
            }
        }).on("click", ".attr_picture .del", function () {
            // 移除图片
            let $obj = $(this).parents('.img'),
                $obj_par = $obj.parent(),
                $trObj = $obj_par.parents("tr"),
                $attrName = $trObj.data("attr"),
                $option = $trObj.data("option"),
                $upload = $obj_par.find('.img:last'),
                i = 0;
            if (products_obj.data_init.relate_method == 'single-single') {
                // 单个属性关联单张主图
                $upload.removeClass('hide')
            }
            $obj.remove();
            $obj_par.find('.img').each(function () {
                $(this).attr('num', i);
                ++i;
            });
            products_obj.function_init.write_option_picture_data($obj_par, $attrName, $option)
        }).on("click", ".btn_popover_sure", function () {
            // 确认图片
            let $attrName = $(this).parents(".box_popover").data("attr"),
                $sAttrName = global_obj.json_decode_data($attrName),
                $htmlAttrName = $.quotationMarksTransferred($sAttrName),
                $extend = $(this).parents(".box_popover").data("extend"),
                $sExtend = global_obj.json_decode_data($extend),
                $htmlExtend = $.quotationMarksTransferred($sExtend),
                $tdObj = $(".box_main_associate .group[data-attr=\"" + $htmlAttrName + "\"][data-option=\"" + $htmlExtend + "\"] .attr_picture"),
                $count = $tdObj.find(".img").length;
            $tdObj.find(".img.isfile").remove()
            if (products_obj.data_init.popover_img.length > 0) {
                for (let k in products_obj.data_init.popover_img) {
                    let imgSrc = products_obj.data_init.popover_img[k]
                    let imgObj = $tdObj.find(".img:last")
                    imgObj.find(".pic_btn .zoom").attr("href", imgSrc)
                    imgObj.find(".preview_pic a").remove()
                    imgObj.find(".preview_pic").append(frame_obj.upload_img_detail(imgSrc)).children(".upload_btn").hide().parent().parent().addClass("isfile").removeClass("show_btn")
                    imgObj.find(".preview_pic").children("input[type=hidden]").val(imgSrc).attr("save", 1).prop("disabled", true).trigger("change")
                    $tdObj.find(".multi_img").append(frame_obj.multi_img_item("PicPath[]", $count, 1))
                    if (products_obj.data_init.relate_method == 'single-single') {
                        // 单个属性关联单张主图
                        $tdObj.find(".multi_img").prepend(imgObj)
                        $tdObj.find(".multi_img").each(function (index, element) {
                            $(element).find('.img:gt(0)').addClass('hide')
                        })
                    }
                    products_obj.function_init.associate_main_image_move($tdObj, $attrName, $sExtend, true)
                }
            }
            let i = 0
            $tdObj.find(".img").each(function () {
                $(this).attr("num", i)
                ++i
            })
            products_obj.function_init.write_option_picture_data($tdObj, $attrName, $sExtend)
            products_obj.function_init.clean_combination_picture();
        });
        //******************************** 关联主图 End ********************************


        //******************************** 数据提交 Start ********************************
        //产品数据提交
        frame_obj.submit_object_init($('#edit_form'), './?m=products&a=products', function () {
            var $name = '', $NotOption = 0, $NotPrice = 0, $cur_null;
            let $ProId = $("#ProId").val();
            if ($('.fixed_btn_submit .btn_submit').hasClass('btn_disabled')) {
                return false;
            }

            $('.btn_platform').click();
            $('#edit_form *[notnull]').not(':disabled').each(function () {
                if ($.trim($(this).val()) == '') {
                    return false;
                }
            });

            // 属性价格 存在负数的情况下
            if ($('.global_container:visible .attr_error_num_tips').length > 0) {
                $('.r_con_wrap').animate({scrollTop: ($('.global_container:visible .attr_error_num_tips').eq(0).offset().top - $('#header').outerHeight(true) - 30 + $('.r_con_wrap').scrollTop())});
                return false;
            }

            //检测属性、属性选项
            if (parseInt($('input[name=IsCombination]:checked').val()) > 0) {
                let attrCount = $(".box_cart_attribute .rows").length;
                if (attrCount < 1) {
                    global_obj.win_alert_auto_close(lang_obj.manage.products.tips.none_attr, 'await', 1000, '8%');
                    return false;
                }
                $(".box_cart_attribute .rows .input").removeClass("has_error").find(".error_tips").remove();
                $(".box_cart_attribute .choice_list .attr_selected").removeAttr("style");
                $(".box_cart_attribute .box_button_choice").each(function (index, element) {
                    if ($(element).find(".btn_attr_choice").length < 1) {
                        //该属性没有选项
                        if (!$(element).find(".error_tips").length) {
                            $(element).find(".rows .input").addClass("has_error").append('<p class="error_tips" style="font-size:14px;">' + lang_obj.manage.products.tips.enter_options + '</p>');
                        }
                        $(element).find(".choice_list .attr_selected").css({"border-color": "red"});
                        $cur_null = $(element);
                        ++$NotOption;
                    }
                });
                if ($NotOption) {
                    if ($ProId > 0) {
                        $(".box_attribute_tab_menu .item[data-type=\"option\"]").click();
                    }
                    $('.r_con_wrap').animate({scrollTop: ($(".box_cart_attribute").offset().top + $(".r_con_wrap").scrollTop())});
                    return false;
                }
            }
            //检查产品规格价格
            let targetId = 0;
            let targetElement;
            $("#AttrId_1 *[notnull]").not(':disabled').each(function (index, element) {
                if ($.trim($(element).val()) == "") {
                    $(element).css({"border-color": "red"});
                    $NotPrice += 1;
                    targetId = $(element).parents("tbody").data("id");
                    targetElement = $(element);
                    return false;
                } else {
                    $(element).removeAttr("style");
                }
            });
            if ($NotPrice > 0) {
                if ($ProId > 0) {
                    $(".box_attribute_tab_menu .item[data-type=\"price\"]").click();
                    if ($(".box_warehouse").length) {
                        $(".box_warehouse .btn_warehouse[data-id=" + targetId + "]").click();
                    }
                    if (targetElement) $('.r_con_wrap').animate({scrollTop: targetElement.offset().top + $('.r_con_wrap').scrollTop() - 200}, 500);
                } else {
                    if ($(".box_warehouse").length) {
                        $(".box_warehouse .btn_warehouse[data-id=" + targetId + "]").click();
                    }
                }
                return false;
            }
            return true;
        }, '', function (result) {
            $('.fixed_btn_submit').find('input:submit').attr('disabled', 'disabled');
            if (result.ret == 1) {
                global_obj.win_alert_auto_close(lang_obj.global.save_success, '', 500, '8%');
                setTimeout(function () {
                    if (result.msg.jump) { //保存产品资料
                        window.location = result.msg.jump;
                    } else {
                        window.location.reload();
                    }
                }, 500);
            } else {
                $('.fixed_btn_submit').find('input:submit').removeAttr('disabled');
                global_obj.win_alert_auto_close(result.msg, 'fail', 1000, '8%');
            }
        });

        // 添加规格属性
        $(".fixed_add_attribute .btn_submit").click(function () {
            var $Obj = $(".fixed_add_attribute"),
                $IsNull = 0;
            $Obj.find("*[notnull]").parent().removeClass("has_error");
            $Obj.find('*[notnull]').each(function () {
                if ($.trim($(this).val()) == '') {
                    $IsNull = 1;
                    $(this).parent().addClass("has_error");
                }
            });
            if ($IsNull == 1) {
                return false;
            }
            var $AttrName = $.trim($Obj.find("input[name=AttrName]").val()),
                $i = 0, $MaxCount = 0, $AttrId = 0, $Position = 0, $Html = "";
            $Obj.find(".attr_option_value").each(function (index, element) {
                $Options[index] = $(this).val();
            });
            // 检查属性名称
            if ($AttrName == "Ship From") {
                $Obj.find("input[name=AttrName]").parent().addClass("has_error").find(".error_tips").text(lang_obj.manage.products.tips.ship_from);
                return false;
            } else {
                $Obj.find("input[name=AttrName]").parent().removeClass("has_error").find(".error_tips").text("");
            }
            $i = 0;
            $(products_obj.data_init.attr_data).each(function (index, element) {
                if ($AttrName == element.Name) {
                    // 名称相同
                    $i += 1;
                }
            });
            if ($i > 0) {
                $Obj.find("input[name=AttrName]").parent().addClass("has_error").find(".error_tips").text(lang_obj.manage.products.tips.same_attr);
                return false;
            } else {
                $Obj.find("input[name=AttrName]").parent().removeClass("has_error").find(".error_tips").text("");
            }
            // 选项类型
            $attrType = $Obj.find("input[name=Type]:checked").val();
            $MaxCount = products_obj.data_init.attr_data.length;
            $Position = $MaxCount + 1;
            products_obj.data_init.attr_data[$MaxCount] = {
                "AttrId": "",
                "Name": $AttrName,
                "Options": [],
                "Position": $Position,
                "Type": $attrType
            };
            products_obj.data_init.option_data[$AttrName] = {};

            //目标不存在
            $Html += '<div class="box_button_choice clean" data-id="' + $AttrId + '" data-position="' + $Position + '" data-attr="' + global_obj.htmlspecialchars($AttrName) + '" data-type="' + $attrType + '">';
            $Html += '<div class="rows clean" data-id="' + $AttrId + '">';
            $Html += '<label>';
            $Html += '<strong>' + $AttrName + '</strong>';
            $Html += '<dl class="box_basic_more box_attr_basic_more">';
            $Html += '<dt><a href="javascript:;" class="btn_basic_more"><i></i></a></dt>';
            $Html += '<dd class="drop_down">';
            $Html += '<a href="javascript:;" class="item input_checkbox_box btn_option_edit"><span>' + lang_obj.manage.global.edit + '</span></a>';
            // $Html += 						'<a href="javascript:;" class="item input_checkbox_box btn_picture_edit"><span>' + lang_obj.manage.products.main_picture + '</span></a>';
            $Html += '<a href="javascript:;" class="item input_checkbox_box btn_attribute_delete"><span>' + lang_obj.global.del + '</span></a>';
            $Html += '</dd>';
            $Html += '</dl>';
            $Html += '</label>';
            $Html += '<div class="input">';
            $Html += '<div class="choice_list">';
            $Html += '<div class="attr_selected">';
            $Html += '<div class="select_list"></div>';
            $Html += '<input type="text" class="box_input" name="_Attr" value="" size="30" maxlength="255" />';
            $Html += '<span class="placeholder">' + lang_obj.manage.products.placeholder + '</span>';
            $Html += '</div>';
            $Html += '</div>';
            $Html += '</div>';
            $Html += '</div>';
            $Html += '<input type="hidden" name="AttrTitle[]" value="' + global_obj.htmlspecialchars($AttrName) + '" class="attr_title" />';
            $Html += '<input type="hidden" name="AttrType[]" value="' + $attrType + '" class="attr_type" />';
            $Html += '<input type="hidden" name="AttrData[]" value="{}" class="attr_data" />';
            $Html += '</div>';
            $('.box_cart_attribute').append($Html);
            products_obj.function_init.attr_load();
            $('#fixed_right .btn_cancel').click();
            if ($(".box_cart_attribute .box_button_choice").length > 0) {
                $(".attr_no_data").hide();
                $("#add_attribute").show();
                if (parseInt($('#edit_form input[name=ProId]').val()) == 0) {
                    $("#attribute_ext_box, #box_main_image").show();
                }
            }
        });

        // 属性排序(提交)
        $(".fixed_edit_attribute .btn_submit").click(function () {
            let $obj = $(".fixed_edit_attribute"),
                $sortAry = [], $newAry = [], $compareAry = {}, $attrNameAry = [], $oldAttrNameAry = [];
            $obj.find(".item").each(function (index, element) {
                $sortAry[index] = $(this).find("input").val();
            });
            $($sortAry).each(function (index, element) {
                $attrNameAry[index] = element;
                $(products_obj.data_init.attr_data).each(function (key, val) {
                    if (element == val.Name) {
                        if (val.Options.length > 0) {
                            $compareAry[val.Position - 1] = index;
                        }
                        $newAry[index] = val;
                        $newAry[index].Position = (index + 1);
                    }
                });
            });
            $(products_obj.data_init.attr_data).each(function (key, val) {
                $oldAttrNameAry[key] = val.Name;
            });
            $compareAry = Object.values($compareAry);
            products_obj.data_init.attr_data = $newAry;
            // 属性调换顺序
            let $AttrSaveAry = [], $AttrPostionAry = [], $Title = "";
            $(".box_cart_attribute .box_button_choice").each(function (index, element) {
                $Title = $(this).find("strong").text();
                $AttrSaveAry[$Title] = $(this).html();
                $AttrPostionAry[$Title] = index + 1;
            });
            $(".box_cart_attribute .box_button_choice").remove(); // 先清空
            $(products_obj.data_init.attr_data).each(function (index, element) {
                let re = new RegExp("AttrOption\\[" + $AttrPostionAry[element.Name] + "\\]", "g");
                $(".box_cart_attribute").append('<div class="box_button_choice clean" data-id="' + element.AttrId + '" data-position="' + element.Position + '" data-attr="' + global_obj.htmlspecialchars(element.Name) + '" data-type="' + element.Type + '">' + $AttrSaveAry[element.Name].replace(re, "AttrOption[" + (index + 1) + "]") + '</div>');
            });
            let $attrName = global_obj.json_decode_data($attrNameAry),
                $oldAttrName = global_obj.json_decode_data($oldAttrNameAry);
            let $extend = "", $newExtend = "", $extendAry = [], $newExtendAry = {};
            for (let id in products_obj.data_init.ext_attr_data[1]) {
                for (let index in products_obj.data_init.ext_attr_data[1][id]) {
                    element = products_obj.data_init.ext_attr_data[1][id][index];
                    $extend = element.Data;
                    $extendAry = global_obj.json_encode_data($extend);
                    $newExtendAry = {};
                    $($extendAry).each(function (index1, element1) {
                        $newExtendAry[$compareAry[index1]] = element1;
                    });
                    Object.keys($newExtendAry).sort();
                    $newExtendAry = Object.values($newExtendAry);
                    $newExtend = global_obj.json_decode_data($newExtendAry);
                    products_obj.data_init.ext_attr_data[1][id][index].AttrName = $attrName;
                    products_obj.data_init.ext_attr_data[1][id][index].Data = $newExtend;
                    products_obj.data_init.ext_attr_data[1][id][index].Combination = $newExtend;
                    products_obj.data_init.ext_attr_data[1][id][index].Title = $newExtendAry.join(" / ");
                    if (typeof (products_obj.data_init.ext_save_data[id][$attrName]) == "undefined") {
                        products_obj.data_init.ext_save_data[id][$attrName] = {};
                    }
                    if (typeof (products_obj.data_init.ext_save_data[id][$attrName][$newExtend]) == "undefined") {
                        products_obj.data_init.ext_save_data[id][$attrName][$newExtend] = products_obj.data_init.ext_save_data[id][$oldAttrName][$extend];
                    }
                }
            }
            ;
            products_obj.function_init.attr_load();
            products_obj.function_init.attr_price_show();
            $("#fixed_right .btn_cancel").click();
        });

        // 修改产品选项(提交)
        $(".fixed_edit_attribute_option_edit .btn_submit").click(function () {
            var $obj = $(".fixed_edit_attribute_option_edit"),
                $IsNull = 0;
            $obj.find("*[notnull]").parent().removeClass("has_error");
            $obj.find("*[notnull]").each(function () {
                if ($.trim($(this).val()) == "") {
                    $IsNull = 1;
                    $(this).parent().addClass("has_error");
                }
            });
            if ($IsNull == 1) {
                return false;
            }
            let $newAttrName = $.trim($obj.find("input[name=AttrName]").val()),
                $Position = parseInt($obj.find("input[name=AttrName]").data("position")),
                $Index = $Position - 1;
            $oldAttrName = products_obj.data_init.attr_data[$Index].Name,
                $Options = [], $oldOptions = [], $i = 0, $OptionsAry = [], $OptionsError = [];
            $obj.find(".attr_option_value").each(function (index, element) {
                let value = $(this).next().text();
                $Options[index] = $.trim($(this).val());
                $oldOptions[index] = value;
            });
            // 检查属性名称
            $i = 0;
            $(products_obj.data_init.attr_data).each(function (index, element) {
                if (index != $Index && $newAttrName == element.Name) {
                    // 名称相同
                    $i += 1;
                }
            });
            if ($i > 0) {
                $obj.find("input[name=AttrName]").parent().addClass("has_error");
                return false;
            } else {
                $obj.find("input[name=AttrName]").parent().removeClass("has_error");
            }
            // 检查选项名称
            $i = 0;
            $($Options).each(function (index, element) {
                if ($OptionsAry[element] && typeof ($OptionsAry[element]) != undefined) {
                    // 已重复名称
                    $OptionsError[$i] = element;
                    $i += 1;
                } else {
                    $OptionsAry[element] = index + 1;
                }
            });
            $obj.find(".attr_option_value").parent().removeClass("has_error");
            if ($i > 0) {
                $($OptionsError).each(function (index, element) {
                    $obj.find(".attr_option_value").each(function () {
                        if (element == $(this).val()) $(this).parent().addClass("has_error");
                    });
                });
                return false;
            }
            // 替换现有的数据
            let $replaceOption = {}, $oldOptionAry = {};
            products_obj.data_init.attr_data[$Index].Name = $newAttrName;
            $(".box_cart_attribute .box_button_choice[data-position=" + $Position + "] strong").text($newAttrName);
            $(".box_cart_attribute .box_button_choice[data-position=" + $Position + "] .attr_title").val($newAttrName);
            if (!products_obj.data_init.option_data[$newAttrName]) {
                products_obj.data_init.option_data[$newAttrName] = products_obj.data_init.option_data[$oldAttrName];
            }
            let _optionAry = products_obj.data_init.option_data[$newAttrName];
            products_obj.data_init.option_data[$newAttrName] = {};
            $(products_obj.data_init.attr_data[$Index].Options).each(function (index, element) {
                var $sObj = $(".box_cart_attribute .box_button_choice[data-position=" + $Position + "] .btn_attr_choice:eq(" + index + ")"),
                    $Value = $sObj.find("b").text();
                products_obj.data_init.attr_data[$Index].Options[index] = $Options[index];
                $sObj.find(".attr_current").attr("name", "AttrOption[" + $Position + "][]");
                $sObj.find("b").text($Options[index]).next('input').val($Options[index]);
                $replaceOption[$Value] = $Options[index]; // 旧换新
                $oldOptionAry[$Value] = $oldOptions[index]; // 旧选项内容
                if ($Options[index] != $oldOptions[index]) {
                    products_obj.data_init.option_data[$newAttrName][$Options[index]] = _optionAry[$oldOptions[index]];
                    delete products_obj.data_init.option_data[$newAttrName][$oldOptions[index]]; // 移除旧有的数据
                } else {
                    products_obj.data_init.option_data[$newAttrName][$Options[index]] = _optionAry[$Options[index]];
                }
            });
            let json = global_obj.json_decode_data(products_obj.data_init.option_data[$newAttrName]);
            $(".box_cart_attribute .box_button_choice[data-attr=\"" + $.quotationMarksTransferred($newAttrName) + "\"] .attr_data").val(json);
            // 多规格
            let $attrName = "", $attrNameAry = [], $extend = "", $extendAry = [], $oldExtendAry = [];
            $(products_obj.data_init.attr_data).each(function (index, element) {
                $attrNameAry[index] = element.Name;
            });
            $attrName = global_obj.json_decode_data($attrNameAry);
            for (let id in products_obj.data_init.ext_attr_data[1]) {
                let extSaveData = $.fn.deepCopy(products_obj.data_init.ext_save_data[id] || [])
                for (let index in products_obj.data_init.ext_attr_data[1][id]) {
                    let element = products_obj.data_init.ext_attr_data[1][id][index];
                    $extend = element.Data;
                    $extendAry = global_obj.json_encode_data($extend);
                    $($extendAry).each(function (index, elements) {
                        $oldExtendAry[index] = $extendAry[index];
                        if (index == $Index) {
                            // 该属性
                            $extendAry[index] = $replaceOption[elements];
                            $oldExtendAry[index] = $oldOptionAry[elements];
                        }
                    });
                    $extend = global_obj.json_decode_data($extendAry);
                    $oldExtend = global_obj.json_decode_data($oldExtendAry);
                    products_obj.data_init.ext_attr_data[1][id][index].AttrName = $attrName;
                    products_obj.data_init.ext_attr_data[1][id][index].Data = $extend;
                    products_obj.data_init.ext_attr_data[1][id][index].Combination = $extend;
                    products_obj.data_init.ext_attr_data[1][id][index].Title = $extendAry.join(" / ");
                    if (typeof (extSaveData[$attrName]) == "undefined") {
                        extSaveData[$attrName] = {};
                    }
                    if (typeof (extSaveData[$attrName][$oldExtend]) != 'undefined') {
                        extSaveData[$attrName][$extend] = products_obj.data_init.ext_save_data[id][$attrName][$oldExtend];
                        if ($extend !== $oldExtend) {
                            extSaveData[$attrName][$extend].AttrName = $attrName;
                            extSaveData[$attrName][$extend].Data = $extend;
                            extSaveData[$attrName][$extend].Combination = $extend;
                            extSaveData[$attrName][$extend].Title = $extendAry.join(" / ");
                        }
                    } else {
                        extSaveData[$attrName][$extend] = products_obj.data_init.ext_attr_data[1][id][index];
                    }
                }
                products_obj.data_init.ext_save_data[id] = extSaveData
            }
            ;
            // 多规格加价
            $oldAttrName = global_obj.json_decode_data([$oldAttrName]);
            $newAttrName = global_obj.json_decode_data([$newAttrName]);
            if (typeof (products_obj.data_init.ext_save_data[$newAttrName]) == "undefined") {
                products_obj.data_init.ext_save_data[$newAttrName] = {};
            }
            let extSaveData = $.fn.deepCopy(products_obj.data_init.ext_save_data || {})
            for (let index in products_obj.data_init.ext_attr_data[0]) {
                let element = products_obj.data_init.ext_attr_data[0][index];
                if ($oldAttrName == element.AttrName) {
                    $extend = element.Data;
                    $extendAry = global_obj.json_encode_data($extend);
                    let ext = $extendAry[0]
                    $extendAry[0] = (typeof $replaceOption[ext] !== 'undefined' ? $replaceOption[ext] : ext);
                    $extend = global_obj.json_decode_data($extendAry);
                    $oldExtendAry = []
                    $oldExtendAry[0] = (typeof $oldOptionAry[ext] !== 'undefined' ? $oldOptionAry[ext] : ext);
                    $oldExtend = global_obj.json_decode_data($oldExtendAry);
                    products_obj.data_init.ext_attr_data[0][index].AttrName = $newAttrName;
                    products_obj.data_init.ext_attr_data[0][index].Data = $extend;
                    products_obj.data_init.ext_attr_data[0][index].Combination = $extend;
                    products_obj.data_init.ext_attr_data[0][index].Title = $extendAry.join(" / ");
                    if (typeof (extSaveData[$newAttrName][$oldExtend]) != 'undefined') {
                        extSaveData[$newAttrName][$extend] = products_obj.data_init.ext_save_data[$newAttrName][$oldExtend];
                        if ($extend !== $oldExtend) {
                            extSaveData[$newAttrName][$extend].Data = $extend;
                            extSaveData[$newAttrName][$extend].Combination = $extend;
                            extSaveData[$newAttrName][$extend].Title = $extendAry.join(" / ");
                        }
                    } else {
                        extSaveData[$newAttrName][$extend] = products_obj.data_init.ext_attr_data[0][index]; // 记录到保存数据
                    }
                }
            }
            ;
            products_obj.data_init.ext_save_data = extSaveData
            products_obj.function_init.option_type_model();
            products_obj.function_init.attr_price_show();
            $("#fixed_right .btn_cancel").click();
        });

        // 修改多图(提交)
        $(".fixed_edit_attribute_picture_edit .btn_submit").click(function () {
            let $obj = $(".fixed_edit_attribute_picture_edit"),
                $attrName = $obj.find(".relation_box").data("attr"),
                $position = $obj.find(".relation_box").data("position"),
                $optionName = "", $picPath = "", $picAry = {};
            $obj.find("tbody tr").each(function (index, element) {
                $picAry = {};
                $optionName = $(element).data("value");
                $(element).find(".img").each(function (index2, element2) {
                    $picPath = $(element2).find("input[type=hidden]").val();
                    if ($picPath) {
                        $picAry[index2] = $picPath;
                    }
                });
                products_obj.data_init.option_data[$attrName][$optionName].picture = $picAry;
            });
            let json = global_obj.json_decode_data(products_obj.data_init.option_data[$attrName]);
            $(".box_cart_attribute .box_button_choice[data-position=" + $position + "] .attr_data").val(json);
            $("#fixed_right .btn_cancel").click();
        });

        // 添加产品规格
        $(".fixed_add_combination .btn_submit").click(function () {
            let $Obj = $(".fixed_add_combination"),
                $Pass = 0;
            $Position = "", $Value = "", $Title = "", $Index = 0, $extendAry = [];
            $Obj.find(".box_input").parent().removeClass("has_error");
            $Obj.find("*[notnull]").each(function () {
                if ($.trim($(this).val()) == "") {
                    $(this).parent().addClass("has_error");
                    $Pass += 1;
                }
            });
            if ($Pass > 0) {
                return false;
            }
            $Obj.find(".box_input").each(function (index, element) {
                $Value = $.trim($(this).val()),
                    $Position = $(this).data("position"),
                    $Index = $Position - 1;
                $extendAry[index] = $Value;
                $Title = $(this).parents(".rows").find("label").text();
                if (global_obj.in_array($Value, products_obj.data_init.attr_data[$Index].Options)) {
                    // 现有选项
                } else {
                    // 创建选项
                    $(".box_cart_attribute .box_button_choice[data-position=" + $Position + "] .select_list").append(frame_obj.buttonChoiceOption($Value, "AttrOption", $Position, 1));
                    $(".box_cart_attribute .box_button_choice[data-position=" + $Position + "]").find('.placeholder').addClass('hide');
                    products_obj.data_init.attr_data[$Index]["Options"][products_obj.data_init.attr_data[$Index]["Options"].length] = $Value;
                }
            });
            let $MaxCount = 0,
                $extend = global_obj.json_decode_data($extendAry),
                $attrName = "",
                $attrNameAry = [],
                $newOptionAry = {};
            $(products_obj.data_init.attr_data).each(function (index, element) {
                $attrNameAry[index] = element.Name;
                $newOptionAry[element.Name] = $extendAry[index];
                if (typeof (products_obj.data_init.option_data[element.Name][$extendAry[index]]) === "undefined") {
                    products_obj.data_init.option_data[element.Name][$extendAry[index]] = {
                        "color": "",
                        "main": "",
                        "picture": []
                    };
                }
            });
            $attrName = global_obj.json_decode_data($attrNameAry);
            if (!$(".attribute_ext .group[data-extend=\"" + global_obj.htmlspecialchars($extend) + "\"]").length) {
                // 添置规格数据(多规格)
                for (let id in products_obj.data_init.ext_attr_data[1]) {
                    $MaxCount = 0;
                    for (let k in products_obj.data_init.ext_attr_data[1][id]) {
                        if (products_obj.data_init.ext_attr_data[1][id][k].Combination == $extend) {
                            global_obj.win_alert_auto_close(lang_obj.manage.products.tips.same_combination, 'fail', 1000, '8%');
                            return false;
                        }
                        $MaxCount += 1;
                    }
                    products_obj.data_init.ext_attr_data[1][id][$MaxCount] = {
                        "AttrName": $attrName,
                        "Combination": $extend,
                        "Data": $extend,
                        "Title": $extendAry.join(" / "),
                        "ProId": "",
                        "OldPrice": "",
                        "Price": "",
                        "CostPrice": "",
                        "SKU": "",
                        "Stock": "",
                        "VariantsId": "",
                        "Weight": "",
                        "WeightUnit": "kg",
                        "PicPath": ""
                    };
                    if (typeof (products_obj.data_init.ext_save_data[id][$attrName]) == "undefined") {
                        // 记录到保存数据(属性)
                        products_obj.data_init.ext_save_data[id][$attrName] = {};
                    }
                    if (typeof (products_obj.data_init.ext_save_data[id][$attrName][$extend]) == 'undefined') {
                        // 记录到保存数据
                        products_obj.data_init.ext_save_data[id][$attrName][$extend] = products_obj.data_init.ext_attr_data[1][id][$MaxCount];
                    }
                }
                // 添置规格数据(多规格加价)
                let $extData = {}, $index = 0, $extAry = [];
                for (let index in products_obj.data_init.ext_attr_data[0]) {
                    let element = products_obj.data_init.ext_attr_data[0][index];
                    if (typeof ($extData[element.AttrName]) == "undefined") {
                        $extData[element.AttrName] = {};
                    }
                    $extData[element.AttrName][element.Data] = index;
                }
                ;
                let $autoAry = products_obj.function_init.auto_combination("separate");
                for (let i in $autoAry) {
                    for (let j in $autoAry[i]) {
                        let $_attrName = i,
                            $title = $autoAry[i][j],
                            $optionName = global_obj.json_decode_data($title);
                        if (typeof ($extData[$_attrName][$optionName]) == "undefined") {
                            // 新添置的选项
                            $extAry[$index] = {
                                "AttrName": $_attrName,
                                "Combination": $optionName,
                                "Data": $optionName,
                                "Title": $title[0],
                                "ProId": "",
                                "OldPrice": "",
                                "Price": "",
                                "CostPrice": "",
                                "SKU": "",
                                "Stock": "",
                                "VariantsId": "",
                                "Weight": "",
                                "WeightUnit": "kg",
                                "PicPath": ""
                            };
                            if (typeof (products_obj.data_init.ext_save_data[$_attrName][$optionName]) == 'undefined') {
                                // 记录到保存数据
                                products_obj.data_init.ext_save_data[$_attrName][$optionName] = $extAry[$index];
                            }
                        } else {
                            $extAry[$index] = products_obj.data_init.ext_attr_data[0][$extData[$_attrName][$optionName]];
                        }
                        ++$index;
                    }
                }
                products_obj.data_init.ext_attr_data[0] = $extAry;
            }
            products_obj.function_init.attr_load();
            products_obj.function_init.attr_price_show();
            $("#fixed_right .btn_cancel").click();
        });

        //批量处理
        $(".fixed_batch_edit .btn_submit").click(function () {
            let $Obj = $(".fixed_batch_edit"),
                $number = ($("#AttrId_1").hasClass("show") ? 1 : 0),
                $tbodyObj = $("#AttrId_" + $number + " tbody:visible"),
                $Group = {}, $Increase = 0, $Position = 0, $Ary = {}, $Ok = 0, $Result = "", $Result_1 = "",
                $Prefix = "", $Number = "";
            $Obj.find(".batch_model[data-number=" + $number + "] input").each(function (index, element) {
                let _Value = _objValue = $.trim($(element).val());

                if ($(element).data("type") !== undefined) {
                    if ($(element).data("type") == 'oldprice' || $(element).data("type") == 'price' || $(element).data("type") == 'cost_price') {
                        if (!$(element).parents('.item_content:visible').length) {
                            return true;
                        }	//没选中的跳过
                        if ($(element).parents('.item_content').hasClass('item_adjustment')) {	//如果选择调整价格的话 记录对应的select的值作为键值 input的为值 下面的时候进行处理 
                            _select = $(element).parents('.item_content').find('select').val()
                            _Value = {};
                            _Value[_select] = _objValue;
                        }
                    }
                    products_obj.data_init.batch_save_data[$(element).data("type")] = _Value;
                    if ($.trim($(element).val())) {
                        $Ok += 1;
                    }
                }
            });
            if (products_obj.data_init.batch_save_data.PicPath) {
                $Ok += 1;
            }
            $SKU = $.trim(products_obj.data_init.batch_save_data.sku);
            if ($Obj.find(".input_checkbox_box").hasClass("checked")) {
                //自动生成
                $Number = 0;
                $Increase = 1;
                reg = /\d+$/g;
                $Result = reg.exec($SKU);
                if ($Result != null) {
                    $SKU = $SKU.substr(0, $Result["index"]);
                    $Number = parseInt($Result[0]);
                    reg = /^[0]+/g;
                    $Result_1 = reg.exec($Result[0]);
                    if ($Result_1 != null) {
                        $Prefix = $Result_1[0];
                    }
                }
            }
            $tbodyObj.find(".input_checkbox_box.checked").each(function (index, element) {
                $Group = $(this).parents(".group");
                $dataAttr = global_obj.json_decode_data($Group.data("attr"));
                $dataExtend = global_obj.json_decode_data($Group.data("extend"));
                $wid = $Group.data("wid");
                $Position = $Group.data("position");
                $Ary = {};
                if (products_obj.data_init.batch_save_data.PicPath) {
                    $Group.find(".attr_picture").attr("style", "background-image:url('" + products_obj.data_init.batch_save_data.PicPath + "');").addClass("saved").next().val(products_obj.data_init.batch_save_data.PicPath);
                    $Ary["PicPath"] = products_obj.data_init.batch_save_data.PicPath;
                }
                if ($Increase == 1) {
                    $a = $Number.toString().length;
                    if (index > 0) {
                        //第二次开始自增
                        $Number += 1;
                    }
                    if ($Prefix) {
                        ss = $Number.toString().length - $a;
                        if (ss > 0) {
                            $Prefix = $Prefix.substr(0, $Prefix.toString().length - ss);
                        }
                    }
                }

                $Group.find(".box_input").each(function (index, element) {
                    if ($(element).data("type") == "sku" && $.trim(products_obj.data_init.batch_save_data.sku)) {
                        $(element).val($.trim($SKU + $Prefix + $Number));
                        $Ary["SKU"] = $.trim($SKU + $Prefix + $Number);
                    }

                    if ($(element).data("type") == "oldprice" && $.trim(products_obj.data_init.batch_save_data.oldprice)) {
                        let _OldPrice = $.trim(products_obj.data_init.batch_save_data.oldprice);
                        if (typeof (products_obj.data_init.batch_save_data.oldprice) == 'object') {
                            _OldPrice = countFun(products_obj.data_init.batch_save_data.oldprice, parseFloat($(element).val()));
                        }
                        $(element).val(_OldPrice);
                        $Ary["OldPrice"] = _OldPrice
                        checkValueFun($(element));
                    }

                    if ($(element).data("type") == "price" && $.trim(products_obj.data_init.batch_save_data.price)) {
                        let _Price = $.trim(products_obj.data_init.batch_save_data.price);
                        if (typeof (products_obj.data_init.batch_save_data.price) == 'object') {
                            _Price = countFun(products_obj.data_init.batch_save_data.price, parseFloat($(element).val()));
                        }
                        $(element).val(_Price);
                        $Ary["Price"] = _Price;
                        checkValueFun($(element));
                    }

                    if ($(element).data("type") == "cost_price" && $.trim(products_obj.data_init.batch_save_data.cost_price)) {
                        let _CostPrice = $.trim(products_obj.data_init.batch_save_data.cost_price);
                        if (typeof (products_obj.data_init.batch_save_data.cost_price) == 'object') {
                            _CostPrice = countFun(products_obj.data_init.batch_save_data.cost_price, parseFloat($(element).val()));
                        }
                        $(element).val(_CostPrice);
                        $Ary["CostPrice"] = _CostPrice;
                        checkValueFun($(element));
                    }


                    if ($(this).data("type") == "stock" && $.trim(products_obj.data_init.batch_save_data.stock)) {
                        $(element).val($.trim(products_obj.data_init.batch_save_data.stock));
                        $Ary["Stock"] = $.trim(products_obj.data_init.batch_save_data.stock);
                    }
                    if ($(element).data("type") == "weight" && $.trim(products_obj.data_init.batch_save_data.weight)) {
                        $(element).val($.trim(products_obj.data_init.batch_save_data.weight));
                        $Ary["Weight"] = $.trim(products_obj.data_init.batch_save_data.weight);
                    }
                });
                for (k in $Ary) {
                    if (products_obj.data_init.attr_model == 1) {
                        // 多规格
                        products_obj.data_init.ext_attr_data[products_obj.data_init.attr_model][$wid][$Position][k] = $Ary[k];
                        products_obj.data_init.ext_save_data[$wid][$dataAttr][$dataExtend][k] = $Ary[k];
                    } else {
                        // 多规格加价
                        products_obj.data_init.ext_attr_data[products_obj.data_init.attr_model][$Position][k] = $Ary[k];
                        products_obj.data_init.ext_save_data[$dataAttr][$dataExtend][k] = $Ary[k];
                    }
                }
            });
            checkValueFun();
            products_obj.data_init.batch_save_data = {}; //清空
            $Ok > 0 && global_obj.win_alert_auto_close(lang_obj.manage.products.tips.all_applied, '', 1000, '8%');
            $("#fixed_right .btn_cancel").click();
        });

        var countFun = function (_object, _thisValue) {
            let _value = parseFloat(_thisValue),
                _key = Object.keys(_object)[0],
                _num = parseFloat(Object.values(_object)[0]),
                _return = 0;
            if (isNaN(_value)) {
                _value = 0;
            }

            if (_key == 'increase') {	// +
                _return = _value + _num;
            } else if (_key == 'reduce') {	// -
                _return = _value - _num;
            } else if (_value > 0 && _num > 0 && _key == 'increase_per') { // _value * (1 + _num / 100)
                _return = _value * (1 + _num / 100);
            } else if (_value > 0 && _num > 0 && _key == 'per_reduction') {	// _value * (1 - _num / 100)
                _return = _value * (1 - _num / 100);
            }
            _return = _return.toFixed(2);
            return _return;
        }

        var checkValueFun = function (_Obj = '') {
            if (_Obj) {
                let _value = parseFloat(_Obj.val());
                if (_value < 0) {
                    _Obj.blur().css({'border-color': 'red'});
                    if (!_Obj.parent().find('.attr_error_num_tips').length) {
                        _Obj.after('<div class="attr_error_num_tips">' + lang_obj.manage.error.attr_error_num_tips + '</div>');
                    }
                } else {
                    _Obj.removeAttr('style');
                    if (_Obj.parent().find('.attr_error_num_tips').length > 0) {
                        _Obj.parent().find('.attr_error_num_tips').remove();
                    }
                }
            } else {
                $.each($('.attr_error_num_tips'), function () {
                    _this = $(this);
                    _this.prev().on('keyup', function () {
                        __this = $(this);
                        if (__this.val() >= 0) {
                            __this.removeAttr('style');
                            _this.remove();
                        }
                    })
                })
            }
        }

        // 修改仓库设置(提交)
        $(".fixed_warehouse_set .btn_submit").click(function () {
            let $obj = $(".fixed_warehouse_set"),
                $data = [],
                json = '';
            // 记录已勾选仓库数据
            $obj.find(".warehouse_item.current").each(function (index, element) {
                $data[index] = $(element).data("id");
            });
            products_obj.data_init.warehouse_set_data = $data;
            json = global_obj.json_decode_data($data);
            $("#edit_form [name=Warehouse]").val(json);
            // 仓库下拉事件
            let changeData = {"current": 0, "show": [], "hide": []};
            $(".warehouse_filter .inside_body li").each(function (index, element) {
                let $link = $(element).find("a"),
                    $id = $link.data("id");
                if (global_obj.in_array($id, $data)) {
                    // 显示
                    if ($(element).hasClass("hide")) {
                        // 隐藏 => 显示
                        $(element).removeClass("hide");
                        changeData.show.push($id);
                    }
                } else {
                    // 隐藏
                    if (!$(element).hasClass("hide")) {
                        // 显示 => 隐藏
                        $(element).addClass("hide");
                        changeData.hide.push($id);
                        if ($link.hasClass("current")) changeData.current = 1; // 当前选项已被选择显示
                    }
                }
            });
            if ($(".warehouse_filter .box_warehouse.current").length == 0) {
                // 显示仓库
                changeData.show.push(0);
            }
            // 显示仓库
            if (changeData.show.length > 0) {
                $(changeData.show).each(function (index, element) {
                    // 单规格(仓库)
                    $("#attribute_unit_box tr[data-type=\"warehouse\"][data-id=" + element + "]").attr("data-disabled", 0);
                });
                // 多规格 补充缺失的数据
                let $count = products_obj.function_init.attr_combination_count();
                if ($count <= products_obj.config_init.option_max) {
                    $autoAry = products_obj.function_init.auto_combination();

                    // 获取默认仓库(data-id="0")的数据用于复制
                    let defaultData = products_obj.data_init.ext_attr_data[1][0] || null;
                    let shouldCopyFromDefault = defaultData && defaultData.length > 0;



                    for (let k in changeData.show) {
                        let $extAry = [];
                        let id = changeData.show[k];
                        $index = 0;



                        for (let i in $autoAry) {
                            for (let j in $autoAry[i]) {
                                let $attrName = i,
                                    $title = $autoAry[i][j],
                                    $optionName = global_obj.json_decode_data($title);

                                // 创建基础数据结构
                                let baseData = {
                                    "AttrName": $attrName,
                                    "Combination": $optionName,
                                    "Data": $optionName,
                                    "Title": $title.join(" / "),
                                    "ProId": "",
                                    "OldPrice": "",
                                    "Price": "",
                                    "CostPrice": "",
                                    "SKU": "",
                                    "Stock": "",
                                    "VariantsId": "",
                                    "Weight": "",
                                    "WeightUnit": "kg",
                                    "PicPath": ""
                                };

                                // 如果需要从默认仓库复制数据
                                if (shouldCopyFromDefault && defaultData && defaultData[$index] && id !== 0) {
                                    const sourceData = defaultData[$index];

                                    // 尝试从ext_save_data中获取更准确的数据
                                    let actualSourceData = sourceData;
                                    if (products_obj.data_init.ext_save_data[0] &&
                                        products_obj.data_init.ext_save_data[0][$attrName] &&
                                        products_obj.data_init.ext_save_data[0][$attrName][$optionName]) {
                                        actualSourceData = products_obj.data_init.ext_save_data[0][$attrName][$optionName];

                                    }

                                    // 如果ext_save_data中的PriorityShippingOvId为0，尝试从DOM中读取
                                    if (!actualSourceData.PriorityShippingOvId || actualSourceData.PriorityShippingOvId === 0) {
                                        const defaultTbody = $('#AttrId_1 tbody[data-id="0"]');
                                        const prioritySelect = defaultTbody.find('select[name*="[PriorityShippingOvId]"]').eq($index);
                                        if (prioritySelect.length > 0) {
                                            const domValue = prioritySelect.val();
                                            if (domValue && domValue !== '0') {
                                                actualSourceData = {...actualSourceData, PriorityShippingOvId: domValue};

                                            }
                                        }
                                    }

                                    // 复制所有字段，保持数据完整性
                                    baseData.Price = actualSourceData.Price || "";
                                    baseData.OldPrice = actualSourceData.OldPrice || "";
                                    baseData.CostPrice = actualSourceData.CostPrice || "";
                                    baseData.Stock = actualSourceData.Stock || "";
                                    baseData.SKU = actualSourceData.SKU || "";
                                    baseData.Weight = actualSourceData.Weight || "";
                                    baseData.WeightUnit = actualSourceData.WeightUnit || "kg";
                                    baseData.PicPath = actualSourceData.PicPath || "";
                                    baseData.PriorityShippingOvId = actualSourceData.PriorityShippingOvId || 0;
                                    // 保留原有的ProId和VariantsId（如果有的话）
                                    baseData.ProId = actualSourceData.ProId || "";
                                    baseData.VariantsId = actualSourceData.VariantsId || "";

                                }

                                $extAry[$index] = baseData;

                                if (typeof (products_obj.data_init.ext_save_data[id]) == 'undefined') {
                                    // 记录到保存数据(仓库)
                                    products_obj.data_init.ext_save_data[id] = {};
                                }
                                if (typeof (products_obj.data_init.ext_save_data[id][$attrName]) == 'undefined') {
                                    // 记录到保存数据(属性)
                                    products_obj.data_init.ext_save_data[id][$attrName] = {};
                                }

                                // 强制更新保存数据，确保复制的数据能正确显示
                                products_obj.data_init.ext_save_data[id][$attrName][$optionName] = $extAry[$index];

                                ++$index;
                            }
                        }
                        products_obj.data_init.ext_attr_data[1][id] = $extAry;

                    }

                }

                // 确保为所有选中的仓库创建DOM tbody
                products_obj.function_init.ensureAllWarehouseTbodies(changeData.show);

                products_obj.function_init.attr_price_show();

                let type = products_obj.data_init.specification;
                if (type == 0 && $.isEmptyObject(products_obj.data_init.warehouse_name_data) === false) {
                    // 单规格(仓库)
                    $('.box_attribute_tab_menu .item:eq(0)').click();
                }
            }
            // 隐藏仓库
            if (changeData.hide.length > 0) {
                $(changeData.hide).each(function (index, element) {
                    // 多规格 - 只删除仓库相关的组合数据，不删除属性选项
                    let $listObj = $("#AttrId_1 tbody[data-id=\"" + element + "\"] .input_checkbox_box");
                    if ($listObj.length) {
                        // 使用安全的删除方法，只删除组合行，保留属性选项
                        products_obj.function_init.safe_delete_warehouse_combination($listObj, element);
                    }
                    // 单规格(仓库)
                    $("#attribute_unit_box tr[data-type=\"warehouse\"][data-id=" + element + "]").attr("data-disabled", 1);
                });
            }
            // 显示数据
            if (changeData.current == 1 || $(".warehouse_filter .box_warehouse.current").length == 0) {
                $(".warehouse_filter li[data-type='warehouse']:not('.hide'):eq(0)>a").click();
            }
            if ($(".warehouse_filter li[data-type='warehouse']:not('.hide')").length == 0) {
                // 仓库选项全都隐藏起来
                $(".warehouse_filter .btn_warehouse:eq(0)").click();
            }
            // 关闭弹窗
            $("#fixed_right .btn_cancel").click();
        });
        //******************************** 数据提交 End ********************************

        //复制产品选择框
        products_obj.copy_box_init();

        // 视频SEO
        products_obj.products_video_init();

        /******************************************** 同步产品编辑 Start ********************************************/
        if ($('#sync_product_hidden').length) {
            products_obj.function_init.sync_product_attribute(0);
        }
        /******************************************** 同步产品编辑 End ********************************************/
    },

    category_init: function () {
        frame_obj.select_all($('.mock_table input[name=select_all]'), $('.mock_table input[name=select]'), $('.mock_table .table_menu_button .del')); // 批量操作
        frame_obj.del_bat($('.mock_table .table_menu_button .del'), $('.mock_table input[name=select]'), '/manage/products/category/delete-batch', function (id_list) {
            $.post('/manage/products/category/check-category-join-nav', {'CIds': id_list.split('-')}, function (result) {
                let confirm_txt = lang_obj.global.del_confirm,
                    extHtml = '';

                $.each(id_list.split('-'), function (k, v) {
                    if ($('[data-child="' + v + '"]').length) confirm_txt = lang_obj.manage.products.category.delCate
                })

                if (result.ret == 0 && result.msg.menuCount > 0) {
                    extHtml = '<p style="display: block;margin-top: 20px;color: #333;">' + lang_obj.manage.products.category.delCateWithNav + '</p>'
                }

                let params = {
                    'title': confirm_txt,
                    'extHtml': extHtml,
                    'confirmBtn': lang_obj.global.del,
                    'confirmBtnClass': 'btn_warn'
                };
                global_obj.win_alert(params, function () {
                    $.get('/manage/products/category/delete-batch', {id: id_list}, function (data) {
                        if (data.ret == 1) {
                            window.location.reload();
                        } else if (data.ret == 0 && data.msg != '') {
                            global_obj.win_alert_auto_close(data.msg, 'fail', 1000, '8%');
                        }
                    }, 'json');
                }, 'confirm');
            }, 'json')
        }); // 批量删除

        // 后台显示已下架的分类
        frame_obj.switchery_checkbox()
        frame_obj.fixed_right($('.btn_manage_category_show'), '.box_manage_category_show');
        frame_obj.submit_form_init($('.form_manage_category_show'));

        // 分类下架
        frame_obj.fixed_right($('.btn_soldout_category'), '.box_soldout_category', '', function (obj) {
            let cateid = obj.attr('data-id');
            $('.box_soldout_category input[name=id]').val(cateid)
            if ($('.box_soldout_category .sold_product .input_checkbox_box.checked').length) {
                $('.box_soldout_category .sold_product .input_checkbox_box.checked').trigger('click')
            }
            $('.box_soldout_category .products_select .input_radio_box:eq(0)').trigger('click')
        });
        frame_obj.submit_form_init($('.form_soldout_category'));
        $('.box_soldout_category .sold_product .input_checkbox_box').click(function () {
            if ($(this).hasClass('checked')) {
                $('.box_soldout_category .sold_product .products_select').fadeOut();
            } else {
                // $('.box_soldout_category .sold_product .products_select').fadeIn();
            }
        });
        $('.box_soldout_category .out_nav .input_checkbox_box').click(function () {
            if ($(this).hasClass('checked')) {
                $('.box_soldout_category .out_nav .products_select').fadeOut();
            }
        });

        // 分类上架
        frame_obj.fixed_right($('.btn_soldin_category'), '.box_soldin_category', '', function (obj) {
            let cateid = obj.attr('data-id');
            $('.box_soldin_category input[name=id]').val(cateid)
            if ($('.box_soldin_category .sold_product .input_checkbox_box.checked').length) {
                $('.box_soldin_category .sold_product .input_checkbox_box.checked').trigger('click')
            }
            $('.box_soldin_category .products_select .input_radio_box:eq(0)').trigger('click')
        });
        frame_obj.submit_form_init($('.form_soldin_category'));
        $('.box_soldin_category .sold_product .input_checkbox_box').click(function () {
            if ($(this).hasClass('checked')) {
                $('.box_soldin_category .sold_product .products_select').fadeOut();
            } else {
                // $('.box_soldin_category .sold_product .products_select').fadeIn();
            }
        });

        // 元素拖动
        frame_obj.dragsort($('#category .mock_table .tbody'), '/manage/products/category/order', '.first_box>.tr .myorder', '.c_id', '<div class="first_box"></div>');
        frame_obj.dragsort($('#category .mock_table .second_box'), '/manage/products/category/order', '.second_item>.tr .myorder', '.c_id', '<div class="second_item"></div>');
        frame_obj.dragsort($('#category .mock_table .third_box'), '/manage/products/category/order', '.tr .myorder', '.c_id', '<div class="tr"></div>');

        $('#category .mock_table').on('click', '.nav_ext', function () {
            $(this).toggleClass('current')
            $(this).parents('.tr').next('div').slideToggle('fast')
        })

        frame_obj.fixed_right($('.mock_table .btn_products_transfer'), '.box_products_transfer', function ($this) {
            $('.box_products_transfer .box_drop_double .box_select input').val('');
            $('.box_products_transfer .box_drop_double .box_select span').text(lang_obj.global.selected);
            $('.box_products_transfer input[name=CateId]').val($this.attr('data-cateid'));
            $('.box_products_transfer .cur_category').text($this.attr('data-name'));
        }, function ($this) {
            $('.box_products_transfer').attr('data-width', '360');
            if ($this.hasClass('disabled')) return false;
        });

        $('.box_category_search .search_box form').submit(function () {
            $.post('/manage/products/category/search-category', $('.box_category_search .search_box form').serialize(), function (data) {
                if (data.ret == 1) {
                    if (data.msg) {
                        $('.box_category_search .search_con_table').show();
                        $('.box_category_search .bg_no_table_data').hide();
                    } else {
                        $('.box_category_search .search_con_table').hide();
                        $('.box_category_search .bg_no_table_data').show();
                    }
                    $('.box_category_search .search_con_table tbody').html(data.msg);
                    //重新绑定勾选事件
                    $('.box_category_search .search_con_table thead tr').removeClass('current').find('.btn_checkbox').removeClass('current indeterminate').find('input:checkbox').prop('checked', false);
                    $('.box_category_search .search_con_table thead tr .open').attr('class', 'open');
                    frame_obj.offBtnCheckbox($('.search_con_table .btn_checkbox, .search_con_table .btn_choice'));
                    frame_obj.btnCheckbox($('.search_con_table .btn_checkbox, .search_con_table .btn_choice'));
                    frame_obj.select_all($('.search_con_table input[name=select_all]'), $('.search_con_table input[name=select]'), $('.search_con_table .table_menu_button .del')); // 批量操作
                    frame_obj.del_bat($('.search_con_table .table_menu_button .del'), $('.search_con_table input[name=select]'), '/manage/products/category/delete-batch', function (id_list) {
                        let confirm_txt = lang_obj.global.del_confirm
                        $.each(id_list.split('-'), function (k, v) {
                            if ($('[data-child="' + v + '"]').length) confirm_txt = lang_obj.manage.products.category.delCate
                        })
                        let params = {
                            'title': confirm_txt,
                            'confirmBtn': lang_obj.global.del,
                            'confirmBtnClass': 'btn_warn'
                        };
                        global_obj.win_alert(params, function () {
                            $.get('/manage/products/category/delete-batch', {id: id_list}, function (data) {
                                if (data.ret == 1) {
                                    window.location.reload();
                                } else if (data.ret == 0 && data.msg != '') {
                                    global_obj.win_alert_auto_close(data.msg, 'fail', 1000, '8%');
                                }
                            }, 'json');
                        }, 'confirm');
                    }); // 批量删除
                    frame_obj.fixed_right_multi_plus($('.search_con_table .btn_products_transfer'), '.box_products_transfer', function ($this) {
                        $('.box_products_transfer .box_drop_double .box_select input').val('');
                        $('.box_products_transfer .box_drop_double .box_select span').text(lang_obj.global.selected);
                        $('.box_products_transfer input[name=CateId]').val($this.attr('data-cateid'));
                        $('.box_products_transfer .cur_category').text($this.attr('data-name'));
                        frame_obj.submit_form_init($('.fixed_right_multi .form_products_transfer'), '/manage/products/category/transfer');
                    }, function ($this) {
                        $('.box_products_transfer').attr('data-width', '600');
                        if ($this.hasClass('disabled')) return false;
                    });
                }
            }, 'json');
            return false;
        });
        frame_obj.fixed_right($('.btn_category_search'), '.box_category_search', function ($this) {
        });

        frame_obj.submit_form_init($('.form_products_transfer'), '/manage/products/category/transfer');
        $('.icon_sub.disabled').click(function () {
            return false
        });
    },

    category_edit_init: function () {
        const INSTANCE = {
            render: {
                form: $('#edit_form'),
                inside: $('#category_inside'),
                proBox: $('.category_products_box'),
                products: $('.products_box')
            }
        }
        const PRODUCT_LIMIT = 20
        var categoryData = {
            products: {manual: [], intelligent: [], fromSubcategory: []},
            addMethod: ''
        }
        var proListObj = {}
        // 初始化产品相关数组变量
        var productsList = []
        var productsAllImageList = {}
        var productsImageList = []
        var _addProImageAry = []
        // 图片上传
        frame_obj.mouse_click($('#PicDetail.multi_img .upload_btn, #PicDetail .pic_btn .edit'), 'pro', function ($this) {
            // 产品主图点击事件
            frame_obj.photo_choice_init('PicDetail', '', 1)
        });
        if (global_obj.query_get('goSort')) {
            $("#category_inside").animate({scrollTop: $('#category_inside .category_products_box').offset().top + 382}, 1000);
        }

        frame_obj.multi_lang_show_all('#edit_form')
        frame_obj.multi_lang_show_item('#edit_form')

        // 修改 SEO 关键词
        frame_obj.fixed_right($('#edit_keyword'), '.fixed_edit_keyword', function ($this) {
            let $id = $('input[name=CateId]').val()
            frame_obj.seo_edit_keyword({
                'do_action': '/manage/action/seo-keyword-select',
                'Type': 'products_category',
                'field': 'CateId',
                'Id': $id
            })
        })
        frame_obj.seo_keyword_form_submit()
        if (typeof (CKEDITOR) == 'object') {
            for (i in shop_config.language) {
                var lang = shop_config.language[i],
                    id = 'Description_' + lang,
                    _seoVal = $('[name="SeoDescription_' + lang + '"]').val();
                CKEDITOR.instances[id].on('change', function () {
                    var $str = $(this)[0].getData();
                    if ($str) {
                        $str = $str.replace(/(<[^>]+>)|(&nbsp;)/g, '') //去掉html
                            .replace(/(^\s*)|(\s*$)/g, '') //去掉前后空格
                            .replace(/(\s+)|([\r\n])|([\r])|([\n])/g, ' ') //多个连续空格和换行替换成一个空格
                            .slice(0, 254); //截取255个字符
                    }
                    if ($(this)[0].name && $('#' + id).data('change') == 1 && _seoVal == '') {
                        $('[name="SeoDescription_' + $(this)[0].name.replace('Description_', '') + '"]').val(global_obj.htmlspecialchars_decode($str));
                    }
                });
            }
        }
        if (typeof (tinymce) == 'object') {
            for (i in shop_config.language) {
                var lang = shop_config.language[i],
                    id = 'Description_' + lang,
                    _seoVal = $('[name="SeoDescription_' + lang + '"]').val();
                tinymce.editors[id].on('change', function () {
                    var $str = $(this)[0].getContent();
                    if ($str) {
                        $str = $str.replace(/(<[^>]+>)|(&nbsp;)/g, '') //去掉html
                            .replace(/(^\s*)|(\s*$)/g, '') //去掉前后空格
                            .replace(/(\s+)|([\r\n])|([\r])|([\n])/g, ' ') //多个连续空格和换行替换成一个空格
                            .slice(0, 254); //截取255个字符
                    }
                    if ($('#' + id).data('change') == 1 && _seoVal == '') {
                        $('[name="SeoDescription_' + lang + '"]').val(global_obj.htmlspecialchars_decode($str));
                    }
                });
            }
        }

        // 复制链接
        var clipboard = new ClipboardJS('.btn_copy')
        clipboard.on('success', function (e) {
            alert(lang_obj.global.copy_complete)
        })

        // SEO相关事件
        $('input[name=PageUrl]').on('keyup', function (e) {
            var $Key = window.event ? e.keyCode : e.which,
                $Value = $.trim($(this).val());
            if ($Key == 8 && $Value == '') {
                // 退格键 (不允许为空)
                $(this).val($('.left_container .global_container:eq(0) .rows:eq(0) .multi_lang .lang_txt:eq(0) input').val().replace(/\s+/g, '-'))
            }
        })
        $('textarea[name=PageUrl]').on('keyup', function () {
            let $prefix = $('.prefix_textarea .prefix').text()
            let $value = $(this).val()
            let $Url = $prefix + $value
            $(this).parents('.custom_row').find('.btn_copy').attr('data-clipboard-text', $Url)
        })

        function setProImageAryFunc() {
            _addProImageAry = [];
            for (i in productsList) {
                _addProImageAry.push(productsAllImageList[productsList[i]]);
            }
            productsImageList = _addProImageAry;
        }

        function getProductsList() {
            let $this = $('.my_order_box .drop_down .item .current')
            let $page = parseInt(proListObj.attr('data-page'))
            let $tbodyObj = proListObj.find('tbody')
            let $cateid = parseInt($('input[name="CateId"]').val())
            let $order = $this.attr('data-order')
            let $where = productsList.slice($page * PRODUCT_LIMIT, ($page + 1) * PRODUCT_LIMIT)
            let $maxPage = Math.ceil(parseInt(productsList.length) / PRODUCT_LIMIT)
            if ($page >= $maxPage) {
                // 删除产品时 如果最后一页没有产品了 往前推一页
                $page = $maxPage - 1
                if ($page < 0) $page = 0
                $where = productsList.slice($page * PRODUCT_LIMIT, ($page + 1) * PRODUCT_LIMIT)
                proListObj.attr('data-page', $page)
            }
            if ($order == 'custom_sort') {
                // 手动排序的时候显示拖动图标
                proListObj.addClass('custom_sort')
            } else {
                proListObj.removeClass('custom_sort')
            }
            proListObj.find('.table_menu_button .open>span').text(0)
            $.post('/manage/products/category/get-products-list?page=' + ($page + 1), {
                    'id': $cateid,
                    'order': $order,
                    'where': $where,
                    'totalCount': productsList.length,
                    'pageSize': PRODUCT_LIMIT
                },
                function (data) {
                    $tbodyObj.html(data.msg.html)
                    afterLoadHtml(proListObj, data)
                    proListObj.removeAttr('disabled')
                    if (proListObj.find('.item').length) {
                        INSTANCE.render.proBox.find('.box_drop_down_menu').show()
                        proListObj.find('.r_con_table').show()
                        proListObj.find('.no_data').hide()
                    } else {
                        INSTANCE.render.proBox.find('.box_drop_down_menu').hide()
                        proListObj.find('.r_con_table').hide()
                        proListObj.find('.no_data').show()
                    }
                },
                'json'
            )
        }

        function getSortList($this) {
            let $cateid = parseInt($('input[name="CateId"]').val())
            let $tbodyObj = proListObj.find('tbody')
            let $order = $this.attr('data-order')
            let $page = 0
            if ($('input[name="OrderType"]').val() == $order && !$this.hasClass('addpro')) return false
            if ($this.hasClass('addpro')) {
                // 添加产品时，保持当前页码不变
                $page = parseInt(proListObj.attr('data-page'))
                $this.removeClass('addpro')
            }
            $('input[name="OrderType"]').val($order)
            proListObj.attr('data-order', $order)
            $('.my_order_box .order_type').text($this.text())
            $('.my_order_box .drop_down .item a').removeClass('current')
            $this.addClass('current')
            if ($order == 'custom_sort') {
                // 手动排序的时候显示拖动图标
                proListObj.addClass('custom_sort')
                return false
            } else {
                proListObj.removeClass('custom_sort')
            }
            if ($this.attr('disabled') == 'disabled') return false
            $where = productsList
            $this.attr('disabled', 'disabled')
            $.post('/manage/products/category/get-products-list?page=' + ($page + 1), {
                    'id': $cateid,
                    'order': $order,
                    'where': $where,
                    'totalCount': productsList.length,
                    'pageSize': PRODUCT_LIMIT
                }, function (data) {
                    $tbodyObj.html(data.msg.html)
                    afterLoadHtml(proListObj, data)
                    productsList = data.msg.productsList
                    setProImageAryFunc();
                    proListObj.attr('data-page', $page)
                    if (proListObj.find('.item').length) {
                        INSTANCE.render.proBox.find('.box_drop_down_menu').show()
                        proListObj.find('.r_con_table').show()
                        proListObj.find('.no_data').hide()
                    } else {
                        INSTANCE.render.proBox.find('.box_drop_down_menu').hide()
                        proListObj.find('.r_con_table').hide()
                        proListObj.find('.no_data').show()
                    }
                    $this.removeAttr('disabled')
                },
                'json'
            )
        }

        function afterLoadHtml(listObj, data) {
            listObj.find('thead tr').removeClass('current').find('.btn_checkbox').removeClass('current indeterminate').find('input:checkbox').prop('checked', false)
            listObj.find('thead tr .open').attr('class', 'open')
            frame_obj.offBtnCheckbox()
            frame_obj.btnCheckbox()
            frame_obj.select_all($('input[name=select_all]'), $('input[name=select]'))
            listObj.find('tbody').dragsort('destroy')
            frame_obj.dragsort(listObj.find('tbody'), '', '.item .order_move', '', '<tr class="item placeHolder"><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr>', '', function () {
                let $page = parseInt(listObj.attr('data-page'))
                let count = $page * PRODUCT_LIMIT
                listObj.find('input[name^=ProId]').each(function (index, element) {
                    productsList[(count + index)] = $(this).val()
                })
                setProImageAryFunc();
            })

            // 排序移动到上下页顶部
            listObj.find('.move_next_top, .move_prev_top').removeClass('disabled')
            let $page = parseInt(listObj.attr('data-page'))
            let $maxPage = Math.ceil(parseInt(productsList.length) / PRODUCT_LIMIT)
            if ($page == 0) listObj.find('.move_prev_top').addClass('disabled')
            if ($page + 1 >= $maxPage) listObj.find('.move_next_top').addClass('disabled')

            let pageHtml = ''
            if (data.msg.productsList.length > 0) pageHtml = data.msg.turn_page
            listObj.find('.cate_pro_page').html(pageHtml)
        }

        // 初始化
        categoryData.addMethod = $('.add_method .input_radio_box.checked input[name=AddMethod]').val()
        proListObj = INSTANCE.render.proBox.find(`.products_list[data-method="${categoryData.addMethod}"]`)

        // 同步全局和局部的productsList
        if (typeof window.productsList !== 'undefined' && Array.isArray(window.productsList) && window.productsList.length > 0) {
            productsList.length = 0;
            productsList.push(...window.productsList);
        }

        if (productsList.length) {
            categoryData.products[categoryData.addMethod] = productsList
            setTimeout(()=>{
                getProductsList()
            },100)
        }

        // 产品添加方式
        $('.add_method .input_radio_box').click(function () {
            let $value = $(this).find('input').val()
            proListObj = INSTANCE.render.inside.find(`.products_list[data-method="${$value}"]`)
            if ($value == 'intelligent') {
                $('.products_add_btn').addClass('hide')
                $('.condition_add_btn').removeClass('hide')
                if (proListObj.find('.item').length == 0) $('.condition_add_btn').trigger('click')
                $('.filter_condition_box .item_box .box_seach_select input').attr('notnull', 'notnull')
                proListObj.find('.cate_pro_page').show()
                proListObj.find('.products_to_more').hide()
            } else if ($value == 'manual') {
                $('.products_add_btn').removeClass('hide')
                $('.condition_add_btn').addClass('hide')
                $('.filter_condition_box .item_box .box_seach_select input').removeAttr('notnull')
            } else {
                $('.products_add_btn').addClass('hide')
                $('.condition_add_btn').addClass('hide')
            }

            // 切换当前的排序方式
            let $order = proListObj.attr('data-order')
            let $orderObj = $(`.my_order_box .drop_down .item a[data-order="${$order}"]`)
            $('input[name="OrderType"]').val($order)
            $('.my_order_box .order_type').text($orderObj.text())
            $('.my_order_box .drop_down .item a').removeClass('current addpro')
            $orderObj.addClass('current addpro')
            if ($order == 'custom_sort') {
                // 手动排序的时候显示拖动图标
                proListObj.addClass('custom_sort')
            } else {
                proListObj.removeClass('custom_sort')
            }
            if (proListObj.find('.item').length) {
                INSTANCE.render.proBox.find('.box_drop_down_menu').show()
                proListObj.find('.r_con_table').show()
                proListObj.find('.no_data').hide()
            } else {
                INSTANCE.render.proBox.find('.box_drop_down_menu').hide()
                proListObj.find('.r_con_table').hide()
                proListObj.find('.no_data').show()
            }

            proListObj.addClass('current').siblings().removeClass('current')
            categoryData.addMethod = $value
            productsList = categoryData.products[categoryData.addMethod]
        })

        // 关联产品 (智能添加)
        $('.filter_condition_box').on('click', '.btn_add_condition', function () {
            // 添加条件
            let $html = $('.filter_condition_box .temp_item').html()
            let $num = -1
            if ($('.filter_condition_box .item_box .item').length) {
                $num = parseInt($('.filter_condition_box .item_box .item:last').attr('data-num').replace('_', ''))
            }
            if ($('.filter_condition_box .item_box .item').length >= 20) {
                global_obj.win_alert_auto_close(lang_obj.manage.products.category.maxcon)
                return false
            }
            $(this).find('span').text($('.filter_condition_box .item_box .item').length + 1)
            $html = $html.replace(/XXX/g, '_' + ($num + 1))
            $('.filter_condition_box .item_box').append($html)
            $('.filter_condition_box .item_box .box_seach_select input').attr('notnull', 'notnull')
        }).on('click', '.i_del', function () {
            // 删除条件
            if ($('.filter_condition_box .item_box .item').length > 1) {
                let $item = $(this).parents('.item')
                $item.fadeOut(500, function () {
                    $item.remove()
                    $('.filter_condition_box .btn_add_condition span').text($('.filter_condition_box .item_box .item').length)
                })
            } else {
                global_obj.win_alert_auto_close(lang_obj.manage.products.category.mincon)
            }
        }).on('change', '.FilterConditionType', function () {
            // 更改条件类型
            let $parent = $(this).parents('.item')
            let $calc = $.evalJSON($(this).find('option:selected').attr('data-calc'))
            $parent.find('.FilterConditionCalc option').hide().removeAttr('selected')
            $.each($calc, function (k, v) {
                $parent.find('.FilterConditionCalc option[value="' + v + '"]').show()
                if (k == 0) $parent.find('.FilterConditionCalc option[value="' + v + '"]').prop('selected', true)
            })
        })

        // 排序方式
        $('.my_order_box .drop_down .item a').click(function () {
            getSortList($(this))
        })

        // 添加产品
        $('#category_inside').on('click', '.products_add_btn, .category_products_box .btn_add_item', function () {
            let params = {iframeTitle: lang_obj.manage.view.select_products, type: 'manual', value: {}, valueOrder: []};
            let value = {};
            let order = [];
            let excludeDate = [];

            $.each(productsList, function (k, v) {
                let image = productsImageList[k];
                value[v] = {image: image};
                order.push(v);
                excludeDate.push(v);
            });

            params.value = value;
            params.valueOrder = order;
            params.isOrder = false;
            params.excludeValue = excludeDate;

            frame_obj.products_choice_iframe_init_v2({
                params: params,
                onSubmit: function (data) {
                    if (!$.isEmptyObject(data.value)) {
                        let _addProAry = [];
                        for (i in data.value) {
                            _addProAry.push(data.value[i].proid)
                        }
                        for (i in productsList) {
                            if ($.inArray(parseInt(productsList[i]), _addProAry) < 0) {
                                $('.del_pro_box').append('<input type="hidden" name="DelProId[]" value="' + parseInt(productsList[i]) + '">');
                            }
                        }
                        categoryData.products.manual = productsList;
                        let _mergeData = _addProAry.concat(categoryData.products.manual);
                        productsList = _mergeData;
                        setProImageAryFunc();
                        let OrderType = $('input[name=OrderType]').val()
                        if (OrderType == 'custom_sort') {
                            getProductsList()
                        } else {
                            $('.my_order_box .drop_down .item a[data-order=' + OrderType + ']').addClass('addpro')
                            getSortList($('.my_order_box .drop_down .item a[data-order=' + OrderType + ']'))
                        }
                        global_obj.win_alert_auto_close(lang_obj.global.add_success, '', 1000, '8%')
                    } else {
                        for (i in productsList) {
                            $('.del_pro_box').append('<input type="hidden" name="DelProId[]" value="' + parseInt(productsList[i]) + '">');
                        }
                        productsList = [];
                        $('.products_list table tbody').html('')
                        getProductsList()
                        setProImageAryFunc();
                        global_obj.win_alert_auto_close(lang_obj.manage.error.no_prod_data, 'await', 1000, '8%')
                    }
                }
            });
        })

        // 添加筛选条件
        frame_obj.fixed_right($('.condition_add_btn'), '.intelligent_box', function ($this) {
            frame_obj.formMigration($('.filter_condition_box_submit'), $('.filter_condition_box .item_box'))
            $('.intelligent_box input[name=FilterCondition]').each(function () {
                if ($(this).val() == INSTANCE.render.form.find('input[name=FilterCondition]').val()) {
                    $(this).prop('checked', true).parent().parent().addClass('checked')
                } else {
                    $(this).prop('checked', false).parent().parent().removeClass('checked')
                }
            })
            if ($('.filter_condition_box .item_box .item').length < 1) $('.filter_condition_box .btn_add_condition').trigger('click')
            $('.filter_condition_box .btn_add_condition span').text($('.filter_condition_box .item_box .item').length)
        })

        // 关联产品提交 (智能添加)
        frame_obj.submit_form_init($('#intelligent_form'), '', '', '', function (data) {
            frame_obj.formMigration($('.filter_condition_box .item_box'), $('.filter_condition_box_submit'))
            INSTANCE.render.form.find('input[name=FilterCondition]').val($('.intelligent_box input[name=FilterCondition]:checked').val())

            if (data.ret == 1) {
                categoryData.products.intelligent = data.msg
                productsList = data.msg
                let OrderType = $('input[name=OrderType]').val()
                proListObj.attr('data-page', 0)
                setProImageAryFunc();
                if (OrderType == 'custom_sort') {
                    getProductsList()
                } else {
                    $('.my_order_box .drop_down .item a[data-order=' + OrderType + ']').addClass('addpro')
                    getSortList($('.my_order_box .drop_down .item a[data-order=' + OrderType + ']'))
                }
            } else if (data.ret == 2) {
                productsList = []
                setProImageAryFunc();
                $('.products_to_more').show()
                proListObj.find('.r_con_table').hide().find('tbody').html('')
                proListObj.find('.cate_pro_page').hide()
                proListObj.find('.no_data').hide()
            }

            $('.intelligent_box .close').click()
        });

        INSTANCE.render.products.on('click', '.pagination li a',
            function () {
                // 翻页
                let $this = $(this)
                let $page = $this.attr('data-page')
                let $fixedLoad = $('.fixed_loading')
                $fixedLoad.fadeIn()
                proListObj.attr('data-page', $page)
                window.history.pushState(null, null, location.href.replace(/&page=([0-9]*)/, '') + '&page=' + $page)
                getProductsList()
                $fixedLoad.fadeOut()
                return false
            }
        ).on('click', '.p_top',
            function () {
                // 手动排序产品置顶
                let $obj = $(this).parents('.item')
                let $page = parseInt(proListObj.attr('data-page'))

                // 获取要置顶的产品ID
                let productId = $obj.find('input[name^=ProId]').val();

                // 在productsList数组中找到正确的索引
                let $index = -1;
                for (let i = 0; i < productsList.length; i++) {
                    if (productsList[i] == productId) {
                        $index = i;
                        break;
                    }
                }

                if (proListObj.attr('disabled')) return false;
                proListObj.attr('disabled', 'disabled');

                // 如果找到了正确的索引，则删除并置顶
                if ($index !== -1) {
                    productsList.splice($index, 1)
                }
                productsList.unshift(productId)
                getProductsList()
                setProImageAryFunc();
            }
        ).on('click', '.move_next_top, .move_prev_top',
            function () {
                // 排序移动到上下页顶部
                if (!$(this).hasClass('disabled')) {
                    let $page = parseInt(proListObj.attr('data-page'))
                    let $select = proListObj.find('input[name=select]')
                    let $moveList = $select.map(function () {
                        if ($(this).is(':checked')) return $(this).val()
                    }).get()
                    let $moveListIndex = $select.map(function () {
                        if ($(this).is(':checked')) {
                            $index = $page * PRODUCT_LIMIT + $(this).index('input[name=select]')
                            return $index
                        }
                    }).get().reverse()
                    $.each($moveListIndex, function ($k, $v) {
                        productsList.splice($v, 1)
                    })
                    if ($(this).hasClass('move_next_top')) $startIndex = ($page + 1) * PRODUCT_LIMIT
                    if ($(this).hasClass('move_prev_top')) $startIndex = ($page - 1) * PRODUCT_LIMIT
                    $.each($moveList, function ($k, $v) {
                        productsList.splice($k + $startIndex, 0, $v)
                    })
                    getProductsList()
                    setProImageAryFunc();
                }
            }
        ).on('click', '.p_del',
            function () {
                // 删除产品
                let $obj = $(this).parents('.item')
                let $page = parseInt(proListObj.attr('data-page'))

                // 获取要删除的产品ID
                let productId = parseInt($obj.find('input[name^=ProId]').val());

                // 在productsList数组中找到正确的索引
                let $index = -1;
                for (let i = 0; i < productsList.length; i++) {
                    if (parseInt(productsList[i]) === productId) {
                        $index = i;
                        break;
                    }
                }

                let params = {
                    'title': lang_obj.global.del_confirm,
                    'confirmBtn': lang_obj.global.del,
                    'confirmBtnClass': 'btn_warn'
                }
                global_obj.win_alert(params, function () {
                    let html = '<input type="hidden" name="DelProId[]" value="' + productId + '" />'
                    proListObj.find('.del_pro_box').append(html)

                    // 如果找到了正确的索引，则删除对应的数组元素
                    if ($index !== -1) {
                        productsList.splice($index, 1)
                    }

                    // 同时更新全局的window.productsList
                    if (typeof window.productsList !== 'undefined' && Array.isArray(window.productsList)) {
                        let globalIndex = window.productsList.indexOf(productId);
                        if (globalIndex !== -1) {
                            window.productsList.splice(globalIndex, 1);
                        }
                    }

                    $obj.remove()
                    getProductsList()
                    setProImageAryFunc();
                }, 'confirm')
            }
        ).on('click', '.batch_del_btn', function () {
            let _selectItem = INSTANCE.render.products.find('tbody .btn_checkbox.current');

            if (_selectItem.length == 0) {
                let selectParams = {
                    'title': lang_obj.global.del_dat_select,
                    'confirmBtnClass': 'btn_warn'
                }
                global_obj.win_alert(selectParams);
                return false;
            }

            let $page = parseInt(proListObj.attr('data-page'))
            let params = {
                'title': lang_obj.global.del_confirm,
                'confirmBtn': lang_obj.global.del,
                'confirmBtnClass': 'btn_warn'
            }
            global_obj.win_alert(params, function () {
                let deletedProductIds = []; // 收集要删除的产品ID

                _selectItem.each(function () {
                    let _thisObj = $(this).parents('.item')
                    let productId = parseInt(_thisObj.find('input[name^=ProId]').val());

                    // 在productsList数组中找到正确的索引
                    let _thisIndex = -1;
                    for (let i = 0; i < productsList.length; i++) {
                        if (parseInt(productsList[i]) === productId) {
                            _thisIndex = i;
                            break;
                        }
                    }

                    let html = '<input type="hidden" name="DelProId[]" value="' + productId + '" />'
                    proListObj.find('.del_pro_box').append(html)

                    // 如果找到了正确的索引，则删除对应的数组元素
                    if (_thisIndex !== -1) {
                        productsList.splice(_thisIndex, 1)
                    }

                    deletedProductIds.push(productId);
                    _thisObj.remove()
                })

                // 同时更新全局的window.productsList
                if (typeof window.productsList !== 'undefined' && Array.isArray(window.productsList)) {
                    deletedProductIds.forEach(function(productId) {
                        let globalIndex = window.productsList.indexOf(productId);
                        if (globalIndex !== -1) {
                            window.productsList.splice(globalIndex, 1);
                        }
                    });
                }

                getProductsList()
                setProImageAryFunc();
            }, 'confirm')
        })

        // 递增定时检测产品处理进度
        function incrementTimeout(i) {
            setTimeout(function () {
                $.post('/manage/products/category/get-loading-status', {'CateId': INSTANCE.render.form.find('input[name=CateId]').val()},
                    function (data) {
                        if (data.ret == 1) {
                            location.href = location.href
                        } else {
                            i = i * 2
                            incrementTimeout(i)
                        }
                    },
                    'json'
                )
            }, 1000 * i)
        }

        if ($('.fixed_btn_submit .box_loading').length) {
            incrementTimeout(8)
        }

        let Name = '',
            NameChangeStatus = true,	//分类名发生改变
            IsSaveStatus = false;		//点击保存后会发生改变

        if ($('input[name=CateId]').val() > 0) {
            Name = $('input[name=Category_en]').val();
        }
        // 提交保存
        frame_obj.submit_object_init(INSTANCE.render.form, '', function () {
            if (Name != $('input[name=Category_en]').val() && $('input[name=CateId]').val() > 0) {
                NameChangeStatus = false;
            }
            if (NameChangeStatus == false && IsSaveStatus == false) {
                let params = {
                    'title': lang_obj.manage.products.category_change_tips,
                    'confirmBtn': lang_obj.manage.products.sync_btn,
                };
                global_obj.win_alert(params, function () {
                    $('#edit_form').append('<input type="hidden" name="IsChangeMenu" value="1">');
                    NameChangeStatus = true;
                    IsSaveStatus = true;
                    $('.fixed_btn_submit').find('input[name=submit_button]').click();
                }, 'confirm', 0, 'success', function () {
                    NameChangeStatus = true;
                    IsSaveStatus = true;
                    global_obj.div_mask(1);
                    $('.fixed_btn_submit').find('input[name=submit_button]').click();
                });
            }
            if (NameChangeStatus == false && IsSaveStatus == false) return false;
        }, '', function (result) {
            global_obj.win_alert_auto_close(lang_obj.global.save_success, '', 500, '8%')
            setTimeout(function () {
                if (result.msg.jump) {
                    window.location = result.msg.jump
                } else {
                    window.location.reload()
                }
            }, 500)
        }, function (data) {
            // 修复ProId为空数组的问题：从多个来源获取产品ID
            let finalProductIds = [];

            // 方法1：使用当前的productsList变量
            if (productsList && productsList.length > 0) {
                finalProductIds = [...productsList];
            }
            // 方法2：从全局变量categoryProductIds获取
            else if (typeof window.categoryProductIds !== 'undefined' && window.categoryProductIds.length > 0) {
                finalProductIds = [...window.categoryProductIds];
            }
            // 方法3：从隐藏字段获取
            else {
                const productIdsStr = $('#productIdsList').val();
                if (productIdsStr) {
                    finalProductIds = productIdsStr.split(',').map(id => parseInt(id)).filter(id => id > 0);
                }
            }
            // 方法4：从表单中的隐藏输入框获取
            if (finalProductIds.length === 0) {
                const formProductIds = [];
                $('#edit_form input[name^="ProId"]').each(function() {
                    const value = parseInt($(this).val());
                    if (value > 0) {
                        formProductIds.push(value);
                    }
                });
                if (formProductIds.length > 0) {
                    finalProductIds = formProductIds;
                }
            }

            data.ProId = finalProductIds;
            return data
        })
    },

    products_video_init: function () {

        $('#PicDetail.pro_multi_img .img.video').find('.video_seo_btn').show();

        $videoType = $('#VideoType').val();
        if ($videoType == 'local') $('#PicDetail.pro_multi_img .img.video').find('.video_seo_btn').hide();

        frame_obj.fixed_right($('.video_seo_btn'), '.fixed_video_seo');

        frame_obj.submit_form_init($('#video_seo_form'), '', '', '', function (result) {
            let VideoTitleSeo = result.msg.VideoTitleSeo;
            let VideoDescriptionSeo = result.msg.VideoDescriptionSeo;
            let VideoPulishTimeSeo = result.msg.VideoPulishTimeSeo;

            $('#VideoTitleSeo').val(VideoTitleSeo);
            $('#VideoDescriptionSeo').val(VideoDescriptionSeo);
            $('#VideoPulishTimeSeo').val(VideoPulishTimeSeo);

            $('#video_seo_form').find('.btn_cancel').click();
        })

        $('#video_seo_form .input_time').daterangepicker({showDropdowns: true, singleDatePicker: true});//时间插件
    },

    products_sorting_init: function () {
        const INSTANCE = {
            form: $('#products_sorting_form'), // 产品表单
            searchForm: $('.form_sorting_search'), // 搜索表单
            body: $('#products_sorting'), // 最大的元素
            htmlBox: $('#products_sorting .html_products_list'), // html 内容替换区域
            products: $('#products_sorting .box_sorting'), // 产品区域
            order: $('#products_sorting .menu_products_order') // 排序模块
        }
        const PARAM = {
            'orderType': '', // 排序类型
            'productLimit': INSTANCE.products.attr('data-product-limit'), // 每页的数量
            'productMax': INSTANCE.products.attr('data-product-max'), // 显示的总数量
            'Search': false, // 搜索状态
            'Keyword': '' // 搜索关键词
        }
        const FUNC = {
            'init': () => {
                frame_obj.global_select_box()
                FUNC.pageTurning()
                FUNC.formSubmit()
                FUNC.searchProducts()
            },
            'pageTurning': () => {
                INSTANCE.products.on('click', '.pagination li a',
                    function () {
                        // 翻页
                        let $this = $(this)
                        let $page = $this.attr('data-page')
                        let $fixedLoad = $('.fixed_loading')
                        $fixedLoad.fadeIn()
                        if (PARAM.Search) {
                            // 搜索状态下
                            $.post($this.attr('href'), {
                                    'Keyword': PARAM.Keyword
                                },
                                function (data) {
                                    INSTANCE.htmlBox.html(data.msg.html)
                                    // 重新绑定事件
                                    FUNC.afterEvent()
                                },
                                'json'
                            )
                        } else {
                            // 正常情况下
                            INSTANCE.products.attr('data-page', $page)
                            FUNC.getProductsList()
                        }
                        $fixedLoad.fadeOut()
                        return false
                    }
                ).on('click', '.move_next_top, .move_prev_top, .move_first_top',
                    function () {
                        // 排序移动到上下页顶部
                        if (!$(this).hasClass('disabled')) {
                            let $page = parseInt(INSTANCE.products.attr('data-page'))
                            let $select = INSTANCE.products.find('input[name=select]')
                            let $moveList = $select.map(function () {
                                if ($(this).is(':checked')) return $(this).val()
                            }).get()
                            FUNC.fixedSubmit()
                            if (PARAM.Search) {
                                // 搜索状态下
                                $.each($moveList, function ($k, $v) {
                                    productsList.splice($k, 0, $v)
                                })
                                productsList = Array.from(new Set(productsList))
                                productsList = productsList.slice(0, PARAM.productMax)
                                global_obj.win_alert_auto_close(lang_obj.manage.products.category.firstTopSuccess, '', 3000, '8%')
                                return false
                            }
                            let $moveListIndex = $select.map(function () {
                                if ($(this).is(':checked')) {
                                    $index = $page * PARAM.productLimit + $(this).index('input[name=select]')
                                    return $index
                                }
                            }).get().reverse()
                            $.each($moveListIndex, function ($k, $v) {
                                productsList.splice($v, 1)
                            })
                            let alertMsg = ''
                            if ($(this).hasClass('move_next_top')) {
                                $startIndex = ($page + 1) * PARAM.productLimit
                                alertMsg = lang_obj.manage.products.category.nextTopSuccess
                            }
                            if ($(this).hasClass('move_prev_top')) {
                                $startIndex = ($page - 1) * PARAM.productLimit
                                alertMsg = lang_obj.manage.products.category.prevTopSuccess
                            }
                            if ($(this).hasClass('move_first_top')) {
                                $startIndex = 0
                                alertMsg = lang_obj.manage.products.category.firstTopSuccess
                            }
                            $.each($moveList, function ($k, $v) {
                                productsList.splice($k + $startIndex, 0, $v)
                            })
                            FUNC.getProductsList()
                            INSTANCE.products.find('.table_menu_button .open>span').text(0)
                            global_obj.win_alert_auto_close(alertMsg, '', 3000, '8%')
                        }
                    }
                )
                // 排序方式
                INSTANCE.order.find('.drop_down .item').click(function () {
                    FUNC.getSortList($(this))
                })
                // 默认加载
                FUNC.getProductsList()
            },
            'setProImageAryFunc': () => {
                _addProImageAry = [];
                for (i in productsList) {
                    _addProImageAry.push(productsAllImageList[productsList[i]]);
                }
                productsImageList = _addProImageAry;
            },
            'getProductsList': () => {
                // 加载分页产品
                let $page = parseInt(INSTANCE.products.attr('data-page'))
                let $order = INSTANCE.order.find('.drop_down .item.selected').attr('data-value')
                let $where = productsList.slice($page * PARAM.productLimit, ($page + 1) * PARAM.productLimit)
                let $maxPage = Math.ceil(parseInt(productsList.length) / PARAM.productLimit)
                if ($page >= $maxPage) {
                    // 删除产品时 如果最后一页没有产品了 往前推一页
                    $page = $maxPage - 1
                    if ($page < 0) $page = 0
                    $where = productsList.slice($page * PARAM.productLimit, ($page + 1) * PARAM.productLimit)
                    INSTANCE.products.attr('data-page', $page)
                }
                if ($order == 'custom') {
                    // 手动排序的时候显示拖动图标
                    INSTANCE.body.addClass('custom_sort')
                } else {
                    INSTANCE.body.removeClass('custom_sort')
                }
                $.post('/manage/products/category/products-sorting-list?page=' + ($page + 1), {
                        'order': $order,
                        'where': $where.join(','),
                        'totalCount': productsList.length,
                        'pageSize': PARAM.productLimit
                    },
                    function (data) {
                        INSTANCE.htmlBox.html(data.msg.html)
                        FUNC.afterLoadHtml(INSTANCE.products, data)
                    },
                    'json'
                )
            },
            'getSortList': ($this) => {
                // 切换排序方式重新加载
                let $order = $this.attr('data-value')
                let $page = 0
                if (PARAM.orderType == $order) return false
                PARAM.orderType = $order
                $('.my_order_box .order_type').text($this.text())
                FUNC.fixedSubmit()
                if ($order == 'custom') {
                    // 手动排序的时候显示拖动图标
                    INSTANCE.body.addClass('custom_sort')
                    return false
                } else {
                    INSTANCE.body.removeClass('custom_sort')
                }
                FUNC.exitSearch() // 退出搜索状态
                $where = productsList.join(',')
                if ($this.attr('disabled') == 'disabled') return false
                $this.attr('disabled', 'disabled')
                $.post('/manage/products/category/products-sorting-list?page=' + ($page + 1), {
                        'order': $order,
                        'where': $where,
                        'totalCount': productsList.length,
                        'pageSize': PARAM.productLimit
                    }, function (data) {
                        INSTANCE.htmlBox.html(data.msg.html)
                        FUNC.afterLoadHtml(INSTANCE.products, data)
                        productsList = data.msg.productsList
                        productsList = productsList.slice(0, PARAM.productMax)
                        INSTANCE.products.attr('data-page', $page)
                        $this.removeAttr('disabled')
                    },
                    'json'
                )
            },
            'afterLoadHtml': (listObj, data) => {
                // 加载完之后处理逻辑
                listObj.find('thead tr').removeClass('current').find('.btn_checkbox').removeClass('current indeterminate').find('input:checkbox').prop('checked', false)
                listObj.find('thead tr .open').attr('class', 'open')
                // 重新绑定事件
                FUNC.afterEvent()
                $('.box_sorting .box_products').dragsort('destroy')
                frame_obj.dragsort($('.box_sorting .box_products'), '', '.pitem .p_move', '', '<div class="item placeHolder"></div>', '', function () {
                    let $page = parseInt(listObj.attr('data-page'))
                    let count = $page * PARAM.productLimit
                    listObj.find('input[name^=ProId]').each(function (index, element) {
                        productsList[(count + index)] = $(this).val()
                    })
                    FUNC.fixedSubmit()
                })

                // 排序移动到上下页顶部
                listObj.find('.move_next_top, .move_prev_top').removeClass('disabled')
                let $page = parseInt(listObj.attr('data-page'))
                let $maxPage = Math.ceil(parseInt(productsList.length) / PARAM.productLimit)
                if ($page == 0) listObj.find('.move_prev_top').addClass('disabled')
                if ($page + 1 >= $maxPage) listObj.find('.move_next_top').addClass('disabled')
            },
            'afterEvent': (type) => {
                frame_obj.offBtnCheckbox()
                frame_obj.btnCheckbox()
                frame_obj.select_all($('input[name=select_all]'), $('input[name=select]'))
                INSTANCE.htmlBox.find('.p_top').click(function (e) {
                    // 手动排序产品置顶
                    e.stopPropagation()
                    let $obj = $(this).parents('.pitem')
                    let $page = parseInt(INSTANCE.products.attr('data-page'))
                    let $index = $page * PARAM.productLimit + $obj.index()
                    global_obj.win_alert_auto_close(lang_obj.manage.products.category.topSuccess, '', 3000, '8%')
                    FUNC.fixedSubmit()
                    // 获取要置顶的产品ID
                    let productId = $obj.find('input[name^=ProId]').val();

                    if (PARAM.Search) {
                        // 搜索状态置顶
                        productsList.unshift(productId)
                        productsList = Array.from(new Set(productsList))
                        productsList = productsList.slice(0, PARAM.productMax)
                        return false
                    }

                    // 在productsList数组中找到正确的索引
                    let correctIndex = -1;
                    for (let i = 0; i < productsList.length; i++) {
                        if (productsList[i] == productId) {
                            correctIndex = i;
                            break;
                        }
                    }

                    // 如果找到了正确的索引，则删除并置顶
                    if (correctIndex !== -1) {
                        productsList.splice(correctIndex, 1)
                    }
                    productsList.unshift(productId)
                    FUNC.getProductsList()
                })
            },
            'fixedSubmit': (type) => {
                if (type == 'hide') {
                    INSTANCE.form.find('.box_submit').removeClass('fixed_btn_submit')
                    INSTANCE.body.height(INSTANCE.body.height() + 66)
                } else {
                    if (!INSTANCE.form.find('.box_submit').hasClass('fixed_btn_submit')) {
                        INSTANCE.form.find('.box_submit').addClass('fixed_btn_submit')
                        INSTANCE.body.height(INSTANCE.body.height() - 66)
                    }
                }
            },
            'formSubmit': () => {
                // 提交保存
                frame_obj.submit_object_init(INSTANCE.form, '', '', '', function (result) {
                    global_obj.win_alert_auto_close(lang_obj.global.save_success, '', 500, '8%')
                    FUNC.fixedSubmit('hide')
                }, function (data) {
                    data.ProId = productsList
                    return data
                })
            },
            'searchProducts': () => {
                frame_obj.submit_form_init(INSTANCE.searchForm, '', '', '', function (data) {
                    // 搜索状态
                    PARAM.Search = true
                    PARAM.Keyword = INSTANCE.searchForm.find('input[name=Keyword]').val()
                    $('.search_box_selected').show().find('b').text(PARAM.Keyword)
                    INSTANCE.body.addClass('search_sort')
                    // 插入html
                    INSTANCE.htmlBox.html(data.msg.html)
                    // 重新绑定事件
                    FUNC.afterEvent()
                    // 不让操作上下页
                    INSTANCE.products.find('.move_prev_top').addClass('disabled')
                    INSTANCE.products.find('.move_next_top').addClass('disabled')
                })
                $('.search_box_selected i').click(function () {
                    // 退出搜索状态
                    FUNC.exitSearch()
                    // 重新加载
                    FUNC.getProductsList()
                })
            },
            'exitSearch': () => {
                // 退出搜索状态
                PARAM.Search = false
                INSTANCE.searchForm.find('input[name=Keyword]').val('')
                $('.search_box_selected').hide().find('b').text('')
                INSTANCE.body.removeClass('search_sort')
            }
        }
        FUNC.init()
    },

    business_global: {
        del_action: '',
        order_action: '',
        init: function () {
            frame_obj.del_init($('#business .r_con_table')); //删除提示
            frame_obj.select_all($('#business .r_con_table input[name=select_all]'), $('#business .r_con_table input[name=select]'), $('.table_menu_button .del')); //批量操作
            frame_obj.del_bat($('.list_menu .del'), $('#business .r_con_table input[name=select]'), products_obj.business_global.del_action); //批量删除
            /* 批量排序 */
            frame_obj.del_bat($('.list_menu .order'), $('#business .r_con_table input[name=select]'), function (id_list) {
                var $this = $(this),
                    $checkbox,
                    my_order_str = '';
                $('#business .myorder select').each(function (index, element) {
                    $checkbox = $(element).parents('tr').find(':checkbox');
                    if ($checkbox.length && $checkbox.get(0).checked) {
                        ;
                        my_order_str += $(element).val() + '-';
                    }
                });
                global_obj.win_alert(lang_obj.global.my_order_confirm, function () {
                    $.get('?', {
                        do_action: products_obj.business_global.order_action,
                        group_id: id_list,
                        my_order_value: my_order_str
                    }, function (data) {
                        if (data.ret == 1) {
                            window.location.reload();
                        }
                    }, 'json');
                }, 'confirm');
                return false;
            }, lang_obj.global.dat_select);
        }
    },

    business_category_init: function () {
        products_obj.business_global.del_action = 'products.business_category_del_bat';
        products_obj.business_global.order_action = 'products.business_category_my_order';
        products_obj.business_global.init();
    },

    business_category_edit_init: function () {
        frame_obj.submit_form_init($('#category_edit_form'), './?m=products&a=business&d=category');
    },

    business_init: function () {
        products_obj.business_global.del_action = 'products.business_del_bat';
        products_obj.business_global.order_action = 'products.business_my_order';
        products_obj.business_global.init();
        $('#business .r_con_column .switchery').click(function () {
            var $this = $(this);
            $.get('?', 'do_action=products.business_uesd', function (data) {
                if (data.ret == 1) {
                    if ($this.hasClass('checked')) {
                        $this.removeClass('checked');
                    } else {
                        $this.addClass('checked');
                    }
                } else {
                    global_obj.win_alert(lang_obj.global.set_error);
                }
            }, 'json');
        });
    },

    business_edit_init: function () {
        /* 资质证书上传 */
        frame_obj.mouse_click($('#ImgDetail .upload_btn, #ImgDetail .pic_btn .edit'), 'img', function ($this) { //点击上传图片
            frame_obj.photo_choice_init('ImgDetail', '', 1);
        });
        /* 合作凭证上传 */
        frame_obj.mouse_click($('#PicDetail .upload_btn, #PicDetail .pic_btn .edit'), 'img', function ($this) { //点击上传图片
            frame_obj.photo_choice_init('PicDetail', '', 1);
        });
        /* 提交 */
        frame_obj.submit_form_init($('#business_edit_form'), './?m=products&a=business');
    },

    review_init: function () {
        frame_obj.del_init($('#review .r_con_table, .review_box')); //删除提示
        frame_obj.select_all($('input[name=select_all]'), $('input[name=select]'), $('.table_menu_button .del')); //批量操作
        frame_obj.del_bat($('.table_menu_button .del'), $('input[name=select]'), '/api/ProductFeedback/DelProductFeedback'); //批量删除
        $('.review_box .switchery').click(function () {
            var $this = $(this),
                $rid = $this.attr('data-id'),
                $review = 0,
                $audit;
            if ($this.hasClass('checked')) {
                $audit = 0;
                $this.removeClass('checked');
            } else {
                $audit = 1;
                $this.addClass('checked');
            }
            if ($this.hasClass('review')) $review = 1;
            $.post('/api/ProductFeedback/ProductsReplyAudit', {
                'RId': $rid,
                'Audit': $audit,
                'Review': $review
            }, function () {
            }, 'json');
        });

        $('.review_number').on('click', function (event) {
            let $this = $(this);
            let RId = $this.data('id');
            if (!$this.find('.review_tit i').length) return false;
            $this.find('.review_box').html('');
            $.post('/manage/products/review/get-review-img-info', {'RId': RId}, function (data) {
                if (data.ret == 1) {
                    $this.find('.review_box').html(data.msg.html);
                    // 控制显示
                    let change = 0;
                    let boxHeight = $this.find('.review_container').height();
                    let clickHeight = event.currentTarget.offsetTop;
                    let tableHeight = $('.inside_table').height();
                    if (clickHeight + boxHeight > tableHeight) {
                        $this.find('.review_container').css({'top': 'auto', 'bottom': '100%'});
                        change = 1;
                    }
                    let thisPosition = clickHeight - parseInt($('.inside_table').css('padding-top')) - $('.inside_table .list_menu').height() - parseInt($('.box_table table').css('margin-top'));
                    if (change) {
                        thisPosition = thisPosition - 5;
                        if (boxHeight > thisPosition) {  // 产品框高度大于表格
                            $this.find('.review_box').css('max-height', thisPosition);
                        }
                    } else {
                        thisPosition = thisPosition + $this.height() + 25;
                        if (thisPosition + boxHeight > tableHeight) {  // 产品框高度大于表格
                            $this.find('.review_box').css('max-height', tableHeight - thisPosition);
                        }
                    }
                    $this.addClass('current').find('.review_container').fadeIn();
                }
            }, 'json');
        });
        $('.review_number').on('mouseleave', function () {
            $(this).removeClass('current').find('.review_container').fadeOut();
        });

        $('.r_con_table tbody td.operation .icon_publish, .r_con_table tbody td.operation .icon_publish_none').on('click', function () {
            var _this = $(this),
                RId = _this.attr('data-id'),
                Audit = _this.attr('data-audit');
            $.post('/api/ProductFeedback/ProductsReplyAudit', {'RId': RId, 'Audit': Audit}, function (data) {
                if (data.ret == 1) {
                    global_obj.win_alert_auto_close(data.msg.alert, '', 1000, '8%');
                    if (data.msg.audit == 1) {
                        _this.attr('data-audit', 0);
                        _this.addClass('icon_publish_none').removeClass('icon_publish').html(data.msg.text);
                        _this.parents('tr').find('.publish_status').addClass('publish_used').removeClass('publish_none').html(data.msg.publish);
                    } else {
                        _this.attr('data-audit', 1);
                        _this.addClass('icon_publish').removeClass('icon_publish_none').html(data.msg.text);
                        _this.parents('tr').find('.publish_status').removeClass('publish_used').addClass('publish_none').html(data.msg.publish);
                    }
                }
            }, 'json')
        })

        frame_obj.submit_form_init($('#review_edit_form'), '/manage/products/review/reply?id=' + $('input[name=RId]').val());
        frame_obj.submit_form_init($('#review_reply_form'), '/manage/products/review/reply?id=' + $('input[name=RId]').val());
    },

    aliexpress_sync_init: function () {
        var step_0 = function () {
            var ajaxTimeout = $.ajax({
                url: './?do_action=products.aliexpress_sync&step=0',
                type: 'get',
                timeout: 10000,
                dataType: 'json',
                success: function (data) {
                    $('.sync').html($('.sync').html() + data.msg.msg);
                    data.ret == 1 && step_1();
                },
                complete: function (XMLHttpRequest, status) {
                    if (status == 'timeout') {
                        ajaxTimeout.abort();
                        step_0();
                    }
                }
            });
        }
        var step_1 = function () {	//获取产品的基本资料
            var ajaxTimeout = $.ajax({
                url: './?do_action=products.aliexpress_sync&step=1',
                type: 'get',
                timeout: 10000,
                dataType: 'json',
                success: function (data) {
                    $('.sync').html($('.sync').html() + data.msg.msg).scrollTop(1000000);
                    if (data.ret == 1) {
                        data.msg.step == 2 ? step_2() : step_1();
                    }
                },
                complete: function (XMLHttpRequest, status) {
                    if (status == 'timeout') {
                        ajaxTimeout.abort();
                        step_1();
                    }
                }
            });
        }
        var step_2 = function () {	//获取产品的详细资料
            var ajaxTimeout = $.ajax({
                url: './?do_action=products.aliexpress_sync&step=2',
                type: 'get',
                timeout: 30000,
                dataType: 'json',
                success: function (data) {
                    $('.sync').html($('.sync').html() + data.msg.msg).scrollTop(1000000);
                    data.ret == 1 && step_2();
                },
                complete: function (XMLHttpRequest, status) {
                    if (status == 'timeout') {
                        ajaxTimeout.abort();
                        step_2();
                    }
                }
            });
        }
        $('.btn_ok').click(function () {
            $(this).prop('disabled', true);
            step_0();
        });
    },

    //库存管理
    stock_list_init: function () {
        frame_obj.select_all($('input[name=select_all]'), $('input[name=select]')); //批量操作
        frame_obj.check_amount($('#stock_list'));

        $(".search_form form").submit(function () {
            let $min = parseInt($(this).find("input[name=Min]").val()),
                $max = parseInt($(this).find("input[name=Max]").val());

            if ($min > $max) {
                global_obj.win_alert_auto_close(lang_obj.manage.products.tips.min_than_max, 'fail', 1000, '8%');
                return false;
            }
        });

        $('#stock_list .inside_menu').insideMenu();

        check_is_submit = function () {
            var $is_submit = 0;
            $('.fixed_stock_set select[name="SoldStatus"], .fixed_stock_set input[name="Stock"], .fixed_stock_set input[name="WarnStock"]').each(function () {
                if ($(this).val() != '') $is_submit = 1;
            });
            if ($is_checkBox != ($('.fixed_stock_set input[name="IsStockWarning"]:checked').length > 0 ? true : false)) {
                $is_submit = 1;
            }
            if ($is_submit == 1) {
                $('.fixed_stock_set input[type="submit"]').removeAttr('disabled');
            } else {
                $('.fixed_stock_set input[type="submit"]').attr('disabled', 'disabled');
            }
        }
        frame_obj.fixed_right($('#stock_list .btn_all_pro_set, #stock_list .btn_pro_set'), '.fixed_stock_set', '', function ($this) {
            var id_list = '';
            if ($this.hasClass('btn_pro_set')) {
                $('#stock_list input[name=select]').each(function (index, element) {
                    id_list += $(element).get(0).checked ? $(element).val() + ',' : '';
                });
                if (!id_list) {
                    global_obj.win_alert(lang_obj.global.dat_select);
                    return false;
                } else {
                    id_list = id_list.substring(0, id_list.lastIndexOf(','));
                }
            }
            $('#products_stock_set input[name="id_list"]').val(id_list);
            $('.fixed_stock_set .top_title span').text($this.text());
            $('.fixed_stock_set input[name="IsStockWarning"]').prop('checked', true).parents('.switchery').addClass('checked');
            $is_checkBox = $('.fixed_stock_set input[name="IsStockWarning"]:checked').length > 0 ? true : false;
            check_is_submit();
        });
        $('.fixed_stock_set input[name="Stock"], .fixed_stock_set input[name="WarnStock"]').keyup(function () {
            check_is_submit();
        });
        $('.fixed_stock_set select[name="SoldStatus"]').change(function () {
            check_is_submit();
        });
        $('.fixed_stock_set input[name="IsStockWarning"]').change(function () {
            check_is_submit();
        });
        frame_obj.submit_form_init($('#products_stock_set'), '', '', '', function (data) {
            global_obj.win_alert_auto_close(data.msg, 'success', 1000, '8%');
            $('.fixed_stock_set .close').trigger('click');
            setTimeout(function () {
                location.href = location.href;
            }, 300);
        });

        $('.r_con_table .mod_input').keyup(function () {
            saveBoxStatusInit();
        });
        $('.r_con_table input[type="checkbox"]').on('change', function () {
            saveBoxStatusInit();
        })

        let saveBoxStatusInit = function () {
            $save = 0;
            $('.r_con_table .mod_input').each(function () {
                if ($(this).val() != '') $save++;
            });

            windowStockWarningAry = $.evalJSON(stockWarningAry);
            $('.r_con_table input[name^=IsStockWarning]').each(function () {
                cid = parseInt($(this).parent().data('cid')),
                    value = $(this).prop('checked') ? parseInt($(this).val()) : 0;
                originValue = (typeof (windowStockWarningAry[cid]) == 'string') ? parseInt(windowStockWarningAry[cid]) : 0;
                if (originValue != value) {
                    $save++;
                }
            })
            if ($save) {
                $('.row_save_box').removeClass('none');
                $(window).resize();
            } else {
                $('.row_save_box').addClass('none');
                $(window).resize();
            }
        }

        frame_obj.submit_form_init($('#edit_form'), location.href);

        // 搜索筛选
        frame_obj.filterRight({
            "onSubmit": function ($obj) {
                // 分类
                let min = $.trim($obj.find("input[name=Min]").val());
                let max = $.trim($obj.find("input[name=Max]").val());
                // 仓库
                let ovid = [];
                $obj.find("input[name=ovid]:checked").each(function (index, element) {
                    ovid[index] = $(element).val();
                });
                ovid = ovid.join(",");

                $('.search_box input[name=Min]').val(min);
                $('.search_box input[name=Max]').val(max);
                $('.search_box input[name=ovid]').val(ovid);
            }
        });

        frame_obj.switchery_checkbox(function (obj) {
            openStockWarningInit(obj);
        }, function (obj) {
            openStockWarningInit(obj);
        });

        let openStockWarningInit = function (obj) {
            let id = parseInt(obj.data('cid'))
            value = obj.find('input:checked').val();
            nextObj = obj.parents('.flex_item').next();
            if (value == undefined) value = 0;
            if (id > 0) {
                if (value) {
                    nextObj.find('input').show().removeAttr('readonly');
                    nextObj.find('.warning_box span').hide();
                } else {
                    nextObj.find('input').hide().attr('readonly', 'readonly').val('').trigger('keyup');
                    nextObj.find('.warning_box span').show();
                }
            }
            if (value == 0) {
                $('.warn_stock_box').hide();
            } else {
                $('.warn_stock_box').show();
            }
            obj.find('input').trigger('change');
        }
        openStockWarningInit($('input[name="IsStockWarning"]'));
        $('.warn_stock_box').show();
    },

    copy_box_init: function () {

        $('.product_menu .btn_menu_copy , #products .icon_copy').click(function () {
            var $Url = $(this).data('url'),
                $Id = $(this).data('id');

            if ($('#copy_box_form').find('.input_checkbox_box').length == 0) {

                global_obj.win_alert(lang_obj.global.copy_confirm, function () {
                    window.location = $Url;
                }, 'confirm');

                return false;
            }

            $('#copy_box_form').find('.input_checkbox_box').show().removeClass('checked').find('input').prop('checked', false);
            $('#copy_box_form').find('input[name="copyId"]').val($Id);

            //检查复制权限
            $.post('/manage/products/products/check-copy-permit', {'ProId': $Id}, function (data) {
                if (data.ret == 1) {
                    $.each(data.msg, function (k, v) {
                        if (v == 0) {
                            $('#copy_set_box .copy_box .box .list .input_checkbox_box[data-type="' + k + '"]').hide();
                        }
                        $('#copy_set_box').show();
                    })
                } else {
                    return false;
                }
            }, 'json')

            if ($('#copy_set_box .list').find('.input_checkbox_box').length > 0) {
                $('#copy_set_box').attr('data-url', $Url);
            }
        });

        $('#copy_box_form').submit(function () {
            return false;
        })

        $('#copy_set_box').on('click', '.btn_submit', function () {
            var $Url = $('#copy_set_box').data('url'),
                $Data = $('#copy_box_form').serializeArray(),
                _this = $(this);
            if (_this.attr('disabled')) return false;
            _this.attr('disabled', 'disabled');
            $otherAttr = '';
            if ($Data) {
                $.each($Data, function (k, v) {
                    if (v.name == 'copyId') return true;
                    $otherAttr += $otherAttr ? ',' + v.value : v.value;
                })
            }

            if ($otherAttr) $Url = $Url + '&copyData=' + $otherAttr;

            //$.post('/manage/products/products/copy-products', $Data, function(result){
            //	if(result.ret == 1) {
            //		window.location.href = '/manage/products/products/view?id=' + result.msg;
            //	}
            //	_this.removeAttr('disabled');
            //}, 'json')
            window.location.href = $Url;

            _this.removeAttr('disabled');
            return false;
        }).on('click', '.close , .btn_cancel', function () {
            $('#copy_set_box').hide();
        })
    }
}