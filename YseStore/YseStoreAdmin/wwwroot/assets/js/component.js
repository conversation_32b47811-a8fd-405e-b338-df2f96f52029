// 独立部件
var component_obj = {
	global_select_category: function(){
		const FUNC = {
			popup: $('.global_select_category_popup_box'),
			selectBox: $('.global_select_category_btn_box'),
			type: '',
			inputName: '',
			init: () => {
				FUNC.select()
				FUNC.search()
				FUNC.save()
				FUNC.close()
				FUNC.delete()
			},
			select: () => {
				$('.global_select_category_btn_box .global_select_category_btn').click(function(){
					FUNC.selectBox = $(this).parents('.global_select_category_btn_box')
					FUNC.type = $(this).attr('data-type') ? $(this).attr('data-type') : ''
					FUNC.inputName = $(this).attr('data-input-name') ? $(this).attr('data-input-name') : ''
					FUNC.popup.find(`.select_category_table input[name=GlobalSelectCateId]`).prop('checked', false).parents('.input_checkbox_box').removeClass('checked')
					FUNC.selectBox.find('.category_value_box .category_item:not(".disabled") input[type=hidden]').each(function(){
						let val = $(this).val()
						FUNC.popup.find(`.select_category_table input[name=GlobalSelectCateId][value='${val}']`).prop('checked', true).parents('.input_checkbox_box').addClass('checked')
					})
					FUNC.checkSelectSataus()
					FUNC.popup.show()
					global_obj.div_mask()
				})
				FUNC.popup.on('click', '.select_category_table .haschild .btn_sub', function(){
					// 打开关掉下级分类
					let obj = $(this).parents('.haschild')
					obj.toggleClass('cur').next('.box_sub').slideToggle()
				}).on('click', '.select_category_table .haschild .btn_select', function(){
					// 全选当前分类下下级分类
					let obj = $(this).parents('.haschild')
					let subObj = obj.next('.box_sub')
					if ($(this).hasClass('selectall')) {
						$(this).removeClass('selectall')
						obj.find('.input_checkbox_box:not(".disabled")').removeClass('checked').find('input').prop('checked', false)
						subObj.find('.btn_select').removeClass('selectall')
						subObj.find('.input_checkbox_box:not(".disabled")').removeClass('checked').find('input').prop('checked', false)
					} else {
						$(this).addClass('selectall')
						obj.find('.input_checkbox_box:not(".disabled")').addClass('checked').find('input').prop('checked', true)
						subObj.find('.btn_select').addClass('selectall')
						subObj.find('.input_checkbox_box:not(".disabled")').addClass('checked').find('input').prop('checked', true)
					}
					FUNC.checkSelectSataus()
				}).on('click', '.select_category_table .input_checkbox_box:not(".disabled")', function(){
					// 选中或者取消选中重新更新全选按钮状态
					setTimeout(function(){
						FUNC.checkSelectSataus()
					}, 100)
				})
			},
			search: () => {
				FUNC.popup.on('keyup', '.search_form input[name=Keyword]', function(){
					let val = $(this).val()
					if (val == '') {
						FUNC.popup.find('.select_category_table').show()
						FUNC.popup.find('.search_category_table').hide()
					}
				}).on('click', '.search_form .search_btn', function(){
					if (FUNC.popup.find('.search_form input[name=Keyword]').val() != '') {
						FUNC.popup.find('.select_category_table').hide()
						FUNC.popup.find('.search_category_table').show()
						let loader = frame_obj.loader('.global_select_category_popup_box .search_category_table')
						loader.on()
						$.post('/api/Coupon/global-search-category', {'keyword':FUNC.popup.find('.search_form input[name=Keyword]').val(), 'type':FUNC.type}, function(data) {
							loader.off()
							if (data.ret == 1) {
								if (data.msg) {
									FUNC.popup.find('.search_category_table .thead, .search_category_table .tbody').show();
									FUNC.popup.find('.search_category_table .bg_no_table_data').hide();
								} else {
									FUNC.popup.find('.search_category_table .thead, .search_category_table .tbody').hide();
									FUNC.popup.find('.search_category_table .bg_no_table_data').show();
								}
								FUNC.popup.find('.search_category_table .tbody').html(data.msg);
								// 同步搜索结果中的选中状态
								FUNC.popup.find('.search_category_table .input_checkbox_box:not(".disabled") input').each(function(){
									let val = $(this).val()
									if (FUNC.popup.find(`.select_category_table input[name=GlobalSelectCateId][value='${val}']`).is(':checked')) {
										$(this).prop('checked', true).parents('.input_checkbox_box').addClass('checked')
									} else {
										$(this).prop('checked', false).parents('.input_checkbox_box').removeClass('checked')
									}
								})
								// 更新选中计数  tips 更新
								FUNC.checkSelectSataus()
							}
						}, 'json');
					}
				}).on('click', '.search_category_table .input_checkbox_box:not(".disabled")', function(){
					// 选中或者取消选中重新更新全选按钮状态
					let inputObj = $(this).find('input')
					let val = inputObj.val()
					setTimeout(function(){
						if (inputObj.is(':checked')) {
							FUNC.popup.find(`.select_category_table input[name=GlobalSelectCateId][value='${val}']`).prop('checked', true).parents('.input_checkbox_box').addClass('checked')
						} else {
							FUNC.popup.find(`.select_category_table input[name=GlobalSelectCateId][value='${val}']`).prop('checked', false).parents('.input_checkbox_box').removeClass('checked')
						}
						FUNC.checkSelectSataus()
					}, 100)
				})
			},
			save: () => {
				FUNC.popup.find('.button .btn_submit').click(function(){
					// 获取主分类表格和搜索结果表格中的所有选中项 --> 更新 start
					//tips 删除： let seletList = FUNC.popup.find('.select_category_table .input_checkbox_box:not(".disabled")')
					let seletList = FUNC.popup.find('.select_category_table .input_checkbox_box:not(".disabled"), .search_category_table .input_checkbox_box:not(".disabled")')
					// --> 更新 end
					let html = ''
					let selectValue = [];
					seletList.each(function(){
						let inputObj = $(this).find('input')
						let val = inputObj.val()
						let checked = inputObj.is(':checked')
						let alias = inputObj.attr('data-alias')
						let isHas = FUNC.selectBox.find(`.category_value_box .category_item[data-cateid="${val}"]`).length
						if (checked) {
							if (!isHas && selectValue.indexOf(val)<0) {
								html += `
								<div class="category_item" data-cateid="${val}">
									<div class="cname">${alias}</div>
									<div class="cdel"><span class="icon iconfont icon_menu_close"></span></div>
									<input type="hidden" name="${FUNC.inputName}[]" value="${val}">
								</div>
							`;
								selectValue.push(val)
							}
						} else {
							if (isHas) FUNC.selectBox.find(`.category_value_box .category_item[data-cateid="${val}"]`).remove()
						}
						
					})
					FUNC.selectBox.find('.category_value_box').append(html)
					FUNC.checkBoxStatus()
					FUNC.afterClose()
				})
			},
			close: () => {
				FUNC.popup.find('.t h2, .button .btn_cancel').click(function(){
					FUNC.afterClose()
				})
			},
			delete: () => {
				FUNC.selectBox.on('click', '.category_item .cdel', function(){
					$(this).parents('.category_item').remove()
					FUNC.checkBoxStatus()
				})
			},
			checkBoxStatus: () => {
				if (FUNC.selectBox.find('.category_value_box .category_item').length) {
					FUNC.selectBox.find('.category_value_box').css('display', 'flex').siblings().hide()
				} else {
					FUNC.selectBox.find('.category_value_box').hide().siblings().css('display', 'flex')
				}
			},
			afterClose: () => {
				FUNC.popup.hide()
				global_obj.div_mask(1)
				FUNC.popup.find('.select_category_table').show()
				FUNC.popup.find('.search_category_table').hide().find('.tbody').html('')
				FUNC.popup.find('.search_category_table .bg_no_table_data').show().siblings().hide()
				FUNC.popup.find('.search_form input[name=Keyword]').val('')
			},
			checkSelectSataus: () => {
				// 更新全选按钮状态
				FUNC.popup.find('.select_category_table .haschild .btn_select').each(function(){
					let obj = $(this).parents('.haschild')
					let subObj = obj.next('.box_sub')
					if (obj.find('.input_checkbox_box:not(".disabled") input').length + subObj.find('.input_checkbox_box:not(".disabled") input').length == obj.find('.input_checkbox_box:not(".disabled") input:checked').length + subObj.find('.input_checkbox_box:not(".disabled") input:checked').length) {
						$(this).addClass('selectall')
					} else {
						$(this).removeClass('selectall')
					}
				})
				// tips:删除FUNC.popup.find('.category_num .num').text(FUNC.popup.find('.select_category_table .input_checkbox_box:not(".disabled") input:checked').length)
				// 计算选中的项数，同时考虑主表格和搜索结果表格中的所有选中项  --> 更新
				let mainTableChecked = FUNC.popup.find('.select_category_table .input_checkbox_box:not(".disabled") input:checked').length;
				let searchTableChecked = FUNC.popup.find('.search_category_table .input_checkbox_box:not(".disabled") input:checked').length;
				
				// 检查是否显示搜索结果表格，如果显示则只计算搜索结果中的选中项
				if (FUNC.popup.find('.search_category_table').is(':visible')) {
					FUNC.popup.find('.category_num .num').text(searchTableChecked);
				} else {
					FUNC.popup.find('.category_num .num').text(mainTableChecked);
				}
				// --> 更新 end
			}
		}
		FUNC.init()
	},
}