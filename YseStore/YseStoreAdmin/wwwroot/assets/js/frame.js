

var frame_obj={
	page_init:function(){
		//页面加载
		var resize=function(){
			$(window).width()>=1280?$('body').addClass('w_1200'):$('body').removeClass('w_1200');
			$(window).width()>=1400?$('body').addClass('w_1400'):$('body').removeClass('w_1400');
			var $mainWidth=($(window).width()>=980?$(window).width():980),
				$mainHeight=$(window).height()-$('#header').outerHeight(true);
			if($('#header_real_tips').length){ $mainHeight = $mainHeight-$('#header_real_tips').outerHeight(true); }
			$('#main').css({'width':$mainWidth, 'height':$mainHeight});
			$mainHeight = $mainHeight - parseInt($('#main .r_con_wrap').css('padding-top')) - parseInt($('#main .r_con_wrap').css('padding-bottom'));
			$('#main .menu_button i').css('marginTop', $('#main .menu_button a').height()/2-6);
			$('#main .righter').width($mainWidth - $('#main .menu').width());
			$('#main .r_con_wrap').css({ height: $mainHeight - ($('.fixed_btn_submit').outerHeight(true)||0 )});
			$('#header').width($mainWidth);
			if($('#edit_form, form[data-fixed-submit]').length && $('#edit_form input.btn_submit, form[data-fixed-submit] input.btn_submit').length){ //提交按钮固定于底部
				$('#edit_form input.btn_submit, form[data-fixed-submit] input.btn_submit').parents('.rows').addClass('fixed_btn_submit');
				$('#main .righter').append($('.fixed_btn_submit'));
				$('.fixed_btn_submit').css({width:$mainWidth-$('#main .menu').width(), left:$('#main .menu').width()});
				$('#main .r_con_wrap').css({height:$mainHeight-$('.fixed_btn_submit').outerHeight(true)-5});
			}
			if($('.fixed_btn_submit').length){
				$('.fixed_btn_submit').css({width:$mainWidth-$('#main .menu').width(), left:$('#main .menu').width()});
				$('#main .r_con_wrap').css({height:$mainHeight-$('.fixed_btn_submit').outerHeight(true)-5});
			}
			if($('#div_mask').length) $('#div_mask').css({height:$(document).height()}); //刷新遮罩层高度
			if($('#main .righter .fixed_loading').length) $('#main .righter .fixed_loading').css({width:$('#main .righter').width(), height:$('#main .righter').height()});
			if($('#main .fixed_loading').length) $('#main .fixed_loading').css('left', $('#main .menu').width());
			//表格列表没有任何数据
			if($('.bg_no_table_data').length && !$('.bg_no_table_data').hasClass('bg_no_table_fixed')){
				var $insideTable=($('.inside_table').length?40:0), //40:内间距
					$content=$('.bg_no_table_data .content').outerHeight(true),
					_this = $('.bg_no_table_data'),
					wTop = _this.offset().top,
					wHeight = $(window).height();
					$H = wHeight - wTop - 40;
					if($H < 150){
						$H = 200;
					}
					if(_this.parents('.fixed_right_products_choice_jsppane').length) {
						$H = _this.parents('.fixed_right_products_choice_jsppane').outerHeight(true);
					}
					if ($('input[name=hidden_height]').length) $H = $H-parseInt($('input[name=hidden_height]').val());
					var $half=($H-$content)/2;
				$('.bg_no_table_data').css('height', $H);
				$('.bg_no_table_data .content').css('top', ($half<0?0:$half));
			}
			// 栏目选项卡切换效果
			// if ($(".inside_menu").length) {
			// 	if ($(window).width() < 1330) {
			// 		$(".inside_menu").addClass("inside_menu_mini");
			// 	} else {
			// 		$(".inside_menu").removeClass("inside_menu_mini");
			// 	}
			// }
		}
		resize();
		$(window).resize(function(){resize();});

		//加载效果
		$(window).ready(function(){
			$('#main .fixed_loading').fadeOut(500);
			// 产品列表,加载完成之后懒加载图片
			$(window).ready(function(){
				if($('.loading_img').length>0){
					$('.loading_img').each(function(index,elem){
						$(elem).attr('src', $(elem).attr('data-src'));
					});
				}
			});
		});

		$('#header .menu .menu_home a').click(function(){
			var status = $(this).attr('status'),
				_this = $(this);
			if (status != 1) {
				$.post('/manage/action/clear-cache', {}, function(data){
					if (data.ret == 1) {
						_this.attr('status', 1);
						_this[0].click();
						setTimeout(function(){
							_this.attr('status', 0);
						}, 1000);
					}
				}, 'json');
				return false;
			}
		});

		//头部管理员下拉 or 消息提示下拉
		frame_obj.message_orders_init();

		//权限
		function prevent_limit_fun(e){
			e.preventDefault();
			e.stopImmediatePropagation();
			$(".popup_limit_tips,.limit_mask").addClass('visible');
		}
		$(".cdx_limit").off().click(function(e){
			if($(this).hasClass('cdx_limit_12')){
				$(".popup_limit_tips .txt").html($(".popup_limit_tips .txt").attr('data-pay_func_12'));
			}else if($(this).hasClass('cdx_limit_13')){
				$(".popup_limit_tips .txt").html($(".popup_limit_tips .txt").attr('data-pay_func_13'));
			}else{
				$(".popup_limit_tips .txt").html($(".popup_limit_tips .txt").attr('data-pay_func'));
			}
			prevent_limit_fun(e);
		});
		$(".cdx_limit").find("*").each(function(){
			$cdx_limit=$(this).parents('.cdx_limit');
			$(this).off().click(function(e){
				if($cdx_limit.hasClass('cdx_limit_12')){
					$(".popup_limit_tips .txt").html($(".popup_limit_tips .txt").attr('data-pay_func_12'));
				}else if($cdx_limit.hasClass('cdx_limit_13')){
					$(".popup_limit_tips .txt").html($(".popup_limit_tips .txt").attr('data-pay_func_13'));
				}else{
					$(".popup_limit_tips .txt").html($(".popup_limit_tips .txt").attr('data-pay_func'));
				}
				prevent_limit_fun(e);
			});
		});

		$(".popup_limit_tips .go_back,.limit_mask").click(function(e){
			$(".popup_limit_tips,.limit_mask").removeClass('visible');
		});

		//微信客服下拉
		$('#header .menu_qrcode').hover(function(){
			$(this).find('.qrcode_box').show().stop(true).animate({'top':40, 'opacity':1}, 250);
		}, function(){
			$(this).find('.qrcode_box').stop(true).animate({'top':30, 'opacity':0}, 100, function(){ $(this).hide(); });
		});

		// 小程序下拉
		$('#header .menu_miniprogram').hover(function(){
			$(this).find('.miniprogram_box').show().stop(true).animate({'top':40, 'opacity':1}, 250);
		}, function(){
			$(this).find('.miniprogram_box').stop(true).animate({'top':30, 'opacity':0}, 100, function(){ $(this).hide(); });
		});

		$('#header .menu>li.menu_qrcode .qrcode_box .box .53kf a').on('click',function(){
			let url = $(this).attr('data-url');
			if(!url){ return false; }
			window.open ( url , 'newwindow', 'height= 600, width=800, top=0, lett=0, toolbar=no, menubar=no, scrollbars=no, resizable=no,ocation=n 0, status=no');
		})

		$('#main .new_menu_list .item_name>a').click(function(){
			let $itemObj = $(this).parent().parent(),
				$listObj = $itemObj.find("ul");
			if ($itemObj.hasClass("open")) {
				// 已展开
				$itemObj.removeClass("open");
				$listObj.slideUp(350);
			} else {
				// 未展开
				$itemObj.addClass("open").siblings().removeClass("open");
				$listObj.slideDown(350);
				$itemObj.siblings().find("ul").slideUp(350);
			}
		});
		$('#main .new_menu_list .menu_item ul li').click(function(){
			$(this).siblings('li').removeClass('current');
			$(this).addClass('current');
		});

		//弹出提示
		$('.tool_tips_ico').each(function(){
			if($(this).attr('content')==''){
				$(this).hide();
				return;
			}else{
				$(this).html(' ');
				$('#main .r_con_wrap').tool_tips($(this), {position:'horizontal', html:$(this).attr('content'), width:260});
			}
		});
		
		$('.tip_ico').hover(function(){
			$(this).append('<span class="tip_ico_txt'+($(this).hasClass('tip_min_ico')?' tip_min_ico_txt':'')+' fadeInUp animate">'+$(this).attr('label')+'<em></em></span>');
		}, function(){
			$(this).removeAttr('style').children('span').remove();
		});
		$('.tip_ico_down').hover(function(){
			$(this).append('<span class="tip_ico_txt_down'+($(this).hasClass('tip_min_ico')?' tip_min_ico_txt_down':'')+' fadeInDown animate">'+$(this).attr('label')+'<em></em></span>');
		}, function(){
			$(this).removeAttr('style').children('span').remove();
		});

		$('body').on('click', '.tab_box .tab_box_btn',
			function () {
				// 选项卡效果
				let $this = $(this)
				let $lang = $this.attr('data-lang')
				let $obj = $this.parents('form')
				$obj.find('.tab_box_btn').show()
				$obj.find('dt>span').text($this.find('span').text())
				$obj.find('.tab_txt').hide()
				$obj.find('.tab_txt_'+$lang).show()
			}
		).on('mouseenter', '.unit_input .box_select_down',
			function () {
				// 带单位文本框下拉效果(展开)
				let move = 10
				let $this = $(this)
				let $inputObj = $this.find('.unit_input_select_input')
				let $listObj = $this.find('.list')
				let listHeight = $listObj.outerHeight()
				let $lable = $inputObj.attr('name')
				let $value = $inputObj.val()
				let height = $this.outerHeight() || 26
				let top = $this.offset().top
				let left = $this.offset().left
				let windowHeight = $(window).height()
				let isMove = 'bottom'
				let moveTop = top - move
				let style = `top: ${moveTop}px; left: ${left}px; padding-top: ${height + 1}px;`
				if ((top + listHeight + height + 1) > windowHeight) {
					moveTop = top - listHeight + move
					style = `top: ${moveTop}px; left: ${left}px; padding-bottom: ${height + 1}px;`
					isMove = 'top'
				}
				if ($('#box_select_down_list').length) $('#box_select_down_list').remove()
				let html = `
				<div id="box_select_down_list" style="${style}" data-label="${$lable}">
					<ul class="list">
						${$(this).find('.list').html()}
					</ul>
				</div>
				`
				$('body').append(html)
				let $boxObj = $('#box_select_down_list')
				$boxObj.stop(true).animate({ 'top': (isMove == 'bottom' ? moveTop + move : moveTop - move), 'opacity': 1 }, 250)
				$boxObj.find(`li[data-value="${$value}"]`).addClass('current').siblings().removeClass('current')
				$this.addClass('current')
			}
		).on('mouseleave', '#box_select_down_list',
			function () {
				let $this = $(this)
				let $label = $this.attr('data-label')
				let $inputObj = $(`input[name="${$label}"]`)
				let $selectObj = $inputObj.parents('.box_select_down')
				$selectObj.removeClass('current')
				$this.remove()
			}
		).on('click', '#box_select_down_list .list li',
			function () {
				// 带单位文本框下拉效果(选择选项)
				let $this = $(this)
				let $boxObj = $this.parents('#box_select_down_list')
				let $label = $boxObj.attr('data-label')
				let $inputObj = $(`input[name="${$label}"]`)
				let $selectObj = $inputObj.parents('.box_select_down')
				let $headObj = $selectObj.find('.head')
				let $text = $this.text()
				let $value = $this.attr('data-value')
				$selectObj.removeClass('current')
				$headObj.text($text)
				$inputObj.val($value)
				$boxObj.remove()
			}
		)

		//勾选按钮
		frame_obj.btnCheckbox();

		//属性勾选按钮
		$('.btn_attr_choice').on('click', function(){
			var $this=$(this),
				$obj=$(this).find('input');
			if($this.hasClass('disabled')) return false; //禁止调用
			if($obj.is(':checked')){ //已勾选
				$obj.prop('checked',false);
				$this.removeClass('current');
			}else{ //未勾选
				$obj.prop('checked', true);
				$this.addClass('current');
			}
		});

		$('body').on('click', '.input_radio_box', function(){
			//单选按钮
			if($(this).hasClass('disabled')){ return false; }
			var name=$(this).find('input').attr('name');
			$("input[name='"+name+"']").prop('checked',false).parent().parent().removeClass('checked');
			$(this).addClass('checked').find('input').prop('checked', true);
		}).on('click', '.input_checkbox_box:not(".disabled")', function(e){
			//多选按钮
			var $obj=$(this);
			if($obj.hasClass('checked')){
				$obj.find('input').prop('checked', false);
				$obj.removeClass('checked half_checked');
			}else{
				$obj.find('input').prop('checked', true);
				$obj.addClass('checked');
			}
		});

		//表头效果
		if($('.list_menu').length){
			$('.r_con_wrap').scroll(function(){
				frame_obj.fixed_list_menu();
			});
			$(document).ready(function(){
				frame_obj.fixed_list_menu();
			});
			$(window).resize(function() {
				if ($(".list_menu").hasClass("fixed")) {
					let w1 = $(".inside_table").length ? $(".inside_table").width() : 0,
						w2 = $(".list_menu").width(),
						w3 = $(".list_menu").innerWidth();
					w1 == 0 && (w1 = $(".center_container_1690").length ? $(".center_container_1690").width() : 0); // 针对运费管理(物流)
					$(".list_menu.fixed").css("width", w1 - (w3 - w2));
				}
			});
		}

		//搜索框
		frame_obj.search_form_init();

		//表格更多下拉效果
		$('.inside_table .more, .global_box_drop_down .more').parent().hover(function(){
			$(this).find('.more_menu').show().stop(true).animate({'top':31, 'opacity':1}, 250);
		}, function(){
			$(this).find('.more_menu').show().stop(true).animate({'top':21, 'opacity':0}, 250, function(){ $(this).hide(); });
		});
		$('.r_con_table .operation dl').hover(function(){
			let trCurrent = $(this).parents('tr').index();
			let trSize = $(this).parents('tr').siblings().length;
			if (trSize == trCurrent) {  // 表格最后一行，向上显示
				if (parseInt($(this).find('dd').css('bottom')) < 0) $(this).find('dd').css('cssText', 'top: auto !important;bottom: 30px');
				let dropDownHeight = $(this).find('dd').outerHeight(true) + $(this).find('dt').outerHeight(true) + 20;
				if (dropDownHeight > $(this).parents('.r_con_table').height() && $(this).parents('.box_table').css('overflow') != undefined) {  // 下拉框比表格高，左侧显示
					$(this).find('dd').css({'bottom': -15, 'left': -70});
					$(this).find('dd').show().stop(true).animate({'bottom':-25, 'opacity':1}, 250);
				} else {
					$(this).find('dd').show().stop(true).animate({'bottom':40, 'opacity':1}, 250);
				}
				return false;
			} 
			$(this).find('dd').show().stop(true).animate({'top':20, 'opacity':1}, 250);
		}, function(){
			let trCurrent = $(this).parents('tr').index();
			let trSize = $(this).parents('tr').siblings().length;
			if (trSize == trCurrent) {  // 表格最后一行，向上显示
				$(this).find('dd').show().stop(true).animate({'bottom':30, 'opacity':0}, 250, function(){ $(this).hide(); });
				return false;
			}
			$(this).find('dd').show().stop(true).animate({'top':10, 'opacity':0}, 250, function(){ $(this).hide(); });
		});

		//图片默认设置
		var delallbackValue = $('input[name="stickerimgdeletecallback"]').val();
		if (delallbackValue) {
			frame_obj.upload_img_init(function () {

				// 替换背景图片并保持其他样式
				$('#sale_sticker_edit .right_container .preview_row').css({
					'background': 'url(/assets/images/frame/preview_icon.jpg) no-repeat center'
				});
			});
		}
		else {

			frame_obj.upload_img_init();
		}
		frame_obj.upload_pro_img_init(1);

		//多功能选项框
		frame_obj.box_option_list();
		frame_obj.box_option_button_choice();

		//下拉文本两用
		window.boxDropDoubleJson = {
			'itemRadioClickBefore':'' // 单选按钮点击 - 选中之前
		}
		frame_obj.box_drop_double();

		frame_obj.rows_input();

		//登录
		frame_obj.expire_login();

		// 按钮提示
		frame_obj.buttonTips();

		/***** 系统消息通知 Start *****/
		frame_obj.pop_up($('#header .btn_global_system'), '.pop_global_system', 1, function(){
			var $Obj=$('.pop_global_system');
			$Obj.find('.message_list').css('height', $Obj.outerHeight()-$Obj.find('.message_title').outerHeight());
		});
		$('.pop_global_system .message_list li>a').click(function(){
			$(this).find('i').remove();
			if ($(this).hasClass('link')) {
				$.post('/manage/action/system-notification', {'Id':$(this).attr('data-id')}, function(data){}, 'json');
			} else {
				if (!$('#global_system_box').length) $('#system_body').append('<div id="global_system_box"></div>');
				$('#global_system_box').html($(this).parent().find('.system_item').prop('outerHTML'));
				var $height = $('#global_system_box .system_item:visible').height();
				$('#global_system_box').height($height);
				global_obj.div_mask();
			}
		});
		$('#system_body').on('click', '#global_system_box .system_btn[data-id]', function(){
			var $obj = $(this),
				$id = $obj.attr('data-id');
			global_obj.div_mask(1);
			$('#global_system_box').remove();
			$.post('/manage/action/system-notification', {'Id':$id, 'Type':1}, function(data){}, 'json');
		});
		if($('#global_system_box').length) {
			$(window).resize(function(){
				$height = $('#global_system_box .system_item:visible').height();
				$('#global_system_box').css('height', $height);
			})
		}
		/***** 系统消息通知 End *****/

		frame_obj.manageFeedbackEvent()

		//a标签点击加载效果
		$('a[href]').not('.no_load, .del, .item').not('[href^="#"]').not('[href^="javascript"]').not('[target="_blank"]').click(function(e){if (!e.ctrlKey) $('.fixed_loading').fadeIn();});

		// 栏目选项卡切换效果
		$.fn.insideMenu = function() {
			if (!$(".inside_menu").length) return false;
			let obj = this;
			let $currentObj = obj.find("ul a.current");
			!$currentObj.length && ($currentObj = obj.find("ul li:eq(0) a"));

			let $title = $currentObj.text();
			obj.find(".inside_title>span").text($title);

			if (!obj.find(".inside_menu_current").length) {
				obj.find(".inside_body").append('<div class="inside_menu_current"></div>');
			}
			let boxWidth = obj.find(".inside_body").length ? obj.find(".inside_body").offset().left : 0,
				curWidth = $currentObj.length ? $currentObj.offset().left : 0;
			obj.find(".inside_menu_current").css("left", curWidth - boxWidth);
			
			obj.find("ul a").on("mouseenter", function() {
				let $this = $(this);
				if (obj.hasClass("inside_menu_mini") || $this.hasClass("hover")) {
					return false;
				}
				let boxWidth = obj.find(".inside_body").offset().left,
					hoverWidth = obj.find(".inside_menu_current").offset().left,
					btnWidth = $this.offset().left;
				if (hoverWidth == btnWidth) {
					return false;
				}
				obj.find("ul a.current").addClass("none");
				$this.parent().siblings().find("a").removeClass("hover");
				obj.find(".inside_menu_current").css({
					"opacity": 1,
					"visibility":"visible"
				});
				obj.find(".inside_menu_current").stop(true).animate({"left": btnWidth - boxWidth}, 0, function() {
					$(this).css({
						"opacity": 0,
						"visibility": "hidden"
					});
					$this.addClass("hover").parent().siblings().find("a").removeClass("hover");
					if ($this.hasClass("none")) {
						$this.removeClass("none");
					}
				});
			});
			obj.find(".inside_body").on("mouseleave", function() {
				
				let boxWidth = obj.find(".inside_body").offset().left,
					hoverWidth = obj.find(".inside_menu_current").offset().left,
					curWidth = obj.find("ul a.current").offset().left;
				if (obj.find("ul a.hover").length == 0 && obj.find("ul a.none").length == 0 && hoverWidth == curWidth) {
					return false;
				}
				obj.find(".inside_menu_current").css({
					"opacity": 1,
					"visibility":"visible"
				});
				obj.find("ul a").removeClass("hover");
				obj.find(".inside_menu_current").stop(true).animate({"left": curWidth - boxWidth}, 0, function() {
					$(this).css({
						"opacity": 0,
						"visibility": "hidden"
					});
					obj.find("ul a.current").removeClass("none");
				});
			});
		}

		// 列表表格滚动栏效果
		if ($(".box_table").length) {
			// 定义第二列的左侧
			$(".box_table table tr").each(function(index, element){
				let $prevColumnWidth = 0;
				$(element).find('td').each(function (key, value){
					if($(value).hasClass('flex_item')) {
						let position = 'left';
						if($(value).hasClass('last')) {
							position = 'right';
							$prevColumnWidth = 0;
						}
						if($(value).hasClass('sec_last')) {
							position = 'right';
							$prevColumnWidth = $(element).find('td').last().outerWidth(true);
						}
						if($(value).hasClass('third_last')) {
							position = 'right';
							$prevColumnWidth = $(element).find('td').last().outerWidth(true) +  $(element).find('td.sec_last').last().outerWidth(true);
						}
						$(value).css(position, $prevColumnWidth);
					}
					$prevColumnWidth += parseFloat($(value).outerWidth(true));
				})
			})

			// 滚动条
			let $boxWidth = $(".box_table").width(),
				$wrapWidth  = $('.r_con_wrap').width()
				$tableWidth = $(".box_table table").width();
			if ($tableWidth > $wrapWidth) {
				$(".box_table table").addClass("fixed");
			}
			$boxWidth >= $tableWidth && ($tableWidth -= 10); // 隐藏滚动条
			$(".scroll_sticky_content>div").css({"width":$tableWidth, "height":1});
			$(window).resize(function() {
				let $boxWidth = $(".box_table").width(),
					$tableWidth = $(".box_table table").width();
				$boxWidth >= $tableWidth && ($tableWidth -= 10); // 隐藏滚动条
				$(".scroll_sticky_content>div").css({"width":$tableWidth, "height":1});
				if ($(".box_table table").width() > $('.r_con_wrap').width()) {
					$(".box_table table").addClass("fixed");
				} else {
					$(".box_table table").removeClass("fixed");
				}
			});
			$(".box_table").on("scroll", function(e) {
				if ($(this).scrollLeft() > 0) {
					$(".box_table table").addClass("fixed");
				}
				$(".scroll_sticky").scrollLeft($(this).scrollLeft());
			});
			$(".scroll_sticky_content").on("scroll", function(e) {
				let tableWidth = $(".box_table table").outerWidth();
				$(".scroll_sticky_content>div").css({"width":tableWidth, "height":1});
				if ($(this).scrollLeft() > 0) {
					$(".box_table table").addClass("fixed");
				}
				$(".box_table").scrollLeft($(this).scrollLeft());
			});
		}
		
		//增加input框数量限制
		if($('input[number_limit] , textarea[number_limit]').length){
			//初始化生成html
			$('input[number_limit] , textarea[number_limit]').each(function(){
				let obj = $(this),
					max_length = $(this).attr('maxlength'),
					parent = obj.parent(),
					html = obj.prop("outerHTML"),
					inner_length = $(this).val().length;
					if ($(this).val().match(/\n/ig)) {
						inner_length += $(this).val().match(/\n/ig).length;
					}
					append_html = '';
					append_html += '<div class="number_limit_relative" style="position:relative">';
					append_html += '<div class="number_limit"><span> ' + inner_length + ' </span> / ' + max_length + '</div>';
					append_html += '</div>';
					obj.before(append_html);
					obj.prev('.number_limit_relative').append(obj);
				if(inner_length > 0 && inner_length < max_length){
					parent.find('.number_limit').addClass('ing');
				}else if(inner_length == max_length){
					parent.find('.number_limit').addClass('max');
				}
			})
			//执行事件
			$('input[number_limit] , textarea[number_limit]').each(function(){
				let obj = $(this);
				obj.on('keyup',function(){
					let inner_length = parseInt(obj.val().length),
						max_length = obj.attr('maxlength');
					if (obj.val().match(/\n/ig)) {
						inner_length += obj.val().match(/\n/ig).length;
					}
					obj.prev().find('span').html(inner_length);
					obj.prev().removeClass('ing').removeClass('max');
					if(inner_length > 0 && inner_length < max_length){
						obj.prev().addClass('ing');
						return false;
					}else if(inner_length >= max_length){
						obj.prev().addClass('max');
					}
				})
				obj.on('keydown',function(e){
					let inner_length = parseInt(obj.val().length),
						max_length = obj.attr('maxlength'),
						key = window.event ? e.keyCode : e.which,
						keyArr = [8, 37, 38, 39, 40, 45, 46, 116]; //操作键
					if (obj.val().match(/\n/ig)) {
						inner_length += obj.val().match(/\n/ig).length;
					}
					if (key == 13) { //Enter 键
						inner_length ++ ;
					}
					if(inner_length >= max_length){
						if (global_obj.in_array(key, keyArr)) {
							return true;
						}
						return false;
					}
				})
			})
		}

		//重置表单框显示数量
		$.fn.resetLimit = function() {
			obj = $(this);
			let inner_length = parseInt(obj.val().length),
				max_length = obj.attr('maxlength');
			if (obj.val().match(/\n/ig)) {
				inner_length += obj.val().match(/\n/ig).length;
			}
			obj.prev().find('span').html(inner_length);
			obj.prev().removeClass('ing').removeClass('max');
			if(inner_length > 0 && inner_length < max_length){
				obj.prev().addClass('ing');
				return false;
			}else if(inner_length >= max_length){
				obj.prev().addClass('max');
			}
		}

		// 获取标识，mark方法自行实现
		let mark = global_obj.getMark()
		if (mark) {
			let jsObject = global_obj.query_get('jsObject')
			if (typeof window[jsObject] != 'undefined') {
				let trackId = global_obj.query_get('trackId')
				let _jsObject = Function('"use strict";return (' + jsObject + ')')()
				if ($.isFunction(_jsObject[mark])) {
					_jsObject[mark](trackId)
				}
			}
		}

		//权限提示
		$('.unsupported').click(function(){
			//版本不支持
			var tips = lang_obj.manage.global.unsupported;
			//业务员没有权限
			if ($(this).hasClass('unpermission')) tips = lang_obj.manage.global.unpermission;
			global_obj.win_alert({"title":tips,"confirmBtn":lang_obj.manage.global.got_it});
		});

		/**应用消息 */
		frame_obj.pop_up($('#header .btn_app_message'), '.pop_app_message', 1, function(){
			var $Obj=$('.pop_app_message');
			$Obj.find('.message_list').css('height', $Obj.outerHeight()-$Obj.find('.message_title').outerHeight());
		});


		//订单消息回复增加快捷键发送
		var formAddQuickReplies = function($obj){
			$obj.keydown(function (e) {
				var keyCode = e.keyCode ? e.keyCode : e.which ? e.which : e.charCode;
				if (keyCode == 13 && !e.shiftKey){
					$obj.parents('form').find('.btn_submit').click();
					return false;
				}
			})
		}
		formAddQuickReplies($('#message_orders_form textarea[name=Message]'));

		frame_obj.new_message(100)
	},

	search_form_init: function() {
		// 搜索框事件
		if($('.list_menu .search_form .ext>div').length){
			$('.list_menu .search_form .more').click(function(){
				if($('.list_menu .search_form .ext').is(':hidden')){
					$('.list_menu .search_form .ext').show();
					$('.list_menu .search_form form').css('border-radius', '5px 5px 0 0');
					$('.list_menu .search_form .more').addClass('more_up');
				}else{
					$('.list_menu .search_form .ext').hide();
					$('.list_menu .search_form form').css('border-radius', '5px');
					$('.list_menu .search_form .more').removeClass('more_up');
				}
			});
		}else{
			$('.list_menu .search_form .more').remove();
			$('.list_menu .search_form .form_input').addClass('long_form_input');
		}
		if ($('.list_menu .search_form_selected').length) {
			$('.list_menu .search_form_selected .btn_item_choice').on('click', function () {
				let Type = $(this).find('input').val();
				$('.list_menu .search_form input[name=' + Type + ']').val('');
				$('.list_menu .search_form input[type=submit]').trigger('click');
			});
		}

		// 搜索框事件(新版)
		if ($(".list_menu .search_box_selected").length) {
			$(".list_menu .search_box_selected .btn_item_choice").on("click", function () {
				let $name = $(this).attr("data-name");
				nameAry = $name.split("|");
				$(nameAry).each(function(index, element) {
					$(".list_menu .search_box input[name=\"" + element + "\"]").val("");
				});
				$(".list_menu .search_box input[type=submit]").trigger("click");
			});
		}

		// 自定义筛选下拉
		$(".down_select_box").on("mouseenter", function() {
			$(this).addClass("hover");
			$(this).find(".select_option").show().css({"top": 25, "opacity": 0}).animate({"top": 32, "opacity": 1}, 250);
		}).on("mouseleave", function() {
			$(this).removeClass("hover");
			$(this).find(".select_option").hide();
		}).on("click", ".select_option_item", function() {
			// 点击选项
			let $this = $(this),
				$mainObj = $this.parents(".select_option_main"),
				$valueObj = $mainObj.find("input[type=hidden]"),
				$type = $this.attr("data-type"),
				$dataValue = $(this).find("input").val();
				value = "", data = [];
			if ($this.hasClass("disabled")) return false;
			if ($valueObj.val() != "") {
				data = $valueObj.val().split(",");
			}
			if ($type == "radio") {
				// 单选
				$this.parent().find(".select_option_item").removeClass("checked").find("input").prop("checked", false);
				$this.addClass("checked").find("input").prop("checked", true);
				data = [];
				data.push($dataValue);
			} else if ($type == "checkbox") {
				// 多选
				if ($this.find(".input_checkbox_box").hasClass("checked")) {
					// 已选中
					$this.removeClass("checked");
					$this.find(".input_checkbox_box").removeClass("checked").find("input").prop("checked", false);
					let index = data.indexOf($dataValue);
					data.splice(index, 1);
				} else {
					// 未选中
					$this.addClass("checked");
					$this.find(".input_checkbox_box").addClass("checked").find("input").prop("checked", true);
					data.push($dataValue);
				}
			}
			if (data) value = data.join(",");
			$valueObj.val(value);
			$(".search_box .search_btn").trigger("click");
			return false;
		}).on("click", ".select_clean", function() {
			// 清除选项
			let $obj = $(this).parent(),
				$valueObj = $obj.find("input[type=hidden]");
			$obj.find(".select_option_item").each(function() {
				let $type = $(this).attr("data-type");
				if ($type == "radio") {
					// 单选
					$(this).removeClass("checked").find("input").prop("checked", false);
				} else if ($type == "checkbox") {
					// 多选
					$(this).removeClass("checked");
					$(this).find(".input_checkbox_box").removeClass("checked").find("input").prop("checked", false);
				}
			});
			$valueObj.val("");
			$(".search_box .search_btn").trigger("click");
			return false;
		});
	},

	filterRight: function(data, fixed = true) {
		// 右侧筛选弹窗事件
		data = $.extend({
			onInit: function() {},
			onSubmit: function() {}
		}, data);

		data.onInit();
		if (fixed == true) frame_obj.fixed_right($('.filter_btn'), '.fixed_search_filter');
		$('.fixed_search_filter').on("click", ".filter_title", function() {
			// 标题
			let $obj = $(this).parent();
			if ($obj.hasClass("current")) {
				$obj.removeClass("current");
			} else {
				$obj.addClass("current");
			}
		}).on("click", ".filter_option_item", function() {
			// 点击选项
			let $this = $(this);
			let $type = $this.attr("data-type");
			if ($this.hasClass("disabled")) return false;
			if ($type == "radio") {
				// 单选
				$this.parent().find(".filter_option_item").removeClass("checked").find("input").prop("checked", false);
				$this.addClass("checked").find("input").prop("checked", true);
			} else if ($type == "checkbox") {
				// 多选
				if ($this.find(".input_checkbox_box").hasClass("checked")) {
					// 已选中
					$this.removeClass("checked");
					$this.find(".input_checkbox_box").removeClass("checked").find("input").prop("checked", false);
				} else {
					// 未选中
					$this.addClass("checked");
					$this.find(".input_checkbox_box").addClass("checked").find("input").prop("checked", true);
				}
			}
			return false;
		}).on("click", ".filter_clean", function() {
			// 清除选项
			let $obj = $(this).parent();
			// 单选 / 多选
			if ($obj.find(".filter_option_item").length) {
				$obj.find(".filter_option_item").each(function() {
					$(this).removeClass("checked");
					$(this).find(".input_checkbox_box").removeClass("checked");
					$(this).find("input").prop("checked", false);
				});
			}
			// 文本
			if ($obj.find(".filter_option_input>input").length) {
				$obj.find(".filter_option_input>input").val("");
			}
			// 下拉 (多选)
			if ($obj.find(".box_drop_double[data-checkbox=1]").length) {
				$obj.find(".select_list .btn_attr_choice").remove();
			}
			// 下拉 (单选)
			if ($obj.find(".box_drop_double[data-checkbox=0]").length) {
				$obj.find(".box_input, .hidden_value").val("");
			}
			if ($obj.find(".filter_scope>.input>input").length) {
				$obj.find(".filter_scope>.input>input").val("");
			}
			// 价格范围
			if ($obj.find(".filter_option_price>input").length) {
				$obj.find(".filter_option_price>input").val("");
			}
			// 价格范围
			if ($obj.find(".filter_range_price input").length) {
				$obj.find(".filter_range_price input").val("");
			}
			return false;
		}).on("click", ".btn_submit", function() {
			// 提交
			let $obj = $(".fixed_search_filter");
			data.onSubmit($obj);
			$('.search_box input[type=submit]').trigger('click');
		});
	},

	rows_input:function(){ //计算多语言表单长度
		$('.global_form .rows .input:visible').has('.lang_input').each(function(){
			var o=$(this);
			var input_width=Math.max(o.find('input').outerWidth(), o.find('textarea').outerWidth());
			var title_width=last_width=0;
			o.find('.lang_input b[class!=last]').each(function(){
				var w=$(this).outerWidth();
				w>title_width && (title_width=w);
			});
			title_width+=1;
			o.find('.lang_input b.last').each(function(){
				var w=$(this).outerWidth();
				w>last_width && (last_width=w);
			});
			last_width+=1;
			o.find('.lang_input').width(input_width+title_width+last_width);
			o.find('.lang_input b[class!=last]').width(title_width);
			o.find('.lang_input b.last').width(last_width);
			o.find('.lang_input input, .lang_input textarea').width(o.find('.lang_input').outerWidth()-o.find('.lang_input b[class!=last]').outerWidth()-o.find('.lang_input b.last').outerWidth()-2);
		});
	},

	//订单消息
	message_orders_init:function(){
		//后台账号信息下拉
		$('#header .user_info').hover(function(){
			$(this).find('dd').show().stop(true).animate({'top':40, 'opacity':1}, 250);
		}, function(){
			$(this).find('dd').stop(true).animate({'top':30, 'opacity':0}, 250, function(){ $(this).hide(); });
		});

		//消息下拉
		$('#header .menu_down').hover(function(){
			$(this).find('.message_info').show().stop(true).animate({'top':40, 'opacity':1}, 250);
		}, function(){
			$(this).find('.message_info').stop(true).animate({'top':30, 'opacity':0}, 100, function(){ $(this).hide(); });
		});

		//二维码下拉
		$('#header .menu_app .box_app_type').hover(function(){
			$(this).find('.drop_down').show().stop(true).animate({'top':44, 'opacity':1}, 250);
		}, function(){
			$(this).find('.drop_down').stop(true).animate({'top':30, 'opacity':0}, 100, function(){ $(this).hide(); });
		});

		$('.pop_orders_message #message_orders_form .input_checkbox_box a').click(function(e){
			e.stopPropagation()
		})

		//订单消息
		frame_obj.pop_up($('#header .btn_orders_message'), '.pop_orders_message_list', 1, function(){
			var $Obj=$('.pop_orders_message_list');
			$Obj.find('.message_list').css('height', $Obj.outerHeight()-$Obj.find('.message_title').outerHeight());
			frame_obj.pop_up($Obj.find('.message_list li'), '.pop_orders_message', 2, function($This){
				var $Obj=$('.pop_orders_message'),
					$MId=$This.attr('data-id');
				$Obj.find('.message_dialogue_list').html(''); //清空对话栏
				$Obj.find('#message_orders_form .input_checkbox_box').removeClass('checked').find('input').prop('checked', false)
				$Obj.find('.message_dialogue').css('height', $Obj.outerHeight()-$Obj.find('.message_title').outerHeight()-$Obj.find('.message_bottom').outerHeight()-10);
				$.post('/manage/action/message-orders-view', {'MId':$MId}, function(data){
					if(data.ret==1){
						var $Html='';
						$Obj.find('.message_title .title strong').html('No#'+data.msg.OId);
						$Obj.find('.message_title .email').html(data.msg.Email);
						for(k in data.msg.Reply){
							$Html+=	'<div class="dialogue_box ' + (data.msg.Reply[k].UserType == 'manager' ? 'dialogue_box_right' : 'dialogue_box_left') + ' clean">';
							$Html+=		'<div class="time">'+data.msg.Reply[k].Time+'</div>';
							$Html+=		'<div class="message">'+data.msg.Reply[k].Content.replace(/\r\n/g, "<br>")+'</div>';
							if (data.msg.Reply[k].PicPath) {
								$Html+=		'<div class="picture"><a href="'+data.msg.Reply[k].PicPath+'" target="_blank" class="pic_box"><img src="'+data.msg.Reply[k].PicPath+'" /><span></span></a></div><div class="clear"></div>';
							}
							if (data.msg.Reply[k].VideoPath) {
								$Html+=		'<div class="picture"><a href="' + data.msg.Reply[k].VideoPath + '" target="_blank" class="pic_box"><img src="' + data.msg.Reply[k].VideoPathCover + '" /><span></span></a><i class="iconfont icon-video2"></i></div><div class="clear"></div>';
							}
							$Html+=	'</div>';
							$Html+=	'<div class="clear"></div>';
						}
						$Obj.find('.message_dialogue_list').append($Html);
						$Obj.find('.message_dialogue').animate({scrollTop:$Obj.find('.message_dialogue_list .dialogue_box:last').offset().top}, 10); //自动滚动到最后一个消息
						$Obj.find('input[name=MId]').val($MId);
						$This.find('.unread_count').hide();
					}
				}, 'json');
			});
		});

		//订单消息回复图片上传
		frame_obj.mouse_click($('#MsgPicDetail .upload_btn, #MsgPicDetail .pic_btn .edit'), 'img', function($this){
			frame_obj.photo_choice_init('MsgPicDetail', '', 1);
		});

		//订单消息回复图片上传
		frame_obj.mouse_click($('#MsgVideoDetail .upload_btn, #MsgVideoDetail .pic_btn .edit'), 'img', function($this){
			frame_obj.file_choice_init('MsgVideoDetail', 'MsgVideoPath', 1, 'mp4', '', 1, "frame_obj.upload_video_init(1, '', 1)");
		});

		frame_obj.mouse_click($('#MsgVideoDetail .file_btn .del'), '', function($this){ //视频删除点击事件
			var $obj=$this.parents('.file')
			$obj.removeClass('isfile').removeClass('show_btn');
			$obj.parent().append($obj);
			$obj.find('.preview_file .upload_btn').show();
			$obj.find('.preview_file a').remove();
			$obj.find('.preview_file input:hidden').val('').attr('save', 0);
			frame_obj.upload_video_init(1);
		});

		//订单消息提交回复
		frame_obj.submit_form_init($('#message_orders_form'), '', '', 0, function(data){
			if (data.ret == 1) {
				let obj = $('.pop_orders_message')
				let form = $('#message_orders_form')
				let pic = $('#MsgPicDetail .img')
				let video = $('#MsgVideoDetail .file')
				let html = ''

				html +=	'<div class="dialogue_box dialogue_box_right clean">';
				html +=		'<div class="time">'+data.msg.Time+'</div>';
				html +=		'<div class="message">'+global_obj.htmlspecialchars(data.msg.Content).replace(/\r\n/g, "<br />")+'</div>';
				if (data.msg.PicPath) {
					html +=		'<div class="picture"><a href="'+data.msg.PicPath+'" target="_blank" class="pic_box"><img src="'+data.msg.PicPath+'" /><span></span></a></div><div class="clear"></div>';
				}
				if (data.msg.VideoPath) {
					html +=		'<div class="picture"><a href="' + data.msg.VideoPath + '" target="_blank" class="pic_box"><img src="' + data.msg.VideoPathCover + '" /><span></span></a><i class="iconfont icon-video2"></i></div><div class="clear"></div>';
				}
				html +=	'</div>';
				html +=	'<div class="clear"></div>';
				obj.find('.message_dialogue_list').append(html);
				obj.find('.message_dialogue').animate({scrollTop:$('.pop_orders_message .message_dialogue_list').outerHeight()}, 500); //自动滚动到最后一个消息

				form.find('textarea[name=Message]').val('')
				pic.removeClass('isfile').removeClass('show_btn')
				pic.find('.preview_pic .upload_btn').show()
				pic.find('.preview_pic a').remove()
				pic.find('.preview_pic input:hidden').val('').attr('save', 0)
				video.removeClass('isfile').removeClass('show_btn')
				video.find('.preview_file .upload_btn').show()
				video.find('.preview_file a').remove()
				video.find('.preview_file input:hidden').val('').attr('save', 0)

				global_obj.win_alert_auto_close(lang_obj.manage.email.send_success, '', 1000, '8%');
			}
			return false;
		});

		frame_obj.pop_up($('.translation_tips_btn'), '.pop_translation_tips', 1, function(){
			$.post('/manage/translation/tips', {}, function(data) {
				$('.pop_translation_tips .list').html(data.msg)
			}, 'json')
		})
	},

	//客服专员和备份时间
	windows_init:function(){
		var $user_service_status=0;
		$("#header").on('click', '.menu_agent .agent_menu>a', function(){
			var _ind=$(this).index();
			$('.menu_agent .agent_menu>a').removeClass('cur').eq(_ind).addClass('cur');
			$('#header .agent_box .agent_list').hide().eq(_ind).show();
		}).on('click', '.menu_agent .iknow', function(){
			$('.menu_agent').fadeOut('1000');
			$.post('/manage/account/update-version-status');
		});

		$(window).on('resize', function(){
			if($("#user_service_box:visible").length){
				$('#user_service_box').css({left:$(window).width()/2-332});
			}
			if($("#user_backup_box:visible").length){
				$("#user_backup_box").css({left:$(window).width()/2-332});
			}
		});
	},

	category_wrap_page_init:function(){
		var resize=function(){
			$('#photo .wrap_content').css({'overflow':'auto', 'height':($(window).height()-$('.list_menu').outerHeight(true))});
			$('#file .wrap_content').css({'overflow':'auto', 'height':($(window).height()-$('.list_menu').outerHeight(true)-40)});
		};
		resize();
		$(window).resize(function(){resize();});
	},

	submit_form_init: function (o, jump, fun, debug, callback, is_pop) {
	
		if(!o.length) return false;
		frame_obj.check_amount(o); //检查价格
		o.find('input[rel=digital]').keydown(function(e){
			var value=$(this).val(),
				key=window.event?e.keyCode:e.which;
			if((key>95 && key<106) || (key>47 && key<60)){
				value==0 && $(this).val('');
			}else if(key!=8){
				if(window.event){//IE
					e.returnValue=false;
				}else{//Firefox
					e.preventDefault();
				}
				return false;
			}
		});
		if(o.find('.btn_submit').length){ //表单自身是否包含了提交按钮
			var obj=o.find('.btn_submit');
		}else{ //提交按钮在定位栏显示
			var obj=$('.fixed_btn_submit .btn_submit');
		}
		o.submit(function(){return false;});
		obj.click(function () {
		
			if($.isFunction(fun) && fun()===false){ return false; };
			if(global_obj.check_form(o.find('*[notnull]'), o.find('*[format]'), 1)){ return false; };
			if ((window.CKEDITOR_VERSION || false) && (typeof(window.CKEDITOR)=='object')) {
				Object.values(window.CKEDITOR.instances).forEach(_ => _.updateSourceElement())
			} else if(typeof(CKEDITOR)=='object'){
				for(var i in CKEDITOR.instances) CKEDITOR.instances[i].updateElement();//更新编辑器内容
			}
			var $url = o.find('input[name=do_action]').val();
			$(this).attr('disabled', true);
			$.post($url, o.serialize(), function (data) {
			 
				if (typeof callback === "function") {
					 
					callback(data);
					obj.attr('disabled', false);
				}else{
					if(debug){
						obj.attr('disabled', false);
						alert(unescape(data.replace(/\\/g, '%')));
					} else if (data.ret == 1) {
					 
						if (data.msg.jump) {
							if (data.msg.content) {
								obj.attr('disabled', false);
								global_obj.win_alert_auto_close(data.msg.content, '', 1000, '8%');
								window.location = data.msg.jump;
							 
							} else {
							 
								window.location = data.msg.jump;
							}
						} else if (data.msg) {
						 
							obj.attr('disabled', false);
							global_obj.win_alert_auto_close(data.msg, '', 1000, '8%');
						} else if (data.jump) {

							window.location = data.jump;
						}
						else if (jump) {
						
							window.location=jump;
						} else {
						 
							window.location.reload();
						}
					} else {
				 
						obj.attr('disabled', false);
						global_obj.win_alert_auto_close(data.msg, 'fail', 1000, '8%');
					}
				}
			}, debug?'text':'json');
		});
	},

	submit_object_init: function(o, jump, fun, debug, callback, extra)
	{
		if(!o.length) return false;
		frame_obj.check_amount(o); //检查价格
		o.find('input[rel=digital]').keydown(function(e){
			var value=$(this).val(),
				key=window.event?e.keyCode:e.which;
			if((key>95 && key<106) || (key>47 && key<60)){
				value==0 && $(this).val('');
			}else if(key!=8){
				if(window.event){//IE
					e.returnValue=false;
				}else{//Firefox
					e.preventDefault();
				}
				return false;
			}
		});
		if (o.find('.btn_submit').length) {
			// 表单自身是否包含了提交按钮
			var obj=o.find('.btn_submit');
		} else {
			// 提交按钮在定位栏显示
			var obj=$('.fixed_btn_submit .btn_submit');
		}
		o.submit(function() { return false; });
		obj.click(function() {
			if ($.isFunction(fun) && fun() === false) { return false; };
			if (global_obj.check_form(o.find('*[notnull]'), o.find('*[format]'), 1)){ return false; };
			if ((window.CKEDITOR_VERSION || false) && (typeof(window.CKEDITOR)=='object')) {
				Object.values(window.CKEDITOR.instances).forEach(_ => _.updateSourceElement())
			}else if (typeof(CKEDITOR)=='object'){
				for (var i in CKEDITOR.instances) CKEDITOR.instances[i].updateElement();//更新编辑器内容
			}
			var $url = o.find('input[name=do_action]').val();
			$(this).attr('disabled', true);
			let formData = o.serializeObject();
			if ($.isFunction(extra)) {
				formData = extra(formData);
			}
			$.ajax({
				url: $url,
				type: 'POST',
				dataType: debug ? 'text' : 'json',
				contentType: 'application/json',
				data: JSON.stringify(formData),
				success: function(data) {
					if (typeof callback === "function") {
						obj.attr('disabled', false);
						callback(data);
					 
					} else {
					 
						if (debug) {
							obj.attr('disabled', false);
							alert(unescape(data.replace(/\\/g, '%')));
						} else if(data.ret == 1) {
							if (data.msg.jump) {
								window.location = data.msg.jump;
							} else if (data.msg) {
								obj.attr('disabled', false);
								global_obj.win_alert_auto_close(data.msg, '', 1000, '8%');
							} else if (jump) {
								window.location = jump;
							} else {
								window.location.reload();
							}
						} else {
							obj.attr('disabled', false);
							global_obj.win_alert_auto_close(data.msg, 'fail', 1000, '8%');
						}
					}
				}
			});
		});
	},

	switchery_checkbox:function(confirmBind, cancelBind, obj){
		var $obj = '.switchery';
		if (obj) $obj = obj;
		$('body').on('click', $obj, function(){
			if ($(this).hasClass("disabled")) return false;
			if($(this).hasClass('checked')){
				$(this).removeClass('checked').find('input').prop('checked', false);
				cancelBind && cancelBind($(this));
			}else{
				$(this).addClass('checked').find('input').prop('checked', true);
				confirmBind && confirmBind($(this));
			}
		});
	},

	config_switchery:function(obj, do_action, param_0, param_1, callback){
		obj.click(function(){
			var $this=$(this),
				$data_value=$this.attr(param_0),
				$used,
				$data=new Object;
				if($this.hasClass('checked')){
					$used=0;
					$this.removeClass('checked');
				}else{
					$used=1;
					$this.addClass('checked');
				}
				$data[param_1]=$data_value;
				$data['IsUsed']=$used;
				$.post(do_action, $data, function(data){
					if(!data.ret){
						global_obj.win_alert_auto_close(data.msg, 'fail', 1000, '8%');
						if($used){
							$this.removeClass('checked');
						}else{
							$this.addClass('checked');
						}
					}else if(data.ret==1){
						global_obj.win_alert_auto_close(data.msg, '', 1000, '8%');
					}
					if(typeof callback === "function"){
						callback(data, $this);
						return false;
					}
				}, 'json');
		});
	},

	select_all: function (checkbox_select_btn, checkbox_list, process_obj, callback) {
		var process = function () {
			if (checkbox_list.parent('.current').length > 0) {
				process_obj.css('display', 'block');
			} else {
				process_obj.css('display', 'none');
			}
		};

		let global_menu_button = checkbox_select_btn.parents("ul.global_menu_button");
		var global_callback = function () {
			var $length = checkbox_list.filter(':checked').length;
			if ($length) {
				checkbox_select_btn.parents('tr,.tr').addClass('current');
				global_menu_button.find('.open').removeClass('no_select');
			} else {
				checkbox_select_btn.parents('tr,.tr').removeClass('current');
				global_menu_button.find('.open').addClass('no_select');
			}
			global_menu_button.find('.open>span').text($length);
		};

		if (global_menu_button.length) {
			global_menu_button.find('.open').click(function () {
				var $length = checkbox_list.filter(':checked').length,
					$parent = $(this).parents('tr,.tr');
				if (!$length) $(this).addClass('no_select');
				if (!$parent.hasClass('current')) {
					$parent.addClass('current');
				} else {
					if ($(this).hasClass('no_select')) {
						$parent.removeClass('current');
					}
				}
			});
		}

		checkbox_select_btn.parent().on("click", function () {
		
			var $isChecked = checkbox_select_btn.is(":checked") ? 1 : 0;

			checkbox_list.each(function () {
				if ($(this).is(':disabled')) return true;
				if ($isChecked == 1) {
					$(this).prop("checked", true);
					$(this).parent().addClass("current");
				} else {
					$(this).prop("checked", false);
					$(this).parent().removeClass("current");
				}
			});

			if ($isChecked == 1) {
				checkbox_select_btn.parent().removeClass("indeterminate");
			}

			if (global_menu_button.length) global_callback();
			if (typeof callback === "function") {
				callback(checkbox_select_btn, checkbox_list);
			}
		});

		checkbox_list.parent().on("click", function () {
			// 部分勾选
			if (checkbox_list.parent(".current").length == checkbox_list.parent().length) {
				// 全选
				checkbox_select_btn.prop("checked", true);
				checkbox_select_btn.parent().removeClass("indeterminate").addClass("current");
			} else if (checkbox_list.parent(".current").length == 0) {
				// 没有勾选
				checkbox_select_btn.prop("checked", false);
				checkbox_select_btn.parent().removeClass("current indeterminate");
			} else {
				// 部分勾选
				checkbox_select_btn.prop("checked", false);
				checkbox_select_btn.parent().removeClass("current").addClass("indeterminate");
			}

			if (global_menu_button.length) global_callback();
			if (typeof callback === "function") {
				callback(checkbox_select_btn, checkbox_list);
			}
		});
	},
	del_bat:function(btn_del_bat, checkbox_list, do_action, callback, alert_txt, confirm_txt, confirm_btn){
		btn_del_bat.on('click', function(){
			var id_list='';
			checkbox_list.each(function(index, element) {
				id_list+=$(element).get(0).checked?$(element).val()+'-':'';
			});
			if(id_list){
				id_list=id_list.substring(0,id_list.length-1);
				if(typeof callback === "function"){
					callback(id_list);
				}else{
					let btn_text = ''
					let btn_class = ''
					if (typeof(confirm_btn) == 'object') {
						btn_text = confirm_btn.text
						btn_class = confirm_btn.class
					} else {
						btn_text = confirm_btn;
					}
					let params = {
						'title':confirm_txt ? confirm_txt : lang_obj.global.del_confirm,
						'confirmBtn':btn_text ? btn_text : lang_obj.global.del,
						'confirmBtnClass':btn_class ? btn_class : (btn_text ? '' :'btn_warn')
					};
					global_obj.win_alert(params, function(){
						$.get(do_action, {id:id_list}, function(data){
							if(data.ret==1){
								window.location.reload();
							} else if (data.ret == 0 && data.msg != '') {
								global_obj.win_alert_auto_close(data.msg, 'fail', 1000, '8%');
							}
						}, 'json');
					}, 'confirm');
				}
			}else{
				global_obj.win_alert(alert_txt?alert_txt:lang_obj.global.del_dat_select);
			}
		});
	},

	del_init:function(o, confirm_txt, jump_url){
		o.find('.del').click(function(){
			var o=$(this);
			let params = {
				'title':confirm_txt?confirm_txt:lang_obj.global.del_confirm,
				'confirmBtn':lang_obj.global.del,
				'confirmBtnClass':'btn_warn'
			};
			if ($(this).hasClass("disabled")) {
				// 被禁止
				return false;
			}
			global_obj.win_alert(params, function(){
				$.get(o.attr('href'), function(data){
					if (data.ret == 1) {
					 
						if (data.msg.content) {
							 
							window.location = data.msg.jump;
						} else if (jump_url) {
							 
							window.location.href=jump_url;
						} else {
						 
							window.location.reload();
						}
					} else {
						 
						global_obj.win_alert(data.msg);
					}
				}, 'json');
			}, 'confirm');
			return false;
		});
	},

	dragsort:function(obj, do_action, dragSelector, dragSelectorExclude, placeHolderTemplate, itemSelector, callback){
		typeof(dragSelector)=='undefined' && (dragSelector='tr');
		typeof(dragSelectorExclude)=='undefined' && (dragSelectorExclude='a, td[data!=move_myorder]');
		typeof(placeHolderTemplate)=='undefined' && (placeHolderTemplate='<tr class="placeHolder"></tr>');
		typeof(itemSelector)=='undefined' && (itemSelector='');
		obj.dragsort({
			dragSelector:dragSelector,
			dragSelectorExclude:dragSelectorExclude,
			placeHolderTemplate:placeHolderTemplate,
			itemSelector:itemSelector,
			scrollSpeed:5,
			dragEnd:function(){
				if(do_action){
					var target=(itemSelector?itemSelector:dragSelector);
					var data=obj.find(target).map(function(){
						return $(this).attr('data-id');
					}).get();
					$.get(do_action, {sort_order:data.join(',')});
				}
				if(typeof callback === "function"){
					callback();
				}
			}
		});
	},

	fixed_list_menu: function() {
		let $ScrollTop = $('.r_con_wrap').scrollTop(),
			$Obj = $('.list_menu'),
			$SideTop = $Obj.offset().top,
			$SideHeight = $Obj.outerHeight(),
			$BoxWidth = $Obj.width(),
			$BoxHeight = $Obj.outerHeight(true),
			$BoxTop = $('#header').outerHeight(true),
			$BoxLeft = $Obj.offset().left,
			$HeaderHeight = $('#header').outerHeight(true);
			if ($Obj.hasClass('no_fixed')) return false;
			if ($('#header_real_tips').length) $BoxTop = $BoxTop+$('#header_real_tips').outerHeight(true);
		if ($(".list_menu_support_div").length) {
			$SideTop = $(".list_menu_support_div").offset().top;
		}
		if ($ScrollTop > ($SideTop + $BoxHeight)) {
			if (!$('.list_menu_support_div').length) $Obj.after('<div class="list_menu_support_div"></div>');
			$('.list_menu_support_div').height($BoxHeight);
			$Obj.css({'width': $BoxWidth, 'position': 'fixed', 'top': $BoxTop, 'left': $BoxLeft});
			$Obj.addClass('fixed');
		} else {
			$('.list_menu_support_div').remove();
			$Obj.removeAttr('style');
			$Obj.removeClass('fixed');
			$('.inside_table').removeAttr('style');
		}
	},

	fixed_right_div_mask:function(remove){
		if(remove==1){
			$('#fixed_right_div_mask').remove();
		}else{
			if(!$('#fixed_right_div_mask').length){
				$('body').prepend('<div id="fixed_right_div_mask"></div>');
				$('#fixed_right_div_mask').show();
				if ($("#header_real_tips").length) {
					// 头部提示
					let $headerTop = $("#header_real_tips").height() + $("#header").height();
					$('#fixed_right_div_mask').css({'top':$headerTop});
				}
			}
		}
	},

	//右侧滑窗弹入
	fixed_right:function(click_obj, target_class){
		var fun=(typeof(arguments[2])=='undefined')?'':arguments[2];
		var before_fun=(typeof(arguments[3])=='undefined')?'':arguments[3];
		if ($("#header_real_tips").length) {
			// 头部提示
			let $headerTop = $("#header_real_tips").height() + $("#header").height();
			$('#fixed_right').css({'top':$headerTop});
		}
		click_obj.off('click').on('click', function(){
			if($.isFunction(before_fun)){
				if (before_fun($(this)) === false) {
					return false;
				}
			}
			var $width = $(target_class).attr('data-width');
			if($width){
				$('#fixed_right').css({'right':0, 'width':$width+'px'});
			}else{
				$('#fixed_right').addClass('show').removeAttr('style');
			}
			$('#fixed_right').find(target_class).show().siblings().hide();
			var $box_height = $('#fixed_right').height(),
				$sub_height = $(target_class).find('.box_submit').length ? 53 : 0,
				$box_less = $(target_class).outerHeight(true)-$(target_class).height()+$sub_height;
				$(target_class).height($box_height-$box_less);
			frame_obj.fixed_right_div_mask();
			if($.isFunction(fun)){ fun($(this)); }
		});
		function close_fixed_right(){
			var $w = $('#fixed_right>.global_container:visible').attr('data-width');
			if($w){
				$('#fixed_right').css('right', '-'+$w+'px');
			}else{
				$('#fixed_right').removeClass('show').removeAttr('style');
			}
			if ($("#header_real_tips").length) {
				// 头部提示 (保留top值)
				let $headerTop = $("#header_real_tips").height() + $("#header").height();
				$('#fixed_right').css({'top':$headerTop});
			}
			if($('.fixed_right_multi').length) $('.fixed_right_multi').remove();
			$('#fixed_right>.global_container').hide();
			frame_obj.fixed_right_div_mask(1);
		}
		$('#fixed_right').off().on('click', '.close, .btn_cancel', function(){
			close_fixed_right();
		});
		$('body').on('click', '#fixed_right_div_mask', function(){
			close_fixed_right();
			//console.log($(this).data('id'));
			//if ($(this).data('id')) {
			//	$($(this).data('id')).hide();
			//	console.log($(this).data('id'));
			//	setTimeout(() => {
			//		$($(this).data('id')).remove();
			//	}, 2000);
			//}
		});
	},

	//右侧滑窗弹入[二级以上]
	fixed_right_multi:function(click_class, target_class)
	{
		var fun=(typeof(arguments[2])=='undefined')?'':arguments[2];
		$('body').on('click', click_class, function(){
			var $width = $(target_class).attr('data-width')?$(target_class).attr('data-width'):$('#fixed_right').width();
			var $height = $('#fixed_right').height() - 50 - 53;
			var size=$('.fixed_right_multi').length+1;
			var html=$(target_class).prop('outerHTML');
			$('#fixed_right').after('<div id="fixed_right_multi_'+size+'" class="fixed_right_multi"></div>');
			$('#fixed_right_multi_'+size).html(html);
			$('#fixed_right_multi_'+size).css({'width':$width, 'right':-$width, 'z-index':10001+size}).find('.global_container').css('height', $height).show();
			$('#fixed_right_multi_'+size).css('right', 0);
			//关闭事件
			$('#fixed_right_multi_'+size+' .close, .btn_cancel').on('click', function(){
				$('#fixed_right_multi_'+size).css('right', -$width);
				setTimeout(function(){$('#fixed_right_multi_'+size).remove();}, 500);
			});
			if($.isFunction(fun)){ fun($(this), $('#fixed_right_multi_'+size)); }
		});
	},

	//右侧滑窗弹入[二级以上]
	fixed_right_multi_plus:function(click_class, target_class)
	{
		var fun=(typeof(arguments[2])=='undefined')?'':arguments[2];
		var before_fun=(typeof(arguments[3])=='undefined')?'':arguments[3];
		click_class.click(function(){
			if($.isFunction(before_fun)){
				if (before_fun($(this)) === false) {
					return false;
				}
			}
			var $width = $(target_class).attr('data-width')?$(target_class).attr('data-width'):$('#fixed_right').width();
			var $height = $('#fixed_right').height() - 50 - 53;
			var size=$('.fixed_right_multi').length+1;
			var html=$(target_class).prop('outerHTML');
			$('#fixed_right').after('<div id="fixed_right_multi_'+size+'" class="fixed_right_multi"></div>');
			$('#fixed_right_multi_'+size).html(html);
			$('#fixed_right_multi_'+size).css({'width':$width, 'right':-$width, 'z-index':10001+size}).find('.global_container').css('height', $height).show();
			$('#fixed_right_multi_'+size).css('right', 0);
			//关闭事件
			$('#fixed_right_multi_'+size+' .close, .btn_cancel').on('click', function(){
				$('#fixed_right_multi_'+size).css('right', -$width);
				setTimeout(function(){$('#fixed_right_multi_'+size).remove();}, 500);
			});
			if($.isFunction(fun)){ fun($(this), $('#fixed_right_multi_'+size)); }
		});
	},

	pop_up:function(click_obj, target_class){
		var number=(typeof(arguments[2])=='undefined')?1:arguments[2],
			fun=(typeof(arguments[3])=='undefined')?'':arguments[3];
		click_obj.off().on('click', function(){
			$(target_class).addClass('show');
			number==1 && frame_obj.fixed_right_div_mask();
			if($.isFunction(fun)){ fun($(this)); }
		});
		if(number==1){ //第一层
			$(target_class).off().on('click', '.btn_close, .btn_cancel', function(){
				$(target_class).removeClass('show');
				frame_obj.fixed_right_div_mask(1);
			});
		}else{
			$(target_class).off().on('click', '.btn_close, .btn_cancel', function(){
				$(target_class).removeClass('show');
			});
		}
		$('body').on('click', '#fixed_right_div_mask', function(){
			$(target_class).removeClass('show');
			frame_obj.fixed_right_div_mask(1);
		});
	},

	pop_form:function(obj, remove, no_mask){ //弹出编辑框
		if(remove==1){
			obj.slideUp(250, function(){
				no_mask==1 && global_obj.div_mask(1);
			});
		}else{
			global_obj.div_mask();
			if(obj.hasClass('photo_choice')){ //图片银行
				obj.css({'top':0, 'opacity':0}).show().animate({'top':30, 'opacity':1}, 250);
			}else if(obj.hasClass('gallery_pop')){  //图库
				obj.css({'top':0, 'opacity':0}).show().animate({'top':50, 'opacity':1}, 250);
			}else if(obj.hasClass('products_choice') || obj.hasClass('products_choice_v2')){  //产品弹窗
				obj.css({'top':0, 'opacity':0}).show().animate({'top':50, 'opacity':1}, 250);
			}else if(obj.hasClass('file_choice')){  //视频银行
				obj.css({'top':0, 'opacity':0}).show().animate({'top':30, 'opacity':1}, 250);
			}else{
				obj.css({'top':110, 'opacity':0}).show().animate({'top':150, 'opacity':1}, 250);
			}
			if($(document).height()<=680){
				obj.css('top', 70);
				obj.find('.r_con_form').css({'max-height':350});
			}
			obj.find('.t h2').add(obj.find('.btn_cancel')).click(function(){
				if(obj.hasClass('photo_choice')) { //图片银行
					obj.slideUp(250, function () {
						no_mask == 1 && global_obj.div_mask(1);
					});
				}else if (obj.hasClass('products_choice') || obj.hasClass('products_choice_v2')) {  // 产品弹出框
					obj.slideUp(250, function () {
						no_mask == 1 && global_obj.div_mask(1);
						$(this).remove();
					});
				}else if(obj.hasClass('file_choice')) { //视频银行
					obj.slideUp(250, function () {
						no_mask == 1 && global_obj.div_mask(1);
					});
				}else{
					obj.fadeOut(250, function(){
						no_mask==1 && global_obj.div_mask(1);
					});
				}
			});
		}
	},

	pop_iframe:function(url, title, no_mask, callback){ //弹出框架显示框
		var o, $html='';
		$html+='<div class="pop_form pop_iframe">';
			$html+='<form id="pop_iframe_form" class="w_1000">';
				$html+='<div class="t"><h1>'+title+'</h1><h2>×</h2></div>';
				$html+='<div class="r_con_form"><iframe src="" frameborder="0"></iframe></div>';
			$html+='</form>';
		$html+='</div>';
		$('.r_con_wrap').append($html);
		o=$('.pop_iframe');
		frame_obj.pop_form(o, 0, no_mask, callback);
		setTimeout(function(){
			o.find('iframe').attr('src', url+'&iframe=1&r='+Math.random()).load(function(){ //效果执行完，才加载内容显示
				iframe_resize();
			});
		}, 200);

		var resize=function(){
			var $h=$(window).height()-o.find('form>.t').outerHeight()-70;
			o.find('.r_con_form').css({'height':$h, 'max-height':$h});
		}, iframe_resize=function(){
			var $h=$(window).height()-o.find('form>.t').outerHeight()-70,
				$iframe=o.find('iframe').contents();
				$iframe.find('.r_con_wrap').css({'overflow':'auto', 'height':($h-10)});
				$iframe.find('.menu_list').height($iframe.find('.r_con_wrap').height()-$iframe.find('.nav_list').outerHeight(true)-10);
				$iframe.find('.shipping_area_edit').height($iframe.find('.r_con_wrap').height()-$iframe.find('.nav_list').outerHeight(true)-8);
		}
		resize();
		$(window).resize(function(){
			resize();
			if(o.find('.r_con_form').outerHeight()>300){ //已经固定min-height为300px
				iframe_resize();
			}
		});

		o.find('.t h2').add(o.find('.btn_cancel')).click(function(){ //取消
			frame_obj.pop_form(o, 1, no_mask, callback);
			o.remove();
		});
	},

	// 固定页面弹窗
	fixed_box_popup: function(opt) {
		let defaults = {
			clickObj: {}, // 点击按钮
			targetClass: "test", // 目标类名
			onClick: function() {}, // 点击事件
			onClose: function() {}, // 关闭事件
			openAfter: function() {} //打开窗口后事件
		};
		opt = $.extend({}, defaults, opt || {});

		const FUNC = {
			Open : (e) => {
				if ($.isFunction(opt.onClick)) {
					if (opt.onClick(e) === false) {
						return false;
					}
				}
				let $width = Obj.attr("data-width");
				if ($width) {
					Obj.find(".box_middle").css({"width": $width + "px"});
				}
				$(".fixed_box_popup").hide();
				Obj.show();
				Obj.find(".box_middle").css({"top": -30, "opacity": 0}).animate({"top": 0, "opacity": 1}, 250);
				if ($.isFunction(opt.onClick)) {
					if (opt.openAfter(e) === false) {
						return false;
					}
				}
			},
			Close : (e) => {
				// 关闭
				if ($.isFunction(opt.onClose)) {
					if (opt.onClose(e) === false) {
						return false;
					}
				}
				Obj.find(".box_middle").animate({"top": -30}, 250, function() {
					$(this).css({"opacity": 0})
					Obj.hide();
				});
			}
		}
		const Obj = $("." + opt.targetClass);

		opt.clickObj.on("click", function() {
			FUNC.Open({
				this: $(this),
				target: Obj,
			});
		});
		Obj.on("click", ".close, .btn_cancel", function() {
			FUNC.Close({
				this: $(this),
				target: Obj,
			});
		});
		Obj.on("click", function(e) {
			if ($(e.target).hasClass(opt.targetClass)) FUNC.Close();
		});

		
	},

	upload_img_detail:function(img){
		if(!img){return;}
		var del=(typeof(arguments[1])=='undefined')?'':arguments[1];
		return '<a href="javascript:;"><img src="'+img+'"><em></em></a>';
	},

	pop_contents_close_init:function(o, remove, no_mask, callback){
		if(callback) eval(callback); //执行回调函数
		frame_obj.pop_form(o, 1, no_mask);
		remove==1?o.remove():o.hide();
	},

	photo_choice_iframe_init:function(url, id, no_mask, callback){
		var $Obj, $html='';
		$html+='<div class="pop_form photo_choice">';
			$html+='<form id="photo_choice_edit_form">';
				$html+='<div class="t"><h1>'+lang_obj.manage.photo.picture_upload+'</h1><h2>×</h2></div>';
				$html+='<div class="r_con_form"><iframe src="" frameborder="0"></iframe></div>';
				$html+='<div class="button"><input type="submit" class="btn_global btn_submit" id="button_add" name="submit_button" value="'+lang_obj.global.submit+'" /><input type="button" class="btn_global btn_cancel" value="'+lang_obj.global.cancel+'" /></div>';
			$html+='</form>';
		$html+='</div>';
		$('#righter').append($html);
		$Obj=$('.photo_choice');
		frame_obj.pop_form($Obj, 0, no_mask, callback);
		setTimeout(function(){
			$Obj.find('iframe').attr('src', url+'&iframe=1&r='+Math.random()); //效果执行完，才加载内容显示
		}, 200);

		var resize=function(){
			var $h = $(window).height()-$Obj.find('form>.t').outerHeight(true)-$Obj.find('form>.button').outerHeight(true)-70;
			$Obj.find('.r_con_form').css({'height':$h, 'max-height':$h});
		}
		resize();
		$(window).resize(function(){resize();});

		$Obj.find('#button_add').click(function(){ //提交
			var obj=$Obj.find('iframe').contents(),
				id=obj.find('input[name=id]').val(),//显示元素的ID
				type=obj.find('input[name=type]').val(),//类型
				maxpic = obj.find('input[name=maxpic]').val();//最大允许图片数
		 
			frame_obj.photo_choice_return(id, type, maxpic);
			return false;
		});
		$Obj.find('.t h2').add($Obj.find('.btn_cancel')).click(function(){ //取消
			frame_obj.pop_contents_close_init($Obj, 1, no_mask, callback);
		});
	},

	/**
	 * 产品弹出选择框（类似图片上传框）
	 * @param title 弹窗标题
	 * @param submit_txt 弹窗提交按钮文字
	 * @param search_callback 搜索后callback
	 */
	products_choice_iframe_init:function(title, submit_txt, search_callback)
	{
		// 构建html
		var $Obj, $html='';
		$html += '<div class="pop_form products_choice">';
			$html += '<form id="products_choice_edit_form">';
				$html += '<div class="t"><h1>' + title + '</h1><h2>×</h2></div>';
				$html += '<div class="products_choice_con">';
					$html += '<div class="list_menu"></div>';
					$html += '<div class="wrap_content products_list auto_load_products"><div class="products_list_box" page="1" total-pages="1"></div></div>';
				$html += '</div>';
				$html += '<div class="products_choice_button_list">';
					$html += '<div class="products_choice_button_list_box">';
						$html += '<div class="products_select"></div>';
						$html += '<div class="products_button"><input type="button" class="btn_global btn_submit" value="' + (submit_txt ? submit_txt : lang_obj.global.confirm) + '"></div>';
						$html += '<div class="products_number"><span class="cur">0</span> / <span>0</span></div>';
						$html += '<div class="products_arrow"></div>';
						$html += '<div class="clear"></div>';
					$html += '</div>';
				$html += '</div>';
			$html += '</form>';
		$html += '</div>';
		$('#righter').append($html);
		// 弹窗
		$Obj=$('.products_choice');
		frame_obj.pop_form($Obj, 0, 1);
		// 响应式
		var resize=function(){
			var $h=$(window).height()-$Obj.find('form>.t').outerHeight()-$Obj.find('form>.products_choice_button_list').outerHeight()-110;
			$Obj.find('.products_choice_con').css({'height':$h, 'max-height':$h});
			// 产品栏宽度设置
			var products_select_width = $Obj.find('.products_choice_button_list').width() - $Obj.find('.products_choice_button_list .products_button').outerWidth('true') - $Obj.find('.products_choice_button_list .products_number').outerWidth('true') - $Obj.find('.products_choice_button_list .products_arrow').outerWidth('true') - 30;
			$Obj.find('.products_choice_button_list .products_select').width(products_select_width);
		}
		resize();
		$(window).resize(function(){resize();});
		// 加载产品搜索框
		$.post('/manage/action/products-choice-search', '', function(data){
			if (data.ret == 1) {
				$Obj.find('.products_choice_con .list_menu').html(data.msg);
				frame_obj.search_form_init();
				$Obj.find('.products_choice_con .list_menu .search_btn').on('click', function(){
					is_animate = 1;
					search_form_search('html');
				});
				$Obj.find('.products_choice_con .list_menu .search_btn').trigger('click');
			}
		}, 'json');
		// 产品弹出框搜索
		var search_form_search = function(type){  // type html|append
			var obj = $Obj.find('.products_choice_con .list_menu .search_form');
			var Keyword = obj.find('input[name=Keyword]').val();
			var CateId = obj.find('select[name=CateId]').val();
			var Page = type == 'append' ? $Obj.find('.products_choice_con .products_list .products_list_box').attr('page') : 1;
			if (CateId == '') CateId = 0;
			$.post('/manage/action/products-choice-search-action',{'Keyword':Keyword, 'CateId':CateId, 'Page':Page}, function(data){
				if (data.ret == 1) {
					var html = '';
					for (var key in data.msg[0]) {
						html += '<div class="item" data-proid="' + data.msg[0][key]['ProId'] + '">';
							html += '<div class="img">';
								html += '<img src="' + data.msg[0][key]['PicPath_0'] + '" />';
								html += '<span></span>';
								html += '<div class="img_mask"></div>';
							html += '</div>';
							html += '<div class="name">' + data.msg[0][key]['Name_' + shop_config.lang] + '</div>';
						html += '</div>';
					}
					if (type == 'append') { //翻页
						$('.products_choice .products_list .products_list_box').attr({'page':Page, 'total-pages':data.msg[3]}).append(html);
					} else {  // 搜索
						$Obj.find('.auto_load_products').scrollTop(0);
						$('.products_choice .products_list .products_list_box').attr({'page':'1', 'total-pages':data.msg[3]}).html(html);
					}
					search_callback && search_callback();
					is_animate = 0;
				}
			}, 'json');
		};
		// 产品下拉翻页
		var is_animate = 0;
		$Obj.find('.auto_load_products').scroll(function(){
			var viewHeight = $(this).outerHeight(true), //可见高度
				contentHeight = $(this).get(0).scrollHeight, //内容高度
				scrollHeight = $(this).scrollTop(), //滚动高度
				page = parseInt($(this).children().attr('page')),
				total_pages = parseInt($(this).children().attr('total-pages'));
			if((contentHeight - viewHeight <= scrollHeight) && is_animate==0 && page < total_pages){
				is_animate = 1;
				$(this).children().attr('page', page + 1);
				search_form_search('append');
			}
			if(page >= total_pages && is_animate == 0){
				is_animate = 1;
			}
			if(page >= total_pages && is_animate == 1 && contentHeight - viewHeight == scrollHeight){
				global_obj.win_alert_auto_close(lang_obj.manage.sales.lastpage, 'await', 2000, '8%', 0);
			}
		});
	},

	/**
	 * 产品弹出选择框
	 * @param object object 参数对象
	 */
	products_choice_iframe_init_v2:function(object)
	{

		// 产品弹出框搜索
		let search_form_search = function(action) {
			let obj = $Obj.find('.middle_top_menu .list_menu .search_form');
			let cateId = obj.find("input[name=cateId]").val();// 分类
			let tagId = [];// 标签
			obj.find("input[name='products_tagsCurrent[]']").each(function(index, element) {
				tagId[index] = $(element).val();
			});
			let moreId = [];
			obj.find("input[name='more_filterCurrent[]']").each(function(index, element) {
				moreId[index] = $(element).val();
			});
			tagId = tagId.join(",");
			moreId = moreId.join(",");
			obj.find('input[name=cateId]').val(cateId);
			obj.find('input[name=tagId]').val(tagId);
			obj.find('input[name=moreId]').val(moreId);
			let Keyword = obj.find('input[name=Keyword]').val();
			cateId = obj.find('input[name=cateId]').val();
			tagId = obj.find('input[name=tagId]').val();
			moreId = obj.find('input[name=moreId]').val();
			let Page = action == 'turn' ? $Obj.find('.middle_content .products_list .products_list_box').attr('page') : 1;
			let Count = ptype == 'manual' ? 32 : $Obj.find('.middle_content .products_number input[name=Number]').val();
			if (cateId == '') cateId = 0;

			if (ptype == 'auto') {
				_autoObj = $Obj.find('.middle_content .product_number_container');
				cateId = _autoObj.find("input[name=cateId]").val();
				_scopeType = _autoObj.find('.box_type_menu .auto_scope_item.checked').attr('data-type');
				if (_scopeType == 'all') {
					cateId = 0;
				}
				tagId = [];// 标签
				_autoObj.find(".select_list .btn_attr_choice").each(function(index, element) {
					tagId[index] = $(element).find('input[name="products_tagsOption[]"]').val();
				});
				tagId = tagId.join(",");

				moreIdString = _autoObj.find(".auto_type_box .input_radio_box.checked input[name='select_type']").val();
				moreIdData = lang_obj.manage.products.select_tpye[moreIdString];
				moreId = moreIdData.val
			}
			$.post('/api/OrderList/products-choice-search-action-v2',{'Keyword':Keyword, 'CateId':cateId, 'TagId':tagId, 'MoreId': moreId, 'Page':Page, 'Count':Count,'exclude':excludeValue}, function(data){
				if (data.ret == 1) {
					var html = '';
					let _proData = data.msg.products_row;
					let _turnPageHtml = data.msg.turnPageHtml;
					for (var key in _proData[0]) {
						html += '<div class="item" data-proid="' + _proData[0][key]['ProId'] + '">';
							html += '<div class="img">';
								html += '<img src="' + _proData[0][key]['PicPath_0'] + '" />';
								html += '<span></span>';
								html += '<div class="img_mask"></div>';
							html += '</div>';
							html += '<div class="name">' + _proData[0][key]['Name_' + shop_config.lang] + '</div>';
						html += '</div>';
					}
					console.log(_proData[3]);
					if (action == 'turn') { //翻页
						$Obj.find('.middle_content .products_list .products_list_box').attr({'page':Page, 'total-pages':_proData[3]}).html(html);
					} else {  // 搜索
						$Obj.find('.auto_load_products').scrollTop(0);
						$Obj.find('.middle_content .products_list .products_list_box').attr({'page':'1', 'total-pages':_proData[3]}).html(html);
					}
					//翻页
					$Obj.find('.middle_content .turn_page_box').html(_turnPageHtml);
					$('#turn_page li a').each(function(){
						let _thisAHref = $(this).attr('href');
						if (_thisAHref) {
							$(this).attr('href','javascript:;');
						}
					})
					products_current_init();
					products_dragsort_init();
					is_animate = 0;
					if (ptype == 'auto') products_auto_select();
					checkProductsChecked(); //检查勾选状态
					if (hiddenRightBox) {
						$Obj.find('.product_list_container .products_select').hide();
						$Obj.find('.product_list_container .products_list').addClass('full_width');
						$Obj.find('.product_list_container .turn_page_box').addClass('full_width');
					} else {
						$Obj.find('.product_list_container .products_select').show();
						$Obj.find('.product_list_container .products_list').removeClass('full_width');	
						$Obj.find('.product_list_container .turn_page_box').removeClass('full_width');	
					}
					if (IsNeedSelectCateId) {
						$Obj.find('.middle_top_menu .search_box_selected').hide();
					}
				}
			}, 'json');
		};
		// 搜索完产品着色
		let products_current_init = function(){
			$Obj.find('.product_list_container .products_select .pro_item_list').each(function(){
				let proid = $(this).data('proid');
				$Obj.find(`.middle_content .products_list_box .item[data-proid=${proid}]`).addClass('cur');
			});
		}
		// 筛选框构建
		let products_filter_init = function () {
			let filterObj = $Obj.find('.middle_top_menu .list_menu .search_form');
			let tagId = [];// 标签
			filterObj.find("input[name='products_tagsCurrent[]']").each(function(index, element) {
				tagId[index] = $(element).val();
			});
			let moreId = [];
			filterObj.find("input[name='more_filterCurrent[]']").each(function(index, element) {
				moreId[index] = $(element).val();
			});
			tagId = tagId.join(",");
			moreId = moreId.join(",");
			filterObj.find('input[name=tagId]').val(tagId);
			filterObj.find('input[name=moreId]').val(moreId);

			let p_cateId = filterObj.find('input[name=cateId]').val();
			let p_tagId = filterObj.find('input[name=tagId]').val();
			let p_moreId = filterObj.find('input[name=moreId]').val();
			$.post('/api/OrderList/SelectProductsFilter',{'CateId':p_cateId, 'TagId':p_tagId, 'MoreId': p_moreId}, function(data){
				if (data.ret == 1) {
					$Obj.find('.middle_top_menu .search_form .ext').html(data.msg);
					if($('.list_menu .search_form .ext').is(':hidden')){
						$('.list_menu .search_form .ext').show();
						$('.list_menu .search_form form').css('border-radius', '5px 5px 0 0');
						$('.list_menu .search_form .more').addClass('more_up');
					}else{
						$('.list_menu .search_form .ext').hide();
						$('.list_menu .search_form form').css('border-radius', '5px');
						$('.list_menu .search_form .more').removeClass('more_up');
					}
					if (IsNeedSelectCateId) {
						$Obj.find('.middle_top_menu .list_menu .search_form .ext .item.i_select_category').hide();
					}
				}
			},'json')
		}
		// 勾选产品增加小图
		let products_add_html = function(proid, proimg, proname = '') {
			let html = '';
			html += '<div class="pro_item_list pic_box" data-proid="' + proid + '">';
				html += '<div class="item_img">';
					html += '<img src="' + proimg + '" />';
					html += '<strong>' + proname + '</strong>';
					if (isOrder) {
					html += '<span></span>';
					}
					html += '<i></i>';
					html += '<input type="hidden" name="ProId[]" value="' + proid + '" />';
				html += '</div>';
			html += '</div>';
			$Obj.find('.product_list_container .products_select').append(html);
		}
		// 小图拖动初始化
		let products_dragsort_init = function(){
			let dragObj = $Obj.find('.product_list_container .products_select');
			dragObj.dragsort('destroy');
			if (isOrder) {
				frame_obj.dragsort(dragObj, '', '.pro_item_list', 'i', '<div class="pro_item_list placeHolder"></div>');
			}
		}
		// 智能添加选中所有产品
		let products_auto_select = function(){
			number = 0;
			$Obj.find('.product_list_container .products_select').html('');
			$Obj.find('.middle_content .products_list_box .item').each(function(){
				let proid = $(this).data('proid');
				let proimg = $(this).find('.img img').attr('src');
				let proname = $(this).find('.name').text();
				products_add_html(proid, proimg, proname);
				number++;
			});
			$Obj.find('.middle_top_menu .products_limit span.cur').text(number);
			$Obj.find('.product_list_container .products_select').dragsort('destroy');
		}
		//组建弹窗html
		let pop_html = function(){
			let $html = '';
			$html += '<div class="pop_form products_choice_v2">';
				$html += '<form id="products_choice_edit_form">';
					$html += '<div class="t"><h1>' + iframeTitle + '</h1><h2>×</h2></div>';
					$html += '<div class="middle_top_menu">';
						$html += '<div class="list_menu"></div>';
						$html += '<div class="search_box_selected"></div>';
					$html += '</div>';
					$html += '<div class="middle_content" data-addtype="' + (ptype == 'auto' ? 'auto' : '') + '">';
						$html += '<div class="middle_container product_list_container" data-type="" ' + (ptype == 'auto' ? 'auto' : '') + '>';
							$html += '<div class="products_list auto_load_products ' + (ptype == 'manual' ? '' : 'hide') + '"><div class="products_list_box" page="1" total-pages="1"></div></div>';
							$html += '<div class="products_select"></div>';
							$html += '<div class="turn_page_box"></div>';
						$html += '</div>';
						$html += '<div class="middle_container product_number_container ' + (ptype == 'manual' ? 'hide' : '') + '">';
							$html += `
								<div class="rows">
									<div class="p_auto_title title">${lang_obj.manage.products.add_conditions}</div>
									<div class="p_auto_tips">${lang_obj.manage.products.add_conditions_tips}</div>
								</div>
								<div class="rows">
									<div class="title">${lang_obj.manage.products.scope_application}</div>
									<div class="box_type_menu">
										<span class="item auto_scope_item checked" data-type="all"><input type="radio" name="scope_type" value="all" checked="checked">${lang_obj.manage.products.scope_type.all}</span>
										<span class="item auto_scope_item" data-type="category"><input type="radio" name="scope_type" value="category">${lang_obj.manage.products.scope_type.category}</span>
									</div>
									<div class="auto_category_select_box"></div>
								</div>
								<div class="rows">
									<div class="title">${lang_obj.manage.products.tags_title}</div>
									<div class="auto_tags_box"></div>
								</div>
								<div class="rows">
									<div class="title">${lang_obj.manage.products.select_tpye_title}</div>
									<div class="input auto_type_box">`
										for (k in lang_obj.manage.products.select_tpye) {
											let _typeTitle =  lang_obj.manage.products.select_tpye[k].title
											let _typeDesc =  lang_obj.manage.products.select_tpye[k].desc
											$html += `
											<span class="input_radio_box input_radio_side_box tab_option ${(k == 'new' ? ' checked' : '')}">
												<span class="input_radio"><input type="radio" name="select_type" value="${k}"></span>
												<strong class="fs14">${_typeTitle}</strong><p class="fs12">${_typeDesc}</p>
											</span>
											`;
										}
						$html +=	`</div>
								</div>
							`;
							$html += '<div class="title">' + lang_obj.manage.products.show_number + '</div>';
							$html += '<div class="products_number"><input class="box_input" type="text" name="Number" rel="amount" value="' + pnumber + '" size="38" /></div>';
						$html += '</div>';
					$html += '</div>';
					$html += '<div class="products_choice_button_list ' + (ptype == 'auto' ? 'auto' : '') + '">';
						$html += '<div class="products_choice_button_list_box">';
							$html += '<div class="products_button"><input type="button" class="btn_global btn_submit" value="' + lang_obj.global.confirm + '"></div>';
							$html += '<div class="clear"></div>';
						$html += '</div>';
					$html += '</div>';
				$html += '</form>';
			$html += '</div>';
			return $html;
		}
		//组件无数据html
		let no_data_html = function(){
			let _html = `
				<div class="bg_no_table_data">
					<div class="content">
						<p>${lang_obj.manage.error.no_table_data}</p>
					</div><span></span>
				</div>
			`;
			return _html;
		}
		// 确认时候获取数据对象
		let get_data = function(){
			let result = {};
			// 类型
			result.type = ptype;
			// 勾选的的产品
			let productsAry = {};
			$Obj.find('.product_list_container .products_select .pro_item_list').each(function(index){
				let proid = $(this).data('proid');
				let proimg = $(this).find('img').attr('src');
				let proname = $(this).find('strong').text();
				productsAry[index] = {
					proid: proid,
					image: proimg,
					name: proname
				};
			});
			result.value = productsAry;
			// 筛选
			let filterAry = {};
			filterAry.cateid = $Obj.find('.middle_top_menu .search_form input[name=cateId]').val();
			filterAry.tagid = $Obj.find('.middle_top_menu .search_form input[name=tagId]').val();
			filterAry.more = $Obj.find('.middle_top_menu .search_form input[name=moreId]').val();
			// 显示数量
			filterAry.number = $Obj.find('.middle_content .products_number input[name=Number]').val();
			if (ptype == 'auto') {
				let _autoObj = $Obj.find('.middle_content .product_number_container');
				let cateId = _autoObj.find("input[name=cateId]").val();
				let _scopeType = _autoObj.find('.box_type_menu .auto_scope_item.checked').attr('data-type');
				if (_scopeType == 'all') {
					cateId = 0;
				}
				let tagId = [];// 标签
				_autoObj.find(".select_list .btn_attr_choice").each(function(index, element) {
					tagId[index] = $(element).find('input[name="products_tagsOption[]"]').val();
				});
				tagId = tagId.join(",");
				let moreIdString = _autoObj.find(".auto_type_box .input_radio_box.checked input[name='select_type']").val();
				let moreIdData = lang_obj.manage.products.select_tpye[moreIdString];
				let moreId = moreIdData.val
				filterAry.cateid = cateId
				filterAry.tagid = tagId
				filterAry.more = moreId
			}
			result.filter = filterAry;
			return result;
		}
		// 产品弹窗初始化
		let products_data_init = function(){
			// 显示限制数量
			let limitObj = $Obj.find('.middle_top_menu .products_limit');
			limitObj.find('span.cur').text(number).nextAll('span.total').text(plimit);
			if (plimit == 0) limitObj.find('span.gap, span.total').hide();
			// 赋值数据
			if (ptype == 'manual') {
				for (let key in pvalueOrder) {
					let proid = pvalueOrder[key];
					if ($.inArray(proid, excludeValue) != -1) continue;
					products_add_html(proid, pvalue[proid].image);
				}
			}
		}
		// 筛选找色初始化
		let products_filter_current_init = function () {
			let searchBox = $Obj.find('.middle_top_menu .list_menu .search_form');
			let cateId = searchBox.find('input[name=cateId]').val();
			let tagId = searchBox.find('input[name=tagId]').val();
			let moreId = searchBox.find('input[name=moreId]').val();
			// 着色
			let html = '';
			if (cateId > 0 && filterMenuAry.cateid[cateId]) {
				html += '<span class="btn_item_choice current" data-name="cateId"><b>' + lang_obj.manage.sales.coupon.products_category + ': ' + filterMenuAry.cateid[cateId] + '</b><i></i></span>';
			}
			
			tagId = tagId.split(',').filter(item => item != '');
			if (tagId.length) {
				html += '<span class="btn_item_choice current" data-name="curtagId"><b>' + lang_obj.manage.sales.coupon.tags + ': ';
				for (let key in tagId) {
					if (key > 0) html += ', ';
					html += filterMenuAry.tagid[tagId[key]];
				}
				html += '</b><i></i></span>';
			}
			moreId = moreId.split(',').filter(item => item != '');
			if (moreId.length) {
				html += '<span class="btn_item_choice current" data-name="curmoreId"><b>' + lang_obj.manage.global.more + ': ';
				for (let key in moreId) {
					if (key > 0) html += ', ';
					html += filterMenuAry.more[moreId[key]];
				}
				html += '</b><i></i></span>';
			}
			$Obj.find('.middle_top_menu .search_box_selected').html(html);
		}
		// 响应式
		let  resize = function(){
			let boxHeight = $(window).height() - 420;
			let typeBox = $Obj.find('.middle_container .type_box').parents('.middle_container:visible');
			if (typeBox.length) boxHeight -= typeBox.outerHeight();
			$Obj.find('.middle_container .products_list').css({'height': boxHeight, 'max-height': boxHeight});
			$Obj.find('.middle_container .products_select').css({'height': boxHeight, 'max-height': boxHeight});
			$Obj.find('.middle_container.product_number_container').css({'height': boxHeight, 'max-height': boxHeight});
		}
		//检查产品是否全选
		let checkProductsChecked = function(){
			let _thisPageCount = $Obj.find('.middle_content .products_list_box').find('.item').length;
			let _curProCount = $Obj.find('.middle_content .products_list_box').find('.item.cur').length;
			if (_thisPageCount == _curProCount) {
				$Obj.find('.middle_top_menu .list_menu .input_checkbox_box').addClass('checked')
			} else {
				$Obj.find('.middle_top_menu .list_menu .input_checkbox_box').removeClass('checked')
			}
			let _selectProLength = $Obj.find('.product_list_container .products_select .pro_item_list').length
			if (_selectProLength == 0) {
				let _notDataHtml = no_data_html()
				$Obj.find('.product_list_container .products_select').html(_notDataHtml)
			} else {
				$Obj.find('.product_list_container .products_select').find('.bg_no_table_data').remove()
			}
			return false;
		}
		// 回调
		object =  $.extend({
			onSubmit: function() {},
			onSelect: function() {}
		}, object);
		// 初始化变量
		let params = object.params;
		let ptype = params.type == 'auto' ? 'auto' : 'manual';
		let plimit = params.limit > 0 ? parseInt(params.limit) : 0;
		let pnumber = params.number > 0 ? parseInt(params.number) : 10;
		let pcateid = params.cateid > 0 ? parseInt(params.cateid) : 0;
		let pmoreid = params.moreid;
		let ptagid = params.tagid;
		let pvalue = params.value;
		let pvalueOrder = params.valueOrder;
		let paddMethod = params.addMethod;
		let excludeValue = params.excludeValue; //排除
		let IsNeedSelectCateId = params.IsNeedSelectCateId ? parseInt(params.IsNeedSelectCateId) : 0;
		let is_animate = 0;
		let number = 0;
		let filterMenuAry = {};
		let isOrder = params.isOrder;
		let hiddenRightBox = params.hiddenRightBox
		for (let key in pvalueOrder) {
			if ($.inArray(pvalueOrder[key], excludeValue) != -1) continue;
			number++;
		}
		let iframeTitle = params.iframeTitle ? params.iframeTitle : lang_obj.global.add + lang_obj.manage.sales.coupon.products;
		// 构建html
		let $html = pop_html();
		$('#righter').append($html);
		// 弹窗
		let $Obj = $('.products_choice_v2');
		frame_obj.pop_form($Obj, 0, 1);
		resize();
		$(window).on('resize', function(){resize();});
		frame_obj.check_amount($("#products_choice_edit_form"))
		// 加载产品搜索框
		$.post('/api/OrderList/products-choice-search-v2', '', function (data) {
			if (data.ret == 1) {
				$Obj.find('.middle_top_menu .list_menu').html(data.msg.html);
				let _limitHtml = '';
				if (plimit == 0) {
					_limitHtml += lang_obj.manage.products.added_count.replace('{{count}}', '<span class="cur">0</span>')
				} else {
					_limitHtml += '<span class="cur">0</span>'
				}
				_limitHtml += '<span class="gap">/</span>';
				_limitHtml += '<span class="total">0</span>';
				$Obj.find('.middle_top_menu .list_menu .products_limit').html(_limitHtml);
				products_data_init();
				filterMenuAry = data.msg.filter;
				
				// 智能添加初始化
				if (paddMethod) {
					$Obj.find('.middle_top_menu .list_menu .box_drop_down_menu').removeClass('hide');
				} else {
					$Obj.find('.middle_top_menu .list_menu .box_drop_down_menu').addClass('hide');	
				}
				if (ptype == 'auto') {
					if (pmoreid) {
						let _curMoreObj = $Obj.find(`.auto_type_box .input_radio_box input[name=select_type][value=${pmoreid}]`)
						_curMoreObj.parents('.input_radio_box').click()
					}
				}
				if (pcateid && IsNeedSelectCateId) {
					$Obj.find('.middle_top_menu .list_menu .search_form input[name=cateId]').val(pcateid);
				}
				$Obj.find('.middle_top_menu .list_menu .search_btn').on('click', function(){
					is_animate = 1
					search_form_search('html');
					products_filter_current_init();
					$Obj.find('.list_menu .search_form .ext').hide();
				});
				$Obj.find('.middle_top_menu .list_menu .search_btn').trigger('click');
				// 筛选产品
				$Obj.find('.middle_top_menu .list_menu .search_form .more').on('click', function(){
					products_filter_init();
				});
				$Obj.on('click',function(e){
					if (!$(e.target).hasClass('more') && !$(e.target).hasClass('ext') && !$(e.target).parent().hasClass('ext') && !$(e.target).parents('.ext').length && !$(e.target).hasClass('search_btn') && $Obj.find('.middle_top_menu .list_menu .search_form .ext').css('display') == 'block') {
						$Obj.find('.middle_top_menu .list_menu .search_form .more').click()
					}
				})
				// 切换产品添加方式
				$Obj.find('.middle_top_menu .type_item').on('click', function() {
					$(this).addClass('checked').find('input').prop('checked',true);
					$(this).siblings().removeClass('checked').find('input').prop('checked',false);
					ptype = $(this).data('type')
					$Obj.find('.product_list_container').attr('data-type', ptype)
					$Obj.find('.middle_content').attr('data-addtype', ptype)
					if (ptype == 'manual') {
						$Obj.find('.middle_content .products_number').parents('.middle_container').hide()
						$Obj.find('.middle_content .products_list').show()
						$Obj.find('.middle_top_menu .search_form .k_input .form_input').removeAttr('readonly');
						$Obj.find('.products_choice_button_list').removeClass('auto')
						$Obj.find('.menu_all_checkbox').show()
						$Obj.find('.turn_page_box').show()
						$Obj.find('.middle_top_menu .search_form').show()
						$Obj.find('.middle_top_menu .products_limit').show()
					} else if (ptype == 'auto') {
						$Obj.find('.middle_content .products_number').parents('.middle_container').show()
						$Obj.find('.middle_content .products_list').hide()
						$Obj.find('.middle_top_menu .search_form .k_input .form_input').attr('readonly','readonly');
						$Obj.find('.products_choice_button_list').addClass('auto')
						$Obj.find('.menu_all_checkbox').hide()
						$Obj.find('.turn_page_box').hide()
						$Obj.find('.middle_top_menu .search_form').hide()
						$Obj.find('.middle_top_menu .products_limit').hide()
						if ($('.auto_category_select_box').length && $('.auto_tags_box').length) {
							$.post('/manage/action/get-products-auto-info', {type:ptype, 'CateId':pcateid, 'TagId':ptagid}, function(result){
								if (result.ret == 1) {
									if ($('.auto_category_select_box').html() == ''){
										$('.auto_category_select_box').html(result.msg.category);
									}
									if ($('.auto_tags_box').html() == '') {
										$('.auto_tags_box').html(result.msg.tags);
									}
									if (pcateid) {
										$Obj.find('.product_number_container .auto_scope_item[data-type=category]').click()
									}
									$Obj.find('.auto_tags_box .select_list').bind('DOMNodeRemoved',function(){
										setTimeout(()=>{
											search_form_search('html')
										},100)
									})
								}
							},'json')
						}
					}
					$Obj.find('.middle_top_menu .search_form .k_input input[name=Keyword]').val('')
					is_animate = 1
					search_form_search('html')
				})
				$Obj.find('.middle_top_menu .type_item[data-type="' + ptype + '"]').trigger('click')
				//翻页
				$Obj.on('click','.middle_content #turn_page li a', function(){
					let _thisPage = parseInt($(this).attr('data-page')) + 1;
					$Obj.find('.middle_content .products_list_box').attr('page',_thisPage)
					search_form_search('turn');
				})
				//全选
				$Obj.on('click', '.middle_top_menu .list_menu .menu_all_checkbox', function() {
					// $Obj.find('.product_list_container .products_select').html('');
					if ($(this).hasClass('is_all')) {
						$(this).removeClass('is_all')
						$Obj.find('.middle_content .products_list_box .item.cur').click();
					} else {
						$(this).addClass('is_all')
						$Obj.find('.middle_content .products_list_box .item').not('.cur').click();
					}
					$Obj.find('.middle_top_menu .products_limit span.cur').text(number);
					$Obj.find('.product_list_container .products_select').dragsort('destroy');
				})
			}
		}, 'json');
		// 筛选关闭
		$Obj.find('.middle_top_menu .search_box_selected').off('click').on('click', '.btn_item_choice', function(){
			let name = $(this).attr('data-name');
			if ($Obj.find(`.middle_top_menu .search_form .ext input[name='${name}']`).parents('.box_checkbox_list').length){
				$Obj.find(`.middle_top_menu .search_form .ext input[name='${name}']`).parents('.box_checkbox_list').find('.select_list').html('');
			} else {
				$Obj.find(`.middle_top_menu .search_form .ext input[name='${name}']`).val('');
			}
			$Obj.find('.middle_top_menu .list_menu .search_btn').trigger('click');
		});

		// 产品显示数量
		let productsNumberTimer = null;
		$Obj.find('.products_number .box_input').on('keyup', function(){
			if (productsNumberTimer) clearTimeout(productsNumberTimer);
			productsNumberTimer = setTimeout(() => {
				let _thisValue = $(this).val();
				if (_thisValue == 0) { $(this).val(1) }
				search_form_search('html');
			}, 1000);
		});
		/*****智能选择****/
		//适用范围
		$Obj.on('click', '.product_number_container  .auto_scope_item', function() {
			$(this).addClass('checked').find('input').attr('checked','checked');
			$(this).siblings().removeClass('checked').find('input').prop('checked',false);
			let _thisValue = $(this).attr('data-type')
			if (_thisValue == 'category') {
				$Obj.find('.c_s_box').show()
			} else{
				$Obj.find('.c_s_box').hide()
			}
			search_form_search('html');
		})
		//指定分类
		$Obj.on('change','.c_s_box .box_drop_double input[name=cateId]', function(){
			search_form_search('html');
		})
		//类型
		$Obj.on('click', '.auto_type_box .input_radio_box', function(){
			$(this).addClass('checked').siblings().removeClass('checked')
			search_form_search('html');
		})
		/*****智能选择****/

		// 产品勾选
		$Obj.find('.products_list_box').on('click', '.item', function(){
			let proid = $(this).data('proid');
			let proimg = $(this).find('.img img').attr('src');
			let proname = $(this).find('.name').text();
			if (!$(this).hasClass('cur')) {  // 选中
				if (plimit > 0 && number >= plimit) {  // 可选大于最大产品
					global_obj.win_alert_auto_close(lang_obj.manage.sales.beyondNumber + plimit, 'await', 2000, '8%', 0);
					return;
				}
				products_add_html(proid, proimg, proname);
				products_dragsort_init();
				number++;
				let data = get_data();
				object.onSelect.call(this, data);
			} else {  // 取消勾选
				$Obj.find(`.product_list_container .products_select .pro_item_list[data-proid=${proid}]`).remove();
				number--;
			}
			$Obj.find('.middle_top_menu .products_limit span.cur').text(number);
			$(this).toggleClass('cur');
			checkProductsChecked();
		});
		// 快捷栏产品删除
		$Obj.find('.product_list_container .products_select').on('click', '.pro_item_list i', function(){
			let proid = $(this).parents('.pro_item_list').data('proid');
			$(this).parents('.pro_item_list').remove();
			$Obj.find(`.middle_content .products_list_box .item[data-proid=${proid}]`).removeClass('cur');
			number--;
			$Obj.find('.middle_top_menu .products_limit span.cur').text(number);
		});
		// 保存产品
		$Obj.find('.products_button .btn_submit').on('click', function(){
			let data = get_data();
			object.onSubmit.call(this, data);
			$Obj.find('.t h2').trigger('click');
		});
	},

	/*
	 *	@param:	id			保存图片隐藏域(单图：id值；多图：name值)
	 *	@param:	type		类型，例如：editor即添加到编辑器
	 *	@param:	maxpic		最大允许图片数
	 *	@param:	del_url		删除图片地址('./?'+del_url+'&...')
	 *	@param:	no_mask		执行完过后，是否需要关闭遮罩层
	 *	@param:	callback	回调函数
	 */
	photo_choice_init:function(id, type, maxpic, del_url, no_mask, callback){
		var del_url=(typeof(arguments[3])=='undefined')?'':arguments[3],
			no_mask=(typeof(arguments[4])=='undefined')?1:arguments[4],	//默认关闭遮罩层
			callback=(typeof(arguments[5])=='undefined')?'':arguments[5],
			html='';
		maxpic==null && (maxpic=-1);
		type=='editor' && (maxpic=9999); //编辑器上传没上限
		frame_obj.photo_choice_iframe_init('/manage/set/photo/choice?id='+id+'&type='+type+'&maxpic='+maxpic, 'photo_choice', no_mask, callback);
		html='<input type="hidden" class="del_url" value="'+del_url+'" /><input type="hidden" class="callback" value="'+callback+'" /><input type="hidden" class="no_mask" value="'+no_mask+'" />';
		$('.photo_choice').append(html);
	},

	photo_choice_return:function(id, type){
		var maxpic=(typeof(arguments[2])=='undefined')?'':arguments[2],		//最大允许图片数
			num=(typeof(arguments[3])=='undefined')?'':arguments[3],		//类型(本地上传/图片银行)
			imgpath=(typeof(arguments[4])=='undefined')?'':arguments[4],	//已上传图片地址
			surplus=(typeof(arguments[5])=='undefined')?0:arguments[5],		//剩余图片数
			number=(typeof(arguments[6])=='undefined')?1:arguments[6],		//当前图片序号
			img_name=(typeof(arguments[7])=='undefined')?1:arguments[7],	//当前图片名称
			del_url=parent.$('.photo_choice .del_url').val(),
			callback=parent.$('.photo_choice .callback').val(),
			no_mask = parent.$('.photo_choice .no_mask').val();

		id=id.replace(';', '=');
		if(type=='editor' || type=='isspecial'){ // isspecial 需要特殊处理 :
			id=id.replace(':', '\:');
		}else{
			id=id.replace(':', '\\:');
		}
		var error_img = $("input[name='file2BigName_hidden_text']").val();
		var big_pic_size;
		if(!surplus){
			big_pic_size = $('.fileupload-buttonbar .classIt').length;
		}
		if(num){
			function isHasImg(pathImg){
				if(pathImg.length==0){
					return false;
				}
				var isExist=true;
				$.ajax('/api/SettingPhotos/HasImg', {
					type: 'get',
					async:false,//取消ajax的异步实现
					timeout: 1000,
					dataType: "json",
					data: {PicPath:pathImg},
					success: function(data) {
						if(data.ret==0){
							isExist=false;
						}
					},
				});
				return isExist;
			}
			/* 本地上传 */
			if((isHasImg(imgpath) || (type=='editor' && big_pic_size)) && id){//防止代码自动执行
				if(type!='editor' && parent.$('#'+id+' div').length>=parseInt(maxpic)){
					global_obj.win_alert(lang_obj.manage.account.picture_tips.replace('xxx', maxpic), function(){
						parent.frame_obj.pop_contents_close_init(parent.$('.photo_choice'), 1, no_mask);
					}, '', no_mask);
					return;
				}
				if(type=='editor'){//编辑框
					// 直接处理 TinyMCE 图片插入，不依赖 fileupload-buttonbar 元素
					if (parent.tinymce) {
						// 尝试多种方式找到正确的编辑器实例
						var editor = null;

						// 方法1: 直接通过ID查找
						if (parent.tinymce.editors[id]) {
							editor = parent.tinymce.editors[id];
						}
						// 方法2: 遍历所有编辑器实例查找匹配的ID
						else {
							for (var editorId in parent.tinymce.editors) {
								if (editorId === id || parent.tinymce.editors[editorId].id === id) {
									editor = parent.tinymce.editors[editorId];
									break;
								}
							}
						}
						// 方法3: 如果还是找不到，尝试通过DOM元素查找
						if (!editor && parent.$('#' + id).length > 0) {
							var targetElement = parent.$('#' + id)[0];
							for (var editorId in parent.tinymce.editors) {
								if (parent.tinymce.editors[editorId].getElement() === targetElement) {
									editor = parent.tinymce.editors[editorId];
									break;
								}
							}
						}

						if (editor) {
							editor.insertContent('<img src="' + imgpath + '" />'); // 向编辑器增加内容
						}
					}

					// 保留原有的逻辑作为备用
					$('.fileupload-buttonbar').find('[name=Name\\[\\]][value="'+img_name+'"]').attr('picpath',imgpath);
					if(!surplus){
						$('.fileupload-buttonbar').find('[name=Name\\[\\]]').each(function(){
							var imgpath_list = $(this).attr('picpath');
							if (parent.CKEDITOR) {
								if (parent.CKEDITOR_VERSION) {
									let ckeditor = Object.values(parent.CKEDITOR.instances).find(_ => _.id==id)
									if (!ckeditor) ckeditor = Object.values(parent.CKEDITOR.instances)[0]
									if (ckeditor) {
										ckeditor.model.insertContent(ckeditor.model.change(writer => {
											const docFrag = writer.createDocumentFragment()
											writer.append(writer.createElement('image', {src: imgpath_list}), docFrag)
											return docFrag
										}))
									} else {
										console.error('--------- not found ckeditor instance.')
									}
								} else {
									parent.CKEDITOR.instances[id].insertHtml('<img src="'+imgpath_list+'" />');//向编辑器增加内容
								}
							}
							if (parent.tinymce) {
								// 尝试多种方式找到正确的编辑器实例
								var editor = null;

								// 方法1: 直接通过ID查找
								if (parent.tinymce.editors[id]) {
									editor = parent.tinymce.editors[id];
								}
								// 方法2: 遍历所有编辑器实例查找匹配的ID
								else {
									for (var editorId in parent.tinymce.editors) {
										if (editorId === id || parent.tinymce.editors[editorId].id === id) {
											editor = parent.tinymce.editors[editorId];
											break;
										}
									}
								}
								// 方法3: 如果还是找不到，尝试通过DOM元素查找
								if (!editor && parent.$('#' + id).length > 0) {
									var targetElement = parent.$('#' + id)[0];
									for (var editorId in parent.tinymce.editors) {
										if (parent.tinymce.editors[editorId].getElement() === targetElement) {
											editor = parent.tinymce.editors[editorId];
											break;
										}
									}
								}

								if (editor) {
									editor.insertContent('<img src="' + imgpath_list + '" />'); // 向编辑器增加内容
								}
							}
						});
					}
				}else{
					let obj = parent.$('#' + id),
						j = 0,
						idAry = id.split(" "),
						detailObj = idAry[0];
					if (number == 1) {
						// 优先上传第一张图片
						obj.find('.pic_btn .zoom').attr('href',imgpath);
						obj.find('.preview_pic a').remove();
						obj.find('.preview_pic').append(frame_obj.upload_img_detail(imgpath)).children('.upload_btn').hide().parent().parent().addClass('isfile').removeClass('show_btn');
						obj.find('.preview_pic').children('input[type=hidden]').val(imgpath).attr('save', 1).trigger('change');
						j += 1;
					} else {
						var oth_obj = '', i, $IsCreated = 0, $Html = "";
						for (i = 0; i < maxpic; ++i) {
							oth_obj=obj.parents('.multi_img').find('.img[num='+i+']');
							if (imgpath) {
								if (!oth_obj.length) {
									$Html = frame_obj.multi_img_item("PicPath[]", i, 1);
									if(obj.parent().hasClass('pro_multi_img') && (type == "products" || type == "nav")) obj.parent().append($Html);
									oth_obj = obj.parents('.multi_img').find('.img[num='+i+']');
								}
								if (oth_obj.find('input[type=hidden]').attr('save')==0 && !oth_obj.hasClass('video')) {
									oth_obj.find('.pic_btn .zoom').attr('href',imgpath);
									oth_obj.find('.preview_pic').append(frame_obj.upload_img_detail(imgpath)).children('.upload_btn').hide().parent().parent().addClass('isfile').removeClass('show_btn');
									oth_obj.find('.preview_pic').children('input[type=hidden]').val(imgpath).attr('save', 1);
									break;
								}
								$IsCreated += 1;
							}
						}
						let opt_img = obj.parents('.multi_img').find('.img').length;
						if ($IsCreated > 0 && opt_img<maxpic) {
							$Html = frame_obj.multi_img_item("PicPath[]", (i + 1), 1);
							if (obj.parent().hasClass('pro_multi_img') && (type == "products" || type == "nav")) {
								obj.parent().append($Html);
							}
						}
					}
					if ((type == "products" || type == "nav") && j > 0) {
						var $num = parent.$("#" + detailObj + " .img").length;
						$Html = frame_obj.multi_img_item("PicPath[]", $num, 1);
						if (obj.parent().hasClass('pro_multi_img')) {
							obj.parent().append($Html);
						}
					}
				}
				if (type == "products") {
					callback += "parent.products_obj.function_init.main_picture_upload();";
					callback += "parent.products_obj.function_init.read_all_image_data();";
				}
				if (callback.indexOf('view_obj') == 0) {
					callback = 'parent.' + callback;
				}

				if(type == 'allFile') {
					var	row = imgpath,
						ary = row.split('.'),
						length = ary.length - 1,
						Ext = ary[length],
						NameAry = ary[0].split('/'),
						NameLength = NameAry.length - 1,
						Name = NameAry[NameLength],
						fileObj = parent.$('#'+id);

					$fileHtml = frame_obj.file_data_init(row, Name, Ext);
					$fileHtml && fileObj.append($fileHtml);
				}

				callback && eval(callback);
				imgpath && parent.global_obj.win_alert_auto_close(lang_obj.manage.photo.upload_success, '', 1000, '8%',0);
			}
			if (!surplus) {

				//$(".photo_multi_img .classIt").remove();
				//$("input[name='file2BigName_hidden_text']").val(" ");
				//error_img && window.parent.global_obj.win_alert(lang_obj.manage.global.photo_gt_2m+error_img,'',"alert");

				// 本地上传完成后，自动关闭对话框
				// 支持编辑器模式和普通图片上传模式
				if (type === 'editor' || type === '' || type === 'img' || type === 'pro' || type === 'products' || type === 'ad') {
					parent.frame_obj.pop_contents_close_init(parent.$('.photo_choice'), 1, no_mask);//最后一张，自动关闭
				}
			}
		}else{
			/* 从图片银行复制 */
			$.post('/api/Setting/PhotoChoiceSave', $('.photo_choice').find('iframe').contents().find('#photo_list_form').serialize()+'&type='+type, function(data){
				$('#button_add').attr('disabled', 'disabled');
				if(data.ret!=1){
					global_obj.win_alert(data.msg, function(){
						frame_obj.pop_contents_close_init($('.photo_choice'), 1, no_mask);
					}, '', callback);
					callback && eval(callback);
					return false;
				}else{
					if (data.type == 'editor') {
						var html='';
						if (parent.CKEDITOR_VERSION && parent.CKEDITOR) {
							let ckModel = Object.values(parent.CKEDITOR.instances).find(_ => _.id == id)
							if (!ckModel) ckModel = Object.values(parent.CKEDITOR.instances)[0]
							ckModel = ckModel.model
							ckModel.insertContent(ckModel.change(writer => {
								const docFrag = writer.createDocumentFragment()
								data.Pic.forEach(src => {
									writer.append(writer.createElement('image', {src}), docFrag)
								})
								return docFrag
							}))
							frame_obj.pop_contents_close_init($('.photo_choice'), 1, no_mask);
							parent.document.querySelector('#div_mask').remove()
							return
						}
						/* 编辑框 */
						if (parent.CKEDITOR) {
							var obj = parent.CKEDITOR.instances[id];
							for (var i in data.Pic){
								html += '<img src="'+data.Pic[i]+'" />';
							}
							obj.insertHtml(html);//向编辑器增加内容
						}
						if (parent.tinymce) {
							var obj = tinymce.editors[id];
							for (var i in data.Pic){
								html += '<img src="'+data.Pic[i]+'" />';
							}
							obj.insertContent(html);
						}
					} else if(data.type == 'allFile') {
						
						var $picHtml = '',
							row = data.Ext,
							fileObj = parent.$('#'+id);

						$.each(row, function(k, v){
							$picHtml = frame_obj.file_data_init(v.Path, v.Name, v.Ext);
							$picHtml && fileObj.append($picHtml);
						})

						$picHtml && parent.global_obj.win_alert_auto_close(lang_obj.manage.photo.upload_success, '', 1000, '8%',0);
						callback && eval(callback);

					} else {
						 
						var html = '';
						var obj = parent.$('#' + id);

						//优先上传第一张图片
						 
					 
						obj.find('.pic_btn .zoom').attr('href', data.Pic[0]);
					 
						obj.find('.preview_pic a').remove();
						obj.find('.preview_pic').append(frame_obj.upload_img_detail(data.Pic[0])).children('.upload_btn').hide().parent().parent().addClass('isfile').removeClass('show_btn');
						obj.find('.preview_pic').children('input[type=hidden]').val(data.Pic[0]).attr('save', 1).trigger('change');
						
						if (data.Pic.length > 1) {
						 
							//选择多张执行下面代码
							var oth_obj = '', i, j = 1, $IsCreated = 0, $Html = "";
							for (i = 0; i < maxpic; ++i) {
								oth_obj = obj.parents('.multi_img').find(".img[num="+i+"]");
								if (data.Pic[j] && !oth_obj.length) {
									$Html = frame_obj.multi_img_item("PicPath[]", i, 1, type);
									if(obj.parent().hasClass('pro_multi_img') && (type == "products" || type == "nav" || type == "user")) obj.parent().append($Html);
									oth_obj = obj.parents('.multi_img').find(".img[num="+i+"]");
									$IsCreated += 1;
								}
								if (data.Pic[j] && oth_obj.find('input[type=hidden]').attr('save') == 0 && !oth_obj.hasClass("video")) {
									oth_obj.find('.pic_btn .zoom').attr('href', data.Pic[j]);
									oth_obj.find('.preview_pic').append(frame_obj.upload_img_detail(data.Pic[j])).children('.upload_btn').hide().parent().parent().addClass('isfile').removeClass('show_btn');
									oth_obj.find('.preview_pic').children('input[type=hidden]').val(data.Pic[j]).attr('save', 1);
									data.Pic[j] = "";
									j++;
								}
								if (i && data.Pic[j] == undefined) {
									break;
								}
							}
							 
							let opt_img = obj.parents('.multi_img').find('.img').length;
							if ($IsCreated > 0 && opt_img<maxpic) {
								$Html = frame_obj.multi_img_item("PicPath[]", (i + 1), 1, type);
								if(obj.parent().hasClass('pro_multi_img') && (type == "products" || type == "nav" || type == "user")) obj.parent().append($Html);
							}
						} else {
							var $Count = obj.parents('.multi_img').find(".img").length,
								oth_obj = obj.parents('.multi_img').find('.img[num='+i+']');
							if (!obj.next(".img:not('.isfile, .video')").length) {
								//如果没有下一个
								$Html = frame_obj.multi_img_item("PicPath[]", $Count, 1, type);
								if(obj.parent().hasClass('pro_multi_img') && (type == "products" || type == "nav" || type == "user") && $Count<maxpic) obj.parent().append($Html);
							}
						}
						if (type == "products") {
							callback += "parent.products_obj.function_init.main_picture_upload();";
							callback += "parent.products_obj.function_init.read_all_image_data();";
						}
						callback && eval(callback);
						parent.global_obj.win_alert_auto_close(lang_obj.manage.photo.upload_success, '', 1000, '8%',0);
					}
				}
				frame_obj.pop_contents_close_init($('.photo_choice'), 1, no_mask);
			}, 'json');
		}

	},

	ajax_photo_del:function(obj, id, Url){
		if(Url==''){return;}
		var $this=$(this);
		global_obj.win_alert(lang_obj.global.del_confirm, function(){
			$.ajax({
				url:'./?'+Url+'&Path='+$(obj).prev().attr('href')+'&Index='+$(obj).parent().index(),
				success:function(data){
					json=eval('('+data+')');
					$('#'+id+' div:eq('+json.msg[0]+')').remove();
				}
			});
		}, 'confirm');
		return false;
	},

	/**
	 * 文件库 - 单个文件上传
	 */
	main_file_upload: function(callback = '') {
		frame_obj.mouse_click($('.upload_menu li[data-type=file]'), 'file', function() {
			frame_obj.file_choice_init('FileDetail', 'allFile', 1,'', '',1 , callback);
		})
	},

	/*
	 *	@param:	id			保存文件隐藏域(单图：id值；多图：name值)
	 *	@param:	type		类型，例如：editor即添加到编辑器
	 *	@param:	maxpic		最大允许图片数
	 *	@param:	del_url		删除图片地址('./?'+del_url+'&...')
	 *	@param:	no_mask		执行完过后，是否需要关闭遮罩层
	 *	@param:	callback	回调函数
	 */
	 file_choice_init:function(id, type, maxpic,filetype, del_url, no_mask, callback){
		var del_url=(typeof(arguments[4])=='undefined')?'':arguments[4],
			no_mask=(typeof(arguments[5])=='undefined')?1:arguments[5],	//默认关闭遮罩层
			callback=(typeof(arguments[6])=='undefined')?'':arguments[6],
			html='',
			title = lang_obj.manage.file.video_upload;
		maxpic==null && (maxpic=-1);
		if(type == 'allFile') title = lang_obj.manage.file.file_upload;
		
		frame_obj.file_choice_iframe_init('/manage/set/photo/file-choice?id='+id+'&type='+type+'&maxfile='+maxpic+'&filetype='+filetype, 'file_choice', no_mask, callback, title);
		html='<input type="hidden" class="del_url" value="'+del_url+'" /><input type="hidden" class="callback" value="'+callback+'" /><input type="hidden" class="no_mask" value="'+no_mask+'" />';
		$('.file_choice').append(html);
	},

	file_upload:function(file_input_obj, filepath_input_obj, img_detail_obj, size){
		var size=(typeof(arguments[3])=='undefined')?'':arguments[3];
		var multi=(typeof(arguments[4])=='undefined')?false:arguments[4];
		var queueSizeLimit=(typeof(arguments[5])=='undefined')?5:arguments[5];
		var callback=arguments[6];
		var fileExt=(typeof(arguments[7])=='undefined' || arguments[7]=='')?'*.jpg;*.png;*.gif;*.jpeg;*.webp;*.ico;*.jp2':arguments[7];//;*.bmp
		var do_action=(typeof(arguments[8])=='undefined')?'/manage/action/file-upload':arguments[8];
		file_input_obj.omFileUpload({
			action:'./?session_id='+session_id,
			actionData:{
				do_action:do_action,
				size:size
			},
			fileExt:fileExt,
			fileDesc:'Files',
			autoUpload:true,
			multi:multi,
			queueSizeLimit:queueSizeLimit,
			swf:'/inc/file/fileupload.swf?r='+Math.random(),
			method:'post',
			buttonText:lang_obj.manage.frame.file_upload,
			onComplete:function(ID, fileObj, response, data, event){
				var jsonData=eval('('+response+')');
				if(jsonData.status==1){ 
					if(typeof callback === "function"){
						callback(jsonData.filepath, data.fileCount, jsonData.name);
					}else{
						filepath_input_obj.val(jsonData.filepath);
						img_detail_obj.html(frame_obj.upload_img_detail(jsonData.filepath));
					}
				}
			}
		});
	},

	file_choice_iframe_init:function(url, id, no_mask, callback,title){
		var $Obj, $html='';
		$html+='<div class="pop_form file_choice">';
			$html+='<form id="file_choice_edit_form">';
				$html+='<div class="t"><h1>'+title+'</h1><h2>×</h2></div>';
				$html+='<div class="r_con_form"><iframe src="" frameborder="0"></iframe></div>';
				$html+='<div class="button"><input type="submit" class="btn_global btn_submit" id="file_button_add" name="submit_button" value="'+lang_obj.global.submit+'" /><input type="button" class="btn_global btn_cancel" value="'+lang_obj.global.cancel+'" /></div>';
			$html+='</form>';
		$html+='</div>';
		$('#righter').append($html);
		$Obj=$('.file_choice');
		frame_obj.pop_form($Obj, 0, no_mask, callback);
		setTimeout(function(){
			$Obj.find('iframe').attr('src', url+'&iframe=1&r='+Math.random()); //效果执行完，才加载内容显示
		}, 200);

		var resize=function(){
			var $h = $(window).height()-$Obj.find('form>.t').outerHeight(true)-$Obj.find('form>.button').outerHeight(true)-70;
			$Obj.find('.r_con_form').css({'height':$h, 'max-height':$h});
		}
		resize();
		$(window).resize(function(){resize();});

		$Obj.find('#file_button_add').click(function(){ //提交
			var obj=$Obj.find('iframe').contents(),
				id=obj.find('input[name=id]').val(),//显示元素的ID
				type=obj.find('input[name=type]').val(),//类型
				maxfile=obj.find('input[name=maxfile]').val();//最大允许图片数
			frame_obj.file_choice_return(id, type, maxfile);
			$(this).attr('disabled', 'disabled');
			return false;
		});
		$Obj.find('.t h2').add($Obj.find('.btn_cancel')).click(function(){ //取消
			frame_obj.pop_contents_close_init($Obj, 1, no_mask, callback);
		});
	},

	file_choice_return:function(id, type){
		var maxfile=(typeof(arguments[2])=='undefined')?'':arguments[2],		//最大允许上传数
			num=(typeof(arguments[3])=='undefined')?'':arguments[3],		//类型(本地上传/图片银行)
			filepath=(typeof(arguments[4])=='undefined')?'':arguments[4],	//已上传文件地址
			surplus=(typeof(arguments[5])=='undefined')?0:arguments[5],		//剩余图片数
			number=(typeof(arguments[6])=='undefined')?1:arguments[6],		//当前图片序号
			del_url=parent.$('.file_choice .del_url').val(),
			callback=parent.$('.file_choice .callback').val(),
			no_mask=parent.$('.file_choice .no_mask').val();
		id=id.replace(';', '=');
		if(type=='editor' || type=='isspecial'){ // isspecial 需要特殊处理 :
			id=id.replace(':', '\:');
		}else{
			id=id.replace(':', '\\:');
		}
		var error_file = $("input[name='file2BigName_hidden_text']").val();
		var big_file_size;
		if(!surplus){
			big_file_size = $('.fileupload-buttonbar .classIt').length;
		}
		if(num){
			function isHasFile(pathFile){
				if(pathFile.length==0){
					return false;
				}
				var isExist=true;

				$.ajax('/api/ProductFileUpload/has-file', {
					type: 'get',
					async:false,//取消ajax的异步实现
					timeout: 1000,
					dataType: "json",
					data: {File:pathFile},
					success: function(data) {
						if(data.ret==0){
							isExist=false;
						}
					},
				});
				return isExist;
			}
			/* 本地上传 */
			if(isHasFile(filepath) || (type=='editor' && big_file_size)){//防止代码自动执行
				if(type!='editor' && parent.$('#'+id+' div').length>=parseInt(maxfile)){
					global_obj.win_alert(lang_obj.manage.account.picture_tips.replace('xxx', maxfile), function(){
						parent.frame_obj.pop_contents_close_init(parent.$('.file_choice'), 1, no_mask);
					}, '', no_mask);
					return;
				}
				let obj = parent.$('#' + id),
					j = 0,
					idAry = id.split(" "),
					detailObj = idAry[0];
					// 优先上传第一个
				obj.find('.file_btn .zoom').attr('href',filepath);
				obj.find('.preview_file a').remove();
				if(type != 'allFile') { parent.frame_obj.generate_file_cover(obj,filepath); }//生成封面图
				obj.find('.preview_file').children('input[type=hidden]').val(filepath).attr('save', 1).trigger('change');
				j += 1;
				
				if (callback.indexOf('view_obj') == 0) {
					callback = 'parent.' + callback;
				}
				
				if(type == 'allFile') {
					var	row = filepath,
						ary = row.split('.'),
						length = ary.length - 1,
						Ext = ary[length],
						NameAry = ary[0].split('/'),
						NameLength = NameAry.length - 1,
						Name = NameAry[NameLength],
						fileObj = parent.$('#'+id);

					$fileHtml = frame_obj.file_data_init(row, Name, Ext);
					$fileHtml && fileObj.append($fileHtml);
				} else if (type == 'reviewVideo') {
					obj.find('.preview_file').children('.upload_btn').hide().parent().parent().addClass('isfile').removeClass('show_btn');
				}

				callback && eval(callback);
				filepath && parent.global_obj.win_alert_auto_close(lang_obj.manage.photo.upload_success, '', 1000, '8%',0);
			}
			if(!surplus){
				$(".file_multi_img .classIt").remove();
				$("input[name='file2BigName_hidden_text']").val(" ");
				error_file && window.parent.global_obj.win_alert(lang_obj.manage.global.photo_gt_8m+error_file,'',"alert");
				parent.frame_obj.pop_contents_close_init(parent.$('.file_choice'), 1, no_mask);//最后一张，自动关闭
			}
		}else{
			/* 从图片银行复制 */
			$.post('/api/Setting/FileChoiceSave', $('.file_choice').find('iframe').contents().find('#file_list_form').serialize()+'&type='+type, function(data){
				$('#button_add').attr('disabled', 'disabled');
				if(data.ret!=1){
					global_obj.win_alert(data.msg, function(){
						frame_obj.pop_contents_close_init($('.file_choice'), 1, no_mask);
					}, '', callback);
					callback && eval(callback);
					return false;
				}else{
					var obj = parent.$('#'+id);
					//优先上传第一张图片
					obj.find('.file_btn .zoom').attr('href', data.File[0]);
					obj.find('.preview_file a').remove();
					obj.find('.preview_file').append(frame_obj.upload_img_detail(data.Cover[0])).children('.upload_btn').hide().parent().parent().addClass('isfile').removeClass('show_btn');
					obj.find('.preview_file').children('input[type=hidden]').val(data.File[0]).attr('save', 1).trigger('change');
					obj.find('.preview_file').children('input[name=fileCover]').val(data.Cover[0]);
					
					var $Count = obj.parents('.multi_file').find(".file").length;
					if (!obj.next(".file:not('.isfile, .video')").length) {
						//如果没有下一个
						$Html = frame_obj.multi_file_item("FilePath[]", $Count, 1, type);
						if(obj.parent().hasClass('pro_multi_file') && (type == "products" || type == "nav") && $Count<maxfile) obj.parent().append($Html);
					}
					
					if(data.type == 'allFile') {
						var row = data.FileAttr,
							obj = parent.$('#'+id);
						
						$.each(row, function(k, v){
							$Html = frame_obj.file_data_init(v.Path, v.Name, v.Ext);
							$Html && obj.append($Html);
						})
					}

					callback && eval(callback);
				}
				frame_obj.pop_contents_close_init($('.file_choice'), 1, no_mask);
				parent.global_obj.win_alert_auto_close(lang_obj.manage.photo.upload_success, '', 1000, '8%',0);
			}, 'json');
		}
	},

	generate_file_cover:function(obj,path){
		$.post('/api/ProductFileUpload/getFileCover',{'path':path},function(data){
			if (data.ret === 1){
				let _coverPath = data.msg;
				let _iframeObj =  `<iframe src="${_coverPath}" style="display:none;"></iframe>`;
				obj.append(_iframeObj);
				setTimeout(()=>{
					if (obj.find('.preview_file').find('a').length === 0) {
						obj.find('.preview_file').append(frame_obj.upload_img_detail(_coverPath)).children('.upload_btn').hide().parent().parent().addClass('isfile').removeClass('show_btn');
						obj.find('.preview_file').children('input[name=fileCover]').val(_coverPath);
						obj.find('iframe').remove();
						$.post('/api/ProductFileUpload/saveFileCover',{'path':path})
					}
				},1000)
			}
		},'json')
	},

	photo_upload_iframe_init: (url, no_mask, callback) => {
		let $html = `
			<div class="pop_form photo_choice">
				<form id="photo_choice_edit_form">
					<div class="t">
						<h1>${lang_obj.manage.photo.picture_upload}</h1>
						<div class="upload_file_tips">${lang_obj.manage.notes.picture_tips}</div>
						<h2>×</h2>
					</div>
					<div class="r_con_form">
						<iframe src="" frameborder="0"></iframe>
					</div>
				</form>
			</div>
		`
		$('#righter').append($html)
		let $Obj = $('.photo_choice')
		frame_obj.pop_form($Obj, 0, no_mask, callback)
		setTimeout(() => {
			$Obj.find('iframe').attr('src', url) // 效果执行完，才加载内容显示
		}, 200)

		let resize = () => {
			let $h = $(window).height() - $Obj.find('form>.t').outerHeight(true) - $Obj.find('form>.button').outerHeight(true) - 70
			$Obj.find('.r_con_form').css({ 'height': $h, 'max-height': $h })
		}
		resize()
		$(window).resize(() => { resize() })

		$Obj.find('.t h2').add($Obj.find('.btn_cancel')).click(() => {
			// 取消
			frame_obj.pop_contents_close_init($Obj, 1, no_mask, callback)
		})
	},

	file_data_init:function(path = '', name = '', ext = ''){
		let $html = '';
		let icon = '';
		let type = '.' + ext;
		let fileType = '';
		switch (type) {
			case ".csv":
				icon = "csv";
				break;
			case ".xls":
			case ".xlsx":
				icon = "excel1";
				break;
			case ".pdf":
				icon = "pdf1";
				break;
			case ".ppt":
			case ".pptx":
				icon = "ppt1";
				break;
			case ".rar":
			case ".zip":
				icon = "a-32yasuowenjian";
				break;
			case ".txt":
				icon = "txt1";
				break;
			case ".doc":
			case ".docx":
				icon = "word1";
				break;
			case ".mp4":
				icon = 'shipinwenjian'; break;
		}

		if($.inArray(ext, ['gif','jpg','webp','jpeg','png']) >= 0) {
			fileType = 'photo';
		} else {
			fileType = 'file';
		}

		$html+='<div class="item" data-type="' + fileType + '">';
			if(fileType == 'photo') {
				$html+='<span class="img_box"><img src="' + path + '" /></span>'
			} else {
				$html+='<svg class="icon" aria-hidden="true"><use xlink:href="#icon-' + icon + '"></use></svg>';
			}
			$html+='<span class="file_del">' + lang_obj.global.del + '</span>';
			$html+='<input type="hidden" name="FilePath" value="' + path + '">';
			$html+='<input type="text" maxlength="100" class="box_input" value="'+ name +'" name="Name" placeholder="" notnull="">';
		$html+='</div>';
		return $html;
	},

	download_file_check:function(){
		var obj=(typeof(arguments[0])=='undefined')?'':arguments[0],
			isParent=(typeof(arguments[1])=='undefined')?0:parseInt(arguments[1]),
			o=new Object;
		if(obj) {
			if(isParent==1){ o=parent.$(obj); }
			else{ o=$(obj); }

			if(o.find('.item').length) {
				o.siblings('.fileinput-button').hide().parent().parent().css('display', 'block').parents('#righter').find('li').hide();
			}
		}
	},

	/******************************旧版数据统计(start)******************************/
	chart:function(){
		$('.chart').height(frame_obj.chart_par.height).highcharts({
			chart:{
				type:frame_obj.chart_par.themes,
				backgroundColor:frame_obj.chart_par.bg
			},
			title:{text:''},
			tooltip: {
				shared: true,
				valueSuffix: frame_obj.chart_par.valueSuffix
			},
			xAxis:{categories:chart_data.date},
			yAxis:[{
				title:{text:''},
				min:0
			}],
			legend:frame_obj.chart_par.legend,
			plotOptions:{
				line:{
					dataLabels:{enabled:true},
					enableMouseTracking:false
				},
				bar:{
					dataLabels:{enabled:true}
				}
			},
			series:chart_data.count,
			exporting:{enabled:false}
		});
	},

	chart_pie:function(){
		$('.chart').height(500).highcharts({
			title:{text:''},
			credits:{enabled:false},
			tooltip:{
				pointFormat:'{series.name}: <b>{point.percentage:.2f}%</b>'
			},
			plotOptions:{
				pie:{
					allowPointSelect:true,
					cursor:'pointer',
					dataLabels:{
						enabled:true,
						color:'#000000',
						connectorColor:'#000000',
						format:'<b>{point.name}</b>: {point.percentage:.2f} %'
					}
				}
			},
			series:[{
				type:'pie',
				name:lang_obj.manage.account.percentage,
				data:user_area_data
			}]
		});
	},

	chart_par:{themes:'column',height:500,bg:'',legend:{},valueSuffix:''},
	/******************************旧版数据统计(end)******************************/
	mouse_click:function(o, name, fun){ //兼容个别浏览器，点击事件与拖动效果相互冲突
		var $obj={};
		$obj[name+'_hasMove'] = false;
		$obj[name+'_obj'] = {};
		$obj[name+'_end'] = 1;
		$obj[name+'_click'] = 1;
		o.off().on('click',function(e){
			if ($obj[name+'_click'] == 1) {
				$.isFunction(fun) && fun($(this));
			}
		}).on('mousedown', function(e){ //按下鼠标键事件
			$obj[name+'_obj'].x = e.pageX;
			$obj[name+'_obj'].y = e.pageY;
			$obj[name+'_end'] = 0;
			$obj[name+'_hasMove'] = false;
		}).on('mousemove', function(e){ //移动鼠标键事件
			return false; //临时
			if($obj[name+'_end'] == 1) return false;
			if(e.pageX === $obj[name+'_obj'].x && e.pageY === $obj[name+'_obj'].y) {
				$obj[name+'_hasMove'] = false;
			}else{
				$obj[name+'_hasMove'] = true;
			}
		}).on('mouseup', function(e){ //松开鼠标键事件
			$obj[name+'_obj'].x = e.pageX;
			$obj[name+'_obj'].y = e.pageY;
			$obj[name+'_end'] = 1;
			if(!$obj[name+'_hasMove']){ //没有移动过当前元素
				$obj[name+'_click'] = 0;
				$.isFunction(fun) && fun($(this));
			}
			$obj[name+'_hasMove'] = false;
		});
	},

	upload_img_init: function()
	{
		// 图片上传默认设置
		let callback = (typeof(arguments[0]) == 'undefined') ? '' : arguments[0];
		$('.multi_img .preview_pic input:hidden').each(function() {
			if ($(this).attr('save') == 1 && !$(this).next('a').length) {
				$(this).parent().append(frame_obj.upload_img_detail($(this).val())).children('.upload_btn').hide();
			}
		});
		frame_obj.mouse_click($('.multi_img .pic_btn .del'), 'Del', function($this) {
			// 产品颜色图点击事件
			var $obj = $this.parents('.img');
			$obj.removeClass('isfile').removeClass('show_btn').parent().append($obj);
			$obj.find('.pic_btn .zoom').attr('href','javascript:;');
			$obj.find('.preview_pic .upload_btn').show();
			$obj.find('.preview_pic a').remove();
			$obj.find('.preview_pic input:hidden').val('').attr('save', 0).trigger('change');
			if (callback && typeof callback === "function") {
				eval(callback());
			}
		});
	},

	upload_file_init: function()
	{
		// 文件上传默认设置
		$('.multi_file .preview_file input:hidden').each(function() {
			if ($(this).attr('save') == 1 && !$(this).next('a').length) {
				let _obj = $(this).parents('.multi_file');
				frame_obj.generate_file_cover(_obj,$(this).val())
			}
		});
		let callback = (typeof(arguments[0]) == 'undefined') ? '' : arguments[0];
		// 本地视频删除点击事件
		$('#plugins_visual').on('click','.component_video .multi_file .file_btn .del', function() {
			var $obj = $(this).parents('.file');
			$obj.removeClass('isfile').removeClass('show_btn').parent().append($obj);
			$obj.find('.file_btn .zoom').attr('href','javascript:;');
			$obj.find('.preview_file .upload_btn').show();
			$obj.find('.preview_file a').remove();
			$obj.find('.preview_file input:hidden').val('').attr('save', 0).trigger('change');
			if (callback && typeof callback === "function") {
				eval(callback());
			}
		});
	},

	//产品图片上传默认设置
	upload_pro_img_init:function(type){
		var obj=(typeof(arguments[1])=='undefined')?'':arguments[1],
			isParent=(typeof(arguments[2])=='undefined')?0:parseInt(arguments[2]),
			o=new Object;
		if (obj) {
			if(isParent==1){ o=parent.$(obj).find(".img:not('.video')"); }
			else{ o=$(obj).find(".img:not('.video')"); }
		}else{
			if(isParent==1){ o=parent.$(".pro_multi_img .img:not('.video')"); }
			else o=$(".pro_multi_img .img:not('.video')");
		}
		if(type==1){ //从左到右
			o.each(function(){
				if(!$(this).hasClass('isfile')){
					$(this).addClass('show_btn');
					return false;
				}
			});
		}else{ //从右到左
			o.each(function(){
				if($(this).hasClass('isfile')){
					$(this).prev().addClass('show_btn'); //上一个显示
					return false;
				}
				if($(this).index()+1==$(this).parent().find('.img').length){ //最后一个，还没有关联图片可以显示
					$(this).addClass('show_btn');
				}
			});
		}
	},

	//产品图片上传默认设置
	upload_video_init:function(type) {
		var obj = (typeof(arguments[1]) == 'undefined') ? '' : arguments[1],
			isParent = (typeof(arguments[2]) == 'undefined') ? 0 : parseInt(arguments[2]),
			o = new Object;
		if (obj) {
			if (isParent == 1) { o = parent.$(obj).find(".file"); }
			else { o = $(obj).find(".file"); }
		} else {
			if (isParent == 1) { o = parent.$(".multi_file .file"); }
			else o = $(".multi_file .file");
		}
		if (type == 1) {
			// 从左到右
			o.each(function(){
				if(!$(this).hasClass('isfile')){
					$(this).addClass('show_btn');
					return false;
				}
			});
		} else {
			// 从右到左
			o.each(function(){
				if($(this).hasClass('isfile')){
					$(this).prev().addClass('show_btn'); //上一个显示
					return false;
				}
				if($(this).index() + 1 == $(this).parent().find('.file').length) {
					//最后一个，还没有关联图片可以显示
					$(this).addClass('show_btn');
				}
			});
		}
	},

	//多功能选项框
	box_option_list:function(){
		//全选按钮
		$('.box_option_list .select_all').off().on('click', function(){
			var $Obj=$(this).parents('.box_option_list'),
				$DataType=$Obj.find('.option_button_menu a.current').attr('data-type');
			$Obj.find('.option_not_yet .btn_attr_choice[data-type='+$DataType+']').click();
			return false;
		});

		//类型选项卡
		$('.box_option_list .option_button_menu>a').off().on('click', function(){
			var $This=$(this),
				$DateType=$This.attr('data-type');
			$This.addClass('current').siblings().removeClass('current');
			$This.parents('.box_option_list').find('.select_list[data-type='+$DateType+']').show().siblings().hide();
			return false;
		});
		$('.box_option_list').each(function(){
			$(this).find('.option_button_menu>a:eq(0)').click(); //默认点击第一个
		});

		//触发属性选项文本框
		$('.option_selected').off().on('click', function(){
			var $type=$(this).data('type'),
				$Obj=$(this).find('.box_input'),
				$Box=$Obj.parents('.box_option_list'),
				$DataType=$Box.find('.option_button_menu>a.current').attr('data-type'),
				$ItemLast=$(this).find('.btn_attr_choice:last'),
				$ItemLastLeft=($ItemLast.length?$ItemLast.offset().left:0),
				$BoxLeft=0, $BoxWidth=0;
			$(this).addClass('option_focus');
			$(this).nextAll('.option_not_yet').show();
			$(this).nextAll('.option_button').show();
			$Obj.show().focus();
			$BoxLeft=($ItemLastLeft?($ItemLastLeft+$ItemLast.outerWidth(true))-$(this).offset().left:0);
			$BoxWidth=($(this).outerWidth()-$BoxLeft-31);
			if($BoxWidth<20){ //小于20，自动换行
				$Obj.css({'width':$(this).outerWidth()-41});
			}else{
				$Obj.css({'width':$BoxWidth, 'position':'absolute', 'bottom':5, 'left':$BoxLeft});
			}
			$Obj.off('keyup blur').on('blur', function(){ //文本框失去焦点
				setTimeout(function(){  // 延迟防止点击选项被消失
					$Obj.val('').hide().removeAttr('style');
				}, 100);
			}).on('keyup', function(e){ //属性选项添加
				var value=$.trim($(this).val()),
					key=window.event?e.keyCode:e.which,
					html;
				if(key==13 || (key == 188 && !e.shiftKey)){ //回车键
					if(value){
						var value_ary, MaxNum=0, vid='ADD:';
						if ($type == 'seo_keyword') {
							value_ary = value.split(',');
						} else {
							value_ary = [value];
						}
						for (i in value_ary) {
							var Stop=0,
								value = value_ary[i];
							if(!value) continue;
							$Obj.val('').hide().removeAttr('style');
							//检查内容有没有重复一致的选项存在
							$Box.find('.option_selected input[name='+$DataType+'Name\\[\\]]').each(function(){ //已选中区域
								let boolValue = $(this).val() == value
								if ($DataType == 'tags') boolValue = $(this).val().toLocaleLowerCase() == value.toLocaleLowerCase()								
								if(boolValue){
									Stop=1;
									return false; //终止继续执行
								}
							});
							if(Stop==0){ //还没有被终止
								$Box.find('.option_not_yet input[name='+$DataType+'Name\\[\\]]').each(function(){ //未选中区域
									let boolValue = $(this).val() == value
									if ($DataType == 'tags') boolValue = $(this).val().toLocaleLowerCase() == value.toLocaleLowerCase()									
									if(boolValue){
										$(this).parent().click();
										Stop=1;
										return false; //终止继续执行
									}
								});
							}
							if(Stop==1){ continue; } //终止继续执行
							MaxNum=parseInt($Box.find('.option_max_number').val()); //获取新标签ID的最大值
							MaxNum+=1;
							vid+=MaxNum;
							$Obj.prev('.select_list').append(frame_obj.box_option_button(value, vid, $DataType, 1, 0));
							if($DataType=='overseas'){
								$.post('?', {"do_action":"set.shipping_overseas_add", 'Name':value}, function(data){
									if(data.ret==1){
										$Box.find('.option_current[value="'+vid+'"]').val(data.msg);
									}else{
										$Box.find('.option_current[value="'+vid+'"]').parent().remove();
									}
								}, 'json');
							}
							$Box.find('.option_max_number').val(MaxNum);
							frame_obj.box_option_button_choice();
							if($Box.attr('data-choice-type')=='radio'){ //属于单选状态，已选中区域只能呆一个选中选项
								$Box.find('.option_selected .btn_attr_choice[data-type='+$DataType+'] .option_current[value="'+vid+'"]').parent().siblings().find('i').click();
							}
							if($Box.find('.option_selected .btn_attr_choice').length<1){ //没有选项，显示placeholder
								$Box.find('.placeholder').removeClass('hide');
							}else{
								$Box.find('.placeholder').addClass('hide');
							}
							$Obj.trigger('change');
						}
						$Obj.click();
					}
					if(window.event){//IE
						e.returnValue=false;
					}else{//Firefox
						e.preventDefault();
					}
					return false;
				}else{ //其他按键
					if(value.length>2){ //两个字母以上才能进行搜索功能
						$.post('/api/SettingPhotos/CheckOption/', {'Keyword':value, 'Type':$DataType}, function(data){
							if(data.ret==1){
								html='';
								for(k in data.msg.Fixed){
									html+=frame_obj.box_option_button(data.msg.Fixed[k], k, $DataType, 0, 1);
								}
								for(k in data.msg.Option){
									html+=frame_obj.box_option_button(data.msg.Option[k], k, $DataType, 0, 0);
								}
								$Box.find('.option_not_yet .select_list[data-type=search]').html(html);
								$Box.find('.option_not_yet .select_list[data-type=search]').show().siblings().hide();
								frame_obj.box_option_button_choice();
							}
						}, 'json');
					}else{
						$Box.find('.option_not_yet .select_list[data-type=search]').html('');
						$Box.find('.option_not_yet .select_list[data-type='+$DataType+']').show().siblings().hide();
					}
				}
			});
		});

		$('body').click(function(e){ //属性框去掉着色
			if($(e.target).parents('.box_option_list').length==0){
				$('.option_selected').removeClass('option_focus');
				$('.option_selected').nextAll('.option_not_yet').hide();
				$('.option_selected').nextAll('.option_button').hide();
			}
		});
	},

	//多功能选项按钮
	box_option_button:function(name, value, type, isCurrent, isFixed, $icon_txt){
		var html='';
		html+='<span class="btn_attr_choice'+(isCurrent?' current':'')+'" data-type="'+type+'"'+(isFixed?' data-fixed="1"':'')+'>';
		html+=	($icon_txt?$icon_txt:'');
		html+=	'<b>'+name+'</b>';
		if(type == 'overseas'){
			html+=	'<input type="checkbox" name="OvId" value="'+value+'" class="option_current" checked="checked">';
		}else{
			html+=	'<input type="checkbox" name="'+type+'Current[]" value="'+value+'" class="option_current"'+(isCurrent?' checked':'')+' />';
			html+=	'<input type="hidden" name="'+type+'Option[]" value="'+value+'" />';
		}
		html+=	'<input type="hidden" name="'+type+'Name[]" value="' + global_obj.htmlspecialchars(name) + '" />';
		html+=	'<i></i>';
		html+='</span>';
		return html;
	},

	//多功能选项按钮事件
	box_option_button_choice:function(){
		//选项整体按钮
		$('.box_option_list .btn_attr_choice').off().on('click', function(){
			var $This=$(this),
				$DataType=$This.attr('data-type'),
				$Obj=$This.find('input.option_current'),
				$Parent=$This.parent().parent()
				$Target=$Parent.prev().find('.select_list'),
				$Box=$Obj.parents('.box_option_list'),
				$IsSave=0;
			if($This.hasClass('disabled')){
				return false;
			}
			if($Parent.hasClass('option_not_yet')){ //未选择 -> 已选中
				$Target.find('.btn_attr_choice[data-type='+$DataType+'] .option_current').each(function(){ //已选中区域里面，相同类型的选项
					if($(this).val()==$Obj.val()){ //有相同的数值
						$IsSave=1;
						return false; //终止继续执行
					}
				});
				if($IsSave==1){ //已经有相同的选项，立马删掉
					$This.remove();
				}else{ //改成选中状态
					$This.appendTo($Target);
					$This.addClass('current');
					$This.find('i').show();
					$Obj.prop('checked', true);
					if($Parent.find('.btn_attr_choice').length<1){
						$Parent.hide();
					}
					if($This.attr('data-fixed')==1){ //固有标签
						$This.find('i').show();
					}
					if($Box.attr('data-choice-type')=='radio'){ //属于单选状态，已选中区域只能呆一个选中选项
						$This.siblings().find('i').click();
					}
				}
				$Box.find('.placeholder').addClass('hide');
			}
			return false;
		});

		//选项删除按钮
		$('.box_option_list .btn_attr_choice>i').off().on('click', function(){
			var $This=$(this).parent(),
				$Obj=$This.find('input.option_current'),
				$Parent=$This.parent().parent(),
				$DataType=$This.attr('data-type'),
				$Target='',
				$IsSave=0;
			if($Parent.hasClass('option_selected')){ //已选中 -> 未选择
				$Target=$Parent.next().find('.select_list[data-type='+$DataType+']');
				$Target.find('.btn_attr_choice .option_current').each(function(){ //未选中区域里面，相同类型的选项
					if($(this).val()==$Obj.val()){ //有相同的数值
						$IsSave=1;
						return false; //终止继续执行
					}
				});
				$Parent.find('.box_input').val('').hide().removeAttr('style');
				if($DataType=='overseas'){ //海外仓
					$(this).hide();
				}
				if($IsSave==1){ //已经有相同的选项，立马删掉
					$This.remove();
				}else{ //改成未选中状态
					$Target.length ? $This.appendTo($Target) : $This.remove();
					$This.removeClass('current');
					$Obj.prop('checked',false);
					$Parent.next('.option_not_yet').show().next('.option_button').show();
					if($This.attr('data-fixed')==1){ //固有标签
						$(this).hide();
					}
				}
				if($Parent.find('.btn_attr_choice').length<1){ //没有选项，显示placeholder
					$Parent.find('.placeholder').removeClass('hide');
				}
			}else{ //未选择 -> 删除
				if ($DataType == 'tags') {
					let params = {
						'title':lang_obj.global.del_confirm,
						'confirmBtn':lang_obj.global.del,
						'confirmBtnClass':'btn_warn'
					};
					global_obj.win_alert(params, function(){
						if ($This.find('input').val() > 0) $Parent.append('<input type="hidden" name="tagsDel[]" value="' + $This.find('input').val() + '">');
						$This.remove();
					}, 'confirm');
				} else {
					$This.remove();
				}
				if($Parent.find('.btn_attr_choice').length<1){
					$Parent.hide();
				}
			}
			return false;
		});
	},

	box_drop_double:function(){
		$('body').on('click', '.box_drop_double dt', function(){
			var $This=$(this),
				$Height=$This.outerHeight(true),
				$Obj=$This.parents('.box_drop_double');
			//隐藏其他相同的下拉效果
			$('.box_drop_double dt').removeClass('box_drop_focus');
			$('.box_drop_double dd').hide();
			//着色
			$This.addClass('box_drop_focus');
			// 提示语消失
			$This.find('.select_placeholder').hide();
			//展开下拉
			$This.next('dd').toggle().css('top', $Height);
			//显示默认内容
			var $IsDefaultList=true;
			if($Obj.find('.box_input').length){
				var $Value=$.trim($Obj.find('.box_input').val());
				if($Value.length>0){
					$IsDefaultList=false;
				}
			}
			if($IsDefaultList){
				//调用默认内容
				frame_obj.box_drop_double_default($Obj);
				$Obj.find('.btn_back').hide();
			}
			//显示文本框（多选）
			if($Obj.attr('data-checkbox')==1){
				var $Input=$Obj.find('.check_input');
				$Input.show().focus();
				$Input.css({'width':($This.outerWidth()-($Input.offset().left-$This.offset().left))-31});
			}

			if($Obj.find('.drop_nodata_tip').length){ //有没数据提示的时候。显示出来其他的先隐藏
				$Obj.find('.btn_load_more').hide();
				$Obj.find('.drop_list').hide();
				$Obj.find('.drop_nodata_tip').show();
			}

			return false;
		}).on('click', '.box_drop_double .children', function(e) {
			//下一级选项
			var $This = $(this),
				$CBox = $This.find('.input_checkbox_box');
			if ($(e.target).hasClass('input_checkbox_box') || $(e.target).hasClass('input_checkbox')) {
				//其实是在点击多选按钮
				return false;
			}
			if ($(e.target).hasClass('input_radio_box') || $(e.target).hasClass('input_radio')) {
				//其实是在点击单选按钮
				return false;
			}
			var $Name = $This.attr('data-name'),
				$Value = $This.attr('data-value'),
				$Type = $This.attr('data-type'),
				$Table = $This.attr('data-table'),
				$Top = $This.attr('data-top'),
				$All = $This.attr('data-all'),
				$CheckAll = $This.attr('data-check-all'),
				$CustomData = $This.attr('data-custom'),
				$Obj = $This.parents('.box_drop_double'),
				$Checkbox = $Obj.attr('data-checkbox'),
				$TopType = $Obj.find('.drop_menu').attr('data-type');
			frame_obj.box_drop_double_ajax($Obj, {'Value':$Value, 'Type':$Type, 'Table':$Table, 'Top':$Top, 'All':$All, 'CheckAll':$CheckAll, 'Checkbox':$Checkbox, 'TopType':$TopType, 'CustomData': $CustomData});
			$Obj.find('.btn_load_more').attr({'data-value':$Value, 'data-type':$Type, 'data-table':$Table, 'data-top':$Top, 'data-all':$All, 'data-check-all':$CheckAll, 'data-start':1});//加载更多按钮
			return false;
		}).on('click', '.box_drop_double .item', function(e) {
			//多选勾选按钮
			var $Item = $(this),
				$Name = $Item.attr('data-name'),
				$Value = $Item.attr('data-value'),
				$Type = $Item.attr('data-type'),
				$Table = $Item.attr('data-table'),
				$Top = $Item.attr('data-top'),
				$Obj = $Item.parents('.box_drop_double'),
				$Checkbox = $Obj.attr('data-checkbox'),
				$Limit = $Obj.find('.check_input').attr('limit'),
				$ItemCount = $Obj.find('.select_list').find('.btn_attr_choice').length;
			if ($Checkbox == 1) {
				//多选
				var $List = $Obj.find('.box_checkbox_list'),
					$Input = $List.find('.current[data-type='+$Type+'] .option_current[value="'+$Value+'"]'),
					$Check = $Obj.find('.check_input'),
					$Icon = '';
				if ($Item.hasClass('btn_check_all')) {
					//全选事件
					if($Item.find('.input_checkbox_box').hasClass('checked')){ //勾选 生成按钮
						$Obj.find('.item:gt(0) .input_checkbox_box').not('.checked').click();
					}else{ //取消勾选
						$Obj.find('.item:gt(0) .input_checkbox_box.checked').click();
					}
				} else {
					//勾选事件
					if($Item.find('.input_checkbox_box').hasClass('checked')){ //勾选 生成按钮
						if($Limit && ($ItemCount >= $Limit)){
							$Item.find('input').prop('checked', false);
							$Item.find('.input_checkbox_box').removeClass('checked');
							return true;
						}
						if($Value==-1){ //“所有”选项
							$Item.siblings().find('.input_checkbox_box').removeClass('checked').find(':checkbox').prop('checked',false); //取消其他选项的勾选
							$List.find('.current[data-type='+$Type+']').remove();
							$List.find('.select_list').append(frame_obj.box_option_button($Name, $Value, $Type, 1, 0));
							$Check.css({'width':20});
							$Check.css({'width':($List.outerWidth()-($Check.offset().left-$List.offset().left))-31});
						}else{
							if($Item.siblings('.item[data-value="-1"]').length){ //取消全选的勾选
								$Item.siblings('.item[data-value="-1"]').find('.input_checkbox_box').removeClass('checked').find(':checkbox').prop('checked',false);
							}
							$List.find('.current[data-type='+$Type+'] .option_current[value="-1"]').parent().remove();
							if(!$Input.length){
								if($Item.find('.icon_products').length){ //带有Icon
									$Icon=$Item.find('.icon_products img').attr('src');
									$Icon='<em class="icon icon_products pic_box"><img src="'+$Icon+'" /><span></span></em>';
								}
								if ($List.find('.select_list .last').length) {
									$List.find('.select_list .last:eq(0)').before(frame_obj.box_option_button($Name, $Value, $Type, 1, 0, $Icon));
								} else {
									$List.find('.select_list').append(frame_obj.box_option_button($Name, $Value, $Type, 1, 0, $Icon));
								}
							}
							$Check.css({'width':20});
							$Check.css({'width':($List.outerWidth()-($Check.offset().left-$List.offset().left))-31});
						}
					}else{ //取消勾选 删除按钮
						$Input.parent().remove();
					}
					if($Obj.find('.btn_check_all').length){ //有全选按钮
						if($Obj.find('.item:gt(0) .input_checkbox_box.checked').length==$Obj.find('.item:gt(0) .input_checkbox_box').length){
							$Obj.find('.btn_check_all .input_checkbox_box').addClass('checked').find(':checkbox').prop('checked', ture);
						}else{
							$Obj.find('.btn_check_all .input_checkbox_box').removeClass('checked').find(':checkbox').prop('checked',false);
						}
					}
				}
				
				frame_obj.box_drop_double_current($Obj);
			} else {
				//单选
				if ($Item.find('.input_radio_box').length) {
					//勾选事件(单选按钮)
					if ($Item.find('.input_radio_box').hasClass('checked')) {
						//勾选 生成按钮
						let fun = function () {
							if ($Obj.hasClass('edit_box')) {
								$Type == 'add' && ($Name = $Value); //添加
								$Obj.find('dt input[type=text]').val($Name).parent().find('.hidden_value').val($Value).next().val($Type);
								$Obj.find('.hidden_value').trigger('change');
							} else {
								$Obj.find('dt>div>span').text($Name).parent().find('.hidden_value').val($Value).next().val($Type);
								$Obj.find('.hidden_value').trigger('change');
							}
						}
						if ($(e.target).hasClass('input_radio_box') || $(e.target).hasClass('input_radio')) {
							if (typeof(window.boxDropDoubleJson.itemRadioClickBefore) == 'function') {
								window.boxDropDoubleJson.itemRadioClickBefore(fun)
							} else {
								fun()
							}
							//其实是在点击单选按钮
							$Item.parents('dd').hide();
						} else {
							fun()
						}
					}
				} else {
					//没有单选按钮
					let fun = function () {
						if ($Obj.hasClass('edit_box')) {
							$Type == 'add' && ($Name = $Value); //添加
							$Obj.find('dt input[type=text]').val($Name).parent().find('.hidden_value').val($Value).next().val($Type);
							$Obj.find('.hidden_value').trigger('change');
						} else {
							$Obj.find('dt>div>span').text($Name).parent().find('.hidden_value').val($Value).next().val($Type);
							$Obj.find('.hidden_value').trigger('change');
						}
					}
					if (!$Item.hasClass('children')) {
						if (typeof(window.boxDropDoubleJson.itemRadioClickBefore) == 'function') {
							window.boxDropDoubleJson.itemRadioClickBefore(fun)
						} else {
							fun()
						}
						//没有下一级选项，最终确定
						$Item.parents('dd').hide();
					} else {
						fun()
					}
				}
			}
			return false;
		}).on('click', '.box_drop_double .box_checkbox_list i', function(e){
			var $Obj = $(this).parents('.box_drop_double');
				dropDownObj = $Obj.find('.drop_down').hide();
			$(this).parent().remove();
			if(window.event){//IE
				e.returnValue=false;
			}else{//Firefox
				e.preventDefault();
			}
			if ($Obj.attr('data-checkbox') == 1 && !$Obj.find('.select_list').html()) {				
				$Obj.find('.select_placeholder').show();
				$Obj.find('.box_input').hide();
			}
			frame_obj.box_drop_double_current($Obj);
			return false;
		}).on('click', '.box_drop_double', function(){
			return false;
		}).on('click', '.btn_back', function(){ //返回
			var $This=$(this),
				$Value=$This.attr('data-value'),
				$Type=$This.attr('data-type'),
				$Table=$This.attr('data-table'),
				$Top=$This.attr('data-top'),
				$All=$This.attr('data-all'),
				$CheckAll=$This.attr('data-check-all'),
				$Obj=$This.parents('.box_drop_double'),
				$Checkbox=$Obj.attr('data-checkbox'),
				$TopType=$Obj.find('.drop_menu').attr('data-type');
			frame_obj.box_drop_double_ajax($Obj, {'Value':$Value, 'Type':$Type, 'Table':$Table, 'Top':$Top, 'All':$All, 'CheckAll':$CheckAll, 'Checkbox':$Checkbox, 'TopType':$TopType});
			return false;
		}).on('click', '.btn_load_more', function() {
			// 加载更多
			let $This = $(this),
				$Value = $This.attr('data-value'),
				$Type = $This.attr('data-type'),
				$Table = $This.attr('data-table'),
				$Top = $This.attr('data-top'),
				$All = $This.attr('data-all'),
				$CheckAll = $This.attr('data-check-all'),
				$Start = $This.attr('data-start'),
				$exclude = $This.attr("data-exclude"),
				$Obj = $This.parents('.box_drop_double'),
				$Checkbox = $Obj.attr('data-checkbox'),
				$TopType = $Obj.find('.drop_menu').attr('data-type');
				$keyword = $.trim($Obj.find('dt .box_input').val())
			frame_obj.box_drop_double_ajax($Obj, {"Value": $Value, "Type": $Type, "Table": $Table, "Top": $Top, "All": $All, "CheckAll": $CheckAll, "Checkbox": $Checkbox, "TopType": $TopType, "Start": $Start, "exclude": $exclude, "Keyword": $keyword});
			$Obj.find(".btn_load_more").attr({"data-value": $Value, "data-type": $Type, "data-table": $Table, "data-top": $Top, "data-all": $All, "data-check-all": $CheckAll, "data-start": parseInt($Start) + 1}); // 加载更多按钮
			return false;
		}).on('keyup', '.box_drop_double dt .box_input', function(e){
			// 搜索功能
			let $Value = $.trim($(this).val()),
				$Key = window.event ? e.keyCode : e.which,
				$Obj = $(this).parents('.box_drop_double'),
				$Checkbox = $Obj.attr('data-checkbox'),
				$isShowAdd = $Obj.attr('data-showadd'),
				$exclude = $Obj.find(".btn_load_more").attr("data-exclude");
			if ($Key == 13) {
				// 回车键
				// 忽略...
			} else {
				// 其他按键
				if ($Value.length > 1) {
					// 两个字母以上才能进行搜索功能
					$Data = $Obj.find('.drop_list').attr('data');
					$Data = global_obj.json_encode_data($Data);
					let $position = $Obj.find('.drop_menu').attr('data-type')
					let formData = { "do_action": "action.check_drop_double", "Keyword": $Value, "Data": $Data, "Checkbox": $Checkbox, "isShowAdd": $isShowAdd, "exclude": $exclude, 'position': $position }
					$.ajax({
						url: '/api/OrderList/CheckDropDouble',
						type: 'POST',
						dataType: 'json',
						contentType: 'application/json',
						data: JSON.stringify(formData),
						success: function(result) {
							if (result.ret == 1) {
								$Obj.find('.btn_back, .btn_load_more').hide()
								$Obj.find('.drop_skin').hide()
								$Obj.find('.drop_list').html(result.msg.Html)
								frame_obj.box_drop_double_current($Obj)
								if (result.msg.dataCount == 20) {
									$Obj.find('.btn_load_more').show()
								}
							}
						}
					});
				} else {
					// 否则自动还原默认
					$Obj.find('.btn_back, .btn_load_more').hide();
					frame_obj.box_drop_double_default($Obj);
				}
			}
			if (window.event) {
				// IE
				e.returnValue = false;
			} else {
				// Firefox
				e.preventDefault();
			}
			return false;
		});
		$('body').click(function(){
			$('.box_drop_double dt').removeClass('box_drop_focus');
			$('.box_drop_double dd').hide();
			$('.box_drop_double .btn_load_more').attr('data-start', 1);
			$('input.check_input[name=ApplyValue]').val('');
			$('.box_drop_double[data-checkbox=1]').each(function(){
				if (!$(this).find('.select_list').html() && $(this).find('input[name=Unit]').val() == '') {
					$(this).find('.select_placeholder').show();
					$(this).find('.box_input').hide();
				}
			});
		});
		$('.box_drop_double .select[selected]').each(function(){
			$(this).click();
		});

		$('.box_drop_double').each(function(){
			frame_obj.box_drop_double_default($(this));
		});
	},

	box_drop_double_default:function(obj) {
		var $Data = obj.find('.drop_list').attr('data'),
			$DataValue = obj.find('.drop_list').attr('data-select'),
			$Checkbox = obj.attr('data-checkbox'),
			$TopType = obj.find('.drop_menu').attr('data-type'),
			$isMore = obj.find('.drop_list').attr('data-more'),
			$Html = '';
		obj.find('.drop_skin').show();
		obj.find('.drop_list').html(' ');
		if ($Data != '[]') {
			$Data = $.evalJSON($Data);
			for (k in $Data) {
				$Html += '<div class="item'+($Data[k]['Table']?' children':'')+($Data[k]['Type'] == 'add'?' drop_add':'')+'" data-name="'+($Data[k]['AliasName'] ? $Data[k]['AliasName'] : $Data[k]['Name'])+'" data-value="' + ($Data[k]['Value'] ? $Data[k]['Value'] : k) + '" data-type="'+$Data[k]['Type']+'" data-table="'+$Data[k]['Table']+'" data-top="0" data-all="'+($Data[k]['All']==0?0:1)+'" data-check-all="'+($Data[k]['CheckAll']==1?1:0)+'" data-custom="' + $Data[k]['customData'] + '">';
				if ($Checkbox == 1) {
					//多选，根目录除外
					let DisabledStatus = false
					if ($Data[k]['Disabled'] == undefined) {
						$Data[k]['Disabled'] = false
					} else {
						DisabledStatus = true
					}
					$Html += '<span class="input_checkbox_box' + ((($Data[k]['Table'] && !DisabledStatus) || $Data[k]['Disabled']) ? " disabled" : "") + '"><span class="input_checkbox"><input type="checkbox" name="_DoubleOption[]" value="' + k + '"' + ((($Data[k]['Table'] && !DisabledStatus) || $Data[k]['Disabled']) ? " disabled" : "") + ' /></span></span><span class="item_name">' + ($Data[k]['Icon'] ? $Data[k]['Icon'] : '') + $Data[k]['Name'] + '</span>';
				} else if ($TopType) {
					//单选，有单选按钮
					$Html += '<span class="input_radio_box' + ($Data[k]['Disabled'] ? " disabled" : "") + '"><span class="input_radio"><input type="radio" name="_DoubleOption[]" value="' + k + '"' + ($Data[k]['Disabled'] ? " disabled" : "") + '></span></span><span class="item_name">' + ($Data[k]['Icon'] ? $Data[k]['Icon'] : '') + $Data[k]['Name'] + '</span>';
				} else {
					//单选
					$Html += '<span>' + ($Data[k]['Icon'] ? $Data[k]['Icon'] : '') + $Data[k]['Name'] + '</span>';
				}
				$Html += ($Data[k]['Table'] ? '<em></em>' : '');
				$Html += '</div>';
			}
			if ($isMore == 'block') obj.find('.btn_load_more').show();
		} else {
			obj.find('.btn_load_more').hide();
		}
		obj.find('.drop_skin').hide();
		obj.find('.drop_list').html($Html);
		frame_obj.box_drop_double_current(obj);
	},

	box_drop_double_ajax:function(obj, data, callback){
		obj.find('.drop_skin').show();
		setTimeout(function(){
			$.post('/manage/action/box-drop-double/', data, function(result){
				if(result.ret==1){
					if(data.Top==1){
						frame_obj.box_drop_double_default(obj);
						obj.find('.btn_back, .btn_load_more').hide();
					}else{
						if (obj.find('.btn_load_more').length && parseInt(obj.find('.btn_load_more').data('start')) > 1 && obj.find('.btn_back').is(':hidden')) {
							//单独页面 并翻页 无需显示返回按钮
							obj.find('.btn_back').hide();
						} else {
							obj.find('.btn_back').show().attr({'data-value':result.msg.Back.Value, 'data-type':result.msg.Back.Type, 'data-table':result.msg.Back.Table, 'data-top':result.msg.Back.Top});
						}
						obj.find('.drop_skin').hide();
						if(result.msg.Start==0){//第一页
							obj.find('.drop_list').html(result.msg.Html);
						}else{//翻页
							obj.find('.drop_list').append(result.msg.Html);
						}
						if(result.msg.Count==20){
							obj.find('.btn_load_more').show();
						}else{
							obj.find('.btn_load_more').html(lang_obj.manage.global.no_more).animate('', 2000, function(){
								$(this).fadeOut(2000, function(){
									$(this).html(lang_obj.manage.global.load_more);
								})
							})
						}
						frame_obj.box_drop_double_current(obj);
					}
				}
			}, 'json');
		}, 500);
	},

	box_type_menu:function(callback){
		let obj =(typeof(arguments[1]) == 'undefined') ? $('#main') : arguments[1]
		obj.delegate('.box_type_menu .item', 'click', function(){
			var $this=$(this),
				$index=$this.index(),
				$content=$this.parent().parent().find('.box_type_menu_content .item').not('.item .item');
			if ($this.hasClass("disabled")) return false;
			$this.parent().find('.item').removeClass('checked').find('input').prop('checked',false);
			$this.addClass('checked').find('input').prop('checked', 'checked');
			$content.hide().eq($index).show();
			if(typeof callback === "function") callback($this);
		});
	},

	box_drop_double_current:function (obj) {  // 下拉列表着色勾选，目前checkbox才会有
		var $Checkbox = obj.attr('data-checkbox');
		var $DataValueAry = new Array();
		if ($Checkbox == 1) {
			// 构造选中数组(多选框)
			obj.find('.box_checkbox_list .select_list .btn_attr_choice').each(function () {
				let value = $(this).find('input[type=checkbox]').val();
				value = isNaN(parseInt(value)) ? value : parseInt(value); 
				$DataValueAry.push(value);
			});
			// 勾选着色
			obj.find('.drop_list').children('.item').removeClass('current');
			obj.find('.drop_list').children('.item').find('.input_checkbox_box').removeClass('checked');
			obj.find('.drop_list').children('.item').find('input[type=checkbox]').prop('checked', false);
			obj.find('.drop_list').children('.item').each(function () {
				let value = $(this).data('value');
				value = isNaN(parseInt(value)) ? value : parseInt(value); 
				if ($.inArray(value, $DataValueAry) != -1) {
					$(this).addClass('current');
					$(this).children('.input_checkbox_box').addClass('checked');
					$(this).find('input[type=checkbox]').prop('checked', true);
				}
			});
		} else {
			if (obj.find('dt').length) {  // 下拉单选
				let value = obj.find('dt input[type=hidden]:first').val();
				if (!value) value = 0;
				obj.find('.drop_list').children(".item[data-value='" + value + "']").find('.input_radio_box').addClass('checked');
			}
		}
	},

	//模拟select
	global_select_box:function(callBack) {
		$("body").on("click",'.global_select_box',function(){
			if($(this).hasClass('disabled')) return false;
			$('.global_select_box').removeClass('focus')
			$(this).addClass('focus');
			if (event.stopPropagation) {   
				// 针对 Mozilla 和 Opera   
				event.stopPropagation();   
			}else if (window.event) {   
				// 针对 IE   
				window.event.cancelBubble = true;   
			}
		}).on("click", ".global_select_box .select_ul li", function() {
			var $this = $(this),
				$val = $this.attr('data-value'),
				$text = $this.text(),
				$parents = $this.parents('.global_select_box');
			if ($this.hasClass("no_data")) {
				// 暂无数据
				return false;
			}
			$this.addClass("selected").siblings().removeClass("selected");
			$parents.find('.input_case input[type=hidden]').val($val);
			$parents.find('.imitation_select').val($text).removeAttr('style');
			$(".global_select_box").removeClass('focus');
			typeof callback === "function" && callBack($this); // 回调函数
			return false;
		}).on("mouseenter", ".global_select_box .select_ul li", function() {
			$(this).removeClass('leave').siblings().addClass('leave');
		}).on("mouseleave", ".global_select_box .select_ul li", function() {
			$(this).siblings().removeClass('leave');
		});
		$(document).click(function(event){
			$(".global_select_box").removeClass('focus');
		});
	},

	Waterfall:function(container, colWidth, colCount, cls){
		function Waterfall(param){
			this.id = typeof param.container == 'string' ? document.getElementById(param.container) : param.container;
			this.colWidth = param.colWidth;
			this.colCount = param.colCount || 4;
			this.cls = param.cls && param.cls != '' ? param.cls : 'wf-cld';
			this.init();
		}
		//瀑布流
		Waterfall.prototype = {
			getByClass:function(cls,p){
				var arr = [],reg = new RegExp("(^|\\s+)" + cls + "(\\s+|$)","g");
				var nodes = p.getElementsByTagName("*"),len = nodes.length;
				for(var i = 0; i < len; i++){
					if(reg.test(nodes[i].className)){
						arr.push(nodes[i]);//
						reg.lastIndex = 0;
					}
				}
				return arr;
			},
			maxArr:function(arr){
				var len = arr.length,temp = arr[0];
				for(var ii= 1; ii < len; ii++){
					if(temp < arr[ii]){
						temp = arr[ii];
					}
				}
				return temp;
			},
			getMar:function(node){
				var dis = 0;
				if(node.currentStyle){
					dis = parseInt(node.currentStyle.marginBottom);
				}else if(document.defaultView){
					dis = parseInt(document.defaultView.getComputedStyle(node,null).marginBottom);
				}
				return dis;
			},
			getMinCol:function(arr){
				var ca = arr,cl = ca.length,temp = ca[0],minc = 0;
				for(var ci = 0; ci < cl; ci++){
					if(temp > ca[ci]){
						temp = ca[ci];
						minc = ci;
					}
				}
				return minc;
			},
			init:function(){
				var _this = this;
				var col = [],//列高
					iArr = [];//索引
				var nodes = _this.getByClass(_this.cls,_this.id),len = nodes.length;
				for(var i = 0; i < _this.colCount; i++){
					col[i] = 0;
				}
				for(var i = 0; i < len; i++){
					nodes[i].h = nodes[i].offsetHeight + _this.getMar(nodes[i]);
					iArr[i] = i;
				}

				for(var i = 0; i < len; i++){
					var ming = _this.getMinCol(col);
					nodes[i].style.left = ming * _this.colWidth + "px";
					nodes[i].style.top = col[ming] + "px";
					col[ming] += nodes[i].h;
				}

				_this.id.style.height = _this.maxArr(col) + "px";
			}
		};

		new Waterfall({
			"container": container, //id
			"colWidth": colWidth,
			"colCount": colCount,
			"cls": cls //class
		});

	},

	highcharts_init:{
		colors:['#068eef', '#07bb8a', '#90ed7d', '#f7a35c', '#91e8e1',  '#f15c80', '#e4d354', '#8d4653'],
		cdx_colors:['#068eef', '#07bb8a', '#90ed7d', '#f7a35c', '#91e8e1',  '#f15c80', '#e4d354', '#8d4653'],

		//曲线面积图
		areaspline:function(ObjName, data){
			var chart=Highcharts.chart(ObjName, $.extend(true, {
					chart:	{type:'areaspline'},
					colors:	frame_obj.check_version('10,11,12,13')?frame_obj.highcharts_init.cdx_colors:frame_obj.highcharts_init.colors,
					credits:{enabled:false},
					title:	{text:''},
					exporting:{enabled:true},
					legend:	{enabled:false},
					yAxis:	{title:{text:''}},
					plotOptions: {areaspline:{fillOpacity:0.2}}
				}, data)
			);
		},

		//基础柱形图
		column_basic:function(ObjName, data){
			var chart=Highcharts.chart(ObjName, $.extend(true, {
					chart:	{type:'column'},
					colors:	frame_obj.check_version('10,11,12,13')?frame_obj.highcharts_init.cdx_colors:frame_obj.highcharts_init.colors,
					credits:{enabled:false},
					title:	{text:''},
					legend:	{enabled:false},
					yAxis:	{title:{text:''}},
					plotOptions: {column:{borderWidth:0}}
				}, data)
			);
		},

		//包含图例的饼图
		pie_legend:function(ObjName, data){
			var chart=Highcharts.chart(ObjName, $.extend(true, {
					chart:	{type:'pie'},
					colors:	frame_obj.check_version('10,11,12,13')?frame_obj.highcharts_init.cdx_colors:frame_obj.highcharts_init.colors,
					credits:{enabled:false},
					title:	{text:''},
					plotOptions: {
							pie:{allowPointSelect:true, cursor:'pointer', dataLabels:{enabled:false}, showInLegend:true}
					}
				}, data)
			);
		},

		//双坐标的折线图、柱状图混合图
		combo_dual_axes:function(ObjName, data){
			var chart = Highcharts.chart(ObjName, $.extend(true, {
					chart:	{zoomType: 'xy'},
					credits:{enabled:false},
					colors:	frame_obj.check_version('10,11,12,13')?frame_obj.highcharts_init.cdx_colors:frame_obj.highcharts_init.colors,
					tooltip:{valuePrefix:''}
				}, data)
			);
		},

		//标题居中的环形图
		pie_donut_center_title:function(ObjName, data){
			var chart = Highcharts.chart(ObjName, $.extend(true, {
				chart:	{spacing:[40, 0 , 40, 0]},
				title: 	{floating:true, text:'圆心显示的标题'},
				credits:{enabled:false},
				legend:	{itemMarginTop:15},
				tooltip:{pointFormat: '{series.name}: <b>{point.percentage:.1f}%</b>'},
				plotOptions:{
					pie:{
						allowPointSelect: true,
						showInLegend: true,
						cursor: 'pointer',
						dataLabels: {enabled: false},
						point:{
							events:{
								mouseOver: function(e){ //鼠标滑过时动态更新标题
									chart.setTitle({
										text: e.target.name+'\t'+e.target.y+' %'
									});
								}
							}
						},
					}
				}
			}, data),
			function(c){ //图表初始化完毕后的会掉函数
				//环形图圆心
				var centerY = c.series[0].center[1],
					titleHeight = parseInt(c.title.styles.fontSize);
				//动态设置标题位置
				c.setTitle({
					y:centerY + titleHeight/2
				});
			});
		}
	},

	//后台SESSION过期重新登录
	expire_login : function ()
	{
		// 登录页面跳过
		if($('#login').length) return false;
		// 监测登录状态
		setTimeout(function () {
			$.post('/manage/account/check-login-status', function (data) {
				if (data.ret == 1) {
					$('body').prepend(data.msg);
					$('#expire_login_form input[name=UserName]').val(shop_config.UserName);
					frame_obj.submit_form_init($('#expire_login_form'), '', '', 0, function (result) {
						if (result.ret == 1) {
							$('#expire_login_form, #expire_login_mask').remove();
						} else {
							$('#expire_login_form .tips').html(result.msg);
						}
					});
				}
			}, 'json');
		}, 7215000);
	},

	// 进度条(批量处理)
	box_progress: function($callBack) {
		$('#btn_progress_keep').click(function() {
			var $Data = new Object;
			$('#box_circle_container input[type=hidden]').each(function() {
				$Data[$(this).attr('name')] = $(this).val();
			});
			frame_obj.box_progress_ajax($Data, $callBack);
		});
		if($('.progress_completed_btn').length){
			$('#box_circle_container').on('click','.progress_completed_btn',function(){
				window.location.reload();
			})
		}
		if($('.circle_container_close').length){
			$('.sync_progress').on('click','.circle_container_close',function(){
				window.location.reload();
			})
		}
	},

	/**
	 * 进度条 ajax请求
	 * 
	 * @param array data 
	 * @param string callBack 
	 */
	box_progress_ajax:function(data, callBack)
	{
		$.ajax({
			url: (data.do_action ? data.do_action : '?'),
			type: 'post',
			data: data,
			dataType: 'json',
			success: function(result) {
				if (result) {
					if (result.ret == 3) {
						// 进度完成
						let completedText = lang_obj.manage.global.update_status[1];
						if(result.msg.Total && result.msg.Platform){
							completedText = lang_obj.manage.products.sync.sync_success_tips.replace("{{Number}}", result.msg.Total).replace("{{Platform}}", result.msg.Platform);
						}
						frame_obj.circle_progress_bar({
							percent: 100,
							processingText: lang_obj.manage.products.sync.sync_wait,
							completedText: completedText
						});
						$('.progress_completed_btn').addClass('show');
					} else if (result.ret == 2) {
						// 下一页
						$('.box_progress input[name=Start]').val(parseInt(result.msg.Start));
						if (parseInt(result.msg.Percent) > 0) {
							frame_obj.circle_progress_bar({
								percent: result.msg.Percent,
								processingText: lang_obj.manage.products.sync.sync_wait,
								completedText: lang_obj.manage.products.sync.sync_completed
							});
						}
					} else if (result.ret == 1) {
						// 反复请求当前的进度状态
						if (parseInt(result.msg.Percent) > 0) {
							frame_obj.circle_progress_bar({
								percent: result.msg.Percent,
								processingText: lang_obj.manage.products.sync.sync_wait,
								completedText: lang_obj.manage.products.sync.sync_completed
							});
						} else if(parseInt(result.msg.Percent) == 0) {
							frame_obj.circle_progress_bar({
								percent: 0,
								processingText: lang_obj.manage.products.sync.sync_wait,
								completedText: lang_obj.manage.products.sync.sync_completed
							});
						}
						setTimeout(function(){frame_obj.box_progress_ajax(data, callBack)}, 1000);
					} else {
						//进度中断
						$('.box_progress .status').text(lang_obj.manage.global.update_status[2]); // 操作失败
					}
				} else {
					// 进度异常
					$('.box_progress .status').text(lang_obj.manage.global.update_status[2]); // 操作失败
				}
				typeof callback === "function" && callBack(result); // 回调函数
			},
			complete: function(XMLHttpRequest, status) {
				if (status == 'timeout') {
					// 超时
					frame_obj.box_progress_ajax(data, callBack);
				}
			},
			error: function(XMLHttpRequest, status) {
				// 服务器错误
				frame_obj.box_progress_ajax(data, callBack);
			}
		});
	},

	check_amount:function(obj){ //只能填入数字
		obj.find('input[rel=amount]').off('keydown').keydown(function(e){
			var value	= $(this).val(),
				key		= (window.event?e.keyCode:e.which),
				ctrl	= (e.ctrlKey || e.metaKey),
				ext   	= 0;
			if (((key==109  && value.indexOf('-')<0) || (key==189  && value.indexOf('-')<0)) && $(this).attr('data-key')=='-') ext = 1;
			if (key == 116 || key == 9 || key == 37 || key == 39 || key == 46) ext = 1; //保留tab 左右方向键 向后删除键
			if (!e.shiftKey && ((key>95 && key<106) || (key>47 && key<60) || (key==110 && value.indexOf('.')<0) || (key==190 && value.indexOf('.')<0) || ext)) { //[0~9][.]
			} else if ((ctrl && key==67) || (ctrl && key==86) || (ctrl && key==65)) { //Ctrl+C Ctrl+V Ctrl+A
			} else if (key!=8) { //删除键
				if (window.event) {//IE
					e.returnValue = false;
				} else {//Firefox
					e.preventDefault();
				}
				return false;
			}
		}).on('input propertychange', function() {
			let value = $(this).val();
			if ($(this).attr('data-key') == '-') {
				value = value.replace(/[^-\d.]/g, "");
			} else {
				value = value.replace(/[^\d.]/g, "");
			}
			$(this).val(value);
		});
		obj.find('input[rel=int]').off('keydown').keydown(function(e){
			var value	= $(this).val(),
				key		= (window.event?e.keyCode:e.which),
				ctrl	= (e.ctrlKey || e.metaKey),
				ext   	= 0;
			if(((key==109  && value.indexOf('-')<0) || (key==189  && value.indexOf('-')<0)) && $(this).attr('data-key')=='-') ext = 1;
			if(key == 116 || key == 9 || key == 37 || key == 39 || key == 46) ext = 1; //保留tab 左右方向键 向后删除键
			if (!e.shiftKey && ((key>95 && key<106) || (key>47 && key<60) || ext)) { //[0~9][.]
			} else if ((ctrl && key==67) || (ctrl && key==86) || (ctrl && key==65)) { //Ctrl+C Ctrl+V Ctrl+A
			} else if (key!=8) { //删除键
				if (window.event) {//IE
					e.returnValue = false;
				} else {//Firefox
					e.preventDefault();
				}
				return false;
			}
		}).on('input propertychange', function() {
			let value = $(this).val();
			if ($(this).attr('data-key') == '-') {
				value = value.replace(/[^-\d.]/g, "");
			} else {
				value = value.replace(/[^\d.]/g, "");
			}
			$(this).val(value);
		});
		obj.find('input[rel=positive]').off('keydown').keydown(function(e){
			var value	= $(this).val(),
				key		= (window.event?e.keyCode:e.which),
				ctrl	= (e.ctrlKey || e.metaKey),
				ext   	= 0;
			if(key == 116 || key == 9 || key == 37 || key == 39 || key == 46) ext = 1; //保留tab 左右方向键 向后删除键
			if (!e.shiftKey && ((key>95 && key<106) || (key>47 && key<60) || ext)) { //[0~9][.]
			} else if ((ctrl && key==67) || (ctrl && key==86) || (ctrl && key==65)) { //Ctrl+C Ctrl+V Ctrl+A
			} else if (key!=8) { //删除键
				if (window.event) {//IE
					e.returnValue = false;
				} else {//Firefox
					e.preventDefault();
				}
				return false;
			}
		}).on('input propertychange', function() {
			let value = $(this).val();
			value = value.replace(/[^\d.]/g, "");
			$(this).val(value);
		});
		// 用于限制输入小数位的位数，以属性decimal的值为限制数
		obj.find('input[decimal]').on('input propertychange', function() {
			let value = $(this).val(),
				decimal = $(this).attr('decimal'),
				preg = '^\\d+(?:\\.\\d{0,' + decimal + '})?',
				pattern = new RegExp(preg);
			value = value.toString().match(pattern);
			$(this).val(value);
		});
	},

	check_version: function($version)
	{
		if (!$version) return false;
		if ($version == '' || $version == ',') return false;
		if (jQuery.inArray(shop_config.FunVersion, $version.split(',')) == -1) return false;
		return true;
	},

	//语言选择
	html_tab_button:function($class, $lang){
		var $language='';
		$default=shop_config.language_pack;
		$lang && ($default=$lang);
		$html='';
		$html+='<dl class="tab_box_row'+(shop_config.language.length==1?' hide':'')+($class?' '+$class:'')+'">';
			$html+='<dt><span>'+lang_obj.language[$default]+'</span><i></i></dt>';
			$html+='<dd class="drop_down">';
			for(i in shop_config.language){
				$language=shop_config.language[i];
				$html+='<a class="tab_box_btn item"'+($default==$language?' style="display:none;"':'')+' data-lang="'+$language+'"><span>'+lang_obj.language[$language]+'</span><i></i></a>';
			}
			$html+='</dd>';
			$html+='<dd class="tips">'+lang_obj.manage.error.supplement_lang+'</dd>';
		$html+='</dl>';
		return $html;
	},

	// SEO start
	seo_edit_keyword:function(jsonData){
		var $Form=$('#edit_keyword_form'),
			$List=$('.global_seo_box .keys_row .option_selected .btn_attr_choice'),
			$Html='', $i=0, $ID='';
		$Form.find('.edit_keyword_list').html(''); //清空内容

		$Html+=	'<div class="rows">';
		$Html+=		'<label>'+lang_obj.manage.global.name+'<div class="tab_box">'+frame_obj.html_tab_button()+'</div></label>';
		$Html+=		'<div class="input">';
		for(k in shop_config.language){
			$i=0;
			$Lang=shop_config.language[k];
			$Html+=		'<div class="tab_txt tab_txt_'+$Lang+'" lang="'+$Lang+'"'+(shop_config.lang==$Lang?'style="display:block;"':'')+'>';
			$List.each(function(){
				var $val = global_obj.htmlspecialchars($(this).find('b').text());
				$Html+=		'<div class="item clean" data-id="'+$i+'">';
				$Html+=			'<input type="text" class="box_input full_input" name="Name['+$i+']['+$Lang+']" value="'+$val+'" size="21" maxlength="255" autocomplete="off" notnull />';
				$Html+=		'</div>';
				++$i;
			});
			$Html+=		'</div>';
		}
		$Html+=		'</div>';
		$Html+=	'</div>';
		$Form.find('.edit_keyword_list').html($Html);
		if($Html){
			$Form.find('.edit_keyword_list, .box_button').show();
			$Form.find('.bg_no_table_data').hide();
		}else{
			$Form.find('.edit_keyword_list, .box_button').hide();
			$Form.find('.bg_no_table_data').show();
		}
		$.post(jsonData.do_action, jsonData, function(data){
			if(data.ret==1){
				for(lang in data.msg){ //属性名称
					for(k in data.msg[lang]){
						$Obj=$Form.find('.edit_keyword_list .tab_txt_'+lang+' .item:eq('+k+')');
						$Obj.find('.box_input').val(global_obj.htmlspecialchars_decode(data.msg[lang][k]));
					}
				}
				//事件
				$Form.on('click', '.attr_delete', function(){
					$Form.find('.edit_keyword_list .item[data-id="'+$(this).parent().attr('data-id')+'"]').remove();
				});
			}
		}, 'json');
	},

	seo_keyword_form_submit:function(){
		$('.fixed_edit_keyword').on('keydown', '.item .box_input', function(e){
			var key = window.event?e.keyCode:e.which;
			if (key == 188) {
				return false;
			}
		}).on('keyup', '.item .box_input', function(e){
			var val = $(this).val();
			$(this).val(val.replace(/,/g, ' '));
		});

		function we(e) {
			switch (typeof e) {
				case "boolean":
				case "number":
				case "object":
				case "string":
				case "undefined":
					return e;
				default:
					return ""
			}
		}

		// $('*[data-auto-change]').on('keyup', function(){
		// 	var $Name = $(this).attr('data-auto-change'),
		// 		$Value = $.trim($(this).val());
		// 	$('[name="'+$Name+'"]').val($Value);
		// });

		$('*[data-auto-change]').on('change', function(){
			var	$Name = $(this).attr('data-auto-change'),
				// $Obj = $('[name="'+$Name+'"]'),
				$Value = $(this).val(),
				valueObj = $Value.match(/\n/ig),
				$Max = parseInt($(this).attr('maxlength')) - parseInt(valueObj ? valueObj.length : $Value.length);

				let inner_length = parseInt($(this).val().length),
					max_length = $(this).attr('maxlength');
				if ($(this).val().match(/\n/ig)) {
					inner_length += $(this).val().match(/\n/ig).length;
				}
				$(this).prev().find('span').html(inner_length);
				$(this).prev().removeClass('ing').removeClass('max');
				if(inner_length > 0 && inner_length < max_length){
					$(this).prev().addClass('ing');
					return false;
				}else if(inner_length >= max_length){
					$(this).prev().addClass('max');
				}
		})

		if ($('[name=PageUrl]').length) {
			frame_obj.seo_url_show();
		}
		frame_obj.submit_form_init($('#edit_keyword_form'), '', function(){
			var $name='';
			$('#edit_keyword_form *[notnull]').each(function(){
				if($.trim($(this).val())==''){
					return false;
				}
			});
			return true;
		}, 0, function(result){
			if(result.ret==1){
				var $Obj=$('.keys_row'),
					$index=0;
				$Obj.find('.option_selected .btn_attr_choice').each(function(){
					$index=$(this).index();
					$(this).find('input').val(result.msg[shop_config.lang][$index]);
					$(this).find('b').html(result.msg[shop_config.lang][$index]);
				});
				global_obj.win_alert_auto_close(lang_obj.global.save_success, '', 1000, '8%');
			}
			$('#fixed_right .btn_cancel').click();
			return false;
		});
	},

	seo_url_show:function($IsFull=0){
		if ($('[name=PageUrl]').length && $('.prefix_textarea').length) {
			var $Textarea_w = $('[name=PageUrl]').width(),
				$Box_h = $('[name=PageUrl]').parent().height(),
				$Height = $('[name=PageUrl]').prev('.prefix').find('i').position().top - 10,
				$Left = $('[name=PageUrl]').prev('.prefix').find('i').position().left,
				$Top = $('[name=PageUrl]').prev('.prefix')[0].getBoundingClientRect().height - 7;
			if ($IsFull) { $Top = 0; }
			$('[name=PageUrl]').css({'top':$Top, 'text-indent':$Left, 'height':$Box_h - $Height});
		}
	},
	//SEO end

	//多语言框显示+自动调整宽度 sta
	multi_lang_show_all: function(obj) {
		$(obj).find('.multi_lang .lang_txt').each(function() {
			//控制语言版文本宽度
			var $Input = $(this).find('.unit_input'),
				$Width = ($(this)[0].getBoundingClientRect().width ? $(this)[0].getBoundingClientRect().width : $(this).outerWidth(true)),
				$b_w = $Input.find('b').length ? $Input.find('b')[0].getBoundingClientRect().width : 0,
				$f_w = $(this).find('.fc_red').length ? $(this).find('.fc_red').outerWidth(true) + 5 : 0,
				$i_w = 0;
			if ($Input.find('.box_input').length) {
				$i_w = $Input.find('.box_input').outerWidth(true) - $Input.find('.box_input').outerWidth();
				$Input.find('.box_input').css('width', $Width - $b_w - $f_w - $i_w - 4);
			} else {
				$i_w = $Input.find('.box_textarea').outerWidth(true) - $Input.find('.box_textarea').outerWidth();
				$Input.find('.box_textarea').css('width', $Width - $b_w - $f_w - $i_w - 4);
			}
		});
	},

	multi_lang_show_item: function(obj) {
		$(obj).on('focus', '.multi_lang .box_input, .multi_lang .box_textarea', function(){
			$('.multi_lang').each(function(){
				$(this).find('.lang_txt').not('[data-default="1"]').hide();
			});
			$(this).parents('.multi_lang').find('.lang_txt').show();
			if($(this).hasClass('box_textarea')){
				$(window).resize();
			}
			//控制语言版文本宽度
			var $Obj = $(this).parents('.multi_lang').find('.lang_txt');
			$Obj.each(function() {
				var $Input = $(this).find('.unit_input'),
					$Width = ($Obj[0].getBoundingClientRect().width ? $Obj[0].getBoundingClientRect().width : $Obj.outerWidth(true)),
					$b_w = $Input.find('b').length ? $Input.find('b')[0].getBoundingClientRect().width : 0,
					$f_w = $(this).find('.fc_red').length ? $(this).find('.fc_red').outerWidth(true) + 5 : 0,
					$i_w = 0;
				if ($Input.find('.box_input').length) {
					$i_w = $Input.find('.box_input').outerWidth(true) - $Input.find('.box_input').outerWidth();
					$Input.find('.box_input').css('width', $Width - $b_w - $f_w - $i_w - 4);
				} else {
					$i_w = $Input.find('.box_textarea').outerWidth(true) - $Input.find('.box_textarea').outerWidth();
					$Input.find('.box_textarea').css('width', $Width - $b_w - $f_w - $i_w - 4);
				}
			});

		});
		$('html').click(function(e){
			if(!$(e.target).parents('.lang_txt').length && !$(e.target).children('.lang_txt').length){
				$('.multi_lang').each(function(){
					$(this).find('.lang_txt').not('[data-default="1"]').hide();
				});
			}
		});
	},
	//多语言框显示+自动调整宽度 end

	multi_img_item: function ($InputName, $Num, $IsMove, $Module) {
		 
		$Html = '';
		$Html += '<dl class="img" num="' + $Num + '">';
		$Html += 	'<dt class="upload_box preview_pic">';
		$Html += 		'<input type="button" class="btn_ok upload_btn" name="submit_button" value="{/global.upload_pic/}" tips="" />';
		$Html += 		'<input type="hidden" name="' + $InputName + '" value="" data-value="" save="0" />';
		$Html += 	'</dt>';
		$Html += 	'<dd class="pic_btn">';
		if ($Module == 'products') {
			$Html += '<span class="input_checkbox_box"><span class="input_checkbox"><input type="checkbox"></span></span>';
		}
		if ($IsMove == 1) {
			$Html += '<a href="javascript:;" class="myorder"><i class="icon_multi_myorder"></i></a>';
		}
		if ($Module == 'products') {
			$Html += '<a href="javascript:;" class="video_edit"><i class="icon_video_edit"></i></a>';
			$Html += '<a href="javascript:;" class="alt_edit">Alt</a>';
			$Html += '<a href="javascript:;" class="video_seo_btn">SEO</a>';
			$Html += '<input type="hidden" name="Alt[]" value="">';
		}
		$Html += 		'<a href="javascript:;" class="video_edit"><i class="icon_video_edit"></i></a>';
		$Html += 		'<a href="javascript:;" class="zoom" target="_blank"><i class="icon_multi_view"></i></a>';
		$Html += 		'<a href="javascript:;" class="del" rel="del"><i class="icon_multi_delete"></i></a>';
		$Html += 	'</dd>';
		$Html += '</dl>';
		 
		return $Html;
	},

	multi_file_item: function($InputName, $Num, $IsMove, $Module) {
		$Html = '';
		$Html += '<dl class="file" num="' + $Num + '">';
		$Html += 	'<dt class="upload_box preview_file">';
		$Html += 		'<input type="button" class="btn_ok upload_btn" name="submit_button" value="{/global.upload_pic/}" tips="" />';
		$Html += 		'<input type="hidden" name="' + $InputName + '" value="" data-value="" save="0" />';
		$Html += 	'</dt>';
		$Html += 	'<dd class="file_btn">';
		if ($Module == 'products') {
			$Html += '<span class="input_checkbox_box"><span class="input_checkbox"><input type="checkbox"></span></span>';
		}
		if ($IsMove == 1) {
			$Html += '<a href="javascript:;" class="myorder"><i class="icon_multi_myorder"></i></a>';
		}
		if ($Module == 'products') {
			$Html += '<a href="javascript:;" class="video_edit"><i class="icon_video_edit"></i></a>';
			$Html += '<a href="javascript:;" class="alt_edit">Alt</a>';
			$Html += '<input type="hidden" name="Alt[]" value="">';
		}
		$Html += 		'<a href="javascript:;" class="video_edit"><i class="icon_video_edit"></i></a>';
		$Html += 		'<a href="javascript:;" class="zoom" target="_blank"><i class="icon_multi_view"></i></a>';
		$Html += 		'<a href="javascript:;" class="del" rel="del"><i class="icon_multi_delete"></i></a>';
		$Html += 	'</dd>';
		$Html += '</dl>';
		return $Html;
	},

	/**
	 * 右侧弹窗的产品选择
	 */
	fixed_right_products_choice: function ()
	{
		var fixed_obj = $('.fixed_right_products_choice');
		// 加载产品列表或翻页加入
		var load_edit_form = function(target_obj, url, type, value, callback, fuc){
			$.ajax({
				type:type,
				url:url,
				data:value,
				success:function(data){
					if(fuc=='append'){
						$(target_obj).append($(data).find(target_obj).html());
					}else{
						$(target_obj).html($(data).find(target_obj).html());
					}
					callback && callback(data);
				}
			});
		}
		// 初始化产品列表
		var products_list_init = function(type){
			var value = $('#fixed_right .search_form form').serialize();
			if (type == 'append') value += '&page=' + (parseInt($('.fixed_right_products_choice_list').attr('data-page')) + 1);
			load_edit_form('.fixed_right_products_choice_list', '/api/Discount/FixedRightProductsChoice', 'post', value, function (data) {
				$('.fixed_right_products_choice_jsppane').removeClass('loading');
				$('.fixed_right_products_choice_list').attr('data-total-pages',$(data).find('.fixed_right_products_choice_list').attr('data-total-pages')).attr('data-page',$(data).find('.fixed_right_products_choice_list').attr('data-page'));
			}, type);
			if (type != "append") fixed_obj.find("input[name=submit_button]").attr("disabled", true);
		};
		// 搜索
		fixed_obj.find('.search_form form').submit(function(){
			fixed_obj.find('.select_all_box .input_checkbox_box').removeClass('checked');
			products_list_init();
			return false;
		});
		// 点击产品
		fixed_obj.on('click', '.fixed_right_products_choice_list_item:not(".disabled")', function (event) {
			event.stopPropagation();
			var ProId = $(this).data('proid');
			if ($(this).find('.btn_checkbox').length) {
				// 多选
				$(this).toggleClass('current');
				$(this).find('.btn_checkbox').toggleClass('current');
				$(this).find('.btn_checkbox input').prop('checked') ? $(this).find('.btn_checkbox input').prop('checked', false) : $(this).find('.btn_checkbox input').prop('checked', true);
			} else if ($(this).find('.btn_radio').length) {
				// 单选
				$(this).addClass('current').find('.btn_radio').addClass('current').find('input').prop('checked', true);
				$(this).siblings().removeClass('current').find('.btn_radio').removeClass('current').find('input').prop('checked', false);
			}
			if (fixed_obj.find(".fixed_right_products_choice_list_item.current").length > 0) {
				//已有勾选产品
				fixed_obj.find("input[name=submit_button]").attr("disabled", false);
			} else {
				//没有勾选产品
				fixed_obj.find("input[name=submit_button]").attr("disabled", true);
			}
		});
		fixed_obj.on('click', '.select_all_box .input_checkbox_box', function(){
			var $this = $(this);
			if ($this.hasClass('checked')) {
				$('.fixed_right_products_choice_list .fixed_right_products_choice_list_item:not(".disabled")').removeClass('current');
				$('.fixed_right_products_choice_list .fixed_right_products_choice_list_item:not(".disabled") .btn_checkbox').removeClass('current');
				$('.fixed_right_products_choice_list .fixed_right_products_choice_list_item:not(".disabled") .btn_checkbox input').prop('checked', false);
			} else {
				$('.fixed_right_products_choice_list .fixed_right_products_choice_list_item:not(".disabled")').addClass('current');
				$('.fixed_right_products_choice_list .fixed_right_products_choice_list_item:not(".disabled") .btn_checkbox').addClass('current');
				$('.fixed_right_products_choice_list .fixed_right_products_choice_list_item:not(".disabled") .btn_checkbox input').prop('checked', true);
			}
			if (fixed_obj.find(".fixed_right_products_choice_list_item.current").length > 0) {
				//已有勾选产品
				fixed_obj.find("input[name=submit_button]").attr("disabled", false);
			} else {
				//没有勾选产品
				fixed_obj.find("input[name=submit_button]").attr("disabled", true);
			}
		});
		// 加载更多
		fixed_obj.on('click', '.fixed_right_products_choice_list_load_more', function () {
			$(this).remove();
			products_list_init('append');
		});
	},

	/**
	 * 右侧弹窗的会员选择
	 */
	 fixed_right_user_choice: function ()
	 {
		 var fixed_obj = $('.fixed_right_user_choice');
		 // 加载会员列表或翻页加入
		 var load_edit_form = function(target_obj, url, type, value, callback, fuc){
			 $.ajax({
				 type:type,
				 url:url+value,
				 success:function(data){
					 if(fuc=='append'){
						 $(target_obj).append($(data).find(target_obj).html());
					 }else{
						 $(target_obj).html($(data).find(target_obj).html());
					 }
					 callback && callback(data);
				 }
			 });
		 }
		 // 初始化会员列表
		 var user_list_init = function(type){
			 var value = '';
			 if (type == 'append') value = '&page=' + (parseInt($('.fixed_right_user_choice_list').attr('data-page')) + 1);
			 if (type != "append") fixed_obj.find("input[name=submit_button]").attr("disabled", true);
			 load_edit_form('.fixed_right_user_choice_list', '/manage/action/fixed-right-user-choice?' + $('#fixed_right .search_form form').serialize(), 'get', value, function (data) {
				 $('.fixed_right_user_choice_jsppane').removeClass('loading');
				 $('.fixed_right_user_choice_list').attr('data-total-pages',$(data).find('.fixed_right_user_choice_list').attr('data-total-pages')).attr('data-page',$(data).find('.fixed_right_user_choice_list').attr('data-page'));
				}, type);
			if (type != "append") fixed_obj.find("input[name=submit_button]").attr("disabled", false);
		 };
		 // 搜索
		 fixed_obj.find('.search_form form').submit(function(){
			 fixed_obj.find('.select_all_box .input_checkbox_box').removeClass('checked');
			 user_list_init();
			 return false;
		 });
		 // 点击产品
		 fixed_obj.on('click', '.fixed_right_user_choice_list_item:not(".disabled")', function (event) {
			 event.stopPropagation();
			 var ProId = $(this).data('proid');
			 $(this).toggleClass('current');
			 $(this).find('.btn_checkbox').toggleClass('current');
			 $(this).find('.btn_checkbox input').prop('checked') ? $(this).find('.btn_checkbox input').prop('checked', false) : $(this).find('.btn_checkbox input').prop('checked', true);
			 if (fixed_obj.find(".fixed_right_user_choice_list_item.current").length > 0) {
				 //已有勾选产品
				 fixed_obj.find("input[name=submit_button]").attr("disabled", false);
			 } else {
				 //没有勾选产品
				 fixed_obj.find("input[name=submit_button]").attr("disabled", true);
			 }
		 });
		 fixed_obj.on('click', '.select_all_box .input_checkbox_box', function(){
			 var $this = $(this);
			 if ($this.hasClass('checked')) {
				 $('.fixed_right_user_choice_list .fixed_right_user_choice_list_item:not(".disabled")').removeClass('current');
				 $('.fixed_right_user_choice_list .fixed_right_user_choice_list_item:not(".disabled") .btn_checkbox').removeClass('current');
				 $('.fixed_right_user_choice_list .fixed_right_user_choice_list_item:not(".disabled") .btn_checkbox input').prop('checked', false);
			 } else {
				 $('.fixed_right_user_choice_list .fixed_right_user_choice_list_item:not(".disabled")').addClass('current');
				 $('.fixed_right_user_choice_list .fixed_right_user_choice_list_item:not(".disabled") .btn_checkbox').addClass('current');
				 $('.fixed_right_user_choice_list .fixed_right_user_choice_list_item:not(".disabled") .btn_checkbox input').prop('checked', true);
			 }
			 if (fixed_obj.find(".fixed_right_user_choice_list_item.current").length > 0) {
				 //已有勾选产品
				 fixed_obj.find("input[name=submit_button]").attr("disabled", false);
			 } else {
				 //没有勾选产品
				 fixed_obj.find("input[name=submit_button]").attr("disabled", true);
			 }
		 });
		 // 加载更多
		 fixed_obj.on('click', '.fixed_right_user_choice_list_load_more', function () {
			 $(this).remove();
			 user_list_init('append');
		 });
	 },

	/***
	 * 切换上一个 下一个
	*/
	global_switch: function($url){
		if ($(".global_switch_box").length) {			
			$.get($url, function(data) {
				if (data.ret == 1) {
					if (data.msg.prev) {
						$(".global_switch_box .btn_back").removeClass("btn_disabled").addClass("btn_custom").attr('data-url', data.msg.prev);
					} else {
						$(".global_switch_box .btn_back").hide();
					}
					if (data.msg.next) {
						$(".global_switch_box .btn_next").removeClass("btn_disabled").addClass("btn_custom").attr('data-url', data.msg.next);
					} else {
						$(".global_switch_box .btn_next").hide();
					}
				}
			}, 'json');
		}
		$(".global_switch_box .btn_global_switch").click(function() {
			if (!$(this).hasClass("btn_disabled") && $(this).data("url")) {
				window.location = $(this).data("url");
			}
			return false;
		});
	},
	
	/***
	 * 实名制
	*/
	web_name_credential: function(){
		$('#credential .add_list li').click(function(){
			var $this = $(this);
			if($this.hasClass('cur')){
				$(this).removeClass('cur');
			}else{
				$(this).addClass('cur').siblings().removeClass('cur');
			}
		});
		$('#credential .type_next_submit').click(function(){
			let net_url = $('#credential .add_list li.cur').attr('data-url');
			if(net_url!=undefined){
				window.location.href = net_url;
			}else{
				global_obj.win_alert_auto_close(lang_obj.manage.account.check_type, 'await', 1000, '8%',0);
				return false;
			}
		})
		if($('#real_name_permit').length){
			global_obj.div_mask();
			$('#real_name_permit .go_real_btn').on('click',function(data){
				window.location.href = '/manage/account/credential-type';
			})
		}
		
		$('#credential .icon_multi_view').on('click',function(){
			let this_img = $(this).data('img');
			if(this_img!=''){
				let $img_html = '<div id="show_example">';
				$img_html += '	<div class="show_example_wrapper">';
				$img_html += '		<div class="example_close"></div>';
				$img_html += '		<div class="example_pic"><img src="" alt=""></div>';
				$img_html += '	</div>';
				$img_html += '</div>';
				$('body').prepend($img_html);
				if($('#show_example').length){
					$('#show_example .example_pic img').attr('src',this_img);
					$('#show_example .example_close').on('click',function(){
						$('#show_example').remove();
					});
				}
			}
		})
		frame_obj.mouse_click($('#HandPicPath .upload_btn, #HandPicPath .pic_btn .edit'), 'img', function($this){
			frame_obj.photo_choice_init('HandPicPath', '', 1,'');
		});
		frame_obj.mouse_click($('#IDFrontPicPath .upload_btn, #IDFrontPicPath .pic_btn .edit'), 'img', function($this){
			frame_obj.photo_choice_init('IDFrontPicPath', '', 1,'');
		});
		frame_obj.mouse_click($('#IDBackPicPath .upload_btn, #IDBackPicPath .pic_btn .edit'), 'img', function($this){
			frame_obj.photo_choice_init('IDBackPicPath', '', 1,'');
		});
		frame_obj.mouse_click($('#LicensePicPath .upload_btn, #LicensePicPath .pic_btn .edit'), 'img', function($this){
			frame_obj.photo_choice_init('LicensePicPath', '', 1,'');
		});
		frame_obj.mouse_click($('#CorporateIDFrontPicPath .upload_btn, #CorporateIDFrontPicPath .pic_btn .edit'), 'img', function($this){
			frame_obj.photo_choice_init('CorporateIDFrontPicPath', '', 1,'');
		});
		frame_obj.mouse_click($('#CorporateIDBackPicPath .upload_btn, #CorporateIDBackPicPath .pic_btn .edit'), 'img', function($this){
			frame_obj.photo_choice_init('CorporateIDBackPicPath', '', 1,'');
		});
		frame_obj.mouse_click($('#LetterPicPath .upload_btn, #LetterPicPath .pic_btn .edit'), 'img', function($this){
			frame_obj.photo_choice_init('LetterPicPath', '', 1,'');
		});

		frame_obj.fixed_right($('#credential .tips_btn'), '.user_agreement_box');

		frame_obj.submit_form_init($('#real_form'),'',function(){
			let promise_checked = $('#real_form input[name=promise]').is(":checked")
			if(!promise_checked){
				global_obj.win_alert_auto_close(lang_obj.manage.account.check_tips, 'await', 1000, '8%',0);
				return false;
			}
		},0,function(data){
			if(data.ret==1){
				window.location.href='/manage/account/credential-submit';
			}else{
				global_obj.win_alert_auto_close(data.msg, 'fail', 1000, '8%',0);
			}
		});
	},

	credential_submit: function() {
		$.post('/manage/account/realname-submit','',function(data){
			if(data.ret==1){
				$('#credential .ball-pulse').hide();
				$('#credential .success_icon').show();
				$('#credential .again_btn').addClass('hide');
				$('#credential .result_title').html(data.msg.tips.result_title);
				$('#credential .result_tips').hide();
				$('#header_real_tips').remove();
			}else{
				$('#credential .ball-pulse').hide();
				$('#credential .fail_icon').show();
				$('#credential .again_btn').removeClass('hide');
				$('#credential .result_title').html(data.msg.tips.result_title);
				$('#credential .result_tips').html(data.msg.tips.result_tips);
				$('.credential_status_list .item_3').addClass('fail_item');
			}
		},'json')
	},

	// 多功能选项按钮
	buttonChoice: function(opt) {
		// 默认事件
		let defaults = {
			InputName: "",
			loadInit: 1, // 加载默认事件
			loadColor: 1, // 加载颜色选取器事件
			onCheck: function() {},
			onEnter: function() {},
			onRemove: function() {},
			onColor: function() {},
			getColor: function() {}
		};
		opt = $.extend({}, defaults, opt || {});
		if (opt.loadInit == 1) {
			// 触发添加文本框
			$(".attr_selected").off().on("click", function(){
				let $Obj = $(this).find(".box_input"),
					$NotYet = $(this).next(".attr_not_yet"),
					$ItemLast = $(this).find(".btn_attr_choice:last"),
					$ItemLastLeft = ($ItemLast.length ? $ItemLast.offset().left : 0),
					$BoxLeft = 0, $BoxWidth = 0;
				$(this).addClass("selected_focus");
				if ($NotYet.find(".btn_attr_choice").length) {
					$NotYet.slideDown();
				}
				$Obj.show().focus();
				$BoxLeft = ($ItemLastLeft ? ($ItemLastLeft + $ItemLast.outerWidth(true)) - $(this).offset().left : 0);
				$BoxWidth = ($(this).outerWidth() - $BoxLeft - 31);
				if ($BoxWidth < 20) {
					// 小于20，自动换行
					$Obj.css({"width":$(this).outerWidth() - 41});
				} else {
					$Obj.css({"width":$BoxWidth, "position":"absolute", "bottom":8, "left":$BoxLeft});
				}
				$Obj.off().on("blur", function() {
					// 文本框失去焦点
					$Obj.val("").hide().removeAttr("style");
				}).on("keydown", function(e) {
					let $Value = $.trim($(this).val()),
						key = window.event ? e.keyCode : e.which;
					if ((key == 189 && e.shiftKey == true) || key == 219 || key == 221) {
						// 禁止输出“_[]”
						return false;
					}
					if (key == 188 && e.shiftKey == false && !$Value) {
						// 禁止只输出“,”
						return false;
					}
				}).on("keyup", function(e) {
					// 属性选项添加
					let $Value = $.trim($(this).val()),
						key = window.event ? e.keyCode : e.which;
					if (key == 13 || (key == 188 && !e.shiftKey)) {
						// 回车键 逗号
						if ($Value) {
							let $This = $(this),
								obj = $This.parents(".box_button_choice"),
								position = obj.data("position"),
								attrName = obj.children('input:hidden').val(), // 属性名称
								Stop = 0, $ValueData = [], tips = "";
							$Value = $Value.replace(/([_])/g, "").replace(/([\[])/g, "").replace(/([\]])/g, "");
							if ($Value.indexOf(',') != -1) {
								// 包含有逗号
								$ValueData = $Value.split(",");
							} else {
								$ValueData[0] = $Value;
							}
							$($ValueData).each(function(index, element) {
								element = $.trim(element);
								if (element == "") return true;
								Stop = 0;
								$This.parents(".attr_selected").find(".attr_current").each(function() {
									// 当前一行
									if ($(this).val() == element) {
										Stop = 1;
										return false;
									}
								});
								if (Stop == 1) {
									// 终止继续执行 (存在相同的选项)
									tips += (tips ? "," : "") + element;
									return true;
								}
								if (obj.length) {
									// 确保在属性选项列表里面才触发
									let result = true;
									if (opt.onCheck) {
										result = eval(opt.onCheck({
											obj: obj,
											attrName: attrName,
											option: element,
											position: position
										}));
									}
									if (result === true) {
										$Obj.prev(".select_list").append(frame_obj.buttonChoiceOption(element, opt.InputName, position, 1));
										if (opt.onEnter) {
											eval(opt.onEnter({
												obj: obj,
												attrName: attrName,
												option: element,
												position: position
											}));
										}
										// 自动触发颜色选取器
										if ($(".colpick.colpick_hex:visible").length) {
											// 先隐藏已显示的颜色选取器
											$(".colpick.colpick_hex:visible").hide();
										}
										$Obj.prev(".select_list").find(".btn_attr_choice:last em").click();
									}
								}
							});
							if (tips) {
								// 提示 存在相同的选项
								$Obj.val(tips);
								global_obj.win_alert_auto_close(lang_obj.manage.error.options_error.replace("{{options}}", tips), "fail", 1000, "8%");
							} else {
								$Obj.val("").hide().removeAttr("style");
							}
							$Obj.next(".placeholder").addClass("hide");
							$Obj.click();
						}
						if (window.event) {
							// IE
							e.returnValue = false;
						} else {
							// Firefox
							e.preventDefault();
						}
						return false;
					}
				});
			});

			// 触发删除选项
			$(".attr_selected .btn_attr_choice").off().on("click", "i", function(){
				let $this = $(this),
					$obj = $this.parents(".box_button_choice"),
					$Parent = $this.parent().parent(),
					$count = $Parent.find(".btn_attr_choice").length - 1;
				
				$Parent.find(".box_input").val("").hide().removeAttr("style");
				if ($count < 1) {
					// 没有选项，显示placeholder
					$Parent.parent().find(".placeholder").removeClass("hide");
				}
				if (opt.onRemove) {
					eval(opt.onRemove({
						obj: $obj,
						this: $this
					}));
				}
				$this.parent().remove();
			});
		}

		// 选择颜色 (类型：颜色)
		if (opt.loadColor == 1) {
			$(".box_button_choice[data-type=color] .btn_attr_choice>em").off().colpick({
				layout: "hex",
				submit: 1,
				onShow: function(el) {
					if ($(el).height() + parseInt($(el).css("top")) > $(window).height()) {
						$(el).css("top", parseInt($(el).css("top")) - $(el).height());
					}
				},
				onChange: function(hsb, hex, rgb, el) {
					$(el).css("background", "#" + hex).val("#" + hex);
					if (opt.onColor && $.isFunction(opt.onColor)) {
						eval(opt.onColor({
							hes: hex,
							el: el
						}));
					}
				},
				onSubmit: function(hsb, hex, rgb, el) {
					$(el).css("background", "#" + hex).val("#" + hex);
					$(el).colpickHide();
					if (opt.onColor && $.isFunction(opt.onColor)) {
						eval(opt.onColor({
							hes: hex,
							el: el
						}));
					}
				}
			});
		}
	},

	buttonChoiceOption: function(name, inputName, position, isCurrent) {
		let html = "";
		if (typeof name != "string") name = name.toString();
		if (inputName == "") inputName = "AttrOption";
		position = parseInt(position);
		html += '<span class="btn_attr_choice' + (isCurrent ? ' current' : '') + '">';
		html += 	'<em style="background-color:#fff;"></em>';
		html += 	'<b>' + global_obj.htmlspecialchars(name) + '</b>';
		html += 	'<input type="checkbox" name="' + inputName + '[' + position + '][]" value="' + global_obj.htmlspecialchars(name) + '" class="attr_current"' + (isCurrent ? ' checked' : '') + ' />';
		html += 	'<i></i>';
		html += '</span>';
		return html;
	},

	buttonTips: function() {
		$("#righter").on("mouseenter", ".button_tips", function() {
			let $obj = $(this),
				$position = $obj.offset(),
				$top = $position.top,
				$width = $obj.outerWidth(),
				$text = $(this).html(),
				html = "",
				$target = {},
				$arrow = {},
				$targetWidth = 0,
				$targetHeight = 0,
				$arrowWidth = 0,
				$arrowHeight = 0,
				left = 0,
				move = 4, // 动画移动的间距
				trCurrent = $obj.parents('tr').index(),
				trSize = $obj.parents('tr').siblings().length;
				
			if ($obj.hasClass('icon_more') && trSize == trCurrent) return false;  // 表格最后一行的更多提示语不显示
			html += '<div id="button_float_tips" class="button_float_tips">';
			html += 	'<div class="button_float_tips_arrow"></div>';
			html += 	'<div class="button_float_tips_content">' + $text + '</div>';
			html += '</div>';
			$("body").append(html);
			$target = $("#button_float_tips");
			$arrow = $target.find(".button_float_tips_arrow");

			$targetWidth = $target.outerWidth();
			$targetHeight = $target.outerHeight();
			$arrowWidth = $arrow.outerWidth(true);
			$arrowHeight = $arrow.outerHeight(true);
			left = $position.left - (($targetWidth - $width) / 2);
			$top = $top - $targetHeight - $arrowHeight;

			$target.css({"left":left, "top":$top});
			$target.animate({"top":$top - move, "opacity":1}, 200);
			$arrow.css("left", ($targetWidth / 2 - $arrowWidth / 2));
			
		}).on("mouseleave", ".button_tips", function() {
			$(".button_float_tips").remove();
		});
	},
	
	circle_progress_bar: function(data, fn) {
		let $obj = $("#box_circle_container"),
			$mainObj = $obj.find(".box_circle_progress");
			$cPercent = $mainObj.data("percent"),
			$degStart = 45,
			$degEnd = 225,
			a1 = 0,
			a2 = 0;
		data = $.extend({
			percent: 0,
			processingText: lang_obj.manage.global.processing_text,
			completedText: lang_obj.manage.global.update_status[1],
			completedStatus: "success",
			comfirmButton: 0,
			cancelButton: 0,
			comfirmButtonText: lang_obj.global.ok2,
			cancelButtonText: lang_obj.global.cancel,
		}, data);

		isNaN($cPercent) && ($cPercent = 0);
		isNaN(data.percent) && (data.percent = 0);
		$cPercent = parseInt($cPercent);
		data.percent = parseInt(data.percent);
		if (data.percent > 100) {
			data.percent = 100;
		}
		$obj.data("animate", 1);
		if (data.percent <= 50) {
			a1 = ($cPercent / 50) * 180;
			a2 = (data.percent / 50) * 180;
			$obj.find(".rightcircle").css("transform", "rotate(" + ($degStart + a2) + "deg)");
			$obj.find(".leftcircle").css("transform", "rotate(" + $degStart + "deg)");
		} else {
			a1 = (($cPercent - 50) / 50) * 180;
			a2 = ((data.percent - 50) / 50) * 180;
			if ($cPercent < 50) {
				$obj.find(".rightcircle").css("transform", "rotate(" + $degEnd + "deg)");
				setTimeout(function() {
					$obj.find(".leftcircle").css("transform", "rotate(" + ($degStart + a2) + "deg)");
				}, 200);
			} else {
				$obj.find(".leftcircle").css("transform", "rotate(" + ($degStart + a2) + "deg)");
			}
		}
		$mainObj.data("percent", data.percent);
		$obj.find(".circle_progress_number>span").text(data.percent);
		$obj.find(".circle_progress_text").text(data.processingText);
		if (data.percent == 0) {
			// 初始化
			$obj.find(".wrapper, .circle_progress_number").show();
			$obj.find(".circle_progress_completed").removeClass(data.completedStatus).hide();
			$obj.find(".circle_progress_text").removeClass("completed");
			$obj.find(".btn_progress_completed").remove();
		}
		if (data.percent == 100) {
			// 已完成
			$obj.data("animate", 0);
			$obj.find(".wrapper, .circle_progress_number").hide();
			$obj.find(".circle_progress_completed").addClass(data.completedStatus).show();
			$obj.find(".circle_progress_text").addClass("completed").text(data.completedText);
			let buttonHtml = "";
			if (data.comfirmButton == 1) {
				buttonHtml += '<div class="btn_global btn_confirm">' + data.comfirmButtonText + '</div>';
			}
			if (data.cancelButton == 1) {
				buttonHtml += '<div class="btn_global btn_cancel">' + data.cancelButtonText + '</div>';
			}
			$obj.append('<div class="btn_progress_completed">' + buttonHtml + '</div>');
		}
		if (typeof fn === "function") {
			// 回调
			fn();
		}
	},

	fixed_right_products_filter: function() {
		frame_obj.fixed_right_multi('.new_filter_btn','.fixed_search_filter',function(){
			// 搜索筛选
			frame_obj.filterRight({
				"onSubmit": function($obj) {
					// 分类
					let _obj = $('#fixed_right_multi_1 .fixed_search_filter'),
						cateId = _obj.find("input[name=FilterCateId]").val(),
						$width = $('.fixed_search_filter').attr('data-width');
					// 标签
					let tagId = [];
					_obj.find("input[name=\"products_tagsCurrent[]\"]").each(function(index, element) {
						tagId[index] = $(element).val();
					});
					tagId = tagId.join(",");
					if($('.search_menu input[name=a]').val() != 'product_top'){
						$('.search_menu input[name=FilterCateId]').val(cateId);
					}
					$('.search_menu input[name=FilterTagId]').val(tagId);

					//显示筛选条件
					let selectCateIdName = _obj.find("input[name=FilterCateId]").parents('.filter_option_input').find('input[name=Select]').val(),
						selectTagAry = _obj.find('.btn_attr_choice.current'),
						selectTagName = '';
					selectTagAry.each(function(index, element){
						let tageName = $(element).find('b').html();
						selectTagName+=tageName+',';
					})
					selectTagName = selectTagName.substring(0, selectTagName.lastIndexOf(','));
					let _searchBox = '';
					_searchBox += '<div class="search_box_selected">';
					if(selectCateIdName){
						_searchBox += '<span class="btn_item_choice current" data-name="FilterCateId"><b>'+lang_obj.manage.sales.coupon.products_category+':'+selectCateIdName+'</b><i></i></span>';
					}
					if(selectTagName){
						_searchBox += '<span class="btn_item_choice current" data-name="FilterTagId"><b>'+lang_obj.manage.sales.coupon.tags+':'+selectTagName+'</b><i></i></span>';
					}
					_searchBox += '</div>';
					if(selectCateIdName || selectTagName){
						$('.search_box_selected').remove();
						$('#fixed_right_products_choice_form').before(_searchBox);
						$(".fixed_right_products_choice .search_menu input[type=submit]").trigger("click");
					}
					$('#fixed_right_multi_1').css('right', -$width);
					setTimeout(function(){$('#fixed_right_multi_1').remove();}, 500);
					
					//删除筛选选项
					if ($(".fixed_right_products_choice .search_box_selected").length) {
						$(".fixed_right_products_choice .search_box_selected .btn_item_choice").on("click", function () {
							let $name = $(this).attr("data-name");
							nameAry = $name.split("|");
							$(nameAry).each(function(index, element) {
								$(".fixed_right_products_choice .search_menu input[name=\"" + element + "\"]").val("");
							});
							$(".fixed_right_products_choice .search_menu input[type=submit]").trigger("click");
							$(this).remove();
							if(!$(".fixed_right_products_choice .search_box_selected .btn_item_choice").length){
								$(".fixed_right_products_choice .search_box_selected").remove();
							}
						});
					}
				}
			});
		});
	},

	fixed_right_user_filter: function() {
		frame_obj.fixed_right_multi('.new_filter_btn','.fixed_search_filter',function(){
			// 搜索筛选
			frame_obj.filterRight({
				"onInit": function() {
					//	点击按钮
					$('#fixed_right_multi_1 .fixed_search_filter').on('click','.box_filter .filter_list .input_radio_box',function(){
						let $obj = $(this).parent();
						$obj.siblings().find('.input_radio_box').removeClass('checked').find('input').prop('checked',false);
						$obj.find('.input_radio_box').addClass('checked').find('input').prop('checked',true);
					})
					// 注册时间
					$('#fixed_right_multi_1 .fixed_search_filter input[name=RegTime]').daterangepicker({showDropdowns:true});
				},
				"onSubmit": function($obj) {
					// 分类
					let _obj = $('#fixed_right_multi_1 .fixed_search_filter'),
						$width = $('.fixed_search_filter').attr('data-width');

					let _searchBox = '';
					_searchBox += '<div class="search_box_selected">';
					//显示筛选条件
					$status = 0;

					//1. 订阅状态
					fileterIsNewsletter = _obj.find('input[name=FilterIsNewsletter]:checked').val();
					$('.search_menu input[name=FilterIsNewsletter]').val(fileterIsNewsletter);
					if(fileterIsNewsletter != '' && fileterIsNewsletter != undefined) {
						_searchBox += '<span class="btn_item_choice current" data-name="FilterIsNewsletter"><b>'+lang_obj.manage.user.newsletter.newsletter_status+':'+lang_obj.manage.user.newsletter[fileterIsNewsletter]+'</b><i></i></span>';
						$status = 1;
					}

					//2. 免税状态
					filterIsTaxExempt = _obj.find('input[name=FilterIsTaxExempt]:checked').val();
					$('.search_menu input[name=FilterIsTaxExempt]').val(filterIsTaxExempt);
					if(filterIsTaxExempt != '' && filterIsTaxExempt != undefined) {
						_searchBox += '<span class="btn_item_choice current" data-name="FilterIsTaxExempt"><b>'+lang_obj.manage.user.tax_exempt.newsletter_status+':'+lang_obj.manage.user.tax_exempt[filterIsTaxExempt]+'</b><i></i></span>';
						$status = 1;
					}

					//3. 会员标签
					let tagId = [];
					_obj.find("input[name=\"user_tagsCurrent[]\"]").each(function(index, element) {
						if(!$(element).val()) return true;
						tagId[index] = $(element).val();
					});
					tagValueId = tagId.join("|");
					tagId = tagId.join(",");
					if(tagId){
						$('.search_menu input[name=FilterTagId]').val(tagValueId);
						_searchBox += '<span class="btn_item_choice current" data-name="FilterTagId"><b>'+lang_obj.manage.user.tags.title+':'+tagId+'</b><i></i></span>';
						$status = 1;
					}

					//4. 购买次数
					OrderNumber = [];
					_obj.find("input[name=\"OrderNumber[]\"]").each(function(index, element) {
						if(!$(element).val()) return true;
						OrderNumber[index] = $(element).val();
					});
					if(OrderNumber.length > 0) {
						OrderNumber = OrderNumber.join("~");
						$('.search_menu input[name=FilterOrderNumber]').val(OrderNumber);
						_searchBox += '<span class="btn_item_choice current" data-name="FilterOrderNumber"><b>'+lang_obj.manage.user.consumption_times.title+':'+ OrderNumber +'</b><i></i></span>';
						$status = 1;
					}

					//5.累计金额
					OrderPrice = [];
					_obj.find("input[name=\"OrderPrice[]\"]").each(function(index, element) {
						if(!$(element).val()) return true;
						OrderPrice[index] = $(element).val();
					});
					if(OrderPrice.length > 0) {
						OrderPrice = OrderPrice.join("~");
						$('.search_menu input[name=FilterOrderPrice]').val(OrderPrice);
						_searchBox += '<span class="btn_item_choice current" data-name="FilterOrderPrice"><b>'+lang_obj.manage.user.consumption_price.title+':'+ OrderNumber +'</b><i></i></span>';
						$status = 1;
					}

					//6. 加入时间
					filterRegTime = _obj.find('input[name=RegTime]').val();
					$('.search_menu input[name=FilterRegTime]').val(filterRegTime);
					if(filterRegTime != '' && filterRegTime != undefined) {
						_searchBox += '<span class="btn_item_choice current" data-name="FilterRegTime"><b>'+lang_obj.manage.user.reg_time.title+':'+filterRegTime+'</b><i></i></span>';
						$status = 1;
					}
					
					_searchBox += '</div>';
					if($status){
						$('.search_box_selected').remove();
						$('#fixed_right_user_choice_form').before(_searchBox);
						$(".fixed_right_user_choice .search_menu input[type=submit]").trigger("click");
					}
					$('#fixed_right_multi_1').css('right', -$width);
					setTimeout(function(){$('#fixed_right_multi_1').remove();}, 500);
					
					//删除筛选选项
					if ($(".fixed_right_user_choice .search_box_selected").length) {
						$(".fixed_right_user_choice .search_box_selected .btn_item_choice").on("click", function () {
							let $name = $(this).attr("data-name");
							nameAry = $name.split("|");
							$(nameAry).each(function(index, element) {
								$(".fixed_right_user_choice .search_menu input[name=\"" + element + "\"]").val("");
							});
							$(".fixed_right_user_choice .search_menu input[type=submit]").trigger("click");
							$(this).remove();
							if(!$(".fixed_right_user_choice .search_box_selected .btn_item_choice").length){
								$(".fixed_right_user_choice .search_box_selected").remove();
							}
						});
					}
				}
			});
		});
	},

	formMigration: function ($from, $to) {
		//把表单的html从一个容器迁移到另一个容器时保证 select input 数据不丢失
		$from.find('input').each(function(){
			$(this).attr('value', $(this).val());
		});
		$from.find('select').each(function(){
			$(this).find('option').not(':selected').removeAttr('selected');
			$(this).find('option:selected').attr('selected', 'selected');
		});
		$to.html($from.html());
		$to.find('select option[selected=selected]').removeAttr('selected').prop('selected', true);
	},

	btnCheckbox: function () {
		let obj = arguments.length === 0 ? $('.btn_checkbox, .btn_choice') : arguments[0];
		obj.on('click', function () {
			var $this = $(this),
				$obj = $this.find('input');
			if ($this.hasClass('disabled')) return false; // 禁止调用
			if ($obj.is(':checked')) { // 已勾选
				$obj.prop('checked', false);
				$this.removeClass('current');
			} else { // 未勾选
				$obj.prop('checked', true);
				$this.addClass('current');
			}
		});
	},

	offBtnCheckbox: function () {
		let obj=(typeof(arguments[0])=='undefined')?$('.btn_checkbox, .btn_choice'):arguments[0]
		obj.off();
	},

	boxPaymentSystem: function ($btn, $box, inCallback, outCallback) {
		$('#main').on('click', $btn, function(){
			$($box).fadeIn()
			if (typeof(inCallback) == 'function') eval(inCallback)
		}).on('click', $box + ' .close', function(){
			$($box).fadeOut()
			if (typeof(outCallback) == 'function') eval(outCallback)
		});
	},


	/**
	 * 单个图片初始化上传按钮
	 */
	 show_btn_init: function(){
		let isImg = $('.upload_file_box').prev('.multi_img').find('img').length;
		if (isImg) {
			$('.upload_file_box').hide();
		} else {
			$('.upload_file_box').show();
		}
	},

	/**
	 * 单个图片上传事件
	 */
	main_picture_upload: function() {
		frame_obj.mouse_click($('.upload_menu li[data-type=gallery]'), 'pro', function() {
			let $num = $('.global_upload_box .img').attr('num');
			frame_obj.photo_choice_init('PicDetail .img[num;'+$num+']', 'global', 1, '', 1, "frame_obj.upload_pro_img_init(1, '', 1);frame_obj.show_btn_init();");
		});
		// 图片删除点击事件
		frame_obj.mouse_click($('.global_upload_box .pic_btn .del'), 'proDel', function($this){
			var $obj=$this.parents('.img'),
				$obj_par=$obj.parent();
			$obj.remove();
			let html = frame_obj.multi_img_item("PicPath", 0, 0);
			$('#PicDetail').append(html);
			frame_obj.upload_pro_img_init(1,'.global_upload_box');
			var $i=0;
			$obj_par.find('.img').each(function() {
				$(this).attr('num', $i);
				++$i;
			});
			if ($(".view_more_image").length) $(".view_more_image").click();
			frame_obj.show_btn_init();
		});
		frame_obj.upload_pro_img_init(1, '.global_upload_box');
	},
	// 图片加入
	put_img: function(imgpath){
		if (!imgpath) return;
		let picNum = $('.global_upload_box .img').length - 1;
		let obj = $('#PicDetail').find('.img[num=' + picNum + ']');
		if (obj.find('input[type=hidden]').attr('save') == 0 && !obj.hasClass('video')) {
			obj.find('.pic_btn .zoom').attr('href',imgpath);
			obj.find('.preview_pic').append(frame_obj.upload_img_detail(imgpath)).children('.upload_btn').hide().parent().parent().addClass('isfile').removeClass('show_btn');
			obj.find('.preview_pic').children('input[type=hidden]').val(imgpath).attr('save', 1);
		}
		frame_obj.upload_pro_img_init(1, '',1);
		frame_obj.main_picture_upload();
		frame_obj.show_btn_init();
	},

	upload_picture_event: function() {
		$('.multi_img input[name=PicPath').each(function(){
			if($(this).attr('save')==1){
				$(this).parent().find('img').attr('src', $(this).attr('data-value')); //直接替换成缩略图
			}
		});
		// 上传按钮
		$('.global_upload_box').on('click', '.img .upload_box .upload_btn', function(){
			$('.upload_file_box input[type=file]').trigger('click');
		});
		// 拖动上传
		$('.upload_file_box').on('fileuploadstart', function(){
			$(this).append('<div class="loading"></div>').removeClass('dragenter');
			$('.show_btn').append('<div class="loading"></div>');
		});
		$('.upload_file_box').on('fileuploadstop', function(){
			$(this).children('.loading').remove();
		});
		$('.upload_file_box, #PicDetail').on('dragenter', function(){
			$('.upload_file_box').addClass('dragenter');
		});
		$('.upload_file_box').on('dragleave', function(){
			$(this).removeClass('dragenter');
		});
		$(window).on('dragend', function(e){
			e.preventDefault();
			$('.upload_file_box').trigger('dragleave');
		});
		// 图片上传
		$('.upload_file_box').fileupload({
			url: '/manage/action/file-upload?size=global',
			acceptFileTypes: /^image\/(gif|jpe?g|png|webp|x-icon|jp2)$/i,
			disableImageResize:false,
			imageMaxWidth: 99999,
			imageMaxHeight: 99999,
			imageForceResize:false,
			maxFileSize: 10485760,
			maxHeight: 5000,
			messages: {
				maxFileSize: lang_obj.manage.photo.size_limit,
				acceptFileTypes: lang_obj.manage.photo.type_error,
				maxHeight: lang_obj.manage.photo.height_limit
			},
			dropZone: $('.upload_file_box'),
			done: function(event, data){
				if (typeof data.result == 'undefined') return;
				$('#PicDetail').find(".loading").remove();

				// 处理不同的API返回格式
				let imgpath = null;

				// 格式1: files数组格式 (原有格式)
				if (data.result.files && data.result.files.length > 0) {
					imgpath = data.result.files[0].url;
				}
				// 格式2: filePath格式 (新的本地上传API格式)
				else if (data.result.filePath) {
					imgpath = data.result.filePath;
				}

				console.log('图片上传完成，路径:', imgpath);

				if (imgpath) {
					if ($(".view_more_image").length) $(".view_more_image").click();
					frame_obj.put_img(imgpath);
				} else {
					console.error('无法从上传响应中获取图片路径:', data.result);
				}
			},
			processfail: function(e, data) {
				$('.upload_file_box').trigger('dragleave');
				let files = data.files;
				let error = "";
				$.each(files, function(index, element) {
					if (typeof element.error !== "undefined") {
						error += element.error;
					}
				});
				error == "" && (error = lang_obj.manage.photo.type_error);
				global_obj.win_alert_auto_close(error, 'await', 1000, '8%', 0);
			}
		}).on('fileuploadadd', function (e, data) {
			$.fn.fileUploadAdd(data);
		});
	},

	importMonitoring: function (url, fromData, time, notify = false) {
		// 请求地址必填
		if (!url) return false;

		if (!fromData) fromData = [];

		// 默认设置为5s
		if (time === undefined) time = 5000;

		let stop = setInterval(function () {
			$.ajax({
				url: url,
				type: 'POST',
				dataType: 'json',
				contentType: 'application/json',
				data: JSON.stringify(fromData),
				success: function(result) {
					if ($('.upload_products_box').length) {
						$.each(result.msg, function (index, data) {
							if (data.ret == 1) {
								frame_obj.circle_progress_bar({
									percent: 100
								});
								
								$('.upload_products_box .close').trigger('click');

								// 已完成
								global_obj.win_alert({
									"title": data.msg.title,
									"subtitle":data.msg.subtitle,
									"confirmBtn":data.msg.confirmBtn,
									"confirmBtnClass":data.msg.confirmBtnClass
								}, () => {
									if (data.msg.confirmBtnClass) {
										window.open('/manage/products/products?uploadId=' + data.msg.id)
										return false
									}
									window.location.reload()
								}, undefined, undefined, undefined, () => {
									window.location.reload()
								});

								clearInterval(stop)
							} else if (!notify) {
								if (data.ret == 2) {
									// 未完成
									frame_obj.circle_progress_bar({
										percent: data.msg.percent,
										processingText: lang_obj.manage.products.upload.import_pending
									});
								} else {
									// 错误提示
									global_obj.win_alert(data.msg);
									clearInterval(stop)
								}
							}
						})
					}
				}
			})
		}, time);
	},

	/** 意见反馈 start **/
	manageFeedbackEvent: function () {
		frame_obj.pop_up($('#header .manage_feedback_btn'), '.pop_manage_feedback',1,function(){
			var $Obj=$('.pop_manage_feedback');
			$Obj.find('.global_form').css('height', $Obj.outerHeight()-$Obj.find('.message_title').outerHeight());
		});

		//图片上传
		frame_obj.upload_pro_img_init(1,'#FeedbackPicDetail');
		frame_obj.mouse_click($('.feedback_multi_img .img .upload_btn,.feedback_multi_img .pic_btn .edit'), 'img', function($this){
			var $num=$this.parents('.img').attr('num');
			frame_obj.photo_choice_init('FeedbackPicDetail .img[num='+$num+']', 'feedback', 4, '', 1, "frame_obj.upload_pro_img_init(1, '#FeedbackPicDetail', 1);frame_obj.feedbackImgNumShow()");
		});
		frame_obj.dragsort($('.feedback_multi_img'), '', 'dl', '.show_btn, a[class!=myorder]', '<dl class="img placeHolder"></dl>'); //图片拖动
		frame_obj.mouse_click($('.feedback_multi_img .pic_btn .del'), 'proDel', function($this){ //产品主图删除点击事件
			var $obj=$this.parents('.img'),
				$num=parseInt($obj.attr('num')),
				$path=$obj.find('input[name=PicPath\\[\\]]').val();
			$obj.removeClass('isfile').removeClass('show_btn');
			$obj.parent().append($obj);
			$obj.find('.preview_pic .upload_btn').show();
			$obj.find('.preview_pic a').remove();
			$obj.find('.preview_pic input:hidden').val('').attr('save', 0);
			frame_obj.upload_pro_img_init(1);
			frame_obj.feedbackImgNumShow()
			var $i=0;
			$obj.parent().find('.img').each(function(){
				$(this).attr('num', $i);
				++$i;
			});
		});
		frame_obj.submit_form_init($('#manage_feedback_form'), location.href);
	},

	feedbackImgNumShow: function () {
		let _from = $('#manage_feedback_form');
		let _showImgSize = parseInt($('.feedback_multi_img').find('.img.isfile').length);
		_from.find('.feedback_img_box label span').html(_showImgSize)
	},
	/** 意见反馈 end **/

	/**
	 * Tinymce 编辑器
	 * 
	 * @param string name 编辑器id
	 * @param object config 编辑器扩展配置
	 */
	tinymceEditorInit: function(name, config)
	{
		var TinyMceGetStatsLost = function(inst){
			var str = inst.getBody().innerHTML;
			var imgReg = /<img.*?(?:>|\/>)/gi;
			//匹配src属性
			var srcReg = /src=[\'\"]?([^\'\"]*)[\'\"]?/i;
			var arr = str.match(imgReg);
			if(arr){
				for (var i = 0; i < arr.length; i++) {
					var src = arr[i].match(srcReg);
					//获取图片地址
					if(src[1].indexOf("data:image/png;base64") != -1){
						var rep_str = src[1];
						$.post('./?do_action=action.editor_img_upload', 'html='+src[1], function(data){
							if (data.ret == 1) {
								str = str.replace(rep_str, data.msg);
								inst.setContent(str);//更新编辑器内容
							}
						}, 'json');
					}else if(src[1].indexOf("file:///") != -1){
						var rep_str = src[1];
						str = str.replace(rep_str, '');
						inst.setContent(str);//更新编辑器内容
					}
				}
			}
		}	
		var demoBaseConfig = {
			selector: `textarea#${name}`,
			contextmenu: 'image editimage link linkchecker lists configurepermanentpen spellchecker table',
			ui_container: '#edit_form',
			language: shop_config.manage_language == 'zh-cn' ? 'zh_CN' : 'EN',
			width: '100%',
			max_width: '100%',
			min_height: 500,
			max_height: 500,
			font_formats: 'Arial=Arial;Arial Black=arial black,Arial-Black,Arial;Andale Mono=andale mono,Arial;Comic Sans Ms=comic sans ms,Arial;Times New Roman=Times New Roman,Arial;Cambria=Cambria,Arial;Calibri=Calibri,Arial;Courier New=Courier New,Arial;Verdana=Verdana,Arial;Georgia=Georgia,Arial;Tahoma=Tahoma,Arial;Times=Times,Arial;serif=serif,Arial;黑体=黑体,Arial;隶书=隶书,Arial;宋体=宋体,Arial;新宋体=新宋体,Arial;微软雅黑=微软雅黑,Arial;楷体_GB2312=楷体_GB2312,Arial',
			fontsize_formats: '12px 14px 15px 16px 18px 20px 22px 24px 28px 36px 48px 60px 72px',
			lineheight_formats: '1 1.5 1.8 2 2.5 3 3.5 4',
			resize: true, // 禁止改变大小
			cleanup: false,
			verify_html: false,
			autosave_ask_before_unload: false,
			powerpaste_allow_local_images: false,
			menubar: false, // 隐藏菜单栏
			plugins: `print preview searchreplace autolink directionality visualblocks visualchars fullscreen localimg_${name} image link anchor media code table advlist lists textpattern autosave autoresize paste`,
			toolbar: `code undo redo | fullscreen | table localimg_${name} media| formatselect fontselect fontsizeselect lineheight | forecolor backcolor bold italic underline strikethrough link anchor removeformat | bullist numlist | alignleft aligncenter alignright alignjustify outdent indent  | cut copy paste pastetext`,
			toolbar_mode: 'wrap',
			spellchecker_dialog: false,
			tinydrive_token_provider: function (success, failure) {
			success({ token: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**********************************************************************.Ks_BdfH4CWilyzLNk8S2gDARFhuxIauLa8PwhdEQhEo.iokrj7v4cosyfzvm9znfr6x3r8oldn90cbbftr2iebdxtcy8' });
			},
			content_css : ['/assets/js/plugin/tinymce/js/tinymce/contents.css'],
			content_css_cors: true,
			// paste_webkit_styles: true,
			forced_root_block: '',
			elements: 'name,style',
			valid_elements: '*[*]',
			extended_valid_elements: '*[*]',
			valid_children : '+body[link|script|style|strong]', // 此选项使您能够控制指定父元素中可以存在哪些子元素
			content_style: 'img{max-width:100%;max-height:100%;vertical-align: middle;}',
			branding: false, // 技术支持
			allow_script_urls: true, // 此选项将允许javascript: urls链接和图像
			// object_resizing: true, // 所有图像/表格大小调整
			setup: function (ed) {
				ed.on("change", function () {
					// TinyMceGetStatsLost(ed);
					ed.save();
				})
			},
			convert_urls: false
			//elementpath: false,//底栏的元素路径
		};
		demoBaseConfig = $.extend(demoBaseConfig, config)
		tinymce.PluginManager.add(`localimg_${name}`, function(editor, url) {
			var openDialog = function () {
				frame_obj.photo_choice_init(name, 'editor', 9999, '', 1);
			};
			/* Add a button that opens a window */
			editor.ui.registry.addButton(`localimg_${name}`, {
				icon: 'image',
				onAction: function (e) {
					/* Open window */
					openDialog();
				}
			});
			/* Adds a menu item, which can then be included in any menu via the menu/menubar configuration */
			editor.ui.registry.addMenuItem(`localimg_${name}`, {
				text: 'Images plugin',
				onAction: function() {
					/* Open window */
					openDialog();
				}
			});
			/* Return the metadata for the help plugin */
			return {
				getMetadata: function () {
					return  {
						name: 'Images plugin',
						url: 'http://exampleplugindocsurl.com'
					};
				}
			};
		});
		tinymce.init(demoBaseConfig);
	},

	/**
	 * 清除HTML格式的文本内容
	 * 
	 * @param string content 
	 * @param integer max 
	 * @returns string content
	 * <AUTHOR> <<EMAIL>>
	 */
	clear_html_content: (content, max) => {
		content = $.trim(content)
		if (content) {
			let msg = content.replace(new RegExp("<.+?>|&.+?;", "g"), ' ') // 匹配html标签的正则表达式，"g"是搜索匹配多个符合的内容
			msg = msg.replace(/\s\s/g, '') // 去掉所有重复的空格（中文空格、英文空格都会被替换）
			msg = msg.replace(/[\r\n]/g, '') // 去掉所有的换行符
			msg = $.trim(msg) // 去掉内容前后的空格
			content = msg.substr(0, max)
		}
		return content
	},

	/**
	 * 图片alt修改
	 */
	imageAltDdit: function() {
	 
		frame_obj.imageAltFixedRight()
		$('#alt_edit_form .btn_submit').click(function(){
			let Form = $('#alt_edit_form'),
				boxId = Form.find('input[name=boxId]').val(),
				Alt = Form.find('textarea[name=Alt]').val(),
				Num = Form.find('input[name=Num]').val()
				$('#' + boxId).find('.img[num='+ Num +'] .pic_btn').find('input[type="hidden"][name^=Alt]').val(Alt)
				$('#fixed_right .btn_cancel').click()
				return false
		})
	},
	imageAltFixedRight: function() {
		 
		frame_obj.fixed_right($('.alt_edit'), '.fixed_edit_alt', function($this){
			$('#fixed_right').addClass('loading')
			let boxId = $this.parents('.multi_img').attr('id'),
				Alt = $this.parent().find('input[name^=Alt]').val(),
				Num = $this.parents('.img').attr('num'),
				Form = $('#alt_edit_form')
			Form.find('textarea[name=Alt]').val(Alt)
			Form.find('input[name=Num]').val(Num)
			Form.find('input[name=boxId]').val(boxId)
			$('#fixed_right').removeClass('loading')
		})
	},

	//加载动效
	loader: function(container, option) {
		option = option || {}
		const el = $(container)
		const loader = $(``)
		//const loader = $(`
		//<div class="loader">
		//	<div class="inner">
		//		<div class="loading"></div>
		//		<div class="text"></div>
		//	</div>
		//</div>
		//`)

		let txt = option.text || ''
		if (txt.length > 0) loader.find('.text').text(txt)
		return {
			el,
			loader,
			on: function(fn) {
				el.css('position', 'relative')
				loader.width(el.outerWidth())
				loader.height(el.outerHeight())
				loader.css({position: 'absolute', top: 0, left: 0})
				el.append(loader)
				if(typeof fn === 'function') fn() //回调
			},
			off: function(fn) {
				if (el.find('.loader').length>0) loader.remove()
				if(typeof fn === 'function') fn() //回调
			}
		}
	},

	global_list_sort_list: function(module, action){
		const $obj = $('#' + module)
			MODULE_LIMIT = 50,
			CURRENT_PAGE = $('#turn_page').attr('data-current'),
			MAX_PAGE = $('#turn_page').data('count');
		if (CURRENT_PAGE == 0) $obj.find('.move_prev_top').addClass('disabled')
		if (CURRENT_PAGE + 1 >= MAX_PAGE) $obj.find('.move_next_top').addClass('disabled')

		let init = function(){
			frame_obj.dragsort($obj.find('.box_table tbody'), '', 'tr .icon_myorder', '', '<div class="tr"></div>', '', function(){
				let $page = parseInt($('#turn_page').data('current'))
				let count = $page * CURRENT_PAGE
				$obj.find('tbody input[name^=select]').each(function (index, element) {
					SortList[(count + index)] = $(this).val()
				})
				order();
			})
			$obj.on('click', '.move_next_top, .move_prev_top', function(){
				if(!$(this).hasClass('disabled')) {
					let $page = parseInt($('#turn_page').attr('data-current'))
					let $select = $obj.find('input[name=select]');
					let $moveList = $select.map(function(){
						if($(this).is(':checked')) return $(this).val()
					}).get();
					let $moveListIndex = $select.map(function () {
						if ($(this).is(':checked')) {
							$index = $page * MODULE_LIMIT + $(this).index('input[name=select]')
							return $index
						}
					}).get().reverse();
					$.each($moveListIndex, function($k, $v){
						SortList.splice($v, 1)
					})
					if ($(this).hasClass('move_next_top')) $startIndex = ($page + 1) * MODULE_LIMIT
					if ($(this).hasClass('move_prev_top')) $startIndex = ($page - 1) * MODULE_LIMIT
					$.each($moveList, function ($k, $v) {
						SortList.splice($k + $startIndex, 0, $v)
					})
					
					order(1);
				}
			}).on('click', '.p_top', function(){
				let $obj = $(this).parents('tr')
					let $page = parseInt($('#turn_page').data('current'))
					let $index = $page * MODULE_LIMIT + $obj.index()
					SortList.splice($index, 1)
					SortList.unshift($(this).data('id'))
					order(1);
			})
		},
		order = function($isReloadPage = 0){
			let where = SortList
				data = {
					where: where,
					pageSize: MODULE_LIMIT,
					module: module,
					action: action
				}
			$.post('/manage/action/get-module-sort', data, function(result){
				if(result.ret == 1) {
					$isReloadPage == 1 && window.location.reload();
				}
			}, 'json')
		}

		init();
	},

	global_category_view_sort: function(module, action){

		const INSTANCE = {
			render: {
				form: $('#edit_form'),
				inside: $('#category_inside'),
				moduleBox: $('.category_module_box'),
				module: $('.module_box')
			}
		}
		const MODULE_LIMIT = 20
		var categoryData = {
			module: {manual: [], intelligent: [], fromSubcategory: []},
			addMethod: ''
		}
		var moduleListObj = {}
		
		let getModulesList = function(){
			let $this = $('.my_order_box .drop_down .item .current')
			let $page = parseInt(moduleListObj.attr('data-page'))
			let $tbodyObj = moduleListObj.find('tbody')
			let $cateid = parseInt($('input[name="CateId"]').val())
			let $order = $this.attr('data-order')
			let $where = moduleList.slice($page * MODULE_LIMIT, ($page + 1) * MODULE_LIMIT)
			let $maxPage = Math.ceil(parseInt(moduleList.length) / MODULE_LIMIT)
			if ($page >= $maxPage) {
				// 删除数据时 如果最后一页没有数据了 往前推一页
				$page = $maxPage - 1
				if ($page < 0 ) $page = 0
				$where = moduleList.slice($page * MODULE_LIMIT, ($page + 1) * MODULE_LIMIT)
				moduleListObj.attr('data-page', $page)
			}

			if ($order == 'custom_sort') {
				// 手动排序的时候显示拖动图标
				moduleListObj.addClass('custom_sort');
			} else {
				moduleListObj.removeClass('custom_sort');
			}
			moduleListObj.find('.table_menu_button .open>span').text(0)

			/* moduleListObj.find('table').hide();
			moduleListObj.find('.cate_module_page').hide();
			moduleListObj.find('.no_data').hide();
			moduleListObj.find('.module_to_more').show(); */

			$.post('/manage/action/get-module-list?page=' + ($page + 1), {
					id: $cateid,
					order: $order,
					where: $where,
					totalCount: moduleList.length,
					pageSize: MODULE_LIMIT,
					module: module,
					action: 'index'
				},
				function(data){
					$tbodyObj.html(data.msg.html);
					afterLoadHtml(moduleListObj, data)
					moduleListObj.removeAttr('disabled')
					if (moduleListObj.find('.item').length) {
						INSTANCE.render.moduleBox.find('.box_drop_down_menu').show()
						moduleListObj.find('.r_con_table').show()
						moduleListObj.find('.no_data').hide()
					} else {
						INSTANCE.render.moduleBox.find('.box_drop_down_menu').hide()
						moduleListObj.find('.r_con_table').hide()
						moduleListObj.find('.no_data').show()
					}
					//moduleListObj.find('.module_to_more').hide().parent().find('.cate_module_page').show();
				}, 
				'json'
			)
		},
		getSortList = function($this){
			let $cateid = parseInt($('input[name="CateId"]').val())
			let $tbodyObj = moduleListObj.find('tbody')
			let $order = $this.attr('data-order')
			let $page = 0
			if ($('input[name="OrderType"]').val() == $order && !$this.hasClass('addpro')) return false
			if ($this.hasClass('addpro')) {
				// 添加数据时，保持当前页码不变
				$page = parseInt(moduleListObj.attr('data-page'))
				$this.removeClass('addpro')
			}
			$('input[name="OrderType"]').val($order)
			moduleListObj.attr('data-order', $order)
			$('.my_order_box .order_type').text($this.text())
			$('.my_order_box .drop_down .item a').removeClass('current')
			$this.addClass('current')
			if ($order == 'custom_sort') {
				// 手动排序的时候显示拖动图标
				moduleListObj.addClass('custom_sort')
				return false
			} else {
				moduleListObj.removeClass('custom_sort')
			}
			if ($this.attr('disabled') == 'disabled') return false
			$where = moduleList
			$this.attr('disabled', 'disabled')

			/* moduleListObj.find('table').hide();
			moduleListObj.find('.cate_module_page').hide();
			moduleListObj.find('.no_data').hide();
			moduleListObj.find('.module_to_more').show(); */

			$.post('/manage/action/get-module-list?page=' + ($page + 1), {
					'id': $cateid,
					'order': $order,
					'where': $where,
					'totalCount': moduleList.length,
					'pageSize': MODULE_LIMIT,
					'module': module,
					'action': 'index'
				}, function(data){
					$tbodyObj.html(data.msg.html)
					afterLoadHtml(moduleListObj, data)
					moduleList = data.msg.moduleList
					moduleListObj.attr('data-page', $page)
					if (moduleListObj.find('.item').length) {
						INSTANCE.render.moduleBox.find('.box_drop_down_menu').show()
						moduleListObj.find('.r_con_table').show()
						moduleListObj.find('.no_data').hide()
					} else {
						INSTANCE.render.moduleBox.find('.box_drop_down_menu').hide()
						moduleListObj.find('.r_con_table').hide()
						moduleListObj.find('.no_data').show()
					}
					//moduleListObj.find('.module_to_more').hide().parent().find('.cate_module_page').show();
					$this.removeAttr('disabled')
				}, 
				'json'
			)
		},
		afterLoadHtml = function(listObj, data){
			listObj.find('thead tr').removeClass('current').find('.btn_checkbox').removeClass('current indeterminate').find('input:checkbox').prop('checked', false)
			listObj.find('thead tr .open').attr('class', 'open')
			frame_obj.offBtnCheckbox()
			frame_obj.btnCheckbox()
			frame_obj.select_all($('input[name=select_all]'), $('input[name=select]'))
			listObj.find('tbody').dragsort('destroy')
			frame_obj.dragsort(listObj.find('tbody'), '', '.item .order_move', '', '<tr class="item placeHolder"><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr>', '', function () {
				let $page = parseInt(listObj.attr('data-page'))
				let count = $page * MODULE_LIMIT
				listObj.find('input[name^=moduleId]').each(function (index, element) {
					moduleList[(count + index)] = $(this).val()
				})
				submitDataAction();
			})

			// 排序移动到上下页顶部
			listObj.find('.move_next_top, .move_prev_top').removeClass('disabled')
			let $page = parseInt(listObj.attr('data-page'))
			let $maxPage = Math.ceil(parseInt(moduleList.length) / MODULE_LIMIT)
			if ($page == 0) listObj.find('.move_prev_top').addClass('disabled')
			if ($page + 1 >= $maxPage) listObj.find('.move_next_top').addClass('disabled')

			let pageHtml = ''
			if (data.msg.moduleList.length > 0) pageHtml = data.msg.turn_page
			listObj.find('.cate_module_page').html(pageHtml)
			submitDataAction();
		},
		submitDataAction = function(){
			moduleListStr = moduleList.join(',');
			if($('#edit_form').find('input[name=moduleIdStr]').length > 0) {
				$('#edit_form').find('input[name=moduleIdStr]').val(moduleListStr);
			} else {
				$('#edit_form').append('<input type="hidden" name="moduleIdStr" value="' + moduleListStr + '">');
			}
		};
		//	初始化
		categoryData.addMethod = 'manual';
		moduleListObj = INSTANCE.render.moduleBox.find(`.modulelist[data-method="${categoryData.addMethod}"]`)
		if (moduleList.length) {
			categoryData.module[categoryData.addMethod] = moduleList
			getModulesList()
		} else {
			moduleListObj.find('.module_to_more').hide()
			moduleListObj.find('.no_data').show()
		}
		// 产品添加方式
		$('.add_method .input_radio_box').click(function () {
			let $value = $(this).find('input').val()
			moduleListObj = INSTANCE.render.inside.find(`.products_list[data-method="${$value}"]`)
			if ($value == 'intelligent') {
				$('.products_add_btn').addClass('hide')
				$('.condition_add_btn').removeClass('hide')
				if (moduleListObj.find('.item').length == 0) $('.condition_add_btn').trigger('click')
				$('.filter_condition_box .item_box .box_seach_select input').attr('notnull', 'notnull')
			} else if ($value == 'manual') {
				$('.products_add_btn').removeClass('hide')
				$('.condition_add_btn').addClass('hide')
				$('.filter_condition_box .item_box .box_seach_select input').removeAttr('notnull')
			} else {
				$('.products_add_btn').addClass('hide')
				$('.condition_add_btn').addClass('hide')
			}

			// 切换当前的排序方式
			let $order = moduleListObj.attr('data-order')
			let $orderObj = $(`.my_order_box .drop_down .item a[data-order="${$order}"]`)
			$('input[name="OrderType"]').val($order)
			$('.my_order_box .order_type').text($orderObj.text())
			$('.my_order_box .drop_down .item a').removeClass('current addpro')
			$orderObj.addClass('current addpro')
			if ($order == 'custom_sort') {
				// 手动排序的时候显示拖动图标
				moduleListObj.addClass('custom_sort')
			} else {
				moduleListObj.removeClass('custom_sort')
			}
			if (moduleListObj.find('.item').length) {
				INSTANCE.render.moduleBox.find('.box_drop_down_menu').show()
				moduleListObj.find('.r_con_table').show()
				moduleListObj.find('.no_data').hide()
			} else {
				INSTANCE.render.moduleBox.find('.box_drop_down_menu').hide()
				moduleListObj.find('.r_con_table').hide()
				moduleListObj.find('.no_data').show()
			}

			moduleListObj.addClass('current').siblings().removeClass('current')
			categoryData.addMethod = $value
			moduleList = categoryData.module[categoryData.addMethod]
		})
		// 排序方式
		$('.my_order_box .drop_down .item a').click(function () {
			getSortList($(this))
		})
		INSTANCE.render.module.on('click', '.pagination li a',
			function () {
				// 翻页
				let $this = $(this)
				let $page = $this.attr('data-page')
				let $fixedLoad = $('.fixed_loading')
				$fixedLoad.fadeIn()
				moduleListObj.attr('data-page', $page)
				window.history.pushState(null, null, location.href.replace(/&page=([0-9]*)/, '') + '&page=' + $page)
				getModulesList()
				$fixedLoad.fadeOut()
				return false
			}
		).on('click', '.p_top',
			function () {
				// 手动排序产品置顶
				let $obj = $(this).parents('.item')
				let $page = parseInt(moduleListObj.attr('data-page'))
				let $index = $page * MODULE_LIMIT + $obj.index()
				if(moduleListObj.attr('disabled')) return false;
				moduleListObj.attr('disabled', 'disabled');
				moduleList.splice($index, 1)
				moduleList.unshift($obj.find('input[name^=moduleId]').val())
				getModulesList()
			}
		).on('click', '.move_next_top, .move_prev_top',
			function () {
				// 排序移动到上下页顶部
				if (!$(this).hasClass('disabled')) {
					let $page = parseInt(moduleListObj.attr('data-page'))
					let $select = moduleListObj.find('input[name=select]')
					let $moveList = $select.map(function () {
						if ($(this).is(':checked')) return $(this).val()
					}).get()
					let $moveListIndex = $select.map(function () {
						if ($(this).is(':checked')) {
							$index = $page * MODULE_LIMIT + $(this).index('input[name=select]')
							return $index
						}
					}).get().reverse()
					$.each($moveListIndex, function($k, $v){
						moduleList.splice($v, 1)
					})
					if ($(this).hasClass('move_next_top')) $startIndex = ($page + 1) * MODULE_LIMIT
					if ($(this).hasClass('move_prev_top')) $startIndex = ($page - 1) * MODULE_LIMIT
					$.each($moveList, function ($k, $v) {
						moduleList.splice($k + $startIndex, 0, $v)
					})
					getModulesList()
				}
			}
		).on('click', '.p_del',
			function () {
				// 删除产品
				let $obj = $(this).parents('.item')
				let $page = parseInt(moduleListObj.attr('data-page'))
				let $index = $page * MODULE_LIMIT + $obj.index()
				let params = {
					'title': lang_obj.global.del_confirm,
					'confirmBtn': lang_obj.global.del,
					'confirmBtnClass': 'btn_warn'
				}
				global_obj.win_alert(params, function () {
					let html = '<input type="hidden" name="delModuleId[]" value="' + $obj.find('input[name^=moduleId]').val() + '" />'
					moduleListObj.find('.del_pro_box').append(html)
					moduleList.splice($index, 1)
					$obj.remove()
					getModulesList()
				}, 'confirm')
			}
		)

		frame_obj.fixed_right_module_choice();
		frame_obj.fixed_right($('.module_add_btn'), '.fixed_right_module_choice', function ($this) {
			if (!$('#fixed_right .search_form form input[name=FilterCateId]').length) {
				$('#fixed_right .search_form form').append('<input type="hidden" name="FilterCateId" value="" />')
			}
			if (!$('#fixed_right .search_form form input[name=FilterTagId]').length) {
				$('#fixed_right .search_form form').append('<input type="hidden" name="FilterTagId" value="" />')
			}
			if (!$('#fixed_right .search_form form input[name=FilterModuleId]').length) {
				$('#fixed_right .search_form form').append('<input type="hidden" name="FilterModuleId" value="" />')
			}
			if (!$('#fixed_right .search_form form input[name=AddModuleId]').length) {
				$('#fixed_right .search_form form').append('<input type="hidden" name="AddModuleId" value="" />')
			}

			//被删除的模块ID也要传进去 可以让他重新添加
			$delModuleObj = $('.del_pro_box').find('input');
			$delModuleAry = new Array();

			$.each($delModuleObj, function(k, v){
				v = $(v).val();
				$delModuleAry.push(v);
			})
			$addModuleId = $delModuleAry.join(',');
			$('#fixed_right .search_form form input[name=AddModuleId]').val($addModuleId)
			
			// 把已经有的模块ID记录起来
			$moduleId = moduleList.join(',')
			$('#fixed_right .search_form form input[name=FilterModuleId]').val($moduleId)
			$('#fixed_right .search_form form').submit()
		})

		// 关联产品提交 (手动添加)
		frame_obj.submit_form_init($('#fixed_right_module_choice_form'), '', '', '', function (data) {
			if (data.ret == 1) {
				categoryData.module.manual = categoryData.module.manual.concat(data.msg)
				moduleList = categoryData.module.manual
				let OrderType = $('input[name=OrderType]').val()
				if (OrderType == 'custom_sort') {
					getModulesList()
				} else {
					$('.my_order_box .drop_down .item a[data-order=' + OrderType +']').addClass('addpro')
					getSortList($('.my_order_box .drop_down .item a[data-order=' + OrderType +']'))
				}
				
				$.each(data.msg, function(k, v){
					if($('.del_pro_box input[value="' + v + '"]').length > 0) {
						$('.del_pro_box input[value="' + v + '"]').remove();
					}
				})

				global_obj.win_alert_auto_close(lang_obj.global.add_success, '', 1000, '8%')
				$('.fixed_right_module_choice').find('.btn_cancel').trigger('click')
			} else {
				global_obj.win_alert_auto_close(data.msg, 'await', 1000, '8%')
			}
		});

	},

	/**
	 * 右侧弹窗的模块数据选择
	 */
	fixed_right_module_choice: function(){
		var fixed_obj = $('.fixed_right_module_choice');
		var load_edit_form = function(target_obj, url, type, form_data, value, callback, fuc){
			$.ajax({
				type:type,
				url:url,
				data: form_data,
				success:function(data){
					if(fuc=='append'){
						$(target_obj).append($(data).find(target_obj).html());
					}else{
						$(target_obj).html($(data).find(target_obj).html());
					}
					callback && callback(data);
				}
			});
		}
		// 初始化模块数据列表
		var module_list_init = function(type){
			var value = '';
			if (type == 'append') value = '?page=' + (parseInt($('.fixed_right_module_choice_list').attr('data-page')) + 1);
			load_edit_form('.fixed_right_module_choice_list', '/manage/action/fixed-right-module-choice' + value, 'post', $('#fixed_right .search_form form').serialize(), value, function (data) {
				$('.fixed_right_module_choice_jsppane').removeClass('loading');
				$('.fixed_right_module_choice_list').attr('data-total-pages',$(data).find('.fixed_right_module_choice_list').attr('data-total-pages')).attr('data-page',$(data).find('.fixed_right_module_choice_list').attr('data-page'));
			}, type);
			if (type != "append") fixed_obj.find("input[name=submit_button]").attr("disabled", true);
		};
		// 搜索
		fixed_obj.find('.search_form form').submit(function(){
			fixed_obj.find('.select_all_box .input_checkbox_box').removeClass('checked');
			module_list_init();
			return false;
		});
		// 点击模块数据
		fixed_obj.on('click', '.fixed_right_module_choice_list_item:not(".disabled")', function (event) {
			event.stopPropagation();
			var ProId = $(this).data('proid');
			$(this).toggleClass('current');
			$(this).find('.btn_checkbox').toggleClass('current');
			$(this).find('.btn_checkbox input').prop('checked') ? $(this).find('.btn_checkbox input').prop('checked', false) : $(this).find('.btn_checkbox input').prop('checked', true);
			if (fixed_obj.find(".fixed_right_module_choice_list_item.current").length > 0) {
				//已有勾选产品
				fixed_obj.find("input[name=submit_button]").attr("disabled", false);
			} else {
				//没有勾选产品
				fixed_obj.find("input[name=submit_button]").attr("disabled", true);
			}
		});
		fixed_obj.on('click', '.select_all_box .input_checkbox_box', function(){
			var $this = $(this);
			if ($this.hasClass('checked')) {
				$('.fixed_right_module_choice_list .fixed_right_module_choice_list_item:not(".disabled")').removeClass('current');
				$('.fixed_right_module_choice_list .fixed_right_module_choice_list_item:not(".disabled") .btn_checkbox').removeClass('current');
				$('.fixed_right_module_choice_list .fixed_right_module_choice_list_item:not(".disabled") .btn_checkbox input').prop('checked', false);
			} else {
				$('.fixed_right_module_choice_list .fixed_right_module_choice_list_item:not(".disabled")').addClass('current');
				$('.fixed_right_module_choice_list .fixed_right_module_choice_list_item:not(".disabled") .btn_checkbox').addClass('current');
				$('.fixed_right_module_choice_list .fixed_right_module_choice_list_item:not(".disabled") .btn_checkbox input').prop('checked', true);
			}
			if (fixed_obj.find(".fixed_right_module_choice_list_item.current").length > 0) {
				//已有勾选产品
				fixed_obj.find("input[name=submit_button]").attr("disabled", false);
			} else {
				//没有勾选产品
				fixed_obj.find("input[name=submit_button]").attr("disabled", true);
			}
		});

		// 加载更多
		fixed_obj.on('click', '.fixed_right_module_choice_list_load_more', function () {
			$(this).remove();
			module_list_init('append');
		});
	},

	checkCommission: function(){
		$('.btn_check_commission_status').click(function(){
			let billPage = $(this).hasClass('bill_page')
			$.post('/manage/set/commission/check-commission', {}, function(data){
				if (data.ret == 1) {
					if (billPage) {
						let param1 = {"title":lang_obj.manage.global.paySuccess,"subtitle":data.msg.tips,"confirmBtn":lang_obj.global.ok2}
						global_obj.win_alert(param1, function(){
							location.href = location.href
						}, undefined, undefined, 'success')
					} else {
						$('#global_commission_box').hide()
						let param = {"title":lang_obj.manage.global.paySuccess,"subtitle":data.msg.tips,"confirmBtn":data.msg.confirmBtn,"cancelBtn":lang_obj.global.close}
						global_obj.win_alert(param, function(){
							window.open('/manage/set/commission/index', '_blank')
							return false
						}, 'confirm', undefined, 'success')
					}
				} else {
					global_obj.win_alert_auto_close(data.msg.tips, 'fail', 2000, '100px')
				}
			}, 'json')
		})
	},

	translation_init:function(){ //翻译器
		let contentBox = $('.fixed_translation .content_box')
		let module = contentBox.data('module')
		let shopLanguage = contentBox.data('shop-language')

		let configJson = {
			"module": module,
			"shopLanguage": shopLanguage,
		}

		frame_obj.fixed_right($('.btn_translation'), ".fixed_translation", function() {}, function () {
			contentBox.html('')
			let relatedId = contentBox.data('related-id')
			let trackId = contentBox.data('track-id')

			configJson.relatedId = relatedId
			configJson.trackId = trackId

			let promise = frame_obj.translation_get_content(configJson)

			promise.then(function(){
				frame_obj.translation_get_chars()
			}).then(function(){
				let form = $('#tranlation_form')

				form.submit(function(){return false;});
				form.find('.btn_submit').click(function(){
					let charNeed = parseInt(form.find('.translation_tips .char_need').text())
					let charRemaining = parseInt(form.find('.translation_tips .char_remaining').text())
					let data = form.serializeObject()

					if (!charNeed) {
						global_obj.win_alert_auto_close(lang_obj.translation.not_content, 'fail', 1000, '8%')
						return;
					}
		
					// 翻译所需字符大于剩余字符
					if (charNeed > charRemaining || !charRemaining) {
						configJson.error = 'not_enough'
						frame_obj.translation_get_content(configJson)
						return false;
					}
		
					$(this).attr('disabled', true);
					$.post('/manage/translation/translation', data, function(result){
						if (result.ret == 1) {
							trackId = result.msg.trackId;
							contentBox.html(result.msg.html).attr('data-track-id', trackId)
							frame_obj.translation_get_status({
								"trackId":trackId
							}, 0)
						} else {
							global_obj.win_alert_auto_close(result.msg, '', 1000, '8%')
						}
						form.find('.btn_submit').attr('disabled', false);
					}, 'json');
				});

				
			})
		})

		contentBox.on('click', '.btn_know', function(){
			let relatedId = contentBox.data('related-id')
			let trackId = contentBox.data('track-id')
			configJson.relatedId = relatedId
			configJson.trackId = trackId

			$.post('/manage/translation/know', configJson, function(result){}, 'json');
		});

		contentBox.on('click', '.btn_restore', function(){
			$(this).attr('disabled', true)
			let trackId = contentBox.data('track-id')
			if (trackId) {
				let form = $('#tranlation_form')
				$.post('/manage/translation/restore', {id: trackId}, function(result2){
					if (result2.ret == 1) {
						contentBox.attr('track-id', 0)
						setTimeout(function(){
							$('.btn_translation').click()
						}, 100)
					} else {
						global_obj.win_alert_auto_close(result2.msg, '', 1000, '8%')
					}
					form.find('.btn_submit').attr('disabled', false);
				}, 'json');
			} else {
				configJson.error = ''
				setTimeout(function(){
					$('.btn_translation').click()
				}, 100)
			}
		})

		contentBox.on('click', '.btn_continue', function(){
			$(this).attr('disabled', true)
			let trackId = contentBox.data('track-id')
			if (trackId) {
				let form = $('#tranlation_form')
				$.post('/manage/translation/continue', {id: trackId}, function(result3){
					if (result3.ret == 1) {
						contentBox.html(result3.msg.html)
						setTimeout(function(){
							$('.btn_translation').click()
						}, 1000)
					} else {
						global_obj.win_alert_auto_close(result3.msg, '', 1000, '8%')
					}
					form.find('.btn_submit').attr('disabled', false);
				}, 'json');
			} else {
				configJson.error = ''
				setTimeout(function(){
					$('.btn_translation').click()
				}, 100)
			}
		})
	},

	translation_get_chars: function(){
		let form = $('#tranlation_form')
		$('.translation_tips').addClass('loading')
		form.find('.btn_submit').attr('disabled', true)
		setTimeout(function(){
			let data = form.serializeObject()
			$.post('/manage/translation/get-need-chars', data, function(result) {
				if (result.ret == 1) {
					let tips = form.find('.translation_tips')
					tips.find('.char_need').text(result.msg.need)
					tips.find('.char_remaining').text(result.msg.remaining)
				}
				$('.translation_tips').removeClass('loading')
				form.find('.btn_submit').attr('disabled', false)
			}, 'json');
		}, 100);
	},

	translation_get_content: function(data) {
		let contentBox = $('.fixed_translation .content_box')
		var promise = new Promise(function(resolve, reject){
			setTimeout(function(){
				$.post('/manage/translation/infomation', data, function(result){
					if (result.ret == 1) {
						contentBox.html(result.msg.html)
						if (result.msg.progress) {
							frame_obj.circle_progress_bar({
								percent: result.msg.progress,
								processingText: ''
							});

							frame_obj.translation_get_status({
								"trackId": result.msg.id
							}, 1)
						}
					} else {
						contentBox.html('')
					}
					resolve()
				}, 'json')
			}, 100)
		})

		return promise
	},

	translation_get_status:function (data, i) {
		let contentBox = $('.fixed_translation .content_box')
		let time = i == 0 ? 100 : 5000;
		setTimeout(function(){
			$.post('/manage/translation/get-status', data, function(result) {
				if (result.ret == 0) {
					// 报错
					global_obj.win_alert_auto_close(result.msg, '', 1000, '8%')
				} else {
					// 进度条
					if (result.ret == 1) {
						frame_obj.circle_progress_bar({
							percent: result.msg.progress,
							processingText: ''
						});
						frame_obj.translation_get_status(data, 1)
					} else if (result.ret == 2) {
						contentBox.html(result.msg)
					}
				}
			}, 'json');
		}, time);
	},

	auto_translation: function(id){
		if (id) {
			$('.fixed_translation .content_box').attr('data-track-id', id)
		}
		
		setTimeout(function(){
			$('.btn_translation').click()
		}, 100)
	},

	new_message:function(i){
		setTimeout(function(){
			$.post('/Api/Message/NewMessage', function(data) {
				if (data.ret == 1) {
					$('#header .menu .menu_message .new_message_box span').text(data.msg)
					//$('#header .menu .menu_message .new_message_box').fadeIn(500).delay(2000).fadeOut(500)
				}
			}, 'json')

			frame_obj.new_message(60000)
		}, i)
	}
}

