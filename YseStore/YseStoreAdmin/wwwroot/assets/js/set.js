

var set_obj = {
	/******************* 基础设置 *******************/
	config_basis_edit: function () {
		/* ICO图片上传 */
		frame_obj.mouse_click($('#IcoDetail .upload_btn, #IcoDetail .pic_btn .edit'), 'img', function ($this) { //点击上传图片
			frame_obj.photo_choice_init('IcoDetail', '', 1);
		});

		/* Blog图片上传 */
		frame_obj.mouse_click($('#BlogDetail .upload_btn, #BlogDetail .pic_btn .edit'), 'img', function ($this) { //点击上传图片
			frame_obj.photo_choice_init('BlogDetail', '', 1);
		});

		/* 底部logo图片上传 */
		frame_obj.mouse_click($('#FooterDetail .upload_btn, #FooterDetail .pic_btn .edit'), 'img', function ($this) { //点击上传图片
			frame_obj.photo_choice_init('FooterDetail', '', 1);
		});

		/* 模块1-图片 */
		frame_obj.mouse_click($('#FooterMode1 .upload_btn, #FooterMode1 .pic_btn .edit'), 'img', function ($this) { //点击上传图片
			frame_obj.photo_choice_init('FooterMode1', '', 1);
		}); 
		/* 模块2-图片 */
		frame_obj.mouse_click($('#FooterMode2 .upload_btn, #FooterMode2 .pic_btn .edit'), 'img', function ($this) { //点击上传图片
			frame_obj.photo_choice_init('FooterMode2', '', 1);
		});
		/* 模块3-图片 */
		frame_obj.mouse_click($('#FooterMode3 .upload_btn, #FooterMode3 .pic_btn .edit'), 'img', function ($this) { //点击上传图片
			frame_obj.photo_choice_init('FooterMode3', '', 1);
		});
		/* 模块4-图片 */
		frame_obj.mouse_click($('#FooterMode4 .upload_btn, #FooterMode4 .pic_btn .edit'), 'img', function ($this) { //点击上传图片
			frame_obj.photo_choice_init('FooterMode4', '', 1);
		});

		/* 模块1-图片 */
		frame_obj.mouse_click($('#GoodsFooterMode1 .upload_btn, #GoodsFooterMode1 .pic_btn .edit'), 'img', function ($this) { //点击上传图片
			frame_obj.photo_choice_init('GoodsFooterMode1', '', 1);
		});
		/* 模块2-图片 */
		frame_obj.mouse_click($('#GoodsFooterMode2 .upload_btn, #GoodsFooterMode2 .pic_btn .edit'), 'img', function ($this) { //点击上传图片
			frame_obj.photo_choice_init('GoodsFooterMode2', '', 1);
		});
		/* 模块3-图片 */
		frame_obj.mouse_click($('#GoodsFooterMode3 .upload_btn, #GoodsFooterMode3 .pic_btn .edit'), 'img', function ($this) { //点击上传图片
			frame_obj.photo_choice_init('GoodsFooterMode3', '', 1);
		});

		//勾选按钮(产品评论)
		frame_obj.config_switchery($('.review_checkbox .switchery'), '/manage/set/basis/used-edit', 'data-type', 'type');

		frame_obj.box_type_menu();

		frame_obj.submit_form_init($('#edit_form'), '/manage/set/basis', () => {
			if ($('.fixed_btn_submit .btn_submit').hasClass('btn_disabled')) return false;
		});
	},

	config_currency_edit: function () {
		// 函数事件
		let currencyData = {};
		let currencyId = 0;
		let defaultData = { "consumer": 0, "business": 0 };
		const FUNC = {
			Load: () => {
				$.get("/api/Setting/ShowCurrency", function (result) {
					if (result.ret == 1) {
						let html = "";
						$(result.msg).each(function (index, element) {
							if (element.IsUsed == 1) {
								html += '<tr data-id="' + element.CId + '">';
								html += '<td>' + element.Name  + ' (' + element.Currency + '/' + element.Symbol + ')</td>';
								html += '<td>' + element.ExchangeRate + '</td>';
								let $divChecked = ''
								let $inputChecked = ''
								if (element.IsWebShow == 1) {
									$divChecked = 'checked'
									$inputChecked = 'checked="checked"'
								}
								html += `<td>
												<div class="switchery ${$divChecked}">
													<input type="checkbox" ${$inputChecked} name="IsWebShow[${element.CId}]" value="1"><div class="switchery_toggler"></div>
													<div class="switchery_inner">
														<div class="switchery_state_on"></div>
														<div class="switchery_state_off"></div>
													</div>
												</div>
											</td>`;
								html += '<td nowrap class="operation tar">';
								html += '<a href="javascript:;" class="oper_icon icon_edit edit button_tips">' + lang_obj.manage.global.edit + '</a>';
								html += '<a href="javascript:;" class="oper_icon icon_del del button_tips">' + lang_obj.global.del + '</a>';
								html += '<input type="hidden" class="CurrencyRate" name="CurrencyRate[' + element.CId + ']" value="' + element.ExchangeRate + '" />';
								html += '<input type="hidden" class="CurrencyMethod" name="CurrencyMethod[' + element.CId + ']" value="' + element.Method + '" />';
								html += '<input type="hidden" class="CurrencyProportion" name="CurrencyProportion[' + element.CId + ']" value="' + element.Proportion + '" />';
								html += '<input type="hidden" class="CurrencyProportionStatus" name="CurrencyProportionStatus[' + element.CId + ']" value="' + element.ProportionStatus + '" />';
								html += '</td>';
								html += '</tr>';
							}
							currencyData[element.CId] = element;
							if (element.ManageDefault == 1) defaultData.business = element.CId;
							if (element.IsDefault == 1) defaultData.consumer = element.CId;
						});
						$(".currency_list tbody").html(html);
						FUNC.DefaultSelect("consumer");
						FUNC.DefaultSelect("business");
						FUNC.ScrollLoad();
					}
					$('.fixed_btn_submit .btn_submit').removeClass('btn_disabled');
				}, "json");
			},
			DefaultSelect: ($type) => {
				let $target = ($type == "business" ? $("select[name=ManageDefaultCurrency]") : $("select[name=DefaultCurrency]"));
				let html = "";
				$(".currency_list tbody tr").each(function (index, element) {
					let $id = $(element).data("id");
					let $data = typeof currencyData[$id] !== undefined ? currencyData[$id] : "";
					let $isWebShow = $(element).find('input[name^=IsWebShow]:checked').length;
					if ($type == "consumer" && !$isWebShow) {
						return true
					}
					if ($data.IsUsed == 1) {
						let isSelected = (($type == "business" && defaultData.business == $id) || ($type == "consumer" && defaultData.consumer == $id) ? " selected" : "");
						html += '<option value="' + $id + '"' + isSelected + '>' + $data.Name + ' (' + $data.Currency + '/' + $data.Symbol + ')</option>';
					}
				});
				$target.html(html);
			},
			CurItem: (item) => {
				let $box = $(".fixed_add_currency");
				let $val = item.attr("data-value");
				if ($val > 0) {
					$box.find(".box_rate , .box_currency_method").show();
					let $data = typeof currencyData[$val] !== undefined ? currencyData[$val] : '';
					if ($data) {
						$method = $data.Method == 'auto' ? 1 : 0;

						$box.find(".box_rate .unit_input .last").text($data.Currency);
						$box.find(".box_rate .unit_input input").val($data.ExchangeRate);
						$box.find(".input_radio_box[data='" + $data.Method + "']").addClass('checked').find('input').prop('checked', true);
						$box.find(".input_radio_box[data='" + $data.Method + "']").siblings().removeClass('checked').find('input').prop('checked', false);
						$box.find('input[name="CurrencyMethod"]').val($method);
						if ($method) {
							$box.find('input[name="CurrencyMethod"]').parents('.switchery').addClass('checked');
						} else {
							$box.find('input[name="CurrencyMethod"]').parents('.switchery').removeClass('checked');
						}

						$passCurrency = ['BOV', 'LVL', 'VEF'];
						if ($.inArray($data.Currency, $passCurrency) >= 0) {
							$('.box_currency_method').hide();
						} else {
							$('.box_currency_method').show();
						}

						//FUNC.USDShow($data.Currency);
						$box.find(".box_currency").removeClass("has_error");
						$box.find(".box_currency .error_tips").remove();
						let html = "";
						if ($data.SupportPayment != "") {
							$.each($data.SupportPayment, function (index, element) {
								html += "<li>" + element + "</li>";
							});
						}
						if (html != "") {
							$box.find(".box_payment_method").show().find("ul").html(html);
						} else {
							$box.find(".box_payment_method").hide().find("ul").html(html);
						}
					}
				}
			},
			USDShow: (currency) => {
				let $obj = $(".fixed_add_currency .box_rate");
				if (currency == 'USD') {
					$obj.find(".usd_only").show();
					$obj.find(".usd_compare, .unit_input").hide();
				} else {
					$obj.find(".usd_only").hide();
					$obj.find(".usd_compare, .unit_input").show();
				}
			},
			ScrollLoad: () => {
				let $boxHeight = $(".currency_list").height();
				let $tableHeight = $(".currency_list table").height();
				$boxHeight >= $tableHeight && ($tableHeight -= 10); // 隐藏滚动条
				$(".scroll_sticky_content>div").css({ "width": 1, "height": $tableHeight });
			}
		}

		FUNC.Load();

		$(".currency_list").on("click", ".edit", function () {
			// 修改
			currencyId = $(this).parents("tr").data("id");
			$("#btn_edit_global").click();
		}).on("click", ".del", function () {
			// 删除
			let $this = $(this);
			let $trObj = $this.parents("tr");
			let $id = $trObj.data("id");
			let $data = typeof currencyData[$id] !== undefined ? currencyData[$id] : "";
			let $default = "";
			if ($data) {
				if (defaultData.consumer == $id) {
					// 前台默认货币
					$default = lang_obj.manage.currency.consumer_default;
				}
				if (defaultData.business == $id) {
					// 后台默认货币
					if ($default != "") {
						$default = lang_obj.manage.currency.and.replace("{{default1}}", $default).replace('{{default2}}', lang_obj.manage.currency.business_default);
					} else {
						$default = lang_obj.manage.currency.business_default;
					}
				}
			}
			if ($default != "") {
				// 失败提示
				let tips = lang_obj.manage.currency.tips_delete_disabled.replace("{{currency}}", $data.Name + " (" + $data.Currency + ")").replace('{{default}}', $default);
				global_obj.win_alert({
					title: tips,
					confirmBtn: lang_obj.global.ok2,
				});
				return false;
			}
			let params = {
				"title": lang_obj.global.del_confirm,
				"confirmBtn": lang_obj.global.del,
				"confirmBtnClass": "btn_warn"
			};
			global_obj.win_alert(params, function () {
				$trObj.fadeOut(300, function () {
					$trObj.remove();
					$data.IsUsed = 0;

					FUNC.DefaultSelect("consumer");
					FUNC.DefaultSelect("business");
					FUNC.ScrollLoad();

					global_obj.win_alert_auto_close(lang_obj.manage.global.del_success, "", 1000, "8%");
				});
			}, "confirm");
			return false;
		});

		$(".box_currency_select").on("change", "select[name=ManageDefaultCurrency]", function () {
			// 后台默认货币
			let $id = $(this).val();
			defaultData.business = $id;
		}).on("change", "select[name=DefaultCurrency]", function () {
			// 前台默认货币
			let $id = $(this).val();
			defaultData.consumer = $id;
		});


		frame_obj.switchery_checkbox(function (obj) {
			if (obj.find('input[name="CurrencyMethod"]').length) {
				obj.find('input[name="CurrencyMethod"]').val(1);
				$CurrencyId = obj.parents('.fixed_add_currency').find('input[name=id]').val();
				if ($CurrencyId <= 0) $CurrencyId = obj.parents('.fixed_add_currency').find('input[name=cid]').val();

				$.post('/manage/set/basis/get-exchange-rate', { 'CurrencyId': $CurrencyId }, function (result) {
					if (result.ret == 1) {
						$Rate = parseFloat(result.msg.Rate);
						$Rate = $Rate.toFixed(4);
						obj.parents('.fixed_add_currency').find('input[name="ExchangeRate"]').val($Rate);
					}
				}, 'json')
				$('.fixed_add_currency .box_proportion').removeClass('hide')
			} else if (obj.find('input[name^="IsWebShow"]').length) {
				FUNC.DefaultSelect("consumer");
			}
		}, function (obj) {
			if (obj.find('input[name="CurrencyMethod"]').length) {
				obj.find('input[name="CurrencyMethod"]').val(0);
				$('.fixed_add_currency .box_proportion').addClass('hide')
			} else if (obj.find('input[name^="IsWebShow"]').length) {
				let _this = obj.find('input[name^="IsWebShow"]').parents('tr').attr('data-id')
				let _default = $("select[name=DefaultCurrency]").val()
				if (_this == _default) {
					global_obj.win_alert_auto_close(lang_obj.manage.set.isShowCurrency, 'await', 1000, '8%')
					obj.addClass('checked').find('input[name^="IsWebShow"]').prop('checked', true)
					return false
				}
				FUNC.DefaultSelect("consumer");
			}
		});
		frame_obj.fixed_right($(".btn_add_currency, #btn_edit_global"), ".fixed_add_currency", function (_this) {
			let $box = $(".fixed_add_currency");
			let submitTxt = "";
			// 初始化
			$box.find(".box_currency, .box_currency_hide, .box_rate, .box_payment_method, .box_currency_method").hide();
			$box.find(".box_currency .input").text("");
			$box.find(".has_error").each(function () {
				$(this).removeClass("has_error");
				$(this).find(".error_tips").remove();
			});
			$box.find('input[name="CurrencyMethod"]').parents('.switchery').removeClass('checked');
			$box.find('.box_proportion').addClass('hide')
			$box.find(".box_proportion .unit_input input").val(2) // 默认比例为2
			let $checkboxObj = $box.find('.box_proportion .btn_checkbox')
			let $currentStatus = $checkboxObj.hasClass('current') ? 1 : 0
			if (!$currentStatus) {
				$box.find('.box_proportion .btn_checkbox').trigger('click')
			}

			// 操作类型
			if (_this.hasClass("btn_add_currency")) {
				// 添加
				$box.find(".top_title .add").show().siblings("span").hide();
				submitTxt = lang_obj.global.add;
				$box.find(".box_currency").show();
				// 现已启用的货币ID
				let cidAry = [];
				$(".currency_list tbody tr").each(function (index, element) {
					cidAry[index] = $(element).data("id");
				});
				let exclude = cidAry.length > 0 ? cidAry.join("-") : "";
				// 请求
				$.post("/api/Setting/SetSelectCurrency", { "exclude": exclude }, function (result) {
					$(".fixed_add_currency .box_currency .input").html(result);

					$(".fixed_add_currency .box_drop_double .btn_load_more").attr("data-exclude", exclude);
				}, "html");
				currencyId = 0;
			} else {
				// 修改
				$box.find(".top_title .edit").show().siblings("span").hide();
				submitTxt = lang_obj.manage.global.save;

				let $data = typeof currencyData[currencyId] !== undefined ? currencyData[currencyId] : '';
				if ($data) {
					$Method = $data.Method == 'auto' ? 1 : 0;
					$box.find(".box_currency_hide, .box_rate, .box_currency_method").show();
					$box.find(".box_currency_hide .box_input").val($data.Name + " (" + $data.Currency + "/" + $data.Symbol + ")");
					$box.find(".box_rate .unit_input .last").text($data.Currency);
					$box.find(".box_rate .unit_input input").val($data.ExchangeRate);
					$box.find(".input_radio_box[data='" + $data.Method + "']").addClass('checked').find('input').prop('checked', true);
					$box.find(".input_radio_box[data='" + $data.Method + "']").siblings().removeClass('checked').find('input').prop('checked', false);
					$box.find(".box_proportion .unit_input input").val($data.Proportion);

					$passCurrency = ['BOV', 'LVL', 'VEF'];
					if ($.inArray($data.Currency, $passCurrency) >= 0) {
						$('.box_currency_method').hide();
					} else {
						$('.box_currency_method').show();
					}

					$box.find('input[name="CurrencyMethod"]').val($Method);
					if ($Method) {
						$box.find('input[name="CurrencyMethod"]').parents('.switchery').addClass('checked');
						$box.find('.box_proportion').removeClass('hide')
					} else {
						$box.find('input[name="CurrencyMethod"]').parents('.switchery').removeClass('checked');
						$box.find('.box_proportion').addClass('hide')
					}

					let $checkboxObj = $box.find('.box_proportion .btn_checkbox')
					let $currentStatus = $checkboxObj.hasClass('current') ? 1 : 0
					if ($data.ProportionStatus != $currentStatus) {
						$box.find('.box_proportion .btn_checkbox').trigger('click')
					}

					FUNC.USDShow($data.Currency);
					let html = "";
					if ($data.SupportPayment != "") {
						$.each($data.SupportPayment, function (index, element) {
							html += "<li>" + element + "</li>";
						});
					}
					if (html != "") {
						$box.find(".box_payment_method").show().find("ul").html(html);
					} else {
						$box.find(".box_payment_method").hide().find("ul").html(html);
					}
				}
			}
			$box.find("input[name=id]").val(currencyId);
			$box.find(".box_button .btn_submit").val(submitTxt);
		});

		$(".box_currency").on("click", ".box_drop_double .input_radio_box", function () {
			FUNC.CurItem($(this).parent());
		}).on("click", ".box_drop_double .item", function () {
			if ($(this).find(".input_radio_box").length == 0) {
				FUNC.CurItem($(this));
			}
		});

		$('.box_proportion .btn_checkbox').on('click', function () {
			let $this = $(this)
			let $obj = $this.find('input')
			let $inputObj = $this.next().find('.box_input')
			if ($obj.is(':checked')) {
				// 已勾选
				$inputObj.removeAttr('readonly')
			} else {
				// 未勾选
				$inputObj.attr('readonly', '')
			}
		})

		frame_obj.check_amount($('.fixed_add_currency'))

		$(".fixed_add_currency").on("click", ".btn_submit", function () {
			// 货币(添加/修改)提交
			let $box = $(".fixed_add_currency");
			let $id = $box.find("input[name=id]").val();
			let $cid = $box.find("input[name=cid]").val();
			let $rate = $.trim($box.find("input[name=ExchangeRate]").val());
			let $method = $box.find("input[name=CurrencyMethod]").val() == 1 ? 'auto' : 'manual';
			let $proportion = parseInt($box.find("input[name=Proportion]").val() || 0)
			let $proportionStatus = $box.find("input[name=ProportionStatus]").is(':checked') ? 1 : 0
			$id = parseInt($id == "" ? 0 : $id);
			$cid = parseInt($cid == "" ? 0 : $cid);
			// 检验数据
			if ($id == 0 && $cid == 0) {
				// 货币选择(添加货币)
				let $rowObj = $box.find(".box_currency");
				let $name = $rowObj.find("label").text();
				let $tips = lang_obj.manage.tips.please_select.replace("{{title}}", $name);
				$rowObj.addClass("has_error");
				if ($rowObj.find(".error_tips").length) {
					$rowObj.find(".error_tips").text($tips);
				} else {
					$rowObj.append("<p class=\"error_tips\">" + $tips + "</p>");
				}
				return false;
			}
			if ($method == 'auto' && $proportionStatus) {
				// 校验汇率波动幅度
				if ($proportion < 1 || $proportion > 10) {
					let $rowObj = $box.find('.box_proportion')
					let $tips = lang_obj.manage.currency.proportion_tips
					$rowObj.addClass('has_error')
					if ($rowObj.find('.error_tips').length) {
						$rowObj.find('.error_tips').text($tips)
					} else {
						$rowObj.append('<p class="error_tips">' + $tips + '</p>')
					}
					return false
				}
			}
			if ($rate == "") {
				// 汇率
				let $rowObj = $box.find(".box_exchange_rate");
				let $name = $(".box_rate label").text();
				let $tips = lang_obj.manage.sales.fillable_unempty.replace("{{name}}", $name);
				$rowObj.addClass("has_error");
				if ($rowObj.find(".error_tips").length) {
					$rowObj.find(".error_tips").text($tips);
				} else {
					$rowObj.append("<p class=\"error_tips\">" + $tips + "</p>");
				}
				return false;
			}
			$rate = parseFloat($rate).toFixed(4);
			// 记录数据
			if ($id) {
				// 修改货币
				let $data = typeof currencyData[$id] !== undefined ? currencyData[$id] : '';
				if ($data) {
					$data.ExchangeRate = $rate;
					$data.Method = $method;
					$data.Proportion = $proportion;
					$data.ProportionStatus = $proportionStatus;

					let $trObj = $(".currency_list tbody tr[data-id=\"" + $id + "\"]");
					$trObj.find("td:eq(1)").text($rate);
					$trObj.find(".CurrencyRate").val($rate);
					$trObj.find(".CurrencyMethod").val($method);
					$trObj.find(".CurrencyProportion").val($proportion);
					$trObj.find(".CurrencyProportionStatus").val($proportionStatus);
				}
			} else {
				// 添加货币
				$id = $box.find("input[name=cid]").val();
				let $data = typeof currencyData[$id] !== undefined ? currencyData[$id] : '';
				if ($data) {
					$data.IsUsed = 1; // 启用
					$data.ExchangeRate = $rate; // 汇率
					$data.Method = $method;
					$data.Proportion = $proportion;
					$data.ProportionStatus = $proportionStatus;

					let html = "";
					html += '<tr data-id="' + $data.CId + '">';
					html += '<td>' + $data.Name + ' (' + $data.Currency + '/' + $data.Symbol + ')</td>';
					html += '<td>' + $data.ExchangeRate + '</td>';
					let $divChecked = ''
					let $inputChecked = ''
					if ($data.IsWebShow == 1) {
						$divChecked = 'checked'
						$inputChecked = 'checked="checked"'
					}
					html += `<td>
									<div class="switchery ${$divChecked}">
										<input type="checkbox" ${$inputChecked} name="IsWebShow[${$data.CId}]" value="1"><div class="switchery_toggler"></div>
										<div class="switchery_inner">
											<div class="switchery_state_on"></div>
											<div class="switchery_state_off"></div>
										</div>
									</div>
								</td>`;
					html += '<td nowrap class="operation tar">';
					html += '<a href="javascript:;" class="oper_icon icon_edit edit button_tips">' + lang_obj.manage.global.edit + '</a>';
					html += '<a href="javascript:;" class="oper_icon icon_del del button_tips">' + lang_obj.global.del + '</a>';
					html += '<input type="hidden" class="CurrencyRate" name="CurrencyRate[' + $data.CId + ']" value="' + $data.ExchangeRate + '" />';
					html += '<input type="hidden" class="CurrencyMethod" name="CurrencyMethod[' + $data.CId + ']" value="' + $data.Method + '" />';
					html += '<input type="hidden" class="CurrencyProportion" name="CurrencyProportion[' + $data.CId + ']" value="' + $data.Proportion + '" />';
					html += '<input type="hidden" class="CurrencyProportionStatus" name="CurrencyProportionStatus[' + $data.CId + ']" value="' + $data.ProportionStatus + '" />';
					html += '</td>';
					html += '</tr>';
					$(".currency_list tbody").append(html);
				}
			}

			FUNC.DefaultSelect("consumer");
			FUNC.DefaultSelect("business");
			FUNC.ScrollLoad();

			$("#fixed_right .btn_cancel").click();
			return false;
		});

		// 滚动条
		$(".currency_list").on("scroll", function () {
			if ($(this).scrollTop() > 0) {
				$(".currency_list table").addClass("fixed");
			} else {
				$(".currency_list table").removeClass("fixed");
			}
			$(".scroll_sticky_content").scrollTop($(this).scrollTop());
		});
		$(".scroll_sticky_content").on("mousedown", function () {
			$(this).on("scroll", function () {
				let tableHeight = $(".currency_list table").outerHeight();
				$(".scroll_sticky_content>div").css({ "width": 1, "height": tableHeight });
				if ($(this).scrollTop() > 0) {
					$(".currency_list table").addClass("fixed");
				} else {
					$(".currency_list table").removeClass("fixed");
				}
				$(".currency_list").scrollTop($(this).scrollTop());
			});
		}).on("mouseup", function () {
			$(this).off("scroll");
		});
	},

	/******************* 购物设置 *******************/
	config_shopping_edit: function () {
		/* LOGO图片上传 */
		frame_obj.mouse_click($('#LogoDetail .upload_btn, #LogoDetail .pic_btn .edit'), 'img', function ($this) { //点击上传图片
			frame_obj.photo_choice_init('LogoDetail', '', 1);
		});
		$('input[name="OrderPrefix"]').keyup(function () {
			var $reg = /^[0-9a-zA-Z_-]*$/,
				$reg2 = /[^0-9a-zA-Z_-]/g,
				$val = $('input[name="OrderPrefix"]').val();
			if (!$reg.test($val)) {
				$('input[name="OrderPrefix"]').val($val.replace($reg2, ''));
			}
		});

		$('.shopping_set .tourists_shopping .input_radio_side_box').on('click', function () {
			let val = $(this).find('input').val();
			if (val == 1) {
				$('.shopping_set .only_members').hide()
			} else {
				$('.shopping_set .only_members').show()
			}
		})

		frame_obj.switchery_checkbox(function (obj) {
		 
			if (obj.find('input[name=AddToCartFlow]').length) $('.shopping_edit_form .addtocartflow_content').show()
			if (obj.find('input[name=Pc]').length || obj.find('input[name=Mobile]').length) {
				obj.parents('.label').next('.input').show()
			}
			if (obj.find('input[name=ForConsultationOnly\\[IsUsed\\]]').length) $('.shopping_edit_form .consultation_only_content').show()
			if (obj.find('input[name=ForConsultationOnly\\[PriceShow\\]]').length) $('.shopping_edit_form .consultation_only_price_visible').show()
			if (obj.find('input[name=UsedOffer]').length) $('.shopping_edit_form .switchery_box').show()
			 
		}, function (obj) {

			if (obj.find('input[name=AddToCartFlow]').length) $('.shopping_edit_form .addtocartflow_content').hide()
			if (obj.find('input[name=Pc]').length || obj.find('input[name=Mobile]').length) {
				obj.parents('.label').next('.input').hide()
			}
			if (obj.find('input[name=ForConsultationOnly\\[IsUsed\\]]').length) $('.shopping_edit_form .consultation_only_content').hide()
			if (obj.find('input[name=ForConsultationOnly\\[PriceShow\\]]').length) $('.shopping_edit_form .consultation_only_price_visible').hide()
			if (obj.find('input[name=UsedOffer]').length) $('.shopping_edit_form .switchery_box').hide()
		});
		frame_obj.box_type_menu();
		frame_obj.submit_form_init($('#edit_form'), '/Setting/Index');

		var AddNum = 0;
		function countryAreaBox() {
			frame_obj.fixed_right($('.btn_set_area, .btn_set_additional_area, .btn_add_additional'), '.country_area_box', function ($this) {
				var $obj = $('.country_area_box'),
					$boxType = $this.attr('data-type'),
					$mark = '',
					$list = [],
					$scope_country_name = '',
					$additionalName = ''
				if ($boxType == 'additionalAdd') { // additionalAdd => 添加附加信息
					AddNum++ // 添加次数
				} else {
					$mark = $this.parents('tr').attr('data-mark')
					$list = $this.parents('tr').find('.scope_country').val().split(',')
					$scope_country_name = $this.parents('tr').find('.scope_country_name').text()
					if ($boxType == 'additionalEdit') $additionalName = $this.parents('tr').find('.additional_name').text()
				}
				if ($boxType == 'additionalAdd' || $boxType == 'additionalEdit') $('.country_area_box .additional_name').show().find('input[name="AdditionalName"]').val($additionalName)
				else $('.country_area_box .additional_name').hide()
				$('.country_area_box .selected_country .has_country').text($scope_country_name)
				if ($scope_country_name != '') {
					$('.country_area_box .selected_country .has_country').show()
					$('.country_area_box .selected_country .no_country').hide()
				} else {
					$('.country_area_box .selected_country .has_country').hide()
					$('.country_area_box .selected_country .no_country').show()
				}
				$obj.find('.top_title span').hide()
				$obj.find(`.top_title span[data-type="${$boxType}"]`).show()
				$obj.find('input[name=mark]').val($mark);
				$obj.find('input[name=boxType]').val($boxType);
				$list.map(function (e) {
					$('.country_area_box .country_item .input_checkbox_box[data-acronym="' + e + '"]').addClass('checked').find('input').prop('checked', true);
				});
				$('.country_area_box .continent_area').each(function () {
					var $all = $(this).find('input[name=Acronym]').length,
						$checked = $(this).find('input[name=Acronym]:checked').length,
						$class = '';
					if ($all == $checked) {
						$class = 'checked';
					} else if ($all > $checked && $checked) {
						$class = 'half_checked';
					}
					if ($class) {
						$(this).find('.continent .input_checkbox_box').addClass($class);
					} else {
						$(this).find('.continent .input_checkbox_box').removeClass('checked half_checked');
					}
				})
			});
		}
		countryAreaBox()
		$('.country_area_box').off('click').on('click', '.continent_area .continent .input_checkbox_box', function () {
			$(this).attr('data-status', 1);
		}).on('click', '.continent_area .continent', function () {
			if ($(this).find('.input_checkbox_box').attr('data-status') != 1) {
				if ($(this).find('.down').hasClass('cur')) {
					$(this).find('.down').removeClass('cur');
				} else {
					$(this).find('.down').addClass('cur');
				}
				$(this).next('.country_item').toggle();
			} else {
				$(this).find('.input_checkbox_box').attr('data-status', 0);
			}
		}).on('click', '.continent .input_checkbox_box', function () {
			var $obj = $(this);
			if ($obj.hasClass('checked')) {
				$obj.parent().next('.country_item').find('.input_checkbox_box').removeClass('checked');
				$obj.parent().next('.country_item').find('.input_checkbox input').prop('checked', false);
			} else {
				$obj.parent().next('.country_item').find('.input_checkbox_box').not('.disabled').addClass('checked');
				$obj.parent().next('.country_item').find('.input_checkbox_box:not(".disabled") input').prop('checked', true);
			}
			selectCountryText()
		}).on('click', '.country_item_sec .input_checkbox_box', function () {
			var $obj = $(this);
			if (!$obj.hasClass('disabled')) {
				//控制上级洲的选中状态
				var $area = $obj.parents('.continent_area'),
					$all_area_count = $area.find('.country_item .input_checkbox_box').length,
					$area_count = $area.find('.country_item .input_checkbox_box.checked').length;
				if ($obj.hasClass('checked')) {
					$area_count -= 1;
				} else {
					$area_count += 1;
				}

				if ($area_count == 0) {
					$area.find('.continent .input_checkbox_box').removeClass('checked half_checked').find('input').prop('checked', false);
				} else if ($all_area_count == $area_count) {
					$area.find('.continent .input_checkbox_box').addClass('checked').removeClass('half_checked').find('input').prop('checked', true);
				} else {
					$area.find('.continent .input_checkbox_box').addClass('checked half_checked').find('input').prop('checked', true);
				}
				setTimeout(function () {
					selectCountryText()
				}, 100)
			}
		});

		function selectCountryText() {
			let $name = $('.country_area_box .country_item input[name=Acronym]:checked').map(function () {
				return $(this).attr('data-country');
			}).get().join("、")
			$('.country_area_box .selected_country .has_country').text($name)
			if ($name != '') {
				$('.country_area_box .selected_country .has_country').show()
				$('.country_area_box .selected_country .no_country').hide()
			} else {
				$('.country_area_box .selected_country .has_country').hide()
				$('.country_area_box .selected_country .no_country').show()
			}
		}

		$('.country_area_box .btn_submit').click(function () {
			
			var $obj = $('.country_area_box .country_item input[name=Acronym]:checked'),
				$mark = $('.country_area_box input[name=mark]').val(),
				$boxType = $('.country_area_box input[name=boxType]').val(),
				$additionalName = $('.country_area_box input[name="AdditionalName"]').val(),
				$val = $obj.map(function () {
					return $(this).val();
				}).get().join(","),
				$name = $obj.map(function () {
					return $(this).attr('data-country');
				}).get().join("、")
			if (($boxType == 'additionalAdd' || $boxType == 'additionalEdit') && !$additionalName) {
				$('.country_area_box input[name="AdditionalName"]').css('border', '1px solid red')
				return false
			}
			$('.country_area_box input[name="AdditionalName"]').removeAttr('style')
			if ($boxType == 'additionalAdd') {
				$('.address_additional .country_list tbody').append(`
					<tr data-mark="custom-additional-add${AddNum}">
						<td>
							<div class="additional_name">${$additionalName}</div>
							<input type="hidden" class="additional_name_input" name="addressExpand[additional][add${AddNum}][Name]" value="${$additionalName}">
						</td>
						<td>
							<div class="scope_country_name">${$name}</div>
							<input type="hidden" class="scope_country" name="addressExpand[additional][add${AddNum}][Country]" value="${$val}">
						</td>
						<td nowrap="nowrap">
							<div class="switchery checked">
								<input type="checkbox" checked="checked" name="addressExpand[additional][add${AddNum}][IsOpen]" value="1">
								<div class="switchery_toggler"></div>
								<div class="switchery_inner">
									<div class="switchery_state_on"></div>
									<div class="switchery_state_off"></div>
								</div>
							</div>
						</td>
						<td nowrap="nowrap" class="operation">
							<a class="btn_set_additional_area" data-type="additionalEdit" href="javascript:;">${lang_obj.global.set}</a>
							&nbsp;&nbsp;&nbsp;
							<a class="btn_del_additional" data-id="0" href="javascript:;">${lang_obj.global.del}</a>
						</td>
					</tr>
				`)
				countryAreaBox()
			} else {
				$('.country_list tr[data-mark="' + $mark + '"] .scope_country').val($val);
				$('.country_list tr[data-mark="' + $mark + '"] .scope_country_name').text($name);
				if ($boxType == 'additionalEdit') {
					$('.country_list tr[data-mark="' + $mark + '"] .additional_name').text($additionalName)
					$('.country_list tr[data-mark="' + $mark + '"] .additional_name_input').val($additionalName)
				}
			}
			$('.country_area_box .btn_cancel').click();
		});
		// 删除附加信息
		$('.address_additional').on('click', '.btn_del_additional', function () {
			let params = {
				'title': lang_obj.global.del_confirm,
				'confirmBtn': lang_obj.global.del,
				'confirmBtnClass': 'btn_warn'
			};
			let _this = $(this)
			let _id = parseInt(_this.attr('data-id'))
			global_obj.win_alert(params, function () {
				_this.parents('tr').remove();
				if (_id) $('.address_additional').append(`<input type="hidden" class="scope_country" name="addressExpandDeleteId[]" value="${_id}">`)
			}, 'confirm');
		})

		// 取消事件
		$('body').on('click', '#fixed_right_div_mask, .country_area_box .btn_cancel', function () {
			$('.country_area_box .country_item input[name=Acronym]:checked').prop('checked', false).parents('.input_checkbox_box').removeClass('checked');
			$('.country_area_box .continent .input_checkbox_box').removeClass('checked half_checkeds');
		});

		frame_obj.fixed_right($('.btn_set_order_auto'), '.fixed_order_auto', function ($this) {
			let $obj = $('.fixed_order_auto'),
				$isCancel = $(".order_automation input[name=orderCancel]").val(),
				$isComplete = $(".order_automation input[name=orderComplete]").val();
			$obj.find(".input_checkbox_side_box").removeClass("checked").find("input").prop("checked", false);
			if ($isCancel == 1) {
				$obj.find(".cancel_input").addClass("checked").find("input").prop("checked", true);
			}
			if ($isComplete == 1) {
				$obj.find(".complete_input").addClass("checked").find("input").prop("checked", true);
			}
		});
		$('.fixed_order_auto .btn_submit').click(function () {
			let $obj = $('.fixed_order_auto'),
				$isCancel = $obj.find(".cancel_input input").is(":checked"),
				$isComplete = $obj.find(".complete_input input").is(":checked");
			if ($isCancel === true) {
				$(".order_automation .auto_cancel").addClass("show");
			} else {
				$(".order_automation .auto_cancel").removeClass("show");
			}
			if ($isComplete === true) {
				$(".order_automation .auto_complete").addClass("show");
			} else {
				$(".order_automation .auto_complete").removeClass("show");
			}
			$('.order_automation input[name=orderCancel]').val($isCancel === true ? 1 : 0);
			$('.order_automation input[name=orderComplete]').val($isComplete === true ? 1 : 0);
			$('.fixed_order_auto .btn_cancel').click();
		});
	},

	/******************* 会员设置 *******************/
	config_user_set_edit: function () {
		frame_obj.switchery_checkbox(function () {
			$('.verification_email_box').fadeIn();
		}, function () {
			$('.verification_email_box').fadeOut();
		}, '.verification_switchery');

		frame_obj.switchery_checkbox('', '', '.fixed_field .switchery');

		frame_obj.submit_form_init($('#edit_form'));

		// 注册表单
		let field_init = () => {
			for (let key in fieldData) {
				maxAddId = Math.max(fieldData[key].id, maxAddId);
				add_field_html(fieldData[key]);
			}
		}

		let add_field_html = (data) => {
			let html = field_html(data);
			$('#field_list').append(html);
		}
		let field_html = (data) => {
			let value = JSON.stringify(data);
			value = global_obj.htmlspecialchars(value);
			let html = '<div class="field_item" data-id="' + data.id + '">';
			html += '<em class=""></em>';
			html += '<span class="field_name">' + data.title + '</span>';
			if ($.inArray(data.format, ['FirstName', 'LastName', 'Email', 'Password', 'ConfirmPassword']) == -1) {
				html += '<a href="javascript:;" class="field_opt field_del_btn">' + lang_obj.global.del + '</a>';
			}
			html += '<a href="javascript:;" class="field_opt field_edit_btn">' + lang_obj.global.edit + '</a>';
			html += '<input type="hidden" name="FieldData[]" value="' + value + '">';
			html += '</div>';
			return html;
		}
		let fixed_field_init = () => {
			let fieldLimit = {
				'input': ['placeholder', 'required', 'tip', 'limit'],
				'textarea': ['placeholder', 'required', 'tip', 'limit'],
				'select': ['placeholder', 'required', 'tip', 'content'],
				'radio': ['tip', 'content'],
				'checkbox': ['tip', 'content'],
				'image': ['required', 'tip'],
			};
			let type = $('.fixed_field input[name=type]').val();
			let format = $('.fixed_field input[name=format]').val();
			$('.field_box').hide();  // 先默认隐藏
			$('.field_box').each(function () {
				$(this).find('input').removeAttr('notnull');
			});
			fieldLimit[type].forEach(function (item) {
				let fieldClass = `.field_box.${item}_box`;
				$(fieldClass).show();
				if ($.inArray(item, ['required', 'limit']) != -1) {
					$(fieldClass).find('input').attr('notnull', 'notnull');
				}
			});
			if ($.inArray(format, ['FirstName', 'LastName', 'Email']) != -1) {
				$('.fixed_field .type_box').hide();
				$('.fixed_field .required_box').hide();
			}
			if ($.inArray(format, ['Password', 'ConfirmPassword']) != -1) {
				$('.fixed_field .type_box').hide();
				$('.fixed_field .required_box').hide();
				$('.fixed_field .limit_box').hide();
			}
			$('.fixed_field').find('.content_box>.input').html('');
			let contentType = ['select', 'radio', 'checkbox'];
			if ($.inArray(type, contentType) != -1) {
				let content = $('.content_box').children('.input').data('content');
				if (content.length) {
					for (let key in content) {
						add_content(ContentHtml, content[key]);
					}
				} else {
					add_content(ContentHtml);
				}
			}
			frame_obj.check_amount($('#field_form'));
		}
		let add_content = ($Txt, $Name) => {
			$Txt = $Txt.replace('%N%', '');
			$('.fixed_field .content_box>.input').append($Txt);
			if ($Name) {
				$('.fixed_field .content_box .field_content_row:last').find(".box_input[name='content[]']").val($Name);
			}
			if ($('.field_content_row').length > 1) {
				$('.field_content_row:last').siblings().find('.button').addClass('hide_add').removeClass('hide_remove');
			} else {
				$('.field_content_row .button').addClass('hide_remove');
			}
		}
		let remove_content = (obj) => {
			obj.parents('.field_content_row').fadeOut(function () {
				$(this).remove();
				$('.field_content_row:last .button').removeClass('hide_add');
				if ($('.field_content_row').length == 1) {
					$('.field_content_row .button').addClass('hide_remove');
				}
			});
		}
		let fixed_right_func = () => {
			frame_obj.fixed_right($('.btn_field_add, .field_edit_btn'), '.fixed_field', function (obj) {
				let text = '';
				let id = 0;
				if (obj.hasClass('btn_field_add')) {
					text = lang_obj.global.add;
					id = maxAddId + 1;
				} else {
					text = lang_obj.global.edit;
					id = obj.parents('.field_item').data('id');
				}
				$('.fixed_field .top_title span').text(text);
				$('.fixed_field .field_container').addClass('loading').html('');
				let params = {};
				params.id = id;
				if (obj.parent().find('input').length) {
					let value = obj.parent().find('input').val();
					params.foramData = value;
				}
				$.post('/manage/set/user-set/get-field-info', params, function (data) {
					if (data.ret == 1) {
						$('.fixed_field .field_container').html(data.msg.fieldHtml).removeClass('loading');
						fixed_field_init();
					}
				}, 'json');
			});
		}
		let ContentHtml = ''; //字段内容html
		ContentHtml += '<div class="field_content_row">';
		ContentHtml += '	<div class="rows clean float">';
		ContentHtml += '		<label>' + lang_obj.manage.global.name + '</label>';
		ContentHtml += '		<div class="input"><input name="content[]" value="%N%" type="text" class="box_input" size="38" notnull></div>';
		ContentHtml += '	</div>';
		ContentHtml += '	<div class="float button">';
		ContentHtml += '		<a href="javascript:;" class="btn_option fl btn_option_add"><i></i></a>';
		ContentHtml += '		<a href="javascript:;" class="btn_option fl btn_option_remove"><i></i></a>';
		ContentHtml += '	</div>';
		ContentHtml += '</div>';
		let fieldData = $('#field_list').data('field');
		let maxAddId = 0;
		field_init();
		fixed_right_func();
		//删除字段
		$('body').on('click', '.field_list .field_del_btn', function () {
			let params = {
				'title': lang_obj.manage.tips.user_field_del_tips,
				'confirmBtn': lang_obj.global.del,
				'confirmBtnClass': 'btn_warn'
			};
			let _this = $(this);
			global_obj.win_alert(params, function () {
				_this.parents('.field_item').remove();
			}, 'confirm');
		});
		// 下拉
		frame_obj.global_select_box();
		$('body').on('click', '.global_select_box .select_ul .item', function () {
			fixed_field_init();
		});
		// 内容
		$('body').on('click', '.field_box .btn_option_add', function () {
			add_content(ContentHtml, '');
		});
		$('body').on('click', '.field_box .btn_option_remove', function () {
			remove_content($(this));
		});
		// 弹窗提交
		$('body').on('click', '#field_form .btn_submit', function () {
			let form = $('#field_form');
			let formData = form.serialize();
			if (global_obj.check_form(form.find('*[notnull]'), form.find('*[format]'), 1)) return false;
			$.post('/manage/set/user-set/save-field-info', formData, function (data) {
				if (data.ret == 1) {
					let id = parseInt(data.msg.id);
					let obj = $(`#field_list .field_item[data-id='${id}']`);
					if (obj.length) {
						let html = field_html(data.msg.inputData);
						$(`#field_list .field_item[data-id='${id}']`).replaceWith(html);
					} else {
						maxAddId = id;
						add_field_html(data.msg.inputData);
					}
					$('.fixed_field .btn_cancel').trigger('click');
					fixed_right_func();
				}
			}, 'json');
		});

		// 排序
		frame_obj.dragsort($('.field_list'), '', '.field_item em', '', '<div class="field_item"></div>');

		$('.shopping_edit_form .verification_email_box .input_radio_side_box').on('click', function () {
			let val = $(this).find('input').val()
			if (val == 'manual') {
				$('.review_item_manual').removeClass('hide')
				$('.review_item_email').addClass('hide')
			} else {
				$('.review_item_manual').addClass('hide')
				$('.review_item_email').removeClass('hide')
			}
		})
		frame_obj.switchery_checkbox(() => { }, () => { }, '.rejected_switchery')
	},

	/******************* 分享栏目 *******************/
	config_share_edit: function () {
		$('.btn_add_share').on('click', function () {
			var allcount = parseInt($('.share_tpl').attr('data-count')),
				length = parseInt($('.share_box .share_item').length);
			if (length < allcount) {
				$('.share_box').append($('.share_tpl').html());
			}
			is_add();
		});
		$('#share_edit_form').on('change', 'select[name=tax_code_type]', function () {
			var $value = $(this).val();
			$old_value = $(this).parents('.share_item').find('.share_del').attr('data-share');
			$('select[name=tax_code_type]>option[value=' + $old_value + ']').removeClass('hide').removeAttr('disabled');
			if ($value == 0) {
				$(this).parents('.share_item').find('input').attr('name', '');
				$(this).parents('.share_item').find('.share_del').attr('data-share', '');
			} else {
				$(this).parents('.share_item').find('input').attr('name', 'Share' + $value);
				$(this).parents('.share_item').find('.share_del').attr('data-share', $value);
				$('select[name=tax_code_type]>option[value=' + $value + ']').addClass('hide').attr('disabled', 'disabled');
				$(this).find('option[value=' + $value + ']').removeClass('hide').removeAttr('disabled');
			}
		}).on('click', '.share_del', function () {
			var $this = $(this),
				$value = $this.attr('data-share');
			let params = {
				'title': lang_obj.global.del_confirm,
				'confirmBtn': lang_obj.global.del,
				'confirmBtnClass': 'btn_warn'
			};
			global_obj.win_alert(params, function () {
				$this.parents('.share_item').remove();
				$('select[name=tax_code_type]>option[value=' + $value + ']').removeClass('hide').removeAttr('disabled');
				is_add();
			}, 'confirm');
		});
		function is_add() {
			var allcount = parseInt($('.share_tpl').attr('data-count')),
				length = parseInt($('.share_box .share_item').length);
			if (length >= allcount) {
				$('.btn_add_share').hide();
			} else {
				$('.btn_add_share').show();
			}
		}
		is_add();
		frame_obj.submit_form_init($('#share_edit_form'), '/Setting/Index');
	},
	/******************* 产品评论 *******************/
	config_review_edit: function () {
		//勾选按钮(产品评论)
		frame_obj.config_switchery($('.review_checkbox .switchery'), 'set.config_review_used_edit', 'data-type', 'type');
	},

	/******************* SEO设置 *******************/
	config_seo_edit: function () {
		// frame_obj.submit_form_init($('#seo_edit_form'), './?m=set&a=config&d=seo');
	},

	/******************* 邮箱设置 *******************/
	config_email_edit: function () {
		var smtpemail = $('#email_config_form').find('input[name=SmtpUserName]');
		smtpemail.blur(function () {
			var match_ret = /^([\w-_]+(?:\.[\w-_]+)*)@((?:[a-z0-9]+(?:-[a-zA-Z0-9]+)*)+)\.[a-z]{2,6}$/i,
				email_str = $.trim($(this).val()),
				match_result = email_str.match(match_ret), //匹配邮箱类型
				smtp_data = $('#email_config_form').data('smtp'),
				smtp_array = new Array();
			for (let i in smtp_data) {
				let o = {};
				o[i] = smtp_data[i];
				smtp_array.push(o)
			}

			if (match_result) {
				var num = 0;
				$.each(smtp_array, function (index, value) {
					$.each(value, function (itemindex, item) {
						if (itemindex == match_result[2]) {
							$('#email_config_form input[name=SmtpHost]').attr('value', item[0]);
							$('#email_config_form input[name=SmtpPort]').attr('value', item[1]);
							$('#email_config_form input[name=SmtpType]').attr('value', itemindex);
							$('#email_config_form').find('.change_smtp_list').addClass('hide');
							$('#email_config_form .smtpauto').addClass('hide');
							$('#email_config_form input[name=IsSmtpBusiness]').attr('value', '');
							num = num + 1;
						}
					})
				})
			}
			if (!num) {
				$('#email_config_form').find('.change_smtp_list').removeClass('hide');
			}
		});
		$('.change_smtp_item').on('click', function () {
			var $this_host = $(this).data('host'),
				$this_port = $(this).data('port');
			$this_type = $(this).data('type');
			if ($this_host && $this_port) {
				$('#email_config_form input[name=SmtpHost]').attr('value', $this_host);
				$('#email_config_form input[name=SmtpPort]').attr('value', $this_port);
				$('#email_config_form input[name=SmtpType]').attr('value', $this_type);
				$('#email_config_form .smtpauto').addClass('hide');
			} else {
				$('#email_config_form .smtpauto').removeClass('hide');
				$('#email_config_form input[name=SmtpType]').attr('value', $this_type);
				//$('#email_config_form input[name=SmtpHost]').attr('value','');
				//$('#email_config_form input[name=SmtpPort]').attr('value','');

			}
			$('#email_config_form input[name=IsSmtpBusiness]').attr('value', 1);
			$(this).addClass('current').siblings().removeClass('current');

		})
	},
	/******************* 邮件设置 *******************/
	config_email_templete_edit: function () {

		//重置
		$('#email_edit_form .mail_reset').click(function () {
			global_obj.win_alert(lang_obj.manage.global.reset_tips, function () {
				var $template = $('#email_edit_form input[name="template"]').val();
				let $language = $('#email_edit_form select[name="EmailLanguage"]').val();
				$.post('/manage/set/email/templete-reset', { template: $template, language: $language }, function (data) {
					location.href = location.href;
				}, 'json');
			}, 'confirm');
			return false;
		});

		//预览
		$('#email_edit_form .mail_preview').click(function () {
			var $template = $('#email_edit_form input[name="template"]').val();
			let $language = $('#email_edit_form select[name="EmailLanguage"]').val();
			$.post('/Api/Setting/EmailTemplatePreview', { template: $template, language: $language }, function (data) {
				global_obj.div_mask();
				$('#mail_preview_box').fadeIn().find('.overflow_y_auto').html(data.msg).height($('#mail_preview_box').height());
			}, 'json');
		});

		$('html').on('click', '#div_mask, #mail_preview_box .btn_close', function () {
			if ($('.win_alert').length > 0) {
				$('.win_alert').remove()
				global_obj.div_mask(1);
			}
			$('#mail_preview_box, #div_mask').fadeOut(function () {
				global_obj.div_mask(1);
			});
		});

		frame_obj.submit_form_init($('#email_edit_form'), '', '', '', function (data) {
			if (data.ret == 1 && data.msg.location) {
				window.location.href = data.msg.location;
			} else {
				window.location.href = '/Setting/Email/';
			}
		});

		//勾选按钮(邮件设置)
		frame_obj.config_switchery($('.used_checkbox .switchery'), '/manage/set/email/used-edit', 'data-type', 'template');

		// 发件人设置
		frame_obj.fixed_right($('.email_config_box .send_config_set'), '.fixed_send_person');

		frame_obj.global_select_box();
		frame_obj.fixed_right($('.email_config_box .manage_email_add, .email_config_box .icon_edit'), '.fixed_manager_send', function ($this) {
			var UserId = parseInt($this.attr('data_id'));
			if (UserId > 0) {
				$.post('/manage/set/email/get-manager-info', { UserId: UserId }, function (result) {
					setConfigForm(result.msg);
				}, 'json');
			} else {
				setConfigForm();
			}
		});

		var setConfigForm = function (result) {
			var _this = $('#managerAddForm');
			EmailPermit = result ? result.EmailPermit : [];
			Email = result ? result.Email : '';
			UserId = result ? result.UserId : 0;

			if (Email) {
				_this.find('input[name=Email]').val(Email);
			} else {
				_this.find('input[name=Email]').val('');
			}

			if (UserId) {
				Name = $('#config .email_config_box .email_config_table tr[data_id="' + UserId + '"] .name span').text();
				_this.find('.global_select_box').addClass('disabled').find('.imitation_select').attr('readonly', 'readonly');
				_this.find('.select_ul').find('li[data-value="' + UserId + '"]').addClass('selected').siblings().removeClass('selected');
				_this.find('input[name=UserId]').val(UserId).prev().val(Name);
			} else {
				_this.find('.global_select_box').removeClass('disabled').find('.imitation_select').removeAttr('readonly', 'readonly');
				_this.find('.select_ul').find('li').removeClass('selected');
				_this.find('input[name=UserId]').val(0).prev().val('');
			}

			$('input[name=Permit\\[\\]]').prop('checked', false).parents('.input_checkbox_box').removeClass('checked')
			if (EmailPermit) {
				for (item in EmailPermit) {
					Data = EmailPermit[item];
					$('#managerAddForm .permit_list .item input[value="' + Data + '"]').prop('checked', true).parents().addClass('checked');
				}
			}
		};

		frame_obj.submit_form_init($('#managerAddForm'), '', function () {
			let $email = $('#managerAddForm').find('input[name=Email]').val();
			$format = /^\w+[a-zA-Z0-9-.+_]+@[a-zA-Z0-9-.+_]+\.\w*$/,
				tips = "";
			if ($email == "" || $format.test($email) === false) {
				tips = lang_obj.format.email;
			}
			if (tips) {
				global_obj.win_alert_auto_close(tips, 'fail', 1000, '8%');
				return false;
			}
		}, 0, function (data) {
			if (data.ret == 1) {
				global_obj.win_alert_auto_close(lang_obj.global.save_success, '', 1000, '8%');
				window.location.reload()
			} else {
				global_obj.win_alert_auto_close(data.msg, 'fail', 1000, '8%');
			}
		})
		frame_obj.del_init($('#config .r_con_table'));


		frame_obj.submit_form_init($('#send_person_form'), '', '', 0, function (data) {
			if (data.ret == 1) {
				global_obj.win_alert_auto_close(lang_obj.global.save_success, '', 1000, '8%');
				window.location.reload()
				$('#fixed_right .btn_cancel').click();
			} else {
				global_obj.win_alert_auto_close(data.msg, 'fail', 1000, '8%');
			}
		});

		$('.global_app_tips a').on('click', function () {
			$Type = $(this).parents('.global_app_tips').attr('data-type');
			if ($Type) {
				$.post('/manage/plugins/app/install', { 'ClassName': $Type }, function (data) {
					if (data.ret == 1) {
						window.open(data.msg);
					} else {
						global_obj.win_alert_auto_close(data.msg, 'fail', 1000, '8%');
					}
				}, 'json')
				return false;
			}
		})

		$('.switch_box .switch_btn').click(function () {
			$(this).hide().siblings().show();
			if ($(this).hasClass('open')) {
				$('.switch_box ul').show();
			} else {
				$('.switch_box ul').hide();
			}
		});

		$('.switch_box .box_type_menu .item').click(function () {
			if ($(this).hasClass('disable')) { return false; }
			$(this).parents('ul').find('.item').removeClass('checked').find('input').prop('checked', false);
			$(this).addClass('checked').find('input').prop('checked', 'checked');
			if ($(this).find('input').val() == 'Customize') {
				$('.customize_box').show();
			} else {
				$('.customize_box').hide();
			}
			if ($(this).find('input').val() == 'Sendcloud') {
				$('.sendcloud_box').show();
			} else {
				$('.sendcloud_box').hide();
			}
		})

		$('select[name=SmtpType]').on('change', function () {
			var $this_host = $(this).find('option:selected').attr('data-host'),
				$this_port = $(this).find('option:selected').attr('data-port');
			$this_type = $(this).find('option:selected').attr('data-type');
			if ($this_host && $this_port) {
				$('#send_person_form input[name=SmtpHost]').attr('value', $this_host);
				$('#send_person_form input[name=SmtpPort]').attr('value', $this_port);
				//$('#send_person_form input[name=SmtpType]').attr('value',$this_type);
			} else {
				$('#send_person_form input[name=SmtpHost]').attr('value', '');
				$('#send_person_form input[name=SmtpPort]').attr('value', '');

			}
			$('#email_config_form input[name=IsSmtpBusiness]').attr('value', 1);
			$(this).addClass('current').siblings().removeClass('current');

		})

		if ($('.toggle_language').length) {
			let _contentEditor = CKEDITOR.instances.Content;
			var _isModifiedEditor = false;
			_contentEditor.on('key', function () {
				_isModifiedEditor = true;
			})
		}

		$('.toggle_language').on('change', 'select[name=EmailLanguage]', function () { //切换 语言
			let _thisValue = $(this).val(),
				_formParent = $('#email_edit_form'),
				_thisTemplate = _formParent.find('input[name=template]').val(),
				_currentLanguage = _formParent.find('input[name=language_default]').val();
			if (_isModifiedEditor) {
				let params = {
					'title': lang_obj.manage.set.toggle_tips,
					'confirmBtn': lang_obj.manage.set.continue,
					'confirmBtnClass': 'btn_warn'
				};
				global_obj.win_alert(params, function () {
					emailRequest(_thisValue, _thisTemplate)
				}, 'confirm', undefined, '', function () {
					$('.toggle_language select[name=EmailLanguage]').val(_currentLanguage)
				});
			} else {
				emailRequest(_thisValue, _thisTemplate)
			}
		})

		function emailRequest(_thisValue, _thisTemplate) {
			let _formParent = $('#email_edit_form');
			$.post('/manage/set/email/get-email-language-content/', { 'EmailLanguage': _thisValue, "Template": _thisTemplate }, function (data) {
				if (data.ret == 1) {
					let _getTitle = data.msg.Title,
						_getContent = data.msg.Content;
					if (_getTitle) {
						_formParent.find('input[name=Title]').val(_getTitle);
					}
					if (_getContent) {
						let editorObj = CKEDITOR.instances.Content
						editorObj && editorObj.setData(_getContent)
						_isModifiedEditor = false
					}
				}
			}, 'json')
		}

		frame_obj.fixed_right($('.btn_merchant_detail'), '.fixed_merchant_view', (e) => {
			let $obj = $('.fixed_merchant_view')
			let $itemObj = e.parents('.table_item')
			let $name = $itemObj.find('.merchant_title').text()
			let $type = e.attr('data-type')
			let $fixedRight = $('#fixed_right')

			$obj.find('.top_title strong').text($name)
			$obj.find('input[name="type"]').val($type)
			$obj.find('.r_con_table tbody').html('')
			$obj.find('.global_app_tips, .box_table, .bg_no_table_data').hide()

			$fixedRight.attr('class', 'loading')

			$.post('/manage/set/email/merchant-info', { 'type': $type },
				(result) => {
					if (result.ret == 1) {
						let html = ''
						for (let k in result.msg) {
							let tpl = `
							<tr data-id="{{id}}">
								<td nowrap>
									<div class="email">{{email}}</div>
									<span class="name">{{name}}</span>
								</td>
								<td nowrap align="center">
									<div class="used_checkbox">
										<div class="switchery {{checked}}" data-type="{{type}}">
											<input type="checkbox" name="Allow[]" value="{{id}}" {{checked}} />
											<div class="switchery_toggler"></div>
											<div class="switchery_inner">
												<div class="switchery_state_on"></div>
												<div class="switchery_state_off"></div>
											</div>
										</div>
									</div>
								</td>
							</tr>
							`
							html += tpl.replace(/({{id}}|{{email}}|{{name}}|{{checked}})/gi, (e) => {
								return {
									"{{id}}": result.msg[k].id || 0,
									"{{email}}": result.msg[k].email || '',
									"{{name}}": result.msg[k].name || '',
									"{{type}}": $type || '',
									"{{checked}}": ((result.msg[k].isused || 0) == 1 ? 'checked' : ''),
								}[e]
							})
						}
						$obj.find('.r_con_table tbody').html(html)
						if (html) {
							$obj.find('.bg_no_table_data').hide()
							$obj.find('.global_app_tips, .box_table').show()
						} else {
							$obj.find('.bg_no_table_data').show()
							$obj.find('.global_app_tips, .box_table').hide()
						}
						$fixedRight.removeAttr('class')

						$obj.off().on('click', '.switchery', function () {
							let $this = $(this)
							if ($this.hasClass('checked')) {
								$this.removeClass('checked').find('input').prop('checked', false)
							} else {
								$this.addClass('checked').find('input').prop('checked', true)
							}
						})
					} else {
						global_obj.win_alert_auto_close(result.msg, 'await', 1000, '8%')
					}
				},
				'json'
			)
		})

		frame_obj.submit_form_init($('#merchant_edit_form'), '', '', '', (result) => {
			if (result.ret == 1) {
				global_obj.win_alert_auto_close(lang_obj.global.save_success, '', 1000, '8%')
				$('.fixed_merchant_view .close').trigger('click')

				let $type = $('#merchant_edit_form input[name="type"]').val()
				let $tipsObj = $(`.email_merchant_box .table_item[data-type="${$type}"] .global_app_tips`)
				if (result.msg > 0) $tipsObj.addClass('hide')
				else $tipsObj.removeClass('hide')
			} else {
				global_obj.win_alert_auto_close(lang_obj.manage.global.update_status[2], 'fail', 1000, '8%')
			}
		})

		let getMerchant = global_obj.query_get('merchant'); // GET参数merchant
		if (getMerchant) {
			let $obj = $(`.email_merchant_box .table_item[data-type="${getMerchant}"]`)
			if ($obj.length) {
				$obj.find('.btn_merchant_detail').click()
			}
		}

	},

	config_email_recipients: function () {
		frame_obj.del_init($('#recipients .r_con_table'))

		frame_obj.fixed_right($('.btn_recipients'), '.fixed_recipients_edit', (e) => {
			let $obj = $('.fixed_recipients_edit')
			let id = parseInt(e.attr('data-id'))

			// 先清空内容
			let $box = $obj.find('.simulate_select_box')
			$box.find('.select .selected').remove()
			$box.find('.select .placeholder').show()
			$box.find('.option_list .item.current').removeClass('current')
			$obj.find('.choose_list .btn_checkbox').removeClass('current').find('input').prop('checked', false)
			$obj.find('input[name="Email"]').val('')

			$obj.find('input[name="id"]').val(id)

			if (id > 0) {
				$obj.find('.top_title strong').text(lang_obj.manage.email.modify_recipient)

				$.post('/manage/set/email/recipients-info', { 'id': id },
					(result) => {
						if (result.ret == 1) {
							let $html = '<span class="selected" data-value="' + result.msg.name + '">' + result.msg.name + '<input type="hidden" name="Manager" value="' + result.msg.name + '"><i></i></span>'
							$box.find('.select').append($html).find('.placeholder').hide()
							$obj.find('input[name="Email"]').val(result.msg.email)
							$obj.find('.choose_list .choose_item').each((index, element) => {
								let value = $(element).find('input').val()
								if (result.msg.premit[value]) {
									$(element).find('.btn_checkbox').addClass('current').find('input').prop('checked', true)
								}
							})
						} else {
							global_obj.win_alert_auto_close(result.msg, 'await', 1000, '8%')
						}
					},
					'json'
				)
			} else {
				$obj.find('.top_title strong').text(lang_obj.manage.email.add_recipient)
			}
		})

		// 模拟下拉插件		
		$('body').click((e) => {
			// 关闭
			$('.simulate_select_box .select').removeClass('focus').siblings('.option_box').hide()
		})
		$('.simulate_select_box')
			.on('click', (e) => {
				// 阻止事件冒泡
				e.stopPropagation()
			})
			.on('click', '.select', function () {
				// 点击打开
				$(this).addClass('focus').siblings('.option_box').show()
			})
			.on('click', '.btn_refresh', function () {
				// 刷新
				let $this = $(this)
				let $box = $this.parents('.simulate_select_box')
				let $parent = $box.find('.option_box')
				let $perpage = parseInt($parent.find('.btn_load_more').attr('data-per-page'))
				$this.addClass('refreshing')
				$.get('/api/manage/set/email/manager-list', { 'page': 1, 'per-page': $perpage },
					(data) => {
						if (data.ret == 1) {
							$parent.find('.option_list .item').remove()
							$parent.find('.btn_load_more').attr('data-page', 2).before(data.msg.html)
							// 加上选择效果 失效的删掉
							$box.find('.select .selected input').each((index, element) => {
								let $item = $parent.find('.item[data-value="' + $(element).val() + '"]')
								$item.addClass('current')
								if ($item.hasClass('disabled')) $(this).parent().remove()
							})
							if (!$box.find('.select .selected input').length) $box.find('.select .placeholder').show()
							$parent.find('.btn_load_more').css('display', (data.msg.pageCount > 1 ? 'block' : 'none'))
							if (data.msg.total > 0) {
								$parent.find('.option_list').show()
								$parent.find('.no_data').hide()
							} else {
								$parent.find('.option_list').hide()
								$parent.find('.no_data').show()
							}
							$this.removeClass('refreshing')
						}
					},
					'json'
				)
			})
			.on('click', '.option_list .item:not(.disabled)', function () {
				let $this = $(this)
				let $parent = $this.parent()
				let $box = $this.parents('.simulate_select_box')
				let $type = $box.attr('data-type')
				let $val = $this.attr('data-value')
				let $html = ''
				let $is_used = $this.parents('.input').find('input[name="IsUsed"]')
				$is_used.prop('checked', false).parents('.input_checkbox_box').removeClass('checked')
				if ($type == 'checkbox') {
					$(this).addClass('current')
					if (!$box.find('.select .selected[data-value="' + $val + '"]').length) {
						$html = '<span class="selected" data-value="' + $val + '">' + $val + '<input type="hidden" name="' + $parent.attr('data-name') + '[]' + '" value="' + $val + '"><i></i></span>'
						$box.find('.select').append($html).find('.placeholder').hide()
					}
				} else if ($type == 'select') {
					$(this).addClass('current').siblings().removeClass('current')
					$box.find('.select .selected').remove()
					$html = '<span class="selected" data-value="' + $val + '">' + $val + '<input type="hidden" name="' + $parent.attr('data-name') + '" value="' + $val + '"><i></i></span>'
					$box.find('.select').append($html).find('.placeholder').hide()
					$this.parents('.manager_box').removeClass('has_error').find('.error_tips').remove()
				}
			})
			.on('click', '.btn_load_more', function () {
				//加载更多
				let $this = $(this)
				let $box = $this.parents('.simulate_select_box')
				let $page = parseInt($this.attr('data-page'))
				let $perpage = parseInt($this.attr('data-per-page'))
				$.get('/api/manage/set/email/manager-list', { 'page': $page, 'per-page': $perpage },
					(data) => {
						if (data.ret == 1) {
							$this.attr('data-page', $page + 1)
							$this.before(data.msg.html)
							// 加上选择效果 失效的删掉
							$box.find('.select .selected input').each((index, element) => {
								let $item = $box.find('.option_list .item[data-value="' + $(element).val() + '"]')
								$item.addClass('current')
								if ($item.hasClass('disabled')) {
									$(element).parent().remove()
								}
							})
							if (!$box.find('.select .selected input').length) $box.find('.select .placeholder').show()
							if (data.msg.page >= data.msg.pageCount) {
								// 最后一页
								global_obj.win_alert_auto_close(lang_obj.manage.sales.lastpage, 'await', 1000, '8%')
								$this.fadeOut()
							}
						}
					},
					'json'
				)
			})
			.on('click', '.select .selected i', function () {
				// 删除选项
				let $this = $(this)
				let $val = $this.parent().find('input').val()
				let $box = $this.parents('.simulate_select_box')
				$this.parent().remove()
				if (!$box.find('.select .selected').length) $box.find('.select .placeholder').show()
				$box.find('.option_list .item[data-value="' + $val + '"]').removeClass('current')
				return false // 阻止冒泡
			})
		$('.simulate_select_box .btn_refresh').click() // 默认点击

		frame_obj.submit_form_init($('#recipients_edit_form'), '', () => {
			let totalError = 0
			let $form = $('#recipients_edit_form')
			let $mBox = $form.find('.manager_box')
			let $box = $form.find('.simulate_select_box')
			let $emailObj = $form.find('input[name="Email"]')
			let $emailBox = $emailObj.parent()

			if (!$box.find('.select .selected input').length) {
				let tips = lang_obj.manage.email.select_recipient_tips
				$mBox.addClass('has_error')
				if ($mBox.find('.error_tips').length) $mBox.find('.error_tips').text(tips)
				else $mBox.append('<p class="error_tips">' + tips + '</p>')
				totalError += 1
			} else {
				$mBox.removeClass('has_error').find('.error_tips').remove()
			}

			let $email = $.trim($emailObj.val())
			let emailTips = ''
			if ($email == '') {
				emailTips = lang_obj.manage.email.enter_email_tips
			} else if (/^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/.test($email) == false) {
				emailTips = lang_obj.manage.email.email_format_tips
			}
			if (emailTips) {
				$emailBox.addClass('has_error')
				if ($emailBox.find('.error_tips').length) $emailBox.find('.error_tips').text(emailTips)
				else $emailBox.append('<p class="error_tips">' + emailTips + '</p>')
				totalError += 1
			} else {
				$emailBox.removeClass('has_error').find('.error_tips').remove()
			}

			if (totalError > 0) return false

		}, '', (result) => {
			if (result.ret == 1) {
				global_obj.win_alert_auto_close(lang_obj.global.save_success, '', 1000, '8%')
				setTimeout(() => {
					window.location.reload()
				}, 800)
			} else if (result.ret == -1) {
				let $mBox = $('#recipients_edit_form').find('.manager_box')
				$mBox.addClass('has_error')
				if ($mBox.find('.error_tips').length) $mBox.find('.error_tips').text(result.msg)
				else $mBox.append('<p class="error_tips">' + result.msg + '</p>')
			} else if (result.ret == -2) {
				let $emailBox = $('#recipients_edit_form').find('input[name="Email"]').parent()
				$emailBox.addClass('has_error')
				if ($emailBox.find('.error_tips').length) $emailBox.find('.error_tips').text(result.msg)
				else $emailBox.append('<p class="error_tips">' + result.msg + '</p>')
			} else {
				global_obj.win_alert_auto_close(lang_obj.manage.global.update_status[2], 'fail', 1000, '8%')
			}

		})
	},

	/******************* 支付方式 *******************/
	payment_init: function () {
		let $ppSignObj = $('.btn_paypal_sign')
		if ($ppSignObj.length) {
			let paypal_sign_permit = $ppSignObj.data('permit');
			let paypal_ppcp = $ppSignObj.data('ppcp');
			if ($ppSignObj.hasClass('btn_disabled')) {
				$ppSignObj.css({ 'display': 'inline-block', 'border-color': '#f2f2f2' });
				$ppSignObj.removeAttr('target');
				return false;
			}
			if (paypal_ppcp) {
				// 拥有PPCP操作权限
				frame_obj.fixed_box_popup({
					'clickObj': $('.btn_paypal_sign'),
					'targetClass': 'box_paypal_onboarding'
				});
				$.post('/manage/set/payment/onboarding-partner-referrals/', '', (result) => {
					if (result.ret == 1) {
						for (let k in result.msg) {
							let $btnObj = $(`#click_onboarding_${k}`)
							if (paypal_sign_permit) {
								$btnObj.attr('href', result.msg[k] + '&displayMode=minibrowser');
							} else {
								$btnObj.removeAttr('target');
							}
						}
						$ppSignObj.css('display', 'inline-block').removeAttr('target');
					} else {
						result.msg && global_obj.win_alert(result.msg);
					}
				}, 'json');
			} else {
				// 只有PayPal结账操作权限
				$.post('/manage/set/payment/onboarding-partner-referrals/', '', (result) => {
					if (result.ret == 1) {
						if (paypal_sign_permit) {
							$ppSignObj.attr('href', result.msg.href + '&displayMode=minibrowser');
						} else {
							$ppSignObj.removeAttr('target');
						}
						$ppSignObj.css('display', 'inline-block');
					} else {
						result.msg && global_obj.win_alert(result.msg);
					}
				}, 'json');
			}
		}
		frame_obj.fixed_right($('.payment_sort'), '.payment_sort_box', function ($this) { });
		frame_obj.dragsort($('.payment_sort_dragsort'), '/manage/set/payment/order/', '.order_list', '', '<div class="order_list placeHolder"></div>'); //元素拖动

		$(".btn_paypal_more").on("click", function () {
			if ($(this).hasClass("hover")) {
				$(this).removeClass("hover");
				$(".paypal_info").slideUp();
			} else {
				$(this).addClass("hover");
				$(".paypal_info").slideDown();
			}
		});

		frame_obj.fixed_right($('.btn_try_activate'), '.paypal_try_box', function ($this) {
			let $obj = $(".paypal_try_box"),
				$isUsed = $(".paypal_try").attr("data-used"),
				$email = $.trim($(".paypal_try").attr("data-email")),
				submitTxt = "";
			$obj.find("input[name=Email]").val($email);
			if ($isUsed == 1) {
				// 已激活
				submitTxt = lang_obj.manage.global.save;
				$obj.find(".btn_disable").show();
			} else {
				// 未激活
				submitTxt = lang_obj.manage.global.start_trial;
				$obj.find(".btn_disable").hide();
			}
			$obj.find(".btn_submit").val(submitTxt);
		});
		frame_obj.submit_form_init($('#paypal_try_form'), '', function () {
			let $emailObj = $(".paypal_try_box input[name=Email]"),
				$email = $.trim($emailObj.val()),
				$emailInput = $emailObj.parent(),
				$format = /^\w+[a-zA-Z0-9-.+_]+@[a-zA-Z0-9-.+_]+\.\w*$/,
				tips = "";
			if ($email == "" || $format.test($email) === false) {
				tips = lang_obj.format.email;
			}
			if (tips) {
				$emailInput.addClass("has_error");
				if ($emailInput.find(".error_tips").length) {
					$emailInput.find(".error_tips").text(tips);
				} else {
					$emailInput.append("<p class=\"error_tips\">" + tips + "</p>");
				}
				return false;
			} else {
				$emailInput.removeClass("has_error").find(".error_tips").remove();
			}
		}, '', function (result) {
			if (result.ret == 1) {
				// 操作成功
				let $email = $(".paypal_try_box input[name=Email]").val();
				$('.paypal_try').attr("data-email", $email);
				$('.paypal_try').attr("data-used", 1);
				$('.btn_try_activate').text(result.data.buttonText);
				global_obj.win_alert_auto_close(result.msg, '', 1000, '8%');
			} else {
				// 操作失败
				global_obj.win_alert_auto_close(lang_obj.global.set_error, 'fail', 1000, '8%');
			}
			$('#fixed_right .top_title .close').click();
		});
		$('#paypal_try_form .btn_disable').click(function () {
			$.get("/manage/set/payment/try-disable", function (result) {
				$('.paypal_try').attr("data-used", 0);
				$('.btn_try_activate').text(result.data.buttonText);
				global_obj.win_alert_auto_close(result.msg, '', 1000, '8%');
				$('#fixed_right .top_title .close').click();
			}, "json");
		});

		$('#btn_remind_again').on('click', () => {
			$.post('/manage/set/payment/dont-remind-again', '', (result) => {
				if (result) {
					global_obj.win_alert_auto_close(result.msg, '', 1000, '8%')
					$('.paypal_notice').fadeOut(500)
				}
			}, 'json')
		})
	},
	payment_ppcp_init: function (type = '') {
		// PayPal校验
		if ($('.payment_box:eq(0) .btn_payment_set').length) {
			$.post('/manage/set/payment/seller-status', '', (result) => {
				let isPPCP = false
				if (result.msg.active) {
					isPPCP = true
				}
				if (isPPCP) {
					// 已开通PPCP功能
					console.log('>>>>>> PayPal 已开通PPCP功能 <<<<<<');
					if ($('.paypal_notice').length) {
						$('.paypal_notice').remove();
					}
				} else {
					// 未开通PPCP功能
					console.log('>>>>>> PayPal 未开通PPCP功能 <<<<<<');
				}
				$.post('/manage/set/payment/check-ppcp', { isPPCP: (isPPCP ? 1 : 0) });
			}, 'json');
		}

		if ($('#payment_edit_form').length && $('#PId').val() == 1) {
			$.post('/manage/set/payment/seller-status', '', (result) => {
				let isPPCP = false
				if (result.msg.active) {
					isPPCP = true
				}
				if (isPPCP) {
					// 已开通PPCP功能
					console.log('>>>>>> PayPal 已开通PPCP功能 <<<<<<');
					if (type != 'set') global_obj.win_alert_auto_close(lang_obj.manage.payment.tips.opend_ppcp, 'success', 3000, '8%');
				} else {
					// 未开通PPCP功能
					console.log('>>>>>> PayPal 未开通PPCP功能 <<<<<<');
				}
				$.post('/manage/set/payment/check-ppcp', { isPPCP: (isPPCP ? 1 : 0) });

				for (let k in result.msg.capabilities) {
					let status = result.msg.capabilities[k];
					if (k == 'APPLE_PAY') {
						if (status == 'ACTIVE') {
							$('#item_applepay_button .switchery, #item_applepay_express .switchery').removeClass('disabled');
							$('.global_container[data-type="applepay"] .ppcp_tips').removeClass('show');
						} else {
							$('#item_applepay_button .switchery, #item_applepay_express .switchery').addClass('disabled').removeClass('checked').find('input').prop('checked', false);
							$('.global_container[data-type="applepay"] .ppcp_tips').addClass('show');
							$('#item_applepay_express').slideUp();
						}
					} else if (k == 'GOOGLE_PAY') {
						if (status == 'ACTIVE') {
							$('#item_googlepay_button .switchery, #item_googlepay_express .switchery').removeClass('disabled');
							$('.global_container[data-type="googlepay"] .ppcp_tips').removeClass('show');
						} else {
							$('#item_googlepay_button .switchery, #item_googlepay_express .switchery').addClass('disabled').removeClass('checked').find('input').prop('checked', false);
							$('.global_container[data-type="googlepay"] .ppcp_tips').addClass('show');
							$('#item_googlepay_express').slideUp();
						}
					} else if (k == 'CUSTOM_CARD_PROCESSING') {
						if (status == 'ACTIVE') {
							$('.box_credit_card_payment .item[data-type="embed"] .input_radio_box').removeClass('disabled');
							$('.global_container[data-type="standard"] .ppcp_tips').removeClass('show');
						} else {
							$('.box_credit_card_payment .item[data-type="embed"] .input_radio_box').addClass('disabled');
							$('.box_credit_card_payment .item[data-type="independent"] .input_radio_box').click();
							$('.global_container[data-type="standard"] .ppcp_tips').addClass('show');
						}
					} else if (k == 'PAYPAL_WALLET_VAULTING_ADVANCED') {
						if (status == 'ACTIVE') {
							$('#item_vault .switchery').removeClass('disabled');
							$('#item_vault .ppcp_tips').removeClass('show');
						} else {
							$('#item_vault .switchery').addClass('disabled').removeClass('checked').find('input').prop('checked', false);
							$('#item_vault .ppcp_tips').addClass('show');
						}
					}
				}
			}, 'json');
		}
	},
	payment_list_init: function () {
		$('.payment_item .btn_submit').on('click', function () {
			if ($(this).hasClass('btn_disabled')) return false;
		});
		var clipboard = new ClipboardJS('.btn_copy');
		clipboard.on('success', function (e) {
			alert(lang_obj.global.copy_complete);
		});
	},
	payment_edit_init: function () {
		frame_obj.box_type_menu(function ($this) {
			let attributeData = $("#attributeData").val() || "";
			let params = $this.find("input").attr("data-value") || "";
			let html = "";
			if (params !== "") {
				attributeData = global_obj.json_encode_data(attributeData);
				params = global_obj.json_encode_data(params);
				$.each(params, function (index, element) {
					html += '<div>';
					html += '<label class="fl">' + element + '</label>';
					html += '<div class="blank6"></div>';
					html += '<div class="input">';
					html += '<input type="text" class="box_input full_input" name="Value[]" value="' + (attributeData[element] || "") + '" size="40" />';
					html += '<input type="hidden" name="Name[]" value="' + element + '" />';
					html += '</div>';
					html += '<div class="blank15"></div>';
					html += '</div>';
				});
				$("#payment_edit_form .account_info").html(html);
			}
		});
		$('#payment_edit_form .pay_del, .fixed_btn_submit .pay_del').on('click', function (e) {
			let $url = $(this).data("url"),
				$type = $(this).attr('data-type'),
				params = {
					"title": $type == 'disable' ? lang_obj.global.disable_confirm : lang_obj.global.del_confirm,
					"confirmBtn": $type == 'disable' ? lang_obj.global.disable : lang_obj.global.del,
					"confirmBtnClass": "btn_warn"
				};
			global_obj.win_alert(params, function () {
				$.get($url, function (data) {
					if (data.ret == 1) {
						global_obj.win_alert_auto_close(data.msg, "", 400, "8%");
						setTimeout(function () {
							location.href = "/Setting/Payment/";
						}, 400);
					}
				}, "json");
			}, "confirm");
			return false;
		});
		//支付接口图片上传
		frame_obj.mouse_click($('#LogoDetail .upload_btn, #LogoDetail .pic_btn .edit'), 'img', function ($this) {
			//点击上传图片
			frame_obj.photo_choice_init('LogoDetail', '', 1);
		});
		frame_obj.switchery_checkbox((obj) => {
			if (obj.hasClass('no_drop')) console.log('111111')
			if (obj.find('input[name=IsExpressCheckout]').length) {
				$('#item_express_credit_card').slideDown()
			} else if (obj.find('input[name=IsLocalPayment]').length) {
				$('.box_extend_content').slideDown()
			} else if (obj.find('input[name=IsGooglepayButton]').length) {
				$('#item_googlepay_express').slideDown()
			} else if (obj.find('input[name=IsApplepayButton]').length) {
				$('#item_applepay_express').slideDown()
			} else if (obj.find('input[name=IsDescription]').length) {
				$('#editor_description').slideDown()
			} else if (obj.find('input[name=IsCreditCardPay]').length && $('.box_credit_card_payment').length) {
				$('.box_credit_card_payment').slideDown()
			}
		}, (obj) => {
			if (obj.find('input[name=IsExpressCheckout]').length) {
				$('#item_express_credit_card').slideUp()
			} else if (obj.find('input[name=IsLocalPayment]').length) {
				$('.box_extend_content').slideUp()
			} else if (obj.find('input[name=IsGooglepayButton]').length) {
				$('#item_googlepay_express').slideUp()
			} else if (obj.find('input[name=IsApplepayButton]').length) {
				$('#item_applepay_express').slideUp()
			} else if (obj.find('input[name=IsDescription]').length) {
				$('#editor_description').slideUp()
			} else if (obj.find('input[name=IsCreditCardPay]').length && $('.box_credit_card_payment').length) {
				$('.box_credit_card_payment').slideUp()
			}
		});
		$('.payment_limit .input_checkbox_box').click(function () {
			let $obj = $(this);
			if ($obj.hasClass('checked')) {
				$obj.parents('.payment_limit').find('input[name=MaxPrice]').removeAttr('readonly')
			} else {
				$obj.parents('.payment_limit').find('input[name=MaxPrice]').attr('readonly', 'readonly')
			}
		});
		frame_obj.submit_form_init($('#payment_edit_form'), '', function () {
			// 价格检测
			let $priceObj = $("input[name=MaxPrice]").parent().parent(),
				$minPrice = $("input[name=MinPrice]").val(),
				$maxPrice = $("input[name=MaxPrice]").val()
			$tips = "",
				totalError = 0;
			$minPrice = parseFloat($minPrice);
			isNaN($minPrice) && ($minPrice = 0);
			$maxPrice = parseFloat($maxPrice);
			isNaN($maxPrice) && ($maxPrice = 0);
			if ($minPrice > 0 && $maxPrice >= 0 && $minPrice >= $maxPrice && !$('input[name=NoMaxLimit]:checked').val()) {
				// 最低价格 大于等于 最高价格
				$tips = lang_obj.manage.sales.fillable_greater_equal.replace("{{name1}}", lang_obj.manage.set.minPrice).replace("{{name2}}", lang_obj.manage.set.maxPrice);
			}
			if ($tips) {
				$priceObj.addClass("has_error");
				if ($priceObj.find(".error_tips").length) {
					$priceObj.find(".error_tips").text($tips);
				} else {
					$priceObj.append("<p class=\"error_tips\">" + $tips + "</p>");
				}
				totalError += 1;
			} else {
				$priceObj.removeClass("has_error").find(".error_tips").remove();
			}
			// 信用卡支付检测
			if ($('#checkCreditCard').length) {
				let $checkCCObj = $('#checkCreditCard')
				let $agree = parseInt($checkCCObj.attr('data-agree') || 0)
				let $isCCPay = true
				let tipTitle = lang_obj.manage.payment.tips.credit_card_used
				if ($('#PId').val() == 1) {
					let $CCObj = $('input[name="IsCreditCardPay"]')
					$isCCPay = $CCObj.is(':checked')
					//tipTitle = lang_obj.manage.payment.tips.credit_card_used_paypal
					tipTitle = lang_obj.manage.mta.add_purchase_charts.excheckout;
				}
				if ($isCCPay && $agree == 0) {
					let name = $checkCCObj.attr('data-name')
					let params = {
						'title': tipTitle,
						//'subtitle': `${lang_obj.manage.payment.credit_card_payment} (${name})`,
					}
					global_obj.win_alert(params, () => {
						$checkCCObj.attr('data-agree', 1)
						if ($('#payment_edit_form .btn_submit').length) {
							$('#payment_edit_form .btn_submit').trigger('click')
						} else {
							$('.fixed_btn_submit .btn_submit').trigger('click')
						}
					}, 'confirm')
					return false
				}
			}
			if (totalError > 0) {
				return false;
			}
		}, 0, function (data) {
			if (data.ret == 1) {
				global_obj.win_alert_auto_close(data.msg, '', 400, '8%');
				setTimeout(function () {
					location.href = '/Setting/Payment/';
				}, 400);
			} else {
				global_obj.win_alert_auto_close(data.msg, 'fail', 1000, '8%');
			}
		});

		//PayPal支付 v2版本
		$('.box_paypal_tab_menu .item').click(function () {
			if (!$(this).hasClass('checked')) {
				var $Index = $(this).index();
				$(this).addClass('checked').siblings().removeClass('checked');
				$('.box_paypal_tab>div').eq($Index).show().siblings().hide();
			}
		});

		$('.box_credit_card_payment .input_radio_side_box').on('click', function () {
			if ($(this).hasClass("disabled")) return false;
			let value = $(this).find('input').val();
			if (value == 'embed') $('.box_credit_card_3ds').addClass('show');
			else $('.box_credit_card_3ds').removeClass('show');
		})

		// 预览
		frame_obj.fixed_box_popup({
			'clickObj': $('.btn_preview'),
			'targetClass': 'box_preview',
			'onClick': (e) => {
				let type = e.this.attr('data-type') || ''
				if (!type) return false
				$('.box_preview img').attr('src', `/manage/web/shop/images/set/payment/paypal-${type}.jpg`)
			}
		})
		frame_obj.fixed_box_popup({
			'clickObj': $('.btn_preview_tab'),
			'targetClass': 'box_preview_tab',
			'onClick': (e) => {
				let type = e.this.attr('data-type') || '';
				if (!type) return false;
				$('.box_preview_tab .content').off().on('click', '.tab_head>li', function () {
					$(this).addClass('current').siblings().removeClass('current');
					$('.box_preview_tab .tab_body>li').eq($(this).index()).show().siblings().hide();
				});
				$('.box_preview_tab .tab_head>li:eq(0)').trigger('click');
			}
		})
	},
	/******************* 税费管理 start *******************/
	tax_init: function () {
		frame_obj.select_all($('input[name=select_all]'), $('tr:visible input[name=select]')); //批量操作
		$('.plugins_app_menu a').click(function () {
			var $id = $(this).data('id'),
				$url = $(this).data('url');
			$('.plugins_app_menu a').removeClass('current');
			$(this).addClass('current');
			window.history.pushState(null, null, $url);
			$('.country_list tbody tr').hide();
			$('.country_list tbody tr[data-id="' + $id + '"]').show();
			$('input[name=select_all]').prop("checked", false).parent().removeClass('current indeterminate').parents('tr').removeClass('current')
			$('.global_menu_button .open').addClass('no_select')
			$('input[name=select]').prop("checked", false).parent().removeClass('current')
			$('.btn_checkbox').unbind('click')
			frame_obj.btnCheckbox()
			frame_obj.select_all($('input[name=select_all]'), $('tr:visible input[name=select]')) //批量操作
		});
		frame_obj.fixed_right($('.btn_tax_bat_edit'), '.box_tax_bat_edit', '', function ($this) {
			let id_list = '';
			$('.country_list tr:visible input[name=select]').each(function (index, element) {
				id_list += $(element).get(0).checked ? $(element).val() + ',' : '';
			});
			if (!id_list) {
				global_obj.win_alert(lang_obj.global.dat_select);
				return false;
			} else {
				id_list = id_list.substring(0, id_list.lastIndexOf(','));
			}
			$('.box_tax_bat_edit input[name="id_list"]').val(id_list);
		});
		frame_obj.submit_form_init($('#form_tax_bat'), '', '', '', function () {
			global_obj.win_alert_auto_close(lang_obj.global.save_success, '', 400, '8%');
			setTimeout(function () {
				location.href = location.href;
			}, 400);
		});
		$('#tax').on('click', '.tax_type', function () {	//启用
			var $this = $(this),
				isused = 0;
			if (!$this.hasClass('checked')) {
				$this.addClass('checked');
				$('.tax_type_parameter').show();
				isused = 1;
			} else {
				$this.removeClass('checked');
				$('.tax_type_parameter').hide();
			}
			$.get('/api/Setting/manager/taxes/tax-type-edit', { 'IsUsed': isused }, function (data) {
				global_obj.win_alert_auto_close(data.msg, 'success', 1000, '8%');
			}, 'json');
		});
		frame_obj.submit_form_init($('#edit_form'));
	},
	/******************* 税费管理 end *******************/

	/******************* 风格公共部分 *******************/
	themes_init: function () {
		//下拉效果
		$('.inside_container .more').parent().hover(function () {
			$(this).find('.more_menu').show().stop(true).animate({ 'top': 31, 'opacity': 1 }, 250);
		}, function () {
			$(this).find('.more_menu').show().stop(true).animate({ 'top': 21, 'opacity': 0 }, 250, function () { $(this).hide(); });
		});
	},

	/******************* 首页设置 *******************/
	themes_index_set_init: function () {
		$('.abs_item').click(function () {
			var $this = $(this);
			$('.abs_item').removeClass('cur');
			$this.addClass('cur');
			set_obj.load_edit_form('.index_set_exit', './?m=set&a=themes&d=index_set', 'get', '&WId=' + $this.attr('data-wid'), function () {
				if ($this.attr('data-type') == 'Banner') {
					ad_init();
				} else {
					web_init();
				}
				frame_obj.upload_img_init();
				frame_obj.submit_form_init($('#index_set_edit_form'), $('input[name=return_url]').val() + '&WId=' + $('.abs_item.cur').attr('data-wid'));
			});
		});
		if ($('.abs_item.cur').length) {
			$('.abs_item.cur').click();
		} else {
			$('.abs_item').eq(0).click();
		}
		function web_init() {
			frame_obj.mouse_click($('#index_set .multi_img .upload_btn, #index_set .multi_img .pic_btn .edit'), 'img', function ($this) { //点击上传图片
				var $id = $this.parents('.multi_img').attr('id');
				frame_obj.photo_choice_init($id, '', 1);
			});
		}
		function ad_init() {
			$('.show_type .ty_list').click(function () {
				$('.show_type .ty_list').removeClass('cur').find('input').prop('checked', false);
				$(this).addClass('cur').find('input').prop('checked', true);
			});
			frame_obj.dragsort($('.ad_drag'), '', '.adpic_row', '.ad_view, .l_img', '<li class="adpic_row placeHolder"></li>');
			frame_obj.mouse_click($('#index_set .multi_img .upload_btn, #index_set .multi_img .pic_btn .edit'), 'ad', function ($this) { //产品颜色图点击事件
				var $id = $this.attr('id'),
					$lang = $this.parents('.img').attr('data-lang'),
					$num = $this.parents('.img').attr('num');
				frame_obj.photo_choice_init('PicDetail_' + $lang + ' .img[num;' + $num + ']', 'ad', 5, 'do_action=products.products_img_del&Model=products');
			});
		}
	},

	/******************* 风格管理 *******************/
	themes_themes_edit_init: function () {
		$('.themes_current .use').click(function () {
			var $this = $(this);
			global_obj.win_alert(lang_obj.manage.module.sure_module, function () {
				if ($this.hasClass('IsMobile')) {
					$action = "do_action=set.themes_mobile_themes_edit&tpl=" + $this.attr('data-themes');
				} else {
					$action = 'do_action=set.themes_themes_edit&themes=' + $this.attr('data-themes');
				}
				$.get('?', $action, function (data) {
					if (data.ret != 1) {
						global_obj.win_alert(data.msg, function () {
							window.location.reload();
						}, 'confirm');
					} else {
						window.location.reload();
					}
				}, 'json');
			}, 'confirm');
		});
		$('#themes_themes .item').click(function () {
			var $themes = $(this).attr('data-themes'),
				$url = $(this).attr('data-url'),
				$img = $(this).attr('data-img'),
				$name = $(this).attr('data-name');
			$('#themes_themes .item').removeClass('current');
			$(this).addClass('current');
			$('.themes_current .themes').text($name);
			$('.themes_current .use').attr('data-themes', $themes);
			$('.themes_current .view').attr('href', $url);
			$('.themes_current .themes_img img').attr('src', $img);
		});
		$('#themes_themes .item.current').click();
		$('.themes_themes').height($(window).height() - $('.themes_themes').offset().top - 25);
		$('.themes_current .themes_img').height($(window).height() - $('.themes_img').offset().top - 25);
		$('.themes_current .themes_img').hover(
			function () {
				if ($(this).height() < $(this).find('img').height()) {
					var $speed = 100;
					if ($(this).find('img').height() > 1000) {
						$speed = 200;
					}
					if ($(this).find('img').height() > 2000) {
						$speed = 300;
					}
					if ($(this).find('img').height() > 3000) {
						$speed = 500;
					}
					var $time = ($(this).find('img').height() - $(this).height()) / $speed;
					$(this).find('img').stop().animate({ 'margin-top': $(this).height() - $(this).find('img').height() }, $time * 1000);
				}
			},
			function () {
				$(this).find('img').stop().animate({ 'margin-top': 0 }, 'fast');
			}
		);
		window.onload = function () {
			$(window).resize(function () {
				frame_obj.Waterfall('themes_box', '157', 3, 'item'); // 瀑布流
			}).resize();
		}
	},

	themes_products_list_edit_init: function () {
		frame_obj.switchery_checkbox(function (obj) {
			if (obj.find('input[name=IsColumn]').length) {
				obj.parents('.rows').next().next().slideDown();
			}
		}, function (obj) {
			if (obj.find('input[name=IsColumn]').length) {
				obj.parents('.rows').next().next().slideUp();
			}
		});
		$('#edit_form').delegate('input[name=reset]', 'click', function () {
			global_obj.win_alert(lang_obj.global.reset_confirm, function () {
				$.get('?', "do_action=set.themes_products_list_reset", function (data) {
					if (data.ret != 1) {
						global_obj.win_alert(data.msg);
					} else {
						window.location.reload();
					}
				}, 'json');
			}, 'confirm');
		});
		$('#edit_form .choice_btn').on('click', function () {
			$(this).addClass('current').siblings().removeClass('current');
			$(this).children('input').prop('checked', true);
		});
		$('#edit_form .order_list select').on('change', function () {
			var $number = $(this).children('option:selected').attr('number'),
				$select = $(this).parents('.rows').next().find('span.input');
			if ($number) {
				var ary = $number.split(',');
				var $html = '<select name="OrderNumber">';
				for (var i = 0; i < ary.length; ++i) {
					$html += '<option' + ($select.attr('number') == ary[i] ? ' selected' : '') + '>' + ary[i] + '</option>';
				}
				$html += '</select>';
			}
			$select.html($html);
		});
		$('#edit_form .order_list select').change();
		$('#edit_form .choice_btn.current').click();
		frame_obj.submit_form_init($('#edit_form'), './?m=set&a=themes&d=products_list');
	},

	themes_products_detail_edit_init: function () {
		$('#edit_form .item').hover(function () {
			$(this).children('.info').stop(true, true).slideDown(500);
		}, function () {
			$(this).children('.info').stop(true, true).slideUp(500);
		}).children('.img').click(function () {
			if (!$(this).parent().hasClass('current')) {
				var $this = $(this);
				global_obj.win_alert(lang_obj.manage.module.sure_module, function () {
					$this.parent().addClass('current').siblings().removeClass('current');
					$.post('?', "do_action=set.themes_products_detail_themes_edit&Key=" + $this.parent().attr('detail-id'), function (data) {
						if (data.ret != 1) {
							global_obj.win_alert(data.msg);
						} else {
							window.location.reload();
						}
					}, 'json');
				}, 'confirm');
				return false;
			}
		});

		$('.tab_box .tab_txt').on('click', '.add', function () { //添加帮助选项
			var $box = $(this).parent('.help_item'),
				$num = $box.index(),
				$obj = $(this).parents('.tab_txt');
			if ($obj.find('.help_item:eq(0) .not_input').is(':hidden')) {
				$('.tab_box .tab_txt').each(function () {
					$(this).find('.help_item:eq(0) .not_input, .help_item:eq(0) .switchery, .help_item:eq(0) b, .help_item:eq(0) .del').show();
					$(this).find('.help_item:eq(0) .not_input input').val('');
					$(this).find('.help_item:eq(0) input').attr('disabled', false);
				});
			} else {
				$('.tab_box .tab_txt').each(function () {
					$(this).find('.help_item:eq(' + $num + ')').after($(this).find('.help_item:eq(' + $num + ')').prop('outerHTML'));
					$(this).find('.help_item:eq(' + ($num + 1) + ')').siblings().find('.add').hide();
					$(this).find('.help_item:eq(' + ($num + 1) + ') .not_input input').val('');
				});
			}
		}).on('click', '.del', function () { //删除帮助选项
			var $box = $(this).parent('.help_item'),
				$num = $box.index(),
				$obj = $(this).parents('.tab_txt');
			if ($obj.find('.help_item').length == 1) {
				$('.tab_box .tab_txt').each(function () {
					$(this).find('.help_item:eq(0) .not_input, .help_item:eq(0) .switchery, .help_item:eq(0) b, .help_item:eq(0) .del').hide();
					$(this).find('.help_item:eq(0) input').attr('disabled', true);
				});
			} else {
				$('.tab_box .tab_txt').each(function () {
					$(this).find('.help_item:eq(' + $num + ')').remove();
					$(this).find('.help_item:last .add').show();
					$(this).find('.help_item:last').siblings().find('.add').hide();
				});
			}
		}).on('change', 'input.input_url', function () {
			var $num = $(this).parents('.help_item').index();
			$(this).parents('.tab_txt').siblings().find('.help_item:eq(' + $num + ') input[name=Url\\[\\]]').val($(this).val());
		}).on('click', '.switchery', function () {
			var $num = $(this).parents('.help_item').index();
			if ($(this).hasClass('checked')) {
				$(this).removeClass('checked').find('input').prop('checked', false);
				$(this).parents('.tab_txt').siblings().find('.help_item:eq(' + $num + ') .switchery input').prop('checked', false).parent().removeClass('checked');
			} else {
				$(this).addClass('checked').find('input').prop('checked', true);
				$(this).parents('.tab_txt').siblings().find('.help_item:eq(' + $num + ') .switchery input').prop('checked', true).parent().addClass('checked');
			}
		});

		frame_obj.dragsort($('#share_list'), '', 'div', '', '<div class="share_btn fl placeHolder"></div>'); //分享图标排序拖动
		frame_obj.submit_form_init($('#edit_form'), './?m=set&a=themes&d=products_detail');
	},

	themes_nav_global: {
		type: '',
		init: function () {
			frame_obj.del_init($('#themes .config_table')); //删除事件
			$('#themes .config_table_body').dragsort({ //元素拖动
				dragSelector: 'div',
				dragSelectorExclude: 'tbody',
				placeHolderTemplate: '<div class="table_item placeHolder"></div>',
				scrollSpeed: 5,
				dragEnd: function () {
					var data = $(this).parent().children('.table_item').map(function () {
						return $(this).attr('data-id');
					}).get();
					$.get('?', { do_action: 'set.themes_nav_order', sort_order: data.join('|'), Type: set_obj.themes_nav_global.type }, function () {
						var num = 0;
						$('#themes .config_table_body .table_item').each(function () {
							$(this).attr('data-id', num);
							num++;
						});
					});
				}
			});

			$('body, html').on('click', '.box_drop_double .children', function () {
				//下一级选项
				var $Item = $(this),
					$Name = $Item.attr('data-name'),
					$Value = $Item.attr('data-value'),
					$Type = $Item.attr('data-type'),
					$Table = $Item.attr('data-table'),
					$Top = $Item.attr('data-top'),
					$Obj = $('.column_rows .box_drop_double');
				$Obj.find('dt input[type=text]').val($Name).parent().find('.hidden_value').val($Value).next().val($Type);
				$Item.parents('dd').hide();
			}).on('click', '.box_drop_double .item', function () {
				//选择下拉选项
				var $This = $(this),
					$Type = $This.attr('data-type');
				$('.url_rows, .pic_rows').hide();
				$('.column_rows .tab_box').hide();
				$('.column_rows .tab_txt:hidden').each(function () {
					$(this).find('.box_input').removeAttr('notnull');
				});
				if ($Type == 'add') {//自定义
					$('.url_rows').show();
					$('.column_rows .tab_box').show();
					$('.column_rows .tab_txt:hidden').each(function () {
						$(this).find('.box_input').attr('notnull', 'notnull');
					});
				} else if ($Type == 'products' || $Type == 'category') {//产品
					if ($('.pic_rows').length) $('.pic_rows').show();
				}
			}).on('keyup', '.box_drop_double dt .box_input', function (e) {
				//输入触发
				var $Value = $.trim($(this).val()),
					$Key = window.event ? e.keyCode : e.which,
					$Obj = $(this).parents('.box_drop_double');
				if ($Key != 13) { //除了回车键
					if ($Value.length > 2) { //输入内容，自动默认为“新添加”
						$Obj.find('.hidden_value').val($Value);
						$Obj.find('.hidden_type').val('add');
						$('.url_rows').show();
						$('.column_rows .tab_box').show();
						$('.column_rows .tab_txt:hidden').each(function () {
							$(this).find('.box_input').attr('notnull', 'notnull');
						});
					}
				}
			});
		}
	},

	themes_nav_init: function () {
		set_obj.themes_nav_global.type = 'nav';
		set_obj.themes_nav_global.init();
		frame_obj.mouse_click($('.multi_img .upload_btn, .pic_btn .edit'), 'ad', function ($this) { //产品颜色图点击事件
			var $id = $this.parents('.multi_img').attr('id'),
				$num = $this.parents('.img').attr('num');
			frame_obj.photo_choice_init($id + ' .img[num;' + $num + ']', 'ad', 5, 'do_action=products.products_img_del&Model=products');
		});
		//图片上传 开始
		$('.ad_drag').dragsort({
			dragSelector: '.adpic_row',
			dragSelectorExclude: '.ad_info, .upload_file_multi',
			placeHolderTemplate: '<li class="adpic_row placeHolder"></li>',
			scrollSpeed: 5
		});
		frame_obj.submit_form_init($('#nav_edit_form'), './?m=set&a=themes&d=nav');
	},

	themes_footer_nav_init: function () {
		set_obj.themes_nav_global.type = 'foot_nav';
		set_obj.themes_nav_global.init();
		frame_obj.submit_form_init($('#nav_edit_form'), './?m=set&a=themes&d=footer_nav');
	},

	themes_style_edit_init: function () {
		var obj = $('#style_edit_form');
		obj.delegate('input[name=reset]', 'click', function () {
			global_obj.win_alert(lang_obj.global.reset_confirm, function () {
				$.get('?', "do_action=set.themes_style_reset", function (data) {
					if (data.ret != 1) {
						global_obj.win_alert(data.msg);
					} else {
						window.location.reload();
					}
				}, 'json');
			}, 'confirm');
		});
		$('input[name=NavBgColor]').length && obj.delegate('input[name=NavBgColor]', 'change', function () { $('.NavBgColor').css('background-color', '#' + $(this).val()); });
		$('input[name=NavHoverBgColor]').length && $('.NavHoverBgColor').mouseover(function () { $(this).css('background', '#' + $('input[name=NavHoverBgColor]').val()); }).mouseleave(function () { $(this).css('background', 'none'); });
		$('input[name=NavBorderColor1]').length && obj.delegate('input[name=NavBorderColor1]', 'change', function () { $('.NavBorderColor1').css('border-color', '#' + $(this).val()); });
		$('input[name=NavBorderColor2]').length && obj.delegate('input[name=NavBorderColor2]', 'change', function () { $('.NavBorderColor2').css('border-color', '#' + $(this).val()); });
		$('input[name=CategoryBgColor]').length && obj.delegate('input[name=CategoryBgColor]', 'change', function () { $('.CategoryBgColor').css('background-color', '#' + $(this).val()); });
		frame_obj.submit_form_init($('#style_edit_form'), './?m=set&a=themes&d=style');
	},

	themes_advanced_edit_init: function () {
		frame_obj.submit_form_init($('#adv_edit_form'), './?m=set&a=themes&d=advanced');
	},

	shipping_global: {
		init: function () { //地区设置
			$('.country_area_box').off('click').on('click', '.country_area_title .down', function () {
				if ($(this).hasClass('cur')) {
					$(this).removeClass('cur').parent().next('.country_area').hide();
				} else {
					$(this).addClass('cur').parent().next('.country_area').show();
				}
			}).on('click', '.continent_area .continent .input_checkbox_box', function () {
				$(this).attr('data-status', 1);
			}).on('click', '.continent_area .continent', function () {
				if ($(this).find('.input_checkbox_box').attr('data-status') != 1) {
					if ($(this).find('.down').hasClass('cur')) {
						$(this).find('.down').removeClass('cur');
					} else {
						$(this).find('.down').addClass('cur');
					}
					$(this).next('.country_item').toggle();
				} else {
					$(this).find('.input_checkbox_box').attr('data-status', 0);
				}
			}).on('click', '.continent .input_checkbox_box', function () {
				var $obj = $(this);
				if ($obj.hasClass('checked')) {
					$obj.parent().next('.country_item').find('.input_checkbox_box').removeClass('checked');
					$obj.parent().next('.country_item').find('.input_checkbox input').prop('checked', false);
				} else {
					$obj.parent().next('.country_item').find('.input_checkbox_box').not('.disabled').addClass('checked');
					$obj.parent().next('.country_item').find('.input_checkbox_box:not(".disabled") input').prop('checked', true);
				}
				$obj.parent().next('.country_item').find('.country_states').each(function () {
					$(this).find('.input_checkbox_box').removeClass('half_checked');
					$(this).find('.states_count span:eq(0)').text($(this).next('.country_item_third').find('.input_checkbox_box.checked').length);
				});
			}).on('click', '.country_item_sec.country_states .states_count', function () {
				var $obj = $(this).parent('.country_item_sec');
				if ($obj.hasClass('cur')) {
					$obj.removeClass('cur').next('.country_item_third').hide();
				} else {
					$obj.addClass('cur').next('.country_item_third').show();
				}
			}).on('click', '.country_item_sec .input_checkbox_box', function () {
				var $parent = $(this).parent(),
					$obj = $(this);
				if ($parent.hasClass('country_states')) { //有下级的时候批量操作下级
					if ($obj.hasClass('checked')) {
						$parent.next('.country_item_third').find('.input_checkbox_box').removeClass('checked');
						$parent.next('.country_item_third').find('.input_checkbox input').prop('checked', false);
					} else {
						$parent.next('.country_item_third').find('.input_checkbox_box').not('.disabled').addClass('checked');
						$parent.next('.country_item_third').find('.input_checkbox_box:not(".disabled") input').prop('checked', true);
					}
					$parent.find('.states_count span:eq(0)').text($parent.next('.country_item_third').find('.input_checkbox_box.checked').length);
				}
				if (!$obj.hasClass('disabled')) {
					//控制上级洲的选中状态
					var $area = $obj.parents('.continent_area'),
						$all_area_count = $area.find('.country_item .input_checkbox_box').length,
						$area_count = $area.find('.country_item .input_checkbox_box.checked').length;
					if ($obj.hasClass('checked')) {
						$area_count -= 1;
					} else {
						$area_count += 1;
					}

					if ($area_count == 0) {
						$area.find('.continent .input_checkbox_box').removeClass('checked half_checked').find('input').prop('checked', false);
					} else if ($all_area_count == $area_count) {
						$area.find('.continent .input_checkbox_box').addClass('checked').removeClass('half_checked').find('input').prop('checked', true);
					} else {
						$area.find('.continent .input_checkbox_box').addClass('checked half_checked').find('input').prop('checked', true);
					}
				}

			}).on('click', '.country_item_third .input_checkbox_box', function () {
				if ($(this).hasClass('disabled')) return false;
				//控制上级国家的选中状态
				var $obj = $(this),
					$total_count = $obj.parents('.country_item_third').find('.input_checkbox_box').length,
					$count = $obj.parents('.country_item_third').find('.input_checkbox_box.checked').length;
				if ($obj.hasClass('checked')) {
					$count -= 1;
				} else {
					$count += 1;
				}
				$obj.parents('.country_item_third').prev('.country_states').find('.states_count span:eq(0)').text($count);
				if ($count == 0) {
					$obj.parents('.country_item_third').prev('.country_states').find('.input_checkbox_box').removeClass('checked half_checked').find('input').prop('checked', false);
				} else if ($total_count == $count) {
					$obj.parents('.country_item_third').prev('.country_states').find('.input_checkbox_box').addClass('checked').removeClass('half_checked').find('input').prop('checked', true);
				} else {
					$obj.parents('.country_item_third').prev('.country_states').find('.input_checkbox_box').addClass('checked half_checked').find('input').prop('checked', true);
				}
				//控制上级洲的选中状态
				var $area = $obj.parents('.continent_area'),
					$all_area_count = $area.find('.country_item .input_checkbox_box').length,
					$area_count = $area.find('.country_item .input_checkbox_box.checked').length;
				if ($obj.hasClass('checked')) {
					$area_count -= 1;
				} else {
					$area_count += 1;
				}

				if ($area_count == 0) {
					$area.find('.continent .input_checkbox_box').removeClass('checked half_checked').find('input').prop('checked', false);
				} else if ($all_area_count == $area_count) {
					$area.find('.continent .input_checkbox_box').addClass('checked').removeClass('half_checked').find('input').prop('checked', true);
				} else {
					$area.find('.continent .input_checkbox_box').addClass('checked half_checked').find('input').prop('checked', true);
				}
			});
		},
		weight: function () {
			var WeightBetween = $('#WeightBetween'),
				VolumeBetween = $('#VolumeBetween'),
				WeightArea = $('#WeightArea'), //重量区间的div
				ExtWeightArea = $('#ExtWeightArea'), //续重区间的div
				Weightrow = $('#Weightrow'), //重量区间，只包含输入框的div
				ExtWeightrow = $('#ExtWeightrow'), //只包含输入框的div
				ExtWeight = $('#ExtWeight'), //首重续重div
				Quantity = $('#Quantity'), //按数量div
				VolumeArea = $('#VolumeArea'), //体积区间的div
				Volumerow = $('#Volumerow'), //体积区间，只包含输入框的div
				FirstWeight = $('#FirstWeight'),
				StartWeight_span = $('#StartWeight_span'),
				StartWeight = $('#StartWeight_span input'), //混合计算，区间开始计算的重量
				MinWeight = $('#MinWeight'); //最小重量限制
			MaxWeight = $('#MaxWeight'); //最大重量限制
			MinVolume = $('#MinVolume'); //最小体积限制
			MaxVolume = $('#MaxVolume'); //最大体积限制
			fixed_weight = $('input[name="WeightArea[]"]:first', WeightArea), //重量区间第一个输入框，固定不能修改
				fixed_Extweight = $('input[name="ExtWeightArea[]"]:first', ExtWeightArea); //重量区间第一个输入框，固定不能修改
			fixed_volume = $('input[name="VolumeArea[]"]:first', VolumeArea); //体积区间第一个输入框，固定不能修改
			$('.weightarea_box .item').click(function () {
				if ($(this).hasClass("disabled")) return false;
				$('.weightarea_box .item').removeClass('cur').find('input[name=IsWeightArea]').prop('checked', false);
				$(this).addClass('cur').find('input[name=IsWeightArea]').prop('checked', true);
				$('.weightarea_type_hide').show();
				$('input[name=IsWeightArea]:checked').change();
			});
			$('input[name="IsWeightArea"]').change(function () {
				var val = parseInt($(this).val());
				WeightBetween.hide();
				VolumeBetween.hide();
				StartWeight_span.hide();
				fixed_weight.val(MinWeight.val()); //只按照区间计费就必须从0kg开始
				WeightArea.hide();
				ExtWeight.hide();
				ExtWeightArea.hide();
				Quantity.hide();
				VolumeArea.hide();
				MaxWeight.attr({ 'disabled': false, 'readonly': false });
				MaxVolume.attr({ 'disabled': false, 'readonly': false });
				WeightBetween.show();
				WeightBetween.find('.box_max').show();
				WeightBetween.find('.box_unlimited').hide();
				switch (val) {
					case 1: //区间
						WeightArea.show();
						break;
					case 2: //重量混合计算，从输入的值开始
						StartWeight_span.show();
						fixed_weight.val(MinWeight.val());
						WeightArea.show();
						ExtWeight.show();
						ExtWeightArea.show();
						break;
					case 3: //按数量
						Quantity.show();
						break;
					case 5: //按数量
						$('.weightarea_type_hide').hide();
						break;
					default: //首重
						ExtWeight.show();
						ExtWeightArea.show();
						break;
				}
			});
			$('input[name="IsWeightArea"]:checked').change(); //勾选点击
			StartWeight.focus(function () {
				StartWeight.select();
			});
			StartWeight.keyup(function () {
				fixed_weight.val($(this).val());
			});
			//重量区间
			set_obj.shipping_global.interval_init('addWeight', 'Weightrow');

			//体积区间
			set_obj.shipping_global.interval_init('addVolume', 'Volumerow');

			//续重区间
			set_obj.shipping_global.interval_init('addExtWeight', 'ExtWeightrow');

			FirstWeight.focus(function () {
				FirstWeight.select();
			});
			FirstWeight.keyup(function () {
				fixed_Extweight.val(($(this).val() ? parseFloat($(this).val()) : 0));
			});

			MinWeight.focus(function () {
				MinWeight.select();
			});
			MinWeight.keyup(function () {
				if ($('input[name="UseCondition"]:checked').val() == 1) {
					fixed_weight.val(($(this).val() ? parseFloat($(this).val()) : 0));
				}
			});

			MinVolume.focus(function () {
				MinVolume.select();
			});
			MinVolume.keyup(function () {
				fixed_volume.val(($(this).val() ? parseFloat($(this).val()) : 0));
			});
		},

		interval_init: function (click_obj, rows_box) {
			// click_obj 点击添加按钮ID
			// rows_box 编辑的容器
			//区间
			$('#' + click_obj).click(function () {//新增区间输入节点
				if (click_obj == 'addWeight' && $('#' + rows_box).find('.row').length == 1 && $('input[name=StartWeight]').val() == '' && $('input[name="IsWeightArea"]:checked').val() == 2) {
					//混合重量必须先填写区间开始重量
					global_obj.win_alert_auto_close(lang_obj.manage.shipping.fill_start_weight, 'fail', 1000, '8%');
					$('input[name=StartWeight]').focus();
					return false;
				}
				var $html = $('#' + rows_box).find('.row:last').prop('outerHTML'),
					$start = $('#' + rows_box).find('.row').eq($('#' + rows_box).find('.row').length - 2).find('input[name]').val(),
					$end = $('#' + rows_box).find('.row:last').find('input[name]'),
					$end_num = $end.val();
				if ($end_num == '' || parseFloat($end_num) < parseFloat($start)) {
					$end.focus();
					global_obj.win_alert_auto_close(lang_obj.manage.shipping.fill_num, 'fail', 1000, '8%');
					return false;
				}
				$('#' + rows_box).append($html);
				$('#' + rows_box).find('.row:last').find('input[name]').val('').removeAttr('readonly').removeAttr('disabled');
				$('#' + rows_box).find('.row:last').show().find('a').show();
			});
			$('#' + rows_box).on('click', '.row a', function () {//删除区间输入节点
				$(this).parent().remove();
			});
		},
	},

	shipping_express_init: function () {
		if ($('#shipping .inside_menu').length) {
			$('#shipping .inside_menu').insideMenu();
		}
		// 下拉效果
		$(".shipping_head .shipping_edit dl").hover(function () {
			$(this).find("dd").show().stop(true).animate({ "top": 39, "opacity": 1 }, 250);
		}, function () {
			$(this).find("dd").show().stop(true).animate({ "top": 30, "opacity": 0 }, 250, function () { $(this).hide(); });
		});
		frame_obj.fixed_box_popup({
			"clickObj": $(".btn_add_area"),
			"targetClass": "box_area_choose_range",
			"onClose": function () {
				return $('.updata_form').length ? false : true
			},
			"openAfter": function (e) {
				let sid = e.this.attr('data-sid')
				$('.box_area_choose_range .delivery_box a').each(function () {
					$(this).attr('href', $(this).attr('href').replace('{{sid}}', sid))
				})
			}
		});

		// 启用 / 停用
		$(".shipping_head .shipping_edit .use").click(function () {
			let $this = $(this),
				$obj = $this.parents(".global_container"),
				$sid = $obj.data("id"),
				$status = $this.attr("data-status");
			if ($status == 1) {
				$used = 0;
			} else {
				$used = 1;
			}
			$.post("/manage/set/shipping/shipping-used?id=" + $sid, { "IsUsed": $used }, function (data) {
				let isAction = 0;
				global_obj.win_alert_auto_close(data.msg.message, "", 1000, "8%", 0);
				$obj.find(".box_title .status").text(data.msg.status);
				if ($used == 1) {
					$obj.find(".box_title .status").addClass("ing");
					$obj.attr("data-status", "enable");

					$(".config_table_body > .global_container[data-status=enable]").each(function (index, element) {
						if ($(element).attr("data-id") < $sid) {
							$obj.insertBefore($(element));
							isAction = 1;
							return false;
						}
					});
					if (isAction == 0) {
						let $targetObj = $(".config_table_body > .global_container[data-status=disable][data-id!=" + $sid + "]:eq(0)");
						$obj.insertBefore($targetObj);
					}
				} else {
					$obj.find(".box_title .status").removeClass("ing");
					$obj.attr("data-status", "disable");

					if ($(".config_table_body > .global_container[data-status=disable]").length > 0) {
						$(".config_table_body > .global_container[data-status=disable]").each(function (index, element) {
							if ($(element).attr("data-id") < $sid) {
								$obj.insertBefore($(element));
								isAction = 1;
								return false;
							}
						});
						if (isAction == 0) {
							let $targetObj = $(".config_table_body > .global_container[data-status=disable][data-id!=" + $sid + "]:last");
							$obj.insertAfter($targetObj);
						}
					} else {
						let $targetObj = $(".config_table_body > .global_container[data-status=enable][data-id!=" + $sid + "]:last");
						$obj.insertAfter($targetObj);
					}
				}
				$this.attr("data-status", $used).text(data.msg.use);
			}, "json");
		});

		// 运费调整
		frame_obj.fixed_right($(".shipping_head .shipping_edit .rate"), ".freight_rate_edit", function ($obj) {
			let $id = $obj.parents(".global_container").data("id");
			let $rate = $obj.attr("data-rate");
			$(".freight_rate_edit input[name=FreightRate]").val($rate);
			$(".freight_rate_edit input[name=id]").val($id);
		});
		frame_obj.submit_object_init($('#freight_rate_form'), '', '', '', function (result) {
			let $id = $(".freight_rate_edit input[name=id]").val();
			$("#shipping .global_container[data-id=" + $id + "] .shipping_head .shipping_edit .rate").attr("data-rate", result.msg);
			$("#shipping .global_container[data-id=" + $id + "] .r_con_table tbody tr .rate").text(result.msg + "%");
			$('#fixed_right .btn_cancel').click();
		});

		// 删除物流
		$(".shipping_head .shipping_edit .del").click(function () {
			let $id = $(this).parents(".global_container").data("id");
			let $url = $(this).attr("data-url");
			let params = {
				"title": lang_obj.global.del_confirm,
				"confirmBtn": lang_obj.global.del,
				"confirmBtnClass": "btn_warn"
			};
			global_obj.win_alert(params, function () {
				$.get($url, function (result) {
					if (result.ret == 1) {
						$("#shipping .global_container[data-id=" + $id + "]").slideUp(function () {
							$(this).remove();
						});
					} else {
						global_obj.win_alert(result.msg);
					}
				}, "json");
			}, "confirm");
			return false;
		});

		// 删除物流分区
		$(".r_con_table .operation .del").click(function () {
			let $obj = $(this).parents("tr");
			let $url = $(this).attr("data-url");
			let params = {
				"title": lang_obj.global.del_confirm,
				"confirmBtn": lang_obj.global.del,
				"confirmBtnClass": "btn_warn"
			};
			global_obj.win_alert(params, function () {
				$.get($url, function (result) {
					if (result.ret == 1) {
						$obj.slideUp(function () {
							$(this).remove();
						});
					} else {
						global_obj.win_alert(result.msg);
					}
				}, "json");
			}, "confirm");
			return false;
		});

		$(".drop_country").on("click", function (event) {
			let $this = $(this);
			let $id = $this.parents("tr").data("id");
			if ($this.find(".country_txt").hasClass("no_drop")) return false;
			if ($this.find(".country_list").length || $this.find(".range_itme").length) {
				$this.addClass("current").find(".country_container").fadeIn();
				return false
			}
			$.post("/manage/set/shipping/get-country-info", { "id": $id }, function (result) {
				if (result.ret == 1) {
					let html = "";
					let count = 0;
					if (result.msg.type == 'zipCode') {
						$this.find(".country_box").html(result.msg.html)
					} else {
						if (result.msg.country) {
							html += '<div class="country_list">';
							for (let k in result.msg.country) {
								let country = result.msg.country[k];
								html += '<span class="list_item">' + country + '</span>';
							}
							html += '</div>';
							count++;
						}
						$this.find(".country_box").html(html);
					}

					// 控制显示
					let change = 0;
					let boxHeight = $this.find(".country_container").height();
					let clickHeight = event.currentTarget.offsetTop;
					let tableHeight = $(".config_table_body").height();
					if (clickHeight + boxHeight > tableHeight) {
						$this.find(".country_container").css({ "top": "auto", "bottom": "100%" });
						change = 1;
					}
					let thisPosition = clickHeight - parseInt($(".inside_table").css("padding-top")) - $(".inside_table .list_menu").height() - parseInt($(".box_table table").css("margin-top"));
					if (change) {
						thisPosition = thisPosition - 5;
						if (boxHeight > thisPosition) {
							// 产品框高度大于表格
							$this.find(".country_box").css("max-height", thisPosition);
						}
					} else {
						thisPosition = thisPosition + $this.height() + 25;
						if (thisPosition + boxHeight > tableHeight) {
							// 产品框高度大于表格
							$this.find(".country_box").css("max-height", tableHeight - thisPosition);
						}
					}
					$this.addClass("current").find(".country_container").fadeIn();
				}
			}, "json");
		});
		$(".drop_country").on("mouseleave", function () {
			$(this).removeClass("current").find(".country_container").fadeOut();
		});
	},

	shipping_express_edit_init: function () {
		frame_obj.switchery_checkbox();
		frame_obj.box_type_menu(function ($this) {
			let $index = $this.index();
			if ($index > 0) {
				$(".bg_between").show();
			} else {
				$(".bg_between").hide();
			}
			$('.box_type_index').hide();
			$('.box_type_index').parent().find('.box_input').removeClass('right_radius').removeClass('left_radius');
			if ($index == 0) {
				$('#WeightBetween .unit_input').hide();
			} else {
				$('#WeightBetween .unit_input').show();
				$('.box_type_index[data-index="' + $index + '"]').show();
				if ($this.index() == 1) {
					$('.box_type_index[data-index="' + $this.index() + '"]').prev().prev().addClass('right_radius');
				} else if ($this.index() == 3) {
					$('.box_type_index[data-index="' + $this.index() + '"]').next().addClass('left_radius');
				}
			}
			var $start_weight = $('input[name="WeightArea[]"]:first', $('#WeightArea')),
				$first_weight = $('#MinWeight').val();
			if ($index != 1) {
				$start_weight.val(0);
			} else {
				$start_weight.val($first_weight);
			}
		});

		$('#MaxWeight').blur(function () {
			set_obj.judgment_max($(this), $('#MinWeight'), '.input_item', '.error_tips')
		});

		/* 快递LOGO上传 */
		frame_obj.mouse_click($('#LogoDetail .upload_btn, #LogoDetail .pic_btn .edit'), 'img', function ($this) { //点击上传图片
			frame_obj.photo_choice_init('LogoDetail', 'shipping', 1);
		});
		set_obj.shipping_global.weight();
		/* 表单提交 */
		frame_obj.submit_form_init($('#asiafly_edit_form'), '/manage/set/shipping');
		frame_obj.submit_form_init($('#edit_form'), '', '', '', function (data) {
			if (data.ret == -1) {
				set_obj.judgment_max($('#MaxWeight'), $('#MinWeight'), '.input_item', '.error_tips')
				return false
			} else {
				location.href = data.msg;
			}
		});
	},

	shipping_express_area_init: function () {
		let $weightUnit = 'kg'
		const DeliveryRange = $('.area_edit_box input[name=DeliveryRange]').val()
		let FUNC = {
			Open: "",
			Init: () => {
				frame_obj.box_type_menu(($this) => {
					let $inputObj = $this.find('input:radio')
					if ($inputObj.attr('name') == 'WeightUnit') {
						// 选择重量单位
						let $value = $this.find('input:radio').val()
						// 更新表格中的单位显示
						$('.box_freight tbody').find('td.type, td.list').each((index, element) => {
							$(element).find('b').text($value)
						})
						$('.box_free input[name=FreeShippingWeight]').next().text($value)
						
						// 更新弹出框中的单位显示
						let $freightObj = $('.box_edit_freight')
						$freightObj.find('b.last').text($value)
						$freightObj.find('.unit').text(` / ${$value}`)
						$freightObj.find('.box_calculation .input_radio_box:eq(2) strong').text(lang_obj.manage.shipping.type_each.replace('{{weightUnit}}', $value))
						$freightObj.find('.box_calculation .input_radio_box:eq(2) p').text(lang_obj.manage.shipping.explain_each.replace('{{weightUnit}}', $value))
						
						// 首重+续重弹窗单位更新
						$freightObj.find('.content[data-type="additional"] b.last').each(function() {
							$(this).text($value);
						})
						
						// 固定运费弹窗单位更新
						$freightObj.find('.content[data-type="total"] b.last').each(function() {
							$(this).text($value);
						})
						
						// 每KG运费弹窗单位更新  
						$freightObj.find('.content[data-type="each"] b.last').each(function() {
							$(this).text($value);
						})
						
						// 更新导入弹窗中的单位显示
						$('.box_freight_import .box_list_freight .content_node').each(function() {
							let text = $(this).text();
							let parts = text.split('~');
							if(parts.length > 1) {
								// 替换最后的单位部分
								$(this).text(parts[0] + '~' + parts[1].replace(/[a-z]+$/, $value));
							}
						})
						
						$weightUnit = $value
					}
				});
				set_obj.shipping_global.init();
				frame_obj.switchery_checkbox(function (obj) {
					if (obj.find('input[name^=IsFreeShipping]').length) {
						obj.parents('.box_free').find('.box_free_content').show();
					}
				}, function (obj) {
					if (obj.find('input[name^=IsFreeShipping]').length) {
						obj.parents('.box_free').find('.box_free_content').hide();
					}
				});
				FUNC.CountryManage();
				FUNC.FreightManage();
				FUNC.ImportManage();
				FUNC.AreaQuickImport();
				frame_obj.global_select_box();
				if (DeliveryRange == 'zipCode') {
					FUNC.zipCodeInit()
				}
				// 添加配送地区
				$('.country_area_box .btn_submit').click(function () {
					let $html = '',
						$aid = $('.country_area_box input[name=AId]').val();
					if (DeliveryRange == 'zipCode') {
						$('.box_area .content .dl').hide().find('.submit_input').attr('disabled', 'disabled')
					}
					$('.country_area_box .country_item input[name=CId]:checked').each(function () {
						let $this = $(this),
							$cid = $this.val(),
							$country = $this.attr('data-country')
						if (DeliveryRange == 'zipCode') {
							// 指定邮政编码
							let cnCountry = $country.split('  ')[0] // 国家中文名称
							if ($(`.box_area .content .dl[data-cid="${$cid}"]`).length) {
								$(`.box_area .content .dl[data-cid="${$cid}"]`).show().find('.submit_input:visible, .submit_input[type="hidden"]').removeAttr('disabled')
							} else {
								let _keyCid = '_' + $cid
								let itemCodeStart = FUNC.strReplaceBat($('.box_hiddent_html .hiddent_value_item[data-value-type="CodeStart"]').html(), {
									'{{keyCId}}': _keyCid,
									'{{keyIndex}}': '_' + '0',
									'disabled="disabled"': '',
									'{{value}}': ''
								})
								let rangeItemHtml = FUNC.strReplaceBat($('.box_hiddent_html .hiddent_range_item').html(), {
									'{{index}}': 0,
									'{{selectedcodestart}}': 'selected',
									'{{selectednumberrange}}': '',
									'{{selectedspecify}}': '',
									'{{rangeValueHtml}}': itemCodeStart
								})
								let dlHtml = FUNC.strReplaceBat($('.box_hiddent_html .hiddent_dl').html(), {
									'{{CId}}': $cid,
									'disabled="disabled"': '',
									'{{CountryName}}': cnCountry,
									'{{maxItem}}': 1,
									'{{rangeItemHtml}}': rangeItemHtml
								})
								$('.box_area .content').append(dlHtml)
							}
						} else {
							// 指定国家/地区
							let $acronym = $this.attr('data-acronym'),
								$flagpath = $this.attr('data-flagpath'),
								$states = $('.country_area_box .country_item .country_item_third[data-cid="' + $cid + '"]'), // 省份
								$total_states_count = $states.find('input[name=StatesSId]').length, // 总数量
								$states_count = $states.find('input[name=StatesSId]:checked').length, // 当前选中
								$select_states_count = $states.find('.input_checkbox_box.disabled').length; // 已经选中
							$html += '<div class="item">';
							$states.find('input[name=StatesSId]:checked').each(function () {
								$html += '<input type="checkbox" name="StatesSId[' + $cid + '][]" value="' + $(this).val() + '" checked />';
								$(this).prop('checked', false).parents('.input_checkbox_box').removeClass('checked').addClass('disabled');
							});
							$html += '<div class="img">';
							if ($cid <= 240) {
								$html += '<div class="icon_flag flag_' + $acronym + '"></div>';
							} else {
								$html += '<img src="' + $flagpath + '" />';
							}
							$html += '</div>';
							$html += '<div class="name">' + $country;
							if ($states.length && $total_states_count != $states_count) {
								$html += ' (' + $states_count + '/' + $total_states_count + ')';
							}
							$html += '</div>';
							$html += '<div class="area_box del"></div><div class="clear"></div>';
							$html += '<input type="checkbox" name="CId[]" value="' + $cid + '" checked />';
							$html += '</div>';
							$this.prop('checked', false).parents('.input_checkbox_box').removeClass('checked half_checked').next('.states_count').find('span:eq(0)').text(0);
							if ($total_states_count == ($states_count + $select_states_count)) {
								$this.parents('.input_checkbox_box').addClass('disabled');
							}
						}
					});
					if (DeliveryRange == 'country') $('.box_area .content').html('').append($html);
					frame_obj.check_amount($('.box_area .range_con'))
					global_obj.win_alert_auto_close(lang_obj.manage.shipping.add_area_suc, '', 1000, '8%');
					$('.country_area_box .continent_area>.continent .input_checkbox_box').removeClass('disabled'); // 开放所有洲的勾选
					$('.country_area_box a.close').click();
					if ($(".box_area .content .item").length > 0 || $('.country_area_box .country_item input[name=CId]:checked').length) {
						// 有国家信息
						$(".box_area .content").fadeIn();
					} else {
						// 没有国家信息
						$(".box_area .content").fadeOut();
					}
				});
				// 取消事件
				$('body').on('click', '#fixed_right_div_mask, .country_area_box .btn_cancel', function () {
					if (FUNC.Open != "country_area_box") return false;
					$('.box_area .content input[type=checkbox][name^="CId"]').each(function () {
						$('.country_area_box .input_checkbox_box[data-cid="' + $(this).val() + '"]').removeClass('checked half_checked').find('input[type=checkbox]').prop('checked', false);

						let $states = $('.country_area_box .country_item .country_item_third[data-cid="' + $(this).val() + '"]'),
							$total_states_count = $states.find('input[name=StatesSId]').length, // 总数量
							$states_count = $states.find('input[name=StatesSId]:checked').length, // 当前选中
							$select_states_count = $states.find('.input_checkbox_box.disabled').length; // 已经选中
						if ($total_states_count == ($states_count + $select_states_count)) {
							$('.country_area_box .input_checkbox_box[data-cid="' + $(this).val() + '"]').addClass('disabled');
						}
					});
					$('.box_area .content input[type=checkbox][name^="StatesSId"]').each(function () {
						$('.country_area_box .input_checkbox_box[data-sid="' + $(this).val() + '"]').removeClass('checked').addClass('disabled').find('input[type=checkbox]').prop('checked', false);
					});
					$('.country_area_box .continent_area>.continent .input_checkbox_box').removeClass('disabled'); // 开放所有洲的勾选
				});
				$(".fixed_btn_submit .btn_continue").click(function () {
					$("#edit_form input[name=isContinue]").val(1);
				});
				frame_obj.submit_object_init($('#edit_form'), '', '', '', function (data) {
					let $id = $("#edit_form input[name=AId]").val();
					let $isContinue = $("#edit_form input[name=isContinue]").val();
					let $Model = $("#edit_form input[name=Model]").val();
					if (data.ret == -1) {
						global_obj.win_alert_auto_close(data.msg, 'await', 1000, '8%');
						$('.option_selected').click();
					} else if (data.ret == -2) {
						$('.option_selected').click();
						let params = {
							'title': lang_obj.manage.shipping.tips.zipcodeConflict,
							'alertClass': 'zipcode_intersect_tips',
							'confirmBtn': lang_obj.manage.global.got_it,
							'extHtml': data.msg,
							'confirmBtnClass': 'btn_warn'
						};
						global_obj.win_alert(params, function () {

						});
					} else {
						global_obj.win_alert_auto_close(lang_obj.global.save_success, '', 1000, '8%');
						if ($Model == '' && ($id > 0 || $isContinue == 1)) {
							window.location.reload();
						} else {
							window.location.href = "/manage/set/shipping";
						}
					}
				});
				$('#fixed_right .country_area_box .search_form form').submit(function () {
					let _Keyword = $(this).find('input[name=Keyword]').val();
					let _parentObj = $('.country_area');
					$.post('/manage/set/shipping/search-country/', { "Keyword": _Keyword }, function (data) {
						if (data.ret == 1) {
							$('#fixed_right .country_area_box .btn_submit').attr("disabled", false);
							let _searchData = data.msg;
							if (_searchData.length != 0) {
								let _areaAry = new Array(),
									_countryAry = new Array(),
									_statesAry = new Array();
								for (k in _searchData) {
									if (_searchData[k]['Continent'] && $.inArray(_searchData[k]['Continent'], _areaAry) == -1) {
										_areaAry.push(_searchData[k]['Continent']);
									}
									if (_searchData[k]['CId'] && $.inArray(_searchData[k]['CId'], _countryAry) == -1) {
										_countryAry.push(_searchData[k]['CId']);
									}
									if (_searchData[k]['SId'] && $.inArray(_searchData[k]['SId'], _statesAry) == -1) {
										_statesAry.push(_searchData[k]['SId']);
									}
								}
								if (_areaAry.length > 0) {
									_parentObj.find('.continent_area').each(function () {
										let _areaId = $(this).find('.continent').attr('continent');
										if ($.inArray(_areaId, _areaAry) != -1) {
											$(this).show();
											$(this).find('.continent').find('a.down').addClass('cur');
											$(this).find('.country_item').show()
										} else {
											$(this).hide();
										}
									})
								}
								if (_countryAry.length > 0) {
									_parentObj.find('.country_item_sec').each(function () {
										let _countryId = $(this).find('.input_checkbox_box').attr('data-cid');
										if ($.inArray(_countryId, _countryAry) != -1) {
											$(this).show();
											if ($(this).next().css('display') == 'block') {
												$(this).next().hide()
											}
										} else {
											$(this).hide();
										}
									})
								}
								if (_statesAry.length > 0) {
									_parentObj.find('.country_item_third .input_checkbox_box').each(function () {
										let _statesId = $(this).attr('data-sid');
										if (_statesId && $.inArray(_statesId, _statesAry) != -1) {
											$(this).parent().show();
											$(this).parent().prev().addClass('cur')
											$(this).show();
										} else {
											$(this).hide();
										}
									})
								}
							} else {
								_parentObj.find('.continent_area').hide();
								$('#fixed_right .country_area_box .btn_submit').attr("disabled", true);
							}
						} else {
							$('#fixed_right .country_area_box .btn_submit').attr("disabled", false);
							_parentObj.find('.continent_area').show();
							_parentObj.find('.continent_area').find('.continent a.down').removeClass('cur');
							_parentObj.find('.continent_area').find('.country_item').hide();
							_parentObj.find('.country_item_sec').removeClass('cur').show();
							_parentObj.find('.country_item_third').hide();
							_parentObj.find('.country_item_third .input_checkbox_box').show();
						}
					}, 'json');
					return false;
				})
			},
			CountryManage: () => {
				frame_obj.fixed_right($('.btn_add_area'), '.country_area_box', function ($this) {
					FUNC.Open = "country_area_box";
					$('.country_area_box input[name=AId]').val($this.attr('data-aid'));
					$('.country_area_box .continent .input_checkbox_box').removeClass('checked').removeClass('half_checked');
					// 打开的时候自动选中国家
					$this.parents('.box_area').find('input[type=checkbox][name^=CId], input[type=hidden][name^=CId]:not(:disabled)').each(function () {
						$('.country_area_box .input_checkbox_box[data-cid="' + $(this).val() + '"]').removeClass('disabled').addClass('checked').find('input[type=checkbox]').prop('checked', true);
					});
					// 打开的时候自动选中国家地区
					$this.parents('.box_area').find('input[type=checkbox][name^=StatesSId]').each(function () {
						$('.country_area_box .input_checkbox_box[data-sid="' + $(this).val() + '"]').removeClass('disabled').addClass('checked').find('input[type=checkbox]').prop('checked', true);
					});
					// 判断有没有选中国家
					$('.country_area_box .country_item').each(function () {
						if ($(this).find('.input_checkbox_box.checked').length == $(this).find('.input_checkbox_box').length) {
							$(this).prev('.continent').find('.input_checkbox_box').addClass('checked').removeClass('half_checked');
						} else if ($(this).find('.input_checkbox_box.checked').length > 0) {
							$(this).prev('.continent').find('.input_checkbox_box').addClass('half_checked');
						} else {
							$(this).prev('.continent').find('.input_checkbox_box').removeClass('checked half_checked');
						}
					});
					// 计算选中的地区数量
					$('.country_area_box .country_states').each(function () {
						let $total_states_count = $(this).next('.country_item_third').find('.input_checkbox_box').length, // 总数量
							$states_count = $(this).next('.country_item_third').find('.input_checkbox_box.checked').length, // 当前选中
							$select_states_count = $(this).next('.country_item_third').find('.input_checkbox_box.disabled').length; // 已经选中
						$(this).find('.states_count span:eq(0)').text($states_count);
						if ($total_states_count == $states_count) {
							$(this).find('.input_checkbox_box').removeClass('half_checked');
						} else if ($states_count == 0) {
							$(this).find('.input_checkbox_box').removeClass('checked half_checked');
						} else {
							$(this).find('.input_checkbox_box').addClass('checked half_checked');
						}
						if ($total_states_count != ($states_count + $select_states_count)) {
							$(this).find('.input_checkbox_box').removeClass('disabled');
						}
					});
					// 判断有没有选中洲
					$('.country_area_box .continent_area').each(function () {
						if ($(this).find('.country_item .input_checkbox_box.disabled').length == $(this).find('.country_item .input_checkbox_box').length) {
							$(this).find('.continent .input_checkbox_box').addClass('disabled');
						}
					});
					//搜索重新搜索一次空 -- 搜索后添加了地区 重新打开右侧栏的时候搜索框留空，列表显示亚洲为方形选中状态
					if ($('.country_area_box').find('.search_form input[name=Keyword]').val() != '') {
						$('.country_area_box').find('.search_form input[name=Keyword]').val('');
						$('#fixed_right .country_area_box .search_form form').submit();
					}
				});

				if ($('#shipping .box_area .content').attr('data-range') == 'country') {
					if ($(".box_area .content .item").length == 0) {
						// 没有国家信息
						$(".box_area .content").hide();
					}
				} else if ($('#shipping .box_area .content').attr('data-range') == 'zipCode') {
					if ($(".box_area .content .item, .box_area .content .dl").length > 0) {
						$(".box_area .content").show();
					} else {
						$(".box_area .content").hide();
					}
				}
			},
			FreightManage: () => {
				const TARGET = $(".box_edit_freight")
				const FORM = $("#freight_edit_form")
				const TABLE = $(".box_freight .r_con_table")
				const UNIT = $(".box_weight_unit")
				const FREE = $(".box_free")

				let numID = 0
				$weightUnit = UNIT.find('input[name=WeightUnit]:checked').val() || 'kg'

				frame_obj.check_amount(FORM); // 检查价格

				frame_obj.fixed_right($(".btn_global_add"), ".box_edit_freight", function () {
					FUNC.Open = "box_edit_freights";
					let $trObj = TABLE.find("tr[data-number=\"" + numID + "\"]") || [],
						$calculation = $trObj.find(".calculation_value").val() || '',
						$weightArea = $trObj.find(".area_value").val() || '',
						$data = $trObj.find(".data_value").val() || '';
					$idValue = $trObj.find(".id_value").val() || '';
					if ($weightArea) $weightArea = global_obj.json_encode_data($weightArea);
					if ($data) $data = global_obj.json_encode_data($data);
					TARGET.find("input[name=number]").val(numID);
					TARGET.find("input[name=PId]").val($idValue);
					// 初始化内容
					TARGET.find(".calculation_content input").val("");
					TARGET.find(".calculation_content .content").each(function () {
						if ($(this).data("type") == "total" || $(this).data("type") == "each") {
							$(this).find(".box_between:eq(0) .btn_option_remove").hide();
							$(this).find(".box_between:gt(0)").remove();
						}
					});
					TARGET.find(".has_error").removeClass("has_error").find(".error_tips").remove();
					// 放置内容
					if (numID > 0) {
						// 修改
						TARGET.find(".top_title .edit").show().siblings(".add").hide();
						TARGET.find(".box_calculation input[value=\"" + $calculation + "\"]").parent().parent().click();
						let $contentObj = TARGET.find(".calculation_content .content[data-type=\"" + $calculation + "\"]");
						if ($calculation == "additional") {
							// 首重+续重
							let firstPrice = 0;
							let extPrice = 0;
							let firstWeight = 0;
							let extWeight = 0;
							let startWeight = 0;
							let endWeight = 0;
							
							try {
								// 尝试解析数据，支持字符串或对象格式
								let dataObj = typeof $data === 'string' ? JSON.parse($data) : $data;
								
								// 提取首重和续重信息
								firstWeight = dataObj.firstWeight || 0;
								firstPrice = parseFloat(dataObj.firstPrice || 0);
								extWeight = dataObj.extWeight || 0;
								extPrice = parseFloat(dataObj.extPrice || 0);
								
								// 处理weightArea，支持两种格式: [x,y] 或 [[x,y]]
								if (Array.isArray($weightArea)) {
									// 直接是数组
									if (Array.isArray($weightArea[0])) {
										// 嵌套数组格式 [[x,y]]
										startWeight = $weightArea[0][0];
										endWeight = $weightArea[0][1];
									} else {
										// 单层数组格式 [x,y]
										startWeight = $weightArea[0];
										endWeight = $weightArea[1];
									}
								} else if (typeof $weightArea === 'string') {
									// 尝试解析可能的字符串表示
									try {
										let weightAreaObj = JSON.parse($weightArea);
										if (Array.isArray(weightAreaObj)) {
											if (Array.isArray(weightAreaObj[0])) {
												startWeight = weightAreaObj[0][0];
												endWeight = weightAreaObj[0][1];
											} else {
												startWeight = weightAreaObj[0];
												endWeight = weightAreaObj[1];
											}
										}
									} catch (e) {
										console.error("解析weightArea失败", e);
									}
								}
							} catch (e) {
								console.error("解析首重+续重数据失败", e, $data);
							}
							
							// 填充表单字段
							$contentObj.find("input[name=FirstWeight]").val(firstWeight);
							$contentObj.find("input[name=FirstPrice]").val(global_obj.ceil_price(firstPrice));
							$contentObj.find("input[name=ExtWeight]").val(extWeight);
							$contentObj.find("input[name=ExtPrice]").val(global_obj.ceil_price(extPrice));
							$contentObj.find("input[name=StartWeight]").val(startWeight);
							$contentObj.find("input[name=EndWeight]").val(endWeight);}
							 else if ($calculation == "total") {
							// 固定运费
							if ($idValue) $idValue = global_obj.json_encode_data($idValue);
							$weightArea.forEach(function (item, index) {
								let price = parseFloat($data[index].fixedPrice);
								if (index > 0) $contentObj.find(".btn_add_node").click();
								$contentObj.find("input[name=StartWeight]:last").val(item[0]);
								$contentObj.find("input[name=EndWeight]:last").val(item[1]);
								$contentObj.find("input[name=ShippingPrice]:last").val(global_obj.ceil_price(price));
								$contentObj.find("input[name=idValue]:last").val($idValue[index]);
							});

						} else {
							// 每KG运费
							if ($idValue) $idValue = global_obj.json_encode_data($idValue);
							$weightArea.forEach(function (item, index) {
								let price = parseFloat($data[index].fixedPrice);
								if (index > 0) $contentObj.find(".btn_add_node").click();
								$contentObj.find("input[name=StartWeight]:last").val(item[0]);
								$contentObj.find("input[name=EndWeight]:last").val(item[1]);
								$contentObj.find("input[name=ShippingPrice]:last").val(global_obj.ceil_price(price));
								$contentObj.find("input[name=idValue]:last").val($idValue[index]);
							});

						}

						let min = $trObj.attr("data-node-min");
						let max = $trObj.attr("data-node-max");
						FORM.attr({ "data-min": min, "data-max": max }); // 限制改动的范围
					} else {
						// 添加
						TARGET.find(".top_title .add").show().siblings(".edit").hide();
						TARGET.find(".box_weight_unit .item").eq(0).click();
						TARGET.find(".box_calculation .input_radio_box").eq(0).click();

						// let min = TABLE.find("tbody tr:last").length ? TABLE.find("tbody tr:last").attr("data-node-max") : 0;
						FORM.attr({ "data-min": 0, "data-max": 0 }); // 限制改动的范围

						// 首重+续重
						let $contentObj = TARGET.find(".calculation_content .content[data-type=\"additional\"]");
						$contentObj.find("input[name=StartWeight]").val(0);
						$contentObj.find("input[name=EndWeight]").val(21);
					}
				});

				$(".btn_add_list").click(function () {
					numID = 0;
					$(".btn_global_add").click();
				});
				TABLE.on("click", ".icon_edit", function () {
					numID = parseInt($(this).parents("tr").data("number"));
					$(".btn_global_add").click();
				}).on("click", ".icon_del", function () {
					$(this).parents("tr").animate({ "opacity": 0 }, 500, function () {
						$(this).remove();
						$("#button_float_tips").remove(); // 防止删掉提示框不消失
						if (TABLE.find("tbody tr").length == 0) {
							TABLE.fadeOut();
						}
					});
				});

				TARGET.on("click", ".box_calculation .input_radio_box", function () {
					// 运费规则
					let $value = $(this).find("input").val();
					TARGET.find(".calculation_content .content[data-type=\"" + $value + "\"]").show().siblings().hide();
				}).on("click", ".btn_add_node", function () {
					// 添加节点
					let $targetObj = $(this).prev();
					$targetObj.after('<div class="box_between clean">' + $targetObj.html() + '</div>');
					$targetObj.parent().find(".box_between:last").addClass("no_title");
					$targetObj.parent().find(".box_between:last .btn_option_remove").show();
					$targetObj.parent().find(".box_between:last input[name=idValue]").val(0);
					$targetObj.parent().find(".box_between:last .has_error").removeClass("has_error").find(".error_tips").remove();

					frame_obj.check_amount(FORM); // 检查价格

					return false;
				}).on("click", ".btn_option_remove", function () {
					// 删除节点
					$(this).parent().fadeOut(300, function () {
						$(this).remove();
					});
				}).on("click", ".btn_submit", function () {
					// 保存
					let $number = TARGET.find("input[name=number]").val(),
						$calculation = TARGET.find("input[name=Calculation]:checked").val(),
						$contentObj = TARGET.find(".calculation_content .content[data-type=\"" + $calculation + "\"]"),
						$weightArea = "",
						$data = "",
						$idValue = "",
						$weightNodeText = "",
						$priceText = "";

					let $nodeMin = parseFloat(FORM.attr("data-min"));
					let $nodeMax = parseFloat(FORM.attr("data-max"));
					let nodeData = { "min": 0, "max": 0, "isFirst": 0, "isLast": 0 };

					if ($number > 0) {
						// 修改
						let $trObj = $(".box_freight .r_con_table tr[data-number=\"" + $number + "\"]");
						nodeData.isFirst = $trObj.prev().length ? 0 : 1;
						nodeData.isLast = $trObj.next().length ? 0 : 1;
					} else {
						// 添加 数值为0等于0 数值大于0的都要+1
						if ($nodeMin > 0) $nodeMin += 1;
					}

					// 列表的范围值
					let rangeAry = [];
					TABLE.find("tbody tr").each(function (index, element) {
						rangeAry[index] = {
							"start": $(this).attr("data-start"),
							"end": $(this).attr("data-end")
						}
					});

					// 检验
					let $tips = "";
					let totalError = 0;
					$contentObj.find("input[type=text]").each(function () {
						let obj = $(this).parent().parent();
						$tips = "";
						if ($.trim($(this).val()) == "") {
							$tips = lang_obj.manage.shipping.tips.enter_value;
							totalError += 1;
						}
						FUNC.ErrorTips(obj, $tips);
					});

					if ($calculation == "additional") {
						// 首重+续重
						let firstWeight = parseFloat($contentObj.find("input[name=FirstWeight]").val() || 0);
						let firstPrice = parseFloat($contentObj.find("input[name=FirstPrice]").val() || 0);
						let extWeight = parseFloat($contentObj.find("input[name=ExtWeight]").val() || 0);
						let extPrice = parseFloat($contentObj.find("input[name=ExtPrice]").val() || 0);
						let startWeight = parseFloat($contentObj.find("input[name=StartWeight]").val() || 0);
						let endWeight = parseFloat($contentObj.find("input[name=EndWeight]").val() || 0);

						startWeight = (startWeight === "" ? startWeight : parseFloat(startWeight));
						endWeight = (endWeight === "" ? endWeight : parseFloat(endWeight));

						$idValue = TARGET.find("input[name=PId]").val();

						$weightArea = [startWeight, endWeight];
						$weightArea = global_obj.json_decode_data($weightArea);

						$data = { "firstWeight": firstWeight, "firstPrice": firstPrice, "extWeight": extWeight, "extPrice": extPrice };
						$data = global_obj.json_decode_data($data);

						$weightNodeText = lang_obj.manage.shipping.weight_node_text.replace(/({{weight1}}|{{weight2}}|{{weightUnit}})/gi, function (e) {
							return {
								"{{weight1}}": startWeight,
								"{{weight2}}": endWeight,
								"{{weightUnit}}": $weightUnit
							}[e]
						});
						$priceText = lang_obj.manage.shipping.additional_price_text.replace(/({{firstPrice}}|{{firstWeight}}|{{extPrice}}|{{extWeight}}|{{weightUnit1}}|{{weightUnit2}})/gi, function (e) {
							return {
								"{{firstPrice}}": FUNC.Price(firstPrice),
								"{{firstWeight}}": firstWeight,
								"{{extPrice}}": FUNC.Price(extPrice),
								"{{extWeight}}": extWeight,
								"{{weightUnit1}}": $weightUnit,
								"{{weightUnit2}}": $weightUnit
							}[e]
						});

						let obj = $contentObj.find("input[name=StartWeight]").parent().parent();
						totalError += FUNC.NodeCheck({
							"obj": obj,
							"start": startWeight,
							"end": endWeight,
							"min": $nodeMin,
							"max": $nodeMax,
							"isFirst": nodeData.isFirst,
							"isLast": nodeData.isLast,
							"rangeAry": rangeAry
						});

						nodeData.min = startWeight;
						nodeData.max = endWeight;

					} else if ($calculation == "total") {
						// 固定运费
						$weightArea = [];
						$data = [];
						$idValue = [];

						$contentObj.find("input[name=StartWeight]").each(function (index, element) {
							let startWeight = $(element).val();
							let endWeight = $contentObj.find("input[name=EndWeight]:eq(" + index + ")").val();
							let shippingPrice = parseFloat($contentObj.find("input[name=ShippingPrice]:eq(" + index + ")").val() || 0);
							let id = $contentObj.find("input[name=idValue]:eq(" + index + ")").val() || 0;

							startWeight = (startWeight === "" ? startWeight : parseFloat(startWeight));
							endWeight = (endWeight === "" ? endWeight : parseFloat(endWeight));

							$weightArea[index] = [startWeight, endWeight];
							$data[index] = { "fixedPrice": shippingPrice };
							$idValue[index] = id;
							$weightNodeText += "<p>" + lang_obj.manage.shipping.weight_node_text.replace(/({{weight1}}|{{weight2}}|{{weightUnit}})/gi, function (e) {
								return {
									"{{weight1}}": startWeight,
									"{{weight2}}": endWeight,
									"{{weightUnit}}": $weightUnit
								}[e]
							}) + "</p>";
							$priceText += "<p>" + FUNC.Price(shippingPrice) + "</p>";

							let obj = $(this).parent().parent();
							totalError += FUNC.NodeCheck({
								"obj": obj,
								"start": startWeight,
								"end": endWeight,
								"min": $nodeMin,
								"max": $nodeMax,
								"isFirst": nodeData.isFirst,
								"isLast": nodeData.isLast,
								"rangeAry": rangeAry
							});
							if (index > 0 && startWeight) {
								// 检查上一个结束重量节点，是否大于它
								let prevEndWeight = $contentObj.find("input[name=EndWeight]:eq(" + (index - 1) + ")").val() || 0;
								if (startWeight <= prevEndWeight) {
									$tips = lang_obj.manage.shipping.tips.greater.replace("{{number}}", prevEndWeight);
									totalError += 1;
									FUNC.ErrorTips(obj, $tips);
								}
							}

							if (index == 0) nodeData.min = startWeight;
							nodeData.max = endWeight;
						});

						$weightArea = global_obj.json_decode_data($weightArea);
						$data = global_obj.json_decode_data($data);
						$idValue = global_obj.json_decode_data($idValue);

					} else {
						// 每KG运费
						$weightArea = [];
						$data = [];
						$idValue = [];

						$contentObj.find("input[name=StartWeight]").each(function (index, element) {
							let startWeight = parseFloat($(element).val() || 0);
							let endWeight = parseFloat($contentObj.find("input[name=EndWeight]:eq(" + index + ")").val() || 0);
							let shippingPrice = parseFloat($contentObj.find("input[name=ShippingPrice]:eq(" + index + ")").val() || 0);
							let id = $contentObj.find("input[name=idValue]:eq(" + index + ")").val() || 0;

							startWeight = (startWeight === "" ? startWeight : parseFloat(startWeight));
							endWeight = (endWeight === "" ? endWeight : parseFloat(endWeight));

							$weightArea[index] = [startWeight, endWeight];
							$data[index] = { "fixedPrice": shippingPrice };
							$idValue[index] = id;
							$weightNodeText += "<p>" + lang_obj.manage.shipping.weight_node_text.replace(/({{weight1}}|{{weight2}}|{{weightUnit}})/gi, function (e) {
								return {
									"{{weight1}}": startWeight,
									"{{weight2}}": endWeight,
									"{{weightUnit}}": $weightUnit
								}[e]
							}) + "</p>";
							$priceText += "<p>" + lang_obj.manage.shipping.each_price_text.replace("{{price}}", FUNC.Price(shippingPrice)).replace("{{weightUnit}}", $weightUnit) + "</p>";

							let obj = $(this).parent().parent();
							totalError += FUNC.NodeCheck({
								"obj": obj,
								"start": startWeight,
								"end": endWeight,
								"min": $nodeMin,
								"max": $nodeMax,
								"isFirst": nodeData.isFirst,
								"isLast": nodeData.isLast,
								"rangeAry": rangeAry
							});
							if (index > 0 && startWeight) {
								// 检查上一个结束重量节点，是否大于它
								let prevEndWeight = $contentObj.find("input[name=EndWeight]:eq(" + (index - 1) + ")").val() || 0;
								if (startWeight <= prevEndWeight) {
									$tips = lang_obj.manage.shipping.tips.greater.replace("{{number}}", prevEndWeight);
									totalError += 1;
									FUNC.ErrorTips(obj, $tips);
								}
							}

							if (index == 0) nodeData.min = startWeight;
							nodeData.max = endWeight;
						});

						$weightArea = global_obj.json_decode_data($weightArea);
						$data = global_obj.json_decode_data($data);
						$idValue = global_obj.json_decode_data($idValue);

					}

					if (totalError > 0) {
						return false;
					}

					if ($number == 0) {
						// 添加
						let html = "";
						TABLE.show();
						html += '<tr data-number="' + $number + '">';
						html += '<td></td>';
						html += '<td class="list"></td>';
						html += '<td class="list"></td>';
						html += '<td nowrap class="operation tar">';
						html += '<a class="icon_edit oper_icon button_tips" href="javascript:;">' + lang_obj.global.edit + '</a>';
						html += '<a class="del item oper_icon icon_del del button_tips" href="javascript:;" rel="del">' + lang_obj.global.del + '</a>';
						html += '<input type="hidden" class="calculation_value" name="PriceList[Calculation][]" value="" />';
						html += '<input type="hidden" class="area_value" name="PriceList[WeightArea][]" value="" />';
						html += '<input type="hidden" class="data_value" name="PriceList[Data][]" value="" />';
						html += '<input type="hidden" class="id_value" name="PriceList[Id][]" value="" />';
						html += '</td>';
						html += '</tr>';

						if (rangeAry && rangeAry.length) {
							// 列表有信息
							let insertPosition = -1;
							$(rangeAry).each(function (index, element) {
								if (nodeData.min > element.start) {
									insertPosition = index;
									$number = index + 2;
								}
							});
							if (insertPosition == -1) {
								// 放置在第一行
								$number = 1;
								TABLE.find("tbody tr").eq(insertPosition).before(html);
							} else {
								// 放置在后面一行
								TABLE.find("tbody tr").eq(insertPosition).after(html);
							}
						} else {
							// 列表为空
							$number = 1;
							TABLE.find("tbody").html(html);
						}
						// 重新排列number
						TABLE.find("tbody>tr").each(function (index, element) {
							$(element).attr("data-number", index + 1);
						});
					}

					let $trObj = $(".box_freight .r_con_table tr[data-number=\"" + $number + "\"]");
					$trObj.find("td").eq(0).text(lang_obj.manage.shipping['type_' + $calculation].replace('{{weightUnit}}', $weightUnit));
					$trObj.find("td").eq(1).html($weightNodeText);
					$trObj.find("td").eq(2).html($priceText);
					$trObj.find(".calculation_value").val($calculation);
					$trObj.find(".area_value").val($weightArea);
					$trObj.find(".data_value").val($data);
					$trObj.find(".id_value").val($idValue);

					// 更新限制范围
					nodeData.isFirst = $trObj.prev().length ? 0 : 1;
					nodeData.isLast = $trObj.next().length ? 0 : 1;

					let prevEnd = $trObj.prev().attr("data-end");
					let nextStart = $trObj.next().attr("data-start");

					let $_nodeMin = nodeData.min;
					if ($nodeMin && $nodeMin < nodeData.min) {
						$_nodeMin = $nodeMin;
					} else if (prevEnd) {
						$_nodeMin = prevEnd;
					}
					let $_nodeMax = nodeData.max;
					if ($nodeMax && $nodeMax > nodeData.max) {
						$_nodeMax = $nodeMax;
					} else if (nextStart) {
						$_nodeMax = nextStart;
					}

					$trObj.attr("data-node-min", (nodeData.isFirst ? 0 : $_nodeMin));
					$trObj.attr("data-node-max", $_nodeMax);
					$trObj.attr("data-start", nodeData.min);
					$trObj.attr("data-end", nodeData.max);
					$trObj.prev().attr("data-node-max", nodeData.min);
					$trObj.next().attr("data-node-min", nodeData.max);

					TARGET.find(".btn_cancel").click();
					return false;
				});
			},
			ImportManage: () => {
				const TARGET = $(".box_freight_import");
				const FORM = $("#freight_import_form");
				const TABLE = $(".box_freight .r_con_table");

				frame_obj.check_amount(FORM); // 检查价格

				frame_obj.fixed_right($(".btn_import"), ".box_freight_import", function ($this) {
					// 初始化内容
					TARGET.find(".global_select_box input").val("");
					TARGET.find(".box_list_freight").html("");
					TARGET.find(".has_error").removeClass("has_error").find(".error_tips").remove();
					// 加载下拉内容
					let id = $("#edit_form input[name=SId]").val();
					$.post("/manage/set/shipping/area-select", { "sid": id }, function (result) {
						if (result.ret == 1) {
							let html = "";
							result.msg.forEach(function (item) {
								html += '<li class="item" data-value="' + item.id + '">' + item.title + '</li>';
							});
							if (html == "") {
								html += '<li class="no_data">' + lang_obj.manage.error.no_data + '</li>';
							}
							TARGET.find(".global_select_box .select_ul").html(html);
						}
					}, "json");
				});

				TARGET.find(".global_select_box").on("click", ".select_ul li", function () {
					let $id = $(this).attr("data-value");
					$.post("/manage/set/shipping/area-info", { "id": $id }, function (result) {
						if (result.ret == 1) {
							let html = "";
							for (let k in result.msg) {
								let data = result.msg[k];
								if (data.Calculation == "additional") {
									// 首重+续重
									html += '<div class="item" data-type="additional">';
									html += '<div class="title">' + lang_obj.manage.shipping.type_additional + '</div>';
									html += '<div class="content">';
									html += '<div class="content_node" data-node="' + data.StartWeight + ',' + data.EndWeight + '">' + data.StartWeight + ' ~ ' + data.EndWeight + $weightUnit + '</div>';
									html += '<div class="content_item">';
									html += '<div class="content_name" data-weight="' + data.DataAry.firstWeight + '">' + lang_obj.manage.shipping.first_weight + ' ' + data.DataAry.firstWeight + $weightUnit + '</div>';
									html += '<div class="unit_input"><b>' + shop_config.currency + '</b><input type="text" name="firstPrice" value="' + data.DataAry.firstPrice + '" class="box_input left_radius" size="30" maxlength="10" rel="amount" /></div>';
									html += '</div>';
									html += '<div class="content_item">';
									html += '<div class="content_name" data-weight="' + data.DataAry.extWeight + '">' + lang_obj.manage.shipping.ext_weight + '</div>';
									html += '<div class="unit_input"><b>' + shop_config.currency + '</b><input type="text" name="extPrice" value="' + data.DataAry.extPrice + '" class="box_input left_radius" size="30" maxlength="10" rel="amount" /></div><div class="unit"> / ' + data.DataAry.extWeight + $weightUnit + '</div>';
									html += '</div>';
									html += '</div>';
									html += '</div>';
								} else if (data.Calculation == "total") {
									// 固定运费
									html += '<div class="item" data-type="total">';
									html += '<div class="title">' + lang_obj.manage.shipping.type_total + '</div>';
									html += '<div class="content">';
									data.WeightArea.forEach(function (item, index) {
										html += '<div class="content_node" data-node="' + item[0] + ',' + item[1] + '">' + item[0] + ' ~ ' + item[1] + $weightUnit + '</div>';
										html += '<div class="content_item">';
										html += '<div class="unit_input"><b>' + shop_config.currency + '</b><input type="text" name="fixedPrice" value="' + data.Data[index].fixedPrice + '" class="box_input left_radius" size="30" maxlength="10" rel="amount" /></div>';
										html += '</div>';
									});
									html += '</div>';
									html += '</div>';
								} else if (data.Calculation == "each") {
									// 每KG运费
									html += '<div class="item" data-type="each">';
									html += '<div class="title">' + lang_obj.manage.shipping.type_each.replace('{{weightUnit}}', $weightUnit) + '</div>';
									html += '<div class="content">';
									data.WeightArea.forEach(function (item, index) {
										html += '<div class="content_node" data-node="' + item[0] + ',' + item[1] + '">' + item[0] + ' ~ ' + item[1] + $weightUnit + '</div>';
										html += '<div class="content_item">';
										html += '<div class="unit_input"><b>' + shop_config.currency + '</b><input type="text" name="fixedPrice" value="' + data.Data[index].fixedPrice + '" class="box_input left_radius" size="30" maxlength="10" rel="amount" /></div>';
										html += '</div>';
									});
									html += '</div>';
									html += '</div>';
								}
							}
							FORM.find(".box_list_freight").html(html);

							frame_obj.check_amount(FORM); // 检查价格
						}
					}, "json");
				});

				TARGET.on("click", ".btn_submit", function () {
					// 保存

					// 检验
					let $tips = "";
					let totalError = 0;
					let $selectObj = FORM.find(".global_select_box input[type=text]");
					if ($selectObj.length) {
						let $selectVal = $.trim($selectObj.val());
						if ($selectVal == "") {
							$tips = lang_obj.global.selected;
							totalError += 1;
							FUNC.ErrorTips($selectObj.parents(".input"), $tips);
						}
					}
					FORM.find(".box_list_freight input[type=text]").each(function () {
						let obj = $(this).parent().parent();
						$tips = "";
						if ($.trim($(this).val()) == "") {
							$tips = lang_obj.manage.shipping.tips.enter_value;
							totalError += 1;
						}
						FUNC.ErrorTips(obj, $tips);
					});

					let saveAry = [];
					FORM.find(".box_list_freight .item").each(function () {
						let $this = $(this),
							$calculation = $this.attr("data-type")
						$weightArea = "",
							$data = "",
							$idValue = "",
							$weightNodeText = "",
							$priceText = "",
							$min = 0,
							$max = 0;

						if ($calculation == "additional") {
							// 首重+续重
							let firstWeight = parseFloat($this.find(".content_item:eq(0) .content_name").attr("data-weight") || 0);
							let firstPrice = parseFloat($this.find(".content_item:eq(0) input").val() || 0);
							let extWeight = parseFloat($this.find(".content_item:eq(1) .content_name").attr("data-weight") || 0);
							let extPrice = parseFloat($this.find(".content_item:eq(1) input").val() || 0);
							let startWeight = 0;
							let endWeight = 0;
							let node = $this.find(".content_node").attr("data-node");
							if (node) {
								node = node.split(",");
								startWeight = parseFloat(node[0] || 0);
								endWeight = parseFloat(node[1] || 0);
							}
							$idValue = 0;

							$weightArea = [startWeight, endWeight];
							$weightArea = global_obj.json_decode_data($weightArea);

							$data = { "firstWeight": firstWeight, "firstPrice": firstPrice, "extWeight": extWeight, "extPrice": extPrice };
							$data = global_obj.json_decode_data($data);

							$weightNodeText = lang_obj.manage.shipping.weight_node_text.replace(/({{weight1}}|{{weight2}}|{{weightUnit}})/gi, function (e) {
								return {
									"{{weight1}}": startWeight,
									"{{weight2}}": endWeight,
									"{{weightUnit}}": $weightUnit
								}[e]
							});
							$priceText = lang_obj.manage.shipping.additional_price_text.replace(/({{firstPrice}}|{{firstWeight}}|{{extPrice}}|{{extWeight}}|{{weightUnit1}}|{{weightUnit2}})/gi, function (e) {
								return {
									"{{firstPrice}}": FUNC.Price(firstPrice),
									"{{firstWeight}}": firstWeight,
									"{{extPrice}}": FUNC.Price(extPrice),
									"{{extWeight}}": extWeight,
									"{{weightUnit1}}": $weightUnit,
									"{{weightUnit2}}": $weightUnit
								}[e]
							});

							$min = startWeight;
							$max = endWeight;

						} else if ($calculation == "total") {
							// 固定运费
							$weightArea = [];
							$data = [];
							$idValue = [];

							$this.find(".content_node").each(function (index, element) {
								let startWeight = 0;
								let endWeight = 0;
								let node = $(element).attr("data-node");
								if (node) {
									node = node.split(",");
									startWeight = parseFloat(node[0] || 0);
									endWeight = parseFloat(node[1] || 0);
								}
								let fixedPrice = parseFloat($this.find(".content_item:eq(" + index + ") input").val() || 0);

								$weightArea[index] = [startWeight, endWeight];
								$data[index] = { "fixedPrice": fixedPrice };
								$idValue[index] = 0;
								$weightNodeText += "<p>" + lang_obj.manage.shipping.weight_node_text.replace(/({{weight1}}|{{weight2}}|{{weightUnit}})/gi, function (e) {
									return {
										"{{weight1}}": startWeight,
										"{{weight2}}": endWeight,
										"{{weightUnit}}": $weightUnit
									}[e]
								}) + "</p>";
								$priceText += "<p>" + FUNC.Price(fixedPrice) + "</p>";

								if (index == 0) $min = startWeight;
								$max = endWeight;
							});

							$weightArea = global_obj.json_decode_data($weightArea);
							$data = global_obj.json_decode_data($data);

						} else {
							// 每KG运费
							$weightArea = [];
							$data = [];
							$idValue = [];

							$this.find(".content_node").each(function (index, element) {
								let startWeight = 0;
								let endWeight = 0;
								let node = $(element).attr("data-node");
								if (node) {
									node = node.split(",");
									startWeight = parseFloat(node[0] || 0);
									endWeight = parseFloat(node[1] || 0);
								}
								let fixedPrice = parseFloat($this.find(".content_item:eq(" + index + ") input").val() || 0);

								$weightArea[index] = [startWeight, endWeight];
								$data[index] = { "fixedPrice": fixedPrice };
								$idValue[index] = 0;
								$weightNodeText += "<p>" + lang_obj.manage.shipping.weight_node_text.replace(/({{weight1}}|{{weight2}}|{{weightUnit}})/gi, function (e) {
									return {
										"{{weight1}}": startWeight,
										"{{weight2}}": endWeight,
										"{{weightUnit}}": $weightUnit
									}[e]
								}) + "</p>";
								$priceText += "<p>" + lang_obj.manage.shipping.each_price_text.replace("{{price}}", FUNC.Price(fixedPrice)).replace("{{weightUnit}}", $weightUnit) + "</p>";

								if (index == 0) $min = startWeight;
								$max = endWeight;
							});

							$weightArea = global_obj.json_decode_data($weightArea);
							$data = global_obj.json_decode_data($data);

						}

						saveAry.push({
							"title": lang_obj.manage.shipping['type_' + $calculation].replace('{{weightUnit}}', $weightUnit),
							"weightNodeText": $weightNodeText,
							"priceText": $priceText,
							"calculation": $calculation,
							"weightArea": $weightArea,
							"data": $data,
							"idValue": $idValue,
							"min": $min,
							"max": $max
						});
					});

					if (totalError > 0) {
						return false;
					}

					let params = {
						'title': lang_obj.manage.shipping.tips.import_overwrite,
						'confirmBtn': lang_obj.global.confirm
					};
					global_obj.win_alert(params, function () {
						// 导入内容
						TABLE.show();
						TABLE.find("tbody").html(""); // 先清空内容
						$(saveAry).each(function (index) {
							let html = "";
							$number = TABLE.find("tbody>tr").length + 1;
							html += '<tr data-number="' + $number + '">';
							html += '<td></td>';
							html += '<td class="list"></td>';
							html += '<td class="list"></td>';
							html += '<td nowrap class="operation tar">';
							html += '<a class="icon_edit oper_icon button_tips" href="javascript:;">' + lang_obj.manage.global.edit + '</a>';
							html += '<a class="del item oper_icon icon_del del button_tips" href="javascript:;" rel="del">' + lang_obj.global.del + '</a>';
							html += '<input type="hidden" class="calculation_value" name="PriceList[Calculation][]" value="" />';
							html += '<input type="hidden" class="area_value" name="PriceList[WeightArea][]" value="" />';
							html += '<input type="hidden" class="data_value" name="PriceList[Data][]" value="" />';
							html += '<input type="hidden" class="id_value" name="PriceList[Id][]" value="" />';
							html += '</td>';
							html += '</tr>';
							TABLE.find("tbody").append(html);

							let $trObj = $(".box_freight .r_con_table tr[data-number=\"" + $number + "\"]");
							$trObj.find("td").eq(0).text(saveAry[index].title);
							$trObj.find("td").eq(1).html(saveAry[index].weightNodeText);
							$trObj.find("td").eq(2).html(saveAry[index].priceText);
							$trObj.find(".calculation_value").val(saveAry[index].calculation);
							$trObj.find(".area_value").val(saveAry[index].weightArea);
							$trObj.find(".data_value").val(saveAry[index].data);
							$trObj.find(".id_value").val(saveAry[index].idValue);

							// 更新限制范围
							let isFirst = $trObj.prev().length ? 0 : 1;
							let min = saveAry[index].min;
							let max = saveAry[index].max;

							let $nodeMin = (index == 0 ? 0 : saveAry[index - 1].max);

							let $_nodeMax = typeof saveAry[index + 1] != "undefined" ? saveAry[index + 1].min : max;

							$trObj.attr("data-node-min", (isFirst ? 0 : $nodeMin));
							$trObj.attr("data-node-max", $_nodeMax);
							$trObj.attr("data-start", min);
							$trObj.attr("data-end", max);
						});

						TARGET.find(".btn_cancel").click();
					}, 'confirm');

					return false;
				});
			},
			Price: (price) => {
				// 价格格式
				return shop_config.currency + global_obj.ceil_price(price);
			},
			ErrorTips: (obj, tips, options = {}) => {
				// 错误提示
				let $class = options['class'] || '';
				if (tips) {
					obj.find('input').addClass("has_error");
					if (obj.find(".error_tips").length) {
						obj.find(".error_tips").text(tips);
						if ($class) {
							obj.find(".error_tips").addClass($class);
						} else {
							obj.find(".error_tips").attr("class", "error_tips");
						}
					} else {
						obj.append("<p class=\"error_tips" + ($class ? " " + $class : "") + "\">" + tips + "</p>");
					}
				} else {
					obj.removeClass("has_error").find(".error_tips").remove();
				}
			},
			NodeCheck: (opt) => {
				let error = 0;
				let $tips = "";
				let $rTips = "";
				let defaults = { obj: {}, start: 1, end: 1, min: 0, max: 0, isFirst: 0, isLast: 0, rangeAry: [] };
				opt = $.extend({}, defaults, opt || {});
				if (opt.isFirst) opt.min = 0;
				if (opt.isLast) opt.max = 99999999;

				if (opt.min == 0 && opt.max == 0) {
					// 添加
					if (opt.start !== "" && opt.end !== "") {
						// 结束重量节点
						if (opt.end <= opt.start) {
							// 结束重量节点 <= 开始重量节点
							$rTips += lang_obj.manage.shipping.tips.greater.replace("{{number}}", opt.start);
							error += 1;
						}
					}
					if (opt.rangeAry && opt.rangeAry.length) {
						// 检测该范围是否已存在
						$(opt.rangeAry).each(function (index, element) {
							if (element.start <= opt.start && opt.start <= element.end) {
								// 在当前的重量节点内 (开始)
								$tips += lang_obj.manage.shipping.tips.greater.replace("{{number}}", element.end);
								if (opt.end <= element.end) {
									$rTips += lang_obj.manage.shipping.tips.greater.replace("{{number}}", element.end);
								}
								error += 1;
							} else if (element.start <= opt.end && opt.end <= element.end && !$rTips) {
								// 在当前的重量节点内 (结束)
								$rTips += " " + lang_obj.manage.shipping.tips.less.replace("{{number}}", element.start);
								$rTips += " " + lang_obj.manage.shipping.tips.greater.replace("{{number}}", element.end);
								error += 1;
							}
							if (opt.start < element.start && opt.end > element.end && !$rTips) {
								// 完全包裹着当前的重量节点
								$rTips += " " + lang_obj.manage.shipping.tips.less.replace("{{number}}", element.start);
								error += 1;
							}
						});
					}
					if (!opt.obj.hasClass("has_error")) {
						// 防止跟“请输入数值”相冲突
						FUNC.ErrorTips(opt.obj, $tips);
					}
					let rObj = opt.obj.parents(".box_range").find(".rows:eq(1) .input");
					if (!rObj.hasClass("has_error")) {
						FUNC.ErrorTips(rObj, $rTips);
					}
				} else {
					// 修改
					if (opt.start !== "") {
						// 开始重量节点
						$tips = "";
						if (opt.min > 0 && opt.start <= opt.min) {
							// 开始重量节点 <= 最小限制
							$tips = lang_obj.manage.shipping.tips.greater.replace("{{number}}", opt.min);
							error += 1;
						}
						FUNC.ErrorTips(opt.obj, $tips);
						if (!$tips && !opt.end) {
							// 结束重量节点 不能为空
							$tips = lang_obj.manage.shipping.tips.greater.replace("{{number}}", opt.start);
							error += 1;
							// 价格范围
							FUNC.ErrorTips(opt.obj.parents(".box_range").find(".rows:eq(1) .input"), $tips);
						}
					}
					if (opt.start !== "" && opt.end !== "") {
						// 结束重量节点
						$tips = "";
						if (opt.end <= opt.start) {
							// 结束重量节点 <= 开始重量节点
							$tips = lang_obj.manage.shipping.tips.greater.replace("{{number}}", opt.start);
							error += 1;
						}
						if (opt.max > 0 && opt.end >= opt.max) {
							// 结束重量节点 >= 开始重量节点
							$tips = lang_obj.manage.shipping.tips.less.replace("{{number}}", opt.max);
							error += 1;
						}
						// 价格范围
						FUNC.ErrorTips(opt.obj.parents(".box_range").find(".rows:eq(1) .input"), $tips);
					}
				}

				return error;
			},
			LoadEditForm: (target_obj, url, type, value, callback) => {
				$.ajax({
					type: type,
					url: url + value,
					success: function (data) {
						if (target_obj == '.shipping_area_list') {
							// 左侧栏目，保留滚动条效果
							// $(target_obj).find('.jspPane').html($(data).find(target_obj).html());
							$(target_obj).html($(data).find(target_obj).html());
						} else {
							$(target_obj).html($(data).find(target_obj).html());
							jQuery.getScript(shop_config.CDN_STATIC_URL + 'js/plugin/tool_tips/tool_tips_shipping.js').done(function () {
								$('.tool_tips_ico').each(function () {
									// 弹出提示
									$(this).html('&nbsp;');
									$('#shipping').tool_tips($(this), {
										position: 'horizontal',
										html: $(this).attr('content'),
										width: 260
									});
								});
							});
						}
						callback && callback(data);
					}
				});
			},
			AreaQuickImport: () => {
				const TARGET = $(".box_area_import");
				const FORM = $("#area_import_form");
				let _countryData = {}

				frame_obj.fixed_right($(".btn_import_area"), TARGET, function ($this) {
					// 初始化内容
					TARGET.find(".global_select_box input").val("");
					FORM.find(".area_down_box").hide();
					FORM.find(".select_tips").hide();
					FORM.find('.btn_submit').prop("disabled", false);
					// 加载下拉内容
					let id = $("#edit_form input[name=SId]").val();
					$.post("/manage/set/shipping/express-select", { "sid": id, 'DeliveryRange': DeliveryRange }, function (result) {
						if (result.ret == 1) {
							let html = "";
							result.msg.forEach(function (item) {
								html += '<li class="item" data-value="' + item.id + '">' + item.title + '</li>';
							});
							if (html == "") {
								html += '<li class="no_data">' + lang_obj.manage.error.no_data + '</li>';
							}
							TARGET.find(".express_down_box .global_select_box .select_ul").html(html);
						}
					}, "json");
				});

				TARGET.find(".express_down_box .global_select_box").on("click", ".select_ul li", function () {
					let $id = $(this).attr("data-value");
					$.post("/manage/set/shipping/get-express-area", { "sid": $id, 'DeliveryRange': DeliveryRange }, function (result) {
						if (result.ret == 1) {
							FORM.find(".area_down_box").show();
							FORM.find(".area_down_box").find('input[type=text]').val('');
							FORM.find('input[name=country_hidden]').val('');
							FORM.find(".select_tips").hide();
							FORM.find('.btn_submit').prop("disabled", false);
							_countryData = {}
							let html = "";
							result.msg.forEach(function (item) {
								if (item.title != '') {
									html += '<li class="item" data-value="' + item.id + '">' + item.title + '</li>';
								}
							});
							if (html == "") {
								html += '<li class="no_data">' + lang_obj.manage.error.no_data + '</li>';
								FORM.find('.btn_submit').prop("disabled", true);
							}
							FORM.find(".area_down_box .select_ul").html(html);
						}
					}, "json");
				});

				TARGET.find(".area_down_box .global_select_box").on("click", ".select_ul li", function () {
					let _aid = $(this).attr("data-value");
					let _sid = $("#edit_form input[name=SId]").val();
					let _CurrentAId = $("#area_import_form input[name=CurrentAId]").val();
					if (!$(this).hasClass('no_data')) {
						$.post("/manage/set/shipping/check-area-country", { "aid": _aid, "sid": _sid, "CurrentAId": _CurrentAId, 'DeliveryRange': DeliveryRange }, function (result) {
							if (result.ret == 1) {
								let _countryNum = parseInt(result.msg.num)
								_countryData = result.msg.data;
								if (_countryNum > 1) {
									FORM.find(".select_tips").show();
									FORM.find('.btn_submit').prop("disabled", true);
								} else {
									FORM.find(".select_tips").hide();
									FORM.find('.btn_submit').prop("disabled", false);
								}
							}
						}, "json");
					}
				});
				TARGET.on("click", ".btn_submit", function () {
					let $html = ''
					if (_countryData) {
						let params = {
							'title': lang_obj.manage.shipping.tips.import_area_overwrite,
							'confirmBtn': lang_obj.global.confirm
						};
						global_obj.win_alert(params, function () {
							let _countryObj = _countryData;
							$('.box_area .content').html('')
							for (k in _countryObj) {
								let _this = _countryObj[k],
									_cid = _this.CId,
									_acronym = _this.Acronym,
									_country = _this.Country,
									_states = _this.States
								_total_states_count = _this.TotalStatesCount, // 总数量
									_states_count = $.isArray(_states) ? _states.length : 0, // 当前选中
									_DeliveryData = _this.DeliveryData,
									_keyCid = '_' + _cid
								if (DeliveryRange == 'zipCode') {
									// 指定邮政编码
									let rangeItemHtml = ''
									let _index = 0
									for (k in _DeliveryData) {
										let _keyIndex = '_' + _index
										let itemData = _DeliveryData[k]
										if (typeof (itemData) == "object") {
											let rangeValueHtml = ''
											let selectedData = {
												'CodeStart': '',
												'NumberRange': '',
												'Specify': '',
											}
											for (codeType in itemData) {
												selectedData[codeType] = 'selected'
												let _value = '',
													_value0 = '',
													_value1 = '',
													_specifyCodeItem = ''
												if (codeType == 'CodeStart') {
													_value = itemData[codeType]
												} else if (codeType == 'NumberRange') {
													_value0 = itemData[codeType][0]
													_value1 = itemData[codeType][1]
												} else if (codeType == 'Specify') {
													specifyItem = itemData[codeType]
													if (typeof (specifyItem) == "object") {
														for (speciKey in specifyItem) {
															_specifyCodeItem += FUNC.strReplaceBat($('.box_hiddent_html .hiddent_code_item').html(), {
																'{{keyCId}}': _keyCid,
																'{{keyIndex}}': _keyIndex,
																'disabled="disabled"': '',
																'{{zipCode}}': specifyItem[speciKey],
																'{{encodeZipCode}}': global_obj.htmlspecialchars(specifyItem[speciKey]),
															})
														}
													}
												}
												rangeValueHtml += FUNC.strReplaceBat($(`.box_hiddent_html .hiddent_value_item[data-value-type="${codeType}"]`).html(), {
													'{{keyCId}}': _keyCid,
													'{{keyIndex}}': _keyIndex,
													'disabled="disabled"': '',
													'{{value}}': _value,
													'{{value0}}': _value0,
													'{{value1}}': _value1,
													'{{specifyCodeItem}}': _specifyCodeItem
												})
											}
											rangeItemHtml += FUNC.strReplaceBat($('.box_hiddent_html .hiddent_range_item').html(), {
												'{{index}}': _index,
												'{{selectedcodestart}}': selectedData['CodeStart'],
												'{{selectednumberrange}}': selectedData['NumberRange'],
												'{{selectedspecify}}': selectedData['Specify'],
												'{{rangeValueHtml}}': rangeValueHtml
											})
										}
										_index++
									}
									let dlHtml = FUNC.strReplaceBat($('.box_hiddent_html .hiddent_dl').html(), {
										'{{CId}}': _cid,
										'disabled="disabled"': '',
										'{{CountryName}}': _country,
										'{{maxItem}}': _index,
										'{{rangeItemHtml}}': rangeItemHtml
									})
									$('.box_area .content').append(dlHtml)
								} else {
									// 指定国家/地区
									$html += '<div class="item">';
									for (i in _states) {
										$html += '<input type="checkbox" name="StatesSId[' + _cid + '][]" value="' + _states[i] + '" checked />';
									};
									$html += '<div class="img">';
									$html += '<div class="icon_flag flag_' + _acronym + '"></div>';
									$html += '</div>';
									$html += '<div class="name">' + _country;
									if ($.isArray(_states) && _total_states_count != _states_count) {
										$html += ' (' + _states_count + '/' + _total_states_count + ')';
									}
									$html += '</div>';
									$html += '<div class="area_box del"></div><div class="clear"></div>';
									$html += '<input type="checkbox" name="CId[]" value="' + _cid + '" checked />';
									$html += '</div>';
								}
							};
							if (DeliveryRange == 'country') {
								$('.box_area .content').append($html);
							}
							frame_obj.check_amount($('.box_area .range_con'))
							global_obj.win_alert_auto_close(lang_obj.manage.shipping.add_area_suc, '', 1000, '8%');
							$('.box_area_import a.close').click();
							if ($(".box_area .content .item, .box_area .content .dl").length > 0) {
								// 有国家信息
								$(".box_area .content").fadeIn();
							} else {
								// 没有国家信息
								$(".box_area .content").fadeOut();
							}
						}, 'confirm');
					} else {
						global_obj.win_alert_auto_close(lang_obj.manage.shipping.tips.submit_area_tips, 'await', 1000, '8%');
					}
				})
			},
			zipCodeInit: () => {
				frame_obj.check_amount($('.box_area .range_con'))
				$('.box_area').on('change', '.range_item select', function () {
					// 切换规则
					let codeType = $(this).val()
					let _keyCid = '_' + $(this).parents('.dl').attr('data-cid')
					let _keyIndex = '_' + $(this).parents('.range_item').attr('data-index')
					let rangeBox = $(this).parents('.range_item').find('.range_value')
					let rangeItemHtml = FUNC.strReplaceBat($(`.box_hiddent_html .hiddent_value_item[data-value-type="${codeType}"]`).html(), {
						'{{keyCId}}': _keyCid,
						'{{keyIndex}}': _keyIndex,
						'disabled="disabled"': '',
						'{{value}}': '',
						'{{value0}}': '',
						'{{value1}}': '',
						'{{specifyCodeItem}}': ''
					})
					rangeBox.find('.value_item').hide().find('.submit_input').attr('disabled', 'disabled')
					if (rangeBox.find(`.value_item[data-value-type="${codeType}"]`).length) {
						rangeBox.find(`.value_item[data-value-type="${codeType}"]`).show().find('.submit_input:visible, .submit_input[type="hidden"]').removeAttr('disabled')
					} else {
						rangeBox.append(rangeItemHtml)
						frame_obj.check_amount($('.box_area .range_con'))
					}
				}).on('click', '.range_item .range_add', function () {
					// 添加规则
					let _keyCid = '_' + $(this).parents('.dl').attr('data-cid')
					let maxItem = parseInt($(this).parents('.range_con').attr('data-max-item'))
					let _keyIndex = '_' + maxItem
					let itemCodeStart = FUNC.strReplaceBat($('.box_hiddent_html .hiddent_value_item[data-value-type="CodeStart"]').html(), {
						'{{keyCId}}': _keyCid,
						'{{keyIndex}}': _keyIndex,
						'disabled="disabled"': '',
						'{{value}}': ''
					})
					let rangeItem = FUNC.strReplaceBat($('.box_hiddent_html .hiddent_range_item').html(), {
						'{{index}}': maxItem,
						'{{selectedcodestart}}': 'selected',
						'{{selectednumberrange}}': '',
						'{{selectedspecify}}': '',
						'{{rangeValueHtml}}': itemCodeStart
					})
					$(this).parents('.range_con').append(rangeItem).attr('data-max-item', maxItem + 1)
					frame_obj.check_amount($('.box_area .range_con'))
					FUNC.rangeDelStatus($(this).parents('.range_con'))
				}).on('click', '.range_item .range_del', function () {
					// 删除规则
					var $this = $(this)
					let params = {
						'title': lang_obj.global.del_confirm,
						'confirmBtn': lang_obj.global.del,
						'confirmBtnClass': 'btn_warn'
					}
					global_obj.win_alert(params, function () {
						let rangeCon = $this.parents('.range_con')
						$this.parents('.range_item').remove()
						FUNC.rangeDelStatus(rangeCon)
					}, 'confirm')
				}).on('click', '.range_item .value_item[data-value-type="Specify"]', function (e) {
					// 默认选中填写框
					e.stopPropagation()
					$(this).find('.enter_input').focus()
				}).on('click', '.code_item .cdel', function (e) {
					// 删除邮编
					e.stopPropagation()
					$(this).parent('.code_item').remove()
				}).on('keydown', '.range_item .enter_input', function (e) {
					// 邮编添加插件
					let $value = $.trim($(this).val()),
						$key = window.event ? e.keyCode : e.which
					if ($key == 13 || ($key == 188 && !e.shiftKey)) {
						if ($value != '') {
							let save = false
							$(this).parents('.value_item').find('.code_item .cname').each(function () {
								if ($value == $.trim($(this).text())) save = true
							})
							if (save) {
								// 有相同的不再重复添加
								$(this).val('')
								return false
							}
							let _keyCid = '_' + $(this).parents('.dl').attr('data-cid')
							let _keyIndex = '_' + $(this).parents('.range_item').attr('data-index')
							let encodeValue = global_obj.htmlspecialchars($value)
							let codeItem = FUNC.strReplaceBat($('.box_hiddent_html .hiddent_code_item').html(), {
								'{{encodeZipCode}}': encodeValue,
								'{{zipCode}}': $value,
								'{{keyCId}}': _keyCid,
								'{{keyIndex}}': _keyIndex,
								'disabled="disabled"': ''
							})
							$(this).val('').before(codeItem)
						}
						if (window.event) {
							// IE
							e.returnValue = false;
						} else {
							// Firefox
							e.preventDefault();
						}
						return false;
					}
				})
			},
			rangeDelStatus: ($obj) => {
				// 只剩一个规则时 隐藏删除按钮
				if ($obj.find('.range_item').length == 1) {
					$obj.find('.range_del').hide()
				} else {
					$obj.find('.range_del').show()
				}
			},
			strReplaceBat: (str, replacements) => {
				// 同时替换多个值 replacements的key不能有|
				let regex = new RegExp(Object.keys(replacements).join("|"), "g")
				let newStr = str.replace(regex, function (matched) {
					return replacements[matched]
				})
				return newStr
			}
		}
		FUNC.Init();
	},

	shipping_warehouse_init: function () {
		frame_obj.del_init($("#shipping .r_con_table"));
		frame_obj.select_all($("input[name=select_all]"), $("input[name=select]"), $(".table_menu_button").find(".del, .edit")); //批量操作
		frame_obj.del_bat($(".table_menu_button .del"), $("input[name=select]"), "", function (id_list) {
			// 批量删除
			let params = {
				"title": lang_obj.global.del_confirm,
				"confirmBtn": lang_obj.global.del,
				"confirmBtnClass": "btn_warn"
			};
			global_obj.win_alert(params, function () {
				$(".btn_warehouse_delete").click();
				deleteWarehouse(id_list, 0);
			}, "confirm");
		});
		$('#shipping .inside_menu').insideMenu();

		// 元素拖动
		frame_obj.dragsort($('#shipping .tbody'), '/manage/set/shipping/warehouse-order', '.first_box>.tr .myorder', '.c_id', '<div class="first_box"></div>');

		// 添加仓库
		frame_obj.fixed_right($(".list_menu_button .add, .warehouse_add_btn"), '.warehouse_add_box', function (_this) {
			$(".warehouse_add_box .top_title .add").show().siblings("span").hide();
			$(".warehouse_add_box input[name=id]").val(0);
			$(".warehouse_add_box input[name=Name]").val("");
			$(".warehouse_add_box .logistics_list .input_checkbox_box").removeClass("checked").find("input").prop("checked", false);
		});
		frame_obj.fixed_right($(".r_con_table .icon_edit"), ".warehouse_add_box", function (_this) {
			let $obj = $(_this).parents(".tr, tr");
			$id = $obj.data("id"),
				$name = $obj.find(".name").text(),
				$data = $obj.find(".logistics").data("json");
			$(".warehouse_add_box .top_title .edit").show().siblings("span").hide();
			$(".warehouse_add_box input[name=id]").val($id);
			$(".warehouse_add_box input[name=Name]").val($name);
			$(".warehouse_add_box .logistics_list .input_checkbox_box").removeClass("checked").find("input").prop("checked", false);
			if ($data.length > 0) {
				for (let k in $data) {
					$(".warehouse_add_box .input_checkbox_box input:checkbox[value=" + $data[k] + "]").prop("checked", true).parent().parent().addClass("checked");
				}
			}
		});

		// 提交
		frame_obj.submit_object_init($("#warehouse_edit"), "/manage/set/shipping/warehouse", function () {
			let $repeat = 0,
				$name = $.trim($("#warehouse_edit input[name=Name]").val()),
				$id = parseInt($("#warehouse_edit input[name=id]").val());
			$("#shipping .mock_table .tbody .tr").each(function () {
				if ($(this).data("name") == $name) {
					$repeat += 1;
				}
			});
			if (($id == 0 && $repeat > 0) || ($id > 0 && $repeat > 1)) {
				$("#warehouse_edit input[name=Name]").css("border", "1px red solid").focus();
				global_obj.win_alert_auto_close(lang_obj.manage.shipping.tips.warehouse_name, "await", 1000, "8%");
				return false;
			}
		});

		// 批量删除
		frame_obj.fixed_right($(".btn_warehouse_delete"), ".warehouse_reset_box", function (_this) {
			$("#warehouse_reset").hide();
			$("#box_circle_container").show();
			frame_obj.circle_progress_bar({ "percent": 0 });
		});
		frame_obj.box_type_menu();
		deleteWarehouse = function (id, start) {
			$.post("/manage/set/shipping/warehouse-delete", { "id": id, "start": start }, function (result) {
				// 返回百分之多少
				if (result.ret == 1) {
					// 继续
					frame_obj.circle_progress_bar({ "percent": result.msg.percent });
					setTimeout(function () {
						deleteWarehouse(id, result.msg.start);
					}, 500);
				} else if (result.ret == 2) {
					// 完成
					frame_obj.circle_progress_bar({
						"percent": result.msg.percent,
						"comfirmButton": 1
					});
				} else {
					// 失败
					global_obj.win_alert_auto_close(result.msg, 'fail', 1000, '8%');
				}
			}, "json");
			return false;
		}
		$("#box_circle_container").on("click", ".btn_progress_completed .btn_confirm", function () {
			// 刷新当前页面
			window.location.reload();
		});

		const FUNC = {
			Open: "",
			Related: (obj, id, page, totalPage, event) => {
				page += 1;
				if (page <= totalPage) {
					obj.find('.products_load_more').remove();
					$.post(
						'/manage/set/shipping/warehouse-related-products-info',
						{
							'id': id,
							'page': page,
							'totalPage': totalPage
						},
						function (result) {
							if (result.ret == 1) {
								let html = ''
								let count = 0
								let newPage = parseInt(result.msg.page)
								for (let status in result.msg.products) {
									let params = result.msg.products[status]
									html += '<div class="products_list">'
									html += '<div class="list_item">'
									html += '<div class="item_img fl">'
									html += '<a href="' + params.url + '" target="_blank"><img src="' + params.img + '" /></a>'
									html += '</div>'
									html += '<div class="item_info fr">'
									html += '<div class="info_name">' + params.name + '</div>'
									html += '</div>'
									html += '<div class="clear"></div>'
									html += '</div>'
									html += '</div>'
									count++
								}
								if (newPage < totalPage) {
									// 加载更多
									html += '<a class="products_load_more" href="javascript:;">加载更多</a>';
								}
								obj.find('.products_box').append(html)
								// 控制显示
								let boxHeight = obj.find('.products_container').height()
								let clickHeight = event.currentTarget.offsetTop
								let tableHeight = $('.center_container_1400').height()
								if (clickHeight + boxHeight > tableHeight) {
									obj.find('.products_container').css({ 'top': 'auto', 'bottom': '100%' })
								}
								obj.addClass('current').find('.products_container').fadeIn()
								// 更新页数
								obj.attr('data-page', newPage)
							}
						},
						'json'
					)
				}
			}
		}

		$('.related_products').on('click', function (event) {
			let $this = $(this)
			let id = $this.attr('data-id')
			let page = parseInt($this.attr('data-page'))
			let totalPage = parseInt($this.attr('data-total-page'))
			if (!$this.find('.products_txt i').length) return false
			FUNC.Related($this, id, page, totalPage, event)
		}).on('mouseleave', function () {
			let $this = $(this)
			$this.attr('data-page', 0)
			$this.removeClass('current').find('.products_container').fadeOut(500, function () {
				$this.find('.products_box').html('')
			})
		}).on('click', '.products_load_more', function (event) {
			let $this = $(this)
			let id = $this.attr('data-id')
			let page = parseInt($this.attr('data-page'))
			let totalPage = parseInt($this.attr('data-total-page'))
			FUNC.Related($this, id, page, totalPage, event)
		})
	},

	photo_global: {
		list: function () {//图片银行列表
			$('body, html').on('click', '.add', function () {//添加
				return false;
			}).on('click', '.photo_list .item', function () {//勾选图片框
				var item_parent = $(this),
					$sort = $('#photo input[name=sort]').val(),
					$val = item_parent.find('input').val();
				if (item_parent.hasClass('cur')) {
					item_parent.removeClass('cur');
					item_parent.find('input').prop('checked', false);
					if ($sort && global_obj.in_array($val, $sort.split('|'))) {
						$('#photo input[name=sort]').val($sort.replace('|' + $val + '|', '|'));
					}
				} else {
					if ($('.photo_list.auto_load_photo').length && ($('input[name=type]').val() == 'products' || $('input[name=type]').val() == 'review')) {
						var box_id = $('input[name=id]').val().replace(';', '='),
							img_len = parseInt(parent.$('#' + box_id).parents('.multi_img').find('.img.isfile').length),
							maxpic = parseInt($('input[name=maxpic]').val()),
							cur_len = parseInt($('.photo_list_box').find('.item.cur').length);
						if (maxpic == img_len) maxpic++;
						if (maxpic - (img_len + cur_len) <= 0) {
							global_obj.win_alert_auto_close(lang_obj.manage.account.picture_tips.replace('xxx', parseInt($('input[name=maxpic]').val())));
							return false;
						}
					}
					item_parent.addClass('cur');
					item_parent.find('input').prop('checked', true);
					if ($sort && !global_obj.in_array($val, $sort.split('|'))) {
						$('#photo input[name=sort]').val($sort + $val + '|');
					}
				}
				if ($('#photo_list_form input:checkbox:checked').length) {
					$('.list_menu .move,.list_menu .del').css('display', 'block');
				} else {
					$('.list_menu .move').hide();
				}
				return false;
			}).on('click', '.photo_list .item .zoom', function (e) {
				e.stopPropagation();
			}).on('click', '.refresh', function () { //单个移动（已移除）
				frame_obj.pop_iframe_page_init('/manage/set/photo/move?PId=' + $(this).prev().val(), 'user_group');
			}).on('click', '.move', function () { //批量移动
				var $obj = $('.box_move_edit'),
					$html = '';
				$obj.find('input[name=PId], input[name=PId\\[\\]]').remove();
				$('.PIds:checked').each(function () {
					$html += '<input type="hidden" name="PId[]" value="' + $(this).val() + '" />';
				});
				$obj.find('.rows').after($html);
				return false;
			});

			frame_obj.fixed_right($('.list_menu .add, .photo_index .btn_add_item'), '.box_photo_edit', function () {
				if (!$('.photo_multi_img .item').length) $("#box_photo_edit .btn_submit").prop("disabled", true);
			}); //添加
			frame_obj.fixed_right($('.list_menu .move'), '.box_move_edit'); //移动
			//var is_animate=0;
			//$('.auto_load_photo').scroll(function(){
			//	var $obj=$('.auto_load_photo'),
			//		viewHeight=$obj.outerHeight(true), //可见高度
			//		contentHeight=$obj.get(0).scrollHeight, //内容高度
			//		scrollHeight=$obj.scrollTop(), //滚动高度
			//		page=parseInt($obj.attr('data-page')),
			//		total_pages=parseInt($obj.attr('data-total-pages'));
			//	if((contentHeight-viewHeight<=scrollHeight) && is_animate==0 && page<=total_pages){
			//		is_animate=1;
			//		set_obj.load_edit_form('.photo_list_box', $('.photo_list_box').attr('data-page-url'), 'get', '&Page='+(page+1), function(){
			//			$obj.attr('data-page', (page+1));
			//			is_animate=0;
			//		}, 'append');
			//	}
			//	if(page>=total_pages && is_animate==0){
			//		is_animate=1;
			//	}
			//	if(page>=total_pages && is_animate==1 && contentHeight-viewHeight==scrollHeight){
			//		global_obj.win_alert_auto_close(lang_obj.manage.sales.lastpage, 'await', 2000, '8%', 0);
			//	}
			//});


			//提交
			frame_obj.submit_form_init($('#box_photo_edit'), '/Setting/Photos');
			frame_obj.submit_form_init($('#move_edit_form'), '/Setting/Photos');

			// 搜索筛选
			frame_obj.filterRight({
				"onSubmit": function ($obj) {
					// 标签
					let tagsId = [];
					$obj.find("input[name=\"photo_tagsCurrent[]\"]").each(function (index, element) {
						tagsId[index] = $(element).val();
					});
					tagsId = tagsId.join(",");

					$('.search_box input[name=TagsId]').val(tagsId);
				}
			});
		},
		tags: function () { //产品标签
			frame_obj.fixed_right($('.btn_tags_edit_bat, .btn_tags_edit'), '.box_photo_tags_edit', '', function (obj) {
				var $PId,
					$Type;
				if (obj.hasClass('btn_tags_edit_bat')) {
					$('span.type_single').hide();
					$('span.type_bat').show().find('span').text($('input[name=select]:checked').length);
					$Type = 'editBat';
					var id_list = '';
					$('input[name=select]').each(function (index, element) {
						id_list += $(element).get(0).checked ? $(element).val() + '-' : '';
					});
					if (id_list) {
						id_list = id_list.substring(0, id_list.length - 1);
						$PId = id_list;
					} else {
						global_obj.win_alert(lang_obj.global.dat_select);
						return false;
					}
				} else {
					$('span.type_single').show();
					$('span.type_bat').hide();
					$PId = obj.attr('data-pid');
					$Type = 'edit';
				}
				$('.box_photo_tags_edit input[name=PId]').val($PId);
				$('.box_photo_tags_edit input[name=Type]').val($Type);
				$.get('/manage/set/photo/tags?id=' + $PId + '&type=' + $Type, '', function (data) {
					$('.box_photo_tags_edit .box_option_list').html(data);
					//多功能选项框
					frame_obj.box_option_list();
					frame_obj.box_option_button_choice();
				}, 'html');
			});
			frame_obj.submit_form_init($('#photo_tags_edit_form'), '', '', '', function () {
				global_obj.win_alert_auto_close(lang_obj.global.save_success, '', 400, '8%');
				setTimeout(function () {
					location.href = location.href;
				}, 400);
			});
		},
		index: function () {
			frame_obj.select_all($('input[name=select_all]'), $('input[name=select]')); // 批量操作
			frame_obj.del_bat($('.table_menu_button .del'), $('input[name=select]'), '/api/SettingPhotos/DelPhoto'); // 批量删除
			// 复制链接
			var clipboard = new ClipboardJS('.btn_copy');
			clipboard.on('success', function (e) {
				alert(lang_obj.global.copy_complete);
			});
		}
	},

	file_global: {
		list: function () {//视频银行列表
			$('body, html').on('click', '.add', function () {//添加
				return false;
			}).on('click', '.file_list .item', function () {//勾选视频框
				var item_parent = $(this),
					$sort = $('#file input[name=sort]').val(),
					$val = item_parent.find('input').val();
				if (item_parent.hasClass('cur')) {
					item_parent.removeClass('cur');
					item_parent.find('input').prop('checked', false);
					if ($sort && global_obj.in_array($val, $sort.split('|'))) {
						$('#file input[name=sort]').val($sort.replace('|' + $val + '|', '|'));
					}
				} else {
					if ($('.file_list.auto_load_file').length && ($('input[name=type]').val() == 'products' || $('input[name=type]').val() == 'review')) {
						var box_id = $('input[name=id]').val().replace(';', '='),
							file_len = parseInt(parent.$('#' + box_id).parents('.multi_img').find('.img.isfile').length),
							maxfile = parseInt($('input[name=maxfile]').val()),
							cur_len = parseInt($('.file_list_box').find('.item.cur').length);
						if (maxfile == file_len) maxfile++;
						if (maxfile - (file_len + cur_len) <= 0) {
							global_obj.win_alert_auto_close(lang_obj.manage.file.video_tips.replace('xxx', parseInt($('input[name=maxfile]').val())));
							return false;
						}
					}
					item_parent.addClass('cur');
					item_parent.find('input').prop('checked', true);
					if ($sort && !global_obj.in_array($val, $sort.split('|'))) {
						$('#file input[name=sort]').val($sort + $val + '|');
					}
				}
				if ($('#file_list_form input:checkbox:checked').length) {
					$('.list_menu .move,.list_menu .del').css('display', 'block');
				} else {
					$('.list_menu .move').hide();
				}
				return false;
			}).on('click', '.file_list .file_item', function () {
				obj = $(this);
				$sort = $('#file input[name=sort]').val();
				$val = obj.find('input').val();

				if (obj.hasClass('cur')) {
					obj.removeClass('cur').find('input').prop('checked', false);
					$('#file input[name=sort]').val('|');
				} else {
					obj.addClass('cur').find('input').prop('checked', true);
					obj.siblings().removeClass('cur').find('input').prop('checked', false);
					$('#file input[name=sort]').val('|' + $val + '|');
				}
				return false;
			}).on('click', '.file_list .item .zoom', function (e) {
				e.stopPropagation();
			}).on('click', '.refresh', function () { //单个移动（已移除）
				frame_obj.pop_iframe_page_init('/manage/set/photo/file-move?FId=' + $(this).prev().val(), 'user_group');
			}).on('click', '.move', function () { //批量移动
				var $obj = $('.box_move_edit'),
					$html = '';
				$obj.find('input[name=FId], input[name=FId\\[\\]]').remove();
				$('.PIds:checked').each(function () {
					$html += '<input type="hidden" name="FId[]" value="' + $(this).val() + '" />';
				});
				$obj.find('.rows').after($html);
				return false;
			});

			frame_obj.fixed_right($('.list_menu .add, .file_index .btn_add_item'), '.box_file_edit', function () {
				if (!$('.file_multi_img .item').length) $("#box_file_edit .btn_submit").prop("disabled", true);
			}); //添加
			frame_obj.fixed_right($('.list_menu .move'), '.box_move_edit'); //移动
			var is_animate = 0;
			$('.auto_load_file').scroll(function () {
				var $obj = $('.auto_load_file'),
					viewHeight = $obj.outerHeight(true), //可见高度
					contentHeight = $obj.get(0).scrollHeight, //内容高度
					scrollHeight = $obj.scrollTop(), //滚动高度
					page = parseInt($obj.attr('data-page')),
					total_pages = parseInt($obj.attr('data-total-pages'));
				if ((contentHeight - viewHeight <= scrollHeight) && is_animate == 0 && page <= total_pages) {
					is_animate = 1;
					set_obj.load_edit_form('.file_list_box', $('.file_list_box').attr('data-page-url'), 'get', '&Page=' + (page + 1), function () {
						$obj.attr('data-page', (page + 1));
						is_animate = 0;
					}, 'append');
				}
				if (page >= total_pages && is_animate == 0) {
					is_animate = 1;
				}
				if (page >= total_pages && is_animate == 1 && contentHeight - viewHeight == scrollHeight) {
					global_obj.win_alert_auto_close(lang_obj.manage.sales.lastpage, 'await', 2000, '8%', 0);
				}
			});

			//提交
			frame_obj.submit_form_init($('#box_file_edit'), '/manage/set/photo/file');
			frame_obj.submit_form_init($('#move_edit_form'), '/manage/set/photo/file');

			// 搜索筛选
			frame_obj.filterRight({
				"onSubmit": function ($obj) {
					// 标签
					let tagsId = [];
					$obj.find("input[name=\"file_tagsCurrent[]\"]").each(function (index, element) {
						tagsId[index] = $(element).val();
					});
					tagsId = tagsId.join(",");

					$('.search_box input[name=TagsId]').val(tagsId);
				}
			});
		}
	},

	photo_choice_init: function () {
		frame_obj.category_wrap_page_init();
		var id = $('input[name=id]').val(),//显示元素的ID
			type = $('input[name=type]').val(),//类型
			maxpic = $('input[name=maxpic]').val(),//最大允许图片数
			number = 0,//执行次数
			fileHiddenObj = $("input[name='file2BigName_hidden_text']"),
			fileName = "</br>";

		// 如果 type 为空，尝试从其他 type 元素获取值
		if (!type || type.trim() === '') {
			$('input[name=type]').each(function() {
				var val = $(this).val();
				if (val && val.trim() !== '') {
					type = val;
					return false; // 跳出循环
				}
			});
		}
		$('form[name=upload_form]').fileupload({
			url: '/api/FileUpload/UploadType?size=' + type,
			acceptFileTypes: /^image\/(gif|jpe?g|png|webp|x-icon|jp2)$/i,
			disableImageResize: false,
			imageMaxWidth: 99999,
			imageMaxHeight: 99999,
			imageForceResize: false,
			maxFileSize: ********,
			maxHeight: 5000,
			messages: {
				maxFileSize: lang_obj.manage.photo.size_limit,
				acceptFileTypes: lang_obj.manage.photo.type_error,
				maxHeight: lang_obj.manage.photo.height_limit
			},
			done: function (error,data) {
				const response = data.result;
				const imgpath = response?.filePath || '';
				const surplus = response?.surplus || '';
				const name = response?.name || '';

				function isHasImg(pathImg) {
					if (pathImg.length == 0) {
						return false;
					}
					var isExist = true;
					$.ajax('/api/SettingPhotos/HasImg', {
						type: 'get',
						async: false,//取消ajax的异步实现
						timeout: 1000,
						dataType: "json",
						data: { PicPath: pathImg },
						success: function (data) {
							if (data.ret == 0) {
								isExist = false;
							}
						},
					});
					return isExist;
				}

				if (error) {
					fileName += name + " </br>";
					fileHiddenObj.val(fileName);
					fileHiddenObj.trigger('change');
					frame_obj.photo_choice_return(id, type, maxpic, 1, imgpath, surplus, ++number, name);
				} else if (!isHasImg(imgpath)) {
					var time = setInterval(function () { //递归检查图片是否已经上传完成
						if (isHasImg(imgpath)) {
							alert(3);
							frame_obj.photo_choice_return(id, type, maxpic, 1, imgpath, surplus, ++number, name);
							clearInterval(time);
						}
					}, 500);
				} else {
					frame_obj.photo_choice_return(id, type, maxpic, 1, imgpath, surplus, ++number, name);
				}
			},
			callback: function (imgpath, surplus, name, error) { //上传后的文件/0/原文件名
			
				function isHasImg(pathImg) {
					if (pathImg.length == 0) {
						return false;
					}
					var isExist = true;

					$.ajax('/manage/action/has-img', {
						type: 'get',
						async: false,//取消ajax的异步实现
						timeout: 1000,
						dataType: "json",
						data: { PicPath: pathImg },
						success: function (data) {
							if (data.ret == 0) {
								isExist = false;
							}
						},
					});
					return isExist;
				}
				if (error) {
					fileName += name + " </br>";
					fileHiddenObj.val(fileName);
					fileHiddenObj.trigger('change');
					frame_obj.photo_choice_return(id, type, maxpic, 1, imgpath, surplus, ++number, name);
				} else if (!isHasImg(imgpath)) {
					var time = setInterval(function () { //递归检查图片是否已经上传完成
						if (isHasImg(imgpath)) {
							frame_obj.photo_choice_return(id, type, maxpic, 1, imgpath, surplus, ++number, name);
							clearInterval(time);
						}
					}, 500);
				} else {
					frame_obj.photo_choice_return(id, type, maxpic, 1, imgpath, surplus, ++number, name);
				}
			},
			processfail: function (e, data) {
				console.log(data);
				$('form[name=upload_form]').trigger('dragleave');
				let files = data.files;
				let error = "";
				$.each(files, function (index, element) {
					if (typeof element.error !== "undefined") {
						error += element.error;
					}
				});
				error == "" && (error = lang_obj.manage.photo.type_error);
				global_obj.win_alert_auto_close(error, 'await', 1000, '8%', 0);
			}
		}).on('fileuploadadd', function (e, data) {
			$.fn.fileUploadAdd(data);
		});
		$('form[name=upload_form]').fileupload(
			'option',
			'redirect',
			window.location.href.replace(/\/[^\/]*$/, '/cors/result.html?%s')
		);
		set_obj.photo_global.list();

		// 复制链接
		var clipboard = new ClipboardJS('.icon-copy1');
		clipboard.on('success', (e) => {
			alert(lang_obj.global.copy_complete)
		})

		_PIdAry = new Array();
		$('#photo').on('click', '.photo_list_box .item', function () {
			var _this = $(this),
				_PId = _this.find('input').val(),
				_html = _this.html(),
				_cur = _this.hasClass('cur') ? false : true,
				_maxpic = $('#photo_list_form').find('input[name=maxpic]').val();

			if (_cur) {
				if ($.inArray(_PId, _PIdAry) >= 0) return '';
				_html = _html.replace('checkPId', 'PId');
				_PIdAry.push(_PId);
				if (_maxpic == 1) {
					$('.photo_choose_box').find('.item').remove();
					$('#photo_list_form').find('.photo_list_box').find('input[value="' + _PId + '"]').parents('.item').siblings().removeClass('cur').find('input').prop('checked', false);
					_PIdAry = new Array();
					_PIdAry.push(_PId);
				}
				$('#photo_list_form').find('.photo_choose_box').append('<div class="item"><div class="close_btn"><i class="iconfont icon_menu_close"></i></div>' + _html + '</item>').find('.bg_no_table_data').hide();
				$('#photo_list_form').find('.photo_choose_box').find('.item').find('input').prop('checked', true);
			} else {
				$.each(_PIdAry, function (k, v) {
					if (v == _PId) {
						_PIdAry.splice(k, 1);
					}
				})
				$('#photo_list_form').find('.photo_choose_box').find('input[value=' + _PId + ']').parents('.item').remove();
			}
			if (!_PIdAry || _PIdAry.length == 0) {
				$('#photo_list_form').find('.photo_choose_box').find('.bg_no_table_data').show();
			}
		})

		$('#photo').on('click', ' .turn_page_box a , .search_btn ', function () {
			var url = $(this).attr('href');

			if ($(this).hasClass('search_btn')) {
				url = '/manage/set/photo/choice?' + $('#photo').find('.search_form').find('form').serialize();
			}

			$.get(url, '', function (data) {
				if (data) {
					var photo_box = $(data).contents().find('.photo_list_box').html();
					var turn_page_box = $(data).contents('.turn_page_box').html();
					$('#photo').find('.photo_list_box').html(photo_box);
					$('#photo').find('.turn_page_box').html(turn_page_box);
					if (_PIdAry) {
						$.each(_PIdAry, function (k, v) {
							$('#photo .photo_list .photo_list_box').find('input[value=' + v + ']').parents('.item').click();
						})
					}
				}
			})
			return false;
		}).on('click', '.photo_choose_box .close_btn', function () {
			let _this = $(this),
				_PId = _this.parent().find('input').val();
			$.each(_PIdAry, function (k, v) {
				if (v == _PId) {
					_PIdAry.splice(k, 1);
					_this.parent().remove();
					$('#photo_list_form').find('.photo_list_box').find('input[value=' + _PId + ']').parents('.item').removeClass('cur');
				}
			})
		})

	},

	photo_init: function () {
		set_obj.photo_global.list();
		set_obj.photo_global.index();
	},

	photo_upload_init: function () {
		$('form[name=upload_form]').fileupload({
			/*url: '/manage/action/file-upload-plugin?size=photo',*/
			url: '/api/FileUpload/upload',
			acceptFileTypes: /^image\/(gif|jpe?g|png|x-icon|webp|jp2)$/i,
			disableImageResize: false,
			imageMaxWidth: 2000,
			imageMaxHeight: 99999,
			imageForceResize: false,
			maxFileSize: ********,
			maxNumberOfFiles: 1000,
			maxHeight: 5000,
			messages: {
				maxFileSize: lang_obj.manage.photo.size_limit,
				maxNumberOfFiles: lang_obj.manage.account.picture_tips.replace('xxx', 1000),
				acceptFileTypes: lang_obj.manage.photo.type_error,
				maxHeight: lang_obj.manage.photo.height_limit
			},
			/*callback: function (imgpath, surplus, name) {*/
			done: function (e, data) {
				const response = data.result;
				const imgpath = response?.filePath || '';
				const surplus = response?.surplus || '';
				const name = response?.name || '';
				const size = response?.size || '';
				const oriname = response?.oriname || '';
				if (!imgpath) {
					// 没有返回图片路径
					return false;
				}
				if ($('#PicDetail .pic').length >= 1000) {
					global_obj.win_alert(lang_obj.manage.account.picture_tips.replace('xxx', 1000));
					return;
				}
				
				$('#PicDetail').append('<div class="item">' + frame_obj.upload_img_detail(imgpath) + '<span>' + lang_obj.global.del + '</span><input type="hidden" name="FileSize[]" value="' + size + '" /><input type="hidden" name="FileOriname[]" value="' + oriname + '" /><input type="hidden" name="PicPath[]" value="' + imgpath + '" /><input type="text" maxlength="30" class="box_input" value="' + name + '" name="Name[]" placeholder="' + lang_obj.global.picture_name + '" notnull></div>');
				$('#PicDetail div span').off('click').on('click', function () {
					var $this = $(this);
					let params = {
						'title': lang_obj.global.del_confirm,
						'confirmBtn': lang_obj.global.del,
						'confirmBtnClass': 'btn_warn'
					};
					global_obj.win_alert(params, function () {
						var index = $this.parents('.item').index();
						$.ajax({
							url: '/manage/set/photo/upload-del?Path=' + $this.prev().children('img').attr('src') + '&Index=' + index,
							success: function (data) {
								$this.parents('.item').remove();
								$('#PicDetail').siblings('.photo_multi_img.template-box').find('.template-download').eq(index).remove();
								if ($('#PicDetail .item').length > 0) {
									$("#box_photo_edit .btn_submit").prop("disabled", false);
								} else {
									$("#box_photo_edit .btn_submit").prop("disabled", true);
								}
							}
						});
					}, 'confirm', 1);
					if ($('#box_photo_edit').length) global_obj.div_mask(1);
					return false;
				});
				if ($('#PicDetail .item').length > 0) {
					$("#box_photo_edit .btn_submit").prop("disabled", false);
				} else {
					$("#box_photo_edit .btn_submit").prop("disabled", true);
				}
				
			}
		}).on('fileuploadadd', function (e, data) {
			$.fn.fileUploadAdd(data);
		/*	$('.fileupload-buttonbar .template-upload').css('display', 'none');*/
		}).on('fileuploadstop', function (e) { // 新增停止事件监听
			// 所有文件上传完成后隐藏上传按钮栏
			$('.fileupload-buttonbar .template-upload').css('display', 'none');
		});
		$('form[name=upload_form]').fileupload(
			'option',
			'redirect',
			window.location.href.replace(/\/[^\/]*$/, '/cors/result.html?%s')
		);
		set_obj.photo_global.tags();
		$('#photo .inside_menu').insideMenu();
	},
	file_img_init: function () {
		const FUNC = {
			Type: (file) => {
				// 文件类型识别
				let point = file.lastIndexOf(".");
				let type = file.substr(point);
				let icon = "";
				switch (type) {
					case ".jpg":
						icon = "jpg";
						break;
					case ".jpeg":
						icon = 'jpeg';
						break;
					case ".png":
						icon = 'png';
						break;
					case ".gif":
						icon = 'gif';
						break;
					case ".ico":
						icon = 'ico';
						break;
					case ".webp":
						icon = 'webp';
						break;
				}
				return { "type": type, "icon": icon };
			},
			List: () => {
				frame_obj.select_all($('input[name=select_all]'), $('input[name=select]')); // 批量操作
				frame_obj.del_bat($('.table_menu_button .del'), $('input[name=select]'), '/manage/set/photo/file-batch-delete'); // 批量删除
				// 复制链接
				let clipboard = new ClipboardJS('.btn_copy');
				clipboard.on('success', function (e) {
					alert(lang_obj.global.copy_complete);
				});

				$('#photo').on('click', '.photo_list .item .img', function () {
					// 勾选图片框
					var item_parent = $(this).parent('.item'),
						$sort = $('#photo input[name=sort]').val(),
						$val = $(this).find('input').val();
					if (item_parent.hasClass('cur')) {
						item_parent.removeClass('cur');
						$(this).find('input').prop('checked', false);
						if ($sort && global_obj.in_array($val, $sort.split('|'))) {
							$('#photo input[name=sort]').val($sort.replace('|' + $val + '|', '|'));
						}
					} else {
						if ($('.photo_list.auto_load_photo').length && ($('input[name=type]').val() == 'products' || $('input[name=type]').val() == 'review')) {
							var box_id = $('input[name=id]').val().replace(';', '='),
								img_len = parseInt(parent.$('#' + box_id).parents('.multi_img').find('.img.isfile').length),
								maxpic = parseInt($('input[name=maxpic]').val()),
								cur_len = parseInt($('.photo_list_box').find('.item.cur').length);
							if (maxpic == img_len) maxpic++;
							if (maxpic - (img_len + cur_len) <= 0) {
								global_obj.win_alert_auto_close(lang_obj.manage.account.picture_tips.replace('xxx', parseInt($('input[name=maxpic]').val())));
								return false;
							}
						}
						item_parent.addClass('cur');
						$(this).find('input').prop('checked', true);
						if ($sort && !global_obj.in_array($val, $sort.split('|'))) {
							$('#photo input[name=sort]').val($sort + $val + '|');
						}
					}
					if ($('#photo_list_form input:checkbox:checked').length) {
						$('.list_menu .move,.list_menu .del').css('display', 'block');
					} else {
						$('.list_menu .move').hide();
					}
					return false;
				}).on('click', '.photo_list .item .zoom', function (e) {
					e.stopPropagation();
				});

				frame_obj.fixed_right($('.list_menu .add, .photo_index .btn_add_item'), '.box_photo_edit', function () {
					// 添加文件
					$("#box_photo_edit .btn_submit").prop("disabled", true);
				});

				frame_obj.fixed_right($('.btn_tags_edit_bat, .btn_tags_edit'), '.box_photo_tags_edit', '', function (obj) {
					// 添加 Or 编辑 标签
					let $FId = "", $Type = "";
					if (obj.hasClass('btn_tags_edit_bat')) {
						$('span.type_single').hide();
						$('span.type_bat').show().find('span').text($('input[name=select]:checked').length);
						$Type = 'editBat';
						let id_list = '';
						$('input[name=select]').each(function (index, element) {
							id_list += $(element).get(0).checked ? $(element).val() + '-' : '';
						});
						if (id_list) {
							id_list = id_list.substring(0, id_list.length - 1);
							$FId = id_list;
						} else {
							global_obj.win_alert(lang_obj.global.dat_select);
							return false;
						}
					} else {
						$('span.type_single').show();
						$('span.type_bat').hide();
						$FId = obj.attr('data-id');
						$Type = 'edit';
					}
					$('.box_photo_tags_edit input[name=id]').val($FId);
					$('.box_photo_tags_edit input[name=Type]').val($Type);
				 
					$.get('/manage/set/photo/file-tags?id=' + $FId + '&type=' + $Type, '', function (data) {
						$('.box_photo_tags_edit .box_option_list').html(data);
						// 多功能选项框
						frame_obj.box_option_list();
						frame_obj.box_option_button_choice();
					}, 'html');
				});

				// 提交
				frame_obj.submit_form_init($('#box_photo_edit'), '/manage/set/photo/file');
				frame_obj.submit_form_init($('#photo_tags_edit_form'), '', '', '', function () {
					global_obj.win_alert_auto_close(lang_obj.global.save_success, '', 400, '8%');
					setTimeout(function () {
						location.href = location.href;
					}, 400);
				});

				// 搜索筛选
				frame_obj.filterRight({
					"onSubmit": function ($obj) {
						// 标签
						let tagsId = [];
						$obj.find("input[name=\"file_tagsCurrent[]\"]").each(function (index, element) {
							tagsId[index] = $(element).val();
						});
						tagsId = tagsId.join(",");

						$('.search_box input[name=tagsId]').val(tagsId);
					}
				});
			},
			Upload: () => {
				// 文件上传
				$('form[name=upload_form]').fileupload({
					url: '/api/FileUpload/upload',
					/*url: '/manage/action/file-upload-plugin?size=photo&position=set',*/
					// pdf、rar、zip、word、excel、ppt、txt、csv
					acceptFileTypes: /^image\/(gif|jpe?g|png|x-icon|webp|jp2)$/i,
					disableImageResize: false,
					imageMaxWidth: 2000,
					imageMaxHeight: 99999,
					dataType: 'json',
					async: true,
					imageForceResize: false,
					maxFileSize: ********,
					maxNumberOfFiles: 20,
					messages: {
						maxFileSize: lang_obj.manage.file.size_limit,
						maxNumberOfFiles: lang_obj.manage.account.file_tips.replace('{{count}}', 20),
						acceptFileTypes: lang_obj.manage.file.type_error,
					},
					done: function (e, data) {
						const response = data.result;
						const filePath = response?.filePath || '';
						const surplus = response?.surplus || '';
						const name = response?.name || '';
						const size = response?.size || '';
						const oriname = response?.oriname || '';
						if (!filePath) {
							// 没有返回图片路径
							return false;
						}
						let fileType = FUNC.Type(filePath);
						$('#PicDetail').append('<div class="item"><svg class="icon" aria-hidden="true"><use xlink:href="#icon-' + fileType.icon + '"></use></svg><span>' + lang_obj.global.del + '</span><input type="hidden" name="FileSize[]" value="' + size + '" /><input type="hidden" name="FileOriname[]" value="' + oriname + '" /><input type="hidden" name="FilePath[]" value="' + filePath + '" /><input type="text" maxlength="100" class="box_input" value="' + name + '" name="Name[]" placeholder="' + lang_obj.global.file_name + '" notnull></div>');

						if ($('#PicDetail .item').length > 0) {
							$("#box_photo_edit .btn_submit").prop("disabled", false);
						} else {
							$("#box_photo_edit .btn_submit").prop("disabled", true);
						}
					}
				}).on('fileuploadadd', function (e, data) {
					let files = data.files;
					for (let i = 0; i < files.length; i++) {
						let file = files[i];
						let _fileSize = file.size;
						let _fileType = file.type;
						if (_fileType == 'video/mp4' && _fileSize > ********) {
							global_obj.win_alert_auto_close(lang_obj.manage.file.video_size_limit, 'await', 1000, '8%')
							return false
						}
					}
				});
				$('form[name=upload_form]').fileupload(
					'option',
					'redirect',
					window.location.href.replace(/\/[^\/]*$/, '/cors/result.html?%s')
				);
				$('#PicDetail').on('click', 'div span', function () {
					// 删除文件
					let $this = $(this);
					let params = {
						'title': lang_obj.global.del_confirm,
						'confirmBtn': lang_obj.global.del,
						'confirmBtnClass': 'btn_warn'
					};
					global_obj.win_alert(params, function () {
						let index = $this.parents('.item').index();
						$this.parents('.item').remove();
						$('#PicDetail').siblings('.photo_multi_img.template-box').find('.template-download').eq(index).remove();
						if ($('#PicDetail .item').length > 0) {
							$("#box_photo_edit .btn_submit").prop("disabled", false);
						} else {
							$("#box_photo_edit .btn_submit").prop("disabled", true);
						}
						//$.ajax({
						//	url: '/manage/set/photo/upload-del?Path=' + $this.prev().children('img').attr('src') + '&Index=' + index,
						//	success: function (data) {
						//		$this.parents('.item').remove();
						//		$('#PicDetail').siblings('.photo_multi_img.template-box').find('.template-download').eq(index).remove();
						//		if ($('#PicDetail .item').length > 0) {
						//			$("#box_photo_edit .btn_submit").prop("disabled", false);
						//		} else {
						//			$("#box_photo_edit .btn_submit").prop("disabled", true);
						//		}
						//	}
						//});
					}, 'confirm', 1);
					if ($('#box_photo_edit').length) global_obj.div_mask(1);
					return false;
				});
			}
		}
		FUNC.List();
		FUNC.Upload();
		$('#photo .inside_menu').insideMenu();

	},
	file_init: function () {
		const FUNC = {
			Type: (file) => {
				// 文件类型识别
				let point = file.lastIndexOf(".");
				let type = file.substr(point);
				let icon = "";
				switch (type) {
					case ".csv":
						icon = "csv";
						break;
					case ".xls":
					case ".xlsx":
						icon = "excel1";
						break;
					case ".pdf":
						icon = "pdf1";
						break;
					case ".ppt":
					case ".pptx":
						icon = "ppt1";
						break;
					case ".rar":
					case ".zip":
						icon = "a-32yasuowenjian";
						break;
					case ".txt":
						icon = "txt1";
						break;
					case ".doc":
					case ".docx":
						icon = "word1";
						break;
					case ".mp4":
						icon = 'shipinwenjian';
						break;
					case ".png":
						icon = 'png';
						break;
					case ".jpg":
						icon = 'jpg';
						break;
				}
				return { "type": type, "icon": icon };
			},
			List: () => {
				frame_obj.select_all($('input[name=select_all]'), $('input[name=select]')); // 批量操作
				frame_obj.del_bat($('.table_menu_button .del'), $('input[name=select]'), '/api/SettingFiles/DelPhoto'); // 批量删除
				// 复制链接
				let clipboard = new ClipboardJS('.btn_copy');
				clipboard.on('success', function (e) {
					alert(lang_obj.global.copy_complete);
				});

				$('#photo').on('click', '.photo_list .item .img', function () {
					// 勾选图片框
					var item_parent = $(this).parent('.item'),
						$sort = $('#photo input[name=sort]').val(),
						$val = $(this).find('input').val();
					if (item_parent.hasClass('cur')) {
						item_parent.removeClass('cur');
						$(this).find('input').prop('checked', false);
						if ($sort && global_obj.in_array($val, $sort.split('|'))) {
							$('#photo input[name=sort]').val($sort.replace('|' + $val + '|', '|'));
						}
					} else {
						if ($('.photo_list.auto_load_photo').length && ($('input[name=type]').val() == 'products' || $('input[name=type]').val() == 'review')) {
							var box_id = $('input[name=id]').val().replace(';', '='),
								img_len = parseInt(parent.$('#' + box_id).parents('.multi_img').find('.img.isfile').length),
								maxpic = parseInt($('input[name=maxpic]').val()),
								cur_len = parseInt($('.photo_list_box').find('.item.cur').length);
							if (maxpic == img_len) maxpic++;
							if (maxpic - (img_len + cur_len) <= 0) {
								global_obj.win_alert_auto_close(lang_obj.manage.account.picture_tips.replace('xxx', parseInt($('input[name=maxpic]').val())));
								return false;
							}
						}
						item_parent.addClass('cur');
						$(this).find('input').prop('checked', true);
						if ($sort && !global_obj.in_array($val, $sort.split('|'))) {
							$('#photo input[name=sort]').val($sort + $val + '|');
						}
					}
					if ($('#photo_list_form input:checkbox:checked').length) {
						$('.list_menu .move,.list_menu .del').css('display', 'block');
					} else {
						$('.list_menu .move').hide();
					}
					return false;
				}).on('click', '.photo_list .item .zoom', function (e) {
					e.stopPropagation();
				});

				frame_obj.fixed_right($('.list_menu .add, .photo_index .btn_add_item'), '.box_photo_edit', function () {
					// 添加文件
					$("#box_photo_edit .btn_submit").prop("disabled", true);
				});

				frame_obj.fixed_right($('.btn_tags_edit_bat, .btn_tags_edit'), '.box_photo_tags_edit', '', function (obj) {
					// 添加 Or 编辑 标签
					let $FId = "", $Type = "";
					if (obj.hasClass('btn_tags_edit_bat')) {
						$('span.type_single').hide();
						$('span.type_bat').show().find('span').text($('input[name=select]:checked').length);
						$Type = 'editBat';
						let id_list = '';
						$('input[name=select]').each(function (index, element) {
							id_list += $(element).get(0).checked ? $(element).val() + '-' : '';
						});
						if (id_list) {
							id_list = id_list.substring(0, id_list.length - 1);
							$FId = id_list;
						} else {
							global_obj.win_alert(lang_obj.global.dat_select);
							return false;
						}
					} else {
						$('span.type_single').show();
						$('span.type_bat').hide();
						$FId = obj.attr('data-id');
						$Type = 'edit';
					}
					$('.box_photo_tags_edit input[name=id]').val($FId);
					$('.box_photo_tags_edit input[name=Type]').val($Type);
					$.get('/manage/set/photo/file-tags?id=' + $FId + '&type=' + $Type, '', function (data) {
						$('.box_photo_tags_edit .box_option_list').html(data);
						// 多功能选项框
						frame_obj.box_option_list();
						frame_obj.box_option_button_choice();
					}, 'html');
				});

				// 提交
				frame_obj.submit_form_init($('#box_photo_edit'), '/manage/set/photo/file');
				frame_obj.submit_form_init($('#photo_tags_edit_form'), '', '', '', function () {
					global_obj.win_alert_auto_close(lang_obj.global.save_success, '', 400, '8%');
					setTimeout(function () {
						location.href = location.href;
					}, 400);
				});

				// 搜索筛选
				frame_obj.filterRight({
					"onSubmit": function ($obj) {
						// 标签
						let tagsId = [];
						$obj.find("input[name=\"file_tagsCurrent[]\"]").each(function (index, element) {
							tagsId[index] = $(element).val();
						});
						tagsId = tagsId.join(",");

						$('.search_box input[name=tagsId]').val(tagsId);
					}
				});
			},
			Upload: () => {
				// 文件上传
				$('form[name=upload_form]').fileupload({
					url: '/api/FileUpload/uploadFiles',
					/*url: '/manage/action/file-upload-plugin?size=photo&position=set',*/
					// pdf、rar、zip、word、excel、ppt、txt、csv
					acceptFileTypes: /^((application\/(pdf|rar|x-rar|octet-stream|zip|x-zip-compressed|vnd.openxmlformats-officedocument.(wordprocessingml.document|spreadsheetml.sheet|presentationml.presentation)|msword|vnd.ms-excel|vnd.ms-powerpoint|plain|csv))|(text\/(plain|csv))|video\/mp4)$/i,
					disableImageResize: false,
					imageMaxWidth: 2000,
					imageMaxHeight: 99999,
					imageForceResize: false,
					maxFileSize: ********,
					maxNumberOfFiles: 20,
					messages: {
						maxFileSize: lang_obj.manage.file.size_limit,
						maxNumberOfFiles: lang_obj.manage.account.file_tips.replace('{{count}}', 20),
						acceptFileTypes: lang_obj.manage.file.type_error,
					},
					/*callback: function (filePath, surplus, name) {*/
					done: function (e, data) {
						const response = data.result;
						const filePath = response?.filePath || '';
						const surplus = response?.surplus || '';
						const name = response?.name || '';
						const size = response?.size || '';
						const oriname = response?.oriname || '';
						if (!filePath) {
							// 没有返回图片路径
							return false;
						}
						let fileType = FUNC.Type(filePath);
						$('#PicDetail').append('<div class="item"><svg class="icon" aria-hidden="true"><use xlink:href="#icon-' + fileType.icon + '"></use></svg><span>' + lang_obj.global.del + '</span><input type="hidden" name="FileSize[]" value="' + size + '" /><input type="hidden" name="FileOriname[]" value="' + oriname + '" /><input type="hidden" name="FilePath[]" value="' + filePath + '" /><input type="text" maxlength="100" class="box_input" value="' + name + '" name="Name[]" placeholder="' + lang_obj.global.file_name + '" notnull></div>');
						if ($('#PicDetail .item').length > 0) {
							$("#box_photo_edit .btn_submit").prop("disabled", false);
						} else {
							$("#box_photo_edit .btn_submit").prop("disabled", true);
						}
					}
				}).on('fileuploadadd', function (e, data) {
					
					let files = data.files;
					for (let i = 0; i < files.length; i++) {
						let file = files[i];
						let _fileSize = file.size;
						let _fileType = file.type;
						if (_fileType == 'video/mp4' && _fileSize > ********) {
							global_obj.win_alert_auto_close(lang_obj.manage.file.video_size_limit, 'await', 1000, '8%')
							return false
						}
					}
				}).on('fileuploadstop', function (e) { // 新增停止事件监听
					// 所有文件上传完成后隐藏上传按钮栏
					$('.fileupload-buttonbar .template-upload').css('display', 'none');
				});
				$('form[name=upload_form]').fileupload(
					'option',
					'redirect',
					window.location.href.replace(/\/[^\/]*$/, '/cors/result.html?%s')
				);
				$('#PicDetail').on('click', 'div span', function () {
					// 删除文件
					let $this = $(this);
					let params = {
						'title': lang_obj.global.del_confirm,
						'confirmBtn': lang_obj.global.del,
						'confirmBtnClass': 'btn_warn'
					};
					global_obj.win_alert(params, function () {
						let index = $this.parents('.item').index();
						$.ajax({
							url: '/manage/set/photo/upload-del?Path=' + $this.prev().children('img').attr('src') + '&Index=' + index,
							success: function (data) {
								$this.parents('.item').remove();
								$('#PicDetail').siblings('.photo_multi_img.template-box').find('.template-download').eq(index).remove();
								if ($('#PicDetail .item').length > 0) {
									$("#box_photo_edit .btn_submit").prop("disabled", false);
								} else {
									$("#box_photo_edit .btn_submit").prop("disabled", true);
								}
							}
						});
					}, 'confirm', 1);
					if ($('#box_photo_edit').length) global_obj.div_mask(1);
					return false;
				});
			}
		}
		FUNC.List();
		FUNC.Upload();
		$('#photo .inside_menu').insideMenu();

	},

	file_choice_init: function () {
		frame_obj.category_wrap_page_init();
		var id = $('input[name=id]').val(),//显示元素的ID
			type = $('input[name=type]').val(),//类型
			maxfile = $('input[name=maxfile]').val(),//最大允许数
			number = 0,//执行次数
			fileHiddenObj = $("input[name='file2BigName_hidden_text']"),
			fileName = "</br>";

		maxFileSizeTips = lang_obj.manage.file.size_limit;

		maxFileSize = ********;
		if (type == 'allFile') { //显示全部
			acceptFileTypes = /^((application\/(pdf|rar|x-rar|octet-stream|zip|x-zip-compressed|vnd.openxmlformats-officedocument.(wordprocessingml.document|spreadsheetml.sheet|presentationml.presentation)|msword|vnd.ms-excel|vnd.ms-powerpoint|plain|csv|))|(text\/(plain|csv)))$/i;
			acceptFileTypesTips = lang_obj.manage.file.type_error;
		} else {	//默认mp4
			acceptFileTypes = /^(video\/mp4)$/i;
			acceptFileTypesTips = lang_obj.manage.file.video_type_error;
		}

		$('form[name=upload_form]').fileupload({
			// url: '/manage/action/file-upload-plugin?size1=' + type,
			url: '/api/ProductFileUpload/uploadFiles?size=' + type,
			acceptFileTypes: /^((application\/(pdf|rar|x-rar|octet-stream|zip|x-zip-compressed|vnd.openxmlformats-officedocument.(wordprocessingml.document|spreadsheetml.sheet|presentationml.presentation)|msword|vnd.ms-excel|vnd.ms-powerpoint|plain|csv))|(text\/(plain|csv))|video\/mp4)$/i,
			disableImageResize: false,
			imageMaxWidth: 2000,
			imageMaxHeight: 99999,
			imageForceResize: false,
			maxFileSize: ********,
			maxNumberOfFiles: 20,
			messages: {
				maxFileSize: lang_obj.manage.file.size_limit,
				maxNumberOfFiles: lang_obj.manage.account.file_tips.replace('{{count}}', 20),
				acceptFileTypes: lang_obj.manage.file.type_error,
			},
			done: function (e, data) {
				console.log(data,"datadatadatadata")
				const response = data.result;
				const filePath = response?.filePath || '';
				const surplus = response?.surplus || '';
				const name = response?.name || '';
				function isHasFile(filePath) {
					if (filePath.length == 0) {
						return false;
					}
					var isExist = true;
					$.ajax('/manage/action/has-file', {
						type: 'get',
						async: false,//取消ajax的异步实现
						timeout: 1000,
						dataType: "json",
						data: { File: filePath },
						success: function (data) {
							if (data.ret == 0) {
								isExist = false;
							}
						},
					});
					return isExist;
				}
			if (!isHasFile(filePath)) {
					var time = setInterval(function () { //递归检查图片是否已经上传完成
						if (isHasFile(filePath)) {
							frame_obj.file_choice_return(id, type, maxfile, 1, filePath, surplus, ++number, name);
							clearInterval(time);
						}
					}, 500);
				} else {
					frame_obj.file_choice_return(id, type, maxfile, 1, filePath, surplus, ++number, name);
				}
			},
			processfail: function (e, data) {
				$('form[name=upload_form]').trigger('dragleave');
				let files = data.files;
				let error = "";
				$.each(files, function (index, element) {
					if (typeof element.error !== "undefined") {
						error += element.error;
					}
				});
				error == "" && (error = lang_obj.manage.file.video_type_error);
				global_obj.win_alert_auto_close(error, 'await', 1000, '8%', 0);
			}
		}).on('fileuploadadd', function (e, data) {
			$.fn.fileUploadAdd(data);
		});
		$('form[name=upload_form]').fileupload(
			'option',
			'redirect',
			window.location.href.replace(/\/[^\/]*$/, '/cors/result.html?%s')
		);
		set_obj.file_global.list();
	},

	/**************************************************平台授权(start)**************************************************/
	authorization_init: function () {
		//添加授权弹出框
		var box_authorization_add = $('.box_authorization_add');
		$('.r_nav').on('click', '.add', function () {//添加
			frame_obj.pop_form(box_authorization_add);
			$('.r_con_form input', box_authorization_add).val('').removeAttr('style');
			$('.r_con_form select', box_authorization_add).removeAttr('style').find('option').removeAttr('selected').first().attr('selected', 'selected');
			$('.box_authorization_add input[name=do_action]').val('set.authorization_add');
		});

		//删除授权
		$('#authorization').on('click', '.del', function () {
			if (!confirm(lang_obj.global.del_confirm)) { return false; }

			var $this = $(this),
				$AId = $this.parent().parent().attr('aid');

			$.post('./', 'do_action=set.authorization_del&AId=' + $AId, function (data) {
				if (data.ret == 1) {
					$this.parent().parent().remove();
					return false;
				}
			}, 'json');
		});
	},

	open_init: function () {//开放接口设置
		function set_open_api() {
			$.post('./', 'do_action=set.set_open_api', function (data) {
				if (data.ret == 1) {
					if (data.msg.jump == 1) {
						window.top.location = window.top.location.href;
					} else {
						$('#authorization .open_api .appkey').text(data.msg.appkey);
					}
				} else {
					global_obj.win_alert('操作失败！');
				}
			}, 'json');
		}

		$('#authorization .open_api').delegate('.enable', 'click', function () {
			if ($(this).hasClass('add')) {
				set_open_api();
			} else {
				if (confirm(lang_obj.manage.set.open_api_refresh)) set_open_api();
			}
		});

		$('#authorization .open_api').delegate('.del', 'click', function () {
			if (!confirm(lang_obj.global.del_confirm)) { return false; }

			$.post('./', 'do_action=set.del_open_api', function (data) {
				if (data.ret == 1) {
					window.top.location = window.top.location.href;
				}
			}, 'json');
		});
	},

	amazon_init: function () {//亚马逊授权
		$('.pop_form form select[name=MarkectPlace]').change(function () {
			if ($(this).val())
				$(this).siblings('a').attr({ 'href': $(this).find('option:selected').attr('data-url'), 'target': '_blank' });
			else
				$(this).siblings('a').attr('href', 'javascript:;').removeAttr('target');
		});
		$('.box_authorization_add form').submit(function () {
			if (global_obj.check_form($(this).find('*[notnull]'), $(this).find('*[format]'), 1)) { return false; };

			$(this).find('input:submit').attr('disabled', 'disabled');
			$.post('./', $(this).serialize(), function (data) {
				$('.box_authorization_add form input:submit').removeAttr('disabled');
				if (data.ret == 1) {
					window.location.href = window.location.href;
				} else {
					alert(data.msg);
				}
			}, 'json');
			return false;
		});

		//店铺重新授权
		$('#authorization').on('click', '.refresh', function () {
			var $account = $(this).parent().parent().attr('account');

			$.post('./', 'do_action=set.authhz_url&Account=' + $account + '&d=amazon', function (data) {
				if (data.ret == 1) {
					window.location.href = window.location.href;
				}
			}, 'json');
			return false;
		});


		//编辑店铺名称
		var box_authorization_edit = $('.box_authorization_edit');
		$('#authorization').on('click', '.mod,.refresh', function () {//修改
			var AId = $(this).parent().parent().attr('aid'),
				Name = $(this).parent().siblings('td.name').text(),
				account = jQuery.parseJSON(global_obj.htmlspecialchars_decode($(this).parent().parent().attr('account')));

			frame_obj.pop_form(box_authorization_edit);
			$('input[type=submit]', box_authorization_edit).attr('disabled', false);
			$('input[name=Name]', box_authorization_edit).val(Name);
			$('select[name=MarkectPlace]', box_authorization_edit).find('option[value=' + account.MarkectPlace + ']').attr('selected', 'selected');
			$('input[name=MerchantId]', box_authorization_edit).val(account.MerchantId).addClass('bg_gray');
			$('input[name=AWSAccessKeyId]', box_authorization_edit).val(account.AWSAccessKeyId).addClass('bg_gray');
			$('input[name=SecretKey]', box_authorization_edit).val(account.SecretKey).addClass('bg_gray');
			$('input[name=AId]', box_authorization_edit).val(AId);
			$('input[name=d]', box_authorization_edit).val('amazon');
			$('input[name=do_action]', box_authorization_edit).val('set.authorization_edit');

			var method = '';
			if ($(this).hasClass('refresh')) method = 'refresh';
			$('input[name=method]', box_authorization_edit).val(method);

			var url = $('select[name=MarkectPlace]', box_authorization_edit).find('option[value=' + account.MarkectPlace + ']').attr('data-url');
			$('a.amazon_url', box_authorization_edit).attr({ 'href': url, 'target': '_blank' });
		});
		$('.box_authorization_edit form').submit(function () {
			var $this = $(this),
				$Name = $this.find('input[name=Name]').val(),
				$MarkectPlace = $this.find('select[name=MarkectPlace]').val(),
				$AId = $this.find('input[name=AId]').val();

			if (global_obj.check_form($this.find('*[notnull]'), $this.find('*[format]'), 1)) { return false; };

			$.post('./', $(this).serialize(), function (data) {
				if (data.ret == 1) {
					var obj = $('#authorization').find('tr[aid=' + data.msg.AId + ']');
					obj.find('td.name').text(data.msg.Name);
					data.msg.token && obj.attr('account', data.msg.token);

					frame_obj.pop_form($('.box_authorization_edit'), 1);
				} else {
					global_obj.win_alert(data.msg, '', '', 1);
				}
			}, 'json');

			return false;
		});
	},
	/**************************************************平台授权(end)**************************************************/
	/**************************************************第三方代码(start)**************************************************/
	seo_third_init: function () {
		frame_obj.del_init($('#third .config_table')); //删除事件
		$('.used_checkbox .switchery').click(function () {
			var $this = $(this),
				$tid = $this.attr('data-tid'),
				$used;
			if ($this.hasClass('checked')) {
				$used = 0;
				$this.removeClass('checked');
			} else {
				$used = 1;
				$this.addClass('checked');
			}
			$.post('?do_action=set.seo_third_used', { 'TId': $tid, 'IsUsed': $used }, function (data) { }, 'json');
		});
	},

	seo_third_edit_init: function () {
		frame_obj.submit_form_init($('#third_edit_form'), './?m=set&a=third');
		//meta标签和body标签
		$('input[name=IsMeta], input[name=IsBody]').parent().parent('.input_checkbox_box').on('click', function () {
			var _this = $(this);
			var name = _this.find('input').attr('name');
			setTimeout(function () {  //延迟执行，防止与开关按钮冲突
				if (name == 'IsMeta' && _this.hasClass('checked') && $('input[name=IsBody]').parent().parent('.input_checkbox_box').hasClass('checked')) {
					$('input[name=IsBody]').parent().parent('.input_checkbox_box').trigger('click');
				} else if (name == 'IsBody' && _this.hasClass('checked') && $('input[name=IsMeta]').parent().parent('.input_checkbox_box').hasClass('checked')) {
					$('input[name=IsMeta]').parent().parent('.input_checkbox_box').trigger('click');
				}
			}, 50);
		});
	},
	/**************************************************第三方代码(end)**************************************************/
	load_edit_form: function (target_obj, url, type, value, callback, fuc) {
		$.ajax({
			type: type,
			url: url + value,
			success: function (data) {
				if (fuc == 'append') {
					$(target_obj).append($(data).find(target_obj).html());
				} else {
					$(target_obj).html($(data).find(target_obj).html());
				}
				callback && callback(data);
			}
		});
	},

	manage_init: function () {
		frame_obj.del_init($('#config .manager .r_con_table'));

		frame_obj.switchery_checkbox(function (obj) {
			var $UserId = obj.find('input').attr('data-id');
			$.post('/api/Setting/manager/locked', { 'UserId': $UserId, 'Locked': 0 }, function (data) {
				if (data.ret == 1) {
					global_obj.win_alert_auto_close(lang_obj.manage.global.can_log, '', 1000, '8%');
				}
			}, 'json');
		}, function (obj) {
			var $UserId = obj.find('input').attr('data-id');
			$.post('/api/Setting/manager/locked', { 'UserId': $UserId, 'Locked': 1 }, function (data) {
				if (data.ret == 1) {
					global_obj.win_alert_auto_close(lang_obj.manage.global.cant_log, '', 1000, '8%');
				}
			}, 'json');
		});

		var check_status = function () {
			let obj = $('#config .config_box');
			let list_size = obj.find('.config_list').length;
			let list_check = 0;
			let all_list_size = 0;
			let total_size = $('.config_box').find('input[name]').length;
			obj.find('.config_list').each(function () {
				let size = $(this).find('.second_box input[type=checkbox]').length;
				let check_size = $(this).find('.second_box .input_checkbox_box.checked').length;
				all_list_size += check_size;
				$(this).children('.info_name').find('.input_checkbox_box').removeClass('checked indeterminate');
				if (check_size > 0 && size != check_size) $(this).children('.info_name').find('.input_checkbox_box').addClass('indeterminate');
				if (size == check_size) {
					$(this).children('.info_name').find('.input_checkbox_box').addClass('checked');
					list_check++;
				}
			});
			$('#selected_all').parents('.input_checkbox_box').removeClass('checked indeterminate');
			if (all_list_size > 0 && list_size != list_check) $('#selected_all').parents('.input_checkbox_box').addClass('indeterminate');
			if (list_size == list_check) $('#selected_all').parents('.input_checkbox_box').addClass('checked');
			if (total_size == all_list_size) {
				$('#selected_all').prop('checked', false).parent().parent().addClass('checked');
			}
			if ($('#Permit_orders_orders_modify:checked').length > 0) {
				$('#Permit_orders_orders_refund').parents('.th_i').show();
			} else {
				$('#Permit_orders_orders_refund').parents('.th_i').hide();
			}
		}

		$('.input_check_box.third_btn').on('click', function () {
			let _this = $(this);
			setTimeout(() => {
				check_status();
			}, 100);
		})

		$('.PermitHead .input_checkbox_box').on('click', function () {
			let _this = $(this);
			setTimeout(function () {
				let checked = _this.hasClass('checked') ? 1 : 0;
				if (checked) {
					$('.config_box').find('input[name]').prop('checked', true);
					$('.config_box').find('.input_checkbox_box').addClass('checked');
				} else {
					$('.config_box').find('input[name]').prop('checked', false);
					$('.config_box').find('.input_checkbox_box').removeClass('checked');
				}
				check_status();
			}, 100);
		})

		//权限事件
		$('#permit_form').submit(function () { return false; });
		$('#permit_form .btn_submit').click(function () {
			$('#permit_form .btn_cancel').click();
		});

		$('.premit_box .config_list>.info_name i').on('click', function () {
			$(this).parents('.config_list').toggleClass('current');
		});

		//点击二级权限全选按钮
		$('body').on('click', '.second_btn', function () {
			let _this = $(this);
			setTimeout(function () {
				let checked = _this.parent().hasClass('checked') ? 1 : 0;
				if (checked) {
					_this.parent().next().find('.input_checkbox_box').addClass('checked').find('input').prop('checked', true);
				} else {
					_this.parent().next().find('.input_checkbox_box').removeClass('checked').find('input').prop('checked', false);
				}
				check_status();
			}, 100);
		})

		$('body').on('click', '.third_btn', function () {
			let _this = $(this),
				$obj = _this.parents('.second_content').prev();
			setTimeout(function () {
				let checked = _this.hasClass('checked') ? 1 : 0;
				if (checked) {
					_this.siblings('.four_box').find('.input_checkbox_box').addClass('checked').find('input').prop('checked', true);
				} else {
					_this.siblings('.four_box').find('.input_checkbox_box').removeClass('checked').find('input').prop('checked', false);
				}
				$obj.removeClass('checked').removeClass('indeterminate');
				let check_size = _this.parents('.second_content').find('.checked').length,
					total_size = _this.parents('.second_content').find('.input_checkbox_box').length;
				if (check_size == 0) {
					$obj.removeClass('checked').find('input').prop('checked', false);
				} else if (check_size == total_size) {
					$obj.addClass('checked').find('input').prop('checked', true);
				} else {
					$obj.addClass('checked').find('input').prop('checked', true);
				}
				check_status();
			}, 100)
		});

		$('body').on('click', '.four_i .four_btn', function () {
			let _this = $(this),
				$obj = _this.parents('.four_box').siblings('.third_btn');
			$secObj = _this.parents('.second_content').prev();
			setTimeout(function () {
				// 三级
				$obj.removeClass('checked').removeClass('indeterminate');
				let check_size = _this.parents('.four_box').find('.checked').length,
					total_size = _this.parents('.four_box').find('.input_checkbox_box').length;
				if (check_size == 0) {
					$obj.removeClass('checked').find('input').prop('checked', false);
				} else if (check_size == total_size) {
					$obj.addClass('checked').find('input').prop('checked', true);
				} else {
					$obj.addClass('checked').find('input').prop('checked', true);
				}

				// 二级
				$secObj.removeClass('checked').removeClass('indeterminate');
				let sec_check_size = _this.parents('.second_content').find('.checked').length,
					sec_total_size = _this.parents('.second_content').find('.input_checkbox_box').length;
				if (sec_check_size == 0) {
					$secObj.removeClass('checked').find('input').prop('checked', false);
				} else if (sec_check_size == sec_total_size) {
					$secObj.addClass('checked').find('input').prop('checked', true);
				} else {
					$secObj.addClass('checked').find('input').prop('checked', true);
				}
				check_status();
			}, 100)
		});

		$('#config .manager .input_radio_box').on('click', function () {
			let value = $(this).find('input').val();
			if (value == 1) {
				$('.premit_box').hide();
			} else {
				$('.premit_box').show();
			}
		});

		$('#config .config_box .config_list>.info_name .input_checkbox_box').on('click', function () {
			let $this = $(this);
			setTimeout(function () {
				let status = $this.hasClass('checked');
				if (status) {
					$this.parents('.config_list').find('.second_box .input_checkbox_box').addClass('checked').find('input[type=checkbox]').prop('checked', true);
				} else {
					$this.parents('.config_list').find('.second_box .input_checkbox_box').removeClass('checked').find('input[type=checkbox]').prop('checked', false);
				}
				check_status();
			}, 100);
		});

		// 弹窗区域子菜单选择
		$('.premit_box .second_box .input_checkbox_box').on('click', function () {
			setTimeout(function () {
				check_status();
			}, 100);
		});
		check_status();

		//选择用户组
		$('#manage_edit_form').delegate('select[name=GroupId]', 'change', function () {
			$('#PermitBox').css('display', $(this).val() == 1 ? 'none' : 'block');
			$('#manage_edit_form .btn_permit').css('display', $(this).val() == 1 ? 'none' : 'inline-block');
			$('#PermitBox .module').show();
		});

		//数据提交
		$('#manage_edit_form').submit(function () { return false; });
		$('#manage_edit_form .btn_submit').click(function () {
			var $Form = $('#manage_edit_form'),
				$Stop = 0;
			$Form.find('*[notnull]').each(function () {
				if ($.trim($(this).val()) == '') {
					$(this).css('border-color', 'red');
					$Stop += 1;
				}
			});
			if ($Stop > 0) {
				return false;
			}
			$.post($Form.find("input[name=do_action]").val(), $Form.serialize(), function (data) {
				if (data.ret == 1) {
					global_obj.win_alert_auto_close(data.msg, '', 2000, '8%');
					window.location = '/';
				} else {
					global_obj.win_alert_auto_close(data.msg, 'fail', 1000, '8%');
				}
			}, 'json');
		});
	},

	permit_checked: function (obj) {
		obj.pop(); // 去掉最后一个元素
		var sObj = obj.join("_");
		$('#' + sObj).prop('checked', true);
		if (obj.length > 2) {
			set_obj.permit_checked(obj);
		}
	},

	/******************* 国家管理 start *******************/
	set_country_init: function () {
		frame_obj.del_init($('.country_list'));
		$('.plugins_app_menu a').click(function () {
			var $id = $(this).data('id'),
				$url = $(this).data('url');
			$('.plugins_app_menu a').removeClass('current');
			$(this).addClass('current');
			window.history.pushState(null, null, $url);
			$('.country_list tbody tr').hide();
			$('.country_list tbody tr[data-id="' + $id + '"]').show();
		});
		$('.country_list').on('click', '.used_checkbox .switchery', function () {	//启用
			var $this = $(this),
				$tr = $this.parents('tr'),
				check = 0;
			if (!$this.hasClass('no_drop')) {
				if (!$this.hasClass('checked')) {
					$this.addClass('checked');
					check = 1;
				} else {
					$this.removeClass('checked');
				}
				$.get('/manage/set/country/switch', { 'CId': $tr.attr('data-cid'), 'Check': +check }, function (data) {
					if (data.ret != 1) {
						global_obj.win_alert(lang_obj.global.set_error);
					}
				}, 'json');
			}
		});
	},

	set_country_edit_init: function () {
		frame_obj.switchery_checkbox();
		frame_obj.multi_lang_show_all('.country_edit');
		frame_obj.multi_lang_show_item('.country_edit');
		/* 国旗上传 */
		frame_obj.mouse_click($('#FlagDetail .upload_btn, #FlagDetail .pic_btn .edit'), 'img', function ($this) { //点击上传图片
			frame_obj.photo_choice_init('FlagDetail', '', 1);
		});
		/* 表单提交 */
		frame_obj.submit_form_init($('#edit_form'));
	},

	set_country_states_init: function () {
		frame_obj.del_init($('.states_edit'));
		frame_obj.dragsort($('.states_edit tbody'), '/manage/set/country/states-order', 'tr i', 'a', '<tr class="placeHolder"><td></td><td></td></tr>', 'tr');
		frame_obj.fixed_right($('.states_edit .edit, .states_add .add'), '.country_states_edit', function ($this) {
			$.get($this.data('url'), '', function (data) {
				$Form = $('#country_states_edit_form');
				if (data.ret == 1) {
					$Form.find('input[name=States]').val(data.msg.States);
					$Form.find('input[name=AcronymCode]').val(data.msg.AcronymCode);
					$Form.find('input[name=SId]').val(data.msg.SId);
				} else {
					$Form.find('input[name=States]').val('');
					$Form.find('input[name=AcronymCode]').val('');
					$Form.find('input[name=SId]').val('');
				}
				return false;
			}, 'json');
		});
		frame_obj.submit_form_init($('#country_states_edit_form'), '', '', '', function () {
			window.location.reload();
		});
	},
	/******************* 国家管理 start *******************/
	//协议 start
	agreement_init: function () {
		frame_obj.switchery_checkbox();
		frame_obj.submit_form_init($('#edit_form'));

		$('.box_type_menu').on('click', '.item', function () {
			$(this).addClass('checked').find('input').prop('checked', true).parent().siblings().removeClass('checked').find('input').prop('checked', false);
		})


	},
	//协议 end

	/**
	 * 店铺语言
	 */
	language_init: function () {
		// 保存默认语言
		frame_obj.submit_form_init($('#edit_form'), '', function () {
			global_obj.win_alert_auto_close(lang_obj.global.save_success, '', 500, '8%');
			setTimeout(function () {
				window.location.reload();
			}, 500);
		});
		let action = function () {
			// 添加修改右侧弹窗
			frame_obj.fixed_right($('.list_menu_button .add, .r_con_table .icon_edit, .btn_add_item'), '.fixed_language', '', function (obj) {
				let LId = "";
				if (obj.parents('tr').length > 0) {
					LId = obj.parents('tr').data('lid');
				}
				let Language = LId ? obj.parents('tr').find('td:eq(0)').text() : '';
				if (!LId) {
					$('.fixed_language .input_radio_side_box').eq(0).click();
				}
				$.post('/manage/set/language/get-info', { LId: LId ?? "0" }, function (data) {
					if (data.ret == 1) {
						// 复制id
						$('.fixed_language input[name=LId]').val(LId);
						// 标题赋值
						let Title = lang_obj.language.add_other_language;
						if (LId) Title = Language;
						$('.fixed_language .top_title span').text(Title);
						// 语言下拉
						$('.fixed_language .box_select').html(data.msg.LanguageHtml);
						let Dispaly = LId ? 'none' : 'block';
						$('.fixed_language .language_select').css('display', Dispaly);
						// 方式
						$('.fixed_language .input_radio_box input[value=' + data.msg.Type + ']').prop('checked', true).parents('.input_radio_box').addClass('checked').siblings().removeClass('checked');
						$('.fixed_language .type_box_tab .rows[data-type=' + data.msg.Type + ']').show().siblings().hide();
						// 方式的值
						if (data.msg.Type == 'translate') {
							$('.fixed_language .app_select input[value=' + data.msg.Value + ']').prop('checked', true).parent('.app_select').addClass('current').siblings().removeClass('checked');
						} else {
							$('.fixed_language .url_box .box_textarea').val(data.msg.Value);
						}
					}
				}, 'json');
			});

			frame_obj.fixed_right($('.r_con_table .icon_file'), '.fixed_language_view', '', function (obj) {
				let LId = obj.parents('tr').data('lid');
				let Language = obj.parents('tr').find('td:eq(0)').text();
				$.post('/manage/set/language/get-info', { LId: LId }, function (data) {
					if (data.ret == 1) {
						// 标题赋值
						$('.fixed_language_view .top_title span').text(Language);
						// 方式的值
						$('.fixed_language_view .url_box .input').text(data.msg.Value);
					}
				}, 'json');
			});

			// 删除语言
			frame_obj.del_init($('#language .r_con_table'));
			// 发布语言
			$('#language .r_con_table .publish').off('click').on('click', function () {
				let _this = $(this);
				let LId = _this.parents('tr').data('lid');
				$.post('/manage/set/language/publish', { LId: LId }, function (data) {
					if (data.ret == 1) {
						_this.parents('tr').find('.status').text(lang_obj.manage.products.sync.publish_used).removeClass('status0').addClass('status1');
						_this.remove();
						global_obj.win_alert_auto_close(lang_obj.manage.module.drafts_publish_success, '', 1000, '8%');
					}
				}, 'json');
			});
		}
		action();
		// 右侧弹窗单选框防止冒泡
		$('.fixed_language input[type=radio]').on('click', function (event) {
			event.stopPropagation();
		});
		// 右侧弹窗方式和翻译插件选择着色
		$('.fixed_language').find('.input_radio_box , .app_select').on('click', function () {
			$(this).addClass('current').siblings().removeClass('current');
			if ($(this).hasClass('input_radio_box')) {  // 方式选择
				let type = $(this).find('input').val();
				$('.fixed_language .type_box_tab .rows[data-type=' + type + ']').show().siblings().hide();
			}
		});
		// 保存右侧弹窗
		frame_obj.submit_form_init($('#add_form'), '', function () {
			let Type = $('#fixed_right .type_box .current input').val();
			if (Type == 'url') {
				$('#fixed_right textarea[name=Url]').attr('notnull');
			} else {
				$('#fixed_right textarea[name=Url]').removeAttr('notnull');
			}
		}, '', function (data) {
			if (data.ret == 1) {
				global_obj.win_alert_auto_close(lang_obj.global.save_success, '', 1000, '8%');
				window.location.reload();
			}
		});

	},
	language_pack_init: function () {
		// 保存默认语言
		$('#config .inside_menu').insideMenu();
		frame_obj.submit_object_init($('#edit_form'));
		if ($('.inside_body ul li').length) {
			$item = $('.inside_body ul li');
			$item.click(function () {
				var ModuleName = $(this).data('modulename');
				$item.find('a').removeClass('current');
				$(this).find('a').addClass('current');
				$('.langpack_content_container').hide();
				$('.langpack_content_container[data-modulename=' + ModuleName + ']').show();
			});
		}
	},

	// 新版规范Ui;
	set_global_ui_init: function () {
		$('.demo_nav_item a[data-tab]').each(function () {
			$(this).on('click', function () {
				$('.demo_nav_item a').removeClass('active');
				$(this).addClass('active');
				var tabId = $(this).attr('data-tab');
				$('.demo_component_content').find('.content').hide();
				$('.demo_component_content').find('.content[data-tab=' + tabId + ']').show();
			});
		});

		$('#public_library').on('click', '.top_title', function () {
			let _this = $(this),
				height = _this.parents('.item').find('.sub').find('.height').outerHeight();
			_this.parents('.item').siblings().removeClass('active').find('.top_title').removeClass('current').parents('.item').find('.sub').height(0).find('.third').height(0);
			_this.addClass('current').parents('.item').addClass('active');
			_this.parents('.item').find('.sub').height(height);
			if ($(this).find('em').length == 0) {
				let _opsition = _this.attr('data'),
					textObj = $('#public_library .content');
				$('#public_library .menu .item_list .item a').removeClass('current');
				textObj.find('.text[data=' + _opsition + ']').show().siblings().hide();
				return false;
			}
		})
		$('#public_library').on('click', '.sec_item a', function () {
			let _this = $(this),
				textObj = $('#public_library .content'),
				_opsition = _this.attr('data');
			if (_this.parents('.sec_item').hasClass('hasThird')) {
				_this.parents('.sec_item').find('.sec_tit').find('a').addClass('current');
			}
			if (_this.parents('.sec_item').find('.third')) {
				let third_height = _this.parents('.sec_item').find('.third_height').outerHeight(),
					height = _this.parents('.item').find('.sub').find('.height').outerHeight();
				_this.parents('.sec_item').find('.third').height(third_height);
				_this.parents('.sub').height(height + third_height);
			}
			$('#public_library').find('a').removeClass('current');
			_this.addClass('current').parents('.hasThird').find('.sec_tit').find('a').addClass('current');

			if (_this.hasClass('no_show_txt')) {
				return false;
			}
			textObj.find('.text[data=' + _opsition + ']').show().siblings().hide();
		})

	},

	logs_init: function () {
		// 搜索筛选
		frame_obj.filterRight({
			"onInit": function () {
				$('.fixed_search_filter input[name=AccTime]').daterangepicker({ showDropdowns: true });
			},
			"onSubmit": function ($obj) {
				// 操作时间
				let accTime = $obj.find("input[name=AccTime]").val();
				// 管理员
				let userName = [];
				$obj.find("input[name=UserName]").each(function (index, element) {
					userName[index] = $(element).val();
				});
				userName = userName.join(",");
				// 功能模块
				let module = [];
				$obj.find("input[name=\"moduleCurrent[]\"]").each(function (index, element) {
					module[index] = $(element).val();
				});
				module = module.join(",");

				$('.search_box input[name=AccTime]').val(accTime);
				$('.search_box input[name=UserName]').val(userName);
				$('.search_box input[name=Module]').val(module);
			}
		});
		frame_obj.fixed_right($('.btn_view_detail'), '.fixed_view_detail', function ($this) {
			$('#fixed_right').addClass('loading');
			$.post('/manage/set/logs/view?id=' + $this.attr('data-lid'), '', function (data) {
				if (data.ret == 1) {
					$('.box_view_detail').html(data.msg);
					$('#fixed_right').removeClass('loading');
				}
			}, 'json');
		});
	},

	judgment_max: function (maxObj, minObj, parentElem, errorElem) {
		let max = parseFloat(maxObj.val())
		let min = parseFloat(minObj.val())
		let error = lang_obj.manage.shipping.max_error_tips.replace('%num%', min)

		if (max < min) {
			maxObj.css({ 'border': '1px solid red' }).parents(parentElem).find(errorElem).text(error).show()
			return false;
		} else {
			maxObj.removeAttr('style').parents(parentElem).find(errorElem).text('').hide()
			return true;
		}
	}
}