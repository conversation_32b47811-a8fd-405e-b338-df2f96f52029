var size_guide_obj={global_init:()=>{$("#size_guide").on("click",".guide_view",function(){let t=$(this),e=t.attr("data-id"),a="";$.post("/manage/plugins/size-guide/get-view-data",{id:e},function(t){if(1==t.ret){let e=t.msg,d=e.GuideData,o=[];for(i in d.data)o.push(i);if(a+='<div class="guide_view_wrapper">',a+='<div class="guide_title">'+lang_obj.manage.app.size_guide.guide_title+"</div>",d){if(a+='<div class="guide_table">',o.length>0){for(n in a+='<div class="guide_table_switch">',o)a+='<a href="javascript:;" unit="'+o[n]+'" class="table_switch_item'+(0==n?" cur":"")+'">'+o[n]+"</a>";a+="</div>"}for(k in a+='<div class="guide_table_info">',a+='<table border="1">',a+="<thead>",a+="<tr>",d.field){let t=d.field.length,e=parseInt(100/t);a+='<td width="'+e+'%">'+d.field[k]+"</td>"}for(k in a+="</tr>",a+="</thead>",d.data){let t=d.data[k];for(i in a+='<tbody unit="'+k+'">',t){let e=t[i];for(n in a+="<tr>",e)a+="<td>"+e[n]+"</td>";a+="</tr>"}a+="</tbody>"}a+="</table>",a+="</div>",a+="</div>"}if(a+='<div class="guide_desc">'+e.Description+"</div>",e.MeasurementData){for(k in a+='<div class="guide_measurement">',a+='<div class="guide_measurement_title">'+e.MeasurementData.title+"</div>",a+='<div class="guide_measurement_content">',a+='<div class="guide_measurement_info">',e.MeasurementData.method){let t=e.MeasurementData.method[k];a+='<div class="guide_measurement_item">',a+='<div class="guide_measurement_item_title"><em>'+(parseInt(k)+1)+"</em>"+t.title+"</div>",a+='<div class="guide_measurement_item_desc">'+global_obj.n2br(t.desc)+"</div>",a+="</div>"}a+="</div>",e.MeasurementData.pic&&(a+='<div class="guide_measurement_img"><img src="'+e.MeasurementData.pic+'" /></div>'),a+="</div>",a+="</div>"}a+='<div class="guide_close"></div>',a+="</div>",a&&(global_obj.div_mask(),$("#size_guide").append(a))}else global_obj.win_alert_auto_close(t.msg,"fail",1e3,"8%")},"json")}),$("#size_guide").on("click",".guide_close",function(){$(".guide_view_wrapper").remove(),global_obj.div_mask(1)}),$("#size_guide").on("click",".guide_view_wrapper .table_switch_item",function(){let t=$(this).attr("unit");$(this).addClass("cur").siblings().removeClass("cur"),$(".guide_view_wrapper").find(".guide_table_info tbody[unit="+t+"]").show().siblings("tbody").hide()}),$("#size_guide").on("click",".switchery",function(){let t=$(this).data("id"),e=$(this).hasClass("checked")?1:0;e?$(this).removeClass("checked"):$(this).addClass("checked"),e=$(this).hasClass("checked")?1:0,$.post("/manage/plugins/size-guide/size-guide-switch",{switch:e,id:t},function(t){let e=1==t.ret?"success":"fail";global_obj.win_alert_auto_close(t.msg,e,500,"20%")},"json")})},list_init:()=>{frame_obj.select_all($("input[name=select_all]"),$("input[name=select]"),$(".table_menu_button").find(".del")),frame_obj.del_bat($(".table_menu_button .del"),$("input[name=select]"),"/manage/plugins/size-guide/del"),frame_obj.fixed_box_popup({clickObj:$(".btn_add_guide, .btn_add_item"),targetClass:"box_guide_edit"})},edit_init:()=>{const t={form_obj:$("#size_guide").find(".guide_global_form"),_fieldForm:$(".fixed_guide_field_set"),_measurementForm:$(".fixed_guide_add_measurement"),_dataForm:$(".fixed_guide_add_data"),_unitData:{0:"CM",1:"IN"},default_scope_click:()=>{t.form_obj.find(".input_radio_side_box.checked").length<1&&t.form_obj.find(".type_box").find(".input_radio_side_box:first").click()},add_field:(t,e)=>{t=t.replaceAll("%N%",""),$("#field_set_form .field_row").append(t),e&&$("#field_set_form .field_row .field_content_row:last").find(".box_input[name=field\\[\\]]").val(e).attr("data-old",e),1==$(".field_content_row").length&&$(".field_content_row .button").addClass("hide_remove")},remove_field:t=>{t.parents(".field_content_row").fadeOut(function(){$(this).remove(),1==$(".field_content_row").length&&$(".field_content_row .button").addClass("hide_remove")})},add_measurement:(e,i,a,n)=>{let d=parseInt(t.form_obj.find(".measurement_info .measurement_item").length),o=d+1;if(1==a)e=e.replace("%title%",o+"、"+i.title).replace("%valtitle%",i.title).replaceAll("%desc%",i.desc).replaceAll("%position%",d),t.form_obj.find(".measurement_info").append(e);else{let e=t.form_obj.find(".measurement_info .measurement_item").eq(n),a=parseInt(n)+1;e.find(".item_title").html(a+"、"+i.title),e.find(".item_desc").html(i.desc),e.find("input.method_title").val(i.title),e.find("input.method_desc").val(i.desc)}"block"==$(".measurement_box .noInfoData").css("display")&&$(".measurement_box .noInfoData").hide()},show_field:()=>{let e=[],a=[],n=t._fieldForm.find("input[name=field\\[\\]]"),d=t.form_obj.find(".guide_show_box table thead tr"),o=t._fieldForm.find("input[name=old_field]").val(),l=o.split(",");n.each(function(){let t=$(this).val(),i=$(this).attr("data-old");e.push(t),a.push(i)});const _=[...e,...l],s=_.filter(t=>!(e.includes(t)&&l.includes(t)));if(e.length>0){d.html("");let a="",n=parseInt(850/e.length),o=e.toString();for(k in e)a+='<td width="'+n+'px">'+e[k]+'<input type="hidden" name="GuideData[field][]" value="'+global_obj.htmlspecialchars(e[k])+'"></td>';a+='<td width="150" class="operation"></td>',a&&d.html(a),t.form_obj.find(".guide_operation_btn .set_btn").attr("data-field",o),t.form_obj.find(".guide_show_box table tbody tr").each(function(){for(k in e){let t=e[k],a=$(this).find("td").eq(k).attr("data-field"),n=$(this).parents("tbody").attr("data-unit"),d=$(this).index();if(s.length>0&&null!=a)for(i in s)$(this).find(`td[data-field='${s[i]}']`).remove();if(null==a){let e='<td data-field="'+t+'"><input type="hidden" name="GuideData[data]['+n+"]["+d+'][]" value=""></td>',i=parseInt(k)-1;$(this).find("td").eq(i).after(e)}if(a!=t){let e=$(this).find(`td[data-field='${a}']`),i=$(this).find(`td[data-field='${t}']`);i.insertBefore(e)}}})}$(".guide_box guide_table_no_data").hasClass("hide")||($(".guide_box .guide_table").removeClass("hide"),$(".guide_box .guide_table_no_data").addClass("hide"))},add_data_html:(e,a,n,d)=>{let o=e.split(","),l="",_=t.data_item,s=t.form_obj.find(".guide_show_box tbody"),r=[],u=[],m=[],f=[];for(k in"IN"==d&&(t._unitData={0:"IN",1:"CM"}),s.each(function(){let t=$(this),e=t.attr("data-unit");r[e]=t.find("tr").length,-1!=n&&(t.find("tr").eq(n).find("input[name*=GuideData]").each(function(){"CM"==t.attr("data-unit")?m.push($(this).val()):f.push($(this).val())}),"CM"==t.attr("data-unit")?u.CM=m:u.IN=f)}),t._unitData){for(i in l+='<div class="unit_data_box" data-unit="'+t._unitData[k]+'">',o)if(0==a){let e=u[t._unitData[k]][i]?u[t._unitData[k]][i]:"";l+=_.replace("%title%",o[i]).replace("%unit%",t._unitData[k]).replace("%index%",r[t._unitData[k]]).replace("%data%",e).replace("%num%",i)}else l+=_.replace("%title%",o[i]).replace("%unit%",t._unitData[k]).replace("%index%",r[t._unitData[k]]).replace("%data%","").replace("%num%",i);l+="</div>"}l&&t._dataForm.find(".fixed_data_box").html(l)},show_data:e=>{if(e.length>0){let a=t.form_obj.find(".guide_show_box tbody"),n=[],d=t._dataForm.find("input[name=isAdd]").val(),o=t._dataForm.find("input[name=position]").val(),l=t.form_obj.find(".set_btn").attr("data-field");for(k in a.each(function(){let t=$(this).attr("data-unit");n[t]=$(this).find("tr").length}),t._unitData){let a=t._unitData[k];if(0==d){let n=t.form_obj.find(".guide_show_box tbody[data-unit="+a+"] tr").eq(o),d=0;for(i in e){if(-1==e[i].name.indexOf(a))continue;let t=n.find("td").eq(d);t.html(e[i].value+'<input type="hidden" name="GuideData[data]['+a+"]["+o+'][]" value="'+e[i].value+'">'),d++}}else{let d="",o=l.split(","),_=0;for(i in d+="<tr>",e)-1!=e[i].name.indexOf(a)&&(d+='<td data-field="'+o[_]+'">'+e[i].value+'<input type="hidden" name="GuideData[data]['+a+"]["+n[a]+'][]" value="'+e[i].value+'"></td>',_+=1);d+='\t<td class="operation tar">\n\t\t\t\t\t\t\t\t\t\t\t<a class="icon_edit oper_icon button_tips" href="javascript:;">'+lang_obj.global.edit+'</a>\n\t\t\t\t\t\t\t\t\t\t\t<a class="icon_del oper_icon button_tips" href="javascript:;">'+lang_obj.global.del+"</a>\n\t\t\t\t\t\t\t\t\t\t</td>",d+="</tr>";let s=0;checkNullItem=$(".unit_data_box[data-unit="+a+"]").find("input[name*=data]"),checkNullItem.each(function(){""!=$(this).val()&&(s+=1)}),s>0&&t.form_obj.find(".guide_show_box tbody[data-unit="+a+"]").append(d)}}}},change_option:e=>{let i=`<option value>${lang_obj.global.selected}</option>`,a=t.form_obj.find("input[name=curAttr]").val();for(k in e){let t=e[k],n=t==a?"selected":"";i+=`<option value="${t}" ${n}>${t}</option>`}return i},check_scope:e=>{let i=e,a=[],n=i.parents(".use_group_box");_objScope=n.attr("data-value"),"products"==_objScope?n.find("input[name='productsOption[]']").each(function(){a.push($(this).val())}):"category"==_objScope&&n.find("input[name='products_categoryOption[]']").each(function(){a.push($(this).val())});let d=a.toString(),o=t.form_obj.find("input[name=curAttr]").val();$.post("/manage/plugins/size-guide/change-attr",{scope:_objScope,select:d,curAttr:o},function(e){1==e.ret&&t.form_obj.find(".attr_select_box").html(e.msg)},"json")},field_item:'<div class="field_content_row">\n\t\t\t\t\t\t\t\t<em class="field_myorder"></em>\n\t\t\t\t\t\t\t\t<div class="rows clean float">\n\t\t\t\t\t\t\t\t\t<div class="input"><input name="field[]" value="%N%" data-old="%N%" type="text" class="box_input" size="28" notnull></div>\n\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t\t<div class="float button">\n\t\t\t\t\t\t\t\t\t<a href="javascript:;" class="btn_field fl btn_field_remove"><i></i></a>\n\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t</div>',measurement_item:'<div class="measurement_item">\n\t\t\t\t\t\t\t\t\t<div class="measurement_item_message">\n\t\t\t\t\t\t\t\t\t\t<div class="item_title">%title%</div>\n\t\t\t\t\t\t\t\t\t\t<div class="item_desc">%desc%</div>\n\t\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t\t\t<div class="measurement_item_operation">\n\t\t\t\t\t\t\t\t\t\t<a class="icon_edit oper_icon edit_measurement_btn button_tips" href="javascript:;">'+lang_obj.manage.global.edit+'</a>\n\t\t\t\t\t\t\t\t\t\t<a class="icon_del oper_icon del_measurement_btn button_tips" href="javascript:;">'+lang_obj.global.del+'</a>\n\t\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t\t\t<input type="hidden" name="MeasurementData[method][%position%][title]" class="method_title" value="%valtitle%">\n\t\t\t\t\t\t\t\t\t<input type="hidden" name="MeasurementData[method][%position%][desc]" class="method_desc" value="%desc%">\n\t\t\t\t\t\t\t\t</div>',data_item:'<div class="rows clean" data-num="%num%">\n\t\t\t\t\t\t\t\t<label>%title%</label>\n\t\t\t\t\t\t\t\t<div class="input">\n\t\t\t\t\t\t\t\t\t<input name="data[%unit%][%index%][]" value="%data%" type="text" class="box_input full_input" size="42" notnull>\n\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t</div>'};frame_obj.box_type_menu(function(e){let i=e.parent();if(i.hasClass("specify_box")){let i=e.find("input[name=Scope\\[type\\]]").val();t.form_obj.find(".use_group_box").hide(),t.form_obj.find(".use_group_box[data-value="+i+"]").show();let a=[],n=t.form_obj.find(".use_group_box[data-value="+i+"]");"products"==i?n.find("input[name='productsOption[]']").each(function(){a.push($(this).val())}):"category"==i&&n.find("input[name='products_categoryOption[]']").each(function(){a.push($(this).val())});let d=a.toString(),o=t.form_obj.find("input[name=curAttr]").val();$.post("/manage/plugins/size-guide/change-attr",{scope:i,select:d,curAttr:o},function(e){1==e.ret&&t.form_obj.find(".attr_select_box").html(e.msg)},"json")}}),$(".select_list").bind("DOMNodeInserted",function(){t.check_scope($(this))}).bind("DOMNodeRemoved",function(){t.check_scope($(this))}),t.default_scope_click(),$(".fixed_guide_field_set").on("change",'select[name="unit"]',function(){let t=$(this),e=(t.val(),t.find("option:selected").siblings().val());$(".fixed_guide_field_set .select_row .change_unit").html(e)}),$("#field_set_form").on("click",".select_row",function(){$(this).addClass("checked").find("input").prop("checked",!0).parents(".select_row").siblings().removeClass("checked").find("input").prop("checked",!1)});var e=function(){let t=$("#edit_form").find("input[name=Unit]").val(),e=$("#edit_form").find("input[name=FillMethod]").val();if("normal"!=e){var i=t,a=$("#size_guide .guide_box .inside_container").find('li[data-unit="'+i+'"]'),n="CM"==i?"IN":"CM";$("#size_guide .guide_box .inside_container").find('li[data-unit="'+n+'"]').before(a),tableObj=$("#size_guide .guide_show_box .r_con_table tbody[data-unit="+i+"]"),$("#size_guide .guide_show_box .r_con_table tbody[data-unit="+n+"]").before(tableObj),tableObj=$("#add_data_form .inside_body li[data-unit="+i+"]"),$("#add_data_form .inside_body li[data-unit="+n+"]").before(tableObj),setTimeout(()=>{$("*[data-unit="+t+"]").click()},300)}};e(),frame_obj.fixed_right($(".set_btn"),".fixed_guide_field_set",e=>{let i=e.attr("data-field");if(t._fieldForm.find(".field_row").html(""),t._fieldForm.find("input[name=old_field]").val(i),$(".field_row .field_content_row").length||i){let e=i.split(",");for(k in e)t.add_field(t.field_item,e[k])}else t.add_field(t.field_item);$(".field_row").dragsort("destroy"),frame_obj.dragsort($(".field_row"),"",".field_content_row .field_myorder",".field_content_row .rows .box_input",'<div class="placeHolder"></div>')}),t._fieldForm.on("click",".btn_submit",()=>{if(global_obj.check_form(t._fieldForm.find("*[notnull]"),t._fieldForm.find("*[format]"),1))return!1;t.show_field(),_unitValue=t._fieldForm.find('select[name="unit"]').val(),_fillMethodValue=t._fieldForm.find('input[name="FillMethod"]:checked').val(),"normal"==_fillMethodValue&&(_unitValue=""),$("#edit_form").find("input[name=FillMethod]").val(_fillMethodValue),$("#edit_form").find("input[name=Unit]").val(_unitValue),$("#fixed_right .btn_cancel").click(),e()}),t._fieldForm.on("change",".field_content_row .box_input",function(){let e=$(this).attr("data-old"),i=$(this).val(),a=t._fieldForm.find("input[name=old_field]").val().split(",");for(k in a)t._fieldForm.find(".field_row input").eq(k).val()&&e&&(a[k]=t._fieldForm.find(".field_row input").eq(k).val());t._fieldForm.find("input[name=old_field]").val(a.toString()),t.form_obj.find(`.guide_box tbody td[data-field='${e}']`).attr("data-field",i)}),t._fieldForm.on("click",".field_add_btn .btn_add_item",function(){t.add_field(t.field_item)}),t._fieldForm.on("click",".btn_field_remove",function(){t.remove_field($(this))}),t.form_obj.on("click",".unit_item",function(){let e=$(this).attr("data-unit");$(this).addClass("current").parent().siblings().find("a").removeClass("current"),t.form_obj.find(".guide_show_box tbody[data-unit="+e+"]").show().siblings("tbody").hide()}),t._dataForm.on("click",".unit_item",function(){let e=$(this).attr("data-unit");$(this).addClass("current").parent().siblings().find("a").removeClass("current"),t._dataForm.find(".fixed_data_box .unit_data_box[data-unit="+e+"]").show().siblings().hide()}),frame_obj.fixed_right($("#guide_add_data"),".fixed_guide_add_data",()=>{t._dataForm.find(".fixed_data_box").html(""),t._dataForm.find("input[name=isAdd]").val(1),t._dataForm.find("input[name=position]").val(-1),t._dataForm.find("input[name=unit]").val("CM"),setTimeout(function(){let e=t.form_obj.find(".guide_box .set_btn").attr("data-field"),i=t._dataForm.find("input[name=isAdd]").val(),a=t._dataForm.find("input[name=position]").val(),n=t.form_obj.find("input[name=Unit]").val();t.add_data_html(e,i,a,n),_innerFillMethod=t.form_obj.find('input[name="FillMethod"]').val(),_innerUnit=t.form_obj.find('input[name="Unit"]').val(),"auto"==_innerFillMethod&&_innerUnit&&($(".fixed_data_box").find(".unit_data_box").removeAttr("auto-change").find(".box_input").removeClass("readonly"),$(".fixed_data_box").find('.unit_data_box[data-unit="'+_innerUnit+'"]').attr("auto-change",!1).show().siblings().hide().attr("auto-change",!0).find(".box_input").attr("readonly","readonly"))},300)},()=>{let e=t.form_obj.find(".guide_box .set_btn").attr("data-field");if(t._dataForm.find(".top_title strong").html(lang_obj.manage.app.size_guide.add_data_title),!e)return global_obj.win_alert_auto_close(lang_obj.manage.app.size_guide.add_field_tips,"fail",1e3,"8%"),!1;t._dataForm.find(".unit_item[data-unit="+t.form_obj.find("input[name=Unit]").val()+"]").click()}),t._dataForm.on("keyup",".fixed_data_box .unit_data_box[auto-change=false] .box_input",function(){let t=$(this).val(),e=/^\d+(\.\d+)?$/,i=$(this).parents(".unit_data_box").attr("data-unit"),n=$(this).parents(".unit_data_box").siblings().attr("data-unit"),d=$(this).attr("name"),o=d.replace(i,n),l=$(this).parents(".rows").attr("data-num");e.test(t)&&($num=parseFloat(t),t=a($num,i,n)),$('input[name="'+o+'"]').eq(l).val(t)});let a=function(t=0,e="CM",i="IN"){let a=0;return t=parseFloat(t),0==t?a:("CM"==e&&"IN"==i?price=t/2.54:"IN"==e&&"CM"==i&&(price=2.54*t),price=parseInt(price.mul(1e4)),a=Math.ceil(price.div(100)).div(100).toFixed(2),a)};t._dataForm.on("click",".btn_submit",()=>{let e=t._dataForm.find("#add_data_form").serializeArray();t.show_data(e);let i=t._dataForm.find(".unit_item.current").attr("data-unit");t.form_obj.find(`.unit_item[data-unit='${i}']`).click(),$("#fixed_right .btn_cancel").click()}),t.form_obj.on("click",".guide_show_box .icon_edit",function(){let e=$(this).parents("tbody").attr("data-unit"),i=$(this).parents("tr").index();$("#guide_add_data").click(),t._dataForm.find(".top_title strong").html(lang_obj.manage.app.size_guide.edit_data_title),t._dataForm.find("input[name=isAdd]").val(0),t._dataForm.find("input[name=position]").val(i),t._dataForm.find("input[name=unit]").val(e),setTimeout(function(){t._dataForm.find(`.unit_item[data-unit='${e}']`).click()},320)}),t.form_obj.on("click",".guide_show_box .icon_del",function(){let t=$(this),e=t.parents("tbody");t.parents("tr").fadeOut().remove(),$("#button_float_tips").remove();let i=e.attr("data-unit"),a=e.find("input:hidden");a.each(function(){let t=$(this).parents("tr").index();$(this).attr("name","GuideData[data]["+i+"]["+t+"][]")})}),frame_obj.fixed_right($("#add_measurement_btn"),".fixed_guide_add_measurement",()=>{t._measurementForm.find("input[name=isAdd]").val(1),t._measurementForm.find("input[name=position]").val(-1),t._measurementForm.find("input[name=title]").val(""),t._measurementForm.find("textarea[name=desc]").val("")},()=>{t._measurementForm.find(".top_title strong").html(lang_obj.manage.app.size_guide.add_measurement_title)}),$("body").on("click",".measurement_box .edit_measurement_btn",function(){let e=$(this),i=e.parents(".measurement_item").find("input.method_title").val(),a=e.parents(".measurement_item").find("input.method_desc").val(),n=e.parents(".measurement_item").index();i&&a&&($("#add_measurement_btn").click(),t._measurementForm.find(".top_title strong").html(lang_obj.manage.app.size_guide.eidt_measurement_title),t._measurementForm.find("input[name=isAdd]").val(0),t._measurementForm.find("input[name=position]").val(n),t._measurementForm.find("input[name=title]").val(i),t._measurementForm.find("textarea[name=desc]").val(a))}),t._measurementForm.on("click",".btn_submit",()=>{let e=t._measurementForm.find("input[name=title]").val(),i=t._measurementForm.find("textarea[name=desc]").val(),a=t._measurementForm.find("input[name=isAdd]").val(),n=t._measurementForm.find("input[name=position]").val(),d={title:e,desc:i};if(global_obj.check_form(t._measurementForm.find("*[notnull]"),t._measurementForm.find("*[format]"),1))return!1;e&&i&&(t.add_measurement(t.measurement_item,d,a,n),$("#fixed_right .btn_cancel").click())}),$("body").on("click",".measurement_box .del_measurement_btn",function(){let e=$(this),i=t.form_obj.find(".measurement_info");e.parents(".measurement_item").fadeOut().remove(),$("#button_float_tips").remove(),i.find(".measurement_item").each(function(t){let e=t+1,i=$(this).find(".method_title").val();$(this).find(".item_title").html(e+"、"+i),$(this).find(".method_title").attr("name","MeasurementData[method]["+t+"][title]"),$(this).find(".method_desc").attr("name","MeasurementData[method]["+t+"][desc]")}),0==i.find(".measurement_item").length&&$(".measurement_box .noInfoData").show()}),frame_obj.mouse_click($(".multi_img .upload_btn, .pic_btn .edit"),"pro",function(t){frame_obj.photo_choice_init("PicDetail","",1)}),$(".guide_unit_change .inside_menu").insideMenu(),$(".fixed_guide_add_data .inside_menu").insideMenu(),frame_obj.submit_form_init($(".guide_global_form"),"","",0,function(t){if(1==t.ret)return global_obj.win_alert_auto_close(lang_obj.global.save_success,"",1e3,"8%"),window.location.href="/manage/plugins/size-guide",!1;global_obj.win_alert_auto_close(lang_obj.global.save_fail,"fail",1e3,"8%")})}};