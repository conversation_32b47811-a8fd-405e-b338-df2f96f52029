

var sync_products_obj={
	//************************************************* 数据储存 Start *************************************************
	data_init:{
		attr_data:{}, //属性和选项数据
		ext_attr_data:{}, //规格数据
		ext_save_data:{}, //规格保存数据
		fixed_tags_data:new Array('IsIndex', 'IsNew', 'IsHot', 'IsBestDeals')
	},
	//************************************************* 数据储存 End *************************************************

	//************************************************* 函数事件 Start *************************************************
	function_init:{
		//文本框
		attr_form_edit:function(Data, Type, Name, Size, Max){
			var $Html='';
			for(i in shop_config.language){
				$lang=shop_config.language[i];
				if(Name.indexOf('Tab')!='-1'){ //判断是不是选项卡选项
					$field_name=Name+'_'+$lang;
				}else{
					$field_name=Name+'['+$lang+']';
				}
				$value=(Data[$lang]?Data[$lang]:'');
				$Html+='<div class="tab_txt tab_txt_'+$lang+'" lang="'+$lang+'"'+(shop_config.language_pack==$lang?' style="display:block;"':'')+'>';
				if(Type=='text'){
					$Html+=	'<input type="text" name="'+$field_name+'" value="'+$value+'" class="box_input check_attribute_name" size="'+Size+'" maxlength="'+Max+'" />';
				}else if(Type=='textarea'){
					$Html+=	'<textarea name="'+$field_name+'" class="box_textarea">'+$value+'</textarea>';
				}else if(Type=='editor'){
					$Html+=	'<textarea id="'+$field_name+'" name="'+$field_name+'">'+$value+'</textarea>';
					$Html+=	'<script type="text/javascript">';
					$Html+=	'CKEDITOR.replace("'+$field_name+'", {"language":"'+shop_config.manage_language+'"});';
					$Html+=	'</script>';
				}
				$Html+='</div>';
			}
			return $Html;
		},

		//文本框
		attr_unit_form_edit:function(Data, Type, Name, Size, Max){
			var $Html='',
				$LangCount=0,
				$langAry=[shop_config.language_pack]; //默认语言排第一个
			for(i in shop_config.language){
				$LangCount++;
				if(shop_config.language_pack!=shop_config.language[i]){ //非默认语言靠后
					$langAry.push(shop_config.language[i]);
				}
			}
			for(i in $langAry){
				$lang=$langAry[i];
				if(Name.indexOf('Tab')!='-1'){ //判断是不是选项卡选项
					$field_name=Name+'_'+$lang;
				}else{
					$field_name=Name+'['+$lang+']';
				}
				$value=(Data[$lang]?Data[$lang]:'');
				$Html+='<div class="lang_txt lang_txt_'+$lang+'" lang="'+$lang+'"'+(shop_config.language_pack==$lang?' style="display:block;"':'')+(shop_config.language_pack==$lang?' data-default="1"':'')+'>';
				if(Type=='text'){
					$Html+='<span class="unit_input">'+($LangCount>1 ? '<b>'+lang_obj.language[$lang]+'</b>' : '')+'<input type="text" class="box_input" name="'+$field_name+'" value="'+$value+'" size="'+Size+'" maxlength="'+Max+'" /></span>';
				}
				$Html+='</div>';
			}
			return $Html;
		},

		//属性选项内容 组合和分解
		attr_option_json:function($Text) {
			var $Type = (typeof(arguments[1]) == 'undefined') ? 0 : 1, //0:组合 1:分解
				$Obj = new Object, $Ary = new Object, $Result = '';
			if ($Type == 1) {
				//分解 字符串转变对象
				$Result = new Object;
				$Obj = $Text.split('#;');
				$($Obj).each(function(index, element) {
					if (element) {
						$Ary = element.split('#:');
						$Result[$Ary[0]] = $Ary[1];
					}
				});
			} else {
				//组合 对象转变字符串
				for (k in $Text) {
					$Result += k + '#:' + $Text[k] + '#;';
				}
			}
			return $Result;
		},

		//产品属性选项按钮
		attr_option_button: function(name, attr_name, isCurrent) {
			var html = '';
			html += '<span class="btn_attr_choice' + (isCurrent ? ' current' : '') + '">';
			html += 	'<b>' + name + '</b>';
			html += 	'<input type="checkbox" name="AttrOption[' + attr_name + '][]" value="' + name + '" class="attr_current"' + (isCurrent ? ' checked' : '') + ' />';
			html += 	'<i></i>';
			html += '</span>';
			return html;
		},

		//标签选项按钮
		tags_option_button: function(name, value, isCurrent) {
			var html = '';
			html+='<span class="btn_attr_choice'+(isCurrent?' current':'')+'">';
			html+=	'<b>'+name+'</b>';
			html+=	'<input type="checkbox" name="TagsCurrent[]" value="'+value+'" class="attr_current"'+(isCurrent?' checked':'')+' />';
			html+=	'<input type="hidden" name="TagsOption[]" value="'+value+'" />';
			html += '<input type="hidden" name="TagsName[]" value="' + name + '" />';
			html+=	'<i></i>';
			html+='</span>';
			return html;
		},

		//产品属性选项
		attr_load:function(){
			sync_products_obj.function_init.attr_input();
			frame_obj.multi_lang_show_all('#edit_form');

			//属性选项整体按钮
			$('.box_attr_list .btn_attr_choice').off().on('click', function(){
				var $This=$(this),
					$Obj=$This.find('input.attr_current'),
					$Parent=$This.parent().parent(),
					$Box=$This.parents('.box_attr'),
					$LBox=$This.parents('.attr_not_yet'),
					$IsCart=$Box.find('.attr_cart').val();
				if($Parent.hasClass('attr_not_yet')){ //未选择 -> 已选中
					$This.appendTo($Parent.prev().find('.select_list'));
					$This.addClass('current');
					$This.find('i').show();
					$Obj.attr('checked', true);
					$Box.find('.placeholder').addClass('hide');
					if($Parent.find('.btn_attr_choice').length<1){
						$Parent.hide();
					}
					if(global_obj.in_array($Obj.val(), sync_products_obj.data_init.fixed_tags_data)){ //固有标签
						$This.find('i').show();
					}
				}
				if($IsCart==1 && $LBox.length){ //确保在属性选项列表里面才触发
					sync_products_obj.function_init.attr_price_show();
				}
				sync_products_obj.function_init.attr_dragsort();//属性选项拖动
				return false;
			});

			//属性选项删除按钮
			$('.box_attr_list .btn_attr_choice>i').off().on('click', function(){
				return false;
			});
			frame_obj.mouse_click($(".box_attr_list .btn_attr_choice>i"), "attr", function($this) {
				var $Obj = $this.parents('.box_attr'),
					$Parent = $this.parent().parent(),
					$Index = $this.parent().index(),
					$AttrIndex = parseInt($Obj.data("position")) - 1,
					$ExtAry = [];
				$this.parent().remove();
				$Parent.find('.box_input').val('').hide().removeAttr('style');
				if ($Parent.find('.btn_attr_choice').length < 1) {
					//没有选项，显示placeholder
					$Parent.find('.placeholder').removeClass('hide');
				}
				sync_products_obj.data_init.attr_data[$AttrIndex].Options.splice($Index, 1);
				$ExtAry = sync_products_obj.function_init.auto_combination(); //重新组合规格搭配
				$($ExtAry).each(function(index, element) {
					var $Extend = element.split('_');
					$ExtAry[index] = {"Combination":"|" + $Extend.join("|") + "|", "Extend":element, "Price":"", "ProId":"", "SKU":"", "Stock":"", "Title":$Extend.join(" / "), "VariantsId":"", "Weight":"", "PicPath":""};
				});
				sync_products_obj.data_init.ext_attr_data = $ExtAry;
				sync_products_obj.function_init.attr_price_show();
				return false;
			});

			//属性选项拖动
			sync_products_obj.function_init.attr_dragsort();

			//下拉效果
			$('.box_attr_basic_more').hover(function() {
				$(this).find('.drop_down').show().stop(true).animate({'top':24, 'opacity':1}, 250);
			}, function() {
				$(this).find('.drop_down').stop(true).animate({'top':14, 'opacity':0}, 100, function(){ $(this).hide(); });
			});

			//修改属性选项
			frame_obj.fixed_right($('.btn_option_edit'), '.fixed_edit_attribute_option_edit', function(obj) {
				var $Box = $('.fixed_edit_attribute_option_edit'),
					$Obj = obj.parents('.box_attr'),
					$AttrName = $Obj.children('input').val(),
					$AttrId = $Obj.data('id'),
					$Position = $Obj.data('position'),
					$Data = new Object, $AttrId, $Html = '', $Num = 0;
				$Box.find('.edit_attr_list').html('');
				$Box.find('input[name=AttrId]').val($AttrId);
				$Box.find('.attribute_title').text($AttrName);
				$Html+=	'<div class="rows">';
				$Html+=		'<label>' + lang_obj.manage.global.name + '</label>';
				$Html+=		'<div class="input">';
				$Html+=			'<div class="item clean">';
				$Html+=				'<input type="text" class="box_input fl" name="AttrName" value="' + $AttrName + '" size="30" maxlength="255" autocomplete="off" data-position="' + $Position + '" notnull />';
				$Html+=			'</div>';
				$Html+=		'</div>';
				$Html+=	'</div>';
				$Obj.find('.attr_selected .btn_attr_choice').each(function(index, element) {
					$Data[index] = $(this).find('.attr_current').val();
					++$Num;
				});
				if ($Num > 0) {
					$Html+=	'<div class="rows">';
					$Html+=		'<label>' + lang_obj.global.option + '</label>';
					$Html+=		'<div class="input">';
					for (k in $Data) {
						$Html+=		'<div class="item clean" data-position="'+k+'">';
						$Html+=			'<input type="text" class="box_input fl attr_option_value" value="' + $Data[k] + '" size="30" maxlength="255" autocomplete="off" notnull />';
						$Html+=			'<div class="default_name fl" title="' + $Data[k] + '">' + $Data[k] + '</div>';
						$Html+=		'</div>';
					};
					$Html+=		'</div>';
					$Html+=	'</div>';
					$Box.find('.edit_attr_list').html($Html);
					$Box.find('.edit_tips, .edit_attr_list, .box_button').show();
					$Box.find('.bg_no_table_data').hide();
				} else {
					$Box.find('.edit_tips, .edit_attr_list, .box_button').hide();
					$Box.find('.bg_no_table_data').show();
				}
			});
		},

		//产品属性选项拖动事件
		attr_dragsort:function(){
			$('.box_general_attribute .attr_selected .select_list').dragsort('destroy'); //先清除所有相关的拖动事件
			$('.box_general_attribute .box_attr').each(function(){
				frame_obj.dragsort($(this).find('.attr_selected .select_list'), '', 'span', '', '<span class="btn_attr_choice placeHolder"></span>');
			});
			$('.box_cart_attribute .attr_selected .select_list').dragsort('destroy'); //先清除所有相关的拖动事件
			$('.box_cart_attribute .box_attr').each(function(){
				frame_obj.dragsort($(this).find('.attr_selected .select_list'), '', 'span', '', '<span class="btn_attr_choice placeHolder"></span>');
			});
		},

		auto_combination: function() {
			//自动搭配选项组合
			var attr_ary = [], checked_data = [], key_ary = new Array, checked_data_len = 0;
			$(sync_products_obj.data_init.attr_data).each(function(index, element) {
				checked_data[element.Name] = element.Options
			});
			for (k in checked_data) {
				key_ary.push(k);
				checked_data_len += 1;
			}
			function CartAttr($arr, $num) {
				var _arr=new Array();
				if($num==0){
					for(j in checked_data[key_ary[$num]]){
						$arr.push(checked_data[key_ary[$num]][j]);
					}
				}else{
					for(i in $arr){
						for(j in checked_data[key_ary[$num]]){
							_arr.push($arr[i]+'_'+checked_data[key_ary[$num]][j]);
						}
					}
					$arr=_arr;
				}
				++$num;
				if($num<checked_data_len){
					CartAttr($arr, $num);
				}else{
					attr_ary=$arr;
				}
			}
			CartAttr(attr_ary, 0);
			return attr_ary;
		},

		//规格图片事件
		combination_picture:function(data) {
			//规格图片上传
			var $Position = data.Position,
				$Extend = data.Extend,
				$Top = data.Top,
				$Left = data.Left,
				$Obj = $(".box_popover[data-extend=" + $Extend + "]"),
				$Html = "", $ImgData = [], $Height = 0, $PicPath = "", $Path = "";
			sync_products_obj.function_init.clean_combination_picture();
			if (!$Obj.length) {
				$(".pro_multi_img .img").each(function(index, element) {
					$Path = $(this).find("input[type=hidden]").val();
					if ($Path) {
						$ImgData[index] = $Path;
					}
				});
				if (sync_products_obj.data_init.ext_save_data[$Extend]) {
					//已有保存数据
					$PicPath = sync_products_obj.data_init.ext_save_data[$Extend].PicPath;
				}
				$Html += '<div class="box_popover box_popover_right" data-extend="' + $Extend + '">';
				$Html += 	'<div class="box_popover_arrow"></div>';
				$Html += 	'<div class="box_popover_content">';
				$Html += 		'<div class="box_popover_picture">';
				if ($ImgData) {
					//有图片数据
					if ($ImgData.length > 8) {
						//有三行
						$Height = 310;
					} else if ($ImgData.length > 4) {
						//有两行
						$Height = 218;
					} else {
						//仅有一行
						$Height = 124;
					}
					$Top -= $Height - 40;
					$($ImgData).each(function(index, element) {
						if (element) {
							$Html += '<div class="popover_img' + ($PicPath == element ? " popover_img_selected" : "") + '" style="background-image:url(' + element + ');"></div>';
						}
					});
				} else {
					//没有图片数据
					$Height = 109;
					$Top -= $Height - 40;
					$Html += '<div class="popover_img_empty"><p>请先上传图片</p><button type="button" class="btn_global btn_add_item" id="btn_picture_upload"><span>上 传</span></button></div>';
				}
				$Html += 		'</div>';
				$Html += 	'</div>';
				$Html += '</div>';
				$("#attribute_ext").append($Html);
				$Obj = $(".box_popover[data-extend=" + $Extend + "]"); //重新获取一次
				$Obj.css({"top":$Top, "left":$Left});
			}
			$("#attribute_ext .group[data-extend=" + $Extend + "] .attr_picture").addClass("attr_picture_hover");
			$Obj.removeClass("box_popover_hidden").addClass("popover_enter_animate popover_enter_active");
			setTimeout(function() {
				$Obj.removeClass("popover_enter_animate popover_enter_active");
			}, 200);
		},

		clean_combination_picture: function(e) {
			//清除图片弹出框
			var $Where = typeof(e) != "undefined" ? $(e.target).parents('#box_popover').length == 0 : 1,
				$Obj = $('.box_popover:visible');
			if ($Obj.length) {
				if ($Where) {
					$Obj.each(function(index, element) {
						$("#attribute_ext .group[data-extend=" + $(this).data("extend") + "] .attr_picture").removeClass("attr_picture_hover");
					});
					$Obj.addClass("popover_leave_animate popover_leave_active");
					setTimeout(function() {
						$Obj.removeClass("popover_leave_animate popover_leave_active").addClass('box_popover_hidden');
					}, 200);
				}
			}
		},

		//产品属性选项添加事件
		attr_input:function(){
			//触发属性选项添加文本框
			$('.attr_selected').off().on('click', function(){
				var $Obj=$(this).find('.box_input'),
					$NotYet=$(this).next('.attr_not_yet'),
					$ItemLast=$(this).find('.btn_attr_choice:last'),
					$ItemLastLeft=($ItemLast.length?$ItemLast.offset().left:0),
					$BoxLeft=0, $BoxWidth=0;
				$(this).addClass('selected_focus');
				if($NotYet.find('.btn_attr_choice').length){
					$NotYet.slideDown();
				}
				$Obj.show().focus();
				$BoxLeft=($ItemLastLeft?($ItemLastLeft+$ItemLast.outerWidth(true))-$(this).offset().left:0);
				$BoxWidth=($(this).outerWidth()-$BoxLeft-31);
				if($BoxWidth<20){ //小于20，自动换行
					$Obj.css({'width':$(this).outerWidth()-41});
				}else{
					$Obj.css({'width':$BoxWidth, 'position':'absolute', 'bottom':5, 'left':$BoxLeft});
				}
				$Obj.off().on('blur', function() {
					//文本框失去焦点
					$Obj.val('').hide().removeAttr('style');
				}).on('keyup', function(e) {
					//属性选项添加
					var $Value = $.trim($(this).val()),
						key = window.event ? e.keyCode : e.which,
						html = '';
					if (key == 13 || key == 188) {
						//回车键 逗号
						if ($Value) {
							var $This = $(this),
								obj = $This.parents('.box_attr'),
								key = obj.attr('data-id'),
								position = obj.data('position'),
								AttrName = obj.children('input:hidden').val(), //属性名称
								Stop = 0, $ValueData = [];
							$Obj.val('').hide().removeAttr('style');
							if ($Value.indexOf(',')) {
								//包含有逗号
								$ValueData = $Value.split(',');
							} else {
								$ValueData[0] = $Value;
							}
							$($ValueData).each(function(index, element) {
								if ($.trim(element) == '') return true;
								Stop = 0;
								$This.parents('.attr_selected').find('input[name=AttrOption\\[\\]]').each(function() {
									//当前一行
									$JSON = sync_products_obj.function_init.attr_option_json($(this).val(), 1);
									if ($JSON['Name'] == element) {
										Stop = 1;
										return false;
									}
								});
								if (Stop == 0) {
									//还没有被终止
									$This.parents('.attr_selected').next().find('input[name=AttrOption\\[\\]]').each(function() {
										//下一行
										$JSON = sync_products_obj.function_init.attr_option_json($(this).val(), 1);
										if ($JSON['Name'] == element) {
											$(this).parent().click();
											Stop = 1;
											return false;
										}
									});
								}
								if (Stop == 1) {
									//终止继续执行
									return true;
								}
								if (obj.length) {
									//确保在属性选项列表里面才触发
									$Obj.prev(".select_list").append(sync_products_obj.function_init.attr_option_button(element, AttrName, 1));
									sync_products_obj.data_init.attr_data[position - 1]["Options"][sync_products_obj.data_init.attr_data[position - 1]["Options"].length] = element;
									var $ExtAry = sync_products_obj.function_init.auto_combination();
									$($ExtAry).each(function(index, element) {
										var $Extend = element.split('_'),
											$Repeat = 0;
										$(sync_products_obj.data_init.ext_attr_data).each(function(key, val) {
											if (element == val.Extend) {
												$Repeat = 1;
												$ExtAry[index] = val;
												return false;
											}
										});
										if ($Repeat == 0) {
											$ExtAry[index] = {"Combination":"|" + $Extend.join("|") + "|", "Extend":element, "Price":"", "ProId":"", "SKU":"", "Stock":"", "Title":$Extend.join(" / "), "VariantsId":"", "Weight":"", "PicPath":""};
										}
									});
									sync_products_obj.data_init.ext_attr_data = $ExtAry;
									var $ExtendAry = [];
									$(sync_products_obj.data_init.ext_attr_data).each(function(index, element) {
										if (typeof(sync_products_obj.data_init.ext_save_data[element.Extend]) == "undefined") {
											$ExtendAry = element.Extend.split("_");
											sync_products_obj.data_init.ext_save_data[element.Extend] = {"Combination":"|" + $ExtendAry.join("|") + "|", "Extend":element.Extend, "Price":"", "ProId":"", "SKU":"", "Stock":"", "Title":$ExtendAry.join(" / "), "VariantsId":"", "Weight":"", "PicPath":""};
										}
									});
									sync_products_obj.function_init.attr_load();
									sync_products_obj.function_init.attr_price_show();
								} else {
									//其他
									MaxNum = parseInt($('#tags_max_number').val()); //获取新标签ID的最大值
									MaxNum += 1;
									vid += MaxNum;
									$Obj.prev('.select_list').append(sync_products_obj.function_init.tags_option_button(element, vid, 1));
									$('#tags_max_number').val(MaxNum);
								}
							});
							$Obj.next('.placeholder').addClass('hide');
							$Obj.click();
						}
						if (window.event) {
							//IE
							e.returnValue = false;
						} else {
							//Firefox
							e.preventDefault();
						}
						return false;
					}
				});
			});

			//禁止使用回车键提交
			$('#check_attribute_name .box_input').off().on('keydown', function(e) {
				var key = window.event ? e.keyCode : e.which;
				if (key == 13) {
					//禁止使用回车键
					if (window.event) {
						//IE
						e.returnValue=false;
					} else {
						//Firefox
						e.preventDefault();
					}
					return false;
				}
			});

			/*
			$(document).off().on('click', function(e) {
				if ($('#check_attribute_name .attribute_select_list:visible').length) {
					if ($(e.target).parents('#check_attribute_name').length == 0) {
						$('.attribute_select_list').remove();
					}
				}
				if ($('.box_cart_attribute .attribute_select_list:visible').length) {
					$('.attribute_select_list').remove();
				}
			});
			*/
		},

		//依据产品分类，显示产品属性选项
		attr_category_select: function(value) {
			var $Status = 0,
                $ProId = $('#ProId').val();
			$.post('?', {"do_action":"products.products_get_attr", "ProId":$ProId}, function(data) {
				if (data.ret == 1) {
					var $Html='', $Attr=new Object, $IsChecked=0, $VId=0, $AttrCount=0, i=0,
						$IsOpenOverseas=parseInt($('#IsOpenOverseas').val());
					if(data.msg.Attr){
						$Attr = data.msg.Attr;
						for(k in $Attr){
							i = 0;
							$IsOverseas=($Attr[k]['AttrId']=='Overseas'?1:0); //是否为海外仓选项
							if($IsOverseas==1){
								for(i in $Attr[k]['Option']){ //统计海外仓对象的数量
									$AttrCount+=1;
								}
							}else{
								$AttrCount=$Attr[k]['Options'].length;
							}
							$Html+=	'<div class="box_attr clean" data-id="' + $Attr[k].AttrId + '" data-position="' + $Attr[k].Position + '">';
							$Html+=		'<div class="rows clean">';
							$Html+=			'<label>';
							$Html+=				'<strong>' + $Attr[k]['Name'] + '</strong>';
							$Html+=					'<dl class="box_basic_more box_attr_basic_more">';
							$Html+=						'<dt><a href="javascript:;" class="btn_basic_more"><i></i></a></dt>';
							$Html+=						'<dd class="drop_down">';
							$Html+=							'<a href="javascript:;" class="item input_checkbox_box btn_option_edit"><span>'+lang_obj.manage.global.edit+'</span></a>';
							$Html+=							'<a href="javascript:;" class="item input_checkbox_box btn_attribute_delete"><span>'+lang_obj.global.del+'</span></a>';
							$Html+=						'</dd>';
							$Html+=					'</dl>';
							$Html+=			'</label>';
							$Html+=			'<div class="input">';
							$Html+=				'<div class="box_attr_list">';
							$Html+=					'<div class="attr_selected">';
							$Html+=						'<div class="select_list">';
														if ($Attr[k].Options) {
															for (k2 in $Attr[k].Options) {
							$Html+=								sync_products_obj.function_init.attr_option_button($Attr[k].Options[k2], $Attr[k].Name, 1);
															}
															++i;
														}
							$Html+=						'</div>';
							$Html+=						'<input type="text" class="box_input" name="_Attr" value="" size="30" maxlength="255" />';
							$Html+=						'<span class="placeholder' + (i > 0 ? ' hide' : '') + '">' + lang_obj.manage.products.placeholder + '</span>';
							$Html+=					'</div>';
							$Html+=				'</div>';
							$Html+=			'</div>';
							$Html+=		'</div>';
							$Html+=		'<input type="hidden" name="AttrTitle[]" value="' + $Attr[k].Name + '" class="attr_title" />';
							$Html+=	'</div>';
						}
						sync_products_obj.data_init.attr_data =data.msg.Attr;
						sync_products_obj.data_init.ext_attr_data = data.msg.Combinatin;
						sync_products_obj.data_init.ext_save_data = data.msg.SaveCombination;
						$('.box_cart_attribute').html($Html);
						sync_products_obj.function_init.attr_load();
						sync_products_obj.function_init.attr_price_show(); //加载完才触发
					}
					sync_products_obj.function_init.attr_load();
				}
                $Status+=1;
                if($Status>0){
                    $('.fixed_btn_submit .btn_submit').removeClass('btn_disabled');
                }
				return false;
			}, 'json');
		},

		//产品属性价格显示
		attr_price_show:function(){
			var $ProId = parseInt($('#ProId').val()),
				IsCombination = parseInt($('input[name=IsCombination]:checked').val()), //是否组合属性
				checked_data = new Object,
				checked_data_len = 0,
				attr_ary = new Array();
			if ($ProId > 0) {
				//产品编辑(输出已有的选项组合)
				$(sync_products_obj.data_init.ext_attr_data).each(function(index, element) {
					//选项组合
					attr_ary[index] = element.Extend;
				});
			} else {
				//添加产品(自动组合)
				//已选中属性选项
				$('.box_cart_attribute .rows').each(function() {
					var _This = $(this),
						_AttrId = $(this).parent().attr('data-id');
					_This.find('.attr_current:checked').each(function() {
						if (!checked_data[_AttrId]) {
							//还没生成数组
							checked_data[_AttrId] = new Array();
							checked_data[_AttrId][0] = $(this).val();
						} else {
							//数组已存在
							checked_data[_AttrId][checked_data[_AttrId].length] = $(this).val();
						}
					});
				});
				//属性是否组合的关键点
				var key_ary = new Array(), ary_0 = ary_1 = new Array();
				if (IsCombination == 1) {
					//自动组合可以搭配的组合
					for (k in checked_data) {
						key_ary.push(k);
						checked_data_len += 1;
					}
					function CartAttr($arr, $num) {
						var _arr=new Array();
						if($num==0){
							for(j in checked_data[key_ary[$num]]){
								$arr.push(checked_data[key_ary[$num]][j]);
							}
						}else{
							for(i in $arr){
								for(j in checked_data[key_ary[$num]]){
									_arr.push($arr[i]+'_'+checked_data[key_ary[$num]][j]);
								}
							}
							$arr=_arr;
						}
						++$num;
						if($num<checked_data_len){
							CartAttr($arr, $num);
						}else{
							attr_ary=$arr;
						}
					}
					CartAttr(attr_ary, 0);
				} else {
					//不需要组合属性
					for (k in checked_data) {
						attr_ary[k]=checked_data[k];
					}
					$('#attribute_ext_box .multi_tab_row').hide();
				}
			}
			//生成表格内容
			$('#AttrId_1, #AttrId_0').html(""); //清除所有表格内容
			var Html = sync_products_obj.function_init.attr_price_row_show(attr_ary, checked_data);
			$(Html).each(function(index, element) {
				$('#attribute_ext #AttrId_' + index).append(element).find('tr').addClass('group');
				if (index == 0) {
					$('#attribute_ext #AttrId_' + index +' .group').find('td:eq(1), td:eq(2), td:gt(3)').hide();
				}
			});
			if ($("#AttrId_1").hasClass("show")) {
				//属性组合
				$('#AttrId_0').find('input').attr('disabled', 'disabled');
				$('#AttrId_1').find('input').removeAttr('disabled');
			} else {
				//属性分开
				$('#AttrId_0').find('input').removeAttr('disabled');
				$('#AttrId_1').find('input').attr('disabled', 'disabled');
			}
			frame_obj.check_amount($('#attribute_ext .relation_box'));
		},

		//tbody显示
		attr_price_row_show:function(attr_ary, checked_data){
			var html = [], _html, html_attr, ary_str, ary_merge, title, name, val_ary, _ary, $PIC, $IMG, $PV,
				ary = new Object,
				object = new Object,
				html_tmp = $('#attribute_tmp .contents').html(),
                Max = 1,
				IsCombination = ($('input[name=IsCombination]').is(':checked')?1:0), //是否组合属性
				$Html_1 = "", $Html_0 = "";
			//需要自动组合属性
			var html_t = $('#attribute_tmp .column tbody').html();
			$Html_1 += html_t.replace('XXX', 0).replace('Column', lang_obj.manage.products.group_attr);
			$(sync_products_obj.data_init.ext_attr_data).each(function(index, element) {
				if (index > (Max * 500)) {
					Max += 1;
				}
				_html = html_tmp;
				Extend = element.Extend.split('_');
				val_ary = {"SKU":element.SKU, "Price":element.Price, "Stock":element.Stock, "Weight":element.Weight};
				$PIC = "class=\"attr_picture attr_picture_empty\"";
				$IMG = "";
				if (sync_products_obj.data_init.ext_save_data[element.Extend]) {
					//已有相关数据
					_ary = sync_products_obj.data_init.ext_save_data[element.Extend];
					val_ary = {"SKU":_ary.SKU, "Price":_ary.Price, "Stock":_ary.Stock, "Weight":_ary.Weight};
					if (_ary.PicPath) {
						$PIC = "class=\"attr_picture attr_picture_empty saved\" style=\"background-image:url(" + _ary.PicPath + ");\"";
						$IMG = _ary.PicPath;
					}
				}
				html_attr = ' data-id="' + val_ary['VariantsId'] + '" data-position="' + index + '" data-extend="' + element.Extend + '"';
				$(Extend).each(function(index, element) {
					html_attr += " data-attr-" + index + "=\"" + element + "\"";
				});
				$Html_1 += _html.replace(/XXX/g, element.Extend).replace('Name', element.Title).replace('%SKUV%', val_ary["SKU"]).replace('%PV%', (parseFloat(val_ary["Price"]) > 0 ? parseFloat(val_ary["Price"]).toFixed(2) : '')).replace('%SV%', val_ary["Stock"]).replace('%WV%', val_ary["Weight"]).replace('attr_txt=""', html_attr).replace(/AttrN/g, 'Attr' + Max).replace(/data-pic=""/g, $PIC).replace(/%IMG%/g, $IMG);
			});
			//不需要自动组合属性
			$Html_0 += html_t.replace('XXX', 0).replace('Column', lang_obj.manage.products.group_attr);
			$(sync_products_obj.data_init.attr_data).each(function(index, element) {
				html += '<tr class="group_title"><td colspan="7">' + element.Name + '</td></tr>';
				if (element.Options) {
					for (i in element.Options) {
						_html = html_tmp;
						$PV = '';
						if (sync_products_obj.data_init.ext_save_data[element.Options[i]]) {
							//已有相关数据
							_ary = sync_products_obj.data_init.ext_save_data[element.Options[i]];
							$PV = _ary.Price;
						}
						$Html_0 += _html.replace(/XXX/g, element.Options[i]).replace('Name', element.Options[i]).replace('%SKUV%', '').replace('%PV%', (parseFloat($PV) > 0 ? parseFloat($PV).toFixed(2) : '')).replace(/AttrN/g, 'Attr' + Max);
					}
				}
			});
			html = [$Html_0, $Html_1];
            $('#attr_max_count').val(Max);
			return html;
		},

		sync_product_attribute: function($CateId) {
			var $Sync = $('#sync_product_hidden').val(),
				$ProId = $('#sync_product_id').val();
			$.post('./?do_action=products.get_sync_attributes', {'Sync':$Sync, 'ProId':$ProId, 'CateId':$CateId}, function(data) {
				if (data.ret == 1) {
					/*
					if (result.msg.Attr) {
						var $VId = '';
						for (k in result.msg.Attr) {
							for (k2 in result.msg.Attr[k]) {
								$VId = result.msg.Attr[k][k2];
								sync_products_obj.data_init.attr_data[$VId] = new Object;
								sync_products_obj.data_init.attr_data[$VId]['Info'] = {"Cart":"1", "Color":"0", "Column":k, "Desc":"0", "Name":k2, "VId":$VId};
								sync_products_obj.data_init.attr_data[$VId]['Data'] = ["", "0", "0.00", "0", "0"];
							}
						}
						$('.box_cart_attribute .box_attr').each(function() {
							var $ID = $(this).data('id'),
								$Title = $(this).children('input:eq(0)').val(),
								$NewID = result.msg.Attr[$Title][0];
							$(this).attr('data-id', $NewID);
							$(this).children('input:eq(0)').attr('name', $(this).children('input:eq(0)').attr('name').replace($ID, $NewID));
							$(this).children('input:eq(1)').attr('name', $(this).children('input:eq(1)').attr('name').replace($ID, $NewID));
							$(this).find('.btn_attr_choice').each(function() {
								var $VId = $(this).find('.attr_current').val(),
									$Target = $(this).find('input[name=AttrOption\\[\\]]'),
									$JSON = sync_products_obj.function_init.attr_option_json($Target.val(), 1),
									$Name = $JSON['Name'],
									$NewVId = result.msg.Attr[$Title][$Name];
								$(this).children('.attr_current').val($(this).children('.attr_current').val().replace($VId , $NewVId));
								$Target.val($Target.val().replace($VId , $NewVId).replace($ID , $NewID));
							});
						});
					}
					if (result.msg.Ext) {
						sync_products_obj.data_init.ext_attr_data = result.msg.Ext;
					}
					if (result.msg.IsCombination == 1) {
						if (!$('.open_attr_price .switchery>input').is(':checked')) {
							//第一次打开
							$('.open_attr_price .switchery').click();
						} else {
							//执行两次，刷新ID值
							$('.open_attr_price .switchery').click();
							$('.open_attr_price .switchery').click();
						}
					}
					$('.description_tab_box .drop_down .item:eq(0)').click(); //默认勾选“详细介绍”
					$('.fixed_btn_submit .btn_submit').removeClass('btn_disabled');
					*/
					var $Html='', $Attr=new Object, $IsChecked=0, $VId=0, $AttrCount=0, i=0,
						$IsOpenOverseas=parseInt($('#IsOpenOverseas').val());
					if(data.msg.Attr){
						$Attr = data.msg.Attr;
						for(k in $Attr){
							i = 0;
							$IsOverseas=($Attr[k]['AttrId']=='Overseas'?1:0); //是否为海外仓选项
							if($IsOverseas==1){
								for(i in $Attr[k]['Option']){ //统计海外仓对象的数量
									$AttrCount+=1;
								}
							}else{
								$AttrCount=$Attr[k]['Options'].length;
							}
							$Html+=	'<div class="box_attr clean" data-id="' + $Attr[k].AttrId + '" data-position="' + $Attr[k].Position + '">';
							$Html+=		'<div class="rows clean">';
							$Html+=			'<label>';
							$Html+=				'<strong>' + $Attr[k]['Name'] + '</strong>';
							$Html+=					'<dl class="box_basic_more box_attr_basic_more">';
							$Html+=						'<dt><a href="javascript:;" class="btn_basic_more"><i></i></a></dt>';
							$Html+=						'<dd class="drop_down">';
							$Html+=							'<a href="javascript:;" class="item input_checkbox_box btn_option_edit"><span>'+lang_obj.manage.global.edit+'</span></a>';
							$Html+=							'<a href="javascript:;" class="item input_checkbox_box btn_attribute_delete"><span>'+lang_obj.global.del+'</span></a>';
							$Html+=						'</dd>';
							$Html+=					'</dl>';
							$Html+=			'</label>';
							$Html+=			'<div class="input">';
							$Html+=				'<div class="box_attr_list">';
							$Html+=					'<div class="attr_selected">';
							$Html+=						'<div class="select_list">';
														if ($Attr[k].Options) {
															for (k2 in $Attr[k].Options) {
							$Html+=								sync_products_obj.function_init.attr_option_button($Attr[k].Options[k2], $Attr[k].Name, 1);
															}
															++i;
														}
							$Html+=						'</div>';
							$Html+=						'<input type="text" class="box_input" name="_Attr" value="" size="30" maxlength="255" />';
							$Html+=						'<span class="placeholder' + (i > 0 ? ' hide' : '') + '">' + lang_obj.manage.products.placeholder + '</span>';
							$Html+=					'</div>';
							$Html+=				'</div>';
							$Html+=			'</div>';
							$Html+=		'</div>';
							$Html+=		'<input type="hidden" name="AttrTitle[]" value="' + $Attr[k].Name + '" class="attr_title" />';
							$Html+=	'</div>';
						}
						sync_products_obj.data_init.attr_data =data.msg.Attr;
						sync_products_obj.data_init.ext_attr_data = data.msg.Combinatin;
						sync_products_obj.data_init.ext_save_data = data.msg.SaveCombination;
						$('.box_cart_attribute').html($Html);
						sync_products_obj.function_init.attr_load();
						sync_products_obj.function_init.attr_price_show(); //加载完才触发
					}
					sync_products_obj.function_init.attr_load();
					if (data.msg.IsCombination == 1) {
						if (!$('.open_attr_price .switchery>input').is(':checked')) {
							//第一次打开
							$('.open_attr_price .switchery').click();
						} else {
							//执行两次，刷新ID值
							$('.open_attr_price .switchery').click();
							$('.open_attr_price .switchery').click();
						}
					}
					$('.fixed_btn_submit .btn_submit').removeClass('btn_disabled');
				}
			}, 'json');
		},

		main_picture_upload: function() {
			//产品主图上传
			frame_obj.mouse_click($('.multi_img .img .upload_btn, .pic_btn .edit'), 'pro', function($this){ //产品主图点击事件
				var $ProId = parseInt($('#ProId').val()),
					$num = $this.parents('.img').attr('num');
				frame_obj.photo_choice_init('PicDetail .img[num;'+$num+']', 'products', 99, 'do_action=products.products_img_del&Model=products', 1, "frame_obj.upload_pro_img_init(1, '', 1);sync_products_obj.function_init.main_picture_upload();");
			});
			frame_obj.dragsort($('.multi_img'), '', 'dl', '.video, .show_btn, a[class!=myorder]', '<dl class="img placeHolder"></dl>'); //图片拖动
			frame_obj.mouse_click($('.pic_btn .del'), 'proDel', function($this){ //产品主图删除点击事件
				var $obj=$this.parents('.img'),
					$num=parseInt($obj.attr('num')),
					$path=$obj.find('input[name=PicPath\\[\\]]').val();
				$obj.removeClass('isfile').removeClass('show_btn');
				if($('.multi_img dl.video').index()){
					$obj.parent().find('.video').before($obj);
				}else{
					$obj.parent().append($obj);
				}
				$obj.find('.preview_pic .upload_btn').show();
				$obj.find('.preview_pic a').remove();
				$obj.find('.preview_pic input:hidden').val('').attr('save', 0);
				frame_obj.upload_pro_img_init(1);
				var $i=0;
				$obj.parent().find('.img').each(function(){
					$(this).attr('num', $i);
					++$i;
				});
			});
		}

	},
	//************************************************* 函数事件 End *************************************************

	//产品编辑详情
	sync_products_edit_init:function(){
		//复制
		$('.product_menu .btn_menu_copy').click(function() {
			var $Url = $(this).data('url');
			global_obj.win_alert(lang_obj.global.copy_confirm, function() {
				$.get($Url, function(data) {
					if (data.ret == 1) {
						window.location = '/manage/products/products/view?id=' + data.msg;
					}
				}, 'json');
			}, 'confirm');
		});
		$('#products .r_con_table .copy').click(function(){
			var $this=$(this);
			global_obj.win_alert(lang_obj.global.copy_confirm, function(){
				$.get($this.attr('href'), function(data){
					if(data.ret==1){
						window.location='/manage/products/products/view?id='+data.msg;
					}
				}, 'json');
			}, 'confirm');
			return false;
		});

		//预览
		$('.product_menu .btn_menu_view').click(function() {
			var $Url = $(this).parents('.product_menu').data('url');
			window.open($Url);
		});

		//我的应用(下拉)
		$('.product_menu_item').hover(function() {
			$(this).find('.box_my_app').show().stop(true).animate({'top':35, 'opacity':1}, 250);
		}, function() {
			$(this).find('.box_my_app').stop(true).animate({'top':25, 'opacity':0}, 100, function(){ $(this).hide(); });
		});

		//更多选项(下拉)
		$('.box_basic_more').hover(function() {
			var $Top = 42;
			$(this).hasClass('box_seo_basic_more') && ($Top = 20);
			$(this).find('.drop_down').show().stop(true).animate({'top':$Top, 'opacity':1}, 250);
		}, function() {
			var $Top = 32;
			$(this).hasClass('box_seo_basic_more') && ($Top = 10);
			$(this).find('.drop_down').stop(true).animate({'top':$Top, 'opacity':0}, 100, function(){ $(this).hide(); });
		});

		//图片上传
		sync_products_obj.function_init.main_picture_upload();
		$('.multi_img input[name=PicPath\\[\\]]').each(function(){
			if($(this).attr('save')==1){
				$(this).parent().find('img').attr('src', $(this).attr('data-value')); //直接替换成缩略图
			}
		});

		//视频事件
		frame_obj.fixed_right($('.multi_img .video .upload_btn'), '.fixed_video');


		//******************************** 销售信息 Start ********************************
		if ($('#products_inside .price_box').length) {
			$('#products_inside .price_box').each(function() {
				var $Input = $(this).find('.unit_input'),
					$Width = $(this).outerWidth(true),
					$b_w = $Input.find('b').length ? $Input.find('b')[0].getBoundingClientRect().width : 0,
					$i_w = 0;
				$i_w = $Input.find('.box_input').outerWidth(true) - $Input.find('.box_input').outerWidth();
				$Input.find('.box_input').css('width', $Width - $b_w - $i_w - 22);
			});
		}
		//******************************** 销售信息 End ********************************


		//勾选按钮
		frame_obj.switchery_checkbox(function(obj) {
			if (obj.find('input[name=SoldOut]').length) {
				//上下架
				$('#sold_out_div').css('display', 'none');
			} else if (obj.find('input[name=IsCombination]').length) {
				//多规格属性模式(显示)
				$('#attribute_unit_box').hide();
				$('#attribute_unit_box input[name=Stock]').removeAttr('notnull');
				$('.box_attribute, .box_combination, #attribute_ext_box').show();
				if (parseInt($('#edit_form input[name=ProId]').val()) > 0) {
					//产品编辑
					$('.box_attribute_tab_menu').show();
					$('.box_combination').addClass('fixed');
					$('.box_attribute_tab_menu .item.checked').click();
				}
			} else if (obj.find('input[name=IsOpenAttrPrice]').length) {
				//属性分开(开启)
				$('#attribute_ext thead').find('td:eq(1), td:eq(2), td:gt(3)').hide();
				$('#AttrId_0').show().addClass("show").find('input').removeAttr('disabled');
				$('#AttrId_1').hide().removeClass("show").find('input').attr('disabled', 'disabled');
				$('#AttrId_0 tr').each(function(index, element) {
					$(this).find('td:eq(1), td:eq(2), td:gt(3)').hide();
				});
			} else if (obj.find('input[name=IsFreeShipping]').length) {
				//免运费
				$('.box_volume_weight').css('display', 'none');
				$('.cubage_box').fadeOut();
			} else if (obj.find('input[name=IsColor]').length) {
				//设置为主图
				var $Form=$('#edit_picture_form');
				$Form.find('.edit_picture_list .item').each(function(){
					$(this).find('.img[num!=0]').show();
				});
			}
		}, function(obj){
			if(obj.find('input[name=SoldOut]').length){
				//上下架
				$('#sold_out_div').css('display', '');
			}else if(obj.find('input[name=IsSoldOut]').length){
				//定时上架
				obj.nextAll('.sold_in_time').css('display', 'none');
			}else if(obj.find('input[name=IsCombination]').length){
				//多规格属性模式(隐藏)
				$('#attribute_unit_box').show();
				$('#attribute_unit_box input[name=Stock]').attr('notnull', 'notnull');
				$('.box_attribute, .box_combination, #attribute_ext_box').hide();
				if (parseInt($('#edit_form input[name=ProId]').val()) > 0) {
					//产品编辑
					$('.box_attribute_tab_menu').hide();
				}
			}else if(obj.find('input[name=IsOpenAttrPrice]').length){
				//属性分开(关闭)
				$('#attribute_ext thead').find('td:eq(1), td:eq(2), td:gt(3)').show();
				$('#AttrId_0').hide().removeClass("show").find('input').attr('disabled', 'disabled');
				$('#AttrId_1').show().addClass("show").find('input').removeAttr('disabled');
			}else if(obj.find('input[name=IsFreeShipping]').length){
				//免运费
				$('.box_volume_weight').css('display', 'inline-block');
				if ($('.box_volume_weight .input_checkbox_box').hasClass('checked')) {
					$('.cubage_box').fadeIn();
				} else {
					$('.cubage_box').fadeOut();
				}
			}else if(obj.find('input[name=IsColor]').length){
				//设置为主图
				var $Form=$('#edit_picture_form');
				$Form.find('.edit_picture_list .item').each(function(){
					$(this).find('.img[num!=0]').hide();
				});
			}
		});

		$('.box_volume_weight .input_checkbox_box').click(function() {
			var $Obj = $(this);
			if ($Obj.hasClass('checked')) {
				$('.cubage_box').fadeOut();
			} else {
				$('.cubage_box').fadeIn();
			}
		});

		//多规格属性模式(开启)
		if ($('#IsCombination').val() == 1) {
			$('.open_attr_price .switchery').click();
		}
		if ($('#IsOpenAttrPrice').val() == 1) {
			//属性分开(开启)
			$('.box_attr_separate .switchery').click();
		} else {
			//属性分开(关闭)
			$("#AttrId_1").addClass("show");
		}

		$('.box_attribute_tab_menu .item').click(function() {
			var $Type = $(this).data('type');
			$(this).addClass('checked').siblings().removeClass('checked');
			if ($('#attribute_unit_box').is(':visible')) {
				//多属性规格模式(已关闭)
				return false;
			}
			if ($Type == 'price') {
				//属性价格管理
				$('#attribute_ext_box, .box_combination').show();
				$('.box_attribute').hide();
			} else {
				//属性选项管理
				$('#attribute_ext_box, .box_combination').hide();
				$('.box_attribute').show();
			}
		});
		if (parseInt($('#edit_form input[name=ProId]').val()) > 0) {
			$('.box_attribute_tab_menu .item:eq(0)').click();
		}

		//状态选项
		$('select[name=SoldOut]').change(function(){
			var $value=$(this).val();
			if($value==1){ //下架
				$('#sold_out_div').show();
				$('#arrival_notice_div').hide();
			}else if($value==2){ //脱销
				$('#sold_out_div').hide();
				$('#arrival_notice_div').show();
			}else{ //上架
				$('#sold_out_div, #arrival_notice_div').hide();
			}
		});

		$('.right_container .big_title').on('click', function(){
			var $This=$(this);
			if($This.find('i').length){
				if($This.next().is(':hidden')){
					$This.find('i').addClass('current');
					$This.next().slideDown();
				}else{
					$This.find('i').removeClass('current');
					$This.next().slideUp();
				}
			}
			$(window).resize();
		});

		//多语言事件
		frame_obj.multi_lang_show_all('#edit_form');
		frame_obj.multi_lang_show_item('#edit_form');


		//******************************** 所属分类 Start ********************************
		var $Html = '';
		$Html += '<div class="classify_row">';
		$Html += 	'<div class="rows float clean">';
		$Html += 		'<div class="input"></div>';
		$Html += 	'</div>';
		$Html += 	'<div class="float button">';
		$Html += 		'<a href="javascript:;" class="btn_option fl btn_option_add"><i></i></a><a href="javascript:;" class="btn_option fl btn_option_remove"><i></i></a>';
		$Html +=	'</div>';
		$Html += 	'<div class="clear"></div>';
		$Html += '</div>';

		var classify_obj = {
			'CateId': $('.classify_hide').data('cate-id'),
			'ExtCateId': ($('.classify_hide').data('ext-id') ? $('.classify_hide').data('ext-id').split(',') : ''),
			'select_html': $('.classify_hide').html(),
			'add_option': function($Txt, $Name, $Count) {
				$('.classify').append($Txt);
				$('.classify_row:last').find('.input').html(classify_obj.select_html);
				if ($('.classify_row').length > 1) {
					var $Index = $('.classify_row:last').index() - 1;
					$('.classify_row:last select').val(classify_obj.ExtCateId[$Index]).attr('name', 'ExtCateId[]').change();
					$('.classify_row:last').siblings().find('.button').addClass('hide_add').removeClass('hide_remove');
				} else {
					$('.classify_row select').val(classify_obj.CateId).attr('name', 'CateId').change();
					$('.classify_row .button').addClass('hide_remove');
				}
			},
			'remove_option': function(obj) {
				obj.parents('.classify_row').fadeOut(function() {
					$(this).remove();
					$('.classify_row:last .button').removeClass('hide_add');
					if ($('.classify_row').length == 1) {
						$('.classify_row .button').addClass('hide_remove');
					}
				});
			},
		}

		classify_obj.add_option($Html);
		if (classify_obj.ExtCateId && classify_obj.ExtCateId.length > 0) {
			for (k in classify_obj.ExtCateId) {
				classify_obj.add_option($Html, k + 1, classify_obj.ExtCateId[k + 1]);
			}
		}
		$('.classify').delegate('.btn_option_add', 'click', function() {
			classify_obj.add_option($Html, '');
		});
		$('.classify').delegate('.btn_option_remove', 'click', function() {
			classify_obj.remove_option($(this));
		});

		$('#edit_form .classify').delegate('select[name=CateId]', 'change', function(){
			if ($('#sync_product_hidden').length) {
				//针对同步产品编辑
				sync_products_obj.function_init.sync_product_attribute($(this).val());
				return false;
			}
		});
		//******************************** 所属分类 End ********************************


		//******************************** SEO Start ********************************
		//SEO编辑
		$('#edit_seo_list').click(function(){
			var $This=$(this),
				$Seo=$('.seo_box'),
				$SeoInfo=$('.seo_info_box'),
				$PageUrl=$Seo.find('.box_textarea[name=PageUrl]'),
				$Ary=new Array('title', 'description', 'keyword', 'url'),
				$Obj='', $Val='', $i=0, $j=0;
			if($This.attr('data-save')==0){ //编辑
				$This.text(lang_obj.global.pack_up).attr('data-save', 1);
				$SeoInfo.hide();
				$Seo.show();
			}else{ //收起
				$SeoInfo.show();
				$Seo.hide();
				$This.text(lang_obj.global.edit).attr('data-save', 0);
				for(i in $Ary){ //注入内容
					$Obj=$Seo.find('div[data-name='+$Ary[i]+']').find('input:eq(0), textarea:eq(0)');
					$Val=$.trim($Obj.val());
					if($Ary[i]=='url'){ //自定义地址
						$Val = $Val.replace($Obj.attr('data-domain'), '').replace('.html', '');
						$Val = $Obj.attr('data-domain') + $Val;
					}
					if($Ary[i]=='keyword'){ //关键词
						$Val='';
						$Obj=$Seo.find('div[data-name='+$Ary[i]+'] .option_selected input[name=keysName\\[\\]]');
						$Obj.each(function(){
							$Val+=($j?',':'')+$(this).val();
							++$j;
						});
					}
					$SeoInfo.find('.'+$Ary[i]).text($Val);
					if($Val==''){
						$SeoInfo.find('.'+$Ary[i]).hide();
					}else{
						++$i;
						$SeoInfo.find('.'+$Ary[i]).show();
					}
				}
				if($i>0){
					$SeoInfo.find('.blank20').show();
				}else{
					$SeoInfo.find('.blank20').hide();
				}
			}
			frame_obj.seo_url_show();
		});

		//SEO相关事件
		$('[name=PageUrl]').on('keyup', function(e) {
			var $Key = window.event ? e.keyCode : e.which,
				$Value = $.trim($(this).val());
			if ($Key == 8 && $Value == '') {
				//退格键 (不允许为空)
				$(this).val($('.global_container[data-name=basic_info] .multi_lang:eq(0) .lang_txt:eq(0) input').val().replace(/\s+/g, '-'));
			}
		});

		// 复制链接
		var clipboard=new ClipboardJS('.btn_copy');
		clipboard.on('success', function(e){
			alert(lang_obj.global.copy_complete);
		});

		//修改 SEO 关键词
		frame_obj.fixed_right($('#edit_keyword'), '.fixed_edit_keyword', function($this){
			var $ProId=$('#ProId').val(),
				$AddProId=$('#AddProId').val();
			frame_obj.seo_edit_keyword({'do_action':'action.seo_keyword_select', 'Type':'products', 'ProId':$ProId, 'AddProId':$AddProId});
		});

		frame_obj.seo_keyword_form_submit();
		//******************************** SEO End ********************************


		//******************************** 属性事件 Start ********************************
		frame_obj.fixed_right($('.box_basic_more .add_attr, #add_attribute'), '.fixed_add_attribute', function($this) {
			//添加属性
			var $Obj = $('.fixed_add_attribute'),
				$IsCart = ($this.attr('id') == 'add_attribute' ? 1 : 0),
				$IsEditor = ($this.attr('id') == 'add_editor_attribute' ? 1 : 0);
			//清空内容
			$Obj.find('.box_input').val('');
		});

		frame_obj.fixed_right($("#myorder_attribute"), ".fixed_edit_attribute", function($this) {
			//属性排序
			var $Obj = $(".fixed_edit_attribute"),
				$Title = "", $Position = 0, $Html = "", $Count = 0;
			$Obj.find(".edit_attr_list").html("");
			$Count = $(".box_cart_attribute .box_attr").length;
			if ($Count > 0) {
				$Html +=	'<div class="rows">';
				$Html +=		'<label>'+lang_obj.manage.global.name+'</label>';
				$Html +=		'<div class="input">';
				$(".box_cart_attribute .box_attr").each(function(index, element) {
					$Title = $.trim($(this).find(".attr_title").val());
					$Position = parseInt($(this).data("position"));
					$Html +=		'<div class="item clean" data-id="' + $(this).attr('data-id') + '" data-position="' + $Position + '">';
					$Html +=			'<span class="myorder"><span class="icon_myorder"></span></span>';
					$Html +=			'<strong>' + $Title + '</strong>';
					$Html +=			'<input type="hidden" value="' + $Title + '" />';
					$Html +=		'</div>';
				});
				$Html +=		'</div>';
				$Html +=	'</div>';
			}
			if ($Html) {
				$Obj.find('.edit_attr_list').html($Html);
				$Obj.find('.bg_no_table_data').hide();
				frame_obj.dragsort($Obj.find('.edit_attr_list .input'), '.item', '', '', '<div class="item placeHolder"></div>');
			} else {
				$Obj.find('.bg_no_table_data').show();
			}
		});

		frame_obj.fixed_right($("#add_combination"), ".fixed_add_combination", function($this) {
			//添加规格
			var $Obj = $(".fixed_add_combination"),
				$Title = "", $Html = "";
			$Obj.find(".add_content").html("");
			$(".box_cart_attribute .box_attr").each(function(index, element) {
				$Title = $.trim($(this).find(".attr_title").val());
				$Html+=	'<div class="rows">';
				$Html+=		'<label>' + $Title + '</label>';
				$Html+=		'<div class="input">';
				$Html+=			'<div class="item clean">';
				$Html+=				'<input type="text" class="box_input fl" value="" size="30" maxlength="255" autocomplete="off" data-position="' + $(this).data("position") + '" notnull />';
				$Html+=			'</div>';
				$Html+=		'</div>';
				$Html+=	'</div>';
			});
			if ($Html) {
				$Obj.find('.add_content').html($Html);
				$Obj.find('.bg_no_table_data').hide();
			} else {
				$Obj.find('.bg_no_table_data').show();
			}
		});
		sync_products_obj.function_init.attr_load(); //加载产品属性
		window.setTimeout(function(){
			//延时加载
			if ($('#sync_product_hidden').length) return false; //针对同步产品编辑
			sync_products_obj.function_init.attr_category_select(); //默认加载
		}, 1000);
		$(document).click(function(e){
			//属性框去掉着色
			if ($(e.target).attr('class') != 'attr_selected selected_focus') {
				$('.attr_selected').removeClass('selected_focus').next('.attr_not_yet').slideUp();
			}
			sync_products_obj.function_init.clean_combination_picture(e);
		});

		var $PicPosition = $("#PicDetail").offset().top;
		$(".box_cart_attribute").delegate(".btn_attribute_delete", "click", function() {
			//删除属性
			var $Obj = $(this).parents(".box_attr"),
				$Position = parseInt($Obj.data("position")),
				$Index = $Position - 1,
				$ExtAry = [];
			$Obj.remove();
			sync_products_obj.data_init.attr_data.splice($Index, 1);
			$ExtAry = sync_products_obj.function_init.auto_combination(); //重新组合规格搭配
			$($ExtAry).each(function(index, element) {
				var $Extend = element.split('_');
				$ExtAry[index] = {"Combination":"|" + $Extend.join("|") + "|", "Extend":element, "Price":"", "ProId":"", "SKU":"", "Stock":"", "Title":$Extend.join(" / "), "VariantsId":"", "Weight":"", "PicPath":""};
			});
			sync_products_obj.data_init.ext_attr_data = $ExtAry;
			sync_products_obj.function_init.attr_load();
			sync_products_obj.function_init.attr_price_show();
		})

		$('#attribute_ext_box').delegate('.attr_picture', 'click', function() {
			//选项组合图片上传
			var $ProId = parseInt($('#ProId').val()),
				$Position = parseInt($(this).parents('.group').data('position')),
				$Extend = $(this).parents('.group').data('extend'),
				$Top = $(this).position().top,
				$Left = $(this).position().left + 50;
			sync_products_obj.function_init.combination_picture({"Position":$Position, "Extend":$Extend, "Top":$Top, "Left":$Left});
			return false;
		}).delegate(".btn_delete", "click", function() {
			//删除选项组合
			var $Obj = $(this).parents("tr.group"),
				$Position = $Obj.data("position"),
				$Extend = $Obj.data('extend'),
				$Table = $Obj.parent(),
				$Index = "", $Count = 0, $OptionParent = [], $OptionObj = [], $OptionIndex = "";
			if ($Extend) {
				$Extend = $Extend.split("_");
				$($Extend).each(function(index, element) {
					//检查是否影响相关的选项数据
					$Count = $("#attribute_ext .group[data-attr-" + index + "=" + element + "]").length;
					if ($Count == 1) {
						//该选项的最后一个选项组合，自动删掉该选项
						$OptionObj = $(".box_cart_attribute .box_attr:eq(" + index + ") .attr_current[value=" + element + "]").parent();
						$OptionIndex = $OptionObj.index();
						$OptionParent = $OptionObj.parents(".attr_selected");
						$OptionObj.remove();
						if ($OptionObj.parent().find(".btn_attr_choice").length < 1) {
							//没有选项，显示placeholder
							$OptionParent.find(".placeholder").removeClass("hide");
						}
						sync_products_obj.data_init.attr_data[index].Options.splice($OptionIndex, 1);
					}
				});
			}
			$(this).parents("tr.group").remove();
			sync_products_obj.data_init.ext_attr_data.splice($Position, 1);
			$Table.find(".group").each(function(index, element) {
				$(this).attr("data-position", index);
			});
		}).delegate(".box_input", "change", function() {
			//文本改动
			var $Obj = $(this).parents(".group"),
				$Extend = $Obj.data("extend"),
				$SKU = $Obj.find(".box_input[data-type=sku]").val(),
				$Price = $Obj.find(".box_input[data-type=price]").val(),
				$Stock = $Obj.find(".box_input[data-type=stock]").val(),
				$Weight = $Obj.find(".box_input[data-type=weight]").val();
			//记录到保存数据
			sync_products_obj.data_init.ext_save_data[$Extend].SKU = $SKU;
			sync_products_obj.data_init.ext_save_data[$Extend].Price = $Price;
			sync_products_obj.data_init.ext_save_data[$Extend].Stock = $Stock;
			sync_products_obj.data_init.ext_save_data[$Extend].Weight = $Weight;
		}).delegate(".popover_img", "click", function() {
			//选中图片
			var $Img = $(this).attr("style"),
				$Extend = $(this).parents(".box_popover").data("extend"),
				$ImgSrc = $Img.replace("background-image:url(", "").replace(");", "");
			if (!$(this).hasClass("popover_img_selected")) {
				$(this).addClass("popover_img_selected");
				$("#attribute_ext .group[data-extend=" + $Extend + "] .attr_picture").attr("style", $Img).addClass("saved").next().val($ImgSrc);
				sync_products_obj.data_init.ext_save_data[$Extend].PicPath = $ImgSrc;
			}
		}).delegate("#btn_picture_upload", "click", function() {
			//请先上传图片
			//$("body, html").animate({scrollTop:500}, 500);
		});
		//******************************** 属性事件 End ********************************


		//******************************** 数据提交 Start ********************************
		//产品数据提交
		frame_obj.submit_form_init($('#edit_form'), '/manage/products/products', function(){
			var $name='', $notattr=0, $notnull=0, $cur_null;
            if($('.fixed_btn_submit .btn_submit').hasClass('btn_disabled')){
                return false;
            }
			$('.btn_platform').click();
			$('#edit_form *[notnull]').each(function(){
				if($.trim($(this).val())==''){
					return false;
				}
			});
			$('#edit_form .check_attribute_name').each(function(){
				if($(this).attr('data-not-add')==1){
					$cur_null=$(this);
					++$notattr;
					return false;
				}
			});
			if($notattr){
				$cur_null.css('border', '1px red solid').focus();
				global_obj.win_alert_auto_close(lang_obj.manage.products.attr_null, 'await', 1000, '8%');
				return false;
			}
			if($notnull){
				if($('.multi_tab_row>a').length>1 && $cur_null.parents('tbody').attr('id')){
					var $ovid=$cur_null.parents('tbody').attr('id').replace('AttrId_','');
					$('.multi_tab_row>a[data-oversea="'+$ovid+'"]').click();
				}
				$('.r_con_wrap').animate({scrollTop:($cur_null.position().top+$('#attribute_ext').parents('.global_container')[0].offsetTop)});
				$cur_null.find('input[name^=AttrPrice]').focus();
				global_obj.win_alert_auto_close(lang_obj.manage.products.price_null, 'await', 1000, '8%');
				return false;
			}
			return true;
		}, '', function(result){
			if(result.ret==1){
				if(result.msg.jump){ //保存产品资料
					window.location=result.msg.jump;
				}else{
					window.location.reload();
				}
			}else{
				$('#edit_form').find('input:submit').removeAttr('disabled');
				global_obj.win_alert_auto_close(result.msg, 'fail', 1000, '8%');
			}
		});

		//视频数据提交
		frame_obj.submit_form_init($('#video_form'), '', '', 0, function(result){
			if(result.ret==1){
				global_obj.win_alert_auto_close(lang_obj.global.save_success, '', 1000, '8%');
				if(result.msg[0]){
					$('#VideoUpload').addClass('is_video');
				}else{
					$('#VideoUpload').removeClass('is_video');
				}
				if(result.msg[1]){
					$('#PicDetail').prepend($('#PicDetail').find('dl.video'));
				}else{
					$('#PicDetail').append($('#PicDetail').find('dl.video'));
				}
			}
			$('#fixed_right .btn_cancel').click();
			return false;
		});

		//添加规格属性
		$(".fixed_add_attribute .btn_submit").click(function() {
			var $Obj = $(".fixed_add_attribute");
			$Obj.find('*[notnull]').each(function() {
				if ($.trim($(this).val()) == '') {
					return false;
				}
			});
			var $AttrName = $.trim($Obj.find("input[name=AttrName]").val()),
				$i = 0, $MaxCount = 0, $AttrId = 0, $Position = 0, $Html = "";
			$Obj.find(".attr_option_value").each(function(index, element) {
				$Options[index] = $(this).val();
			});
			//检查属性名称
			$i = 0;
			$(sync_products_obj.data_init.attr_data).each(function(index, element) {
				if ($AttrName == element.Name) {
					//名称相同
					$i += 1;
				}
			});
			if ($i > 0) {
				$Obj.find("input[name=AttrName]").parent().addClass("has_error");
				return false;
			} else {
				$Obj.find("input[name=AttrName]").parent().removeClass("has_error");
			}
			$MaxCount = sync_products_obj.data_init.attr_data.length;
			$Position = $MaxCount + 1;
			sync_products_obj.data_init.attr_data[$MaxCount] = {"AttrId":"", "Name":$AttrName, "Options":[], "Position":$Position};

			//目标不存在
			$Html+=	'<div class="box_attr clean" data-id="' + $AttrId + '" data-position="' + $Position + '">';
			$Html+=		'<div class="rows clean" data-id="' + $AttrId + '">';
			$Html+=			'<label>';
			$Html+=				'<strong>' + $AttrName + '</strong>';
			$Html+=				'<dl class="box_basic_more box_attr_basic_more">';
			$Html+=					'<dt><a href="javascript:;" class="btn_basic_more"><i></i></a></dt>';
			$Html+=					'<dd class="drop_down">';
			$Html+=						'<a href="javascript:;" class="item input_checkbox_box btn_option_edit"><span>' + lang_obj.manage.global.edit + '</span></a>';
			$Html+=						'<a href="javascript:;" class="item input_checkbox_box btn_attribute_delete"><span>' + lang_obj.global.del + '</span></a>';
			$Html+=					'</dd>';
			$Html+=				'</dl>';
			$Html+=			'</label>';
			$Html+=			'<div class="input">';
			$Html+=				'<div class="box_attr_list">';
			$Html+=					'<div class="attr_selected">';
			$Html+=						'<div class="select_list"></div>';
			$Html+=						'<input type="text" class="box_input" name="_Attr" value="" size="30" maxlength="255" />';
			$Html+=						'<span class="placeholder">' + lang_obj.manage.products.placeholder + '</span>';
			$Html+=					'</div>';
			$Html+=				'</div>';
			$Html+=			'</div>';
			$Html+=		'</div>';
			$Html+=		'<input type="hidden" name="AttrTitle[]" value="' + $AttrName + '" class="attr_title" />';
			$Html+=	'</div>';
			$('.box_cart_attribute').append($Html);
			sync_products_obj.function_init.attr_load();
			$('#fixed_right .btn_cancel').click();
		});

		//属性排序(提交)
		$(".fixed_edit_attribute .btn_submit").click(function() {
			var $Obj = $(".fixed_edit_attribute"),
				$SortAry = [], $NewAry = [], $CompareAry = [];
			$Obj.find(".item").each(function(index, element) {
				$SortAry[index] = $(this).find("input").val();
			});
			$($SortAry).each(function(index, element) {
				$(sync_products_obj.data_init.attr_data).each(function(key, val) {
					if (element == val.Name) {
						$CompareAry[val.Position - 1] = index;
						$NewAry[index] = val;
						$NewAry[index].Position = (index + 1);
					}
				});
			});
			sync_products_obj.data_init.attr_data = $NewAry;
			//属性调换顺序
			var $AttrSaveAry = [], $Title = "";
			$(".box_cart_attribute .box_attr").each(function(index, element) {
				$Title = $(this).find("strong").text();
				$AttrSaveAry[$Title] = $(this).html();
			});
			$(".box_cart_attribute .box_attr").remove(); //先清空
			$(sync_products_obj.data_init.attr_data).each(function(index, element) {
				$(".box_cart_attribute").append('<div class="box_attr clean" data-id="' + element.AttrId + '" data-position="' + element.Position + '">' + $AttrSaveAry[element.Name] + '</div>');
			});
			var $Extend = "", $NewExtend = "", $ExtendAry = [], $NewExtendAry = [];
			$(sync_products_obj.data_init.ext_attr_data).each(function(index, element) {
				$Extend = element.Extend;
				$ExtendAry = $Extend.split("_");
				$NewExtendAry = [];
				$($ExtendAry).each(function(index, element) {
					$NewExtendAry[$CompareAry[index]] = element;
				});
				$NewExtend = $NewExtendAry.join("_");
				sync_products_obj.data_init.ext_attr_data[index].Extend = $NewExtend;
				sync_products_obj.data_init.ext_attr_data[index].Combination = "|" + $NewExtendAry.join("|") + "|";
				sync_products_obj.data_init.ext_attr_data[index].Title = $NewExtendAry.join(" / ");
				if (typeof(sync_products_obj.data_init.ext_save_data[$NewExtend]) == "undefined") {
					sync_products_obj.data_init.ext_save_data[$NewExtend] = sync_products_obj.data_init.ext_save_data[$Extend];
				}
			});
			sync_products_obj.function_init.attr_load();
			sync_products_obj.function_init.attr_price_show();
			$("#fixed_right .btn_cancel").click();
		});

		//修改产品选项(提交)
		$(".fixed_edit_attribute_option_edit .btn_submit").click(function() {
			var $Obj = $(".fixed_edit_attribute_option_edit");
			$Obj.find("*[notnull]").each(function(){
				if ($.trim($(this).val()) == "") {
					return false;
				}
			});
			var $AttrName = $.trim($Obj.find("input[name=AttrName]").val()),
				$Position = parseInt($Obj.find("input[name=AttrName]").data("position")),
				$Index = $Position - 1;
				$Options = [], $i = 0, $OptionsAry = [],  $OptionsError = [];
			$Obj.find(".attr_option_value").each(function(index, element) {
				$Options[index] = $(this).val();
			});
			//检查属性名称
			$i = 0;
			$(sync_products_obj.data_init.attr_data).each(function(index, element) {
				if (index != $Index && $AttrName == element.Name) {
					//名称相同
					$i += 1;
				}
			});
			if ($i > 0) {
				$Obj.find("input[name=AttrName]").parent().addClass("has_error");
				return false;
			} else {
				$Obj.find("input[name=AttrName]").parent().removeClass("has_error");
			}
			//检查选项名称
			$i = 0;
			$($Options).each(function(index, element) {
				if ($OptionsAry[element] && typeof($OptionsAry[element]) != undefined) {
					//已重复名称
					$OptionsError[$i] = element;
					$i += 1;
				} else {
					$OptionsAry[element] = index + 1;
				}
			});
			$Obj.find(".attr_option_value").parent().removeClass("has_error");
			if ($i > 0) {
				$($OptionsError).each(function(index, element) {
					$Obj.find(".attr_option_value[value=" + element + "]").parent().addClass("has_error");
				});
				return false;
			}
			//替换现有的数据
			var $ReplaceAry = [];
			sync_products_obj.data_init.attr_data[$Index].Name = $AttrName;
			$(".box_cart_attribute .box_attr[data-position=" + $Position + "] strong").text($AttrName);
			$(".box_cart_attribute .box_attr[data-position=" + $Position + "] .attr_title").val($AttrName);
			$(sync_products_obj.data_init.attr_data[$Index].Options).each(function(index, element) {
				var $sObj = $(".box_cart_attribute .box_attr[data-position=" + $Position + "] .btn_attr_choice:eq(" + index + ")"),
					$Value = $sObj.find("b").text();
				sync_products_obj.data_init.attr_data[$Index].Options[index] = $Options[index];
				$sObj.find("b").text($Options[index]).next('input').val($Options[index]);
				$ReplaceAry[$Value] = $Options[index]; //旧换新
			});
			$(sync_products_obj.data_init.ext_attr_data).each(function(index, element) {
				var $Extend = element.Extend.split('_');
				$($Extend).each(function(index, element) {
					if (index == $Index) {
						//该属性
						$Extend[index] = $ReplaceAry[element];
					}
				});
				sync_products_obj.data_init.ext_attr_data[index].Extend = $Extend.join("_");
				sync_products_obj.data_init.ext_attr_data[index].Combination = "|" + $Extend.join("|") + "|";
				sync_products_obj.data_init.ext_attr_data[index].Title = $Extend.join(" / ");
				sync_products_obj.data_init.ext_save_data[element.Extend] = sync_products_obj.data_init.ext_attr_data[index]; //记录到保存数据
			});
			sync_products_obj.function_init.attr_price_show();
			//global_obj.win_alert_auto_close(lang_obj.global.save_success, '', 1000, '8%');
			$("#fixed_right .btn_cancel").click();
		});

		//添加产品规格
		$(".fixed_add_combination .btn_submit").click(function() {
			var $Obj = $(".fixed_add_combination"),
				$Pass = 0;
				$Position = "", $Value = "", $Title = "", $Index = 0, $Extend = [];
			$Obj.find(".box_input").parent().removeClass("has_error");
			$Obj.find("*[notnull]").each(function() {
				if ($.trim($(this).val()) == "") {
					$(this).parent().addClass("has_error");
					$Pass += 1;
				}
			});
			if ($Pass > 0) {
				return false;
			}
			$Obj.find(".box_input").each(function(index, element) {
				$Value = $.trim($(this).val()),
				$Position = $(this).data("position"),
				$Index = $Position - 1;
				$Extend[index] = $Value;
				$Title = $(this).parents(".rows").find("label").text();
				if (global_obj.in_array($Value, sync_products_obj.data_init.attr_data[$Index].Options)) {
					//现有选项
				} else {
					//创建选项
					$(".box_cart_attribute .box_attr[data-position=" + $Position + "] .select_list").append(sync_products_obj.function_init.attr_option_button($Value, $Title, 1));
					sync_products_obj.data_init.attr_data[$Index]["Options"][sync_products_obj.data_init.attr_data[$Index]["Options"].length] = $Value;
				}
			});
			var $MaxCount = sync_products_obj.data_init.ext_attr_data.length,
				$ExtendStr = $Extend.join("_");
			if (!$("#attribute_ext .group[data-extend=" + $ExtendStr + "]").length) {
				//添置规格数据
				sync_products_obj.data_init.ext_attr_data[$MaxCount] = {"Combination":"|" + $Extend.join("|") + "|", "Extend":$ExtendStr, "Price":"", "ProId":"", "SKU":"", "Stock":"", "Title":$Extend.join(" / "), "VariantsId":"", "Weight":"", "PicPath":""};
				if (typeof(sync_products_obj.data_init.ext_save_data[$ExtendStr]) == 'undefined') {
					//记录到保存数据
					sync_products_obj.data_init.ext_save_data[$ExtendStr] = sync_products_obj.data_init.ext_attr_data[$MaxCount];
				}
			}
			sync_products_obj.function_init.attr_load();
			sync_products_obj.function_init.attr_price_show();
			global_obj.win_alert_auto_close(lang_obj.global.save_success, '', 1000, '8%');
			$("#fixed_right .btn_cancel").click();
		});
		//******************************** 数据提交 End ********************************


		/******************************************** 同步产品编辑 Start ********************************************/
		if ($('#sync_product_hidden').length) {
			sync_products_obj.function_init.sync_product_attribute(0);
		}
		/******************************************** 同步产品编辑 End ********************************************/
	}
}