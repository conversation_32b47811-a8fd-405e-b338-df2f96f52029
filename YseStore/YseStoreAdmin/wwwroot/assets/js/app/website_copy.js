

var website_copy_obj={
	website_copy_init: function(){
		frame_obj.fixed_right($('#website_copy .sync_btn'), '.sync_website_box'); //同步网站
		frame_obj.fixed_right($('#website_copy .key_btn'), '.key_management_box'); //密钥管理
		$('#website_copy_creat_apikey').click(function(){
			var $this = $(this);
			$.post('/manage/plugins/website-copy/create-apikey', $('#key_management_copytype').serialize(), function(data){
				if (data.ret == 1) {
					$('.key_input').show();
					$this.html(lang_obj.manage.app.website_copy.refresh_key);
					$('#key_management_copytype .btn_copy').show();
					$('#key_management_copytype .expired_btn').hide();
					$('.key_management_box input[name="Key"]').removeClass('expired_input').val(data.msg.CopyApi<PERSON>ey).focus();
					global_obj.win_alert_auto_close(data.msg.tips, '', 1500, '8%');
				} else {
					global_obj.win_alert_auto_close(data.msg.tips, 'await', 1000, '8%');
				}
			},'json');
		});
		// 复制链接
		var clipboard=new ClipboardJS('.btn_copy');
		clipboard.on('success', function(e){
			alert(lang_obj.global.copy_complete);
		});
		var o = $('#sync_website_form'),
			obj = $('#website_copy_sync'),
			$timeout = $error = 0;
		o.submit(function(){return false;});
		obj.click(function(){
			if(global_obj.check_form(o.find('*[notnull]'), o.find('*[format]'), 1)){ return false; };
			$('#website_copy .sync_progress_bg, #website_copy .sync_progress').show();
			$(this).attr('disabled', true);
			$.ajax({
				url: '/manage/plugins/website-copy/sync',
				type: 'post',
				data: o.serialize(),
				dataType: 'json',
				success: function(result) {
					if (result.ret == 2) {
						var $AllCouont = $IngCount = $Rata = 0;
						if(typeof(result.msg) == 'object') {
							for (i in result.msg) {
								if (typeof(result.msg[i]) == 'object') {
									var $ing = result.msg[i][2];
									(result.msg[i][2] > result.msg[i][1]) && ($ing = result.msg[i][1]);
									$AllCouont += parseInt(result.msg[i][1]);
									$IngCount += parseInt(result.msg[i][2]);
								}
							}
						}
						$Rata = ($IngCount/$AllCouont*100).toFixed(2);
						$('#website_copy .sync_progress .complete').text($IngCount);
						$('#website_copy .sync_progress .total').text($AllCouont);
						$('#website_copy .sync_progress .num').css('width', $Rata+'%');
						$('#website_copy .sync_progress .num span').text(($Rata>100 ? 100 : $Rata)+'%');
						setTimeout(function(){
							$('#website_copy_sync').click();
						}, 300);

					} else if (result.ret == 1) {
						$('.sync_website_box input[name="CopyApiKey"]').val('');
						$('.sync_website_box .close').click();
						global_obj.win_alert_auto_close(result.msg, '', 1500, '8%');
						$('#website_copy .sync_progress_bg, #website_copy .sync_progress').hide();
					} else {
						global_obj.win_alert_auto_close(result.msg, 'await', 3000, '8%');
						$('#website_copy .sync_progress_bg, #website_copy .sync_progress').hide();
						$('.sync_website_box input[name="CopyApiKey"]').val('');
					}
					obj.attr('disabled', false);
				},
				complete: function(XMLHttpRequest, status) {
					if (status == 'timeout') {
						//超时
						obj.attr('disabled', false);
						$timeout++;
						if ($timeout <=1) {
							obj.click();
						} else {
							global_obj.win_alert(lang_obj.manage.error.server_timeout);
							$('#website_copy .sync_progress_bg, #website_copy .sync_progress').hide();
							$('.sync_website_box input[name="CopyApiKey"]').val('');
						}
					}
				},
				error: function(XMLHttpRequest, status) {
					//服务器错误
					obj.attr('disabled', false);
					$error++;
					if ($error <=1) {
						obj.click();
					} else {
						global_obj.win_alert(lang_obj.manage.error.server_error);
						$('#website_copy .sync_progress_bg, #website_copy .sync_progress').hide();
						$('.sync_website_box input[name="CopyApiKey"]').val('');
					}
				}
			});
		});
	}
}