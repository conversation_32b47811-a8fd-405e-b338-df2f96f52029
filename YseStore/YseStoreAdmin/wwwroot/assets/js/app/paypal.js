

var paypal_obj = {
	invoice_init: function () {
        const INSTANCE = {
			render: {
				invoice: $('#paypal_invoice')
			}
		}

        // 初始化页面 Start

        if ($('.btn_paypal_sign').length) {
            $.post('/manage/plugins/paypal/partner', '', 
                function (data) {
                    if (data.ret == 1) {
                        $('.btn_paypal_sign').attr('href', data.msg + '&displayMode=minibrowser')
                        $('.btn_paypal_sign').css('display', 'inline-block')
                    } else {
                        data.msg && global_obj.win_alert(data.msg)
                    }
                },
                'json'
            )
		}

        
        frame_obj.fixed_right($('.btn_paypal_activate'), '.box_activate')

        frame_obj.submit_form_init($('#form_activate'), '', '', '', function (result) {
            if (result.ret == 1) {
                // 激活成功
                global_obj.win_alert_auto_close(result.msg, '', 1000, '8%')
                setTimeout(() => {
                    window.location.reload()
                }, 1000)
            } else {
                // 激活失败
                global_obj.win_alert_auto_close(result.msg, 'fail', 1000, '8%')
            }
			$('#fixed_right .top_title .close').click()
		})

        // 初始化页面 End

        // 列表页面 Start

        frame_obj.del_init(INSTANCE.render.invoice.find('.r_con_table')) // 删除事件
        frame_obj.select_all(INSTANCE.render.invoice.find('input[name=select_all]'), INSTANCE.render.invoice.find('input[name=select]'), INSTANCE.render.invoice.find('.table_menu_button .del')) // 批量操作
        frame_obj.del_bat(INSTANCE.render.invoice.find('.table_menu_button .del'), INSTANCE.render.invoice.find('.r_con_table input[name=select]'), '/manage/plugins/paypal/invoice-delete-bat') // 批量删除

        frame_obj.fixed_right($('.btn_invoice_set'), '.box_account')

        // 禁用账号
        $('.btn_account_disabled').click(function () {
            global_obj.win_alert(lang_obj.global.disable_confirm, () => {
                $.get('/manage/plugins/paypal/invoice-disabled', '',
                    () => {
                        window.location.reload()
                    },
                    'json'
                )
			}, 'confirm')
        })

        $('.icon_publish').click(function () {
            let $id = $(this).parents('tr').attr('data-id')
            $.get('/manage/plugins/paypal/invoice-send', {'id': $id},
                (result) => {
                    if (result.ret == 1) {
                        // 激活成功
                        global_obj.win_alert_auto_close(result.msg, '', 1000, '8%')
                        setTimeout(() => {
                            window.location.reload()
                        }, 1000)
                    } else {
                        // 激活失败
                        global_obj.win_alert_auto_close(result.msg, 'fail', 1000, '8%')
                    }
                },
                'json'
            )
        })

        // 列表页面 End
	},

    invoice_edit_init: function () {
        const INSTANCE = {
			render: {
				form: $('#edit_form'),
                charge: $('.charge_container')
			}
		}

        $('.box_email').on('click', '.btn_add', function () {
            // 添加客户邮箱
            let $obj = $('.box_email')
            let $html = $obj.find('.item_account').eq(0).html()
            let tpl = `<div class="item_account">${$html}</div>`
            $obj.find('.input').append(tpl)
            $obj.find('.item_account:last .box_input').val('')
        }).on('click', '.btn_item_remove', function () {
            // 删除客户邮箱
            $(this).parent().remove()
        })

        $('.inventory_container').on('click', '.btn_add', function () {
            // 添加物品清单
            let $obj = $('.box_inventory')
            let $html = $obj.find('.item_inventory').eq(0).html()
            let tpl = `<div class="item_inventory">${$html}</div>`
            $obj.append(tpl)
            $obj.find('.item_inventory:last .box_input').val('')
        }).on('click', '.btn_item_remove', function () {
            // 删除物品清单
            $(this).parent().remove()
        })

        let resultPrice = function () {
			let amount = 0
			INSTANCE.render.form.find('input[name=Price\\[\\]]').each(function (index, element) {
                let qty = parseInt($('input[name=Qty\\[\\]]').eq(index).val() || 0)
                if (isNaN(qty)) qty = 0
				amount += $(this).val() * qty
			})
			INSTANCE.render.form.find('input[name=amount]').val(amount)
			INSTANCE.render.form.find('.amount_price>.change_amount').html(amount.toFixed(2))

            let $discountType = INSTANCE.render.charge.find('.box_type_menu .item.checked input').val()
            let discountPrice = 0
			if ($discountType == 'percent') {
                // 百分比
				discountPrice = amount / 100 * INSTANCE.render.charge.find('input[name=Discount]').val()
			} else {
                // 现金减价
                discountPrice = INSTANCE.render.charge.find('input[name=DiscountPrice]').val()
            }

            let shippingPrice = INSTANCE.render.charge.find('input[name=ShippingPrice]').val()
            let feePrice = INSTANCE.render.charge.find('input[name=FeePrice]').val()

			let total = parseFloat(amount) - parseFloat(discountPrice) + parseFloat(shippingPrice) + parseFloat(feePrice)
			INSTANCE.render.form.find('input[name=total]').val(total)
			INSTANCE.render.form.find('.bill_total_price>.change_amount').html(total.toFixed(2))
		}
		INSTANCE.render.form.on('change', 'input[name=Price\\[\\]], input[name=Qty\\[\\]], input[name=Discount], input[name=DiscountPrice], input[name=ShippingPrice], input[name=FeePrice]',function () {
            resultPrice()
		})
        resultPrice()

        INSTANCE.render.charge.find('select[name=Currency]').on('change', function () {
			let symbol = $(this).find('option:checked').attr('attr')
			INSTANCE.render.form.find('input[name=symbol]').val(symbol)
			INSTANCE.render.form.find('.change_currency').html(symbol)
		})
        INSTANCE.render.charge.find('select[name=Currency]').trigger('change')

        // 折扣类型
		frame_obj.box_type_menu(function(obj) {
			let $con_obj = obj.parent().next('.box_type_menu_content')
			let $value = obj.find('input[name="DiscountType"]').val()
			$con_obj.find('.item input').attr('disabled', true)
			$con_obj.find('.item[data-type="' + $value + '"] input').removeAttr('disabled')
		})

        // 提交
		frame_obj.submit_form_init(INSTANCE.render.form, '', function() {
            /*
			let $Error = 0,
				$StockError = 0,
				$TotalError = 0,
				$StockErrorAry = new Array();
			$('.orders_products_list tbody tr').each(function() {
				let $SoldStatus = parseInt($(this).find('.ext_attr').attr('data-soldstatus')),
					$Qty = parseInt($(this).find('input[name^=Qty]').val()),
					$Stock = parseInt($(this).find('.stock').text())
				$Error = 0
				$(this).find('.box_select').each(function() {
					if ($(this).find('select[notnull]').length && !$(this).find('select[notnull]').val()) {
						++$Error
					}
				});
				$(this).find('.attr_data .error').remove()
				$(this).removeClass('stock_error')
				if ($Error > 0) {
					$(this).find('.attr_data').append('<p class="error fc_red">' + lang_obj.manage.sales.unselected_attr + '</p>')
					++$TotalError
				}
				if ($SoldStatus != 1 && $Qty > $Stock) {
					$StockErrorAry.push($(this).index('tbody tr'))
					++$StockError
				}
			});
			if ($TotalError > 0) {
				global_obj.win_alert_auto_close(lang_obj.manage.orders.someNoAttr, 'fail', 3000, '8%')
				return false
			}
			if ($StockError > 0) {
				global_obj.win_alert_auto_close(lang_obj.manage.orders.someNoStock, 'fail', 3000, '8%')
				$StockErrorAry.forEach((elem, index) => {
					$('.orders_products_list tbody tr:eq(' + elem + ')').addClass('stock_error')
				});
				$('.orders_products_list tbody tr.stock_error:eq(0) input[name^=Qty]').focus()
				return false
			}
            */
		}, '', function(result) {
            let $return_url = $('#paypal_invoice_inside .return_title a').attr('href')
            window.location.href = $return_url
		})
    }
}
