

var shopping_feed_obj={
	init: function(){
		$('#shopping_feed .r_con_table .box_input').keyup(function(){
			if ($('.row_save_box').hasClass('none')) {
				$('.row_save_box').removeClass('none');
				$(window).resize();
			}
		});
		$('#shopping_feed .r_con_table .box_input').change(function(){
			if ($('.row_save_box').hasClass('none')) {
				$('.row_save_box').removeClass('none');
				$(window).resize();
			}
		});
		frame_obj.submit_object_init($('#edit_form'), location.href);
		shopping_feed_obj.category()
		$('.relate_attr_select').change(function(){
			let _Val = $(this).val()
			let _ProId = $(this).attr('data-proid')
			let _Type = $(this).attr('data-type')
			let _loader = frame_obj.loader('.shopping_feed')
			_loader.on()
			$.post('/manage/plugins/shopping-feed/relate-attr', {ProId:_ProId, AttrId:_Val, Type:_Type}, function(data){
				if(data.ret == 1 && typeof(data.msg) == 'object'){
					$.each(data.msg, function(k, v){
						let _com = $(`.shipping_feed_data tr.pattr[data-cid="${k}"]`).find(`.box_input[data-type="${_Type}"]`)
						_com.val(v)
						if (_Val > 0) {
							_com.attr('readonly', 'readonly')
						} else {
							_com.removeAttr('readonly')
						}
					})
				}
				setTimeout(function(){
					_loader.off()
				}, 200)
			},'json');
		})
	},
	category: function () {
		//选择谷歌分类
		frame_obj.fixed_right($('.btn_google_category'), '.box_google_category', '', function($this){
			$('.box_google_category .input_radio_box.checked').removeClass('checked').find('input').prop('checked', false);
			$('.box_google_category .child.current').removeClass('current');
			if ($('.box_google_category input[name=ProId]').length) {
				$('.box_google_category input[name=ProId]').val($this.attr('data-proid'));
				var $val = $this.siblings('input').val();
			} else {
				var $val = $('input[name="GoogleProductCategoryId').val();
			}
			$('.box_google_category .input_radio_box input[value=' + $val + ']').prop('checked', true).parents('.input_radio_box').addClass('checked').parents('.child').addClass('current');
		});
		$('.box_google_category .ul .child>.name>.btn_child').click(function(){
			if ($(this).parent().parent().hasClass('current')) {
				$(this).parent().parent().removeClass('current')
			} else {
				$(this).parent().parent().addClass('current')
			}
		});
		$('.box_google_category .save_google_category').click(function(){
			if ($('.box_google_category input[name=ProId]').length) {
				$('.google_cate[data-proid=' + $('.box_google_category input[name=ProId]').val() + '] span').text($('.box_google_category .input_radio_box.checked').text());
				$('.google_cate[data-proid=' + $('.box_google_category input[name=ProId]').val() + '] input').val($('.box_google_category .input_radio_box.checked input').val());
				$('#shopping_feed .r_con_table .box_input').eq(0).trigger('change');
			} else {
				$('#api_setting .google_category_val').text($('.box_google_category .input_radio_box.checked').text());
				$('#api_setting input[name="GoogleProductCategoryId"]').val($('.box_google_category .input_radio_box.checked input').val());
			}
		});
	}
}