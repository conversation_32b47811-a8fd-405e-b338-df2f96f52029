/*

 * 批发 APP
 */

var recall_obj = {
    recall_init: () => {
        frame_obj.switchery_checkbox((obj) => {
            if (obj.find('input[name=IsCart]').length) {
                $('.fixed_recall_set .box_cart_set_overlay').removeClass('show')
                if ($('.box_cart_set input[name="UsedCoupon"]').is(':checked')) {
                    $('.coupon_switchery_box input[name="Coupon"]').attr('notnull', '')
                }
            } else if (obj.find('input[name=UsedCoupon]').length) {
                $('.coupon_switchery_box').show().find('input[name="Coupon"]').attr('notnull', '')
            } else if (obj.find('input[name=openCoupon]').length) {
                $('.coupon_select_box').removeClass('hide')
            }
        }, (obj) => {
            if (obj.find('input[name=IsCart]').length) {
                $('.fixed_recall_set .box_cart_set_overlay').addClass('show')
                $('.coupon_switchery_box input[name="Coupon"]').removeAttr('notnull')
            } else if (obj.find('input[name=UsedCoupon]').length) {
                $('.coupon_switchery_box').hide().find('input[name="Coupon"]').removeAttr('notnull')
            } else if (obj.find('input[name=openCoupon]').length) {
                $('.coupon_select_box').addClass('hide')
            }
        })

        // 设置
        frame_obj.fixed_right($('#btn_recall_set'), '.fixed_recall_set', ($this) => { })
        frame_obj.submit_form_init($('#recall_set_form'), '', '', '', (data) => {
            if (data.ret == 1) {
                global_obj.win_alert_auto_close(lang_obj.global.save_success, '', 1000, '8%')
            } else {
                global_obj.win_alert_auto_close(data.msg, 'fail', 1000, '8%')
            }
            $('#fixed_right .btn_cancel').click()
        })
    },

    recall_user_init: () => {
        // 客户加购页面初始化
        const INSTANCE = {
            render: {
                user: $('.recall_user_box'),
            }
        }

        recall_obj.recall_init()

        new dateselector('#recallViews', {
            submit: async ({date, diff, compare}) => {
                recall_obj.get_user_data(1, date)
            }
        })
        $('.dateselector').find('.btn_submit').click()
        setTimeout(() => {
            $('.dateselector').find('select[name=date]').val(0).trigger('change')
        }, 1000)

        INSTANCE.render.user.on('click', '.pagination li a',
            function (event) {
                // 翻页
                event.stopPropagation()
                let $this = $(this)
                let $page = parseInt($this.attr('data-page')) + 1
                let $dateValue = $('#recallViews').attr('data-value')
                let $date = global_obj.json_encode_data($dateValue).date || []
                window.history.pushState(null, null, location.href.replace(/\?page=([0-9]*)/, '') + '?page=' + $page)
                recall_obj.get_user_data($page, $date)
                return false
            }
        )
    },

    recall_log_init: () => {
        // 召回日志页面初始化
        const INSTANCE = {
            render: {
                user: $('.recall_user_box'),
                search: $('.search_box')
            }
        }

        recall_obj.recall_init()
        
        recall_obj.get_log_data(1)

        INSTANCE.render.user.on('click', '.pagination li a',
            function (event) {
                // 翻页
                event.stopPropagation()
                let $this = $(this)
                let $page = parseInt($this.attr('data-page')) + 1
                window.history.pushState(null, null, location.href.replace(/\?page=([0-9]*)/, '') + '?page=' + $page)
                recall_obj.get_log_data($page)
                return false
            }
        )

        INSTANCE.render.search
        .on('submit', 'form', () => {
            return false
        })
        .on('click', '.search_btn', () => {
            recall_obj.get_log_data(1)
            return false
        })
    },
    
    get_user_data: (page, date) => {
        loader($('.loader_content')).on()
        $getData = { 'page': page, 'date': date }
        if (page == 0) $('.r_con_table').find('tbody').html('')
        $.get('/manage/plugins/shopping-cart-recall/get-user-data', $getData,
            function (result) {
                if (result.ret == 1) {
                    let _data = result.msg
                    let trHtml = ''
                    let pageHtml = ''
                    let boxObj = $('.recall_user_box')
                    if (_data['list'].length > 0) {
                        boxObj.find('table').show()
                        boxObj.find('.bg_no_table_data').hide()
                        for (i in _data['list']) {
                            let _userData = _data['list'][i]
                            trHtml += `
                            <tr data-id="${_userData.id}">
                                <td nowrap>${_userData.email}</td>
                                <td nowrap>${lang_obj.manage.app.shopping_cart_recall.products.replace('{{count}}', _userData.count)}<a href="javascript:;" class="btn_user_detail">${lang_obj.manage.app.shopping_cart_recall.details}</a></td>
                                <td nowrap class="operation tar flex_item last">
                                    <a href="/manage/plugins/shopping-cart-recall/send?id=${_userData.id}" class="oper_icon icon_email button_tips">${lang_obj.manage.app.shopping_cart_recall.manual_recall}</a>
                                    <a href="/manage/user/user/view?UserId=${_userData.id}" target="_blank" class="oper_icon icon_file button_tips">${lang_obj.manage.global.view}</a>
                                </td>
                            </tr>
                            `
                        }
                        if (trHtml) boxObj.find('tbody').html(trHtml)
                        if (_data['list'].length > 0) pageHtml = _data.turnPage
                        boxObj.find('.user_view_page').html(pageHtml)
                        boxObj.find('.user_view_page .pagination a').attr('href', 'javascript:;')
                        
                        recall_obj.user_detail()
                    } else {
                        boxObj.find('table').hide()
                        boxObj.find('.bg_no_table_data').show()
                    }
                }
                setTimeout(() => {
                    $('.loader').remove()
                }, 1000)
            },
            'json'
        )
    },

    user_detail: () => {
        // 客户加购明细
        frame_obj.fixed_right($('.btn_user_detail'), '.fixed_cart_box', function (_this) {
            let $obj = $('.fixed_cart_box')
            let $id = _this.parents('tr').attr('data-id')
            let $dateValue = $('#recallViews').attr('data-value')
            let $date = global_obj.json_encode_data($dateValue).date || []
            let $page = 1
            $obj.find('.box_table').find('tbody').html('')
            $obj.find('.load_more').remove()
            $obj.find('.no_data').remove()
            let viewData = {
                id: $id,
                date: $date,
                page: $page
            }
            recall_obj.append_html('view-cart', viewData)
        })

        $('.fixed_cart_box').off().on('click', '.load_more', function () {
            let $this = $(this)
            let $id = $this.attr('data-id')
            let $dateValue = $('#recallViews').attr('data-value')
            let $date = global_obj.json_encode_data($dateValue).date || []
            let $page = $this.attr('data-page')
            $this.remove()
            let viewData = {
                id: $id,
                date: $date,
                page: $page
            }
            recall_obj.append_html('view-cart', viewData)
        })
    },

    get_log_data: (page) => {
        loader($('.loader_content')).on()
        let keyword = $.trim($('input[name="keyword"]').val())
        $getData = { 'page': page, 'keyword': keyword }
        if (page == 0) $('.r_con_table').find('tbody').html('')
        $.get('/manage/plugins/shopping-cart-recall/get-log-data', $getData,
            function (result) {
                if (result.ret == 1) {
                    let _data = result.msg
                    let trHtml = ''
                    let pageHtml = ''
                    let boxObj = $('.recall_user_box')
                    if (_data['list'].length > 0) {
                        boxObj.find('table').show()
                        boxObj.find('.bg_no_table_data').hide()
                        for (i in _data['list']) {
                            let _logData = _data['list'][i]
                            trHtml += `
                            <tr data-email="${_logData.email}">
                                <td nowrap>${_logData.email}</td>
                                <td nowrap>${_logData.successedCount} / ${_logData.sentCount}</td>
                                <td nowrap class="operation tar flex_item last">
                                    <a href="javascript:;" class="oper_icon icon_file button_tips btn_log_detail">${lang_obj.manage.global.view}</a>
                                </td>
                            </tr>
                            `
                        }
                        if (trHtml) boxObj.find('tbody').html(trHtml)
                        if (_data['list'].length > 0) pageHtml = _data.turnPage
                        boxObj.find('.user_view_page').html(pageHtml)
                        boxObj.find('.user_view_page .pagination a').attr('href', 'javascript:;')
                        
                        recall_obj.log_detail()
                    } else {
                        boxObj.find('table').hide()
                        boxObj.find('.bg_no_table_data').show()
                    }
                }
                setTimeout(() => {
                    $('.loader').remove()
                }, 1000)
            },
            'json'
        )
    },

    log_detail: () => {
        // 召回日志明细
        frame_obj.fixed_right($('.btn_log_detail'), '.fixed_log_box', function (_this) {
            let $obj = $('.fixed_log_box')
            let $email = _this.parents('tr').attr('data-email')
            let $page = 1
            $obj.find('.box_table').find('tbody').html('')
            $obj.find('.load_more').remove()
            $obj.find('.no_data').remove()
            let viewData = {
                email: $email,
                page: $page
            }
            recall_obj.append_html('view-log', viewData)
        })

        $('.fixed_log_box').off().on('click', '.load_more', function () {
            let $this = $(this)
            let $email = $this.attr('data-email')
            let $page = $this.attr('data-page')
            $this.remove()
            let viewData = {
                email: $email,
                page: $page
            }
            recall_obj.append_html('view-log', viewData)
        })
    },

    append_html: ($method, data) => {
        $.post('/manage/plugins/shopping-cart-recall/' + $method, data,
            (result) => {
                if (result.msg) {
                    let Html = result.msg.Html
                    let ExtHtml = result.msg.ExtHtml
                    if ($method == 'view-cart' || $method == 'view-cart-now') {
                        $Obj = $('.fixed_cart_box')
                    } else {
                        $Obj = $('.fixed_log_box')
                    }

                    if (Html) {
                        $Obj.find('.box_table tbody').append(Html)
                    }
                    if (ExtHtml) {
                        $Obj.find('.box_table').append(ExtHtml)
                    }
                }
            },
            'json'
        )
    },

    recall_send_init: () => {
        // 手动召回页面初始化
        const INSTANCE = {
            render: {
                form: $('.recall_send_form'),
                select: $('.simulate_select_box'),
                fixedCart: $('.fixed_cart_box'),
                proTable: $('.box_products_table')
            }
        }

        recall_obj.recall_init()

        // 添加产品
        frame_obj.fixed_right($('.btn_add_product'), '.fixed_cart_box', (_this) => {
            let $theadObj = INSTANCE.render.fixedCart.find('.box_table thead')
            let $id = $('input[name=id]').val()
            let $page = 1
            INSTANCE.render.fixedCart.find('.box_table tbody').html('')
            INSTANCE.render.fixedCart.find('.load_more').remove()
            INSTANCE.render.fixedCart.find('.no_data').remove()
            $theadObj.find('.btn_checkbox').removeClass('current indeterminate').find('input').attr('checked', false)
            $theadObj.find('tr').removeClass('current')
            $theadObj.find('.global_menu_button .open').addClass('no_select')
            let viewData = {
                id: $id,
                page: $page
            }
            recall_obj.append_html('view-cart-now', viewData)
        })

        INSTANCE.render.fixedCart.on('click', '.load_more', function () {
            let $this = $(this)
            let $id = $('input[name=id]').val()
            let $page = $this.attr('data-page')
            $this.remove()
            let viewData = {
                id: $id,
                page: $page
            }
            recall_obj.append_html('view-cart-now', viewData)
            // 取消全选效果
            if (INSTANCE.render.fixedCart.find('.box_table .btn_checkbox').hasClass('current')) {
                INSTANCE.render.fixedCart.find('.box_table .btn_checkbox').trigger('click')
            }
        }).on('click', '.btn_submit', function () {
            let $tableObj = INSTANCE.render.fixedCart.find('.box_table')
            let html = ''
            $tableObj.find('.input_checkbox_box.checked').each(function () {
                html += '<tr>' + $(this).parents('tr').html() + '</tr>'
            })
            if (html) {
                INSTANCE.render.proTable.removeClass('hide').find('tbody').html(html)
                INSTANCE.render.proTable.find('.attr_checkbox').remove()
                INSTANCE.render.proTable.find('tr').removeClass('current')
                global_obj.win_alert_auto_close(lang_obj.global.add_success, '', 1000, '8%')
                $('.fixed_cart_box').find('.top_title .close').trigger('click')
            } else {
                global_obj.win_alert_auto_close(lang_obj.global.add_fail, 'await', 1000, '8%')
            }
        })

        INSTANCE.render.proTable.on('click', '.icon_delete', function () {
            // 删除产品
            $(this).parents('tr').remove()
            $('#button_float_tips').remove()
            if (!INSTANCE.render.proTable.find('tbody tr').length) {
                INSTANCE.render.proTable.addClass('hide')
            }
        })
        
        INSTANCE.render.fixedCart.delegate('.input_checkbox_box, .btn_checkbox', 'click', function() {
            // 批量勾选
            let $Checked = 0
            let $mainObj = $(this).parents('.box_table')
            let $tbodyObj = $mainObj.find('tbody')
            let $CheckedCount = $tbodyObj.find('.input_checkbox_box.checked').length
            let $Count = $tbodyObj.find('.input_checkbox_box').length
            if ($Count == 0) {
                return false
            }
            if ($(this).hasClass('btn_checkbox')) {
                if ($(this).hasClass('current')) {
                    // 勾选
                    $Checked = 1
                    $CheckedCount += 1
                } else {
                    // 取消勾选
                    $CheckedCount -= 1
                }
            } else {
                if ($(this).hasClass('checked')) {
                    // 取消勾选
                    $CheckedCount -= 1
                } else {
                    // 勾选
                    $Checked = 1
                    $CheckedCount += 1
                }
            }
            if ($(this).parents('thead').length) {
                // 全选
                if ($Checked == 1) {
                    $tbodyObj.find('.input_checkbox_box').each(function(index, element) {
                        $(element).addClass('checked').find('input').attr('checked', true)
                    })
                } else {
                    $tbodyObj.find('.input_checkbox_box').each(function(index, element) {
                        $(element).removeClass('checked').find('input').attr('checked', false)
                    })
                }
                $CheckedCount = $tbodyObj.find('.input_checkbox_box.checked').length
                if ($Checked == 1) {
                    $mainObj.find('thead .btn_checkbox').removeClass('indeterminate')
                }
            } else {
                // 部分勾选
                if ($CheckedCount == $Count) {
                    // 全选
                    $mainObj.find('thead .btn_checkbox').removeClass('indeterminate').addClass('current').find('input').attr('checked', true)
                } else if ($CheckedCount == 0) {
                    // 没有勾选
                    $mainObj.find('thead .btn_checkbox').removeClass('current indeterminate').find('input').attr('checked', false)
                } else {
                    $mainObj.find('thead .btn_checkbox').removeClass('current').addClass('indeterminate').find('input').attr('checked', false)
                }
            }
            if ($CheckedCount > 0) {
                $('.box_table thead tr').addClass('current')
                $('.box_table thead .global_menu_button .open').removeClass('no_select')
                $('.box_table thead .global_menu_button .open>span').text($CheckedCount)
            } else {
                $('.box_table thead tr').removeClass('current');
                $('.box_table thead .global_menu_button .open').addClass('no_select')
            }
        })

        // 选择优惠券插件		
		$('body').click((e) => {
			// 关闭
			INSTANCE.render.select.find('.select').removeClass('focus').siblings('.option_box').hide()
		})
		INSTANCE.render.select.on('click',
            (e) => {
                // 阻止事件冒泡
                e.stopPropagation();
            }
        ).on('click', '.select',
            function () {
                // 点击打开
                $(this).addClass('focus').siblings('.option_box').show();
            }
        ).on('click', '.btn_refresh',
            function () {
                // 刷新
                let $this = $(this)
                let $box = $this.parents('.simulate_select_box')
                let $parent = $box.find('.option_box')
                let $perpage = parseInt($parent.find('.btn_load_more').attr('data-per-page'))
                $this.addClass('refreshing');			
                $.get('/manage/sales/operation-activities/load-more?page=1&per-page=' + $perpage, function(data){
                    if (data.ret == 1) {
                        $parent.find('.option_list .item').remove();
                        $parent.find('.btn_load_more').attr('data-page', 2).before(data.msg.html);
                        //加上选择效果 失效的删掉
                        $box.find('.select .selected input').each(function(){		
                            var $item = $parent.find('.item[data-value="' + $(this).val() + '"]');				
                            $item.addClass('current');
                            if ($item.hasClass('disabled')) {
                                $(this).parent().remove();
                            }
                        });
                        if (!$box.find('.select .selected input').length) $box.find('.select .placeholder').show();
                        $parent.find('.btn_load_more').css('display', (data.msg.pageCount > 1 ? 'block' : 'none'));
                        if (data.msg.total > 0) {
                            $parent.find('.option_list').show();
                            $parent.find('.no_data').hide();
                        } else {
                            $parent.find('.option_list').hide();
                            $parent.find('.no_data').show();
                        }
                        $this.removeClass('refreshing');
                    }
                }, 'json');
            }
        ).on('click', '.option_list .item:not(.disabled)',
            function () {
                let $this = $(this)
                let $parent = $this.parent()
                let $box = $this.parents('.simulate_select_box')
                let $type = $box.attr('data-type')
                let $val = $this.attr('data-value')
                let $html = ''
                let $is_used = $this.parents('.input').find('input[name="IsUsed"]')
                $is_used.prop('checked', false).parents('.input_checkbox_box').removeClass('checked');
                if ($type == 'checkbox') {
                    $(this).addClass('current');
                    if (!$box.find('.select .selected[data-value="' + $val + '"]').length) {
                        $html = '<span class="selected" data-value="' + $val + '">' + $val + '<input type="hidden" name="' + $parent.attr('data-name')+'[]' + '" value="' + $val + '"><i></i></span>';
                        $box.find('.select').append($html).find('.placeholder').hide();
                    }
                } else if ($type == 'select') {
                    $(this).addClass('current').siblings().removeClass('current');
                    $box.find('.select .selected').remove();
                    $html = '<span class="selected" data-value="' + $val + '">' + $val + '<input type="hidden" name="' + $parent.attr('data-name') + '" value="' + $val + '"><i></i></span>';
                    $box.find('.select').append($html).find('.placeholder').hide();
                }
            }
        ).on('click', '.btn_load_more',
            function () {
                //加载更多
                let $this = $(this)
                let $box = $this.parents('.simulate_select_box')
                let $page = parseInt($this.attr('data-page'))
                let $perpage = parseInt($this.attr('data-per-page'))
                $.get('/manage/sales/operation-activities/load-more?page=' + $page + '&per-page=' + $perpage, function(data){
                    if (data.ret == 1) {
                        $this.attr('data-page', $page + 1);
                        $this.before(data.msg.html);
                        //加上选择效果 失效的删掉
                        $box.find('.select .selected input').each(function(){		
                            var $item = $box.find('.option_list .item[data-value="' + $(this).val() + '"]');				
                            $item.addClass('current');
                            if ($item.hasClass('disabled')) {
                                $(this).parent().remove();
                            }
                        });
                        if (!$box.find('.select .selected input').length) $box.find('.select .placeholder').show();
                        if (data.msg.page >= data.msg.pageCount) { //最后一页
                            global_obj.win_alert_auto_close(lang_obj.manage.sales.lastpage, 'await', 1000, '8%');
                            $this.fadeOut();
                        }
                    }
                }, 'json');
            }
        ).on('click', '.select .selected i',
            function () {
                //删除选项
                let $this = $(this)
                let $val = $this.parent().find('input').val()
                let $box = $this.parents('.simulate_select_box')
                $this.parent().remove();
                if (!$box.find('.select .selected').length) $box.find('.select .placeholder').show();			
                $box.find('.option_list .item[data-value="' + $val + '"]').removeClass('current');
                return false; //阻止冒泡
            }
        )
		$.post('/manage/sales/operation-activities/update-coupon-status', '',
            () => {
                INSTANCE.render.select.find('.btn_refresh').click()
            },
            'json'
        )

        frame_obj.submit_form_init(INSTANCE.render.form, '', '', '', (data) => {
            if (data.ret == 1) {
                global_obj.win_alert_auto_close(lang_obj.manage.email.send_success, '', 1000, '8%')
            } else {
                global_obj.win_alert_auto_close(data.msg, 'fail', 1000, '8%')
            }
        })
    }
}

// 加载动效
function loader (container, option) {
    option = option || {}
    const el = $(container)
    const loader = $(`
    <div class="loader">
        <div class="inner">
            <div class="loading"></div>
            <div class="text"></div>
        </div>
    </div>
    `)
    let txt = option.text || ''
    if (txt.length > 0) loader.find('.text').text(txt)
    return {
        el,
        loader,
        on: function(fn) {
            el.css('position', 'relative')
            loader.width(el.outerWidth())
            loader.height(el.outerHeight())
            loader.css({position: 'absolute', top: 0, left: 0, zIndex: 0})
            el.append(loader)
            if(typeof fn === 'function') fn() //回调
        },
        off: function(fn) {
            if (el.find('.loader').length>0) loader.remove()
            if(typeof fn === 'function') fn() //回调
        }
    }
}