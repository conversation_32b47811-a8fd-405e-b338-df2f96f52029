function loader(e,t){t=t||{};const a=$(e),n=$('\n    <div class="loader">\n        <div class="inner">\n            <div class="loading"></div>\n            <div class="text"></div>\n        </div>\n    </div>\n    ');let o=t.text||"";return o.length>0&&n.find(".text").text(o),{el:a,loader:n,on:function(e){a.css("position","relative"),n.width(a.outerWidth()),n.height(a.outerHeight()),n.css({position:"absolute",top:0,left:0,zIndex:0}),a.append(n),"function"==typeof e&&e()},off:function(e){a.find(".loader").length>0&&n.remove(),"function"==typeof e&&e()}}}var recall_obj={recall_init:()=>{frame_obj.switchery_checkbox(e=>{e.find("input[name=IsCart]").length?($(".fixed_recall_set .box_cart_set_overlay").removeClass("show"),$('.box_cart_set input[name="UsedCoupon"]').is(":checked")&&$('.coupon_switchery_box input[name="Coupon"]').attr("notnull","")):e.find("input[name=UsedCoupon]").length?$(".coupon_switchery_box").show().find('input[name="Coupon"]').attr("notnull",""):e.find("input[name=openCoupon]").length&&$(".coupon_select_box").removeClass("hide")},e=>{e.find("input[name=IsCart]").length?($(".fixed_recall_set .box_cart_set_overlay").addClass("show"),$('.coupon_switchery_box input[name="Coupon"]').removeAttr("notnull")):e.find("input[name=UsedCoupon]").length?$(".coupon_switchery_box").hide().find('input[name="Coupon"]').removeAttr("notnull"):e.find("input[name=openCoupon]").length&&$(".coupon_select_box").addClass("hide")}),frame_obj.fixed_right($("#btn_recall_set"),".fixed_recall_set",e=>{}),frame_obj.submit_form_init($("#recall_set_form"),"","","",e=>{1==e.ret?global_obj.win_alert_auto_close(lang_obj.global.save_success,"",1e3,"8%"):global_obj.win_alert_auto_close(e.msg,"fail",1e3,"8%"),$("#fixed_right .btn_cancel").click()})},recall_user_init:()=>{const e={render:{user:$(".recall_user_box")}};recall_obj.recall_init(),new dateselector("#recallViews",{submit:async({date:e,diff:t,compare:a})=>{recall_obj.get_user_data(1,e)}}),$(".dateselector").find(".btn_submit").click(),setTimeout(()=>{$(".dateselector").find("select[name=date]").val(0).trigger("change")},1e3),e.render.user.on("click",".pagination li a",function(e){e.stopPropagation();let t=$(this),a=parseInt(t.attr("data-page"))+1,n=$("#recallViews").attr("data-value"),o=global_obj.json_encode_data(n).date||[];return window.history.pushState(null,null,location.href.replace(/\?page=([0-9]*)/,"")+"?page="+a),recall_obj.get_user_data(a,o),!1})},recall_log_init:()=>{const e={render:{user:$(".recall_user_box"),search:$(".search_box")}};recall_obj.recall_init(),recall_obj.get_log_data(1),e.render.user.on("click",".pagination li a",function(e){e.stopPropagation();let t=$(this),a=parseInt(t.attr("data-page"))+1;return window.history.pushState(null,null,location.href.replace(/\?page=([0-9]*)/,"")+"?page="+a),recall_obj.get_log_data(a),!1}),e.render.search.on("submit","form",()=>!1).on("click",".search_btn",()=>(recall_obj.get_log_data(1),!1))},get_user_data:(e,t)=>{loader($(".loader_content")).on(),$getData={page:e,date:t},0==e&&$(".r_con_table").find("tbody").html(""),$.get("/manage/plugins/shopping-cart-recall/get-user-data",$getData,function(e){if(1==e.ret){let t=e.msg,a="",n="",o=$(".recall_user_box");if(t.list.length>0){for(i in o.find("table").show(),o.find(".bg_no_table_data").hide(),t.list){let e=t.list[i];a+=`\n                            <tr data-id="${e.id}">\n                                <td nowrap>${e.email}</td>\n                                <td nowrap>${lang_obj.manage.app.shopping_cart_recall.products.replace("{{count}}",e.count)}<a href="javascript:;" class="btn_user_detail">${lang_obj.manage.app.shopping_cart_recall.details}</a></td>\n                                <td nowrap class="operation tar flex_item last">\n                                    <a href="/manage/plugins/shopping-cart-recall/send?id=${e.id}" class="oper_icon icon_email button_tips">${lang_obj.manage.app.shopping_cart_recall.manual_recall}</a>\n                                    <a href="/manage/user/user/view?UserId=${e.id}" target="_blank" class="oper_icon icon_file button_tips">${lang_obj.manage.global.view}</a>\n                                </td>\n                            </tr>\n                            `}a&&o.find("tbody").html(a),t.list.length>0&&(n=t.turnPage),o.find(".user_view_page").html(n),o.find(".user_view_page .pagination a").attr("href","javascript:;"),recall_obj.user_detail()}else o.find("table").hide(),o.find(".bg_no_table_data").show()}setTimeout(()=>{$(".loader").remove()},1e3)},"json")},user_detail:()=>{frame_obj.fixed_right($(".btn_user_detail"),".fixed_cart_box",function(e){let t=$(".fixed_cart_box"),a=e.parents("tr").attr("data-id"),n=$("#recallViews").attr("data-value"),o=global_obj.json_encode_data(n).date||[],l=1;t.find(".box_table").find("tbody").html(""),t.find(".load_more").remove(),t.find(".no_data").remove();let i={id:a,date:o,page:l};recall_obj.append_html("view-cart",i)}),$(".fixed_cart_box").off().on("click",".load_more",function(){let e=$(this),t=e.attr("data-id"),a=$("#recallViews").attr("data-value"),n=global_obj.json_encode_data(a).date||[],o=e.attr("data-page");e.remove();let l={id:t,date:n,page:o};recall_obj.append_html("view-cart",l)})},get_log_data:e=>{loader($(".loader_content")).on();let t=$.trim($('input[name="keyword"]').val());$getData={page:e,keyword:t},0==e&&$(".r_con_table").find("tbody").html(""),$.get("/manage/plugins/shopping-cart-recall/get-log-data",$getData,function(e){if(1==e.ret){let t=e.msg,a="",n="",o=$(".recall_user_box");if(t.list.length>0){for(i in o.find("table").show(),o.find(".bg_no_table_data").hide(),t.list){let e=t.list[i];a+=`\n                            <tr data-email="${e.email}">\n                                <td nowrap>${e.email}</td>\n                                <td nowrap>${e.successedCount} / ${e.sentCount}</td>\n                                <td nowrap class="operation tar flex_item last">\n                                    <a href="javascript:;" class="oper_icon icon_file button_tips btn_log_detail">${lang_obj.manage.global.view}</a>\n                                </td>\n                            </tr>\n                            `}a&&o.find("tbody").html(a),t.list.length>0&&(n=t.turnPage),o.find(".user_view_page").html(n),o.find(".user_view_page .pagination a").attr("href","javascript:;"),recall_obj.log_detail()}else o.find("table").hide(),o.find(".bg_no_table_data").show()}setTimeout(()=>{$(".loader").remove()},1e3)},"json")},log_detail:()=>{frame_obj.fixed_right($(".btn_log_detail"),".fixed_log_box",function(e){let t=$(".fixed_log_box"),a=e.parents("tr").attr("data-email"),n=1;t.find(".box_table").find("tbody").html(""),t.find(".load_more").remove(),t.find(".no_data").remove();let o={email:a,page:n};recall_obj.append_html("view-log",o)}),$(".fixed_log_box").off().on("click",".load_more",function(){let e=$(this),t=e.attr("data-email"),a=e.attr("data-page");e.remove();let n={email:t,page:a};recall_obj.append_html("view-log",n)})},append_html:(e,t)=>{$.post("/manage/plugins/shopping-cart-recall/"+e,t,t=>{if(t.msg){let a=t.msg.Html,n=t.msg.ExtHtml;$Obj="view-cart"==e||"view-cart-now"==e?$(".fixed_cart_box"):$(".fixed_log_box"),a&&$Obj.find(".box_table tbody").append(a),n&&$Obj.find(".box_table").append(n)}},"json")},recall_send_init:()=>{const e={render:{form:$(".recall_send_form"),select:$(".simulate_select_box"),fixedCart:$(".fixed_cart_box"),proTable:$(".box_products_table")}};recall_obj.recall_init(),frame_obj.fixed_right($(".btn_add_product"),".fixed_cart_box",t=>{let a=e.render.fixedCart.find(".box_table thead"),n=$("input[name=id]").val(),o=1;e.render.fixedCart.find(".box_table tbody").html(""),e.render.fixedCart.find(".load_more").remove(),e.render.fixedCart.find(".no_data").remove(),a.find(".btn_checkbox").removeClass("current indeterminate").find("input").attr("checked",!1),a.find("tr").removeClass("current"),a.find(".global_menu_button .open").addClass("no_select");let l={id:n,page:o};recall_obj.append_html("view-cart-now",l)}),e.render.fixedCart.on("click",".load_more",function(){let t=$(this),a=$("input[name=id]").val(),n=t.attr("data-page");t.remove();let o={id:a,page:n};recall_obj.append_html("view-cart-now",o),e.render.fixedCart.find(".box_table .btn_checkbox").hasClass("current")&&e.render.fixedCart.find(".box_table .btn_checkbox").trigger("click")}).on("click",".btn_submit",function(){let t=e.render.fixedCart.find(".box_table"),a="";t.find(".input_checkbox_box.checked").each(function(){a+="<tr>"+$(this).parents("tr").html()+"</tr>"}),a?(e.render.proTable.removeClass("hide").find("tbody").html(a),e.render.proTable.find(".attr_checkbox").remove(),e.render.proTable.find("tr").removeClass("current"),global_obj.win_alert_auto_close(lang_obj.global.add_success,"",1e3,"8%"),$(".fixed_cart_box").find(".top_title .close").trigger("click")):global_obj.win_alert_auto_close(lang_obj.global.add_fail,"await",1e3,"8%")}),e.render.proTable.on("click",".icon_delete",function(){$(this).parents("tr").remove(),$("#button_float_tips").remove(),e.render.proTable.find("tbody tr").length||e.render.proTable.addClass("hide")}),e.render.fixedCart.delegate(".input_checkbox_box, .btn_checkbox","click",function(){let e=0,t=$(this).parents(".box_table"),a=t.find("tbody"),n=a.find(".input_checkbox_box.checked").length,o=a.find(".input_checkbox_box").length;if(0==o)return!1;$(this).hasClass("btn_checkbox")?$(this).hasClass("current")?(e=1,n+=1):n-=1:$(this).hasClass("checked")?n-=1:(e=1,n+=1),$(this).parents("thead").length?(1==e?a.find(".input_checkbox_box").each(function(e,t){$(t).addClass("checked").find("input").attr("checked",!0)}):a.find(".input_checkbox_box").each(function(e,t){$(t).removeClass("checked").find("input").attr("checked",!1)}),n=a.find(".input_checkbox_box.checked").length,1==e&&t.find("thead .btn_checkbox").removeClass("indeterminate")):n==o?t.find("thead .btn_checkbox").removeClass("indeterminate").addClass("current").find("input").attr("checked",!0):0==n?t.find("thead .btn_checkbox").removeClass("current indeterminate").find("input").attr("checked",!1):t.find("thead .btn_checkbox").removeClass("current").addClass("indeterminate").find("input").attr("checked",!1),n>0?($(".box_table thead tr").addClass("current"),$(".box_table thead .global_menu_button .open").removeClass("no_select"),$(".box_table thead .global_menu_button .open>span").text(n)):($(".box_table thead tr").removeClass("current"),$(".box_table thead .global_menu_button .open").addClass("no_select"))}),$("body").click(t=>{e.render.select.find(".select").removeClass("focus").siblings(".option_box").hide()}),e.render.select.on("click",e=>{e.stopPropagation()}).on("click",".select",function(){$(this).addClass("focus").siblings(".option_box").show()}).on("click",".btn_refresh",function(){let e=$(this),t=e.parents(".simulate_select_box"),a=t.find(".option_box"),n=parseInt(a.find(".btn_load_more").attr("data-per-page"));e.addClass("refreshing"),$.get("/manage/sales/operation-activities/load-more?page=1&per-page="+n,function(n){1==n.ret&&(a.find(".option_list .item").remove(),a.find(".btn_load_more").attr("data-page",2).before(n.msg.html),t.find(".select .selected input").each(function(){var e=a.find('.item[data-value="'+$(this).val()+'"]');e.addClass("current"),e.hasClass("disabled")&&$(this).parent().remove()}),t.find(".select .selected input").length||t.find(".select .placeholder").show(),a.find(".btn_load_more").css("display",n.msg.pageCount>1?"block":"none"),n.msg.total>0?(a.find(".option_list").show(),a.find(".no_data").hide()):(a.find(".option_list").hide(),a.find(".no_data").show()),e.removeClass("refreshing"))},"json")}).on("click",".option_list .item:not(.disabled)",function(){let e=$(this),t=e.parent(),a=e.parents(".simulate_select_box"),n=a.attr("data-type"),o=e.attr("data-value"),l="",i=e.parents(".input").find('input[name="IsUsed"]');i.prop("checked",!1).parents(".input_checkbox_box").removeClass("checked"),"checkbox"==n?($(this).addClass("current"),a.find('.select .selected[data-value="'+o+'"]').length||(l='<span class="selected" data-value="'+o+'">'+o+'<input type="hidden" name="'+t.attr("data-name")+'[]" value="'+o+'"><i></i></span>',a.find(".select").append(l).find(".placeholder").hide())):"select"==n&&($(this).addClass("current").siblings().removeClass("current"),a.find(".select .selected").remove(),l='<span class="selected" data-value="'+o+'">'+o+'<input type="hidden" name="'+t.attr("data-name")+'" value="'+o+'"><i></i></span>',a.find(".select").append(l).find(".placeholder").hide())}).on("click",".btn_load_more",function(){let e=$(this),t=e.parents(".simulate_select_box"),a=parseInt(e.attr("data-page")),n=parseInt(e.attr("data-per-page"));$.get("/manage/sales/operation-activities/load-more?page="+a+"&per-page="+n,function(n){1==n.ret&&(e.attr("data-page",a+1),e.before(n.msg.html),t.find(".select .selected input").each(function(){var e=t.find('.option_list .item[data-value="'+$(this).val()+'"]');e.addClass("current"),e.hasClass("disabled")&&$(this).parent().remove()}),t.find(".select .selected input").length||t.find(".select .placeholder").show(),n.msg.page>=n.msg.pageCount&&(global_obj.win_alert_auto_close(lang_obj.manage.sales.lastpage,"await",1e3,"8%"),e.fadeOut()))},"json")}).on("click",".select .selected i",function(){let e=$(this),t=e.parent().find("input").val(),a=e.parents(".simulate_select_box");return e.parent().remove(),a.find(".select .selected").length||a.find(".select .placeholder").show(),a.find('.option_list .item[data-value="'+t+'"]').removeClass("current"),!1}),$.post("/manage/sales/operation-activities/update-coupon-status","",()=>{e.render.select.find(".btn_refresh").click()},"json"),frame_obj.submit_form_init(e.render.form,"","","",e=>{1==e.ret?global_obj.win_alert_auto_close(lang_obj.manage.email.send_success,"",1e3,"8%"):global_obj.win_alert_auto_close(e.msg,"fail",1e3,"8%")})}};