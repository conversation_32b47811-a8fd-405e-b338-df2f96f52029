

var tongdun_obj={
	tongdun_init: function(){

		if ($('#riskChart').length) {
			let chart = Highcharts.chart('riskChart', {
				title: {
					floating: true,
					text: '<span style="font-size:18px; font-weight: bold;">' + $('#riskChart').data('count') + '</span><span style="font-size:14px;">' + lang_obj.manage.app.tongdun.strokes + '</span>'
				},
				legend: {
					enabled: true,
					align: 'center',
					verticalAlign: 'bottom',
					useHTML: true,
					labelFormatter() {
						const {name, color} = this
						return `
						<span style="display:inline-block; width:8px; height:8px; border-radius:50%; background-color:${color}"></span>
						<span style="font-size:12px; font-weight:normal; color:#404852;">${name}</span>
						`
					}
				},
				credits: {enabled:false},
				tooltip: {pointFormat: '{series.name}: <b>{point.percentage:.1f}%</b>'},
				plotOptions: {
					pie: {
						showInLegend: false,
						dataLabels: {enabled: false},
						size: 170,
					}
				},
				series: [
					{
						type: 'pie',
						innerSize: 100,
						name: lang_obj.manage.products.qty,
						data: [
							{
								name: lang_obj.manage.app.tongdun.risk.high,
								y: $('#tongdun .global_box.data .item.high').data('count'),
								color: '#e83c1a'
							},
							{
								name: lang_obj.manage.app.tongdun.risk.middle,
								y: $('#tongdun .global_box.data .item.middle').data('count'),
								color: '#fcc726'
							},
							{
								name: lang_obj.manage.app.tongdun.risk.low,
								y: $('#tongdun .global_box.data .item.low').data('count'),
								color: '#0baf4e'
							},
						]
					}
				]
			}, function(c) { // 图表初始化完毕后的会掉函数
				// 环形图圆心
				var centerY = c.series[0].center[1],
					titleHeight = parseInt(c.title.styles.fontSize);
				// 动态设置标题位置
				c.setTitle({
					y:centerY + titleHeight/2
				});
			})
	
			if (!$('#riskChart').data('count')) {
				chart.update({
					title: {
						text: '<span style="font-size:18px; font-weight: bold;">0</span><span style="font-size:14px;">' + lang_obj.manage.app.tongdun.strokes + '</span>',
						align: 'center',
						style: {color: '#7d8d9e', fontSize: '12px'},
					},
					series: [
						{
							data: [
								{
									name:'',
									y: 100,
									color: '#f4f4f4'
								}
							]
						}
					]
				})
			}
		}
		

		$('.menu_lsit .item').click(function(){
			let index = $(this).index()

			$(this).addClass('current').siblings('.item').removeClass('current')

			if (index == 0) {
				$('.overview_box').removeClass('hide')
				$('.account_box').addClass('hide')
			} else {
				$('.overview_box').addClass('hide')
				$('.account_box').removeClass('hide')
			}

			$(association).removeClass('.hide')
		});

		frame_obj.fixed_right($('#tongdun .tongdun_oauth'), '.oauth_edit_box');

		/* 广告图上传 */
		frame_obj.mouse_click($('#PicDetail .upload_btn, #PicDetail .pic_btn .edit'), 'img', function($this){
			// 点击上传图片
			frame_obj.photo_choice_init('PicDetail', '', 1);
		});

		// 提交资料
		frame_obj.submit_form_init($('#tongdun_form'), '', '', '', function(data){
			if (data.ret == 1) {
				window.location.reload();
			} else if (data.ret == -1) {
				global_obj.win_alert(data.msg);
			} else {
				global_obj.win_alert(lang_obj.payment.required_fields_tips);
			}
		});

		frame_obj.boxPaymentSystem('.btn_tongdun_recharge', '.box_tongdun_recharge');

	},

	
}