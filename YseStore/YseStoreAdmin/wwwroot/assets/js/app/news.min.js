var news_obj={news_category_edit_init:()=>{if(frame_obj.multi_lang_show_all("#edit_form"),frame_obj.multi_lang_show_item("#edit_form"),frame_obj.fixed_right($("#edit_keyword"),".fixed_edit_keyword",function(e){var t=$("input[name=CateId]").val();frame_obj.seo_edit_keyword({do_action:"/manage/action/seo-keyword-select",Type:"news_category",field:"CateId",Id:t})}),frame_obj.seo_keyword_form_submit(),"object"==typeof CKEDITOR)for(i in shop_config.language){var e=shop_config.language[i],t="Description_"+e;CKEDITOR.instances[t].on("change",function(){var e=$(this)[0].getData();e&&(e=e.replace(/(<[^>]+>)|(&nbsp;)/g,"").replace(/(^\s*)|(\s*$)/g,"").replace(/(\s+)|([\r\n])|([\r])|([\n])/g," ").slice(0,254)),$(this)[0].name&&1==$("#"+t).data("change")&&$('[name="SeoDescription_'+$(this)[0].name.replace("Description_","")+'"]').val(global_obj.htmlspecialchars_decode(e))})}if("object"==typeof tinymce)for(i in shop_config.language){e=shop_config.language[i],t="Description_"+e;tinymce.editors[t].on("change",function(){var n=$(this)[0].getContent();n&&(n=n.replace(/(<[^>]+>)|(&nbsp;)/g,"").replace(/(^\s*)|(\s*$)/g,"").replace(/(\s+)|([\r\n])|([\r])|([\n])/g," ").slice(0,254)),1==$("#"+t).data("change")&&$('[name="SeoDescription_'+e+'"]').val(global_obj.htmlspecialchars_decode(n))})}var n=new ClipboardJS(".btn_copy");n.on("success",function(e){alert(lang_obj.global.copy_complete)}),$("input[name=PageUrl]").on("keyup",function(e){var t=window.event?e.keyCode:e.which,n=$.trim($(this).val());8==t&&""==n&&$(this).val($(".left_container .global_container:eq(0) .rows:eq(0) .multi_lang .lang_txt:eq(0) input").val().replace(/\s+/g,"-"))}),$("textarea[name=PageUrl]").on("keyup",function(){let e=$(".prefix_textarea .prefix").text(),t=$(this).val(),n=e+t;$(this).parents(".custom_row").find(".btn_copy").attr("data-clipboard-text",n)}),$(".box_basic_more").hover(function(){var e=42;$(this).hasClass("box_seo_basic_more")&&(e=20),$(this).find(".drop_down").show().stop(!0).animate({top:e,opacity:1},250)},function(){var e=32;$(this).hasClass("box_seo_basic_more")&&(e=10),$(this).find(".drop_down").stop(!0).animate({top:e,opacity:0},100,function(){$(this).hide()})}),frame_obj.submit_object_init($("#edit_form"),"","","",function(e){global_obj.win_alert_auto_close(lang_obj.global.save_success,"",500,"8%"),setTimeout(function(){e.msg.jump?window.location=e.msg.jump:window.location.reload()},500)})},news_category_init:()=>{function e(t,n){if("default"==n)var a=$("input[name=CateIdAry]").length,o=$("input[name=CateIdAry]").eq(t).val();else a=1,o=$(".pro_count.load").map(function(){return $(this).attr("data-cateid")}).get().join(",");o&&$.post("/manage/plugins/news/news-count",{id:o},function(o){if(1==o.ret)for(i in o.msg)"loading"==o.msg[i]?$('.pro_count[data-cateid="'+i+'"]').parent().find(".icon_edit").addClass("disabled").off("click").click(function(){return!1}):$('.pro_count[data-cateid="'+i+'"]').html(o.msg[i]).removeClass("load").parent().find(".icon_edit").removeClass("disabled").off("click").click(function(e){e.ctrlKey||$(".fixed_loading").fadeIn()});t++,t<a&&e(t,n)},"json")}function t(n){setTimeout(function(){$(".pro_count.load").length&&(e(0,"loading"),n*=2,t(n))},1e3*n)}frame_obj.del_init($("#category .r_con_table")),frame_obj.select_all($("input[name=select_all]"),$("input[name=select]"),$(".table_menu_button .del")),frame_obj.del_bat($(".table_menu_button .del"),$("input[name=select]"),"/manage/plugins/news/category-delete-batch"),frame_obj.dragsort($("#news_category .r_con_table tbody"),"/manage/plugins/news/order"),e(0,"default"),t(2)},news_edit_init:()=>{if(frame_obj.multi_lang_show_all("#edit_form"),frame_obj.multi_lang_show_item("#edit_form"),frame_obj.main_picture_upload(),frame_obj.upload_picture_event(),$("input[name=EditTime]").daterangepicker({showDropdowns:!0,singleDatePicker:!0}),frame_obj.fixed_right($("#edit_keyword"),".fixed_edit_keyword",function(e){var t=$("input[name=NewsId]").val();frame_obj.seo_edit_keyword({do_action:"/manage/action/seo-keyword-select",Type:"news",field:"NewsId",Id:t})}),frame_obj.seo_keyword_form_submit(),"object"==typeof CKEDITOR)for(i in shop_config.language){var e=shop_config.language[i],t="Description_"+e;CKEDITOR.instances[t].on("change",function(){var e=$(this)[0].getData();e&&(e=e.replace(/(<[^>]+>)|(&nbsp;)/g,"").replace(/(^\s*)|(\s*$)/g,"").replace(/(\s+)|([\r\n])|([\r])|([\n])/g," ").slice(0,254)),$(this)[0].name&&1==$("#"+t).data("change")&&$('[name="SeoDescription_'+$(this)[0].name.replace("Description_","")+'"]').val(global_obj.htmlspecialchars_decode(e))})}var n=new ClipboardJS(".btn_copy");n.on("success",function(e){alert(lang_obj.global.copy_complete)}),$(".top_tool_menu .btn_menu_view").click(function(){var e=$(this).parents(".top_tool_menu").data("url");window.open(e)}),$("input[name=PageUrl]").on("keyup",function(e){var t=window.event?e.keyCode:e.which,n=$.trim($(this).val());8==t&&""==n&&$(this).val($(".left_container .global_container:eq(0) .rows:eq(0) .multi_lang .lang_txt:eq(0) input").val().replace(/\s+/g,"-"))}),$("textarea[name=PageUrl]").on("keyup",function(){let e=$(".prefix_textarea .prefix").text(),t=$(this).val(),n=e+t;$(this).parents(".custom_row").find(".btn_copy").attr("data-clipboard-text",n)}),$(".box_basic_more").hover(function(){var e=42;$(this).hasClass("box_seo_basic_more")&&(e=20),$(this).find(".drop_down").show().stop(!0).animate({top:e,opacity:1},250)},function(){var e=32;$(this).hasClass("box_seo_basic_more")&&(e=10),$(this).find(".drop_down").stop(!0).animate({top:e,opacity:0},100,function(){$(this).hide()})}),frame_obj.submit_object_init($("#edit_form"),"","","",function(e){global_obj.win_alert_auto_close(lang_obj.global.save_success,"",500,"8%"),setTimeout(function(){e.msg.jump?window.location=e.msg.jump:window.location.reload()},500)}),frame_obj.switchery_checkbox(function(e){e.find("input[name=UsedMobile]").length&&$(".mobile_description").fadeIn()},function(e){e.find("input[name=UsedMobile]").length&&$(".mobile_description").fadeOut()})},news_init:()=>{frame_obj.del_init($("#news .r_con_table")),frame_obj.select_all($("input[name=select_all]"),$("input[name=select]"),$(".table_menu_button .del")),frame_obj.del_bat($(".table_menu_button .del"),$("input[name=select]"),"/manage/plugins/news/delete-batch"),frame_obj.filterRight({onInit:function(){$(".fixed_search_filter input[name=editTime]").daterangepicker({showDropdowns:!0})},onSubmit:function(e){let t=e.find("input[name=cateId]").val(),n=e.find("input[name=editTime]").val();$(".search_box input[name=cateId]").val(t),$(".search_box input[name=editTime]").val(n)}})}};