
var amazon_obj={
	collection_init: function () {
		// 翻译器加载
		frame_obj.translation_init();

		frame_obj.select_all($('input[name=select_all]'), $('input[name=select]'), $('.table_menu_button .del, .table_menu_button .publish')); //批量操作
		frame_obj.del_bat($('.table_menu_button .del'), $('input[name=select]'), '/manage/plugins/amazon/remove'); // 批量删除

		// 单个发布
		frame_obj.fixed_right($('.table_menu_button .publish, .r_con_table .operation .publish'), '.publish_collect_products', '', function($this){
			var id = ''
			if ($this.hasClass('batch')) {
				$('#amazon_collection input[name=select]').each(function(index, element) {
					id += $(element).get(0).checked?$(element).val() + ',' : '';
				});
				if (!id) {
					global_obj.win_alert(lang_obj.global.dat_select);
					return false;
				} else {
					id = id.substring(0, id.lastIndexOf(','));
				}
			} else {
				id = $this.data('pid')
			}
			$('#publish_products_form input[name="id"]').val(id)
		})

		frame_obj.submit_form_init($('#publish_products_form'), '', '', 0, function(data){
			if (data.ret == 1) {
				$('.publish_collect_products .close').click()
				let ids = data.msg.ids
				let len = data.msg.len
				let idAry = data.msg.idAry
				
				for (idKey in idAry) {
					$('#amazon_collection input[name=select][value=' + idAry[idKey] + ']').parents('tr').find('.status').addClass('ing').text(lang_obj.manage.products.sync.publish_used)
				}

				$('.fixed_translation .content_box').attr('data-related-id', ids).attr('data-len', len)

				global_obj.win_alert({
					"title": lang_obj.manage.app.product_collection.publish_success.replace('%num%', len),
					"confirmBtn": lang_obj.manage.app.product_collection.translate_products,
					"cancelBtn": lang_obj.manage.app.product_collection.continue_collection
				}, function(){
					$('.btn_translation').click()
				}, 'confirm')
			} else {
				global_obj.win_alert(data.msg)
			}
		})

		// 采集产品按钮
		frame_obj.fixed_right($('.collection_btn'), '.box_collection_product');

		// 类型切换
		$('#crawl_form').delegate('.box_type_menu .item', 'click', function(){
			var $this=$(this);

			if ($this.hasClass("disabled")) return false;
			$this.parent().find('.item').removeClass('checked').find('input').prop('checked', false);
			$this.addClass('checked').find('input').prop('checked', 'checked');
		});

		frame_obj.submit_form_init($('#crawl_form'), '', '', 0, function(data){
			if (data.ret == 1) {
				global_obj.win_alert({
					title: data.msg.title || lang_obj.global.ok,
					subtitle: data.msg.content || '',
					confirmBtn: lang_obj.global.ok2,
				}, function() {
					window.location.reload()
				})
			} else {
				global_obj.win_alert_auto_close(data.msg, 'fail', 1000, '8%')
			}
		});

		// 定时查询状态
		if ($('#amazon_collection .table_list').data('process')) {
			var t = setInterval(() => {
				let crawlStatusPromise = new Promise((resolve, reject) => {
					$.ajax({
						url: `/manage/plugins/amazon/crawl-status`,
						type: 'POST',
						async: true,
						dataType: 'json',
						data: {},
						success: res => { resolve(res) },
						error: err => { reject(err) }
					})
				})
	
				crawlStatusPromise.then(res => {
					if (res.data == 0) {
						clearInterval(t)
						window.location.reload()
					}
				})
			}, 10000)
		}
		
		// 采集评论
		frame_obj.fixed_right($('.btn_collect'), '.fixed_collect_products', function ($this) {
		})
	},
}