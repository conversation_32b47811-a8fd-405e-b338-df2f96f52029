var mailchimp_obj = {
	mailchimp_oauth: function () {
		// 授权成功后刷新页面
		if($('#mailchimp').find('.mailchimp_oauth_btn').length > 0) {
			if (document.hidden !== undefined) {
				document.addEventListener('visibilitychange', () => {
					if(document.visibilityState == 'visible') {
						$.post('/manage/plugins/mailchimp/check-authorize-status', '', function(result){
							if(result.ret == 1) {
								window.location.reload();
							}
						},'json')
					}
				})
			}
		}

		//切换账号
		$('#mailchimp').on('click', '.change_mailchimp_account',function(){
			global_obj.win_alert({
				"title": lang_obj.global.del_confirm,
				"confirmBtn": lang_obj.global.confirm,
				"confirmBtnClass": 'btn_warn'
			}, function () {
				$.ajax({
					url: '/manage/plugins/mailchimp/release',
					type: 'POST',
					async: true,
					dataType: 'json',
					data: {},
					success: function (res) {
						if (res.msg == 'success') {
							//操作成功刷新当前页
							location.reload()
						} else {
							//业务错误
						}
					},
					error: function () {
						//后端异常返回
					}
				})

			}, 'confirm');
		})

		// 绑定 添加 按钮事件
		frame_obj.fixed_right($('#mailchimp .id_add'), '.mailchimp_pannel.audience', function ($this) {
			let type = $this.data('type')
			let position = $this.data('position')
			let name = $this.data('name')
			let _id = $this.data('id')

			if(type){
				$('#audienceForm input[name=position]').val(position)
				$('#audienceForm input[name=MembersType]').val(type)
				$('#audienceForm input[name=MembersList]').val(_id)
				$('#audienceForm .designated_customer .imitation_select').val(lang_obj.manage.app.mailchimp.members_type[type])
				$('#audienceForm .designated_customer .select_ul .item[data-value=' + type + ']').addClass('selected').siblings().removeClass('selected');
				$('#audienceForm .members_list .imitation_select').val(name)
				$('#audienceForm .members_list .select_ul .item[data-value=' + _id + ']').addClass('selected').siblings().removeClass('selected');
			}else{
				$('#audienceForm input[name=position]').val(-1)
				$('#audienceForm input[name=MembersType]').val('')
				$('#audienceForm input[name=MembersList]').val('')
				$('#audienceForm .imitation_select').val('')
				$('#audienceForm .select_ul .item').removeClass('selected');
			}
		})

		// 开始同步客户
		frame_obj.submit_form_init($('#audienceForm'), '', '', 0, function(data){
			if (data.ret == 1) {
				global_obj.win_alert_auto_close(lang_obj.global.save_success, '', 2000, '', false);
				window.location.reload()
			} else {
				global_obj.win_alert_auto_close(data.msg, 'fail', 3000, '8%')
			}
		})

		// 右侧弹窗的 指定客户 和 Mailchimp List 下拉
		frame_obj.global_select_box()

		// 删除 指定客户绑定的列表
		frame_obj.del_init($('#mailchimp .r_con_table'));

		// 点击 完成 按钮
		$('.complete_setup_btn').click(function() {
			window.location.href = '/manage/plugins/mailchimp/index';
		});

	},
	mailchimp_init: function () {
		frame_obj.del_init($('#mailchimp .r_con_table')); // 删除提示
		frame_obj.select_all($('input[name=select_all]'), $('input[name=select]'), $('.table_menu_button').find('.del')); // 批量操作
		frame_obj.del_bat($('.table_menu_button .del'), $('input[name=select]'), '/manage/plugins/mailchimp/delete'); // 批量删除
	},
	mailchimp_edit_init: function () {
		$('#campaignForm select[name="lid"]').change(function () {
			var selOpt = $(this).find('option:selected')
			$('#campaignForm input[name="list_id"]').val(selOpt.data('list-id'))
		})

		//表单提交
		$('#btnFormSubmit').click(function ($this) {
			var errStack = []

			// var form = document.getElementById('campaignForm')
			var form = $('#campaignForm')

			var selOpt = form.find('select[name="lid"] option:selected')
			form.find('input[name="list_id"]').val(selOpt.data('list-id'))

			form.serializeArray().forEach(function (row) {
				if (row.name.indexOf('Content') >= 0) {
					var cnt = CKEDITOR.instances[row.name].getData()
					if (cnt.length == 0) {
						errStack.push(row.name)
					}
				} else {
					if (row.value.length == 0) {
						errStack.push(row.name)
					}
				}
			})

			if (errStack.length == 0) {
				form.submit()
			} else {
				var label = errStack.shift()
				if (label.indexOf('Subject') >= 0) {
					label = 'Subject'
				} else if (label.indexOf('lid') >= 0) {
					label = 'list_id'
				} else if (label.indexOf('Content') >= 0) {
					label = 'Content'
				}

				global_obj.win_alert_auto_close(form.find('[input-label="'+label+'"]').data('tips'), 'fail', 1000, '8%');
			}
			return false
		})
	}
}
