

var add_to_cart_obj = { //eslint-disable-line no-unused-vars
	add_to_cart_init: function(){
		frame_obj.switchery_checkbox(function(obj){
			if (obj.find('input[name=Pc]').length || obj.find('input[name=Mobile]').length) {
				obj.parents('.label').next('.input').show()
			}
		}, function(obj){
			if (obj.find('input[name=Pc]').length || obj.find('input[name=Mobile]').length) {
				obj.parents('.label').next('.input').hide()
			}
		})
		frame_obj.submit_form_init($('#add_to_cart_edit_form')); //eslint-disable-line no-undef
	},
}