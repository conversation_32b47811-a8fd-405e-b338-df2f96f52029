

var shopify_obj={
	global_init: function(){
		frame_obj.del_init($('#products_sync .r_con_table'));
		frame_obj.select_all($('input[name=select_all]'), $('input[name=select]'));
	},
	progress: function(data){
		if (data) {
			if (data.ret == 3) {  // 进度完成
				$('#box_circle_container input[name=Start]').val(1);
				$('#box_circle_container .tips').hide();
				$('#btn_progress_cancel').val(lang_obj.global.confirm).show();
			} else if (data.ret == 2) {  // 下一页
				$('#box_circle_container .status').text(lang_obj.manage.global.update_status[0]);
				$('#btn_progress_continue').click();
			} else if (data.ret ==1 ) {  // 反复请求当前的进度状态
				// $('#box_circle_container input[name=Start]').val()>1 && $('.box_progress .progress .num').css('width', '90%').find('span').text('90%');
				if ($('#box_circle_container input[name=Start]').val() > 1) {
					frame_obj.circle_progress_bar({
						percent: 90,
						processingText: '',
						completedText: ''
					});
				}
			}
		}
	},
	store_init: function(){
		// 添加店铺
		frame_obj.fixed_right($('a.add_store'), '.store_add');
		// 添加店铺提交
		frame_obj.submit_form_init($('#store_add'), '', '', '',function(data){
			if(data.ret==1){
				window.location.href=window.location.href;
			}else{
				global_obj.win_alert(data.msg);
			}
		});
		// 删除店铺
		frame_obj.del_init($('#app_shopify .store_item'));
		// 修改店铺
		frame_obj.fixed_right($('#app_shopify .store_item .edit'), '.box_authorization_edit');
		var box_authorization_edit = $('.box_authorization_edit');
		$('.store_item').on('click', '.edit', function() {
			let AId		= $(this).parents('.store_item').data('aid');
			let Name	= $(this).parents('.info').find('.name').text();
			let account	= $(this).parents('.store_item').data('account');
			$('input[name=Name]', box_authorization_edit).val(Name);
			$('input[name=client_username]', box_authorization_edit).val(account.UserName).addClass('bg_gray');
			$('input[name=client_password]', box_authorization_edit).val(account.Password).addClass('bg_gray');
			$('input[name=client_shop]', box_authorization_edit).val(account.Shop).addClass('bg_gray');
			$('input[name=AId]', box_authorization_edit).val(AId);
		});
		frame_obj.submit_form_init($('#authorization_mod'), '', '', '',function(data){
			if(data.ret==1){
				window.location.href=window.location.href;
			}else{
				global_obj.win_alert(data.msg);
				global_obj.div_mask(1);
			}
		});
	},
	sync_init: function(){
		shopify_obj.global_init();
		frame_obj.del_bat($('.table_menu_button .del'), $('input[name=select]'), '/manage/plugins/shopify/delete-batch-products');
		/***********************************检查产品部分(start)************************************/
		var $id_ary = new Array,
			$Page = $('.r_con_table').data('page');
		$('.r_con_table tr').each(function(index, element) {
			var $ID = parseInt($(this).data('id'));
			if ($ID > 0) {
				$id_ary[index - 1] = $ID;
			}
		});
		if ($id_ary.length) {
			$.post('/manage/plugins/shopify/check-products', {'Page':$Page, 'ProIdAry':$id_ary}, function(data){
				if (data.ret == 1 && data.msg) {
					for (k in data.msg) {
						if ($('.r_con_table tr[data-id=' + data.msg[k] + ']').length) {
							$('.r_con_table tr[data-id=' + data.msg[k] + '] td.status').html(lang_obj.manage.products.sync.publish_none);
						}
					}
				}
			}, 'json');
		}
		/***********************************检查产品部分(end)************************************/
		/***********************************开始同步产品(start)************************************/
		frame_obj.box_progress(shopify_obj.progress);
		$('a.shopify_product_list_sync').click(function() {
			var extHtml = '<span class="input_checkbox_box"><span class="input_checkbox"><input type="checkbox" name="SameCoverage" value="1"></span>' + lang_obj.manage.products.sync.same_cover + '</span>';
			global_obj.win_alert({title:lang_obj.manage.products.sync.sync_shopify_confirm, extHtml:extHtml}, function() {
				var $Start = parseInt($('#box_circle_container input[name=Start]').val());
				$.post('/manage/plugins/shopify/products-sync', {'Start':$Start,'SameCoverage':($('input[name=SameCoverage]:checked').length ? 1 : 0)}, function(data) {
					if (data.ret == 1) {
						if ($Start == 1) {
							global_obj.div_mask();
							$('.pop_form.sync_progress').css({'top':110, 'opacity':0}).show().animate({'top':'50%', 'opacity':1}, 250);
						}
						$('#box_circle_container input[name=TaskId]').val(data.msg.TaskId);
						$('#btn_progress_keep').click();
					} else if (data.ret == -1) {
						//global_obj.win_alert(lang_obj.manage.products.sync.not_repeat_task);
						//弹出任务窗口
						$('#box_circle_container input[name=TaskId]').val(data.msg.TaskId);
						global_obj.div_mask();
						$('.pop_form.sync_progress').css({'top':110, 'opacity':0}).show().animate({'top':'50%', 'opacity':1}, 250);
						var $Data = new Object;
						$('#box_circle_container input[type=hidden]').each(function(){
							$Data[$(this).attr('name')]=$(this).val();
						});
						frame_obj.box_progress_ajax($Data, shopify_obj.progress);
					} else {
						global_obj.win_alert(data.msg);
					}
				}, 'json');
			}, 'confirm');
			return false;
		});
		$('#btn_progress_continue').click(function(){ //继续同步
			$('a.shopify_product_list_sync').click();
		});
		$('#btn_progress_cancel').click(function(){ //关闭
			frame_obj.pop_form($('.pop_form.sync_progress'), 1, 1);
			$('.box_progress .status').text(lang_obj.manage.global.update_status[0]);
			$('.box_progress .progress .num').css('width', '10%').find('span').text('10%');
			$('.box_progress .tips').show();
			$('.box_progress .btn_global').hide();
			window.top.location.reload();
			return false;
		});
		//单独发布
		$('.btn_publish').click(function() {
			let $this = $(this)
			let $obj = $this.parents('tr')
			let $id = $obj.data('id')
			$.post('/manage/plugins/shopify/copy-shopify-to-products', { group_id: $id, Type: 'Publish' }, () => {
				$this.remove()
				$obj.find('td.status').html(`<span class="status ing">${lang_obj.manage.products.sync.publish_used}</span>`)
				global_obj.win_alert_auto_close(lang_obj.manage.products.sync.publish_once, '', 2000, '8%')
			});
		});
		/***********************************开始同步产品(end)************************************/
	},
	sync_user_init: function(){
		shopify_obj.global_init();
		frame_obj.del_bat($('.table_menu_button .del'), $('input[name=select]'), '/manage/plugins/shopify/delete-batch-user');
		/***********************************开始同步会员(start)************************************/
		frame_obj.box_progress(shopify_obj.progress);
		$('a.shopify_user_list_sync').click(function() {
			var extHtml = '<span class="input_checkbox_box"><span class="input_checkbox"><input type="checkbox" name="SameCoverage" value="1"></span>' + lang_obj.manage.products.sync.same_user + '</span>';
			global_obj.win_alert({title:lang_obj.manage.products.sync.sync_shopify_user_confirm, extHtml:extHtml}, function() {
				var $Start = parseInt($('#box_circle_container input[name=Start]').val());
				$.post('/manage/plugins/shopify/user-sync', {'Start':$Start,'SameCoverage':($('input[name=SameCoverage]:checked').length ? 1 : 0)}, function(data) {
					if (data.ret == 1) {
						if ($Start == 1) {
							global_obj.div_mask();
							$('.pop_form.sync_progress').css({'top':110, 'opacity':0}).show().animate({'top':'50%', 'opacity':1}, 250);
						}
						$('#box_circle_container input[name=TaskId]').val(data.msg.TaskId);
						$('#btn_progress_keep').click();
					} else if (data.ret == -1) {
						//global_obj.win_alert(lang_obj.manage.products.sync.not_repeat_task);
						//弹出任务窗口
						$('#box_circle_container input[name=TaskId]').val(data.msg.TaskId);
						global_obj.div_mask();
						$('.pop_form.sync_progress').css({'top':110, 'opacity':0}).show().animate({'top':'50%', 'opacity':1}, 250);
						var $Data = new Object;
						$('#box_circle_container input[type=hidden]').each(function(){
							$Data[$(this).attr('name')]=$(this).val();
						});
						frame_obj.box_progress_ajax($Data, shopify_obj.progress);
					} else {
						global_obj.win_alert(data.msg);
					}
				}, 'json');
			}, 'confirm');
			return false;
		});
		$('#btn_progress_continue').click(function(){ //继续同步
			$('a.shopify_user_list_sync').click();
		});
		$('#btn_progress_cancel').click(function(){ //关闭
			frame_obj.pop_form($('.pop_form.sync_progress'), 1, 1);
			$('.box_progress .status').text(lang_obj.manage.global.update_status[0]);
			$('.box_progress .progress .num').css('width', '10%').find('span').text('10%');
			$('.box_progress .tips').show();
			$('.box_progress .btn_global').hide();
			window.top.location.reload();
			return false;
		});
		//单独发布
		$('.btn_publish').click(function() {
			let $this = $(this)
			let $obj = $this.parents('tr')
			let $id = $obj.data('id')
			$.post('/manage/plugins/shopify/copy-shopify-to-user', { group_id: $id, Type: 'Publish' }, () => {
				$this.remove()
				$obj.find('td.status').html(`<span class="status ing">${lang_obj.manage.products.sync.publish_used}</span>`)
				global_obj.win_alert_auto_close(lang_obj.manage.products.sync.publish_once, '', 2000, '8%')
			});
		});
		/***********************************开始同步会员(end)************************************/
	},
	scv_init: function(){
		shopify_obj.global_init();
		frame_obj.del_bat($('.table_menu_button .del'), $('input[name=select]'), '/manage/plugins/shopify/delete-batch-products');
		// 上传CSV
		frame_obj.fixed_right($('a.shopifycsvupload_product_list_sync'), '.csvupload_products_box', function() {
			$("#upload_edit_form .btn_submit").attr("disabled", "disabled");
		});
		var $Form=$('#upload_edit_form');
		$Form.fileupload({
			url: '/manage/action/file-upload-plugin?size=file',
			acceptFileTypes: /^.*.(csv|vnd\.ms-excel)$/i, //csv xlsx xls
			callback: function(filepath, count){
				$('#excel_path').val(filepath);
				if (filepath) $("#upload_edit_form .btn_submit").removeAttr("disabled");
			}
		});
		$Form.fileupload(
			'option',
			'redirect',
			window.location.href.replace(/\/[^\/]*$/, '/cors/result.html?%s')
		);

		frame_obj.submit_form_init($Form, '', function(){
			$Form.find('.form_container').hide();
			$Form.find('.progress_container_right').show();
			return true;
		}, '', function(data){
			if(data.ret==2){
				//继续产品资料上传
				var $Html='', $Number=0;
				if($Form . find('.progress_container_right tbody tr').length){
					$Number=parseInt($Form . find('.progress_container_right tbody tr:eq(0) td:eq(0)').text());
				}
				if(data.msg.Data){
					for(k in data.msg.Data){
						$Html = '<tr class="'+(data.msg.Data[k].Status==0?'error':'success')+'" data-id="'+data.msg.Data[k].ProId+'" data-type="'+data.msg.Data[k].Type+'" data-pic-status="0">\
									<td nowrap="nowrap"><i class="icon"></i></td>\
									<td nowrap="nowrap"><div class="img"></div><div class="name" title="'+data.msg.Data[k].Name+'">'+data.msg.Data[k].Name+'</div></td>\
								</tr>';
						$Form.find('.progress_container_right tbody').prepend($Html);
					}
					$Form.find('input[name=Number]').val(data.msg.Num);
					$Form.find('input[name=Current]').val(data.msg.Cur);
					$Form.find('input[name=TaskId]').val(data.msg.TaskId);
					$Form.find('.btn_submit').removeAttr('disabled').click();
				}
			}else if(data.ret==1){
				//产品资料上传完成
				if ($Form.find('.progress_container_right tbody').find('tr').length > 0) {
					$('.btn_picture').click(); //开始图片上传
				} else {
					// 没有产品数据
					$Form.find('.global_tips').removeClass('load').addClass('success').find('span').text(lang_obj.manage.products.sync.sync_success) //全部导入完成
					$('#fixed_right_div_mask, .csvupload_products_box .close').click(function() {
						//硬性绑定页面刷新事件
						window.location.reload();
						return false;
					});
				}
			}else{
				global_obj.win_alert_auto_close(data.msg, 'fail', 2000, '8%');
				$Form.find('.form_container').show();
				$Form.find('.progress_container_right').hide();
			}
		});
		$('.btn_picture').on('click', function(){
			var $Obj = $Form.find('.progress_container_right tbody'),
				checkTime = 5 * 1000, //检查时间上限
				checkFun = '', //储存检查程序代码
				ProIdAry = new Array(); // 产品ID数组
			$Obj.find('tr.success[data-id!=0][data-pic-status=0]').each(function(){
				if(ProIdAry.length > 100){ return false;}
				var $ProId = parseInt($(this).attr('data-id'));
				$ProId && ProIdAry.push($ProId);
			});
			if(ProIdAry.length){
				$.post('/manage/plugins/shopify/upload-picture', {ProIdAry:ProIdAry}, function(data){
					clearTimeout(checkFun);
					if(data.ret==1){ // 导入成功
						var $CompleteProId = data.msg.CompleteProId;
						var $CompletePicPath = data.msg.CompletePicPath;
						var $CompleteStatus = data.msg.CompleteStatus;
						for(var key in $CompleteProId){
							let tr = $Obj.find('tr[data-id='+$CompleteProId[key]+']');
							if ($CompleteStatus[key] == -1) {
								// 获取图片失败
								tr.attr('data-pic-status', -1).find('td>div.img').html('<img src="' + $CompletePicPath[key] + '" />');
							} else {
								// 获取完毕
								tr.attr('data-pic-status', 1).find('td>i.icon').addClass('on');
								tr.attr('data-pic-status', 1).find('td>div.img').html('<img src="' + $CompletePicPath[key] + '" />');
								// 把成功的放到最前面
								let clone = tr.clone();
								tr.remove();
								$Form.find('.progress_container_right tbody').prepend(clone);
							}
						}
					}
					if($Obj.find('tr.success[data-id!=0][data-pic-status=0]').length == 0){
						if ($Obj.find('tr.success[data-id!=0][data-pic-status=-1]').length > 0) {
							// 包含有产品同步失败
							let $successCount = $Obj.find('tr.success[data-id!=0][data-pic-status=1]').length,
								$failCount = $Obj.find('tr.success[data-id!=0][data-pic-status=-1]').length;
							$Form.find('.global_tips').removeClass('load').find('span').text(lang_obj.manage.products.sync.sync_part_tips.replace("{{Number1}}", $successCount).replace("{{Number2}}", $failCount));
						} else {
							// 全部同步完成
							$Form.find('.global_tips').removeClass('load').addClass('success').find('span').text(lang_obj.manage.products.sync.sync_success) //全部导入完成
						}
						$('#fixed_right_div_mask, .csvupload_products_box .close').click(function() {
							//硬性绑定页面刷新事件
							window.location.reload();
							return false;
						});
					}
					checkFun=setTimeout(function(){
						if($Obj.find('tr.success[data-id!=0][data-pic-status=0]').length>0){ //继续导入其他产品图片
							$('.btn_picture').click();
						}
					}, checkTime);
				}, 'json');
				//超过5秒钟后，没有任何返回结果，系统自动再次执行
				
			}
		});
		//单独发布
		$('.btn_publish').click(function() {
			let $this = $(this)
			let $obj = $this.parents('tr')
			let $id = $obj.data('id')
			$.post('/manage/plugins/shopify/copy-shopify-to-products', { group_id: $id, Type: 'Publish' }, () => {
				$this.remove()
				$obj.find('td.status').html(`<span class="status ing">${lang_obj.manage.products.sync.publish_used}</span>`)
				global_obj.win_alert_auto_close(lang_obj.manage.products.sync.publish_once, '', 2000, '8%')
			});
		});
	}
}