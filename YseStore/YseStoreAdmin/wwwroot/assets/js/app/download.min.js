var download_obj={download_init:function(){$(".inside_menu").insideMenu(),frame_obj.del_init($("#download .r_con_table")),frame_obj.select_all($("input[name=select_all]"),$("input[name=select]"),$(".table_menu_button").find(".del, .sold_in, .sold_out, .facebook, .batch_price")),frame_obj.del_bat($(".table_menu_button .del"),$("input[name=select]"),"/manage/plugins/download/download-batch"),$(".products_number").on("click",function(e){let t=$(this),o=t.data("id");if(!t.find(".products_txt i").length)return!1;$.post("/manage/plugins/download/get-download-products-info",{DId:o},function(o){if(1==o.ret){let i="",a=0,n=o.msg;i+='<div class="products_list">',a>0&&(i+='<div class="products_list_line"></div>');for(let e in n)i+='<div class="list_item">',i+='<div class="item_img fl">',i+='<img src="'+n[e].PicPath+'" />',i+="</div>",i+='<div class="item_info fr">',i+='<div class="info_name">'+n[e].Name+"</div>",i+="</div>",i+='<div class="clear"></div>',i+="</div>",a++;i+="</div>",t.find(".products_box").html(i);let l=0,d=t.find(".products_container").height(),s=e.currentTarget.offsetTop,r=$(".box_table").height();s+d>r&&(t.find(".products_container").css({top:"auto",bottom:"100%"}),l=1);let c=s-parseInt($(".inside_table").css("padding-top"))-$(".inside_table .list_menu").height()-parseInt($(".box_table table").css("margin-top"));l?(c-=5,d>c&&t.find(".products_box").css("max-height",c)):(c=c+t.height()+25,c+d>r&&t.find(".products_box").css("max-height",r-c)),t.addClass("current").find(".products_container").fadeIn()}},"json")}),$(".products_number").on("mouseleave",function(){$(this).removeClass("current").find(".products_container").fadeOut()}),frame_obj.filterRight({onSubmit:function(e){cateId=e.find("input[name=cateId]").val(),$(".search_box").find("input[name=cateId]").val(cateId)}}),frame_obj.fixed_right($(".download_set"),".fixed_download_set"),frame_obj.submit_form_init($("#download_set_form"));var e={};$(".color_list .color_box .color_button").each(function(){let t=$(this).parent().data("colpickid"),o=$(this).siblings(".color_color"),i=o.val(),a={el:this,theme:"nano",default:i,comparison:!1,components:{preview:!0,opacity:!0,hue:!0,interaction:{hex:!1,rgba:!1,hsva:!1,input:!0,clear:!1,save:!1}}},n=new Pickr(a);e[t]=n;let l=null;n.on("change",e=>{l&&clearTimeout(l),l=setTimeout(()=>{o.val(e.toHEXA().toString()).trigger("change")},1e3)})});var t=function(){let e=$("#download_set_form").find("select[name=btn_type]").val(),t=lang_obj.manage.app.download[e];$("#download_set_form").find(".color_list li").each(function(e,o){o=$(o),dataType=o.find(".component_color").data("type"),dataTypeName=lang_obj.manage.app.download.btn_ary[dataType].replace("%type%",t),o.find(".color_name").text(dataTypeName)})},o=function(){let e=$(".fixed_download_set .rows .item .input_checkbox_box").find("input[value=pop]").prop("checked");e?$(".pop_container").show():$(".pop_container").hide()};$("#download_set_form").find("select[name=btn_type]").on("change",function(){t()}),t(),$("body").on("click",".input_checkbox_box",function(){o()}),o(),$(".go_to_new").on("click",function(){$.post("/manage/view/visual/switch-version",e=>{1==e.ret&&window.open($(this).data("url"),"_blank")},"json")})},download_edit:function(){frame_obj.main_picture_upload(),frame_obj.upload_picture_event(),frame_obj.main_file_upload("frame_obj.download_file_check('#FileDetail', 1);"),frame_obj.download_file_check("#FileDetail",1),frame_obj.fixed_right_products_filter(),frame_obj.mouse_click($(".upload_menu li[data-type=photo]"),"file",function(){frame_obj.photo_choice_init("FileDetail","allFile",1,"",1,"frame_obj.download_file_check('#FileDetail', 1);")}),$(".global_container.upload_file").fileupload({url:"/manage/action/file-upload-plugin?size=photo",acceptFileTypes:/^((application\/(pdf|rar|x-rar|octet-stream|zip|x-zip-compressed|vnd.openxmlformats-officedocument.(wordprocessingml.document|spreadsheetml.sheet|presentationml.presentation)|msword|vnd.ms-excel|vnd.ms-powerpoint|plain|csv|))|(text\/(plain|csv))|image\/(gif|jpe?g|png|x-icon|jp2|webp))$/i,disableImageResize:!1,imageMaxWidth:2e3,imageMaxHeight:99999,imageForceResize:!1,maxFileSize:********,maxNumberOfFiles:1,messages:{maxFileSize:lang_obj.manage.file.size_limit,maxNumberOfFiles:lang_obj.manage.account.file_tips.replace("{{count}}",1),acceptFileTypes:lang_obj.manage.file.download_type_error},callback:function(t,o,i){if(!t)return!1;let a=e(t),n=a.type?a.type.replace(".",""):"",l="";$.inArray(n,["gif","jpg","webp","jpeg","png"])>=0?(l+='<div class="item" data-type="photo">',l+='<span class="img_box"><img src="'+t+'"></span>',l+='<span class="file_del">'+lang_obj.global.del+"</span>",l+='<input type="hidden" name="FilePath" value="'+t+'" />',l+='<input type="text" maxlength="100" class="box_input" value="'+i+'" name="Name" placeholder="'+lang_obj.global.file_name+'" notnull>',l+="</div>"):(l+='<div class="item" data-type="file">',l+='<svg class="icon" aria-hidden="true"><use xlink:href="#icon-'+a.icon+'"></use></svg>',l+='<span class="file_del">'+lang_obj.global.del+"</span>",l+='<input type="hidden" name="FilePath" value="'+t+'" />',l+='<input type="text" maxlength="100" class="box_input" value="'+i+'" name="Name" placeholder="'+lang_obj.global.file_name+'" notnull>',l+="</div>"),$("#FileDetail").html(""),$("#FileDetail").append(l),$("#FileDetail .item").length>0?$("#box_photo_edit .btn_submit").prop("disabled",!1):$("#box_photo_edit .btn_submit").prop("disabled",!0),$(".upload_file .fileinput-button , .upload_file .upload_menu").hide(),$(".upload_file .fileinput-button").parents(".input").css("display","block")}}).on("fileuploadadd",function(e,t){if(t.originalFiles[0]&&(vName=t.originalFiles[0].name,vSize=t.originalFiles[0].size,vType=t.originalFiles[0].type,vType.match(/^(image\/(gif|jpe?g|png|x-icon|jp2|webp))$/i)&&vSize>10485760))return global_obj.win_alert_auto_close(lang_obj.manage.photo.size_limit,"fail",1e3,"20%"),!1}),$(".global_container.upload_file").fileupload("option","redirect",window.location.href.replace(/\/[^\/]*$/,"/cors/result.html?%s")),$("#FileDetail").on("click","div span.file_del",function(){let e=$(this),t={title:lang_obj.global.del_confirm,confirmBtn:lang_obj.global.del,confirmBtnClass:"btn_warn"};return global_obj.win_alert(t,function(){let t=e.parents(".item").index();e.parents(".item").remove(),$("#FileDetail").siblings(".photo_multi_img.template-box").find(".template-download").eq(t).remove(),$("#FileDetail .item").length>0?$("#box_photo_edit .btn_submit").prop("disabled",!1):$("#box_photo_edit .btn_submit").prop("disabled",!0),$("#download_inside .upload_file .fileinput-button, .upload_menu").show(),$(".fileinput-button").parents(".input").css("display","inline-block"),$(".upload_menu li").show(),global_obj.div_mask(1)},"confirm"),$("#box_photo_edit").length&&global_obj.div_mask(1),!1}),$(".download_scope").on("click",".box_type_menu .item",function(){let e=$(this);e.addClass("checked").find("input").prop("checked",!0),e.siblings().removeClass("checked").find("input").prop("checked",!1)}),$(".download_type").on("click",".box_type_menu .item",function(){let e=$(this).attr("data-type");$(this).find("input").prop("checked",!0).parent().siblings().find("input").prop("checked",!1),$(this).addClass("checked").siblings().removeClass("checked"),$(".group_item").hide(),$('.group_item[data-value="'+e+'"]').show()}),$("#download_inside .download_scope .input_radio_box").on("click",function(){t()}),$("#download_inside").on("click","#add_pro",function(){let e={iframeTitle:lang_obj.manage.view.select_products,type:"manual",value:{},isOrder:!0,valueOrder:[],IsNeedSelectCateId:1},t={},o=[];$("#download_inside #pro_list .products_list .item").each(function(){let e=$(this).find('input[name^="select"]').val(),i=$(this).find(".img img").attr("src");t[e]={image:i},o.push(e)}),e.value=t,e.valueOrder=o,e.isOrder=!1,frame_obj.products_choice_iframe_init_v2({params:e,onSubmit:function(e){if($.isEmptyObject(e.value))productsList=[],$(".products_list table tbody").html(""),a(),global_obj.win_alert_auto_close(lang_obj.manage.error.no_prod_data,"await",1e3,"8%"),$(".bg_no_table_data").show(),$(".products_box").hide();else{let t=[];for(i in e.value)t.push(e.value[i].proid);productsList=t,a(),global_obj.win_alert_auto_close(lang_obj.global.add_success,"",1e3,"8%"),$(".bg_no_table_data").hide(),$(".products_box").show()}for($idStr="",$i=0;$i<productsList.length;$i++)$idStr+=($idStr?",":"")+productsList[$i];$("#download_edit_form").find('input[name="ProId"]').val($idStr)}})}),$("#download_edit_form").on("click",".pagination li a",function(){let e=$(this),t=e.attr("data-page"),o=$(".fixed_loading");return o.fadeIn(),$("#pro_list .products_list").attr("data-page",t),window.history.pushState(null,null,location.href.replace(/&page=([0-9]*)/,"")+"&page="+t),a(),o.fadeOut(),!1}),$('.cover_box .global_upload_menu li[data-type="gallery"]').show(),frame_obj.submit_form_init($("#download_edit_form"),"",function(){let e=$("#FileDetail").find(".item").data("type");e&&$("#download_edit_form").find("input[name=FileType]").val(e)},"",function(e){1==e.ret?(global_obj.win_alert_auto_close(lang_obj.global.save_success,"success"),setTimeout(function(){window.location.href=e.msg},500)):global_obj.win_alert_auto_close(e.msg,"fail")});let e=function(e){let t=e.lastIndexOf("."),o=e.substr(t),i="";switch(o){case".csv":i="csv";break;case".xls":case".xlsx":i="excel1";break;case".pdf":i="pdf1";break;case".ppt":case".pptx":i="ppt1";break;case".rar":case".zip":i="a-32yasuowenjian";break;case".txt":i="txt1";break;case".doc":case".docx":i="word1"}return{type:o,icon:i}},t=function(){setTimeout(()=>{$value=$("#download_inside .download_scope .input_radio_box input[value=password]").prop("checked"),$value?$(".password_input").show().find("input").attr("notnull","notnull"):$(".password_input").hide().find("input").removeAttr("notnull")},100)};t();var o=20;let a=function(){var e=$(".my_order_box .drop_down .item .current"),t=parseInt($("#pro_list .products_list").attr("data-page"));$obj=$("#download_inside .products_list tbody"),$order=e.attr("data-order"),$where=productsList.slice(t*o,(t+1)*o),$maxPage=Math.ceil(parseInt(productsList.length)/o),t>=$maxPage&&(t=$maxPage-1,t<0&&(t=0),$where=productsList.slice(t*o,(t+1)*o),$(".category_products_box .products_list").attr("data-page",t)),$.post("/manage/plugins/download/get-products-list?page="+(t+1),{order:$order,where:$where,totalCount:productsList.length,pageSize:o},function(e){for($obj.html(e.msg.html),$("#download_inside .products_list .item").length?($(".bg_no_table_data").hide(),$("#pro_list").find(".products_box").removeClass("hide")):($(".bg_no_table_data").show(),$("#pro_list").find(".products_box").addClass("hide")),$("#pro_page").html(e.msg.turn_page),$idStr="",$i=0;$i<productsList.length;$i++)$idStr+=($idStr?",":"")+productsList[$i];$("#download_edit_form").find('input[name="ProId"]').val($idStr)},"json")};productsList.length&&a(),$("#download_inside .products_list").on("click",".icon_del",function(){var e=$(this).parents(".item"),t=parseInt($(".products_box .products_list").attr("data-page")),i=t*o+e.index();params={title:lang_obj.global.del_confirm,confirmBtn:lang_obj.global.del,confirmBtnClass:"btn_warn"},global_obj.win_alert(params,function(){$("#download_inside .products_list .del_pro_box").append('<input type="hidden" name="DelProId[]" value="'+e.find("input[name^=ProId]").val()+'">'),productsList.splice(i,1),e.remove(),a()},"confirm")})},download_category_init:function(){function e(t,o){if("default"==o)var a=$("input[name=CateIdAry]").length,n=$("input[name=CateIdAry]").eq(t).val();else a=1,n=$(".pro_count.load").map(function(){return $(this).attr("data-cateid")}).get().join(",");n&&$.post("/manage/plugins/download/download-count",{id:n},function(n){if(1==n.ret)for(i in n.msg)"loading"==n.msg[i]?$('.pro_count[data-cateid="'+i+'"]').parent().find(".icon_edit").addClass("disabled").off("click"):$('.pro_count[data-cateid="'+i+'"]').html(n.msg[i]).removeClass("load").parent().find(".icon_edit").removeClass("disabled");t++,t<a&&e(t,o)},"json")}function t(o){setTimeout(function(){$(".pro_count.load").length&&(e(0,"loading"),o*=2,t(o))},1e3*o)}$(".inside_menu").insideMenu(),frame_obj.del_init($("#download_category .r_con_table")),frame_obj.select_all($("input[name=select_all]"),$("input[name=select]"),$(".table_menu_button").find(".del, .sold_in, .sold_out, .facebook, .batch_price")),frame_obj.del_bat($(".table_menu_button .del"),$("input[name=select]"),"/manage/plugins/download/download-category-batch"),e(0,"default"),t(2),frame_obj.submit_form_init($("#update_form"),"/manage/plugins/download/category/")},download_category_edit_init:function(){if(frame_obj.submit_form_init($("#edit_form"),"/manage/plugins/download/category/"),frame_obj.fixed_right($("#edit_keyword"),".fixed_edit_keyword",function(e){let t=$("input[name=CateId]").val();frame_obj.seo_edit_keyword({do_action:"/manage/action/seo-keyword-select",Type:"download_category",field:"CateId",Id:t})}),frame_obj.seo_keyword_form_submit(),"object"==typeof CKEDITOR)for(i in shop_config.language){var e=shop_config.language[i],t="Description_"+e;CKEDITOR.instances[t].on("change",function(){var e=$(this)[0].getData();e&&(e=e.replace(/(<[^>]+>)|(&nbsp;)/g,"").replace(/(^\s*)|(\s*$)/g,"").replace(/(\s+)|([\r\n])|([\r])|([\n])/g," ").slice(0,254)),$(this)[0].name&&1==$("#"+t).data("change")&&$('[name="SeoDescription_'+$(this)[0].name.replace("Description_","")+'"]').val(global_obj.htmlspecialchars_decode(e))})}if("object"==typeof tinymce)for(i in shop_config.language){e=shop_config.language[i],t="Description_"+e;tinymce.editors[t].on("change",function(){var o=$(this)[0].getContent();o&&(o=o.replace(/(<[^>]+>)|(&nbsp;)/g,"").replace(/(^\s*)|(\s*$)/g,"").replace(/(\s+)|([\r\n])|([\r])|([\n])/g," ").slice(0,254)),1==$("#"+t).data("change")&&$('[name="SeoDescription_'+e+'"]').val(global_obj.htmlspecialchars_decode(o))})}const o=$(".global_seo_box").find("textarea[name=SeoDescription_en]").val();$("[name=BriefDescription_en]").on("keyup",function(){var e=$.trim($(this).val());if(""!=o)return!1;$(".global_seo_box").find("textarea[name=SeoDescription_en]").val(e)});let a=new ClipboardJS(".btn_copy");a.on("success",function(e){alert(lang_obj.global.copy_complete)}),$("textarea[name=PageUrl]").on("keyup",function(){let e=$(".prefix_textarea .prefix").text(),t=$(this).val(),o=e+t;$(this).parents(".custom_row").find(".btn_copy").attr("data-clipboard-text",o)}),$(".box_basic_more").hover(function(){var e=42;$(this).hasClass("box_seo_basic_more")&&(e=20),$(this).find(".drop_down").show().stop(!0).animate({top:e,opacity:1},250)},function(){var e=32;$(this).hasClass("box_seo_basic_more")&&(e=10),$(this).find(".drop_down").stop(!0).animate({top:e,opacity:0},100,function(){$(this).hide()})})}};