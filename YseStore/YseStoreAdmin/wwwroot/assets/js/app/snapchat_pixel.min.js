var snapchat_pixel_obj={snapchat_pixel_init:function(){frame_obj.fixed_right($("#snapchat_pixel .id_add"),".fixed_id_set",function(i){$("#id_set_form textarea[name=TrackingID]").html("");let t=i.data("pixelid"),a=i.data("position");t?($("#id_set_form textarea[name=TrackingID]").html(t),$("#id_set_form input[name=position]").val(a)):$("#id_set_form input[name=position]").val(-1)}),frame_obj.del_init($("#snapchat_pixel .r_con_table")),frame_obj.submit_form_init($("#id_set_form"),"",function(){},0,function(i){return global_obj.win_alert_auto_close(lang_obj.global.save_success,"",1e3,"8%"),$("#fixed_right .btn_cancel").click(),window.location.reload(),!1})}};