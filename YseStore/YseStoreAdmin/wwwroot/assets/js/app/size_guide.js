

var size_guide_obj={
	global_init: () => {//预览
		$('#size_guide').on('click','.guide_view',function(){
			let _this = $(this),
				_Id = _this.attr('data-id'),
				_viewHtml = '';
			$.post('/manage/plugins/size-guide/get-view-data',{'id':_Id},function(data){
				if (data.ret == 1) {
					let o = data.msg;
					let _guideData = o.GuideData;
					let _switchData = [];
					for (i in _guideData.data) {
						_switchData.push(i)
					}
					_viewHtml += '<div class="guide_view_wrapper">';
					_viewHtml += 	'<div class="guide_title">'+lang_obj.manage.app.size_guide.guide_title+'</div>';
					if (_guideData){
					_viewHtml +=	'<div class="guide_table">';
						if(_switchData.length > 0){
					_viewHtml +=		'<div class="guide_table_switch">';
							for ( n in _switchData) {
					_viewHtml +=			'<a href="javascript:;" unit="'+_switchData[n]+'" class="table_switch_item'+(n==0?' cur':'')+'">'+_switchData[n]+'</a>';
							}
					_viewHtml +=		'</div>';
						}
					_viewHtml +=		'<div class="guide_table_info">';
					_viewHtml +=			'<table border="1">';
					_viewHtml +=				'<thead>';
					_viewHtml +=					'<tr>';
					for( k in _guideData.field ){
						let _tdLength = _guideData.field.length,
							 _tdWidth = parseInt(100 / _tdLength);
					_viewHtml +=						'<td width="'+_tdWidth+'%">'+_guideData.field[k]+'</td>'
					}
					_viewHtml +=					'</tr>';
					_viewHtml +=				'</thead>';
					for ( k in _guideData.data ) {
						let _dataBody = _guideData.data[k];
					_viewHtml +=				'<tbody unit="'+k+'">';
						for ( i in _dataBody ) {
							let _dataTr = _dataBody[i];
					_viewHtml +=					'<tr>';
							for (n in _dataTr) {
					_viewHtml +=						'<td>'+_dataTr[n]+'</td>';
							}
					_viewHtml +=					'</tr>';
						}
					_viewHtml +=				'</tbody>';
					}
					_viewHtml +=			'</table>';
					_viewHtml +=		'</div>';
					_viewHtml +=	'</div>';
					}
					_viewHtml +=	'<div class="guide_desc">'+o.Description+'</div>';
					if( o.MeasurementData ){
					_viewHtml +=	'<div class="guide_measurement">';
					_viewHtml +=		'<div class="guide_measurement_title">'+o.MeasurementData.title+'</div>';
					_viewHtml +=		'<div class="guide_measurement_content">';
					_viewHtml +=			'<div class="guide_measurement_info">';
					for (k in o.MeasurementData.method) {
						let _metodData = o.MeasurementData.method[k];
					_viewHtml += 				'<div class="guide_measurement_item">';
					_viewHtml +=					'<div class="guide_measurement_item_title"><em>'+(parseInt(k)+1)+'</em>'+_metodData.title+'</div>';
					_viewHtml +=					'<div class="guide_measurement_item_desc">'+global_obj.n2br(_metodData.desc)+'</div>';
					_viewHtml += 				'</div>';
					}
					_viewHtml +=			'</div>';
					if (o.MeasurementData.pic) {
					_viewHtml +=			'<div class="guide_measurement_img"><img src="'+o.MeasurementData.pic+'" /></div>';
					}
					_viewHtml +=		'</div>';
					_viewHtml +=	'</div>';
					}
					_viewHtml +=	'<div class="guide_close"></div>';
					_viewHtml += '</div>';
					if (_viewHtml) {
						global_obj.div_mask();
						$('#size_guide').append(_viewHtml);
					}
				} else {
					global_obj.win_alert_auto_close(data.msg, 'fail', 1000, '8%');
				}
			},'json')
		})
		$('#size_guide').on('click','.guide_close',function(){
			$('.guide_view_wrapper').remove();
			global_obj.div_mask(1);
		})
		$('#size_guide').on('click','.guide_view_wrapper .table_switch_item',function(){
			let _unitType = $(this).attr('unit');
			$(this).addClass('cur').siblings().removeClass('cur');
			$('.guide_view_wrapper').find('.guide_table_info tbody[unit='+_unitType+']').show().siblings('tbody').hide();
		})

		$('#size_guide').on('click', '.switchery', function(){
			let id = $(this).data('id'),
				status = $(this).hasClass('checked') ? 1 : 0;
			if(status) {
				$(this).removeClass('checked');
			} else {
				$(this).addClass('checked');
			}
			
			status = $(this).hasClass('checked') ? 1 : 0;
			$.post('/manage/plugins/size-guide/size-guide-switch', {'switch': status, 'id': id}, function(result){
				let status = result.ret == 1 ? 'success' : 'fail';
				global_obj.win_alert_auto_close(result.msg, status, 500, '20%');
			}, 'json')
		})

	},

	list_init: () => {
		frame_obj.select_all($('input[name=select_all]'), $('input[name=select]'), $('.table_menu_button').find('.del')); // 批量操作
		frame_obj.del_bat($('.table_menu_button .del'), $('input[name=select]'), '/manage/plugins/size-guide/del'); //批量删除
		frame_obj.fixed_box_popup({
			"clickObj": $(".btn_add_guide, .btn_add_item"),
			"targetClass": "box_guide_edit"
		});
	},

	edit_init: () => {
		const guide_obj = {
			'form_obj' : $('#size_guide').find('.guide_global_form'),
			'_fieldForm' : $('.fixed_guide_field_set'),
			'_measurementForm' : $('.fixed_guide_add_measurement'),
			'_dataForm' : $('.fixed_guide_add_data'),
			'_unitData' : {0:"CM",1:"IN"},
			'default_scope_click' : () => {
				if(guide_obj.form_obj.find('.input_radio_side_box.checked').length<1){
					guide_obj.form_obj.find('.type_box').find('.input_radio_side_box:first').click()
				}
			},
			'add_field' : (_Txt,_Name) => { //添加字段
				_Txt = _Txt.replaceAll('%N%', '');
				$('#field_set_form .field_row').append(_Txt);
				if (_Name) {
					$('#field_set_form .field_row .field_content_row:last').find('.box_input[name=field\\[\\]]').val(_Name).attr('data-old',_Name);
				}
				if ($('.field_content_row').length == 1) {
					$('.field_content_row .button').addClass('hide_remove');
				}
			},
			'remove_field': (obj) => {//移除字段
				obj.parents('.field_content_row').fadeOut(function() {
					$(this).remove();
					if ($('.field_content_row').length == 1) {
						$('.field_content_row .button').addClass('hide_remove');
					}
				});
			},
			'add_measurement' : (_Txt,_Value,_Status,_Position) => { //添加测量
				let _itemSize = parseInt(guide_obj.form_obj.find('.measurement_info .measurement_item').length);
				let _infoCount = _itemSize + 1;
				if (_Status == 1) {
					_Txt = _Txt.replace('%title%', _infoCount+'、'+_Value.title)
							.replace('%valtitle%', _Value.title)
							.replaceAll('%desc%', _Value.desc)
							.replaceAll('%position%',_itemSize);
					guide_obj.form_obj.find('.measurement_info').append(_Txt);
				} else {
					let _itemObj = guide_obj.form_obj.find('.measurement_info .measurement_item').eq(_Position),
						_itemNum = parseInt(_Position) + 1;
					_itemObj.find('.item_title').html(_itemNum+'、'+_Value.title);
					_itemObj.find('.item_desc').html(_Value.desc);
					_itemObj.find('input.method_title').val(_Value.title);
					_itemObj.find('input.method_desc').val(_Value.desc);
				}
				if ($('.measurement_box .noInfoData').css('display') == 'block') $('.measurement_box .noInfoData').hide()
			},
			'show_field' : () => { //显示字段
				let _fieldAry = [],
					_oldFieldInputAry = [],
					_fieldItem = guide_obj._fieldForm.find('input[name=field\\[\\]]'),
					_fieldShowObj = guide_obj.form_obj.find('.guide_show_box table thead tr'),
					_oldFieldData = guide_obj._fieldForm.find('input[name=old_field]').val(),
					_oldFieldAry = _oldFieldData.split(',');
				_fieldItem.each(function(){
					let _thisValue =$(this).val(),
						_thisOldData = $(this).attr('data-old');
					_fieldAry.push(_thisValue);
					_oldFieldInputAry.push(_thisOldData)
				})
				const _mergeArr = [..._fieldAry, ..._oldFieldAry];
				const filterArr = _mergeArr.filter(item => {
				  return !(_fieldAry.includes(item) && _oldFieldAry.includes(item));
				});
				if (_fieldAry.length > 0) {
					_fieldShowObj.html('');
					let _showHtml = '';
					let _fieldWidth = parseInt(850 / _fieldAry.length);
					let _fieldString = _fieldAry.toString();
					for (k in _fieldAry) {
						_showHtml += '<td width="'+_fieldWidth+'px">'+_fieldAry[k]+'<input type="hidden" name="GuideData[field][]" value="'+global_obj.htmlspecialchars(_fieldAry[k])+'"></td>';
					}
					_showHtml += '<td width="150" class="operation"></td>';
					if (_showHtml) _fieldShowObj.html(_showHtml)
					guide_obj.form_obj.find('.guide_operation_btn .set_btn').attr('data-field',_fieldString)
					guide_obj.form_obj.find('.guide_show_box table tbody tr').each(function(){
						for (k in _fieldAry) {
							let _tdField = _fieldAry[k];
							let _tdForeachField = $(this).find('td').eq(k).attr('data-field'),
								_addThisUnit = $(this).parents('tbody').attr('data-unit'),
								_trIndex = $(this).index();
							if (filterArr.length > 0 && _tdForeachField != undefined) { //删除
								for(i in filterArr) {
									$(this).find(`td[data-field='${filterArr[i]}']`).remove()
								}
							}
							if (_tdForeachField == undefined){ //添加
								let _addNewTd = '<td data-field="'+_tdField+'"><input type="hidden" name="GuideData[data]['+_addThisUnit+']['+_trIndex+'][]" value=""></td>',
								_addNum = parseInt(k) - 1;
								$(this).find('td').eq(_addNum).after(_addNewTd)
							}
							
							if (_tdForeachField != _tdField) { //排序
								let _originalTdData = $(this).find(`td[data-field='${_tdForeachField}']`),
									_moveTdData = $(this).find(`td[data-field='${_tdField}']`)
								_moveTdData.insertBefore(_originalTdData)
							}
						}
					})
				}
				if(!$('.guide_box guide_table_no_data').hasClass('hide')) {
					$('.guide_box .guide_table').removeClass('hide')
					$('.guide_box .guide_table_no_data').addClass('hide')
				}
			},
			'add_data_html' : (_fieldData,_IsAdd,_Position,_Unit) => { //表单数据
				let _fieldAry = _fieldData.split(','),
					_fixedDataHtml = '',
					_Txt = guide_obj.data_item,
					_tbodyItem = guide_obj.form_obj.find('.guide_show_box tbody'),
					_indexData = [],
					_positionData = [],
					_CMData =[],
					_INData = [];
				if(_Unit == 'IN') {
					guide_obj._unitData = {0:"IN",1:"CM"};
				} 
				_tbodyItem.each(function(){
					let _thisTbody = $(this);
					let _thisUnit = _thisTbody.attr('data-unit');
					_indexData[_thisUnit] = _thisTbody.find('tr').length
					if (_Position != -1){
						_thisTbody.find('tr').eq(_Position).find('input[name*=GuideData]').each(function(){
							if (_thisTbody.attr('data-unit') == 'CM'){
								_CMData.push($(this).val())
							} else{ 
								_INData.push($(this).val())
							}
						})
						if (_thisTbody.attr('data-unit') == 'CM'){
							_positionData['CM']	= _CMData
						} else {
							_positionData['IN']	= _INData
						}
					}
				})
				
				for (k in guide_obj._unitData) {
					_fixedDataHtml +=	'<div class="unit_data_box" data-unit="'+guide_obj._unitData[k]+'">';
					for (i in _fieldAry) {
						if (_IsAdd == 0) {
							let _replaceData = _positionData[guide_obj._unitData[k]][i] ? _positionData[guide_obj._unitData[k]][i] : '';
					_fixedDataHtml +=		_Txt.replace('%title%', _fieldAry[i])
												.replace('%unit%', guide_obj._unitData[k])
												.replace('%index%',_indexData[guide_obj._unitData[k]])
												.replace('%data%',_replaceData)
												.replace('%num%', i);
						} else {
					_fixedDataHtml +=		_Txt.replace('%title%', _fieldAry[i])
												.replace('%unit%', guide_obj._unitData[k])
												.replace('%index%',_indexData[guide_obj._unitData[k]])
												.replace('%data%','')
												.replace('%num%', i);
						}
					}
					_fixedDataHtml +=	'</div>';
				}
				if (_fixedDataHtml) guide_obj._dataForm.find('.fixed_data_box').html(_fixedDataHtml);
			},
			'show_data' : (_data) => { //显示数据
				if (_data.length > 0) {
					let _tbodyItem = guide_obj.form_obj.find('.guide_show_box tbody'),
						_positionData = [],
						_isAdd = guide_obj._dataForm.find('input[name=isAdd]').val(),
						_Position = guide_obj._dataForm.find('input[name=position]').val(),
						_fieldString = guide_obj.form_obj.find('.set_btn').attr('data-field');
					_tbodyItem.each(function(){
						let _thisUnit = $(this).attr('data-unit');
						_positionData[_thisUnit] = $(this).find('tr').length
					})
					for (k in guide_obj._unitData) {
						let _curUnit = guide_obj._unitData[k];
						if (_isAdd == 0) {
							let _thisTbody = guide_obj.form_obj.find('.guide_show_box tbody[data-unit='+_curUnit+'] tr').eq(_Position);
							let num = 0;
							for ( i in _data ) {
								if (_data[i].name.indexOf(_curUnit) == -1) continue
								let _feachTd = _thisTbody.find('td').eq(num);
								_feachTd.html(_data[i].value+'<input type="hidden" name="GuideData[data]['+_curUnit+']['+_Position+'][]" value="'+_data[i].value+'">')
								num++;
							}
						} else {
							let _sHtml = '',
								_fieldData = _fieldString.split(','),
								_i = 0;
							_sHtml += '<tr>';
							for ( i in _data ) {
								if (_data[i].name.indexOf(_curUnit) == -1) continue
							_sHtml += 	'<td data-field="'+_fieldData[_i]+'">'+_data[i].value+'<input type="hidden" name="GuideData[data]['+_curUnit+']['+_positionData[_curUnit]+'][]" value="'+_data[i].value+'"></td>';
								_i += 1;

							}
							_sHtml += `	<td class="operation tar">
											<a class="icon_edit oper_icon button_tips" href="javascript:;">`+lang_obj.global.edit+`</a>
											<a class="icon_del oper_icon button_tips" href="javascript:;">`+lang_obj.global.del+`</a>
										</td>`;
							_sHtml += '</tr>';
							let _IsAllowValue = 0;
							checkNullItem = $('.unit_data_box[data-unit='+_curUnit+']').find('input[name*=data]');
							checkNullItem.each(function(){
								if($(this).val() != '') _IsAllowValue += 1;
							})
							_IsAllowValue > 0 && guide_obj.form_obj.find('.guide_show_box tbody[data-unit='+_curUnit+']').append(_sHtml)
						}
					}
				}
			},
			'change_option' : data => {
				let _optionItem = `<option value>${lang_obj.global.selected}</option>`,
				_curAttr = guide_obj.form_obj.find('input[name=curAttr]').val();
				for ( k in data) {
					let o = data[k],
						_checked = o == _curAttr ? 'selected' : ''; 
					_optionItem += `<option value="${o}" ${_checked}>${o}</option>`;
				}
				return _optionItem;
			},
			'check_scope' : e => {
				let _doubleThis = e,
					_selectValue = [],
					_obj = _doubleThis.parents('.use_group_box')
					_objScope = _obj.attr('data-value');
				if (_objScope == 'products') {
					_obj.find("input[name='productsOption[]']").each(function(){
						_selectValue.push($(this).val());
					})
				} else if (_objScope == 'category') {
					_obj.find("input[name='products_categoryOption[]']").each(function(){
						_selectValue.push($(this).val());
					})
				}
				let _selectString = _selectValue.toString(),
					_curAttr = guide_obj.form_obj.find('input[name=curAttr]').val();
				$.post('/manage/plugins/size-guide/change-attr',{'scope':_objScope,'select':_selectString,'curAttr':_curAttr},function(data){
					if (data.ret == 1) {
						guide_obj.form_obj.find('.attr_select_box').html(data.msg)
					}
				},'json')
			},
			'field_item' : `<div class="field_content_row">
								<em class="field_myorder"></em>
								<div class="rows clean float">
									<div class="input"><input name="field[]" value="%N%" data-old="%N%" type="text" class="box_input" size="28" notnull></div>
								</div>
								<div class="float button">
									<a href="javascript:;" class="btn_field fl btn_field_remove"><i></i></a>
								</div>
							</div>`,
			'measurement_item' : `<div class="measurement_item">
									<div class="measurement_item_message">
										<div class="item_title">%title%</div>
										<div class="item_desc">%desc%</div>
									</div>
									<div class="measurement_item_operation">
										<a class="icon_edit oper_icon edit_measurement_btn button_tips" href="javascript:;">`+lang_obj.manage.global.edit+`</a>
										<a class="icon_del oper_icon del_measurement_btn button_tips" href="javascript:;">`+lang_obj.global.del+`</a>
									</div>
									<input type="hidden" name="MeasurementData[method][%position%][title]" class="method_title" value="%valtitle%">
									<input type="hidden" name="MeasurementData[method][%position%][desc]" class="method_desc" value="%desc%">
								</div>`,
			'data_item'	:	`<div class="rows clean" data-num="%num%">
								<label>%title%</label>
								<div class="input">
									<input name="data[%unit%][%index%][]" value="%data%" type="text" class="box_input full_input" size="42" notnull>
								</div>
							</div>`,
		}
		/** 适用范围start */
		frame_obj.box_type_menu(function(obj){
			let menuObj = obj.parent();
			if (menuObj.hasClass('specify_box')) {
				let specifyValue = obj.find('input[name=Scope\\[type\\]]').val();
				guide_obj.form_obj.find('.use_group_box').hide();//先默认隐藏
				guide_obj.form_obj.find('.use_group_box[data-value='+specifyValue+']').show(); //展开对应的下拉
				let _selectValue = [],
					_selectObj = guide_obj.form_obj.find('.use_group_box[data-value='+specifyValue+']');
				if (specifyValue == 'products') {
					_selectObj.find("input[name='productsOption[]']").each(function(){
						_selectValue.push($(this).val());
					})
				} else if (specifyValue == 'category') {
					_selectObj.find("input[name='products_categoryOption[]']").each(function(){
						_selectValue.push($(this).val());
					})
				}
				let _selectString = _selectValue.toString(),
					_curAttr = guide_obj.form_obj.find('input[name=curAttr]').val();
				$.post('/manage/plugins/size-guide/change-attr',{'scope':specifyValue,'select':_selectString,'curAttr':_curAttr},function(data){
					if (data.ret == 1) {
						guide_obj.form_obj.find('.attr_select_box').html(data.msg)
					}
				},'json')
			}
		});

		$('.select_list').bind('DOMNodeRemoved',function(){
			guide_obj.check_scope($(this));
		});
		//默认选中适用范围
		guide_obj.default_scope_click();
		/** 适用范围end */
		
		$('.fixed_guide_field_set').on('change', 'select[name="unit"]', function(){
			let _this = $(this),
				_value = _this.val(),
				_otherValue = _this.find('option:selected').siblings().val();
			$('.fixed_guide_field_set .select_row .change_unit').html(_otherValue);
		})

		$('#field_set_form').on('click', '.select_row', function(){
			$(this).addClass('checked').find('input').prop('checked', true).parents('.select_row').siblings().removeClass('checked').find('input').prop('checked', false);
		})

		var changePosition = function(){

			let innerUnit = $('#edit_form').find('input[name=Unit]').val(),
				innerFillMethod = $('#edit_form').find('input[name=FillMethod]').val();
			if(innerFillMethod == 'normal') return;
	
			var unit = innerUnit,
				obj = $('#size_guide .guide_box .inside_container').find('li[data-unit="'+unit+'"]'),
				otherUnit = unit == 'CM' ? 'IN' : 'CM';
			$('#size_guide .guide_box .inside_container').find('li[data-unit="'+otherUnit+'"]').before(obj);

			
			tableObj = $('#size_guide .guide_show_box .r_con_table tbody[data-unit='+unit+']');
			$('#size_guide .guide_show_box .r_con_table tbody[data-unit='+otherUnit+']').before(tableObj);

			tableObj = $('#add_data_form .inside_body li[data-unit='+unit+']');
			$('#add_data_form .inside_body li[data-unit='+otherUnit+']').before(tableObj);

			setTimeout(() => {
				$('*[data-unit='+ innerUnit +']').click();
			}, 300);
		}

		changePosition();

		/** 字段设置start */
		frame_obj.fixed_right($('.set_btn'), '.fixed_guide_field_set', (e) => {
			let _fieldData = e.attr('data-field');
			guide_obj._fieldForm.find('.field_row').html('');
			guide_obj._fieldForm.find('input[name=old_field]').val(_fieldData)
			if (!$('.field_row .field_content_row').length && !_fieldData){
				guide_obj.add_field(guide_obj.field_item);
			} else {
				let _fieldAry = _fieldData.split(',');
				for (k in _fieldAry) {
					guide_obj.add_field(guide_obj.field_item,_fieldAry[k]);
				}
			}
			//排序
			$('.field_row').dragsort('destroy');
			frame_obj.dragsort($('.field_row'), '', '.field_content_row .field_myorder', '.field_content_row .rows .box_input', '<div class="placeHolder"></div>');
		})
		guide_obj._fieldForm.on('click','.btn_submit',() => { //保存
			if(global_obj.check_form(guide_obj._fieldForm.find('*[notnull]'), guide_obj._fieldForm.find('*[format]'), 1)){ return false; };
			guide_obj.show_field();

			_unitValue = guide_obj._fieldForm.find('select[name="unit"]').val();
			_fillMethodValue = guide_obj._fieldForm.find('input[name="FillMethod"]:checked').val();
			if(_fillMethodValue == 'normal') {
				_unitValue = '';
			}
			$('#edit_form').find('input[name=FillMethod]').val(_fillMethodValue);
			$('#edit_form').find('input[name=Unit]').val(_unitValue);

			$('#fixed_right .btn_cancel').click();
			//尺码表标题互换

			changePosition();

		})
		guide_obj._fieldForm.on('change', '.field_content_row .box_input', function(){
			let _changeOldField = $(this).attr('data-old'),
				_changeField = $(this).val(),
				_oldAry = guide_obj._fieldForm.find('input[name=old_field]').val().split(',');
			for (k in _oldAry){
				if(guide_obj._fieldForm.find('.field_row input').eq(k).val() && _changeOldField){
					_oldAry[k] = guide_obj._fieldForm.find('.field_row input').eq(k).val()
				}
			}
			guide_obj._fieldForm.find('input[name=old_field]').val(_oldAry.toString());
			guide_obj.form_obj.find(`.guide_box tbody td[data-field='${_changeOldField}']`).attr('data-field',_changeField)
		})
		guide_obj._fieldForm.on('click', '.field_add_btn .btn_add_item', function(){
			guide_obj.add_field(guide_obj.field_item);
		})
		guide_obj._fieldForm.on('click', '.btn_field_remove', function(){
			guide_obj.remove_field($(this));
		})
		/** 字段设置end */

		/**尺码表操作start */
		guide_obj.form_obj.on('click','.unit_item',function(){ //单位切换
			let _unit = $(this).attr('data-unit');
			$(this).addClass('current').parent().siblings().find('a').removeClass('current');
			guide_obj.form_obj.find('.guide_show_box tbody[data-unit='+_unit+']').show().siblings('tbody').hide();
		})
		/**尺码表操作end */

		/**尺码表数据添加start */
		guide_obj._dataForm.on('click','.unit_item',function(){ //单位切换
			let _unit = $(this).attr('data-unit');
			$(this).addClass('current').parent().siblings().find('a').removeClass('current');
			guide_obj._dataForm.find('.fixed_data_box .unit_data_box[data-unit='+_unit+']').show().siblings().hide();
		})
		frame_obj.fixed_right($('#guide_add_data'), '.fixed_guide_add_data', () => {
			guide_obj._dataForm.find('.fixed_data_box').html('');
			guide_obj._dataForm.find('input[name=isAdd]').val(1)
			guide_obj._dataForm.find('input[name=position]').val(-1)
			guide_obj._dataForm.find('input[name=unit]').val('CM')
			setTimeout(function(){
				let _fieldData = guide_obj.form_obj.find('.guide_box .set_btn').attr('data-field');
				let _dataIsAdd = guide_obj._dataForm.find('input[name=isAdd]').val(),
					_dataPosition = guide_obj._dataForm.find('input[name=position]').val(),
					_dataUnit = guide_obj.form_obj.find('input[name=Unit]').val()
				guide_obj.add_data_html(_fieldData,_dataIsAdd,_dataPosition,_dataUnit)

				_innerFillMethod = guide_obj.form_obj.find('input[name="FillMethod"]').val();
				_innerUnit = guide_obj.form_obj.find('input[name="Unit"]').val();
				if(_innerFillMethod == 'auto' && _innerUnit) {
					$('.fixed_data_box').find('.unit_data_box').removeAttr('auto-change').find('.box_input').removeClass('readonly');
					$('.fixed_data_box').find('.unit_data_box[data-unit="' + _innerUnit + '"]').attr('auto-change', false).show().siblings().hide().attr('auto-change', true).find('.box_input').attr('readonly', 'readonly');
				}
				
			},300)

		},() => {
			let _fieldData = guide_obj.form_obj.find('.guide_box .set_btn').attr('data-field');
			guide_obj._dataForm.find('.top_title strong').html(lang_obj.manage.app.size_guide.add_data_title);
			if (!_fieldData) {
				global_obj.win_alert_auto_close(lang_obj.manage.app.size_guide.add_field_tips, 'fail', 1000, '8%');
				return false;
			}
			
			guide_obj._dataForm.find('.unit_item[data-unit='+ guide_obj.form_obj.find('input[name=Unit]').val() +']').click();
		})

		guide_obj._dataForm.on('keyup', '.fixed_data_box .unit_data_box[auto-change=false] .box_input', function(){
			let _value = $(this).val(),
				_chars = /^\d+(\.\d+)?$/,
				_unit = $(this).parents('.unit_data_box').attr('data-unit'),
				_changeUnit = $(this).parents('.unit_data_box').siblings().attr('data-unit'),
				_name = $(this).attr('name'),
				_changeName = _name.replace(_unit, _changeUnit),
				_index = $(this).parents('.rows').attr('data-num');

			if (_chars.test(_value)) {
				$num = parseFloat(_value);
				_value = unitConvert($num, _unit, _changeUnit);
			}
			$('input[name="'+ _changeName +'"]').eq(_index).val(_value);
		})

		let unitConvert = function( number = 0 , $origin_unit = 'CM', $unit = 'IN'){

			let result = 0;
				number = parseFloat(number);

			if(number == 0) return result;

			if($origin_unit == 'CM' && $unit == 'IN') {
				price = number / 2.54;
			} else if($origin_unit == 'IN' && $unit == 'CM') {
				price = number * 2.54;
			}
			price = parseInt(price.mul(10000));
			result = (Math.ceil(price.div(100)).div(100)).toFixed(2);
			
			return result;
		}

		guide_obj._dataForm.on('click','.btn_submit',() => { //保存
			let _data = guide_obj._dataForm.find('#add_data_form').serializeArray()
			guide_obj.show_data(_data)
			let _curUnit = guide_obj._dataForm.find('.unit_item.current').attr('data-unit');
			guide_obj.form_obj.find(`.unit_item[data-unit='${_curUnit}']`).click();
			$('#fixed_right .btn_cancel').click();
		})
		guide_obj.form_obj.on('click','.guide_show_box .icon_edit',function(){
			let _thisUnit = $(this).parents('tbody').attr('data-unit'),
				_thisPosition = $(this).parents('tr').index();
			$('#guide_add_data').click();
			guide_obj._dataForm.find('.top_title strong').html(lang_obj.manage.app.size_guide.edit_data_title);
			guide_obj._dataForm.find('input[name=isAdd]').val(0)
			guide_obj._dataForm.find('input[name=position]').val(_thisPosition)
			guide_obj._dataForm.find('input[name=unit]').val(_thisUnit)
			setTimeout(function(){
				guide_obj._dataForm.find(`.unit_item[data-unit='${_thisUnit}']`).click();
			},320)
		})
		guide_obj.form_obj.on('click','.guide_show_box .icon_del',function(){
			let _this = $(this),
				_thisTbody = _this.parents('tbody');
			_this.parents('tr').fadeOut().remove();
			$('#button_float_tips').remove();

			let _tbodyUnit = _thisTbody.attr('data-unit'),
				_tbodyHidden = _thisTbody.find('input:hidden');
			_tbodyHidden.each(function(){
				let _thisPosition = $(this).parents('tr').index();
				$(this).attr('name','GuideData[data]['+_tbodyUnit+']['+_thisPosition+'][]')
			})
		})
		/**尺码表数据添加end */

		/**尺码表说明操作start */
		frame_obj.fixed_right($('#add_measurement_btn'), '.fixed_guide_add_measurement', () => {
			// 初始化
			guide_obj._measurementForm.find('input[name=isAdd]').val(1);
			guide_obj._measurementForm.find('input[name=position]').val(-1);
			guide_obj._measurementForm.find('input[name=title]').val('');
			guide_obj._measurementForm.find('textarea[name=desc]').val('');
		},()=>{
			guide_obj._measurementForm.find('.top_title strong').html(lang_obj.manage.app.size_guide.add_measurement_title);
		})
		$('body').on('click', '.measurement_box .edit_measurement_btn', function(){
			let _this = $(this),
				_thisTitle = _this.parents('.measurement_item').find('input.method_title').val(),
				_thisDesc = _this.parents('.measurement_item').find('input.method_desc').val(),
				_thisPosition = _this.parents('.measurement_item').index();
			if (_thisTitle && _thisDesc) { //修改
				$('#add_measurement_btn').click();
				guide_obj._measurementForm.find('.top_title strong').html(lang_obj.manage.app.size_guide.eidt_measurement_title);
				guide_obj._measurementForm.find('input[name=isAdd]').val(0);
				guide_obj._measurementForm.find('input[name=position]').val(_thisPosition);
				guide_obj._measurementForm.find('input[name=title]').val(_thisTitle);
				guide_obj._measurementForm.find('textarea[name=desc]').val(_thisDesc);
			}
		})
		guide_obj._measurementForm.on('click','.btn_submit',() => { //保存
			let	_thisTitle = guide_obj._measurementForm.find('input[name=title]').val(),
				_thisDesc = guide_obj._measurementForm.find('textarea[name=desc]').val(),
				_isAdd = guide_obj._measurementForm.find('input[name=isAdd]').val(),
				_position = guide_obj._measurementForm.find('input[name=position]').val(),
				_thisValue = {
					'title'	: _thisTitle,
					'desc'	: _thisDesc
				};
			if(global_obj.check_form(guide_obj._measurementForm.find('*[notnull]'), guide_obj._measurementForm.find('*[format]'), 1)){ return false; };
			if (_thisTitle && _thisDesc) {
				guide_obj.add_measurement(guide_obj.measurement_item,_thisValue,_isAdd,_position);
				$('#fixed_right .btn_cancel').click();
			}
		})
		$('body').on('click', '.measurement_box .del_measurement_btn' ,function(){ //删除
			let _this = $(this),
				_thisParentsObj = guide_obj.form_obj.find('.measurement_info');
			_this.parents('.measurement_item').fadeOut().remove();
			$('#button_float_tips').remove();
			_thisParentsObj.find('.measurement_item').each(function(index){
				let _num = index + 1,
					_title = $(this).find('.method_title').val();
				$(this).find('.item_title').html(_num+'、'+_title)
				$(this).find('.method_title').attr('name','MeasurementData[method]['+index+'][title]')
				$(this).find('.method_desc').attr('name','MeasurementData[method]['+index+'][desc]')
			})
			if (_thisParentsObj.find('.measurement_item').length == 0) $('.measurement_box .noInfoData').show()
		})
		frame_obj.mouse_click($('.multi_img .upload_btn, .pic_btn .edit'), 'pro', function($this){ // 图片上传
			frame_obj.photo_choice_init('PicDetail', '', 1);
		});
		/**尺码表说明操作end */

		$('.guide_unit_change .inside_menu').insideMenu();
		$('.fixed_guide_add_data .inside_menu').insideMenu();

		frame_obj.submit_form_init($('.guide_global_form'), '', '', 0, function(result){
			if (result.ret == 1) {
				global_obj.win_alert_auto_close(lang_obj.global.save_success, '', 1000, '8%');
				window.location.href = '/manage/plugins/size-guide';
				return false;
			} else {
				global_obj.win_alert_auto_close(lang_obj.global.save_fail, 'fail', 1000, '8%');
			}
		});
	}
}