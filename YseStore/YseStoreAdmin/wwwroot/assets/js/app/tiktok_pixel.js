

var tiktok_pixel_obj={
	tiktok_pixel_init: function(){
		$('#main').on('click','.plugins_app_content .list_item .list_item_title',function(){
			let $itemObj = $(this).parents('.list_item'),
				$listObj = $itemObj.find('.list_item_info');
			if ($itemObj.hasClass("open")) {
				// 已展开
				$itemObj.removeClass("open");
				$listObj.slideUp(350);
			} else {
				// 未展开
				$itemObj.addClass("open").siblings().removeClass("open");
				$listObj.slideDown(350);
				$itemObj.siblings().find(".list_item_info").slideUp(350);
			}
		});

		frame_obj.fixed_right($('#tiktok_pixel .id_add'), '.fixed_id_set', function($this){
			$('#id_set_form textarea[name=TiktokPixelId]').html('');
			let pixelid = $this.data('pixelid');
			let position = $this.data('position');
			if(pixelid){
				$('#id_set_form textarea[name=TiktokPixelId]').html(pixelid);
				$('#id_set_form input[name=position]').val(position);
			}else{
				$('#id_set_form input[name=position]').val(-1);
			}
		});

		frame_obj.del_init($('#tiktok_pixel .r_con_table'));

		frame_obj.submit_form_init($('#id_set_form'), '', function(){
			
		},0,function(result){
			global_obj.win_alert_auto_close(lang_obj.global.save_success, '', 1000, '8%');
			$('#fixed_right .btn_cancel').click();
			window.location.reload();
			return false;
		});
	},
	tiktiok_feed: function() {
		// 复制链接
		var clipboard=new ClipboardJS('.btn_copy');
		clipboard.on('success', function(e){
			alert(lang_obj.global.copy_complete);
		});
		
		$('#tiktok_pixel').on('click','.feed_updateBtn',function(){
			global_obj.div_mask();
			$('.progress_wrapper').show();
			$('#tiktokfeed_update_form .btn_submit').click();
		});
		$('#tiktok_pixel').on('click','.circle_container_close',function(){
			global_obj.div_mask(1);
			$('.progress_wrapper').hide();
			window.location.reload();
		});
		$('#tiktok_pixel').on('click','.btn_comfirm',function(){
			window.location.href = "/manage/products/products";
			return false;
		});
		$('#tiktok_pixel').on('click','.again_btn',function(){
			$('#tiktokfeed_update_form').show();
			$('.progress_wrapper .fial_box').hide();
			$('#tiktokfeed_update_form .btn_submit').click();
		});
		let num = 0;
		frame_obj.submit_form_init($('#tiktokfeed_update_form'), '', '', '', function(result){
			if(result.ret!=0){
				$('#tiktokfeed_update_form').show();
				$('.progress_completed_btn').removeClass('show');
				num += result.msg.Percent;
				frame_obj.circle_progress_bar({
					percent: num,
					processingText: result.msg.langPack.processingText,
					completedText: result.msg.langPack.completedText
					});
			}
			if(result.ret==-1){
				$('#tiktokfeed_update_form .btn_submit').click();
			}else if(result.ret==1){
				$('.progress_completed_btn').addClass('show');
			}else{
				$('#tiktokfeed_update_form').hide();
				$('.progress_wrapper .fial_box').show();
				$('.progress_wrapper .fial_box .again_btn').addClass('btn_comfirm').removeClass('again_btn').text(lang_obj.global.confirm);
			}
		});
	}
}