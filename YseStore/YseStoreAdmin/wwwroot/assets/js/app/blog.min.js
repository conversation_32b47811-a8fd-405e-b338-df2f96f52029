var blog_obj={blog_global:{del_action:"",order_action:"",init:function(){frame_obj.del_init($("#blog .r_con_table")),frame_obj.select_all($("input[name=select_all]"),$("input[name=select]"),$(".table_menu_button .del")),frame_obj.del_bat($(".table_menu_button .del"),$(".r_con_table input[name=select]"),blog_obj.blog_global.del_action),$("#blog .r_con_table .myorder_select").on("click",function(){var e,t=$(this),n=t.attr("data-num"),o=t.parents("tr").find("td:eq(0) input").val(),a=t.html(),i=$("#myorder_select_hide").html();1!=t.attr("data-status")&&(t.attr("data-status",1),t.html(i+'<span style="display:none;">'+a+"</span>"),n&&t.find("select").val(n).focus(),t.find("select").on("blur",function(){e=$(this).val(),e!=n?$.post(blog_obj.blog_global.order_action,"Id="+o+"&Number="+$(this).val(),function(n){1==n.ret&&(t.html(n.msg),t.attr("data-num",e))},"json"):t.html(t.find("span").html()),t.attr("data-status",0)}))}),$("body").on("click",".version_btn",function(){var e=$(this).attr("data-version");if(!e)return!1;var t={version:e};$.post("/manage/plugins/blog/change-version",t,function(e){1==e.ret&&(window.location.href=e.msg)},"json")}),$(".sync_btn").click(function(){let e={title:lang_obj.manage.app.blog.sync_to_new,confirmBtn:lang_obj.manage.app.blog.copy,cancelBtn:lang_obj.global.cancel};global_obj.win_alert(e,function(){setTimeout(function(){global_obj.win_loading(),frame_obj.circle_progress_bar({percent:0,processingText:lang_obj.manage.global.update_loading});let e="";e+='<input type="button" class="btn_global version_btn" value="'+lang_obj.manage.app.blog.use_new_version+'" data-version="v2" />',e+='<input type="button" class="btn_global cancel_btn" value="'+lang_obj.manage.global.got_it+'" />',e+='<input type="button" class="btn_global" id="btn_progress_keep" />',e+='<input type="hidden" name="Start" value="0" />',e+='<input type="hidden" name="WebSiteNum" value="0" />',e+='<input type="hidden" name="do_action" value="/manage/plugins/blog/sync" />',$("#box_circle_container").append(e),frame_obj.box_progress(function(e){e&&(3==e.ret?($Text=lang_obj.manage.app.blog.sync_success.replace("{blogNum}",e.msg.blogNum),$Text=$Text.replace("{categoryNum}",e.msg.blogCategoryNum),$("#box_circle_container .circle_progress_text.completed").html($Text),$("#box_circle_container .version_btn, #box_circle_container .cancel_btn").css({display:"inline-block"}),$("#box_circle_container .cancel_btn").on("click",function(){$(".win_alert").find(".win_close button").click()}),$(".sync_btn").remove()):2==e.ret&&($("#box_circle_container input[name=Start]").val(parseInt(e.msg.Start)),$("#box_circle_container input[name=WebSiteNum]").val(parseInt(e.msg.WebSiteNum)),setTimeout(function(){$("#btn_progress_keep").click()},300)))}),$("#btn_progress_keep").click()},500)},"confirm")})}},blog_set_init:function(){$(".blog_menu .btn_menu_view").click(function(){var e=$(this).parents(".blog_menu").data("url");window.open(e)}),$("#blog_edit_form").on("click",".addNav",function(){var e=$(".blog_nav"),t=e.attr("data-name"),n=e.attr("data-link");$(".blog_nav").each(function(){var e=$(this).attr("data-lang"),o=($(this).attr("delang"),"block"==$(this).find(".tab_txt").css("display")?"display:block;":"display:none;");0==$(this).find(".tab_txt").length&&(o="display:block;");var a="";a+='<div class="tab_txt tab_txt_'+e+'" style="'+o+'" lang="'+e+'">',a+='<div class="unit_input"><b>'+t+'</b><input type="text" name="name['+e+'][]" class="box_input" value="" size="10" maxlength="30" /></div> &nbsp;&nbsp;',a+='<div class="unit_input"><b>'+n+'</b><input type="text" name="link['+e+'][]" class="box_input" value="" size="30" max="150" /></div>',a+='<a class="d_del icon_delete_1" href="javascript:;"><i></i></a>',a+='<div class="blank6"></div>',a+="</div>",$(this).append(a)})}),$(".blog_nav").on("click","div a",function(){$(this).parent().remove()}),frame_obj.mouse_click($("#AdDetail .upload_btn, #AdDetail .pic_btn .edit"),"img",function(e){frame_obj.photo_choice_init("AdDetail","",1)}),frame_obj.submit_form_init($("#blog_edit_form"),"/manage/plugins/blog/set")},blog_init:function(){blog_obj.blog_global.del_action="/manage/plugins/blog/blog-del-bat",blog_obj.blog_global.order_action="/manage/plugins/blog/blog-edit-myorder",blog_obj.blog_global.init()},blog_edit_init:function(){frame_obj.mouse_click($("#PicDetail .upload_btn, #PicDetail .pic_btn .edit"),"img",function(e){frame_obj.photo_choice_init("PicDetail","",1)}),$(".blog_menu .btn_menu_view").click(function(){var e=$(this).parents(".blog_menu").data("url");window.open(e)}),$("#edit_form .choice_btn").click(function(){var e=$(this);e.children("input").is(":checked")?(e.removeClass("current"),e.children("input").attr("checked",!1)):(e.addClass("current"),e.children("input").attr("checked",!0))}),$("#edit_form").on("click",".open",function(){$(this).hasClass("close")?($(this).removeClass("close").text(lang_obj.global.open),$(".seo_hide").slideUp(300)):($(this).addClass("close").text(lang_obj.global.pack_up),$(".seo_hide").slideDown(300))}),frame_obj.multi_lang_show_all("#edit_form"),frame_obj.multi_lang_show_item("#edit_form"),frame_obj.fixed_right($("#edit_keyword"),".fixed_edit_keyword",function(e){var t=$("input[name=AId]").val();frame_obj.seo_edit_keyword({do_action:"/manage/action/seo-keyword-select",Type:"blog",field:"AId",Id:t})}),frame_obj.seo_keyword_form_submit();const e={CheckCharLength:function(e,t){e.change(function(n){let o=e.val().length;if(o>t)return e.val($.trim(e.val()).substr(0,t-1)).trigger("change"),void global_obj.win_alert_auto_close(lang_obj.manage.products.tips.text_input_max.replace("{{max}}",t),"fail",1e3,"8%")}).keyup(function(){e.trigger("change")})}};e.CheckCharLength($("textarea[name=BriefDescription_en]"),255),$("#edit_form input[name=Blog\\[AccTime\\]]").daterangepicker({singleDatePicker:!0}),frame_obj.submit_form_init($("#edit_form"),"/manage/plugins/blog/blog/")},blog_category_init:function(){blog_obj.blog_global.del_action="/manage/plugins/blog/category-del-bat",blog_obj.blog_global.init(),frame_obj.dragsort($("#blog .r_con_table tbody"),"/manage/plugins/blog/category-order","tr","a, td[data!=move_myorder]",'<tr class="placeHolder"></tr>')},blog_category_edit_init:function(){frame_obj.submit_form_init($("#blog_edit_form"),"/manage/plugins/blog/category")},blog_review_init:function(){blog_obj.blog_global.del_action="/manage/plugins/blog/review-del-bat",blog_obj.blog_global.init()},blog_review_reply_init:function(){frame_obj.submit_form_init($("#blog_review_edit_form"),"/manage/plugins/blog/review")}};