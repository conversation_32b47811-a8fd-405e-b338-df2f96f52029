var news_obj = {
    news_category_edit_init: () => {

        // 多语言框自动调整宽度
		frame_obj.multi_lang_show_all('#edit_form');
		frame_obj.multi_lang_show_item('#edit_form');

		// 修改 SEO 关键词
		frame_obj.fixed_right($('#edit_keyword'), '.fixed_edit_keyword', function($this){
			var $Id=$('input[name=CateId]').val();
			frame_obj.seo_edit_keyword({'do_action':'/manage/action/seo-keyword-select', 'Type':'news_category', 'field':'CateId', 'Id':$Id});
		});
		frame_obj.seo_keyword_form_submit();
		if (typeof(CKEDITOR) == 'object') {
			for(i in shop_config.language){
				var lang = shop_config.language[i],
					id = 'Description_'+lang;
				CKEDITOR.instances[id].on('change', function(){
					var $str = $(this)[0].getData();
					if ($str) {
						$str = $str.replace(/(<[^>]+>)|(&nbsp;)/g,'') //去掉html
						.replace(/(^\s*)|(\s*$)/g,'') //去掉前后空格
						.replace(/(\s+)|([\r\n])|([\r])|([\n])/g,' ') //多个连续空格和换行替换成一个空格
						.slice(0, 254); //截取255个字符
					}
					if ($(this)[0].name && $('#' + id).data('change') == 1) {
						$('[name="SeoDescription_'+$(this)[0].name.replace('Description_', '')+'"]').val(global_obj.htmlspecialchars_decode($str));
					}
				});
			}
		}
		if (typeof(tinymce) == 'object') {
			for (i in shop_config.language) {
				var lang = shop_config.language[i],
					id = 'Description_'+lang;
				tinymce.editors[id].on('change', function(){
					var $str = $(this)[0].getContent();
					if ($str) {
						$str = $str.replace(/(<[^>]+>)|(&nbsp;)/g,'') //去掉html
						.replace(/(^\s*)|(\s*$)/g,'') //去掉前后空格
						.replace(/(\s+)|([\r\n])|([\r])|([\n])/g,' ') //多个连续空格和换行替换成一个空格
						.slice(0, 254); //截取255个字符
					}
					if ($('#' + id).data('change') == 1) {
						$('[name="SeoDescription_'+lang+'"]').val(global_obj.htmlspecialchars_decode($str));
					}
				});
			}
		}
		var clipboard=new ClipboardJS('.btn_copy');
		clipboard.on('success', function(e){
			alert(lang_obj.global.copy_complete);
		});

		// SEO相关事件
		$('input[name=PageUrl]').on('keyup', function(e) {
			var $Key = window.event ? e.keyCode : e.which,
				$Value = $.trim($(this).val());
			if ($Key == 8 && $Value == '') {
				// 退格键 (不允许为空)
				$(this).val($('.left_container .global_container:eq(0) .rows:eq(0) .multi_lang .lang_txt:eq(0) input').val().replace(/\s+/g, '-'));
			}
		});
		$('textarea[name=PageUrl]').on('keyup', function(){
			let $prefix = $('.prefix_textarea .prefix').text(),
				$value = $(this).val(),
				$Url = $prefix + $value; 
			$(this).parents('.custom_row').find('.btn_copy').attr('data-clipboard-text', $Url);
		});

		// 更多选项(下拉)
		$('.box_basic_more').hover(function() {
			var $Top = 42;
			$(this).hasClass('box_seo_basic_more') && ($Top = 20);
			$(this).find('.drop_down').show().stop(true).animate({'top':$Top, 'opacity':1}, 250);
		}, function() {
			var $Top = 32;
			$(this).hasClass('box_seo_basic_more') && ($Top = 10);
			$(this).find('.drop_down').stop(true).animate({'top':$Top, 'opacity':0}, 100, function(){ $(this).hide(); });
		});

        // 提交保存
		frame_obj.submit_object_init($('#edit_form'), '', '', '', function(result) {
			global_obj.win_alert_auto_close(lang_obj.global.save_success, '', 500, '8%');
			setTimeout(function() {
				if (result.msg.jump) {
					window.location = result.msg.jump;
				} else {
					window.location.reload();
				}
			}, 500);
		});

    },
    news_category_init: () => {
        frame_obj.del_init($('#category .r_con_table')); // 删除提示		
		frame_obj.select_all($('input[name=select_all]'), $('input[name=select]'), $('.table_menu_button .del')); // 批量操作
		frame_obj.del_bat($('.table_menu_button .del'), $('input[name=select]'), '/manage/plugins/news/category-delete-batch'); // 批量删除
		frame_obj.dragsort($('#news_category .r_con_table tbody'), '/manage/plugins/news/order'); // 元素拖动

		// 显示产品数量
		function category_get_products_count($i, $type){
			if ($type == 'default') {
				var $len = $('input[name=CateIdAry]').length,
					$cateid = $('input[name=CateIdAry]').eq($i).val();
			} else {
				var $len = 1,
					$cateid = $('.pro_count.load').map(function(){
						return $(this).attr('data-cateid');
					}).get().join(',');
			}
			if ($cateid) {
				$.post('/manage/plugins/news/news-count', {'id':$cateid}, function(data) {
					if (data.ret == 1) {
						for (i in data.msg) {
							if (data.msg[i] == 'loading') {
								$('.pro_count[data-cateid="'+i+'"]').parent().find('.icon_edit').addClass('disabled').off('click').click(function(){
									return false;
								});
							} else {
								$('.pro_count[data-cateid="'+i+'"]').html(data.msg[i]).removeClass('load').parent().find('.icon_edit').removeClass('disabled').off('click').click(function(e){if (!e.ctrlKey) $('.fixed_loading').fadeIn();});
							}
						}						
					}
					$i++;
					if ($i < $len) category_get_products_count($i, $type);
				}, 'json');
			}
		}
		category_get_products_count(0, 'default');
		//递增定时检测产品处理进度
		function incrementTimeout(i) {
			setTimeout(function(){
				if ($('.pro_count.load').length) {
					category_get_products_count(0, 'loading');
					i = i * 2;
					incrementTimeout(i);
				}
			}, 1000 * i);	
		}
		incrementTimeout(2);
    },
	news_edit_init: () => {
		
		// 多语言框自动调整宽度
		frame_obj.multi_lang_show_all('#edit_form');
		frame_obj.multi_lang_show_item('#edit_form');

		// 图片上传
		frame_obj.main_picture_upload();
		frame_obj.upload_picture_event();

		// 时间插件
		$('input[name=EditTime]').daterangepicker({showDropdowns:true, singleDatePicker:true});

		// 修改 SEO 关键词
		frame_obj.fixed_right($('#edit_keyword'), '.fixed_edit_keyword', function($this){
			var $Id=$('input[name=NewsId]').val();
			frame_obj.seo_edit_keyword({'do_action':'/manage/action/seo-keyword-select', 'Type':'news', 'field':'NewsId', 'Id':$Id});
		});
		frame_obj.seo_keyword_form_submit();
		if (typeof(CKEDITOR) == 'object') {
			for(i in shop_config.language){
				var lang = shop_config.language[i],
					id = 'Description_'+lang;
				CKEDITOR.instances[id].on('change', function(){
					var $str = $(this)[0].getData();
					if ($str) {
						$str = $str.replace(/(<[^>]+>)|(&nbsp;)/g,'') //去掉html
						.replace(/(^\s*)|(\s*$)/g,'') //去掉前后空格
						.replace(/(\s+)|([\r\n])|([\r])|([\n])/g,' ') //多个连续空格和换行替换成一个空格
						.slice(0, 254); //截取255个字符
					}
					if ($(this)[0].name && $('#' + id).data('change') == 1) {
						$('[name="SeoDescription_'+$(this)[0].name.replace('Description_', '')+'"]').val(global_obj.htmlspecialchars_decode($str));
					}
				});
			}
		}
		var clipboard=new ClipboardJS('.btn_copy');
		clipboard.on('success', function(e){
			alert(lang_obj.global.copy_complete);
		});

        // 预览
		$('.top_tool_menu .btn_menu_view').click(function() {
			var $Url = $(this).parents('.top_tool_menu').data('url');
			window.open($Url);
		});

		// SEO相关事件
		$('input[name=PageUrl]').on('keyup', function(e) {
			var $Key = window.event ? e.keyCode : e.which,
				$Value = $.trim($(this).val());
			if ($Key == 8 && $Value == '') {
				// 退格键 (不允许为空)
				$(this).val($('.left_container .global_container:eq(0) .rows:eq(0) .multi_lang .lang_txt:eq(0) input').val().replace(/\s+/g, '-'));
			}
		});
		$('textarea[name=PageUrl]').on('keyup', function(){
			let $prefix = $('.prefix_textarea .prefix').text(),
				$value = $(this).val(),
				$Url = $prefix + $value; 
			$(this).parents('.custom_row').find('.btn_copy').attr('data-clipboard-text', $Url);
		});

		// 更多选项(下拉)
		$('.box_basic_more').hover(function() {
			var $Top = 42;
			$(this).hasClass('box_seo_basic_more') && ($Top = 20);
			$(this).find('.drop_down').show().stop(true).animate({'top':$Top, 'opacity':1}, 250);
		}, function() {
			var $Top = 32;
			$(this).hasClass('box_seo_basic_more') && ($Top = 10);
			$(this).find('.drop_down').stop(true).animate({'top':$Top, 'opacity':0}, 100, function(){ $(this).hide(); });
		});

		// 提交保存
		frame_obj.submit_object_init($('#edit_form'), '', '', '', function(result) {
			global_obj.win_alert_auto_close(lang_obj.global.save_success, '', 500, '8%');
			setTimeout(function() {
				if (result.msg.jump) {
					window.location = result.msg.jump;
				} else {
					window.location.reload();
				}
			}, 500);
		});

		frame_obj.switchery_checkbox(function(obj){
			if (obj.find('input[name=UsedMobile]').length) {
				// 是否开启移动端详细描述
				$('.mobile_description').fadeIn();
			}
		}, function(obj){
			if (obj.find('input[name=UsedMobile]').length) {
				// 是否开启移动端详细描述
				$('.mobile_description').fadeOut();
			}
		})

	},
	news_init: () => {
        frame_obj.del_init($('#news .r_con_table')); // 删除提示		
		frame_obj.select_all($('input[name=select_all]'), $('input[name=select]'), $('.table_menu_button .del')); // 批量操作
		frame_obj.del_bat($('.table_menu_button .del'), $('input[name=select]'), '/manage/plugins/news/delete-batch'); // 批量删除

		// 搜索筛选
		frame_obj.filterRight({
			"onInit": function() {
				$('.fixed_search_filter input[name=editTime]').daterangepicker({showDropdowns:true});
			},
			"onSubmit": function($obj) {
				let cateId = $obj.find("input[name=cateId]").val();
				let editTime = $obj.find("input[name=editTime]").val();
				$('.search_box input[name=cateId]').val(cateId);
				$('.search_box input[name=editTime]').val(editTime);
			}
		});
	},
}