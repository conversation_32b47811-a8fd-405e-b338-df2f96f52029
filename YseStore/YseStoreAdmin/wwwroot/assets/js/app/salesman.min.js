function loader(t,a){a=a||{};const e=$(t),n=$('\n\t<div class="loader">\n\t\t<div class="inner">\n\t\t\t<div class="loading"></div>\n\t\t\t<div class="text"></div>\n\t\t</div>\n\t</div>\n\t');let i=a.text||"";return i.length>0&&n.find(".text").text(i),{el:e,loader:n,on:function(t){e.css("position","relative"),n.width(e.outerWidth()),n.height(e.outerHeight()),n.css({position:"absolute",top:0,left:0}),e.append(n),"function"==typeof t&&t()},off:function(t){e.find(".loader").length>0&&n.remove(),"function"==typeof t&&t()}}}var salesman_obj={salesman_init:function(){if(frame_obj.select_all($("input[name=select_all]"),$("input[name=select]")),frame_obj.fixed_right($(".batch_assign"),".fixed_assign",function(t){var a="";$("input[name=select]").each(function(t,e){a+=$(e).get(0).checked?$(e).val()+"-":""}),a&&(a=a.substring(0,a.length-1)),$(".fixed_assign select[name=SalesId]").find('option[value=""]').attr("selected",!0),$(".fixed_assign").find("input[name=Id]").val(a)},function(t){var a="";if($("#salesman input[name=select]").each(function(t,e){a+=$(e).get(0).checked?$(e).val()+",":""}),!a)return global_obj.win_alert(lang_obj.global.dat_select),!1}),frame_obj.fixed_right($(".operation .assign"),".fixed_assign",function(t){var a=t.data("id");let e=t.data("salesid");e=e||"",$(".fixed_assign select[name=SalesId]").find("option[value="+e+"]").attr("selected",!0).siblings("option").attr("selected",!1),$(".fixed_assign").find("input[name=Id]").val(a)}),frame_obj.submit_form_init($("#assign_from"),"","","",function(t){1==t.ret?window.location.reload():global_obj.win_alert_auto_close(t.msg,"fail",1e3,"8%")}),frame_obj.fixed_right($(".salesman_list .btn_add_item, .salesman_list .add"),".fixed_right_add_salesman",function(t){}),frame_obj.submit_form_init($("#salesman_add"),"","","",function(t){1==t.ret?window.location.reload():global_obj.win_alert_auto_close(t.msg,"fail",1e3,"8%")}),$(".salesman_list .operation .del").click(function(){let t=$(this).data("salesid"),a={title:lang_obj.global.del_confirm,confirmBtn:lang_obj.global.del,confirmBtnClass:"btn_warn"};global_obj.win_alert(a,function(){$.post("/manage/plugins/salesman/del",{SalesId:t},function(t){1==t.ret?window.location.reload():global_obj.win_alert_auto_close(t.msg,"fail",1e3,"8%")},"json")},"confirm")}),$(".copy_link").length){var t=new ClipboardJS(".copy_link");t.on("success",function(t){alert(lang_obj.global.copy_complete)})}frame_obj.filterRight({onSubmit:function(t){let a=[];t.find("input[name=OrderStatus]:checked").each(function(t,e){a[t]=$(e).val()}),a=a.join(",");let e=[];t.find("input[name=PaymentStatus]:checked").each(function(t,a){e[t]=$(a).val()}),e=e.join(",");let n=[];t.find("input[name=ShippingStatus]:checked").each(function(t,a){n[t]=$(a).val()});let i=[];t.find("input[name=PId]:checked").each(function(t,a){i[t]=$(a).val()}),i=i.join(",");let s=[];s=t.find('input[name="SalesId[]"]').val(),$(".search_box input[name=OrderStatus]").val(a),$(".search_box input[name=PaymentStatus]").val(e),$(".search_box input[name=ShippingStatus]").val(n),$(".search_box input[name=PId]").val(i),$(".search_box input[name=SalesId]").val(s)}})},salesman_views:function(){moment($("html").processZoneOffsetData());new dateselector("#salesmanViews",{submit:async({date:t,diff:a,compare:e})=>{let n=$(".load_more"),i=0,s=n.attr("data-current");salesman_obj.get_salesman_data(i,t,s)}}),$(".dateselector").find(".btn_submit").click(),frame_obj.switchery_checkbox(function(t){let a=t.parents("tr").attr("data-id");$.get("/manage/plugins/salesman/switch-performance",{id:a,type:"open"},function(t){1==t.ret?(global_obj.win_alert_auto_close(lang_obj.global.save_success,"",1e3,"8%"),$(".dateselector").find(".btn_submit").click()):global_obj.win_alert_auto_close(lang_obj.global.save_fail,"fail",1e3,"8%")},"json")},function(t){let a=t.parents("tr").attr("data-id");$.get("/manage/plugins/salesman/switch-performance",{id:a,type:"close"},function(t){1==t.ret?(global_obj.win_alert_auto_close(lang_obj.global.save_success,"",1e3,"8%"),$(".dateselector").find(".btn_submit").click()):global_obj.win_alert_auto_close(lang_obj.global.save_fail,"fail",1e3,"8%")},"json")})},get_salesman_data:function(t,a,e){let n=$(".load_more"),s=$(".salesman_views").find("input[name=salesId]").val();if(loader($(".loader_content")).on(),n.attr("disabled"))return!1;$post={page:t,date:a,current:e,id:s},n.attr("disabled","disabled"),0==t&&$(".r_con_table").find("tbody").html("");let l=$(".salesman_views");$.post("/manage/plugins/salesman/get-salesman-data",$post,function(t){if(1==t.ret){let a=t.msg;for(k in a){let t=a[k];l.find(".data_item ."+k).html(t)}let e="",n=$(".salesman_orders_box");if(a.orders_list.length>0){for(i in n.find("table").show(),n.find(".bg_no_table_data").hide(),a.orders_list){let t=a.orders_list[i],n=1==t.isPerformance?"checked":"";e+=`<tr data-id="${t.OrderId}">\n\t\t\t\t\t\t\t\t<td nowrap>${t.OId}</td>\n\t\t\t\t\t\t\t\t<td nowrap><span class="green">${t.Email}</span><br /><span class="gory">${t.Name}</span></td>\n\t\t\t\t\t\t\t\t<td nowrap>${t.total}</td>\n\t\t\t\t\t\t\t\t<td nowrap>${t.actualTotal}</td>\n\t\t\t\t\t\t\t\t<td nowrap><span class="status ${t.iconPaymentStatus}">${t.PaymentStatus}</span></td>\n\t\t\t\t\t\t\t\t<td nowrap><span class="status ${t.iconShippingStatus}">${t.ShippingStatus}</span></td>\n\t\t\t\t\t\t\t\t<td nowrap>${t.PayTime}<div class="color_888">${t.PaymentMethod}</div></td>\n\t\t\t\t\t\t\t\t<td nowrap>\n\t\t\t\t\t\t\t\t\t<div class="switchery ${n}">\n\t\t\t\t\t\t\t\t\t\t<input type="checkbox" name="IsPerformance" value="1" ${n} />\n\t\t\t\t\t\t\t\t\t\t<div class="switchery_toggler"></div>\n\t\t\t\t\t\t\t\t\t\t<div class="switchery_inner">\n\t\t\t\t\t\t\t\t\t\t\t<div class="switchery_state_on"></div>\n\t\t\t\t\t\t\t\t\t\t\t<div class="switchery_state_off"></div>\n\t\t\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t\t</td>\n\t\t\t\t\t\t\t\t<td nowrap class="operation tar flex_item last"><a class="oper_icon icon_file button_tips" target="_blank" href="/manage/orders/orders/view?id=${t.OrderId}">${t.detailTips}</a></td>\n\t\t\t\t\t\t\t</tr>`}e&&n.find("tbody").html(e)}else n.find("table").hide(),n.find(".bg_no_table_data").show()}setTimeout(()=>{$(".loader").remove()},1e3)},"json")},orders_view_init:()=>{$(".box_my_app .item[data-type=salesman]").click(function(){let t=parseInt($("#OrderId").val()),a=$(".fixed_edit_salesman"),e="";a.length||(e+='<div class="global_container fixed_edit_salesman fixed_assign" data-width="350"><button id="btn_salesman_fixed_show"></button></div>',$("#fixed_right").append(e),frame_obj.fixed_right($("#btn_salesman_fixed_show"),".fixed_edit_salesman",t=>{$("#fixed_right").addClass("loading")})),a.find(".top_title, #assign_from").remove(),$("#btn_salesman_fixed_show").click(),$.post("/manage/plugins/salesman/fixed-right",{id:t},t=>{if(t){let a=$(t).find("#salesman_fixed_right").html();$("#fixed_right").removeClass("loading"),$(".fixed_edit_salesman").append(a),salesman_obj.salesman_fixed()}})})},salesman_fixed:()=>{frame_obj.submit_form_init($("#assign_from"),"","","",t=>{1==t.ret?global_obj.win_alert_auto_close(lang_obj.global.save_success,"",500,"8%"):global_obj.win_alert_auto_close(t.msg,"fail",1e3,"8%"),$("#fixed_right .btn_cancel").click()})}};