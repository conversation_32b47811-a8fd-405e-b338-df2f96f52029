var aliexpress_obj = {

    aliexpress_init:function(){

		//添加授权提交
		$('#store_add').submit(function(){
			var $this=$(this),
				$Name=$this.find('input[name=Name]').val();
			if(global_obj.check_form($this.find('*[notnull]'), $this.find('*[format]'), 1)){return false;};

			var wi = window.open('about:blank', '_blank');
			$.post('/manage/plugins/aliexpress/authhz-url', 'Name='+$Name, function(data){
				if(data.ret==1){
					wi.location.href=data.msg.url;
					return false;
				}
			},'json');
			return false;
		});

		//店铺重新授权
		$('.account_list').on('click', '.refresh', function (){
			var $account=$(this).parent().parent().attr('account');

			var wi = window.open('about:blank', '_blank');
			$.post('/manage/plugins/aliexpress/authhz-url', 'Account='+$account, function(data){
				if(data.ret==1){
					wi.location.href=data.msg.url;
					return false;
				}
			},'json');
			return false;
		});

		//编辑店铺名称
		var box_authorization_edit = $('.box_authorization_edit');
		$('.account_list').on('click', '.edit', function (){//修改
			var AId=$(this).parent().parent().attr('aid'),
				Name=$(this).parent().siblings('a.change_account').text();
			$('input[type=submit]', box_authorization_edit).attr('disabled', false);
			$('input[name=Name]', box_authorization_edit).val(Name);
			$('input[name=AId]', box_authorization_edit).val(AId);
			$('input[name=d]', box_authorization_edit).val('aliexpress');
			$('input[name=do_action]', box_authorization_edit).val('plugins.authorization_edit');
		});

		frame_obj.fixed_right($('.account_list .edit'), '.box_authorization_edit');
		$('#authorization_mod').submit(function(){
			var $this=$(this);
			var $Name=$this.find('input[name=Name]').val(),
				$AId=$this.find('input[name=AId]').val();

			if(global_obj.check_form($this.find('*[notnull]'), $this.find('*[format]'), 1)){return false;};

			$.post('/manage/plugins/aliexpress/authorization-edit', 'Name='+$Name+'&AId='+$AId, function(data){
				if(data.ret==1){
					window.location.reload();
				}else{
					global_obj.win_alert(data.msg);
					global_obj.div_mask(1);
				}
			},'json');

			return false;
		});

        // 批量删除产品
		frame_obj.del_bat($('.table_menu_button .del'), $('input[name=select]'), '', function(id_list){
			let params = {
				'title': lang_obj.global.del_confirm,
				'confirmBtn': lang_obj.global.del,
				'confirmBtnClass': 'btn_warn'
			};
			global_obj.win_alert(params, function(){
				$.get('/manage/plugins/aliexpress/aliexpress-products-del-bat', {group_proid:id_list}, function(data){
					if(data.ret==1){window.location.reload();}
				}, 'json');
			}, 'confirm');
			return false;
		}, lang_obj.global.dat_select);

		/***********************************开始同步产品(start)************************************/
		frame_obj.fixed_right($('a.aliexpress_product_list_sync'), '.box_aliexpress_sync', function() {
			var stop;
			let box = $('#aliexpress_sync_form .group_box')
			var status = box.data('status')
			box.find('.select_value').off().click(function(){
				if (status != 'complete') {
					box.find('.loader').css('display', 'flex')
					groupSyncTimeout(1)
				} else {
					box.find('ul').show()
				}
			})

			function groupSyncTimeout(i) {
				stop = setTimeout(function(){
					$.post('/manage/plugins/aliexpress/group-sync', {}, function(data){
						if (data.ret == 1) {
							clearTimeout(stop)
							box.find('ul').show().html(data.msg.html)
							box.find('.loader').remove()
							status = 'complete'
							box.attr('data-status', status)
						} else {
							i = 8;
							groupSyncTimeout(i);
						}
					}, 'json')
				}, 1000 * i)
			}

			$('#aliexpress_sync_form .group_box').on('click', 'li', function() {
				if (!$(this).hasClass('group')) {
					let id = $(this).data('id')
					let name = $(this).text()
					box.find('.select_value').text(name)
					box.find('input[name=GroupId]').val(id)
					$('#aliexpress_sync_form .group_box').find('ul').hide()
				}
			});

			$(document).click(function(){
				box.find('.loader').hide();
				box.find('ul').hide()
				clearTimeout(stop)
			})

			$('#aliexpress_sync_form .group_box').click(function(){
				event.stopPropagation()
			})
		}, function () {
			let valid = $('.aliexpress_product_list_sync').data('valid')
			let tips = $('.aliexpress_product_list_sync').data('tips')
			if (valid == 'expired') {
				global_obj.win_alert({
					"title":tips,
					"winBtnsClass":"hide",
					"extHtml":"<a class='g_btn g_btn_main g_is_round' href='https://marketplace.aliexpress.com/web/detail.html?articleCode=FW_GOODS-1000920613' target='_blank'>" + lang_obj.manage.app.aliexpress.buynow + "</a>"
				}, );
				return false;
			}
		});
		frame_obj.box_progress(function(data){
			if(data && data.ret==3){ //进度完成
				$('#box_circle_container .tips').hide();
				$('#box_circle_container .circle_container_close').val(lang_obj.global.confirm).show();
			}
		});
		$('#aliexpress_sync_form input.btn_submit').click(function(){
			$.post('/manage/plugins/aliexpress/aliexpress-products-sync', $('#aliexpress_sync_form').serialize(), function(data){
				$('#fixed_right .close').click();
				if(data.ret==1){
					global_obj.div_mask();
					$('.pop_form.sync_progress').css({'top':110, 'opacity':0}).show().animate({'top':'50%', 'opacity':1}, 250);
					$('#box_circle_container input[name=TaskId]').val(data.msg.TaskId);
					$('#btn_progress_keep').click();
				}else if(data.ret==-1){
					global_obj.win_alert(lang_obj.manage.products.sync.not_repeat_task);
				}else{
					global_obj.win_alert(data.msg);
				}
			},'json');
		});
		$('.circle_container_close').click(function(){ //关闭
			frame_obj.pop_form($('.pop_form.sync_progress'), 1, 1);
			window.location.reload();
		});
		/***********************************开始同步产品(end)************************************/

		/***********************************复制产品部分************************************/
		/* 复制产品分类选择弹出框 */
		$('a.aliexpress_product_list_post').on('click', function(){
			var $obj=$('.copy_products_box');
			$('.copy_products_box select[name=CateId]').find('option:selected').removeAttr('selected');
		});
		frame_obj.fixed_right($('a.aliexpress_product_list_post'), '.copy_products_box', function(){
			var id_list='';
			$('#products_sync .btn_checkbox input[type=checkbox]:checked').each(function(index, element) {
				id_list+=$(element).get(0).checked?$(element).val()+'-':'';
			});
			if(id_list) {
				id_list=id_list.substring(0,id_list.length-1);
			}
			$('#copy_edit_form').find('input[name=CheckId]').val(id_list);
			check_currency_form($('#copy_edit_form'));
		});

		//多分类按钮
		$('#btn_expand').on('click', function(){
			var obj=$(this).nextAll('.expand_list');
			var category_sel=$('#copy_edit_form select[name=CateId]').html();
			obj.append('<li><div class="box_select"><select name="ExtCateId[]" notnull>'+category_sel.replace(' selected','')+'</select></div><a class="close icon_delete_1" href="javascript:;"><i></i></a></li>');
			$('.expand_list .close').on('click', function(){
				$(this).parent().remove();
			});
		});
		$('.expand_list .close').on('click', function(){
			$(this).prev().remove();
			$(this).remove();
		});

		//提交产品复制
		var alexpress_sync_obj = {
			action: function(id_list, $CateId, $ExtCateId) {
				var $Html = '',
					$Obj = $('#copy_edit_form .progress_container_right'),
					$Number = $Obj.find('input[name=Number]').val(),
					$Currency = $Obj.parent().find('input[name=Currency]').val(),
					$Current = $Obj.find('input[name=Current]').val();
				$.post('/manage/plugins/aliexpress/copy-alexpress-to-products', {group_id:id_list, CateId:$CateId, ExtCateId:$ExtCateId, 'Number':$Number, Current:$Current, Currency: $Currency, Type:"BatchPublish"}, function(data) {
					if (data.ret == 2) {
						//继续产品资料上传
						if (data.msg.Data) {
							var Num = 0, arr = [], i, len, obj = {};
							if ($('#copy_edit_form .tbody tr').length) {
								Num = parseInt($('#copy_edit_form .progress_container_right tbody tr:eq(0) td:eq(0)').text());
							}
							for (i in data.msg.Data) {
								arr.push([data.msg.Data[i], i]);
							};
							arr.sort(function (a,b) {
								return a[0] - b[0];
							});
							len = arr.length;
							for (i = 0; i < len; i++) {
								obj[arr[i][1]] = arr[i][0];
							}
							for (k in obj) {
								$Html += '<tr class="' + (obj[k].Status == 0 ? 'error' : 'success') + '" data-id="'+obj[k].Type+'" data-type="'+obj[k].Type+'" data-pic-status="0">\
									<td nowrap="nowrap">' + k + '</td>\
									<td nowrap="nowrap"><div class="name" title="'+obj[k].Name+'">'+obj[k].Name+'</div></td>\
									<td nowrap="nowrap" data-type="message">' + lang_obj.manage.products.upload.imported + '</td>\
								</tr>';
							}
							$Obj.find('tbody').prepend($Html);
							$Obj.find('input[name=Number]').val(data.msg.Num);
							$Obj.find('input[name=Current]').val(data.msg.Cur);
							alexpress_sync_obj.action(id_list, $CateId, $ExtCateId);
						}
					} else if (data.ret == 1) {
						//产品资料上传完成
						$('.copy_products_box input.btn_submit').removeAttr('disabled');
						$('#copy_edit_form .progress_loading_right').addClass('completed').html(lang_obj.manage.products.upload.imported_all).append('<div class="box_explain">' + lang_obj.manage.products.sync.closed_reload + '</div>'); //全部导入完成
						$('#fixed_right_div_mask, .copy_products_box .close').click(function() {
							//硬性绑定页面刷新事件
							window.location.reload();
							return false;
						});
					} else {
						$('.copy_products_box input.btn_submit').removeAttr('disabled');
						global_obj.win_alert_auto_close(data.msg, 'fail', 2000, '8%');
						$('#copy_edit_form .progress_container_right').hide();
					}
				}, 'json');
			}
		}

		/* 提交产品复制 */
		frame_obj.del_bat($('.copy_products_box input.btn_submit'), $('input[name=select]'), '', function(id_list){
			$('.copy_products_box input.btn_submit').attr('disabled', 'disabled');
			var $obj=$('.copy_products_box select[name=CateId]').find('option:selected');
			if($obj.val()!=''){
				var $CateId=parseInt($obj.val());
			}else{
				global_obj.win_alert(lang_obj.manage.products.category_tips);
				global_obj.div_mask(1);
				$('.copy_products_box input.btn_submit').removeAttr('disabled');
				return false;
			}
			var $ExtCateId='0';
            $('.copy_products_box select[name=ExtCateId\\[\\]]').each(function(){
                $ExtCateId+=','+$(this).val();
            });

			$('#copy_edit_form .form_container').hide();
			$('#copy_edit_form .progress_container_right').show();

			alexpress_sync_obj.action(id_list, $CateId, $ExtCateId);
		}, lang_obj.global.dat_select);

		frame_obj.fixed_right($('.btn_publish'), '.aliexpress_currency_box', function(obj){
			var $Obj = obj.parents("tr"),
				$ProId = $Obj.data("id");
			$('#check_currency_form').find('input[name=ProId]').val($ProId);
			$('#check_currency_form').find('input[name=CheckId]').val($ProId);
			check_currency_form($('#check_currency_form'));
		})

		$('#check_currency_form').submit(function(){
			var $ProId = $('#check_currency_form').find('input[name=ProId]').val(),
				$Obj = $('#products_sync').find('tr[data-id='+$ProId+']'),
				$Currency = $('#check_currency_form').find('input[name=Currency]').val();

			$.post("/manage/plugins/aliexpress/copy-alexpress-to-products", {group_id:$ProId, Type:"Publish",Currency:$Currency}, function(data) {
				$Obj.find("td.status").html("<span class=\"status ing\">" + lang_obj.manage.products.sync.publish_used + "</span>");
				global_obj.win_alert_auto_close(lang_obj.manage.products.sync.publish_once, "", 2000, "8%");
				$('#check_currency_form').find('.btn_cancel').click();
			});

			return false;
		})

		//检验该货币是否开启
		$('select[name=currency]').on('change', function(){
			let _obj = $(this),
			_value = _obj.val(),
			_form = _obj.parents('form'),
			params = {'Currency': _value};
			check_currency_form(_form, params);
		})
		
		function check_currency_form(_form, params = {}){
			_form.find('.btn_submit').attr('disabled', 'disabled');
			params['ProId'] = _form.find('input[name=CheckId]').val();
			$.post('/manage/plugins/aliexpress/check-currency', params, function(result){
				if(result.ret == 1) {
					_form.find('.error_tips').hide().html('');
					_form.find('.btn_submit').removeAttr('disabled');
				} else {
					_form.find('.error_tips').show().html(result.msg);
					_form.find('.btn_submit').attr('disabled', 'disabled');
				}
			}, 'json')
		}

		/***********************************复制产品部分************************************/

		$('#products_sync .account_list .change_account').click(function(){
			if(!$(this).hasClass('cur')){
				global_obj.div_mask();
				global_obj.win_alert_auto_close(lang_obj.manage.products.sync_change_account, 'loading', -1);
				$.post('/manage/plugins/aliexpress/change-aliexpress-authorization-account', 'AccountId='+$(this).attr('data-id'), function(data){
					if(data.ret==1){
						window.top.location.reload();
					}else{
						data.msg && global_obj.win_alert(data.msg);
					}
					setTimeout(function(){global_obj.div_mask(1)},500);
					global_obj.win_alert_auto_close('', 'loading', 500, '', 0);
				},'json');
			}
		});
	},
}