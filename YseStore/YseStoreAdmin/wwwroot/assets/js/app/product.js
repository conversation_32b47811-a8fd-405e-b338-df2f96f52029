

var product_obj = {
	switch_init: function() {
		// 操作事件
		const FUNC = {
			Related : ($obj, $next) => {
				let $trObj = $obj.parents("tr"),
					$id = $trObj.data("id"),
					$page = parseInt($obj.find(".related_box").attr("data-page"));
				if (!$obj.find(".related_list").length || $next == 1) {
					$next == 1 && $obj.find(".related_list").addClass("loading");
					$.post("/manage/plugins/product/switch-get-related-info", {"id": $id, 'page': $page}, function(data) {
						if (data.ret == 1) {
							let html = "";
							if (data.msg && data.msg.list) {
								let params = data.msg.list;
								html += ($page == 1 ? '<div class="related_list" data-type="' + data.msg.apply + '">' : '');
									for (let key in params) {
										html += '<div class="item clean">';
										html += 	(data.msg.apply == "products" ? '<div class="item_img pic_box"><img src="' + params[key].Picture + '" /><span></span></div>' : "");
										html += 	'<div class="item_info"><div class="info_name">' + params[key].Name + '</div></div>';
										html += '</div>';
									}
									if (data.msg.next == 1) {
										html += '<a class="related_list_load_more" href="javascript:;">加载更多</a>';
									}
								html += ($page == 1 ? '</div>' : '');
							}
							if ($page == 1) {
								$obj.find(".related_box").html(html);
							} else {
								$obj.find(".related_list").append(html);
							}
							$obj.find(".related_list").removeClass("loading");
							// 翻页
							$obj.find(".related_box").attr("data-page", $page + 1);
						}
					}, "json");
				}
				// 控制显示
				let change = 0;
				let boxHeight = $obj.find(".related_container").height();
				let clickHeight = event.currentTarget.offsetTop;
				let tableHeight = $(".box_table").height();
				if (clickHeight + boxHeight > tableHeight) {
					$obj.find(".related_container").css({"top": "auto"});
					change = 1;
				}
				let thisPosition = clickHeight - parseInt($(".inside_table").css("padding-top")) - $(".inside_table .list_menu").height() - parseInt($(".box_table table").css("margin-top"));
				if (change) {
					thisPosition = thisPosition - 5;
					if (boxHeight > thisPosition) {
						// 产品框高度大于表格
						$obj.find(".related_box").css("max-height", thisPosition);
					}
				} else {
					thisPosition = thisPosition +  $obj.height() + 25;
					if (thisPosition + boxHeight > tableHeight) {
						// 产品框高度大于表格
						$obj.find(".related_box").css("max-height", tableHeight - thisPosition);
					}
				}
				$obj.addClass("current").find(".related_container").fadeIn();
			}
		}

		// 批量操作
		frame_obj.select_all($('input[name=select_all]'), $('input[name=select]'), $('.table_menu_button .del'));

		// 批量删除
		frame_obj.del_bat($('.table_menu_button .del.btn_switch_delete'), $('input[name=select]'), '/manage/plugins/product/switch-delete');
		frame_obj.del_bat($('.table_menu_button .del.btn_switch_content_delete'), $('input[name=select]'), '/manage/plugins/product/switch-content-delete');

		// 拖动排序
		if ($('#products_switch .r_con_table').length) {
			frame_obj.dragsort($('#products_switch .r_con_table tbody'), '/manage/plugins/product/switch-order');
		}

		// 添加 / 编辑
		frame_obj.fixed_box_popup({
			"clickObj": $(".btn_add_item, .btn_switch_edit"),
			"targetClass": "box_switch_edit",
			"onClick": function(e) {
				let id = parseInt(e.this.attr("data-id"));
				let apply = e.this.parents("tr").attr("data-apply");
				let name = e.this.parents("tr").find("td.name").text();
				e.target.find("input[name=id]").val(id);
				e.target.find("input[name=Name]").val(name);
				if ( id > 0) {
					e.target.find(".box_type_menu .item").not(".checked").removeClass("disabled");
				}
				if (apply) {
					e.target.find("input[name=Apply][value=" + apply + "]").parent().click();
				} else {
					e.target.find(".box_type_menu .item:eq(0)").click();
				}
				if (id > 0) {
					// 修改
					e.target.find(".title").text(lang_obj.manage.global.edit);
					e.target.find(".box_type_menu .item").not(".checked").addClass("disabled");
				} else {
					// 添加
					e.target.find(".title").text(lang_obj.global.add);
					e.target.find(".box_type_menu .item").removeClass("disabled");
				}
			}
		});

		// 适用范围
		frame_obj.box_type_menu();

		// 关联内容
		$(".box_related_info").on("click", ".related_txt", function(event) {
			// 点击触发
			let $obj = $(this).parent();
			if (!$obj.find(".related_txt i").length) return false;
			FUNC.Related($obj, 0);
		}).on("mouseleave", function() {
			// 挪开消失
			$(this).removeClass("current").find(".related_container").fadeOut();
		}).on("click", ".related_list_load_more", function() {
			let $obj = $(this).parents(".box_related_info");
			$(this).remove();
			FUNC.Related($obj, 1);
		});

		// 提交 (添加 / 编辑)
		frame_obj.submit_object_init($("#form_switch_edit"), "", "", "", function(result) {
			if (result.ret == 1) {
				global_obj.win_alert_auto_close(lang_obj.global.save_success, "", 500, "8%");
				// $(".box_switch_edit .btn_cancel").click();
				window.location.reload();
			} else {
				$("#form_switch_edit input:submit").removeAttr("disabled");
				global_obj.win_alert_auto_close(result.msg, "fail", 1000, "8%");
			}
		});

		// 设置
		frame_obj.fixed_right($("#products_switch .list_menu_button .set"), ".box_switch_set");
		$("#form_switch_set").on("click", ".set_item", function() {
			$(this).addClass("current").siblings().removeClass("current");
			$(this).find("input").prop("checked", true);
			$(this).siblings().find("input").prop("checked", false);
		});

		// 提交 (设置)
		frame_obj.submit_object_init($("#form_switch_set"), "", "", "", function(result) {
			if (result.ret == 1) {
				global_obj.win_alert_auto_close(lang_obj.global.save_success, "", 500, "8%");
				$("#form_switch_set .btn_cancel").click();
			} else {
				$("#form_switch_edit input:submit").removeAttr("disabled");
				global_obj.win_alert_auto_close(result.msg, "fail", 1000, "8%");
			}
		});
	},

	switch_content_init: function() {
		// 选择 隶属切换卡
		$(".box_switch_select .box_select").change(function() {
			let $apply = $(this).find("option:selected").data("apply");
			$(".use_products_box").hide();
			$(".use_products_box[data-apply=\"" + $apply + "\"]").show();
			$(".use_products_box[data-apply=\"" + $apply + "\"] .input").removeClass("has_error").find(".error_tips").remove();
		});
		if (parseInt($(".box_switch_select .box_select select").val()) > 0) {
			// 默认触发
			$(".box_switch_select .box_select").change();
		}

        // 提交
		frame_obj.submit_object_init($("#edit_form"), "", function() {
			let totalError = 0;
			// 隶属切换卡
			let $selectObj = $("select[name=SwitchID]").parent().parent(),
				$switchID = parseInt($("select[name=SwitchID]").val()),
				$tips = "";
			if ($switchID == 0) {
				$tips = lang_obj.manage.tips.please_select.replace("{{title}}", lang_obj.manage.app.products_switch.affiliation_switch);
			}
			if ($tips) {
				$selectObj.addClass("has_error");
				if ($selectObj.find(".error_tips").length) {
					$selectObj.find(".error_tips").text($tips);
				} else {
					$selectObj.append("<p class=\"error_tips\">" + $tips + "</p>");
				}
				totalError += 1;
			} else {
				$selectObj.removeClass("has_error").find(".error_tips").remove();
			}
			// 指定产品 / 指定分类
			let $useObj = $(".use_products_box:visible"),
				$useBoxObj = $useObj.find(".input");
			$tips = "";
			if ($useObj.length && ((($useObj.data("apply") == "Products" && $useObj.find(".select_list .btn_attr_choice").length == 0) || ($useObj.data("apply") == "Categories" && $useObj.find(".category_item").length == 0 && $useObj.find(".category_select_list .btn_attr_choice").length == 0)))) {
				let name = $useObj.data("apply") == "Products" ? "apply_products" : "apply_categories";
				$tips = lang_obj.manage.tips.please_select.replace("{{title}}", lang_obj.manage.app.products_switch[name]);
			}
			if ($tips) {
				$useBoxObj.addClass("has_error");
				if ($useBoxObj.find(".error_tips").length) {
					$useBoxObj.find(".error_tips").text($tips);
				} else {
					$useBoxObj.append("<p class=\"error_tips\">" + $tips + "</p>");
				}
				totalError += 1;
			} else {
				$useBoxObj.removeClass("has_error").find(".error_tips").remove();
			}
			if (totalError > 0) {
                return false;
            }
		}, "", function(result) {
			if (result.ret == 1) {
				global_obj.win_alert_auto_close(lang_obj.global.save_success, "", 500, "8%");
				window.location.href = "/manage/plugins/product/switch-content";
			} else {
				$("#form_switch_edit input:submit").removeAttr("disabled");
				global_obj.win_alert_auto_close(result.msg, "fail", 1000, "8%");
			}
		});

		frame_obj.switchery_checkbox(function(obj){
			if (obj.find('input[name=UsedMobile]').length) {
				// 是否开启移动端详细描述
				$('.mobile_description').fadeIn();
			}
		}, function(obj){
			if (obj.find('input[name=UsedMobile]').length) {
				// 是否开启移动端详细描述
				$('.mobile_description').fadeOut();
			}
		})

	}
}
