

var googlefeed_api_obj={
	googlefeed_api_init: function(){	
		frame_obj.global_select_box();
		
		//获取授权链接
		$('#googlefeed_api').on('click','.googlefeed_api_btn',function(){
			$.get('/manage/plugins/google/get-authorization-url',{},function(data){
				if(data.ret==1){
					window.open(data.msg, '', 'width=1000,height=500');
					return false;
				}else{
					global_obj.win_alert(data.msg);
				}
			},'json');
			return false;
		});
		
		//更换账号
		$('#googlefeed_api').on('click', '.change_google_account',function(){
			global_obj.win_alert(lang_obj.manage.app.googlefeed_api.change_account_tips, function(){
				$.get('/manage/plugins/google/change-authorization',{},function(data){
					if(data.ret==1){
						location.href = data.msg;
					}
				},'json');
			}, 'confirm');
		})

		//选择账号
		$('#googlefeed_api').on('click', '.btn_select_account',function(){
			if ($('input[name=merchantId]').val()) {
				$.post('/manage/plugins/google/select-account',{MerchantId:$('input[name=merchantId]').val()},function(data){
					if(data.ret==1){
						location.href = '/manage/plugins/google/feed?step=second';
						return false;
					}
				},'json');
				return false;			
			} else {
				$('input[name=merchantId]').siblings('.imitation_select').css('border', '1px solid red');				
			}
		});

		frame_obj.box_type_menu(function(e){
			if (e.find('input').attr('name') == 'SyncProductType') {
				//同步产品范围
				var radio_pro_category = e.find('input[name=SyncProductType]').val();
				if(radio_pro_category==1){
					$('#api_setting .select_pro_category').attr('parent_null', '1').find('select').attr('parent_null', '1').attr('notnull','notnull');
					$('#api_setting .select_pro_category').find('#SelectCategory option[value=]').removeAttr('selected');
				}else{
					$('#api_setting .select_pro_category').removeAttr('parent_null').find('select').removeAttr('parent_null').removeAttr('notnull');
					$('#api_setting .select_pro_category').find('#SelectCategory option[value=]').attr('selected','selected');
				}
			}
		});

		//国家-语言-货币
		var currencyObj = {};
		$('.selected_currency_box select').each(function(){
			//记录每个语言对应的货币
			var c_val = $(this).val(),
				c_key = $(this).attr('data-country');
			currencyObj[c_key] = c_val;
		});

		$('.selected_currency_box').on('change', 'select', function(){
			//切换货币记录起来 用于切换国家时重新选择对应货币
			var c_val = $(this).val(),
				c_key = $(this).attr('data-country');
			currencyObj[c_key] = c_val;
		});

		//选择国家
		frame_obj.fixed_right($('.api_select_country'), '.google_country', '', function(){
			$('.google_country .country_box .input_checkbox_box').removeClass('checked').prop('checked', false)
			$('#api_setting input[name^=TargetCountry_Language]').each(function(){
				$('.google_country .country_box .input_checkbox_box input[value="' + $(this).val() + '"]').prop('checked', true).parent().parent().addClass('checked')
			});
		});
		$('#google_country_form').on('click','.input_checkbox_box',function(e){			
			if ($(this).hasClass('checked') && $('#google_country_form .input_checkbox_box.checked').length == 1) {
				global_obj.win_alert_auto_close(lang_obj.manage.app.googlefeed_api.min_country, 'fail', 1000, '8%');
				return false;
			}
		});
		$('.google_country .btn_submit').click(function(){
			var all_count_html = '',
				count_value = $(this).find('input[name^=TargetCountry_Language]').val(),
				currency_template = $('.selected_currency_template').html();
			$('#api_setting input[name^=TargetCountry_Language]').remove();
			$('.selected_currency_box').html('');
			$('#google_country_form .input_checkbox_box.checked').each(function(){
				$('.selected_currency_box').append(currency_template.replace('%CountryLanguage%', $(this).text()).replace(/%CurrencyKey%/g, $(this).find('input[name^=TargetCountry_Language]').val()));
				all_count_html += $(this).text();
				all_count_html += ', ';
				$('#api_setting input[name=do_action]').before('<input type="hidden" name="TargetCountry_Language[]" value="' + $(this).find('input[name^=TargetCountry_Language]').val() + '">');
			});
			if (all_count_html) all_count_html = all_count_html.substring(0, all_count_html.length - 2);
			$('.selected_currency_box select').each(function(){
				var c_key = $(this).attr('data-country');
				$(this).find('option[value=' + currencyObj[c_key] + ']').prop('selected', true);
			});
			$('#api_setting .selected_country_txt').html(all_count_html);
			$('.google_country .close').trigger('click')
		});

		//完成设置
		frame_obj.submit_form_init($('#api_setting'), '', function(){
			var TargetCountry_Language_val = $('#api_setting input[name=TargetCountry_Language]').val();
			if(TargetCountry_Language_val==''){
				global_obj.win_alert_auto_close(lang_obj.manage.app.googlefeed_api.select_country, 'fail', 1000, '8%');
				return false;
			}
		}, 0, function(data){
			var $Form=$('#api_setting');
			if(data.ret==1){
				location.href = data.msg;
			}
			return false;
		});

		frame_obj.switchery_checkbox(function(obj){
			$('#googlefeed .sync_time').addClass('show');
			$.post('/manage/plugins/google/start-sync', {}, function(data){
				if(data.ret==1){
					global_obj.win_alert_auto_close(data.msg.alert_tips, '', 1000, '8%');
				}
			}, 'json');
		}, function(obj){
			$('#googlefeed .sync_time').removeClass('show');
			$.post('/manage/plugins/google/start-sync', {}, function(data){
				if(data.ret==1){
					global_obj.win_alert_auto_close(data.msg.alert_tips, '', 1000, '8%');
				}
			}, 'json');
		},'.sync_status_btn');
	},

	googlefeed_index_init:function(){
		// 复制链接
		let clipboard = new ClipboardJS('.btn_copy')
		clipboard.on('success', function (e) {
			alert(lang_obj.global.copy_complete)
		})
	},

	googlefeed_edit:function(){
		$('.input_radio_box').click(function(){
			if($(this).hasClass('checked')){
				$(this).removeClass('checked').find('input').prop('checked', false);
				return false;
			}
		});
		frame_obj.fixed_right($('.btn_google_category'), '.google_category');
		$('.google_category select[name=TopCateId]').change(function(){
			var $val = $(this).val(),
			    html='<option value="0">'+lang_obj.global.selected+'</option>';
			if($val == 0){
				$('.google_category select[name=SecCateId]').html(html);
			}else{
				$.post('/manage/plugins/google/sec-google-category', {TopCateId:$val}, function(data){
					for(i=0;i<data.msg.length;i++){
						html+='<option value ="'+data.msg[i].id+'">'+data.msg[i].name+'</option>';
					}
					$('.google_category select[name=SecCateId]').html(html);
				}, 'json');
			}
		});
		frame_obj.submit_form_init($('#google_category_form'), '', '', '', function(data){
			var $Top = $('.google_category select[name=TopCateId]'),
				$Sec = $('.google_category select[name=SecCateId]'),
				$cate = '';
			if($Top.val()==0 && $Sec.val()==0){
				$('.google_category_item .status_1').addClass('hide');
				$('.google_category_item .status_0').removeClass('hide');
				$('.btn_google_category').text($('.btn_google_category').attr('data-no'));
			}else{
				$cate = $Top.find('option:selected').text();
				if($Sec.val()>0) $cate+='&nbsp;&nbsp;>&nbsp;&nbsp;'+$Sec.find('option:selected').text();
				$('.google_category_item .status_1 span').html($cate);
				$('.google_category_item .status_1').removeClass('hide');
				$('.google_category_item .status_0').addClass('hide');
				$('.btn_google_category').text($('.btn_google_category').attr('data-yes'));
				if($Top.val()==166) $('.apparel_box').removeClass('hide'); //服饰类产品有颜色 尺寸 年龄段 性别属性
				else $('.apparel_box').addClass('hide');
			}
			$('#fixed_right .close').click();
		});
		frame_obj.submit_form_init($('#googlefeed_edit_form'), '/manage/plugins/google/feed-list');
	},

	googlefeed_succrss:function(){
		window.opener && window.opener.submitPlatformAuth && window.opener.submitPlatformAuth();

		function closeWindow(num){
			if(num--==0){
				window.opener=null;
				window.open('','_self');
				window.close();
			}else{
				setTimeout(function(){closeWindow(num);}, 1000);
			}
		}
		closeWindow(2);
	}
}