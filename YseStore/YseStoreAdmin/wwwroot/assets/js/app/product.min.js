var product_obj={switch_init:function(){const e={Related:(e,t)=>{let i=e.parents("tr"),a=i.data("id"),s=parseInt(e.find(".related_box").attr("data-page"));e.find(".related_list").length&&1!=t||(1==t&&e.find(".related_list").addClass("loading"),$.post("/manage/plugins/product/switch-get-related-info",{id:a,page:s},function(t){if(1==t.ret){let i="";if(t.msg&&t.msg.list){let e=t.msg.list;i+=1==s?'<div class="related_list" data-type="'+t.msg.apply+'">':"";for(let a in e)i+='<div class="item clean">',i+="products"==t.msg.apply?'<div class="item_img pic_box"><img src="'+e[a].Picture+'" /><span></span></div>':"",i+='<div class="item_info"><div class="info_name">'+e[a].Name+"</div></div>",i+="</div>";1==t.msg.next&&(i+='<a class="related_list_load_more" href="javascript:;">加载更多</a>'),i+=1==s?"</div>":""}1==s?e.find(".related_box").html(i):e.find(".related_list").append(i),e.find(".related_list").removeClass("loading"),e.find(".related_box").attr("data-page",s+1)}},"json"));let n=0,l=e.find(".related_container").height(),o=event.currentTarget.offsetTop,r=$(".box_table").height();o+l>r&&(e.find(".related_container").css({top:"auto"}),n=1);let _=o-parseInt($(".inside_table").css("padding-top"))-$(".inside_table .list_menu").height()-parseInt($(".box_table table").css("margin-top"));n?(_-=5,l>_&&e.find(".related_box").css("max-height",_)):(_=_+e.height()+25,_+l>r&&e.find(".related_box").css("max-height",r-_)),e.addClass("current").find(".related_container").fadeIn()}};frame_obj.select_all($("input[name=select_all]"),$("input[name=select]"),$(".table_menu_button .del")),frame_obj.del_bat($(".table_menu_button .del.btn_switch_delete"),$("input[name=select]"),"/manage/plugins/product/switch-delete"),frame_obj.del_bat($(".table_menu_button .del.btn_switch_content_delete"),$("input[name=select]"),"/manage/plugins/product/switch-content-delete"),$("#products_switch .r_con_table").length&&frame_obj.dragsort($("#products_switch .r_con_table tbody"),"/manage/plugins/product/switch-order"),frame_obj.fixed_box_popup({clickObj:$(".btn_add_item, .btn_switch_edit"),targetClass:"box_switch_edit",onClick:function(e){let t=parseInt(e.this.attr("data-id")),i=e.this.parents("tr").attr("data-apply"),a=e.this.parents("tr").find("td.name").text();e.target.find("input[name=id]").val(t),e.target.find("input[name=Name]").val(a),t>0&&e.target.find(".box_type_menu .item").not(".checked").removeClass("disabled"),i?e.target.find("input[name=Apply][value="+i+"]").parent().click():e.target.find(".box_type_menu .item:eq(0)").click(),t>0?(e.target.find(".title").text(lang_obj.manage.global.edit),e.target.find(".box_type_menu .item").not(".checked").addClass("disabled")):(e.target.find(".title").text(lang_obj.global.add),e.target.find(".box_type_menu .item").removeClass("disabled"))}}),frame_obj.box_type_menu(),$(".box_related_info").on("click",".related_txt",function(t){let i=$(this).parent();if(!i.find(".related_txt i").length)return!1;e.Related(i,0)}).on("mouseleave",function(){$(this).removeClass("current").find(".related_container").fadeOut()}).on("click",".related_list_load_more",function(){let t=$(this).parents(".box_related_info");$(this).remove(),e.Related(t,1)}),frame_obj.submit_object_init($("#form_switch_edit"),"","","",function(e){1==e.ret?(global_obj.win_alert_auto_close(lang_obj.global.save_success,"",500,"8%"),window.location.reload()):($("#form_switch_edit input:submit").removeAttr("disabled"),global_obj.win_alert_auto_close(e.msg,"fail",1e3,"8%"))}),frame_obj.fixed_right($("#products_switch .list_menu_button .set"),".box_switch_set"),$("#form_switch_set").on("click",".set_item",function(){$(this).addClass("current").siblings().removeClass("current"),$(this).find("input").prop("checked",!0),$(this).siblings().find("input").prop("checked",!1)}),frame_obj.submit_object_init($("#form_switch_set"),"","","",function(e){1==e.ret?(global_obj.win_alert_auto_close(lang_obj.global.save_success,"",500,"8%"),$("#form_switch_set .btn_cancel").click()):($("#form_switch_edit input:submit").removeAttr("disabled"),global_obj.win_alert_auto_close(e.msg,"fail",1e3,"8%"))})},switch_content_init:function(){$(".box_switch_select .box_select").change(function(){let e=$(this).find("option:selected").data("apply");$(".use_products_box").hide(),$('.use_products_box[data-apply="'+e+'"]').show(),$('.use_products_box[data-apply="'+e+'"] .input').removeClass("has_error").find(".error_tips").remove()}),parseInt($(".box_switch_select .box_select select").val())>0&&$(".box_switch_select .box_select").change(),frame_obj.submit_object_init($("#edit_form"),"",function(){let e=0,t=$("select[name=SwitchID]").parent().parent(),i=parseInt($("select[name=SwitchID]").val()),a="";0==i&&(a=lang_obj.manage.tips.please_select.replace("{{title}}",lang_obj.manage.app.products_switch.affiliation_switch)),a?(t.addClass("has_error"),t.find(".error_tips").length?t.find(".error_tips").text(a):t.append('<p class="error_tips">'+a+"</p>"),e+=1):t.removeClass("has_error").find(".error_tips").remove();let s=$(".use_products_box:visible"),n=s.find(".input");if(a="",s.length&&("Products"==s.data("apply")&&0==s.find(".select_list .btn_attr_choice").length||"Categories"==s.data("apply")&&0==s.find(".category_item").length)){let e="Products"==s.data("apply")?"apply_products":"apply_categories";a=lang_obj.manage.tips.please_select.replace("{{title}}",lang_obj.manage.app.products_switch[e])}if(a?(n.addClass("has_error"),n.find(".error_tips").length?n.find(".error_tips").text(a):n.append('<p class="error_tips">'+a+"</p>"),e+=1):n.removeClass("has_error").find(".error_tips").remove(),e>0)return!1},"",function(e){1==e.ret?(global_obj.win_alert_auto_close(lang_obj.global.save_success,"",500,"8%"),window.location.href="/manage/plugins/product/switch-content"):($("#form_switch_edit input:submit").removeAttr("disabled"),global_obj.win_alert_auto_close(e.msg,"fail",1e3,"8%"))}),frame_obj.switchery_checkbox(function(e){e.find("input[name=UsedMobile]").length&&$(".mobile_description").fadeIn()},function(e){e.find("input[name=UsedMobile]").length&&$(".mobile_description").fadeOut()})}};