var facebookfeed_obj={facebookfeed_init:function(){var e=new ClipboardJS(".btn_copy");e.on("success",function(e){alert(lang_obj.global.copy_complete)}),$("#facebookfeed").on("click",".feed_updateBtn",function(){global_obj.div_mask(),$(".progress_wrapper").show(),$("#facebookfeed_update_form .btn_submit").click()}),$("#facebookfeed").on("click",".circle_container_close",function(){global_obj.div_mask(1),$(".progress_wrapper").hide(),window.location.reload()}),$("#facebookfeed").on("click",".btn_comfirm",function(){return window.location.href="/manage/products/products",!1}),$("#facebookfeed").on("click",".again_btn",function(){$("#facebookfeed_update_form").show(),$(".progress_wrapper .fial_box").hide(),$("#facebookfeed_update_form .btn_submit").click()});let o=0;frame_obj.submit_form_init($("#facebookfeed_update_form"),"","","",function(e){0!=e.ret&&($("#facebookfeed_update_form").show(),$(".progress_completed_btn").removeClass("show"),o+=e.msg.Percent,frame_obj.circle_progress_bar({percent:o,processingText:e.msg.langPack.processingText,completedText:e.msg.langPack.completedText})),-1==e.ret?$("#facebookfeed_update_form .btn_submit").click():1==e.ret?$(".progress_completed_btn").addClass("show"):($("#facebookfeed_update_form").hide(),$(".progress_wrapper .fial_box").show(),$(".progress_wrapper .fial_box .again_btn").addClass("btn_comfirm").removeClass("again_btn").text(lang_obj.global.confirm))})}};