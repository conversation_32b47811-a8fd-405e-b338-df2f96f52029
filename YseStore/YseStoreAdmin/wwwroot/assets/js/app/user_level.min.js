function loader(e,t){t=t||{};const i=$(e),n=$('\n\t<div class="loader">\n\t\t<div class="inner">\n\t\t\t<div class="loading"></div>\n\t\t\t<div class="text"></div>\n\t\t</div>\n\t</div>\n\t');let o=t.text||"";return o.length>0&&n.find(".text").text(o),{el:i,loader:n,on:function(e){i.css("position","relative"),n.width(i.outerWidth()),n.height(i.outerHeight()),n.css({position:"absolute",top:0,left:0}),i.append(n),"function"==typeof e&&e()},off:function(e){i.find(".loader").length>0&&n.remove(),"function"==typeof e&&e()}}}var user_level_obj={init:()=>{frame_obj.del_init($(".r_con_table"));let e=$("#edit_shipping_template_form");frame_obj.fixed_right($(".btn_add_level"),".box_level_update",function(t){let i=t.data("lid");e.find("input[name=LId]").val(i),$.post("/manage/plugins/user-level/data",{id:i},function(t){e.find("input[name=Name_en]").val(t.Name_en),e.find("input[name=FullPrice]").val(t.FullPrice),e.find("input[name=Discount]").val(t.Discount),e.find(".full_tips").find("span").html(t.tips);let i=e.find('input[name="FreeShipping"]').parents(".switchery"),n=i.hasClass("checked")?1:0;t.IsFreeShipping!=n&&i.trigger("click"),t.Name_en&&t.FullPrice&&t.Discount&&e.find(".full_tips").show()},"json")}),frame_obj.fixed_right($(".btn_set_price_show"),".box_set_price_show"),frame_obj.submit_form_init($("#form_set_price_show"),"","","",function(e){global_obj.win_alert_auto_close(e.msg,"",1e3,"8%"),$(".box_set_price_show .close").trigger("click")}),e.find("input[name=Name_en]").change(function(){let t=$(this).val();$("#level_level").text(t),e.find(".full_tips").show()}),e.find("input[name=FullPrice]").change(function(){let t=$(this).val(),i=e.find("input[name=symbol]").val();$("#level_price").text(i+t),e.find(".full_tips").show()}),e.find("input[name=Discount]").change(function(){let t=$(this).val();$("#level_discount").text(t+"% off"),e.find(".full_tips").show()}),frame_obj.switchery_checkbox(t=>{t.find('input[name="FreeShipping"]').length&&($("#level_free").text(lang_obj.manage.app.user_level.free_ship_tips),e.find(".full_tips").show())},e=>{e.find('input[name="FreeShipping"]').length&&$("#level_free").text("")}),frame_obj.submit_form_init($("#edit_shipping_template_form"),"","","",function(e){1==e.ret?window.location.reload():global_obj.win_alert_auto_close(e.msg,"fail",1e3,"8%")})},user_list_init:()=>{frame_obj.select_all($("input[name=select_all]"),$("input[name=select]")),frame_obj.switchery_checkbox(),frame_obj.fixed_right($(".bat_edit_level, .edit_level"),".edit_level_box",e=>{let t=e.hasClass("bat_edit_level")?"batch":"alone";user_level_obj.set_form(e,$(".edit_level_box"),t)}),user_level_obj.user_level_fixed(),frame_obj.check_amount($("#user_level")),frame_obj.fixed_right($("#user_level .edit_points"),".edit_points_box",function(e){let t=e.attr("data-points"),i=e.parents("tr").attr("data-id");$('.edit_points_box input[name="Points"]').val(t),$('.edit_points_box input[name="UserId"]').val(i)}),frame_obj.submit_form_init($("#edit_points_form"),"","","",function(e){1==e.ret?window.location.reload():global_obj.win_alert_auto_close(e.msg,"fail",1e3,"8%")})},set_form:(e,t,i)=>{let n=0,o=0;if(t.find(".list .input_radio_box").eq(0).click(),$("select[name=Level]").find("option[value=0]").prop("selected",!0).siblings().prop("selected",!1),"alone"==i){o=1,n=e.parents("tr").attr("data-id");let t=$("#edit_level_form"),i=e.attr("data-type"),l=e.attr("data-lid"),a=e.attr("data-level-title"),s=e.attr("data-level-discount"),_=parseInt(e.attr("data-level-free")||0),r=parseInt(e.attr("data-free")||0);$("#user_level .input_radio_side_box[data-type="+i+"]").click(),$("select[name=Level]").find("option[value="+l+"]").prop("selected",!0).siblings().prop("selected",!1),t.find(".level_title").text(a),t.find(".level_discount").text(s);let d=t.find('.benefits_item[data-type="discount"]');s>0?d.find(".empty").hide().siblings(".full").show():d.find(".empty").show().siblings(".full").hide();let p=t.find('.benefits_item[data-type="free"]');_?p.find(".switchery").hide().siblings("div").show():p.find(".switchery").show().siblings("div").hide();let u=t.find('input[name="FreeShipping"]').parents(".switchery"),f=u.hasClass("checked")?1:0;r!=f&&u.trigger("click")}else if("batch"==i){let e=$("#user_level input[name=select]"),t=e.map(function(){if($(this).is(":checked"))return $(this).val()}).get();for($i=0;$i<t.length;$i++){if(!t[$i])return!0;n+=","+t[$i],o++}}else if("user"==i){o=1,n=parseInt($("#UserId").val());let e=t.find(".user_info"),i=e.attr("data-type"),l=e.attr("data-lid"),a=e.attr("data-level-title"),s=e.attr("data-level-discount"),_=parseInt(e.attr("data-level-free")||0),r=parseInt(e.attr("data-free")||0);t.find(".input_radio_side_box[data-type="+i+"]").click(),$("select[name=Level]").find("option[value="+l+"]").prop("selected",!0).siblings().prop("selected",!1),t.find(".level_title").text(a),t.find(".level_discount").text(s);let d=t.find('.benefits_item[data-type="discount"]');s>0?d.find(".empty").hide().siblings(".full").show():d.find(".empty").show().siblings(".full").hide();let p=t.find('.benefits_item[data-type="free"]');_?p.find(".switchery").hide().siblings("div").show():p.find(".switchery").show().siblings("div").hide();let u=t.find('input[name="FreeShipping"]').parents(".switchery"),f=u.hasClass("checked")?1:0;r!=f&&u.trigger("click")}t.find("input[name=UserId]").val(n);let l=lang_obj.manage.app.user_level.tips.replace("{Num}",o);t.find(".level_tips span").html(l),o>1||"batch"==i?t.find(".level_tips").show():t.find(".level_tips").hide()},user_level_fixed:()=>{$("#edit_level_form").on("click",".list .input_radio_box",function(){let e=$(this).find("input").val(),t=$("#edit_level_form .level_box");"auto"==e?(t.find('div[data-type="auto"]').show(),t.find('div[data-type="manual"]').hide()):(t.find('div[data-type="auto"]').hide(),t.find('div[data-type="manual"]').show())}).on("change",'select[name="Level"]',function(){let e=$(this),t=e.val(),i=e.find(`option[value="${t}"]`),n=i.attr("data-discount")||0,o=parseInt(i.attr("data-free")||0),l=$("#edit_level_form");l.find(".level_discount").text(n);let a=l.find('.benefits_item[data-type="discount"]');n>0?a.find(".empty").hide().siblings(".full").show():a.find(".empty").show().siblings(".full").hide();let s=l.find('.benefits_item[data-type="free"]');o?s.find(".switchery").hide().siblings("div").show():s.find(".switchery").show().siblings("div").hide()}),frame_obj.submit_form_init($("#edit_level_form"),"","","",e=>{1==e.ret?window.location.reload():global_obj.win_alert_auto_close(e.msg,"fail",1e3,"8%")})},user_view_init:()=>{$(".box_my_app .item[data-type=user_level]").click(function(){let e=parseInt($("#UserId").val()),t=$(".fixed_edit_level_box"),i="";t.length||(i+='<div class="global_container fixed_edit_level_box fixed_assign" data-width="420"><button id="btn_user_level_show"></button><div class="fixed_edit_level_html"></div></div>',$("#fixed_right").append(i),frame_obj.fixed_right($("#btn_user_level_show"),".fixed_edit_level_box",e=>{$("#fixed_right").addClass("loading")})),t.find(".fixed_edit_level_html").html(""),$("#btn_user_level_show").click(),$(".fixed_edit_level_box").off("click").on("click",".user_menu .m_list",function(){let e=$(this).attr("data-type");$(".fixed_edit_level_box .user_menu .m_list").removeClass("cur"),$(this).addClass("cur"),$(".fixed_edit_level_box .user_content").hide(),$(`.fixed_edit_level_box .user_content[data-type="${e}"]`).show()}),$.post("/manage/plugins/user-level/fixed-right-user",{id:e},e=>{e&&($("#fixed_right").removeClass("loading"),$(".fixed_edit_level_box .fixed_edit_level_html").html(e),$(".fixed_edit_level_box .user_menu .m_list:eq(0)").trigger("click"),user_level_obj.user_level_fixed(),user_level_obj.set_form({},$(".fixed_edit_level_box"),"user"),frame_obj.submit_form_init($("#edit_points_form"),"","","",e=>{1==e.ret?window.location.reload():global_obj.win_alert_auto_close(e.msg,"fail",1e3,"8%")}))})})},points_init:()=>{frame_obj.switchery_checkbox(e=>{$.post("/manage/plugins/user-level/points-switch",{isUsed:1},t=>{global_obj.win_alert_auto_close(t.msg,"",1e3,"8%");let i=e.parents(".points_item");i.find(".status_pill").addClass("opend").text(lang_obj.manage.global.switch_on)},"json")},e=>{$.post("/manage/plugins/user-level/points-switch",{isUsed:0},t=>{global_obj.win_alert_auto_close(t.msg,"",1e3,"8%");let i=e.parents(".points_item");i.find(".status_pill").removeClass("opend").text(lang_obj.manage.global.switch_off)},"json")}),frame_obj.box_type_menu(),frame_obj.fixed_right($("#btn_return_points"),".box_return_points"),frame_obj.submit_form_init($("#form_return_points"),"","","",e=>{global_obj.win_alert_auto_close(e.msg,"",1e3,"8%"),$(".box_return_points .close").trigger("click")}),frame_obj.fixed_right($("#btn_validity_points"),".box_validity_points"),frame_obj.submit_form_init($("#form_validity_points"),"","","",e=>{global_obj.win_alert_auto_close(e.msg,"",1e3,"8%"),$(".box_validity_points .close").trigger("click")}),$("#form_validity_points").on("click",".input_radio_box",function(){let e=$(this).find("input").val(),t=$("#form_validity_points .box_expiration");"permanent"==e?t.hide().find('input[name="days"]').removeAttr("notnull"):t.show().find('input[name="days"]').attr("notnull","notnull")})},points_get_init:()=>{frame_obj.switchery_checkbox(e=>{let t=e.parents(".global_container"),i=t.attr("data-type");$.post("/manage/plugins/user-level/points-get-switch",{type:i,isUsed:1},t=>{global_obj.win_alert_auto_close(t.msg,"",1e3,"8%"),e.parents(".points_get_title").next(".points_get_content").removeClass("hide")},"json")},e=>{let t=e.parents(".global_container"),i=t.attr("data-type");$.post("/manage/plugins/user-level/points-get-switch",{type:i,isUsed:0},t=>{global_obj.win_alert_auto_close(t.msg,"",1e3,"8%"),e.parents(".points_get_title").next(".points_get_content").addClass("hide")},"json")}),$(".points_get .btn_preview").click(function(){let e=$(this).attr("data-type");console.log(e),"credit-card"==e?frame_obj.pop_form($(".pop_credit_card"),0,1):frame_obj.pop_form($(".pop_credit_register"),0,1)}),$(".pop_credit_card .tab_head li").click(function(){let e=$(this).index();$(".pop_credit_card .tab_head li").removeClass("current").eq(e).addClass("current"),$(".pop_credit_card .preview_box .preview_img").hide().eq(e).show()}),frame_obj.box_type_menu(),$("#points_get_order_form input[name=amount]").keyup(function(){$(this).val()<=0?$("#points_get_order_form input[name=amount]").css("border","1px solid red"):$("#points_get_order_form input[name=amount]").removeAttr("style")}),frame_obj.submit_form_init($("#points_get_order_form"),"",()=>{if($("#points_get_order_form input[name=amount]").val()<=0)return $("#points_get_order_form input[name=amount]").focus().css("border","1px solid red"),!1}),frame_obj.submit_form_init($("#points_get_register_form")),frame_obj.submit_form_init($("#points_get_newsletter_form"))},points_use_init:()=>{frame_obj.del_init($("#user_level .r_con_table")),frame_obj.fixed_box_popup({clickObj:$(".btn_add_points, .btn_add_item"),targetClass:"box_points_use_edit"})},points_use_edit_init:()=>{const e={form:$("#points_use_edit_form")};frame_obj.box_type_menu(function(e){var t=e.parent().next(".box_type_menu_content");if(e.find('input[name="CouponType"]').length){var i=parseInt(e.find('input[name="CouponType"]').val());t.find(".item input").attr("disabled","disabled"),t.find(".item:eq("+i+") input").removeAttr("disabled")}e.find('input[name="UseCustomer"]').length&&("all"==e.find('input[name="UseCustomer"]').val()?$(".pro_detail_show").show():$(".pro_detail_show").hide()),e.find('input[name="ValidityType"]').length&&("fixed"==e.find('input[name="ValidityType"]').val()?($(".box_validity").addClass("nowrap"),t.find("input[name=StartTime], input[name=EndTime]").attr("notnull","notnull"),t.find("input[name=Duration]").removeAttr("notnull")):($(".box_validity").removeClass("nowrap"),t.find("input[name=StartTime], input[name=EndTime]").removeAttr("notnull"),t.find("input[name=Duration]").attr("notnull","notnull")))}),$(".checkbox_max_amount .input_checkbox_box").click(function(){$(this).hasClass("checked")?$(".box_max_amount").hide():$(".box_max_amount").show()}),$("#points_use_edit_form").on("keyup","input[name=points], input[name=UseLimit]",function(){let e=parseInt($("#points_use_edit_form input[name=points]").val()),t=parseInt($("#points_use_edit_form input[name=UseLimit]").val());e>0?$("#points_use_edit_form input[name=points]").removeAttr("style"):$("#points_use_edit_form input[name=points]").css("border","1px solid red");let i=t.div(e);i=parseInt(i.mul(1e4)),i=Math.floor(i.div(100)).div(100).toFixed(2),isNaN(i)&&(i=0),$("#deduction_amount").text(i)}),$(".useproducts .box_type_menu .item").on("click",function(){let e=$(this).find("input[type=radio]").val();$(".use_products_box").hide(),$('.use_products_box[data-value="'+e+'"]').show()}),e.form.on("click",".choose_item",function(e){e.stopPropagation(),$(this).addClass("current").find(".btn_radio").addClass("current").find("input").prop("checked",!0),$(this).siblings().removeClass("current").find(".btn_radio").removeClass("current").find("input").prop("checked",!1)}),frame_obj.submit_form_init(e.form,"/manage/plugins/user-level/points-use",()=>{var e=parseInt($("input[name=CouponType]:checked").val()),t=parseInt($("input[name=ConditionType]:checked").val()),i=parseFloat($("input[name=CouponTypeValue]:eq(1)").val()),n=parseFloat($("input[name=ConditionPrice]").val());return n<i&&1==e&&0==t?(global_obj.win_alert_auto_close(lang_obj.manage.sales.sales_tips,"await",1e3,"8%"),$("input[name=CouponTypeValue]:eq(1)").focus(),!1):$("#points_use_edit_form input[name=points]").val()<=0?($("#points_use_edit_form input[name=points]").focus().css("border","1px solid red"),!1):void 0},"",e=>{1==e.ret?(global_obj.win_alert_auto_close(lang_obj.global.save_success,"",1e3,"8%"),setTimeout(()=>{location.href="/manage/plugins/user-level/points-use"},800)):(global_obj.win_alert_auto_close(e.msg,"fail",1e3,"8%"),-1==e.ret&&$("input[name=CouponTypeValue]:eq(1)").focus(),-2==e.ret&&$("input[name=CouponTypeValue]:eq(0)").focus())})}};