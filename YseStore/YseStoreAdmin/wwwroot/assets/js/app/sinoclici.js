

var sinoclici_obj={
	sinoclici_init:function(){
		$('#facebook_sinoclici_edit_form').submit(function(){ return false; });
		$('#facebook_sinoclici_edit_form').on('click','.btn_submit',function(){
			$.post('/manage/plugins/sinoclici/sinoclici-save',$('#facebook_sinoclici_edit_form').serialize(),function(data){
				if(data.ret==2){
					global_obj.win_alert_auto_close(data.msg.result_message, 'fail', 1000, '8%',0);
				}else if(data.ret==1){
					window.open(data.msg.result_url, '', 'width=1000,height=500');
				}
			},'json')
		})
	},
}