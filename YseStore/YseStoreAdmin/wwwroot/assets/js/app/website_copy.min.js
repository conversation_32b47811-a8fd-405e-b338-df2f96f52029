var website_copy_obj={website_copy_init:function(){frame_obj.fixed_right($("#website_copy .sync_btn"),".sync_website_box"),frame_obj.fixed_right($("#website_copy .key_btn"),".key_management_box"),$("#website_copy_creat_apikey").click(function(){var e=$(this);$.post("/manage/plugins/website-copy/create-apikey",$("#key_management_copytype").serialize(),function(s){1==s.ret?($(".key_input").show(),e.html(lang_obj.manage.app.website_copy.refresh_key),$("#key_management_copytype .btn_copy").show(),$("#key_management_copytype .expired_btn").hide(),$('.key_management_box input[name="Key"]').removeClass("expired_input").val(s.msg.CopyApiKey).focus(),global_obj.win_alert_auto_close(s.msg.tips,"",1500,"8%")):global_obj.win_alert_auto_close(s.msg.tips,"await",1e3,"8%")},"json")});var e=new ClipboardJS(".btn_copy");e.on("success",function(e){alert(lang_obj.global.copy_complete)});var s=$("#sync_website_form"),t=$("#website_copy_sync"),o=$error=0;s.submit(function(){return!1}),t.click(function(){if(global_obj.check_form(s.find("*[notnull]"),s.find("*[format]"),1))return!1;$("#website_copy .sync_progress_bg, #website_copy .sync_progress").show(),$(this).attr("disabled",!0),$.ajax({url:"/manage/plugins/website-copy/sync",type:"post",data:s.serialize(),dataType:"json",success:function(e){if(2==e.ret){var s=$IngCount=$Rata=0;if("object"==typeof e.msg)for(i in e.msg)if("object"==typeof e.msg[i]){e.msg[i][2];e.msg[i][2]>e.msg[i][1]&&e.msg[i][1],s+=parseInt(e.msg[i][1]),$IngCount+=parseInt(e.msg[i][2])}$Rata=($IngCount/s*100).toFixed(2),$("#website_copy .sync_progress .complete").text($IngCount),$("#website_copy .sync_progress .total").text(s),$("#website_copy .sync_progress .num").css("width",$Rata+"%"),$("#website_copy .sync_progress .num span").text(($Rata>100?100:$Rata)+"%"),setTimeout(function(){$("#website_copy_sync").click()},300)}else 1==e.ret?($('.sync_website_box input[name="CopyApiKey"]').val(""),$(".sync_website_box .close").click(),global_obj.win_alert_auto_close(e.msg,"",1500,"8%"),$("#website_copy .sync_progress_bg, #website_copy .sync_progress").hide()):(global_obj.win_alert_auto_close(e.msg,"await",3e3,"8%"),$("#website_copy .sync_progress_bg, #website_copy .sync_progress").hide(),$('.sync_website_box input[name="CopyApiKey"]').val(""));t.attr("disabled",!1)},complete:function(e,s){"timeout"==s&&(t.attr("disabled",!1),o++,o<=1?t.click():(global_obj.win_alert(lang_obj.manage.error.server_timeout),$("#website_copy .sync_progress_bg, #website_copy .sync_progress").hide(),$('.sync_website_box input[name="CopyApiKey"]').val("")))},error:function(e,s){t.attr("disabled",!1),$error++,$error<=1?t.click():(global_obj.win_alert(lang_obj.manage.error.server_error),$("#website_copy .sync_progress_bg, #website_copy .sync_progress").hide(),$('.sync_website_box input[name="CopyApiKey"]').val(""))}})})}};