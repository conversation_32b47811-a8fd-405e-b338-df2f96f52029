

var xshoppy_obj={
	xshoppy_init:function(){
        frame_obj.del_init($('#products_sync .r_con_table'));
		frame_obj.select_all($('input[name=select_all]'), $('input[name=select]'), $('.table_menu_button').find('.del, .copy'));
		frame_obj.fixed_right($('a.add_store,.bg_no_table_data .btn_add_item'), '.store_add');
		//删除授权
		frame_obj.del_init($('#products_sync .account_list'));
        
		// 上传CSV
		frame_obj.fixed_right($('a.xshoppycsvupload_product_list_sync'), '.csvupload_products_box', function() {
			$("#upload_edit_form .btn_submit").attr("disabled", "disabled");
		});
		var $Form=$('#upload_edit_form');
		$Form.fileupload({
			url: '/manage/action/file-upload-plugin?size=file',
			acceptFileTypes: /^.*.(csv|vnd\.ms-excel)$/i, //csv xlsx xls
			callback: function(filepath, count){
				$('#excel_path').val(filepath);
				if (filepath) $("#upload_edit_form .btn_submit").removeAttr("disabled");
			}
		});
		$Form.fileupload(
			'option',
			'redirect',
			window.location.href.replace(/\/[^\/]*$/, '/cors/result.html?%s')
		);

		frame_obj.submit_form_init($Form, '', function(){
			$Form.find('.form_container').hide();
			$Form.find('.progress_container_right').show();
			return true;
		}, '', function(data){
			if(data.ret==2){
				//继续产品资料上传
				var $Html='', $Number=0;
				if($Form . find('.progress_container_right tbody tr').length){
					$Number=parseInt($Form . find('.progress_container_right tbody tr:eq(0) td:eq(0)').text());
				}
				if(data.msg.Data){
					for(k in data.msg.Data){
						$Html = '<tr class="'+(data.msg.Data[k].Status==0?'error':'success')+'" data-id="'+data.msg.Data[k].ProId+'" data-type="'+data.msg.Data[k].Type+'" data-pic-status="0">\
									<td nowrap="nowrap">'+($Number+parseInt(k))+'</td>\
									<td nowrap="nowrap"><div class="name" title="'+data.msg.Data[k].Name+'">'+data.msg.Data[k].Name+'</div></td>\
									<td nowrap="nowrap" data-type="status">'+(data.msg.Data[k].Status==1?'<div class="loading"></div>':'')+'</td>\
									<td nowrap="nowrap" data-type="message">'+data.msg.Data[k].Tips+'</td>\
								</tr>';
						$Form.find('.progress_container_right tbody').prepend($Html);
					}
					$Form.find('input[name=Number]').val(data.msg.Num);
					$Form.find('input[name=Current]').val(data.msg.Cur);
					$Form.find('input[name=TaskId]').val(data.msg.TaskId);
					$Form.find('.btn_submit').removeAttr('disabled').click();
				}
			}else if(data.ret==1){
				//产品资料上传完成
				if ($Form.find('.progress_container_right tbody').find('tr').length > 0) {
					$('.btn_picture').click(); //开始图片上传
					$Form.find('.progress_loading_right').addClass('completed').html(data.msg);
				} else {
					// 没有产品数据
					$Form.find('.progress_loading_right').addClass('completed').html(lang_obj.manage.products.upload.imported_all).append('<div class="box_explain">' + lang_obj.manage.products.sync.closed_reload + '</div>'); //全部导入完成
					$('#fixed_right_div_mask, .csvupload_products_box .close').click(function() {
						//硬性绑定页面刷新事件
						window.location.reload();
						return false;
					});
				}
			}else{
				global_obj.win_alert_auto_close(data.msg, 'fail', 2000, '8%');
				$Form.find('.form_container').show();
				$Form.find('.progress_container_right').hide();
			}
		});

		$('.btn_picture').on('click', function(){
			var $Obj = $Form.find('.progress_container_right tbody'),
				checkTime = 5 * 1000, //检查时间上限
				checkFun = '', //储存检查程序代码
				ProIdAry = new Array(); // 产品ID数组
			
			if ($Obj.find('tr.success[data-id!=0][data-pic-status=0]').length > 0) {
				$Obj.find('tr.success[data-id!=0][data-pic-status=0]').each(function(){
					if(ProIdAry.length > 100){ return false;}
					$(this).find('td[data-type="message"]').html(lang_obj.manage.products.upload.uploading); //图片上传中
					var $ProId = parseInt($(this).attr('data-id'));
					$ProId && ProIdAry.push($ProId);
				});
			}
			
			if (ProIdAry.length) {
				$.post('/manage/plugins/xshoppy/upload-picture', {ProIdAry:ProIdAry}, function(data){
					clearTimeout(checkFun);
					if(data.ret==1){ // 导入成功
						var $CompleteProId = data.msg.CompleteProId;
						for(var key in $CompleteProId){
							$Obj.find('tr[data-id='+$CompleteProId[key]+']').attr('data-pic-status', 1).find('td[data-type="status"]>div').attr('class', 'completed');
							$Obj.find('tr[data-id='+$CompleteProId[key]+'] td[data-type="message"]').html(lang_obj.manage.products.upload.imported); //导入完成
						}
					}
					if($Obj.find('tr.success[data-id!=0][data-pic-status=0]').length == 0){
						$Form.find('.progress_loading_right').addClass('completed').html(lang_obj.manage.products.upload.imported_all).append('<div class="box_explain">' + lang_obj.manage.products.sync.closed_reload + '</div>'); //全部导入完成
						$('#fixed_right_div_mask, .csvupload_products_box .close').click(function() {
							//硬性绑定页面刷新事件
							window.location.reload();
							return false;
						});
					}
					checkFun=setTimeout(function(){
						if($Obj.find('tr.success[data-id!=0][data-pic-status=0]').length>0){ //继续导入其他产品图片
							$('.btn_picture').click();
						}
					}, checkTime);
				}, 'json');
				//超过5秒钟后，没有任何返回结果，系统自动再次执行				
			} else {
				if ($Obj.find('tr').length == 0) {
					// 没有产品数据
					$Form.find('.progress_loading_right').addClass('completed').html(lang_obj.manage.products.upload.imported_all).append('<div class="box_explain">' + lang_obj.manage.products.sync.closed_reload + '</div>'); //全部导入完成
				}
			}
		});

		frame_obj.del_bat($('.table_menu_button .del'), $('input[name=select]'), '', function(id_list){
			global_obj.win_alert(lang_obj.global.del_confirm, function(){
				global_obj.win_alert_auto_close(lang_obj.global.delete, 'loading', -1);
				$.get('/manage/plugins/xshoppy/del-batch', {group_proid:id_list}, function(data){
					if (data.ret == 1) {
						window.location.reload();
					}
				}, 'json');
			}, 'confirm');
			return false;
		}, lang_obj.global.dat_select);

		//单独发布
		$(".btn_publish").click(function() {
			var $Obj = $(this).parents("tr"),
				$ProId = $Obj.data("id");
			$.post('/manage/plugins/xshoppy/copy-products', {group_id:$ProId, Type:"Publish"}, function(data) {
				$Obj.find("td.status").html("<span class=\"status ing\">" + lang_obj.manage.products.sync.publish_used + "</span>");
				global_obj.win_alert_auto_close(lang_obj.manage.products.sync.publish_once, "", 2000, "8%");
			});
		});

		//复制产品分类选择弹出框
		$('a.xshoppy_product_list_post').on('click', function(){
			var $obj=$('.copy_products_box');
			$('.copy_products_box select[name=CateId]').find('option:selected').removeAttr('selected');
		});
		frame_obj.fixed_right($('a.xshoppy_product_list_post'), '.copy_products_box');
        //多分类按钮
		$('#btn_expand').on('click', function(){
			var obj=$(this).nextAll('.expand_list');
			var category_sel=$('#copy_edit_form select[name=CateId]').html();
			obj.append('<li><div class="box_select"><select name="ExtCateId[]" notnull>'+category_sel.replace(' selected','')+'</select></div><a class="Cclose icon_delete_1" href="javascript:;"><i></i></a></li>');
			$('.expand_list .Cclose').on('click', function(){
				$(this).parent().remove();
			});
		});
		$('.expand_list .Cclose').on('click', function(){
			$(this).prev().remove();
			$(this).remove();
		});

		//提交产品复制
		var xshoppy_sync_obj = {
			action: function(id_list, $CateId, $ExtCateId) {
				var $Html = '',
					$Obj = $('#copy_edit_form .progress_container_right'),
					$Number = $Obj.find('input[name=Number]').val(),
					$Current = $Obj.find('input[name=Current]').val();
				$.post('/manage/plugins/xshoppy/copy-products', {group_id:id_list, CateId:$CateId, ExtCateId:$ExtCateId, 'Number':$Number, Current:$Current, Type:"BatchPublish"}, function(data) {
					if (data.ret == 2) {
						//继续产品资料上传
						if (data.msg.Data) {
							var Num = 0, arr = [], i, len, obj = {};
							if ($('#copy_edit_form .progress_container_right tbody tr').length) {
								Num = parseInt($('#copy_edit_form .progress_container_right tbody tr:eq(0) td:eq(0)').text());
							}
							for (i in data.msg.Data) {
								arr.push([data.msg.Data[i], i]);
							};
							arr.sort(function (a,b) {
								return a[0] - b[0];
							});
							len = arr.length;
							for (i = 0; i < len; i++) {
								obj[arr[i][1]] = arr[i][0];
							}
							for (k in obj) {
								$Html += '<tr class="' + (obj[k].Status == 0 ? 'error' : 'success') + '" data-id="'+obj[k].Type+'" data-type="'+obj[k].Type+'" data-pic-status="0">\
									<td nowrap="nowrap">' + k + '</td>\
									<td nowrap="nowrap"><div class="name" title="'+obj[k].Name+'">'+obj[k].Name+'</div></td>\
									<td nowrap="nowrap" data-type="message">' + lang_obj.manage.products.upload.imported + '</td>\
								</tr>';
							}
							$Obj.find('tbody').prepend($Html);
							$Obj.find('input[name=Number]').val(data.msg.Num);
							$Obj.find('input[name=Current]').val(data.msg.Cur);
							xshoppy_sync_obj.action(id_list, $CateId, $ExtCateId);
						}
					} else if (data.ret == 1) {
						//产品资料上传完成
						$('.copy_products_box input.btn_submit').removeAttr('disabled');
						$('#copy_edit_form .progress_loading_right').addClass('completed').html(lang_obj.manage.products.upload.imported_all).append('<div class="box_explain">' + lang_obj.manage.products.sync.closed_reload + '</div>'); //全部导入完成
						$('#fixed_right_div_mask, .copy_products_box .close').click(function() {
							//硬性绑定页面刷新事件
							window.location.reload();
							return false;
						});
					} else {
						$('.copy_products_box input.btn_submit').removeAttr('disabled');
						global_obj.win_alert_auto_close(data.msg, 'fail', 2000, '8%');
						$('#copy_edit_form .progress_container_right').hide();
					}
				}, 'json');
			}
		}
		frame_obj.del_bat($('.copy_products_box input.btn_submit'), $('input[name=select]'), '', function(id_list){
			$('.copy_products_box input.btn_submit').attr('disabled', 'disabled');
			var $obj=$('.copy_products_box select[name=CateId]').find('option:selected');
			if($obj.val()!=''){
				var $CateId=parseInt($obj.val());
			}else{
				global_obj.win_alert(lang_obj.manage.products.category_tips);
				global_obj.div_mask(1);
				$('.copy_products_box input.btn_submit').removeAttr('disabled');
				return false;
			}
			var $ExtCateId='0';
            $('.copy_products_box select[name=ExtCateId\\[\\]]').each(function(){
                $ExtCateId+=','+$(this).val();
            });

			$('#copy_edit_form .form_container').hide();
			$('#copy_edit_form .progress_container_right').show();

			xshoppy_sync_obj.action(id_list, $CateId, $ExtCateId);
		}, lang_obj.global.dat_select);

		/***********************************检查产品部分************************************/

		var $id_ary = new Array,
			$Page = $('.r_con_table').data('page');
		$('.r_con_table tr').each(function(index, element) {
			var $ID = parseInt($(this).data('id'));
			if ($ID > 0) {
				$id_ary[index - 1] = $ID;
			}
		});
		if ($id_ary.length) {
			$.post('/manage/plugins/xshoppy/check', {'Page':$Page, 'ProIdAry':$id_ary}, function(data){
				if (data.ret == 1 && data.msg) {
					for (k in data.msg) {
						if ($('.r_con_table tr[data-id=' + data.msg[k] + ']').length) {
							$('.r_con_table tr[data-id=' + data.msg[k] + '] td.status').html(lang_obj.manage.products.sync.publish_none);
						}
					}
				}
			}, 'json');
		}
		/***********************************检查产品部分************************************/
    }
}