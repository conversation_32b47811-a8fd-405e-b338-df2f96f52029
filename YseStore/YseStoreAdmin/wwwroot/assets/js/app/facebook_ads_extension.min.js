var facebook_ads_extension_obj={facebook_ads_extension_init:function(){var o=parseInt($("#facebook_ads_extension").attr("data-in-china"));$.post("?",{operationName:"getAdsAccountInfo"},function(n){var a=n.msg.FbData&&n.msg.FbData.setting_id?1:0,s="";1==o&&(s+='<div class="global_tips no_icon rows"><strong>'+lang_obj.manage.app.facebook_ads_extension.tips+"</strong></div>"),s+='<div class="store_id rows'+(a?"":" hide")+'">'+lang_obj.manage.app.facebook_ads_extension.store_id+': <span id="facebook_store_id" class="fs12 color_888">'+(a?n.msg.FbData.page_id:"")+"</span></div>",s+='<div class="button">',s+='\t<iframe id="facebook_login_box" src="https://sync.ly200.com/plugin/facebook/login.php?domain='+n.msg.domain+'&newVersion=1" style="width:240px; height:40px;"></iframe>',s+='\t<button id="button_settings" style="display:none;" class="btn_global btn_submit">'+(a?lang_obj.manage.app.facebook_ads_extension.manage_settings:lang_obj.manage.app.facebook_ads_extension.get_started)+"</button>",s+="</div>",$("#facebook_flow").append(s),window.facebookAdsToolboxConfig=n.msg.Config,window.facebookAdsConfig=new Object,window.facebookMessenger=new Object,$("#facebook_ads_extension").off().on("click","#button_settings",function(){plugins_ads.launchDiaWizard()}).on("click","#button_ad",function(){plugins_ads.launchAutomatedAds()}),plugins_ads.getTokenInfo(),$("#facebook_flow").removeClass("loading")},"json")}};