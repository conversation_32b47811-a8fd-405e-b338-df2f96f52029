var nav_themes_obj={nav_themes_init:function(){$_MenuHtml="",$_MenuHtml+='<div class="option_row">',$_MenuHtml+='<div class="rows float clean">',$_MenuHtml+="<label>"+lang_obj.global.picture+"</label>",$_MenuHtml+='<div class="input">',$_MenuHtml+='<div class="multi_img upload_file_multi pro_multi_img" id="PicDetail_%OptionIndex%">',$_MenuHtml+=frame_obj.multi_img_item("PicPath[]","%OptionIndex%",0),$_MenuHtml+="</div>",$_MenuHtml+="</div>",$_MenuHtml+="</div>",$_MenuHtml+='<div class="rows float clean">',$_MenuHtml+="<label>"+lang_obj.manage.app.nav_themes.link+"</label>",$_MenuHtml+='<div class="input"><input type="text" name="Url[]" value="" class="box_input" size="70" maxlength="225" data-input="name" /></div>',$_MenuHtml+="</div>",$_MenuHtml+='<div class="float button fr">',$_MenuHtml+='<a href="javascript:;" class="btn_option fl btn_option_remove"><i></i></a>',$_MenuHtml+="</div>",$_MenuHtml+='<div class="clear"></div>',$_MenuHtml+="</div>";let t={themes_obj:$("#nav_themes"),menu_form:$("#nav_themes_edit_form"),add_menu_option:function(e,i){let n=t.menu_form.find(".data_list");e=e.replaceAll("%OptionIndex%",i),n.append(e)},del_option:function(t){t.parents(".option_row").fadeOut(function(){$(this).remove()})},side_picture_upload:function(){$(".navpic_box  .data_list").off().on("click",".upload_btn",function(){let t=$(this).parents(".multi_img").attr("id"),e=$(this).parents(".img").attr("num");frame_obj.photo_choice_init(t+" .img[num;"+e+"]","",1,"",1,"")}),$("body").on("click",".navpic_box  .data_list .del",function(t){let e=$(this),i=e.parents(".img");i.removeClass("isfile").removeClass("show_btn").parent().append(i),i.find(".pic_btn .zoom").attr("href","javascript:;"),i.find(".preview_pic .upload_btn").show(),i.find(".preview_pic a").remove(),i.find(".preview_pic input:hidden").val("").attr("save",0).trigger("change")})}};t.side_picture_upload(),t.themes_obj.on("click",".type_item",function(){let e=$(this),i=e.parent(),n=e.data("type"),a=0;e.addClass("cur").siblings().removeClass("cur"),i.find("input[name=Type]").val(n),1!=n&&(a=1),a?t.themes_obj.find(".navpic_box").show():t.themes_obj.find(".navpic_box").hide()}),t.menu_form.delegate(".btn_option_remove","click",function(){t.del_option($(this))}).delegate(".add_option_btn","click",function(){let e=parseInt($(".data_list").find(".option_row").length);t.add_menu_option($_MenuHtml,e+1)}),frame_obj.submit_form_init(t.menu_form,"",function(){},"",function(t){1==t.ret&&(global_obj.win_alert_auto_close(lang_obj.global.saved,"",1e3,"8%"),window.location.reload())})}};