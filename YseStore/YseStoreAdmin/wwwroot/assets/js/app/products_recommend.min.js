var recommend_obj={recommend_init:function(){frame_obj.del_bat($(".table_menu_button .del"),$("input[name=select]"),"/manage/plugins/products-recommend/delete-batch"),frame_obj.select_all($("input[name=select_all]"),$("input[name=select]"),$(".table_menu_button .del"));var e=new ClipboardJS(".btn_copy");e.on("success",function(e){global_obj.win_alert_auto_close(lang_obj.global.copy_complete,"success",1e3,"8%")}),$(".products_number").on("click",function(e){let t=$(this),o=t.data("id");if(!t.find(".products_txt i").length)return!1;$.post("/manage/plugins/products-recommend/get-recommend-products-info",{RId:o},function(o){if(1==o.ret){let i="",n=0;for(let e in o.msg){let t=o.msg[e];i+='<div class="products_list">',i+='<div class="list_item">',i+='<div class="item_img fl">',i+='<a href="'+t.Url+'" target="_blank"><img src="'+t.PicPath+'" /></a>',i+="</div>",i+='<div class="item_info fr">',i+='<div class="info_name">'+t.Name+"</div>",i+="</div>",i+='<div class="clear"></div>',i+="</div>",i+="</div>",n++}t.find(".products_box").html(i);let r=0,s=t.find(".products_container").height(),a=e.currentTarget.offsetTop,d=$(".box_table").height();a+s>d&&(t.find(".products_container").css({top:"auto",bottom:"100%"}),r=1);let c=a-parseInt($(".inside_table").css("padding-top"))-$(".inside_table .list_menu").height()-parseInt($(".box_table table").css("margin-top"));r?(c-=5,s>c&&t.find(".products_box").css("max-height",c)):(c=c+t.height()+25,c+s>d&&t.find(".products_box").css("max-height",d-c)),t.addClass("current").find(".products_container").fadeIn()}},"json")}),$(".products_number").on("mouseleave",function(){$(this).removeClass("current").find(".products_container").fadeOut()})},recommend_edit_init:function(){function e(){$length=0,$(".page_box .input_checkbox_box").each(function(){$(this).hasClass("checked")&&"goods"!=$(this).attr("data-type")&&"cart"!=$(this).attr("data-type")&&$length++,$length>0?($("#products_recommend_inside .input_radio_side_box[data-type=related]").addClass("disabled").removeClass("checked").find("input").prop("checked",!1),$("#products_recommend_inside .scope_box[data-type=related]").hide()):$("#products_recommend_inside .input_radio_side_box[data-type=related]").removeClass("disabled")})}const t={showType:"",optionType:"",specialType:""},o={quantityShow:()=>{"auto"==t.showType||"manual"==t.showType&&"special"==t.optionType&&"products"==t.specialType?$(".quantity_box").addClass("hide"):$(".quantity_box").removeClass("hide")}};frame_obj.fixed_right($(".add_pro_btn , .icon_edit"),".fixed_edit_recommend",function(e){let t=e.attr("attr-data"),o=e.parents("tr").attr("data");$.post("/manage/plugins/products-recommend/products-right/",{Data:t,No:o},function(e){1==e.ret&&($(".fixed_edit_recommend").html(e.msg),frame_obj.submit_form_init($("#right_edit_form"),"","",0,function(e){1==e.ret?($("#products_recommend_inside .pro_data_list tbody").append(e.msg.Html),$("#right_edit_form .btn_cancel").click(),$(".no_data_yet").hide(),$(".pro_data_list").show(),recommend_obj.fixed_form(e.msg.No)):2==e.ret?($("#products_recommend_inside .pro_data_list tbody tr[data="+e.msg.No+"]").replaceWith(e.msg.Html),$("#right_edit_form .btn_cancel").click(),recommend_obj.fixed_form(e.msg.No)):$("#right_edit_form .btn_cancel").click()}))},"json")}),frame_obj.fixed_box_popup({clickObj:$(".copy_code"),targetClass:"flex_promote"});var i=new ClipboardJS(".btn_copy");i.on("success",function(e){global_obj.win_alert_auto_close(lang_obj.global.copy_complete,"success",1e3,"8%")}),$(window).ready(function(){$("#products_recommend_inside .rows.type_box .input_radio_side_box.checked").length&&$("#products_recommend_inside .rows.type_box .input_radio_side_box.checked").click(),$(".box_type_menu span.checked").length&&$(".box_type_menu span.checked").click()}),$(".show_box .input_radio_box").on("click",function(){let e=$(this).find("input").val();"manual"==e?$(".page_box").hide():$(".page_box").show(),t.showType=e,o.quantityShow()}),t.showType=$(".show_box .input_radio_box.checked input").val()||"",$(".fixed_edit_recommend").on("click",".recommend_box .input_checkbox_box , .btn_attr_choice.current>i",function(){let e=$(this).parents(".recommend_box"),t=e.find(".select_list").find(".btn_attr_choice").length,o=$(this).hasClass("checked")?0:1;o&&$(this).hasClass("input_checkbox_box")?t+=1:t-=1,t>4&&(t=4),$("#right_edit_form .recommend_box label b").html(t),t>0?$("#right_edit_form .recommend_box label b").addClass("change"):$("#right_edit_form .recommend_box label b").removeClass("change")}),$("body").on("click",".type_box .input_radio_side_box",function(){let e=$(this).hasClass("show_exclude")?1:0,i=$(this).hasClass("show_scope")?1:0,n=$(this).hasClass("show_manual")?1:0,r=$(this).attr("data-type"),s=$(this).attr("title");if($(this).hasClass("disabled"))return $(this).removeClass("checked").find("input").prop("checked",!1),!1;$TitleAry=new Array,$("#products_recommend_inside .rows.type_box .input_radio_side_box").each(function(){$TitleAry.push($(this).attr("title"))}),($.inArray($("input[name=Data\\[Title\\]]").val(),$TitleAry)>=0||""==$("input[name=Data\\[Title\\]]").val())&&$("input[name=Data\\[Title\\]]").val(s),e?$(".exclude_box").show():$(".exclude_box").hide(),i||n?$(".scope_box[data-type="+r+"]").show().siblings(".scope_box").hide():$(".scope_box").siblings(".scope_box").hide(),$(".type_box label .error_tips").css("display","none"),t.optionType=r,o.quantityShow()}),$("body").on("click","#products_recommend_inside .input_checkbox_box",function(){$(this);e()}),$("body").on("click",".box_type_menu .item",function(){let e=$(this).attr("data-type");$(this).find("input").prop("checked",!0).parent().siblings().find("input").prop("checked",!1),$(this).addClass("checked").siblings().removeClass("checked"),$(this).parents(".scope_box").find(".use_group_box[data-value="+e+"]").show().siblings(".use_group_box").hide(),t.specialType=e,o.quantityShow()}),$("#edit_form").on("click",".scope_box[data-type=special] .use_group_box[data-value=products] .input_checkbox_box , .btn_attr_choice.current>i",function(){let e=$(this).parents(".use_group_box"),t=e.find(".select_list").find(".btn_attr_choice").length,o=$(this).hasClass("checked")?0:1;o&&$(this).hasClass("input_checkbox_box")?t+=1:t-=1,t>20&&(t=20),$(".scope_box[data-type=special] .box_type_menu .item").find("b").html(t),t>0?$(".scope_box[data-type=special] .box_type_menu .item").find("b").addClass("change"):$(".scope_box[data-type=special] .box_type_menu .item").find("b").removeClass("change")}),e(),frame_obj.submit_form_init($("#edit_form"),"",function(){if("related"==$("#edit_form").find('input[name="Type"]:checked').val()&&!$("#edit_form").find('input[name="ProductsScope"]:checked').val())return $("#edit_form").find(".box_type_menu span").css("border","1px solid red").addClass("null"),setTimeout(function(){$("#edit_form").find(".box_type_menu span").css({border:"none"})},1e3),!1;if(!$("input[name=backUrl]").val()&&(0==$("#edit_form").find('input[name="Type"]:checked').length||0==$("#edit_form").find('input[name="Page[]"]:checked').length&&"auto"==$(".show_box .input_radio_box input[name=ShowType]:checked").val()))return 0==$("#edit_form").find('input[name="Page[]"]:checked').length&&$(".page_box label .error_tips").css("display","inline"),0==$("#edit_form").find('input[name="Type"]:checked').length&&$(".type_box label .error_tips").css("display","inline"),!1;if($(".quantity_box").is(":visible")){let e=$(".quantity_box"),t=e.find("input"),o=t.val();if(o<1||o>20)return t.css("border","1px solid red"),e.hasClass("has_error")||e.addClass("has_error").append(`<p class="error_tips" style="font-size:14px;">${lang_obj.manage.app.products_recommend.qty_tips}</p>`),!1;t.removeAttr("style"),e.removeClass("has_error"),e.find(".error_tips").remove()}},0,function(e){1==e.ret&&(global_obj.win_alert_auto_close(lang_obj.global.saved,"success",1e3,"8%"),$("input[name=backUrl]").val()?window.location.href=$("input[name=backUrl]").val()+"?id="+e.msg:window.location.href="/manage/plugins/products-recommend/edit?id="+e.msg)}),$("#products_recommend_inside .rows.type_box .input_radio_side_box[data-type=manual]").click(),$("#edit_form").on("click",".products_number",function(){let e=$(this);e.addClass("current").find(".products_container").fadeIn()}),$("#edit_form").on("mouseleave",".products_number",function(){$(this).removeClass("current").find(".products_container").fadeOut()}),$(".pro_data_list").on("click","tbody .btn_checkbox",function(){let e=$(this),t=e.hasClass("current"),o=e.parents("tbody");t?e.removeClass("current"):e.addClass("current"),$length=o.find(".current").length,$total=o.find(".btn_checkbox").length,$length>0?(e.find("input").prop("checked",!0),o.prev().find("tr").addClass("current").find(".btn_checkbox").addClass("indeterminate").addClass("current"),o.prev().find("tr").find(".open").find("span").html($length)):(e.find("input").prop("checked",!1),o.prev().find("tr").removeClass("current").find(".btn_checkbox").removeClass("indeterminate").removeClass("current"),o.prev().find("tr").find(".open").find("span").html($length)),$length==$total&&o.prev().find("tr").find(".btn_checkbox").removeClass("indeterminate").addClass("current")}),$(".pro_data_list").on("click","thead .btn_checkbox",function(){let e=$(this),t=e.hasClass("current");$indeterminate=e.hasClass("indeterminate"),$parent=e.parents("thead").next("tbody"),t&&$indeterminate?(e.addClass("current").parents("tr").addClass("current"),e.removeClass("indeterminate"),$parent.find("tr").each(function(){$(this).find(".btn_checkbox").addClass("current").find("input").prop("checked",!0)})):t?(e.addClass("current").parents("tr").addClass("current"),$parent.find("tr").each(function(){$(this).find(".btn_checkbox").addClass("current").find("input").prop("checked",!0)})):(e.removeClass("current").removeClass("indeterminate").parents("tr").removeClass("current"),$parent.find("tr").each(function(){$(this).find(".btn_checkbox").removeClass("current").find("input").prop("checked",!1)})),$length=$parent.find(".btn_checkbox").length,$parent.prev().find("tr").find(".open").find("span").html($length)}),$("#products_box .products_list").on("click",".p_del",function(){var e=$(this).parents(".item");e.fadeOut(300,function(){e.remove(),$("#products_box .products_list .item").length?($("#products_box .products_list").show(),$("#products_box .no_data").hide()):($("#products_box .products_list").hide(),$("#products_box .no_data").show()),$("#ProNum").html($("#products_box .products_list .item").length)})}),frame_obj.fixed_right_products_choice(),frame_obj.fixed_right($("#add_btn"),".fixed_right_products_choice",function(e){$("#fixed_right .search_form form input[name=FilterCateId]").length||$("#fixed_right .search_form form").append('<input type="hidden" name="FilterCateId" value="'+$("input[name=CateId]").val()+'" />'),$("#fixed_right .search_form form input[name=FilterProId]").length||$("#fixed_right .search_form form").append('<input type="hidden" name="FilterProId" value="" />');var t=$('#products_box .products_list input[name^="ProId"]').map(function(){return $(this).val()}).get();t=t.join(","),$("#fixed_right .search_form form input[name=FilterProId]").val(t),$("#fixed_right .search_form form").submit()}),frame_obj.fixed_right_products_filter(),$(".product_menu .btn_menu_view").click(function(){var e=$(this).parents(".product_menu").data("url");window.open(e)}),frame_obj.submit_form_init($("#fixed_right_products_choice_form"),"","","",function(e){if(1==e.ret){$Count=$("#products_box .products_list .item").length,$("#products_box .no_data").hide(),$("#products_box .products_list").append(e.msg);$("input[name=OrderType]").val();$(".fixed_right_products_choice").find(".btn_cancel").trigger("click"),global_obj.win_alert_auto_close(lang_obj.global.add_success,"",1e3,"8%")}else global_obj.win_alert_auto_close(e.msg,"await",1e3,"8%");$("#products_box .products_list .item").length?($("#products_box .products_list").show(),$("#products_box .no_data").hide()):($("#products_box .products_list").hide(),$("#products_box .no_data").show()),$("#ProNum").html($("#products_box .products_list .item").length)}),$("body").on("click",".page_box .input_checkbox_box",function(){$(".page_box label .error_tips").css("display","none")});var n=function(){let e=$('input[name="ShowPosition"]:checked').val(),t=$('input[name="Mode"]:checked').val();"themes_global_point"==e?$(".style_box").hide():$(".style_box").show(),t||$('input[name="Mode"]').parents(".input_radio_box").click()};n(),$(".position_box").on("click",".input_radio_box",function(){setTimeout(()=>{n()},100)})},fixed_form:function(e){frame_obj.fixed_right($("tr[data="+e+"] .icon_edit"),".fixed_edit_recommend",function(e){let t=e.attr("attr-data"),o=e.parents("tr").attr("data");$.post("/manage/plugins/products-recommend/products-right/",{Data:t,No:o},function(e){$(".fixed_edit_recommend").html(e.msg),frame_obj.submit_form_init($("#right_edit_form"),"","",0,function(e){1==e.ret?($("#products_recommend_inside .pro_data_list tbody").append(e.msg.Html),$("#right_edit_form .btn_cancel").click(),recommend_obj.fixed_form(e.msg.No)):2==e.ret&&($("#products_recommend_inside .pro_data_list tbody tr[data="+e.msg.No+"]").replaceWith(e.msg.Html),$("#right_edit_form .btn_cancel").click(),recommend_obj.fixed_form(e.msg.No))})},"json")})}};