/*

 * 平台导流 APP
 */

var chat_app_obj = {
	chat_init : function(){
		frame_obj.del_init($('#chat .chat_box'));
		frame_obj.fixed_right($('#chat .add_btn , #chat .set_add , #chat .list_box .edit'), '.box_chat_edit');
		frame_obj.submit_form_init($('#chat_edit_form'), '/manage/plugins/chat');
		frame_obj.config_switchery($('.used .switchery'), '/manage/plugins/chat/config-switchery', 'data-config', 'config');
		frame_obj.dragsort($('#chat_box .list_box'), '/manage/plugins/chat/my-order', '.list .icon_myorder', '', '<div class="list cur"></div>', '.list'); //排序拖动
		frame_obj.dragsort($('#chat_box'), '/manage/plugins/chat/type-my-order', '.chat_list .type_myorder', '', '<div class="chat_list cur"></div>', '.chat_list'); //排序拖动
		const _chatType = {0:'skype', 1:'whatsApp', 2:'email', 3:'wechat', 4:'trademanager', 5:'qq', '6':'telegram'};
		/* 图片上传 */
		frame_obj.mouse_click($('#PicDetail .upload_btn, #PicDetail .pic_btn .edit'), 'img', function($this){ //点击上传图片
			frame_obj.photo_choice_init('PicDetail', '', 1);
		});

		$('#chat_edit_form select').on('change',function(){
			var $this = $(this),
				$value = $this.val();
			$('#Picture , #chat_edit_form .box_explain').hide();
			if($value==3 || $value==7){
				$('#Picture').show();
				$('#Picture label span').hide()
				$(`#Picture label span[data-id="${$value}"]`).show()
			}else if($value==1){
				$('#chat_edit_form .whatsapp_tips').show();
			}
			$lowValue = _chatType[$value];
			$(`#chat_edit_form .${$lowValue}_tips`).show();
		})
		
		$('#chat_box .list_box .list .name a.edit , #chat .add_btn , #chat .set_add').on('click',function(){
			var Data = '';
			
			if($(this).attr('data-chat')){
				var Data = $.parseJSON($(this).attr('data-chat'));
			}

			if(Data){
				chat_app_obj.form_check(Data);
			}else{
				chat_app_obj.form_check();
			}
		})

		$('#chat_box .list_box .list .name .del').on('click',function(){
			var data_id=$(this).attr('data-cid'),
				$this = $(this);
			global_obj.win_alert(lang_obj.global.del_confirm, function(){
				$.post('/manage/plugins/chat/del',{'CId':data_id},function(data){
					if(data.ret==1){
						window.location.reload();
					}
				},'json')
			}, 'confirm');
		})

	},
	form_check:function(data){
		var box_chat_edit = $('.box_chat_edit');
		var select_Type = $('select[name=Type]', box_chat_edit);//选择框
		var ubox = $('.ubox', box_chat_edit);//图片上传框
		var $data = box_chat_edit.attr('data-chat');//数据
		
		if(data){
			var CId = data.CId;
			$('#Picture, .whatsapp_tips').hide(0);
			$('#PicDetail .preview_pic a').remove();
			$('#PicDetail .upload_btn').css('display', 'block');
			$('input[name=Name]', box_chat_edit).val(data.Name);
			$('input[name=PicPath]', box_chat_edit).val(data.PicPath);
			$('input[name=CId]', box_chat_edit).val(CId);
			$('input[name=Account]', box_chat_edit).val(data.Account);
			select_Type.find('option:selected').prop('selected', false);
			select_Type.find('option[value="'+data.Type+'"]').prop('selected', true);
			if (data.Type==3 || data.Type==7){
				$('#Picture').show(0);
				$('#Picture label span').hide()
				$(`#Picture label span[data-id="${data.Type}"]`).show()
				if (data.PicPath){
					$('#PicDetail .img').addClass('isfile').find('.preview_pic').append(frame_obj.upload_img_detail(data.PicPath)).children('.upload_btn').hide(0);
					$('#PicDetail .zoom').attr('href', data.PicPath);
				}else{
					$('#PicDetail .img').removeClass('isfile').find('.preview_pic').children('.upload_btn').show(0);
				}
			}
			if(data.Type==1) $('.whatsapp_tips').show(0);
			let lowerType = data.Type.toLowerCase()
			$(`.${lowerType}_tips`).show(0);
		}else{
			$('#Picture, .whatsapp_tips').hide(0);
			$('#PicDetail .preview_pic a').remove();
			$('#PicDetail .upload_btn').css('display', 'block');
			$('#PicDetail .img').removeClass('isfile').find('.preview_pic').children('.upload_btn').show(0);
			$('#PicDetail .zoom').attr('href', 'javascript:;');
			$('input[name=Name]', box_chat_edit).val('');
			$('input[name=CId]', box_chat_edit).val('');
			$('input[name=PicPath]', box_chat_edit).val('');
			$('input[name=Account]', box_chat_edit).val('');
			select_Type.find('option:selected').prop('selected', false);
			select_Type.find('option:eq(0)').prop('selected', true);
			select_Type.change()
		}
	}
}
