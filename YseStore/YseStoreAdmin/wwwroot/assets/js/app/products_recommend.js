var recommend_obj = {
    // 产品推荐

	recommend_init: function(){
        frame_obj.del_bat($('.table_menu_button .del'), $('input[name=select]'), '/manage/plugins/products-recommend/delete-batch'); // 批量删除
		frame_obj.select_all($('input[name=select_all]'), $('input[name=select]'), $('.table_menu_button .del')); //批量操作
		
		var clipboard=new ClipboardJS('.btn_copy');
		clipboard.on('success', function(e){
			global_obj.win_alert_auto_close(lang_obj.global.copy_complete, 'success', 1000, '8%');
		});

		$('.products_number').on('click', function(event){
			let $this = $(this);
			let RId = $this.data('id');
			if (!$this.find('.products_txt i').length) return false; 
			$.post('/manage/plugins/products-recommend/get-recommend-products-info', {'RId':RId}, function(data){
				if (data.ret == 1) {
					let html = '';
					let count = 0;
					for (let status in data.msg) {
						let params = data.msg[status];	
						html += '<div class="products_list">';
							html += '<div class="list_item">';
								html += '<div class="item_img fl">';
									html += '<a href="' + params.Url + '" target="_blank"><img src="' + params.PicPath + '" /></a>';
									//if (params.Count > 1) html += '<i>' + params.Count + '</i>';
								html += '</div>';
								html += '<div class="item_info fr">';
									html += '<div class="info_name">' + params.Name + '</div>';
								html += '</div>';
								html += '<div class="clear"></div>';
							html += '</div>';
						html += '</div>';
						count++;
					}
					$this.find('.products_box').html(html);
					// 控制显示
					let change = 0;
					let boxHeight = $this.find('.products_container').height();
					let clickHeight = event.currentTarget.offsetTop;
					let tableHeight = $('.box_table').height();
					if (clickHeight + boxHeight > tableHeight) {
						$this.find('.products_container').css({'top':'auto', 'bottom':'100%'});
						change = 1;
					}
					let thisPosition = clickHeight - parseInt($('.inside_table').css('padding-top')) - $('.inside_table .list_menu').height() - parseInt($('.box_table table').css('margin-top'));
					if (change) {
						thisPosition = thisPosition - 5;
						if (boxHeight > thisPosition) {  // 产品框高度大于表格
							$this.find('.products_box').css('max-height', thisPosition);
						}
					} else {
						thisPosition = thisPosition +  $this.height() + 25;
						if (thisPosition + boxHeight > tableHeight) {  // 产品框高度大于表格
							$this.find('.products_box').css('max-height', tableHeight - thisPosition);
						}
					}
					$this.addClass('current').find('.products_container').fadeIn();
				}
			}, 'json');
		});
		$('.products_number').on('mouseleave', function(){
			$(this).removeClass('current').find('.products_container').fadeOut();
		});

	},

	recommend_edit_init: function () {
		const PARAM = {
			'showType': '',
			'optionType': '',
			'specialType': '',
		}
		const FUNC = {
			quantityShow : () => {
				// 展示数量
				if (PARAM.showType == 'auto' || (PARAM.showType == 'manual' && PARAM.optionType == 'special' && PARAM.specialType == 'products')) {
					$('.quantity_box').addClass('hide')
				} else {
					$('.quantity_box').removeClass('hide')
				}
			}
		}

		frame_obj.fixed_right($('.add_pro_btn , .icon_edit'), '.fixed_edit_recommend',function($this){
			let Data = $this.attr('attr-data'),
				No = $this.parents('tr').attr('data');
			$.post('/manage/plugins/products-recommend/products-right/',{'Data':Data , 'No': No},function(data){
				if(data.ret == 1){
					$('.fixed_edit_recommend').html(data.msg);
					frame_obj.submit_form_init($('#right_edit_form'),'','',0,function(result){
						if(result.ret == 1){
							$('#products_recommend_inside .pro_data_list tbody').append(result.msg.Html);
							$('#right_edit_form .btn_cancel').click();
							$('.no_data_yet').hide();
							$('.pro_data_list').show();
							recommend_obj.fixed_form(result.msg.No);
						}else if(result.ret == 2){
							$('#products_recommend_inside .pro_data_list tbody tr[data='+result.msg.No+']').replaceWith(result.msg.Html);
							$('#right_edit_form .btn_cancel').click();
							recommend_obj.fixed_form(result.msg.No);
						}else{
							$('#right_edit_form .btn_cancel').click();
						}
					})

				}
			},'json')
		});

		frame_obj.fixed_box_popup({
			"clickObj": $(".copy_code"),
			"targetClass": "flex_promote"
		});

		var clipboard=new ClipboardJS('.btn_copy');
		clipboard.on('success', function(e){
			global_obj.win_alert_auto_close(lang_obj.global.copy_complete, 'success', 1000, '8%');
		});

		//recommend_obj.recommend_list_init();

		//默认点击
		$(window).ready(function(){
			if($('#products_recommend_inside .rows.type_box .input_radio_side_box.checked').length){
				$('#products_recommend_inside .rows.type_box .input_radio_side_box.checked').click();
			}
			if($('.box_type_menu span.checked').length){
				$('.box_type_menu span.checked').click();
			}
		})

        $('.show_box .input_radio_box').on('click', function () {
            let $value = $(this).find('input').val()
            if ($value == 'manual') {
				$('.page_box').hide()
            } else {
				$('.page_box').show()
            }
			PARAM.showType = $value
			FUNC.quantityShow()
        })
		PARAM.showType = $('.show_box .input_radio_box.checked input').val() || ''

		$('.fixed_edit_recommend').on('click','.recommend_box .input_checkbox_box , .btn_attr_choice.current>i',function(){
			let $Obj = $(this).parents('.recommend_box'),
			ItemCount = $Obj.find('.select_list').find('.btn_attr_choice').length,
			$IsChecked = $(this).hasClass('checked') ? 0: 1;
			if($IsChecked && $(this).hasClass('input_checkbox_box')){
				ItemCount = ItemCount + 1;
			}else{
				ItemCount = ItemCount - 1;
			}

			if(ItemCount > 4){
				ItemCount = 4;
			}
			$('#right_edit_form .recommend_box label b').html(ItemCount);
			if(ItemCount>0){
				$('#right_edit_form .recommend_box label b').addClass('change');
			}else{
				$('#right_edit_form .recommend_box label b').removeClass('change');
			}
		})

		//推荐产品类型点击效果
		$('body').on('click','.type_box .input_radio_side_box',function(){
			let show_exclude = $(this).hasClass('show_exclude') ? 1 : 0,
				show_scope = $(this).hasClass('show_scope') ? 1 : 0,
				show_manual = $(this).hasClass('show_manual') ? 1 : 0,
				type = $(this).attr('data-type'),
				title = $(this).attr('title');
			if($(this).hasClass('disabled')){ 
				$(this).removeClass('checked').find('input').prop('checked',false);
				return false;
			}
			$TitleAry = new Array;
			$('#products_recommend_inside .rows.type_box .input_radio_side_box').each(function(){
				$TitleAry.push($(this).attr('title'));
			})
			if($.inArray($('input[name=Data\\[Title\\]]').val() , $TitleAry) >= 0 || $('input[name=Data\\[Title\\]]').val() == ''){
				$('input[name=Data\\[Title\\]]').val(title);
			}
			if(show_exclude){
				$('.exclude_box').show();
			}else{
				$('.exclude_box').hide();
			}
			if(show_scope || show_manual){
				$('.scope_box[data-type='+ type +']').show().siblings('.scope_box').hide();		
			}else{
				$('.scope_box').siblings('.scope_box').hide();	
			}
			$('.type_box label .error_tips').css('display','none')
			PARAM.optionType = type
			FUNC.quantityShow()
		});
		$('body').on('click','#products_recommend_inside .input_checkbox_box',function(){
			let _this = $(this);
			type_status();
		})
		$('body').on('click','.box_type_menu .item', function(){
			let type = $(this).attr('data-type');
			$(this).find('input').prop('checked',true).parent().siblings().find('input').prop('checked',false);
			$(this).addClass('checked').siblings().removeClass('checked');
			$(this).parents('.scope_box').find('.use_group_box[data-value='+ type +']').show().siblings('.use_group_box').hide();
			PARAM.specialType = type
			FUNC.quantityShow()
		})
		$('#edit_form').on('click','.scope_box[data-type=special] .use_group_box[data-value=products] .input_checkbox_box , .btn_attr_choice.current>i',function(){
			let $Obj = $(this).parents('.use_group_box'),
				ItemCount = $Obj.find('.select_list').find('.btn_attr_choice').length,
				$IsChecked = $(this).hasClass('checked') ? 0: 1;
			if($IsChecked && $(this).hasClass('input_checkbox_box')){
				ItemCount = ItemCount + 1;
			}else{
				ItemCount = ItemCount - 1;
			}

			if (ItemCount > 20) {
				ItemCount = 20;
			}
			$('.scope_box[data-type=special] .box_type_menu .item').find('b').html(ItemCount);
			if(ItemCount>0){
				$('.scope_box[data-type=special] .box_type_menu .item').find('b').addClass('change');
			}else{
				$('.scope_box[data-type=special] .box_type_menu .item').find('b').removeClass('change');
			}
		})

		function type_status(){
			$length = 0;
			$('.page_box .input_checkbox_box').each(function(){
				if($(this).hasClass('checked') && ($(this).attr('data-type') != 'goods' && $(this).attr('data-type') != 'cart')){
					$length++;
				}
				if($length > 0){
					$('#products_recommend_inside .input_radio_side_box[data-type=related]').addClass('disabled').removeClass('checked').find('input').prop('checked',false);
					$('#products_recommend_inside .scope_box[data-type=related]').hide();
				}else{
					$('#products_recommend_inside .input_radio_side_box[data-type=related]').removeClass('disabled');
				}
			})
		}
		type_status();
		
		frame_obj.submit_form_init($('#edit_form'),'',function(){
			if($('#edit_form').find('input[name="Type"]:checked').val() == 'related') {
				if(! $('#edit_form').find('input[name="ProductsScope"]:checked').val() ) {
					$('#edit_form').find('.box_type_menu span').css('border', '1px solid red').addClass('null');

					setTimeout(function(){
						$('#edit_form').find('.box_type_menu span').css({border: 'none'});
					}, 1000)
					return false;
				}
			}
			if(!$('input[name=backUrl]').val()){
				if($('#edit_form').find('input[name="Type"]:checked').length == 0 || ($('#edit_form').find('input[name="Page[]"]:checked').length == 0 && $('.show_box .input_radio_box input[name=ShowType]:checked').val() == 'auto')){
					if($('#edit_form').find('input[name="Page[]"]:checked').length == 0){
						$('.page_box label .error_tips').css('display','inline')
					}
					if($('#edit_form').find('input[name="Type"]:checked').length == 0){
						$('.type_box label .error_tips').css('display','inline')
					}
					return false;
				}
			}
			if ($('.quantity_box').is(':visible')) {
				let $qtyObj = $('.quantity_box')
				let $qtyInputObj = $qtyObj.find('input')
				let $qty = $qtyInputObj.val()
				if ($qty < 1 || $qty > 20) {
					$qtyInputObj.css('border', '1px solid red')
					if (!$qtyObj.hasClass('has_error')) {
						$qtyObj.addClass('has_error').append(`<p class="error_tips" style="font-size:14px;">${lang_obj.manage.app.products_recommend.qty_tips}</p>`)
					}
					return false
				} else {
					$qtyInputObj.removeAttr('style')
					$qtyObj.removeClass('has_error')
					$qtyObj.find('.error_tips').remove()
				}
			}
		},0,function(result){
           if(result.ret==1){
                global_obj.win_alert_auto_close(lang_obj.global.saved, 'success', 1000, '8%');
				if($('input[name=backUrl]').val()){
					window.location.href = $('input[name=backUrl]').val();
				}else{
					window.location.href = '/Products/Recommend';
				}
           }
        });
		$('#products_recommend_inside .rows.type_box .input_radio_side_box[data-type=manual]').click();
		$('#edit_form').on('click','.products_number',function(){
			let $this = $(this);
			$this.addClass('current').find('.products_container').fadeIn()
		})
		$('#edit_form').on('mouseleave','.products_number', function(){
			$(this).removeClass('current').find('.products_container').fadeOut();
		});
		$('.pro_data_list').on('click','tbody .btn_checkbox',function(){
			let $obj = $(this),
				$IsChecked = $obj.hasClass('current'),
				$parent = $obj.parents('tbody');
			if($IsChecked){
				$obj.removeClass('current');
			}else{
				$obj.addClass('current');
			}
			$length = $parent.find('.current').length;	//当前勾选
			$total = $parent.find('.btn_checkbox').length;	//总勾选
			if($length>0){
				$obj.find('input').prop('checked',true);
				$parent.prev().find('tr').addClass('current').find('.btn_checkbox').addClass('indeterminate').addClass('current');
				$parent.prev().find('tr').find('.open').find('span').html($length);
			}else{
				$obj.find('input').prop('checked',false);
				$parent.prev().find('tr').removeClass('current').find('.btn_checkbox').removeClass('indeterminate').removeClass('current');
				$parent.prev().find('tr').find('.open').find('span').html($length);
			}
			if($length == $total){
				$parent.prev().find('tr').find('.btn_checkbox').removeClass('indeterminate').addClass('current');
			}
		});
		$('.pro_data_list').on('click','thead .btn_checkbox',function(){
			let $obj = $(this),
				$IsChecked = $obj.hasClass('current');
				$indeterminate = $obj.hasClass('indeterminate');
				$parent = $obj.parents('thead').next('tbody');
			//if($parent.find('.btn_checkbox').length <= 0){
			//	$obj.removeClass('current');
			//	return false;
			//}
			if($IsChecked && $indeterminate){
				$obj.addClass('current').parents('tr').addClass('current');
				$obj.removeClass('indeterminate');
				$parent.find('tr').each(function(){
					$(this).find('.btn_checkbox').addClass('current').find('input').prop('checked',true);
				})
			}else if($IsChecked){
				$obj.addClass('current').parents('tr').addClass('current');
				$parent.find('tr').each(function(){
					$(this).find('.btn_checkbox').addClass('current').find('input').prop('checked',true);
				})
			}else{
				$obj.removeClass('current').removeClass('indeterminate').parents('tr').removeClass('current');
				$parent.find('tr').each(function(){
					$(this).find('.btn_checkbox').removeClass('current').find('input').prop('checked',false);
				})
			}
			$length = $parent.find('.btn_checkbox').length;
			$parent.prev().find('tr').find('.open').find('span').html($length);
		})
		$('#products_box .products_list').on('click', '.p_del', function(){
			var $obj = $(this).parents('.item');
			//global_obj.win_alert(lang_obj.global.del_confirm, function(){
				$obj.fadeOut(300, function(){
					$obj.remove();
					if ($('#products_box .products_list .item').length) {
						$('#products_box .products_list').show();
						$('#products_box .no_data').hide();
					} else {
						$('#products_box .products_list').hide();
						$('#products_box .no_data').show();
					}
					$('#ProNum').html($('#products_box .products_list .item').length);
				});
			//}, 'confirm');
		});

		// 推荐集
		// 添加产品
		frame_obj.fixed_right_products_choice();  // 初始化右侧弹窗产品列表函数
		frame_obj.fixed_right($('#add_btn'), '.fixed_right_products_choice', function($this){
			if (!$('#fixed_right .search_form form input[name=FilterCateId]').length) {
				$('#fixed_right .search_form form').append('<input type="hidden" name="FilterCateId" value="' + $('input[name=CateId]').val() + '" />')
			}
			if (!$('#fixed_right .search_form form input[name=FilterProId]').length) {
				$('#fixed_right .search_form form').append('<input type="hidden" name="FilterProId" value="" />');
			}
			//把已经有的产品ID记录起来
			var $proid = $('#products_box .products_list input[name^="ProId"]').map(function(){
				return $(this).val();
			}).get();
			$proid = $proid.join(',');
			$('#fixed_right .search_form form input[name=FilterProId]').val($proid);
			$('#fixed_right .search_form form').submit();
		});

		frame_obj.fixed_right_products_filter();

		// 预览
		$('.product_menu .btn_menu_view').click(function() {
			var $Url = $(this).parents('.product_menu').data('url');
			window.open($Url);
		});



		frame_obj.submit_form_init($('#fixed_right_products_choice_form'), '', '', '', function (data) {
			if (data.ret == 1) {
				$Count = $('#products_box .products_list .item').length;
				$('#products_box .no_data').hide();
				$('#products_box .products_list').append(data.msg);
				let OrderType = $('input[name=OrderType]').val();
				$('.fixed_right_products_choice').find('.btn_cancel').trigger('click');
				global_obj.win_alert_auto_close(lang_obj.global.add_success, '', 1000, '8%');
			} else {
				global_obj.win_alert_auto_close(data.msg, 'await', 1000, '8%');
			}
			if ($('#products_box .products_list .item').length) {
				$('#products_box .products_list').show();
				$('#products_box .no_data').hide();
			} else {
				$('#products_box .products_list').hide();
				$('#products_box .no_data').show();
			}
			$('#ProNum').html($('#products_box .products_list .item').length);
		});
		//显示页面点击取消提示
		$('body').on('click','.page_box .input_checkbox_box',function(){
			$('.page_box label .error_tips').css('display','none')
		})

		var styleBoxChange = function(){
			let value = $('input[name="ShowPosition"]:checked').val(),
				styleValue = $('input[name="Mode"]:checked').val();
			if(value == 'themes_global_point') {
				$('.style_box').hide();
			} else {
				$('.style_box').show();
			}

			if(!styleValue) {
				$('input[name="Mode"]').parents('.input_radio_box').click();
			}

		}  
		styleBoxChange();
		$('.position_box').on('click', '.input_radio_box', function(){
			setTimeout(() => {
				styleBoxChange();
			}, 100);
		})

	}, 

	fixed_form :function(No){
		frame_obj.fixed_right($('tr[data='+ No +'] .icon_edit'), '.fixed_edit_recommend',function($this){
			let Data = $this.attr('attr-data'),
				No = $this.parents('tr').attr('data');
			$.post('/manage/plugins/products-recommend/products-right/',{'Data':Data,'No':No},function(call_data){
				$('.fixed_edit_recommend').html(call_data.msg);
				frame_obj.submit_form_init($('#right_edit_form'),'','',0,function(call_result){
					if(call_result.ret == 1){
						$('#products_recommend_inside .pro_data_list tbody').append(call_result.msg.Html);
						$('#right_edit_form .btn_cancel').click();
						recommend_obj.fixed_form(call_result.msg.No);
					}else if(call_result.ret == 2){
						$('#products_recommend_inside .pro_data_list tbody tr[data='+call_result.msg.No+']').replaceWith(call_result.msg.Html);
						$('#right_edit_form .btn_cancel').click();
						recommend_obj.fixed_form(call_result.msg.No);
					}
				})
			},'json');
		})
	},

	//recommend_list_init: function(){
	//	let $id = $('#id').val();
	//	if($id){
	//		$.post('/manage/plugins/products-recommend/list/',{'id':$id},function(data){
	//		
	//		},'json')
	//	}
	//}

}