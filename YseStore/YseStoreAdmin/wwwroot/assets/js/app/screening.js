/*

 * 条件筛选 APP
 */

var condition_screening_app_obj = {
	function_init:{
		//禁止相关文字
		prohibit_input: function($Obj) {
			$Obj.off().on('keydown', function(e){
				var $Value = $.trim($(this).val()),
					key = window.event ? e.keyCode : e.which;
				if ((key == 189 && e.shiftKey == true) || key == 219 || key == 221 || key == 220 || key == 191 || (key == 187 && e.shiftKey == true) || key == 107) {
					//禁止输出“_[]”
					return false;
				}
			}).on('keyup', function(e){
				var $Value = $.trim($(this).val());
				$Value = $Value.replace(/([_])/g, "").replace(/([\[])/g, "").replace(/([\]])/g, "").replace(/([\/]])/g, "").replace(/([\\]])/g, "").replace(/([+]])/g, "");
				$(this).val($Value);
			});
		},

		//产品属性选项按钮
		attr_option_button: function(name, attr_name, isCurrent) {
			var html = '';
			if (typeof name != 'string') name = name.toString();
			if (typeof attr_name != 'string') attr_name = attr_name.toString();
			html += '<span class="btn_attr_choice' + (isCurrent ? ' current' : '') + '">';
			html += 	'<b>' + global_obj.htmlspecialchars(name) + '</b>';
			html += 	'<input type="checkbox" name="ScreeningAttrOption[' + global_obj.htmlspecialchars(attr_name) + '][]" value="' + global_obj.htmlspecialchars(name) + '" class="attr_current"' + (isCurrent ? ' checked' : '') + ' />';
			html += 	'<i></i>';
			html += '</span>';
			return html;
		},

		//产品属性选项
		attr_load:function(){
			condition_screening_app_obj.function_init.attr_input();
			
			if($('#edit_condition_screening_form .box_cart_attribute_screening .box_attr ').length==0){
				$Html = condition_screening_app_obj.function_init.add_attr_box();
				$('#edit_condition_screening_form .box_cart_attribute_screening').append($Html);
			}

			$('#edit_condition_screening_form').off().on('click','#add_attribute_screening',function(){
				let remove_length = parseInt($('#edit_condition_screening_form input[name=attrLength]').val());
				$('#edit_condition_screening_form input[name=attrLength]').val(remove_length+1);
				$Html = condition_screening_app_obj.function_init.add_attr_box();
				$('#edit_condition_screening_form .box_cart_attribute_screening').append($Html);
				condition_screening_app_obj.function_init.attr_load();
			});

			$('#edit_condition_screening_form').on('click','.attrDelBtn',function(){
				let _this = $(this),
					box_attr_parents = _this.parents('.box_attr');
				box_attr_parents.remove();
				let remove_length = parseInt($('.box_cart_attribute_screening .box_attr').length)-1;
					remove_length = remove_length<0?0:remove_length;
				$('#edit_condition_screening_form input[name=attrLength]').val(remove_length);
				if($('.box_cart_attribute_screening .add_new_attr').length){ //重置id
					$('.box_cart_attribute_screening .add_new_attr').each(function(){
						let now_attr_length = $('#edit_condition_screening_form input[name=attrLength]').val(),
							now_add_num = 'ADD_'+now_attr_length;
						$(this).attr({'data-id':now_add_num,'data-position':now_add_num});
						$(this).find('input[name=ScreeningAttrTitlehide\\[\\]]').val(now_add_num);
					})
				}
					
			})

			$('#edit_condition_screening_form').on('click','.attrFoldBtn',function(){
				let _this = $(this),
					box_attr_parents = _this.parents('.box_attr');
				
				if(_this.hasClass('down')){
					box_attr_parents.find('.input').each(function(){
						if($(this).hasClass('hide')){
							$(this).removeAttr('style')
						}else{
							$(this).show();
						}
					});
					_this.removeClass('down');
				}else{
					box_attr_parents.find('.input').hide();
					_this.addClass('down');
				}
			})

			$('#edit_condition_screening_form').on('keyup', '.box_attr input[name=ScreeningAttrTitle\\[\\]]' , function(e){
				let _this_val = $(this).val(),
					_this_obj = $(this).parents('.box_attr');
				if (_this_val) {
					_this_obj.find('.attr_top_name').html(_this_val);
					$.post('/manage/plugins/screening/check-attr/',{'value':_this_val},function(data){
						if (data.msg.length !=0) {
							_this_obj.find('.check_attr_list').html('');
							let opt_data = data.msg;
							_this_obj.find('.check_attr_list').show();
							for (k in opt_data) {
								if(opt_data[k]['Name']){
									let to_be_html = '<div class="to_be_option" data-option="'+opt_data[k]['Options']+'">'+opt_data[k]['Name']+'</div>';
									_this_obj.find('.check_attr_list').append(to_be_html);
								}
							}
							//检测属性展示点击
							if ($('.check_attr_list .to_be_option').length > 0) {
								$('#edit_condition_screening_form').on('click','.to_be_option',function(){
									let _opt_item = $(this),
										_opt_attr = global_obj.htmlspecialchars_decode($(this).html());
										_opt_data = _opt_item.data('option');
									_this_obj.find('.attr_not_yet .select_list').html('');
									_this_obj.find('.check_attr_list').hide();
									_this_obj.find('input[name=ScreeningAttrTitle\\[\\]]').val(_opt_attr);
									if(_opt_data){
										for( i in _opt_data){
											let opt_html = condition_screening_app_obj.function_init.attr_option_button(_opt_data[i],_opt_attr);
											_this_obj.find('.attr_not_yet .select_list').append(opt_html);
										}
										condition_screening_app_obj.function_init.attr_load();
									}
								})
								
							}
							$('body').click(function(e){ //没焦点选择框的时候，隐藏
								if($(e.target).parents('.add_attr').length==0){
									_this_obj.find('.check_attr_list').hide();
								}
							});
						}else{
							_this_obj.find('.check_attr_list').hide();
						}
					},'json')
				}
			})

			//属性排序按钮(超过两个属性才显示“属性排序”按钮)
			if ($(".box_cart_attribute_screening .box_attr").length > 1) {
				$("#myorder_attribute").show();
			} else {
				$("#myorder_attribute").hide();
			}

			//属性选项整体按钮
			$('.box_attr_list .btn_attr_choice').off().on('click', function(){
				var $This=$(this),
					$Obj=$This.find('input.attr_current'),
					$Parent=$This.parent().parent(),
					$Box=$This.parents('.box_attr'),
					$LBox=$This.parents('.attr_not_yet'),
					$IsCart=$Box.find('.attr_cart').val();
				if($Parent.hasClass('attr_not_yet')){ //未选择 -> 已选中
					$This.appendTo($Parent.prev().find('.select_list'));
					$This.addClass('current');
					$This.find('i').show();
					$Obj.attr('checked', true);
					$Box.find('.placeholder').addClass('hide');
					if($Parent.find('.btn_attr_choice').length<1){
						$Parent.hide();
					}
				}
				condition_screening_app_obj.function_init.attr_load();
				return false;
			});

			//属性选项全选按钮
			$('.box_attr_list .select_all').off().on('click', function(){
				$(this).parent().find('.btn_attr_choice').click();
				return false;
			});

			//属性选项删除按钮
			$('.box_attr_list .btn_attr_choice>i').off().on('click', function(){
				return false;
			});
			frame_obj.mouse_click($(".box_attr_list .btn_attr_choice>i"), "attr", function($this) {
				var $This=$this.parent(),
					$Obj = $Obj=$This.find('input.attr_current'),
					$Parent = $this.parent().parent().parent(),
					$Index = $this.parent().index(),
					$AttrIndex = parseInt($Obj.data("position")) - 1,
					$ExtAry = [];
				$this.parent().remove();
				$Parent.find('.box_input').val('').hide().removeAttr('style');
				if($Parent.hasClass('screening_attr_selected')){ //已选中 -> 未选择
					$This.removeClass('current');
					$This.appendTo($Parent.next().find('.select_list'));
					$Obj.prop('checked', false);
					$Parent.next().show();
					$Parent.find('.box_input').val('').hide().removeAttr('style');
					if($Parent.find('.btn_attr_choice').length<1){ //没有选项，显示placeholder
						$Parent.find('.placeholder').removeClass('hide');
					}
				}else{ //未选择 -> 删除
					$This.remove();
					if($Parent.find('.btn_attr_choice').length<1){
						$Parent.hide();
					}
				}
				condition_screening_app_obj.function_init.attr_load();
				return false;
			});

			//修改属性选项
			frame_obj.fixed_right($('.btn_option_edit'), '.fixed_edit_attribute_option_edit', function(obj) {
				var $Box = $('.fixed_edit_attribute_option_edit'),
					$Obj = obj.parents('.box_attr'),
					$AttrName = $Obj.children('input').val(),
					$AttrId = $Obj.data('id'),
					$Position = $Obj.data('position'),
					$Data = new Object, $AttrId, $Html = '', $Num = 0;
				$Box.find('.edit_attr_list').html('');
				$Box.find('input[name=AttrId]').val($AttrId);
				$Box.find('.attribute_title').text($AttrName);
				$Html+=	'<div class="rows">';
				$Html+=		'<label>' + lang_obj.manage.global.name + '</label>';
				$Html+=		'<div class="input">';
				$Html+=			'<div class="item clean">';
				$Html+=				'<input type="text" class="box_input fl" name="AttrName" value="' + global_obj.htmlspecialchars($AttrName) + '" size="30" maxlength="255" autocomplete="off" data-position="' + $Position + '" notnull />';
				$Html+=			'</div>';
				$Html+=		'</div>';
				$Html+=	'</div>';
				$Obj.find('.screening_attr_selected .btn_attr_choice').each(function(index, element) {
					$Data[index] = $(this).find('.attr_current').val();
					++$Num;
				});
				if ($Num > 0) {
					$Html+=	'<div class="rows">';
					$Html+=		'<label>' + lang_obj.global.option + '</label>';
					$Html+=		'<div class="input">';
					for (k in $Data) {
						$Html+=		'<div class="item clean" data-position="'+k+'">';
						$Html+=			'<input type="text" class="box_input fl attr_option_value" value="' + global_obj.htmlspecialchars($Data[k]) + '" size="30" maxlength="255" autocomplete="off" notnull />';
						$Html+=			'<div class="default_name fl" title="' + global_obj.htmlspecialchars($Data[k]) + '">' + global_obj.htmlspecialchars($Data[k]) + '</div>';
						$Html+=		'</div>';
					};
					$Html+=		'</div>';
					$Html+=	'</div>';
				}
				$Box.find('.edit_attr_list').html($Html);
				$Box.find('.edit_tips, .edit_attr_list, .box_button').show();
				$Box.find('.bg_no_table_data').hide();
				condition_screening_app_obj.function_init.prohibit_input($Box.find("input[name=AttrName]"));
				condition_screening_app_obj.function_init.prohibit_input($Box.find(".attr_option_value"));
			});
		},

		//产品属性选项添加事件
		attr_input:function(){
			//触发属性选项添加文本框
			$('.screening_attr_selected').off().on('click', function(){
				var $Obj=$(this).find('.box_input'),
					$NotYet=$(this).next('.attr_not_yet'),
					$ItemLast=$(this).find('.btn_attr_choice:last'),
					$ItemLastLeft=($ItemLast.length?$ItemLast.offset().left:0),
					$BoxLeft=0, $BoxWidth=0;
				$(this).addClass('selected_focus');
				if($NotYet.find('.btn_attr_choice').length){
					$NotYet.slideDown();
				}
				$Obj.show().focus();
				$BoxLeft=($ItemLastLeft?($ItemLastLeft+$ItemLast.outerWidth(true))-$(this).offset().left:0);
				$BoxWidth=($(this).outerWidth()-$BoxLeft-31);
				if($BoxWidth<20){ //小于20，自动换行
					$Obj.css({'width':$(this).outerWidth()-41});
				}else{
					$Obj.css({'width':$BoxWidth, 'position':'absolute', 'bottom':5, 'left':$BoxLeft});
				}
				$Obj.off().on('blur', function() {
					//文本框失去焦点
					$Obj.val('').hide().removeAttr('style');
				}).on('keydown', function(e){
					var $Value = $.trim($(this).val()),
						key = window.event ? e.keyCode : e.which;
					if ((key == 189 && e.shiftKey == true) || key == 219 || key == 221 || key == 220 || key == 191 || (key == 187 && e.shiftKey == true) || key == 107) {
						//禁止输出“_[]”
						return false;
					}
					if (key == 188 && e.shiftKey == false && !$Value) {
						//禁止只输出“,”
						return false;
					}
				}).on('keyup', function(e) {
					//属性选项添加
					var $Value = $.trim($(this).val()),
						key = window.event ? e.keyCode : e.which,
						html = '';
					if (key == 13 || (key == 188 && !e.shiftKey)) {
						//回车键 逗号
						if ($Value) {
							var $This = $(this),
								obj = $This.parents('.box_attr'),
								key = obj.attr('data-id'),
								position = obj.data('position'),
								AttrName = obj.children('input:hidden').val(), //属性名称
								Stop = 0, $ValueData = [];
							$Obj.val('').hide().removeAttr('style');
							$Value = $Value.replace(/([_])/g, "").replace(/([\[])/g, "").replace(/([\]])/g, "").replace(/([\/]])/g, "").replace(/([\\]])/g, "").replace(/([+]])/g, "");
							if ($Value.indexOf(',') != -1) {
								//包含有逗号
								$ValueData = $Value.split(',');
							} else {
								$ValueData[0] = $Value;
							}
							$($ValueData).each(function(index, element) {
								if ($.trim(element) == '') return true;
								Stop = 0;
								$This.parents('.screening_attr_selected').find('.attr_current').each(function() {
									//当前一行
									if ($(this).val() == element) {
										Stop = 1;
										return false;
									}
								});
								$This.parents('.box_attr_list').find('.attr_not_yet .attr_current').each(function() {
									//当前一行
									if ($(this).val() == element) {
										Stop = 1;
										$(this).parent('.btn_attr_choice').click();
										return false;
									}
								});
								if (Stop == 1) {
									//终止继续执行
									return true;
								}
								if (obj.length) {
									//确保在属性选项列表里面才触发
									$Obj.prev(".select_list").append(condition_screening_app_obj.function_init.attr_option_button(element, AttrName, 1));
								}
							});
							condition_screening_app_obj.function_init.attr_load();
							$Obj.next('.placeholder').addClass('hide');
							$Obj.click();
						}
						if (window.event) {
							//IE
							e.returnValue = false;
						} else {
							//Firefox
							e.preventDefault();
						}
						return false;
					}
				});
			});

		},

		add_attr_box:function(){
				let $Html = '',
					attrLength = $('#edit_condition_screening_form input[name=attrLength]').val();	
				$Html+=	'<div class="box_attr add_new_attr clean" data-id="ADD_'+attrLength+'" data-position="ADD_'+attrLength+'">';
				$Html+=		'<div class="rows clean">';
				$Html+=			'<div class="attr_top_show">';
				$Html+=				'<span class="attr_top_name"></span>';
				$Html+=				'<i class="attrFoldBtn icon-arrow1"></i>';
				$Html+=				'<i class="attrDelBtn icon-delete1"></i>';
				$Html+=			'</div>';
				$Html+=			'<div class="input add_attr">';
				$Html+=				'<label>'+lang_obj.manage.app.screening.condition_title+'</label>';
				$Html+=				'<input type="text" name="ScreeningAttrTitle[]" autocomplete="off" notnull value="" placeholder="'+lang_obj.manage.app.screening.attr_placeorder+'" class="add_attr_title" />';
				$Html+=				'<div class="check_attr_list"></div>';
				$Html+=			'</div>';
				$Html+=			'<div class="input add_input">';
				$Html+=				'<label>'+lang_obj.global.option+'</label>';
				$Html+=				'<div class="box_attr_list">';
				$Html+=					'<div class="screening_attr_selected">';
				$Html+=						'<div class="select_list">';
				$Html+=						'</div>';
				$Html+=						'<input type="text" class="box_input" name="_Attr" value="" size="30" maxlength="255" />';
				$Html+=						'<span class="placeholder">' + lang_obj.manage.products.placeholder + '</span>';
				$Html+=					'</div>';


				$Html+=					'<div class="attr_not_yet">';
				$Html+=						'<div class="select_list">';
				$Html+=						'</div>';
				$Html+=						'<a href="javascript:;" class="select_all">'+lang_obj.global.select_all+'</a>';
				$Html+=					'</div>';

				$Html+=				'</div>';
				$Html+=			'</div>';
				$Html+=		'</div>';
				$Html+=		'<input type="hidden" name="ScreeningAttrTitlehide[]" value="ADD_'+attrLength+'" class="attr_title" />';
				$Html+=	'</div>';
				return $Html;
		},

	},

	condition_screening_init: function() {
		//禁止使用回车键提交
		$('#condition_screening').off().on('keydown', '.btn_edit_condition_screening',function(e) {
			var key = window.event ? e.keyCode : e.which;
			if (key == 13) {
				//禁止使用回车键
				if (window.event) {
					//IE
					e.returnValue=false;
				} else {
					//Firefox
					e.preventDefault();
				}
				return false;
			}
		});
		//产品编辑筛选属性
		frame_obj.fixed_right($('.btn_edit_condition_screening,.box_my_app .item[data-type=screening]'), '.fixed_edit_condition_screening', function($this) {
			let $ProId = $this.data('id');
			if ($this.attr('data-type') == 'screening') { //产品编辑
				$ProId = $('#ProId').val();
			}
			$('#edit_condition_screening_form input[name=ProId]').val($ProId);
			condition_screening_app_obj.condition_screening_fixed();
		});

		// 进入产品编辑页面 默认执行.
		if($('.box_my_app .item[data-type=screening]').length && parseInt($('#ProId').val()) > 0) {
			let _ProId = $('#ProId').val();
			$('#edit_condition_screening_form input[name=ProId]').val(_ProId);
			condition_screening_app_obj.condition_screening_fixed();
		}

		frame_obj.submit_form_init($('#edit_condition_screening_form'), '', function(){
			let attr_item = $('#edit_condition_screening_form .condition_screening_list').find('.box_attr');
			let attr_choice_count = 0;
			attr_item.each(function(){
				attr_choice_count = $(this).find('.screening_attr_selected .btn_attr_choice').length;
				if(attr_choice_count==0){
					$(this).find('.box_attr_list .screening_attr_selected').css('border', '1px solid red');
					return false;
				}else{
					$(this).find('.box_attr_list .screening_attr_selected').css('border', '1px #ccdced solid');
				}
			})
			if(attr_item.length && attr_choice_count==0) return false;
		},0,function(result){
			condition_screening_app_obj.check_save_screening_data(result.msg)
			global_obj.win_alert_auto_close(lang_obj.global.save_success, '', 1000, '8%');
			$('#fixed_right .btn_cancel').click();
			return false;
		});
		if (!$('#products_inside').length) {
			$('#condition_screening .inside_menu').insideMenu();
		}
	},

	check_save_screening_data: function(data) {
		let _saveObj = $('#edit_form input[name=APP\\[screening\\]\\[screeningUpdate\\]]');
		if (!_saveObj.length) {
			$('#edit_form').append('<input type="hidden" name="APP[screening][screeningUpdate]" value="" />');
		}
		$('#edit_form input[name=APP\\[screening\\]\\[screeningUpdate\\]]').val(data);
	},

	condition_screening_fixed: function() {
		var $ProId = $('#edit_condition_screening_form input[name=ProId]').val();
		var _temSave = parseInt($('#edit_condition_screening_form input[name=temSave]').val());
		if (!_temSave) {
			$.post('/manage/plugins/screening/screening-get-attr', {"ProId":$ProId}, function(data) {
				$('#edit_condition_screening_form input[name=temSave]').val(1);
				if (data.ret == 1) {
					var $Html='', $Attr=new Object, i=0;
					if(data.msg.Attr){
						$Attr = data.msg.Attr;
						$Combinatin = data.msg.Combinatin;
						for(k in $Attr){
							let attrName = $Attr[k]['Name'];
							var i=0;
							$Html+=	'<div class="box_attr clean" data-id="' + $Attr[k].SId + '" data-position="' + $Attr[k].Position + '">';
							$Html+=		'<div class="rows clean">';
							$Html+=			'<div class="attr_top_show">';
							$Html+=				'<span class="attr_top_name">' + $Attr[k]['Name'] + '</span>';
							$Html+=				'<i class="attrFoldBtn icon-arrow1"></i>';
							$Html+=				'<i class="attrDelBtn icon-delete1"></i>';
							$Html+=			'</div>';
							$Html+=			'<div class="input hide">';
							$Html+=				'<label>'+lang_obj.manage.app.screening.condition_title+'</label>';
							$Html+=				'<input type="text" name="ScreeningAttrTitle[]" value="' + global_obj.htmlspecialchars($Attr[k]['Name']) + '" class="add_attr_title" />';
							$Html+=			'</div>';

							$Html+=			'<div class="input">';
							$Html+=				'<div class="box_attr_list">';
							$Html+=					'<div class="screening_attr_selected">';
							$Html+=						'<div class="select_list">';
													if ($Combinatin[attrName] && $Combinatin[attrName].Extend) {//$Attr[k].Options
														for (k2 in $Combinatin[attrName].Extend) {//k2 in $Attr[k].Options && 
															if($Combinatin[attrName].Extend[k2]){
							$Html+=									condition_screening_app_obj.function_init.attr_option_button($Combinatin[attrName].Extend[k2], $Combinatin[attrName].Name, 1);
																++i;
															}
														}
													}
							$Html+=						'</div>';
							$Html+=						'<input type="text" class="box_input" name="_Attr" value="" size="30" maxlength="255" />';
							$Html+=						'<span class="placeholder' + (i > 0 ? ' hide' : '') + '">' + lang_obj.manage.products.placeholder + '</span>';
							$Html+=					'</div>';


							$Html+=					'<div class="attr_not_yet">';
							$Html+=						'<div class="select_list">';
														if($Attr[k]['Options']){
															for(k3 in $Attr[k]['Options']){
																if($Combinatin[attrName]){
																	if($.inArray($Attr[k].Options[k3], $Combinatin[attrName].Extend)==-1){
							$Html+=								condition_screening_app_obj.function_init.attr_option_button($Attr[k].Options[k3], $Attr[k].Name, 0);
																	}
																}else{
							$Html+=								condition_screening_app_obj.function_init.attr_option_button($Attr[k].Options[k3], $Attr[k].Name, 0);
																}
															}
														}
							$Html+=						'</div>';
							$Html+=						'<a href="javascript:;" class="select_all">'+lang_obj.global.select_all+'</a>';
							$Html+=					'</div>';

							$Html+=				'</div>';
							$Html+=			'</div>';
							$Html+=		'</div>';
							$Html+=		'<input type="hidden" name="ScreeningAttrTitlehide[]" value="' + global_obj.htmlspecialchars($Attr[k]['Name']) + '" class="attr_title" />';
							$Html+=	'</div>';
						}
						$('.box_cart_attribute_screening').html($Html);
						let attrLength = parseInt($('.box_cart_attribute_screening .box_attr').length)-1;
						attrLength = attrLength<0?0:attrLength;
						$('#edit_condition_screening_form input[name=attrLength]').val(attrLength);
						condition_screening_app_obj.function_init.attr_load();
						setTimeout(() => {
							$.post('/manage/plugins/screening/data-post', $('#edit_condition_screening_form').serialize() , function(result) {
								if(result.ret == 1) {
									condition_screening_app_obj.check_save_screening_data(result.msg)
								}
							}, 'json')
						}, 500);
					}
					$('.fixed_edit_condition_screening .condition_screening_list').show();
					$('#edit_condition_screening_form .btn_submit').attr('disabled', false);
					condition_screening_app_obj.function_init.attr_load();
				}else{
					$('.fixed_edit_condition_screening .condition_screening_list').hide();
					$('#edit_condition_screening_form .btn_submit').attr('disabled', true);
				}
				return false;
			}, 'json');
		}

		$('body').click(function(e){ //没焦点选择框的时候，隐藏
			if($(e.target).parents('.box_attr_list').length==0){
				$('.screening_attr_selected').removeClass('selected_focus');
				$('.screening_attr_selected').nextAll('.attr_not_yet').slideUp();
			}
		});
	},

	screening_attr_manage: function() {
		// 批量操作
		frame_obj.select_all($('input[name=select_all]'), $('input[name=select]'), $('.table_menu_button .del'));
		// 批量删除
		frame_obj.del_bat($('.table_menu_button .del.btn_switch_delete'), $('input[name=select]'), '/manage/plugins/screening/attr-batch-delete/','','',lang_obj.manage.app.screening.condition_del);
		
		//设置对应高度
		const _attrTable = $('#condition_screening table'); 
		_attrTable.find('tbody tr').each(function(){
			let optionItem = $(this).find('td').eq(2).find('.table_option_item');
			var _associateTd = $(this).find('td').eq(3).find('.associate_option_item');
			optionItem.each(function(index){
				let _thisHeight = $(this).height()
				_associateTd.eq(index).height(_thisHeight)
			})
		})

		frame_obj.submit_form_init($('#edit_options_attr_form'), '', function(){
			
		},0,function(result){
			global_obj.win_alert_auto_close(lang_obj.global.save_success, '', 1000, '8%');
			$('#fixed_right .btn_cancel').click();
			window.location.reload();
			return false;
		});

		//选项
		let option_item = '';
		option_item += '<div class="option_content_row">';
		option_item += '	<em class="option_myorder"></em>';
		option_item += '	<div class="rows clean float">';
		option_item += '		<div class="input"><input name="AttrOption[]" value="%N%" type="text" class="box_input" size="28" notnull><input type="hidden" name="OldAttrOption[]" value=""> </div>';
		option_item += '	</div>';
		option_item += '	<div class="float button">';
		option_item += '		<a href="javascript:;" class="btn_option fl btn_option_add"><i></i></a>';
		option_item += '		<a href="javascript:;" class="btn_option fl btn_option_remove"><i></i></a>';
		option_item += '	</div>';
		option_item += '</div>';

		let attr_obj = {
			'_attrTable': $('.attr_box .r_con_table'),
			'_attrForm': $('#attr_add_form'),
			'add_option': function($Txt,$Name){//添加选项
				$Txt = $Txt.replace('%N%', '');
				$('#attr_add_form .option_row').append($Txt);
				if ($Name) {
					$('#attr_add_form .option_row .option_content_row:last').find('.box_input[name=AttrOption\\[\\]]').val($Name);
					$('#attr_add_form .option_row .option_content_row:last').find('input[name=OldAttrOption\\[\\]]').val($Name);
				}
				if ($('.option_content_row').length > 1) {
					$('.option_content_row:last').siblings().find('.button').addClass('hide_add').removeClass('hide_remove');
				} else {
					$('.option_content_row .button').addClass('hide_remove');
				}
			},
			'remove_option': function(obj) {//移除选项
				obj.parents('.option_content_row').fadeOut(function() {
					$(this).remove();
					$('.option_content_row:last .button').removeClass('hide_add');
					if ($('.option_content_row').length == 1) {
						$('.option_content_row .button').addClass('hide_remove');
					}
				});
			},
		}

		frame_obj.fixed_right($('.attr_add_btn'), '.fixed_attr_add', function($this) {
			let _id = $this.data('id');
			let _showtype = $this.data('showtype');
			attr_obj._attrForm.find('.option_row').html('');
			if (_id) { // 修改属性
				$('.fixed_attr_add .top_title strong').html(lang_obj.manage.app.screening.edit_condition)
				let _option_info = $this.parents('tr').find('.table_option_item'),
					_attr_name = global_obj.htmlspecialchars_decode($this.parents('tr').find('td').eq(1).html());
				attr_obj._attrForm.find('input[name=Name]').val(_attr_name);
				console.log(_showtype);
				attr_obj._attrForm.find('select[name=ShowType] option[value='+_showtype+']').attr('selected', 'selected');
				_option_info.each(function(){
					let _option_push_name = global_obj.htmlspecialchars_decode($(this).html());
					attr_obj.add_option(option_item,_option_push_name);
				})
				attr_obj._attrForm.find('input[name=SId]').val(_id);
			} else { //添加属性
				attr_obj._attrForm.find('input[name=Name]').val('');
				attr_obj._attrForm.find('input[name=SId]').val('');
				attr_obj.add_option(option_item);
			}

			attr_obj._attrForm.find('input[name=AttrOption\\[\\]]').on('keydown', function(e){
				var $Value = $.trim($(this).val()),
					key = window.event ? e.keyCode : e.which;
				if ((key == 189 && e.shiftKey == true) || key == 219 || key == 221 || key == 220 || key == 191 || (key == 187 && e.shiftKey == true) || key == 107) {
					//禁止输出“_[]”
					return false;
				}
				if (key == 188 && e.shiftKey == false && !$Value) {
					//禁止只输出“,”
					return false;
				}
			})

			//排序
			$('.option_row').dragsort('destroy');
			frame_obj.dragsort($('.option_row'), '', '.option_content_row .option_myorder', '.option_content_row .rows .box_input', '<div class="placeHolder"></div>');
	
		})
		
		frame_obj.submit_form_init($('#attr_add_form'), '', function(){
			
		},0,function(result){
			global_obj.win_alert_auto_close(lang_obj.global.save_success, '', 1000, '8%');
			$('#fixed_right .btn_cancel').click();
			window.location.reload();
			return false;
		});

		$('.option_row').delegate('.btn_option_add', 'click', function() {
			attr_obj.add_option(option_item, '');
		})
		$('.option_row').delegate('.btn_option_remove','click',function(){
			let _this = $(this);
			let params = {
				'title':lang_obj.manage.app.screening.option_del,
				'confirmBtnClass':'btn_warn'
			};
			global_obj.win_alert(params, function(e){
				attr_obj.remove_option(_this);
			}, 'confirm');
		});

	},

	filter_init: function () {
		// 批量操作
		frame_obj.select_all($('input[name=select_all]'), $('input[name=select]'), $('.table_menu_button .del'));
		// 批量删除
		frame_obj.del_bat($('.table_menu_button .del.btn_switch_delete'), $('input[name=select]'), '/manage/plugins/screening/filter-batch-delete/');
		frame_obj.del_init($('#condition_screening .r_con_table tbody')); //删除提示

		frame_obj.box_type_menu()

		// 元素拖动
		if ($('#condition_screening .r_con_table').length) {
			frame_obj.dragsort($('#condition_screening .r_con_table tbody'), '/manage/plugins/screening/filter-order', 'tr .move_myorder', '', '')
		}
		if($('#condition_screening .filter_load_more').length) {
			$('#condition_screening').on('click', '.filter_load_more', function(){
				let href = $(this).attr('href');
				$.get(href, '', function(data){
					result = $(data);
					html = result.find('tbody tr');
					$('#condition_screening').find('tbody').append(html);	
					$('#condition_screening').find('.filter_load_more').remove();
					$('#condition_screening').find('table').parent().append(result.find('.filter_load_more'));
					let currentPage = $('.filter_load_more').attr('current-page'),
						totalPage = $('.filter_load_more').data('page');
					if(currentPage >= totalPage) {
						$('.filter_load_more').remove();
					}
				})

				return false;
			})
		}

		const FUNC = {
			'_fixedBox':$('.fixed_edit_filter_option'),
			'_optionTable':$('.option_table_box .r_con_table'),
			filter_status:function($href, status){
				$.get($href, {Status:status}, function(data){
					if (data.ret == 1) {
						window.location.reload();
					} else {
						global_obj.win_alert_auto_close(data.msg, 'fail', 1000, '8%');
					}
				}, 'json');
			},
		}

		//筛选器切换状态
		$('#condition_screening .r_con_table .locked').click(function(){
			var $href = $(this).attr('href'),
				status = $(this).data('status');
				
			if (status == 'disable') {
				global_obj.win_alert(lang_obj.manage.app.screening.disable, function(){
					FUNC.filter_status($href, status);
				}, 'confirm');
			} else {
				FUNC.filter_status($href, status);
			}
			return false;
		});

		let numID = 0;

		frame_obj.fixed_right($('.option_btn'),'.fixed_edit_filter_option',function(){
			let $trObj = FUNC._optionTable.find("tr[data-number=\"" + numID + "\"]") || [],
			$idValue = $trObj.find(".id_value").val() || 0,
			$nameValue = $trObj.find(".name_value").val() || '',
			$typeValue = $trObj.find(".type_value").val() || 0,
			$keyIdValue = $trObj.find(".keyid_value").val() || 0;
			FUNC._fixedBox.find("input[name=number]").val(numID);
			FUNC._fixedBox.find("input[name=OId]").val($idValue);
			// 初始化内容
			FUNC._fixedBox.find("#edit_option_form input[name=Name]").val("");
			FUNC._fixedBox.find('.box_drop_double .box_select').find('span').html(lang_obj.global.selected);
			FUNC._fixedBox.find('.box_drop_double .box_select').find('.hidden_value').val('');
			FUNC._fixedBox.find('.box_drop_double .box_select').find('.hidden_type').val('');
			if (numID > 0) {
				FUNC._fixedBox.find('#edit_option_form input[name=Name]').val($nameValue);
				FUNC._fixedBox.find('.box_drop_double .box_select').find('span').html($trObj.find('.type').html());
				FUNC._fixedBox.find('.box_drop_double .box_select').find('.hidden_value').val($typeValue);
				FUNC._fixedBox.find('.box_drop_double .box_select').find('.hidden_type').val($keyIdValue);
				$('.fixed_edit_filter_option').find('.top_title strong').html(lang_obj.manage.app.screening.edit_option);
				$('.fixed_edit_filter_option').find('.btn_submit').val(lang_obj.manage.global.save);
			} else {
				$('.fixed_edit_filter_option').find('.top_title strong').html(lang_obj.manage.app.screening.add_option);
				$('.fixed_edit_filter_option').find('.btn_submit').val(lang_obj.global.add);
			}
		});

		$(".content_btn").click(function() { //添加
			numID = 0;
			$(".btn_global_add").click();
		});
		FUNC._optionTable.on("click", ".icon_edit", function() { //修改
			numID = parseInt($(this).parents("tr").data("number"));
			$(".option_btn").click();
		}).on("click", ".icon_del", function() { //删除
			$(this).parents("tr").animate({"opacity": 0}, 500, function() {
				$(this).remove();
				$("#button_float_tips").remove(); // 防止删掉提示框不消失
				if (FUNC._optionTable.find("tbody tr").length == 0) {
					FUNC._optionTable.hide();
					$('.option_table_box .no_data').fadeIn();
				}
			});
		});

		FUNC._fixedBox.on('click','.btn_submit',function(){
			// 保存
			let html = '',
				_number = FUNC._fixedBox.find("input[name=number]").val(),
				_editFrom = FUNC._fixedBox.find('#edit_option_form');
			if(global_obj.check_form(_editFrom.find('*[notnull]'), _editFrom.find('*[format]'), 1)){return false;};
			if(_editFrom.find('input.hidden_value').val() == 4 && _editFrom.find('input.hidden_type').val() == 'screening') {
				global_obj.win_alert_auto_close(lang_obj.manage.app.screening.screening_attr_tips, 'await', 1000, '8%');
				return false;
			}
			let  _name = FUNC._fixedBox.find('input[name=Name]').val(),
				_typeNmae = FUNC._fixedBox.find('.box_drop_double .box_select').find('span').html(),
				_type = FUNC._fixedBox.find('.box_drop_double .box_select').find('.hidden_value').val(),
				_keyid = FUNC._fixedBox.find('.box_drop_double .box_select').find('.hidden_type').val(),
				_in_ary_sub = ['products_category_sub', 'attributes_sub', 'screening_sub'];
				_subObj ={'products_category_sub':0, 'attributes_sub':3, 'screening_sub':4},
				_subAry = [];
			
			for(i in _subObj){
				_subAry[i] = _subObj[i];
			}

			if(_number == 0){
				FUNC._optionTable.show();
				$('.option_table_box .no_data').hide();
				html += '<tr class="option_item" data-number="'+_number+'">';
				html += '	<td nowrap align="center" class="myorder move_myorder" data="move_myorder"><i class="icon_myorder"></i></td>';
				html += '	<td nowrap class="name"></td>';
				html += '	<td nowrap class="type"></td>';
				html += '	<td nowrap class="operation tar">';
				html += '		<a class="icon_edit oper_icon button_tips" href="javascript:;">'+lang_obj.manage.global.edit+'</a>';
				html += '		<a class="icon_del oper_icon button_tips" href="javascript:;">'+lang_obj.global.del+'</a>';
				html += '		<input type="hidden" class="id_value" name="Option[Id][]" value="">';
				html += '		<input type="hidden" class="name_value" name="Option[Name][]" value="">';
				html += '		<input type="hidden" class="type_value" name="Option[Type][]" value="">';
				html += '		<input type="hidden" class="keyid_value" name="Option[KeyId][]" value="">';
				html += '		';
				html += '	</td>';
				html += '</tr>';

				FUNC._optionTable.append(html);

				// 重新排列number
				FUNC._optionTable.find("tbody>tr").each(function(index, element) {
					$(element).attr("data-number", index + 1);
				});
				
				_number = FUNC._optionTable.find('tbody tr:last').data('number');
				
			}

			let $trObj = $(".option_table_box .r_con_table tr[data-number=\"" + _number + "\"]");
			$trObj.find("td").eq(1).html(_name);
			if($.inArray(_keyid,_in_ary_sub) != -1){
				let lang_type = _subAry[_keyid];
				$trObj.find("td").eq(2).html(lang_obj.manage.app.screening.option_type[lang_type]+':'+_typeNmae);
			}else{
				$trObj.find("td").eq(2).html(_typeNmae);
			}
			$trObj.find(".name_value").val(_name);
			$trObj.find(".type_value").val(_type);
			$trObj.find(".keyid_value").val(_keyid);

			FUNC._fixedBox.find('.btn_cancel').click();
			//排序
			$('.option_list').dragsort('destroy');
			frame_obj.dragsort($('.option_list'), '', '.option_item .myorder', '', '<div class="placeHolder"></div>');
		})

		//排序
		$('.option_list').dragsort('destroy');
		frame_obj.dragsort($('.option_list'), '', '.option_item .myorder', '', '<div class="placeHolder"></div>');

		frame_obj.submit_form_init($('.filter_edit_form'),'/manage/plugins/screening/filter/');
	},

	associate_init: function () {
		const FUNC = {
			_list : $('.associate_products_list'),
			_id : $('#condition_screening').find('input[name=id]').val(),
			_attr : $('#condition_screening').find('input[name=attr]').val(),
			addProItem : function(id,name,picpath){
				let _html = `
					<tr data-id="${id}">
						<td><div class="btn_checkbox"><em class="button"></em><input type="checkbox" name="select" value="${id}"></div></td>
						<td class="pro_info">
							<div class="pro_picpath"><img src="${picpath}"></div>
							<div class="pro_name">${name}</div>
						</td>
						<input type="hidden" name="ProId[]" value="${id}">
					</tr>
				`;
				return _html;

			},
			appendData : function (Data,type) {
				let _defaultHtml = '';
				if (Data.length != 0) {
					FUNC._list.find('.box_no_data').hide()
					FUNC._list.find('.associate_table').show()
					for (k in Data) {
						let _ProData = Data[k],
							_id = _ProData['ProId'],
							_name = _ProData['Name_en'],
							_picpath = _ProData['PicPath_0'];
						_defaultHtml += FUNC.addProItem(_id,_name,_picpath);
					}
				} else {
					FUNC._list.find('.box_no_data').show()
					FUNC._list.find('.associate_table').hide()
				}
				if (_defaultHtml) {
					if (type == 'html') {
						FUNC._list.find('.associate_table tbody').html(_defaultHtml)
					} else if (type == 'append') {
						FUNC._list.find('.associate_table tbody').append(_defaultHtml)
					}
				}
				setTimeout(()=>{
					frame_obj.offBtnCheckbox($('.associate_table tbody .btn_checkbox'));
					frame_obj.btnCheckbox($('.associate_table tbody .btn_checkbox'));
					// 批量操作
					frame_obj.select_all($('input[name=select_all]'), $('input[name=select]'), $('.table_menu_button .del'));
				},50)
			},
			createDefaultProList : function (id,attr) {
				$.post('/manage/plugins/screening/create-default-pro', {id:id,attr:attr}, (data) => {
					FUNC._list.find('.loading').hide()
					if (data.ret == 1) {
						let _Date = data.msg;
						FUNC.appendData(_Date,'html')
					} else {
						$('.box_no_data').show()
					}
				},'json')
			}
		}

		FUNC.createDefaultProList(FUNC._id,FUNC._attr);

		// 删除
		FUNC._list.on('click', '.del.btn_switch_delete', function(){
			let params = {
				'title':lang_obj.manage.app.screening.option_del,
				'confirmBtnClass':'btn_warn'
			};
			global_obj.win_alert(params, function(){
				FUNC._list.find('input[name=select]').each(function(index,element) {
					if ($(element).get(0).checked) {
						$(element).parent().click()
						let _thisparents = $(element).parents('tr');
						_thisparents.remove()
					}
				});
			}, 'confirm')
		})

		//添加产品
		$('#condition_screening').on('click', '.btn_add_item' ,function(){
			let params = {iframeTitle: lang_obj.manage.view.select_products, type: 'manual',  isOrder: false, value: {}, valueOrder: []};
			let value = {};
			let order = [];
			let excludeDate = [];
			FUNC._list.find('tbody tr').each(function(){
				let proid = $(this).attr('data-id');
				let image = $(this).find('.pro_picpath img').attr('src')
				value[proid] = {image: image};
				order.push(proid);
				excludeDate.push(proid);
			});
			params.value = value;
			params.valueOrder = order;
			params.excludeValue = excludeDate;
			frame_obj.products_choice_iframe_init_v2({
				params: params,
				onSubmit: function(data){
					if (!$.isEmptyObject(data.value)) {
						let _addProAry = [];
						for( i in data.value) {
							_addProAry.push(data.value[i].proid)
						}
						if (_addProAry.length) {
							$.ajax({
								url: '/manage/plugins/screening/associate-pro',
								data: {ProId:_addProAry},
								type: 'post',
								dataType: 'json',
								async: false,
								success: function(result) {
									if (result.ret == 1) {
										let _typeAppend = 'html';
										if (FUNC._list.find('tbody tr').length > 0) {
											_typeAppend = 'append';
										}
										FUNC.appendData(result.msg, _typeAppend)
									} else {
										global_obj.win_alert_auto_close(result.msg, "await", 1000, "8%");
									}
								}
							})
						}
						global_obj.win_alert_auto_close(lang_obj.global.add_success, '', 1000, '8%')
					} else {
						FUNC._list.find('table').hide()
						FUNC._list.find('table tbody').html('')
						$('.box_no_data').show()
						global_obj.win_alert_auto_close(lang_obj.manage.error.no_prod_data, 'await', 1000, '8%')
					}
				}
			});
		})

		frame_obj.submit_object_init($('.associate_global_form'), '',function() {
			
		}, '', function(result) {
			if (result.ret == 1) {
				global_obj.win_alert_auto_close(lang_obj.global.save_success, '', 500, '8%');
				setTimeout(function() {
					window.location = '/manage/plugins/screening/attr';
				}, 500);
			} else {
				$('#edit_form').find('input:submit').removeAttr('disabled');
				global_obj.win_alert_auto_close(result.msg, 'fail', 1000, '8%');
			}
		});
	}
}

$(function() {
	if ($('#products_inside').length) {
		condition_screening_app_obj.condition_screening_init();
	}
});