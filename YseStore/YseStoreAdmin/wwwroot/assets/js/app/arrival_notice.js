var arrival_notice_obj = {
	data: {
		"template": "arrival_notice",
		"current_id": 0,
		"processing": ""
	},
	
	arrival_notice_init: function() {
		$("#arrival_notice .btn_notify").click(function() {
			// 发送通知
			if ($(this).hasClass("disabled")) return false;
			let id = $(this).parents("tr").data("id")
			$.post(
				"/manage/plugins/arrival-notice/notify",
				{
					"id": id
				},
				function(result) {
					if (result.ret == 1) {
						let startTime = result.msg.startTime;
						global_obj.win_alert_auto_close(result.msg.tips, "loading", -1, "8%")
						arrival_notice_obj.data.processing = setInterval(function() {
							$.post(
								"/manage/plugins/arrival-notice/processing",
								{
									"id": id,
									"startTime": startTime
								},
								function(resultTo) {
									if (resultTo.ret == "completed") {
										// 已完成
										global_obj.win_alert_auto_close(resultTo.msg, "", 2000, "8%")
									} else if (resultTo.ret == "failed") {
										// 通知失败
										global_obj.win_alert_auto_close(resultTo.msg, "fail", 2000, "8%")
									}
									if (resultTo.ret != "continue") {
										// 停止计时器
										clearInterval(arrival_notice_obj.data.processing);
										$("#arrival_notice .r_con_table tr[data-id=\"" + id + "\"] .await_count").text("0");
										$("#arrival_notice .r_con_table tr[data-id=\"" + id + "\"] .btn_notify ").addClass("disabled");
									}
								},
								"json"
							)
						}, 3000)
					} else {
						global_obj.win_alert({
							"title": result.msg,
							"confirmBtn": lang_obj.global.ok2
						})
					}
				},
				"json"
			)
			return false
		})

		$("#arrival_notice .btn_detail").click(function() {
			// 查看详细
			arrival_notice_obj.data.current_id = $(this).parents("tr").data("id")
			$("#btn_detail_global").click()
			return false
		})

		$(".fixed_detail_info .load_more").click(function() {
			// 查看更多
			let $box = $(".fixed_detail_info")
			let page = parseInt($box.find(".detail_list").attr("data-page"))
			let total = parseInt($box.find(".detail_list").attr("data-total-page"))
			$.post(
				"/manage/plugins/arrival-notice/detail",
				{
					"id": arrival_notice_obj.data.current_id,
					"page": page + 1
				},
				function(result) {
					$box.find(".r_con_table tbody").append(result.html)
					$box.find(".detail_list").attr({
						"data-page": result.page,
						"data-total-page": result.total
					})
					if (result.page >= result.total) {
						$(".fixed_detail_info .load_more").hide()
					}
				},
				"json"
			)
			return false
		})

		frame_obj.fixed_right($("#btn_detail_global"), ".fixed_detail_info", function(_this) {
			let $box = $(".fixed_detail_info")
			// 初始化
			$box.find(".r_con_table tbody").html("")
			$(".fixed_detail_info .load_more").show()
			$.post(
				"/manage/plugins/arrival-notice/detail",
				{
					"id": arrival_notice_obj.data.current_id
				},
				function(result) {
					$box.find(".r_con_table tbody").append(result.html)
					$box.find(".detail_list").attr({
						"data-page": result.page,
						"data-total-page": result.total
					})
					if (result.page >= result.total) {
						$(".fixed_detail_info .load_more").hide()
					}
				},
				"json"
			);
		})


		frame_obj.fixed_right($('.mail_method_btn'), '.fixed_send_type');


		frame_obj.submit_form_init($('#send_type_form'), '', '','', function(result){
			if(result.ret == 1) {
				global_obj.win_alert_auto_close(lang_obj.global.save_success, 'success', '2000', '20%');
				$('.send_type_tips').text(result.msg);
				$('#send_type_form').find('.btn_cancel').click();
			}
		})

	},

	arrival_notice_config: function() {
		$(".arrival_notice_config .color_style").colpick({
			// 选择颜色
            layout: "hex",
            submit: 1,
            onChange: function(hsb, hex, rgb, el, bySetColor) {
                $(el).css("background", "#" + hex).val("#" + hex)
            },
            onSubmit: function(hsb, hex, rgb, el) {
                $(el).css("background", "#" + hex).val("#" + hex)
                $(el).colpickHide()
            }
        })

		frame_obj.submit_form_init($("#edit_form"), "", "", "", function(data) {
			if (data.ret == 1) {
				global_obj.win_alert_auto_close(lang_obj.global.save_success, "", 1000, "8%")
				setTimeout(function() {
					window.location.reload()
				}, 800)
			}
		})

		$(".btn_preview_edit").click(function() {
			// 预览
			let formData = $("#edit_form").serialize();
			$.post(
				"/manage/plugins/arrival-notice/preview",
				formData,
				function(data) {
					$("#arrival_notice_preview").fadeIn().find(".preview_box").html(data.msg.html)
					if ($("#arrival_notice_popups").length) {
						$("#arrival_notice_popups").show().animate({"opacity": 1}, 300).addClass("show")
						$("#arrival_notice_popups").attr("data-step", "form")
						$("#arrival_notice_popups").off().on("click", ".popups_btn", function() {
							return false
						})
					}
				},
				"json"
			)
		})
		$("#arrival_notice_preview .go_home").click(function() {
			$("#arrival_notice_preview").fadeOut();
		});
	},

	arrival_notice_email: function() {
		// 重置
		$("#email_edit_form .mail_reset").click(function() {
			global_obj.win_alert(lang_obj.manage.global.reset_tips, function() {
				let $language = $("#email_edit_form select[name=\"EmailLanguage\"]").val();
				$.post(
					"/manage/set/email/templete-reset",
					{
						"template": arrival_notice_obj.data.template,
						"language": $language
					},
					function(data) {
						location.href = location.href
					},
					"json"
				)
			}, "confirm")
			return false
		})

		// 预览
		$("#email_edit_form .mail_preview").click(function() {
			let $language = $("#email_edit_form select[name=\"EmailLanguage\"]").val();
			$.post(
				"/manage/set/email/templete-preview",
				{
					"template": arrival_notice_obj.data.template,
					"language": $language
				},
				function(data) {
					global_obj.div_mask()
					$("#mail_preview_box").fadeIn().find(".overflow_y_auto").html(data.msg).height($("#mail_preview_box").height())
				},
				"json"
			)
		})

		$("html").on("click", "#div_mask, #mail_preview_box .btn_close", function() {
			$("#mail_preview_box, #div_mask").fadeOut(function() {
				global_obj.div_mask(1)
			})
		})

		if ($(".toggle_language").length) {
			let _contentEditor = CKEDITOR.instances.Content;
			var _isModifiedEditor = false;
			_contentEditor.on("key", function() {
				_isModifiedEditor = true;
			})
		}

		// 切换语言
		$(".toggle_language").on("change", "select[name=EmailLanguage]", function() {
			let value = $(this).val()
			let formParent = $("#email_edit_form")
			let language = formParent.find("input[name=language_default]").val()
			if (_isModifiedEditor) {
				let params = {
					"title": lang_obj.manage.set.toggle_tips,
					"confirmBtn": lang_obj.manage.set.continue,
					"confirmBtnClass": "btn_warn"
				};
				global_obj.win_alert(params, function() {
					emailRequest(value)
				}, "confirm", undefined, "", function() {
					$(".toggle_language select[name=EmailLanguage]").val(language)
				});
			} else {
				emailRequest(value)
			}
		})

		function emailRequest(value) {
			let formParent = $("#email_edit_form")
			if (value != "") {
				$.post(
					"/manage/set/email/get-email-language-content/",
					{
						"EmailLanguage": value,
						"Template": arrival_notice_obj.data.template
					},
					function(data) {
						if (data.ret == 1) {
							let getTitle = data.msg.Title
							let getContent = data.msg.Content
							if (getTitle) {
								formParent.find("input[name=Title]").val(getTitle)
							}
							if (getContent) {
								let editorObj = CKEDITOR.instances.Content
								editorObj && editorObj.setData(getContent)
								_isModifiedEditor = false
							}
						}
					},
					"json"
				)
			}
		}

		frame_obj.submit_form_init($("#email_edit_form"), "/manage/plugins/arrival-notice/set")
	}
}