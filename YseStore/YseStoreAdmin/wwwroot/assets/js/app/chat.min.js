var chat_app_obj={chat_init:function(){frame_obj.del_init($("#chat .chat_box")),frame_obj.fixed_right($("#chat .add_btn , #chat .set_add , #chat .list_box .edit"),".box_chat_edit"),frame_obj.submit_form_init($("#chat_edit_form"),"/manage/plugins/chat"),frame_obj.config_switchery($(".used .switchery"),"/manage/plugins/chat/config-switchery","data-config","config"),frame_obj.dragsort($("#chat_box .list_box"),"/manage/plugins/chat/my-order",".list .icon_myorder","",'<div class="list cur"></div>',".list"),frame_obj.dragsort($("#chat_box"),"/manage/plugins/chat/type-my-order",".chat_list .type_myorder","",'<div class="chat_list cur"></div>',".chat_list");const a={0:"skype",1:"whatsApp",2:"email",3:"wechat",4:"trademanager",5:"qq",6:"telegram"};frame_obj.mouse_click($("#PicDetail .upload_btn, #PicDetail .pic_btn .edit"),"img",function(a){frame_obj.photo_choice_init("PicDetail","",1)}),$("#chat_edit_form select").on("change",function(){var t=$(this),i=t.val();$("#Picture , #chat_edit_form .box_explain").hide(),3==i||7==i?($("#Picture").show(),$("#Picture label span").hide(),$(`#Picture label span[data-id="${i}"]`).show()):1==i&&$("#chat_edit_form .whatsapp_tips").show(),$lowValue=a[i],$(`#chat_edit_form .${$lowValue}_tips`).show()}),$("#chat_box .list_box .list .name a.edit , #chat .add_btn , #chat .set_add").on("click",function(){var a="";if($(this).attr("data-chat"))a=$.parseJSON($(this).attr("data-chat"));a?chat_app_obj.form_check(a):chat_app_obj.form_check()}),$("#chat_box .list_box .list .name .del").on("click",function(){var a=$(this).attr("data-cid");$(this);global_obj.win_alert(lang_obj.global.del_confirm,function(){$.post("/manage/plugins/chat/del",{CId:a},function(a){1==a.ret&&window.location.reload()},"json")},"confirm")})},form_check:function(a){var t=$(".box_chat_edit"),i=$("select[name=Type]",t);$(".ubox",t),t.attr("data-chat");if(a){var e=a.CId;$("#Picture, .whatsapp_tips").hide(0),$("#PicDetail .preview_pic a").remove(),$("#PicDetail .upload_btn").css("display","block"),$("input[name=Name]",t).val(a.Name),$("input[name=PicPath]",t).val(a.PicPath),$("input[name=CId]",t).val(e),$("input[name=Account]",t).val(a.Account),i.find("option:selected").prop("selected",!1),i.find('option[value="'+a.Type+'"]').prop("selected",!0),3!=a.Type&&7!=a.Type||($("#Picture").show(0),$("#Picture label span").hide(),$(`#Picture label span[data-id="${a.Type}"]`).show(),a.PicPath?($("#PicDetail .img").addClass("isfile").find(".preview_pic").append(frame_obj.upload_img_detail(a.PicPath)).children(".upload_btn").hide(0),$("#PicDetail .zoom").attr("href",a.PicPath)):$("#PicDetail .img").removeClass("isfile").find(".preview_pic").children(".upload_btn").show(0)),1==a.Type&&$(".whatsapp_tips").show(0);let c=a.Type.toLowerCase();$(`.${c}_tips`).show(0)}else $("#Picture, .whatsapp_tips").hide(0),$("#PicDetail .preview_pic a").remove(),$("#PicDetail .upload_btn").css("display","block"),$("#PicDetail .img").removeClass("isfile").find(".preview_pic").children(".upload_btn").show(0),$("#PicDetail .zoom").attr("href","javascript:;"),$("input[name=Name]",t).val(""),$("input[name=CId]",t).val(""),$("input[name=PicPath]",t).val(""),$("input[name=Account]",t).val(""),i.find("option:selected").prop("selected",!1),i.find("option:eq(0)").prop("selected",!0),i.change()}};