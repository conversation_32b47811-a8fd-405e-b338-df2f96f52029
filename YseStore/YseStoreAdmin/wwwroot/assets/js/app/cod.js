

var cod_obj = {
	function_init: {
		products_list: () => {
			let $id = $('#products_info').val();
			$.post('/manage/plugins/cod/info-list', {"type": "product", "id": $id},
				function (result) {
					if (result.ret == 1) {
						$('.product_table>tbody').html(result.msg.html)
						if ($('.loading_img').length > 0) {
							$('.loading_img').each(function(index, element) {
								$(element).attr('src', $(element).data('src'))
							})
						}
						if (result.msg.data.isVirtual == 1) {
							// 虚拟产品
							$('.shipping_container').hide()
						} else {
							// 普通产品
							$('.shipping_container').show()
						}
					}
					if ($('.product_table tbody tr').length > 0) {
						$('.product_table').show()
					} else {
						$('.product_table').hide()
					}
					cod_obj.function_init.judge_submit()
				},
				'json'
			)
		},
		choose_list: (type) => {
			let $dataInfo = $('#' + type + '_info').val()
			$.post('/manage/plugins/cod/info-list', {"type": type, 'data': $dataInfo},
				function (result) {
					if (result.ret == 1) {
						$('.option_list[data-type="' + type + '"] .input').html(result.msg.html);
					}
					cod_obj.function_init.judge_submit()
				},
				'json'
			)
		},
		check_submit: () => {
			let result = true
			let $productInfo = $('#product_info').val()
			let $languageInfo = $('#language_info').val()
			let $shippingInfo = $('#shipping_info').val()
			let $paymentInfo = $('#payment_info').val()
			let $shippingVisible = $('.shipping_container').is(':visible')
			$shippingInfo = global_obj.json_encode_data($shippingInfo)
			$paymentInfo = global_obj.json_encode_data($paymentInfo)

			if ($productInfo == '' || $productInfo == 0 || $languageInfo == '' || ($shippingVisible === true && ($shippingInfo == '' || $shippingInfo.length == 0)) || $paymentInfo == '' || $paymentInfo.length == 0) result = false

			return result
		},
		judge_submit: () => {
			let result = cod_obj.function_init.check_submit()
			if (result == true) {
				$('.fixed_btn_submit .btn_submit').attr('disabled', false)
			} else {
				$('.fixed_btn_submit .btn_submit').attr('disabled', true)
			}
		},
		change_tmp_drafts : (DraftsId, PageUrl) => {
			let Data = {"DraftsId": DraftsId, "returnType": 1}
			if (DraftsId) {
				$.post('/manage/view/visual-v2/change-tmp-drafts/', Data,
					function (result) {
						if (result.ret == 1) {
							DraftsId = result.msg
							window.location.href = '/manage/view/visual-v2/edit?DraftsId=' + DraftsId + '&publicFull=1&store=' + PageUrl
						}
					},
					'json'
				)
			}
			return false;
		}
	},
	cod_index: () => {
		frame_obj.select_all($('input[name=select_all]'), $('input[name=select]'), $('.table_menu_button').find('.del')); // 批量操作
		frame_obj.del_bat($('.table_menu_button .del'), $('input[name=select]'), '/manage/plugins/cod/del'); // 批量删除

		$('#cod').on('click', '.btn_design', function() {
			let draftsId = $('#cod input[name=DraftsId]').val()
			let url = $(this).attr('data-store')
			cod_obj.function_init.change_tmp_drafts(draftsId, url)
		})

		// 前往新版
		$('.go_to_new').on('click', function () {
			$.post('/manage/view/visual/switch-version', (data) => {
				if (data.ret == 1) window.open($(this).data('url'), '_blank')
			}, 'json')
		})
	},

	cod_view: () => {
		cod_obj.function_init.products_list()
		cod_obj.function_init.choose_list('language')
		cod_obj.function_init.choose_list('shipping')
		cod_obj.function_init.choose_list('payment')

		// 公共事件
		$('.option_list').on('click', '.btn_attr_choice>i', function (event) {
			event.stopPropagation()
			let $listObj = $(this).parents('.option_list')
			let $type = $listObj.attr('data-type')
			let $value = $(this).parent().attr('data-value')
			let $dataInfo = $('#' + $type + '_info').val()
			if ($type == 'language') {
				// 语言
				$('#' + $type + '_info').val('')
			} else if ($type == 'shipping' || $type == 'payment') {
				// 物流方式 or 付款方式
				$value = parseInt($value).toString()
				$dataInfo = global_obj.json_encode_data($dataInfo)
				let position = $.inArray($value, $dataInfo)
				if (position != -1) $dataInfo.splice(position, 1)
				if ($dataInfo.length > 0) {
					$dataInfo = global_obj.json_decode_data($dataInfo)
				} else {
					$dataInfo = ''
				}
				$('#' + $type + '_info').val($dataInfo)
			}
			$(this).parent().remove()
			cod_obj.function_init.judge_submit()
		})
		$('.radio_choose_list').on('click', '.choose_item', function (event) {
			event.stopPropagation()
			$(this).addClass('current').find('.btn_radio').addClass('current').find('input').prop('checked', true)
			$(this).siblings().removeClass('current').find('.btn_radio').removeClass('current').find('input').prop('checked', false)
		})
		$('.checkbox_choose_list').on('click', '.choose_item', function (event) {
			event.stopPropagation()
			let $this = $(this)
			let $formObj = $this.parents('.global_form')
			if ($this.hasClass('current')) {
				// 去选
				$this.find('.btn_checkbox input').prop('checked', false)
				$this.removeClass('current')
				$this.find('.btn_checkbox').removeClass('current')
			} else {
				// 选中
				$this.find('.btn_checkbox input').prop('checked', true)
				$this.addClass('current')
				$this.find('.btn_checkbox').addClass('current')
			}
			if ($formObj.find('.checkbox_choose_list .choose_item.current').length > 0) {
				// 已有勾选选项
				$formObj.find('.btn_submit').prop('disabled', false);
			} else {
				// 没有勾选选项
				$formObj.find('.btn_submit').prop('disabled', true);
			}
			return false
		})
		$('#fixed_right .global_form[id!=""]').on('click', '.select_all_box .input_checkbox_box', function () {
			let $this = $(this)
			let $obj = $this.parents('.global_form')
			if ($this.hasClass('checked')) {
				$obj.find('.checkbox_choose_list .choose_item:not(".disabled")').removeClass('current');
				$obj.find('.checkbox_choose_list .choose_item:not(".disabled") .btn_checkbox').removeClass('current');
				$obj.find('.checkbox_choose_list .choose_item:not(".disabled") .btn_checkbox input').prop('checked', false);
			} else {
				$obj.find('.checkbox_choose_list .choose_item:not(".disabled")').addClass('current');
				$obj.find('.checkbox_choose_list .choose_item:not(".disabled") .btn_checkbox').addClass('current');
				$obj.find('.checkbox_choose_list .choose_item:not(".disabled") .btn_checkbox input').prop('checked', true);
			}
			if ($obj.find('.checkbox_choose_list .choose_item.current').length > 0) {
				// 已有勾选选项
				$obj.find('.btn_submit').prop('disabled', false);
			} else {
				// 没有勾选选项
				$obj.find('.btn_submit').prop('disabled', true);
			}
		})

		// 加购方式
		frame_obj.box_type_menu()

		//添加产品
		$('#cod').on('click', '.big_title .btn_choose[data-type=product]' ,function(){
			let params = {iframeTitle: lang_obj.manage.view.select_products, type: 'manual', value: {}, isOrder: true, valueOrder: [], limit: 1, hiddenRightBox:1};
			let value = {};
			let order = [];
			let excludeDate = [];
			$('#cod .cod_edit_form tbody tr').each(function(){
				let proid = $(this).attr('data-id');
				let image = $(this).find('.pro_info img').attr('src')
				value[proid] = {image: image};
				order.push(proid);
				excludeDate.push(proid);
			});
			params.value = value;
			params.valueOrder = order;
			params.excludeValue = excludeDate;
			frame_obj.products_choice_iframe_init_v2({
				params: params,
				onSubmit: function(data){
					if (!$.isEmptyObject(data.value)) {
						let _addProAry = data.value[0].proid;
						if (_addProAry) {
							$.post('/manage/plugins/cod/add-info',{ProId:_addProAry,type: 'product'}, function(result){
								if (result.ret == 1) {
									$('#products_info').val(result.msg.data)
									cod_obj.function_init.products_list()
								} else {
									global_obj.win_alert_auto_close(result.msg, 'await', 1000, '8%')
								}
							},'json')
						}
						global_obj.win_alert_auto_close(lang_obj.global.add_success, '', 1000, '8%')
					} else {
						$('#products_info').val('')
						cod_obj.function_init.products_list()
						global_obj.win_alert_auto_close(lang_obj.manage.error.no_prod_data, 'await', 1000, '8%')
					}
				}
			});
		})

		$('.product_table').on('click', '.icon_delete', function () { 
            $(this).parents('tr').remove()
            $('#products_info').val('')
			$('.product_table').hide()
			$('.shipping_container').show()
			$(".fixed_btn_submit .btn_submit").attr("disabled", true)
        })

		// 选择语言
		frame_obj.fixed_right($(".big_title .btn_choose[data-type=language]"), ".fixed_language_choose",
			function ($this) {
				let $language = $('#language_info').val()
				$('.fixed_language_choose .choose_list .choose_item').removeClass('current').find('.btn_radio').removeClass('current').find('input').prop('checked', false)
				$('.fixed_language_choose .choose_list .choose_item').each(function (index, element) {
					if ($(element).find('input').val() == $language) {
						$(element).addClass('current').find('.btn_radio').addClass('current').find('input').prop('checked', true)
					}
				})
			}
		)
		frame_obj.submit_form_init($('#language_choose_form'), '', '', '',
			function (result) {
				if (result.ret == 1) {
					let type = result.msg.type
					let data = result.msg.data
					$('#' + type + '_info').val(data)
					cod_obj.function_init.choose_list(type)
					global_obj.win_alert_auto_close(lang_obj.global.add_success, '', 1000, '8%')
					$('.fixed_right_products_choice').find('.btn_cancel').trigger('click')
				} else {
					global_obj.win_alert_auto_close(result.msg, 'await', 1000, '8%')
				}
			}
		)

		// 选择物流方式
		frame_obj.fixed_right($(".big_title .btn_choose[data-type=shipping]"), ".fixed_shipping_choose",
			function($this) {
				let $shipping = $('#shipping_info').val()
				$shipping = global_obj.json_encode_data($shipping)
				let $obj = $('.fixed_shipping_choose')
				$obj.find('.choose_list .choose_item').removeClass('current').find('.btn_checkbox').removeClass('current').find('input').prop('checked', false)
				$obj.find('.choose_list .choose_item').each(function(index, element) {
					if ($.inArray($(element).find('input').val(), $shipping) != -1) {
						$(element).addClass('current').find('.btn_checkbox').addClass('current').find('input').prop('checked', true)
					}
				})
				if ($obj.find('.checkbox_choose_list .choose_item.current').length > 0) {
					// 已有勾选选项
					$obj.find('.btn_submit').prop('disabled', false);
				} else {
					// 没有勾选选项
					$obj.find('.btn_submit').prop('disabled', true);
				}
			}
		)
		frame_obj.submit_form_init($('#shipping_choose_form'), '', '', '',
			function (result) {
				if (result.ret == 1) {
					let type = result.msg.type
					let data = result.msg.data
					data = global_obj.json_decode_data(data)
					$('#' + type + '_info').val(data)
					cod_obj.function_init.choose_list(type)
					global_obj.win_alert_auto_close(lang_obj.global.add_success, '', 1000, '8%')
					$('.fixed_right_products_choice').find('.btn_cancel').trigger('click')
				} else {
					global_obj.win_alert_auto_close(result.msg, 'await', 1000, '8%')
				}
			}
		)

		// 选择付款方式
		frame_obj.fixed_right($(".big_title .btn_choose[data-type=payment]"), ".fixed_payment_choose",
			function ($this) {
				let $payment = $('#payment_info').val()
				$payment = global_obj.json_encode_data($payment)
				let $obj = $('.fixed_payment_choose')
				$obj.find('.choose_list .choose_item').removeClass('current').find('.btn_checkbox').removeClass('current').find('input').prop('checked', false)
				$obj.find('.choose_list .choose_item').each(function(index, element) {
					if ($.inArray($(element).find('input').val(), $payment) != -1) {
						$(element).addClass('current').find('.btn_checkbox').addClass('current').find('input').prop('checked', true)
					}
				})
				if ($obj.find('.checkbox_choose_list .choose_item.current').length > 0) {
					// 已有勾选选项
					$obj.find('.btn_submit').prop('disabled', false);
				} else {
					// 没有勾选选项
					$obj.find('.btn_submit').prop('disabled', true);
				}
			}
		)
		frame_obj.submit_form_init($('#payment_choose_form'), '', '', '',
			function (result) {
				if (result.ret == 1) {
					let type = result.msg.type
					let data = result.msg.data
					data = global_obj.json_decode_data(data)
					$('#' + type + '_info').val(data)
					cod_obj.function_init.choose_list(type)
					global_obj.win_alert_auto_close(lang_obj.global.add_success, '', 1000, '8%')
					$('.fixed_right_products_choice').find('.btn_cancel').trigger('click')
				} else {
					global_obj.win_alert_auto_close(result.msg, 'await', 1000, '8%')
				}
			}
		)

		frame_obj.submit_form_init($('.cod_edit_form'), '', '', '', function (result) {
			if (result.ret == 1) {
				if (result.msg.done == 'jump') {
					cod_obj.function_init.change_tmp_drafts(result.msg.DraftsId, result.msg.PageUrl);
				}
				global_obj.win_alert_auto_close(lang_obj.global.add_success, '', 1000, '8%')
			} else {
				global_obj.win_alert_auto_close(result.msg, 'await', 1000, '8%')
			}
		})
	}
}