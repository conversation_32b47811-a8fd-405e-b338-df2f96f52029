var google_ads_obj={google_ads_init:function(){frame_obj.fixed_right($("#google_ads .id_add"),".google_ads_edit",function(e){let a=e.data("category"),o=e.data("position"),t=e.data("name"),l=e.data("code");a?($(".google_ads_edit input[name=Name]").val(t),$(".google_ads_edit input[name=position]").val(o),$(".google_ads_edit textarea[name=EventCode]").val(l),$(".google_ads_edit input[name=Category]").val(a),$(".google_ads_edit .imitation_select").val(lang_obj.manage.app.ads.category[a]),$(".google_ads_edit .select_ul .item[data-value="+a+"]").addClass("selected").siblings().removeClass("selected")):($(".google_ads_edit input[name=position]").val(-1),$(".google_ads_edit input[name=Name]").val(""),$(".google_ads_edit textarea[name=EventCode]").val(""),$(".google_ads_edit input[name=Category]").val(""),$(".google_ads_edit .imitation_select").val(""),$(".google_ads_edit .select_ul .item").removeClass("selected"))}),frame_obj.submit_form_init($("#google_ads_set_form"),"","",0,e=>{let a=$("#google_ads_set_form"),o=a.find('textarea[name="EventCode"]');return 0==e.ret?(global_obj.win_alert_auto_close(e.msg,"fail",1e3,"8%"),o.css("border","1px red solid"),o.parents(".rows").find(".error_tips").length||o.parent().prev().append(`<em class="error_tips">${e.msg}</em>`)):(global_obj.win_alert_auto_close(lang_obj.global.save_success,"",1e3,"8%"),$("#fixed_right .btn_cancel").click(),window.location.reload()),!1}),frame_obj.global_select_box(),frame_obj.del_init($("#google_ads .r_con_table"))}};