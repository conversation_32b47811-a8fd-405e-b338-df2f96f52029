var wholesale_app_obj={wholesale_init:function(){const e={batchInit:function(){$(".fixed_edit_wholesale .batch_item_overlay").addClass("show"),$(".fixed_edit_wholesale .batch_item").find("input, select").prop("disabled",!0),e.checkSubmit(),$(".fixed_edit_wholesale").off().on("click",".switchery",function(){let a=$(this).parents(".batch_item");return $(this).hasClass("checked")?($(this).removeClass("checked").find("input").prop("checked",!1),a.find(".switchery, .input_checkbox_box").removeClass("checked").find("input").removeAttr("checked"),a.find(".batch_item_overlay").addClass("show"),a.find("input, select").prop("disabled",!0)):($(this).addClass("checked").find("input").prop("checked",!0),a.find(".batch_item_overlay").removeClass("show"),a.find("input, select").prop("disabled",!1)),e.checkSubmit(),!1})},checkSubmit:function(){$(".fixed_edit_wholesale .switchery.checked").length>0?$('.fixed_edit_wholesale input[type="submit"]').prop("disabled",!1):$('.fixed_edit_wholesale input[type="submit"]').prop("disabled",!0)}};if(frame_obj.select_all($("input[name=select_all]"),$("input[name=select]")),frame_obj.del_bat($(".table_menu_button .del"),$("input[name=select]"),"/manage/plugins/wholesale/batch-del"),$("#wholesale .btn_batch_set").click(function(){let a=$(".fixed_edit_wholesale"),t="";a.length||(t+='<div class="global_container fixed_edit_wholesale" data-width="420"><button id="btn_wholesale_fixed_show"></button></div>',$("#fixed_right").append(t),frame_obj.fixed_right($("#btn_wholesale_fixed_show"),".fixed_edit_wholesale",function(e){$("#fixed_right").addClass("loading")})),a.find(".top_title, #edit_wholesale_form").remove(),$("#btn_wholesale_fixed_show").click();let l=[];$('#wholesale input[name="select"]:checked').each(function(e,a){l[e]=$(a).val()});let i=l.join("|");$.post("/manage/plugins/wholesale/batch","",function(a){if(a){let t=$(a).find("#wholesale_fixed_right").html();$("#fixed_right").removeClass("loading"),$(".fixed_edit_wholesale").append(t),$('.fixed_edit_wholesale input[name="id"]').val(i),wholesale_app_obj.wholesale_fixed(),e.batchInit()}})}),frame_obj.switchery_checkbox(function(e){var a=!0;wholesale_app_obj.wholesale_mixed_batch_init(e,a)},function(e){var a=!1;wholesale_app_obj.wholesale_mixed_batch_init(e,a)},".used_checkbox .switchery"),frame_obj.fixed_right($("#btn_mixed_batch"),".fixed_mixed_batch",function(){var e=$("#edit_mixed_batch_wholesale");$.post("/manage/plugins/wholesale/mixed-batch-setting","",function(a){1==a.ret&&($.inArray("amount",a.msg.mixedBatch.type)>=0?(e.find("input[value=amount]").prop("checked",!0).parent().parent().addClass("checked"),e.find("input[name=amount]").val(a.msg.mixedBatch.amount)):e.find("input[value=amount]").prop("checked",!1).parent().parent().removeClass("checked"),$.inArray("quantity",a.msg.mixedBatch.type)>=0?(e.find("input[value=quantity]").prop("checked",!0).parent().parent().addClass("checked"),e.find("input[name=quantity]").val(a.msg.mixedBatch.quantity)):e.find("input[value=quantity]").prop("checked",!1).parent().parent().removeClass("checked"),a.msg.mixedBatch.moqType?e.find("input[value="+a.msg.mixedBatch.moqType+"]").prop("checked",!0).parent().addClass("checked").siblings().removeClass("checked").find("input").prop("checked",!1):e.find("input[name=moqType]").prop("checked",!1).parent().removeClass("checked"))},"json")}),$(".fixed_mixed_batch").on("click",".box_type_menu .item",function(){$(this).addClass("checked").find("input").prop("checked",!0).parent().siblings().removeClass("checked").find("input").prop("checked",!1)}),frame_obj.submit_form_init($("#edit_mixed_batch_wholesale"),"",function(){let e=$(".fixed_mixed_batch"),a=e.find("input[name=batchUsed]").is(":checked"),t=e.find("input[name=mixedType]:checked").val(),l=e.find('input[name="amount"]').val(),i=e.find('input[name="quantity"]').val();if(!0===a){if("amount"==t&&""==l)return global_obj.win_alert_auto_close(lang_obj.manage.shipping.tips.enter_value,"fail",1e3,"8%"),e.find('input[name="amount"]').focus(),!1;if("quantity"==t&&""==i)return global_obj.win_alert_auto_close(lang_obj.manage.shipping.tips.enter_value,"fail",1e3,"8%"),e.find('input[name="quantity"]').focus(),!1}},0,function(e){return global_obj.win_alert_auto_close(lang_obj.global.save_success,"",1e3,"8%"),setTimeout(function(){window.location.reload()},1e3),!1}),$("#wholesale .batch_edit").on("click",function(){let e=$(this),a=e.hasClass("btn_batch_open")?"1":"0",t=[];$('#wholesale input[name="select"]:checked').each(function(e,a){t[e]=$(a).val()});let l=t.join(",");$.post("/manage/plugins/wholesale/batch-set-status",{id:l,type:a},function(e){global_obj.win_alert_auto_close(e.msg,"success"),setTimeout(()=>{window.location.reload()},1e3)},"json")}),$(".box_my_app .item[data-type=wholesale]").click(function(){var e=parseInt($("#ProId").val()),a=$(".fixed_edit_wholesale"),t="",l="";a.length||(t+='<div class="global_container fixed_edit_wholesale" data-width="420"><button id="btn_wholesale_fixed_show"></button></div>',$("#fixed_right").append(t),frame_obj.fixed_right($("#btn_wholesale_fixed_show"),".fixed_edit_wholesale",function(e){$("#fixed_right").addClass("loading")})),$("#btn_wholesale_fixed_show").click(),$("#edit_form input[name=APP\\[wholesale\\]\\[productsSave\\]]").length&&(l=$("#edit_form input[name=APP\\[wholesale\\]\\[productsSave\\]]").val()),$.post("/manage/plugins/wholesale/fixed-right",{ProId:e,Data:l,editProducts:1},function(e){if(e){a.find(".top_title, #edit_wholesale_form").remove();let t=$(e).find("#wholesale_fixed_right").html();$("#fixed_right").removeClass("loading"),$(".fixed_edit_wholesale").append(t),wholesale_app_obj.wholesale_fixed()}})}),$(".box_my_app .item[data-type=wholesale]").length&&parseInt($("#ProId").val())>0){var a=parseInt($("#ProId").val()),t=$(".fixed_edit_wholesale"),l="",i="";t.length||(l+='<div class="global_container fixed_edit_wholesale" data-width="420"><button id="btn_wholesale_fixed_show"></button></div>',$("#fixed_right").append(l),frame_obj.fixed_right($("#btn_wholesale_fixed_show"),".fixed_edit_wholesale",function(e){$("#fixed_right").addClass("loading")})),$("#edit_form input[name=APP\\[wholesale\\]\\[productsSave\\]]").length&&(i=$("#edit_form input[name=APP\\[wholesale\\]\\[productsSave\\]]").val()),$.post("/manage/plugins/wholesale/fixed-right",{ProId:a,Data:i},function(e){if(e){let l=$(e).find("#wholesale_fixed_right").html();$(".fixed_edit_wholesale").append(l),wholesale_app_obj.wholesale_fixed();var a=$(".fixed_edit_wholesale input[name='MOQ']").val(),t=$(".fixed_edit_wholesale input[name='MaxOQ']").val();if(""!=a&&""!=t&&(a=parseInt(a),t=parseInt(t),a>t))return!1;$.post("/manage/plugins/wholesale/update",$("#edit_wholesale_form").serialize(),function(e){if(1==e.ret){var a=$("#edit_form input[name=APP\\[wholesale\\]\\[productsSave\\]]");a.length||$("#edit_form").append('<input type="hidden" name="APP[wholesale][productsSave]" value="" />'),$("#edit_form input[name=APP\\[wholesale\\]\\[productsSave\\]]").val(e.msg)}},"json")}})}$(".plugins_app_box").on("click",".btn_edit_wholesale",function(){var e=$(this).data("id"),a=$(".fixed_edit_wholesale"),t="",l="";a.length||(t+='<div class="global_container fixed_edit_wholesale" data-width="420"><button id="btn_wholesale_fixed_show"></button></div>',$("#fixed_right").append(t),frame_obj.fixed_right($("#btn_wholesale_fixed_show"),".fixed_edit_wholesale",function(e){$("#fixed_right").addClass("loading")})),a.find(".top_title, #edit_wholesale_form").remove(),$("#btn_wholesale_fixed_show").click(),$("#edit_form input[name=APP\\[wholesale\\]\\[productsSave\\]]").length&&(l=$("#edit_form input[name=APP\\[wholesale\\]\\[productsSave\\]]").val()),$.post("/manage/plugins/wholesale/fixed-right",{ProId:e,Data:l},function(e){if(e){let a=$(e).find("#wholesale_fixed_right").html();$("#fixed_right").removeClass("loading"),$(".fixed_edit_wholesale").append(a),$(".fixed_edit_wholesale form").append('<input type="hidden" name="SaveNow" value="1" />'),wholesale_app_obj.wholesale_fixed()}})}),$("#main").on("change",'.fixed_edit_wholesale select[name="WholesaleType"]',function(){$(".wholesale_box label span").hide(),$('.wholesale_box label span[data-type="'+$(this).val()+'"]').show(),wholesale_app_obj.wholesale_fixed()})},wholesaler_list_init:function(){frame_obj.select_all($("input[name=select_all]"),$("input[name=select]")),frame_obj.del_bat($(".table_menu_button .del"),$("input[name=select]"),"/manage/plugins/wholesale/bat-del-wholesaler"),frame_obj.fixed_right_user_choice(),frame_obj.fixed_right_user_filter(),frame_obj.fixed_right($("#btn_add_wholesaler"),".fixed_right_user_choice",function(e){$("#fixed_right .search_form form input[name=FilterUserId]").length||$("#fixed_right .search_form form").append('<input type="hidden" name="FilterUserId" value="" />'),$("#fixed_right .search_form form input[name=FilterIsNewsletter]").length||$("#fixed_right .search_form form").append('<input type="hidden" name="FilterIsNewsletter" value="" />'),$("#fixed_right .search_form form input[name=FilterIsTaxExempt]").length||$("#fixed_right .search_form form").append('<input type="hidden" name="FilterIsTaxExempt" value="" />'),$("#fixed_right .search_form form input[name=FilterTagId]").length||$("#fixed_right .search_form form").append('<input type="hidden" name="FilterTagId" value="" />'),$("#fixed_right .search_form form input[name=FilterOrderNumber]").length||$("#fixed_right .search_form form").append('<input type="hidden" name="FilterOrderNumber" value="" />'),$("#fixed_right .search_form form input[name=FilterOrderPrice]").length||$("#fixed_right .search_form form").append('<input type="hidden" name="FilterOrderPrice" value="" />'),$("#fixed_right .search_form form input[name=FilterRegTime]").length||$("#fixed_right .search_form form").append('<input type="hidden" name="FilterRegTime" value="" />'),$proid=userList.join(","),$("#fixed_right .search_form form input[name=FilterUserId]").val($proid),$("#fixed_right .search_form form").submit()}),frame_obj.fixed_right($("#btn_set_wholesaler"),".fixed_wholesaler_set_box",function(e){}),frame_obj.submit_form_init($("#wholesaler_set_form"),"","","",function(e){1==e.ret?window.location.reload():global_obj.win_alert_auto_close(e.msg,"await",1e3,"8%")}),frame_obj.submit_form_init($("#fixed_right_user_choice_form"),"","","",function(e){1==e.ret?window.location.reload():global_obj.win_alert_auto_close(e.msg,"await",1e3,"8%")})},wholesale_fixed:function(){var e="",a=$('.fixed_edit_wholesale .price_type_box input[name="WholesaleType"]:checked').val();e+='<div class="wholesale_row">',e+='<div class="rows float clean">',e+='<div class="unit_input"><input type="text" rel="int" name="Qty[]" value="%Q%" class="box_input right_radius" size="6" maxlength="10" data-input="name" /><b class="wholesale_symbol last">%Symbol%</b></div>',e+="</div>",e+='<div class="rows float clean">',e+='<div class="input"><span data-type="reduce" class="unit_text">'+lang_obj.manage.sales.less+'</span><span class="unit_input"><b data-type="price">'+shop_config.currSymbol+'</b><input name="Discount[]" value="%D%" type="text" class="box_input" maxlength="10" size="5" placeholder="0.00" /><b class="last"  data-type="discount">% off</b></span><span class="wholesale_row_symbol">&nbsp;&nbsp; / '+$("input[name=WholesaleMethod][value=pieces]").data("symbol")+"</div>",e+="</div>",e+='<div class="float button">',e+='<a href="javascript:;" class="btn_option fl btn_option_add"><i></i></a><a href="javascript:;" class="btn_option fl btn_option_remove"><i></i></a>',e+="</div>",e+='<div class="clear"></div>';var t=$(".fixed_edit_wholesale .wholesale_box").data("wholesale"),l={add_option:function(e,t,l){a=$('.fixed_edit_wholesale .price_type_box input[name="WholesaleType"]:checked').val();let i=$("input[name=WholesaleMethod]:checked").data("symbol");i&&null!=i||(i=$("input[name=WholesaleMethod]").data("symbol")),e=t?e.replace("%Q%",t).replace("%D%",l):e.replace("%Q%","").replace("%D%",""),e=e.replace("%Symbol%",i),$(".fixed_edit_wholesale .wholesale_list").append(e),a&&($(".fixed_edit_wholesale .wholesale_list .unit_input b").hide(),$(".fixed_edit_wholesale .wholesale_list .unit_input b.wholesale_symbol").show(),$('.fixed_edit_wholesale .wholesale_list .unit_input b[data-type="'+("reduce"==a?"price":a)+'"]').show(),$(".fixed_edit_wholesale .wholesale_list .unit_text").hide(),$('.fixed_edit_wholesale .wholesale_list .unit_text[data-type="'+a+'"]').show()),"single_product"==$('input[name="ApplicationRange"]:checked').val()&&$('input[value="individual_shopping"]').prop("checked",!1).parent().removeClass("checked").hide().next().addClass("checked").find("input").prop("checked",!0),$(".wholesale_row").length>1?$(".wholesale_row:last").siblings().find(".button").addClass("hide_add").removeClass("hide_remove"):$(".wholesale_row .button").addClass("hide_remove"),$(".wholesale_row").length>4&&$(".wholesale_row:last .button").addClass("hide_add").removeClass("hide_remove"),frame_obj.check_amount($(".fixed_edit_wholesale .wholesale_list"))},remove_option:function(e){e.parents(".wholesale_row").fadeOut(function(){$(this).remove(),$(".wholesale_row:last .button").removeClass("hide_add"),1==$(".wholesale_row").length&&$(".wholesale_row .button").addClass("hide_remove")})}};$("#edit_wholesale_form , #wholesale").on("click",".sale_method_box .item",function(){let e=$(this),a=e.find("input").val();"batch"==a?e.siblings(".batch_box").css({display:"inline-block"}):e.siblings(".batch_box").hide(),$(".wholesale_symbol").html(e.find("input").data("symbol")),s()});var o=$("#edit_wholesale_form").find("input[name=IsWholesale]").prop("checked");if(o||$("#edit_wholesale_form").find(".wholesale_switch_box").hide(),$("#edit_wholesale_form").on("click",".switchery",function(){setTimeout(()=>{$("#edit_wholesale_form").find("input[name=IsWholesale]").prop("checked")?$("#edit_wholesale_form").find(".wholesale_switch_box").show():$("#edit_wholesale_form").find(".wholesale_switch_box").hide()},100)}),$(".box_type_menu").on("click",".item",function(){var e=$(this),t=e.find("input").val();if(e.addClass("checked").find("input").prop("checked",!0).parent().siblings().removeClass("checked").find("input").prop("checked",!1),e.parent().hasClass("price_type_box")){$(".wholesale_box label span").hide(),$('.wholesale_box label span[data-type="'+t+'"]').show(),a="reduce"==t?"price":t;var l="price"==t?"0.00":"0";$(".wholesale_row input[name=Discount\\[\\]]").attr("placeholder",l),$(".fixed_edit_wholesale .wholesale_row .unit_input>b").hide(),$(".fixed_edit_wholesale .wholesale_row .unit_text").hide(),$('.fixed_edit_wholesale .wholesale_row .unit_text[data-type="'+t+'"]').show(),$('.fixed_edit_wholesale .wholesale_row .unit_input>b[data-type="'+a+'"]').show(),$(".fixed_edit_wholesale .wholesale_list .unit_input b.wholesale_symbol").show()}e.parent().hasClass("application_range_box")&&("single_product"==t?$('input[value="individual_shopping"]').prop("checked",!1).parent().removeClass("checked").hide().next().addClass("checked").find("input").prop("checked",!0):$('input[value="individual_shopping"]').parent().show())}),$(".fixed_edit_wholesale .wholesale_list").html(""),t&&0!=t.length)for(k in t)l.add_option(e,k,t[k]);else l.add_option(e);$(".wholesale_box").off("click").delegate(".btn_option_add","click",function(){l.add_option(e,"")}),$(".wholesale_box").delegate(".btn_option_remove","click",function(){l.remove_option($(this))}),$(".wholesale_list").on("blur",'input[name^="Qty"]',function(){let e=$("input[name=WholesaleMethod]:checked").val(),a=1;"batch"==e&&(a=isNaN(parseInt($("input[name=WholesaleMethodPieces]").val()))?0:parseInt($("input[name=WholesaleMethodPieces]").val())),$(this).val()*a==1&&($(this).val("").focus(),global_obj.win_alert_auto_close(lang_obj.manage.products.wholesale_moq,"fail",1e3,"8%"))}),$("#edit_wholesale_form .btn_submit").off("click"),frame_obj.submit_form_init($("#edit_wholesale_form"),"",function(){var e=$(".fixed_edit_wholesale input[name='MOQ']").val(),a=$(".fixed_edit_wholesale input[name='MaxOQ']").val();return""!=e&&""!=a&&(e=parseInt(e),a=parseInt(a),e>a)?(global_obj.win_alert_auto_close(lang_obj.manage.products.tips.min_than_max,"fail",1e3,"8%"),$(".fixed_edit_wholesale input[name='MOQ']").focus(),!1):($status=1,$MaxOQ=parseInt($("#edit_wholesale_form").find("input[name=MaxOQ]").val()),$("#edit_wholesale_form").find(".wholesale_row").each(function(e,a){a=$(a),$qtyObj=a.find("input[name=Qty\\[\\]]"),parseInt($qtyObj.val())>$MaxOQ&&($qtyObj.css({"border-color":"red"}),a.find(".error_tips").length||$qtyObj.parents(".wholesale_row").append('<div class="error_tips">'+lang_obj.manage.app.wholesale.qty_than_max_tips.replace("%%num%%",$MaxOQ)+"</div>"),$status=0)}),!!$status&&void 0)},0,function(a){if(1==a.ret){var t=$("#edit_form input[name=APP\\[wholesale\\]\\[productsSave\\]]");t.length||$("#edit_form").append('<input type="hidden" name="APP[wholesale][productsSave]" value="" />'),$("#edit_form input[name=APP\\[wholesale\\]\\[productsSave\\]]").val(a.msg),global_obj.win_alert_auto_close(lang_obj.global.save_success,"",1e3,"8%")}else if(2==a.ret){let t=a.msg,l=a.param;for(k in l){let a=l[k].ProId,o=void 0===l[k].MOQ?"":""==l[k].MOQ?1:parseInt(l[k].MOQ),s=void 0===l[k].MaxOQ?"":""==l[k].MaxOQ||0==l[k].MaxOQ?lang_obj.manage.app.wholesale.unlimited:parseInt(l[k].MaxOQ),n=(void 0===l[k].WholesaleConditions||l[k].WholesaleConditions,void 0===l[k].ApplicationRange?"":l[k].ApplicationRange),d=void 0===l[k].AddPurchaseMethod?"":l[k].AddPurchaseMethod,_=(void 0===l[k].Wholesale||l[k].Wholesale,void 0===l[k].WholesaleSymbol?"":l[k].WholesaleSymbol);if(e="",$WholesaleData=t[a],""!=$WholesaleData)for(i in $WholesaleData)e+=i+"+&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;","price"==l[k].WholesaleType?e+=shop_config.currSymbol+parseFloat($WholesaleData[i]).toFixed(2)+"/"+_+"<br />":"discount"==l[k].WholesaleType?e+=parseInt($WholesaleData[i])+"% off<br />":"reduce"==l[k].WholesaleType&&(e+=lang_obj.manage.sales.less+" "+shop_config.currSymbol+parseFloat($WholesaleData[i]).toFixed(2)+"/"+_+"<br />");""!=n&&$(".plugins_app_box .r_con_table tr[data-id="+a+"] td:eq(3)").html(n),""!=d&&$(".plugins_app_box .r_con_table tr[data-id="+a+"] td:eq(4)").html(d),""!=o&&$(".plugins_app_box .r_con_table tr[data-id="+a+"] td:eq(5)").html(o+" "+_),""!=s&&$(".plugins_app_box .r_con_table tr[data-id="+a+"] td:eq(6)").html(s+" "+_),$(".plugins_app_box .r_con_table tr[data-id="+a+"] td:eq(7)").html(e)}global_obj.win_alert_auto_close(lang_obj.global.save_success,"",1e3,"8%")}return $("#fixed_right .btn_cancel").click(),!1}),$("#wholesale , #edit_wholesale_form").on("keyup","input[type=text]",function(){s()});var s=function(){$(".wholesale_symbol_tips").remove(),$.each($("#wholesale input[type=text] , #edit_wholesale_form input[type=text]"),function(){let e=$(this),a=parseInt(e.val()),t=$("input[name=WholesaleMethod]:checked").val(),l=isNaN(parseInt($("input[name=WholesaleMethodPieces]").val()))?0:parseInt($("input[name=WholesaleMethodPieces]").val()),i=isNaN(parseInt(e.val()))?0:parseInt(e.val());piecesNumber=l*i,piecesSymbol=$("input[name=WholesaleMethod][value=pieces]").data("symbol"),batchSymbol=$("input[name=WholesaleMethod][value=batch]").data("symbol"),Html="",isNaN(a)&&(a=0),"batch"==t&&e.next(".wholesale_symbol").length>0&&(Html+='<div class="wholesale_symbol_tips">',Html+=a+" "+batchSymbol+" = "+piecesNumber+" "+piecesSymbol,Html+="</div>",e.parent().after(Html))})};s()},wholesale_mixed_batch_init:function(e,a){let t=e.attr("data-id"),l=a?1:0;$.post("/manage/plugins/wholesale/set-mixed-batch-switch",{id:t,status:l},function(e){1==e.ret?global_obj.win_alert_auto_close(e.msg,"success",2e3,"20%"):global_obj.win_alert_auto_close(e.msg,"fail",2e3,"20%")},"json")},wholesale_add_init:function(){$("#wholesale").on("click",".add_pro_btn",function(){let e={iframeTitle:lang_obj.manage.view.select_products,type:"manual",isOrder:!1,value:{},valueOrder:[]},a={},t=[],l=[];$("#wholesale .wholesale_add_form tbody tr").each(function(){let e=$(this).find('input[name^="ProId"]').val(),i=$(this).find(".pic_box img").attr("src");a[e]={image:i},l.push(e),t.push(e)}),$(".filter_box .filter_item[data-type=ProId]").each(function(){let e=$(this).attr("data-value");t.push(e)}),e.value=a,e.valueOrder=l,e.excludeValue=t,frame_obj.products_choice_iframe_init_v2({params:e,onSubmit:function(e){if($.isEmptyObject(e.value))global_obj.win_alert_auto_close(lang_obj.manage.error.no_prod_data,"await",1e3,"8%");else{let a=[];for(i in e.value)a.push(e.value[i].proid);let t=$("#wholesale .add_products").find("tbody tr");t.length>0&&t.each(function(){let e=$(this).find('input[name="ProId[]"]').val();a.push(e)}),a.length&&$.post("/manage/plugins/wholesale/add-wholesale-products-list",{ProId:a},function(e){1==e.ret?$(".wholesale_add_form .pro_box").html(e.msg.html).show():global_obj.win_alert_auto_close(e.msg,"await",1e3,"8%")},"json"),global_obj.win_alert_auto_close(lang_obj.global.add_success,"",1e3,"8%")}}})}),$("#wholesale").on("click",".del",function(){var e={title:lang_obj.global.del_confirm,confirmBtn:lang_obj.global.del,confirmBtnClass:"btn_warn"},a=$(this);global_obj.win_alert(e,function(){a.parents("tr").remove(),$(".wholesale_add_form .pro_box").find("tbody").find("tr").length||$(".wholesale_add_form .pro_box").html("").hide()},"confirm")}),wholesale_app_obj.wholesale_fixed(),frame_obj.submit_form_init($("#edit_form"),"",function(){var e=$("#edit_form input[name='MOQ']").val(),a=$("#edit_form input[name='MaxOQ']").val();return""!=e&&""!=a&&(e=parseInt(e),a=parseInt(a),e>a)?(global_obj.win_alert_auto_close(lang_obj.manage.products.tips.min_than_max,"fail",1e3,"8%"),$("#edit_form input[name='MOQ']").focus(),!1):($status=1,$MaxOQ=parseInt($("#edit_form").find("input[name=MaxOQ]").val()),$WholesaleMethod=$("input[name=WholesaleMethod]:checked").val(),$WholesaleMethodPieces=1,"batch"==$WholesaleMethod&&($WholesaleMethodPieces=isNaN(parseInt($("input[name=WholesaleMethodPieces]").val()))?0:parseInt($("input[name=WholesaleMethodPieces]").val())),$("#edit_form").find(".wholesale_row").each(function(e,a){a=$(a),$qtyObj=a.find("input[name=Qty\\[\\]]"),parseInt($qtyObj.val())*$WholesaleMethodPieces>$MaxOQ*$WholesaleMethodPieces?($qtyObj.css({"border-color":"red"}),a.find(".error_tips").length||$qtyObj.parents(".wholesale_row").append('<div class="error_tips">'+lang_obj.manage.app.wholesale.qty_than_max_tips.replace("%%num%%",$MaxOQ*$WholesaleMethodPieces)+"</div>"),$status=0):a.find(".error_tips").length&&(a.find(".error_tips").remove(),$qtyObj.css({"border-color":"#ccdced"}))}),!!$status&&void 0)},"",function(e){1==e.ret?(global_obj.win_alert_auto_close(e.msg,"successs",500),setTimeout(()=>{window.location.href="/manage/plugins/wholesale/index"},600)):global_obj.win_alert_auto_close(e.msg,"fail")})}};$(function(){$(".box_my_app .item[data-type=wholesale]").length&&wholesale_app_obj.wholesale_init()});