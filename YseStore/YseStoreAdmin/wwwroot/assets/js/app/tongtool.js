

var tongtool_obj={  //同步通途
	tongtool_init: function(){
		frame_obj.del_init($('#sync_tongtool .account_list,#sync_tongtool .account_setting'));//删除授权
		frame_obj.fixed_right($('a.add_store,.bg_no_table_data .btn_add_item'), '.store_add');
		frame_obj.del_bat($('.table_menu_button .del'), $('input[name=select]'), '', function(id_list) {
			global_obj.win_alert(lang_obj.global.sold_in_confirm, function(){
				$.get('/manage/plugins/erp/tongtool-products-del', {id:id_list}, function(data) {
					if(data.ret==1){
						window.location.reload();
					}
				}, 'json');
			}, 'confirm');
			return false;
		}, lang_obj.global.dat_select);

		//添加授权提交
		$('#store_add').submit(function(){ return false; })
		$('#store_add').on('click','.btn_submit',function(){
			var $this=$('#store_add'),
				$Name=$this.find('input[name=Name]').val();
				$AppKey=$this.find('input[name=app_key]').val();
				$AppSecret=$this.find('input[name=app_secret]').val();
			if(global_obj.check_form($this.find('*[notnull]'), $this.find('*[format]'), 1)){return false;};

			$.post('/manage/plugins/erp/tongtool-authorization-add', {'Name':$Name, 'AppKey':$AppKey, 'AppSecret':$AppSecret, 'd':'tongtool'}, function(data){
				if(data.ret==1){
					window.location.href=window.location.href;
				}else{
					global_obj.win_alert(data.msg);
					global_obj.div_mask(1);
				}
			},'json');
			return false;
		});

		//修改账号
		frame_obj.fixed_right($('.account_setting .account_edit'), '.box_authorization_edit');
		var box_authorization_edit = $('.box_authorization_edit');
		$('.account_setting').on('click', '.account_edit', function (){//修改
			var AId=$(this).parents('tr').attr('aid'),
				Name=$(this).parent().siblings('td.account_name').text(),
				account=jQuery.parseJSON(global_obj.htmlspecialchars_decode($(this).parents('tr').attr('account')));
			$('input[type=submit]', box_authorization_edit).attr('disabled', false);
			$('input[name=Name]', box_authorization_edit).val(Name);
			$('input[name=app_key]', box_authorization_edit).val(account.app_key).addClass('bg_gray');
			$('input[name=app_secret]', box_authorization_edit).val(account.app_secret).addClass('bg_gray');
			$('input[name=AId]', box_authorization_edit).val(AId);
			$('input[name=d]', box_authorization_edit).val('tongtool');
			$('input[name=do_action]', box_authorization_edit).val('/manage/plugins/erp/tongtool-authorization-edit');
		});

		frame_obj.submit_form_init($('#authorization_mod'), '', '', '',function(data){
			if(data.ret==1){
				window.location.href=window.location.href;
			}else{
				global_obj.win_alert(data.msg);
				global_obj.div_mask(1);
			}
		});

		//切换账号
		$('#sync_tongtool .account_list .change_account').click(function(){
			if(!$(this).hasClass('cur')){
				global_obj.div_mask();
				global_obj.win_alert_auto_close(lang_obj.manage.products.sync_change_account, 'loading', -1);
				$.post('/manage/plugins/erp/change-tongtool-authorization-account', 'AccountId='+$(this).attr('data-id'), function(data){
					if(data.ret==1){
						window.top.location.reload();
					}else{
						data.msg && global_obj.win_alert(data.msg);
					}
					setTimeout(function(){global_obj.div_mask(1)},500);
					global_obj.win_alert_auto_close('', 'loading', 500, '', 0);
				},'json');
			}
		});
	},

	tongtool_products_init: function(){
		frame_obj.del_init($('#sync_tongtool .r_con_table'));//删除商品
		frame_obj.select_all($('input[name=select_all]'), $('input[name=select]'), $('.table_menu_button').find('.del, .copy'));

		//批量删除产品
		frame_obj.del_bat($('.list_menu_button .del'), $('input[name=select]'), '', function(id_list){
			global_obj.win_alert({'title':lang_obj.global.del_confirm,'confirmBtnClass':'btn_warn'}, function(){
				$.get('/manage/plugins/erp/tongtool-products-bat-del', {group_proid:id_list}, function(data){
					if(data.ret==1){window.location.reload();}
				}, 'json');
			}, 'confirm');
			return false;
		}, lang_obj.global.dat_select);

		//复制产品分类选择弹出框
		$('a.tongtool_product_list_post').on('click', function(){
			var $obj=$('.copy_products_box');
			$('.copy_products_box select[name=CateId]').find('option:selected').removeAttr('selected');
		});
		frame_obj.fixed_right($('a.tongtool_product_list_post'), '.copy_products_box');//多分类按钮

        //批量发布
		frame_obj.del_bat($('.copy_products_box input.btn_submit'), $('input[name=select]'), '', function(id_list){
			$('.copy_products_box input.btn_submit').attr('disabled', 'disabled');
			var $obj=$('.copy_products_box select[name=CateId]').find('option:selected');
			if($obj.val()!=''){
				var $CateId=parseInt($obj.val());
			}else{
				global_obj.win_alert(lang_obj.manage.products.category_tips);
				global_obj.div_mask(1);
				$('.copy_products_box input.btn_submit').removeAttr('disabled');
				return false;
			}
			$.post('/manage/plugins/erp/copy-tongtool-to-products', {group_id:id_list, CateId:$CateId}, function(data){
				$('.copy_products_box input.btn_submit').removeAttr('disabled');
				if(data.ret==1){
					$('#fixed_right .close').click();
					global_obj.win_alert(lang_obj.manage.products.copy_complete,function(){
						window.top.location.reload();
					});
				}else{
					global_obj.win_alert(data.msg);
					global_obj.div_mask(1);
				}
			}, 'json');
		}, lang_obj.global.dat_select);

		$('#sync_tongtool .push_pro_btn').on('click',function(){
			$ProId = $(this).data('proid');
			$.post('/manage/plugins/erp/copy-tongtool-to-products', {'group_id':$ProId}, function(data){
				if(data.ret==1){
					global_obj.win_alert(lang_obj.manage.products.copy_complete,function(){
						window.top.location.reload();
					});
				}else{
					global_obj.win_alert(data.msg);
					global_obj.div_mask(1);
				}
			},'json')
		})
		/***********************************开始同步产品(start)************************************/
		var tongtool_progress_obj = {
			progress: function(data){
				if(data){
					if(data.ret==3){ //进度完成
						$('#box_circle_container input[name=Start]').val(1);
						$('#box_circle_container .tips').hide();
						$('#btn_progress_cancel').val(lang_obj.global.confirm).show();
					}else if(data.ret==2){ //下一页
						$('#box_circle_container .status').text(lang_obj.manage.global.update_status[0]);
						$('#btn_progress_continue').click();
					}else if(data.ret==1){ //反复请求当前的进度状态
						if($('#box_circle_container input[name=Start]').val()>1){
							frame_obj.circle_progress_bar({
								percent: 90,
								processingText: '',
								completedText: ''
							});
						}
					}
				}
			},
			alert:function(){
				global_obj.win_alert({
					title: lang_obj.manage.app.tongtool.sync_process,
					subtitle: lang_obj.manage.app.tongtool.sync_no_effect,
					confirmBtn: lang_obj.global.ok2,
				}, function() {
					window.location.reload()
				})
			}
		}

		frame_obj.fixed_box_popup({
			"clickObj": $(".tongtool_product_list_sync"),
			"targetClass": "box_type_edit"
		});

		$('a.tongtool_product_sync_btn , a.tongtool_product_list_checked_sync').click(function(){
			let $Start = parseInt($('#box_circle_container input[name=Start]').val()),
				_typeData = $('.delivery_type_form').serialize(),
				checkNum = 0;
			$('.delivery_type_form .type_checkbox').each(function(){
				if($(this).hasClass('checked')) checkNum+=1;
			})
			if(checkNum==0){
				global_obj.win_alert(lang_obj.manage.app.tongtool.check_type);
			}
			$('.box_type_edit').find(".box_middle").animate({"top": -30}, 250, function() {
				$(this).css({"opacity": 0})
				$('.box_type_edit').hide();
			});

			$.post('/manage/plugins/erp/tongtool-products-sync', _typeData + '&Start='+$Start, function(data){
				if(data.ret==1 || data.ret == -3){
					if ($Start == 1) {
						global_obj.div_mask();
						tongtool_progress_obj.alert();
					}
					$('#box_circle_container input[name=TaskId]').val(data.msg.TaskId);
					$('#btn_progress_keep').click();
				} else if (data.ret == -1) {
					//弹出任务窗口
					$('#box_circle_container input[name=TaskId]').val(data.msg.TaskId);
					global_obj.div_mask();
					var $Data = new Object;
					$('#box_circle_container input[type=hidden]').each(function(){
						$Data[$(this).attr('name')]=$(this).val();
					});
					tongtool_progress_obj.alert();
				} else {
					global_obj.win_alert(data.msg);
				}
			},'json');
		});
		/***********************************开始同步产品(end)************************************/

		$('#know_it').on('click',function(){
			let _this = $(this),
				type = _this.attr('data-type');
			$.post('/manage/plugins/erp/request-type-understood',{'Type':type},function(result){
				if(result.ret == 1){
					$('.status_box').fadeOut(500);
				}
			},'json')	
		})

	},

	tongtool_orders_init: function(){

		var tongtool_progress_obj = {
			progress: function(data){
				if(data){
					if(data.ret==3){ //进度完成
						$('#box_circle_container input[name=Start]').val(1);
						$('#box_circle_container .tips').hide();
						$('#btn_progress_cancel').val(lang_obj.global.confirm).show();
					}else if(data.ret==2){ //下一页
						$('#box_circle_container .status').text(lang_obj.manage.global.update_status[0]);
						$('#btn_progress_continue').click();
					}else if(data.ret==1){ //反复请求当前的进度状态
						if($('#box_circle_container input[name=Start]').val()>1){
							frame_obj.circle_progress_bar({
								percent: 90,
								processingText: '',
								completedText: ''
							});
						}
					}
				}
			},
			alert:function(){
				global_obj.win_alert({
					title: lang_obj.manage.app.tongtool.sync_process,
					subtitle: lang_obj.manage.app.tongtool.sync_no_effect,
					confirmBtn: lang_obj.global.ok2,
				}, function() {
					window.location.reload()
				})
			}
		}
		
		frame_obj.fixed_box_popup({
			"clickObj": $(".tongtool_orders_sync"),
			"targetClass": "box_type_edit",
		});

		var check_checked_status = () => {
			setTimeout(()=>{
				let _item = $('#sync_tongtool').find('.orders_tips_form .input_checkbox_box.checked');
				let _syncBtn = $('#sync_tongtool').find('.tongtool_orders_sync_btn');
				if (_item.length == 0) {
					_syncBtn.attr('disabled', true).addClass('disabled');
				} else {
					_syncBtn.removeAttr('disabled').removeClass('disabled')
				}
			},100)
		}

		//检查勾选状态至少要勾选一个
		check_checked_status();
		let _checkedItem = $('#sync_tongtool').find('.orders_tips_form .input_checkbox_box')
		_checkedItem.on('click',function(){
			check_checked_status();
		})

		$('a.tongtool_orders_sync_btn , a.tongtool_orders_check_sync').click(function(){
			if ($(this).hasClass('disabled')) return false;
			let account = $('.tongtool_orders_sync').attr('data-account');
			if (!account) account = $('.tongtool_orders_check_sync').attr('data-account');
			
			if (!account) {
				global_obj.win_alert({
					"title": lang_obj.manage.app.tongtool.unable_sync,
					"subtitle":lang_obj.manage.app.tongtool.not_account,
					"confirmBtn":lang_obj.manage.global.got_it
				});
				return false;
			}

			$('.box_type_edit').find(".box_middle").animate({"top": -30}, 250, function() {
				$(this).css({"opacity": 0})
				$('.box_type_edit').hide();
			});
			
			_typeData = $('.orders_tips_form').serialize()
			$.post('/manage/plugins/erp/request-orders-sync', _typeData + '&account=' + account, function(data){
				if (data.ret == -1) {
					// 已有同步任务
					global_obj.win_alert(data.msg);
				} else if (data.ret == -2) {
					// 没有账号
					global_obj.win_alert({
						"title": lang_obj.manage.app.tongtool.unable_sync,
						"subtitle":lang_obj.manage.app.tongtool.not_account
					});
				} else if (data.ret == -3) {
					// 通途账号验证错误
					global_obj.win_alert({
						"title": lang_obj.manage.app.tongtool.unable_sync,
						"subtitle":lang_obj.manage.app.tongtool.account_error
					});
				} else if (data.ret == -4 || data.ret == 1) {
					// 同步中
					global_obj.div_mask();
					tongtool_progress_obj.alert();
				}
			},'json');
		});

		if ($('.tongtool .status_box').hasClass('pending')) {
			setInterval(function(){
				$.post('/manage/plugins/erp/tongtool-orders-sync-status', {}, function(data){
					if (data.ret == 1) window.location.reload();
				}, 'json');
			}, 10000)
		}
		
		$('#know_it').on('click',function(){
			let _this = $(this),
				type = _this.attr('data-type');
			$.post('/manage/plugins/erp/request-type-understood',{'Type':type},function(result){
				if(result.ret == 1){
					$('.status_box').fadeOut(500);
				}
			},'json')	
		})

		frame_obj.fixed_right($('.status_box .fail_btn'), '.box_fail_list');

		frame_obj.switchery_checkbox(function(obj){
			$('#sync_tongtool .next_sync_time').addClass('show');
			$.post('/manage/plugins/erp/tongtool-start-sync', {}, function(data){
				if(data.ret==1){
					global_obj.win_alert_auto_close(data.msg.alert_tips, '', 1000, '8%');
				}
			}, 'json');
		}, function(obj){
			$('#sync_tongtool .next_sync_time').removeClass('show');
			$.post('/manage/plugins/erp/tongtool-start-sync', {}, function(data){
				if(data.ret==1){
					global_obj.win_alert_auto_close(data.msg.alert_tips, '', 1000, '8%');
				}
			}, 'json');
		},'.sync_status_btn');
	},

	tongtool_orders_view: function(){
		//调整“订单详情”和“右侧”的高度对齐
		if($('.orders_info').length){
			var obj=$('.orders_info'),
				leftH=obj.parent().outerHeight(),
				rightH=obj.parent().next().outerHeight(),
				maxH=0;
			maxH=Math.max(leftH, rightH);
			obj.css('height', maxH-42); //30:内间距 10:右侧的间隔 2:边框
		}
	}
}