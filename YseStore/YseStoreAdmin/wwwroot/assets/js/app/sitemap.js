

var sitemap_obj = {
	sitemap_init: function() {
		frame_obj.switchery_checkbox(function(obj) {
			obj.find("input:checkbox").prop("checked", true);
		}, function(obj) {
			obj.find("input:checkbox").prop("checked", false);
		});

		frame_obj.fixed_right($("#sitemap .inside_table .icon_set"), ".box_domain_edit", function(_this) {
			let $trObj = $(_this).parents("tr"),
				$domain = $trObj.attr("data-domain"),
				$prefix = $trObj.attr("data-prefix");
			if ($prefix == 1) {
				// 勾选
				$(".box_domain_edit .box_prefix .switchery").addClass("checked").find("input").prop("checked", true);
			} else {
				// 未选
				$(".box_domain_edit .box_prefix .switchery").removeClass("checked").find("input").prop("checked", false);
			}
			$(".box_domain_edit input[name=domain]").val($domain);
		});

		// 提交
		frame_obj.submit_object_init($("#domain_edit"), "", "", "", function(result) {
			if (result.ret == 1) {
				let domain = result.msg.domain,
					$trObj = $("#sitemap .inside_table tr[data-domain=\"" + domain + "\"]"),
					url = "";
				if (result.msg.isPrefix == 1) {
					$trObj.attr("data-prefix", 1);
					url += domain.replace("http://", "http://www.").replace("https://", "https://www.");
				} else {
					$trObj.attr("data-prefix", 0);
					url += domain.replace("www.", "");
				}
				$trObj.find("td:eq(0)").text(url);
				$trObj.find(".icon_update").attr("data-domain", url);
				global_obj.win_alert_auto_close(lang_obj.global.save_success, '', 500, '8%');
				$(".box_domain_edit .btn_cancel").click();
			} else {
				$("#domain_edit input:submit").removeAttr("disabled");
				global_obj.win_alert_auto_close(result.msg, "fail", 1000, "8%");
			}
		});

		// 更新
		$(".icon_update").click(function() {
			let domain = $(this).attr("data-domain");
			if (!$('.win_alert').length) {
				global_obj.win_loading();
				frame_obj.circle_progress_bar({
					percent: 0,
					processingText: lang_obj.manage.global.update_loading
				});
				let html = '';
				html += '<input type="button" class="btn_global" id="btn_progress_keep" />';
				html += '<input type="hidden" name="Start" value="0" />';
				html += '<input type="hidden" name="WebSiteNum" value="0" />';
				html += '<input type="hidden" name="Domain" value="' + domain + '" />';
				html += '<input type="hidden" name="do_action" value="/manage/plugins/sitemap/progress" />';
				$("#box_circle_container").append(html);
 
				frame_obj.box_progress(function(data) {
					if (data) {
						if (data.ret == 3) {
							// 进度完成
						} else if (data.ret == 2) {
							// 下一页
							$("#box_circle_container input[name=Start]").val(parseInt(data.msg.Start));
							$("#box_circle_container input[name=WebSiteNum]").val(parseInt(data.msg.WebSiteNum));
							setTimeout(function() {
								$("#btn_progress_keep").click();
							}, 300);
						}
					}
				});
				$("#btn_progress_keep").click();
			}
		});
	},
}