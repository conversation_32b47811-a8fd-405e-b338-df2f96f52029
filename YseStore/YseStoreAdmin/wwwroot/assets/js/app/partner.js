/**************************** 友情链接 start ****************************/
var partner_obj={
	partner_init:function(){
		frame_obj.del_init($('#partner .r_con_table'));
		frame_obj.select_all($('input[name=select_all]'), $('input[name=select]'), $('.table_menu_button .del')); //批量操作
		frame_obj.del_bat($('.list_menu .del'), $('#partner .r_con_table input[name=select]'), '/manage/plugins/partner/partner-del-bat'); //批量删除
		
		$('.r_con_table').on('click', '.used_checkbox .switchery', function(){//启用或关闭合作伙伴
			var $this=$(this),
				$tr=$this.parents('tr');
			if(!$this.hasClass('checked')){
				var IsUsed=1;
				$this.addClass('checked');
			}else{
				var IsUsed=0;
				$this.removeClass('checked');
			}
			$.post('/manage/plugins/partner/partner-used', 'PId='+$tr.attr('pid')+'&IsUsed='+IsUsed, function(data){
				if(data.msg){
					global_obj.win_alert_auto_close(data.msg, '', 1000, '8%');
				}
			}, 'json');
		});
	},
    partner_edit_init:function(){
		/* 友情连接图片上传 */
		frame_obj.mouse_click($('#PicDetail .upload_btn, #PicDetail .pic_btn .edit'), 'img', function($this){
			frame_obj.photo_choice_init('PicDetail', '', 1);
		});
		frame_obj.switchery_checkbox();
		frame_obj.submit_form_init($('#partners_edit_form'), '/manage/plugins/partner/');
	},
}
/**************************** 友情链接 end ****************************/