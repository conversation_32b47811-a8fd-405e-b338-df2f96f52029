var shopline_obj={global_init:function(){frame_obj.del_init($("#products_sync .r_con_table")),frame_obj.select_all($("input[name=select_all]"),$("input[name=select]"))},progress:function(t){t&&(3==t.ret?($("#box_circle_container input[name=Start]").val(1),$("#box_circle_container .tips").hide(),$("#btn_progress_cancel").val(lang_obj.global.confirm).show()):2==t.ret?($("#box_circle_container .status").text(lang_obj.manage.global.update_status[0]),$("#btn_progress_continue").click()):1==t.ret&&$("#box_circle_container input[name=Start]").val()>1&&frame_obj.circle_progress_bar({percent:90,processingText:"",completedText:""}))},csv_init:function(){shopline_obj.global_init(),frame_obj.del_bat($(".table_menu_button .del"),$("input[name=select]"),"/manage/plugins/shopline/delete-batch-products"),frame_obj.fixed_right($("a.shoplinecsvupload_product_list_sync"),".csvupload_products_box",function(){$("#upload_edit_form .btn_submit").attr("disabled","disabled")});var t=$("#upload_edit_form");t.fileupload({url:"/manage/action/file-upload-plugin?size=file",acceptFileTypes:/^.*.(csv|vnd\.ms-excel)$/i,callback:function(t,a){$("#excel_path").val(t),t&&$("#upload_edit_form .btn_submit").removeAttr("disabled")}}),t.fileupload("option","redirect",window.location.href.replace(/\/[^\/]*$/,"/cors/result.html?%s")),frame_obj.submit_form_init(t,"",function(){return t.find(".form_container").hide(),t.find(".progress_container_right").show(),!0},"",function(a){if(2==a.ret){var e="";if(t.find(".progress_container_right tbody tr").length&&parseInt(t.find(".progress_container_right tbody tr:eq(0) td:eq(0)").text()),a.msg.Data){for(k in a.msg.Data)e='<tr class="'+(0==a.msg.Data[k].Status?"error":"success")+'" data-id="'+a.msg.Data[k].ProId+'" data-type="'+a.msg.Data[k].Type+'" data-pic-status="0">\t\t\t\t\t\t\t\t\t<td nowrap="nowrap"><i class="icon"></i></td>\t\t\t\t\t\t\t\t\t<td nowrap="nowrap"><div class="img"></div><div class="name" title="'+a.msg.Data[k].Name+'">'+a.msg.Data[k].Name+"</div></td>\t\t\t\t\t\t\t\t</tr>",t.find(".progress_container_right tbody").prepend(e);t.find("input[name=Number]").val(a.msg.Num),t.find("input[name=Current]").val(a.msg.Cur),t.find("input[name=TaskId]").val(a.msg.TaskId),t.find(".btn_submit").removeAttr("disabled").click()}}else 1==a.ret?t.find(".progress_container_right tbody").find("tr").length>0?$(".btn_picture").click():(t.find(".global_tips").removeClass("load").addClass("success").find("span").text(lang_obj.manage.products.sync.sync_success),$("#fixed_right_div_mask, .csvupload_products_box .close").click(function(){return window.location.reload(),!1})):(global_obj.win_alert_auto_close(a.msg,"fail",2e3,"8%"),t.find(".form_container").show(),t.find(".progress_container_right").hide())}),$(".btn_picture").on("click",function(){var a=t.find(".progress_container_right tbody"),e=5e3,s="",i=new Array;a.find("tr.success[data-id!=0][data-pic-status=0]").each(function(){if(i.length>100)return!1;var t=parseInt($(this).attr("data-id"));t&&i.push(t)}),i.length&&$.post("/manage/plugins/shopline/upload-picture",{ProIdAry:i},function(i){if(clearTimeout(s),1==i.ret){var n=i.msg.CompleteProId,o=i.msg.CompletePicPath,r=i.msg.CompleteStatus;for(var c in n){let e=a.find("tr[data-id="+n[c]+"]");if(-1==r[c])e.attr("data-pic-status",-1).find("td>div.img").html('<img src="'+o[c]+'" />');else{e.attr("data-pic-status",1).find("td>i.icon").addClass("on"),e.attr("data-pic-status",1).find("td>div.img").html('<img src="'+o[c]+'" />');let a=e.clone();e.remove(),t.find(".progress_container_right tbody").prepend(a)}}}if(0==a.find("tr.success[data-id!=0][data-pic-status=0]").length){if(a.find("tr.success[data-id!=0][data-pic-status=-1]").length>0){let e=a.find("tr.success[data-id!=0][data-pic-status=1]").length,s=a.find("tr.success[data-id!=0][data-pic-status=-1]").length;t.find(".global_tips").removeClass("load").find("span").text(lang_obj.manage.products.sync.sync_part_tips.replace("{{Number1}}",e).replace("{{Number2}}",s))}else t.find(".global_tips").removeClass("load").addClass("success").find("span").text(lang_obj.manage.products.sync.sync_success);$("#fixed_right_div_mask, .csvupload_products_box .close").click(function(){return window.location.reload(),!1})}s=setTimeout(function(){a.find("tr.success[data-id!=0][data-pic-status=0]").length>0&&$(".btn_picture").click()},e)},"json")}),$(".btn_publish").click(function(){let t=$(this),a=t.parents("tr"),e=a.data("id");$.post("/manage/plugins/shopline/copy-shopline-to-products",{group_id:e,Type:"Publish"},()=>{t.remove(),a.find("td.status").html(`<span class="status ing">${lang_obj.manage.products.sync.publish_used}</span>`),global_obj.win_alert_auto_close(lang_obj.manage.products.sync.publish_once,"",2e3,"8%")})})}};