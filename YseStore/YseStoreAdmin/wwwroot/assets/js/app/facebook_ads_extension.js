

var facebook_ads_extension_obj = {
	//Facebook官方商务插件
	facebook_ads_extension_init: function() {
		var $InChina = parseInt($('#facebook_ads_extension').attr('data-in-china'));
		$.post('?', {'operationName':'getAdsAccountInfo'}, function(data) {
			var $IsSetting = (data.msg.FbData && data.msg.FbData.setting_id) ? 1 : 0,
				$Html = '';
			if($InChina==1){
				$Html += '<div class="global_tips no_icon rows"><strong>'+lang_obj.manage.app.facebook_ads_extension.tips+'</strong></div>';
			}
			$Html += '<div class="store_id rows'+($IsSetting?'':' hide')+'">'+lang_obj.manage.app.facebook_ads_extension.store_id+': <span id="facebook_store_id" class="fs12 color_888">'+($IsSetting?data.msg.FbData.page_id:'')+'</span></div>';
			$Html += '<div class="button">';
			$Html += '	<iframe id="facebook_login_box" src="https://sync.ly200.com/plugin/facebook/login.php?domain=' + data.msg['domain'] + '&newVersion=1" style="width:240px; height:40px;"></iframe>';
			$Html += '	<button id="button_settings" style="display:none;" class="btn_global btn_submit">'+($IsSetting?lang_obj.manage.app.facebook_ads_extension.manage_settings:lang_obj.manage.app.facebook_ads_extension.get_started)+'</button>';
			$Html += '</div>';
			$('#facebook_flow').append($Html);
			//插件数据
			window.facebookAdsToolboxConfig=data.msg.Config;
			window.facebookAdsConfig=new Object;
			window.facebookMessenger=new Object;
			$('#facebook_ads_extension').off().on('click', '#button_settings', function(){
				plugins_ads.launchDiaWizard();
			}).on('click', '#button_ad', function(){
				plugins_ads.launchAutomatedAds();
			});
			plugins_ads.getTokenInfo();
			$('#facebook_flow').removeClass('loading');
		}, 'json');
	},
}