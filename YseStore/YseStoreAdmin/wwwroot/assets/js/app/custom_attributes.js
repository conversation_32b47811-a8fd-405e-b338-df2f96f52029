/*

 * 批发 APP
 */

var custom_attributes_obj = {
	custom_attributes_init: function() {
		// 产品编辑
		const FUNC = {
			ListLoad : (callback) => {
				// 加载列表内容
				let $obj = $(".fixed_edit_custom_attributes"),
					data = $obj.find(".search_form form").serializeObject();
				data.page = parseInt($(".fixed_right_custom_list").attr("data-page")) + 1;
				data.id = parseInt($obj.find("input[name=ProId]").val());
				$obj.find(".fixed_right_custom_jsppane").addClass("loading");
				$.post("/manage/plugins/product/custom-fixed-right-list", data, function(result) {
					if (result.ret == 1) {
						$obj.find(".fixed_right_custom_jsppane").removeClass("loading");
						$obj.find(".fixed_right_custom_list").append(result.msg.html);
						$obj.find(".fixed_right_custom_list").attr("data-page", result.msg.page);
						$obj.find(".fixed_right_custom_list").attr("data-total", result.msg.total);
						$.isFunction(callback) && callback();
					} else {
						$obj.find(".search_menu, #edit_custom_attributes_form").remove();
						$obj.append('<div class="no_data">' + lang_obj.manage.error.no_content + '</div><button class="btn_global btn_submit btn_setting">' + lang_obj.manage.view.set + '</button>');
					}
				}, "json");
			},
			ListInit : (callback) => {
				// 加载默认事件
				FUNC.ListLoad(callback);
				let $obj = $(".fixed_edit_custom_attributes");
				// 搜索
				$obj.find(".search_form form").submit(function() {
					$obj.find(".fixed_right_custom_list").html("");
					$obj.find(".fixed_right_custom_list").attr("data-page", 0);
					FUNC.ListLoad();
					return false;
				});
				$obj.off().on("click", ".fixed_right_custom_list_item:not(.disabled)", function() {
					// 点击产品
					$(this).toggleClass("current");
					$(this).find(".btn_checkbox").toggleClass("current");
					$(this).find(".btn_checkbox input").prop("checked") ? $(this).find(".btn_checkbox input").prop("checked", false) : $(this).find(".btn_checkbox input").prop("checked", true);
					if ($obj.find(".fixed_right_custom_list_item.current").length > 0) {
						//已有勾选产品
						$obj.find("input[name=submit_button]").prop("disabled", false);
					} else {
						//没有勾选产品
						$obj.find("input[name=submit_button]").prop("disabled", true);
					}
				}).on("click", ".fixed_right_custom_list_load_more", function() {
					// 加载更多
					$(this).remove();
					FUNC.ListLoad();
					return false;
				}).on("click", ".btn_set, .btn_setting", function() {
					// 设置按钮
					window.open("/manage/plugins/product/custom");
					return false;
				});
				// 提交
				frame_obj.submit_form_init($("#edit_custom_attributes_form"), "", "", 0, function(result) {
					if (result.ret == 1) {
						// 产品编辑页面
						var $Obj = $("#edit_form input[name=APP\\[custom_attributes\\]\\[productsSave\\]]");
						if (!$Obj.length) {
							$("#edit_form").append('<input type="hidden" name="APP[custom_attributes][productsSave]" value="" />');
						}
						$("#edit_form input[name=APP\\[custom_attributes\\]\\[productsSave\\]]").val(result.msg);
						global_obj.win_alert_auto_close(lang_obj.global.saved, '', 1000, '8%');
					}
					$("#fixed_right .btn_cancel").click();
					return false;
				});
			}
		}

		$(".box_my_app .item[data-type=custom_attributes]").click(function() {
			var $ProId = parseInt($("#ProId").val()),
				$Obj = $(".fixed_edit_custom_attributes"),
				$Html = "", $Data = "";
			if (!$Obj.length) {
				$Html += '<div class="global_container fixed_edit_custom_attributes" data-width="400"><button id="btn_custom_attributes_fixed_show"></button></div>';
				$("#fixed_right").append($Html);
				frame_obj.fixed_right($("#btn_custom_attributes_fixed_show"), ".fixed_edit_custom_attributes", function($this) {
					$("#fixed_right").addClass("loading");
				});
			} else {
				$('.fixed_edit_custom_attributes').html('<button id="btn_custom_attributes_fixed_show"></button>');
				frame_obj.fixed_right($("#btn_custom_attributes_fixed_show"), ".fixed_edit_custom_attributes", function($this) {
					$("#fixed_right").addClass("loading");
				});
			}
			$Obj.find(".top_title, .search_menu, #edit_custom_attributes_form").remove();
			$("#btn_custom_attributes_fixed_show").click();
			if ($("#edit_form input[name=APP\\[custom_attributes\\]\\[productsSave\\]]").length) {
				$Data = $("#edit_form input[name=APP\\[custom_attributes\\]\\[productsSave\\]]").val();
			}
			$.post("/manage/plugins/product/custom-fixed-right", {"ProId":$ProId, "Data":$Data}, function(result) {
				if (result) {
					let txt = $(result).find("#custom_attributes_fixed_right").html();
					$("#fixed_right").removeClass("loading");
					$(".fixed_edit_custom_attributes").append(txt);
					FUNC.ListInit();
				}
			});
		});

		// 进入产品编辑页面 默认执行.
		if($('.box_my_app .item[data-type=custom_attributes]').length && parseInt($('#ProId').val()) > 0) {
			var $ProId = parseInt($('#ProId').val()),
				$Obj = $('.fixed_edit_custom_attributes'),
				$Html = '', $Data = '';
			if (!$Obj.length) {
				$Html += '<div class="global_container fixed_edit_custom_attributes" data-width="420"><button id="btn_custom_attributes_fixed_show"></button></div>';
				$('#fixed_right').append($Html);
				frame_obj.fixed_right($('#btn_custom_attributes_fixed_show'), '.fixed_edit_custom_attributes', function($this) {
					$('#fixed_right').addClass('loading');
				});
			}
			if ($('#edit_form input[name=APP\\[custom_attributes\\]\\[productsSave\\]]').length) {
				$Data = $('#edit_form input[name=APP\\[custom_attributes\\]\\[productsSave\\]]').val();
			}
			$.post('/manage/plugins/product/custom-fixed-right', {'ProId':$ProId, 'Data':$Data}, function(result) {
				if (result) {
					let txt = $(result).find("#custom_attributes_fixed_right").html();
					$('.fixed_edit_custom_attributes').append(txt);

					let returnJson = function () {
						$.post('/manage/plugins/product/return-json', $('#edit_custom_attributes_form').serialize() , function(data) {
							if(data.ret == 1) {
								var $inputObj = $('#edit_form input[name=APP\\[custom_attributes\\]\\[productsSave\\]]');
								if (!$inputObj.length) {
									$('#edit_form').append('<input type="hidden" name="APP[custom_attributes][productsSave]" value="" />');
								}
								$('#edit_form input[name=APP\\[custom_attributes\\]\\[productsSave\\]]').val(data.msg);
							}
						}, 'json')
					}

					FUNC.ListInit(returnJson);
				}
			});
		}
	}
}

$(function() {
	if ($("#products_inside").length) {
		custom_attributes_obj.custom_attributes_init();
	}
});
