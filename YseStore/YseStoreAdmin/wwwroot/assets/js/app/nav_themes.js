

var nav_themes_obj={
	nav_themes_init: function(){
		
		$_MenuHtml = '';
		$_MenuHtml += '<div class="option_row">';
		$_MenuHtml +=     '<div class="rows float clean">';
		$_MenuHtml +=         '<label>' + lang_obj.global.picture + '</label>';
		$_MenuHtml +=         '<div class="input">';
		$_MenuHtml += 			'<div class="multi_img upload_file_multi pro_multi_img" id="PicDetail_%OptionIndex%">';
		$_MenuHtml += 				frame_obj.multi_img_item("PicPath[]", '%OptionIndex%', 0);
		$_MenuHtml += 			'</div>';
		$_MenuHtml +=         '</div>';
		$_MenuHtml +=     '</div>';
		$_MenuHtml +=     '<div class="rows float clean">';
		$_MenuHtml +=         '<label>' + lang_obj.manage.app.nav_themes.link + '</label>';
		$_MenuHtml +=         '<div class="input"><input type="text" name="Url[]" value="" class="box_input" size="70" maxlength="225" data-input="name" /></div>';
		$_MenuHtml +=     '</div>';
		$_MenuHtml +=     '<div class="float button fr">';
		$_MenuHtml +=         '<a href="javascript:;" class="btn_option fl btn_option_remove"><i></i></a>';
		$_MenuHtml +=    '</div>';
		$_MenuHtml +=     '<div class="clear"></div>';
		$_MenuHtml += '</div>';

		let nav_obj = {
			'themes_obj' : $('#nav_themes'),
			'menu_form' : $('#nav_themes_edit_form'),
			'add_menu_option' : function($_MenuHtml,OptionIndex){
				let data_box = nav_obj.menu_form.find('.data_list');
				$_MenuHtml = $_MenuHtml.replaceAll('%OptionIndex%', OptionIndex);
				data_box.append($_MenuHtml);
			},
			'del_option' : function(obj){
				//移除选项
				obj.parents('.option_row').fadeOut(function() {
					$(this).remove();
				});
				
			},
			'side_picture_upload': function() {
				$(".navpic_box  .data_list").off().on("click", ".upload_btn", function() {
					let $id = $(this).parents(".multi_img").attr("id"),
					$num = $(this).parents('.img').attr('num');
					frame_obj.photo_choice_init($id + " .img[num;" + $num + "]", "", 1, "", 1, "");
				});
				$('body').on("click", ".navpic_box  .data_list .del", function(e) {
					let $this = $(this);
					let $obj = $this.parents('.img');
					$obj.removeClass('isfile').removeClass('show_btn').parent().append($obj);
					$obj.find('.pic_btn .zoom').attr('href','javascript:;');
					$obj.find('.preview_pic .upload_btn').show();
					$obj.find('.preview_pic a').remove();
					$obj.find('.preview_pic input:hidden').val('').attr('save', 0).trigger('change');
				});
			},
		}
		nav_obj.side_picture_upload();

		//选中类型效果
		nav_obj.themes_obj.on('click','.type_item',function(){
			let _this = $(this),
				$Obj = _this.parent(),
				type = _this.data('type'),
				picShow = 0;
			_this.addClass('cur').siblings().removeClass('cur');
			$Obj.find('input[name=Type]').val(type);
			if(type!=1) picShow = 1;
			if(picShow){
				nav_obj.themes_obj.find('.navpic_box').show()
			}else{
				nav_obj.themes_obj.find('.navpic_box').hide()
			}
		})

		nav_obj.menu_form.delegate('.btn_option_remove','click',function(){
			nav_obj.del_option($(this))
		}).delegate('.add_option_btn','click',function(){
			let OptionIndex = parseInt($('.data_list').find('.option_row').length)
			nav_obj.add_menu_option($_MenuHtml, OptionIndex + 1)
		})

		frame_obj.submit_form_init(nav_obj.menu_form, '', function () {
			
		}, '', function(data) {
			if(data.ret==1){
				global_obj.win_alert_auto_close(lang_obj.global.saved, '', 1000, '8%');
				window.location.reload();
			}
		});

	}
}