var supplier_obj={products_init:()=>{var e=new ClipboardJS("#supplier .btn_copy");e.on("success",function(e){alert(lang_obj.global.copy_complete)}),frame_obj.select_all($("input[name=select_all]"),$("input[name=select]"),$(".table_menu_button").find(".del")),frame_obj.fixed_right($(".edit_button,.batch_edit,.box_my_app .item[data-type=supplier]"),".fixed_supplier_edit"),$(".edit_button,.batch_edit,.box_my_app .item[data-type=supplier]").on("click",function(){var e=0;if($(this).hasClass("batch_btn")&&(id_list="",$("input[name=select]").each(function(e,t){id_list+=$(t).get(0).checked?$(t).val()+"-":""}),id_list=id_list.substring(0,id_list.length-1),$(this).attr("data-url","/manage/plugins/supplier/edit?id="+id_list)),"supplier"==$(this).attr("data-type")){let t=$("#ProId").val();e=1,$(this).attr("data-url","/manage/plugins/supplier/edit?id="+t)}supplier_obj.load_edit_form(".fixed_supplier_edit",$(this).attr("data-url"),"get","",function(t){$(".box_drop_double").each(function(){frame_obj.box_drop_double_default($(this))}),e||$("#supplier_edit_form").append('<input type="hidden" name="SaveNow" value="1" />'),frame_obj.submit_form_init($("#supplier_edit_form"),"",function(){let e=$("#supplier_edit_form"),t=e.find("textarea[name=PurchaseLinks]").val();if(""!=t){var i=/http(s)?:\/\/([\w-]+\.)+[\w-]+(\/[\w- .\/?%&=]*)?/,n=new RegExp(i);if(!n.test(t))return e.find(".erro_tips").show(),e.find("textarea[name=PurchaseLinks]").css("border","1px solid red"),!1;e.find(".erro_tips").hide(),e.find("textarea[name=PurchaseLinks]").removeAttr("style")}let a=$(".box_drop_double").find("input[name=Unit]").val(),l=$(".box_drop_double").find("input[name=UnitType]").val();if(""==a&&(""==l||"add"==l))return e.find("input[name=UnitValue]").css("border","1px solid red"),!1;e.find("input[name=UnitValue]").removeAttr("style")},0,function(e){if(1==e.ret)window.location.reload();else if(2==e.ret){var t=$("#edit_form input[name=APP\\[supplier\\]\\[productsSupplierSave\\]]");t.length||$("#edit_form").append('<input type="hidden" name="APP[supplier][productsSupplierSave]" value="" />'),$("#edit_form input[name=APP\\[supplier\\]\\[productsSupplierSave\\]]").val(e.msg),global_obj.win_alert_auto_close(lang_obj.global.save_success,"",1e3,"8%")}return $("#fixed_right .btn_cancel").click(),!1}),$('.box_drop_double input[name^="UnitValue"]').off("change").change(function(){var e=$(this).val();$(this).parent().find('[name="Unit"]').val(e),$(this).parent().find('[name="UnitType"]').val("add")})})})},supplier_init:()=>{const e=$("#name_edit_form");frame_obj.select_all($("input[name=select_all]"),$("input[name=select]"),$(".table_menu_button").find(".del")),frame_obj.del_bat($(".table_menu_button .del"),$("input[name=select]"),"/manage/plugins/supplier/del"),$(".products_number").on("click",function(e){let t=$(this),i=t.data("id");if(!t.find(".products_txt i").length)return!1;$.post("/manage/plugins/supplier/get-supplier-products-info",{SId:i},function(i){if(1==i.ret){let n="",a=0;for(let e in i.msg){let t=i.msg[e];n+='<div class="products_list">',n+='<div class="list_item">',n+='<div class="item_img fl">',n+='<a href="'+t.Url+'" target="_blank"><img src="'+t.PicPath+'" /></a>',n+="</div>",n+='<div class="item_info fr">',n+='<div class="info_name">'+t.Name+"</div>",n+="</div>",n+='<div class="clear"></div>',n+="</div>",n+="</div>",a++}t.find(".products_box").html(n);let l=t.find(".products_container").height(),o=e.currentTarget.offsetTop,d=$(".box_table").height();o+l>d&&t.find(".products_container").css({top:"100%",bottom:"auto"});parseInt($(".inside_table").css("padding-top")),$(".inside_table .list_menu").height(),parseInt($(".box_table table").css("margin-top"));t.addClass("current").find(".products_container").fadeIn()}},"json")}),$(".products_number").on("mouseleave",function(){$(this).removeClass("current").find(".products_container").fadeOut()}),frame_obj.fixed_right($(".edit_button"),".fixed_name_edit",function(t){let i=t.attr("id");e.find("input[name=SId]").val(i),$.post("/manage/plugins/supplier/get-supplier-info",{SId:i},function(t){if(1==t.ret){let i=t.msg;e.find("input[name=Name]").val(i)}},"json")},function(e){let t=e.attr("id");0==t?$(".fixed_name_edit").find(".top_title strong").html(lang_obj.global.add):$(".fixed_name_edit").find(".top_title strong").html(lang_obj.manage.global.edit)}),frame_obj.submit_form_init(e,"","",0,function(e){1==e.ret?(global_obj.win_alert_auto_close(lang_obj.global.save_success,"",1e3,"8%"),window.location.reload()):global_obj.win_alert_auto_close(e.msg,"fail",1e3,"8%")})},load_edit_form:function(e,t,i,n,a,l){$.ajax({type:i,url:t+n,success:function(t){"append"==l?$(e).append($(t).find(e).html()):$(e).html($(t).find(e).html()),a&&a(t)}})}};$(function(){$("#products_inside").length&&supplier_obj.products_init()});