var custom_attributes_obj={custom_attributes_init:function(){const t={ListLoad:t=>{let i=$(".fixed_edit_custom_attributes"),e=i.find(".search_form form").serializeObject();e.page=parseInt($(".fixed_right_custom_list").attr("data-page"))+1,e.id=parseInt(i.find("input[name=ProId]").val()),i.find(".fixed_right_custom_jsppane").addClass("loading"),$.post("/manage/plugins/product/custom-fixed-right-list",e,function(e){1==e.ret?(i.find(".fixed_right_custom_jsppane").removeClass("loading"),i.find(".fixed_right_custom_list").append(e.msg.html),i.find(".fixed_right_custom_list").attr("data-page",e.msg.page),i.find(".fixed_right_custom_list").attr("data-total",e.msg.total),$.isFunction(t)&&t()):(i.find(".search_menu, #edit_custom_attributes_form").remove(),i.append('<div class="no_data">'+lang_obj.manage.error.no_content+'</div><button class="btn_global btn_submit btn_setting">'+lang_obj.manage.view.set+"</button>"))},"json")},ListInit:i=>{t.ListLoad(i);let e=$(".fixed_edit_custom_attributes");e.find(".search_form form").submit(function(){return e.find(".fixed_right_custom_list").html(""),e.find(".fixed_right_custom_list").attr("data-page",0),t.ListLoad(),!1}),e.off().on("click",".fixed_right_custom_list_item:not(.disabled)",function(){$(this).toggleClass("current"),$(this).find(".btn_checkbox").toggleClass("current"),$(this).find(".btn_checkbox input").prop("checked")?$(this).find(".btn_checkbox input").prop("checked",!1):$(this).find(".btn_checkbox input").prop("checked",!0),e.find(".fixed_right_custom_list_item.current").length>0?e.find("input[name=submit_button]").prop("disabled",!1):e.find("input[name=submit_button]").prop("disabled",!0)}).on("click",".fixed_right_custom_list_load_more",function(){return $(this).remove(),t.ListLoad(),!1}).on("click",".btn_set, .btn_setting",function(){return window.open("/manage/plugins/product/custom"),!1}),frame_obj.submit_form_init($("#edit_custom_attributes_form"),"","",0,function(t){if(1==t.ret){var i=$("#edit_form input[name=APP\\[custom_attributes\\]\\[productsSave\\]]");i.length||$("#edit_form").append('<input type="hidden" name="APP[custom_attributes][productsSave]" value="" />'),$("#edit_form input[name=APP\\[custom_attributes\\]\\[productsSave\\]]").val(t.msg),global_obj.win_alert_auto_close(lang_obj.global.saved,"",1e3,"8%")}return $("#fixed_right .btn_cancel").click(),!1})}};if($(".box_my_app .item[data-type=custom_attributes]").click(function(){var i=parseInt($("#ProId").val()),e=$(".fixed_edit_custom_attributes"),s="",o="";e.length?($(".fixed_edit_custom_attributes").html('<button id="btn_custom_attributes_fixed_show"></button>'),frame_obj.fixed_right($("#btn_custom_attributes_fixed_show"),".fixed_edit_custom_attributes",function(t){$("#fixed_right").addClass("loading")})):(s+='<div class="global_container fixed_edit_custom_attributes" data-width="400"><button id="btn_custom_attributes_fixed_show"></button></div>',$("#fixed_right").append(s),frame_obj.fixed_right($("#btn_custom_attributes_fixed_show"),".fixed_edit_custom_attributes",function(t){$("#fixed_right").addClass("loading")})),e.find(".top_title, .search_menu, #edit_custom_attributes_form").remove(),$("#btn_custom_attributes_fixed_show").click(),$("#edit_form input[name=APP\\[custom_attributes\\]\\[productsSave\\]]").length&&(o=$("#edit_form input[name=APP\\[custom_attributes\\]\\[productsSave\\]]").val()),$.post("/manage/plugins/product/custom-fixed-right",{ProId:i,Data:o},function(i){if(i){let e=$(i).find("#custom_attributes_fixed_right").html();$("#fixed_right").removeClass("loading"),$(".fixed_edit_custom_attributes").append(e),t.ListInit()}})}),$(".box_my_app .item[data-type=custom_attributes]").length&&parseInt($("#ProId").val())>0){var i=parseInt($("#ProId").val()),e=$(".fixed_edit_custom_attributes"),s="",o="";e.length||(s+='<div class="global_container fixed_edit_custom_attributes" data-width="420"><button id="btn_custom_attributes_fixed_show"></button></div>',$("#fixed_right").append(s),frame_obj.fixed_right($("#btn_custom_attributes_fixed_show"),".fixed_edit_custom_attributes",function(t){$("#fixed_right").addClass("loading")})),$("#edit_form input[name=APP\\[custom_attributes\\]\\[productsSave\\]]").length&&(o=$("#edit_form input[name=APP\\[custom_attributes\\]\\[productsSave\\]]").val()),$.post("/manage/plugins/product/custom-fixed-right",{ProId:i,Data:o},function(i){if(i){let e=$(i).find("#custom_attributes_fixed_right").html();$(".fixed_edit_custom_attributes").append(e);let s=function(){$.post("/manage/plugins/product/return-json",$("#edit_custom_attributes_form").serialize(),function(t){if(1==t.ret){var i=$("#edit_form input[name=APP\\[custom_attributes\\]\\[productsSave\\]]");i.length||$("#edit_form").append('<input type="hidden" name="APP[custom_attributes][productsSave]" value="" />'),$("#edit_form input[name=APP\\[custom_attributes\\]\\[productsSave\\]]").val(t.msg)}},"json")};t.ListInit(s)}})}}};$(function(){$("#products_inside").length&&custom_attributes_obj.custom_attributes_init()});