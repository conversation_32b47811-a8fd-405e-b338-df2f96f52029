var google_pixel_obj={google_pixel_init:function(){frame_obj.fixed_right($("#google_pixel .id_add"),".fixed_id_set",function(t){$("#id_set_form textarea[name=TrackingID]").html("");let e=t.data("pixelid"),i=t.data("position"),o=t.data("apisecret"),n=($(".fixed_id_set"),/^G-[a-zA-Z0-9]+/);e?($("#id_set_form textarea[name=TrackingID]").html(e),$("#id_set_form input[name=position]").val(i),n.test(e)&&o?($(".protocol_switchery_box .switchery").addClass("checked").find("input").attr("checked",!0),$(".protocol_apisecret_box").show().find("input").attr("notnull","notnull").val(o)):($(".protocol_switchery_box .switchery").removeClass("checked").find("input").attr("checked",!1),$(".protocol_apisecret_box").hide().find("input").removeAttr("notnull").val("")),$(".measureid_tips_box").hide(),$("#id_set_form input[name=position]").val(i)):($("#id_set_form input[name=position]").val(-1),$(".protocol_switchery_box .switchery").removeClass("checked").find("input").attr("checked",!1),$(".protocol_apisecret_box").hide().find("input").removeAttr("notnull").val(""),$(".measureid_tips_box").hide())}),$(".protocol_switchery_box").on("click",".switchery",function(){let t=$("#id_set_form").find("textarea[name=TrackingID]").val(),e=/^G-[a-zA-Z0-9]+/;return $(this).hasClass("checked")?($(this).removeClass("checked").find("input").attr("checked",!1),e.test(t)?$(".protocol_apisecret_box").hide().find("input").removeAttr("notnull").val(""):$(".measureid_tips_box").hide()):($(this).addClass("checked").find("input").attr("checked",!0),e.test(t)?$(".protocol_apisecret_box").show().find("input").attr("notnull","notnull"):$(".measureid_tips_box").show()),!1}),$("#id_set_form").find("textarea[name=TrackingID]").on("blur",function(){let t=$(this).val(),e=/^G-[a-zA-Z0-9]+/,i=$("#id_set_form .protocol_switchery_box").find(".switchery");i.hasClass("checked")?e.test(t)?($(".protocol_apisecret_box").show().find("input").attr("notnull","notnull"),$(".measureid_tips_box").hide()):($(".protocol_apisecret_box").hide().find("input").removeAttr("notnull"),$(".measureid_tips_box").show()):e.test(t)||$(".measureid_tips_box").show()}),frame_obj.del_init($("#google_pixel .r_con_table")),frame_obj.submit_form_init($("#id_set_form"),"","",0,function(t){return global_obj.win_alert_auto_close(lang_obj.global.save_success,"",1e3,"8%"),$("#fixed_right .btn_cancel").click(),window.location.reload(),!1})}};