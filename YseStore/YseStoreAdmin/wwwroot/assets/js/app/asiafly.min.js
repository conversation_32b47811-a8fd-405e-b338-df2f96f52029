var asiafly_obj={asiafly_verify:function(){frame_obj.fixed_right($("#apply"),".fixed_apply","",function(i){let e=i.data("hint");if(e)return global_obj.win_alert_auto_close(e,"await",2e3,"8%"),!1}),frame_obj.submit_form_init($("#apply_form"),"","","",function(i){1==i.ret?(global_obj.win_alert_auto_close(i.msg,"",2e3,"8%"),setTimeout(function(){window.location.reload()},2e3)):global_obj.win_alert_auto_close(i.msg,"await",2e3,"8%")}),frame_obj.submit_form_init($("#activate_form"),"","","",function(i){1==i.ret?(global_obj.win_alert_auto_close(i.msg,"",2e3,"8%"),setTimeout(function(){window.location.href="/manage/plugins/asiafly/set"},2e3)):global_obj.win_alert_auto_close(i.msg,"await",2e3,"8%")})},asiafly_info:function(){frame_obj.select_all($("input[name=select_all]"),$("input[name=select]")),frame_obj.filterRight({onSubmit:function(i){let e=i.find("input[name=cateId]").val(),a=[];i.find('input[name="products_tagsCurrent[]"]').each(function(i,e){a[i]=$(e).val()}),a=a.join(","),$(".search_box input[name=cateId]").val(e),$(".search_box input[name=tagId]").val(a)}}),frame_obj.fixed_right($("#asiafly .icon_set"),".fixed_info","",function(i){let e=i.parents("tr").data("id");$.post("/manage/plugins/asiafly/get-info",{ProId:e},function(i){if(1==i.ret){$(".fixed_info input[name=ProId]").val(e);let a=parseInt(i.msg.GoodsType);a-=1,$(".fixed_info input[name=GoodsType]").eq(a).prop("checked",!0).parents(".input_radio_box").addClass("checked").siblings().removeClass("checked").find("input").prop("checked",!1),$(".fixed_info textarea[name=Description_en]").val(i.msg.Description_en),$(".fixed_info textarea[name=Description_cn]").val(i.msg.Description_cn),$(".fixed_info input[name=HsCode]").val(i.msg.HsCode),$(".fixed_info input[name=Unit]").val(i.msg.Unit),$(".fixed_info input[name=CustomsValue]").val(i.msg.CustomsValue),$(".unit_input").each(function(){var i=$(this);i.find("input").width(i[0].getBoundingClientRect().width-i.find("b")[0].getBoundingClientRect().width-24)}),$(".fixed_info").find("input, textarea").removeAttr("style")}},"json")}),frame_obj.submit_form_init($("#info_form"),"","","",function(i){if(1==i.ret){global_obj.win_alert_auto_close(lang_obj.global.save_success,"",2e3,"8%"),$(".fixed_info a.close").trigger("click");let e=$("#info_form input[name=ProId]").val();$("#asiafly .r_con_table tr[data-id="+e+"] .desc").html(i.msg)}}),frame_obj.fixed_right($("#asiafly .btn_bat_set"),".bat_fixed_info","",function(i){let e=$(".bat_fixed_info"),a=$("#bat_info_form");a.find("input[name^=ProId]").remove(),$(".r_con_table input[name=select]").each(function(i,e){$(e).get(0).checked&&a.append('<input type="hidden" name="ProId[]" value="'+$(e).val()+'">')}),e.find("input[name=GoodsType]").eq(0).prop("checked",!0).parents(".input_radio_box").addClass("checked").siblings().removeClass("checked").find("input").prop("checked",!1),e.find("textarea[name=Description_en]").val(""),e.find("textarea[name=Description_cn]").val(""),e.find("input[name=HsCode]").val(""),e.find("input[name=Unit]").val(""),e.find("input[name=CustomsValue]").val("0.00"),a.find(".unit_input").each(function(){var i=$(this);i.find("input").width(i[0].getBoundingClientRect().width-i.find("b")[0].getBoundingClientRect().width-24)}),e.find(".rows input, .rows textarea").removeAttr("style").attr("disabled","disabled"),e.find(".input_radio_box").addClass("disabled"),e.find(".switchery").removeClass("checked")}),frame_obj.submit_form_init($("#bat_info_form"),"","","",function(i){1==i.ret&&(global_obj.win_alert_auto_close(lang_obj.global.save_success,"",2e3,"8%"),$(".fixed_info a.close").trigger("click"),setTimeout(function(){location.href=location.href},100))}),frame_obj.switchery_checkbox(function(i){let e=i.parents(".rows");"radio"==i.attr("data-type")?(e.find("input[type=radio]").removeAttr("disabled"),e.find(".input_radio_box").removeClass("disabled")):"textarea"==i.attr("data-type")?e.find("textarea").removeAttr("disabled"):"input"==i.attr("data-type")&&e.find("input[type=text]").removeAttr("disabled"),$("#bat_info_form .switchery.checked").length&&$("#bat_info_form .btn_submit").removeAttr("disabled")},function(i){let e=i.parents(".rows");"radio"==i.attr("data-type")?(e.find("input[type=radio]").attr("disabled","disabled"),e.find(".input_radio_box").addClass("disabled")):"textarea"==i.attr("data-type")?e.find("textarea").attr("disabled","disabled").removeAttr("style"):"input"==i.attr("data-type")&&e.find("input[type=text]").attr("disabled","disabled").removeAttr("style"),$("#bat_info_form .switchery.checked").length||$("#bat_info_form .btn_submit").attr("disabled","disabled")})},asiafly_set:function(){$("#asiafly #permit_limit").on("click",function(){let i=$(this).data("hint");global_obj.win_alert_auto_close(i,"await",2e3,"8%")})},asiafly_pickup:function(){},info_check:function(i){let e="";for(var a in i.msg)e+='<div class="customs_item">',e+='<div class="customs_img pic_box"><img src="'+i.msg[a].PicPath+'" /><span></span></div>',e+='<div class="customs_info">',e+='<div class="rows clean">',e+="<label>"+lang_obj.manage.products.desc_en+"</label>",e+='<div class="input"><input type="text" class="box_input" name="Description_en['+i.msg[a].ProId+']" value="'+i.msg[a].Description_en+'" size="56" maxlength="50" notnull /></div>',e+="</div>",e+='<div class="rows clean">',e+="<label>"+lang_obj.manage.products.desc_cn+"</label>",e+='<div class="input"><input type="text" class="box_input" name="Description_cn['+i.msg[a].ProId+']" value="'+i.msg[a].Description_cn+'" size="56" maxlength="50" notnull /></div>',e+="</div>",e+='<div class="rows clean">',e+="<label>"+lang_obj.manage.products.hscode+"</label>",e+='<div class="input"><input type="text" class="box_input" name="HsCode['+i.msg[a].ProId+']" value="'+i.msg[a].HsCode+'" size="56" notnull /></div>',e+="</div>",e+='<div class="rows clean">',e+="<label>"+lang_obj.manage.products.units+"</label>",e+='<div class="input"><input type="text" class="box_input" name="Unit['+i.msg[a].ProId+']" value="'+i.msg[a].Unit+'" size="56" notnull /></div>',e+="</div>",e+='<div class="rows clean'+(i.msg[a].SKU?" hide":"")+'">',e+="<label>SKU</label>",e+='<div class="input"><input type="text" class="box_input" name="SKU['+i.msg[a].ProId+']" value="'+i.msg[a].SKU+'" size="56" notnull /></div>',e+="</div>",e+='<div class="rows clean">',e+="<label>"+lang_obj.manage.products.customs_value+"<span>"+lang_obj.manage.products.customs_value_tips+"</span></label>",e+='<div class="input"><span class="unit_input"><b>'+shop_config.currSymbol+'</b><input type="text" class="box_input" name="CustomsValue['+i.msg[a].ProId+']" value="'+(i.msg[a].CustomsValue>0?i.msg[a].CustomsValue:"")+'" size="20" maxlength="255" rel="amount" /></span></div>',e+="</div>",e+="</div>",e+='<div class="clear"></div>',e+="</div>";return e},orders_ship:function(){frame_obj.fixed_right($(".fixed_hidden"),".fixed_asiafly_info"),$(".fixed_btn_submit .input_button").css("position","relative").append('<div class="btn_global btn_submit asiafly_check"></div>'),$(".asiafly_check").on("click",function(){let i=$("#edit_form input[name=OrderId]").val(),e=$("#edit_form input[name=WId]").val();$.post("/manage/plugins/asiafly/info-check",{OrderId:i,WId:e},function(i){if(2==i.ret){let e=asiafly_obj.info_check(i);$(".fixed_asiafly_info").find(".customs_list").html(e),$(".fixed_hidden").trigger("click")}else 1==i.ret&&($(".asiafly_check").remove(),$(".fixed_btn_submit .input_button .btn_submit").trigger("click"))},"json")}),$(".orders_ship_asiafly .express_list").on("click",function(){let i=$(this).find("input").val(),e=$(".fixed_btn_submit .input_button");"custom"==i?0==$(".asiafly_selfshipping").length&&e.append('<div class="btn_global btn_submit asiafly_selfshipping"></div>'):$(".asiafly_selfshipping").remove()}),$("body").on("click",".asiafly_selfshipping",function(){window.location.href=$(".orders_ship_asiafly .express_list.selfshipping").data("url")}),frame_obj.submit_form_init($("#asiafly_info_form"),"","",0,function(i){1==i.ret&&($("#asiafly_info_form").find(".btn_cancel").click(),global_obj.win_alert_auto_close(lang_obj.global.save_success,"",2e3,"8%"),$(".asiafly_check").remove())})},waybill:function(){let i=$(".package_button .btn_delivery").width();$(".package_button[data-apitype=asiafly]").css("position","relative").append('<div class="btn_global asiafly_ship"></div>'),$(".package_button .asiafly_ship").width(i),frame_obj.fixed_right($(".asiafly_ship"),".fixed_asiafly_ship",function(i){let e=i.parents(".waybill_list").data("orderid"),a=i.parents(".waybill_list").data("wid");$.post("/manage/plugins/asiafly/get-ship",{OrderId:e,WId:a},function(i){if(1==i.ret){let t="";if(i.msg.Data.length){for(let e in i.msg.Data)t+='<label class="express_list">',t+='<span class="input_radio_box fl '+(e>0?"":"checked")+'">',t+='<span class="input_radio">',t+='<input type="radio" name="ApiShippingId" value="'+i.msg.Data[e].ProductCode+'" '+(e>0?"":"checked")+">",t+="</span>",t+="</span>",t+='<div class="express_info fl">',t+='<div class="express_name">'+i.msg.Data[e].ProductShortName+"</div>",t+="</div>",t+='<div class="express_price fr">'+i.msg.Data[e].Symbol+i.msg.Data[e].Price+"</div>",t+='<div class="clear"></div>',t+="</label>";$(".fixed_asiafly_ship input[name=OrderId]").val(e),$(".fixed_asiafly_ship input[name=WId]").val(a),$(".fixed_asiafly_ship .asiafly_check").length||$(".fixed_asiafly_ship .input_button").css("position","relative").append('<div class="btn_global btn_submit asiafly_check"></div>'),$(".asiafly_selfshipping").remove()}else t+=i.msg.TipsHtml;t+='<label class="express_list selfshipping" data-url="/manage/orders/orders/ship?id='+e+"&WId="+a+'&method=custom">',t+='<span class="input_radio_box fl">',t+='<span class="input_radio">',t+='<input type="radio" name="ApiShippingId" value="custom">',t+="</span>",t+="</span>",t+='<div class="express_info fl">',t+='<div class="express_name">'+lang_obj.manage.app.asiafly.selfshipping+"</div>",t+="</div>",t+='<div class="clear"></div>',t+="</label>",$(".fixed_asiafly_ship .express_box").html(t)}},"json")},function(){$(".fixed_asiafly_ship .express_box").html("")}),$("#asiafly_ship_form").on("click",".asiafly_check",function(){let i=$("#asiafly_ship_form input[name=OrderId]").val();$.post("/manage/plugins/asiafly/info-check",{OrderId:i},function(i){if(2==i.ret){let e=asiafly_obj.info_check(i);$(".fixed_asiafly_info").find(".customs_list").html(e),$(".fixed_hidden").trigger("click")}else 1==i.ret&&($(".asiafly_check").remove(),$(".fixed_asiafly_ship .btn_submit").trigger("click"))},"json")}),$(".fixed_asiafly_ship").on("change",".express_list",function(){let i=$(this).find("input").val(),e=$(".fixed_asiafly_ship .input_button");"custom"==i?e.append('<div class="btn_global btn_submit asiafly_selfshipping"></div>'):$(".asiafly_selfshipping").remove()}),$("body").on("click",".asiafly_selfshipping",function(){window.location.href=$("#asiafly_ship_form .express_list.selfshipping").data("url")}),frame_obj.fixed_right_multi(".fixed_hidden",".fixed_asiafly_info",function(i,e){frame_obj.submit_form_init(e.find("#asiafly_info_form"),"","",0,function(i){1==i.ret&&(e.find("#asiafly_info_form").find(".btn_cancel").click(),global_obj.win_alert_auto_close(lang_obj.global.save_success,"",2e3,"8%"),$(".asiafly_check").remove())})}),frame_obj.submit_form_init($("#asiafly_ship_form"),"","",0,function(i){1==i.ret?(window.location.reload(),$("#asiafly_ship_form").find(".btn_cancel").click()):global_obj.win_alert_auto_close(i.msg,"await",2e3,"8%")})},orders_view:function(){}};