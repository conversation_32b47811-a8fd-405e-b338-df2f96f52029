
var sales_areas_obj = {
    //初始化方法
    sales_areas_init: function () {
        const DeliveryRange = $('.area_edit_box input[name=DeliveryRange]').val()
        let FUNC = {
            Open: "",
            Init: () => {
                frame_obj.box_type_menu(($this) => {
                    let $inputObj = $this.find('input:radio')
                    if ($inputObj.attr('name') == 'WeightUnit') {
                        // 选择重量单位
                        let $value = $this.find('input:radio').val()
                        // 更新表格中的单位显示
                        $('.box_freight tbody').find('td.type, td.list').each((index, element) => {
                            $(element).find('b').text($value)
                        })
                        $('.box_free input[name=FreeShippingWeight]').next().text($value)

                        // 更新弹出框中的单位显示
                        let $freightObj = $('.box_edit_freight')
                        $freightObj.find('b.last').text($value)
                        $freightObj.find('.unit').text(` / ${$value}`)
                        $freightObj.find('.box_calculation .input_radio_box:eq(2) strong').text(lang_obj.manage.shipping.type_each.replace('{{weightUnit}}', $value))
                        $freightObj.find('.box_calculation .input_radio_box:eq(2) p').text(lang_obj.manage.shipping.explain_each.replace('{{weightUnit}}', $value))

                        // 首重+续重弹窗单位更新
                        $freightObj.find('.content[data-type="additional"] b.last').each(function () {
                            $(this).text($value);
                        })

                        // 固定运费弹窗单位更新
                        $freightObj.find('.content[data-type="total"] b.last').each(function () {
                            $(this).text($value);
                        })

                        // 每KG运费弹窗单位更新  
                        $freightObj.find('.content[data-type="each"] b.last').each(function () {
                            $(this).text($value);
                        })

                        // 更新导入弹窗中的单位显示
                        $('.box_freight_import .box_list_freight .content_node').each(function () {
                            let text = $(this).text();
                            let parts = text.split('~');
                            if (parts.length > 1) {
                                // 替换最后的单位部分
                                $(this).text(parts[0] + '~' + parts[1].replace(/[a-z]+$/, $value));
                            }
                        })

                        $weightUnit = $value
                    }
                });
                set_obj.shipping_global.init();
                frame_obj.switchery_checkbox(function (obj) {
                    if (obj.find('input[name^=IsFreeShipping]').length) {
                        obj.parents('.box_free').find('.box_free_content').show();
                    }
                }, function (obj) {
                    if (obj.find('input[name^=IsFreeShipping]').length) {
                        obj.parents('.box_free').find('.box_free_content').hide();
                    }
                }, ".switchery.default_button");
                FUNC.CountryManage();

                frame_obj.fixed_right($('.btn_set_fielId'), '.fixed_field_sort', function (e) { //B端字段排序
                    $('.field_list_wrapper').dragsort('destroy');
                    frame_obj.dragsort($('.field_list_wrapper'), '', '.set_field_item em', 'a', '<div class="placeHolder"></div>');

                })

                frame_obj.submit_form_init($('#area_sort_form'), '', '', 0, function () {
                    global_obj.win_alert_auto_close(lang_obj.global.saved, '', 1000, '8%');
                    window.location.reload();
                });


                frame_obj.select_all($('input[name=select_all]'), $('input[name=select]'), $('.table_menu_button').find('.del')); // 批量操作
                frame_obj.del_bat($('.table_menu_button .del'), $('input[name=select]'), '/api/setting/DeleteSalesArea'); //批量删除

                $('.plugins_app_menu a').click(function () {
                    var $id = $(this).data('id'),
                        $url = $(this).data('url');
                    $('.plugins_app_menu a').removeClass('current');
                    $(this).addClass('current');
                    window.history.pushState(null, null, $url);
                    $('.country_list tbody tr').hide();
                    $('.country_list tbody tr[data-id="' + $id + '"]').show();
                    $('input[name=select_all]').prop("checked", false).parent().removeClass('current indeterminate').parents('tr').removeClass('current')
                    $('.global_menu_button .open').addClass('no_select')
                    $('input[name=select]').prop("checked", false).parent().removeClass('current')
                    $('.btn_checkbox').unbind('click')
                    frame_obj.btnCheckbox()
                    frame_obj.select_all($('input[name=select_all]'), $('tr:visible input[name=select]')) //批量操作
                });

                frame_obj.global_select_box();

                // 添加配送地区
                $('.country_area_box .btn_submit').click(function () {
                    let $html = '',
                        $aid = $('.country_area_box input[name=AId]').val();
                    if (DeliveryRange == 'zipCode') {
                        $('.box_area .content .dl').hide().find('.submit_input').attr('disabled', 'disabled')
                    }
                    $('.country_area_box .country_item input[name=CId]:checked').each(function () {
                        let $this = $(this),
                            $cid = $this.val(),
                            $country = $this.attr('data-country')
                        if (DeliveryRange == 'zipCode') {
                            // 指定邮政编码
                            let cnCountry = $country.split('  ')[0] // 国家中文名称
                            if ($(`.box_area .content .dl[data-cid="${$cid}"]`).length) {
                                $(`.box_area .content .dl[data-cid="${$cid}"]`).show().find('.submit_input:visible, .submit_input[type="hidden"]').removeAttr('disabled')
                            } else {
                                let _keyCid = '_' + $cid
                                let itemCodeStart = FUNC.strReplaceBat($('.box_hiddent_html .hiddent_value_item[data-value-type="CodeStart"]').html(), {
                                    '{{keyCId}}': _keyCid,
                                    '{{keyIndex}}': '_' + '0',
                                    'disabled="disabled"': '',
                                    '{{value}}': ''
                                })
                                let rangeItemHtml = FUNC.strReplaceBat($('.box_hiddent_html .hiddent_range_item').html(), {
                                    '{{index}}': 0,
                                    '{{selectedcodestart}}': 'selected',
                                    '{{selectednumberrange}}': '',
                                    '{{selectedspecify}}': '',
                                    '{{rangeValueHtml}}': itemCodeStart
                                })
                                let dlHtml = FUNC.strReplaceBat($('.box_hiddent_html .hiddent_dl').html(), {
                                    '{{CId}}': $cid,
                                    'disabled="disabled"': '',
                                    '{{CountryName}}': cnCountry,
                                    '{{maxItem}}': 1,
                                    '{{rangeItemHtml}}': rangeItemHtml
                                })
                                $('.box_area .content').append(dlHtml)
                            }
                        } else {
                            // 指定国家/地区
                            let $acronym = $this.attr('data-acronym'),
                                $flagpath = $this.attr('data-flagpath'),
                                $states = $('.country_area_box .country_item .country_item_third[data-cid="' + $cid + '"]'), // 省份
                                $total_states_count = $states.find('input[name=StatesSId]').length, // 总数量
                                $states_count = $states.find('input[name=StatesSId]:checked').length, // 当前选中
                                $select_states_count = $states.find('.input_checkbox_box.disabled').length; // 已经选中
                            $html += '<div class="item">';
                            $states.find('input[name=StatesSId]:checked').each(function () {
                                $html += '<input type="checkbox" name="StatesSId[' + $cid + '][]" value="' + $(this).val() + '" checked />';
                                $(this).removeAttr('checked').parents('.input_checkbox_box').removeClass('checked').addClass('disabled');
                            });
                            $html += '<div class="img">';
                            if ($cid <= 240) {
                                $html += '<div class="icon_flag flag_' + $acronym + '"></div>';
                            } else {
                                $html += '<img src="' + $flagpath + '" />';
                            }
                            $html += '</div>';
                            $html += '<div class="name">' + $country;
                            if ($states.length && $total_states_count != $states_count) {
                                $html += ' (' + $states_count + '/' + $total_states_count + ')';
                            }
                            $html += '</div>';
                            $html += '<div class="area_box del"></div><div class="clear"></div>';
                            $html += '<input type="checkbox" name="CId[]" value="' + $cid + '" checked />';
                            $html += '</div>';
                            $this.removeAttr('checked').parents('.input_checkbox_box').removeClass('checked half_checked').next('.states_count').find('span:eq(0)').text(0);
                            if ($total_states_count == ($states_count + $select_states_count)) {
                                $this.parents('.input_checkbox_box').addClass('disabled');
                            }
                        }
                    });
                    if (DeliveryRange == 'country') $('.box_area .content').html('').append($html);
                    frame_obj.check_amount($('.box_area .range_con'))
                    global_obj.win_alert_auto_close(lang_obj.manage.shipping.add_area_suc, '', 1000, '8%');
                    $('.country_area_box .continent_area>.continent .input_checkbox_box').removeClass('disabled'); // 开放所有洲的勾选
                    $('.country_area_box a.close').click();
                    if ($(".box_area .content .item").length > 0 || $('.country_area_box .country_item input[name=CId]:checked').length) {
                        // 有国家信息
                        $(".box_area .content").fadeIn();
                    } else {
                        // 没有国家信息
                        $(".box_area .content").fadeOut();
                    }
                });
                // 取消事件
                $('body').on('click', '#fixed_right_div_mask, .country_area_box .btn_cancel', function () {
                    if (FUNC.Open != "country_area_box") return false;
                    $('.box_area .content input[type=checkbox][name^="CId"]').each(function () {
                        $('.country_area_box .input_checkbox_box[data-cid="' + $(this).val() + '"]').removeClass('checked half_checked').find('input[type=checkbox]').removeAttr('checked');

                        let $states = $('.country_area_box .country_item .country_item_third[data-cid="' + $(this).val() + '"]'),
                            $total_states_count = $states.find('input[name=StatesSId]').length, // 总数量
                            $states_count = $states.find('input[name=StatesSId]:checked').length, // 当前选中
                            $select_states_count = $states.find('.input_checkbox_box.disabled').length; // 已经选中
                        if ($total_states_count == ($states_count + $select_states_count)) {
                            $('.country_area_box .input_checkbox_box[data-cid="' + $(this).val() + '"]').addClass('disabled');
                        }
                    });
                    $('.box_area .content input[type=checkbox][name^="StatesSId"]').each(function () {
                        $('.country_area_box .input_checkbox_box[data-sid="' + $(this).val() + '"]').removeClass('checked').addClass('disabled').find('input[type=checkbox]').removeAttr('checked');
                    });
                    $('.country_area_box .continent_area>.continent .input_checkbox_box').removeClass('disabled'); // 开放所有洲的勾选
                });
                $(".fixed_btn_submit .btn_continue").click(function () {
                    $("#edit_form input[name=isContinue]").val(1);
                });
                frame_obj.submit_form_init($('#edit_form'), '', '', '', function (data) {
                    let $id = $("#edit_form input[name=AId]").val();
                    let $isContinue = $("#edit_form input[name=isContinue]").val();
                    //let $Model = $("#edit_form input[name=Model]").val();
                    if (data.ret == -1) {
                        global_obj.win_alert_auto_close(data.msg, 'await', 1000, '8%');
                        $('.option_selected').click();
                    } else if (data.ret == -2) {
                        $('.option_selected').click();
                        let params = {
                            'title': lang_obj.manage.shipping.tips.zipcodeConflict,
                            'alertClass': 'zipcode_intersect_tips',
                            'confirmBtn': lang_obj.manage.global.got_it,
                            'extHtml': data.msg,
                            'confirmBtnClass': 'btn_warn'
                        };
                        global_obj.win_alert(params, function () {

                        });
                    } else {
                        global_obj.win_alert_auto_close(lang_obj.global.save_success, '', 1000, '8%');
                        if (($id > 0 || $isContinue == 1)) {
                            window.location.reload();
                        } else {
                            window.location.href = "/store/SalesAreas";
                        }
                    }
                });
                $('#fixed_right .country_area_box .search_form form').submit(function () {
                    let _Keyword = $(this).find('input[name=Keyword]').val();
                    let _parentObj = $('.country_area');
                    $.post('/manage/set/shipping/search-country/', { "Keyword": _Keyword }, function (data) {
                        if (data.ret == 1) {
                            $('#fixed_right .country_area_box .btn_submit').attr("disabled", false);
                            let _searchData = data.msg;
                            if (_searchData.length != 0) {
                                let _areaAry = new Array(),
                                    _countryAry = new Array(),
                                    _statesAry = new Array();
                                for (k in _searchData) {
                                    if (_searchData[k]['Continent'] && $.inArray(_searchData[k]['Continent'], _areaAry) == -1) {
                                        _areaAry.push(_searchData[k]['Continent']);
                                    }
                                    if (_searchData[k]['CId'] && $.inArray(_searchData[k]['CId'], _countryAry) == -1) {
                                        _countryAry.push(_searchData[k]['CId']);
                                    }
                                    if (_searchData[k]['SId'] && $.inArray(_searchData[k]['SId'], _statesAry) == -1) {
                                        _statesAry.push(_searchData[k]['SId']);
                                    }
                                }
                                if (_areaAry.length > 0) {
                                    _parentObj.find('.continent_area').each(function () {
                                        let _areaId = $(this).find('.continent').attr('continent');
                                        if ($.inArray(_areaId, _areaAry) != -1) {
                                            $(this).show();
                                            $(this).find('.continent').find('a.down').addClass('cur');
                                            $(this).find('.country_item').show()
                                        } else {
                                            $(this).hide();
                                        }
                                    })
                                }
                                if (_countryAry.length > 0) {
                                    _parentObj.find('.country_item_sec').each(function () {
                                        let _countryId = $(this).find('.input_checkbox_box').attr('data-cid');
                                        if ($.inArray(_countryId, _countryAry) != -1) {
                                            $(this).show();
                                            if ($(this).next().css('display') == 'block') {
                                                $(this).next().hide()
                                            }
                                        } else {
                                            $(this).hide();
                                        }
                                    })
                                }
                                if (_statesAry.length > 0) {
                                    _parentObj.find('.country_item_third .input_checkbox_box').each(function () {
                                        let _statesId = $(this).attr('data-sid');
                                        if (_statesId && $.inArray(_statesId, _statesAry) != -1) {
                                            $(this).parent().show();
                                            $(this).parent().prev().addClass('cur')
                                            $(this).show();
                                        } else {
                                            $(this).hide();
                                        }
                                    })
                                }
                            } else {
                                _parentObj.find('.continent_area').hide();
                                $('#fixed_right .country_area_box .btn_submit').attr("disabled", true);
                            }
                        } else {
                            $('#fixed_right .country_area_box .btn_submit').attr("disabled", false);
                            _parentObj.find('.continent_area').show();
                            _parentObj.find('.continent_area').find('.continent a.down').removeClass('cur');
                            _parentObj.find('.continent_area').find('.country_item').hide();
                            _parentObj.find('.country_item_sec').removeClass('cur').show();
                            _parentObj.find('.country_item_third').hide();
                            _parentObj.find('.country_item_third .input_checkbox_box').show();
                        }
                    }, 'json');
                    return false;
                })

                //开启关闭

                frame_obj.switchery_checkbox(function (obj) {

                    if (obj.find('input[name=IsOpen]').length) {
                        console.log(obj);

                        $.post('/api/setting/SetSalesAreasStatus/', { "IsOpen": 1 }, function (data) {
                            if (data.ret == 1) {
                                global_obj.win_alert_auto_close(lang_obj.global.save_success, '', 1000, '8%');
                            } else {
                                global_obj.win_alert_auto_close(data.msg, 'await', 1000, '8%');
                            }
                        });
                    }

                }, function (obj) {
                    if (obj.find('input[name=IsOpen]').length) {

                        $.post('/api/setting/SetSalesAreasStatus/', { "IsOpen": 0 }, function (data) {
                            if (data.ret == 1) {
                                global_obj.win_alert_auto_close(lang_obj.global.save_success, '', 1000, '8%');
                            } else {
                                global_obj.win_alert_auto_close(data.msg, 'await', 1000, '8%');
                            }
                        });

                    }
                }, ".sale_areas_box .switchery");


            },
            CountryManage: () => {
                frame_obj.fixed_right($('.btn_add_area'), '.country_area_box', function ($this) {
                    FUNC.Open = "country_area_box";
                    $('.country_area_box input[name=AId]').val($this.attr('data-aid'));
                    $('.country_area_box .continent .input_checkbox_box').removeClass('checked').removeClass('half_checked');
                    // 打开的时候自动选中国家
                    $this.parents('.box_area').find('input[type=checkbox][name^=CId], input[type=hidden][name^=CId]:not(:disabled)').each(function () {
                        $('.country_area_box .input_checkbox_box[data-cid="' + $(this).val() + '"]').removeClass('disabled').addClass('checked').find('input[type=checkbox]').prop('checked', true);
                    });
                    // 打开的时候自动选中国家地区
                    $this.parents('.box_area').find('input[type=checkbox][name^=StatesSId]').each(function () {
                        $('.country_area_box .input_checkbox_box[data-sid="' + $(this).val() + '"]').removeClass('disabled').addClass('checked').find('input[type=checkbox]').prop('checked', true);
                    });
                    // 判断有没有选中国家
                    $('.country_area_box .country_item').each(function () {
                        if ($(this).find('.input_checkbox_box.checked').length == $(this).find('.input_checkbox_box').length) {
                            $(this).prev('.continent').find('.input_checkbox_box').addClass('checked').removeClass('half_checked');
                        } else if ($(this).find('.input_checkbox_box.checked').length > 0) {
                            $(this).prev('.continent').find('.input_checkbox_box').addClass('half_checked');
                        } else {
                            $(this).prev('.continent').find('.input_checkbox_box').removeClass('checked half_checked');
                        }
                    });
                    // 计算选中的地区数量
                    $('.country_area_box .country_states').each(function () {
                        let $total_states_count = $(this).next('.country_item_third').find('.input_checkbox_box').length, // 总数量
                            $states_count = $(this).next('.country_item_third').find('.input_checkbox_box.checked').length, // 当前选中
                            $select_states_count = $(this).next('.country_item_third').find('.input_checkbox_box.disabled').length; // 已经选中
                        $(this).find('.states_count span:eq(0)').text($states_count);
                        if ($total_states_count == $states_count) {
                            $(this).find('.input_checkbox_box').removeClass('half_checked');
                        } else if ($states_count == 0) {
                            $(this).find('.input_checkbox_box').removeClass('checked half_checked');
                        } else {
                            $(this).find('.input_checkbox_box').addClass('checked half_checked');
                        }
                        if ($total_states_count != ($states_count + $select_states_count)) {
                            $(this).find('.input_checkbox_box').removeClass('disabled');
                        }
                    });
                    // 判断有没有选中洲
                    $('.country_area_box .continent_area').each(function () {
                        if ($(this).find('.country_item .input_checkbox_box.disabled').length == $(this).find('.country_item .input_checkbox_box').length) {
                            $(this).find('.continent .input_checkbox_box').addClass('disabled');
                        }
                    });
                    //搜索重新搜索一次空 -- 搜索后添加了地区 重新打开右侧栏的时候搜索框留空，列表显示亚洲为方形选中状态
                    if ($('.country_area_box').find('.search_form input[name=Keyword]').val() != '') {
                        $('.country_area_box').find('.search_form input[name=Keyword]').val('');
                        $('#fixed_right .country_area_box .search_form form').submit();
                    }
                });

                if ($('#shipping .box_area .content').attr('data-range') == 'country') {
                    if ($(".box_area .content .item").length == 0) {
                        // 没有国家信息
                        $(".box_area .content").hide();
                    }
                } else if ($('#shipping .box_area .content').attr('data-range') == 'zipCode') {
                    if ($(".box_area .content .item, .box_area .content .dl").length > 0) {
                        $(".box_area .content").show();
                    } else {
                        $(".box_area .content").hide();
                    }
                }
            },

            ErrorTips: (obj, tips, options = {}) => {
                // 错误提示
                let $class = options['class'] || '';
                if (tips) {
                    obj.find('input').addClass("has_error");
                    if (obj.find(".error_tips").length) {
                        obj.find(".error_tips").text(tips);
                        if ($class) {
                            obj.find(".error_tips").addClass($class);
                        } else {
                            obj.find(".error_tips").attr("class", "error_tips");
                        }
                    } else {
                        obj.append("<p class=\"error_tips" + ($class ? " " + $class : "") + "\">" + tips + "</p>");
                    }
                } else {
                    obj.removeClass("has_error").find(".error_tips").remove();
                }
            },

            LoadEditForm: (target_obj, url, type, value, callback) => {
                $.ajax({
                    type: type,
                    url: url + value,
                    success: function (data) {
                        if (target_obj == '.shipping_area_list') {
                            // 左侧栏目，保留滚动条效果
                            // $(target_obj).find('.jspPane').html($(data).find(target_obj).html());
                            $(target_obj).html($(data).find(target_obj).html());
                        } else {
                            $(target_obj).html($(data).find(target_obj).html());
                            jQuery.getScript(shop_config.CDN_STATIC_URL + 'js/plugin/tool_tips/tool_tips_shipping.js').done(function () {
                                $('.tool_tips_ico').each(function () {
                                    // 弹出提示
                                    $(this).html('&nbsp;');
                                    $('#shipping').tool_tips($(this), {
                                        position: 'horizontal',
                                        html: $(this).attr('content'),
                                        width: 260
                                    });
                                });
                            });
                        }
                        callback && callback(data);
                    }
                });
            },

            rangeDelStatus: ($obj) => {
                // 只剩一个规则时 隐藏删除按钮
                if ($obj.find('.range_item').length == 1) {
                    $obj.find('.range_del').hide()
                } else {
                    $obj.find('.range_del').show()
                }
            },
            strReplaceBat: (str, replacements) => {
                // 同时替换多个值 replacements的key不能有|
                let regex = new RegExp(Object.keys(replacements).join("|"), "g")
                let newStr = str.replace(regex, function (matched) {
                    return replacements[matched]
                })
                return newStr
            }
        }
        FUNC.Init();



    }
}