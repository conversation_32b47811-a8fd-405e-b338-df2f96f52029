.dateselector {display: flex; flex-direction: row; align-items: center; position: relative;}
.dateselector.disabled::after,
.dateselector[disabled]::after {content: ''; display: block; position: absolute; top: 0; left: 0; width: 100%; height: 100%; z-index: 10; background-color: rgba(255, 255, 255, 0.75); }
.dateselector .text.compare {margin-left: 10px;}
.dateselector .text.compare {display: none;}
.dateselector .text.compare > span {margin: 0 4px;}
.dateselector .inner {display: block; position: relative; border: 1px solid #ccdced; border-radius: 4px;}
.dateselector .text.main {position: relative; display: block; box-sizing: border-box; min-width: 120px; margin: 0 10px; padding: 0 12px 0 20px; font-size: 12px; color: #1f2328; cursor: pointer;}
.dateselector .text.main::before {content: ''; position: absolute; top: 1px; left: 0; width: 20px; height: 30px; background: url('../../../images/frame/icon_date.png') no-repeat center;}
.dateselector .text.main .icon {position: absolute; right: 0; color: #8f9dac; font-size: 10px; transform: scale(0.8); -webkit-transform: scale(0.8); transition: all 0.4s;}
.dateselector.active .text.main .icon {transform: rotate(-180deg);}
.dateselector .pane {display: none; opacity: 0; position: absolute; top: 100%; left: 0; z-index: 11; margin: 10px 0 0; padding: 20px 30px 30px; background-color: #fff; border-radius: 5px; box-shadow: 0 0 10px rgba(0, 0, 0, 0.2); transition: all 0.3s;}
.dateselector.active .pane {display: block; opacity: 1;}
.dateselector .item+.item {margin-top: 15px;}
.dateselector .item.btn_group {margin-top: 25px;}
.dateselector .label {font-size: 14px; line-height: 36px; color: #1f2328;}
.dateselector .compare .input {display: none;}
.dateselector select {position: relative; z-index: 1; width: 250px; padding: 5px 20px 5px 10px; background-color: transparent; line-height: 20px; border: 1px solid #ccdced; border-radius: 4px; cursor: pointer; appearance: none; -moz-appearance: none; -webkit-appearance: none;}
.dateselector select::-ms-expand {display: none;}
.dateselector select+.icon {position: absolute; z-index: -1; color: #8f9dac; margin: 1px 0 0 -21px; font-size: 10px; transform: scale(0.8); -webkit-transform: scale(0.8);}
.dateselector select.active+.icon {transform: scale(0.8) rotate(-180deg);}
.dateselector input[type=checkbox] {display: none; opacity: 0;}
.dateselector .checkbox input[type=checkbox]+label{position: relative; padding-left: 25px; cursor: pointer;}
.dateselector .checkbox input[type=checkbox]+label::before {content: ''; position: absolute; top: 0; left: 0; width: 16px; height: 16px; border: 1px solid #ccdced; border-radius: 2px; background-color: #fff;}
.dateselector .checkbox input[type=checkbox]:checked+label::before {border-color: var(--primaryColor); background: var(--primaryColor) url('../../../images/frame/icon_checkbox_checked.png') no-repeat center;}
.dateselector .btn_global {border-radius: 5px; height: 36px; line-height: 36px;}
.dateselector .btn_global+.btn_global {margin-left: 5px;}
.dateselector .btn_global:disabled {background-color: #ccc; border-color: #ccc; cursor: no-drop;}
.dateselector .custom_date {display: none;}
.dateselector .custom_date input.date {
    box-sizing: border-box;
    width: 100%;
    padding: 5px 25px 5px 10px;
    background: url('../../../images/frame/icon_date.png') no-repeat 95% center transparent;
    border: 1px solid #ccdced;
    border-radius: 4px;
}
.dateselector .custom_date input.date.error {border-color: #f00;}