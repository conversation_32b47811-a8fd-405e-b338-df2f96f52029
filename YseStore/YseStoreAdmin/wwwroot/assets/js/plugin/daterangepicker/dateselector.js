if (!window.dateselector) {
    window.dateselector = function(selector, option) {
        const _today = moment($('html').processZoneOffsetData()).startOf('day')

        const _el = $(selector)
        const _model = {
            date: [_today.clone(), _today.clone()],
            compare: {
                checked: false,
                date: [],
            }
        }
        let _debug = false
        let submitCallback = null
        
        option = option || {}
        _debug = option['debug']||false
        if (option['submit']) submitCallback = option['submit']

        const _dateRangeOpt = {
            showDropdowns: true,
            singleDatePicker: true,
            timePicker: false,
            maxDate: _today,
            format: 'YYYY-MM-DD',
            locale: {
                format: 'YYYY-MM-DD',
            },
        }

        function calc() {
            let days = _el.find('select[name=date]').val()
            if (days != 'custom') {
                switch(days) {
                    case 'thisWeek':
                        _model.date[1] = _today.clone()
                        _model.date[0] = _model.date[1].clone().startOf('week')
                        break
                    case 'thisMonth':
                        _model.date[1] = _today.clone()
                        _model.date[0] = _model.date[1].clone().startOf('month')
                        break
                    case 'lastWeek':
                        _model.date[1] = _today.clone().startOf('week').subtract(1, 'day')
                        _model.date[0] = _model.date[1].clone().subtract(1, 'week').add(1, 'day')
                        break
                    case 'lastMonth':
                        _model.date[1] = _today.clone().startOf('month').subtract(1, 'day')
                        _model.date[0] = _model.date[1].clone().startOf('month')
                        break
                    case 'halfyear':
                        _model.date[1] = _today.clone()
                        _model.date[0] = _model.date[1].clone().subtract(0.5, 'years')
                        break
                    case 'year':
                        _model.date[1] = _today.clone().endOf('year').startOf('day')
                        _model.date[0] = _today.clone().startOf('year')
                        break
                    default:
                        _model.date[1] = _today.clone().subtract(days>0 ? 1 : 0, 'days')
                        _model.date[0] = _today.clone().subtract(parseInt(days), 'days')
                        break
                }
            }

            if (!_model.compare.checked) {
                _model.compare.date = []
                debug()
                return
            }

            let type = _el.find('select[name=compare_type]').val()
            switch(type) {
                case 'year':
                    _model.compare.date[0] = _model.date[0].clone().subtract(1, 'years')
                    _model.compare.date[1] = _model.date[1].clone().subtract(1, 'years')
                break
                    
                case 'term':
                default:
                    let diffDays = _model.date[1].diff(_model.date[0], 'days')
                    if (diffDays > 1) {
                        _model.compare.date[1] = _model.date[0].clone().subtract(1, 'days')
                        switch(days) {
                            case 'thisWeek':
                                _model.compare.date[0] = _model.date[0].clone().subtract(1, 'week')
                                break
                            case 'thisMonth':
                                _model.compare.date[0] = _model.date[0].clone().subtract(1, 'month')
                                break
                            case 'lastWeek':
                                _model.compare.date[0] = _model.date[0].clone().subtract(1, 'week')
                                break
                            case 'lastMonth':
                                _model.compare.date[0] = _model.date[0].clone().subtract(1, 'month')
                                break
                            case 'halfyear':
                                _model.compare.date[0] = _model.compare.date[1].clone().subtract(0.5, 'years')
                                break
                            default:
                            _model.compare.date[0] = _model.compare.date[1].clone().subtract(diffDays, 'days')
                                break
                        }
                    } else {
                        _model.compare.date[1] = _model.date[0].clone().subtract(1, 'days')
                        _model.compare.date[0] = _model.compare.date[1].clone().subtract(diffDays, 'days')
                    }
                break
            }

            debug()
        }
        function submit(fn) {
            debug()

            const DATE_FMT = 'YYYY-MM-DD'
            let val = {
                date: [
                    _model.date[0].format(DATE_FMT),
                    _model.date[1].format(DATE_FMT),
                ],
                diff: {
                    ms: _model.date[1].diff(_model.date[0]),
                    days: _model.date[1].diff(_model.date[0], 'days'),
                },
                compare: {
                    checked: _model.compare.checked,
                    date: _model.compare.date.length==0 ? [] : [
                        _model.compare.date[0].format(DATE_FMT),
                        _model.compare.date[1].format(DATE_FMT),
                    ]
                }
            }
            _el.attr('data-value', JSON.stringify(val))

            
            let txt = _el.find('select[name=date]>option:selected').text()
            if (_el.find('select[name=date]>option:selected').val() == 'custom') txt = val.date.join('/')
            _el.find('.text.main > span').text(txt)

            if (val.compare.checked) {
                let text = val.compare.date.join('/')
                if (val.diff.days < 1) text = val.compare.date[0]
                _el.find('.text.compare > span').text(text)
                _el.find('.text.compare').show()
            } else {
                _el.find('.text.compare').hide()
                _el.find('.text.compare > span').text('')
            }

            close()
            //reset()

            if(typeof fn === 'function') fn(val) //回调
        }
        function validate() {
            //开始日期在结束日期之前
            const validated = _model.date[0].isSame(_model.date[1]) || _model.date[0].isBefore(_model.date[1])

            if (validated) {
                _el.find('button.submit').removeAttr('disabled')
                _el.find('.custom_date input.date').removeClass('error')
            } else {
                _el.find('button.submit').attr('disabled', true)
                _el.find('.custom_date input.date').addClass('error')
            }

            return validated
        }
        function close() {
            if (_el.hasClass('active')) {
                _el.removeClass('active')
            }
        }
        function reset() {
            model = {
                date: [_today.clone(), _today.clone()],
                compare: {
                    checked: false,
                    date: [],
                }
            }
        }
        function debug() {
            if (!_debug) return

            const DATE_FMT = 'YYYY-MM-DD HH:mm:ss'
            console.log('------------------ [debug-output] data:', [
                _model.date[0].format(DATE_FMT),
                _model.date[1].format(DATE_FMT),
            ], 'compare: ', {
                checked: _model.compare.checked, 
                date: _model.compare.date.length==0 ? [] : [
                    _model.compare.date[0].format(DATE_FMT),
                    _model.compare.date[1].format(DATE_FMT),
                ]
            })
        }
        function getZoneTime(offset) {
            // 取本地时间
            var localtime = new Date();
            // 取本地毫秒数
            var localmesc = localtime.getTime();
            // 取本地时区与格林尼治所在时区的偏差毫秒数
            var localOffset = localtime.getTimezoneOffset() * 60000;
            // 反推得到格林尼治时间
            var utc = localOffset + localmesc;
            // 得到指定时区时间
            var calctime = utc + (3600000 * offset);
            var nd = new Date(calctime);
        
            return nd.toJSON().replace("T"," ").replace("Z"," ");
        }


        (function init() {
            _el.on('click mouseover', '.selector', function() {
                if (_el.hasClass('active')) return
                _el.addClass('active')
            }).on('mouseout', '.pane', function() {
                close()
            })

            _el.find('.form input:checkbox').change(function() {
                _model.compare.checked = $(this).is(':checked')
                if (_model.compare.checked) {
                    _el.find('.item.compare .input').show()
                } else {
                    _el.find('.item.compare .input').hide()
                }
                calc()
            })
            if (_el.find('.form input:checkbox').is(':checked')) {
                _el.find('.item.compare .input').show()
                _model.compare.checked = true
                calc()
            }

            _el.find('select[name=date]').change(function() {
                const val = $(this).val()
                if (val == 'custom') {
                    _el.find('.custom_date input[name=beginAt]').val(_today.format(_dateRangeOpt.format))
                    _el.find('.custom_date input[name=endAt]').val(_today.format(_dateRangeOpt.format))
                    _el.find('.custom_date.item').show()
                    return 
                } else {
                    _el.find('.custom_date.item').hide()
                }
                calc()
            })

            _el.find('select').on('focus', function() {
                _el.off('mouseout')
            }).on('blur', function() {
                _el.on('mouseout', '.pane', function() {
                    close()
                })
            }).on('blur click', function(e) {
                const self = $(this)
                switch (e.type) {
                    case 'blur':
                        self.removeClass('active')
                        e.stopPropagation()
                        return
                    case 'click':
                        if (self.hasClass('active')) { self.removeClass('active') }
                        else { self.addClass('active') }
                        e.stopPropagation()
                        return
                }
            })

            _el.find('.custom_date input[name=beginAt]').daterangepicker(_dateRangeOpt, function(start) {
                _model.date[0] = start
                _el.find('.custom_date input[name=beginAt]').val(start.format(_dateRangeOpt.format))

                if (_el.find('.custom_date input[name=endAt]').val()) {
                    if (validate()) calc()
                }
            })
            
            _el.find('.custom_date input[name=endAt]').daterangepicker(_dateRangeOpt, function(start) {
                _model.date[1] = start
                _el.find('.custom_date input[name=endAt]').val(start.format(_dateRangeOpt.format))

                if (_el.find('.custom_date input[name=beginAt]').val()) {
                    if (validate()) calc()
                }
            })

            _el.find('.custom_date input[name=beginAt]').on('apply.daterangepicker', function(e, picker) {
                if (typeof(picker) == "undefined") picker = {"startDate":moment(_el.find('.custom_date input[name=beginAt]').val()), "format": "YYYY-MM-DD"}
                _model.date[0] = picker.startDate
                _el.find('.custom_date input[name=beginAt]').val(picker.startDate.format(picker.format))
                
                if (_el.find('.custom_date input[name=endAt]').val()) {
                    if (validate()) calc()
                }
                _el.find('.custom_date input[name=endAt]').data('daterangepicker').setStartDate(_model.date[0])
            }).on('show.daterangepicker', function() {
                _el.off('mouseout')
            }).on('hide.daterangepicker', function() {
                _el.on('mouseout', '.pane', function() {
                    close()
                })
            })
            _el.find('.custom_date input[name=endAt]').on('apply.daterangepicker', function(e, picker) {
                if (typeof(picker) == "undefined") picker = {"startDate":moment(_el.find('.custom_date input[name=endAt]').val()), "format": "YYYY-MM-DD"}
                _model.date[1] = picker.startDate
                _el.find('.custom_date input[name=endAt]').val(picker.startDate.format(picker.format))
                
                if (_el.find('.custom_date input[name=beginAt]').val()) {
                    if (validate()) calc()
                }
                _el.find('.custom_date input[name=beginAt]').data('daterangepicker').setEndDate(_model.date[1])
            }).on('show.daterangepicker', function() {
                _el.off('mouseout')
            }).on('hide.daterangepicker', function() {
                _el.on('mouseout', '.pane', function() {
                    close()
                })
            })

            _el.find('select[name=compare_type]').change(function() {
                calc()
            })

            _el.find('button.submit').click(function(e) {
                e.stopPropagation()
                if ($(this).is(':disabled')) return
                submit(submitCallback)
            })

            _el.find('.cancel').click(function(e) {
                e.stopPropagation()
                close()
            })
        })()

        return {el: _el, debug: _debug}
    }
}