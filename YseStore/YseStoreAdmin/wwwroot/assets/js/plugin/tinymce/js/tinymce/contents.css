/*
Copyright (c) 2003-2016, CKSource - <PERSON><PERSON>. All rights reserved.
For licensing, see LICENSE.md or http://ckeditor.com/license
*/
/*
body
{
	font-family: Verdana;
	font-size: 12px;

	color: #333;
	background-color: #fff;

	margin: 20px;
}

.cke_editable
{
	font-size: 13px;
	line-height: 1.6;
	word-wrap: break-word;
}

blockquote
{
	font-style: italic;
	font-family: Georgia, Times, "Times New Roman", serif;
	padding: 2px 0;
	border-style: solid;
	border-color: #ccc;
	border-width: 0;
}

.cke_contents_ltr blockquote
{
	padding-left: 20px;
	padding-right: 8px;
	border-left-width: 5px;
}

.cke_contents_rtl blockquote
{
	padding-left: 8px;
	padding-right: 20px;
	border-right-width: 5px;
}

a
{
	color: #0782C1;
}

ol,ul,dl
{
	*margin-right: 0px;
	padding: 0 40px;
}

h1,h2,h3,h4,h5,h6
{
	font-weight: normal;
	line-height: 1.2;
}

hr
{
	border: 0px;
	border-top: 1px solid #ccc;
}

img.right
{
	border: 1px solid #ccc;
	float: right;
	margin-left: 15px;
	padding: 5px;
}

img.left
{
	border: 1px solid #ccc;
	float: left;
	margin-right: 15px;
	padding: 5px;
}

pre
{
	white-space: pre-wrap; 
	word-wrap: break-word;
	-moz-tab-size: 4;
	tab-size: 4;
}

.marker
{
	background-color: Yellow;
}

span[lang]
{
	font-style: italic;
}

figure
{
	text-align: center;
	border: solid 1px #ccc;
	border-radius: 2px;
	background: rgba(0,0,0,0.05);
	padding: 10px;
	margin: 10px 20px;
	display: inline-block;
}

figure > figcaption
{
	text-align: center;
	display: block; 
}

a > img {
	padding: 1px;
	margin: 1px;
	border: none;
	outline: 1px solid #0782C1;
}

*/
/*
Copyright (c) 2003-2016, CKSource - Frederico Knabben. All rights reserved.
For licensing, see LICENSE.md or http://ckeditor.com/license
*/
body { font-family: Verdana; font-size: 12px;	/* Text color */ color: #333;	/* Remove the background color to make it transparent */ background-color: #fff; margin: 20px; }
.cke_editable { font-size: 13px; line-height: 1.6;	/* Fix for missing scrollbars with RTL texts. (#10488) */ word-wrap: break-word; }
.cke_editable img{max-width: 100%;max-height: 100%;}
blockquote { font-style: italic; font-family: Georgia, Times, "Times New Roman", serif; padding: 2px 0; border-style: solid; border-color: #ccc; border-width: 0; }
.cke_contents_ltr blockquote { padding-left: 20px; padding-right: 8px; border-left-width: 5px; }
.cke_contents_rtl blockquote { padding-left: 8px; padding-right: 20px; border-right-width: 5px; }

/*h1,h2,h3,h4,h5,h6,em{ font-weight: normal; line-height: 1.2; }*/
hr { border: 0px; border-top: 1px solid #ccc; }
img.right { float: right; margin-left: 15px; padding: 5px; }
img.left { float: left; margin-right: 15px; padding: 5px; }
pre { white-space: pre-wrap; /* CSS 2.1 */ word-wrap: break-word; /* IE7 */ -moz-tab-size: 4; tab-size: 4; }
.marker { background-color: Yellow; }
/*span[lang] { font-style: italic; }*/
figure { text-align: center; border: solid 1px #ccc; border-radius: 2px; background: rgba(0, 0, 0, 0.05); padding: 10px; margin: 10px 20px; display: inline-block; }
figure > figcaption { text-align: center; display: block; /* For IE8 */ }

/*global css*/
html,body,h1,h2,h3,h4,h5,h6,div,dl,dt,dd,ul,ol,li,p,blockquote,pre,hr,figure,table,caption,th,td,form,fieldset,legend,input,button,textarea,menu{margin:0; padding:0;}
body{padding: 10px 20px!important;line-height: 1.6;}
body,textarea,input,button,select,keygen,legend{font-size:14px; color:#333; font-family:Arial; -webkit-text-size-adjust:none; outline:0;}
/*
header,footer,section,article,aside,nav,hgroup,address,figure,figcaption,menu,details{display:block;}
table{border-collapse:collapse; border-spacing:0;}
body.cke_show_borders table.cke_show_border, body.cke_show_borders table.cke_show_border > tr > td, body.cke_show_borders table.cke_show_border > tr > th, body.cke_show_borders table.cke_show_border > tbody > tr > td, body.cke_show_borders table.cke_show_border > tbody > tr > th, body.cke_show_borders table.cke_show_border > thead > tr > td, body.cke_show_borders table.cke_show_border > thead > tr > th, body.cke_show_borders table.cke_show_border > tfoot > tr > td, body.cke_show_borders table.cke_show_border > tfoot > tr > th{border:none;}
caption,th{text-align:left; font-weight:normal;}
html,body,fieldset,img,iframe,abbr{border:none;}
i,cite,em,var,address,dfn{font-style:normal;}
li{list-style:none;}
sup,sub{font-size:80%;}
pre,code,kbd,samp{font-family:inherit;}
q:before,q:after{content:none;}
textarea{overflow:auto; resize:none;}
label,summary{cursor:default;}
*/
a,button{cursor:pointer;}
ins,u,s,a{/*text-decoration:none;*/}
textarea,input{outline:none; -webkit-tap-highlight-color:rgba(0,0,0,0);}
input[type='text'],input[type='number'],input[type='password'],input[type='reset'],input[type='submit'],input[type='button'],input[type='tel'],button,textarea{-webkit-appearance:none; border:1px #cbcecd solid; outline:none;}
input:-webkit-autofill,textarea:-webkit-autofill,select:-webkit-autofill{-webkit-box-shadow:0 0 0 1000px #fff inset;}

*{font-family:inherit;}/*兼容子级span的字体被顶级字体所覆盖*/
/*
h1,h2,h3,h4,h5,h6{font-weight:normal; line-height:1.2; margin:.67em 0;}
b,strong{font-weight:bold; line-height:1.2;color:inherit; font-size:inherit; font-family:inherit;}
h1{font-size:2em;}
h2{font-size:1.5em;}
h3{font-size:1.17em;}
h4{font-size:1em; margin:1.33em 0;}
h5{font-size:0.83em;}
h6{font-size:0.67em;}
*/
li{list-style-type:inherit;}
i,  cite,  em{font-style:italic;}

.selectTdClass{background-color:#edf5fa!important;}
table { border-collapse:collapse; display:table; }
table th,
table td { word-break: break-word; }
table:not([cellpadding]) td, table:not([cellpadding]) th { padding:.4rem }
table:not([border="0"]):not([style*=border-width]) td, table:not([border="0"]):not([style*=border-width]) th { border-width: 1px }
table:not([border="0"]):not([style*=border-style]) td, table:not([border="0"]):not([style*=border-style]) th { border-style: solid }
table:not([border="0"]):not([style*=border-color]) td, table:not([border="0"]):not([style*=border-color]) th { border-color: #ccc }
caption{border:1px #ddd dashed; border-bottom:0; padding:3px; text-align:center;}
th{border-top:1px #bbb solid; background-color:#f7f7f7;}
.ue-table-interlace-color-single{background-color:#fcfcfc;}
.ue-table-interlace-color-double{background-color:#f7faff;}

.pagebreak{display:block; clear:both!important; cursor:default!important; width:100%!important; margin:0;}
pre{margin:.5em 0; padding:.4em .6em; border-radius:8px; background:#f8f8f8;}

.fl { float: left; }
.fr { float: right; }
.clear { margin: 0 auto; clear: both; height: 0; font-size: 0; overflow: hidden; }

.tox-notifications-container,.tox .tox-tiered-menu .tox-menu.tox-collection.tox-collection--list .tox-collection__item[title='图片...']{display: none!important;}/*,.tox .tox-statusbar*/