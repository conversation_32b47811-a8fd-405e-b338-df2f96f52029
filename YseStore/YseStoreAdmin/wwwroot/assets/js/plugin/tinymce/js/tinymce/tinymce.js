tinymce.PluginManager.add('localimg_'+"<?=$name;?>", function(editor, url) {
	var openDialog = function () {
		frame_obj.photo_choice_init('','',"<?=$name;?>", 'editor', 9999);
	};
	/* Add a button that opens a window */
	editor.ui.registry.addButton('localimg_'+"<?=$name;?>", {
		icon: 'image',
		onAction: function (e) {
			/* Open window */
			openDialog();
		}
	});
	/* Adds a menu item, which can then be included in any menu via the menu/menubar configuration */
	editor.ui.registry.addMenuItem('localimg_'+"<?=$name;?>", {
		text: 'Images plugin',
		onAction: function() {
			/* Open window */
			openDialog();
		}
	});
	/* Return the metadata for the help plugin */
	return {
		getMetadata: function () {
			return  {
				name: 'Images plugin',
				url: 'http://exampleplugindocsurl.com'
			};
		}
	};
});
var demoBaseConfig = {
	selector: 'textarea#'+"<?=$name;?>",
    // contextmenu: "copy link image",
    contextmenu: "cut copy link linkchecker image imagetools table spellchecker configurepermanentpen",// paste
	ui_container: '#edit_form',
	language: shop_config.manage_language=='cn'?'zh_CN':'EN',
	width: '100%',
	max_width: '100%',
	min_height: 500,
	max_height: 500,
	fontsize_formats: '10px 12px 14px 15px 16px 18px 20px 22px 24px 28px 36px 48px 60px 72px',
	lineheight_formats: '1 1.5 1.8 2 2.5 3 3.5 4',
	font_formats: 'Arial=Arial;Arial Black=arial black,Arial-Black,Arial;Andale Mono=andale mono,Arial;Comic Sans Ms=comic sans ms,Arial;Times New Roman=Times New Roman,Arial;Cambria=Cambria,Arial;Calibri=Calibri,Arial;Courier New=Courier New,Arial;Verdana=Verdana,Arial;Georgia=Georgia,Arial;Tahoma=Tahoma,Arial;Times=Times,Arial;serif=serif,Arial;黑体=黑体,Arial;隶书=隶书,Arial;宋体=宋体,Arial;新宋体=新宋体,Arial;微软雅黑=微软雅黑,Arial;楷体_GB2312=楷体_GB2312,Arial',
	resize: true,//false
	cleanup: false,//true
	verify_html: false,//true
	autosave_ask_before_unload: false,//false
	powerpaste_allow_local_images: false,//true
	plugins: 'print preview searchreplace autolink directionality visualblocks visualchars fullscreen localimg_'+"<?=$name;?>"+' image link anchor media code table advlist lists textpattern autosave autoresize',//
	toolbar: 'code undo redo | fullscreen | table localimg_'+"<?=$name;?>"+' media| fontselect fontsizeselect lineheight | forecolor backcolor bold italic underline strikethrough link anchor removeformat | bullist numlist | alignleft aligncenter alignright alignjustify outdent indent  | cut copy paste pastetext',// image
	spellchecker_dialog: false,//true
	tinydrive_token_provider: function (success, failure) {
	success({ token: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**********************************************************************.Ks_BdfH4CWilyzLNk8S2gDARFhuxIauLa8PwhdEQhEo.iokrj7v4cosyfzvm9znfr6x3r8oldn90cbbftr2iebdxtcy8' });
	},
	content_css : ['/assets/js/plugin/tinymce/js/tinymce/contents.css'],
	content_css_cors: true,
	paste_webkit_styles: true,
	forced_root_block: '',
	elements: 'name,style',
	valid_elements: '*[*]',
	extended_valid_elements: '*[*]',
	valid_children : '+body[link|script|style|strong]',
	content_style: 'img{max-width:100%;max-height:100%;vertical-align: middle;}',
	branding: false,//技术支持
	allow_script_urls: true,
	object_resizing: false,
	setup: function (ed) {
		ed.on("change", function () {
			TinyMceGetStatsLost(ed);
		})
	}
	//elementpath: false,//底栏的元素路径
};
function TinyMceGetStatsLost(inst) {
	var str = inst.getBody().innerHTML;
	var imgReg = /<img.*?(?:>|\/>)/gi;
	//匹配src属性
	var srcReg = /src=[\'\"]?([^\'\"]*)[\'\"]?/i;
	var arr = str.match(imgReg);
	if(arr){
		for (var i = 0; i < arr.length; i++) {
			var src = arr[i].match(srcReg);
			//获取图片地址
			if(src[1].indexOf("data:image/png;base64") != -1){
				var rep_str = src[1];
				$.post('./?do_action=action.editor_img_upload', 'html='+src[1], function(data){
					if (data.ret == 1) {
						str = str.replace(rep_str, data.msg);
						inst.setContent(str);//更新编辑器内容
					}
				}, 'json');
			}else if(src[1].indexOf("file:///") != -1){
				var rep_str = src[1];
				str = str.replace(rep_str, '');
				inst.setContent(str);//更新编辑器内容
			}
		}
	}
}

tinymce.init(demoBaseConfig);