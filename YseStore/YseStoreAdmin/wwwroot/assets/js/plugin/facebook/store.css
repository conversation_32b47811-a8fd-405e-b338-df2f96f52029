

.facebook_process{width:650px; height:450px; overflow:hidden; background:#fff; border:1px #ccc solid; border-radius:2px; -moz-border-radius:2px; -webkit-border-radius:2px; position:fixed; left:0; top:25%; z-index:99999; opacity:0.95; filter:alpha(opacity=95); box-shadow:0 2px 10px rgba(0,0,0,.25); -webkit-box-shadow:0 2px 10px rgba(0,0,0,.25); transition:top 0.25s; -webkit-transition:top 0.25s; -moz-transition:top 0.25s;}
.facebook_process .content{height:398px; overflow-y:scroll; position:relative;}
.facebook_process.facebook_process .r_con_table{margin:10px;}
.facebook_process .r_con_table .loading{width:14px; height:20px; background:url(../images/global/loading_small.gif) no-repeat left center; display:block;}
.facebook_process .r_con_table .error *{color:#c00;}
.facebook_process .btn_view{height:32px; padding:10px 0; text-align:right; background-color:#f9f9f9;}
.facebook_process .btn_view .btn_close{margin-right:20px;}