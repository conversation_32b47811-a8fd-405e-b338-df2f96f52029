!function(e){"object"==typeof exports&&"object"==typeof module?e(require("../../lib/codemirror")):"function"==typeof define&&define.amd?define(["../../lib/codemirror"],e):e(CodeMirror)}(function(g){"use strict";function e(i){return function(c,o){var d=o.line,l=c.getLine(d);for(var e=[],r=0;r<i.length;r++){var n=function(e){for(var r,n=o.ch,t=0;;){var i=n<=0?-1:l.lastIndexOf(e[0],n-1);if(-1!=i){if(1==t&&i<o.ch)break;if(r=c.getTokenTypeAt(g.Pos(d,i+1)),!/^(comment|string)/.test(r))return{ch:i+1,tokenType:r,pair:e};n=i-1}else{if(1==t)break;t=1,n=l.length}}}(i[r]);n&&e.push(n)}e.sort(function(e,r){return e.ch-r.ch});for(r=0;r<e.length;r++){var t=function(e){var r,n,t=1,i=c.lastLine(),o=e.ch;e:for(var l=d;l<=i;++l)for(var f=c.getLine(l),u=l==d?o:0;;){var s=f.indexOf(e.pair[0],u),a=f.indexOf(e.pair[1],u);if(s<0&&(s=f.length),a<0&&(a=f.length),(u=Math.min(s,a))==f.length)break;if(c.getTokenTypeAt(g.Pos(l,u+1))==e.tokenType)if(u==s)++t;else if(!--t){r=l,n=u;break e}++u}return null==r||d==r?null:{from:g.Pos(d,o),to:g.Pos(r,n)}}(e[r]);if(t)return t}return null}}g.registerHelper("fold","brace",e([["{","}"],["[","]"]])),g.registerHelper("fold","brace-paren",e([["{","}"],["[","]"],["(",")"]])),g.registerHelper("fold","import",function(o,e){function r(e){if(e<o.firstLine()||e>o.lastLine())return null;var r=o.getTokenAt(g.Pos(e,1));if("keyword"!=(r=!/\S/.test(r.string)?o.getTokenAt(g.Pos(e,r.end+1)):r).type||"import"!=r.string)return null;for(var n=e,t=Math.min(o.lastLine(),e+10);n<=t;++n){var i=o.getLine(n).indexOf(";");if(-1!=i)return{startCh:r.end,end:g.Pos(n,i)}}}var n=e.line,t=r(n);if(!t||r(n-1)||(e=r(n-2))&&e.end.line==n-1)return null;for(var i=t.end;;){var l=r(i.line+1);if(null==l)break;i=l.end}return{from:o.clipPos(g.Pos(n,t.startCh+1)),to:i}}),g.registerHelper("fold","include",function(n,e){function r(e){if(e<n.firstLine()||e>n.lastLine())return null;var r=n.getTokenAt(g.Pos(e,1));return"meta"==(r=!/\S/.test(r.string)?n.getTokenAt(g.Pos(e,r.end+1)):r).type&&"#include"==r.string.slice(0,8)?r.start+8:void 0}var t=e.line,e=r(t);if(null==e||null!=r(t-1))return null;for(var i=t;;){if(null==r(i+1))break;++i}return{from:g.Pos(t,e+1),to:n.clipPos(g.Pos(i))}})});