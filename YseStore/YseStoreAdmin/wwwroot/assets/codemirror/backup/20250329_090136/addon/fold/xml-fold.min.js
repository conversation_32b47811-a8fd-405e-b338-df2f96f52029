!function(e){"object"==typeof exports&&"object"==typeof module?e(require("../../lib/codemirror")):"function"==typeof define&&define.amd?define(["../../lib/codemirror"],e):e(CodeMirror)}(function(e){"use strict";var l=e.Pos;function o(e,n){return e.line-n.line||e.ch-n.ch}var n="A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD",i=new RegExp("<(/?)(["+n+"][A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD-:.0-9\\u00B7\\u0300-\\u036F\\u203F-\\u2040]*)","g");function c(e,n,t,i){this.line=n,this.ch=t,this.cm=e,this.text=e.getLine(n),this.min=i?Math.max(i.from,e.firstLine()):e.firstLine(),this.max=i?Math.min(i.to-1,e.lastLine()):e.lastLine()}function a(e,n){n=e.cm.getTokenTypeAt(l(e.line,n));return n&&/\btag\b/.test(n)}function r(e){return!(e.line>=e.max)&&(e.ch=0,e.text=e.cm.getLine(++e.line),1)}function s(e){return!(e.line<=e.min)&&(e.text=e.cm.getLine(--e.line),e.ch=e.text.length,1)}function h(e){for(;;){var n=e.text.indexOf(">",e.ch);if(-1==n){if(r(e))continue;return}if(a(e,n+1)){var t=e.text.lastIndexOf("/",n),t=-1<t&&!/\S/.test(e.text.slice(t+1,n));return e.ch=n+1,t?"selfClose":"regular"}e.ch=n+1}}function F(e){for(;;){var n=e.ch?e.text.lastIndexOf("<",e.ch-1):-1;if(-1==n){if(s(e))continue;return}if(a(e,n+1)){i.lastIndex=n,e.ch=n;var t=i.exec(e.text);if(t&&t.index==n)return t}else e.ch=n}}function x(e){for(;;){i.lastIndex=e.ch;var n=i.exec(e.text);if(!n){if(r(e))continue;return}if(a(e,n.index+1))return e.ch=n.index+n[0].length,n;e.ch=n.index+1}}function g(e,n){for(var t=[];;){var i,r=x(e),u=e.line,f=e.ch-(r?r[0].length:0);if(!r||!(i=h(e)))return;if("selfClose"!=i)if(r[1]){for(var o=t.length-1;0<=o;--o)if(t[o]==r[2]){t.length=o;break}if(o<0&&(!n||n==r[2]))return{tag:r[2],from:l(u,f),to:l(e.line,e.ch)}}else t.push(r[2])}}function d(e,n){for(var t=[];;){var i=function(e){for(;;){var n=e.ch?e.text.lastIndexOf(">",e.ch-1):-1;if(-1==n){if(s(e))continue;return}if(a(e,n+1)){var t=e.text.lastIndexOf("/",n),t=-1<t&&!/\S/.test(e.text.slice(t+1,n));return e.ch=n+1,t?"selfClose":"regular"}e.ch=n}}(e);if(!i)return;if("selfClose"!=i){var r=e.line,i=e.ch,u=F(e);if(!u)return;if(u[1])t.push(u[2]);else{for(var f=t.length-1;0<=f;--f)if(t[f]==u[2]){t.length=f;break}if(f<0&&(!n||n==u[2]))return{tag:u[2],from:l(e.line,e.ch),to:l(r,i)}}}else F(e)}}e.registerHelper("fold","xml",function(e,n){for(var t=new c(e,n.line,0);;){var i=x(t);if(!i||t.line!=n.line)return;var r=h(t);if(!r)return;if(!i[1]&&"selfClose"!=r){r=l(t.line,t.ch),i=g(t,i[2]);return i&&0<o(i.from,r)?{from:r,to:i.from}:null}}}),e.findMatchingTag=function(e,n,t){var i=new c(e,n.line,n.ch,t);if(-1!=i.text.indexOf(">")||-1!=i.text.indexOf("<")){var r=h(i),u=r&&l(i.line,i.ch),f=r&&F(i);if(r&&f&&!(0<o(i,n))){n={from:l(i.line,i.ch),to:u,tag:f[2]};return"selfClose"==r?{open:n,close:null,at:"open"}:f[1]?{open:d(i,f[2]),close:n,at:"close"}:{open:n,close:g(i=new c(e,u.line,u.ch,t),f[2]),at:"open"}}}},e.findEnclosingTag=function(e,n,t,i){for(var r=new c(e,n.line,n.ch,t);;){var u=d(r,i);if(!u)break;var f=g(new c(e,n.line,n.ch,t),u.tag);if(f)return{open:u,close:f}}},e.scanForClosingTag=function(e,n,t,i){return g(new c(e,n.line,n.ch,i?{from:0,to:i}:null),t)}});