# CodeMirror Update Script

param (
    [string]$Version = "5.65.2" # Default version
)

# Ensure script runs with proper encoding
if ($PSVersionTable.PSVersion.Major -ge 6) {
    # PowerShell Core 6+ has better UTF-8 support
    $PSDefaultParameterValues['Out-File:Encoding'] = 'utf8'
    $PSDefaultParameterValues['Set-Content:Encoding'] = 'utf8'
    $OutputEncoding = [System.Text.Encoding]::UTF8
} else {
    # Windows PowerShell 5.1 and earlier
    $OutputEncoding = [System.Text.Encoding]::UTF8
    # Try to enable UTF-8 where possible
    try {
        [Console]::OutputEncoding = [System.Text.Encoding]::UTF8
    } catch {
        Write-Warning "Could not set console encoding to UTF-8. You may see some characters incorrectly displayed."
    }
}

$baseDir = $PSScriptRoot
$cdnBase = "https://cdnjs.cloudflare.com/ajax/libs/codemirror/$Version"

Write-Host "Starting update of CodeMirror to version $Version" -ForegroundColor Cyan

# Ensure directories exist
$dirs = @(
    "$baseDir\lib",
    "$baseDir\theme",
    "$baseDir\addon\selection",
    "$baseDir\addon\edit",
    "$baseDir\addon\fold",
    "$baseDir\addon\search",
    "$baseDir\addon\dialog",
    "$baseDir\mode\javascript",
    "$baseDir\mode\css",
    "$baseDir\mode\xml",
    "$baseDir\mode\htmlmixed",
    "$baseDir\mode\markdown"
)

foreach ($dir in $dirs) {
    if (!(Test-Path $dir)) {
        New-Item -ItemType Directory -Path $dir -Force | Out-Null
        Write-Host "Created directory: $dir" -ForegroundColor Gray
    }
}

# Define file list to download
$files = @(
    # Core files
    @{url="$cdnBase/codemirror.min.js"; dest="$baseDir\lib\codemirror.min.js"},
    @{url="$cdnBase/codemirror.min.css"; dest="$baseDir\lib\codemirror.min.css"},
    
    # Theme files
    @{url="$cdnBase/theme/monokai.min.css"; dest="$baseDir\theme\monokai.min.css"},
    
    # Addon - Selection
    @{url="$cdnBase/addon/selection/active-line.min.js"; dest="$baseDir\addon\selection\active-line.min.js"},
    
    # Addon - Edit
    @{url="$cdnBase/addon/edit/matchbrackets.min.js"; dest="$baseDir\addon\edit\matchbrackets.min.js"},
    
    # Addon - Fold
    @{url="$cdnBase/addon/fold/foldcode.min.js"; dest="$baseDir\addon\fold\foldcode.min.js"},
    @{url="$cdnBase/addon/fold/foldgutter.min.js"; dest="$baseDir\addon\fold\foldgutter.min.js"},
    @{url="$cdnBase/addon/fold/foldgutter.min.css"; dest="$baseDir\addon\fold\foldgutter.min.css"},
    @{url="$cdnBase/addon/fold/brace-fold.min.js"; dest="$baseDir\addon\fold\brace-fold.min.js"},
    @{url="$cdnBase/addon/fold/xml-fold.min.js"; dest="$baseDir\addon\fold\xml-fold.min.js"},
    @{url="$cdnBase/addon/fold/comment-fold.min.js"; dest="$baseDir\addon\fold\comment-fold.min.js"},
    
    # Addon - Search
    @{url="$cdnBase/addon/search/search.min.js"; dest="$baseDir\addon\search\search.min.js"},
    @{url="$cdnBase/addon/search/searchcursor.min.js"; dest="$baseDir\addon\search\searchcursor.min.js"},
    @{url="$cdnBase/addon/search/jump-to-line.min.js"; dest="$baseDir\addon\search\jump-to-line.min.js"},
    
    # Addon - Dialog
    @{url="$cdnBase/addon/dialog/dialog.min.js"; dest="$baseDir\addon\dialog\dialog.min.js"},
    @{url="$cdnBase/addon/dialog/dialog.min.css"; dest="$baseDir\addon\dialog\dialog.min.css"},
    
    # Language modes
    @{url="$cdnBase/mode/javascript/javascript.min.js"; dest="$baseDir\mode\javascript\javascript.min.js"},
    @{url="$cdnBase/mode/css/css.min.js"; dest="$baseDir\mode\css\css.min.js"},
    @{url="$cdnBase/mode/xml/xml.min.js"; dest="$baseDir\mode\xml\xml.min.js"},
    @{url="$cdnBase/mode/htmlmixed/htmlmixed.min.js"; dest="$baseDir\mode\htmlmixed\htmlmixed.min.js"},
    @{url="$cdnBase/mode/markdown/markdown.min.js"; dest="$baseDir\mode\markdown\markdown.min.js"}
)

# js-beautify library (fixed version 1.14.0)
$jsBeautifyFiles = @(
    @{url="https://cdnjs.cloudflare.com/ajax/libs/js-beautify/1.14.0/beautify.min.js"; dest="$baseDir\lib\beautify.min.js"},
    @{url="https://cdnjs.cloudflare.com/ajax/libs/js-beautify/1.14.0/beautify-css.min.js"; dest="$baseDir\lib\beautify-css.min.js"},
    @{url="https://cdnjs.cloudflare.com/ajax/libs/js-beautify/1.14.0/beautify-html.min.js"; dest="$baseDir\lib\beautify-html.min.js"}
)

$files += $jsBeautifyFiles

# Create backup directory
$backupDir = "$baseDir\backup\$((Get-Date).ToString('yyyyMMdd_HHmmss'))"
New-Item -ItemType Directory -Path $backupDir -Force | Out-Null
Write-Host "Created backup directory: $backupDir" -ForegroundColor Yellow

# Backup current files
foreach ($file in $files) {
    $destFile = $file.dest
    if (Test-Path $destFile) {
        $fileName = Split-Path $destFile -Leaf
        $destFolder = Split-Path $destFile -Parent
        $relativePath = $destFolder.Replace($baseDir, "").TrimStart("\")
        $backupFolder = Join-Path $backupDir $relativePath
        
        if (!(Test-Path $backupFolder)) {
            New-Item -ItemType Directory -Path $backupFolder -Force | Out-Null
        }
        
        Copy-Item $destFile -Destination (Join-Path $backupFolder $fileName) -Force
        Write-Host "Backed up file: $destFile" -ForegroundColor Gray
    }
}

# Download all files
$successCount = 0
$failCount = 0

foreach ($file in $files) {
    Write-Host "Downloading $($file.url)"
    try {
        Invoke-WebRequest -Uri $file.url -OutFile $file.dest -ErrorAction Stop
        Write-Host "  Success!" -ForegroundColor Green
        $successCount++
    } catch {
        Write-Host "  Failed: $_" -ForegroundColor Red
        $failCount++
    }
}

# Update version in README.md
$readmeFile = "$baseDir\README.md"
if (Test-Path $readmeFile) {
    # Use UTF-8 encoding without BOM
    $readmeContent = Get-Content $readmeFile -Raw -Encoding UTF8
    
    # Both English and Chinese patterns for compatibility
    $readmeContent = $readmeContent -replace "Current version is CodeMirror [0-9\.]+", "Current version is CodeMirror $Version"
    $readmeContent = $readmeContent -replace "当前版本是 CodeMirror [0-9\.]+", "当前版本是 CodeMirror $Version"
    
    # Use UTF-8 encoding without BOM when writing
    if ($PSVersionTable.PSVersion.Major -ge 6) {
        Set-Content -Path $readmeFile -Value $readmeContent -Encoding utf8NoBOM
    } else {
        $Utf8NoBomEncoding = New-Object System.Text.UTF8Encoding $False
        [System.IO.File]::WriteAllText($readmeFile, $readmeContent, $Utf8NoBomEncoding)
    }
    
    Write-Host "Updated version in README.md to $Version" -ForegroundColor Yellow
}

Write-Host "`nUpdate complete! Total: $($successCount + $failCount), Success: $successCount, Failed: $failCount" -ForegroundColor Cyan
if ($failCount -gt 0) {
    Write-Host "There were $failCount failed downloads. Check your internet connection or version number." -ForegroundColor Yellow
}

Write-Host "If needed, you can restore files from backup directory: $backupDir" -ForegroundColor Cyan 