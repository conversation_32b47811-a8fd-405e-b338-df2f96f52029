!function(t){"object"==typeof exports&&"object"==typeof module?t(require("../../lib/codemirror")):"function"==typeof define&&define.amd?define(["../../lib/codemirror"],t):t(CodeMirror)}(function(i){"use strict";var p,v,x=i.Pos;function m(t,e){for(var n,r,n=null!=(r=(n=t).flags)?r:(n.ignoreCase?"i":"")+(n.global?"g":"")+(n.multiline?"m":""),i=n,o=0;o<e.length;o++)-1==i.indexOf(e.charAt(o))&&(i+=e.charAt(o));return n==i?t:new RegExp(t.source,i)}function d(t){return/\\s|\\n|\n|\\W|\\D|\[\^/.test(t.source)}function a(t,e,n){e=m(e,"g");for(var r=n.line,i=n.ch,o=t.lastLine();r<=o;r++,i=0){e.lastIndex=i;var h=t.getLine(r),h=e.exec(h);if(h)return{from:x(r,h.index),to:x(r,h.index+h[0].length),match:h}}}function o(t,e,n){if(!d(e))return a(t,e,n);e=m(e,"gm");for(var r=1,i=n.line,o=t.lastLine();i<=o;){for(var h=0;h<r&&!(o<i);h++)var l=t.getLine(i++),c=null==c?l:c+"\n"+l;r*=2,e.lastIndex=n.ch;var s=e.exec(c);if(s){var f=c.slice(0,s.index).split("\n"),g=s[0].split("\n"),u=n.line+f.length-1,f=f[f.length-1].length;return{from:x(u,f),to:x(u+g.length-1,1==g.length?f+g[0].length:g[g.length-1].length),match:s}}}}function L(t,e,n){for(var r,i=0;i<=t.length;){e.lastIndex=i;var o=e.exec(t);if(!o)break;var h=o.index+o[0].length;if(h>t.length-n)break;(!r||h>r.index+r[0].length)&&(r=o),i=o.index+1}return r}function O(t,e,n){e=m(e,"g");for(var r=n.line,i=n.ch,o=t.firstLine();o<=r;r--,i=-1){var h=t.getLine(r),h=L(h,e,i<0?0:h.length-i);if(h)return{from:x(r,h.index),to:x(r,h.index+h[0].length),match:h}}}function h(t,e,n){if(!d(e))return O(t,e,n);e=m(e,"gm");for(var r=1,i=t.getLine(n.line).length-n.ch,o=n.line,h=t.firstLine();h<=o;){for(var l=0;l<r&&h<=o;l++)var c=t.getLine(o--),s=null==s?c:c+"\n"+s;r*=2;var f=L(s,e,i);if(f){var g=s.slice(0,f.index).split("\n"),u=f[0].split("\n"),a=o+g.length,g=g[g.length-1].length;return{from:x(a,g),to:x(a+u.length-1,1==u.length?g+u[0].length:u[u.length-1].length),match:f}}}}function y(t,e,n,r){if(t.length==e.length)return n;for(var i=0,o=n+Math.max(0,t.length-e.length);;){if(i==o)return i;var h=i+o>>1,l=r(t.slice(0,h)).length;if(l==n)return h;n<l?o=h:i=1+h}}function l(t,e,n,r){if(!e.length)return null;var i=r?p:v,o=i(e).split(/\r|\n\r?/);t:for(var h=n.line,l=n.ch,c=t.lastLine()+1-o.length;h<=c;h++,l=0){var s=t.getLine(h).slice(l),f=i(s);if(1==o.length){var g=f.indexOf(o[0]);if(-1!=g){n=y(s,f,g,i)+l;return{from:x(h,y(s,f,g,i)+l),to:x(h,y(s,f,g+o[0].length,i)+l)}}}else{var u=f.length-o[0].length;if(f.slice(u)==o[0]){for(var a=1;a<o.length-1;a++)if(i(t.getLine(h+a))!=o[a])continue t;var m=t.getLine(h+o.length-1),d=i(m),g=o[o.length-1];if(d.slice(0,g.length)==g)return{from:x(h,y(s,f,u,i)+l),to:x(h+o.length-1,y(m,d,g.length,i))}}}}}function c(t,e,n,r){if(!e.length)return null;var i=r?p:v,o=i(e).split(/\r|\n\r?/);t:for(var h=n.line,l=n.ch,c=t.firstLine()-1+o.length;c<=h;h--,l=-1){var s=t.getLine(h),f=i(s=-1<l?s.slice(0,l):s);if(1==o.length){var g=f.lastIndexOf(o[0]);if(-1!=g)return{from:x(h,y(s,f,g,i)),to:x(h,y(s,f,g+o[0].length,i))}}else{var u=o[o.length-1];if(f.slice(0,u.length)==u){for(var a=1,n=h-o.length+1;a<o.length-1;a++)if(i(t.getLine(n+a))!=o[a])continue t;var m=t.getLine(h+1-o.length),g=i(m);if(g.slice(g.length-o[0].length)==o[0])return{from:x(h+1-o.length,y(m,g,m.length-o[0].length,i)),to:x(h,y(s,f,u.length,i))}}}}}function r(n,r,t,e){var i;this.atOccurrence=!1,this.afterEmptyMatch=!1,this.doc=n,t=t?n.clipPos(t):x(0,0),this.pos={from:t,to:t},"object"==typeof e?i=e.caseFold:(i=e,e=null),"string"==typeof r?(null==i&&(i=!1),this.matches=function(t,e){return(t?c:l)(n,r,e,i)}):(r=m(r,"gm"),e&&!1===e.multiline?this.matches=function(t,e){return(t?O:a)(n,r,e)}:this.matches=function(t,e){return(t?h:o)(n,r,e)})}v=String.prototype.normalize?(p=function(t){return t.normalize("NFD").toLowerCase()},function(t){return t.normalize("NFD")}):(p=function(t){return t.toLowerCase()},function(t){return t}),r.prototype={findNext:function(){return this.find(!1)},findPrevious:function(){return this.find(!0)},find:function(t){var e=this.doc.clipPos(t?this.pos.from:this.pos.to);if(this.afterEmptyMatch&&this.atOccurrence&&(e=x(e.line,e.ch),t?(e.ch--,e.ch<0&&(e.line--,e.ch=(this.doc.getLine(e.line)||"").length)):(e.ch++,e.ch>(this.doc.getLine(e.line)||"").length&&(e.ch=0,e.line++)),0!=i.cmpPos(e,this.doc.clipPos(e))))return this.atOccurrence=!1;e=this.matches(t,e);if(this.afterEmptyMatch=e&&0==i.cmpPos(e.from,e.to),e)return this.pos=e,this.atOccurrence=!0,this.pos.match||!0;t=x(t?this.doc.firstLine():this.doc.lastLine()+1,0);return this.pos={from:t,to:t},this.atOccurrence=!1},from:function(){if(this.atOccurrence)return this.pos.from},to:function(){if(this.atOccurrence)return this.pos.to},replace:function(t,e){this.atOccurrence&&(t=i.splitLines(t),this.doc.replaceRange(t,this.pos.from,this.pos.to,e),this.pos.to=x(this.pos.from.line+t.length-1,t[t.length-1].length+(1==t.length?this.pos.from.ch:0)))}},i.defineExtension("getSearchCursor",function(t,e,n){return new r(this.doc,t,e,n)}),i.defineDocExtension("getSearchCursor",function(t,e,n){return new r(this,t,e,n)}),i.defineExtension("selectMatches",function(t,e){for(var n=[],r=this.getSearchCursor(t,this.getCursor("from"),e);r.findNext()&&!(0<i.cmpPos(r.to(),this.getCursor("to")));)n.push({anchor:r.from(),head:r.to()});n.length&&this.setSelections(n,0)})});