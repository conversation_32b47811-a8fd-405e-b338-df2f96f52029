<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8"/>
  <title>iconfont Demo</title>
  <link rel="shortcut icon" href="//img.alicdn.com/imgextra/i4/O1CN01Z5paLz1O0zuCC7osS_!!6000000001644-55-tps-83-82.svg" type="image/x-icon"/>
  <link rel="icon" type="image/svg+xml" href="//img.alicdn.com/imgextra/i4/O1CN01Z5paLz1O0zuCC7osS_!!6000000001644-55-tps-83-82.svg"/>
  <link rel="stylesheet" href="https://g.alicdn.com/thx/cube/1.3.2/cube.min.css">
  <link rel="stylesheet" href="demo.css">
  <link rel="stylesheet" href="iconfont.css">
  <script src="iconfont.js"></script>
  <!-- jQuery -->
  <script src="https://a1.alicdn.com/oss/uploads/2018/12/26/7bfddb60-08e8-11e9-9b04-53e73bb6408b.js"></script>
  <!-- 代码高亮 -->
  <script src="https://a1.alicdn.com/oss/uploads/2018/12/26/a3f714d0-08e6-11e9-8a15-ebf944d7534c.js"></script>
  <style>
    .main .logo {
      margin-top: 0;
      height: auto;
    }

    .main .logo a {
      display: flex;
      align-items: center;
    }

    .main .logo .sub-title {
      margin-left: 0.5em;
      font-size: 22px;
      color: #fff;
      background: linear-gradient(-45deg, #3967FF, #B500FE);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }
  </style>
</head>
<body>
  <div class="main">
    <h1 class="logo"><a href="https://www.iconfont.cn/" title="iconfont 首页" target="_blank">
      <img width="200" src="https://img.alicdn.com/imgextra/i3/O1CN01Mn65HV1FfSEzR6DKv_!!6000000000514-55-tps-228-59.svg">
      
    </a></h1>
    <div class="nav-tabs">
      <ul id="tabs" class="dib-box">
        <li class="dib active"><span>Unicode</span></li>
        <li class="dib"><span>Font class</span></li>
        <li class="dib"><span>Symbol</span></li>
      </ul>
      
      <a href="https://www.iconfont.cn/manage/index?manage_type=myprojects&projectId=4911096" target="_blank" class="nav-more">查看项目</a>
      
    </div>
    <div class="tab-container">
      <div class="content unicode" style="display: block;">
          <ul class="icon_lists dib-box">
          
            <li class="dib">
              <span class="icon iconfont">&#xe612;</span>
                <div class="name">历史价格</div>
                <div class="code-name">&amp;#xe612;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe697;</span>
                <div class="name">notification</div>
                <div class="code-name">&amp;#xe697;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe698;</span>
                <div class="name">minus</div>
                <div class="code-name">&amp;#xe698;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6a0;</span>
                <div class="name">task</div>
                <div class="code-name">&amp;#xe6a0;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6a6;</span>
                <div class="name">add-bold</div>
                <div class="code-name">&amp;#xe6a6;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6ee;</span>
                <div class="name">Arrow-up-outlined</div>
                <div class="code-name">&amp;#xe6ee;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe681;</span>
                <div class="name">眼睛</div>
                <div class="code-name">&amp;#xe681;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6ed;</span>
                <div class="name">Arrow-down-outlined</div>
                <div class="code-name">&amp;#xe6ed;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xeb60;</span>
                <div class="name">view-grid</div>
                <div class="code-name">&amp;#xeb60;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe66d;</span>
                <div class="name">view-list</div>
                <div class="code-name">&amp;#xe66d;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe611;</span>
                <div class="name">csv</div>
                <div class="code-name">&amp;#xe611;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6e9;</span>
                <div class="name">close</div>
                <div class="code-name">&amp;#xe6e9;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe664;</span>
                <div class="name">从列表选择</div>
                <div class="code-name">&amp;#xe664;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe639;</span>
                <div class="name">file</div>
                <div class="code-name">&amp;#xe639;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe63a;</span>
                <div class="name">exe</div>
                <div class="code-name">&amp;#xe63a;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe63b;</span>
                <div class="name">jpg</div>
                <div class="code-name">&amp;#xe63b;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe63c;</span>
                <div class="name">pdf</div>
                <div class="code-name">&amp;#xe63c;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe63d;</span>
                <div class="name">txt</div>
                <div class="code-name">&amp;#xe63d;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe63e;</span>
                <div class="name">zip</div>
                <div class="code-name">&amp;#xe63e;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe63f;</span>
                <div class="name">xml</div>
                <div class="code-name">&amp;#xe63f;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe640;</span>
                <div class="name">doc</div>
                <div class="code-name">&amp;#xe640;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe641;</span>
                <div class="name">html</div>
                <div class="code-name">&amp;#xe641;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe642;</span>
                <div class="name">js</div>
                <div class="code-name">&amp;#xe642;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe643;</span>
                <div class="name">mp4</div>
                <div class="code-name">&amp;#xe643;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe644;</span>
                <div class="name">json</div>
                <div class="code-name">&amp;#xe644;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe645;</span>
                <div class="name">ppt</div>
                <div class="code-name">&amp;#xe645;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe646;</span>
                <div class="name">xls</div>
                <div class="code-name">&amp;#xe646;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe647;</span>
                <div class="name">css</div>
                <div class="code-name">&amp;#xe647;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe648;</span>
                <div class="name">mp3</div>
                <div class="code-name">&amp;#xe648;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe649;</span>
                <div class="name">png</div>
                <div class="code-name">&amp;#xe649;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe64a;</span>
                <div class="name">svg</div>
                <div class="code-name">&amp;#xe64a;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe60f;</span>
                <div class="name">download</div>
                <div class="code-name">&amp;#xe60f;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe610;</span>
                <div class="name">contact</div>
                <div class="code-name">&amp;#xe610;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe61b;</span>
                <div class="name">标签-蓝</div>
                <div class="code-name">&amp;#xe61b;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe616;</span>
                <div class="name">打勾</div>
                <div class="code-name">&amp;#xe616;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe76e;</span>
                <div class="name">向上2</div>
                <div class="code-name">&amp;#xe76e;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe772;</span>
                <div class="name">向下2</div>
                <div class="code-name">&amp;#xe772;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe775;</span>
                <div class="name">向右1</div>
                <div class="code-name">&amp;#xe775;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe779;</span>
                <div class="name">向左1</div>
                <div class="code-name">&amp;#xe779;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe78e;</span>
                <div class="name">类目</div>
                <div class="code-name">&amp;#xe78e;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7ae;</span>
                <div class="name">国际物流</div>
                <div class="code-name">&amp;#xe7ae;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe613;</span>
                <div class="name">暂停</div>
                <div class="code-name">&amp;#xe613;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe628;</span>
                <div class="name">激活</div>
                <div class="code-name">&amp;#xe628;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe655;</span>
                <div class="name">折线图</div>
                <div class="code-name">&amp;#xe655;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe660;</span>
                <div class="name">取消置顶</div>
                <div class="code-name">&amp;#xe660;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe663;</span>
                <div class="name">置顶</div>
                <div class="code-name">&amp;#xe663;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe60b;</span>
                <div class="name">删 除</div>
                <div class="code-name">&amp;#xe60b;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe662;</span>
                <div class="name">三角形下拉</div>
                <div class="code-name">&amp;#xe662;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe626;</span>
                <div class="name">三角形向左</div>
                <div class="code-name">&amp;#xe626;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe60a;</span>
                <div class="name">邮件</div>
                <div class="code-name">&amp;#xe60a;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xea28;</span>
                <div class="name">打印,复印</div>
                <div class="code-name">&amp;#xea28;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe72a;</span>
                <div class="name">image</div>
                <div class="code-name">&amp;#xe72a;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6b4;</span>
                <div class="name">video</div>
                <div class="code-name">&amp;#xe6b4;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6d6;</span>
                <div class="name">KHCFDC_上架</div>
                <div class="code-name">&amp;#xe6d6;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6e0;</span>
                <div class="name">KHCFDC_下架</div>
                <div class="code-name">&amp;#xe6e0;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe71e;</span>
                <div class="name">排序-左右</div>
                <div class="code-name">&amp;#xe71e;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe669;</span>
                <div class="name">树状</div>
                <div class="code-name">&amp;#xe669;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7c6;</span>
                <div class="name">排序sort-降</div>
                <div class="code-name">&amp;#xe7c6;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7c7;</span>
                <div class="name">排序sort-升</div>
                <div class="code-name">&amp;#xe7c7;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe770;</span>
                <div class="name">排序_0</div>
                <div class="code-name">&amp;#xe770;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe609;</span>
                <div class="name">隐藏 (1)</div>
                <div class="code-name">&amp;#xe609;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe625;</span>
                <div class="name">隐藏</div>
                <div class="code-name">&amp;#xe625;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6d3;</span>
                <div class="name">编辑</div>
                <div class="code-name">&amp;#xe6d3;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe618;</span>
                <div class="name">复制</div>
                <div class="code-name">&amp;#xe618;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe600;</span>
                <div class="name">搜索</div>
                <div class="code-name">&amp;#xe600;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6a3;</span>
                <div class="name">发送</div>
                <div class="code-name">&amp;#xe6a3;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe73a;</span>
                <div class="name">更多</div>
                <div class="code-name">&amp;#xe73a;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe619;</span>
                <div class="name">详情</div>
                <div class="code-name">&amp;#xe619;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe735;</span>
                <div class="name">店铺-fill</div>
                <div class="code-name">&amp;#xe735;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe73d;</span>
                <div class="name">客户管理</div>
                <div class="code-name">&amp;#xe73d;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7ce;</span>
                <div class="name">g报表s</div>
                <div class="code-name">&amp;#xe7ce;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe608;</span>
                <div class="name">产品管理</div>
                <div class="code-name">&amp;#xe608;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe64f;</span>
                <div class="name">社区互动2</div>
                <div class="code-name">&amp;#xe64f;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe688;</span>
                <div class="name">应用</div>
                <div class="code-name">&amp;#xe688;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6a5;</span>
                <div class="name">设 置-树菜单设置</div>
                <div class="code-name">&amp;#xe6a5;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe605;</span>
                <div class="name">标签</div>
                <div class="code-name">&amp;#xe605;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe60e;</span>
                <div class="name">订单</div>
                <div class="code-name">&amp;#xe60e;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe607;</span>
                <div class="name">home</div>
                <div class="code-name">&amp;#xe607;</div>
              </li>
          
          </ul>
          <div class="article markdown">
          <h2 id="unicode-">Unicode 引用</h2>
          <hr>

          <p>Unicode 是字体在网页端最原始的应用方式，特点是：</p>
          <ul>
            <li>支持按字体的方式去动态调整图标大小，颜色等等。</li>
            <li>默认情况下不支持多色，直接添加多色图标会自动去色。</li>
          </ul>
          <blockquote>
            <p>注意：新版 iconfont 支持两种方式引用多色图标：SVG symbol 引用方式和彩色字体图标模式。（使用彩色字体图标需要在「编辑项目」中开启「彩色」选项后并重新生成。）</p>
          </blockquote>
          <p>Unicode 使用步骤如下：</p>
          <h3 id="-font-face">第一步：拷贝项目下面生成的 <code>@font-face</code></h3>
<pre><code class="language-css"
>@font-face {
  font-family: 'iconfont';
  src: url('iconfont.woff2?t=1751882985719') format('woff2'),
       url('iconfont.woff?t=1751882985719') format('woff'),
       url('iconfont.ttf?t=1751882985719') format('truetype');
}
</code></pre>
          <h3 id="-iconfont-">第二步：定义使用 iconfont 的样式</h3>
<pre><code class="language-css"
>.iconfont {
  font-family: "iconfont" !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
</code></pre>
          <h3 id="-">第三步：挑选相应图标并获取字体编码，应用于页面</h3>
<pre>
<code class="language-html"
>&lt;span class="iconfont"&gt;&amp;#x33;&lt;/span&gt;
</code></pre>
          <blockquote>
            <p>"iconfont" 是你项目下的 font-family。可以通过编辑项目查看，默认是 "iconfont"。</p>
          </blockquote>
          </div>
      </div>
      <div class="content font-class">
        <ul class="icon_lists dib-box">
          
          <li class="dib">
            <span class="icon iconfont icon_menu_lishijiage"></span>
            <div class="name">
              历史价格
            </div>
            <div class="code-name">.icon_menu_lishijiage
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon_menu_notification"></span>
            <div class="name">
              notification
            </div>
            <div class="code-name">.icon_menu_notification
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon_menu_minus"></span>
            <div class="name">
              minus
            </div>
            <div class="code-name">.icon_menu_minus
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon_menu_task"></span>
            <div class="name">
              task
            </div>
            <div class="code-name">.icon_menu_task
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon_menu_add-bold"></span>
            <div class="name">
              add-bold
            </div>
            <div class="code-name">.icon_menu_add-bold
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon_menu_ArrowUp"></span>
            <div class="name">
              Arrow-up-outlined
            </div>
            <div class="code-name">.icon_menu_ArrowUp
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon_menu_eyes"></span>
            <div class="name">
              眼睛
            </div>
            <div class="code-name">.icon_menu_eyes
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon_menu_ArrowDown"></span>
            <div class="name">
              Arrow-down-outlined
            </div>
            <div class="code-name">.icon_menu_ArrowDown
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon_menu_viewgrid"></span>
            <div class="name">
              view-grid
            </div>
            <div class="code-name">.icon_menu_viewgrid
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon_menu_viewlist"></span>
            <div class="name">
              view-list
            </div>
            <div class="code-name">.icon_menu_viewlist
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon_menu_csv"></span>
            <div class="name">
              csv
            </div>
            <div class="code-name">.icon_menu_csv
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon_menu_close"></span>
            <div class="name">
              close
            </div>
            <div class="code-name">.icon_menu_close
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon_menu_listselect"></span>
            <div class="name">
              从列表选择
            </div>
            <div class="code-name">.icon_menu_listselect
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon_menu_file"></span>
            <div class="name">
              file
            </div>
            <div class="code-name">.icon_menu_file
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon_menu_exe"></span>
            <div class="name">
              exe
            </div>
            <div class="code-name">.icon_menu_exe
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon_menu_jpg"></span>
            <div class="name">
              jpg
            </div>
            <div class="code-name">.icon_menu_jpg
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon_menu_pdf"></span>
            <div class="name">
              pdf
            </div>
            <div class="code-name">.icon_menu_pdf
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon_menu_txt"></span>
            <div class="name">
              txt
            </div>
            <div class="code-name">.icon_menu_txt
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon_menu_zip"></span>
            <div class="name">
              zip
            </div>
            <div class="code-name">.icon_menu_zip
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon_menu_xml"></span>
            <div class="name">
              xml
            </div>
            <div class="code-name">.icon_menu_xml
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon_menu_doc"></span>
            <div class="name">
              doc
            </div>
            <div class="code-name">.icon_menu_doc
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon_menu_html"></span>
            <div class="name">
              html
            </div>
            <div class="code-name">.icon_menu_html
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon_menu_js"></span>
            <div class="name">
              js
            </div>
            <div class="code-name">.icon_menu_js
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon_menu_mp4"></span>
            <div class="name">
              mp4
            </div>
            <div class="code-name">.icon_menu_mp4
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon_menu_json"></span>
            <div class="name">
              json
            </div>
            <div class="code-name">.icon_menu_json
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon_menu_ppt"></span>
            <div class="name">
              ppt
            </div>
            <div class="code-name">.icon_menu_ppt
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon_menu_xls"></span>
            <div class="name">
              xls
            </div>
            <div class="code-name">.icon_menu_xls
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon_menu_css"></span>
            <div class="name">
              css
            </div>
            <div class="code-name">.icon_menu_css
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon_menu_mp3"></span>
            <div class="name">
              mp3
            </div>
            <div class="code-name">.icon_menu_mp3
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon_menu_png"></span>
            <div class="name">
              png
            </div>
            <div class="code-name">.icon_menu_png
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon_menu_svg"></span>
            <div class="name">
              svg
            </div>
            <div class="code-name">.icon_menu_svg
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon_menu_download"></span>
            <div class="name">
              download
            </div>
            <div class="code-name">.icon_menu_download
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon_menu_contact"></span>
            <div class="name">
              contact
            </div>
            <div class="code-name">.icon_menu_contact
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon_menu_tag"></span>
            <div class="name">
              标签-蓝
            </div>
            <div class="code-name">.icon_menu_tag
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon_menu_right"></span>
            <div class="name">
              打勾
            </div>
            <div class="code-name">.icon_menu_right
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon_menu_uparrow"></span>
            <div class="name">
              向上2
            </div>
            <div class="code-name">.icon_menu_uparrow
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon_menu_downarrow"></span>
            <div class="name">
              向下2
            </div>
            <div class="code-name">.icon_menu_downarrow
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon_menu_rightarrow"></span>
            <div class="name">
              向右1
            </div>
            <div class="code-name">.icon_menu_rightarrow
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon_menu_leftarrow"></span>
            <div class="name">
              向左1
            </div>
            <div class="code-name">.icon_menu_leftarrow
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon_menu_list"></span>
            <div class="name">
              类目
            </div>
            <div class="code-name">.icon_menu_list
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon_menu_logistic"></span>
            <div class="name">
              国际物流
            </div>
            <div class="code-name">.icon_menu_logistic
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon_menu_pause"></span>
            <div class="name">
              暂停
            </div>
            <div class="code-name">.icon_menu_pause
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon_menu_active"></span>
            <div class="name">
              激活
            </div>
            <div class="code-name">.icon_menu_active
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon_menu_linechart"></span>
            <div class="name">
              折线图
            </div>
            <div class="code-name">.icon_menu_linechart
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon_menu_cancelstick"></span>
            <div class="name">
              取消置顶
            </div>
            <div class="code-name">.icon_menu_cancelstick
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon_menu_stick"></span>
            <div class="name">
              置顶
            </div>
            <div class="code-name">.icon_menu_stick
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon_menu_delete"></span>
            <div class="name">
              删 除
            </div>
            <div class="code-name">.icon_menu_delete
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon_menu_triangledown"></span>
            <div class="name">
              三角形下拉
            </div>
            <div class="code-name">.icon_menu_triangledown
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon_menu_triangleleft"></span>
            <div class="name">
              三角形向左
            </div>
            <div class="code-name">.icon_menu_triangleleft
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon_menu_email"></span>
            <div class="name">
              邮件
            </div>
            <div class="code-name">.icon_menu_email
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon_menu_print"></span>
            <div class="name">
              打印,复印
            </div>
            <div class="code-name">.icon_menu_print
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon_menu_image"></span>
            <div class="name">
              image
            </div>
            <div class="code-name">.icon_menu_image
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon_menu_video"></span>
            <div class="name">
              video
            </div>
            <div class="code-name">.icon_menu_video
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon_menu_listing"></span>
            <div class="name">
              KHCFDC_上架
            </div>
            <div class="code-name">.icon_menu_listing
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon_menu_takedown"></span>
            <div class="name">
              KHCFDC_下架
            </div>
            <div class="code-name">.icon_menu_takedown
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon_menu_hsort"></span>
            <div class="name">
              排序-左右
            </div>
            <div class="code-name">.icon_menu_hsort
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon_menu_tree"></span>
            <div class="name">
              树状
            </div>
            <div class="code-name">.icon_menu_tree
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon_menu_sortdown"></span>
            <div class="name">
              排序sort-降
            </div>
            <div class="code-name">.icon_menu_sortdown
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon_menu_sortup"></span>
            <div class="name">
              排序sort-升
            </div>
            <div class="code-name">.icon_menu_sortup
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon_menu_updown"></span>
            <div class="name">
              排序_0
            </div>
            <div class="code-name">.icon_menu_updown
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon_menu_eye"></span>
            <div class="name">
              隐藏 (1)
            </div>
            <div class="code-name">.icon_menu_eye
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon_menu_hide"></span>
            <div class="name">
              隐藏
            </div>
            <div class="code-name">.icon_menu_hide
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon_menu_edit"></span>
            <div class="name">
              编辑
            </div>
            <div class="code-name">.icon_menu_edit
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon_menu_copy"></span>
            <div class="name">
              复制
            </div>
            <div class="code-name">.icon_menu_copy
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon_menu_search"></span>
            <div class="name">
              搜索
            </div>
            <div class="code-name">.icon_menu_search
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon_menu_send"></span>
            <div class="name">
              发送
            </div>
            <div class="code-name">.icon_menu_send
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon_menu_more"></span>
            <div class="name">
              更多
            </div>
            <div class="code-name">.icon_menu_more
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon_menu_detail"></span>
            <div class="name">
              详情
            </div>
            <div class="code-name">.icon_menu_detail
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon_menu_view"></span>
            <div class="name">
              店铺-fill
            </div>
            <div class="code-name">.icon_menu_view
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon_menu_user"></span>
            <div class="name">
              客户管理
            </div>
            <div class="code-name">.icon_menu_user
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon_menu_mta"></span>
            <div class="name">
              g报表s
            </div>
            <div class="code-name">.icon_menu_mta
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon_menu_products"></span>
            <div class="name">
              产品管理
            </div>
            <div class="code-name">.icon_menu_products
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon_menu_interact"></span>
            <div class="name">
              社区互动2
            </div>
            <div class="code-name">.icon_menu_interact
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon_menu_plugins"></span>
            <div class="name">
              应用
            </div>
            <div class="code-name">.icon_menu_plugins
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon_menu_set"></span>
            <div class="name">
              设 置-树菜单设置
            </div>
            <div class="code-name">.icon_menu_set
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon_menu_sales"></span>
            <div class="name">
              标签
            </div>
            <div class="code-name">.icon_menu_sales
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon_menu_orders"></span>
            <div class="name">
              订单
            </div>
            <div class="code-name">.icon_menu_orders
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon_menu_index"></span>
            <div class="name">
              home
            </div>
            <div class="code-name">.icon_menu_index
            </div>
          </li>
          
        </ul>
        <div class="article markdown">
        <h2 id="font-class-">font-class 引用</h2>
        <hr>

        <p>font-class 是 Unicode 使用方式的一种变种，主要是解决 Unicode 书写不直观，语意不明确的问题。</p>
        <p>与 Unicode 使用方式相比，具有如下特点：</p>
        <ul>
          <li>相比于 Unicode 语意明确，书写更直观。可以很容易分辨这个 icon 是什么。</li>
          <li>因为使用 class 来定义图标，所以当要替换图标时，只需要修改 class 里面的 Unicode 引用。</li>
        </ul>
        <p>使用步骤如下：</p>
        <h3 id="-fontclass-">第一步：引入项目下面生成的 fontclass 代码：</h3>
<pre><code class="language-html">&lt;link rel="stylesheet" href="./iconfont.css"&gt;
</code></pre>
        <h3 id="-">第二步：挑选相应图标并获取类名，应用于页面：</h3>
<pre><code class="language-html">&lt;span class="iconfont icon_menu_xxx"&gt;&lt;/span&gt;
</code></pre>
        <blockquote>
          <p>"
            iconfont" 是你项目下的 font-family。可以通过编辑项目查看，默认是 "iconfont"。</p>
        </blockquote>
      </div>
      </div>
      <div class="content symbol">
          <ul class="icon_lists dib-box">
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon_menu_lishijiage"></use>
                </svg>
                <div class="name">历史价格</div>
                <div class="code-name">#icon_menu_lishijiage</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon_menu_notification"></use>
                </svg>
                <div class="name">notification</div>
                <div class="code-name">#icon_menu_notification</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon_menu_minus"></use>
                </svg>
                <div class="name">minus</div>
                <div class="code-name">#icon_menu_minus</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon_menu_task"></use>
                </svg>
                <div class="name">task</div>
                <div class="code-name">#icon_menu_task</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon_menu_add-bold"></use>
                </svg>
                <div class="name">add-bold</div>
                <div class="code-name">#icon_menu_add-bold</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon_menu_ArrowUp"></use>
                </svg>
                <div class="name">Arrow-up-outlined</div>
                <div class="code-name">#icon_menu_ArrowUp</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon_menu_eyes"></use>
                </svg>
                <div class="name">眼睛</div>
                <div class="code-name">#icon_menu_eyes</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon_menu_ArrowDown"></use>
                </svg>
                <div class="name">Arrow-down-outlined</div>
                <div class="code-name">#icon_menu_ArrowDown</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon_menu_viewgrid"></use>
                </svg>
                <div class="name">view-grid</div>
                <div class="code-name">#icon_menu_viewgrid</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon_menu_viewlist"></use>
                </svg>
                <div class="name">view-list</div>
                <div class="code-name">#icon_menu_viewlist</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon_menu_csv"></use>
                </svg>
                <div class="name">csv</div>
                <div class="code-name">#icon_menu_csv</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon_menu_close"></use>
                </svg>
                <div class="name">close</div>
                <div class="code-name">#icon_menu_close</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon_menu_listselect"></use>
                </svg>
                <div class="name">从列表选择</div>
                <div class="code-name">#icon_menu_listselect</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon_menu_file"></use>
                </svg>
                <div class="name">file</div>
                <div class="code-name">#icon_menu_file</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon_menu_exe"></use>
                </svg>
                <div class="name">exe</div>
                <div class="code-name">#icon_menu_exe</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon_menu_jpg"></use>
                </svg>
                <div class="name">jpg</div>
                <div class="code-name">#icon_menu_jpg</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon_menu_pdf"></use>
                </svg>
                <div class="name">pdf</div>
                <div class="code-name">#icon_menu_pdf</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon_menu_txt"></use>
                </svg>
                <div class="name">txt</div>
                <div class="code-name">#icon_menu_txt</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon_menu_zip"></use>
                </svg>
                <div class="name">zip</div>
                <div class="code-name">#icon_menu_zip</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon_menu_xml"></use>
                </svg>
                <div class="name">xml</div>
                <div class="code-name">#icon_menu_xml</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon_menu_doc"></use>
                </svg>
                <div class="name">doc</div>
                <div class="code-name">#icon_menu_doc</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon_menu_html"></use>
                </svg>
                <div class="name">html</div>
                <div class="code-name">#icon_menu_html</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon_menu_js"></use>
                </svg>
                <div class="name">js</div>
                <div class="code-name">#icon_menu_js</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon_menu_mp4"></use>
                </svg>
                <div class="name">mp4</div>
                <div class="code-name">#icon_menu_mp4</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon_menu_json"></use>
                </svg>
                <div class="name">json</div>
                <div class="code-name">#icon_menu_json</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon_menu_ppt"></use>
                </svg>
                <div class="name">ppt</div>
                <div class="code-name">#icon_menu_ppt</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon_menu_xls"></use>
                </svg>
                <div class="name">xls</div>
                <div class="code-name">#icon_menu_xls</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon_menu_css"></use>
                </svg>
                <div class="name">css</div>
                <div class="code-name">#icon_menu_css</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon_menu_mp3"></use>
                </svg>
                <div class="name">mp3</div>
                <div class="code-name">#icon_menu_mp3</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon_menu_png"></use>
                </svg>
                <div class="name">png</div>
                <div class="code-name">#icon_menu_png</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon_menu_svg"></use>
                </svg>
                <div class="name">svg</div>
                <div class="code-name">#icon_menu_svg</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon_menu_download"></use>
                </svg>
                <div class="name">download</div>
                <div class="code-name">#icon_menu_download</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon_menu_contact"></use>
                </svg>
                <div class="name">contact</div>
                <div class="code-name">#icon_menu_contact</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon_menu_tag"></use>
                </svg>
                <div class="name">标签-蓝</div>
                <div class="code-name">#icon_menu_tag</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon_menu_right"></use>
                </svg>
                <div class="name">打勾</div>
                <div class="code-name">#icon_menu_right</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon_menu_uparrow"></use>
                </svg>
                <div class="name">向上2</div>
                <div class="code-name">#icon_menu_uparrow</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon_menu_downarrow"></use>
                </svg>
                <div class="name">向下2</div>
                <div class="code-name">#icon_menu_downarrow</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon_menu_rightarrow"></use>
                </svg>
                <div class="name">向右1</div>
                <div class="code-name">#icon_menu_rightarrow</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon_menu_leftarrow"></use>
                </svg>
                <div class="name">向左1</div>
                <div class="code-name">#icon_menu_leftarrow</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon_menu_list"></use>
                </svg>
                <div class="name">类目</div>
                <div class="code-name">#icon_menu_list</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon_menu_logistic"></use>
                </svg>
                <div class="name">国际物流</div>
                <div class="code-name">#icon_menu_logistic</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon_menu_pause"></use>
                </svg>
                <div class="name">暂停</div>
                <div class="code-name">#icon_menu_pause</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon_menu_active"></use>
                </svg>
                <div class="name">激活</div>
                <div class="code-name">#icon_menu_active</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon_menu_linechart"></use>
                </svg>
                <div class="name">折线图</div>
                <div class="code-name">#icon_menu_linechart</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon_menu_cancelstick"></use>
                </svg>
                <div class="name">取消置顶</div>
                <div class="code-name">#icon_menu_cancelstick</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon_menu_stick"></use>
                </svg>
                <div class="name">置顶</div>
                <div class="code-name">#icon_menu_stick</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon_menu_delete"></use>
                </svg>
                <div class="name">删 除</div>
                <div class="code-name">#icon_menu_delete</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon_menu_triangledown"></use>
                </svg>
                <div class="name">三角形下拉</div>
                <div class="code-name">#icon_menu_triangledown</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon_menu_triangleleft"></use>
                </svg>
                <div class="name">三角形向左</div>
                <div class="code-name">#icon_menu_triangleleft</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon_menu_email"></use>
                </svg>
                <div class="name">邮件</div>
                <div class="code-name">#icon_menu_email</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon_menu_print"></use>
                </svg>
                <div class="name">打印,复印</div>
                <div class="code-name">#icon_menu_print</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon_menu_image"></use>
                </svg>
                <div class="name">image</div>
                <div class="code-name">#icon_menu_image</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon_menu_video"></use>
                </svg>
                <div class="name">video</div>
                <div class="code-name">#icon_menu_video</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon_menu_listing"></use>
                </svg>
                <div class="name">KHCFDC_上架</div>
                <div class="code-name">#icon_menu_listing</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon_menu_takedown"></use>
                </svg>
                <div class="name">KHCFDC_下架</div>
                <div class="code-name">#icon_menu_takedown</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon_menu_hsort"></use>
                </svg>
                <div class="name">排序-左右</div>
                <div class="code-name">#icon_menu_hsort</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon_menu_tree"></use>
                </svg>
                <div class="name">树状</div>
                <div class="code-name">#icon_menu_tree</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon_menu_sortdown"></use>
                </svg>
                <div class="name">排序sort-降</div>
                <div class="code-name">#icon_menu_sortdown</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon_menu_sortup"></use>
                </svg>
                <div class="name">排序sort-升</div>
                <div class="code-name">#icon_menu_sortup</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon_menu_updown"></use>
                </svg>
                <div class="name">排序_0</div>
                <div class="code-name">#icon_menu_updown</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon_menu_eye"></use>
                </svg>
                <div class="name">隐藏 (1)</div>
                <div class="code-name">#icon_menu_eye</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon_menu_hide"></use>
                </svg>
                <div class="name">隐藏</div>
                <div class="code-name">#icon_menu_hide</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon_menu_edit"></use>
                </svg>
                <div class="name">编辑</div>
                <div class="code-name">#icon_menu_edit</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon_menu_copy"></use>
                </svg>
                <div class="name">复制</div>
                <div class="code-name">#icon_menu_copy</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon_menu_search"></use>
                </svg>
                <div class="name">搜索</div>
                <div class="code-name">#icon_menu_search</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon_menu_send"></use>
                </svg>
                <div class="name">发送</div>
                <div class="code-name">#icon_menu_send</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon_menu_more"></use>
                </svg>
                <div class="name">更多</div>
                <div class="code-name">#icon_menu_more</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon_menu_detail"></use>
                </svg>
                <div class="name">详情</div>
                <div class="code-name">#icon_menu_detail</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon_menu_view"></use>
                </svg>
                <div class="name">店铺-fill</div>
                <div class="code-name">#icon_menu_view</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon_menu_user"></use>
                </svg>
                <div class="name">客户管理</div>
                <div class="code-name">#icon_menu_user</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon_menu_mta"></use>
                </svg>
                <div class="name">g报表s</div>
                <div class="code-name">#icon_menu_mta</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon_menu_products"></use>
                </svg>
                <div class="name">产品管理</div>
                <div class="code-name">#icon_menu_products</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon_menu_interact"></use>
                </svg>
                <div class="name">社区互动2</div>
                <div class="code-name">#icon_menu_interact</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon_menu_plugins"></use>
                </svg>
                <div class="name">应用</div>
                <div class="code-name">#icon_menu_plugins</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon_menu_set"></use>
                </svg>
                <div class="name">设 置-树菜单设置</div>
                <div class="code-name">#icon_menu_set</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon_menu_sales"></use>
                </svg>
                <div class="name">标签</div>
                <div class="code-name">#icon_menu_sales</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon_menu_orders"></use>
                </svg>
                <div class="name">订单</div>
                <div class="code-name">#icon_menu_orders</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon_menu_index"></use>
                </svg>
                <div class="name">home</div>
                <div class="code-name">#icon_menu_index</div>
            </li>
          
          </ul>
          <div class="article markdown">
          <h2 id="symbol-">Symbol 引用</h2>
          <hr>

          <p>这是一种全新的使用方式，应该说这才是未来的主流，也是平台目前推荐的用法。相关介绍可以参考这篇<a href="">文章</a>
            这种用法其实是做了一个 SVG 的集合，与另外两种相比具有如下特点：</p>
          <ul>
            <li>支持多色图标了，不再受单色限制。</li>
            <li>通过一些技巧，支持像字体那样，通过 <code>font-size</code>, <code>color</code> 来调整样式。</li>
            <li>兼容性较差，支持 IE9+，及现代浏览器。</li>
            <li>浏览器渲染 SVG 的性能一般，还不如 png。</li>
          </ul>
          <p>使用步骤如下：</p>
          <h3 id="-symbol-">第一步：引入项目下面生成的 symbol 代码：</h3>
<pre><code class="language-html">&lt;script src="./iconfont.js"&gt;&lt;/script&gt;
</code></pre>
          <h3 id="-css-">第二步：加入通用 CSS 代码（引入一次就行）：</h3>
<pre><code class="language-html">&lt;style&gt;
.icon {
  width: 1em;
  height: 1em;
  vertical-align: -0.15em;
  fill: currentColor;
  overflow: hidden;
}
&lt;/style&gt;
</code></pre>
          <h3 id="-">第三步：挑选相应图标并获取类名，应用于页面：</h3>
<pre><code class="language-html">&lt;svg class="icon" aria-hidden="true"&gt;
  &lt;use xlink:href="#icon-xxx"&gt;&lt;/use&gt;
&lt;/svg&gt;
</code></pre>
          </div>
      </div>

    </div>
  </div>
  <script>
  $(document).ready(function () {
      $('.tab-container .content:first').show()

      $('#tabs li').click(function (e) {
        var tabContent = $('.tab-container .content')
        var index = $(this).index()

        if ($(this).hasClass('active')) {
          return
        } else {
          $('#tabs li').removeClass('active')
          $(this).addClass('active')

          tabContent.hide().eq(index).fadeIn()
        }
      })
    })
  </script>
</body>
</html>
