/**
 * 图片选择表单处理器
 * 处理图片选择弹窗表单提交
 */

var PhotoFormHandler = {
    /**
     * 初始化
     */
    init: function() {
        $(document).ready(function() {
            PhotoFormHandler.setupFormHandlers();
        });
    },
    
    /**
     * 设置表单处理器
     */
    setupFormHandlers: function() {
        var self = this;
        
        // 监听表单提交事件
        $(document).on('submit', '#photo_list_form', function(e) {
            self.handleFormSubmit(this);
        });
        
        // 监听确定按钮点击事件
        $(document).on('click', '.btn_ok, .btn_submit, [type="submit"]', function(e) {
            var $form = $(this).closest('form');
            if ($form.attr('id') === 'photo_list_form') {
                self.handleFormSubmit($form[0]);
            }
        });
        
        // 监听文件上传完成事件
        $(document).on('fileuploaddone', function(e, data) {
            // 如果是编辑器模式，通过协调器处理
            if (window.isEditorImageChoice && window.ImageUploadCoordinator) {
                window.ImageUploadCoordinator.handleUpload(e, data);
            } else {
                // 否则使用原有逻辑
                self.handleFileUploadDone(data);
            }
        });
        
        // 监听jQuery File Upload的done事件
        if (typeof $.fn.fileupload !== 'undefined') {
            $('.fileupload-buttonbar').fileupload({
                done: function(e, data) {
                    // 如果是编辑器模式，通过协调器处理
                    if (window.isEditorImageChoice && window.ImageUploadCoordinator) {
                        window.ImageUploadCoordinator.handleUpload(e, data);
                    } else {
                        // 否则使用原有逻辑
                        self.handleFileUploadDone(data);
                    }
                }
            });
        }
    },
    
    /**
     * 处理文件上传完成
     */
    handleFileUploadDone: function(data) {
        if (data.result && data.result.filePath) {
            // 延迟处理，确保DOM更新完成
            var self = this;
            setTimeout(function() {
                self.ensurePicPathEnabled();
            }, 500);
        }
    },
    
    /**
     * 确保PicPath字段启用
     */
    ensurePicPathEnabled: function() {
        var $picPathInputs = $('input[name="PicPath[]"]');
        
        $picPathInputs.each(function() {
            var $input = $(this);
            var path = $input.val();
            
            if (path && path.trim() !== '') {
                $input.removeAttr('disabled');
                
                // 确保字段可见
                var $container = $input.closest('.upload_item, .photo_item');
                if ($container.length > 0) {
                    $container.show();
                }
            }
        });
    },
    
    /**
     * 处理表单提交
     */
    handleFormSubmit: function(form) {
        var $form = $(form);
        
        // 确保所有有值的PicPath字段都被启用
        $form.find('input[name="PicPath[]"]').each(function() {
            var $input = $(this);
            var path = $input.val();
            
            if (path && path.trim() !== '') {
                $input.removeAttr('disabled');
            }
        });
        
        // 如果是编辑器模式，处理特殊逻辑
        if (window.isEditorImageChoice) {
            this.handleEditorFormSubmit($form);
        }
    },
    
    /**
     * 处理编辑器表单提交
     */
    handleEditorFormSubmit: function($form) {
        // 获取选中的图片路径
        var selectedImages = [];
        $form.find('input[name="PicPath[]"]:not([disabled])').each(function() {
            var path = $(this).val();
            if (path && path.trim() !== '') {
                selectedImages.push(path);
            }
        });
        
        // 如果有选中的图片，插入到编辑器
        if (selectedImages.length > 0) {
            var imagePath = selectedImages[0]; // 只取第一张图片
            
            // 通过协调器处理
            if (window.ImageUploadCoordinator) {
                window.ImageUploadCoordinator.handleEditorImageUpload(imagePath);
            }
        }
    },
    
    /**
     * 验证表单
     */
    validateForm: function($form) {
        var hasValidImages = false;
        
        $form.find('input[name="PicPath[]"]').each(function() {
            var path = $(this).val();
            if (path && path.trim() !== '') {
                hasValidImages = true;
                return false; // 跳出循环
            }
        });
        
        return hasValidImages;
    },
    
    /**
     * 显示错误消息
     */
    showError: function(message) {
        // 可以在这里添加错误显示逻辑
        console.error('PhotoFormHandler Error:', message);
    },
    
    /**
     * 显示成功消息
     */
    showSuccess: function(message) {
        // 可以在这里添加成功显示逻辑
        console.log('PhotoFormHandler Success:', message);
    }
};

// 自动初始化
PhotoFormHandler.init();
