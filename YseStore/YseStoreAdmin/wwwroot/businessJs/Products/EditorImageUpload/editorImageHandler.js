/**
 * 富文本编辑器图片处理器
 * 处理图片选择对话框中的图片上传和插入
 */

var EditorImageHandler = {
    
    /**
     * 初始化
     */
    init: function() {
        $(document).ready(function() {
            EditorImageHandler.setupEditorImageHandling();
            EditorImageHandler.monitorFileUpload();
            EditorImageHandler.enhanceFormSubmission();
        });
    },
    
    /**
     * 设置编辑器图片处理
     */
    setupEditorImageHandling: function() {
        // 检查是否在图片选择对话框中
        if (window.location.href.indexOf('/manage/set/photo/choice') === -1) {
            return;
        }
        
        // 获取URL参数
        var urlParams = new URLSearchParams(window.location.search);
        var type = urlParams.get('type');
        var id = urlParams.get('id');
        
        // 如果是编辑器类型，设置特殊处理
        if (type === 'editor') {
            window.isEditorImageChoice = true;
            window.editorTargetId = id;
            
            // 存储到父窗口
            if (window.parent && window.parent !== window) {
                window.parent.currentEditorId = id;
            }
        }
    },
    
    /**
     * 监听文件上传
     */
    monitorFileUpload: function() {
        var self = this;

        // 监听jQuery File Upload的done事件
        $(document).on('fileuploaddone', function(e, data) {
            if (window.isEditorImageChoice && data.result) {
                // 通过协调器处理
                if (window.ImageUploadCoordinator) {
                    window.ImageUploadCoordinator.handleUpload(e, data);
                } else {
                    // 备用处理
                    self.handleEditorImageUpload(data.result);
                }
            }
        });
        
        // 重写jQuery File Upload的done回调
        if (typeof $.fn.fileupload !== 'undefined') {
            $(document).on('fileuploadcreate', function(e) {
                var $widget = $(e.target);

                $widget.bind('fileuploaddone', function(e, data) {
                    if (!data) {
                        return;
                    }

                    if (!data.files) {
                        data.files = [];
                    }

                    if (window.isEditorImageChoice && data.result) {
                        // 通过协调器处理
                        if (window.ImageUploadCoordinator) {
                            window.ImageUploadCoordinator.handleUpload(e, data);
                        } else {
                            // 备用处理
                            self.handleEditorImageUpload(data.result);
                        }
                    }
                });

                $widget.bind('fileuploadprocessalways', function(e, data) {
                    if (data && !data.files) {
                        data.files = [];
                    }
                });
            });
        }
    },

    /**
     * 提取图片路径
     */
    extractImagePath: function(result) {
        if (result.filePath) {
            return result.filePath;
        } else if (result.files && result.files.length > 0 && result.files[0].url) {
            return result.files[0].url;
        } else if (result.url) {
            return result.url;
        } else if (typeof result === 'string') {
            return result;
        }
        return null;
    },

    /**
     * 处理编辑器图片上传
     */
    handleEditorImageUpload: function(result) {
        var imagePath = this.extractImagePath(result);
        
        if (imagePath) {
            // 启用对应的PicPath字段
            this.enablePicPathField(imagePath);
            
            // 延迟处理，确保DOM更新完成
            var self = this;
            setTimeout(function() {
                self.insertImageToEditor(imagePath);
            }, 500);
        }
    },
    
    /**
     * 启用PicPath字段
     */
    enablePicPathField: function(imagePath) {
        $('input[name="PicPath[]"]').each(function() {
            var $input = $(this);
            if ($input.val() === imagePath) {
                $input.removeAttr('disabled');
            }
        });
    },
    
    /**
     * 插入图片到编辑器
     */
    insertImageToEditor: function(imagePath) {
        // 方法1: 通过父窗口的TinymceImageUploadFix插入
        if (window.parent && window.parent !== window) {
            var parentWindow = window.parent;
            
            if (parentWindow.TinymceImageUploadFix && parentWindow.TinymceImageUploadFix.insertImageToActiveEditor) {
                var success = parentWindow.TinymceImageUploadFix.insertImageToActiveEditor(imagePath);
                if (success) {
                    this.closeDialog();
                    return;
                }
            }
            
            // 方法2: 直接通过父窗口的tinymce编辑器插入
            if (parentWindow.tinymce && window.editorTargetId) {
                var editor = parentWindow.tinymce.editors[window.editorTargetId];
                if (editor && editor.initialized) {
                    try {
                        editor.focus();
                        var imageHtml = '<img src="' + imagePath + '" alt="" style="max-width: 100%;" />';
                        editor.insertContent(imageHtml);
                        this.closeDialog();
                        return;
                    } catch (e) {
                        // 继续尝试其他方法
                    }
                }
            }
        }
    },
    
    /**
     * 关闭对话框
     */
    closeDialog: function() {
        setTimeout(function() {
            if (window.parent && window.parent.layer) {
                window.parent.layer.closeAll();
            } else if (window.layer) {
                window.layer.closeAll();
            }
        }, 300);
    },
    
    /**
     * 增强表单提交
     */
    enhanceFormSubmission: function() {
        // 监听确定按钮点击
        $(document).on('click', '.btn_ok, .btn_submit, [type="submit"]', function(e) {
            var $form = $(this).closest('form');
            if ($form.attr('id') === 'photo_list_form' && window.isEditorImageChoice) {
                // 确保所有有值的PicPath字段都被启用
                $form.find('input[name="PicPath[]"]').each(function() {
                    var $input = $(this);
                    var path = $input.val();
                    
                    if (path && path.trim() !== '') {
                        $input.removeAttr('disabled');
                    }
                });
            }
        });
    }
};

// 自动初始化
EditorImageHandler.init();
