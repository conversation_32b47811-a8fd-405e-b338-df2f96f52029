/**
 * TinyMCE富文本编辑器图片上传修复脚本
 * 解决图片上传成功但不插入到编辑器的问题
 */

var TinymceImageUploadFix = {
    /**
     * 初始化修复功能
     */
    init: function() {
        // console.log('TinyMCE图片上传修复脚本已加载');
        
        // 监听TinyMCE编辑器初始化完成事件
        this.hookTinymceInit();
        
        // 增强图片插入逻辑
        this.enhanceImageInsertion();
        
        // 监听图片上传事件
        this.monitorImageUpload();
    },
    
    /**
     * 监听TinyMCE编辑器初始化
     */
    hookTinymceInit: function() {
        var self = this;
        
        // 如果TinyMCE已经加载，直接处理
        if (typeof tinymce !== 'undefined') {
            tinymce.on('AddEditor', function(e) {
                // console.log('TinyMCE编辑器已添加:', e.editor.id);
                self.setupEditorImageHandler(e.editor);
            });
        }
        
        // 监听页面中的编辑器初始化
        $(document).ready(function() {
            setTimeout(function() {
                self.checkAndSetupEditors();
            }, 1000);
        });
    },
    
    /**
     * 检查并设置编辑器
     */
    checkAndSetupEditors: function() {
        var self = this;
        
        if (typeof tinymce !== 'undefined' && tinymce.editors) {
            for (var editorId in tinymce.editors) {
                var editor = tinymce.editors[editorId];
                if (editor && editor.initialized) {
                    // console.log('设置编辑器图片处理器:', editorId);
                    self.setupEditorImageHandler(editor);
                }
            }
        }
    },
    
    /**
     * 为编辑器设置图片处理器
     */
    setupEditorImageHandler: function(editor) {
        var self = this;
        
        // 添加自定义图片插入方法
        editor.insertImageFromUpload = function(imagePath) {
            if (imagePath) {
                // 确保编辑器处于活动状态
                editor.focus();

                // 插入图片
                var imageHtml = '<img src="' + imagePath + '" alt="" style="max-width: 100%;" />';
                editor.insertContent(imageHtml);

                return true;
            }
            return false;
        };
        
        // 存储编辑器引用以便后续使用
        window.tinymceEditors = window.tinymceEditors || {};
        window.tinymceEditors[editor.id] = editor;
    },
    
    /**
     * 增强图片插入逻辑
     */
    enhanceImageInsertion: function() {
        var self = this;

        // 重写全局的图片插入函数
        if (typeof window.frame_obj !== 'undefined') {
            var originalPhotoChoiceInit = window.frame_obj.photo_choice_init;

            window.frame_obj.photo_choice_init = function(id, type, maxpic, del_url, no_mask, callback) {
                // console.log('图片选择初始化 - ID:', id, 'Type:', type);

                // 如果是编辑器类型，记录目标编辑器ID
                if (type === 'editor') {
                    window.currentEditorId = id;

                    // 确保编辑器处于活动状态
                    if (typeof tinymce !== 'undefined' && tinymce.editors[id]) {
                        var editor = tinymce.editors[id];
                        if (editor && editor.initialized) {
                            editor.focus();
                        }
                    }
                }

                // 调用原始函数
                return originalPhotoChoiceInit.call(this, id, type, maxpic, del_url, no_mask, callback);
            };
        }

        // 监听图片选择对话框关闭事件
        $(document).on('dialogClosed', function() {
            // console.log('图片选择对话框已关闭');
            // 清理编辑器ID
            window.currentEditorId = null;
        });
    },
    
    /**
     * 监听图片上传事件
     */
    monitorImageUpload: function() {
        var self = this;

        // 重写frame_obj中的图片处理逻辑
        if (typeof window.frame_obj !== 'undefined') {
            // 保存原始的put_img函数
            var originalPutImg = window.frame_obj.put_img;

            // 重写put_img函数以支持编辑器
            window.frame_obj.put_img = function(imgpath) {
                // console.log('put_img被调用，图片路径:', imgpath, '当前编辑器ID:', window.currentEditorId);

                // 如果当前有活动的编辑器，直接插入图片
                if (window.currentEditorId && imgpath) {
                    // console.log('检测到编辑器上传，目标编辑器:', window.currentEditorId);

                    // 立即插入图片到编辑器
                    var success = self.insertImageToActiveEditor(imgpath);

                    if (success) {
                        // console.log('图片已成功插入编辑器');
                        return;
                    } else {
                        console.warn('图片插入编辑器失败，尝试延迟插入');
                        setTimeout(function() {
                            self.insertImageToActiveEditor(imgpath);
                        }, 300);
                        return;
                    }
                }

                // 否则调用原始函数
                if (originalPutImg) {
                    return originalPutImg.call(this, imgpath);
                }
            };

            // 重写photo_choice_return函数以支持编辑器
            var originalPhotoChoiceReturn = window.frame_obj.photo_choice_return;
            window.frame_obj.photo_choice_return = function(id, type, maxpic, num, imgpath, surplus, number, img_name) {
                // 如果是编辑器类型，直接插入图片
                if (type === 'editor' && imgpath && window.currentEditorId) {
                    var success = self.insertImageToActiveEditor(imgpath);

                    if (success) {
                        // 关闭对话框
                        if (typeof parent !== 'undefined' && parent.frame_obj && parent.frame_obj.pop_contents_close_init) {
                            parent.frame_obj.pop_contents_close_init(parent.$('.photo_choice'), 1, 1);
                        }
                        return;
                    }
                }

                // 否则调用原始函数
                if (originalPhotoChoiceReturn) {
                    return originalPhotoChoiceReturn.apply(this, arguments);
                }
            };
        }

        // 监听Ajax请求完成事件
        $(document).ajaxComplete(function(event, xhr, settings) {
            var imagePath = null;

            // 检查是否是图片上传相关的API
            if (settings.url && (
                settings.url.indexOf('/api/Setting/PhotoChoiceSave') !== -1 ||
                settings.url.indexOf('PhotoChoiceSave') !== -1 ||
                settings.url.indexOf('/api/FileUpload/upload') !== -1
            )) {
                try {
                    var response = xhr.responseJSON;

                    // 处理图库选择和本地上传的响应格式 (PhotoChoiceSave)
                    if (response && response.ret === 1 && response.Pic && response.Pic.length > 0) {
                        // 处理多张图片
                        if (window.currentEditorId) {
                            response.Pic.forEach(function(picPath, index) {
                                setTimeout(function() {
                                    self.insertImageToActiveEditor(picPath);
                                }, 500 + (index * 100)); // 每张图片间隔100ms插入
                            });
                        }
                        return; // 处理完多张图片后直接返回
                    }
                    // 处理本地上传的响应格式 (FileUpload)
                    else if (response && response.filePath) {
                        imagePath = response.filePath;
                    }

                    // 如果获取到图片路径且是编辑器上传，插入图片（单张图片的情况）
                    if (imagePath && window.currentEditorId) {
                        setTimeout(function() {
                            self.insertImageToActiveEditor(imagePath);
                        }, 500);
                    }
                } catch (e) {
                    console.error('处理图片上传响应时出错:', e);
                }
            }
        });
    },
    
    /**
     * 向活动编辑器插入图片
     */
    insertImageToActiveEditor: function(imagePath) {
        var self = this;
        var inserted = false;

        // 方法1: 使用记录的当前编辑器ID
        if (window.currentEditorId && window.tinymceEditors && window.tinymceEditors[window.currentEditorId]) {
            var editor = window.tinymceEditors[window.currentEditorId];
            if (editor && editor.insertImageFromUpload) {
                inserted = editor.insertImageFromUpload(imagePath);
            }
        }

        // 方法2: 直接使用tinymce.editors查找当前编辑器
        if (!inserted && window.currentEditorId && typeof tinymce !== 'undefined' && tinymce.editors[window.currentEditorId]) {
            var editor = tinymce.editors[window.currentEditorId];
            if (editor && editor.initialized) {
                // console.log('使用tinymce.editors直接插入图片:', window.currentEditorId);
                try {
                    editor.focus();
                    var imageHtml = '<img src="' + imagePath + '" alt="" style="max-width: 100%;" />';
                    editor.insertContent(imageHtml);
                    inserted = true;
                    // console.log('图片插入成功');
                } catch (e) {
                    console.error('插入图片时出错:', e);
                }
            }
        }

        // 方法3: 如果方法1和2失败，尝试查找活动编辑器
        if (!inserted && typeof tinymce !== 'undefined' && tinymce.editors) {
            for (var editorId in tinymce.editors) {
                var editor = tinymce.editors[editorId];
                if (editor && editor.hasFocus && editor.hasFocus()) {
                    // console.log('找到有焦点的编辑器:', editorId);
                    try {
                        var imageHtml = '<img src="' + imagePath + '" alt="" style="max-width: 100%;" />';
                        editor.insertContent(imageHtml);
                        inserted = true;
                        // console.log('图片插入成功');
                        break;
                    } catch (e) {
                        // console.error('插入图片时出错:', e);
                    }
                }
            }
        }

        // 方法4: 如果还是没有插入，尝试插入到最后一个编辑器
        if (!inserted && typeof tinymce !== 'undefined' && tinymce.editors) {
            var editorIds = Object.keys(tinymce.editors);
            if (editorIds.length > 0) {
                var lastEditorId = editorIds[editorIds.length - 1];
                var editor = tinymce.editors[lastEditorId];
                if (editor && editor.initialized) {
                    // console.log('使用最后一个编辑器插入图片:', lastEditorId);
                    try {
                        editor.focus();
                        var imageHtml = '<img src="' + imagePath + '" alt="" style="max-width: 100%;" />';
                        editor.insertContent(imageHtml);
                        inserted = true;
                        // console.log('图片插入成功');
                    } catch (e) {
                        // console.error('插入图片时出错:', e);
                    }
                }
            }
        }

        if (!inserted) {
            console.error('无法找到合适的编辑器来插入图片');
            // console.log('可用编辑器:', typeof tinymce !== 'undefined' ? Object.keys(tinymce.editors) : 'tinymce未定义');
            // console.log('当前编辑器ID:', window.currentEditorId);
        }

        // 清除当前编辑器ID记录
        window.currentEditorId = null;

        return inserted;
    }
};

// 页面加载完成后初始化
$(document).ready(function() {
    // 延迟初始化，确保其他脚本已加载
    setTimeout(function() {
        TinymceImageUploadFix.init();
    }, 1000);
    
});

// 添加调试方法
TinymceImageUploadFix.debugInfo = function() {
    // console.log('=== TinyMCE调试信息 ===');
    // console.log('TinyMCE是否加载:', typeof tinymce !== 'undefined');

    if (typeof tinymce !== 'undefined') {
        // console.log('TinyMCE版本:', tinymce.majorVersion + '.' + tinymce.minorVersion);
        // console.log('编辑器实例数量:', Object.keys(tinymce.editors).length);
        // console.log('编辑器实例列表:', Object.keys(tinymce.editors));

        for (var editorId in tinymce.editors) {
            var editor = tinymce.editors[editorId];
            // console.log('编辑器 ' + editorId + ':', {
            //     id: editor.id,
            //     initialized: editor.initialized,
            //     hasFocus: editor.hasFocus ? editor.hasFocus() : 'N/A',
            //     element: editor.getElement(),
            //     isHidden: editor.isHidden ? editor.isHidden() : 'N/A'
            // });
        }
    }

    // console.log('当前编辑器ID:', window.currentEditorId);
    // console.log('自定义编辑器引用:', window.tinymceEditors);
    // console.log('frame_obj是否存在:', typeof window.frame_obj !== 'undefined');

    // 测试图片插入
    var testImagePath = 'https://via.placeholder.com/150x150.png?text=Test';
    // console.log('测试图片插入...');
    TinymceImageUploadFix.insertImageToActiveEditor(testImagePath);

    // alert('调试信息已输出到控制台，请按F12查看。已尝试插入测试图片。');
};
