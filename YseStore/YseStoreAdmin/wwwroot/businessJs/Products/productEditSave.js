/**
 * 产品编辑相关JS
 */

var productEditModule = (function () {
    // 常量定义
    const API_SAVE_PRODUCT = '/Products/Edit?handler=SaveProduct';

    // 模块状态
    let state = {
        isSubmitting: false,
        productId: 0,
        saveSuccessful: false // 添加保存成功标记
    };

    // 初始化函数
    function init() {
        // 获取当前编辑的产品ID
        const productIdElement = document.getElementById('ProId');
        if (productIdElement) {
            state.productId = parseInt(productIdElement.value) || 0;
        }

        // 颜色选择器初始化已移至 colorPickerFix.js

        // 确保关联方式选择器正确初始化
        // 如果选择了single-single或single-multiple，则显示主属性选择器
        const relateMethodRadios = $('input[name="RelateMethod"]:checked');
        if (relateMethodRadios.length > 0) {
            const value = relateMethodRadios.val();
            if (value === 'single-single' || value === 'single-multiple') {
                // 显示主属性选择器
                $('.box_main_attr').show();
            }
        }

        // 确保提交按钮始终可用，覆盖框架的disabled设置
        ensureSubmitButtonEnabled();

        // 监听任何可能重新禁用按钮的事件
        $(document).on('DOMNodeInserted DOMSubtreeModified', function() {
            ensureSubmitButtonEnabled();
        });

        // 定期检查按钮状态
        setInterval(ensureSubmitButtonEnabled, 1000);

        // 监听关联方式选择器变化
        $('input[name="RelateMethod"]').on('change', function () {
            const value = $('input[name="RelateMethod"]:checked').val();

            if (value === 'multiple-single') {
                // 多个属性关联单张主图
                $('.box_main_attr').hide();
            } else {
                // 单个属性关联单张主图、单个属性关联多张主图
                $('.box_main_attr').show();
            }
        });
    }

    /**
     * 确保提交按钮始终可用
     * 覆盖框架可能设置的disabled属性
     * 但如果保存成功，则保持禁用状态
     */
    function ensureSubmitButtonEnabled() {
        // 如果保存已成功，不要重新启用按钮
        if (state.saveSuccessful) {
            return;
        }

        // 移除所有提交按钮的disabled属性
        $('.btn_global.btn_submit[name="submit_button"]').removeAttr('disabled').removeClass('disabled');
        $('.fixed_btn_submit').find('input[type="submit"][name="submit_button"]').removeAttr('disabled').removeClass('disabled');

        // 确保按钮可点击
        $('.btn_global.btn_submit[name="submit_button"]').css('pointer-events', 'auto');
        $('.fixed_btn_submit').find('input[type="submit"][name="submit_button"]').css('pointer-events', 'auto');
    }

    /**
     * 获取CSRF令牌
     * @returns {string} CSRF令牌值
     */
    function getCsrfToken() {
        const tokenElement = document.querySelector('input[name="__RequestVerificationToken"]');
        return tokenElement ? tokenElement.value : '';
    }

    /**
     * 获取URL参数值
     * @param {string} name 参数名
     * @returns {string} 参数值
     */
    function getUrlParameter(name) {
        const urlParams = new URLSearchParams(window.location.search);
        return urlParams.get(name) || '';
    }

    /**
     * 收集产品表单数据
     * @returns {Object} 产品数据对象
     */
    function collectProductData() {
        // 收集产品规格类型
        const isCombination = parseInt($('input[name="IsCombination"]:checked').val()) || 0;
        // 这里需要根据实际表单结构收集数据
        // 基本信息
        const product = {
            ProId: state.productId,
            Name_en: $('input[name="Name_en"]').val(),
            // 将三个体积输入框的值组合成一个逗号分隔的字符串
            Cubage: [
                $('input[name="Cubage_Length"]').val() || '0',
                $('input[name="Cubage_Width"]').val() || '0',
                $('input[name="Cubage_Height"]').val() || '0'
            ].join(','),
            Number: $('input[name="Number"]').val(),
            Status: parseInt($('select[name="Status"]').val()) || 0,
            BriefDescription_en: $('textarea[name="BriefDescription_en"]').val(),
            BriefDescription1_en: $('textarea[name="BriefDescription1_en"]').val(),
            Tags: collectTagIds(), // 返回竖线分隔的ID字符串
            SoldOut: $('select[name="SoldOut"]').val() === "1", // 转换为布尔值，1表示下架(true)，0表示上架(false)
            SoldStatus: parseInt($('select[name="SoldStatus"]').val()) || 0,
            IsCombination: isCombination,
            PageUrl: $('textarea[name="PageUrl"]').val() || '' // 收集自定义地址的值
        };

        // 收集产品描述
        const productDescription = {
            Description_en: $('textarea[name="Description_en"]').val(),
            UsedMobile: $('input[name="UsedMobile"]').prop('checked'),
            MobileDescription: $('textarea[name="MobileDescription"]').val()
        };

        // 收集SEO信息
        const productSeo = {
            SeoTitle_En: $('input[name="SeoTitle_en"]').val(),
            SeoKeyword_En: getSeoKeywordValue(),
            SeoDescription_En: $('textarea[name="SeoDescription_en"]').val()
        };

        // 收集产品图片
        const productImages = [];
        $('.global_container[data-name=pic_info] .pro_multi_img .img .preview_pic input[type="hidden"]').each(function (index) {
            if ($(this).val()) {
                productImages.push({
                    PicPath: $(this).val(),
                    Position: index + 1
                });
            }
        });


        // 收集产品属性
        const productAttributes = [];

        // 收集通用的属性信息（无论规格类型如何）
        $('.box_cart_attribute .box_button_choice').each(function () {
            const attrId = $(this).data('id');
            const attrName = $(this).attr('data-attr') || '';
            const position = $(this).data('position');
            const type = $(this).find('.attr_type').val() || '';

            // 构建选项数组
            const optionsArray = [];
            $(this).find('.btn_attr_choice').each(function (index) {
                optionsArray.push($(this).text().trim());
            });

            // 将选项数组序列化为JSON字符串格式
            const options = JSON.stringify(optionsArray);

            // 获取OptionsData
            const optionsData = $(this).find('.attr_data').val() || '';

            // 创建完整的产品属性对象
            productAttributes.push({
                AttrId: attrId || 0,
                Name_en: attrName,
                Position: position,
                Type: type,
                Options: options,
                OptionsData: optionsData,
                ParentId: 0,
                ProId: state.productId,
                IsMain: false
            });
        });
        // 收集产品变体（根据规格模式的不同进行不同处理）
        const productVariants = [];

        // 获取所有激活的仓库ID（用于后续过滤）
        const activeWarehouseIds = [];
        $('.warehouse_filter .inside_body li[data-type="warehouse"]:not(.hide) .btn_warehouse').each(function () {
            const warehouseId = parseInt($(this).data('id')) || 0;
            // 仅考虑有效的仓库ID
            if (warehouseId > 0) {
                activeWarehouseIds.push(warehouseId);
            }
        });

        // 单规格
        if (isCombination === 0) {
            // 如果没有选择任何仓库，则使用默认仓库
            if (activeWarehouseIds.length === 0) {
                // 添加一个默认变体
                const defaultVariant = {
                    SKU: $('input[name="SKU"]').val() || '',
                    Price: parseFloat($('input[name="Price_1"]').val()) || 0,
                    OldPrice: parseFloat($('input[name="Price_0"]').val()) || 0,
                    CostPrice: parseFloat($('input[name="CostPrice"]').val()) || 0,
                    Stock: parseInt($('input[name="Stock"]').val()) || 0,
                    Weight: parseFloat($('input[name="Weight"]').val()) || 0,
                    WeightUnit: $('input[name="WeightUnit"]').val() || 'kg',
                    OvId: 0, // 默认仓库ID为0
                    IsDefault: true,
                    Combination: '|default|',
                    Data: JSON.stringify(['default'])
                };
                productVariants.push(defaultVariant);
            } else {
                // 收集仓库特定的变体数据
                activeWarehouseIds.forEach(function (warehouseId) {
                    // 检查对应的仓库行是否存在
                    const warehouseRow = $(`#attribute_unit_box tbody tr[data-id="${warehouseId}"]`);
                    if (warehouseRow.length > 0) {
                        // 确保仓库行已启用
                        warehouseRow.find('input').prop('disabled', false);
                        // 收集仓库特定的变体数据
                        const warehouseVariant = {
                            SKU: $(`input[name="ProInfo[_${warehouseId}][SKU]"]`).val() || '',
                            Price: parseFloat($(`input[name="ProInfo[_${warehouseId}][Price_1]"]`).val()) || 0,
                            OldPrice: parseFloat($(`input[name="ProInfo[_${warehouseId}][Price_0]"]`).val()) || 0,
                            CostPrice: parseFloat($(`input[name="ProInfo[_${warehouseId}][CostPrice]"]`).val()) || 0,
                            Stock: parseInt($(`input[name="ProInfo[_${warehouseId}][Stock]"]`).val()) || 0,
                            Weight: parseFloat($(`input[name="ProInfo[_${warehouseId}][Weight]"]`).val()) || 0,
                            WeightUnit: $(`input[name="ProInfo[_${warehouseId}][WeightUnit]"]`).val() || 'kg',
                            OvId: warehouseId,
                            IsDefault: false,
                            Combination: '|default|',
                            Data: JSON.stringify(['default'])
                        };
                        productVariants.push(warehouseVariant);
                    }
                });

                // 如果遍历完所有可见仓库后仍然没有收集到变体，则添加默认变体
                if (productVariants.length === 0) {
                    const defaultVariant = {
                        SKU: $('input[name="SKU"]').val() || '',
                        Price: parseFloat($('input[name="Price_1"]').val()) || 0,
                        OldPrice: parseFloat($('input[name="Price_0"]').val()) || 0,
                        CostPrice: parseFloat($('input[name="CostPrice"]').val()) || 0,
                        Stock: parseInt($('input[name="Stock"]').val()) || 0,
                        Weight: parseFloat($('input[name="Weight"]').val()) || 0,
                        WeightUnit: $('input[name="WeightUnit"]').val() || 'kg',
                        OvId: 0, // 默认仓库ID为0
                        IsDefault: true,
                        Combination: '|default|',
                        Data: JSON.stringify(['default'])
                    };
                    productVariants.push(defaultVariant);
                }
            }
        } else if (isCombination === 1) {
            // 多规格
            // 存储所有收集到的变体数据，按仓库ID和位置索引进行组织
            const variantMap = new Map();

            // 如果没有选择任何仓库，则使用默认仓库
            if (activeWarehouseIds.length === 0) {
                // 不再只添加一个默认变体，而是收集所有规格组合
                // 查找所有规格组合行
                $('#AttrId_1 tbody tr').each(function () {
                    // 对于每一行，尝试直接获取所有必要的变体数据字段
                    const sku = $(this).find('input[data-type="sku"]').val() || '';
                    const oldPrice = parseFloat($(this).find('input[data-type="oldprice"]').val()) || 0;

                    // 处理价格 - 确保是有效数字
                    const priceInput = $(this).find('input[data-type="price"]');
                    const priceValue = priceInput.val();
                    const finalPrice = (priceValue && !isNaN(parseFloat(priceValue))) ? parseFloat(priceValue) : 0;

                    const costPrice = parseFloat($(this).find('input[data-type="cost_price"]').val()) || 0;
                    const stock = parseInt($(this).find('input[data-type="stock"]').val()) || 0;
                    const weight = parseFloat($(this).find('input[data-type="weight"]').val()) || 0;
                    const weightUnit = $(this).find('.unit_input_select_input').val() || 'kg';

                    // 获取隐藏字段信息（规格名称、选项等）
                    const variantId = $(this).find('input[name*="[VariantsId]"]').val() || '';
                    const attr = $(this).find('input[name*="[Attr]"]').val() || '';
                    const options = $(this).find('input[name*="[Options]"]').val() || '';
                    const position = $(this).find('input[name*="[Position]"]').val() || '';
                    const isDefault = $(this).find('input[name*="[IsDefault]"]').val() === '1';
                    const picPath = $(this).find('input[name*="[PicPath]"]').val() || '';
                    const rawAttrName = $(this).find('.attr_name').text().trim();
                    const attrName = rawAttrName.replace('默认选项', '').trim();
                    // 获取优先发货仓库ID
                    const priorityShippingSelect = $(this).find('select[data-type="priority_shipping"]');
                    const priorityShippingOvId = parseInt(priorityShippingSelect.val()) || 0;


                    try {
                        const options2 = JSON.parse(options); // 解析为数组
                        // 转换为竖线分隔的字符串格式
                        const combination = '|' + options2.join('|') + '|';

                        // 构建变体对象
                        const variant = {
                            VariantsId: variantId,
                            Title: attrName,
                            AttrName: attr,
                            Combination: combination,
                            Data: options,
                            IsDefault: isDefault,
                            OvId: 0, // 默认仓库ID为0
                            PicPath: picPath,
                            SKU: sku,
                            OldPrice: oldPrice,
                            Price: finalPrice,
                            CostPrice: costPrice,
                            Stock: stock,
                            Weight: weight,
                            WeightUnit: weightUnit,
                            PriorityShippingOvId: priorityShippingOvId//优先发货仓库ID
                        };

                        // 添加到产品变体数组
                        productVariants.push(variant);
                    } catch (e) {
                        console.error('解析规格选项时出错:', e);
                    }
                });

                // 如果仍然没有收集到任何变体，添加一个默认变体
                if (productVariants.length === 0) {
                    const defaultVariant = createDefaultVariantForMultiSpec();
                    productVariants.push(defaultVariant);
                }
            } else {
                // 收集所有实际渲染在页面上的变体行
                $('#AttrId_1 tbody tr').each(function () {
                    // 尝试确定行所属的仓库ID
                    const parentTbody = $(this).closest('tbody');
                    const warehouseId = parseInt(parentTbody.data('id')) || 0;

                    // 只处理用户选择的仓库的变体
                    if (!activeWarehouseIds.includes(warehouseId)) {
                        return;
                    }

                    // 对于每一行，尝试直接获取所有必要的变体数据字段
                    const sku = $(this).find('input[data-type="sku"]').val() || '';
                    const oldPrice = parseFloat($(this).find('input[data-type="oldprice"]').val()) || 0;

                    // 处理价格 - 确保是有效数字
                    const priceInput = $(this).find('input[data-type="price"]');
                    const priceValue = priceInput.val();
                    const finalPrice = (priceValue && !isNaN(parseFloat(priceValue))) ? parseFloat(priceValue) : 0;

                    const costPrice = parseFloat($(this).find('input[data-type="cost_price"]').val()) || 0;

                    // 处理库存 - 如果为空或0，使用默认库存值
                    let stock = parseInt($(this).find('input[data-type="stock"]').val()) || 0;
                    if (stock === 0) {
                        const defaultInventory = parseInt($('input[name="DefaultInventory"]').val()) || 5000;
                        stock = defaultInventory;
                    }

                    const weight = parseFloat($(this).find('input[data-type="weight"]').val()) || 0;
                    const weightUnit = $(this).find('.unit_input_select_input').val() || 'kg';

                    // 获取优先发货仓库ID
                    const priorityShippingSelect = $(this).find('select[data-type="priority_shipping"]');
                    const priorityShippingOvId = parseInt(priorityShippingSelect.val()) || 0;


                    // 获取隐藏字段信息（规格名称、选项等）
                    const variantId = $(this).find('input[name*="[VariantsId]"]').val() || '';
                    const attr = $(this).find('input[name*="[Attr]"]').val() || '';
                    const options = $(this).find('input[name*="[Options]"]').val() || '';
                    const position = $(this).find('input[name*="[Position]"]').val() || '';
                    const isDefault = $(this).find('input[name*="[IsDefault]"]').val() === '1';
                    const picPath = $(this).find('input[name*="[PicPath]"]').val() || '';
                    const rawAttrName = $(this).find('.attr_name').text().trim();
                    const attrName = rawAttrName.replace('默认选项', '').trim();

                    const options2 = JSON.parse(options); // 解析为数组
                    // 转换为竖线分隔的字符串格式
                    const combination = '|' + options2.join('|') + '|';
                    // 构建变体对象
                    const variant = {
                        VariantsId: variantId,
                        Title: attrName,
                        AttrName: attr,
                        // Options: options,
                        Combination: combination,
                        Data: options,
                        IsDefault: isDefault,
                        OvId: warehouseId,//仓库ID
                        PicPath: picPath,//图片路径
                        SKU: sku,//SKU
                        OldPrice: oldPrice,//原价
                        Price: finalPrice,//价格
                        CostPrice: costPrice,//成本价
                        Stock: stock,//库存
                        Weight: weight,//重量
                        WeightUnit: weightUnit,//重量单位
                        PriorityShippingOvId: priorityShippingOvId//优先发货仓库ID
                    };

                    // 生成唯一键（仓库ID + 位置）
                    const key = `${warehouseId}_${position}`;

                    // 存储变体
                    variantMap.set(key, variant);
                });

                // 将Map转换为数组，并按仓库ID和位置排序
                const sortedVariants = Array.from(variantMap.values())
                    .sort((a, b) => {
                        // 首先按仓库ID排序
                        if (a.OvId !== b.OvId) {
                            return a.OvId - b.OvId;
                        }

                        // 然后按位置排序
                        return parseInt(a.Position || 0) - parseInt(b.Position || 0);
                    });

                // 将排序后的变体添加到产品变体数组
                sortedVariants.forEach(variant => productVariants.push(variant));

                // 如果没有收集到任何变体，添加一个默认变体
                if (productVariants.length === 0) {
                    const defaultVariant = createDefaultVariantForMultiSpec();
                    productVariants.push(defaultVariant);
                }
            }
        } else if (isCombination === 2) {
            // 多规格加价
            // 先收集单规格基础信息
            const baseVariant = {
                SKU: $('input[name="SKU"]').val() || '',
                Price: parseFloat($('input[name="Price_1"]').val()) || 0,
                OldPrice: parseFloat($('input[name="Price_0"]').val()) || 0,
                CostPrice: parseFloat($('input[name="CostPrice"]').val()) || 0,
                Stock: parseInt($('input[name="Stock"]').val()) || 0,
                Weight: parseFloat($('input[name="Weight"]').val()) || 0,
                WeightUnit: $('input[name="WeightUnit"]').val() || 'kg',
                IsDefault: true,
                IsBaseVariant: true,
                OvId: 0 // 确保基础变体也有OvId
            };
            productVariants.push(baseVariant);

            // 获取所有多规格加价变体，无论是否选择仓库
            $('#AttrId_0 tbody tr').each(function () {
                // 获取变体数据
                const variantId = $(this).find('input[name^="variants[0]"][name$="[VariantsId]"]').val() || '';
                const attr = $(this).find('input[name^="variants[0]"][name$="[Attr]"]').val() || '';
                const options = $(this).find('input[name^="variants[0]"][name$="[Options]"]').val() || '';
                const position = $(this).find('input[name^="variants[0]"][name$="[Position]"]').val() || '';

                try {
                    const options2 = JSON.parse(options); // 解析为数组
                    const singleOption = Array.isArray(options2) && options2.length > 0 ? options2[0] : ''; // 提取第一个值
                    // 转换为竖线分隔的字符串格式
                    const combination = '|' + options2.join('|') + '|';

                    // 提取加价信息
                    const variant = {
                        VariantsId: variantId,
                        Title: singleOption,
                        AttrName: attr,
                        Combination: combination,
                        Data: options,
                        Position: position,
                        OldPrice: parseFloat($(this).find('input[name^="variants[0]"][name$="[OldPrice]"]').val()) || 0,
                        Price: parseFloat($(this).find('input[name^="variants[0]"][name$="[Price]"]').val()) || 0,
                        IsExtraVariant: true,
                        OvId: 0 // 确保加价变体也有OvId
                    };

                    productVariants.push(variant);
                } catch (e) {
                    console.error('解析加价规格选项时出错:', e);
                }
            });
        }

        // 收集分类ID
        const categoryIds = [];
        // 扩展选择器范围，尝试多种可能的DOM结构
        $('.global_select_category_value_box .category_item, .category_list .category_item, .select_category_box .category_item, input[name="CategoryIds[]"]').each(function () {
            // 尝试从多个可能的位置获取分类ID
            let cateId = parseInt($(this).data('id')) || 0;

            // 如果没有找到data-id，尝试从value属性获取
            if (!cateId && $(this).is('input')) {
                cateId = parseInt($(this).val()) || 0;
            }

            // 如果还没找到，尝试从隐藏字段获取
            if (!cateId) {
                const hiddenInput = $(this).find('input[type="hidden"]');
                if (hiddenInput.length > 0) {
                    cateId = parseInt(hiddenInput.val()) || 0;
                }
            }

            if (cateId > 0) {
                categoryIds.push(cateId);
            }
        });

        // 收集关联主图信息
        const relateMethod = $('input[name="RelateMethod"]:checked').val() || '';

        // 修改这里，确保即使元素隐藏也能获取MainAttr的值
        // 判断MainAttr选择器是否有值，并确保在隐藏状态下也能获取
        let mainAttrId = 0;
        const mainAttrSelect = $('select[name="MainAttr"]');
        if (mainAttrSelect.length > 0) {
            const mainAttrValue = mainAttrSelect.val();
            // 获取选中选项的data-id属性值(如果有的话)
            if (mainAttrValue && mainAttrValue !== '') {
                const selectedOption = mainAttrSelect.find('option:selected');
                if (selectedOption.length > 0 && selectedOption.attr('data-id')) {
                    mainAttrId = parseInt(selectedOption.attr('data-id')) || 0;
                } else {
                    // 如果没有data-id属性，尝试将选中的值转换为整数
                    mainAttrId = parseInt(mainAttrValue) || 0;
                }
            }
        }



        // 从URL获取model参数
        const modelParam = getUrlParameter('model');

        // 构建最终请求数据
        return {
            Product: product,
            ProductDescription: productDescription,
            ProductSeo: productSeo,
            ProductImages: productImages,
            ProductAttributes: productAttributes,
            ProductVariants: productVariants,
            CategoryIds: categoryIds,
            RelateMethod: relateMethod,
            MainAttrId: mainAttrId,
            IsFreeShipping: $('input[name="IsFreeShipping"]').prop('checked'),
            isVirtual: !$('input[name="needLogistics"]').prop('checked'),//需要物流 -> 对应是否是虚拟产品
            TId: parseInt($('select[name="TId"]').val()) || 0,
            VideoType: $('select[name="VideoType"]').val() || '',
            VideoUrl: $('input[name="VideoUrl"]').val() || '',
            Model: modelParam // 从URL参数获取的model值
        };
    }

    /**
     * 收集标签ID，返回竖线分隔的ID字符串
     * @returns {string} 标签ID字符串，格式如 "|5|2|"
     */
    function collectTagIds() {
        // 尝试从页面上的标签元素中获取ID
        const tagIds = [];

        // 从标签元素中获取ID
        $('.global_container[data-name="tag_info"] .option_selected .select_list .btn_attr_choice').each(function () {
            // 尝试从多个可能的位置获取标签ID
            let tagId = $(this).data('id');

            // 如果没有直接在元素上找到ID，尝试从子元素中查找
            if (!tagId) {
                // 尝试从checkbox获取
                const checkbox = $(this).find('input[type="checkbox"]');
                if (checkbox.length > 0) {
                    tagId = parseInt(checkbox.val());
                } else {
                    // 尝试从隐藏字段获取
                    const hiddenInput = $(this).find('input[type="hidden"]');
                    if (hiddenInput.length > 0) {
                        tagId = hiddenInput.data('id') || parseInt(hiddenInput.val());
                    }
                }
            }

            // 确保ID是有效的数字
            if (tagId && !isNaN(parseInt(tagId))) {
                tagIds.push(parseInt(tagId));
            }
        });

        // 如果没有找到ID，尝试从其他位置获取
        if (tagIds.length === 0) {
            // 可能存在一个存储标签ID的隐藏字段
            const tagIdsField = $('input[name="TagIds"]');
            if (tagIdsField.length > 0 && tagIdsField.val()) {
                const existingValue = tagIdsField.val();
                // 如果已经是竖线格式则直接返回，否则进行转换
                if (existingValue.startsWith('|') && existingValue.endsWith('|')) {
                    return existingValue;
                } else if (existingValue.includes(',')) {
                    // 将逗号分隔转换为竖线分隔
                    const ids = existingValue.split(',').filter(id => id.trim() !== '');
                    return ids.length > 0 ? '|' + ids.join('|') + '|' : '';
                }
                return existingValue; // 返回原始值
            }

            // 尝试从其他可能的隐藏字段获取
            const tagInput = $('input[name="Tag"]');
            if (tagInput.length > 0 && tagInput.val() && tagInput.val() !== "1") {
                const existingValue = tagInput.val();
                // 如果已经是竖线格式则直接返回，否则进行转换
                if (existingValue.startsWith('|') && existingValue.endsWith('|')) {
                    return existingValue;
                } else if (existingValue.includes(',')) {
                    // 将逗号分隔转换为竖线分隔
                    const ids = existingValue.split(',').filter(id => id.trim() !== '');
                    return ids.length > 0 ? '|' + ids.join('|') + '|' : '';
                }
                return existingValue; // 返回原始值
            }
        }

        // 将tagIds数组转换为竖线分隔的字符串格式
        return tagIds.length > 0 ? '|' + tagIds.join('|') + '|' : '';
    }

    /**
     * 获取SEO关键词值，尝试多种可能的字段名
     * @returns {string} SEO关键词值
     */
    function getSeoKeywordValue() {
        // 先尝试从SeoKeyword_en_value获取
        let keywordValue = $('input[name="SeoKeyword_en_value"]').val();

        // 如果不存在，尝试从SeoKeyword_en获取
        if (!keywordValue) {
            keywordValue = $('input[name="SeoKeyword_en"]').val();
        }

        // 如果还是不存在，尝试从关键词选择列表中收集
        if (!keywordValue) {
            const keywords = [];
            $('.seo_box .option_selected[data-type="seo_keyword"] .select_list .btn_attr_choice b').each(function () {
                keywords.push($(this).text().trim());
            });

            if (keywords.length > 0) {
                keywordValue = keywords.join(',');
            }
        }

        return keywordValue || '';
    }

    /**
     * 保存产品数据
     */
    function toSaveProduct() {
        // 如果正在提交，则不重复处理
        if (state.isSubmitting) {
            return;
        }

        // 设置提交状态
        state.isSubmitting = true;

        // 强制重置按钮状态 - 最高优先级
        const forceEnableButton = () => {
            $('.btn_global.btn_submit').prop('disabled', false).removeAttr('disabled').removeClass('disabled');
            $('.fixed_btn_submit').find('input:submit').prop('disabled', false).removeAttr('disabled').removeClass('disabled');
        };

        // 显示加载状态
        // customize_pop.loading('正在保存产品信息...');

        try {
            // 收集产品数据
            const productData = collectProductData();
            if (productData.Product.Name_en === '') {
                customize_pop.loadingClose();
                customize_pop.error('请输入产品名称');
                state.isSubmitting = false;
                // 确保提交按钮没有被禁用
                forceEnableButton();
                ensureSubmitButtonEnabled();
                return;
            }

            // 多规格模式下验证价格和默认规格
            if (productData.Product.IsCombination === 1) {
                const emptyPriceVariants = [];
                productData.ProductVariants.forEach((variant, index) => {
                    if (!variant.Price || variant.Price <= 0) {
                        // 获取变体的显示名称，用于错误提示
                        const variantName = variant.Title || variant.AttrName || `规格${index + 1}`;
                        emptyPriceVariants.push(variantName);
                    }
                });

                if (emptyPriceVariants.length > 0) {
                    customize_pop.loadingClose();
                    const errorMessage = `请填写以下规格的价格：${emptyPriceVariants.join('、')}`;
                    customize_pop.error(errorMessage);
                    state.isSubmitting = false;
                    // 确保提交按钮没有被禁用
                    forceEnableButton();
                    ensureSubmitButtonEnabled();
                    return;
                }

                // 验证是否有默认规格
                const hasDefaultVariant = productData.ProductVariants.some(variant => variant.IsDefault === true);
                if (!hasDefaultVariant) {
                    customize_pop.error('请设置一条规格数据为默认选项！');
                    state.isSubmitting = false;
                    // 确保提交按钮没有被禁用
                    forceEnableButton();
                    ensureSubmitButtonEnabled();
                    return;
                }
            }

            console.log('产品数据:', productData);

            // 获取CSRF令牌
            const token = getCsrfToken();

            // 使用直接的AJAX请求，以确保添加CSRF令牌
            $.ajax({
                url: API_SAVE_PRODUCT,
                type: 'POST',
                contentType: 'application/json',
                data: JSON.stringify(productData),
                headers: {
                    'RequestVerificationToken': token
                },
                success: function (response) {
                    // 处理响应
                    if (response && response.ret === 1) {
                        // 保存成功
                        customize_pop.loadingClose();
                        customize_pop.success('产品保存成功');

                        // 设置保存成功标记，防止定时器重新启用按钮
                        state.saveSuccessful = true;

                        // 保存成功后禁用提交按钮，防止重复提交
                        $('.btn_global.btn_submit').prop('disabled', true).attr('disabled', 'disabled').addClass('disabled');
                        $('.fixed_btn_submit').find('input:submit').prop('disabled', true).attr('disabled', 'disabled').addClass('disabled');

                        // 如果是新增产品，更新ID并跳转
                        if (state.productId === 0 && response.data && response.data.proId) {
                            state.productId = response.data.proId;
                            // 可以选择跳转到编辑页面或留在当前页面
                        }
                        setTimeout(() => {
                            // window.location.href = `/Products/Edit?id=${response.data.proId}`;
                            window.location.href = `/Products/Index?status=0`;
                        }, 1000);

                    } else {
                        // 保存失败
                        customize_pop.loadingClose();
                        customize_pop.error(response.msg || '保存失败，请重试');
                        // 保存失败时重新启用提交按钮，允许重试
                        $('.btn_global.btn_submit').prop('disabled', false).removeAttr('disabled').removeClass('disabled');
                        $('.fixed_btn_submit').find('input:submit').prop('disabled', false).removeAttr('disabled').removeClass('disabled');
                    }
                },
                error: function (xhr, status, error) {
                    // 处理错误
                    customize_pop.loadingClose();
                    customize_pop.error('发生错误: ' + (error || '未知错误'));
                    console.error('保存产品错误:', xhr, status, error);
                    // 发生错误时重新启用提交按钮，允许重试
                    forceEnableButton();
                    ensureSubmitButtonEnabled();
                },
                complete: function (xhr) {
                    // 重置提交状态
                    state.isSubmitting = false;

                    // 只有在请求失败或出错时才重新启用按钮
                    // 如果是成功响应，按钮应该保持禁用状态
                    if (xhr.status !== 200 || (xhr.responseJSON && xhr.responseJSON.ret !== 1)) {
                        forceEnableButton();
                        ensureSubmitButtonEnabled();
                    }
                }
            });
        } catch (error) {
            console.error('收集或提交产品数据时发生错误:', error);
            customize_pop.loadingClose();
            customize_pop.error('提交数据时发生错误: ' + error.message);
            state.isSubmitting = false;
            // 发生异常时重新启用提交按钮，允许重试
            forceEnableButton();
            ensureSubmitButtonEnabled();
        }
    }

    // 暴露公共API
    return {
        init: init,
        collectProductData: collectProductData,
        toSaveProduct: toSaveProduct
    };
})();

// 页面加载完成后初始化模块
$(document).ready(function () {
    productEditModule.init();
});

// 将保存方法挂载到window对象，使其成为真正的全局函数
window.toSaveProduct = function () {
    productEditModule.toSaveProduct();
};

// 在函数的适当位置添加这个辅助函数
function createDefaultVariantForMultiSpec() {
    // 尝试从页面中获取第一个属性和选项
    let attr = '';
    let option = '';

    const firstAttr = $('.box_cart_attribute .box_button_choice').first();
    if (firstAttr.length > 0) {
        attr = firstAttr.attr('data-attr') || '';
        const firstOption = firstAttr.find('.btn_attr_choice').first();
        if (firstOption.length > 0) {
            option = firstOption.text().trim();
        }
    }

    // 如果没有找到属性和选项，使用默认值
    if (!attr) attr = 'default';
    if (!option) option = 'default';

    return {
        VariantsId: '',
        Title: attr,
        AttrName: attr,
        Combination: `|${option}|`,
        Data: JSON.stringify([option]),
        IsDefault: true,
        OvId: 0,
        SKU: $('input[name="SKU"]').val() || '',
        OldPrice: parseFloat($('input[name="Price_0"]').val()) || 0,
        Price: parseFloat($('input[name="Price_1"]').val()) || 0,
        CostPrice: parseFloat($('input[name="CostPrice"]').val()) || 0,
        Stock: parseInt($('input[name="Stock"]').val()) || 0,
        Weight: parseFloat($('input[name="Weight"]').val()) || 0,
        WeightUnit: $('input[name="WeightUnit"]').val() || 'kg'
    };
} 