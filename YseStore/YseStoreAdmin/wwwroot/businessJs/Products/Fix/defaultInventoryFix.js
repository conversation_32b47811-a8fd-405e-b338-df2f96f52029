/**
 * 默认库存区域显示控制修复
 * 只在多规格模式下显示"设置默认库存"区域
 */

var DefaultInventoryFix = {
    /**
     * 初始化默认库存区域显示控制
     */
    init: function() {
        const self = this;

        // 延迟初始化，确保其他脚本先加载完成
        setTimeout(function() {
            // 页面加载时检查当前规格模式
            self.checkAndToggleDefaultInventory();

            // 监听规格模式变化
            self.bindSpecificationModeChange();
        }, 1000);
    },

    /**
     * 检查并切换默认库存区域的显示状态
     */
    checkAndToggleDefaultInventory: function() {
        const checkedRadio = $('input[name="IsCombination"]:checked');
        if (checkedRadio.length > 0) {
            const isCombination = parseInt(checkedRadio.val());
            this.toggleDefaultInventoryDisplay(isCombination);
        }
    },

    /**
     * 切换默认库存区域的显示状态
     * @param {number} isCombination 规格模式值 (0:单规格, 1:多规格, 2:多规格加价)
     */
    toggleDefaultInventoryDisplay: function(isCombination) {
        const $defaultInventoryContainer = $('.default_inventory_container');

        // console.log('DefaultInventoryFix: 切换规格模式到', isCombination);
        // console.log('DefaultInventoryFix: 找到的容器数量', $defaultInventoryContainer.length);

        if (isCombination === 1) {
            // 多规格模式：显示默认库存区域
            // console.log('DefaultInventoryFix: 显示默认库存区域');
            $defaultInventoryContainer.show();
        } else {
            // 单规格或多规格加价模式：隐藏默认库存区域
            // console.log('DefaultInventoryFix: 隐藏默认库存区域');
            $defaultInventoryContainer.hide();
        }
    },

    /**
     * 绑定规格模式变化事件
     */
    bindSpecificationModeChange: function() {
        const self = this;

        // 监听radio input的change事件
        $('input[name="IsCombination"]').on('change', function() {
            const isCombination = parseInt($(this).val());

            // 延迟执行，确保其他相关逻辑先执行完成
            setTimeout(function() {
                self.toggleDefaultInventoryDisplay(isCombination);
            }, 100);
        });

        // 监听规格模式容器的点击事件（因为原始代码使用的是点击事件）
        $('.specification_container .input_radio_box').on('click', function() {
            const isCombination = parseInt($(this).find('input').val());
            // console.log('DefaultInventoryFix: 检测到规格模式点击事件，值为', isCombination);

            // 延迟执行，确保其他相关逻辑先执行完成
            setTimeout(function() {
                self.toggleDefaultInventoryDisplay(isCombination);
            }, 150);
        });

        // console.log('DefaultInventoryFix: 事件绑定完成，找到规格模式容器数量:', $('.specification_container .input_radio_box').length);
    }
};

// 页面加载完成后初始化
$(document).ready(function() {
    DefaultInventoryFix.init();
});
