/**
 * RecommendEdit页面分类选择弹窗修复脚本
 * 解决分类选择弹窗中无法选中分类的问题
 */

var RecommendEditCategoryFix = (function() {
    
    /**
     * 初始化修复
     */
    function init() {
        $(document).ready(function() {
            // 延迟执行，确保页面完全加载
            setTimeout(function() {
                fixCategorySelection();
                setupDOMObserver();
            }, 500);
        });
    }
    
    /**
     * 修复分类选择功能
     */
    function fixCategorySelection() {
        console.log('RecommendEditCategoryFix: 开始修复分类选择功能');
        
        // 移除所有可能的旧事件绑定
        $(document).off('click.recommendCategoryFix');
        $('.global_select_category_popup_box').off('click.recommendCategoryFix');
        
        // 绑定分类选择弹窗中的复选框点击事件
        bindCategoryCheckboxEvents();
        
        // 绑定弹窗打开事件
        bindPopupOpenEvent();
        
        // 绑定保存按钮事件
        bindSaveButtonEvent();
        
        // 绑定取消按钮事件
        bindCancelButtonEvent();
    }
    
    /**
     * 绑定分类复选框点击事件
     */
    function bindCategoryCheckboxEvents() {
        // 使用事件委托绑定复选框点击事件
        $(document).on('click.recommendCategoryFix', '.global_select_category_popup_box .select_category_table .input_checkbox_box:not(.disabled)', function(e) {
            e.preventDefault();
            e.stopPropagation();
            
            var $checkboxBox = $(this);
            var $checkbox = $checkboxBox.find('input[type="checkbox"]');
            
            // 切换选中状态
            if ($checkboxBox.hasClass('checked')) {
                $checkboxBox.removeClass('checked');
                $checkbox.prop('checked', false);
            } else {
                $checkboxBox.addClass('checked');
                $checkbox.prop('checked', true);
            }
            
            // 更新选中数量显示
            updateSelectedCount();
            
            console.log('RecommendEditCategoryFix: 分类选择状态已更新', {
                categoryId: $checkbox.val(),
                categoryName: $checkbox.attr('data-alias'),
                isChecked: $checkbox.prop('checked')
            });
        });
        
        // 绑定展开/收起按钮事件
        $(document).on('click.recommendCategoryFix', '.global_select_category_popup_box .select_category_table .btn_sub', function(e) {
            e.preventDefault();
            e.stopPropagation();
            
            var $btn = $(this);
            var $parentRow = $btn.closest('.tr.haschild');
            var $subBox = $parentRow.next('.box_sub');
            
            if ($subBox.length > 0) {
                $parentRow.toggleClass('cur');
                $subBox.slideToggle();
            }
        });
        
        // 绑定全选按钮事件
        $(document).on('click.recommendCategoryFix', '.global_select_category_popup_box .select_category_table .btn_select', function(e) {
            e.preventDefault();
            e.stopPropagation();
            
            var $btn = $(this);
            var $parentRow = $btn.closest('.tr.haschild');
            var $subBox = $parentRow.next('.box_sub');
            var isSelectAll = $btn.hasClass('selectall');
            
            if (isSelectAll) {
                // 取消全选
                $btn.removeClass('selectall');
                $parentRow.find('.input_checkbox_box:not(.disabled)').removeClass('checked').find('input').prop('checked', false);
                $subBox.find('.input_checkbox_box:not(.disabled)').removeClass('checked').find('input').prop('checked', false);
                $subBox.find('.btn_select').removeClass('selectall');
            } else {
                // 全选
                $btn.addClass('selectall');
                $parentRow.find('.input_checkbox_box:not(.disabled)').addClass('checked').find('input').prop('checked', true);
                $subBox.find('.input_checkbox_box:not(.disabled)').addClass('checked').find('input').prop('checked', true);
                $subBox.find('.btn_select').addClass('selectall');
            }
            
            // 更新选中数量显示
            updateSelectedCount();
        });
    }
    
    /**
     * 绑定弹窗打开事件
     */
    function bindPopupOpenEvent() {
        $(document).on('click.recommendCategoryFix', '.global_select_category_btn', function(e) {
            e.preventDefault();
            
            var $btn = $(this);
            var $popup = $('.global_select_category_popup_box');
            
            // 显示弹窗
            $popup.show();
            
            // 重置搜索表单
            $popup.find('.search_form input[name="Keyword"]').val('');
            $popup.find('.select_category_table').show();
            $popup.find('.search_category_table').hide();
            
            // 更新选中数量显示
            updateSelectedCount();
            
            console.log('RecommendEditCategoryFix: 分类选择弹窗已打开');
        });
    }
    
    /**
     * 绑定保存按钮事件
     */
    function bindSaveButtonEvent() {
        $(document).on('click.recommendCategoryFix', '.global_select_category_popup_box .button .btn_submit', function(e) {
            e.preventDefault();
            
            var selectedCategories = [];
            var $popup = $('.global_select_category_popup_box');
            
            // 收集选中的分类
            $popup.find('.select_category_table input[name="GlobalSelectCateId"]:checked').each(function() {
                var $checkbox = $(this);
                selectedCategories.push({
                    id: $checkbox.val(),
                    name: $checkbox.attr('data-alias')
                });
            });
            
            // 更新分类显示区域
            updateCategoryDisplay(selectedCategories);
            
            // 关闭弹窗
            $popup.hide();
            
            console.log('RecommendEditCategoryFix: 已保存选中的分类', selectedCategories);
        });
    }
    
    /**
     * 绑定取消按钮事件
     */
    function bindCancelButtonEvent() {
        $(document).on('click.recommendCategoryFix', '.global_select_category_popup_box .button .btn_cancel, .global_select_category_popup_box .t h2', function(e) {
            e.preventDefault();
            
            var $popup = $('.global_select_category_popup_box');
            $popup.hide();
            
            console.log('RecommendEditCategoryFix: 已取消分类选择');
        });
    }
    
    /**
     * 更新选中数量显示
     */
    function updateSelectedCount() {
        var $popup = $('.global_select_category_popup_box');
        var selectedCount = $popup.find('.select_category_table input[name="GlobalSelectCateId"]:checked').length;
        
        $popup.find('.category_num .num').text(selectedCount);
    }
    
    /**
     * 更新分类显示区域
     */
    function updateCategoryDisplay(selectedCategories) {
        var $categoryBox = $('.global_select_category_btn_box .category_value_box');
        var $noDataBox = $('.global_select_category_btn_box .no-data');
        
        // 清空现有内容
        $categoryBox.empty();
        
        if (selectedCategories.length > 0) {
            // 显示选中的分类
            var html = '';
            selectedCategories.forEach(function(category) {
                html += `
                    <div class="category_item" data-cateid="${category.id}">
                        <div class="cname">${category.name}</div>
                        <div class="cdel"><span class="icon iconfont icon_menu_close"></span></div>
                        <input type="hidden" name="products_categoryCurrent[]" value="${category.id}">
                    </div>
                `;
            });
            
            $categoryBox.html(html).removeClass('hide');
            $noDataBox.addClass('hide');
        } else {
            // 显示无数据提示
            $categoryBox.addClass('hide');
            $noDataBox.removeClass('hide');
        }
        
        // 绑定删除按钮事件
        bindDeleteButtonEvent();
    }
    
    /**
     * 绑定删除按钮事件
     */
    function bindDeleteButtonEvent() {
        $(document).off('click.recommendCategoryDelete');
        $(document).on('click.recommendCategoryDelete', '.global_select_category_btn_box .category_item .cdel', function(e) {
            e.preventDefault();
            
            var $item = $(this).closest('.category_item');
            var categoryId = $item.attr('data-cateid');
            
            // 移除分类项
            $item.remove();
            
            // 更新弹窗中对应的复选框状态
            var $popup = $('.global_select_category_popup_box');
            $popup.find(`.select_category_table input[name="GlobalSelectCateId"][value="${categoryId}"]`)
                  .prop('checked', false)
                  .closest('.input_checkbox_box')
                  .removeClass('checked');
            
            // 检查是否还有分类
            var $categoryBox = $('.global_select_category_btn_box .category_value_box');
            var $noDataBox = $('.global_select_category_btn_box .no-data');
            
            if ($categoryBox.find('.category_item').length === 0) {
                $categoryBox.addClass('hide');
                $noDataBox.removeClass('hide');
            }
            
            console.log('RecommendEditCategoryFix: 已删除分类', categoryId);
        });
    }
    
    /**
     * 设置DOM变化监听器
     */
    function setupDOMObserver() {
        // 监听DOM变化，确保动态加载的内容也能正常工作
        if (window.MutationObserver) {
            var observer = new MutationObserver(function(mutations) {
                mutations.forEach(function(mutation) {
                    if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
                        // 检查是否有新的分类选择相关元素被添加
                        var hasRelevantNodes = Array.from(mutation.addedNodes).some(function(node) {
                            return node.nodeType === 1 && (
                                node.classList && (
                                    node.classList.contains('global_select_category_popup_box') ||
                                    node.classList.contains('select_category_table') ||
                                    node.querySelector && node.querySelector('.global_select_category_popup_box')
                                )
                            );
                        });
                        
                        if (hasRelevantNodes) {
                            setTimeout(function() {
                                fixCategorySelection();
                            }, 100);
                        }
                    }
                });
            });
            
            observer.observe(document.body, {
                childList: true,
                subtree: true
            });
        }
    }
    
    // 公开接口
    return {
        init: init,
        fixCategorySelection: fixCategorySelection
    };
})();

// 页面加载完成后自动初始化
jQuery(function() {
    RecommendEditCategoryFix.init();
});
