/**
 * 产品筛选功能修复扩展
 * 用于修复筛选框回显和显示问题以及规格筛选功能
 */

const ProductFilterFix = {
    /**
     * 初始化筛选修复功能
     */
    init: function() {
        // 等待页面加载完成后初始化
        $(document).ready(() => {
            this.initFilterEcho();
            this.bindFilterEvents();
            this.initSpecificationFilter();
        });
    },

    /**
     * 初始化规格筛选功能
     */
    initSpecificationFilter: function() {
        // 等待产品数据和products_obj加载完成
        const checkDataInterval = setInterval(() => {
            if (window.product_data && window.product_data.product_detail && typeof products_obj !== 'undefined') {
                this.setupAttributeData();
                this.generateFilterOptions();
                // 延迟绑定事件，确保DOM完全加载
                setTimeout(() => {
                    this.bindSpecificationFilterEvents();
                }, 500);
                clearInterval(checkDataInterval);
            }
        }, 100);
    },

    /**
     * 设置属性数据到 products_obj.data_init.attr_data
     */
    setupAttributeData: function() {
        try {
            const productDetail = window.product_data.product_detail;

            if (!productDetail || !productDetail.ProductAttributes) {
                return;
            }

            // 确保 products_obj 存在
            if (typeof products_obj === 'undefined') {
                return;
            }

            // 初始化 attr_data 数组
            products_obj.data_init.attr_data = [];

            // 转换属性数据格式
            productDetail.ProductAttributes.forEach((attr, index) => {
                try {
                    // 解析选项数据
                    let options = [];
                    if (attr.Options) {
                        if (typeof attr.Options === 'string') {
                            options = JSON.parse(attr.Options);
                        } else if (Array.isArray(attr.Options)) {
                            options = attr.Options;
                        }
                    }

                    // 添加到 attr_data
                    products_obj.data_init.attr_data.push({
                        AttrId: attr.AttrId,
                        Name: attr.Name_en,
                        Position: attr.Position || index + 1,
                        Type: attr.Type || 'text',
                        Options: options
                    });

                } catch (error) {
                    // 处理属性时出错，跳过该属性
                }
            });

        } catch (error) {
            // 设置属性数据时出错
        }
    },

    /**
     * 生成筛选选项
     */
    generateFilterOptions: function() {
        try {
            if (!products_obj || !products_obj.data_init.attr_data || products_obj.data_init.attr_data.length === 0) {
                return;
            }

            let html = '';
            let repeatAry = {};

            // 统计选项重复次数
            $(products_obj.data_init.attr_data).each((index, element) => {
                $(element.Options).each((index2, element2) => {
                    if (typeof repeatAry[element2] == "undefined") {
                        repeatAry[element2] = 1;
                    } else {
                        repeatAry[element2] += 1;
                    }
                });
            });

            html += '<div class="filter_grid">';
            $(products_obj.data_init.attr_data).each((index, element) => {
                html += '<ul>';
                html += '<li class="title">' + element.Name + '</li>';
                $(element.Options).each((index2, element2) => {
                    html += '<li><a href="javascript:;" class="btn_warehouse" data-number="' + index + '" data-attr="' + this.htmlspecialchars(element.Name.toString()) + '" data-option="' + this.htmlspecialchars(element2.toString()) + '">' + element2 + '</a></li>';
                });
                html += '</ul>';
            });
            html += '</div>';
            html += '<a href="javascript:;" class="clear_option_filter">清除</a>';

            // 更新筛选框内容
            const filterContainer = $(".option_filter .inside_body .box_filter");
            if (filterContainer.length > 0) {
                filterContainer.html(html);
            } else {
                // 如果没有找到容器，尝试创建
                const insideBody = $(".option_filter .inside_body");
                if (insideBody.length > 0) {
                    insideBody.html('<div class="box_filter">' + html + '</div>');
                }
            }

            // 设置标题
            const titleSpan = $(".option_filter .inside_title span");
            if (titleSpan.length > 0) {
                titleSpan.html('选择规格');
            }

            // 显示筛选框
            const optionFilter = $('.option_filter');
            if (optionFilter.length > 0 && products_obj.data_init.attr_data.length > 0) {
                optionFilter.show();
            }

        } catch (error) {
            // 生成筛选选项时出错
        }
    },

    /**
     * HTML特殊字符转义
     */
    htmlspecialchars: function(str) {
        if (typeof str !== 'string') {
            return str;
        }
        return str
            .replace(/&/g, '&amp;')
            .replace(/</g, '&lt;')
            .replace(/>/g, '&gt;')
            .replace(/"/g, '&quot;')
            .replace(/'/g, '&#039;');
    },

    /**
     * 初始化筛选框回显
     */
    initFilterEcho: function() {
        // 从URL参数中获取当前的筛选条件
        const urlParams = new URLSearchParams(window.location.search);
        const cateId = urlParams.get('cateId');
        const tagId = urlParams.get('tagId');
        const minPrice = urlParams.get('minPrice');
        const maxPrice = urlParams.get('maxPrice');

        // 回显分类选择
        if (cateId) {
            this.echoCategorySelection(cateId);
        }

        // 回显标签选择
        if (tagId) {
            this.echoTagSelection(tagId);
        }

        // 回显价格范围
        if (minPrice || maxPrice) {
            this.echoPriceRange(minPrice, maxPrice);
        }
    },

    /**
     * 回显分类选择
     */
    echoCategorySelection: function(cateId) {
        try {
            // 查找分类筛选框
            const categoryBox = $('.fixed_search_filter .box_drop_double.edit_box');
            if (categoryBox.length > 0) {
                // 设置隐藏字段的值
                const hiddenInput = categoryBox.find('input[name="cateId"]');
                if (hiddenInput.length > 0) {
                    hiddenInput.val(cateId);
                }

                // 查找对应的分类名称并设置显示文本
                const dropList = categoryBox.find('.drop_list');
                if (dropList.length > 0) {
                    const dataStr = dropList.attr('data');
                    if (dataStr) {
                        try {
                            const categories = JSON.parse(dataStr);
                            const selectedCategory = categories.find(cat => cat.Value == cateId);
                            if (selectedCategory) {
                                const selectInput = categoryBox.find('input[name="Select"]');
                                if (selectInput.length > 0) {
                                    selectInput.val(selectedCategory.Name);
                                }
                            }
                        } catch (e) {
                            console.warn('解析分类数据失败:', e);
                        }
                    }
                }
            }
        } catch (error) {
            console.error('回显分类选择失败:', error);
        }
    },

    /**
     * 回显标签选择
     */
    echoTagSelection: function(tagId) {
        try {
            const tagIds = tagId.split(',').map(id => id.trim()).filter(id => id);
            if (tagIds.length === 0) return;

            // 查找标签筛选框
            const tagBox = $('.fixed_search_filter .box_drop_double[data-checkbox="1"]');
            if (tagBox.length > 0) {
                // 设置隐藏字段的值
                const hiddenInput = tagBox.find('input[name="tagId"]');
                if (hiddenInput.length > 0) {
                    hiddenInput.val(tagId);
                }

                // 查找对应的标签名称并设置选中状态
                const dropList = tagBox.find('.drop_list');
                if (dropList.length > 0) {
                    const dataStr = dropList.attr('data');
                    if (dataStr) {
                        try {
                            const tags = JSON.parse(dataStr);
                            const selectList = tagBox.find('.select_list');
                            const placeholder = tagBox.find('.select_placeholder');

                            // 清空现有选择
                            selectList.empty();

                            // 添加选中的标签
                            tagIds.forEach(selectedTagId => {
                                const selectedTag = tags.find(tag => tag.Value == selectedTagId);
                                if (selectedTag) {
                                    // 创建选中的标签元素
                                    const tagElement = $(`
                                        <span class="btn_attr_choice current">
                                            <input type="checkbox" name="products_tagsCurrent[]" value="${selectedTag.Value}" checked="checked" class="attr_current">
                                            ${selectedTag.Name}
                                            <i></i>
                                        </span>
                                    `);
                                    selectList.append(tagElement);
                                }
                            });

                            // 隐藏占位符
                            if (selectList.children().length > 0) {
                                placeholder.hide();
                            }
                        } catch (e) {
                            console.warn('解析标签数据失败:', e);
                        }
                    }
                }
            }
        } catch (error) {
            console.error('回显标签选择失败:', error);
        }
    },

    /**
     * 回显价格范围
     */
    echoPriceRange: function(minPrice, maxPrice) {
        try {
            // 设置最小价格
            if (minPrice) {
                const minPriceInput = $('.fixed_search_filter input[name="MinPrice"]');
                if (minPriceInput.length > 0) {
                    minPriceInput.val(minPrice);
                }
            }

            // 设置最大价格
            if (maxPrice) {
                const maxPriceInput = $('.fixed_search_filter input[name="MaxPrice"]');
                if (maxPriceInput.length > 0) {
                    maxPriceInput.val(maxPrice);
                }
            }
        } catch (error) {
            console.error('回显价格范围失败:', error);
        }
    },

    /**
     * 绑定筛选相关事件
     */
    bindFilterEvents: function() {
        // 监听筛选框打开事件
        $(document).on('click', '.filter_btn', () => {
            // 延迟执行回显，确保筛选框已经完全加载
            setTimeout(() => {
                this.initFilterEcho();
            }, 100);
        });

        // 监听筛选条件移除事件
        $(document).on('click', '.search_box_selected .btn_item_choice i', (e) => {
            e.preventDefault();
            e.stopPropagation();

            const filterItem = $(e.target).closest('.btn_item_choice');
            const filterName = filterItem.attr('data-name');

            if (filterName) {
                this.removeFilterCondition(filterName);
            }
        });

        // 规格筛选事件在initSpecificationFilter中延迟绑定
    },

    /**
     * 绑定规格筛选点击事件
     */
    bindSpecificationFilterEvents: function() {
        // 先解绑所有相关的事件，包括原始的products.js中的事件
        $('.option_filter .inside_body').off('click');
        $(document).off('click.specFilter', '.option_filter .inside_body .filter_grid li a');
        $(document).off('click.specFilterClear', '.option_filter .clear_option_filter');

        // 规格筛选点击事件
        $(document).on('click.specFilter', '.option_filter .inside_body .filter_grid li a', (e) => {
            e.preventDefault();
            e.stopPropagation();

            const $this = $(e.target);
            const $filterObj = $('.option_filter');
            const $show = ($('#AttrId_1').hasClass('show') ? 1 : 0);
            const $tbodyObj = $(`#AttrId_${$show} tbody:visible`);

            // 高亮切换
            $this.parent().siblings().find('a').removeClass('current');
            if ($this.hasClass('current')) {
                $this.removeClass('current');
            } else {
                $this.addClass('current');
            }

            // 全部取消勾选
            $tbodyObj.find('.input_checkbox_box').each((index, element) => {
                $(element).removeClass('checked').find('input').attr('checked', false);
            });

            // 获取已勾选的选项
            let optionFind = '';
            let optionAry = [];
            $filterObj.find('a.current').each((index, element) => {
                const $number = $(element).data('number');
                const $option = $(element).data('option');
                optionFind += `[data-attr-${$number}="${this.quotationMarksTransferred($option)}"]`;
                optionAry.push('<b>' + $(element).data('option') + '</b>');
            });

            // 勾选相应的规格
            if (optionFind) {
                $filterObj.addClass('lock'); // 锁住(筛选选项不能变动)
                const matchedElements = $tbodyObj.find(optionFind + ' .input_checkbox_box');
                matchedElements.trigger('click');
                $tbodyObj.find('tr').hide();
                $tbodyObj.find(optionFind).removeAttr('style');
                $filterObj.find('.inside_title>span').html(optionAry.join('<i>, </i>')); // 显示选中名字
                $filterObj.removeClass('lock'); // 解锁
            } else {
                const $extObj = $('.attribute_ext');
                $extObj.find('thead .btn_checkbox').removeClass('current indeterminate');
                $extObj.find('thead tr').removeClass('current');
                $extObj.find('thead .global_menu_button .open').addClass('no_select');
                $tbodyObj.find('tr').removeAttr('style');
                $filterObj.find('.inside_title>span').html('选择规格');
            }

            return false;
        });

        // 清除筛选事件
        $(document).on('click.specFilterClear', '.option_filter .clear_option_filter', (e) => {
            e.preventDefault();
            e.stopPropagation();

            const $filterObj = $('.option_filter');
            const $show = ($('#AttrId_1').hasClass('show') ? 1 : 0);
            const $tbodyObj = $(`#AttrId_${$show} tbody:visible`);
            const $extObj = $('.attribute_ext');

            // 全部取消勾选
            $tbodyObj.find('.input_checkbox_box').each((index, element) => {
                $(element).removeClass('checked').find('input').attr('checked', false);
            });

            // 清除所有
            $extObj.find('thead .btn_checkbox').removeClass('current indeterminate');
            $extObj.find('thead tr').removeClass('current');
            $extObj.find('thead .global_menu_button .open').addClass('no_select');
            $tbodyObj.find('tr').removeAttr('style');
            $filterObj.find('a.current').removeClass('current');
            $filterObj.find('.inside_title>span').html('选择规格');

            return false;
        });
    },

    /**
     * 引号转义处理
     */
    quotationMarksTransferred: function(str) {
        if (typeof str !== 'string') {
            return str;
        }
        return str.replace(/"/g, '\\"').replace(/'/g, "\\'");
    },

    /**
     * 移除筛选条件
     */
    removeFilterCondition: function(filterName) {
        try {
            const urlParams = new URLSearchParams(window.location.search);

            // 根据筛选类型移除对应的参数
            switch (filterName) {
                case 'cateId':
                    urlParams.delete('cateId');
                    break;
                case 'tagId':
                    urlParams.delete('tagId');
                    break;
                case 'minPrice|maxPrice':
                    urlParams.delete('minPrice');
                    urlParams.delete('maxPrice');
                    break;
            }

            // 重新构建URL并跳转
            const newUrl = window.location.pathname + (urlParams.toString() ? '?' + urlParams.toString() : '');
            window.location.href = newUrl;
        } catch (error) {
            // 移除筛选条件失败
        }
    },

    /**
     * 更新搜索框选中状态显示
     */
    updateSearchBoxSelected: function() {
        // 这个方法可以用来动态更新选中状态的显示
        // 当前通过后端模板已经处理了显示问题，这里保留接口以备将来使用
    }
};

// 自动初始化
ProductFilterFix.init();