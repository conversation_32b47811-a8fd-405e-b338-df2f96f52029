/**
 * 全选复选框修复脚本
 * 修复 btn_checkbox 只能点击一次，再次点击不能取消全选的问题
 * 
 * 问题分析：
 * 1. 源码中的逻辑判断有误：当btn_checkbox有"current"类时，应该是已选中状态，点击应该取消全选
 * 2. 源码中没有正确切换btn_checkbox的"current"类状态
 * 3. 需要重新绑定事件处理器，覆盖原有的错误逻辑
 */

var CheckboxSelectAllFix = (function () {
    
    /**
     * 初始化修复
     */
    function init() {
        // 等待DOM完全加载后再执行修复
        $(document).ready(function() {
            setTimeout(function() {
                fixAttributeTableSelectAll();
                // 强制覆盖原有事件处理器
                overrideOriginalEvents();
            }, 1000); // 延长等待时间确保原有脚本加载完成
        });
    }

    /**
     * 强制覆盖原有的事件处理器
     */
    function overrideOriginalEvents() {
        // 只移除全选按钮的事件绑定，保留单选按钮的原有逻辑
        $('.attribute_ext thead .btn_checkbox').off('click');

        // 直接在元素上绑定事件，优先级更高
        bindEventsToElements();

        // 设置MutationObserver监听DOM变化，确保动态添加的元素也能被修复
        setupMutationObserver();
    }

    /**
     * 绑定事件到现有元素
     */
    function bindEventsToElements() {
        // 只修复全选按钮，单选按钮保持原有逻辑
        $('.attribute_ext thead .btn_checkbox').off('click').on('click', function(e) {
            return handleSelectAllClick.call(this, e);
        });

        // 不再覆盖单选按钮的事件，让它们使用原有的逻辑
        // 但是我们需要监听单选按钮的变化来更新全选按钮状态
        monitorSingleCheckboxChanges();
    }

    /**
     * 监听单选按钮的变化，更新全选按钮状态
     */
    function monitorSingleCheckboxChanges() {
        // 使用事件委托监听单选按钮的变化
        $(document).off('click.singleCheckboxMonitor', '.attribute_ext tbody .input_checkbox_box');
        $(document).on('click.singleCheckboxMonitor', '.attribute_ext tbody .input_checkbox_box', function() {
            var $checkbox = $(this);
            var $mainObj = $checkbox.closest('.attribute_ext');
            var $tbodyObj = $mainObj.find('tbody:visible');
            var $headerCheckbox = $mainObj.find('thead .btn_checkbox');

            // 延迟执行，让原有逻辑先执行
            setTimeout(function() {
                updateHeaderCheckboxStateFromSingle($mainObj, $tbodyObj, $headerCheckbox);
            }, 10);
        });
    }

    /**
     * 根据单选按钮状态更新全选按钮状态
     */
    function updateHeaderCheckboxStateFromSingle($mainObj, $tbodyObj, $headerCheckbox) {
        var $allCheckboxes = $tbodyObj.find('.input_checkbox_box');
        var $checkedCheckboxes = $tbodyObj.find('.input_checkbox_box.checked');
        var checkedCount = $checkedCheckboxes.length;
        var totalCount = $allCheckboxes.length;

        if (checkedCount === 0) {
            // 没有选中任何项
            $headerCheckbox.removeClass('current indeterminate');
            $headerCheckbox.find('input').prop('checked', false);
        } else if (checkedCount === totalCount) {
            // 全部选中
            $headerCheckbox.addClass('current').removeClass('indeterminate');
            $headerCheckbox.find('input').prop('checked', true);
        } else {
            // 部分选中
            $headerCheckbox.removeClass('current').addClass('indeterminate');
            $headerCheckbox.find('input').prop('checked', false);
        }
    }

    /**
     * 设置DOM变化监听器
     */
    function setupMutationObserver() {
        if (typeof MutationObserver !== 'undefined') {
            var observer = new MutationObserver(function(mutations) {
                var needsRebind = false;
                mutations.forEach(function(mutation) {
                    if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
                        for (var i = 0; i < mutation.addedNodes.length; i++) {
                            var node = mutation.addedNodes[i];
                            if (node.nodeType === 1) { // Element node
                                if ($(node).find('.attribute_ext').length > 0 || $(node).hasClass('attribute_ext')) {
                                    needsRebind = true;
                                    break;
                                }
                            }
                        }
                    }
                });

                if (needsRebind) {
                    setTimeout(bindEventsToElements, 100);
                }
            });

            observer.observe(document.body, {
                childList: true,
                subtree: true
            });
        }
    }
    
    /**
     * 修复属性表格的全选功能
     */
    function fixAttributeTableSelectAll() {
        // 这个函数现在主要用于初始化，实际的事件处理由overrideOriginalEvents完成
    }
    
    /**
     * 更新表头全选按钮的状态
     */
    function updateHeaderCheckboxState($mainObj, $tbodyObj, $headerCheckbox) {
        var $allCheckboxes = $tbodyObj.find('.input_checkbox_box');
        var $checkedCheckboxes = $tbodyObj.find('.input_checkbox_box.checked');
        var checkedCount = $checkedCheckboxes.length;
        var totalCount = $allCheckboxes.length;

        if (checkedCount === 0) {
            // 没有选中任何项
            $headerCheckbox.removeClass('current indeterminate');
            $headerCheckbox.find('input').prop('checked', false);
        } else if (checkedCount === totalCount) {
            // 全部选中
            $headerCheckbox.addClass('current').removeClass('indeterminate');
            $headerCheckbox.find('input').prop('checked', true);
        } else {
            // 部分选中
            $headerCheckbox.removeClass('current').addClass('indeterminate');
            $headerCheckbox.find('input').prop('checked', false);
        }

        // 更新计数显示
        updateSelectedCount($mainObj, checkedCount);
    }
    
    /**
     * 处理全选按钮点击
     */
    function handleSelectAllClick(e) {
        e.preventDefault();
        e.stopPropagation();

        var $btn = $(this);
        var $mainObj = $btn.closest('.attribute_ext');
        var $tbodyObj = $mainObj.find('tbody:visible');
        var $tbodyAllObj = $mainObj.find('tbody');
        var $checkboxes = $tbodyObj.find('.input_checkbox_box');

        // 检查当前状态
        var isCurrentlySelected = $btn.hasClass('current');

        if (isCurrentlySelected) {
            // 取消全选
            $btn.removeClass('current indeterminate');
            $btn.find('input').prop('checked', false);

            $tbodyAllObj.find('.input_checkbox_box').each(function() {
                $(this).removeClass('checked');
                $(this).find('input').prop('checked', false);
            });

            updateSelectedCount($mainObj, 0);
        } else {
            // 全选
            $btn.addClass('current').removeClass('indeterminate');
            $btn.find('input').prop('checked', true);

            $checkboxes.each(function() {
                $(this).addClass('checked');
                $(this).find('input').prop('checked', true);
            });

            updateSelectedCount($mainObj, $checkboxes.length);
        }

        // 处理规格筛选逻辑（从原始代码复制）
        handleSpecificationFilter();

        return false;
    }

    /**
     * 处理规格筛选逻辑（从原始源码复制）
     */
    function handleSpecificationFilter() {
        // 规格筛选 (归元)
        var $filterObj = $('.option_filter');
        if (!$filterObj.hasClass('lock')) {
            // 没锁住
            if (typeof lang_obj !== 'undefined' && lang_obj.manage && lang_obj.manage.products) {
                $filterObj.find('.inside_title>span').html(lang_obj.manage.products.choose_specifications);
            }
            $filterObj.find('a.current').removeClass('current');
            var $show = ($('#AttrId_1').hasClass('show') ? 1 : 0);
            var $tbodyObj = $('#AttrId_' + $show + ' tbody:visible');
            $tbodyObj.find('tr').removeAttr('style');
        }
    }

    /**
     * 更新选中数量显示
     */
    function updateSelectedCount($mainObj, count) {
        if (count > 0) {
            $(".attribute_ext thead tr").addClass("current");
            $(".attribute_ext thead .global_menu_button .open").removeClass('no_select');
            $(".attribute_ext thead .global_menu_button .open>span").text(count);
        } else {
            $(".attribute_ext thead tr").removeClass("current");
            $(".attribute_ext thead .global_menu_button .open").addClass('no_select');
            $(".attribute_ext thead .global_menu_button .open>span").text('');
        }
    }
    
    // 公开接口
    return {
        init: init
    };
})();

// 自动初始化
CheckboxSelectAllFix.init();
