/**
 * 产品关键词修复模块
 * 用于解决SEO关键词修改后提交旧数据的问题
 */

var productKeywordFixModule = (function () {
    // 初始化函数
    function init() {
        // 监听关键词编辑表单提交按钮点击事件
        $(document).on('click', '#edit_keyword_form .btn_submit', handleKeywordSubmit);
        
        // 监听关键词输入框回车事件（直接添加关键词时）
        $(document).on('keydown', '.option_selected[data-type="seo_keyword"] input[name="_Option"]', handleKeywordInput);
        
        // 在调用toSaveProduct前同步一次关键词
        const originalSaveProduct = window.toSaveProduct;
        if (typeof originalSaveProduct === 'function') {
            window.toSaveProduct = function() {
                // 在保存前同步一次关键词
                syncKeywords();
                // 调用原始保存方法
                return originalSaveProduct.apply(this, arguments);
            };
        }
    }
    
    /**
     * 处理关键词编辑表单提交
     * @param {Event} e - 事件对象
     */
    function handleKeywordSubmit(e) {
        // 原按钮功能执行后，添加我们的同步逻辑
        setTimeout(function() {
            syncKeywords();
        }, 100);
    }
    
    /**
     * 处理直接在页面上输入关键词的情况
     * @param {Event} e - 事件对象
     */
    function handleKeywordInput(e) {
        // 按下回车或逗号后，添加延迟同步
        const key = e.keyCode || e.which;
        if (key === 13 || key === 188) {
            setTimeout(function() {
                syncKeywords();
            }, 100);
        }
    }
    
    /**
     * 同步页面上展示的关键词到隐藏字段
     */
    function syncKeywords() {
        // 收集所有关键词
        const keywords = [];
        $('.option_selected[data-type="seo_keyword"] .select_list .btn_attr_choice b').each(function() {
            keywords.push($(this).text().trim());
        });
        
        // 更新隐藏字段
        if (keywords.length > 0) {
            const keywordString = keywords.join(',');
            $('input[name="SeoKeyword_en"]').val(keywordString);
            $('input[name="SeoKeyword_en_value"]').val(keywordString);
        }
    }
    
    // 返回公共接口
    return {
        init: init,
        syncKeywords: syncKeywords
    };
})();

// 初始化模块
jQuery(function () {
    productKeywordFixModule.init();
}); 