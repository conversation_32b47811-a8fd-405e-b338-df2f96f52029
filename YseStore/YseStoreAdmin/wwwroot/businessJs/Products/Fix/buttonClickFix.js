/**
 * 按钮点击事件修复模块
 * 用于修复table_menu_button、global_menu_button和upload_select_menu的点击事件问题
 */
var ButtonClickFix = (function() {
    /**
     * 初始化
     */
    function init() {
        // 页面加载完成后执行修复
        $(document).ready(function() {
            fixTableMenuButton();
            fixGlobalMenuButton();
            fixUploadSelectMenu();
            fixDelBatButtons();

            // 特别处理产品图片选择状态
            setTimeout(function() {
                checkAndUpdateImageSelectStatus();
                fixImageSelectionDisplay();

                // 强制绑定全选按钮点击事件
                bindSelectAllButtonClick();

                // 特别处理ProductCateEdit页面的复选框
                fixProductCateEditCheckbox();
            }, 300);

            // 监听DOM变化，处理动态加载的元素
            observeDOMChanges();
        });
    }

    /**
     * 修复ProductCateEdit页面的复选框功能
     */
    function fixProductCateEditCheckbox() {
        // 检查是否在ProductCateEdit页面
        if ($('.products_list[data-page]').length > 0) {
            // 强制绑定全选按钮事件
            $(document).off('click', '.table_menu_button.global_menu_button .btn_checkbox')
                .on('click', '.table_menu_button.global_menu_button .btn_checkbox', function(e) {
                e.preventDefault();
                e.stopPropagation();

                var $btn = $(this);
                var isChecked = $btn.hasClass('current');
                var $checkboxes = $('.r_con_table tbody input[type="checkbox"][name="select"]:not(:disabled)');

                if (isChecked) {
                    // 取消全选
                    $btn.removeClass('current indeterminate');
                    $('.r_con_table tbody .btn_checkbox').removeClass('current');
                    $checkboxes.prop('checked', false);
                } else {
                    // 全选
                    $btn.addClass('current').removeClass('indeterminate');
                    $('.r_con_table tbody .btn_checkbox').addClass('current');
                    $checkboxes.prop('checked', true);
                }

                // 触发change事件
                $checkboxes.trigger('change');

                // 更新选中数量显示
                var count = $checkboxes.filter(':checked').length;
                $('.table_menu_button.global_menu_button .open span').text(count);

                // 更新按钮状态
                updateBatchButtonStatus();

                return false;
            });
        }
    }
    
    /**
     * 修复table_menu_button点击事件
     */
    function fixTableMenuButton() {
        // 解绑旧事件，绑定新事件 - 支持table_menu_button和global_menu_button
        $(document).off('click', '.table_menu_button .btn_checkbox, .global_menu_button .btn_checkbox')
            .on('click', '.table_menu_button .btn_checkbox, .global_menu_button .btn_checkbox', function(e) {
            e.preventDefault();
            e.stopPropagation();

            var isChecked = $(this).hasClass('current');
            var $checkboxes = $('.r_con_table tbody input[type="checkbox"][name="select"]:not(:disabled)');

            if (isChecked) {
                // 取消全选
                $(this).removeClass('current indeterminate');
                $('.r_con_table tbody .btn_checkbox').removeClass('current');
                $checkboxes.prop('checked', false);
            } else {
                // 全选
                $(this).addClass('current').removeClass('indeterminate');
                $('.r_con_table tbody .btn_checkbox').addClass('current');
                $checkboxes.prop('checked', true);
            }

            // 触发每个选中状态改变的事件，以便其他函数能响应
            $checkboxes.trigger('change');

            // 更新选中数量
            var count = $('.r_con_table tbody input[type="checkbox"][name="select"]:checked').length;
            $('.table_menu_button .open span, .global_menu_button .open span').text(count);

            // 更新按钮状态
            updateBatchButtonStatus();

            return false;
        });
        
        // 监听每个单选框的变化，更新全选按钮状态
        $(document).off('change', '.r_con_table tbody input[type="checkbox"][name="select"]')
            .on('change', '.r_con_table tbody input[type="checkbox"][name="select"]', function() {
                var $all = $('.r_con_table tbody input[type="checkbox"][name="select"]:not(:disabled)');
                var $checked = $('.r_con_table tbody input[type="checkbox"][name="select"]:checked');
                var $headerCheckbox = $('.table_menu_button .btn_checkbox, .global_menu_button .btn_checkbox');

                if ($checked.length === 0) {
                    // 没有选中
                    $headerCheckbox.removeClass('current indeterminate');
                } else if ($checked.length === $all.length) {
                    // 全部选中
                    $headerCheckbox.addClass('current').removeClass('indeterminate');
                } else {
                    // 部分选中
                    $headerCheckbox.removeClass('current').addClass('indeterminate');
                }

                // 更新选中数量
                $('.table_menu_button .open span, .global_menu_button .open span').text($checked.length);

                // 更新按钮状态
                updateBatchButtonStatus();
            });
        
        // 修复更多菜单按钮
        $(document).off('click', '.table_menu_button .more').on('click', '.table_menu_button .more', function(e) {
            e.stopPropagation();
            $(this).siblings('.more_menu').toggle();
            return false;
        });
        
        // 点击外部区域关闭菜单
        $(document).on('click', function() {
            $('.table_menu_button .more_menu').hide();
        });
    }
    
    /**
     * 修复global_menu_button点击事件
     */
    function fixGlobalMenuButton() {
        // 解绑旧事件，绑定新事件
        $(document).off('click', '.global_menu_button .open').on('click', '.global_menu_button .open', function(e) {
            e.stopPropagation();
            
            const $tr = $(this).closest('tr,.tr');
            
            // 切换当前行状态
            if ($tr.hasClass('current')) {
                $tr.removeClass('current');
                $(this).addClass('no_select');
            } else {
                $tr.addClass('current');
                $(this).removeClass('no_select');
            }
            
            // 显示操作按钮
            const $menu = $(this).siblings('.drop_down');
            $menu.toggle();
            
            // 点击外部区域关闭菜单
            $(document).one('click', function() {
                $('.drop_down').hide();
            });
            
            return false;
        });
    }
    
    /**
     * 修复批量删除按钮相关功能
     */
    function fixDelBatButtons() {
        // 监听表格中的复选框变化，更新批量删除按钮状态
        $(document).on('change', 'input[name=select]', function() {
            updateBatchButtonStatus();
        });
        
        // 监听全选复选框变化，更新批量删除按钮状态
        $(document).on('change', 'input[name=select_all]', function() {
            updateBatchButtonStatus();
        });
        
        // 处理行点击时触发的复选框选中事件
        $(document).on('click', '.r_con_table tbody tr', function(e) {
            if ($(e.target).is('a, button, input, select, textarea, .btn_checkbox, label, .operation, .no_select_tr') || 
                $(e.target).parents('.operation, .no_select_tr').length > 0) {
                return;
            }
            
            let checkbox = $(this).find('input[name=select]');
            if (checkbox.length > 0 && !checkbox.prop('disabled')) {
                checkbox.prop('checked', !checkbox.prop('checked'));
                checkbox.trigger('change');
            }
        });
        
        // 修复批量删除按钮点击事件
        $(document).on('click', '.table_menu_button .del, .global_menu_button .del', function(e) {
            if ($(this).hasClass('no_select')) {
                e.stopImmediatePropagation();
                global_obj.win_alert(lang_obj.global.del_dat_select);
                return false;
            }
        });
        
        // 监听删除操作完成后的事件
        $(document).ajaxComplete(function(event, xhr, settings) {
            if (settings.url && (settings.url.indexOf('/delete') > -1 || settings.url.indexOf('/del') > -1)) {
                // 删除操作完成后，更新按钮状态
                setTimeout(function() {
                    updateBatchButtonStatus();
                }, 500);
            }
        });
        
        // 初始调用一次更新状态
        updateBatchButtonStatus();
    }
    
    /**
     * 更新批量按钮状态
     */
    function updateBatchButtonStatus() {
        // 延迟执行以确保复选框状态已更新
        setTimeout(function() {
            // 获取所有选中的复选框数量
            var checkedCount = $('input[name=select]:checked').length;
            
            // 更新表格菜单按钮状态
            if (checkedCount > 0) {
                $('.table_menu_button .del, .global_menu_button .del, .table_menu_button .batch_del_btn, .global_menu_button .batch_del_btn').removeClass('no_select');
                $('.table_menu_button .open, .global_menu_button .open').removeClass('no_select');
                $('.table_menu_button .open>span, .global_menu_button .open>span').text(checkedCount);
                $('.table_menu_button, .global_menu_button').parents('tr,.tr').addClass('current');
            } else {
                $('.table_menu_button .del, .global_menu_button .del, .table_menu_button .batch_del_btn, .global_menu_button .batch_del_btn').addClass('no_select');
                $('.table_menu_button .open, .global_menu_button .open').addClass('no_select');
                $('.table_menu_button .open>span, .global_menu_button .open>span').text('0');
                $('.table_menu_button, .global_menu_button').parents('tr,.tr').removeClass('current');
            }
            
            // 更新所有批量操作按钮状态
            if (checkedCount > 0) {
                $('.table_menu_button .btn_batch, .table_menu_button .sold_in, .table_menu_button .sold_out, .table_menu_button .facebook_release, .table_menu_button .facebook_delete, .table_menu_button .change_status_paid, .table_menu_button .change_status_success, .table_menu_button .btn_unused, .table_menu_button .btn_used, .table_menu_button .batch_price').removeClass('no_select');
            } else {
                $('.table_menu_button .btn_batch, .table_menu_button .sold_in, .table_menu_button .sold_out, .table_menu_button .facebook_release, .table_menu_button .facebook_delete, .table_menu_button .change_status_paid, .table_menu_button .change_status_success, .table_menu_button .btn_unused, .table_menu_button .btn_used, .table_menu_button .batch_price').addClass('no_select');
            }
        }, 50);
    }
    
    /**
     * 修复upload_select_menu功能
     */
    function fixUploadSelectMenu() {
        // 移除之前可能的事件绑定
        $(document).off('click', '.global_container[data-name="pic_info"] .input_checkbox_box');
        $(document).off('click', '.global_container[data-name="pic_info"] .btn_checkbox');
        $(document).off('click', '.upload_select_menu .btn_checkbox');
        $(document).off('click', '.batch_delete_pictrue');
        $('.global_container[data-name="pic_info"]').off('click', '.input_checkbox_box, .btn_checkbox');
        $('.global_container[data-name="pic_info"]').off('click', '.batch_delete_pictrue');
        $('.upload_select_menu').off('click', '.btn_checkbox');
        $('.upload_select_menu .btn_checkbox').off('click');
        $('.upload_select_menu label').off('click');
        
        // 直接绑定到图片上的点击事件
        $(document).on('click', '.global_container[data-name="pic_info"] .pro_multi_img .img.isfile', function(e) {
            // 如果点击的是复选框或其内部元素，不处理
            if ($(e.target).hasClass('input_checkbox_box') || $(e.target).closest('.input_checkbox_box').length) {
                return;
            }
            
            // 切换选中状态
            let $checkbox = $(this).find('.input_checkbox_box');
            if ($checkbox.length) {
                if ($checkbox.hasClass('checked')) {
                    // 取消选中
                    $checkbox.removeClass('checked').find('input').prop('checked', false);
                    $(this).removeClass('isshow');
                } else {
                    // 选中
                    $checkbox.addClass('checked').find('input').prop('checked', true);
                    $(this).addClass('isshow');
                }
                
                // 更新选中数量和按钮状态
                let $checkedCount = $('.global_container[data-name="pic_info"] .pro_multi_img .input_checkbox_box.checked').length;
                updateImageSelectStatus($checkedCount);
                
                // 更新全选按钮状态
                updateSelectAllButtonStatus();
            }
        });
        
        // 使用直接的方式绑定全选按钮点击事件
        $('.upload_select_menu .btn_checkbox').on('click', function(e) {
         
            e.preventDefault();
            e.stopPropagation();
            
            forceToggleSelectAll();
            
            return false;
        });
        
        // 额外绑定全选按钮的label点击事件
        $('.upload_select_menu label').on('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            
            forceToggleSelectAll();
            
            return false;
        });
        
        // 绑定单个复选框点击事件
        $(document).on('click', '.global_container[data-name="pic_info"] .pro_multi_img .input_checkbox_box', function(e) {
            e.stopPropagation();
            
            let $mainObj = $('.global_container[data-name="pic_info"]');
            let $listObj = $mainObj.find('.pro_multi_img');
            let $count = $listObj.find('.input_checkbox_box').length;
            
            // 切换选中状态
            if ($(this).hasClass('checked')) {
                // 取消选中
                $(this).removeClass('checked').find('input').prop('checked', false);
                $(this).parents('.img.isfile').removeClass('isshow');
            } else {
                // 选中
                $(this).addClass('checked').find('input').prop('checked', true);
                $(this).parents('.img.isfile').addClass('isshow');
            }
            
            // 更新选中数量
            let $checkedCount = $listObj.find('.input_checkbox_box.checked').length;
            
            // 更新全选按钮状态
            updateSelectAllButtonStatus();
            
            // 更新按钮状态
            updateImageSelectStatus($checkedCount);
            
            return false;
        });
        
        // 批量删除产品主图
        $(document).off('click', '.batch_delete_pictrue').on('click', '.batch_delete_pictrue', function(e) {
            e.preventDefault();
            e.stopPropagation();
            
            let $listObj = $(".global_container[data-name=pic_info] .pro_multi_img .input_checkbox_box.checked");
            
            if ($listObj.length) {
                let params = {
                    'title': lang_obj.global.del_confirm,
                    'confirmBtn': lang_obj.global.del,
                    'confirmBtnClass': 'btn_warn'
                };
                
                global_obj.win_alert(params, function() {
                    // 删除图片
                    $listObj.each(function() {
                        $(this).parents('.img').remove();
                    });
                    
                    frame_obj.upload_pro_img_init(1);
                    
                    let $i = 0;
                    $('.global_container[data-name=pic_info] .pro_multi_img').find('.img').each(function() {
                        $(this).attr('num', $i);
                        ++$i;
                    });
                    
                    if ($('.view_more_image').length) {
                        $('.view_more_image').trigger('click');
                    }
                    
                    if (typeof products_obj !== 'undefined' && products_obj.function_init) {
                        products_obj.function_init.show_btn_init();
                        products_obj.function_init.read_all_image_data();
                    }
                    
                    // 最后去掉全选
                    $('.upload_select_menu .btn_checkbox').removeClass('current indeterminate').find('input').prop('checked', false);
                    updateImageSelectStatus(0);
                    
                    // 提示成功
                    global_obj.win_alert_auto_close(lang_obj.manage.global.del_success, '', 1000, '8%');
                }, 'confirm');
            } else {
                global_obj.win_alert(lang_obj.global.del_dat_select);
            }
            
            return false;
        });
        
        // 初始化检查是否有选中的图片
        checkAndUpdateImageSelectStatus();
        
        // 监听DOM变化，处理动态加载的图片
        const imageObserver = new MutationObserver(function() {
            checkAndUpdateImageSelectStatus();
        });
        
        // 配置观察选项
        const config = { 
            childList: true,
            subtree: true,
            attributes: true
        };
        
        // 开始观察文档中的图片容器
        const imgContainer = document.querySelector('.global_container[data-name="pic_info"] .pro_multi_img');
        if (imgContainer) {
            imageObserver.observe(imgContainer, config);
        }
        
        // 在初始化时添加一个直接的点击处理程序
        setTimeout(function() {
            // 强制绑定全选按钮点击事件
            bindSelectAllButtonClick();
        }, 500);
    }
    
    /**
     * 切换全选/取消全选状态
     * @param {jQuery} $btn 全选按钮元素
     */
    function toggleSelectAll($btn) {
        let $mainObj = $('.global_container[data-name="pic_info"]');
        let $listObj = $mainObj.find('.pro_multi_img');
        let $isChecked = $btn.hasClass('current');
        

        
        // 强制切换状态，不依赖当前状态
        let $checkedCount = $listObj.find('.input_checkbox_box.checked').length;
        let $totalCount = $listObj.find('.input_checkbox_box').length;
        
        // 如果所有项都已选中或部分选中，则执行取消全选
        if ($checkedCount > 0) {

            // 取消全选
            $btn.removeClass('current indeterminate');
            $listObj.find('.img.isfile').each(function() {
                $(this).removeClass('isshow');
                $(this).find('.input_checkbox_box').removeClass('checked').find('input').prop('checked', false);
            });
            updateImageSelectStatus(0);
        } else {
            // 全选
      
            $btn.addClass('current').removeClass('indeterminate');
            $listObj.find('.img.isfile').each(function() {
                $(this).addClass('isshow');
                $(this).find('.input_checkbox_box').addClass('checked').find('input').prop('checked', true);
            });
            $checkedCount = $listObj.find('.input_checkbox_box.checked').length;
            updateImageSelectStatus($checkedCount);
        }
        
        if ($('.view_more_image').length) {
            $('.view_more_image').trigger('click');
        }
    }
    
    /**
     * 更新全选按钮状态
     */
    function updateSelectAllButtonStatus() {
        let $mainObj = $('.global_container[data-name="pic_info"]');
        let $listObj = $mainObj.find('.pro_multi_img');
        let $btn = $('.upload_select_menu .btn_checkbox');
        
        // 获取实际图片数量（排除上传按钮）
        let $totalCount = $listObj.find('.img.isfile').length;
        let $checkedCount = $listObj.find('.input_checkbox_box.checked').length;
        
        if ($checkedCount === 0) {
            // 没有选中项
            $btn.removeClass('current indeterminate');
        } else if ($checkedCount === $totalCount && $totalCount > 0) {
            // 全部选中
            $btn.addClass('current').removeClass('indeterminate');
        } else if ($checkedCount > 0 && $checkedCount < $totalCount) {
            // 部分选中
            $btn.removeClass('current').addClass('indeterminate');
        }
    }
    
    /**
     * 检查并更新图片选择状态
     */
    function checkAndUpdateImageSelectStatus() {
        let $mainObj = $('.global_container[data-name="pic_info"]');
        let $listObj = $mainObj.find('.pro_multi_img');
        let $checkedCount = $listObj.find('.input_checkbox_box.checked').length;
        
        // 更新图片选择状态
        updateImageSelectStatus($checkedCount);
        
        // 更新全选按钮状态
        updateSelectAllButtonStatus();
    }
    
    /**
     * 更新图片选择状态
     * @param {number} checkedCount 选中的图片数量
     */
    function updateImageSelectStatus(checkedCount) {
        if (checkedCount > 0) {
            $('.upload_select_menu').addClass('current');
            $('.upload_select_menu .open').removeClass('no_select').text(`已选择${checkedCount}个`);
            // 显示删除按钮
            showDeleteButton();
        } else {
            $('.upload_select_menu').removeClass('current');
            $('.upload_select_menu .open').addClass('no_select').text('全选');
            // 隐藏删除按钮
            hideDeleteButton();
        }
    }
    
    /**
     * 显示删除按钮
     */
    function showDeleteButton() {
        // 确保删除按钮存在
        if ($('.batch_delete_pictrue').length === 0) {
            // 如果不存在，创建删除按钮
            let $deleteBtn = $('<a href="javascript:;" class="batch_delete_pictrue">删除</a>');
            $('.upload_select_menu').append($deleteBtn);
        } else {
            // 显示现有的删除按钮
            $('.batch_delete_pictrue').show();
        }
    }
    
    /**
     * 隐藏删除按钮
     */
    function hideDeleteButton() {
        $('.batch_delete_pictrue').hide();
    }
    
    /**
     * 监听DOM变化，处理动态加载的元素
     */
    function observeDOMChanges() {
        // 创建MutationObserver实例
        const observer = new MutationObserver(function(mutations) {
            mutations.forEach(function(mutation) {
                // 检查是否有新的按钮元素被添加
                if (mutation.addedNodes && mutation.addedNodes.length > 0) {
                    for (let i = 0; i < mutation.addedNodes.length; i++) {
                        const node = mutation.addedNodes[i];
                        
                        // 检查是否是元素节点
                        if (node.nodeType === 1) {
                            // 检查是否包含目标按钮
                            if ($(node).find('.table_menu_button').length || 
                                $(node).find('.global_menu_button').length || 
                                $(node).find('.upload_select_menu').length ||
                                $(node).find('.pic_detail_list').length ||
                                $(node).hasClass('global_container') ||
                                $(node).find('.global_container[data-name="pic_info"]').length) {
                                
                                // 重新应用修复
                                fixTableMenuButton();
                                fixGlobalMenuButton();
                                fixUploadSelectMenu();
                                fixDelBatButtons();
                            }
                        }
                    }
                }
            });
        });
        
        // 配置观察选项
        const config = { 
            childList: true,
            subtree: true
        };
        
        // 开始观察文档
        observer.observe(document.body, config);
    }
    
    /**
     * 修复图片选择显示
     */
    function fixImageSelectionDisplay() {
        // 检查是否有选中的图片
        let isAnyImageSelected = false;
        
        // 检查图片是否有选中样式
        $('.global_container[data-name="pic_info"] .pro_multi_img .img').each(function() {
            if ($(this).hasClass('isshow') || $(this).find('.input_checkbox_box').hasClass('checked')) {
                isAnyImageSelected = true;
                return false; // 跳出循环
            }
        });
        
        // 如果有选中的图片，但没有显示删除按钮，则强制显示
        if (isAnyImageSelected) {
            let checkedCount = $('.global_container[data-name="pic_info"] .pro_multi_img .input_checkbox_box.checked').length;
            if (checkedCount === 0) {
                // 可能是样式问题，重新计算选中数量
                checkedCount = $('.global_container[data-name="pic_info"] .pro_multi_img .img.isshow').length;
            }
            
            if (checkedCount > 0) {
                $('.upload_select_menu').addClass('current');
                $('.upload_select_menu .open').removeClass('no_select').text(`已选择${checkedCount}个`);
                showDeleteButton();
            }
        }
    }
    
    /**
     * 绑定全选按钮点击事件
     */
    function bindSelectAllButtonClick() {
        // 移除所有可能的事件
        $('.upload_select_menu .btn_checkbox').off('click');
        $('.upload_select_menu label').off('click');
        
        // 直接绑定点击事件
        $('.upload_select_menu .btn_checkbox').on('click', function(e) {
    
            e.preventDefault();
            e.stopPropagation();
            
            forceToggleSelectAll();
            
            return false;
        });
        
        // 绑定label点击事件
        $('.upload_select_menu label').on('click', function(e) {
         
            e.preventDefault();
            e.stopPropagation();
            
            forceToggleSelectAll();
            
            return false;
        });
        
        // 额外添加一个点击处理器到父元素
        $('.upload_select_menu').off('click.selectAllForce').on('click.selectAllForce', function(e) {
            if ($(e.target).hasClass('btn_checkbox') || $(e.target).closest('.btn_checkbox').length) {
              
                e.preventDefault();
                e.stopPropagation();
                
                forceToggleSelectAll();
                
                return false;
            }
        });
    }
    
    /**
     * 强制切换全选状态
     */
    function forceToggleSelectAll() {
        let $mainObj = $('.global_container[data-name="pic_info"]');
        let $listObj = $mainObj.find('.pro_multi_img');
        let $btn = $('.upload_select_menu .btn_checkbox');
        let $checkedCount = $listObj.find('.input_checkbox_box.checked').length;
        
        
        // 如果有选中项，则取消全选；否则执行全选
        if ($checkedCount > 0) {
          
            // 取消全选
            $btn.removeClass('current indeterminate');
            $listObj.find('.img.isfile').each(function() {
                $(this).removeClass('isshow');
                $(this).find('.input_checkbox_box').removeClass('checked').find('input').prop('checked', false);
            });
            updateImageSelectStatus(0);
        } else {
      
            // 全选
            $btn.addClass('current').removeClass('indeterminate');
            $listObj.find('.img.isfile').each(function() {
                $(this).addClass('isshow');
                $(this).find('.input_checkbox_box').addClass('checked').find('input').prop('checked', true);
            });
            $checkedCount = $listObj.find('.input_checkbox_box.checked').length;
            updateImageSelectStatus($checkedCount);
        }
    }
    
    // 返回公共接口
    return {
        init: init
    };
})();

// 自动初始化
ButtonClickFix.init(); 