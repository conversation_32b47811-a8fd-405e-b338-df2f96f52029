/* 产品编辑页面标签滚动样式 */

/* 标签选择区域的基础样式 */
.tags_row .box_option_list {
    position: relative;
}

/* 已选择标签区域 - 设置最大高度和滚动 */
.tags_row .box_option_list .option_selected .select_list {
    max-height: 120px; /* 最大高度约4行标签 */
    overflow-y: auto;
    overflow-x: hidden;
    padding-right: 5px; /* 为滚动条留出空间 */
    margin-bottom: 8px;
    display: flex;
    flex-wrap: wrap;
    gap: 6px;
}

/* 可选标签下拉区域 - 设置最大高度和滚动 */
.tags_row .box_option_list .option_not_yet {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    z-index: 1000;
    background: #fff;
    border: 1px solid #ccdced;
    border-radius: 5px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    margin-top: 2px;
}

.tags_row .box_option_list .option_not_yet .select_list {
    max-height: 200px; /* 最大高度约6-7行标签 */
    overflow-y: auto;
    overflow-x: hidden;
    padding: 8px 10px;
    display: flex;
    flex-wrap: wrap;
    gap: 6px;
}

/* 自定义滚动条样式 */
.tags_row .box_option_list .select_list::-webkit-scrollbar {
    width: 6px;
}

.tags_row .box_option_list .select_list::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

.tags_row .box_option_list .select_list::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

.tags_row .box_option_list .select_list::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* Firefox 滚动条样式 */
.tags_row .box_option_list .select_list {
    scrollbar-width: thin;
    scrollbar-color: #c1c1c1 #f1f1f1;
}

/* 标签项的样式优化 */
.tags_row .box_option_list .btn_attr_choice {
    margin-bottom: 0;
    margin-right: 0;
    flex-shrink: 0; /* 防止标签被压缩 */
    display: inline-flex;
    align-items: center;
    white-space: nowrap;
}

/* 可选标签的悬停效果 */
.tags_row .box_option_list .option_not_yet .btn_attr_choice:hover {
    background-color: #f0f8ff;
    cursor: pointer;
    transform: translateY(-1px);
    transition: all 0.2s ease;
}

/* 输入框样式调整 */
.tags_row .box_option_list .option_selected .box_input {
    margin-top: 8px;
    width: 100%;
    box-sizing: border-box;
}

/* 选项按钮区域 */
.tags_row .box_option_list .option_button {
    position: absolute;
    top: 100%;
    right: 0;
    z-index: 1001;
    background: #fff;
    border: 1px solid #ccdced;
    border-top: none;
    border-radius: 0 0 5px 5px;
    padding: 5px 10px;
    margin-top: -1px;
}

/* 响应式调整 */
@media (max-width: 768px) {
    .tags_row .box_option_list .option_selected .select_list {
        max-height: 80px; /* 移动端减少高度 */
    }
    
    .tags_row .box_option_list .option_not_yet .select_list {
        max-height: 150px; /* 移动端减少高度 */
    }
}

/* 空状态提示 */
.tags_row .box_option_list .option_not_yet .select_list:empty::before {
    content: "暂无可选标签";
    color: #999;
    font-size: 14px;
    display: block;
    text-align: center;
    padding: 20px;
    width: 100%;
}

/* 加载状态 */
.tags_row .box_option_list .loading {
    text-align: center;
    padding: 20px;
    color: #999;
}

.tags_row .box_option_list .loading::before {
    content: "加载中...";
}

/* 确保下拉框不会超出视窗 */
.tags_row .box_option_list .option_not_yet {
    max-height: 250px;
    overflow: hidden;
}

/* 标签数量提示 */
.tags_row .tag_count_info {
    font-size: 12px;
    color: #999;
    margin-top: 5px;
    text-align: right;
}

/* 搜索高亮 */
.tags_row .box_option_list .btn_attr_choice.highlight {
    background-color: #fff3cd;
    border-color: #ffeaa7;
}

/* 动画效果 */
.tags_row .box_option_list .option_not_yet {
    opacity: 0;
    transform: translateY(-10px);
    transition: all 0.2s ease;
}

.tags_row .box_option_list .option_not_yet.show {
    opacity: 1;
    transform: translateY(0);
}

/* 确保标签容器有足够的相对定位空间 */
.tags_row {
    position: relative;
    z-index: 1;
}

/* 修复标签容器的布局问题 */
.tags_row .box_option_list .option_selected {
    position: relative;
    min-height: 40px;
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 8px;
    background: #fff;
}

/* 当没有标签时的最小高度 */
.tags_row .box_option_list .option_selected .select_list:empty {
    min-height: 0;
}

/* placeholder文本样式 */
.tags_row .box_option_list .option_selected .placeholder {
    position: absolute;
    top: 50%;
    left: 12px;
    transform: translateY(-50%);
    color: #999;
    pointer-events: none;
    font-size: 14px;
}

/* 当有标签时隐藏placeholder */
.tags_row .box_option_list .option_selected .select_list:not(:empty) + .box_input + .placeholder {
    display: none !important;
}

/* 输入框在有标签时的样式 */
.tags_row .box_option_list .option_selected .select_list:not(:empty) + .box_input {
    margin-top: 8px;
    border-top: 1px solid #eee;
    padding-top: 8px;
}

/* 滚动条在hover时显示 */
.tags_row .box_option_list .select_list:not(:hover)::-webkit-scrollbar-thumb {
    background: transparent;
}

.tags_row .box_option_list .select_list:hover::-webkit-scrollbar-thumb {
    background: #c1c1c1;
}
