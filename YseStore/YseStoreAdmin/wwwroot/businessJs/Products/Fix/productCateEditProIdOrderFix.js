/**
 * ProductCateEdit页面ProId顺序修复脚本
 * 专门解决拖拽排序后ProId[]数组顺序不更新的问题
 */

var ProductCateEditProIdOrderFix = (function() {
    
    var isInitialized = false;
    var dragInProgress = false;
    
    /**
     * 初始化修复功能
     */
    function init() {
        if (isInitialized) {
            return;
        }
        
        $(document).ready(function() {
            // 延迟执行，确保页面完全加载
            setTimeout(function() {
                if (isProductCateEditPage()) {
                    setupDragSortFix();
                    setupSubmitFix();
                    isInitialized = true;
                }
            }, 2000);
        });
    }
    
    /**
     * 检查是否在ProductCateEdit页面
     */
    function isProductCateEditPage() {
        return window.location.pathname.includes('/Products/CateEdit') || 
               $('#category_inside').length > 0 ||
               $('.category_products_box').length > 0;
    }
    
    /**
     * 获取当前页面产品ID顺序
     */
    function getCurrentProductIds() {
        var ids = [];

        // 尝试多种选择器
        var selectors = [
            '.products_list.current tbody .item input[name="select"]',
            '.products_list.current tbody tr.item input[name="select"]',
            '.category_products_box .products_list.current tbody .item input[name="select"]'
        ];

        for (var i = 0; i < selectors.length; i++) {
            $(selectors[i]).each(function() {
                var proId = $(this).val();
                if (proId && ids.indexOf(proId) === -1) {
                    ids.push(proId);
                }
            });

            if (ids.length > 0) {
                break;
            }
        }

        return ids;
    }

    /**
     * 检查当前是否为手动排序模式
     */
    function isCustomSortMode() {
        var debugInfo = {
            bodyHasCustomSort: $('body').hasClass('custom_sort'),
            categoryBoxHasCustomSort: $('.category_products_box').hasClass('custom_sort'),
            selectedOrder: null,
            orderTypeValue: null,
            result: false
        };

        // 检查页面是否有custom_sort类
        if ($('body').hasClass('custom_sort') || $('.category_products_box').hasClass('custom_sort')) {
            debugInfo.result = true;
            return true;
        }

        // 检查排序下拉框的选中值 - 使用正确的选择器
        var selectedOrder = null;
        var orderSelectors = [
            '.my_order_box .drop_down .item a.current',
            '.my_order_box .drop_down .item a.selected',
            '.order_type_dropdown .item a.current',
            '.order_type_dropdown .item a.selected'
        ];

        for (var i = 0; i < orderSelectors.length; i++) {
            var element = $(orderSelectors[i]);
            if (element.length > 0) {
                selectedOrder = element.attr('data-order');
                if (selectedOrder) break;
            }
        }

        debugInfo.selectedOrder = selectedOrder;

        if (selectedOrder === 'custom' || selectedOrder === 'custom_sort') {
            debugInfo.result = true;
            return true;
        }

        // 检查隐藏字段OrderType的值
        var orderType = $('input[name="OrderType"]').val();
        debugInfo.orderTypeValue = orderType;

        if (orderType === 'custom' || orderType === 'custom_sort') {
            debugInfo.result = true;
            return true;
        }

        // 检查当前显示的排序文本
        var orderTypeText = $('.my_order_box .order_type').text() || $('.order_type_display').text();
        if (orderTypeText && (orderTypeText.includes('手动') || orderTypeText.includes('自定义') || orderTypeText.includes('custom'))) {
            debugInfo.result = true;
            debugInfo.orderTypeText = orderTypeText;
            return true;
        }


        return false;
    }

    /**
     * 获取完整的产品ID列表（包括所有页面）
     */
    function getCompleteProductIds() {
        var completeIds = [];

        // 方法1：从全局变量productsList获取
        if (typeof window.productsList !== 'undefined' && window.productsList && window.productsList.length > 0) {
            completeIds = window.productsList.slice();
        }
        // 方法2：从categoryProductIds获取
        else if (typeof window.categoryProductIds !== 'undefined' && window.categoryProductIds && window.categoryProductIds.length > 0) {
            completeIds = window.categoryProductIds.slice();
        }
        // 方法3：从隐藏字段获取
        else {
            var productIdsStr = $('#productIdsList').val();
            if (productIdsStr) {
                completeIds = productIdsStr.split(',').map(function(id) {
                    return parseInt(id);
                }).filter(function(id) {
                    return id > 0;
                });
            }
        }

        return completeIds;
    }
    
    /**
     * 更新ProId[]隐藏字段（处理分页情况）
     */
    function updateProIdFields(currentPageNewOrder) {
        if (!currentPageNewOrder || currentPageNewOrder.length === 0) {
            return;
        }

        // 获取完整的产品ID列表
        var completeProductIds = getCompleteProductIds();
        if (!completeProductIds || completeProductIds.length === 0) {
            completeProductIds = currentPageNewOrder.slice();
        }

        // 获取当前页面信息
        var currentPage = parseInt($('.products_list.current').attr('data-page')) || 0;
        var pageSize = (typeof PRODUCT_LIMIT !== 'undefined') ? PRODUCT_LIMIT : 20;
        var startIndex = currentPage * pageSize;
        var endIndex = startIndex + currentPageNewOrder.length;

        // 创建新的完整产品ID列表
        var newCompleteProductIds = completeProductIds.slice(); // 复制原数组

        // 将当前页面的新顺序替换到完整列表中的对应位置
        for (var i = 0; i < currentPageNewOrder.length; i++) {
            var globalIndex = startIndex + i;
            if (globalIndex < newCompleteProductIds.length) {
                newCompleteProductIds[globalIndex] = currentPageNewOrder[i];
            }
        }

        // 获取表单
        var form = $('#edit_form');
        if (form.length === 0) {
            form = $('form').first();
        }

        if (form.length === 0) {
            return;
        }

        // 清除现有的ProId隐藏字段
        var removedCount = form.find('input[name^="ProId"]').length;
        form.find('input[name^="ProId"]').remove();

        // 重新创建所有ProId[]隐藏字段（使用完整的产品列表）
        var addedCount = 0;
        newCompleteProductIds.forEach(function(proId, index) {
            if (proId) {
                var hiddenInput = $('<input type="hidden" name="ProId[' + index + ']" value="' + proId + '">');
                form.append(hiddenInput);
                addedCount++;
            }
        });


        // 更新全局变量
        if (typeof window.productsList !== 'undefined') {
            window.productsList = newCompleteProductIds.slice();
        }

        if (typeof window.categoryProductIds !== 'undefined') {
            window.categoryProductIds = newCompleteProductIds.slice();
        }

        // 更新隐藏字段
        var productIdsListField = $('#productIdsList');
        if (productIdsListField.length > 0) {
            productIdsListField.val(newCompleteProductIds.join(','));
        }

        // 关键：只有在手动排序模式下才创建UpdatedProId字段
        var isCustomSort = isCustomSortMode();

        if (isCustomSort) {
            // 清除现有的UpdatedProId字段
            form.find('input[name^="UpdatedProId"]').remove();

            // 创建新的UpdatedProId数组字段，存储更新后的完整顺序
            var createdCount = 0;
            newCompleteProductIds.forEach(function(proId, index) {
                if (proId) {
                    var updatedProIdField = $('<input type="hidden" name="UpdatedProId[' + index + ']" value="' + proId + '">');
                    form.append(updatedProIdField);
                    createdCount++;
                }
            });

            // 验证字段是否真的创建了
            var verifyFields = form.find('input[name^="UpdatedProId"]');
        } else {
            // 非手动排序模式，清除UpdatedProId字段（如果存在）
            form.find('input[name^="UpdatedProId"]').remove();
        }
    }
    
    /**
     * 设置拖拽排序修复
     */
    function setupDragSortFix() {
        
        // 监听鼠标按下事件（拖拽开始）
        $(document).on('mousedown', '.products_list.current tbody .item .order_move', function(e) {
            dragInProgress = true;
        });
        
        // 监听鼠标释放事件（拖拽结束）
        $(document).on('mouseup', function(e) {
            if (dragInProgress) {
                dragInProgress = false;
                
                // 延迟执行，确保DOM已更新
                setTimeout(function() {
                    var currentOrder = getCurrentProductIds();
                    if (currentOrder.length > 0) {
                        updateProIdFields(currentOrder);
                    }
                }, 200);
            }
        });
        
        // 监听dragsort插件的dragend事件
        $(document).on('dragend', '.products_list.current tbody .item', function(e) {
            setTimeout(function() {
                var currentOrder = getCurrentProductIds();
                if (currentOrder.length > 0) {
                    updateProIdFields(currentOrder);
                }
            }, 100);
        });
        
        // 监听sortupdate事件（jQuery UI sortable）
        $(document).on('sortupdate', '.products_list.current tbody', function(e, ui) {
            setTimeout(function() {
                var currentOrder = getCurrentProductIds();
                if (currentOrder.length > 0) {
                    updateProIdFields(currentOrder);
                }
            }, 100);
        });

        // 监听排序方式切换 - 正确的选择器应该是点击a标签
        $(document).on('click', '.my_order_box .drop_down .item a', function() {
            var $this = $(this);
            var orderType = $this.attr('data-order');

            setTimeout(function() {
                // 更新隐藏字段OrderType的值
                var orderTypeField = $('input[name="OrderType"]');
                if (orderTypeField.length > 0) {
                    orderTypeField.val(orderType);
                }

                // 如果切换到手动排序，立即创建UpdatedProId字段
                if (orderType === 'custom' || orderType === 'custom_sort') {
                    var completeProductIds = getCompleteProductIds();
                    if (completeProductIds.length > 0) {
                        updateProIdFieldsWithCompleteList(completeProductIds);
                    }
                } else {
                    // 切换到其他排序方式，清除UpdatedProId字段
                    $('#edit_form input[name^="UpdatedProId"]').remove();
                }
            }, 200);
        });

    }
    
    /**
     * 设置提交修复
     */
    function setupSubmitFix() {

        // 拦截保存按钮点击
        $(document).on('click', '.btn_submit', function(e) {
            var $btn = $(this);

            // 检查是否是分类编辑页面的保存按钮
            if ($btn.closest('#edit_form').length > 0 ||
                $btn.attr('name') === 'submit_button') {

                // 确保使用完整的产品列表
                var completeProductIds = getCompleteProductIds();
                if (completeProductIds.length > 0) {
                    // 如果当前页面有拖拽变化，获取当前页面的新顺序
                    var currentPageOrder = getCurrentProductIds();
                    if (currentPageOrder.length > 0) {
                        // 使用当前页面的新顺序更新完整列表
                        updateProIdFields(currentPageOrder);
                    } else {
                        // 如果当前页面没有数据，直接使用完整列表
                        updateProIdFieldsWithCompleteList(completeProductIds);
                    }

                    // 检查最终的UpdatedProId字段
                    var finalUpdatedProIdFields = $('#edit_form input[name^="UpdatedProId"]');
                } else {
                }
            }
        });

        // 拦截表单提交
        $('#edit_form').on('submit', function(e) {

            // 确保使用完整的产品列表
            var completeProductIds = getCompleteProductIds();
            if (completeProductIds.length > 0) {
                var currentPageOrder = getCurrentProductIds();
                if (currentPageOrder.length > 0) {
                    updateProIdFields(currentPageOrder);
                } else {
                    updateProIdFieldsWithCompleteList(completeProductIds);
                }
            }
        });

    }

    /**
     * 使用完整列表更新ProId字段（无需分页处理）
     */
    function updateProIdFieldsWithCompleteList(completeProductIds) {
        if (!completeProductIds || completeProductIds.length === 0) {
            return;
        }


        // 获取表单
        var form = $('#edit_form');
        if (form.length === 0) {
            form = $('form').first();
        }

        if (form.length === 0) {
            return;
        }

        // 清除现有的ProId隐藏字段
        var removedCount = form.find('input[name^="ProId"]').length;
        form.find('input[name^="ProId"]').remove();

        // 重新创建所有ProId[]隐藏字段
        var addedCount = 0;
        completeProductIds.forEach(function(proId, index) {
            if (proId) {
                var hiddenInput = $('<input type="hidden" name="ProId[' + index + ']" value="' + proId + '">');
                form.append(hiddenInput);
                addedCount++;
            }
        });


        // 只有在手动排序模式下才创建UpdatedProId字段
        if (isCustomSortMode()) {
            // 清除现有的UpdatedProId字段
            form.find('input[name^="UpdatedProId"]').remove();

            // 创建新的UpdatedProId数组字段
            completeProductIds.forEach(function(proId, index) {
                if (proId) {
                    var updatedProIdField = $('<input type="hidden" name="UpdatedProId[' + index + ']" value="' + proId + '">');
                    form.append(updatedProIdField);
                }
            });
        } else {
            // 非手动排序模式，清除UpdatedProId字段（如果存在）
            form.find('input[name^="UpdatedProId"]').remove();
        }
    }
    
    /**
     * 手动更新ProId顺序（供控制台调用）
     */
    function manualUpdate() {

        // 优先使用完整的产品列表
        var completeProductIds = getCompleteProductIds();
        if (completeProductIds.length > 0) {
            var currentOrder = getCurrentProductIds();
            if (currentOrder.length > 0) {
                // 如果当前页面有数据，使用分页更新逻辑
                updateProIdFields(currentOrder);
            } else {
                // 如果当前页面没有数据，直接使用完整列表
                updateProIdFieldsWithCompleteList(completeProductIds);
            }
        } else {
        }
    }
    
    /**
     * 调试函数：显示当前的产品数据状态
     */
    function debugProductData() {

        var currentPageIds = getCurrentProductIds();

        var completeIds = getCompleteProductIds();

        var currentPage = parseInt($('.products_list.current').attr('data-page')) || 0;
        var pageSize = (typeof PRODUCT_LIMIT !== 'undefined') ? PRODUCT_LIMIT : 20;

        var updatedProIdFields = [];
        $('#edit_form input[name^="UpdatedProId"]').each(function() {
            updatedProIdFields.push($(this).val());
        });

        var proIdFields = [];
        $('#edit_form input[name^="ProId"]').each(function() {
            proIdFields.push($(this).val());
        });

    }

    // 暴露函数到全局
    window.updateProIdOrder = manualUpdate;
    window.debugProductData = debugProductData;

    // 添加测试函数
    window.testUpdatedProIdCreation = function() {

        var isCustomSort = isCustomSortMode();

        if (isCustomSort) {
            var completeProductIds = getCompleteProductIds();

            if (completeProductIds.length > 0) {
                updateProIdFieldsWithCompleteList(completeProductIds);

                var createdFields = $('#edit_form input[name^="UpdatedProId"]');

                if (createdFields.length > 0) {
                    createdFields.slice(0, 5).each(function(index, field) {
                    });
                }
            }
        } else {
        }

    };

    // 添加强制切换到手动排序的测试函数
    window.forceCustomSort = function() {

        // 更新隐藏字段
        var orderTypeField = $('input[name="OrderType"]');
        if (orderTypeField.length > 0) {
            orderTypeField.val('custom_sort');
        }

        // 添加CSS类
        $('body').addClass('custom_sort');
        $('.category_products_box').addClass('custom_sort');
        $('.products_list.current').addClass('custom_sort');

        // 更新UI显示
        $('.my_order_box .drop_down .item a').removeClass('current');
        $('.my_order_box .drop_down .item a[data-order="custom_sort"]').addClass('current');
        $('.order_type').text('手动排序');

        // 立即创建UpdatedProId字段
        var completeProductIds = getCompleteProductIds();
        if (completeProductIds.length > 0) {
            updateProIdFieldsWithCompleteList(completeProductIds);
        }

    };

    // 添加检查当前排序状态的函数
    window.checkSortStatus = function() {

        var orderTypeField = $('input[name="OrderType"]').val();

        var currentOrderLink = $('.my_order_box .drop_down .item a.current');

        var hasCustomSortClass = $('body').hasClass('custom_sort') || $('.category_products_box').hasClass('custom_sort') || $('.products_list.current').hasClass('custom_sort');

        var isCustomSort = isCustomSortMode();

        var updatedProIdFields = $('#edit_form input[name^="UpdatedProId"]').length;

    };

    // 添加测试函数
    window.testUpdatedProIdCreation = function() {

        var isCustomSort = isCustomSortMode();

        if (isCustomSort) {
            var completeProductIds = getCompleteProductIds();

            if (completeProductIds.length > 0) {
                updateProIdFieldsWithCompleteList(completeProductIds);

                var createdFields = $('#edit_form input[name^="UpdatedProId"]');

                if (createdFields.length > 0) {
                    createdFields.slice(0, 5).each(function(index, field) {
                    });
                }
            }
        } else {
        }

    };

    // 返回公共接口
    return {
        init: init,
        manualUpdate: manualUpdate,
        debugProductData: debugProductData
    };
})();

// 自动初始化
ProductCateEditProIdOrderFix.init();
