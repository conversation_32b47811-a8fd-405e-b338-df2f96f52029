/**
 * 产品编辑页面标签滚动功能修复脚本
 */

$(document).ready(function() {
    // 初始化标签滚动功能
    initProductTagsScroll();
});

function initProductTagsScroll() {
    // 初始化标签下拉框功能
    initTagsDropdown();
    
    // 处理标签选择
    handleTagSelection();
    
    // 处理标签删除
    handleTagRemoval();
    
    // 更新标签计数
    updateTagCount();
}

// 初始化标签下拉框功能
function initTagsDropdown() {
    var $tagInput = $('.tags_row input[name="_Option"]');
    var $optionNotYet = $('.tags_row .option_not_yet');
    var $optionButton = $('.tags_row .option_button');

    // 输入框获得焦点时显示下拉框
    $tagInput.on('focus', function() {
        showTagsDropdown();
    });

    // 点击输入框区域时显示下拉框
    $('.tags_row .option_selected').on('click', function(e) {
        if (e.target === this || $(e.target).hasClass('select_list')) {
            showTagsDropdown();
            $tagInput.focus();
        }
    });

    // 点击文档其他地方时隐藏下拉框
    $(document).on('click', function(e) {
        if (!$(e.target).closest('.tags_row .box_option_list').length) {
            hideTagsDropdown();
        }
    });

    // ESC键隐藏下拉框
    $(document).on('keydown', function(e) {
        if (e.keyCode === 27) { // ESC键
            hideTagsDropdown();
        }
    });

    // 处理输入框回车和逗号分隔
    $tagInput.on('keydown', function(e) {
        if (e.keyCode === 13 || e.keyCode === 188) { // 回车键或逗号
            e.preventDefault();
            var tagText = $(this).val().trim();
            if (tagText) {
                addNewTag(tagText);
                $(this).val('');
            }
        }
    });
}

// 显示标签下拉框
function showTagsDropdown() {
    var $optionNotYet = $('.tags_row .option_not_yet');
    var $optionButton = $('.tags_row .option_button');

    if ($optionNotYet.find('.btn_attr_choice').length > 0) {
        $optionNotYet.show().addClass('show');
        $optionButton.show();
    }
}

// 隐藏标签下拉框
function hideTagsDropdown() {
    var $optionNotYet = $('.tags_row .option_not_yet');
    var $optionButton = $('.tags_row .option_button');

    $optionNotYet.removeClass('show');
    setTimeout(function() {
        $optionNotYet.hide();
        $optionButton.hide();
    }, 200);
}

// 处理标签选择
function handleTagSelection() {
    // 处理可用标签选择
    $(document).on('click', '.tags_row .option_not_yet .select_list .btn_attr_choice', function(e) {
        e.stopPropagation();

        var tagText = $(this).find('b').text();
        var tagId = $(this).find('input[name="tagsOption[]"]').val();
        
        if (!tagText) return;

        // 检查是否已存在于选中标签中
        var exists = false;
        $('.tags_row .option_selected .select_list .btn_attr_choice b').each(function() {
            if ($(this).text().toLowerCase() === tagText.toLowerCase()) {
                exists = true;
                return false;
            }
        });

        if (!exists) {
            addSelectedTag(tagText, tagId);
            $(this).hide(); // 隐藏已选择的标签
        }

        hideTagsDropdown();
    });

    // 处理全选按钮
    $(document).on('click', '.tags_row .option_button .select_all', function(e) {
        e.preventDefault();
        
        $('.tags_row .option_not_yet .select_list .btn_attr_choice:visible').each(function() {
            var tagText = $(this).find('b').text();
            var tagId = $(this).find('input[name="tagsOption[]"]').val();
            
            // 检查是否已存在
            var exists = false;
            $('.tags_row .option_selected .select_list .btn_attr_choice b').each(function() {
                if ($(this).text().toLowerCase() === tagText.toLowerCase()) {
                    exists = true;
                    return false;
                }
            });

            if (!exists) {
                addSelectedTag(tagText, tagId);
                $(this).hide();
            }
        });

        hideTagsDropdown();
    });
}

// 处理标签删除
function handleTagRemoval() {
    $(document).on('click', '.tags_row .option_selected .select_list .btn_attr_choice i', function(e) {
        e.stopPropagation();
        
        var $tag = $(this).closest('.btn_attr_choice');
        var tagText = $tag.find('b').text();
        
        // 从选中标签中移除
        $tag.remove();
        
        // 在可选标签中重新显示
        $('.tags_row .option_not_yet .select_list .btn_attr_choice').each(function() {
            if ($(this).find('b').text().toLowerCase() === tagText.toLowerCase()) {
                $(this).show();
                return false;
            }
        });
        
        // 更新隐藏字段
        updateHiddenFields();
        updateTagCount();
        updatePlaceholder();
    });
}

// 添加选中的标签
function addSelectedTag(tagText, tagId) {
    var $selectedList = $('.tags_row .option_selected .select_list');
    
    var tagHtml = '<span class="btn_attr_choice current" data-type="tags" data-id="' + (tagId || '') + '">' +
                  '<b>' + tagText + '</b>' +
                  '<input type="hidden" name="tagsOption[]" value="' + tagText + '">' +
                  '<input type="hidden" name="tagsName[]" value="' + tagText + '">' +
                  '<input type="hidden" name="tagsId[]" value="' + (tagId || '') + '">' +
                  '<i></i>' +
                  '</span>';
    
    $selectedList.append(tagHtml);
    
    // 更新隐藏字段
    updateHiddenFields();
    updateTagCount();
    updatePlaceholder();
}

// 添加新标签（用户输入）
function addNewTag(tagText) {
    // 检查是否已存在
    var exists = false;
    $('.tags_row .option_selected .select_list .btn_attr_choice b').each(function() {
        if ($(this).text().toLowerCase() === tagText.toLowerCase()) {
            exists = true;
            return false;
        }
    });

    if (!exists) {
        addSelectedTag(tagText, ''); // 新标签没有ID
    }
}

// 更新隐藏字段
function updateHiddenFields() {
    var tagNames = [];
    var tagIds = [];
    
    $('.tags_row .option_selected .select_list .btn_attr_choice').each(function() {
        var tagName = $(this).find('b').text();
        var tagId = $(this).data('id') || '';
        
        tagNames.push(tagName);
        if (tagId) {
            tagIds.push(tagId);
        }
    });
    
    // 更新TagString字段
    $('input[name="TagString"]').val(tagNames.join(','));
    
    // 更新TagIds字段
    if (tagIds.length > 0) {
        $('input[name="TagIds"]').val('|' + tagIds.join('|') + '|');
    } else {
        $('input[name="TagIds"]').val('');
    }
}

// 更新标签计数
function updateTagCount() {
    var count = $('.tags_row .option_selected .select_list .btn_attr_choice').length;
    var $countInfo = $('.tags_row .tag_count_info');
    
    if ($countInfo.length === 0) {
        $countInfo = $('<div class="tag_count_info"></div>');
        $('.tags_row').append($countInfo);
    }
    
    if (count > 0) {
        $countInfo.text('已选择 ' + count + ' 个标签').show();
    } else {
        $countInfo.hide();
    }
}

// 更新placeholder显示
function updatePlaceholder() {
    var $placeholder = $('.tags_row .option_selected .placeholder');
    var hasSelectedTags = $('.tags_row .option_selected .select_list .btn_attr_choice').length > 0;
    
    if (hasSelectedTags) {
        $placeholder.hide();
    } else {
        $placeholder.show();
    }
}

// 搜索功能（可选）
function initTagSearch() {
    var $tagInput = $('.tags_row input[name="_Option"]');
    
    $tagInput.on('input', function() {
        var searchText = $(this).val().toLowerCase();
        
        $('.tags_row .option_not_yet .select_list .btn_attr_choice').each(function() {
            var tagText = $(this).find('b').text().toLowerCase();
            
            if (tagText.includes(searchText)) {
                $(this).removeClass('highlight').show();
                if (searchText && tagText.startsWith(searchText)) {
                    $(this).addClass('highlight');
                }
            } else {
                $(this).hide();
            }
        });
    });
}
