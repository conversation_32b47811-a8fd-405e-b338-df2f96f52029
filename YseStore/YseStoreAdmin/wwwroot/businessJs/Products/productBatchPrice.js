/**
 * 产品批量改价功能
 */
const productBatchPrice = {
    /**
     * 初始化批量改价功能
     */
    init: function() {
        this.bindEvents();
        this.initFormElements();
    },

    /**
     * 绑定事件处理函数
     */
    bindEvents: function() {
        // 批量改价按钮点击事件
        const batchPriceBtn = document.querySelector('.more_feat .batch-price');
        if (batchPriceBtn) {
            batchPriceBtn.addEventListener('click', function() {
                productBatchPrice.showBatchPriceDialog();
            });
        }

        // 改价方式单选按钮切换事件
        const calcMethodRadios = document.querySelectorAll('input[name="calc_method"]');
        calcMethodRadios.forEach(radio => {
            radio.addEventListener('change', function() {
                productBatchPrice.togglePriceInputType(this.value);
            });
        });

        // 价格类型选择事件
        const priceTypeSelect = document.querySelector('select[name="price_type"]');
        if (priceTypeSelect) {
            priceTypeSelect.addEventListener('change', function() {
                productBatchPrice.updatePriceTypeText(this.value);
            });
        }

        // 表单提交事件
        const batchPriceForm = document.getElementById('batchPriceForm');
        if (batchPriceForm) {
            batchPriceForm.addEventListener('submit', function(e) {
                e.preventDefault();
                productBatchPrice.submitBatchPrice();
            });
        }

        // 取消按钮点击事件
        const cancelBtn = document.querySelector('.batch_price_by_cate .btn_cancel');
        if (cancelBtn) {
            cancelBtn.addEventListener('click', function() {
                productBatchPrice.hideBatchPriceDialog();
            });
        }

        // 关闭按钮点击事件
        const closeBtn = document.querySelector('.batch_price_by_cate .close');
        if (closeBtn) {
            closeBtn.addEventListener('click', function() {
                productBatchPrice.hideBatchPriceDialog();
            });
        }
    },

    /**
     * 初始化表单元素
     */
    initFormElements: function() {
        // 默认选择百分比方式
        const percentRadio = document.querySelector('input[name="calc_method"][value="percent"]');
        if (percentRadio) {
            percentRadio.checked = true;
            this.togglePriceInputType('percent');
        }

        // 初始化价格类型文本
        const priceTypeSelect = document.querySelector('select[name="price_type"]');
        if (priceTypeSelect) {
            this.updatePriceTypeText(priceTypeSelect.value);
        }
    },

    /**
     * 显示批量改价对话框
     */
    showBatchPriceDialog: function() {
        const batchPriceDialog = document.querySelector('.batch_price_by_cate');
        if (batchPriceDialog) {
            // 显示对话框
            batchPriceDialog.style.display = 'block';
            
            // 重置表单
            this.resetBatchPriceForm();
        }
    },

    /**
     * 隐藏批量改价对话框
     */
    hideBatchPriceDialog: function() {
        const batchPriceDialog = document.querySelector('.batch_price_by_cate');
        if (batchPriceDialog) {
            batchPriceDialog.style.display = 'none';
        }
    },

    /**
     * 切换价格输入类型（百分比或固定值）
     */
    togglePriceInputType: function(type) {
        const percentUnit = document.querySelector('.unit_input .percent');
        const priceUnit = document.querySelector('.unit_input .price');
        
        if (type === 'percent') {
            // 显示百分比符号，隐藏价格符号
            if (percentUnit) percentUnit.style.display = 'inline-block';
            if (priceUnit) priceUnit.style.display = 'none';
        } else {
            // 显示价格符号，隐藏百分比符号
            if (percentUnit) percentUnit.style.display = 'none';
            if (priceUnit) priceUnit.style.display = 'inline-block';
        }
    },

    /**
     * 更新价格类型文本
     */
    updatePriceTypeText: function(priceType) {
        const priceTypeText = document.querySelector('.price_value .price_type_text');
        if (priceTypeText) {
            let text = '';
            switch (priceType) {
                case 'origin_price':
                    text = '原价';
                    break;
                case 'shop_price':
                    text = '价格';
                    break;
                case 'cost_price':
                    text = '成本价';
                    break;
                default:
                    text = '价格';
            }
            priceTypeText.textContent = text;
        }
    },

    /**
     * 重置批量改价表单
     */
    resetBatchPriceForm: function() {
        const form = document.getElementById('batchPriceForm');
        if (form) {
            form.reset();
            
            // 重新初始化表单元素
            this.initFormElements();
        }
    },

    /**
     * 收集表单数据
     */
    collectFormData: function() {
        const form = document.getElementById('batchPriceForm');
        if (!form) return null;

        const formData = {
            cateId: form.querySelector('select[name="cate_id"]').value,
            priceType: form.querySelector('select[name="price_type"]').value,
            calcMethod: form.querySelector('input[name="calc_method"]:checked').value,
            calcSymbol: form.querySelector('select[name="calc_symbol"]').value,
            calcValue: form.querySelector('input[name="calc_value"]').value
        };

        return formData;
    },

    /**
     * 验证表单数据
     */
    validateFormData: function(formData) {
        if (!formData) return false;

        // 验证分类ID
        if (!formData.cateId) {
            customize_pop.warning('请选择产品分类');
            return false;
        }

        // 验证调整值
        if (!formData.calcValue || isNaN(parseFloat(formData.calcValue)) || parseFloat(formData.calcValue) <= 0) {
            customize_pop.warning('请输入有效的调整值');
            return false;
        }

        return true;
    },

    /**
     * 提交批量改价表单
     */
    submitBatchPrice: function() {
        try {
            // 收集表单数据
            const formData = this.collectFormData();
            
            // 验证表单数据
            if (!this.validateFormData(formData)) {
                return;
            }
            
            // 获取CSRF令牌
            const csrfToken = document.querySelector('input[name="_csrf"]');
            if (!csrfToken) {
                customize_pop.error('找不到CSRF令牌');
                throw new Error('找不到CSRF令牌');
            }
            
            // 构建请求数据
            const requestData = new FormData();
            requestData.append('cate_id', formData.cateId);
            requestData.append('price_type', formData.priceType);
            requestData.append('calc_method', formData.calcMethod);
            requestData.append('calc_symbol', formData.calcSymbol);
            requestData.append('calc_value', formData.calcValue);
            if (csrfToken) {
                requestData.append('_csrf', csrfToken.value);
            }
            
            // 添加调试日志
            console.log('批量改价请求参数:', {
                cate_id: formData.cateId,
                price_type: formData.priceType,
                calc_method: formData.calcMethod,
                calc_symbol: formData.calcSymbol,
                calc_value: formData.calcValue
            });
            
            // 显示加载提示
            customize_pop.loading('正在处理，请稍候...');
            
            // 发送AJAX请求
            fetch('/api/ProductBatchPrice/BatchPrice', {
                method: 'POST',
                body: requestData
            })
            .then(response => {
                // 添加调试日志
                console.log('响应状态:', response.status);
                return response.json();
            })
            .then(data => {
                // 隐藏加载提示
                customize_pop.loadingClose();
                
                // 添加调试日志
                console.log('响应数据:', data);
                
                if (data.success) {
                    // 批量改价成功
                    customize_pop.success('批量改价成功！', () => {
                        // 隐藏对话框
                        this.hideBatchPriceDialog();
                        
                        // 刷新页面以显示更新后的价格
                        window.location.reload();
                    });
                } else {
                    // 批量改价失败
                    customize_pop.error(data.message || '批量改价失败，请稍后重试');
                }
            })
            .catch(error => {
                // 隐藏加载提示
                customize_pop.loadingClose();
                
                console.error('批量改价时发生错误:', error);
                customize_pop.error('批量改价时发生错误，请稍后重试');
            });
        } catch (error) {
            console.error('提交批量改价表单时发生错误:', error);
            customize_pop.error('提交批量改价表单时发生错误，请稍后重试');
        }
    },
};

// 页面加载完成后初始化批量改价功能
document.addEventListener('DOMContentLoaded', function() {
    productBatchPrice.init();
}); 