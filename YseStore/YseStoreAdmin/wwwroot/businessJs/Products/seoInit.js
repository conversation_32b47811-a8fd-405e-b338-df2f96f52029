/**
 * SEO初始化编辑区域脚本
 * 用于处理产品编辑页面中的SEO相关功能
 */

var SeoInit = {
    /**
     * 初始化SEO功能
     */
    init: function() {
        $(function () {
            // 添加额外的点击事件处理程序，不覆盖原有事件
            $("#edit_seo_list").on('click', function () {
                // 延迟执行，确保原始事件处理完成
                setTimeout(function () {
                    var dataSave = $("#edit_seo_list").attr("data-save");
                    if (dataSave == "1") {
                        $("#custom_url_container").show();
                    } else {
                        $("#custom_url_container").hide();
                    }

                    // 修复样式问题
                    SeoInit.fixPageUrlStyles();

                    // 无论是展开还是收起，都确保URL正确显示
                    SeoInit.updateSeoUrl();
                }, 100);
            });

            // 初始化自定义地址的显示状态 - 默认隐藏
            $("#custom_url_container").hide();

            // 确保初始化时data-save为0（如果没有设置）
            if (!$("#edit_seo_list").attr("data-save")) {
                $("#edit_seo_list").attr("data-save", "0");
            }

            // 页面加载时修复样式
            $(document).ready(function () {
                SeoInit.fixPageUrlStyles();
                SeoInit.initializeClipboard();

                // 监听PageUrl文本框输入，动态更新复制链接
                $('[name=PageUrl]').on('input', function () {
                    SeoInit.updateSeoUrl();
                });

                // 页面加载时模拟点击一次编辑按钮，确保SEO区域正确初始化
                setTimeout(function () {
                    // 先保存当前data-save状态
                    var originalDataSave = $("#edit_seo_list").attr("data-save");

                    // 模拟点击编辑按钮
                    $("#edit_seo_list").trigger('click');

                    // 确保URL正确显示
                    setTimeout(function () {
                        SeoInit.updateSeoUrl();
                    }, 200);
                }, 500);
            });

            // 窗口大小改变时重新计算样式
            $(window).resize(function () {
                SeoInit.fixPageUrlStyles();
            });
        });
    },

    /**
     * 修复PageUrl的样式问题
     */
    fixPageUrlStyles: function() {
        if ($('[name=PageUrl]').length && $('.prefix_textarea').length) {
            var $Textarea_w = $('[name=PageUrl]').width(),
                $Box_h = $('[name=PageUrl]').parent().height(),
                $Height = $('[name=PageUrl]').prev('.prefix').find('i').position().top - 10,
                $Left = $('[name=PageUrl]').prev('.prefix').find('i').position().left,
                $Top = $('[name=PageUrl]').prev('.prefix')[0].getBoundingClientRect().height - 7;

            $('[name=PageUrl]').css({
                'top': $Top,
                'text-indent': $Left,
                'height': $Box_h - $Height
            });
        }
    },

    /**
     * 初始化复制链接功能
     */
    initializeClipboard: function() {
        // 检查ClipboardJS是否已加载
        if (typeof ClipboardJS !== 'undefined') {
            // 初始化复制功能
            var clipboard = new ClipboardJS('.btn_copy');

            // 复制成功事件
            clipboard.on('success', function (e) {
                if (typeof customize_pop !== 'undefined') {
                    customize_pop.success('链接已复制到剪贴板');
                } else {
                    alert('链接已复制到剪贴板');
                }
                e.clearSelection();
            });

            // 复制失败事件
            clipboard.on('error', function (e) {
                if (typeof customize_pop !== 'undefined') {
                    customize_pop.error('复制失败，请手动复制');
                } else {
                    alert('复制失败，请手动复制');
                }
            });
        }
    },

    /**
     * 更新SEO URL的通用函数
     */
    updateSeoUrl: function() {
        // 获取页面URL和基础域名
        const pageUrlElement = $('[name=PageUrl]');
        let pageUrl = 'nuove-uscite';
        let baseUrl = 'https://www.retekess.it';

        if (pageUrlElement.length > 0) {
            const pageUrlValue = pageUrlElement.val();
            if (pageUrlValue && typeof pageUrlValue === 'string') {
                const trimmedPageUrl = pageUrlValue.trim();
                if (trimmedPageUrl) {
                    pageUrl = trimmedPageUrl;
                }
            }

            const domainValue = pageUrlElement.data('domain');
            if (domainValue && typeof domainValue === 'string') {
                baseUrl = domainValue;
            }
        }

        // 组合完整URL
        const fullUrl = baseUrl + pageUrl;

        // 更新复制链接按钮的data-clipboard-text属性
        $('.btn_copy').attr('data-clipboard-text', fullUrl);

        // 更新SEO预览区域的URL文本
        $('.seo_info_box .url.input_copy').text(fullUrl);

        // 根据当前data-save状态决定是否显示URL
        var dataSave = $("#edit_seo_list").attr("data-save");
        if (dataSave == "1") {
            $('.seo_info_box .url.input_copy').show();
        }
    }
};

// 页面加载完成后自动初始化
$(document).ready(function() {
    SeoInit.init();
});
