/**
 * 图片库选择功能调试助手
 * 用于诊断和修复图片库选择问题
 */

var GalleryDebugHelper = {
    
    /**
     * 检查图片库选择功能状态
     */
    checkGalleryStatus: function() {
        console.log('=== 图片库选择功能状态检查 ===');
        
        // 检查必要的对象是否存在
        console.log('frame_obj存在:', typeof frame_obj !== 'undefined');
        console.log('products_obj存在:', typeof products_obj !== 'undefined');
        
        if (typeof frame_obj !== 'undefined') {
            console.log('photo_choice_init函数存在:', typeof frame_obj.photo_choice_init === 'function');
            console.log('multi_img_item函数存在:', typeof frame_obj.multi_img_item === 'function');
            console.log('upload_pro_img_init函数存在:', typeof frame_obj.upload_pro_img_init === 'function');
        }
        
        if (typeof products_obj !== 'undefined' && products_obj.function_init) {
            console.log('main_picture_upload函数存在:', typeof products_obj.function_init.main_picture_upload === 'function');
        }
        
        // 检查DOM元素
        var $galleryBtn = $('.upload_menu li[data-type="gallery"]');
        var $picDetail = $('#PicDetail');
        
        console.log('图片库选择按钮存在:', $galleryBtn.length > 0);
        console.log('PicDetail容器存在:', $picDetail.length > 0);
        
        if ($picDetail.length > 0) {
            console.log('PicDetail容器可见:', $picDetail.is(':visible'));
            console.log('图片项数量:', $picDetail.find('.img').length);
            console.log('已上传图片数量:', $picDetail.find('.img.isfile').length);
            console.log('上传按钮数量:', $picDetail.find('.img.show_btn').length);
        }
        
        // 检查事件绑定
        if ($galleryBtn.length > 0) {
            var events = $._data($galleryBtn[0], 'events');
            console.log('图片库按钮绑定的事件:', events ? Object.keys(events) : '无');
        }
    },
    
    /**
     * 手动触发图片库选择
     */
    triggerGallerySelection: function() {
        console.log('=== 手动触发图片库选择 ===');
        
        var $picDetail = $('#PicDetail');
        if ($picDetail.length === 0) {
            console.error('PicDetail容器不存在');
            return;
        }
        
        // 显示容器
        $picDetail.show();
        
        // 获取当前最后一个图片项的num
        var $lastImg = $picDetail.find('.img:last');
        var $num = $lastImg.length > 0 ? $lastImg.attr('num') : '0';
        
        // 如果没有可用的上传项，创建一个
        if ($picDetail.find('.img.show_btn').length === 0) {
            var newNum = parseInt($num) + 1;
            if (typeof frame_obj !== 'undefined' && frame_obj.multi_img_item) {
                var html = frame_obj.multi_img_item("PicPath[]", newNum, 1, 'products');
                $picDetail.append(html);
                $num = newNum.toString();
            } else {
                console.error('frame_obj.multi_img_item函数不存在');
                return;
            }
        }
        
        console.log('目标num:', $num);
        
        // 调用图片库选择功能
        if (typeof frame_obj !== 'undefined' && frame_obj.photo_choice_init) {
            frame_obj.photo_choice_init(
                'PicDetail .img[num=' + $num + ']', 
                'products', 
                99, 
                'do_action=products.products_img_del&Model=products', 
                1, 
                "frame_obj.upload_pro_img_init(1, '#PicDetail', 1);if(typeof products_obj !== 'undefined' && products_obj.function_init && products_obj.function_init.main_picture_upload) products_obj.function_init.main_picture_upload();"
            );
        } else {
            console.error('frame_obj.photo_choice_init函数不存在');
        }
    },
    
    /**
     * 修复图片显示问题
     */
    fixImageDisplay: function() {
        console.log('=== 修复图片显示问题 ===');
        
        var $picDetail = $('#PicDetail');
        if ($picDetail.length === 0) {
            console.error('PicDetail容器不存在');
            return;
        }
        
        var fixedCount = 0;
        
        // 检查所有已上传的图片项
        $picDetail.find('.img.isfile').each(function() {
            var $imgItem = $(this);
            var $hiddenInput = $imgItem.find('input[name="PicPath[]"]');
            var imagePath = $hiddenInput.val();
            
            if (imagePath && $hiddenInput.attr('save') == '1') {
                var $previewPic = $imgItem.find('.preview_pic');
                var $existingImg = $previewPic.find('img');
                
                if ($existingImg.length === 0) {
                    // 创建图片结构
                    var imgHtml = '<a href="' + imagePath + '" target="_blank">' +
                                 '<img src="' + imagePath + '" style="max-width: 100%; max-height: 100%;">' +
                                 '</a>';
                    $previewPic.prepend(imgHtml);
                    fixedCount++;
                } else {
                    // 确保图片src正确
                    if ($existingImg.attr('src') !== imagePath) {
                        $existingImg.attr('src', imagePath);
                        fixedCount++;
                    }
                    
                    // 确保有链接包装
                    if ($existingImg.parent('a').length === 0) {
                        $existingImg.wrap('<a href="' + imagePath + '" target="_blank"></a>');
                        fixedCount++;
                    }
                }

                // 设置zoom链接
                $imgItem.find('.zoom').attr('href', imagePath);

                // 确保有正确的CSS类
                $imgItem.addClass('isfile').removeClass('show_btn');

                // 隐藏上传按钮
                $imgItem.find('.upload_btn').hide();
            }
        });
        
        console.log('修复了', fixedCount, '个图片显示问题');
        
        // 确保容器可见
        if ($picDetail.find('.img.isfile').length > 0) {
            $picDetail.show();
            $('.upload_file_box').hide();
            $('.upload_select_menu').show();
        }
    },
    
    /**
     * 手动创建上传按钮
     */
    createUploadButton: function() {
        console.log('=== 手动创建上传按钮 ===');

        var $picDetail = $('#PicDetail');
        if ($picDetail.length === 0) {
            console.error('PicDetail容器不存在');
            return;
        }

        // 检查是否已经有可用的上传按钮
        var $existingUploadBtn = $picDetail.find('.img.show_btn');
        if ($existingUploadBtn.length > 0) {
            console.log('已存在上传按钮，无需创建新的');
            return;
        }

        // 获取下一个num值
        var maxNum = 0;
        $picDetail.find('.img').each(function() {
            var num = parseInt($(this).attr('num')) || 0;
            if (num > maxNum) {
                maxNum = num;
            }
        });
        var newNum = maxNum + 1;

        console.log('创建新的上传按钮，num:', newNum);

        // 创建新的上传按钮项
        if (typeof frame_obj !== 'undefined' && frame_obj.multi_img_item) {
            var html = frame_obj.multi_img_item("PicPath[]", newNum, 1, 'products');
            $picDetail.append(html);

            // 显示容器
            $picDetail.show();

            // 重新初始化上传功能
            this.reinitializeUpload();

            console.log('新上传按钮创建完成');
        } else {
            console.error('frame_obj.multi_img_item函数不存在，无法创建上传按钮');
        }
    },

    /**
     * 重新初始化图片上传功能
     */
    reinitializeUpload: function() {
        console.log('=== 重新初始化图片上传功能 ===');

        // 重新初始化frame_obj的上传功能
        if (typeof frame_obj !== 'undefined' && frame_obj.upload_pro_img_init) {
            frame_obj.upload_pro_img_init(1, '#PicDetail', 1);
            console.log('已调用frame_obj.upload_pro_img_init');
        }

        // 重新初始化products_obj的上传功能
        if (typeof products_obj !== 'undefined' && products_obj.function_init && products_obj.function_init.main_picture_upload) {
            products_obj.function_init.main_picture_upload();
            console.log('已调用products_obj.function_init.main_picture_upload');
        }

        // 重新绑定图片库选择按钮
        $('.upload_menu li[data-type="gallery"]').off('click.debug').on('click.debug', function() {
            console.log('图片库选择按钮被点击（调试绑定）');
            GalleryDebugHelper.triggerGallerySelection();
        });

        console.log('重新初始化完成');
    },
    
    /**
     * 强制修复上传按钮丢失问题
     */
    forceFixUploadButton: function() {
        console.log('=== 强制修复上传按钮丢失问题 ===');

        var $picDetail = $('#PicDetail');
        if ($picDetail.length === 0) {
            console.error('PicDetail容器不存在');
            return;
        }

        var hasImages = $picDetail.find('.img.isfile').length > 0;
        var hasUploadBtn = $picDetail.find('.img.show_btn').length > 0;

        console.log('当前状态 - 有图片:', hasImages, '有上传按钮:', hasUploadBtn);

        if (hasImages && !hasUploadBtn) {
            console.log('发现问题：有图片但没有上传按钮，开始强制修复');

            // 强制创建上传按钮
            this.createUploadButton();

            // 确保容器可见
            $picDetail.show();
            $('.upload_file_box').hide();
            $('.upload_select_menu').show();

            // 重新初始化所有相关功能
            this.reinitializeUpload();

            console.log('强制修复完成');
        } else if (!hasImages) {
            console.log('当前没有图片，这是正常状态');
        } else {
            console.log('当前状态正常，有图片且有上传按钮');
        }
    },

    /**
     * 添加调试按钮到页面
     */
    addDebugButtons: function() {
        if ($('#galleryDebugPanel').length > 0) {
            return; // 已存在
        }
        
        var debugHtml = `
            <div id="galleryDebugPanel" style="position: fixed; top: 10px; right: 10px; z-index: 9999; background: white; border: 1px solid #ccc; padding: 10px; border-radius: 5px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); font-size: 12px;">
                <h4 style="margin: 0 0 10px 0; font-size: 14px;">图片库调试工具</h4>
                <button onclick="GalleryDebugHelper.checkGalleryStatus()" style="margin: 2px; padding: 5px 8px; font-size: 11px;">检查状态</button><br>
                <button onclick="GalleryDebugHelper.triggerGallerySelection()" style="margin: 2px; padding: 5px 8px; font-size: 11px;">手动选择图片</button><br>
                <button onclick="GalleryDebugHelper.fixImageDisplay()" style="margin: 2px; padding: 5px 8px; font-size: 11px;">修复图片显示</button><br>
                <button onclick="GalleryDebugHelper.createUploadButton()" style="margin: 2px; padding: 5px 8px; font-size: 11px;">创建上传按钮</button><br>
                <button onclick="GalleryDebugHelper.forceFixUploadButton()" style="margin: 2px; padding: 5px 8px; font-size: 11px; background: #ff6b6b; color: white;">强制修复按钮</button><br>
                <button onclick="GalleryDebugHelper.reinitializeUpload()" style="margin: 2px; padding: 5px 8px; font-size: 11px;">重新初始化</button><br>
                <button onclick="$('#galleryDebugPanel').remove()" style="margin: 2px; padding: 5px 8px; font-size: 11px;">关闭面板</button>
            </div>
        `;
        
        $('body').append(debugHtml);

    }
};

// 自动添加调试面板（仅在开发环境）
$(document).ready(function() {
    // 检查是否在开发环境
    if (window.location.hostname === 'localhost' || window.location.hostname.includes('dev') || window.location.search.includes('debug=1')) {
        setTimeout(function() {
            GalleryDebugHelper.addDebugButtons();
        }, 2000);
    }
});

// 将调试对象暴露到全局，方便在控制台使用
window.GalleryDebugHelper = GalleryDebugHelper;
