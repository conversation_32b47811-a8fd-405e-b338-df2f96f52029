/**
 * 图片回显功能与源代码集成
 * 确保与原有的 frame.js 和 products.js 功能完全兼容
 * 支持三种上传方式：直接上传、图片库选择、拖拽上传
 * 主要功能：与原有系统（frame.js、products.js）的集成
 * 职责：imageEchoIntegration.js 专注于系统集成
 * 重写原有系统的关键函数以确保兼容性
 * 处理图片库选择功能
 * 监听DOM变化和上传事件
 * 自动创建新的上传按钮
 * 管理上传按钮的可见性
 */

var ImageEchoIntegration = {

    // 标记是否正在处理上传
    isProcessingUpload: false,

    // 防止重复初始化
    initialized: false,

    // 上次图片数量
    lastImageCount: 0,

    // 防抖定时器
    debounceTimer: null,

    /**
     * 初始化集成功能
     */
    init: function() {
        if (this.initialized) {
            return;
        }
        this.initialized = true;

        this.hookFrameEvents();
        this.hookProductEvents();
        this.initUploadEvents();
        this.initImageChangeDetection();
        this.updateImageCount();
    },
    
    /**
     * 监听 frame.js 的相关事件
     */
    hookFrameEvents: function() {
        var self = this;

        // 重写 put_img 函数 - 处理拖拽上传和图片库选择
        if (typeof window.frame_obj !== 'undefined' && window.frame_obj.put_img) {
            var originalPutImg = window.frame_obj.put_img;
            window.frame_obj.put_img = function(imgpath) {
                self.isProcessingUpload = true;

                // 确保目标容器可见且没有隐藏类
                self.prepareForUpload();

                // 调用原函数
                var result = originalPutImg.call(this, imgpath);

                // 延迟更新显示状态
                setTimeout(function() {
                    self.isProcessingUpload = false;

                    // 确保图片正确显示
                    self.ensureImagesVisible();

                    // 创建新的上传按钮项
                    self.createNewUploadButton();

                    self.updateUploadButtonVisibility();

                    // 触发图片变化事件
                    self.triggerImageChangeEvent();
                }, 200);

                return result;
            };
        }
        
        // 重写 multi_img_item 函数 - 处理新创建的图片项
        if (typeof window.frame_obj !== 'undefined' && window.frame_obj.multi_img_item) {
            var originalMultiImgItem = window.frame_obj.multi_img_item;
            window.frame_obj.multi_img_item = function($InputName, $Num, $IsMove, $Module) {
                var result = originalMultiImgItem.call(this, $InputName, $Num, $IsMove, $Module);

                // 延迟更新显示状态
                setTimeout(function() {
                    self.updateUploadButtonVisibility();
                }, 50);

                return result;
            };
        }

        // 重写 upload_pro_img_init 函数 - 确保与我们的逻辑兼容
        if (typeof window.frame_obj !== 'undefined' && window.frame_obj.upload_pro_img_init) {
            var originalUploadProImgInit = window.frame_obj.upload_pro_img_init;
            window.frame_obj.upload_pro_img_init = function(type, obj, isParent) {
                // 调用原函数
                var result = originalUploadProImgInit.call(this, type, obj, isParent);

                // 延迟更新我们的显示状态
                setTimeout(function() {
                    if (!self.isProcessingUpload) {
                        self.updateUploadButtonVisibility();
                    }
                }, 50);

                return result;
            };
        }
        
        // 重写 photo_choice_return 函数 - 处理图片库选择
        if (typeof window.frame_obj !== 'undefined' && window.frame_obj.photo_choice_return) {
            var originalPhotoChoiceReturn = window.frame_obj.photo_choice_return;
            window.frame_obj.photo_choice_return = function() {
                self.isProcessingUpload = true;

                // 确保目标容器可见且没有隐藏类
                self.prepareForUpload();

                // 调用原函数
                var result = originalPhotoChoiceReturn.apply(this, arguments);

                // 延迟更新显示状态，给图片加载更多时间
                setTimeout(function() {
                    self.isProcessingUpload = false;

                    // 确保图片正确显示
                    self.ensureImagesVisible();

                    // 创建新的上传按钮项
                    self.createNewUploadButton();

                    self.updateUploadButtonVisibility();
                }, 500);

                return result;
            };
        }
    },

    /**
     * 准备上传环境 - 确保容器可见且没有隐藏类
     */
    prepareForUpload: function() {
        var $picDetail = $('#PicDetail');
        var $uploadFileBox = $('.upload_file_box');

        // 移除所有隐藏类，让源代码正常工作
        $picDetail.find('.img.show_btn').removeClass('hide-upload');

        // 确保容器可见
        $picDetail.show();

        // 如果没有图片，确保有一个可用的上传项
        if ($picDetail.find('.img').length === 0) {
            var html = frame_obj.multi_img_item("PicPath[]", 0, 1, 'products');
            $picDetail.append(html);
        }


    },

    /**
     * 确保图片正确显示
     */
    ensureImagesVisible: function() {
        var $picDetail = $('#PicDetail');
        
        // 检查所有已上传的图片项
        $picDetail.find('.img.isfile').each(function() {
            var $imgItem = $(this);
            var $hiddenInput = $imgItem.find('input[name="PicPath[]"]');
            var imagePath = $hiddenInput.val();
            
            if (imagePath && $hiddenInput.attr('save') == '1') {
                // 确保图片正确显示
                var $previewPic = $imgItem.find('.preview_pic');
                var $existingImg = $previewPic.find('img');
                
                // 使用统一的图片URL处理助手添加OSS处理参数
                var imagePathWithOss = window.ImageUrlHelper ?
                    window.ImageUrlHelper.getThumbnailUrl(imagePath) :
                    imagePath + '?x-oss-process=image/format,webp/resize,m_lfit,h_120,w_120';

                if ($existingImg.length === 0) {
                    // 如果没有img标签，创建完整的图片结构
                    var imgHtml = '<a href="' + imagePath + '" target="_blank">' +
                                 '<img src="' + imagePathWithOss + '" style="max-width: 100%; max-height: 100%;">' +
                                 '</a>';
                    $previewPic.prepend(imgHtml);
                } else {
                    // 确保现有图片的src正确
                    $existingImg.attr('src', imagePathWithOss);

                    // 确保有链接包装
                    if ($existingImg.parent('a').length === 0) {
                        $existingImg.wrap('<a href="' + imagePath + '" target="_blank"></a>');
                    }
                }

                // 设置zoom链接
                $imgItem.find('.zoom').attr('href', imagePath);

                // 确保有正确的CSS类
                $imgItem.addClass('isfile').removeClass('show_btn');

                // 隐藏上传按钮（如果存在）
                $imgItem.find('.upload_btn').hide();


            }
        });
    },

    /**
     * 初始化上传事件监听
     */
    initUploadEvents: function() {
        var self = this;

        // 监听拖拽上传开始
        $('.upload_file_box').on('fileuploadstart', function() {
            self.isProcessingUpload = true;
            self.prepareForUpload();
        });

        // 监听拖拽上传完成
        $('.upload_file_box').on('fileuploadstop', function() {
            setTimeout(function() {
                self.isProcessingUpload = false;
                self.updateUploadButtonVisibility();
            }, 200);
        });

        // 监听直接上传按钮点击
        $(document).on('click', '#PicDetail .upload_btn', function() {
            self.isProcessingUpload = true;
            self.prepareForUpload();
        });

        // 监听图片库选择按钮点击
        $(document).on('click', '.upload_menu li[data-type="gallery"]', function() {
            self.isProcessingUpload = true;
            self.prepareForUpload();
        });
    },

    /**
     * 监听 products.js 的相关事件
     */
    hookProductEvents: function() {
        var self = this;
        
        // 监听产品相关的上传事件
        if (typeof window.products_obj !== 'undefined' && window.products_obj.function_init) {
            // 重写 main_picture_upload 函数
            if (window.products_obj.function_init.main_picture_upload) {
                var originalMainPictureUpload = window.products_obj.function_init.main_picture_upload;
                window.products_obj.function_init.main_picture_upload = function() {
                    var result = originalMainPictureUpload.call(this);
                    
                    // 延迟更新显示状态
                    setTimeout(function() {
                        self.updateUploadButtonVisibility();
                    }, 200);
                    
                    return result;
                };
            }
        }
    },
    
    /**
     * 初始化图片变化检测（替代DOM监听）
     */
    initImageChangeDetection: function() {
        var self = this;

        // 监听隐藏域值变化
        $(document).on('change', '#PicDetail input[name="PicPath[]"]', function() {
            var $input = $(this);

            // 防抖处理
            self.debounceImageChange(function() {
                // 如果是图片上传成功（save=1），则创建新的上传按钮
                if ($input.val() && $input.attr('save') == '1') {
                    self.ensureImagesVisible();
                    self.createNewUploadButton();
                    self.updateUploadButtonVisibility();
                    self.triggerImageChangeEvent();

                    // 调用预览修复功能
                    if (window.ImagePreviewFix) {
                        setTimeout(function() {
                            window.ImagePreviewFix.fixImagePreview($input.closest('.img'));
                            window.ImagePreviewFix.ensurePreviewVisible();
                        }, 100);
                    }
                } else {
                    self.updateUploadButtonVisibility();
                    self.ensureImagesVisible();
                }
            });
        });
    },

    /**
     * 防抖处理图片变化
     */
    debounceImageChange: function(callback) {
        var self = this;

        if (self.debounceTimer) {
            clearTimeout(self.debounceTimer);
        }

        self.debounceTimer = setTimeout(function() {
            callback();
            self.debounceTimer = null;
        }, 200);
    },

    /**
     * 更新图片数量记录
     */
    updateImageCount: function() {
        this.lastImageCount = $('#PicDetail .img.isfile').length;
    },

    /**
     * 检查是否有新图片
     */
    hasNewImages: function() {
        var currentCount = $('#PicDetail .img.isfile').length;
        return currentCount !== this.lastImageCount;
    },

    /**
     * 触发图片变化事件
     */
    triggerImageChangeEvent: function() {
        if (this.hasNewImages()) {
            this.updateImageCount();
            $(document).trigger('imageChanged');
        }
    },
    
    /**
     * 创建新的上传按钮项
     */
    createNewUploadButton: function() {
        var $picDetail = $('#PicDetail');
        if ($picDetail.length === 0) return;

        // 检查是否已经有可用的上传按钮
        var $existingUploadBtn = $picDetail.find('.img.show_btn');
        if ($existingUploadBtn.length > 0) {
            return;
        }

        // 获取下一个num值
        var maxNum = 0;
        $picDetail.find('.img').each(function() {
            var num = parseInt($(this).attr('num')) || 0;
            if (num > maxNum) {
                maxNum = num;
            }
        });
        var newNum = maxNum + 1;

        // 创建新的上传按钮项
        if (typeof frame_obj !== 'undefined' && frame_obj.multi_img_item) {
            var html = frame_obj.multi_img_item("PicPath[]", newNum, 1, 'products');
            $picDetail.append(html);

            // 重新初始化上传功能
            if (typeof frame_obj.upload_pro_img_init === 'function') {
                frame_obj.upload_pro_img_init(1, '#PicDetail', 1);
            }

            // 重新绑定产品上传事件
            if (typeof products_obj !== 'undefined' &&
                products_obj.function_init &&
                products_obj.function_init.main_picture_upload) {
                products_obj.function_init.main_picture_upload();
            }

        }
    },

    /**
     * 更新上传按钮的可见性
     */
    updateUploadButtonVisibility: function() {
        var $picDetail = $('#PicDetail');
        var $uploadFileBox = $('.upload_file_box');

        if ($picDetail.length === 0) return;

        var hasImages = $picDetail.find('.img.isfile').length > 0;
        var totalImages = $picDetail.find('.img').length;

        if (hasImages) {
            // 有图片时：显示图片容器，隐藏上传框，显示上传按钮
            $picDetail.show();
            $uploadFileBox.hide();
            $('.upload_select_menu').show();

            // 移除所有隐藏类，显示上传按钮
            $picDetail.find('.img.show_btn').removeClass('hide-upload');

        } else {
            // 没有图片时：隐藏图片容器，显示上传框，但保持上传按钮可见以支持图片库选择
            $picDetail.hide();
            $uploadFileBox.show();
            $('.upload_select_menu').hide();

            // 不完全隐藏上传按钮，保持其功能性
            $picDetail.find('.img.show_btn').removeClass('hide-upload');
        }

        // 重新应用样式
        if (typeof ProductImageEcho !== 'undefined' && ProductImageEcho.forceApplyStyles) {
            ProductImageEcho.forceApplyStyles();
        }
    }
};

// 当页面加载完成后初始化
$(document).ready(function() {
    // 延迟初始化，确保其他脚本已加载
    setTimeout(function() {
        ImageEchoIntegration.init();

        // 初始更新一次
        setTimeout(function() {
            if (ImageEchoIntegration.initialized) {
                ImageEchoIntegration.updateUploadButtonVisibility();
            }
        }, 200);
    }, 1000);
});

// 暴露到全局作用域，方便调试
window.ImageEchoIntegration = ImageEchoIntegration;
