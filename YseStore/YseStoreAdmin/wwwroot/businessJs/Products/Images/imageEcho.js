/**
 * 产品图片回显功能
 * 用于在编辑产品时显示已存在的图片
 * 主要功能：处理图片的显示、样式和布局
 * 职责： imageEcho.js 专注于UI和样式
 * 初始化已存在图片的显示
 * 管理CSS样式和Grid布局
 * 处理图片的Alt属性编辑
 * 处理图片删除功能
 * 创建空白上传项的HTML结构
 */
var ProductImageEcho = {

    // 防止重复初始化
    initialized: false,

    // 上次图片数量，用于检测变化
    lastImageCount: 0,

    /**
     * 初始化图片回显功能
     */
    init: function() {
        // 防止重复初始化
        if (this.initialized) {
            return;
        }
        this.initialized = true;

        this.forceApplyStyles();
        this.initExistingImages();
        this.bindEvents();
        this.updateImageNumbers();
        this.updateImageCount();
    },

    /**
     * 强制应用样式
     */
    forceApplyStyles: function() {
        var $picDetail = $('#PicDetail');

        // 确保容器有正确的CSS类
        if (!$picDetail.hasClass('pro_multi_img')) {
            $picDetail.addClass('pro_multi_img');
        }

        var hasImages = $picDetail.find('.img.isfile').length > 0;
        var onlyUploadBtn = $picDetail.find('.img').length === 1 && $picDetail.find('.img.show_btn').length === 1;

        // 根据是否有图片应用不同的grid布局
        if (hasImages) {
            // 有图片时的grid布局
            $picDetail.css({
                'display': 'grid',
                'grid-auto-flow': 'row dense',
                'grid-gap': '18px',
                'grid-template-columns': 'repeat(6, 1fr)',
                'margin': '0',
                'grid-template-rows': 'repeat(2, 1fr)'
            });
        } else if (onlyUploadBtn) {
            // 只有上传按钮时的布局
            $picDetail.css({
                'display': 'grid',
                'grid-auto-flow': 'row dense',
                'grid-gap': '18px',
                'grid-template-columns': 'repeat(3, 1fr)',
                'margin': '0',
                'grid-template-rows': '1fr'
            });

            // 让上传按钮占据更大的空间
            $picDetail.find('.img.show_btn').css({
                'grid-column': '1 / 3',
                'grid-row': '1 / 2'
            });
        }

        // 确保图片项有正确的样式
        $picDetail.find('.img.isfile').css({
            'width': 'auto',
            'height': 'auto',
            'overflow': 'visible',
            'margin': '0'
        });

        $picDetail.find('.img.show_btn').css({
            'width': 'auto',
            'height': 'auto',
            'overflow': 'visible',
            'margin': '0'
        });
        
    },
    
    /**
     * 初始化已存在的图片显示
     */
    initExistingImages: function() {
        var self = this;

        // 确保图片容器显示并添加正确的CSS类
        var $picDetail = $('#PicDetail');
        if ($picDetail.find('.img.isfile').length > 0) {
            $picDetail.show();
            // 确保容器有正确的CSS类
            if (!$picDetail.hasClass('pro_multi_img')) {
                $picDetail.addClass('pro_multi_img');
            }
        }

        // 为每个已存在的图片初始化功能
        $picDetail.find('.img.isfile').each(function(index) {
            var $imgItem = $(this);
            var $hiddenInput = $imgItem.find('input[name="PicPath[]"]');
            var imagePath = $hiddenInput.val();

            if (imagePath && $hiddenInput.attr('save') == '1') {
                // 确保图片正确显示
                var $img = $imgItem.find('img');
                // 使用统一的图片URL处理助手添加OSS处理参数
                var imagePathWithOss = window.ImageUrlHelper ?
                    window.ImageUrlHelper.getThumbnailUrl(imagePath) :
                    imagePath + '?x-oss-process=image/format,webp/resize,m_lfit,h_120,w_120';

                if ($img.length === 0) {
                    // 如果没有img标签，创建一个
                    $imgItem.find('.preview_pic').prepend('<img src="' + imagePathWithOss + '" style="max-width: 100%; max-height: 100%;">');
                } else {
                    // 确保现有图片的src正确
                    $img.attr('src', imagePathWithOss);
                }

                // 设置zoom链接
                $imgItem.find('.zoom').attr('href', imagePath);

                // 确保有正确的CSS类
                $imgItem.addClass('isfile').removeClass('show_btn');

                // 隐藏上传按钮（如果存在）
                $imgItem.find('.upload_btn').hide();

                // 创建图片链接容器（如果不存在）
                if ($imgItem.find('.preview_pic > a').length === 0) {
                    $imgItem.find('.preview_pic img').wrap('<a href="' + imagePath + '" target="_blank"></a>');
                }


            }
        });

        // 确保上传按钮项有正确的类和样式
        var hasImages = $picDetail.find('.img.isfile').length > 0;

        $picDetail.find('.img.show_btn').each(function() {
            var $showBtn = $(this);
            $showBtn.removeClass('isfile');

            // 确保上传按钮容器有正确的样式
            var $previewPic = $showBtn.find('.preview_pic');
            $previewPic.css({
                'background-color': 'var(--GlobalPicUploadBgColor, #f6fdff)',
                'border': '1px var(--primaryColor, #4BA0FC) dashed',
                'box-sizing': 'border-box'
            });

            // 根据是否有图片决定是否显示上传按钮、容器和文字
            var $uploadBtn = $showBtn.find('.upload_btn');
            var $uploadBox = $showBtn.find('.upload_box.preview_pic');
            var $uploadTxt = $showBtn.find('.upload_txt');

            if (hasImages) {
                // 有图片时显示上传容器、透明的上传按钮和文字
                $uploadBox.css({
                    'display': 'block'
                });
                $uploadBtn.css({
                    'display': 'block',
                    'opacity': '0',
                    'background': 'none',
                    'border': 'none'
                });
                $uploadTxt.css({
                    'display': 'block',
                    'color': 'var(--GlobalBtnSimpleColor, #7d8d9e)'
                });
                $uploadTxt.find('p:first-child').css({
                    'color': 'var(--primaryColor, #4BA0FC)'
                });
            } else {
                // 没有图片时隐藏上传容器、上传按钮和文字
                $uploadBox.css({
                    'display': 'none'
                });
                $uploadBtn.css({
                    'display': 'none'
                });
                $uploadTxt.css({
                    'display': 'none'
                });
            }
        });



        // 初始化上传功能
        if (typeof frame_obj !== 'undefined') {
            frame_obj.upload_pro_img_init(1, '#PicDetail', 1);
        }

        // 初始化产品相关功能
        if (typeof products_obj !== 'undefined' && products_obj.function_init) {
            products_obj.function_init.main_picture_upload();
            products_obj.function_init.show_btn_init();
        }
    },
    
    /**
     * 绑定事件
     */
    bindEvents: function() {
        var self = this;
        
        // 绑定删除按钮事件
        $(document).on('click', '#PicDetail .img .del', function(e) {
            e.preventDefault();
            var $imgItem = $(this).closest('.img');
            var $picDetail = $('#PicDetail');
            
            // 删除图片项
            $imgItem.remove();
            
            // 重新编号
            self.updateImageNumbers();
            
            // 如果没有图片了，隐藏容器
            if ($picDetail.find('.img').length <= 1) { // 只剩下空白上传项
                $picDetail.hide();
            }
            
            // 重新初始化上传功能
            if (typeof frame_obj !== 'undefined') {
                frame_obj.upload_pro_img_init(1, '#PicDetail', 1);
            }
        });
        
        // 绑定Alt编辑功能
        $(document).on('click', '#PicDetail .img .alt_edit', function(e) {
            e.preventDefault();
            var $imgItem = $(this).closest('.img');
            var $altInput = $imgItem.find('input[name="Alt[]"]');
            var currentAlt = $altInput.val();
            
         
        });
    },
    
    /**
     * 更新图片编号
     */
    updateImageNumbers: function() {
        $('#PicDetail .img').each(function(index) {
            $(this).attr('num', index);
        });
    },

    /**
     * 更新图片数量记录
     */
    updateImageCount: function() {
        this.lastImageCount = $('#PicDetail .img.isfile').length;
    },

    /**
     * 检查是否有新图片
     */
    hasNewImages: function() {
        var currentCount = $('#PicDetail .img.isfile').length;
        return currentCount !== this.lastImageCount;
    },

    /**
     * 仅在有新图片时触发检测
     */
    checkForNewImages: function() {
        if (this.hasNewImages()) {
            this.updateImageCount();
            this.forceApplyStyles();
            this.initExistingImages();
            return true;
        }
        return false;
    },
    
    /**
     * 添加新图片后的处理
     * @param {string} imagePath 图片路径
     * @param {string} alt Alt属性
     */
    addNewImage: function(imagePath, alt) {
        if (!imagePath) return;
        
        var $picDetail = $('#PicDetail');
        var imageCount = $picDetail.find('.img').length - 1; // 减去空白上传项
        
        // 找到最后一个空白上传项
        var $emptyItem = $picDetail.find('.img.show_btn').last();
        
        if ($emptyItem.length > 0 && $emptyItem.find('input[name="PicPath[]"]').attr('save') == '0') {
            // 更新空白项为图片项
            $emptyItem.find('.preview_pic').prepend('<img src="' + imagePath + '?x-oss-process=image/format,webp/resize,m_lfit,h_270,w_275' + '" style="max-width: 100%; max-height: 100%;">');
            $emptyItem.find('input[name="PicPath[]"]').val(imagePath).attr('data-value', imagePath).attr('save', '1');
            $emptyItem.find('input[name="Alt[]"]').val(alt || '');
            $emptyItem.find('.zoom').attr('href', imagePath);
            $emptyItem.find('.upload_btn').hide();
            $emptyItem.addClass('isfile').removeClass('show_btn');
            
            // 添加新的空白上传项
            var newNum = imageCount + 1;
            var newItemHtml = this.createEmptyImageItem(newNum);
            $picDetail.append(newItemHtml);
        }
        
        // 显示容器
        $picDetail.show();
        
        // 重新初始化功能
        this.updateImageNumbers();
        if (typeof frame_obj !== 'undefined') {
            frame_obj.upload_pro_img_init(1, '#PicDetail', 1);
        }
    },
    
    /**
     * 创建空白图片上传项
     * @param {number} num 编号
     * @returns {string} HTML字符串
     */
    createEmptyImageItem: function(num) {
        return `
            <dl class="img show_btn" num="${num}">
                <dt class="upload_box preview_pic">
                    <input type="button" class="btn_ok upload_btn" name="submit_button" value="上传图片" tips="">
                    <input type="hidden" name="PicPath[]" value="" data-value="" save="0">
                </dt>
                <dd class="pic_btn">
                    <span class="input_checkbox_box">
                        <span class="input_checkbox"><input type="checkbox"></span>
                    </span>
                    <a href="javascript:;" class="myorder">
                        <i class="icon_multi_myorder"></i>
                    </a>
                    <a href="javascript:;" class="video_edit"><i class="icon_video_edit"></i></a>
                    <a href="javascript:;" class="alt_edit">Alt</a>
                    <a href="javascript:;" class="video_seo_btn">SEO</a>
                    <input type="hidden" name="Alt[]" value="">
                    <a href="javascript:;" class="zoom" target="_blank">
                        <i class="icon_multi_view"></i>
                    </a>
                    <a href="javascript:;" class="del" rel="del">
                        <i class="icon_multi_delete"></i>
                    </a>
                </dd>
                <dd class="upload_txt">
                    <p>上传图片</p>
                    <p>或拖入本地图片</p>
                </dd>
            </dl>
        `;
    }
};

// 当页面加载完成后初始化
$(document).ready(function() {
    // 延迟初始化，确保其他脚本已加载
    setTimeout(function() {
        ProductImageEcho.init();
    }, 500);

    // 监听窗口大小变化，重新应用样式
    $(window).on('resize', function() {
        if (ProductImageEcho.initialized) {
            ProductImageEcho.forceApplyStyles();
        }
    });

    // 监听图片变化事件（由其他脚本触发）
    $(document).on('imageChanged', function() {
        if (ProductImageEcho.initialized) {
            ProductImageEcho.checkForNewImages();
        }
    });
});
