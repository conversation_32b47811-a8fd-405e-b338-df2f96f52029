/**
 * 产品导入相关功能
 */
const productImport = {
    /**
     * 从CSV文件导入产品
     */
    ToImportProductCsv: function() {
        try {
            // 从隐藏字段获取已上传的文件路径
            const filePath = document.getElementById('excel_path').value;
            if (!filePath) {
                this.showError('请选择要上传的CSV文件');
                return;
            }
            
            // 显示上传进度界面
            this.showUploadProgress();
            
            // 准备FormData对象
            const formData = new FormData();
            formData.append('filePath', filePath);
            
            // 发送AJAX请求到后端API
            fetch('/api/ProductImport/ImportFromCsv', {
                method: 'POST',
                body: formData
            })
            .then(response => {
                if (!response.ok) {
                    return response.json().then(data => {
                        throw new Error(data.message || '导入失败，请稍后重试');
                    });
                }
                return response.json();
            })
            .then(data => {
                if (data.success) {
                    // 导入成功，显示结果信息
                    this.showImportResults(data.data);
                } else {
                    // 导入失败，显示错误信息
                    this.showError(data.message || '导入失败，请稍后重试');
                }
            })
            .catch(error => {
                this.hideUploadProgress();
                console.error('导入产品时发生错误:', error);
                this.showError(error.message || '导入产品时发生错误，请稍后重试');
            });
        } catch (error) {
            this.hideUploadProgress();
            console.error('导入产品时发生错误:', error);
            this.showError('导入产品时发生错误，请稍后重试');
        }
    },
    
    /**
     * 显示上传进度界面
     */
    showUploadProgress: function() {
        // 显示圆形进度条
        const circleContainer = document.getElementById('box_circle_container');
        if (circleContainer) {
            circleContainer.style.display = 'block';
        }
        
        // 设置进度条初始值
        const progressCircle = document.querySelector('.box_circle_progress');
        if (progressCircle) {
            progressCircle.setAttribute('data-percent', '0');
            progressCircle.setAttribute('data-animate', '1');
            
            // 更新进度显示
            const progressNumber = progressCircle.querySelector('.circle_progress_number span');
            if (progressNumber) {
                progressNumber.textContent = '0';
            }
        }
        
        // 设置进度文本
        const progressText = document.querySelector('.circle_progress_text');
        if (progressText) {
            progressText.textContent = '正在上传并处理CSV文件，请稍候...';
        }
        
        // 隐藏关闭提示
        const closeTips = document.querySelector('.container_close_tips');
        if (closeTips) {
            closeTips.classList.remove('hide');
        }
        
        // 模拟进度增长
        this.startProgressAnimation();
    },
    
    /**
     * 隐藏上传进度界面
     */
    hideUploadProgress: function() {
        // 停止进度动画
        if (this.progressInterval) {
            clearInterval(this.progressInterval);
            this.progressInterval = null;
        }
        
        // 隐藏圆形进度条
        const circleContainer = document.getElementById('box_circle_container');
        if (circleContainer) {
            circleContainer.style.display = 'none';
        }
    },
    
    /**
     * 开始进度动画
     */
    progressInterval: null,
    startProgressAnimation: function() {
        let percent = 0;
        
        // 清除之前的计时器
        if (this.progressInterval) {
            clearInterval(this.progressInterval);
        }
        
        // 创建新的计时器，模拟进度
        this.progressInterval = setInterval(() => {
            // 缓慢增加进度，最多到95%
            if (percent < 95) {
                // 前期快速增长，后期缓慢
                const increment = percent < 30 ? 5 : (percent < 60 ? 3 : 1);
                percent += increment;
                this.updateProgress(percent);
            }
        }, 800);
    },
    
    /**
     * 更新进度显示
     */
    updateProgress: function(percent) {
        const progressCircle = document.querySelector('.box_circle_progress');
        if (!progressCircle) return;
        
        // 更新百分比属性
        progressCircle.setAttribute('data-percent', percent);
        
        // 更新显示的数字
        const progressNumber = progressCircle.querySelector('.circle_progress_number span');
        if (progressNumber) {
            progressNumber.textContent = Math.round(percent);
        }
        
        // 更新右侧半圆
        const rightCircle = progressCircle.querySelector('.rightcircle');
        if (rightCircle) {
            if (percent <= 50) {
                rightCircle.style.transform = `rotate(${percent * 3.6}deg)`;
            } else {
                rightCircle.style.transform = 'rotate(180deg)';
            }
        }
        
        // 更新左侧半圆
        const leftCircle = progressCircle.querySelector('.leftcircle');
        if (leftCircle) {
            if (percent > 50) {
                leftCircle.style.transform = `rotate(${(percent - 50) * 3.6}deg)`;
            } else {
                leftCircle.style.transform = 'rotate(0deg)';
            }
        }
    },
    
    /**
     * 显示导入结果
     */
    showImportResults: function(results) {
        // 停止进度动画并设置为100%
        if (this.progressInterval) {
            clearInterval(this.progressInterval);
            this.progressInterval = null;
            this.updateProgress(100);
        }
        
        // 更新进度文本
        const progressText = document.querySelector('.circle_progress_text');
        if (progressText) {
            progressText.textContent = '导入完成！';
        }
        
        // 隐藏进度容器，显示结果容器
        const circleContainer = document.getElementById('box_circle_container');
        if (circleContainer) {
            circleContainer.style.display = 'none';
        }
        
        // 显示导入完成信息
        const completedBox = document.querySelector('.box_import_completed');
        if (completedBox) {
            completedBox.style.display = 'block';
            
            // 更新成功数量
            const successCount = completedBox.querySelector('.item_done span');
            if (successCount) {
                successCount.textContent = results.successCount || 0;
            }
            
            // 更新失败数量
            const failCount = completedBox.querySelector('.item_fail span');
            if (failCount) {
                failCount.textContent = results.failedCount || 0;
            }
            
            // 显示或隐藏失败列表
            const failList = completedBox.querySelector('.fail_list');
            if (failList) {
                if (results.failedCount > 0 && results.failedItems && results.failedItems.length > 0) {
                    failList.style.display = 'block';
                    
                    // 更新失败原因列表
                    const failContent = failList.querySelector('.list_content');
                    if (failContent) {
                        failContent.innerHTML = '';
                        results.failedItems.forEach(item => {
                            const div = document.createElement('div');
                            div.textContent = item;
                            failContent.appendChild(div);
                        });
                    }
                    
                    // 显示下载失败列表按钮
                    const failButton = completedBox.querySelector('.btn_import_fail');
                    if (failButton) {
                        failButton.style.display = 'block';
                        
                        // 添加下载失败列表事件
                        failButton.onclick = () => {
                            this.downloadFailedList(results.failedItems);
                        };
                    }
                } else {
                    failList.style.display = 'none';
                }
            }
        }
    },
    
    /**
     * 下载失败列表
     */
    downloadFailedList: function(failedItems) {
        if (!failedItems || failedItems.length === 0) {
            return;
        }
        
        try {
            // 创建CSV内容
            let csvContent = "数据,错误原因\n";
            failedItems.forEach(item => {
                csvContent += `"${item.replace(/"/g, '""')}"\n`;
            });
            
            // 创建Blob对象
            const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
            
            // 创建下载链接
            const link = document.createElement('a');
            const url = URL.createObjectURL(blob);
            
            // 设置下载属性
            link.setAttribute('href', url);
            link.setAttribute('download', `产品导入失败列表_${new Date().toISOString().slice(0, 10)}.csv`);
            link.style.visibility = 'hidden';
            
            // 添加到文档并触发点击
            document.body.appendChild(link);
            link.click();
            
            // 清理
            document.body.removeChild(link);
            URL.revokeObjectURL(url);
        } catch (error) {
            console.error('下载失败列表时出错:', error);
            this.showError('下载失败列表时出错，请稍后重试');
        }
    },
    
    /**
     * 显示错误信息
     */
    showError: function(message) {
        // 使用现有的错误提示机制，或者使用alert
        alert(message);
    }
};

// 在dom加载完成后绑定事件
document.addEventListener('DOMContentLoaded', function() {
    // 绑定保存按钮的点击事件
    const submitButton = document.querySelector('.upload_products_box .btn_submit');
    if (submitButton) {
        submitButton.onclick = function() {
            productImport.ToImportProductCsv();
            return false; // 阻止默认行为
        };
    }
}); 