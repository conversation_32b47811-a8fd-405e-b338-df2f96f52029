/**
 * 产品详情数据加载器
 * 用于从页面数据中获取产品详情并回显到表单中
 */
var productDetailLoader = (function () {
    // 内部状态
    let state = {
        productDetail: null,
        isLoaded: false,
        initAttempts: 0,
        maxInitAttempts: 5
    };

    /**
     * 初始化函数
     */
    function init() {
        // 确保在页面完全加载（包括图片和其他资源）后执行
        if (document.readyState === 'complete') {
            initProductData();
        } else {
            window.addEventListener('load', function () {
                initProductData();
            });

            // 添加备用初始化方法，以防load事件未触发
            setTimeout(function () {
                if (!state.isLoaded && state.initAttempts < state.maxInitAttempts) {
                    state.initAttempts++;
                    initProductData();
                }
            }, 500);
        }
    }

    /**
     * 初始化产品数据
     */
    function initProductData() {
        // 防止重复初始化
        if (state.isLoaded) {
            return;
        }

        // 从全局变量中获取产品详情数据
        if (window.product_data && product_data.product_detail) {
            state.productDetail = product_data.product_detail;
            state.isLoaded = true;
            fillProductForm(state.productDetail);
        }
    }
    
    /**
     * 填充产品表单 - 核心方法
     * @param {Object} productDetail - 产品详情数据
     */
    function fillProductForm(productDetail) {
        // 分析数据结构
        if (!productDetail) {
            return;
        }

        // 检查并验证Product对象
        if (!productDetail.Product && !productDetail.product) {
            return;
        }

        try {
            // 根据保存时的字段结构，确保使用正确的属性名
            const product = productDetail.Product;

            // 设置产品ID到隐藏字段
            const proIdField = document.getElementById('ProId');
            if (proIdField) {
                proIdField.value = product.ProId;
            }
            
            // 回显关联主图方式和主属性
            if (productDetail.RelateMethod) {
                // 先确保box_main_image容器显示
                const boxMainImage = document.getElementById('box_main_image');
                if (boxMainImage) {
                    boxMainImage.style.display = 'block';
                }

                // 选择对应的关联方式单选按钮
                const relateMethodInput = document.querySelector(`input[name="RelateMethod"][value="${productDetail.RelateMethod}"]`);
                if (relateMethodInput) {
                    // 设置checked属性
                    relateMethodInput.checked = true;

                    // 找到父容器并添加checked类
                    const radioItem = relateMethodInput.closest('.item');
                    if (radioItem) {
                        // 移除所有item的checked类
                        document.querySelectorAll('.box_relate_method .item').forEach(item => {
                            item.classList.remove('checked');
                        });

                        // 添加checked类到当前选中的item
                        radioItem.classList.add('checked');
                    }

                    // 根据关联方式显示或隐藏主属性选择器
                    const boxMainAttr = document.querySelector('.box_main_attr');
                    const boxMainAssociate = document.querySelector('.box_main_associate');

                    if (productDetail.RelateMethod === 'multiple-single') {
                        // 多个属性关联单张主图
                        if (boxMainAttr) boxMainAttr.style.display = 'none';
                        if (boxMainAssociate) boxMainAssociate.style.display = 'none';
                    } else {
                        // 单个属性关联单张主图、单个属性关联多张主图
                        if (boxMainAttr) boxMainAttr.style.display = 'block';
                        if (boxMainAssociate) boxMainAssociate.style.display = 'block';
                    }

                    // 模拟点击事件触发UI更新
                    try {
                        radioItem.click();
                    } catch (e) {
                        console.error('触发关联方式点击事件失败:', e);
                    }
                }
            }
            // 回显主属性
            if (productDetail.MainAttrId && productDetail.MainAttrId > 0) {
                setTimeout(() => {
                    try {
                        // 尝试选择主属性下拉框中对应的选项
                        const mainAttrSelect = document.querySelector('select[name="MainAttr"]');
                        if (mainAttrSelect) {
                            // 先检查有没有data-id属性的选项
                            let optionFound = false;
                            const options = mainAttrSelect.querySelectorAll('option');

                            for (let i = 0; i < options.length; i++) {
                                const dataId = parseInt(options[i].getAttribute('data-id')) || 0;
                                if (dataId === productDetail.MainAttrId) {
                                    options[i].selected = true;
                                    optionFound = true;
                                    break;
                                }
                            }

                            // 如果没找到data-id匹配的选项，尝试直接匹配选项值
                            if (!optionFound) {
                                mainAttrSelect.value = productDetail.MainAttrId.toString();
                            }

                            // 触发change事件
                            const event = new Event('change', {bubbles: true});
                            mainAttrSelect.dispatchEvent(event);
                        }
                    } catch (e) {
                        console.error('设置主属性选择器失败:', e);
                    }
                }, 500); // 延迟执行，确保属性选择器已经被填充
            }
        } catch (error) {
            console.error('填充产品表单时出错:', error);
        }
    }

    /**
     * 获取产品详情数据
     * @returns {Object|null} 产品详情数据
     */
    function getProductDetail() {
        return state.productDetail;
    }

    /**
     * 检查产品详情数据是否已加载
     * @returns {boolean} 产品详情数据是否已加载
     */
    function isProductDetailLoaded() {
        return state.isLoaded;
    }


    // 公开API
    return {
        init: init,
        getProductDetail: getProductDetail,
        isProductDetailLoaded: isProductDetailLoaded
    };
})();

// 尽早初始化模块
productDetailLoader.init();
