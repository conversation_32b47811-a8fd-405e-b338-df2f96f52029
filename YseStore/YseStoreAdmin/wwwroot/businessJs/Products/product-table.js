/**
 * 产品表格相关功能
 */
const productTableModule = {
    /**
     * 初始化产品表格功能
     */
    init: function() {
        // 初始化全选功能
        this.initSelectAll();
        
        // 初始化删除功能
        this.initDeleteFunction();
        
        // 初始化下架功能
        this.initSoldOutFunction();
        
        // 初始化其他功能按钮
        this.initOtherButtons();
        
        // 初始化标签管理相关功能
        this.initTagsManagement();
    },
    
    /**
     * 初始化全选功能
     */
    initSelectAll: function() {
        // 全选/取消全选
        $('input[name=select_all]').on('click', function() {
            const isChecked = $(this).prop('checked');
            $('input[name=select]').prop('checked', isChecked);
            
            // 更新已选择数量
            productTableModule.updateSelectedCount();
        });
        
        // 单个选择事件
        $('input[name=select]').on('click', function() {
            productTableModule.updateSelectedCount();
            
            // 检查是否全部选中
            const allChecked = $('input[name=select]').length === $('input[name=select]:checked').length;
            $('input[name=select_all]').prop('checked', allChecked);
        });
    },
    
    /**
     * 更新已选择的数量
     */
    updateSelectedCount: function() {
        const selectedCount = $('input[name=select]:checked').length;
        $('.table_menu_button .open > span').text(selectedCount);
    },
    
    /**
     * 初始化删除功能
     */
    initDeleteFunction: function() {
        // 确保自定义删除按钮可以正常工作
        $('.table_menu_button .custom_del').off('click').on('click', function() {
            productTableModule.batchDelete();
        });
    },
    
    /**
     * 批量删除产品
     */
    batchDelete: function() {
        // 获取选中的产品ID
        const selectedProducts = $('input[name=select]:checked');
        if (selectedProducts.length === 0) {
            global_obj.win_alert({
                title: "提示",
                subtitle: "请至少选择一个产品",
                confirmBtn: "确定"
            });
            return;
        }
        
        // 收集所有选中的ID
        const productIds = [];
        selectedProducts.each(function() {
            productIds.push($(this).val());
        });
        
        // 显示确认弹窗
        global_obj.win_alert({
            title: "批量删除确认",
            subtitle: `确定要删除选中的 ${productIds.length} 个产品吗？`,
            confirmBtn: "删除",
            confirmBtnClass: "btn_warn",
            cancelBtn: "取消"
        }, function() {
            productTableModule.performDelete(productIds);
        }, 'confirm');
    },
    
    /**
     * 执行删除操作
     * @param {Array} ids 要删除的产品ID数组
     */
    performDelete: function(ids) {
        if (!ids || ids.length === 0) return;
        
        // 显示加载中
        customize_pop.loading();
        
        // 获取防伪令牌
        const token = $('input[name="__RequestVerificationToken"]').val() || 
                     $('input:hidden[name="__RequestVerificationToken"]').val();
        
        // 发送删除请求
        $.ajax({
            url: '/api/Product/DeleteBatch',
            type: 'POST',
            contentType: 'application/x-www-form-urlencoded',
            data: "ids=" + ids.join(','),
            headers: {
                "RequestVerificationToken": token
            },
            success: function(result) {
                customize_pop.loadingClose();
                
                if (result.success) {
                    // 显示成功消息
                    customize_pop.success(result.message || '产品删除成功');
                    
                    // 延迟刷新页面
                    setTimeout(function() {
                        window.location.reload();
                    }, 1000);
                } else {
                    customize_pop.error(result.message || '产品删除失败');
                }
            },
            error: function(xhr, status, error) {
                console.error("删除请求错误:", error);
                customize_pop.loadingClose();
                customize_pop.error('删除请求失败: ' + error);
            }
        });
    },
    
    /**
     * 初始化下架功能
     */
    initSoldOutFunction: function() {
        // 确保自定义下架按钮可以正常工作
        $('.table_menu_button .custom_sold_out').off('click').on('click', function() {
            productTableModule.batchSoldOut();
        });

        // 确保自定义上架按钮可以正常工作
        $('.table_menu_button .custom_sold_in').off('click').on('click', function() {
            productTableModule.batchSoldIn();
        });
    },
    
    /**
     * 批量下架产品
     */
    batchSoldOut: function() {
        // 获取选中的产品ID
        const selectedProducts = $('input[name=select]:checked');
        if (selectedProducts.length === 0) {
            global_obj.win_alert({
                title: "提示",
                subtitle: "请至少选择一个产品",
                confirmBtn: "确定"
            });
            return;
        }

        // 收集所有选中的ID
        const productIds = [];
        selectedProducts.each(function() {
            productIds.push($(this).val());
        });

        // 显示确认弹窗
        global_obj.win_alert({
            title: "批量下架确认",
            subtitle: `确定要下架选中的 ${productIds.length} 个产品吗？`,
            confirmBtn: "下架",
            confirmBtnClass: "btn_warn",
            cancelBtn: "取消"
        }, function() {
            productTableModule.performSoldOut(productIds, 1); // 1表示下架
        }, 'confirm');
    },

    /**
     * 批量上架产品
     */
    batchSoldIn: function() {
        // 获取选中的产品ID
        const selectedProducts = $('input[name=select]:checked');
        if (selectedProducts.length === 0) {
            global_obj.win_alert({
                title: "提示",
                subtitle: "请至少选择一个产品",
                confirmBtn: "确定"
            });
            return;
        }

        // 收集所有选中的ID
        const productIds = [];
        selectedProducts.each(function() {
            productIds.push($(this).val());
        });

        // 显示确认弹窗
        global_obj.win_alert({
            title: "批量上架确认",
            subtitle: `确定要上架选中的 ${productIds.length} 个产品吗？`,
            confirmBtn: "上架",
            confirmBtnClass: "btn_success",
            cancelBtn: "取消"
        }, function() {
            productTableModule.performSoldOut(productIds, 0); // 0表示上架
        }, 'confirm');
    },
    
    /**
     * 执行下架/上架操作
     * @param {Array} ids 要操作的产品ID数组
     * @param {Number} soldOut 1表示下架，0表示上架
     */
    performSoldOut: function(ids, soldOut) {
        if (!ids || ids.length === 0) return;
        
        // 显示加载中
        customize_pop.loading();
        
        // 获取防伪令牌
        const token = $('input[name="__RequestVerificationToken"]').val() || 
                     $('input:hidden[name="__RequestVerificationToken"]').val();
        
        // 发送下架/上架请求
        $.ajax({
            url: '/api/Product/UpdateSoldOutStatus',
            type: 'POST',
            contentType: 'application/x-www-form-urlencoded',
            data: `ids=${ids.join(',')}&soldOut=${soldOut}`,
            headers: {
                "RequestVerificationToken": token
            },
            success: function(result) {
                customize_pop.loadingClose();
                
                if (result.success) {
                    // 显示成功消息
                    const actionText = soldOut === 1 ? '下架' : '上架';
                    customize_pop.success(result.message || `产品${actionText}成功`);
                    
                    // 延迟刷新页面
                    setTimeout(function() {
                        window.location.reload();
                    }, 1000);
                } else {
                    customize_pop.error(result.message || '操作失败');
                }
            },
            error: function(xhr, status, error) {
                console.error("操作请求错误:", error);
                customize_pop.loadingClose();
                customize_pop.error('操作请求失败: ' + error);
            }
        });
    },
    
    /**
     * 初始化其他功能按钮
     */
    initOtherButtons: function() {
        // 批量修改价格
        $('.table_menu_button .batch_price').on('click', function() {
            // TODO: 实现批量修改价格功能
            console.log("批量修改价格功能待实现");
        });
        
        // 批量添加标签
        $('.table_menu_button .bat_add_tags').off('click').on('click', function() {
            productTableModule.showAddTagsDialog();
        });
        
        // 批量删除标签
        $('.table_menu_button .bat_del_tags').off('click').on('click', function() {
            productTableModule.showDeleteTagsDialog();
        });
    },
    
    /**
     * 显示添加标签对话框
     */
    showAddTagsDialog: function() {
        // 获取选中的产品ID
        const selectedProducts = $('input[name=select]:checked');
        if (selectedProducts.length === 0) {
            global_obj.win_alert({
                title: "提示",
                subtitle: "请至少选择一个产品",
                confirmBtn: "确定"
            });
            return;
        }
        
        // 收集所有选中的ID
        const productIds = [];
        selectedProducts.each(function() {
            productIds.push($(this).val());
        });
        
        // 创建对话框内容
        let dialogContent = `
            <div class="pop_form">
                <h1>批量添加标签</h1>
                <p class="hint">为 ${productIds.length} 个选中的产品添加标签，多个标签请用英文逗号分隔</p>
                <div class="rows">
                    <label>标签名称：</label>
                    <span class="input">
                        <textarea id="tagNames" style="width: 100%; height: 80px; padding: 5px;" placeholder="输入标签名称，多个标签请用英文逗号分隔"></textarea>
                    </span>
                </div>
                <div class="rows">
                    <label></label>
                    <span class="submit">
                        <input type="button" value="确定" id="confirmAddTags" class="btn_green">
                        <input type="button" value="取消" id="cancelAddTags" class="btn_gray">
                    </span>
                </div>
            </div>
        `;
        
        // 显示自定义对话框
        let popupBox = $('<div class="pop_form_box"></div>').append(dialogContent);
        $('body').append(popupBox);
        
        // 绑定确定按钮事件
        $('#confirmAddTags').on('click', function() {
            const tagNames = $('#tagNames').val().trim();
            if (!tagNames) {
                global_obj.win_alert({
                    title: "提示",
                    subtitle: "请输入标签名称",
                    confirmBtn: "确定"
                });
                return;
            }
            
            // 执行添加标签操作
            productTableModule.performAddTags(productIds, tagNames);
            
            // 关闭对话框
            popupBox.remove();
        });
        
        // 绑定取消按钮事件
        $('#cancelAddTags').on('click', function() {
            popupBox.remove();
        });
    },
    
    /**
     * 执行添加标签操作
     * @param {Array} productIds 产品ID数组
     * @param {String} tagNames 标签名称，逗号分隔
     */
    performAddTags: function(productIds, tagNames) {
        if (!productIds || productIds.length === 0 || !tagNames) return;
        
        // 显示加载中
        customize_pop.loading();
        
        // 获取防伪令牌
        const token = $('input[name="__RequestVerificationToken"]').val() || 
                     $('input:hidden[name="__RequestVerificationToken"]').val();
        
        // 发送添加标签请求
        $.ajax({
            url: '/Products/Index?handler=AddTagsBatch',
            type: 'POST',
            contentType: 'application/x-www-form-urlencoded',
            data: `ids=${productIds.join(',')}&tags=${tagNames}`,
            headers: {
                "RequestVerificationToken": token
            },
            success: function(result) {
                customize_pop.loadingClose();
                
                if (result.success) {
                    // 显示成功消息
                    customize_pop.success(result.message || '标签添加成功');
                    
                    // 延迟刷新页面
                    setTimeout(function() {
                        window.location.reload();
                    }, 1000);
                } else {
                    customize_pop.error(result.message || '标签添加失败');
                }
            },
            error: function(xhr, status, error) {
                console.error("添加标签请求错误:", error);
                customize_pop.loadingClose();
                customize_pop.error('添加标签请求失败: ' + error);
            }
        });
    },
    
    /**
     * 显示删除标签对话框
     */
    showDeleteTagsDialog: function() {
        // 获取选中的产品ID
        const selectedProducts = $('input[name=select]:checked');
        if (selectedProducts.length === 0) {
            global_obj.win_alert({
                title: "提示",
                subtitle: "请至少选择一个产品",
                confirmBtn: "确定"
            });
            return;
        }
        
        // 收集所有选中的ID
        const productIds = [];
        selectedProducts.each(function() {
            productIds.push($(this).val());
        });
        
        // 创建对话框内容
        let dialogContent = `
            <div class="pop_form">
                <h1>批量删除标签</h1>
                <p class="hint">从 ${productIds.length} 个选中的产品中删除标签，多个标签请用英文逗号分隔</p>
                <div class="rows">
                    <label>标签名称：</label>
                    <span class="input">
                        <textarea id="deleteTagNames" style="width: 100%; height: 80px; padding: 5px;" placeholder="输入要删除的标签名称，多个标签请用英文逗号分隔"></textarea>
                    </span>
                </div>
                <div class="rows">
                    <label></label>
                    <span class="submit">
                        <input type="button" value="确定" id="confirmDeleteTags" class="btn_green">
                        <input type="button" value="取消" id="cancelDeleteTags" class="btn_gray">
                    </span>
                </div>
            </div>
        `;
        
        // 显示自定义对话框
        let popupBox = $('<div class="pop_form_box"></div>').append(dialogContent);
        $('body').append(popupBox);
        
        // 绑定确定按钮事件
        $('#confirmDeleteTags').on('click', function() {
            const tagNames = $('#deleteTagNames').val().trim();
            if (!tagNames) {
                global_obj.win_alert({
                    title: "提示",
                    subtitle: "请输入要删除的标签名称",
                    confirmBtn: "确定"
                });
                return;
            }
            
            // 显示确认提示
            global_obj.win_alert({
                title: "删除标签确认",
                subtitle: `确定要从选中的 ${productIds.length} 个产品中删除指定标签吗？`,
                confirmBtn: "删除",
                confirmBtnClass: "btn_warn",
                cancelBtn: "取消"
            }, function() {
                // 执行删除标签操作
                productTableModule.performDeleteTags(productIds, tagNames);
                
                // 关闭对话框
                popupBox.remove();
            }, 'confirm');
        });
        
        // 绑定取消按钮事件
        $('#cancelDeleteTags').on('click', function() {
            popupBox.remove();
        });
    },
    
    /**
     * 执行删除标签操作
     * @param {Array} productIds 产品ID数组
     * @param {String} tagNames 标签名称，逗号分隔
     */
    performDeleteTags: function(productIds, tagNames) {
        if (!productIds || productIds.length === 0 || !tagNames) return;
        
        // 显示加载中
        customize_pop.loading();
        
        // 获取防伪令牌
        const token = $('input[name="__RequestVerificationToken"]').val() || 
                     $('input:hidden[name="__RequestVerificationToken"]').val();
        
        // 发送删除标签请求
        $.ajax({
            url: '/Products/Index?handler=DeleteTagsBatch',
            type: 'POST',
            contentType: 'application/x-www-form-urlencoded',
            data: `ids=${productIds.join(',')}&tags=${tagNames}`,
            headers: {
                "RequestVerificationToken": token
            },
            success: function(result) {
                customize_pop.loadingClose();
                
                if (result.success) {
                    // 显示成功消息
                    customize_pop.success(result.message || '标签删除成功');
                    
                    // 延迟刷新页面
                    setTimeout(function() {
                        window.location.reload();
                    }, 1000);
                } else {
                    customize_pop.error(result.message || '标签删除失败');
                }
            },
            error: function(xhr, status, error) {
                console.error("删除标签请求错误:", error);
                customize_pop.loadingClose();
                customize_pop.error('删除标签请求失败: ' + error);
            }
        });
    },
    
    /**
     * 初始化标签管理相关功能
     */
    initTagsManagement: function() {
        // 监听标签表单提交
        $(document).on('click', '#products_tags_edit_form .btn_submit', function(e) {
            e.preventDefault();
            
            const form = $('#products_tags_edit_form');
            const type = form.find('input[name="Type"]').val();
            
            // 获取输入的标签值
            let tagOption = '';
            
            // 先尝试获取可能的选择项
            const selectedTags = form.find('.option_selected .select_list .btn_attr_choice');
            if (selectedTags.length > 0) {
                // 如果有选中的标签，则获取标签名称
                const tagNames = [];
                selectedTags.each(function() {
                    tagNames.push($(this).find('b').text());
                });
                tagOption = tagNames.join(',');
            } else {
                // 否则获取输入框的值
                tagOption = form.find('input[name="_Option"]').val() || form.find('textarea').val() || '';
            }
            
            // 如果没有标签值，则提示用户
            if (!tagOption) {
                global_obj.win_alert({
                    title: "提示",
                    subtitle: "请输入或选择至少一个标签",
                    confirmBtn: "确定"
                });
                return;
            }
            
            // 调用添加标签接口
            productTableModule.submitTagsUpdate(type, tagOption);
        });
    },
    
    /**
     * 提交标签更新
     * @param {String} type 标签类型
     * @param {String} option 标签选项，逗号分隔
     */
    submitTagsUpdate: function(type, option) {
        // 显示加载中
        customize_pop.loading ? customize_pop.loading() : (global_obj.loading && global_obj.loading());
        
        // 获取表单数据
        const form = $('#products_tags_edit_form');
        const formData = {};
        
        // 收集表单中所有输入字段的值
        form.find('input, select, textarea').each(function() {
            const input = $(this);
            const name = input.attr('name');
            
            if (name) {
                // 处理数组类型的表单字段（如tagsOption[]）
                if (name.endsWith('[]')) {
                    const baseName = name.slice(0, -2);
                    if (!formData[baseName]) {
                        formData[baseName] = [];
                    }
                    formData[baseName].push(input.val());
                } else {
                    formData[name] = input.val();
                }
            }
        });
        
        // 确保Type和Option字段存在
        formData.Type = type || formData.Type || 'tags';
        formData.Option = option || formData.Option || '';
        
        // 如果有选中的标签，收集它们的值
        const selectedTags = form.find('.option_selected .select_list .btn_attr_choice');
        if (selectedTags.length > 0) {
            // 初始化数组
            formData.insertCurrent = [];
            formData.insertOption = [];
            formData.insertName = [];
            
            selectedTags.each(function() {
                const tag = $(this);
                const tagId = tag.data('id') || '';
                const tagName = tag.find('b').text() || '';
                
                // 添加到相应的数组
                formData.insertCurrent.push(tagId);
                formData.insertOption.push(tagId || tagName);
                formData.insertName.push(tagName);
            });
        }
        
        // 发送AJAX请求
        $.ajax({
            url: '/manage/products/products/tags-update',
            type: 'POST',
            contentType: 'application/json', // 使用JSON格式
            data: JSON.stringify(formData),
            success: function(response) {
                // 关闭加载提示
                customize_pop.loadingClose ? customize_pop.loadingClose() : (global_obj.loading_close && global_obj.loading_close());
                
                if (response.ret === 1) {
                    // 成功
                    global_obj.win_alert_auto_close(response.msg || "标签更新成功", "", 1000);
                    
                    // 关闭弹窗
                    $('.global_container .close').click();
                    
                    // 如果需要刷新页面，延迟1秒
                    setTimeout(function() {
                        window.location.reload();
                    }, 1000);
                } else {
                    // 失败
                    global_obj.win_alert_auto_close(response.msg || "标签更新失败", "fail", 1500);
                }
            },
            error: function(xhr, status, error) {
                // 关闭加载提示
                customize_pop.loadingClose ? customize_pop.loadingClose() : (global_obj.loading_close && global_obj.loading_close());
                
                // 显示错误信息
                global_obj.win_alert_auto_close("提交标签更新失败: " + error, "fail", 1500);
            }
        });
    },

    /**
     * 排序功能
     * @param {string} field - 排序字段 (AccTime 或 ModifyTime)
     */
    sortBy: function(field) {
        // 获取当前URL参数
        const urlParams = new URLSearchParams(window.location.search);
        const currentSort = urlParams.get('sort');

        let newSortDirection = 'DESC'; // 默认降序

        // 如果当前已经是按这个字段排序，则切换排序方向
        if (currentSort && currentSort.startsWith(field + '_')) {
            const currentDirection = currentSort.split('_')[1];
            newSortDirection = currentDirection === 'DESC' ? 'ASC' : 'DESC';
        }

        // 构建新的排序参数
        const newSort = field + '_' + newSortDirection;

        // 更新URL参数
        urlParams.set('sort', newSort);

        // 跳转到新的URL
        window.location.href = window.location.pathname + '?' + urlParams.toString();
    }
};

// 页面加载完成后初始化
$(document).ready(function() {
    productTableModule.init();
}); 