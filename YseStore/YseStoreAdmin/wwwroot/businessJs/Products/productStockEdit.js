/**
 * 产品库存编辑相关JS
 */
var productStockEditModule = (function () {
    // 初始化函数
    function init() {
    }
    
    // 全站设置产品库存
    function updateProductStock() {
        // 获取表单数据
        var soldStatus = $('.fixed_stock_set select[name="SoldStatus"]').val();
        var stock = $('.fixed_stock_set input[name="Stock"]').val();
        var isStockWarning = $('.fixed_stock_set input[name="IsStockWarning"]').prop('checked');
        var warnStock = $('.fixed_stock_set input[name="WarnStock"]').val();

        // 验证必填项
        if (!soldStatus && !stock && !isStockWarning && !warnStock) {
            customize_pop.error('请至少设置一个参数');
            return;
        }
        
        // 获取选中的产品ID列表
        var idList = [];
        var idListStr = $('#products_stock_set input[name="id_list"]').val();
        
        // 如果id_list不为空，说明是批量更新，需要收集选中的ID
        if (idListStr) {
            idList = idListStr.split(',').map(function(item) {
                return parseInt(item.trim());
            }).filter(function(item) {
                return !isNaN(item);
            });
        }

        // 构建请求数据
        var formData = new FormData();
        
        // 如果有选中的产品ID，加入到请求中
        if (idList.length > 0) {
            idList.forEach(function(id, index) {
                formData.append("IdList[" + index + "]", id);
            });
        }
        
        formData.append("SoldStatus", soldStatus);
        formData.append("Stock", stock);
        formData.append("IsStockWarning", isStockWarning);
        formData.append("WarnStock", warnStock);

        // 发送AJAX请求
        $.ajax({
            url: '/Products/Stock?handler=UpdateStock',
            type: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            headers: {
                "RequestVerificationToken": $('input:hidden[name="__RequestVerificationToken"]').val()
            },
            success: function (res) {
                if (res.success) {
                    customize_pop.success('产品库存设置成功');
                    // 关闭弹窗
                    $('.fixed_stock_set').removeClass('active');
                    // 刷新页面或重新加载数据
                    setTimeout(function () {
                        location.reload();
                    }, 1500);
                } else {
                    customize_pop.error('产品库存设置失败: ' + res.message);
          
                }
            },
            error: function (xhr, status, error) {
                customize_pop.error('操作失败: ' + error);

            },
        });
    }

    // 返回公共接口
    return {
        init: init,
        updateProductStock: updateProductStock
    };
})();

// 全局可调用函数 - 全站更新产品库存设置
function updateProductStock() {
    productStockEditModule.updateProductStock();
} 