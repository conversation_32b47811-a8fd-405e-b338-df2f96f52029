/**
 * TinyMCE编辑器初始化脚本
 * 用于初始化产品编辑页面中的所有TinyMCE编辑器
 */

var TinymceInit = {
    /**
     * 初始化所有TinyMCE编辑器
     */
    init: function() {
        jQuery(function ($) {
            // 初始化简要描述编辑器 (英文)
            TinymceInit.initBriefDescriptionEn();
            
            // 初始化简要描述1编辑器 (英文)
            TinymceInit.initBriefDescription1En();
            
            // 初始化详细描述编辑器 (英文)
            TinymceInit.initDescriptionEn();
            
            // 初始化移动端描述编辑器
            TinymceInit.initMobileDescription();
            
            // 页面准备完成后初始化产品编辑功能
            $(document).ready(function () {
                products_obj.products_edit_init();
                // 添加分类选择初始化
                component_obj.global_select_category();
            });
        });
    },

    /**
     * 初始化简要描述编辑器 (英文)
     */
    initBriefDescriptionEn: function() {
        frame_obj.tinymceEditorInit('BriefDescription_en', {
            min_height: 300,
            max_height: 300,
            plugins: `template advtemplate print preview searchreplace autolink directionality visualblocks visualchars localimg_BriefDescription_en image link anchor media code advlist lists textpattern autosave autoresize paste`,
            toolbar: 'code undo redo |table localimg_BriefDescription_en media|  formatselect fontselect fontsizeselect lineheight | forecolor backcolor | bold italic underline strikethrough| link anchor removeformat | bullist numlist | alignleft aligncenter alignright alignjustify outdent indent  | cut copy paste pastetext',
            setup: function (ed) {
                ed.on("change", function () {
                    let autoChange = $(`textarea#BriefDescription_en`).attr('data-auto-change')
                    if (autoChange) {
                        let content = ed.getContent()
                        content = frame_obj.clear_html_content(content, 500)
                        $('[name="' + autoChange + '"]:not([readonly]')
                            .val(content).trigger('keyup')
                        $(".seo_info_box").show()
                        $(".seo_info_box .description").text(content)
                        $(".seo_box").find('textarea[name=SeoDescription_en]').val(content)
                        if (content) {
                            $(".seo_info_box .description").show()
                        } else {
                            $(".seo_info_box .description").hide()
                        }
                    }
                    ed.save()
                })
            },
        });
    },

    /**
     * 初始化简要描述1编辑器 (英文)
     */
    initBriefDescription1En: function() {
        frame_obj.tinymceEditorInit('BriefDescription1_en', {
            min_height: 300,
            max_height: 300,
            plugins: `template advtemplate  print preview searchreplace autolink directionality visualblocks visualchars localimg_BriefDescription1_en image link anchor media code advlist lists textpattern autosave autoresize paste`,
            toolbar: 'code undo redo | table localimg_BriefDescription1_en media| formatselect fontselect fontsizeselect lineheight | forecolor backcolor | bold italic underline strikethrough| link anchor removeformat | bullist numlist | alignleft aligncenter alignright alignjustify outdent indent  | cut copy paste pastetext',
            setup: function (ed) {
                ed.on("change", function () {
                    let autoChange = $(`textarea#BriefDescription1_en`).attr('data-auto-change')
                    if (autoChange) {
                        let content = ed.getContent()
                        content = frame_obj.clear_html_content(content, 500)
                        $('[name="' + autoChange + '"]:not([readonly]')
                            .val(content).trigger('keyup')
                        $(".seo_info_box").show()
                        $(".seo_info_box .description").text(content)
                        $(".seo_box").find('textarea[name=SeoDescription_en]').val(content)
                        if (content) {
                            $(".seo_info_box .description").show()
                        } else {
                            $(".seo_info_box .description").hide()
                        }
                    }
                    ed.save()
                })
            },
        });
    },

    /**
     * 初始化详细描述编辑器 (英文)
     */
    initDescriptionEn: function() {
        frame_obj.tinymceEditorInit('Description_en', {
            min_height: 500,
            max_height: 500,
            plugins: `template advtemplate  fullscreen table print preview searchreplace autolink directionality visualblocks visualchars localimg_Description_en image link anchor media code advlist lists textpattern autosave autoresize paste`,
            templates: [
                {title: '上文下图', description: '上文下图', url: '../../web/assets/template/product_temp1.html'},
                {title: '上图下文', description: '上图下文', url: '../../web/assets/template/product_temp2.html'},
                {title: '左文右图', description: '左文右图', url: '../../web/assets/template/product_temp3.html'},
                {title: '左图右文', description: '左图右文', url: '../../web/assets/template/product_temp4.html'},
                {title: '一行双图', description: '一行双图', url: '../../web/assets/template/product_temp5.html'},
                {
                    title: '一行三图文',
                    description: '一行三图文',
                    url: '../../web/assets/template/product_temp6.html'
                },
                {
                    title: '轮播带按钮',
                    description: '轮播带按钮',
                    url: '../../web/assets/template/product_temp7.html'
                },
                {title: '轮播展示', description: '轮播展示', url: '../../web/assets/template/product_temp8.html'}
            ],
            toolbar: 'code undo redo | fullscreen | table localimg_Description_en media| formatselect fontselect fontsizeselect lineheight | forecolor backcolor | bold italic underline strikethrough| link anchor removeformat | bullist numlist | alignleft aligncenter alignright alignjustify outdent indent  | cut copy paste pastetext| template'
        });
    },

    /**
     * 初始化移动端描述编辑器
     */
    initMobileDescription: function() {
        frame_obj.tinymceEditorInit('MobileDescription', {
            min_height: 500,
            max_height: 500,
            plugins: `template advtemplate  fullscreen table print preview searchreplace autolink directionality visualblocks visualchars localimg_MobileDescription image link anchor media code advlist lists textpattern autosave autoresize paste`,
            templates: [
                {title: '上文下图', description: '上文下图', url: '../../web/assets/template/product_temp1.html'},
                {title: '上图下文', description: '上图下文', url: '../../web/assets/template/product_temp2.html'},
                {title: '左文右图', description: '左文右图', url: '../../web/assets/template/product_temp3.html'},
                {title: '左图右文', description: '左图右文', url: '../../web/assets/template/product_temp4.html'},
                {title: '一行双图', description: '一行双图', url: '../../web/assets/template/product_temp5.html'},
                {
                    title: '一行三图文',
                    description: '一行三图文',
                    url: '../../web/assets/template/product_temp6.html'
                },
                {
                    title: '轮播带按钮',
                    description: '轮播带按钮',
                    url: '../../web/assets/template/product_temp7.html'
                },
                {title: '轮播展示', description: '轮播展示', url: '../../web/assets/template/product_temp8.html'}
            ],
            toolbar: 'code undo redo | fullscreen | table localimg_MobileDescription media| formatselect fontselect fontsizeselect lineheight | forecolor backcolor | bold italic underline strikethrough| link anchor removeformat | bullist numlist | alignleft aligncenter alignright alignjustify outdent indent  | cut copy paste pastetext| template'
        });
    }
};

// 页面加载完成后自动初始化
$(document).ready(function() {
    TinymceInit.init();
});
