/**
 * 产品搜索相关功能
 */
const productSearch = {
    /**
     * 筛选产品
     */
    filterProducts: function() {
        try {
            // 获取所有筛选条件
            const filterData = this.collectFilterData();
            
            // 构建查询参数
            const queryParams = new URLSearchParams();
            
            // 添加基本筛选条件
            if (filterData.keyword) {
                queryParams.append('keyword', filterData.keyword);
            }
            if (filterData.cateId) {
                queryParams.append('cateId', filterData.cateId);
            }
            if (filterData.tagId) {
                queryParams.append('tagId', filterData.tagId);
            }
            
            // 只有当价格不都是0时才添加价格参数
            if (!(filterData.minPrice === '0' && filterData.maxPrice === '0')) {
                if (filterData.minPrice) {
                    queryParams.append('minPrice', filterData.minPrice);
                }
                if (filterData.maxPrice) {
                    queryParams.append('maxPrice', filterData.maxPrice);
                }
            }
            
            if (filterData.status) {
                queryParams.append('status', filterData.status);
            }
            
            // 添加分页参数
            queryParams.append('page', '1');
            
            // 构建URL
            const currentUrl = new URL(window.location.href);
            const newUrl = `${currentUrl.pathname}?${queryParams.toString()}`;
            
            // 跳转到新的URL
            window.location.href = newUrl;
        } catch (error) {
            console.error('筛选产品时发生错误:', error);
            alert('筛选产品时发生错误，请稍后重试');
        }
    },
    
    /**
     * 通过AJAX调用后端搜索接口
     */
    searchToProducts: function() {
        try {
            // 获取所有筛选条件
            const filterData = this.collectFilterData();
            
            console.log('筛选条件:', filterData); // 添加日志输出

            // 构建请求数据
            const requestData = {
                Keyword: filterData.keyword || '',
                CateId: filterData.cateId ? parseInt(filterData.cateId) : null,
                TagId: filterData.tagId || '',
                Status: filterData.status ? parseInt(filterData.status) : null
            };
            
            // 只有当价格不都是0时才添加价格参数
            if (!(filterData.minPrice === '0' && filterData.maxPrice === '0')) {
                requestData.MinPrice = filterData.minPrice ? parseFloat(filterData.minPrice) : null;
                requestData.MaxPrice = filterData.maxPrice ? parseFloat(filterData.maxPrice) : null;
            }
            
            console.log('发送请求数据:', requestData); // 添加日志输出
            
            // 发送AJAX请求
            fetch('/api/Product/Search', {
                method: 'POST',
                body: JSON.stringify(requestData),
                headers: {
                    'Content-Type': 'application/json'
                }
            })
            .then(response => {
                console.log('响应状态:', response.status); // 添加日志输出
                return response.json();
            })
            .then(data => {
                console.log('响应数据:', data); // 添加日志输出
                if (data.success) {
                    // 搜索成功，跳转到包含筛选条件的URL
                    if (data.url) {
                        window.location.href = data.url;
                    } else {
                        window.location.reload();
                    }
                } else {
                    // 搜索失败，显示错误信息
                    alert(data.message || '搜索失败，请稍后重试');
                }
            })
            .catch(error => {
                console.error('搜索产品时发生错误详情:', error); // 添加更详细的错误日志
                alert('搜索产品时发生错误，请稍后重试');
            });
        } catch (error) {
            console.error('调用搜索接口时发生错误:', error);
            alert('搜索产品时发生错误，请稍后重试');
        }
    },
    
    /**
     * 收集所有筛选条件
     */
    collectFilterData: function() {
        const filterData = {};
        
        // 获取关键词
        const keywordInput = document.querySelector('input[name="keyword"]');
        if (keywordInput) {
            filterData.keyword = keywordInput.value.trim();
        }
        
        // 获取分类信息
        const categoryBox = document.querySelector('.box_drop_double.edit_box');
        if (categoryBox) {
            // 获取隐藏字段的值
            const cateIdInput = categoryBox.querySelector('input[name="cateId"]');
            
            // 获取显示输入框的值
            const selectInput = categoryBox.querySelector('input[name="Select"]');
            
            // 检查分类选择器的选中项
            const selectedItem = categoryBox.querySelector('.drop_list .selected');
            
            if (selectedItem) {
                // 如果从下拉列表选择了分类
                const cateId = selectedItem.getAttribute('data-value');
                if (cateId) {
                    filterData.cateId = cateId;
                }
            } else if (cateIdInput && cateIdInput.value) {
                // 如果隐藏字段有值
                filterData.cateId = cateIdInput.value;
            } else if (selectInput && selectInput.value) {
                // 如果用户手动输入了分类
                filterData.cateId = selectInput.value;
            }
        }
        
        // 获取标签信息
        const tagBox = document.querySelector('.box_drop_double[data-checkbox="1"]');
        if (tagBox) {
            // 获取已选择的标签（从select_list中查找）
            const selectedTags = tagBox.querySelectorAll('.select_list .btn_attr_choice.current');
            
            if (selectedTags && selectedTags.length > 0) {
                // 从checkbox输入框中获取值
                const tagIds = Array.from(selectedTags).map(tag => {
                    const checkbox = tag.querySelector('input[type="checkbox"]');
                    return checkbox ? checkbox.value : null;
                }).filter(id => id !== null);
                
                if (tagIds.length > 0) {
                    filterData.tagId = tagIds.join(',');
                }
            } else {
                // 尝试通过隐藏字段获取
                const tagIdInput = tagBox.querySelector('input[name="tagId"]');
                if (tagIdInput && tagIdInput.value) {
                    filterData.tagId = tagIdInput.value;
                } else {
                    // 尝试查找所有选中的checkbox
                    const checkedInputs = tagBox.querySelectorAll('input[type="checkbox"]:checked');
                    if (checkedInputs && checkedInputs.length > 0) {
                        const tagIds = Array.from(checkedInputs).map(input => input.value);
                        if (tagIds.length > 0) {
                            filterData.tagId = tagIds.join(',');
                        }
                    }
                }
            }
        }
        
        // 获取价格范围
        const minPriceInput = document.querySelector('input[name="MinPrice"]');
        const maxPriceInput = document.querySelector('input[name="MaxPrice"]');
        if (minPriceInput) {
            filterData.minPrice = minPriceInput.value.trim();
        }
        if (maxPriceInput) {
            filterData.maxPrice = maxPriceInput.value.trim();
        }
        
        // 获取状态
        const statusInput = document.querySelector('input[name="status"]');
        if (statusInput) {
            filterData.status = statusInput.value;
        }
        
        return filterData;
    },
    
    /**
     * 重置所有筛选条件
     */
    resetFilters: function() {
        // 重置所有输入框
        const inputs = document.querySelectorAll('input[type="text"]');
        inputs.forEach(input => {
            input.value = '';
        });
        
        // 重置隐藏字段
        const hiddenInputs = document.querySelectorAll('input[type="hidden"]');
        hiddenInputs.forEach(input => {
            input.value = '';
        });
        
        // 重置所有下拉框
        const selects = document.querySelectorAll('select');
        selects.forEach(select => {
            select.selectedIndex = 0;
        });
        
        // 重置所有复选框
        const checkboxes = document.querySelectorAll('input[type="checkbox"]');
        checkboxes.forEach(checkbox => {
            checkbox.checked = false;
        });
        
        // 重置所有单选框
        const radios = document.querySelectorAll('input[type="radio"]');
        radios.forEach(radio => {
            radio.checked = false;
        });
        
        // 重置分类和标签选择器
        const categorySelect = document.querySelector('.box_drop_double.edit_box');
        if (categorySelect) {
            const categoryInput = categorySelect.querySelector('input[type="text"]');
            if (categoryInput) {
                categoryInput.value = '';
            }
        }
        
        const tagSelect = document.querySelector('.box_drop_double[data-checkbox="1"]');
        if (tagSelect) {
            const tagList = tagSelect.querySelector('.select_list');
            if (tagList) {
                tagList.innerHTML = '';
            }
            // 重置标签选择器的显示
            const selectPlaceholder = tagSelect.querySelector('.select_placeholder');
            if (selectPlaceholder) {
                selectPlaceholder.textContent = '请选择';
            }
        }
    },
    
    /**
     * 移除单个筛选条件
     * @param {string} filterName - 筛选条件的名称
     */
    removeFilter: function(filterName) {
        try {
            console.log('移除筛选条件:', filterName);
            
            // 根据筛选条件名称处理不同类型的筛选条件
            const nameArray = filterName.split('|');
            
            // 构建当前URL的查询参数对象
            const currentUrl = new URL(window.location.href);
            const queryParams = new URLSearchParams(currentUrl.search);
            
            // 移除对应的筛选条件
            nameArray.forEach(name => {
        
                queryParams.delete(name);
                
                // 同时清除表单中对应的输入字段
                const input = document.querySelector(`input[name="${name}"]`);
                if (input) {
                    input.value = '';
                }
                
                // 如果是价格筛选条件，确保同时清除minPrice和maxPrice
                if (name === 'minPrice' || name === 'maxPrice') {
                    queryParams.delete('minPrice');
                    queryParams.delete('maxPrice');
                    
                    const minPriceInput = document.querySelector('input[name="MinPrice"]');
                    const maxPriceInput = document.querySelector('input[name="MaxPrice"]');
                    
                    if (minPriceInput) minPriceInput.value = '';
                    if (maxPriceInput) maxPriceInput.value = '';
                }
            });
            
            // 构建新的URL
            const newUrl = `${currentUrl.pathname}?${queryParams.toString()}`;
            
            // 跳转到新的URL
            window.location.href = newUrl;
        } catch (error) {
            console.error('移除筛选条件时发生错误:', error);
        }
    },
    
    /**
     * 初始化筛选功能
     */
    init: function() {
        // 绑定取消按钮事件
        const cancelButton = document.querySelector('.btn_cancel');
        if (cancelButton) {
            cancelButton.addEventListener('click', () => {
                this.resetFilters();
            });
        }
        
        // 绑定清除按钮事件
        const clearButtons = document.querySelectorAll('.filter_clean button');
        clearButtons.forEach(button => {
            button.addEventListener('click', () => {
                const filterList = button.closest('.filter_list');
                if (filterList) {
                    // 重置该筛选组的所有输入
                    const inputs = filterList.querySelectorAll('input');
                    inputs.forEach(input => {
                        input.value = '';
                    });
                    
                    // 重置分类和标签选择器
                    const categorySelect = filterList.querySelector('.box_drop_double.edit_box');
                    if (categorySelect) {
                        const categoryInput = categorySelect.querySelector('input[type="text"]');
                        if (categoryInput) {
                            categoryInput.value = '';
                        }
                    }
                    
                    const tagSelect = filterList.querySelector('.box_drop_double[data-checkbox="1"]');
                    if (tagSelect) {
                        const tagList = tagSelect.querySelector('.select_list');
                        if (tagList) {
                            tagList.innerHTML = '';
                        }
                        // 重置标签选择器的显示
                        const selectPlaceholder = tagSelect.querySelector('.select_placeholder');
                        if (selectPlaceholder) {
                            selectPlaceholder.textContent = '请选择';
                        }
                    }
                }
            });
        });
        
        // 绑定搜索按钮点击事件
        const searchBtn = document.querySelector('.search_btn');
        if (searchBtn) {
            searchBtn.addEventListener('click', (e) => {
                e.preventDefault(); // 阻止表单默认提交
                this.searchToProducts();
            });
        }
        
        // 绑定回车键事件
        const inputs = document.querySelectorAll('input[type="text"]');
        inputs.forEach(input => {
            input.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    this.filterProducts();
                }
            });
        });
        
        // 绑定标签选择事件
        const tagBox = document.querySelector('.box_drop_double[data-checkbox="1"]');
        if (tagBox) {
            const tagList = tagBox.querySelector('.drop_list');
            if (tagList) {
                tagList.addEventListener('click', (e) => {
                    const target = e.target.closest('.item');
                    if (target) {
                        // 更新隐藏字段的值
                        const tagIdInput = tagBox.querySelector('input[name="tagId"]');
                        if (tagIdInput) {
                            const selectedTags = tagList.querySelectorAll('.selected');
                            const tagIds = Array.from(selectedTags).map(tag => tag.getAttribute('data-value'));
                            tagIdInput.value = tagIds.join(',');
                            
                            // 保持分类的值
                            const categoryBox = document.querySelector('.box_drop_double.edit_box');
                            if (categoryBox) {
                                const cateIdInput = categoryBox.querySelector('input[name="cateId"]');
                                if (cateIdInput) {
                                    const form = document.querySelector('form');
                                    if (form) {
                                        const formCateId = form.querySelector('input[name="cateId"]');
                                        if (formCateId) {
                                            formCateId.value = cateIdInput.value;
                                        }
                                    }
                                }
                            }
                        }
                    }
                });
            }
        }
        
        // 绑定筛选条件移除事件
        const searchBoxSelected = document.querySelector('.search_box_selected');
        if (searchBoxSelected) {
            searchBoxSelected.addEventListener('click', (e) => {
                const target = e.target.closest('.btn_item_choice');
                if (target) {
                    const filterName = target.getAttribute('data-name');
                    if (filterName) {
                        this.removeFilter(filterName);
                    }
                }
            });
        }
    }
};

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', () => {
    productSearch.init();
});


