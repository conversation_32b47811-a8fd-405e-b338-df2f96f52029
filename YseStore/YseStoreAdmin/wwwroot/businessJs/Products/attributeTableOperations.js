/**
 * 产品属性表格操作JS
 * 用于处理产品编辑页面中属性表格的操作
 */

var AttributeTableOperations = (function () {
    
    /**
     * 初始化表格操作
     */
    function init() {
        // 初始化复选框和菜单操作
        initCheckboxOperations();
        initMenuOperations();
        initDefaultOptionOperations();
    }
    
    /**
     * 初始化复选框操作
     */
    function initCheckboxOperations() {
        // 处理复选框点击事件
        $('#AttrId_0, #AttrId_1').on('click', '.input_checkbox_box, .btn_checkbox', function(e) {
            e.stopPropagation(); // 阻止事件冒泡
            
            // 批量勾选逻辑
            let isChecked = false;
            const $mainObj = $(this).closest('.attribute_ext');
            const $tbodyObj = $mainObj.find('tbody:visible');
            const $tbodyAllObj = $mainObj.find('tbody');
            let $checkedCount = 0; // 重置已选计数
            const $count = $tbodyObj.find('.input_checkbox_box').length;
            
            if ($count === 0) {
                return false;
            }
            
            if ($(this).hasClass('btn_checkbox')) {
                // 表头全选/取消全选按钮
                if ($(this).hasClass('current')) {
                    // 取消全选
                    $(this).removeClass('current');
                    isChecked = false;
                } else {
                    // 全选
                    $(this).addClass('current');
                    isChecked = true;
                }
                
                // 根据全选按钮状态更新所有复选框
                if (isChecked) {
                    // 全选
                    $tbodyObj.find('.input_checkbox_box').each(function() {
                        $(this).addClass('checked').find('input').prop('checked', true);
                    });
                } else {
                    // 取消全选
                    $tbodyAllObj.find('.input_checkbox_box').each(function() {
                        $(this).removeClass('checked').find('input').prop('checked', false);
                    });
                }
                
                // 更新实际选中的数量
                $checkedCount = $tbodyObj.find('.input_checkbox_box.checked').length;
            } else {
                // 表格中的复选框
                if ($(this).hasClass('checked')) {
                    // 取消选中
                    $(this).removeClass('checked').find('input').prop('checked', false);
                } else {
                    // 选中
                    $(this).addClass('checked').find('input').prop('checked', true);
                    isChecked = true;
                }
                
                // 重新计算选中数量
                $checkedCount = $tbodyObj.find('.input_checkbox_box.checked').length;
                
                // 普通复选框处理 - 计算表头全选框状态
                if ($checkedCount === $count) {
                    // 全选状态
                    $mainObj.find('thead .btn_checkbox').removeClass('indeterminate').addClass('current')
                        .find('input').prop('checked', true);
                } else if ($checkedCount === 0) {
                    // 没有选中
                    $mainObj.find('thead .btn_checkbox').removeClass('current indeterminate')
                        .find('input').prop('checked', false);
                } else {
                    // 部分选中
                    $mainObj.find('thead .btn_checkbox').removeClass('current').addClass('indeterminate')
                        .find('input').prop('checked', false);
                }
            }
            
            // 更新表头行的状态和显示选中项数量
            if ($checkedCount > 0) {
                $mainObj.find('thead tr').addClass('current');
                $mainObj.find('thead .global_menu_button .open').removeClass('no_select');
                $mainObj.find('thead .global_menu_button .open>span').text($checkedCount);
            } else {
                $mainObj.find('thead tr').removeClass('current');
                $mainObj.find('thead .global_menu_button .open').addClass('no_select');
            }
            
            // 重置筛选状态
            resetFilterState();
        });
    }
    
    /**
     * 初始化菜单操作
     */
    function initMenuOperations() {
        // 处理全局菜单按钮点击
        $('#AttrId_0, #AttrId_1').on('click', '.global_menu_button .open', function(e) {
            e.stopPropagation();
            
            const $tr = $(this).closest('thead tr');
            
            // 切换当前行状态
            if ($tr.hasClass('current')) {
                $tr.removeClass('current');
                $(this).addClass('no_select');
            } else {
                $tr.addClass('current');
                $(this).removeClass('no_select');
            }
            
            // 显示操作按钮
            const $menu = $(this).siblings('.drop_down');
            $menu.toggle();
            
            // 点击外部区域关闭菜单
            $(document).one('click', function() {
                $('.drop_down').hide();
            });
            
            return false;
        });
        
        // 处理删除按钮点击
        $('#AttrId_0, #AttrId_1').on('click', '.batch_delete, .drop_down .delete', function(e) {
            e.stopPropagation();
            
            const tableId = $(this).closest('.attribute_ext').attr('id');
            const $listObj = $(`#${tableId} tbody:visible .input_checkbox_box.checked`);
            
            if ($listObj.length > 0) {
                if (confirm('确定要删除选中的项目吗?')) {
                    // 删除选中的行
                    $listObj.closest('tr').remove();
                    
                    // 重置表头状态
                    const $mainObj = $(this).closest('.attribute_ext');
                    $mainObj.find('thead .btn_checkbox').removeClass('current indeterminate')
                        .find('input').prop('checked', false);
                    $mainObj.find('thead tr').removeClass('current');
                    $mainObj.find('thead .global_menu_button .open').addClass('no_select');
                    
                    // 关闭下拉菜单
                    $('.drop_down').hide();
                    
                    alert('删除成功');
                }
            } else {
                alert('请先选择要删除的项目');
            }
            
            return false;
        });
    }
    
    /**
     * 初始化默认选项操作
     */
    function initDefaultOptionOperations() {
        // 处理设为默认选项按钮点击
        $('#AttrId_0, #AttrId_1').on('click', '.set_default', function(e) {
            e.stopPropagation();
            
            const $listObj = $('#AttrId_1 tbody:visible .input_checkbox_box.checked');
            const listLength = $listObj.length;
            
            if (listLength === 0) {
                alert('请先选择要设置为默认的项目');
                return false;
            }
            
            // 取最后一个选中项设为默认
            const $lastChecked = $listObj.last();
            const $row = $lastChecked.closest('tr');
            const parentId = $row.attr('data-id') || -1;
            
            // 检查是否已经是默认项
            let attrDefaultId = 0;
            $('#AttrId_1 tbody tr').each(function() {
                if ($(this).find('.attr_name .attr_default').length) {
                    attrDefaultId = $(this).attr('data-id');
                }
            });
            
            if (parentId == attrDefaultId) {
                return false; // 当前项已经是默认选项
            }
            
            // 设置为默认
            const $parentObj = $lastChecked.parent();
            const labelHtml = '<span class="attr_default">默认选项</span>';
            
            // 设置默认标记
            $parentObj.find("input[name*='[IsDefault]']").val(1);
            
            // 添加默认标签
            if ($row.find('.attr_name .attr_default').length === 0) {
                $row.find('.attr_name').append(labelHtml);
            }
            
            // 移除其他项的默认标记
            $row.siblings().find('.attr_checkbox').find("input[name*='[IsDefault]']").val(0);
            $row.siblings().find('.attr_name').find('.attr_default').remove();
            
            // 检查其他仓库的默认标记
            const $visibleTrList = $('#AttrId_1 tbody:visible tr');
            const $siblingsTbodyTr = $visibleTrList.parent().siblings("tbody:not([data-id=0])").find('tr');
            
            $siblingsTbodyTr.each(function() {
                if ($(this).find(".attr_checkbox input[name*='[IsDefault]']").val() == 1) {
                    $(this).find('.attr_checkbox').find("input[name*='[IsDefault]']").val(0);
                    $(this).find('.attr_name').find('.attr_default').remove();
                }
            });
            
            alert('已设置为默认选项');
            return false;
        });
    }
    
    /**
     * 重置筛选状态
     */
    function resetFilterState() {
        const $filterObj = $('.option_filter');
        if (!$filterObj.hasClass('lock')) {
            // 没锁住
            $filterObj.find('.inside_title>span').html('请选择规格');
            $filterObj.find('a.current').removeClass('current');
            
            const $show = ($('#AttrId_1').hasClass('show') ? 1 : 0);
            const $tbodyObj = $(`#AttrId_${$show} tbody:visible`);
            $tbodyObj.find('tr').removeAttr('style');
        }
    }
    
    // 返回公共接口
    return {
        init: init
    };
})();

// 如果需要自动初始化，可以取消下面的注释
jQuery(function() {
    AttributeTableOperations.init();
}); 