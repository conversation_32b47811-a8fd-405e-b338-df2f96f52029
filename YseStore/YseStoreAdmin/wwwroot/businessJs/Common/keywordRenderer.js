/**
 * 关键词渲染器模块
 * 用于处理SEO关键词和标签的回显逻辑
 */
var KeywordRenderer = (function() {
    /**
     * 渲染SEO关键词
     * @param {string} seoKeywordValue - 关键词字符串，多个关键词用逗号分隔
     * @param {Object} options - 配置选项
     * @param {string} options.containerSelector - 关键词容器选择器
     * @param {string} options.hiddenFieldName - 隐藏字段名称
     * @param {string} options.valueFieldName - 值字段名称（可选）
     * @param {string} options.dataType - 数据类型，默认为"seo_keyword"
     * @param {Function} options.beforeRender - 渲染前回调函数
     * @param {Function} options.afterRender - 渲染后回调函数
     */
    function renderKeywords(seoKeywordValue, options) {
        if (!seoKeywordValue) {
            return;
        }

        // 设置默认选项
        options = options || {};
        const containerSelector = options.containerSelector || '.seo_box .option_selected[data-type="seo_keyword"]';
        const hiddenFieldName = options.hiddenFieldName || 'SeoKeyword_en';
        const valueFieldName = options.valueFieldName || 'SeoKeyword_en_value';
        const dataType = options.dataType || 'seo_keyword';
        
        // 触发渲染前回调
        if (typeof options.beforeRender === 'function') {
            options.beforeRender(seoKeywordValue);
        }
        
        // 设置隐藏字段值
        setHiddenFieldValue(hiddenFieldName, seoKeywordValue);
        
        // 如果有值字段，也设置它
        if (valueFieldName) {
            setHiddenFieldValue(valueFieldName, seoKeywordValue);
        }

        // 在选择列表中显示关键词
        const keywordContainer = document.querySelector(containerSelector);
        if (keywordContainer) {
            const selectList = keywordContainer.querySelector('.select_list');
            if (selectList) {
                // 清空现有关键词
                selectList.innerHTML = '';
                
                // 分割关键词并添加到列表
                const keywords = seoKeywordValue.split(',');
                
                keywords.forEach((keyword, index) => {
                    const kw = keyword.trim();
                    if (kw) {
                        // 创建关键词HTML
                        const keywordItemHtml = `
                            <span class="btn_attr_choice current" data-type="${dataType}">
                                <b>${kw}</b>
                                <input type="checkbox" name="${dataType}Current[]" value="ADD:${index + 1}" class="option_current" checked>
                                <input type="hidden" name="${dataType}Option[]" value="ADD:${index + 1}">
                                <input type="hidden" name="${dataType}Name[]" value="${kw}">
                                <i></i>
                            </span>
                        `;
                        
                        // 添加到选择列表
                        selectList.insertAdjacentHTML('beforeend', keywordItemHtml);
                    }
                });

                // 处理占位符
                handlePlaceholder(keywordContainer, keywords.length > 0);

                // 调用框架函数以正确应用样式
                callFrameworkFunctions();
            }
        }
        
        // 触发渲染后回调
        if (typeof options.afterRender === 'function') {
            options.afterRender(seoKeywordValue, keywordContainer);
        }
    }

    /**
     * 设置隐藏字段的值
     * @param {string} fieldName - 字段名称
     * @param {string} fieldValue - 字段值
     */
    function setHiddenFieldValue(fieldName, fieldValue) {
        let field = document.querySelector(`input[name="${fieldName}"]`);
        
        if (field) {
            field.value = fieldValue;
        } else {
            // 如果字段不存在，创建一个
            field = document.createElement('input');
            field.type = 'hidden';
            field.name = fieldName;
            field.value = fieldValue;
            
            // 添加到表单中
            const form = document.getElementById('edit_form');
            if (form) {
                form.appendChild(field);
            } else {
                // 如果找不到edit_form，添加到body
                document.body.appendChild(field);
            }
        }
    }

    /**
     * 处理占位符的显示/隐藏
     * @param {HTMLElement} container - 容器元素
     * @param {boolean} hasItems - 是否有项目
     */
    function handlePlaceholder(container, hasItems) {
        const placeholder = container.querySelector('.placeholder');
        if (placeholder) {
            if (hasItems) {
                placeholder.classList.add('hide');
            } else {
                placeholder.classList.remove('hide');
            }
        }
    }

    /**
     * 调用框架函数来应用样式
     */
    function callFrameworkFunctions() {
        if (typeof frame_obj !== 'undefined') {
            if (typeof frame_obj.box_option_button_choice === 'function') {
                frame_obj.box_option_button_choice();
            }
            
            if (typeof frame_obj.box_option_list === 'function') {
                frame_obj.box_option_list();
            }
        }
    }

    /**
     * 渲染标签
     * @param {Array} tags - 标签数组，每个标签应包含TagId和NameEn属性
     * @param {Object} options - 配置选项
     */
    function renderTags(tags, options) {
        if (!tags || !Array.isArray(tags) || tags.length === 0) {
            return;
        }

        // 设置默认选项
        options = options || {};
        const containerSelector = options.containerSelector || '.global_container[data-name="tag_info"]';
        const tagIdsFieldName = options.tagIdsFieldName || 'TagIds';
        const tagStringFieldName = options.tagStringFieldName || 'TagString';
        const dataType = options.dataType || 'tags';
        
        // 构建标签ID和名称数组
        const tagIds = tags.map(tag => tag.TagId || tag.TId);
        const tagNames = tags
            .map(tag => tag.NameEn || tag.TagName || "")
            .filter(name => name);
        
        // 触发渲染前回调
        if (typeof options.beforeRender === 'function') {
            options.beforeRender(tags);
        }

        // 获取标签容器
        const tagContainer = document.querySelector(containerSelector);
        if (!tagContainer) {
            return;
        }

        // 设置隐藏字段值
        if (tagIds.length > 0) {
            // 设置TagIds字段 - 竖线分隔格式 |id1|id2|
            setHiddenFieldValue(tagIdsFieldName, '|' + tagIds.join('|') + '|');
            
            // 设置TagString字段 - 逗号分隔的标签名
            if (tagNames.length > 0) {
                setHiddenFieldValue(tagStringFieldName, tagNames.join(','));
            }
        }

        // 在选择列表中显示标签
        const selectList = tagContainer.querySelector(`.option_selected[data-type="${dataType}"] .select_list`);
        if (selectList) {
            // 清空现有标签
            selectList.innerHTML = '';
            
            // 添加标签项
            tags.forEach(tag => {
                const tagId = tag.TagId || tag.TId;
                const tagName = tag.NameEn || tag.TagName || "";
                
                if (tagName) {
                    // 创建标签HTML
                    const tagItemHtml = `
                        <span class="btn_attr_choice current" data-type="${dataType}" data-id="${tagId}">
                            <b>${tagName}</b>
                            <input type="checkbox" name="${dataType}Current[]" value="${tagId}" class="option_current" checked>
                            <input type="hidden" name="${dataType}Option[]" value="${tagName}">
                            <input type="hidden" name="${dataType}Name[]" value="${tagName}">
                            <input type="hidden" name="${dataType}Id[]" value="${tagId}">
                            <i></i>
                        </span>
                    `;
                    
                    // 添加到选择列表
                    selectList.insertAdjacentHTML('beforeend', tagItemHtml);
                }
            });

            // 处理占位符
            const optionSelected = tagContainer.querySelector(`.option_selected[data-type="${dataType}"]`);
            if (optionSelected) {
                handlePlaceholder(optionSelected, tags.length > 0);
            }

            // 调用框架函数以正确应用样式
            callFrameworkFunctions();
        }
        
        // 触发渲染后回调
        if (typeof options.afterRender === 'function') {
            options.afterRender(tags, tagContainer);
        }
    }

    // 返回公共接口
    return {
        renderKeywords: renderKeywords,
        renderTags: renderTags
    };
})();

// 如果模块加载器存在，定义模块
if (typeof define === 'function' && define.amd) {
    define([], function() {
        return KeywordRenderer;
    });
}

// 确保全局访问
window.KeywordRenderer = KeywordRenderer; 