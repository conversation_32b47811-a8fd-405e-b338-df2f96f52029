/**
 * 接口监控模块
 * 用于监控接口的请求，当操作成功时自动跳转到指定页面
 */
var ApiMonitor = (function() {
    // 私有变量
    var _config = {
        // 需要监控的接口URL关键字列表
        monitoredApis: [],
        // 成功操作后的跳转地址
        redirectUrl: '',
        // 成功的返回值标识
        successCode: 1,
        // 成功字段名称，默认为 'ret'，也可以设置为 'success' 等
        successField: 'ret',
        // 是否开启监控
        enabled: true,
        // 请求成功后的回调函数，返回true时执行重定向，返回false时不执行重定向
        successCallback: null
    };

    // 保存原始的方法
    var originalFetch = window.fetch;
    var originalXhrOpen = XMLHttpRequest.prototype.open;
    var originalXhrSend = XMLHttpRequest.prototype.send;

    // 私有方法 - 处理响应
    function _handleResponse(data) {
        if (!_config.enabled) return false;

        // 检查是否操作成功 - 支持多种返回格式
        var isSuccess = false;

        // 方式1：使用配置的成功字段和成功值
        if (data && data[_config.successField] === _config.successCode) {
            isSuccess = true;
        }

        // 方式2：兼容常见的返回格式
        if (!isSuccess && data) {
            // 检查 { ret: 1 } 格式
            if (data.ret === 1) {
                isSuccess = true;
            }
            // 检查 { success: true } 格式
            else if (data.success === true) {
                isSuccess = true;
            }
            // 检查 { status: true } 格式
            else if (data.status === true) {
                isSuccess = true;
            }
        }

        // 如果有回调函数，则执行回调
        if (isSuccess && typeof _config.successCallback === 'function') {
            isSuccess = _config.successCallback(data);
        }

        // 如果操作成功，跳转到指定页面
        if (isSuccess) {
            if (_config.redirectUrl) {
                window.location.href = _config.redirectUrl;
            }
            return true;
        }

        return false;
    }

    // 初始化监控
    function init(options) {
        // 合并配置
        if (options) {
            for (var key in options) {
                if (options.hasOwnProperty(key) && _config.hasOwnProperty(key)) {
                    _config[key] = options[key];
                }
            }
        }

        // 如果未启用或未配置监控接口，则不进行监控
        if (!_config.enabled || !_config.monitoredApis || _config.monitoredApis.length === 0) {
            return;
        }

        // 重写fetch方法
        window.fetch = function(url, options) {
            // 调用原始fetch方法
            return originalFetch(url, options).then(function(response) {
                // 检查是否是需要监控的接口请求
                if (typeof url === 'string' && _config.monitoredApis.some(function(api) { 
                    return url.includes(api); 
                })) {
                    // 克隆响应，因为response只能被使用一次
                    var clonedResponse = response.clone();
                    
                    // 处理响应
                    clonedResponse.json().then(function(data) {
                        _handleResponse(data);
                    }).catch(function(error) {
                        console.error('解析响应失败:', error);
                    });
                }
                
                // 返回原始响应
                return response;
            });
        };
        
        // 重写XMLHttpRequest方法
        XMLHttpRequest.prototype.open = function(method, url, async, user, password) {
            // 保存URL以便在send中使用
            this._url = url;
            return originalXhrOpen.apply(this, arguments);
        };
        
        XMLHttpRequest.prototype.send = function(body) {
            // 检查是否是需要监控的接口请求
            if (this._url && _config.monitoredApis.some(function(api) { 
                return this._url.includes(api); 
            }, this)) {
                // 添加响应处理程序
                this.addEventListener('load', function() {
                    if (this.status >= 200 && this.status < 300) {
                        try {
                            var data = JSON.parse(this.responseText);
                            _handleResponse(data);
                        } catch (e) {
                            console.error('解析响应失败:', e);
                        }
                    }
                });
            }
            
            return originalXhrSend.apply(this, arguments);
        };

        return this;
    }

    // 禁用监控
    function disable() {
        _config.enabled = false;
        return this;
    }

    // 启用监控
    function enable() {
        _config.enabled = true;
        return this;
    }

    // 暴露公共方法
    return {
        init: init,
        disable: disable,
        enable: enable
    };
})();

// 使用示例:
/*
// 监控配送地区接口（使用默认的 ret 字段）
ApiMonitor.init({
    monitoredApis: ['/Setting/ShippingArea'],
    redirectUrl: '/Setting/Shipping',
    successCode: 1
});

// 监控使用 success 字段的接口
ApiMonitor.init({
    monitoredApis: ['/Products/Cate?handler=DeleteBatch'],
    redirectUrl: '',
    successField: 'success',
    successCode: true,
    successCallback: function(data) {
        console.log('操作成功:', data);
        return false; // 返回false表示不需要重定向，由回调函数自己处理
    }
});

// 或者使用更多配置选项
ApiMonitor.init({
    monitoredApis: ['/Setting/ShippingArea', '/Setting/ShippingAreaZipcode'],
    redirectUrl: '/Setting/Shipping',
    successField: 'ret',
    successCode: 1,
    successCallback: function(data) {
        console.log('操作成功:', data);
        return true; // 返回true表示需要重定向，false表示不需要重定向
    }
});
*/