/**
 * 标签和关键词处理模块
 * 提供标签和SEO关键词的统一处理逻辑
 */
 
var TagsKeywordsHandler = (function() {
    // 配置对象，存储标签和关键词的配置信息
    const config = {
        // 关键词配置
        keyword: {
            // 选择器和字段名配置
            selectors: {
                container: '.seo_box .option_selected[data-type="seo_keyword"]',
                selectList: '.seo_box .option_selected[data-type="seo_keyword"] .select_list',
                inputField: '.seo_box .option_selected[data-type="seo_keyword"] input[name="_Option"]',
                hiddenField: 'input[name="SeoKeyword_en"]',
                valueField: 'input[name="SeoKeyword_en_value"]',
                editForm: '#edit_keyword_form',
                editInput: '#edit_keyword_form .edit_keyword_list .box_input',
                editSubmit: '#edit_keyword_form .btn_submit'
            },
            // 数据配置
            dataType: 'seo_keyword',
            itemFieldName: 'seoKeywords[]',
            nameFieldName: 'seoKeywordsName[]'
        },
        // 标签配置 - 产品页面中的标签
        tag: {
            // 选择器和字段名配置
            selectors: {
                container: '.global_container[data-name="tag_info"] .option_selected[data-type="tags"]',
                selectList: '.global_container[data-name="tag_info"] .option_selected[data-type="tags"] .select_list',
                inputField: '.global_container[data-name="tag_info"] .option_selected[data-type="tags"] input[name="_Option"]',
                hiddenField: 'input[name="TagString"]',
                valueField: 'input[name="TagString"]',
                idField: 'input[name="TagIds"]',
                editForm: '#edit_tag_form',
                editInput: '#edit_tag_form .edit_tag_list .box_input',
                editSubmit: '#edit_tag_form .btn_submit'
            },
            // 数据配置
            dataType: 'tags',
            itemFieldName: 'tagsOption[]',
            nameFieldName: 'tagsName[]',
            idFieldName: 'tagsId[]'
        }
    };
    
    /**
     * 初始化模块
     */
    function init() {
        // 确保存在TagIds隐藏字段
        ensureTagIdsField();
        
        // 初始化关键词处理
        initializeItems('keyword');
        
        // 初始化标签处理
        initializeItems('tag');
        
        // 绑定事件
        bindEvents();
    }
    
    /**
     * 确保存在TagIds隐藏字段
     */
    function ensureTagIdsField() {
        // 检查TagIds字段是否存在
        const tagIdsField = $(config.tag.selectors.idField);
        if (tagIdsField.length === 0) {
            // 如果不存在，创建一个
            const tagStringField = $(config.tag.selectors.hiddenField);
            if (tagStringField.length > 0) {
                $('<input type="hidden" name="TagIds" value="">').insertAfter(tagStringField);
            }
        }
    }
    
    /**
     * 初始化项目（标签或关键词）
     * @param {string} type - 类型，'keyword'或'tag'
     */
    function initializeItems(type) {
        const cfg = config[type];
        if (!cfg) return;
        
        // 获取隐藏字段值
        var hiddenInput = $(cfg.selectors.hiddenField);
        if (hiddenInput.length === 0) return;
        
        var inputVal = hiddenInput.val().trim();
        if (!inputVal) return;
        
        // 检查是否已经有显示的项目
        var existingElements = $(cfg.selectors.selectList + ' .btn_attr_choice').length;
        if (existingElements > 0) return;
        
        // 分割字符串
        var items = inputVal.split(',');
        var uniqueItems = {};
        
        // 添加每个项目
        items.forEach(function(item) {
            var trimmedItem = item.trim();
            if (trimmedItem && !uniqueItems[trimmedItem.toLowerCase()]) {
                uniqueItems[trimmedItem.toLowerCase()] = true;
                
                // 创建HTML元素
                var itemHtml = '<span class="btn_attr_choice" data-type="' + cfg.dataType + '">' +
                    '<b>' + trimmedItem + '</b>' +
                    '<input type="hidden" name="' + cfg.itemFieldName + '" value="' + trimmedItem + '">' +
                    '<input type="hidden" name="' + cfg.nameFieldName + '" value="' + trimmedItem + '">' +
                    '<i></i>' +
                    '</span>';
                
                // 添加到容器
                $(cfg.selectors.selectList).append(itemHtml);
            }
        });
        
        // 更新隐藏字段
        updateHiddenFields(type);
    }
    
    /**
     * 绑定事件
     */
    function bindEvents() {
        // 绑定关键词事件
        bindItemEvents('keyword');
        
        // 绑定标签事件
        bindItemEvents('tag');
        
        // 绑定标签选择事件
        bindTagSelectionEvents();
    }
    
    /**
     * 绑定标签选择事件
     */
    function bindTagSelectionEvents() {
        // 监听标签选择事件（从可选标签区域选择标签）
        $(document).on('click', '.option_not_yet .select_list[data-type="tags"] .btn_attr_choice', function(e) {
            e.stopPropagation();
            
            // 获取标签信息
            const tagText = $(this).find('b').text().trim();
            const tagId = $(this).data('id') || parseInt($(this).find('input[type="checkbox"]').val()) || 0;
            
            if (!tagText) return;
            
            // 检查是否已添加
            let alreadyAdded = false;
            $(config.tag.selectors.selectList + ' .btn_attr_choice b').each(function() {
                if ($(this).text().trim().toLowerCase() === tagText.toLowerCase()) {
                    alreadyAdded = true;
                    return false;
                }
            });
            
            // 如果未添加，则添加
            if (!alreadyAdded) {
                addTagWithId(tagText, tagId);
                updateHiddenFields('tag');
            }
        });
    }
    
    /**
     * 添加带ID的标签
     * @param {string} text - 标签文本
     * @param {number} id - 标签ID
     */
    function addTagWithId(text, id) {
        const cfg = config.tag;
        
        // 创建HTML元素，包含ID属性
        const $itemHtml = $('<span class="btn_attr_choice" data-type="' + cfg.dataType + '" data-id="' + id + '">' +
            '<b>' + text + '</b>' +
            '<input type="hidden" name="' + cfg.itemFieldName + '" value="' + text + '">' +
            '<input type="hidden" name="' + cfg.nameFieldName + '" value="' + text + '">' +
            '<input type="hidden" name="' + cfg.idFieldName + '" value="' + id + '">' +
            '<i></i>' +
            '</span>');
        
        // 添加到列表
        $(cfg.selectors.selectList).append($itemHtml);
    }
    
    /**
     * 绑定项目（标签或关键词）的事件
     * @param {string} type - 类型，'keyword'或'tag'
     */
    function bindItemEvents(type) {
        const cfg = config[type];
        if (!cfg) return;
        
        // 处理输入框事件
        const $inputField = $(cfg.selectors.inputField);
        
        // 处理回车和逗号输入
        $inputField.on('keyup', function(e) {
            // 获取输入的值
            var value = $(this).val().trim();
            
            // 回车键或逗号
            if ((e.keyCode === 13 || value.endsWith(',')) && value.length > 0) {
                e.preventDefault();
                
                // 移除末尾的逗号
                if (value.endsWith(',')) {
                    value = value.slice(0, -1);
                }
                
                // 如果有值，添加项目
                if (value) {
                    if (type === 'tag') {
                        // 对于标签，添加时同时设置ID
                        addTagWithId(value, 0); // 自定义标签ID为0
                    } else {
                        addItem(type, value);
                    }
                    $(this).val('');
                }
            }
        });
        
        // 处理项目删除
        $(document).on('click', cfg.selectors.selectList + ' .btn_attr_choice i', function() {
            $(this).parent().remove();
            updateHiddenFields(type);
        });
        
        // 处理编辑弹窗提交
        $(document).on('click', cfg.selectors.editSubmit, function(e) {
            e.preventDefault();
            e.stopPropagation();
            
            // 获取所有输入
            var items = [];
            var uniqueItems = {};
            
            $(cfg.selectors.editInput).each(function() {
                var item = $(this).val().trim();
                if (item && !uniqueItems[item.toLowerCase()]) {
                    uniqueItems[item.toLowerCase()] = true;
                    items.push(item);
                }
            });
            
            if (items.length > 0) {
                // 清空原有的项目
                var $selectList = $(cfg.selectors.selectList);
                $selectList.empty();
                
                // 添加新的项目
                items.forEach(function(item) {
                    if (type === 'tag') {
                        // 对于标签，添加时同时设置ID
                        addTagWithId(item, 0); // 编辑的标签ID为0
                    } else {
                        var $itemHtml = $('<span class="btn_attr_choice" data-type="' + cfg.dataType + '">' +
                            '<b>' + item + '</b>' +
                            '<input type="hidden" name="' + cfg.itemFieldName + '" value="' + item + '">' +
                            '<input type="hidden" name="' + cfg.nameFieldName + '" value="' + item + '">' +
                            '<i></i>' +
                            '</span>');
                        $selectList.append($itemHtml);
                    }
                });
                
                // 更新隐藏的值
                updateHiddenFields(type);
            }
            
            // 关闭弹窗
            if (typeof frame_obj !== 'undefined' && frame_obj.fixed_close) {
                frame_obj.fixed_close();
            }
            
            return false;
        });
    }
    
    /**
     * 添加项目（标签或关键词）
     * @param {string} type - 类型，'keyword'或'tag'
     * @param {string} text - 项目文本
     */
    function addItem(type, text) {
        const cfg = config[type];
        if (!cfg) return;
        
        // 检查是否已存在
        let exists = false;
        $(cfg.selectors.selectList + ' .btn_attr_choice').each(function() {
            if ($(this).find('b').text().toLowerCase() === text.toLowerCase()) {
                exists = true;
                return false; // 跳出循环
            }
        });
        
        // 如果已存在，不再添加
        if (exists) {
            return;
        }
        
        // 创建项目元素
        const $itemHtml = $('<span class="btn_attr_choice" data-type="' + cfg.dataType + '">' +
            '<b>' + text + '</b>' +
            '<input type="hidden" name="' + cfg.itemFieldName + '" value="' + text + '">' +
            '<input type="hidden" name="' + cfg.nameFieldName + '" value="' + text + '">' +
            '<i></i>' +
            '</span>');
        
        // 添加到列表
        $(cfg.selectors.selectList).append($itemHtml);
        
        // 更新隐藏字段
        updateHiddenFields(type);
    }
    
    /**
     * 更新项目（标签或关键词）的隐藏字段
     * @param {string} type - 类型，'keyword'或'tag'
     */
    function updateHiddenFields(type) {
        const cfg = config[type];
        if (!cfg) return;
        
        const itemValues = [];
        $(cfg.selectors.selectList + ' .btn_attr_choice').each(function() {
            const itemText = $(this).find('b').text().trim();
            if (itemText) {
                itemValues.push(itemText);
            }
        });
        
        // 设置字符串（逗号分隔）
        const valuesString = itemValues.join(',');
        $(cfg.selectors.hiddenField).val(valuesString);
        
        // 如果是标签，同时更新TagIds字段
        if (type === 'tag') {
            const tagIds = [];
            $(cfg.selectors.selectList + ' .btn_attr_choice').each(function() {
                // 尝试获取标签ID
                let tagId = $(this).data('id');
                if (!tagId) {
                    // 尝试从隐藏字段获取
                    const idInput = $(this).find('input[name="' + cfg.idFieldName + '"]');
                    if (idInput.length > 0) {
                        tagId = parseInt(idInput.val());
                    }
                }
                
                // 如果找到有效ID，添加到列表
                if (tagId && !isNaN(parseInt(tagId))) {
                    tagIds.push(parseInt(tagId));
                }
            });
            
            // 设置标签ID字符串 - 使用竖线分隔格式：|1|2|3|
            const tagIdsString = tagIds.length > 0 ? '|' + tagIds.join('|') + '|' : '';
            $(cfg.selectors.idField).val(tagIdsString);
        }
        
        // 如果存在valueField，也设置它的值
        if ($(cfg.selectors.valueField).length > 0) {
            if (type === 'tag') {
                // 对于标签，设置为1表示有值
                $(cfg.selectors.valueField).val(itemValues.length > 0 ? "1" : "0");
            } else {
                // 对于关键词，设置为相同的字符串
                $(cfg.selectors.valueField).val(valuesString);
            }
        }
    }
    
    /**
     * 更新所有隐藏字段
     */
    function updateAllHiddenFields() {
        updateHiddenFields('keyword');
        updateHiddenFields('tag');
    }
    
    // 返回公共接口
    return {
        init: init,
        addKeyword: function(text) { addItem('keyword', text); },
        addTag: function(text) { addTagWithId(text, 0); },
        updateKeywordHiddenFields: function() { updateHiddenFields('keyword'); },
        updateTagHiddenFields: function() { updateHiddenFields('tag'); },
        updateAllHiddenFields: updateAllHiddenFields
    };
})();

// 自动初始化
jQuery(function() {
    if (typeof TagsKeywordsHandler !== 'undefined') {
        TagsKeywordsHandler.init();
    }
}); 