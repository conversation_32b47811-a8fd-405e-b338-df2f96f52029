/* 历史抽屉和历史记录样式 */

/* 历史抽屉背景 */
.history-drawer-backdrop {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 1040;
    display: none;
    opacity: 0;
    transition: opacity 0.3s ease;
    backdrop-filter: blur(2px);
}

.history-drawer-backdrop.show {
    opacity: 1;
    display: block;
}

/* 历史抽屉 */
.history-drawer {
    position: fixed;
    top: 60px; /* 从顶部预留60px的距离，与顶部导航栏保持一致 */
    right: 0;
    bottom: 0;
    width: 450px;
    background-color: #fff;
    box-shadow: -3px 0 20px rgba(0, 0, 0, 0.15);
    z-index: 1050;
    transform: translateX(100%);
    transition: transform 0.3s ease;
    display: flex;
    flex-direction: column;
    border-top-left-radius: 12px;
    border-left: 1px solid #e9ecef;
    overflow: hidden;
}

.history-drawer.show {
    transform: translateX(0);
}

/* 历史抽屉内容 */
.history-drawer-content {
    display: flex;
    flex-direction: column;
    height: 100%;
    overflow: hidden;
}

.history-drawer-header {
    padding: 16px;
    border-bottom: 1px solid #e9ecef;
    display: flex;
    align-items: center;
    justify-content: space-between;
    background-color: #f8f9fa;
    border-top-left-radius: 12px;
}

.history-drawer-title {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
    color: #212529;
}

.history-drawer-header .close {
    font-size: 22px;
    color: #6c757d;
    opacity: 0.7;
    transition: all 0.2s ease;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
}

.history-drawer-header .close:hover {
    opacity: 1;
    background-color: rgba(108, 117, 125, 0.1);
}

.history-file-path {
    padding: 12px 16px;
    background-color: #f8f9fa;
    border-bottom: 1px solid #e9ecef;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    color: #495057;
}

.history-file-path i {
    color: #6c757d;
    margin-right: 6px;
}

.history-drawer-body {
    padding: 16px;
    flex-grow: 1;
    overflow-y: auto;
}

/* 历史版本预览面板 */
.history-preview-panel {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    background-color: #fff;
    z-index: 1;
    display: flex;
    flex-direction: column;
}

.history-preview-header {
    padding: 16px;
    border-bottom: 1px solid #e9ecef;
    display: flex;
    align-items: center;
    justify-content: space-between;
    background-color: #f8f9fa;
}

.history-preview-title {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
    color: #212529;
}

.history-preview-header .close {
    font-size: 22px;
    color: #6c757d;
    opacity: 0.7;
    transition: all 0.2s ease;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
}

.history-preview-header .close:hover {
    opacity: 1;
    background-color: rgba(108, 117, 125, 0.1);
}

.history-preview-body {
    padding: 16px;
    flex-grow: 1;
    overflow-y: auto;
}

.history-preview-footer {
    padding: 12px 16px;
    border-top: 1px solid #e9ecef;
    text-align: right;
    background-color: #f8f9fa;
}

.history-preview-content pre {
    background-color: #f8f9fa;
    padding: 16px;
    border-radius: 8px;
    font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
    font-size: 13px;
    white-space: pre-wrap;
    word-break: break-all;
    max-height: calc(100vh - 220px);
    overflow: auto;
    border: 1px solid #e9ecef;
    box-shadow: inset 0 1px 3px rgba(0,0,0,0.05);
}

/* 增强历史记录列表样式 */
.history-list {
    max-height: none;
}

.history-item {
    border-left: 3px solid #ddd;
    transition: all 0.2s ease;
    margin-bottom: 12px;
    border-radius: 8px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.05);
    overflow: hidden;
    background-color: #fff;
    border: 1px solid #e9ecef;
}

.history-item:hover {
    background-color: #f8f9fa;
    border-left-color: #0d6efd;
    box-shadow: 0 3px 6px rgba(0,0,0,0.08);
}

.history-item .list-group-item {
    border: none;
    background: transparent;
    padding: 12px 16px;
}

.badge {
    padding: 6px 8px;
    font-weight: 500;
    border-radius: 6px;
    font-size: 11px;
}

.badge-success {
    background-color: #d4edda;
    color: #155724;
}

.badge-primary {
    background-color: #cfe2ff;
    color: #084298;
}

.badge-danger {
    background-color: #f8d7da;
    color: #721c24;
}

.history-item .btn {
    padding: 5px 10px;
    font-size: 12px;
    border-radius: 6px;
    transition: all 0.2s ease;
    box-shadow: 0 1px 2px rgba(0,0,0,0.05);
    margin-top: 8px;
}

.history-item .btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
}

.history-item .btn-outline-primary {
    color: #606266;
    border-color: #dcdfe6;
    background-color: #fff;
}

.history-item .btn-outline-primary:hover {
    background-color: #f4f4f5;
    border-color: #c6c8cc;
    color: #303133;
}

.history-item .btn-outline-success {
    color: #409eff;
    border-color: #c6e2ff;
    background-color: #ecf5ff;
}

.history-item .btn-outline-success:hover {
    background-color: #dcedff;
    border-color: #a0cfff;
    color: #409eff;
}

.history-comment {
    color: #6c757d;
    font-style: italic;
    margin-top: 6px;
    padding-top: 6px;
    border-top: 1px dashed #e9ecef;
    font-size: 13px;
}

/* 应用按钮样式 */
#apply-history-content {
    background: #409eff;
    border: none;
    box-shadow: 0 2px 6px rgba(0,0,0,0.1);
    padding: 8px 18px;
    font-weight: 500;
    border-radius: 6px;
    transition: all 0.2s ease;
}

#apply-history-content:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(64, 158, 255, 0.2);
    background: #66b1ff;
}

#apply-history-content:active {
    transform: translateY(0px);
    box-shadow: 0 2px 4px rgba(64, 158, 255, 0.1);
    background: #3a8ee6;
}

/* 调整按钮组容器的样式 */
.history-item .btn-group, 
.history-item .btn-toolbar {
    margin-top: 10px; /* 调整按钮组的上边距 */
    display: flex;
    gap: 8px; /* 按钮之间的间距 */
} 