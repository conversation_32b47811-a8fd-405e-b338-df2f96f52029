/* 编辑器核心样式 */

.editor-wrapper {
    display: flex;
    flex-direction: column;
    height: calc(100vh - 60px); /* 减少高度，消除底部空白 */
    border: 1px solid #ddd;
    border-radius: 8px;
    background-color: #fff;
    box-shadow: 0 3px 10px rgba(0,0,0,0.08);
    overflow: hidden; /* 防止内容溢出 */
    position: relative; /* 添加相对定位 */
}

.editor-toolbar {
    display: flex;
    padding: 10px 16px;
    background-color: #f8f9fa;
    border-bottom: 1px solid #e9ecef;
    flex-wrap: wrap;
    gap: 8px; /* 按钮之间添加间距 */
    align-items: center;
}

.editor-container {
    flex-grow: 1;
    position: relative;
    height: 100%;
    overflow: hidden;
    min-height: 400px; /* 增加最小高度 */
}

/* 确保CodeMirror占据整个容器 */
.CodeMirror {
    height: 100% !important;
    font-size: 14px;
    font-family: 'Consolas', 'Monaco', 'Courier New', monospace; /* 更好的代码字体 */
}

.editor-statusbar {
    padding: 6px 16px;
    background-color: #f8f9fa;
    border-top: 1px solid #e9ecef;
    font-size: 12px;
    color: #6c757d;
    display: flex;
    justify-content: space-between;
}

.editor-loading {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(255, 255, 255, 0.9);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    backdrop-filter: blur(2px);
}

.editor-loading-spinner {
    border: 3px solid #f3f3f3;
    border-top: 3px solid #3498db;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    animation: spin 0.8s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.editor-loading-text {
    margin-top: 12px;
    font-size: 14px;
    color: #495057;
    font-weight: 500;
}

/* 改进按钮样式 */
.editor-toolbar .btn {
    padding: 8px 12px;
    border-radius: 6px;
    font-weight: 500;
    font-size: 13px;
    transition: all 0.2s ease;
    border: none;
    box-shadow: 0 1px 3px rgba(0,0,0,0.12);
    display: flex;
    align-items: center;
    gap: 6px;
    line-height: 1.2;
    height: 36px;
}

.editor-toolbar .btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 3px 6px rgba(0,0,0,0.15);
}

.editor-toolbar .btn:active {
    transform: translateY(0);
    box-shadow: 0 1px 2px rgba(0,0,0,0.1);
}

.editor-toolbar .btn-outline-secondary {
    background-color: #fff;
    color: #495057;
    border: 1px solid #e9ecef;
}

.editor-toolbar .btn-outline-secondary:hover {
    background-color: #f8f9fa;
    color: #212529;
}

.editor-toolbar .btn-outline-info {
    background-color: #e3f2fd;
    color: #0d6efd;
    border: none;
}

.editor-toolbar .btn-outline-info:hover {
    background-color: #d0e8ff;
    color: #0a58ca;
}

.editor-toolbar .btn-primary {
    background: #409eff;
    color: white;
    border: none;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.editor-toolbar .btn-primary:hover {
    background: #66b1ff;
    box-shadow: 0 4px 8px rgba(64, 158, 255, 0.2);
}

.editor-toolbar .btn-primary:active {
    background: #3a8ee6;
    box-shadow: 0 2px 4px rgba(64, 158, 255, 0.1);
}

/* 添加更多空间给"保存"按钮 */
.editor-toolbar .ml-auto {
    margin-left: auto;
    min-width: 90px;
    justify-content: center;
}

/* 确保页面内容不会出现底部滚动条 */
.container-fluid {
    padding-bottom: 0 !important;
    margin-bottom: 0 !important;
}

/* 适配主布局，消除底部空白 */
body, html {
    height: 100%;
    margin: 0;
    padding: 0;
    overflow: hidden;
}

main {
    padding-bottom: 0 !important;
    margin-bottom: 0 !important;
}

/* 历史记录相关样式 */
.history-list {
    max-height: 400px;
    overflow-y: auto;
}

.history-item {
    border-left: 3px solid #ddd;
    transition: all 0.2s;
    margin-bottom: 8px;
    border-radius: 6px;
}

.history-item:hover {
    background-color: #f8f9fa;
    border-left-color: #0d6efd;
    box-shadow: 0 2px 5px rgba(0,0,0,0.05);
}

.history-comment {
    color: #6c757d;
    font-style: italic;
}

.history-preview {
    background-color: #f8f9fa;
    padding: 12px;
    border-radius: 6px;
    font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
    font-size: 13px;
    white-space: pre-wrap;
    word-break: break-all;
    max-height: 350px;
    overflow: auto;
    box-shadow: inset 0 1px 3px rgba(0,0,0,0.05);
} 