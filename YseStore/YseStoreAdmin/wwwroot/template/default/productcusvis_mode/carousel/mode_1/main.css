.ly_carousel_1.container_screen{ margin: 0 auto; }
.ly_carousel_1 .carousel_wrapper.screen_full{ width: 100%; }
.ly_carousel_1 .carousel_wrapper{position: relative; margin:0 auto;z-index: 0;}
.ly_carousel_1 .carousel_wrapper a:hover{text-decoration: unset;}
.ly_carousel_1 .carousel_wrapper .carousel_box{width: 100%;height: 100%;position: absolute;top:0;left: 0;overflow: hidden;}
.ly_carousel_1 .carousel_wrapper .carousel_box .carousel_item{position: absolute;width: 100%;}
.ly_carousel_1 .carousel_wrapper .carousel_box .carousel_item .carousel_info{position: absolute;width: 55%;z-index: 10;box-sizing: border-box;}
.ly_carousel_1 .carousel_wrapper .carousel_box .carousel_item .carousel_info .carousel_title{margin-bottom:20px;}
.ly_carousel_1 .carousel_wrapper .carousel_box .carousel_item .carousel_info .carousel_content{margin-bottom:40px;line-height:1.8; }
.ly_carousel_1 .carousel_wrapper .carousel_box .carousel_item .carousel_info .carousel_btn{  display: inline-block; width: 160px;height: 40px;line-height: 40px; border-radius: 20px;text-align: center;font-size:16px; border: 1px solid transparent; overflow: hidden;text-overflow: ellipsis;display: -webkit-box;-webkit-box-orient: vertical;-webkit-line-clamp: 1 }
.ly_carousel_1 .carousel_wrapper .carousel_box .carousel_item .carousel_info .carousel_btn:hover{text-decoration: unset;}
.ly_carousel_1 .carousel_wrapper .tab_type_position{position: absolute;bottom:23px;width: 100%; text-align: center;z-index: 1000;}
.ly_carousel_1 .carousel_wrapper .carousel_tab.btn_none{display: none;}
.ly_carousel_1 .carousel_wrapper .carousel_tab .tab_btn{display: inline-block;vertical-align: middle;}
.ly_carousel_1 .carousel_wrapper .carousel_tab .tab_dots{width: 10px;height: 10px;margin:0 3px; background-color: #8e8d8f; border-radius: 50%;}
.ly_carousel_1 .carousel_wrapper .carousel_tab .tab_dots.activity{background-color: #ffffff;}
.ly_carousel_1 .carousel_wrapper .carousel_tab .tab_line{width: 17px;height: 7px;margin:0 3px;background-color: #8e8d8f;border-radius: 4px;}
.ly_carousel_1 .carousel_wrapper .carousel_tab .tab_line.activity{background-color: #ffffff;}
.ly_carousel_1 .carousel_wrapper .carousel_tab .tab_arrow{position: absolute;top:50%;transform: translateY(-50%);color:#a5a7ab;z-index: 10;transition: all .3s;}
.ly_carousel_1 .carousel_wrapper .carousel_tab .tab_arrow i{font-size: 46px;}
.ly_carousel_1 .carousel_wrapper .carousel_tab .tab_arrow.tab_prev{__left__: 3%;}
.ly_carousel_1 .carousel_wrapper .carousel_tab .tab_arrow.tab_next{__right__: 3%;}
.ly_carousel_1 .carousel_wrapper .carousel_tab .tab_arrow:hover{color:#fff;}
.ly_carousel_1 .carousel_wrapper .animate_transition {transition:transform 0.5s ease-in 0.05s,opacity 0.5s ease-in 0.05s;}
.ly_carousel_1 .carousel_wrapper .carousel_none{opacity: 0;z-index: -1;pointer-events: none;}
.ly_carousel_1 .carousel_wrapper .carousel_show{opacity: 1;z-index: 1;pointer-events: auto;}
.ly_carousel_1 .carousel_wrapper .animate_out_img_move{opacity:0;transform:translateX(50px);}
.ly_carousel_1 .carousel_wrapper .animation_rise_3{ animation:1s cubic-bezier(0.26, 0.54, 0.32, 1) 0.3s forwards;animation-name:rise_up;}
.ly_carousel_1 .carousel_wrapper .animation_rise_4{ animation:1s cubic-bezier(0.26, 0.54, 0.32, 1) 0.4s forwards;animation-name:rise_up;}
.ly_carousel_1 .carousel_wrapper .txt_opacity{opacity: 0;}
.ly_carousel_1 .carousel_wrapper .carousel_item .animate_btn{opacity: 0;transition:all 1s ;}
.ly_carousel_1 .carousel_wrapper .carousel_item.carousel_show .animate_btn{opacity: 1;}
.ly_carousel_1 .carousel_wrapper .animate_out_img_left{-webkit-transform: translateX(-200px);transform: translateX(-200px);-webkit-transition: opacity .5s ease-in .05s,-webkit-transform .5s ease-in .05s;transition: opacity .5s ease-in .05s,-webkit-transform .5s ease-in .05s;transition: transform .5s ease-in .05s,opacity .5s ease-in .05s;transition: transform .5s ease-in .05s,opacity .5s ease-in .05s,-webkit-transform .5s ease-in .05s;z-index: 10;}
.ly_carousel_1 .carousel_wrapper .carousel_item.carousel_show.carousel_animate_scale .carousel_img{ animation:carousel_img 2s ease;}

@media screen and (max-width: 1000px) {
	.ly_carousel_1 .carousel_wrapper .carousel_box .carousel_item .carousel_info{width: 95%;}
	.ly_carousel_1 .carousel_wrapper .tab_type_position{bottom:3%}
	.ly_carousel_1 .carousel_wrapper .carousel_box .carousel_item .carousel_info .carousel_title{margin-bottom:10px; }
	.ly_carousel_1 .carousel_wrapper .carousel_box .carousel_item .carousel_info .carousel_content{margin-bottom:20px; }
}
.themes_box_content{font-family: var(--ThemesTextContentFont);}
@keyframes rise_up{
	0%{
	  opacity:0;
	  transform:translateY(120%);
	}
	to{
	  opacity:1;
	  transform:translateY(0%);
	}
}

@keyframes carousel_img{
	0% {
		-webkit-transform: scale(1.3);
		transform: scale(1.3);
	}

	to {
		-webkit-transform: scale(1);
		transform: scale(1);
	}
}