{"Blocks": {"Poster-1": {"Category": {"type": "category", "autofill": {"CategoryName": "Title", "CategoryImage": "Pic", "CategoryUrl": "Link"}, "value": ""}, "Pic": {"type": "image", "expand": {"width": "311", "height": "311"}, "value": "{CDN_URL_MODE}poster/mode_233/index00.jpg"}, "Title": {"type": "input", "value": "Baby"}, "Link": {"type": "link", "value": ""}, "ImageAlt": {"type": "input", "value": "", "expand": {"hint": "tips"}}}, "Poster-2": {"Category": {"type": "category", "autofill": {"CategoryName": "Title", "CategoryImage": "Pic", "CategoryUrl": "Link"}, "value": ""}, "Pic": {"type": "image", "expand": {"width": "311", "height": "311"}, "value": "{CDN_URL_MODE}poster/mode_233/index00.jpg"}, "Title": {"type": "input", "value": "Baby"}, "Link": {"type": "link", "value": ""}, "ImageAlt": {"type": "input", "value": "", "expand": {"hint": "tips"}}}, "Poster-3": {"Category": {"type": "category", "autofill": {"CategoryName": "Title", "CategoryImage": "Pic", "CategoryUrl": "Link"}, "value": ""}, "Pic": {"type": "image", "expand": {"width": "311", "height": "311"}, "value": "{CDN_URL_MODE}poster/mode_233/index00.jpg"}, "Title": {"type": "input", "value": "Baby"}, "Link": {"type": "link", "value": ""}, "ImageAlt": {"type": "input", "value": "", "expand": {"hint": "tips"}}}, "Poster-4": {"Category": {"type": "category", "autofill": {"CategoryName": "Title", "CategoryImage": "Pic", "CategoryUrl": "Link"}, "value": ""}, "Pic": {"type": "image", "expand": {"width": "311", "height": "311"}, "value": "{CDN_URL_MODE}poster/mode_233/index00.jpg"}, "Title": {"type": "input", "value": "Baby"}, "Link": {"type": "link", "value": ""}, "ImageAlt": {"type": "input", "value": "", "expand": {"hint": "tips"}}}, "Poster-5": {"Category": {"type": "category", "autofill": {"CategoryName": "Title", "CategoryImage": "Pic", "CategoryUrl": "Link"}, "value": ""}, "Pic": {"type": "image", "expand": {"width": "311", "height": "311"}, "value": "{CDN_URL_MODE}poster/mode_233/index00.jpg"}, "Title": {"type": "input", "value": "Baby"}, "Link": {"type": "link", "value": ""}, "ImageAlt": {"type": "input", "value": "", "expand": {"hint": "tips"}}}, "Poster-6": {"Category": {"type": "category", "autofill": {"CategoryName": "Title", "CategoryImage": "Pic", "CategoryUrl": "Link"}, "value": ""}, "Pic": {"type": "image", "expand": {"width": "311", "height": "311"}, "value": "{CDN_URL_MODE}poster/mode_233/index00.jpg"}, "Title": {"type": "input", "value": "Baby"}, "Link": {"type": "link", "value": ""}, "ImageAlt": {"type": "input", "value": "", "expand": {"hint": "tips"}}}}, "Settings": {"Title": {"type": "input", "value": "POPULAR"}, "SubTitle": {"type": "input", "value": "CATEGORIES"}, "Content": {"type": "richtext", "value": "Join over half a million tools lovers and get our latest deals, articles, and resources sent straight to your inbox!"}, "ColumnsPc": {"type": "select", "options": ["2", "3", "4", "5", "6"], "value": "6"}, "ColumnsMobile": {"type": "select", "options": ["1", "2", "3"], "value": "2"}}, "Config": {"PagePermit": ["*"], "BlocksAdd": true}, "Translation": {"Blocks": {"Poster-{x}": ["Title"]}, "Settings": ["Title", "ButtonText"]}}