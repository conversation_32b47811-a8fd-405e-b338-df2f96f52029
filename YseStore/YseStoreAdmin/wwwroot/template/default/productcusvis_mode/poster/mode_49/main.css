.ly_poster_49{margin: 20px auto;}
.ly_poster_49 .text{position: relative; width: 48.5%; font-size: 24px; margin-__right__: 3%;margin-bottom: 3%; background-repeat: no-repeat; background-size: cover;overflow: hidden;}
.ly_poster_49 .text:nth-child(2n){margin-__right__: 0;}
.ly_poster_49 .text_box{position:absolute; z-index: 2;}
.ly_poster_49 .text_content{top:50%; left:45%;transform: translateY(-50%);text-align: left;}
.ly_poster_49 .text_content .t_c_more{display: flex;align-items: center;justify-content: flex-start;margin-top: 18px; text-decoration: none;}
.ly_poster_49 .text_content .t_c_more .btn_name{height: 42px;line-height: 42px;font-size: 16px;color: #333333;padding: 0 16px;background: #fed925;border-radius: 4px 0 0 4px;}
.ly_poster_49 .text_content .t_c_more .icon{width: 45px;height: 42px;background-image: url(/static/ico/web/cart_icon_1.png);background-repeat: no-repeat;background-color: #111111;background-position: center center;border-radius: 0 4px 4px 0;}
.ly_poster_49 img{-webkit-transition:all 0.5s ease; -o-transition:all 0.5s ease; transition:all 0.5s ease; -webkit-transform:scale(1); -ms-transform:scale(1); transform:scale(1);}
.ly_poster_49 a{display: block;width: 100%;height: 100%;}
.ly_poster_49 a:hover img{-webkit-transform:scale(1.05); -ms-transform:scale(1.05); transform:scale(1.05); -webkit-transition:all 0.5s ease; -o-transition:all 0.5s ease; transition:all 0.5s ease;}
.ly_poster_49 a:before {content:""; width:100%; height:100%; position:absolute; top:0; __left__:0; z-index:1;}
.ly_poster_49 .wrap{ width: 1200px; margin: 0 auto; }
.ly_poster_49 .text_box{ box-sizing: border-box; padding: 0 10px 0 0; }
.ly_poster_49 .text_box .t_c_title{font-size: 24px;font-family: "Montserrat-Bold";color: #333333;}
@media screen and (max-width:1250px){
	.ly_poster_49 .wrap{ width: 100%; }
}
@media screen and (max-width: 1000px){
	.ly_poster_49{margin-top: 20px;}
	.ly_poster_49 a:hover img{-webkit-transform: scale(1);-ms-transform: scale(1);transform: scale(1);}
    .ly_poster_49 .wrap{ padding: 0;}
	.ly_poster_49 .text{width: calc( 100% - 30px );margin: 0 15px 15px 15px;}
	.ly_poster_49 .text_content .t_c_more{margin-top: 10px;}
	.ly_poster_49 .text_box .t_c_title{font-size: 14px;}
	.ly_poster_49 .text_content .t_c_more .btn_name{height: 32px;line-height: 32px;font-size: 14px;}
	.ly_poster_49 .text_content .t_c_more .icon{width: 35px;height: 32px;}
}