{"Blocks": {"Poster-1": {"Pic": {"type": "image", "expand": {"width": "183", "height": "80"}, "value": "{CDN_URL_MODE}poster/mode_137/index70.jpg"}, "ImageAlt": {"type": "input", "value": "", "expand": {"hint": "tips"}}, "Link": {"type": "link", "value": ""}}, "Poster-2": {"Pic": {"type": "image", "expand": {"width": "183", "height": "80"}, "value": "{CDN_URL_MODE}poster/mode_137/index70.jpg"}, "ImageAlt": {"type": "input", "value": "", "expand": {"hint": "tips"}}, "Link": {"type": "link", "value": ""}}, "Poster-3": {"Pic": {"type": "image", "expand": {"width": "183", "height": "80"}, "value": "{CDN_URL_MODE}poster/mode_137/index70.jpg"}, "ImageAlt": {"type": "input", "value": "", "expand": {"hint": "tips"}}, "Link": {"type": "link", "value": ""}}, "Poster-4": {"Pic": {"type": "image", "expand": {"width": "183", "height": "80"}, "value": "{CDN_URL_MODE}poster/mode_137/index70.jpg"}, "ImageAlt": {"type": "input", "value": "", "expand": {"hint": "tips"}}, "Link": {"type": "link", "value": ""}}, "Poster-5": {"Pic": {"type": "image", "expand": {"width": "183", "height": "80"}, "value": "{CDN_URL_MODE}poster/mode_137/index70.jpg"}, "ImageAlt": {"type": "input", "value": "", "expand": {"hint": "tips"}}, "Link": {"type": "link", "value": ""}}, "Poster-6": {"Pic": {"type": "image", "expand": {"width": "183", "height": "80"}, "value": "{CDN_URL_MODE}poster/mode_137/index70.jpg"}, "ImageAlt": {"type": "input", "value": "", "expand": {"hint": "tips"}}, "Link": {"type": "link", "value": ""}}}, "Config": {"PagePermit": ["*"], "BlocksAdd": true}, "Translation": {"Blocks": {"Poster-{x}": ["Title"]}}}