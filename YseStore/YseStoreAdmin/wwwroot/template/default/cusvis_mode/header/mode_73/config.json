{"Blocks": {"Logo": {"Logo": {"type": "image", "expand": {"width": "270", "height": "85"}, "value": "{CDN_URL}static/images/logo.png"}, "ImageAlt": {"type": "input", "value": ""}, "LogoWidth": {"type": "progress", "options": ["100", "400"], "suffix": "px", "value": "270px"}, "MobileLogoWidth": {"type": "progress", "options": ["60", "200"], "suffix": "px", "value": "200px"}}, "Menu": {"Menu": {"type": "panel", "expand": {"editlink": "/manage/view/nav", "hint": "tips"}, "linkage": {"1": ["MenuStyle"]}, "value": 1}}}, "Settings": {"Search": {"type": "switch", "linkage": {"1": ["SearchPlaceholder"]}, "value": 1}, "SearchPlaceholder": {"type": "input", "expand": {"placeholder": "placeholder"}, "value": "Search..."}, "User": {"type": "switch", "value": 1}, "ShoppingCart": {"type": "switch", "value": 1}, "HeaderFixedPc": {"type": "switch", "value": 0}, "HeaderFixedMobile": {"type": "switch", "value": 0}, "LanguageSwitch": {"type": "switch", "expand": {"hint": "goset"}, "value": 1}}, "Config": {"AllowDelete": false}, "Translation": {"Blocks": {"Board": ["Title"], "ProductsCategory": ["Title"]}, "Settings": ["SearchPlaceholder"]}}