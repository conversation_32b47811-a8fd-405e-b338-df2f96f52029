#header.ly_header_73.header_fixed_top .headerFixed{ position: fixed; top: 0; __left__: 0; width: 100%; z-index: 100; background-color: var(--ThemesHeaderBgColor); }
@media screen and (max-width: 1000px) {
	#header.ly_header_73 .headerFixed{ position: absolute;top: 0; __left__: 0; width: 100%; z-index: 100; }
}
#header.ly_header_73{box-shadow: 0 0 10px rgb(0 0 0 / 8%);}
#header.ly_header_73 .default_nav_style {font-family: var(--ThemesNavFont);}
#header.ly_header_73 .ly_header_top{display: none;}
#header.ly_header_73 .ly_header_top .ly_i_wrapper_1200{display:flex;align-items:center;justify-content:flex-end;}
#header.ly_header_73 .ly_header_top .ly_header_lctext{display:inline-flex;align-items:center;justify-content:center;font-size:12px;line-height:36px;}
#header.ly_header_73 .ly_header_bot .ly_i_wrapper_1200{display:flex;flex-wrap:wrap;align-items:center;justify-content:space-between;padding-top:9px;padding-bottom:10px;}
#header.ly_header_73 .ly_header_bot .ly_i_wrapper_1200 .ly_header_b_left{ margin-__right__: 0; }
.ly_header_73 .default_language_currency_style{margin-__left__: 15px;display:inline-flex;align-items:center;justify-content:center;line-height:36px;font-size:0;margin-right: 0;}
.ly_header_73 .default_language_currency_style dt{padding: 0 0 ;}
.ly_header_73 .default_language_currency_style dt i{display:none;}
.ly_header_73 .default_nav_style{width: 100%;display:inline-flex;flex-wrap:wrap;align-items:center;justify-content:flex-start;padding: 5px 0;}
.ly_header_73 .default_nav_style li{font-size:16px;line-height:34px;padding:0px 36px 0 0;}
.ly_header_73 .default_nav_style li:first-child{padding-left: 0;}
.ly_header_73 .default_nav_style li a{padding: 0 0 ;font-family: 'Montserrat-Light';}
.ly_header_73 .default_nav_style li a:hover{font-family: 'Montserrat-Regular';}
.ly_header_73 .default_nav_style li.has_sec i{margin-__left__:7px;font-size:14px;}
.ly_header_73 .ly_nav.ly_unhas_cate  .ly_nav_cate{width: 0;height: 0;}
.ly_header_73 .ly_nav.ly_unhas_cate  .ly_nav_default {width: 100%;}

.ly_header_73 .ly_header_b_left img{top:0; __right__: unset; bottom: 0; __left__: 0; margin: auto;}
.ly_header_73 .ly_header_b_center{flex:1;margin-__left__:20px;margin-__right__:20px;max-width: 520px;}

.ly_header_73 .default_search_style{display:flex;width:100%;}
.ly_header_73 .default_search_style .default_search_form{display:flex;width:100%;line-height:50px;}
.ly_header_73 .default_search_style .default_search_form .form{position:relative;display:flex;width:100%;align-items: center;}
.ly_header_73 .default_search_style .default_search_form .form .text{width:100%;height: 36px;border:0;text-indent:18px;border: 1px solid #cccccc;border-left: none;border-radius: 0 4px 4px 0;}
.ly_header_73 .default_search_style .default_search_form .form .button{position:absolute;top:0;bottom:0;__right__:0;display:inline-flex;align-items:center;justify-items:center;width: 38px;height: 38px;border: 1px solid #cccccc;background-image: url(/static/ico/web/header_search_icon.png);background-position: center center;background-repeat: no-repeat;z-index: 2;border-radius: 0 4px 4px 0;}
.ly_header_73 .default_search_style .default_search_form .form .button i{display: none; __right__:0;bottom:0;margin:auto;z-index: 3;font-size: 0;}
.ly_header_73 .default_search_style .default_search_form .cate_box{height: 36px;font-size: 0;border: 1px solid #cccccc;border-radius: 8px 0 0 8px;}
.ly_header_73 .default_search_style .default_search_form .cate_box select{position: relative;top: 0;width: 138px;height: 36px;appearance:none;  -moz-appearance:none;  -webkit-appearance:none;  background: url(/static/ico/web/cate_icon.png) no-repeat scroll 94% center transparent;text-align: center;border: none;display: -webkit-box;text-overflow:ellipsis;overflow:hidden;-webkit-line-clamp: 1;-webkit-box-orient: vertical;padding: 0 20px 0 10px;text-align: left;}
.index .ly_header_73 .default_search_style .default_search_form .cate_box select{top: -1px;}
.ly_header_73 .default_search_style .default_search_form .cate_box option{font-size: 16px;color: #666666;}

.ly_header_73 .ly_header_b_right{display:inline-flex;align-items:center;justify-content:flex-end;}
.ly_header_73 .ly_header_user{display:flex;align-items:center;}
.ly_header_73 .default_account_style{display:inline-flex;width:auto;line-height:1;}
.ly_header_73 .default_account_style .iconfont{position: absolute;top: 0;left: 0;display:flex;align-items:center;justify-content:center;font-size: 24px;margin-__right__: 0px;}
.ly_header_73 .default_account_style dl{display:inline-flex;align-items:center;}
.ly_header_73 .default_account_style dl dt{display:flex;flex-wrap:wrap;padding:0 0;font-size:0;line-height:1;}
.ly_header_73 .default_account_style dl dt i{display:none;}
.ly_header_73 .default_account_style dl dt a{order: 0;}
.ly_header_73 .default_account_style dl dt a{width:auto;font-size:14px;line-height:16px;}
.ly_header_73 .default_account_style dl dt.has_after::after{content:'/';font-size:14px;line-height:16px;order:1;margin:0 5px;}
.ly_header_73 .default_account_style dl dt a:nth-child(2) , 
.ly_header_73 .default_account_style dl dt a:not(.SignInButton){order:2;}
.ly_header_73 .default_account_style dl dt a:hover{text-decoration: none;}
.ly_header_73 .default_account_style dl dt	{width: 180px;}
.ly_header_73 .default_account_style .FontColor.fl{display: none;}
.ly_header_73 .default_account_style .themes_dropdown{z-index: 100;}
.ly_header_73 .default_shopping_cart_style{display:inline-flex;margin-__left__:15px;align-items:center;position:relative;padding-__left__: 24px;flex-wrap:wrap;font-size:10px;height:30px;flex-direction:column;justify-content:center;}
.ly_header_73 .default_shopping_cart_style:hover{text-decoration:none;}
.ly_header_73 .default_shopping_cart_style .iconfont{position:absolute;__left__:0;top:50%;transform:translateY(-50%);font-size:20px;}
.ly_header_73 .default_shopping_cart_style .iconfont{width: 15px;height: 20px;background: url(/static/ico/web/cart_header_icon.png) no-repeat center center / 15px 17px;font-size: 0;}
.ly_header_73 .default_shopping_cart_style .cart_count{position:absolute;display:block;top:-5px;right:-12px;padding:1px 6px;font-size:10px;border-radius:10px;display: block;border: none;}
.ly_header_73 .default_shopping_cart_style .text_0{display: none; font-size:14px;width:100%;font-family:"OpenSans-Bold";}
.ly_header_73 .default_shopping_cart_style .cart_count_price{display: none; width:100%;font-size:14px;}
.ly_header_73 .default_shopping_cart_style .text_1{display:none;}
.ly_header_73 .logo a{display:block;max-width:200px;}
.ly_header_73 .ly_header_menu{display:none;}
.ly_header_73 .ly_nav{display:flex;align-items:center;justify-content:center;}
.ly_header_73 .ly_nav .ly_i_wrapper_1200{display:flex;align-items:flex-end;justify-content:space-between;}
.ly_header_73 .ly_nav_cate{position:relative;width:270px;margin-__right__:20px;min-height:50px;}
.ly_header_73 .ly_nav_default{flex: 1;}
.ly_header_73 .default_cate_nav_style .cate_nav_title{border-top-__right__-radius:4px;border-top-__left__-radius:4px;padding:8px 21px;cursor: pointer;}
.ly_header_73 .default_cate_nav_style .cate_nav_title i{display:none;}
.ly_header_73 .default_cate_nav_style .cate_nav_title span{font-size:16px;line-height:34px;font-weight:600;}
.ly_header_73 .default_cate_nav_style .cate_nav_list{position:absolute;display:block;width:100%;__left__:0;top:auto;z-index:22;height:0;}
.ly_header_73 .default_cate_nav_style .cate_nav_list .cate_nav_box li{width:100%;box-sizing:border-box;}
.ly_header_73 .default_cate_nav_style .cate_nav_list .cate_nav_box li a{display:block;padding:6px 20px;font-size:14px;line-height:24px;}
.ly_header_73 .default_cate_nav_style .cate_nav_list .cate_nav_box li a:hover{text-decoration:none;}
.ly_header_73 .default_cate_nav_style .cate_nav_list .cate_nav_box li.has_sec a{padding-__right__:40px; text-align: __left__;}
.ly_header_73 .default_cate_nav_style .cate_nav_list .cate_nav_box li.has_sec{position:relative;}
.ly_header_73 .default_cate_nav_style .cate_nav_list .cate_nav_box li.has_sec::after{position:absolute;__right__:15px;top:50%;transform:translateY(-50%) rotate(270deg);content:"\e62b";font-family:"iconfont" !important;font-size:12px;font-style:normal;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale;font-weight:600;}
.ly_header_73 .default_cate_nav_style .cate_nav_list .cate_nav_box{height:100%;-webkit-box-sizing:border-box;box-sizing:border-box;overflow-y:scroll;overflow-x:hidden;}
.ly_header_73 .default_cate_nav_style .cate_nav_list .cate_nav_box{height: 100%;-webkit-box-sizing: border-box;box-sizing: border-box;overflow-y: scroll;overflow-x: hidden;}
.ly_header_73 .default_cate_nav_style .cate_nav_list .cate_nav_box::-webkit-scrollbar{width: 0px;background: #1f1f20;border-radius: 5px;}
.ly_header_73 .default_cate_nav_style .cate_nav_list .cate_nav_box::-webkit-scrollbar-thumb{background: rgba(255, 255, 255, 0.6);border-radius: 5px;}
.ly_header_73 .default_cate_nav_style .cate_nav_list .cate_nav_box::-webkit-scrollbar-thumb:hover{background: rgba(255, 255, 255, 0.4);}
.ly_header_73 .default_cate_nav_style  a:hover{text-decoration: none	;}
.ly_header_73 .default_cate_nav_style .cate_nav_list .cate_nav_sec .cate_nav_sec_box{display:block;font-size:0;}
.ly_header_73 .default_cate_nav_style .cate_nav_list .cate_nav_sec .cate_nav_sec_box .cate_nav_sec_item{display:inline-block;vertical-align:top;}
.ly_header_73 .default_cate_nav_style .cate_nav_title{position: relative;}
.ly_header_73 .default_cate_nav_style .cate_nav_title i{display:block;position:absolute;top:50%;transform:translateY(-50%);__right__:15px;font-size:14px;}
body.lang_ar .ly_header_73 .default_cate_nav_style .cate_nav_list .cate_nav_box li.has_sec::after{ transform: translateY(-50%) rotate(90deg); }

.ly_header_73 .ly_i_wrapper_1200{width: 100%;max-width: 1200px;margin:0 auto;}
@media screen and (max-width:1600px){ 
	.ly_header_73 .ly_i_wrapper_1200{width:92%;}
}
.ly_header_73 .default_account_style_mb{display: none;}
@media screen and (max-width:1000px){
	.ly_header_73 .ly_i_wrapper_1200{width:92vw;margin:0 auto;overflow:hidden;}
	.ly_header_73 .ly_header_b_left {padding: 10px 0;flex: 1;}
    .ly_header_73 .ly_header_b_left a img{vertical-align: middle;}
    #header.ly_header_73 .ly_header_top{display:none;}
	#header.ly_header_73 .ly_header_bot .ly_i_wrapper_1200{padding-top:10px;padding-bottom:10px;box-sizing:border-box;min-height:70px;}
	.ly_header_73 .ly_header_b_right{flex:0;}
	.ly_header_73 .ly_header_b_center{position:relative;width:40px;}
	.ly_header_73 .ajax_default_search a{display:block;width:40px;height:40px;}
	.ly_header_73 .default_search_style .default_search_form .form{position:static;border:0;}
	.ly_header_73 .default_search_style .default_search_form .form .text{display:none;}
	.ly_header_73 .default_search_style .default_search_form .form .button{width:40px;height:40px;}
	.ly_header_73 .default_search_style .default_search_form .form .button i{font-size:25px;}
	.ly_header_73 .default_search_style .default_search_form .form{border: 0;}
	.ly_header_73 .default_search_style{position: relative;}
	.ly_header_73 .default_search_style .global_search{display: block;width: 35px;height: 40px;position: absolute;top: 0;left: 0;right: 0;bottom: 0;z-index: 3;text-align: center;line-height: 40px;}
	.ly_header_73 .default_search_style .default_search_form .form .button i{font-size:24px;}
	.ly_header_73 .ly_nav{display:none;}
	.ly_header_73 .default_account_style{display: none;}
	.ly_header_73 .default_account_style_mb{display: block;}
	.ly_header_73 .default_account_style_mb i{font-size: 20px;}
	.ly_header_73 .default_shopping_cart_style .text_0,.default_shopping_cart_style .cart_count_price{display:none;}
	.ly_header_73 .ly_header_menu{display:inline-flex;}
	.ly_header_73 .ly_header_menu{display:inline-flex;padding-__left__:0;}
	.ly_header_73 .ly_header_menu i{font-size:22px;margin-right: 10px;}
	.ly_header_73 .default_shopping_cart_style .iconfont{font-size:0;}
	.ly_header_73 .default_shopping_cart_style .cart_count{font-size:12px;padding:0px 4px;}
	.ly_header_73 .ly_header_b_center{height:40px;width:35px;margin:0 0;flex:unset;}
	.ly_header_73 .default_shopping_cart_style{padding-__left__:0;margin:0 15px;width:auto;}
	.ly_header_73 .default_shopping_cart_style .iconfont{position:static;transform:translate(0);}
}

#header.ly_header_73 .ly_header_top{background-color:var(--ThemesBoardBgColor);}
#header.ly_header_73 .ly_header_top .ly_header_lctext{font-family:"HarmonyOSHans-Regular";color:var(--ThemesBoardColor);}
#header.ly_header_73 .ly_header_bot{background-color: #fff;}
#header.ly_header_73 .ly_nav{background-color: #fed925;}
.ly_header_73 .default_language_currency_style{font-family:"HarmonyOSHans-Regular";color:var(--ThemesBoardColor);}
.ly_header_73 .default_shopping_cart_style .text_0{color:var(--ThemesHeaderTextColor);}
.ly_header_73 .default_search_style .default_search_form .form{border-color:var(--ThemesHeaderInputBorderColor);}
.ly_header_73 .default_search_style .default_search_form .form .text{background-color:var(--ThemesHeaderInputBgColor);color:var(--ThemesHeaderTextColor);}
.ly_header_73 .default_search_style .default_search_form .form .button{background-color: #ffffff;}
.ly_header_73 .default_search_style .default_search_form .form .button i{color:var(--ThemesHeaderIconSearchColor);}
.ly_header_73 .default_account_style .iconfont{color: #333;}
.ly_header_73 .default_account_style dl dt a,.default_account_style dl dt::after{color:var(--ThemesHeaderTextColor);font-family:"OpenSans-Bold";}
.ly_header_73 .default_shopping_cart_style .iconfont{color: #333;}
.ly_header_73 .default_shopping_cart_style .cart_count{background-color: #fed925;color: #333;}
.ly_header_73 .default_shopping_cart_style .cart_count_price{color:var(--ThemesHeaderTipsColor);}
.ly_header_73 .default_cate_nav_style .cate_nav_title{background-color:var(--ThemesHeaderProductsCategoryColor);font-family:"OpenSans-Bold";}
.ly_header_73 .default_cate_nav_style .cate_nav_title span{color:#fff;}
.ly_header_73 .default_cate_nav_style .cate_nav_list{background-color:#fff;}
.ly_header_73 .cate_nav_list .cate_nav_box li a{color:#404852;}
.ly_header_73 .cate_nav_list .cate_nav_box li.has_sec::after{color:var(--ThemesNavTextColor);}
.ly_header_73 .default_nav_style li a{color:var(--ThemesNavTextColor);}
.ly_header_73 .default_nav_style li a:hover{color: #fff;}
.ly_header_73 .global_search{display: none;}
@media screen and (max-width:1000px){
	.ly_header_73 .default_shopping_cart_style{margin-right: 0;margin-left: 10px;}
	.ly_header_73 .default_search_style .default_search_form{display: none;}
    .ly_header_73 .default_search_style .default_search_form .form .button{background-color:transparent;}
    .ly_header_73 .default_search_style .default_search_form .form .button i{color:var(--ThemesHeaderIconColor);}
    .ly_header_73 .ly_header_menu i{color: #333;}
    .ly_header_73 .global_search{display: block;}
}
.ly_header_73 .default_cate_nav_style .cate_nav_title i{color:#fff;}
@media  screen and (min-width:1000px) {
    .ly_header_73 .default_cate_nav_style .cate_nav_list .cate_nav_box li:hover>a,
	.ly_header_73 .default_cate_nav_style .cate_nav_list .cate_nav_box li.hover>a{color:#1e71ce;}
    .ly_header_73 .default_cate_nav_style .cate_nav_list .cate_nav_box li:hover::after,
	.ly_header_73 .default_cate_nav_style .cate_nav_list .cate_nav_box li.hover::after{color:#1e71ce;}
    .ly_header_73 .default_cate_nav_style .cate_nav_list .cate_nav_sec .cate_nav_sec_box li a:hover{color:#1e71ce;}
}

.ly_header_73 .default_cate_nav_style .cate_nav_list .cate_nav_box{overflow-y: auto;}
.ly_header_73 .default_cate_nav_style .cate_nav_list .cate_nav_box::-webkit-scrollbar{width: 6px;background: #fff;border-radius: 3px;}
.ly_header_73 .default_cate_nav_style .cate_nav_list .cate_nav_box::-webkit-scrollbar-thumb{background: rgba(193, 193, 193, 0.8);border-radius: 3px;}
.ly_header_73 .default_cate_nav_style .cate_nav_list .cate_nav_box::-webkit-scrollbar-thumb:hover{background: rgba(168, 168, 168, 0.8);}

.ly_header_73 .ly_slide_pages .default_cate_nav_style .cate_nav_list{height: 0;box-shadow: 0 0 10px rgb(0 0 0 / 12%);}

.ly_header_73 .ly_nav_cate{display: none;}
.ly_header_73 .global_login_sec, .ly_header_73 .global_account_sec{width: 25px;height: 25px;font-size: 0;}
.ly_header_73 .account_box_sec .btn a{font-size: 14px;}
.ly_header_73 .header_favorite{position: relative;margin-left: 13px;}
.ly_header_73 .header_favorite:hover{text-decoration: none;}
.ly_header_73 .header_favorite i{font-size: 25px;}
.ly_header_73 .header_favorite em{position: absolute;top: -8px;right: -13px;padding: 1px 5px;font-size: 10px;border-radius: 10px;background: #fed925;}
