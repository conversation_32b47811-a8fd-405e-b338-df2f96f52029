.ly_products_4 .themes_box_title{display: inline-block;vertical-align: middle;font-size: 34px;}
.ly_products_4 .themes_box_title.sub_title{color: #fed925;margin-left: 5px;}
.ly_products_4 .wrap{-webkit-box-sizing:border-box; box-sizing:border-box; width:100%; padding:0 6.3%;}
.ly_products_4 .ovh{overflow:hidden;}
.ly_products_4.clearfix:after{clear:both;}
.ly_products_4.clearfix:before,.clearfix:after{content:""; display:table;}
.ly_products_4 .textH{overflow:hidden; white-space:nowrap; -o-text-overflow:ellipsis; text-overflow:ellipsis;}
.ly_products_4 .text_center{text-align:center;}
.ly_products_4 .bold{font-weight:bold;}
.ly_products_4 a:hover{text-decoration: none;}

.ly_products_4.ixPart2{-webkit-box-sizing:border-box; box-sizing:border-box;margin-top: 50px;}
.ly_products_4.ixPart2 .box{float: __left__; width: 225px;  margin:0; margin-__right__: 17px;}
.ly_products_4.ixPart2 .box .img{ position:relative; text-align:center;overflow:hidden; }
.ly_products_4.ixPart2 .box .img a{display: block;position: relative;height: 100%;font-size: 0;}
.ly_products_4.ixPart2 .box .img img:first-child{__left__:0;}
/*.ly_products_4.ixPart2 .box .img img:nth-child(2){__left__:-100%;}*/
/*.ly_products_4.ixPart2 .box .img:hover img:nth-child(2){__left__:0;z-index: 1; }*/
.ly_products_4.ixPart2 .box .star{width:100%;background:none;position:static;z-index:1;text-align:__left__;}
.ly_products_4.ixPart2 .box .star .review_star_l span{margin-__right__: 2px;}
.ly_products_4.ixPart2 .box .star img{display:inline-block; margin:0 1px;}
.ly_products_4.ixPart2 .box .icon_seckill{display:none; padding:3px 10px; position:absolute; top:10px; __left__:0; font-size:14px; z-index: 1;}
.ly_products_4.ixPart2 .box .text{position: relative;font-size:16px; padding:12px 0 0;}
.ly_products_4.ixPart2 .box .text .t1{margin-top: 6px;}
.ly_products_4.ixPart2 .box .text .t1 a{text-decoration:none;}
.ly_products_4.ixPart2 .box .text .t1 span{display: -webkit-box;text-overflow:ellipsis;overflow:hidden;-webkit-line-clamp: 2;-webkit-box-orient: vertical;width: 100%;}
.ly_products_4.ixPart2 .box .text .t1 em{width:24%;text-align:__right__;}
.ly_products_4.ixPart2 .box .text .t2{font-size:16px; padding-top:10px;text-align: __left__;}
.ly_products_4.ixPart2 .box .text .t2 .themes_products_price{display: inline-block;margin-top: 3px;}
.ly_products_4.ixPart2 .box .text .t2 .themes_price{margin-__right__:15px;}
.ly_products_4.ixPart2 .box .text .t2 .price_data{font-style:normal;}
.ly_products_4.ixPart2 .box .text .t2 em{}
.t092_slide .srcoll_btn_prev{background-image: url(/static/ico/web/index_lfet_icon.png);}
.t092_slide .srcoll_btn_next{background-image: url(/static/ico/web/index_right_icon.png);}
.ly_products_4.ixPart2 .srcoll_btn_prev,
.ly_products_4.ixPart2 .srcoll_btn_next{width:11px; height:19px; position:absolute; top: 100px; cursor:pointer;opacity: 1;}
.ly_products_4.ixPart2 .srcoll_btn_prev{left: 0%;}
.ly_products_4.ixPart2 .srcoll_btn_next{right: 0%;}
.ly_products_4.ixPart2 .srcoll_btn_prev.none,
.ly_products_4.ixPart2 .srcoll_btn_next.none{opacity: 0;visibility: hidden;}

.ly_products_4 .sellersSlide{-webkit-box-sizing:border-box; box-sizing:border-box; margin:20px auto 40px; padding:0 40px; position:relative;overflow-x:auto;width:100%;max-width: 1280px;}
.ly_products_4 .sellersSlide .box{-webkit-box-sizing:border-box; box-sizing:border-box;}
.ly_products_4.ixPart2 .box .img img{transition: all .3s ease-in-out;}

.ly_products_4 .ly_pro_addcart{position:absolute;__right__:0;bottom:0;transition:all .3s ease-in-out;}
.ly_products_4 .ly_pro_addcart{background-color: #fed925;background-image: url(/static/ico/web/cart_icon.png);background-repeat: no-repeat;background-position: center center;}
.ly_products_4 .ly_pro_addcart{display:inline-flex;align-items:center;justify-content:center;width:55px;height:42px;border-radius: 20px;}
.ly_products_4 .ly_pro_addcart i{font-size:17px;}
.ly_products_4 .mobShow{display:none;}
.ly_products_4 .mobHide{display:block;}

.ly_products_4 .ixTit i{display:inline-block; width:108px; height:5px;}
.ly_products_4.ixPart2 .srcoll_btn_prev,
.ly_products_4.ixPart2 .srcoll_btn_next{background-repeat:no-repeat; background-size:contain;}
/*.ly_products_4.ixPart2 .srcoll_btn_prev{transform:rotate(180deg);-webkit-transform:rotate(180deg);-moz-transform:rotate(180deg);-ms-transform:rotate(180deg);-o-transform:rotate(180deg);}*/
.ly_products_4.ixPart2 .box .img{padding: 1px;background: #ffffff;border-radius: 8px;overflow: hidden;transition: all .3s ease-in-out;}
.ly_products_4.ixPart2 .box .img a{background: #e5e5e5;padding: 1px;transition: all .3s ease-in-out;}
.ly_products_4.ixPart2 .box .img a .compute_item_img{ overflow: hidden; }
.ly_products_4.ixPart2 .box .img a .compute_item_img img{}
.ly_products_4.ixPart2 .box .img:hover{background: #333333;}
.ly_products_4.ixPart2 .box .img:hover a{background: #333333;}

.ly_products_4.ixPart2 .box .img img{-webkit-transition: all .5s;-o-transition: all .5s;transition: all .5s;}
.ly_products_4.ixPart2 .box .img:hover img{-webkit-transform: scale(1.05);-ms-transform: scale(1.05);transform: scale(1.05);}
.ly_products_4.ixPart2 .box .img a .compute_item_img{}
.ly_products_4.ixPart2 .box .img a .compute_item_img .compute_process_img{border-radius: 4px;}
.ly_products_4.ixPart2 .box .img a:hover .compute_item_img, .ly_products_4.ixPart2 .box .img a:hover .compute_item_img .compute_process_img{}
@media screen and (max-width: 1800px) {
	.ly_products_4.ixPart2 .box {width:16.05208vw; margin-__right__: 1.6vw;}
}
@media screen and (max-width:1366px) {
    .ly_products_4 .wrap{padding:0 40px;}
}

@media screen and (max-width:1280px) {
    .ly_products_4 .wrap{padding:0 20px;}
}

@media screen and (max-width:1024px) {
    .ly_products_4 .wrap{padding:0 10px;}
}

@media screen and (max-width:1000px) {
    .ly_products_4 .mobShow{display:block;}
    .ly_products_4 .mobHide{display:none;}
}

@media screen and (max-width:500px){
    .ly_products_4 .wrap{padding:0;}
}

@media  screen and (max-width:1000px) {
    .ly_products_4 .ly_pro_addcart{width:28px;height:28px;}
    .ly_products_4 .ly_pro_addcart i{font-size:14px;margin-top:2px;}
}

@media screen and (max-width:1000px){
     .ly_products_4.ixPart2 .box .img .compute_item_img:nth-child(2){ display: none;  }
    .ly_products_4.ixPart2 .box .img:hover img:nth-child(2){z-index:-1;}
    .ly_products_4.ixPart2 {overflow-x:srcoll;}
    .ly_products_4.ixPart2 .box{width:28vw; float:__left__; margin-__right__:calc((16vw - 60px) / 2); padding:0;position: relative;}
    .ly_products_4.ixPart2 .box .t1{font-size:20px; line-height:30px; height:60px; overflow:hidden;}
    .ly_products_4.ixPart2 .box .text .t2{font-size:24px;}
    .ly_products_4.ixPart2 .box .t3{font-size:26px; margin-top:10px;}
    .ly_products_4.ixPart2 .ixTit span{margin-bottom: 10px;}
    .ly_products_4.ixPart2 .sellersSlide {margin:18px auto 0; padding:0 30px;}
    .ly_products_4.ixPart2 .srcoll_btn_prev,.ly_products_4.ixPart2 .srcoll_btn_next{display:none;}
    .ly_products_4.ixPart2 .box .text{padding-top: 12px;}
}

@media screen and (max-width:750px){
    .ly_products_4.ixPart2 .box .t1{font-size:16px;}
    .ly_products_4.ixPart2 .box .text .t2{font-size:18px;}
    .ly_products_4.ixPart2 .box .star{bottom:5px;}
    .ly_products_4.ixPart2 .box .text{padding-top: 8px;}
}


@media screen and (max-width:750px){
    .ly_products_4 .sellersSlide{display: flex;align-items: flex-start;justify-content: space-between;flex-wrap: wrap;}
    .ly_products_4 .sellersSlide .box{width: calc( ( ( 100% + 15px ) / 2 ) - 15px );margin-right: 15px;margin-bottom: 15px;}
    .ly_products_4 .sellersSlide .box .img{ text-align:center;}
    .ly_products_4.ixPart2 .sellersSlide {padding:0 15px;}
	.ly_products_4.ixPart2 .box
    .ly_products_4.ixPart2 .box .text{padding:5px 0;}
	.ly_products_4.ixPart2 .box .text .t2{font-size:14px;}
	.ly_products_4.ixPart2 .box .text .t2 .themes_price{margin-__right__:5px;}
    .ly_products_4.ixPart2 .box .t1{height:40px; line-height:20px; font-size:14px;}
	.ly_products_4.ixPart2 .box .text .t1 span{width:auto; -o-text-overflow:inherit; text-overflow:inherit; white-space:inherit;}
	.ly_products_4.ixPart2 .box .text .t1 em{width:auto; line-height:1; border:2px #52b8e0 solid; padding:3px 5px; position:absolute; top:10px; left:0;}
    .ly_products_4.ixPart2 .box .t3{font-size:18px;}
}


.ly_products_4 .topCon .box{width:20%;}
.ly_products_4 .topCon .box{-webkit-box-sizing:border-box; box-sizing:border-box; margin:0 0 40px 0;}
.ly_products_4 .topCon .more a{display:inline-block; font-size:14px; line-height:48px; padding:0 30px;}
@media screen and (max-width:1000px){
    .ly_products_4 .topCon .box{width:46%; margin:2%; padding:0;}
    .ly_products_4 .topCon .more a{font-size:30px; line-height:68px; padding:0 60px;}
}
@media screen and (max-width:500px){
    .ly_products_4 .topCon .box{width:42%; margin:0 0 4% 5%;}
    .ly_products_4 .topCon .more a{ font-size:16px; line-height:40px; padding:0 30px;margin-top: 20px;}
    .ly_products_4 .ixTit i{width:60px;}
    .ly_products_4 .mobPart1{padding:15px 15px 15px 15px;}
    .ly_products_4 .mobPart1 .img img{margin-bottom:15px;}
}


@-webkit-keyframes iconMove{
    0%{-webkit-transform:translateY(-20px);transform:translateY(-20px); opacity:0;}
    50%{-webkit-transform:translateY(-10px);transform:translateY(-10px); opacity:.5;}
    100%{-webkit-transform:translateY(0px);transform:translateY(0px); opacity:1;}
}
@keyframes iconMove{
    0%{-webkit-transform:translateY(-20px);transform:translateY(-20px); opacity:0;}
    50%{-webkit-transform:translateY(-10px);transform:translateY(-10px); opacity:.5;}
    100%{-webkit-transform:translateY(0px);transform:translateY(0px); opacity:1;}
}

/********************************* skin_demo.css *****************************/
.ly_products_4.ixPart2 .box .icon_seckill{border:2px #52b8e0 solid;}
.ly_products_4.ixPart2 .box .text .t1 span>a:hover{color:#000;}
.ly_products_4.ixPart2 .box .text .t1 em{color:#52b8e0;}
.ly_products_4.ixPart2 .box_visual_srcoll{position:relative;}
.ly_products_4 .ixTit i{background:#64bbb2;}
@media screen and (max-width:1000px){
    .ly_products_4.ixPart2 .box .img img:first-child{ display: block; }
    .ly_products_4.ixPart2 .box .img img:nth-child(2){ display: none; }
    .ly_products_4.ixPart2 .box .t3{color:#52B8E0;}
}

.ly_products_4 .ly_pro_addcart i:before{content: unset;}

.ly_products_4 .review_star .star_0:after, .ly_products_4 .review_star .half_star:after{top: 2.5px;}

@media screen and (max-width:768px){
    .ly_products_4 .themes_box_title{font-size: 20px;}
    .ly_products_4 .bold{font-weight: normal;}
    .ly_products_4.ixPart2{margin-top: 25px;}
    .ly_products_4.ixPart2 .ixTit span{margin-bottom: 0;}
    .ly_products_4 .srcoll_list{margin-bottom: 25px;}
    .ly_products_4.ixPart2 .box{margin-bottom: 15px;}
    .ly_products_4.ixPart2 .box:nth-child(2n){margin-right: 0;}
    .ly_products_4.ixPart2 .box .text .t1{margin-top: 0;}
    .ly_products_4 .review_star .star_0:after, .ly_products_4 .review_star .half_star:after{top: 2.5px;}
    .ly_products_4 .ly_pro_addcart{width: 50px;}
    .ly_products_4 .ly_pro_addcart i{font-size: 12px;}
    .ly_products_4 .review_star_l span{font-size: 18px;}
    .ly_products_4 .review_star .star_0:after{font-size: 14px;}
}

.ly_products_4.ixPart2 .box .products_countdown_box .ly_countdown_box{display: flex;align-items: flex-start;justify-content: flex-start;margin-top: 12px;}
.ly_products_4.ixPart2 .box .products_countdown_box .ly_countdown_box .time_span{width: 38px;margin-left: 10px;}
.ly_products_4.ixPart2 .box .products_countdown_box .ly_countdown_box .time_span:first-child{margin-left: 0;}
.ly_products_4.ixPart2 .box .products_countdown_box .ly_countdown_box .time_span .num{display: inline-block;width: 38px;height: 38px;line-height: 38px;font-size: 16px;color: #333333;text-align: center;background: #fffadd;border-radius: 4px;font-family: "Montserrat-Bold";}
.ly_products_4.ixPart2 .box .products_countdown_box .ly_countdown_box .time_span .word{display: inline-block;width: 38px;margin-top: 3px;text-align: center;font-size: 12px;color: #666666;font-style: italic;}
.ly_products_4.ixPart2 .box .products_countdown_box .ly_countdown_box i{font-size: 16px;color: #666666;margin-top: 10px;margin-left: 9px;}

@media(max-width: 1000px){
    .ly_products_4.ixPart2 .box .products_countdown_box .ly_countdown_box .time_span{width: 30px;margin-left: 4px;}
    .ly_products_4.ixPart2 .box .products_countdown_box .ly_countdown_box .time_span .num{width: 30px;height: 30px;line-height: 30px;font-size: 14px;}
    .ly_products_4.ixPart2 .box .products_countdown_box .ly_countdown_box .time_span .word{width: 30px;font-size: 12px;}
    .ly_products_4.ixPart2 .box .products_countdown_box .ly_countdown_box i{margin-left: 4px;}
}