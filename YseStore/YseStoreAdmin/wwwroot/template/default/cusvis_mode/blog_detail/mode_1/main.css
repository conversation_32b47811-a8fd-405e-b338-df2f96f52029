.ly_blog_detail_1 { padding: 40px 0; }
.ly_blog_detail_1 .container_screen{max-width: 1200px;}
.ly_blog_detail_1 .blog_wrapper { display: flex; justify-content: space-between; margin: 0 auto; }
.ly_blog_detail_1 .left_wrapper { width: 71.53792%; }
.ly_blog_detail_1 .left_wrapper.full_width{ width: 100%; }
.ly_blog_detail_1 .right_wrapper { width: 25.67849%; }

.ly_blog_detail_1 .blog_detail .blog_title { font-size: 36px; line-height: 42px; }
.ly_blog_detail_1 .blog_detail .blog_param { margin-top: 15px; font-size: 16px; color: #5a6272; line-height: 26px; }
.ly_blog_detail_1 .blog_detail .blog_param span{ display: inline-block; }
.ly_blog_detail_1 .blog_detail .blog_param>span::before { content: "|"; display: inline-block; vertical-align: top; margin: 0 20px; }
.ly_blog_detail_1 .blog_detail .blog_param>span:first-child::before { content: ""; margin: 0; }
.ly_blog_detail_1 .blog_detail .blog_brief{padding-top:25px;font-size: 16px;}
.ly_blog_detail_1 .blog_detail .blog_img {
-webkit-border-radius: 20px;
-moz-border-radius: 20px;
-ms-border-radius: 20px;
-o-border-radius: 20px;
border-radius: 20px;
overflow: hidden;
padding-top: 25px;
}
.ly_blog_detail_1 .blog_detail .blog_content { margin: 30px 0; }
.ly_blog_detail_1 .blog_detail .blog_share { margin-top: 30px; }
.ly_blog_detail_1 .blog_detail .blog_other { display: flex; justify-content: space-between; margin: 50px 0; }
.ly_blog_detail_1 .blog_detail .blog_other .blog_item {
position: relative;
-webkit-border-radius: 20px;
-moz-border-radius: 20px;
-ms-border-radius: 20px;
-o-border-radius: 20px;
border-radius: 20px;
overflow: hidden;
width: calc(50% - 20px);
box-shadow: rgba(0, 0, 0, .2) 6px 6px 18px;
}
.ly_blog_detail_1 .blog_detail .blog_other .blog_item .item_img { text-align: center; }
.ly_blog_detail_1 .blog_detail .blog_other .blog_item .item_text { padding: 9px 29px 32px; }
.ly_blog_detail_1 .blog_detail .blog_other .blog_item .item_text .target {
display: inline-block;
vertical-align: top;
-webkit-border-radius: 5px;
-moz-border-radius: 5px;
-ms-border-radius: 5px;
-o-border-radius: 5px;
border-radius: 5px;
margin-top: 20px;
padding: 0 20px;
font-size: 14px;
color: #000;
line-height: 35px;
background-color: #fecf00;
}
.ly_blog_detail_1 .blog_detail .blog_other .blog_item .item_text .title { margin-top: 20px; font-size: 18px; color: #000; line-height: 34px; }
.ly_blog_detail_1 .blog_detail .blog_other .blog_item .item_link {
position: absolute;
top: 0;
left: 0;
display: block;
-webkit-border-radius: 20px;
-moz-border-radius: 20px;
-ms-border-radius: 20px;
-o-border-radius: 20px;
border-radius: 20px;
overflow: hidden;
width: 100%;
height: 100%;
font-size: 0;
}

.ly_blog_detail_1 #turn_page { margin-top: 50px; }
.ly_blog_detail_1 #turn_page li a,
.ly_blog_detail_1 #turn_page li span { font-size: 20px; color: #727272; }
.ly_blog_detail_1 #turn_page li,
.ly_blog_detail_1 #turn_page li a:hover { background-color: transparent; }
.ly_blog_detail_1 #turn_page li.active a { color: #000; }
.ly_blog_detail_1 #turn_page li.first,
.ly_blog_detail_1 #turn_page li.last { display: none; }
.ly_blog_detail_1 #turn_page .prev span,
.ly_blog_detail_1 #turn_page .prev a { position: relative; display: inline-block; vertical-align: top; width: 24px; font-size: 0; }
.ly_blog_detail_1 #turn_page .prev span::before,
.ly_blog_detail_1 #turn_page .prev span::after,
.ly_blog_detail_1 #turn_page .prev a::before,
.ly_blog_detail_1 #turn_page .prev a::after { content: '\e63c'; position: absolute; top: 0; left: 0; font-family: 'iconfont'; font-size: 14px; color: #727272; }
.ly_blog_detail_1 #turn_page .prev span::after,
.ly_blog_detail_1 #turn_page .prev a::after { left: 7px; }
.ly_blog_detail_1 #turn_page .next span,
.ly_blog_detail_1 #turn_page .next a { position: relative; display: inline-block; vertical-align: top; width: 24px; font-size: 0; }
.ly_blog_detail_1 #turn_page .next span::before,
.ly_blog_detail_1 #turn_page .next span::after,
.ly_blog_detail_1 #turn_page .next a::before,
.ly_blog_detail_1 #turn_page .next a::after { content: '\e641'; position: absolute; top: 0; left: 0; font-family: 'iconfont'; font-size: 14px; color: #727272; }
.ly_blog_detail_1 #turn_page .next span::after,
.ly_blog_detail_1 #turn_page .next a::after { left: 7px; }

@media screen and (max-width: 1000px) {
.ly_blog_detail_1 { padding: 40px 0; }
.ly_blog_detail_1 .blog_wrapper { flex-direction: column; }
.ly_blog_detail_1 .left_wrapper,
.ly_blog_detail_1 .right_wrapper { width: 100%; }

.ly_blog_detail_1 .blog_detail .blog_title { font-size: 18px; line-height: 34px; }
.ly_blog_detail_1 .blog_detail .blog_param { margin-top: 8px; font-size: 12px; line-height: 22px; }
.ly_blog_detail_1 .blog_detail .blog_img {
	-webkit-border-radius: 10px;
	-moz-border-radius: 10px;
	-ms-border-radius: 10px;
	 -o-border-radius: 10px;
		border-radius: 10px;
	margin-top: 15px;
}
.ly_blog_detail_1 .blog_detail .blog_other { flex-direction: column; margin: 20px 0 40px; }
.ly_blog_detail_1 .blog_detail .blog_other .blog_item {
	-webkit-border-radius: 10px;
	-moz-border-radius: 10px;
	-ms-border-radius: 10px;
	 -o-border-radius: 10px;
		border-radius: 10px;
	width: 100%;
	margin-top: 18px;
}
.ly_blog_detail_1 .blog_detail .blog_other .blog_item .item_text { padding: 6px 16px 21px; }
.ly_blog_detail_1 .blog_detail .blog_other .blog_item .item_text .target {
	-webkit-border-radius: 5px;
	-moz-border-radius: 5px;
	-ms-border-radius: 5px;
	 -o-border-radius: 5px;
		border-radius: 5px;
	margin-top: 11px;
	padding: 0 15px;
	font-size: 12px;
	line-height: 30px;
}
.ly_blog_detail_1 .blog_detail .blog_other .blog_item .item_text .title { margin-top: 11px; font-size: 16px; line-height: 26px; }
.ly_blog_detail_1 .blog_detail .blog_other .blog_item .item_link {
	-webkit-border-radius: 10px;
	-moz-border-radius: 10px;
	-ms-border-radius: 10px;
	 -o-border-radius: 10px;
		border-radius: 10px;
}

.ly_blog_detail_1 #turn_page { margin: 35px 0; }
}

.ly_blog_detail_1 .module_item {margin-top: 30px;background-color: #fff;}
.ly_blog_detail_1 .module_item .item_title { line-height: normal; margin-bottom: 16px; font-size: 20px; color: #333333; }
.ly_blog_detail_1 .module_item .item_list { line-height: 28px; font-size: 14px; color: #5a6272; }
.ly_blog_detail_1 .module_item .item_list>li { margin-top: 12px; }
.ly_blog_detail_1 .module_item .item_list>li>a { color: #333333; font-size: 14px;}
.ly_blog_detail_1 .module_item .item_list>li>a:hover{ color: #034d92; }
.ly_blog_detail_1 .module_item:first-child { margin-top: 0; }
.ly_blog_detail_1 .module_item.cate_module_item .item_list>li{margin-top: 8px;}
.ly_blog_detail_1 .module_item.popular_module_item{}
.ly_blog_detail_1 .module_item.popular_module_item .item_list{}
.ly_blog_detail_1 .module_item.popular_module_item .item_list li{display: inline-block;vertical-align: middle;margin: 0 6px 14px 0;border-radius: 8px;padding: 3px 20px;border: 1px solid #e5e5e5;}
.ly_blog_detail_1 .module_item.popular_module_item .item_list li:hover{background: #fed925;border-color: #fed925;color: #333333;}
.ly_blog_detail_1 .module_item.popular_module_item .item_list li a{color: #034d92;}
.ly_blog_detail_1 .module_item.popular_module_item .item_list li:hover a{color: #333333;text-decoration: none;}
.ly_blog_detail_1 .module_item.banner_module_item .banner_box{margin-bottom: 25px;}
.ly_blog_detail_1 .module_item.banner_module_item .banner_desc .banner_title{font-size: 20px;margin-bottom: 10px;}
.ly_blog_detail_1 .module_item.banner_module_item .banner_desc .banner_brief{font-size: 14px;line-height: 22px;}

.ly_blog_detail_1 .products_module_item{}
.ly_blog_detail_1 .products_module_item .products_list{}
.ly_blog_detail_1 .products_module_item .products_list .list{display: flex;flex-wrap: wrap;align-items: flex-start;justify-content: space-between;margin-bottom: 15px;}
.ly_blog_detail_1 .products_module_item .products_list .list .img_box{width: 74px;height: 93px;border-radius: 5px;border: 1px solid #e5e5e5;overflow: hidden;}
.ly_blog_detail_1 .products_module_item .products_list .list .img_box img{display: inline-block;vertical-align: middle;max-width: 100%;max-height: 100%;-webkit-transition: all .5s;-o-transition: all .5s;transition: all .5s;}
.ly_blog_detail_1 .products_module_item .products_list .list .img_box:hover img{-webkit-transform: scale(1.05);-ms-transform: scale(1.05);transform: scale(1.05);}
.ly_blog_detail_1 .products_module_item .products_list .list .img_box span{display: inline-block;vertical-align: middle;height: 100%;}
.ly_blog_detail_1 .products_module_item .products_list .list .desc_box{width: calc( 100% - 90px );}
.ly_blog_detail_1 .products_module_item .products_list .list .name{margin-bottom: 5px;}
.ly_blog_detail_1 .products_module_item .products_list .list .name a{font-size: 14px;color: #333333;line-height: normal;}
.ly_blog_detail_1 .products_module_item .products_list .list .price_box{}
.ly_blog_detail_1 .products_module_item .products_list .list .star_box{}

.ly_blog_detail_1 .search_module_item{}
.ly_blog_detail_1 .search_module_item .search_box{position: relative;margin: 15px 0;}
.ly_blog_detail_1 .search_module_item .search_box input{width: calc( 100% - 32px );height: 36px;line-height: 36px;padding: 0 15px;border: 1px solid #e5e5e5;border-radius: 4px;}
.ly_blog_detail_1 .search_module_item .search_box button{position: absolute;top: 0;right: 0;width: 38px;height: 38px;border: none;background: url(/static/ico/web/blog_search_icon.png) no-repeat center center / 14px 14px;}

@media(max-width: 1520px){
	.ly_blog_detail_1 .container_screen{max-width: 100%;width: 100%;}
}
@media(max-width: 1240px){
	.ly_blog_detail_1{max-width: 92%;margin: 0 auto;}
	.ly_blog_detail_1 .container_screen{margin-top: 20px;}
	.ly_blog_detail_1 .left_wrapper{width: calc( 100% - 330px );}
	.ly_blog_detail_1 .right_wrapper{width: 300px;}
	.ly_blog_detail_1 .blog_list .blog_item .pic_box{width: 320px;}
	.ly_blog_detail_1 .blog_list .blog_item .item_text{width: calc( 100% - 350px );}
}
@media(max-width: 1100px){
	.ly_blog_detail_1 .blog_list .blog_item .pic_box{width: 100%;margin-bottom: 15px;}
	.ly_blog_detail_1 .blog_list .blog_item .item_text{width: 100%;}
}

@media screen and (max-width: 1000px) {
	.ly_blog_detail_1{padding: 0;}
	.ly_blog_detail_1 .blog_title{font-size: 24px;}
	.ly_blog_detail_1 .blog_wrapper { flex-direction: column; }
	.ly_blog_detail_1 .left_wrapper,
	.ly_blog_detail_1 .right_wrapper { width: 100%; }
	.ly_blog_detail_1 .right_wrapper { margin: 25px 0; }
	.ly_blog_detail_1 .blog_list .blog_item{padding-bottom: 15px;}
	.ly_blog_detail_1 #turn_page { margin: 15px 0; }
	.ly_blog_detail_1 .left_wrapper{width: 100%;}
	.ly_blog_detail_1 .module_item{margin-top: 15px;}
}