$(function() {
	if ($('.ly_poster_246 .ly_index_countdown_box').size()) {
		let _this = $('.ly_poster_246 .ly_index_countdown_box');
		_this.genTimer({
			beginTime: ueeshop_config.date,
			targetTime: _this.attr("data-endTime"),
			type:'day',
			day_label:'d',
			days_label:':',
			day_unitWord: 'd',
			callbackOnlyDatas: 1,
			callback: function(e){
				let html = '';
				if (e.dates > 0) {
					html += '<span class="time_span"><span class="num">'+e.dates+'</span><span class="word">Days</span></span><i>:</i>';
				}
				html += '<span class="time_span"><span class="num">'+e.hours+'</span><span class="word">Hours</span></span><i>:</i><span class="time_span"><span class="num">'+e.minutes+'</span><span class="word">Minutes</span></span><i>:</i><span class="time_span"><span class="num">'+e.seconds+'</span><span class="word">Seconds</span></span>';
				this.html(html)
			}
		});
	}
});