.ly_poster_246{ margin: 45px auto 55px; overflow: hidden; }
.ly_poster_246 .poster_box{ position: relative; }
.ly_poster_246 .poster_box:before{ content:''; width: 100%; height: 100%; position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); z-index: 1; }
.ly_poster_246 .poster_box .text_box{ width: 50%; position: absolute; }
.ly_poster_246 .text_box{ z-index: 2; }
.ly_poster_246 .text_box .button{ margin-top: 45px; display: inline-block; padding: 0 60px; line-height: 60px; font-size: 18px; box-sizing: border-box; text-decoration: none;background: #fed925;border-radius: 8px;color: #333333;}
.ly_poster_246 .text_box .poster_title,
.ly_poster_246 .text_box .poster_subtitle{ line-height: 1; }
.ly_poster_246 .text_box .poster_content{ line-height: 1.2; }
@media screen and (max-width: 1000px) {
.ly_poster_246 .text_box .poster_content{ margin-top: 12px; }   
.ly_poster_246 .text_box .button{ margin-top: 12px; padding: 0 25px; line-height: 40px; font-size: 16px; }
}

.ly_poster_246 .poster_box .text_box{width: 495px;top: 50%;right: 12.5%;left: unset;}
.ly_poster_246 .poster_box .ly_countdown_content_box{display: flex;align-items: flex-start;justify-content: flex-start;}
.ly_poster_246 .poster_box .ly_countdown_content_box .ly_countdown_title{display: flex;align-items: flex-start;justify-content: center;flex-wrap: wrap;width: 135px;margin-top: 8px;}
.ly_poster_246 .poster_box .ly_countdown_content_box .ly_countdown_title span{display: inline-block;width: 100%;}
.ly_poster_246 .poster_box .ly_countdown_content_box .ly_countdown_title span.s1{font-size: 24px;color: #333333;font-family: "Montserrat-Bold";font-style: italic;}
.ly_poster_246 .poster_box .ly_countdown_content_box .ly_countdown_title span.s2{font-size: 24px;color: #666666;font-style: italic;}
.ly_poster_246 .poster_box .ly_countdown_content_box .ly_index_countdown_box{display: flex;align-items: flex-start;justify-content: flex-start;}
.ly_poster_246 .poster_box .ly_countdown_content_box .ly_index_countdown_box .time_span{margin-left: 10px;}
.ly_poster_246 .poster_box .ly_countdown_content_box .ly_index_countdown_box .time_span .num{display: inline-block;width: 65px;height: 65px;line-height: 65px;font-size: 24px;color: #333333;text-align: center;background: #fffadd;border-radius: 4px;font-family: "Montserrat-Bold";}
.ly_poster_246 .poster_box .ly_countdown_content_box .ly_index_countdown_box .time_span .word{display: inline-block;width: 65px;margin-top: 7px;text-align: center;font-size: 16px;color: #666666;}
.ly_poster_246 .poster_box .ly_countdown_content_box .ly_index_countdown_box i{font-size: 18px;color: #666666;margin-top: 18px;}

@media(max-width: 1500px){
	.ly_poster_246 .poster_box .text_box{right: 4%;}
	.ly_poster_246 .poster_box .ly_countdown_content_box .ly_countdown_title{width: 85px;}
	.ly_poster_246 .poster_box .ly_countdown_content_box .ly_countdown_title span.s1{font-size: 18px;}
	.ly_poster_246 .poster_box .ly_countdown_content_box .ly_countdown_title span.s2{font-size: 18px}
	.ly_poster_246 .poster_box .ly_countdown_content_box .ly_index_countdown_box .time_span{display: flex;flex-wrap: wrap;width: 40px;}
	.ly_poster_246 .poster_box .ly_countdown_content_box .ly_index_countdown_box .time_span .num{width: 40px;height: 40px;line-height: 40px;font-size: 18px;}
	.ly_poster_246 .poster_box .ly_countdown_content_box .ly_index_countdown_box .time_span .word{width: 40px;font-size: 14px;}
}
@media(max-width: 1000px){
	.ly_poster_246 .poster_box .ly_countdown_content_box{justify-content: flex-end;}
	.ly_poster_246 .poster_box .ly_countdown_content_box .ly_index_countdown_box .time_span .num{width: 30px;height: 30px;line-height: 30px;font-size: 14px;}
	.ly_poster_246 .poster_box .ly_countdown_content_box .ly_index_countdown_box .time_span .word{font-size: 12px;margin-top: 2px;}
	.ly_poster_246 .poster_box .text_box{width: auto;}
	.ly_poster_246 .text_box .button{height: 30px;line-height: 30px;margin-top: 5px;}
	.ly_poster_246 .poster_box .ly_countdown_content_box .ly_index_countdown_box i{margin-top: 2px;}
}