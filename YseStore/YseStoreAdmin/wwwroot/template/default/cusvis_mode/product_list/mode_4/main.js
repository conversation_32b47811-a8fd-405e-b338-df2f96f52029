function _show_products_list_attribute(){
    let parentObj = $('.default_products_list4');
    if ($('#plugins_iframe_themes').size()) {
        parentObj = $('#plugins_iframe_themes').contents().find('.default_products_list4');
    }
    if(parentObj.find('.item_attr').size()){
        let ProAry = new Array;
        parentObj.find('.item_attr').each(function($k,$v){
            let ProId = $(this).attr('data');
            ProAry.push(ProId);
        })
        let _request_prod_attribute = function(ProAry){
            if(!ProAry.length){ return true; }
            parentObj.find('.item_attr .item_attr_list').html('').removeClass('posted').siblings('.ext_list').find('.list').html('');
            $.post('/ajax/request_prod_attr.html', {'ProAry': ProAry}, function(result) {
                let msg = result.msg;
                if (result.ret == 1) {
                    for ($i = 0; $i < ProAry.length; $i++) {
                        let obj = msg[ProAry[$i]];
                        if (!obj || obj == undefined) continue;
                        let type = obj['Type'],
                            proid = ProAry[$i],
                            htmlObj = parentObj.find('.item_attr[data=' + proid + ']').find('.item_attr_list'),
                            width = htmlObj.width(),
                            widthOffsetLeft = htmlObj.offset().left + width,
                            top = htmlObj.offset().top,
                            computeRatio = obj['computeRatio'];
                        if (obj['Main'] == undefined || obj['Pic'] == undefined) continue;
                        for (var key in obj['Main']) {
                            Html = '';
                            let Main = obj['Main'][key];
                            let Pic = obj['Pic'] ? (obj['Pic'][key] || '') : '';
                            let PicId = obj['PicId'] ? (obj['PicId'][key] || '') : 0;
                            
                            if (type == 'color') {
                                Html += '<div class="attr_item_select color_item"><div class="check_box"><span style="background-color:' + Main + '"></span><input type="hidden" class="picture_input" name="picture[' + PicId + ']" value="' + Pic + '" /><input type="hidden" class="picture_computeRatio" name="computeratio[' + PicId + ']" value="' + computeRatio + '" /></div></div>';
                            } else if (type == 'picture') {
                                Html += '<div class="attr_item_select picture_item"><div class="check_box"><span><img src="' + Main + '" /><input type="hidden" name="picture[' + PicId + ']" class="picture_input" value="' + Pic + '" /><input type="hidden" class="picture_computeRatio" name="computeratio[' + PicId + ']" value="' + computeRatio + '" /></span></div></div>';	
                            } else {
                                Html += '<div class="attr_item_select text_item"><div class="check_box">' + Main + '<input type="hidden" name="picture[' + PicId + ']" class="picture_input" value="' + Pic + '" /><input type="hidden" class="picture_computeRatio" name="computeratio[' + PicId + ']" value="' + computeRatio + '" /></div></div>';	
                            }
                            if (!htmlObj.hasClass('posted')) {
								htmlObj.append(Html);
							}
                            let lastObj = htmlObj.find('.attr_item_select:last-child'),
                                lastObjOffset = widthOffsetLeft - htmlObj.find('.attr_item_select:last-child').offset().left;
                            //处理超过第一行 或 最后一个div距离父级最右侧的距离
                            if ((htmlObj.parent().find('.attr_item_select:last-child').offset().top > top  || lastObjOffset < 27)) {
                                if (!htmlObj.parent().find('.ext_list').length) {
                                    htmlObj.parent().append('<div class="ext_list"><div class="ext_title"></div><div class="list"></div></div>');
                                    lastObj.remove();
                                    htmlObj.parent().find('.ext_list').find('.list').append(Html);
                                } else {
                                    lastObj.remove();
                                    htmlObj.parent().find('.ext_list').find('.list').append(Html);
                                }
                            }								
                        }
                        //计算数量
                        let ExtObj = htmlObj.parent().find('.ext_list'),
                            ExtTotalLength = ExtObj.find('.attr_item_select').length,
                            itemObjWidth = ExtObj.find('.attr_item_select').innerWidth(),
                            parentsWidth = htmlObj.parents('.list_products_item').innerWidth(),
                            AfterWidth = (((itemObjWidth + 6)) * ExtTotalLength) + 25;
                            if (AfterWidth > parentsWidth) {
                                AfterWidth = parentsWidth;
                            }
                            if (ExtTotalLength > 0) {
                                ExtObj.find('.list').width(AfterWidth);
                                ExtObj.find('.ext_title').html('+' + ExtTotalLength);
                            }
                        htmlObj.addClass('posted');
                    }
                    _show_attribute_pic('.attr_item_select', '.item_img', '.list_products_item');
                    if ($(window).width() > 1000) {
                        parentObj.find('.attr_item_select').mouseenter(function() {
                            $(this).click();
                        });
                    }
                }
            },'json')
        }
        let _show_attribute_pic = function($click_obj, $alert_pic, $parent_obj) {
            let obj = $($click_obj);
            $('body').off('click' , $click_obj)
            $('body').on('click' , $click_obj , function() {
                let _this = $(this),
                    current = _this.hasClass('current') ? 1 : 0;
                    $status = 0,
                    $parentObj = _this.parents($parent_obj),
                    value = _this.find('input.picture_input').val(),
                    _computeRatio = _this.find('input.picture_computeRatio').val();
                $parentObj.find($click_obj).removeClass('current');
                if (!current) {
                    $status = 1;
                    _this.addClass('current');
                }
                if ($status) {
                    if (!$parentObj.find('.attribute_show_pic').size()) {
                        $parentObj.find($alert_pic).append('<div class="attribute_show_pic"><div class="compute_item_img"><div class="compute_process_img" style="'+_computeRatio+'"><img /></div>');
                    }
                    $parentObj.find('.attribute_show_pic').addClass('current').find('img').attr('src', value);
                    if (value == 'undefined' || value == '') {
                        $parentObj.find('.attribute_show_pic').find('img').attr('src','');
                    }
                } else {
                    $parentObj.find('.attribute_show_pic').removeClass('current');
                }
            })
        }
        // 递增定时防止有其它请求内容新插入影响显示效果
		let _incrementTimeout = function (i) {
			setTimeout(function () {
				if (i > 3 || (i == 3 && $('#themes_plist_left').width() == 0)) return false
				_request_prod_attribute(ProAry);
				i = i * 3
				_incrementTimeout(i)
			}, 1000 * i)
		}
		_incrementTimeout(1)
    }
}
function dropdown_page(){
	// 翻页
	let timer = null;
	let start = new Date();
	let limit = 1000;  // 1s间隔执行一次     
	$(window).on('scroll', function(){
		if (timer) clearTimeout(timer);
		let cur = new Date();
		let scrollFunc = () => {
			let _container = $('.default_products_list .list_products_box');
			let top = $(this).scrollTop();
			let height = $(this).height();
			let scrollHeight = _container[0].scrollHeight;
			let loadHeight = 120;
			if (scrollHeight - (top + height) < loadHeight) {
				let _pageObj = $('#dropdown_page');
				let page = parseInt(_pageObj.attr('page'));
				let maxPage = parseInt(_pageObj.attr('max-page'));
				if (page < maxPage) {
					$('.dropdown_loading').loading();
					page = page + 1;
					_pageObj.attr('page', page);
					let data = {'page':page};
                    let url = window.location.search;
					$.ajax({
						url:url,
						async:true,
						type:'get',
						data: data,
						dataType:'html',
						success:function(data){
							$('.dropdown_loading').unloading();
							let _dataHtml = $(data),
								_listData = _dataHtml.find('.list_products_box'),
								_itemData = _listData.find('.list_products_item');
							if (_itemData.length > 0){
								_container.append(_itemData);
							}
						}
					});
				}
			}
		}
		if (cur - start >= limit) {
			scrollFunc();
			start = cur;
		} else {
			timer = setTimeout(scrollFunc, 300);
		}
	});
}
$(document).ready(function(){
	let curIndex = $('.countdown_box').find('.countdown_item.cur').index(),
		_IsTouch = $(window).width()<1000 ? 1 : 0;
	$(".countdown_box .countdown_wrapper").visualSrcoll({
		count: 3,
		isTouch: _IsTouch,
		index: curIndex,
		callback: function() {
            if($(".countdown_box .countdown_wrapper .countdown_item").size() <= 3) {
                $('.countdown_box .countdown_wrapper .srcoll_btn').hide()
            }
			if ($('.countdown_box .countdown_time').size()) {
				$('.countdown_box .countdown_time').each(function(){
					$(this).genTimer({
						beginTime: ueeshop_config.date,
						targetTime: $(this).attr("data-endTime"),
						callbackOnlyDatas: 1,
						callback: function(e){
							if (e.dates == 0 && e.hours == 0 && e.minutes == 0 && e.seconds == 0) {
								this.parents('.countdown_item').addClass('out').attr('href','javascript:;').removeClass('cur');
							}
							for (k in e){
								let _timeData = e[k];
								this.find('.'+k).html(_timeData);
							}
							if (e.dates == 0) {
								this.find('.time_day').remove()
							}
						}
					});
				})
			}
		}
	});
    //监听有子节点 有增加的时候 就重新执行一次
	let _productsListObj = $('.default_products_list .list_products_box');
	if (_productsListObj.size()) { 
		let observer = new MutationObserver(() => {
			_show_products_list_attribute()
		})
		observer.observe(_productsListObj[0], {childList: true});
	}

	if ($('.default_products_list .ly_countdown_box').size()) {
		$('.default_products_list .list_products_item').each(function(){
			let _id = $(this).attr('data');
			let _this = $(this).find('.ly_countdown_box');
			if($(this).find('.ly_countdown_box').length){
				_this.genTimer({
					beginTime: ueeshop_config.date,
					targetTime: _this.attr("data-endTime"),
					type:'day',
					day_label:'d',
					days_label:':',
					day_unitWord: 'd',
					callbackOnlyDatas: 1,
					callback: function(e){
						let html = '';
						if (e.dates > 0) {
							html += '<span class="time_span"><span class="num">'+e.dates+'</span><span class="word">Days</span></span><i>:</i>';
						}
						html += '<span class="time_span"><span class="num">'+e.hours+'</span><span class="word">Hr</span></span><i>:</i><span class="time_span"><span class="num">'+e.minutes+'</span><span class="word">Min</span></span><i>:</i><span class="time_span"><span class="num">'+e.seconds+'</span><span class="word">Sc</span></span>';
						this.html(html)
					}
				});
			}
		})
	}

	Fancybox.bind("[data-fancybox]", {
		maxScale: 1000,
	});
})