{"Settings": {"Title": {"type": "input", "value": "Download"}, "Download": {"type": "panel", "expand": {"editlink": "/manage/plugins/download", "hint": "tips", "deletebutton": false}, "value": 1}, "FilePreview": {"type": "switch", "expand": {"hint": "hint"}, "value": false}, "FillingMethod": {"type": "select", "options": ["ratio", "tiled"], "linkage": {"tiled": ["PicDisplayArea"]}, "value": "tiled"}, "PicDisplayArea": {"type": "select", "options": ["top", "center", "bottom"], "value": "center"}, "TitleColor": {"type": "color", "value": "#000000"}, "TitleFontSizePc": {"type": "progress", "options": ["12", "60"], "suffix": "px", "value": "36px"}, "TitleFontSizeMobile": {"type": "progress", "options": ["12", "40"], "suffix": "px", "value": "24px"}, "IconColor": {"type": "color", "value": "#222222"}, "IconHoverColor": {"type": "color", "value": "#FFFFFF"}, "ButtonBgColor": {"type": "color", "value": "#00000000"}, "ButtonBgHoverColor": {"type": "color", "value": "#222222"}, "ButtonBorderColor": {"type": "color", "value": "#222222"}, "ButtonBorderHoverColor": {"type": "color", "value": "#00000000"}, "BgColor": {"type": "color", "value": "#ffffff"}, "ColumnsPc": {"type": "select", "options": ["1", "2"], "value": "1"}, "UpperSpacing": {"type": "input", "expand": {"suffix": "px"}, "value": "27"}, "UpperSpacingMobile": {"type": "input", "expand": {"suffix": "px"}, "value": "20"}, "LowerSpacing": {"type": "input", "expand": {"suffix": "px"}, "value": "40"}, "LowerSpacingMobile": {"type": "input", "expand": {"suffix": "px"}, "value": "40"}, "ColorFont": {"type": "word", "style": "notice", "hint": "hint"}}, "Config": {"PagePermit": ["goods"], "AllowDelete": false}, "Translation": {"Settings": ["Title"]}}