.ly_download_1 .download_list_box .box .list_box .list{ padding-top: 22px; display: grid; gap: 20px;}
.ly_download_1 .download_list_box .download_item{ display: flex; align-items: center; justify-content: space-between;padding: 16px 20px; background-color: #f3f3f3; box-sizing: border-box; }
.ly_download_1 .download_list_box .download_item:first-child{ margin-top: 0; }
.ly_download_1 .download_list_box .download_item .info_box{ display: flex; align-items: flex-start; justify-content: __left__; flex-wrap: wrap; flex: 1; }
.ly_download_1 .download_list_box .download_item .info_box .info{ width: calc( 100% - 160px ); }
.ly_download_1 .download_list_box .download_item .info_box .pic_box{ margin-__right__: 16px; height: 68px; width: 68px; position: relative; }
.ly_download_1 .download_list_box .download_item .info_box .pic_box img{ position: absolute; }
.ly_download_1 .download_list_box .download_item .info_box .tips{ display: block; font-size: 14px; color: #333333; }
.ly_download_1 .download_list_box .download_item .info_box .tips .size{ margin-__left__: 6px; }
.ly_download_1 .download_list_box .download_item .info_box .name{ display: inline-block; margin-bottom: 12px; font-size: 14px; color: #222222; word-break: break-all; }
.ly_download_1 .download_list_box .download_item .btn{ display: flex; align-items: center; justify-content: center; }
.ly_download_1 .download_list_box .download_item .btn a{ border-radius: 100%!important; border-width: 1px; border-style: solid; display: flex; vertical-align: middle; text-decoration: none; border-radius: 5px; position: relative; width: 33px; height: 33px; box-sizing: border-box; box-sizing: border-box; font-size: 14px; text-transform: uppercase; }
.ly_download_1 .download_list_box .download_item .btn a.download_file_view{ margin-__right__: 16px; }
.ly_download_1 .download_list_box .download_item .btn a i{ position: absolute; font-weight: bold; top: 50%; left: 50%; transform: translate(-50%, -50%); font-size: 18px; }
.ly_download_1 .download_list_box .download_item .btn a.download_file_view i{ font-weight: normal; font-size: 20px; }
.ly_download_1 .download_list_box #turn_page{ margin-top: 80px; }
.ly_download_1 .download_list_box .total_page,
.ly_download_1 .download_list_box #turn_page li.first,
.ly_download_1 .download_list_box #turn_page li.last{ display: none; }
.ly_download_1 .download_list_box #turn_page li:hover a{ background-color:  transparent; }
.ly_download_1 .download_list_box #turn_page li{ font-family: "iconfont" !important; font-size: 14px; font-style: normal; -webkit-font-smoothing: antialiased; -moz-osx-font-smoothing: grayscale; }
.ly_download_1 .download_list_box #turn_page li span{ display: inline-block; min-width: 24px; height: 24px; }
.ly_download_1 .download_list_box #turn_page li a{ color: #727272; }
.ly_download_1 .download_list_box #turn_page li.active a{ color: #000000; }
.ly_download_1 .download_list_box #turn_page li.prev a::before,
.ly_download_1 .download_list_box #turn_page li.prev span::before{ content: '\e63c'; font-size: 12px; }
.ly_download_1 .download_list_box #turn_page li.next a::before,
.ly_download_1 .download_list_box #turn_page li.next span::before{ content: '\e641'; font-size: 12px; }
@media screen and (max-width: 768px) {
    .ly_download_1{padding:0 10px;box-sizing: border-box;}
    .ly_download_1 .download_list_box #turn_page{ margin-top: 40px; }
    .ly_download_1 .download_list_box .download_item{ padding: 15px; }
    .ly_download_1 .download_list_box .download_item .info_box .pic_box{ margin-__right__: 8px; width: 50px; height: 50px; }
    .ly_download_1 .download_list_box .download_item .info_box .info{ padding-__right__: 10px; width: calc( 100% - 80px ); box-sizing: border-box; }
    .ly_download_1 .download_list_box .download_item .info_box .name{ margin-bottom: 10px; font-size: 14px; }
    .ly_download_1 .download_list_box .download_item .info_box .tips{ font-size: 14px; }
    .ly_download_1 .download_list_box .download_item .btn a{ padding: 0 14px; font-size: 12px; }
}
@media screen and (max-width: 450px) {
    .ly_download_1 .download_list_box .download_item .info_box .tips{ font-size: 12px; }
}