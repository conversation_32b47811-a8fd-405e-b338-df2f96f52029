.global_mode_order_tracking1{ padding: 105px 0 120px; position: relative; }
.global_mode_order_tracking1 .container{ margin: 0 auto; padding: 0 10px; position: relative; box-sizing: border-box; }
.global_mode_order_tracking1 .container .content{ margin: 0 auto; }
.global_mode_order_tracking1 .top_title{ font-weight: bold; text-align: center; }
.global_mode_order_tracking1 .sub_title{ margin-top: 10px; text-align: center; }
.global_mode_order_tracking1 .global_tracking_form{ margin: 50px auto 0; max-width: 750px; }
.global_mode_order_tracking1 .global_tracking_form .rows:first-child{ margin-top: 0; }
.global_mode_order_tracking1 .global_tracking_form .rows{ margin-top: 25px; }
.global_mode_order_tracking1 .global_tracking_form .rows label{ font-size: 14px; color: #333333; }
.global_mode_order_tracking1 .global_tracking_form .rows .input{ margin-top: 8px; }
.global_mode_order_tracking1 .global_tracking_form .rows .input .input_txt{ padding: 0 20px; width: 100%; height: 50px; line-height: 48px; border-radius: 5px; box-sizing: border-box; border: 1px solid #c9c9c9; }
.global_mode_order_tracking1 .global_tracking_form .submit{ margin-top: 60px; text-align: center; }
.global_mode_order_tracking1 .global_tracking_form .submit .input_button{ display: inline-block; padding: 0 45px; height: 52px; line-height: 52px; border-radius: 5px; font-size: 18px; cursor: pointer; overflow: hidden; }
.global_mode_order_tracking1 .abs{ position: absolute; padding: 50px 0; width: 100%; height: 100%; background-color: #fff; top: 0; __left__: 0; text-align: center; z-index: 9999;}
.global_mode_order_tracking1 .abs .loader{ margin: 0 auto; width: 94px; height: 94px; background-color: transparent; border-__left__: 5px #000 solid; border-__right__: 5px #cbcbca solid; border-bottom: 5px #cbcbca solid; border-top: 5px #000000 solid; border-radius: 100%; animation: rotation .7s infinite linear; -o-animation: rotation .7s infinite linear; -moz-animation: rotation .7s infinite linear; -webkit-animation: rotation .7s infinite linear; display: inline-block; vertical-align: middle;}
.global_mode_order_tracking1 .abs .middle{ display: inline-block ; vertical-align: middle; }
.global_mode_order_tracking1 .abs span{ display: inline-block; vertical-align: middle; height: 100%; }
.global_mode_order_tracking1 .abs .txt{ margin-top: 30px; font-size: 18px; color: #222222; font-weight: bold; }
.global_mode_order_tracking1 .error_content{ display: none; padding: 100px 0; text-align: center; }
.global_mode_order_tracking1 .error_content .top_title{ font-size: 30px; }
.global_mode_order_tracking1 .success_content{ padding: 100px 0; display: none; text-align: center; }
.global_mode_order_tracking1 .success_content .order_box .top{ padding: 0 10px; width: 100%; line-height: 60px; box-sizing: border-box; text-align: __left__; }
.global_mode_order_tracking1 .success_content .order_box .rows{ margin-top: 20px; }
.global_mode_order_tracking1 .success_content .order_box .rows:first-child{ margin-top: 0; }
.global_mode_order_tracking1 .success_content .order_box .rows.current .tracking_tit{ border-bottom: 0; }
.global_mode_order_tracking1 .success_content .order_box .tracking_tit{ padding: 28px; text-align: __left__; box-sizing: border-box; background-color: #f7f7f7; border: 1px solid #ebebeb; font-size: 14px;  position: relative; cursor: pointer; }
.global_mode_order_tracking1 .success_content .order_box .tracking_tit .tit{ font-weight: normal; font-size: 14px; }
.global_mode_order_tracking1 .success_content .order_box .rows.current .tracking_tit .iconfont{ transform: translateY(-50%) rotate(0deg); }
.global_mode_order_tracking1 .success_content .order_box .tracking_tit .iconfont{ position: absolute; top: 50%; transform: translateY(-50%) rotate(-180deg); __right__: 20px; font-size: 12px; transition: .4s; }
.global_mode_order_tracking1 .success_content .order_box .top .tit{ font-size: 14px; color: #666666; }
.global_mode_order_tracking1 .success_content .order_box .top .order_num{ font-size: 18px; color: #222222; font-weight: bold; }
.global_mode_order_tracking1 .success_content .order_box .top .status{ font-size: 14px; color: #666666; }
.global_mode_order_tracking1 .success_content .order_box .top .status b{ font-size: 14px; color: #000000; font-weight: bold; }
.global_mode_order_tracking1 .success_content .order_box .rows.current .tracking_box{ display: block; }
.global_mode_order_tracking1 .success_content .order_box .tracking_box{ display: none; position: relative; }
.global_mode_order_tracking1 .success_content .order_box .tracking_box iframe{ z-index: 0!important; width: 100%!important;position: static!important;box-sizing: border-box; border-top: 0; }
.global_mode_order_tracking1 .back{ display: inline-block; margin-top: 70px; padding: 0 70px; height: 50px; line-height: 50px;border: 1px solid #000000; border-radius: 5px; background-color: #000000; font-size: 18px; color: #fff; overflow: hidden; text-align: center;text-decoration: none;}

@media screen and (max-width: 1200px){
    .global_mode_order_tracking1 .container{ width: 750px; }
}

@media screen and (max-width: 780px){
    .global_mode_order_tracking1 .container{ width: 92%; }
    .global_mode_order_tracking1 .container .content{ width: 100%;box-sizing: border-box; }
}

@media screen and (max-width: 750px){
    .global_mode_order_tracking1{ padding: 30px 0; }
    .global_mode_order_tracking1 .sub_title{ margin-top: 10px; }
    .global_mode_order_tracking1 .global_tracking_form{ margin-top: 30px; width: 100%; }
    .global_mode_order_tracking1 .global_tracking_form .rows { margin-top: 15px; }
    .global_mode_order_tracking1 .global_tracking_form .rows label{ font-size: 12px; }
    .global_mode_order_tracking1 .global_tracking_form .rows .input{ margin-top: 5px; }
    .global_mode_order_tracking1 .global_tracking_form .rows .input .input_txt{ height: 42px; line-height: 40px; }
    .global_mode_order_tracking1 .global_tracking_form .submit{ margin-top: 30px; display: block; width: 100%;}
    .global_mode_order_tracking1 .global_tracking_form .submit .input_button{ display: block; padding: 0; width: 100%; }
    .global_mode_order_tracking1 .back{ display: block; margin-top: 30px; padding: 0; width: 100%; height: 42px; line-height: 40px; box-sizing: border-box; }
    .global_mode_order_tracking1 .success_content{ padding: 0; }
    .global_mode_order_tracking1 .success_content .order_box .top{ padding: 15px 0; line-height: 30px; }
    .global_mode_order_tracking1 .success_content .order_box .top .status{ float: __right__;}
    .global_mode_order_tracking1 .success_content .order_box .top .order_num{ font-size: 14px; }
    .global_mode_order_tracking1 .success_content .order_box .tracking_tit{ padding: 18px 12px; }
}

@keyframes rotation{from{transform:rotate(0)}to{transform:rotate(359deg)}}
@-o-keyframes rotation{from{-o-transform:rotate(0)}to{-o-transform:rotate(359deg)}}
@-moz-keyframes rotation{from{-moz-transform:rotate(0)}to{-moz-transform:rotate(359deg)}}
@-webkit-keyframes rotation{from{-webkit-transform:rotate(0)}to{-webkit-transform:rotate(359deg)}}


/**** 新订单查询 ****/
.global_mode_order_tracking1{background: #FFF;}
.global_mode_order_tracking1 .success_content .order_box{ padding-bottom: 38px; }
.global_mode_order_tracking1 .success_content .order_box .list{margin-top: 20px;}
.global_mode_order_tracking1 .success_content .order_box .list:first-child{margin-top: 0;}
.global_mode_order_tracking1 .success_content .order_box .rows.current{border:1px solid #ebebeb;}
.global_mode_order_tracking1 .success_content .order_box .rows.current .tracking_tit{border:none;border-bottom: 1px solid #ebebeb;}
.global_mode_order_tracking1 .success_content .order_box .tracking_tit .iconfont{ __right__: 20px; font-size: 12px; transition: .4s; }
.global_mode_order_tracking1 .success_content .status_box{ margin-top: 38px;padding:0 30px;}
.global_mode_order_tracking1 .success_content .status_box.status_box_margin{ margin-bottom: 80px;}
.global_mode_order_tracking1 .success_content .status_box .item{position: relative;float: __left__;text-align: center;font-size: 12px;color: #999;text-align: center; z-index: 0;}
.global_mode_order_tracking1 .success_content .status_box .item .line{position: absolute;top: 11px;__left__: 0;height: 2px;width: 100%;background: #cccccc;line-height: 22px;}
.global_mode_order_tracking1 .success_content .status_box .item .fir{__left__: auto;__right__: 0;border-top-__left__-radius: 11px;border-bottom-__left__-radius: 11px;}
.global_mode_order_tracking1 .success_content .status_box .item .last{border-top-__right__-radius: 11px;border-bottom-__right__-radius: 11px;}
.global_mode_order_tracking1 .success_content .status_box .item.cur{color: #333;}
.global_mode_order_tracking1 .success_content .status_box .item.cur .line.error{ display: none; }
.global_mode_order_tracking1 .success_content .status_box .item.cur .line{background: #00a850;}
.global_mode_order_tracking1 .success_content .status_box .item.cur .line:after{content:'';display: inline-block;width:0;height:0;border-width:5px;border-__right__-width: 0px; border-style:solid;border-color:transparent;border-__left__-color: #00a850; position:absolute;top: -4px;__right__: -4px;z-index: 1;}
.global_mode_order_tracking1 .success_content .status_box .item .status{ margin-top: 5px; line-height: 24px; font-size: 14px; color: #999999; }
.global_mode_order_tracking1 .success_content .status_box .item.cur .status{ color: #00a84f; font-weight: 600; }
.global_mode_order_tracking1 .success_content .status_box .item .time{ line-height: 20px; font-size: 12px; color: #999999; }
.global_mode_order_tracking1 .success_content .status_box .item.cur .time{ color: #999999; font-weight: 600; }
.global_mode_order_tracking1 .success_content .status_box .item i{ display: block; margin: 0 auto; width: 72px; height: 28px; line-height: 28px; background-color: #fff; background-position: center center; background-repeat: no-repeat; position: relative; z-index: 2; font-size: 32px; }
body.lang_ar .global_mode_order_tracking1 .success_content .status_box .item i{transform: rotate3d(0, 1, 0, 180deg);}
.global_mode_order_tracking1 .success_content .status_box .item.cur i{ color: #00a84f; }
.global_mode_order_tracking1 .success_content .status_box .item .fir ,
.global_mode_order_tracking1 .success_content .status_box .item .last{ max-width: 50%; }
.global_mode_order_tracking1 .success_content .tracking_box_content{margin-top:30px;padding: 30px;text-align: __left__;}
.global_mode_order_tracking1 .success_content .tracking_box_list li{margin-__left__: 10px; border-__left__: 2px dashed #e1e5eb; padding-__left__:10px;position: relative; padding-bottom: 20px;}
.global_mode_order_tracking1 .success_content .tracking_box_list .track_radio{display: inline-block;width: 12px;;height: 12px;background: #fff;position: absolute; top:38px;__left__: -9px;border-radius: 50%; border: 2px solid #7d8d9e;}
.global_mode_order_tracking1 .success_content .tracking_box_list .track_content{margin-__left__:27px;background: #f7f9fb;min-height: 45px;padding:20px 20px 20px 30px;border-radius: 5px;position: relative}
.global_mode_order_tracking1 .success_content .tracking_box_list .track_content .arrow {width: 0;height: 0;border-top: 20px solid transparent;border-__right__: 24px solid #f7f9fb;border-bottom: 20px solid transparent; position: absolute;top:24px;__left__:-23px}
.global_mode_order_tracking1 .success_content .tracking_box_list .track_content .track_content_text{line-height: 20px;color:#333333;font-size: 14px;}
.global_mode_order_tracking1 .success_content .tracking_box_list .track_content .track_content_time{line-height: 16px;color:#888888;font-size: 12px;margin-top:10px;}
.global_mode_order_tracking1 .success_content .tracking_list_hide{display: none;}
.global_mode_order_tracking1 .success_content .tracking_box_show_more{margin-__left__:40px;}
.global_mode_order_tracking1 .success_content .tracking_box_show_more_hide{display: none;}
.global_mode_order_tracking1 .success_content .tracking_box_show_more a{font-size: 14px; font-weight: bold;color:#222;}
.global_mode_order_tracking1 .success_content .tracking_box_show_more a:hover{text-decoration:none; color: #00a84f;}
.global_mode_order_tracking1 .success_content .tracking_box_show_more .iconfont{margin-__left__: 5px;line-height: 20px;vertical-align: middle;}
