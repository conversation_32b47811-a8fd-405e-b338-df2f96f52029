{"about": ["mode_1", "mode_2", "mode_3", "mode_4", "mode_5", "mode_6", "mode_7", "mode_8"], "article": ["mode_1"], "banner": ["mode_1"], "blog": ["mode_1", "mode_2", "mode_3", "mode_4", "mode_5", "mode_6", "mode_7", "mode_8", "mode_9", "mode_10", "mode_11", "mode_12", "mode_13", "mode_14", "mode_15", "mode_16", "mode_17", "mode_18", "mode_19", "mode_20", "mode_21", "mode_22", "mode_23"], "brands": ["mode_1", "mode_2"], "carousel": ["mode_1", "mode_2"], "combination_purchase": ["mode_1", "mode_2"], "coupon": ["mode_1", "mode_2"], "customize": ["mode_1"], "description": ["mode_1", "mode_3", "mode_4", "mode_5", "mode_6"], "footer": ["mode_1", "mode_2", "mode_3", "mode_4", "mode_5", "mode_6", "mode_7", "mode_8", "mode_9", "mode_10", "mode_11", "mode_12", "mode_13", "mode_14", "mode_15", "mode_16", "mode_17", "mode_18", "mode_19", "mode_20", "mode_21", "mode_22", "mode_23", "mode_24", "mode_25", "mode_26", "mode_27", "mode_28", "mode_29", "mode_30", "mode_31", "mode_32", "mode_33", "mode_34", "mode_35", "mode_36", "mode_37", "mode_38", "mode_39", "mode_40", "mode_41", "mode_42", "mode_43", "mode_44", "mode_45", "mode_46", "mode_47", "mode_48", "mode_49", "mode_50", "mode_51", "mode_52", "mode_53", "mode_54", "mode_55", "mode_56", "mode_57", "mode_58", "mode_59", "mode_60", "mode_61", "mode_62", "mode_63", "mode_64", "mode_65", "mode_66", "mode_67", "mode_68", "mode_69", "mode_70", "mode_71", "mode_72", "mode_73", "mode_74", "mode_75", "mode_76"], "gallery": ["mode_1", "mode_2", "mode_3", "mode_4", "mode_5", "mode_6", "mode_7", "mode_8", "mode_9", "mode_10", "mode_11", "mode_12", "mode_13", "mode_14"], "header": ["mode_1", "mode_2", "mode_3", "mode_4", "mode_5", "mode_6", "mode_7", "mode_8", "mode_9", "mode_10", "mode_11", "mode_12", "mode_13", "mode_14", "mode_15", "mode_16", "mode_17", "mode_18", "mode_19", "mode_20", "mode_21", "mode_22", "mode_23", "mode_24", "mode_25", "mode_26", "mode_27", "mode_28", "mode_29", "mode_30", "mode_31", "mode_32", "mode_33", "mode_34", "mode_35", "mode_36", "mode_37", "mode_38", "mode_39", "mode_40", "mode_41", "mode_42", "mode_43", "mode_44", "mode_45", "mode_46", "mode_47", "mode_48", "mode_49", "mode_50", "mode_51", "mode_52", "mode_53", "mode_54", "mode_55", "mode_56", "mode_57", "mode_58", "mode_59", "mode_60", "mode_61", "mode_62", "mode_63", "mode_64", "mode_65", "mode_66", "mode_67", "mode_68", "mode_69", "mode_70", "mode_71", "mode_72", "mode_73", "mode_74", "mode_75", "mode_76"], "newsletter": ["mode_1", "mode_2", "mode_3", "mode_4", "mode_5", "mode_6", "mode_7", "mode_8", "mode_9", "mode_10", "mode_11", "mode_12", "mode_13", "mode_14", "mode_15", "mode_16", "mode_17", "mode_18", "mode_19", "mode_20", "mode_21", "mode_22", "mode_23", "mode_24", "mode_25", "mode_26", "mode_27", "mode_28", "mode_29", "mode_30", "mode_31", "mode_32", "mode_33", "mode_34", "mode_35", "mode_36", "mode_37", "mode_38", "mode_39", "mode_40", "mode_41", "mode_42"], "order_tracking": ["mode_1"], "other": ["mode_1", "mode_3", "mode_4"], "poster": ["mode_270", "mode_269", "mode_268", "mode_267", "mode_266", "mode_265", "mode_264", "mode_263", "mode_262", "mode_261", "mode_260", "mode_258", "mode_257", "mode_256", "mode_255", "mode_254", "mode_253", "mode_252", "mode_251", "mode_250", "mode_249", "mode_248", "mode_247", "mode_246", "mode_245", "mode_244", "mode_243", "mode_242", "mode_241", "mode_240", "mode_239", "mode_238", "mode_237", "mode_236", "mode_235", "mode_234", "mode_233", "mode_232", "mode_230", "mode_229", "mode_1", "mode_2", "mode_3", "mode_4", "mode_5", "mode_6", "mode_7", "mode_8", "mode_9", "mode_10", "mode_11", "mode_12", "mode_31", "mode_32", "mode_33", "mode_34", "mode_41", "mode_42", "mode_43", "mode_44", "mode_45", "mode_46", "mode_47", "mode_48", "mode_49", "mode_50", "mode_51", "mode_52", "mode_53", "mode_54", "mode_55", "mode_56", "mode_57", "mode_58", "mode_59", "mode_60", "mode_61", "mode_62", "mode_63", "mode_64", "mode_65", "mode_66", "mode_67", "mode_68", "mode_69", "mode_70", "mode_71", "mode_72", "mode_73", "mode_74", "mode_75", "mode_76", "mode_77", "mode_78", "mode_79", "mode_80", "mode_81", "mode_82", "mode_83", "mode_84", "mode_85", "mode_86", "mode_87", "mode_88", "mode_89", "mode_90", "mode_91", "mode_92", "mode_93", "mode_94", "mode_95", "mode_96", "mode_97", "mode_98", "mode_99", "mode_100", "mode_101", "mode_102", "mode_103", "mode_104", "mode_105", "mode_106", "mode_107", "mode_108", "mode_109", "mode_110", "mode_111", "mode_112", "mode_113", "mode_114", "mode_115", "mode_116", "mode_117", "mode_118", "mode_119", "mode_120", "mode_121", "mode_122", "mode_123", "mode_124", "mode_125", "mode_126", "mode_127", "mode_128", "mode_129", "mode_130", "mode_131", "mode_132", "mode_133", "mode_134", "mode_135", "mode_136", "mode_137", "mode_138", "mode_140", "mode_141", "mode_142", "mode_143", "mode_144", "mode_145", "mode_146", "mode_147", "mode_148", "mode_149", "mode_150", "mode_151", "mode_152", "mode_153", "mode_154", "mode_155", "mode_156", "mode_157", "mode_158", "mode_159", "mode_160", "mode_161", "mode_162", "mode_163", "mode_164", "mode_165", "mode_166", "mode_167", "mode_168", "mode_169", "mode_170", "mode_171", "mode_172", "mode_173", "mode_231", "mode_174", "mode_175", "mode_176", "mode_177", "mode_178", "mode_179", "mode_180", "mode_181", "mode_182", "mode_183", "mode_184", "mode_185", "mode_186", "mode_187", "mode_189", "mode_190", "mode_191", "mode_192", "mode_193", "mode_194", "mode_195", "mode_196", "mode_197", "mode_198", "mode_199", "mode_200", "mode_201", "mode_202", "mode_203", "mode_204", "mode_205", "mode_206", "mode_207", "mode_208", "mode_209", "mode_210", "mode_211", "mode_212", "mode_213", "mode_214", "mode_215", "mode_216", "mode_217", "mode_218", "mode_219", "mode_220", "mode_221", "mode_222", "mode_223", "mode_224", "mode_225", "mode_226", "mode_227", "mode_228"], "product_description": ["mode_1", "mode_2"], "product_list": ["mode_1", "mode_4", "mode_5"], "product_maylike": ["mode_1"], "product_purchase": ["mode_1", "mode_2", "mode_4", "mode_5", "mode_7", "mode_8"], "product_reviews": ["mode_1", "mode_2", "mode_3", "mode_4"], "products": ["mode_138", "mode_137", "mode_136", "mode_135", "mode_1", "mode_2", "mode_3", "mode_4", "mode_5", "mode_6", "mode_7", "mode_8", "mode_9", "mode_10", "mode_11", "mode_12", "mode_13", "mode_14", "mode_15", "mode_16", "mode_17", "mode_18", "mode_19", "mode_20", "mode_21", "mode_22", "mode_23", "mode_24", "mode_25", "mode_26", "mode_27", "mode_28", "mode_29", "mode_30", "mode_31", "mode_32", "mode_33", "mode_34", "mode_35", "mode_36", "mode_37", "mode_38", "mode_39", "mode_40", "mode_41", "mode_42", "mode_43", "mode_44", "mode_45", "mode_46", "mode_47", "mode_48", "mode_49", "mode_50", "mode_51", "mode_52", "mode_53", "mode_54", "mode_55", "mode_56", "mode_57", "mode_58", "mode_59", "mode_60", "mode_61", "mode_62", "mode_63", "mode_64", "mode_65", "mode_66", "mode_67", "mode_68", "mode_69", "mode_70", "mode_71", "mode_72", "mode_73", "mode_74", "mode_75", "mode_76", "mode_77", "mode_78", "mode_79", "mode_80", "mode_81", "mode_82", "mode_83", "mode_84", "mode_85", "mode_86", "mode_87", "mode_88", "mode_89", "mode_90", "mode_91", "mode_92", "mode_93", "mode_94", "mode_95", "mode_96", "mode_97", "mode_98", "mode_99", "mode_100", "mode_102", "mode_103", "mode_104", "mode_105", "mode_106", "mode_107", "mode_108", "mode_109", "mode_110", "mode_111", "mode_112", "mode_113", "mode_114", "mode_115", "mode_116", "mode_117", "mode_118", "mode_119", "mode_120", "mode_121", "mode_122", "mode_123", "mode_124", "mode_125", "mode_126", "mode_127", "mode_128", "mode_129", "mode_130", "mode_131", "mode_132", "mode_133", "mode_134"], "review": ["mode_1", "mode_2", "mode_3", "mode_4", "mode_5", "mode_6", "mode_7", "mode_8"], "service": ["mode_1", "mode_2", "mode_3", "mode_4", "mode_5", "mode_6", "mode_7", "mode_8", "mode_9"], "special": ["mode_1", "mode_2", "mode_3", "mode_4", "mode_5", "mode_6", "mode_7", "mode_8"], "team": ["mode_1", "mode_2", "mode_3", "mode_4", "mode_5", "mode_6"], "video": ["mode_1", "mode_2", "mode_3", "mode_4", "mode_5", "mode_6", "mode_7", "mode_8", "mode_9", "mode_10", "mode_11", "mode_12", "mode_13"], "countdown": ["mode_4", "mode_3", "mode_1", "mode_2"], "download": ["mode_1", "mode_2"]}