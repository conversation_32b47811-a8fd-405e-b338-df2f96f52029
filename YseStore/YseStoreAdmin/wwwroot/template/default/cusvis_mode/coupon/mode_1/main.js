function detailCoupon ()
{ //详细页优惠券
    $('#detail_coupon_fixed .btn_close').click(function(){
        $('#detail_coupon_fixed').fadeOut(500, function(){});            
        $('#detail_coupon_fixed .code').select();
    });
    $('.coupon_wrpper .coupon_item .get_btn').click(function(){
        var _this = $(this),
            _code = _this.attr('data-code');
            _isOk = _this.parents('.coupon_item').hasClass('disabled');
        if (_code && !_isOk) {
            _this.hide().siblings('.cou_code').show();
            $('#detail_coupon_fixed').fadeIn(500).find('.code').val(_code);
            $.post('/', 'do_action=action.getCouponCode&couponCode=' + _code, function() {}, 'json');
        }
    });
    $('.coupon_wrpper .coupon_item .cou_code').click(function(){
        $(this).next('input').trigger('click')
    })
    $('.coupon_wrpper .coupon_item .btn_box input').click(function(){
        this.select();
        document.execCommand('copy');
        global_obj.win_alert_auto_close(lang_obj.user.account.copy_success, 'success', 2000);
    })
}