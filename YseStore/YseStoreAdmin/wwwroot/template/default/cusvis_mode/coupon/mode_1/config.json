{"Blocks": {"Coupon-1": {"Coupon": {"type": "coupon", "expand": {"module": "coupon", "hint": "hint", "link": "link"}, "value": ""}, "TextAlign": {"type": "textalign", "options": ["left", "center", "right"], "expand": {"visibility": "hidden"}, "value": "center"}, "TitleFontSizePc": {"type": "progress", "options": ["12", "60"], "suffix": "px", "value": "42px"}, "TitleFontSizeMobile": {"type": "progress", "options": ["12", "40"], "suffix": "px", "value": "30px"}, "SubTitleFontSizePc": {"type": "progress", "options": ["12", "60"], "suffix": "px", "value": "22px"}, "SubTitleFontSizeMobile": {"type": "progress", "options": ["12", "40"], "suffix": "px", "value": "12px"}, "ContentFontSizePc": {"type": "progress", "options": ["12", "60"], "suffix": "px", "value": "18px"}, "ContentFontSizeMobile": {"type": "progress", "options": ["12", "40"], "suffix": "px", "value": "12px"}, "ButtonTextSizePc": {"type": "progress", "options": ["12", "60"], "suffix": "px", "value": "22px"}, "ButtonTextSizeMobile": {"type": "progress", "options": ["12", "40"], "suffix": "px", "value": "16px"}, "TitleColor": {"type": "color", "value": "#FFFFFF"}, "SubTitleColor": {"type": "color", "value": "#FFFFFF"}, "ContentColor": {"type": "color", "value": "#FFFFFF"}, "ButtonColor": {"type": "color", "value": "#ffe931"}, "ButtonTextColor": {"type": "color", "value": "#000000"}, "BgBorderColor": {"type": "color", "value": "#ffffff80"}, "BgColor": {"type": "color", "value": "#fd3a31"}, "ColorFont": {"type": "word", "style": "notice", "hint": "hint"}}, "Coupon-2": {"Coupon": {"type": "coupon", "expand": {"module": "coupon", "hint": "hint", "link": "link"}, "value": ""}, "TextAlign": {"type": "textalign", "options": ["left", "center", "right"], "expand": {"visibility": "hidden"}, "value": "center"}, "TitleFontSizePc": {"type": "progress", "options": ["12", "60"], "suffix": "px", "value": "42px"}, "TitleFontSizeMobile": {"type": "progress", "options": ["12", "40"], "suffix": "px", "value": "30px"}, "SubTitleFontSizePc": {"type": "progress", "options": ["12", "60"], "suffix": "px", "value": "22px"}, "SubTitleFontSizeMobile": {"type": "progress", "options": ["12", "40"], "suffix": "px", "value": "12px"}, "ContentFontSizePc": {"type": "progress", "options": ["12", "60"], "suffix": "px", "value": "18px"}, "ContentFontSizeMobile": {"type": "progress", "options": ["12", "40"], "suffix": "px", "value": "12px"}, "ButtonTextSizePc": {"type": "progress", "options": ["12", "60"], "suffix": "px", "value": "22px"}, "ButtonTextSizeMobile": {"type": "progress", "options": ["12", "40"], "suffix": "px", "value": "16px"}, "TitleColor": {"type": "color", "value": "#FFFFFF"}, "SubTitleColor": {"type": "color", "value": "#FFFFFF"}, "ContentColor": {"type": "color", "value": "#FFFFFF"}, "ButtonColor": {"type": "color", "value": "#ffe931"}, "ButtonTextColor": {"type": "color", "value": "#000000"}, "BgBorderColor": {"type": "color", "value": "#ffffff80"}, "BgColor": {"type": "color", "value": "#fd3a31"}, "ColorFont": {"type": "word", "style": "notice", "hint": "hint"}}, "Coupon-3": {"Coupon": {"type": "coupon", "expand": {"module": "coupon", "hint": "hint", "link": "link"}, "value": ""}, "TextAlign": {"type": "textalign", "options": ["left", "center", "right"], "expand": {"visibility": "hidden"}, "value": "center"}, "TitleFontSizePc": {"type": "progress", "options": ["12", "60"], "suffix": "px", "value": "42px"}, "TitleFontSizeMobile": {"type": "progress", "options": ["12", "40"], "suffix": "px", "value": "30px"}, "SubTitleFontSizePc": {"type": "progress", "options": ["12", "60"], "suffix": "px", "value": "22px"}, "SubTitleFontSizeMobile": {"type": "progress", "options": ["12", "40"], "suffix": "px", "value": "12px"}, "ContentFontSizePc": {"type": "progress", "options": ["12", "60"], "suffix": "px", "value": "18px"}, "ContentFontSizeMobile": {"type": "progress", "options": ["12", "40"], "suffix": "px", "value": "12px"}, "ButtonTextSizePc": {"type": "progress", "options": ["12", "60"], "suffix": "px", "value": "22px"}, "ButtonTextSizeMobile": {"type": "progress", "options": ["12", "40"], "suffix": "px", "value": "16px"}, "TitleColor": {"type": "color", "value": "#FFFFFF"}, "SubTitleColor": {"type": "color", "value": "#FFFFFF"}, "ContentColor": {"type": "color", "value": "#FFFFFF"}, "ButtonColor": {"type": "color", "value": "#ffe931"}, "ButtonTextColor": {"type": "color", "value": "#000000"}, "BgBorderColor": {"type": "color", "value": "#ffffff80"}, "BgColor": {"type": "color", "value": "#fd3a31"}, "ColorFont": {"type": "word", "style": "notice", "hint": "hint"}}, "Coupon-4": {"Coupon": {"type": "coupon", "expand": {"module": "coupon", "hint": "hint", "link": "link"}, "value": ""}, "TextAlign": {"type": "textalign", "options": ["left", "center", "right"], "expand": {"visibility": "hidden"}, "value": "center"}, "TitleFontSizePc": {"type": "progress", "options": ["12", "60"], "suffix": "px", "value": "42px"}, "TitleFontSizeMobile": {"type": "progress", "options": ["12", "40"], "suffix": "px", "value": "30px"}, "SubTitleFontSizePc": {"type": "progress", "options": ["12", "60"], "suffix": "px", "value": "22px"}, "SubTitleFontSizeMobile": {"type": "progress", "options": ["12", "40"], "suffix": "px", "value": "12px"}, "ContentFontSizePc": {"type": "progress", "options": ["12", "60"], "suffix": "px", "value": "18px"}, "ContentFontSizeMobile": {"type": "progress", "options": ["12", "40"], "suffix": "px", "value": "12px"}, "ButtonTextSizePc": {"type": "progress", "options": ["12", "60"], "suffix": "px", "value": "22px"}, "ButtonTextSizeMobile": {"type": "progress", "options": ["12", "40"], "suffix": "px", "value": "16px"}, "TitleColor": {"type": "color", "value": "#FFFFFF"}, "SubTitleColor": {"type": "color", "value": "#FFFFFF"}, "ContentColor": {"type": "color", "value": "#FFFFFF"}, "ButtonColor": {"type": "color", "value": "#ffe931"}, "ButtonTextColor": {"type": "color", "value": "#000000"}, "BgBorderColor": {"type": "color", "value": "#ffffff80"}, "BgColor": {"type": "color", "value": "#fd3a31"}, "ColorFont": {"type": "word", "style": "notice", "hint": "hint"}}}, "Settings": {"ColumnsPc": {"type": "select", "options": ["2", "3", "4", "5"], "value": "4"}, "ColumnsMobile": {"type": "select", "options": ["1", "2"], "value": "2"}, "BgColor": {"type": "color", "value": "#FFFFFF"}, "UpperSpacing": {"type": "input", "expand": {"suffix": "px"}, "value": "100"}, "UpperSpacingMobile": {"type": "input", "expand": {"suffix": "px"}, "value": "30"}, "LowerSpacing": {"type": "input", "expand": {"suffix": "px"}, "value": "100"}, "LowerSpacingMobile": {"type": "input", "expand": {"suffix": "px"}, "value": "30"}, "ColorFont": {"type": "word", "style": "notice", "hint": "hint"}}, "Config": {"PagePermit": ["*"], "BlocksAdd": true}}