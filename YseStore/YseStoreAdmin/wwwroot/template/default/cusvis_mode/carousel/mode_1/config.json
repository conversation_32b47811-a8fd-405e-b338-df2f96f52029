{"Blocks": {"Carousel-1": {"ContentType": {"type": "select", "options": ["images", "local"], "linkage": {"images": ["PicPc", "PicDisplayAreaPc", "PicMobile", "PicDisplayAreaMobile", "ImageAltPc", "ImageAltMobile"], "local": ["LocalVideo"]}, "value": "images"}, "PicPc": {"type": "image", "expand": {"width": "1920", "height": "600"}, "value": "{CDN_URL_MODE}carousel/mode_1/index10.jpg"}, "ImageAltPc": {"type": "input", "value": "", "expand": {"hint": "tips"}}, "FillingMethodPc": {"type": "select", "options": ["ratio", "tiled"], "value": "tiled"}, "PicDisplayAreaPc": {"type": "select", "options": ["top", "center", "bottom"], "value": "center"}, "PicMobile": {"type": "image", "expand": {"width": "750", "height": "600"}, "value": "{CDN_URL_MODE}carousel/mode_1/index11.jpg"}, "ImageAltMobile": {"type": "input", "value": "", "expand": {"hint": "tips"}}, "FillingMethodMobile": {"type": "select", "options": ["ratio", "tiled"], "value": "tiled"}, "PicDisplayAreaMobile": {"type": "select", "options": ["top", "center", "bottom"], "value": "center"}, "LocalVideo": {"type": "video", "value": ""}, "Title": {"type": "input", "value": "Lorem ipsum dolor"}, "Content": {"type": "richtext", "value": "consectetur adipisc ing elit, sed do Eiusmod Tempor inci didunt ut labore et dolore magna.Sed ut pers piciatisunde omnis iste natus error sit voluptatem"}, "ButtonText": {"type": "input", "value": "Shop Now"}, "Link": {"type": "link", "value": ""}, "TextPosition": {"type": "select", "options": ["lefttop", "top", "righttop", "left", "center", "right", "<PERSON><PERSON><PERSON>", "bottom", "<PERSON><PERSON><PERSON>"], "value": "center"}, "TextAlign": {"type": "textalign", "options": ["left", "center", "right"], "value": "center"}, "TextContentSetMobile": {"type": "switch", "linkage": {"1": ["TitleMobile", "ContentMobile"]}, "value": false}, "TitleMobile": {"type": "input", "value": "Lorem ipsum dolor"}, "ContentMobile": {"type": "richtext", "value": "consectetur adipisc ing elit, sed do Eiusmod Tempor inci didunt ut labore et dolore magna.Sed ut pers piciatisunde omnis iste natus error sit voluptatem"}, "TitleFontSizePc": {"type": "progress", "options": ["20", "100"], "suffix": "px", "value": "48px"}, "TitleFontSizeMobile": {"type": "progress", "options": ["12", "60"], "suffix": "px", "value": "30px"}, "ContentFontSizePc": {"type": "progress", "options": ["12", "28"], "suffix": "px", "value": "16px"}, "ContentFontSizeMobile": {"type": "progress", "options": ["12", "24"], "suffix": "px", "value": "14px"}, "TitleColor": {"type": "color", "value": "#ffffff"}, "ContentColor": {"type": "color", "value": "#ffffff"}, "ButtonTextSizePc": {"type": "progress", "options": ["12", "60"], "suffix": "px", "value": "16px"}, "ButtonTextSizeMobile": {"type": "progress", "options": ["12", "30"], "suffix": "px", "value": "16px"}, "ButtonTextColor": {"type": "color", "value": "#ffffff"}, "ButtonHoverTextColor": {"type": "color", "value": "#ff830a"}, "ButtonColor": {"type": "color", "value": "#ff830a"}, "ButtonBorderColor": {"type": "color", "value": "#ff820a"}, "ButtonHoverColor": {"type": "color", "value": "#ffffff"}, "ButtonBorderHoverColor": {"type": "color", "value": "#fff"}, "ButtonRadiusSize": {"type": "progress", "options": ["0", "100"], "suffix": "px", "value": "20px"}, "ButtonHeightPc": {"type": "input", "expand": {"suffix": "px"}, "value": "40"}, "ButtonWidthPc": {"type": "input", "expand": {"suffix": "px"}, "value": "160"}, "ButtonHeightMobile": {"type": "input", "expand": {"suffix": "px"}, "value": "40"}, "ButtonWidthMobile": {"type": "input", "expand": {"suffix": "px"}, "value": "160"}, "MaskColor": {"type": "color", "value": "#000000"}, "MaskColorOpacity": {"type": "progress", "options": ["0", "100"], "suffix": "%", "value": "55%"}, "ColorFont": {"type": "word", "style": "notice", "hint": "hint"}}, "Carousel-2": {"ContentType": {"type": "select", "options": ["images", "local"], "linkage": {"images": ["PicPc", "PicDisplayAreaPc", "PicMobile", "PicDisplayAreaMobile", "ImageAltPc", "ImageAltMobile"], "local": ["LocalVideo"]}, "value": "images"}, "PicPc": {"type": "image", "expand": {"width": "1920", "height": "600"}, "value": "{CDN_URL_MODE}carousel/mode_1/index20.jpg"}, "ImageAltPc": {"type": "input", "value": "", "expand": {"hint": "tips"}}, "FillingMethodPc": {"type": "select", "options": ["ratio", "tiled"], "value": "tiled"}, "PicDisplayAreaPc": {"type": "select", "options": ["top", "center", "bottom"], "value": "center"}, "PicMobile": {"type": "image", "expand": {"width": "750", "height": "600"}, "value": "{CDN_URL_MODE}carousel/mode_1/index11.jpg"}, "ImageAltMobile": {"type": "input", "value": "", "expand": {"hint": "tips"}}, "FillingMethodMobile": {"type": "select", "options": ["ratio", "tiled"], "value": "tiled"}, "PicDisplayAreaMobile": {"type": "select", "options": ["top", "center", "bottom"], "value": "center"}, "LocalVideo": {"type": "video", "value": ""}, "Title": {"type": "input", "value": "Lorem ipsum dolor"}, "Content": {"type": "richtext", "value": "consectetur adipisc ing elit, sed do Eiusmod Tempor inci didunt ut labore et dolore magna.Sed ut pers piciatisunde omnis iste natus error sit voluptatem"}, "ButtonText": {"type": "input", "value": "Shop Now"}, "Link": {"type": "link", "value": ""}, "TextPosition": {"type": "select", "options": ["lefttop", "top", "righttop", "left", "center", "right", "<PERSON><PERSON><PERSON>", "bottom", "<PERSON><PERSON><PERSON>"], "value": "center"}, "TextAlign": {"type": "textalign", "options": ["left", "center", "right"], "value": "center"}, "TextContentSetMobile": {"type": "switch", "linkage": {"1": ["TitleMobile", "ContentMobile"]}, "value": false}, "TitleMobile": {"type": "input", "value": "Lorem ipsum dolor"}, "ContentMobile": {"type": "richtext", "value": "consectetur adipisc ing elit, sed do Eiusmod Tempor inci didunt ut labore et dolore magna.Sed ut pers piciatisunde omnis iste natus error sit voluptatem"}, "TitleFontSizePc": {"type": "progress", "options": ["40", "100"], "suffix": "px", "value": "48px"}, "TitleFontSizeMobile": {"type": "progress", "options": ["12", "60"], "suffix": "px", "value": "30px"}, "ContentFontSizePc": {"type": "progress", "options": ["12", "28"], "suffix": "px", "value": "16px"}, "ContentFontSizeMobile": {"type": "progress", "options": ["12", "24"], "suffix": "px", "value": "14px"}, "TitleColor": {"type": "color", "value": "#ffffff"}, "ContentColor": {"type": "color", "value": "#ffffff"}, "ButtonTextSizePc": {"type": "progress", "options": ["12", "60"], "suffix": "px", "value": "14px"}, "ButtonTextSizeMobile": {"type": "progress", "options": ["12", "30"], "suffix": "px", "value": "14px"}, "ButtonTextColor": {"type": "color", "value": "#ffffff"}, "ButtonHoverTextColor": {"type": "color", "value": "#ff830a"}, "ButtonColor": {"type": "color", "value": "#ff830a"}, "ButtonBorderColor": {"type": "color", "value": "#ff820a"}, "ButtonHoverColor": {"type": "color", "value": "#ffffff"}, "ButtonBorderHoverColor": {"type": "color", "value": "#fff"}, "ButtonRadiusSize": {"type": "progress", "options": ["0", "100"], "suffix": "px", "value": "20px"}, "ButtonHeightPc": {"type": "input", "expand": {"suffix": "px"}, "value": "40"}, "ButtonWidthPc": {"type": "input", "expand": {"suffix": "px"}, "value": "160"}, "ButtonHeightMobile": {"type": "input", "expand": {"suffix": "px"}, "value": "40"}, "ButtonWidthMobile": {"type": "input", "expand": {"suffix": "px"}, "value": "160"}, "MaskColor": {"type": "color", "value": "#000000"}, "MaskColorOpacity": {"type": "progress", "options": ["0", "100"], "suffix": "%", "value": "55%"}, "ColorFont": {"type": "word", "style": "notice", "hint": "hint"}}, "Carousel-3": {"ContentType": {"type": "select", "options": ["images", "local"], "linkage": {"images": ["PicPc", "PicDisplayAreaPc", "PicMobile", "PicDisplayAreaMobile", "ImageAltPc", "ImageAltMobile"], "local": ["LocalVideo"]}, "value": "images"}, "PicPc": {"type": "image", "expand": {"width": "1920", "height": "600"}, "value": "{CDN_URL_MODE}carousel/mode_1/index30.jpg"}, "ImageAltPc": {"type": "input", "value": "", "expand": {"hint": "tips"}}, "FillingMethodPc": {"type": "select", "options": ["ratio", "tiled"], "value": "tiled"}, "PicDisplayAreaPc": {"type": "select", "options": ["top", "center", "bottom"], "value": "center"}, "PicMobile": {"type": "image", "expand": {"width": "750", "height": "600"}, "value": "{CDN_URL_MODE}carousel/mode_1/index11.jpg"}, "ImageAltMobile": {"type": "input", "value": "", "expand": {"hint": "tips"}}, "FillingMethodMobile": {"type": "select", "options": ["ratio", "tiled"], "value": "tiled"}, "PicDisplayAreaMobile": {"type": "select", "options": ["top", "center", "bottom"], "value": "center"}, "LocalVideo": {"type": "video", "value": ""}, "Title": {"type": "input", "value": "Lorem ipsum dolor"}, "Content": {"type": "richtext", "value": "consectetur adipisc ing elit, sed do Eiusmod Tempor inci didunt ut labore et dolore magna.Sed ut pers piciatisunde omnis iste natus error sit voluptatem"}, "ButtonText": {"type": "input", "value": "Shop Now"}, "Link": {"type": "link", "value": ""}, "TextPosition": {"type": "select", "options": ["lefttop", "top", "righttop", "left", "center", "right", "<PERSON><PERSON><PERSON>", "bottom", "<PERSON><PERSON><PERSON>"], "value": "center"}, "TextAlign": {"type": "textalign", "options": ["left", "center", "right"], "value": "center"}, "TextContentSetMobile": {"type": "switch", "linkage": {"1": ["TitleMobile", "ContentMobile"]}, "value": false}, "TitleMobile": {"type": "input", "value": "Lorem ipsum dolor"}, "ContentMobile": {"type": "richtext", "value": "consectetur adipisc ing elit, sed do Eiusmod Tempor inci didunt ut labore et dolore magna.Sed ut pers piciatisunde omnis iste natus error sit voluptatem"}, "TitleFontSizePc": {"type": "progress", "options": ["40", "100"], "suffix": "px", "value": "48px"}, "TitleFontSizeMobile": {"type": "progress", "options": ["12", "60"], "suffix": "px", "value": "30px"}, "ContentFontSizePc": {"type": "progress", "options": ["12", "28"], "suffix": "px", "value": "16px"}, "ContentFontSizeMobile": {"type": "progress", "options": ["12", "24"], "suffix": "px", "value": "14px"}, "TitleColor": {"type": "color", "value": "#ffffff"}, "ContentColor": {"type": "color", "value": "#ffffff"}, "ButtonTextSizePc": {"type": "progress", "options": ["12", "60"], "suffix": "px", "value": "14px"}, "ButtonTextSizeMobile": {"type": "progress", "options": ["12", "30"], "suffix": "px", "value": "14px"}, "ButtonTextColor": {"type": "color", "value": "#ffffff"}, "ButtonHoverTextColor": {"type": "color", "value": "#ff830a"}, "ButtonColor": {"type": "color", "value": "#ff830a"}, "ButtonBorderColor": {"type": "color", "value": "#ff820a"}, "ButtonHoverColor": {"type": "color", "value": "#ffffff"}, "ButtonBorderHoverColor": {"type": "color", "value": "#fff"}, "ButtonRadiusSize": {"type": "progress", "options": ["0", "100"], "suffix": "px", "value": "20px"}, "ButtonHeightPc": {"type": "input", "expand": {"suffix": "px"}, "value": "40"}, "ButtonWidthPc": {"type": "input", "expand": {"suffix": "px"}, "value": "160"}, "ButtonHeightMobile": {"type": "input", "expand": {"suffix": "px"}, "value": "40"}, "ButtonWidthMobile": {"type": "input", "expand": {"suffix": "px"}, "value": "160"}, "MaskColor": {"type": "color", "value": "#000000"}, "MaskColorOpacity": {"type": "progress", "options": ["0", "100"], "suffix": "%", "value": "55%"}, "ColorFont": {"type": "word", "style": "notice", "hint": "hint"}}}, "Settings": {"HeightPc": {"type": "select", "options": ["firstpic", "600"], "value": "firstpic"}, "HeightMobile": {"type": "select", "options": ["firstpic", "400"], "value": "firstpic"}, "ContainerWidth": {"type": "select", "options": ["full", "standard"], "value": "full"}, "FlipType": {"type": "select", "options": ["dots", "line", "arrow", "none"], "value": "dots"}, "CarouselAuto": {"type": "switch", "value": 1}, "CarouselInterval": {"type": "progress", "options": ["3", "10"], "suffix": "s", "value": "5s"}, "Effect": {"type": "effect", "expand": {"options": [6, 7]}, "value": 6}}, "Config": {"PagePermit": ["*"], "BlocksAdd": true}, "Translation": {"Blocks": {"Carousel-{x}": ["Title", "Content", "ButtonText", "TitleMobile", "ContentMobile"]}}}