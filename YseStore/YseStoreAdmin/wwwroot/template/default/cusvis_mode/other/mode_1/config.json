{"Settings": {"ContainerWidth": {"type": "select", "options": ["full", "standard", "1200"], "value": "standard"}, "TitleFontSizePc": {"type": "progress", "options": ["12", "60"], "suffix": "px", "value": "12px"}, "TitleFontSizeMobile": {"type": "progress", "options": ["12", "60"], "suffix": "px", "value": "12px"}, "UpperSpacing": {"type": "input", "expand": {"suffix": "px"}, "value": "20"}, "UpperSpacingMobile": {"type": "input", "expand": {"suffix": "px"}, "value": "20"}, "LowerSpacing": {"type": "input", "expand": {"suffix": "px"}, "value": "0"}, "LowerSpacingMobile": {"type": "input", "expand": {"suffix": "px"}, "value": "0"}, "ColorFont": {"type": "word", "style": "notice", "hint": "hint"}}, "Config": {"PagePermit": ["*"], "PluginDescription": "view.module.type_sub.other.bread_crumbs"}}