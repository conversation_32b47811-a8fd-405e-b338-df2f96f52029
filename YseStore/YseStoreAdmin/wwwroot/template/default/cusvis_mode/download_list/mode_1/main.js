const DownloadFunc = {
	page: () => {
		// 翻页
		let timer = null
		let start = new Date()
		let limit = 1000 // 1s间隔执行一次     
		$(window).on('scroll', function () {
			if (timer) clearTimeout(timer)
			let cur = new Date()
			let scrollFunc = () => {
				let _container = $('.ly_download_list_1  .list_box .list')
				let top = $(this).scrollTop()
				let height = $(this).height()
				let scrollHeight = _container[0].scrollHeight
				let loadHeight = 120
				if (scrollHeight - (top + height) < loadHeight) {
					let _pageObj = $('#dropdown_page');
					let page = parseInt(_pageObj.attr('page'))
					let maxPage = parseInt(_pageObj.attr('max-page'))
					let mobile = parseInt(_pageObj.attr('mobile'))
					if (page < maxPage) {
						$('.dropdown_loading').loading()
						page = page + 1
						_pageObj.attr('page', page)
						let data = {'page':page}
						let url = window.location.search
						$.ajax({
							url:url,
							async:true,
							type:'get',
							data: data,
							dataType:'html',
							success: function (data) {
								$('.dropdown_loading').unloading()
								let _dataHtml = $(data)
								let _listData = _dataHtml.find('.download_list_box')
								let _itemData = _listData.find('.download_item')
								if (_itemData.length > 0) {
									_container.append(_itemData)
								}
							}
						})
					}
				}
			}
			if (cur - start >= limit) {
				scrollFunc()
				start = cur
			} else {
				timer = setTimeout(scrollFunc, 300)
			}
		})
	}
}