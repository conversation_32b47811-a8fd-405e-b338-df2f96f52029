{"Settings": {"Download": {"type": "panel", "expand": {"editlink": "/manage/plugins/download", "hint": "tips", "deletebutton": false}, "value": true}, "FileCategory": {"type": "switch", "linkage": {"1": ["CategoryTitleColor", "CategoryTitleHoverColor"]}, "value": true}, "FileCover": {"type": "switch", "value": true}, "FilePreview": {"type": "switch", "expand": {"hint": "hint"}, "value": false}, "FillingMethod": {"type": "select", "options": ["ratio", "tiled"], "linkage": {"tiled": ["PicDisplayArea"]}, "value": "tiled"}, "PicDisplayArea": {"type": "select", "options": ["top", "center", "bottom"], "value": "center"}, "TitleColor": {"type": "color", "value": "#222222"}, "CategoryTitleColor": {"type": "color", "value": "#555555"}, "CategoryTitleHoverColor": {"type": "color", "value": "#222222"}, "IconColor": {"type": "color", "value": "#222222"}, "IconHoverColor": {"type": "color", "value": "#FFFFFF"}, "ButtonBgColor": {"type": "color", "value": "#00000000"}, "ButtonBgHoverColor": {"type": "color", "value": "#222222"}, "ButtonBorderColor": {"type": "color", "value": "#222222"}, "ButtonBorderHoverColor": {"type": "color", "value": "#00000000"}, "ColumnsPc": {"type": "select", "options": ["1", "2"], "value": "1"}, "PageStyleMobile": {"type": "select", "options": ["number", "dropdown"], "value": "number"}, "UpperSpacing": {"type": "input", "expand": {"suffix": "px"}, "value": "27"}, "UpperSpacingMobile": {"type": "input", "expand": {"suffix": "px"}, "value": "20"}, "LowerSpacing": {"type": "input", "expand": {"suffix": "px"}, "value": "140"}, "LowerSpacingMobile": {"type": "input", "expand": {"suffix": "px"}, "value": "40"}, "ColorFont": {"type": "word", "style": "notice", "hint": "hint"}}, "Config": {"PagePermit": ["download"], "AllowDelete": false}}