.ly_download_list_1 .download_list_box .download_item{ display: flex; align-items: center; justify-content: space-between; padding: 16px 20px; background-color: #f4f4f4; box-sizing: border-box; }
.ly_download_list_1 .download_list_box .download_item:first-child{ margin-top: 0; }
.ly_download_list_1 .download_list_box .download_item .info_box{ display: flex; align-items: flex-start; justify-content: __left__; flex-wrap: wrap; flex: 1; }
.ly_download_list_1 .download_list_box .download_item .info_box .info{ width: calc( 100% - 160px ); }
.ly_download_list_1 .download_list_box .download_item .info_box .pic_box{ margin-__right__: 16px; height: 68px; width: 68px; position: relative; overflow: hidden; }
.ly_download_list_1 .download_list_box .download_item .info_box .pic_box img{ position: absolute; }
.ly_download_list_1 .download_list_box .download_item .info_box .tips{ display: block; font-size: 14px; color: #333333; }
.ly_download_list_1 .download_list_box .download_item .info_box .tips .size{ margin-__left__: 6px; }
.ly_download_list_1 .download_list_box .download_item .info_box .name{ display: inline-block; margin-bottom: 12px; font-size: 14px; color: #232323; word-break: break-all;}
.ly_download_list_1 .download_list_box .download_item .btn{ display: flex; align-items: center; justify-content: center; }
.ly_download_list_1 .download_list_box .download_item .btn a{ border-radius: 100%!important; border-width: 1px; border-style: solid; display: flex; vertical-align: middle; text-decoration: none; border-radius: 5px; position: relative; width: 33px; height: 33px; box-sizing: border-box; box-sizing: border-box; font-size: 14px; text-transform: uppercase; }
.ly_download_list_1 .download_list_box .download_item .btn a.download_file_view{ margin-__right__: 16px; }
.ly_download_list_1 .download_list_box .download_item .btn a i{ position: absolute; font-weight: bold; top: 50%; left: 50%; transform: translate(-50%, -50%); font-size: 18px; }
.ly_download_list_1 .download_list_box .download_item .btn a.download_file_view i{ font-weight: normal; font-size: 20px; }
.ly_download_list_1 .download_list_box #turn_page{ margin-top: 50px; }
.ly_download_list_1 .download_list_box .total_page,
.ly_download_list_1 .download_list_box #turn_page li.first,
.ly_download_list_1 .download_list_box #turn_page li.last{ display: none; }
.ly_download_list_1 .download_list_box #turn_page li:hover a{ background-color:  transparent; }
.ly_download_list_1 .download_list_box #turn_page li{ font-family: "iconfont" !important; font-size: 14px; font-style: normal; -webkit-font-smoothing: antialiased; -moz-osx-font-smoothing: grayscale; }
.ly_download_list_1 .download_list_box #turn_page li span{ display: inline-block; min-width: 24px; height: 24px; color: #727272; }
.ly_download_list_1 .download_list_box #turn_page li a{ color: #727272; }
.ly_download_list_1 .download_list_box #turn_page li.active a{ color: #000000; }

.ly_download_list_1 .download_list_box .box.hasCategory{ display: flex; align-items: flex-start; justify-content: space-between; flex-wrap: wrap; }
.ly_download_list_1 .download_list_box .box.hasCategory .category_box{ width: 18.26%; }
.ly_download_list_1 .download_list_box .box.hasCategory .category_box .list{ padding: 10px 0; }
.ly_download_list_1 .download_list_box .box.hasCategory .category_box .list .item{ display: block; position: relative; line-height: 40px; font-size: 14px; box-sizing: border-box;  }
.ly_download_list_1 .download_list_box .box.hasCategory .category_box .list .item a{ text-decoration: none; color: #555555; }
.ly_download_list_1 .download_list_box .box.hasCategory .category_box .list .item.active a{ color: #222222; }
.ly_download_list_1 .download_list_box .box.hasCategory .list_box{ width: 78.47%; }
.ly_download_list_1 .download_list_box .box .list_box .list{ padding-top: 22px; display: grid; gap: 20px;}
.ly_download_list_1 .download_list_box .box .top_title{line-height: 56px; border-bottom: 1px solid #e8e8e8; overflow: hidden; font-size: 20px; text-transform: uppercase;  }

@media screen and (max-width: 768px) {
    .ly_download_list_1{padding:0 10px;box-sizing: border-box;}
    .ly_download_list_1 .download_list_box #turn_page{ margin-top: 40px; }
    .ly_download_list_1 .download_list_box .download_item{ padding: 15px; }
    .ly_download_list_1 .download_list_box .download_item .info_box .pic_box{ margin-__right__: 8px; width: 50px; height: 50px; }
    .ly_download_list_1 .download_list_box .download_item .info_box .info{ padding-__right__: 10px; width: calc( 100% - 60px ); box-sizing: border-box; }
    .ly_download_list_1 .download_list_box .download_item .info_box .name{ margin-bottom: 10px; font-size: 14px; }
    .ly_download_list_1 .download_list_box .download_item .info_box .tips{ font-size: 14px; }
    .ly_download_list_1 .download_list_box .download_item .btn a{ width: 30px; height: 30px; font-size: 12px; border-width: 1px; }
    .ly_download_list_1 .download_list_box .download_item .btn a i{ font-size: 14px; }
    .ly_download_list_1 .download_list_box .download_item .btn a.download_file_view{ margin-__right__: 6px; }
    .ly_download_list_1 .download_list_box .download_item .btn a.download_file_view i{ font-size: 12px; }
    
    .ly_download_list_1 .download_list_box .box .list_box .list{ gap: 12px; }
    .ly_download_list_1 .download_list_box .box.hasCategory .category_box{ width: 100%; }
    .ly_download_list_1 .download_list_box .box.hasCategory .list_box{ width: 100%; }
    .ly_download_list_1 .download_list_box .box.hasCategory .top_title{ padding-__left__: 15px; padding-__right__: 40px; height: 43px; line-height: 41px; border: 1px solid #e8e8e8; font-size: 20px; position: relative; box-sizing: border-box; }
    .ly_download_list_1 .download_list_box .box.hasCategory .top_title.top_list_name{ position: relative; font-family: "iconfont" !important; font-size: 16px; font-style: normal; -webkit-font-smoothing: antialiased; -moz-osx-font-smoothing: grayscale; }
    .ly_download_list_1 .download_list_box .box.hasCategory .top_title.top_list_name:before{ content: '\e643'; position: absolute; top: 50%; transform: translateY(-50%); __right__: 14px;  }
    .ly_download_list_1 .download_list_box .box .top_title.top_category_name { display: none; }
    
    .ly_download_list_1 .download_list_box .box{ position: relative; }
    .ly_download_list_1 .download_list_box .box.hasCategory .category_box .list{ display: none; position: absolute; width: 100%; top: 48px; padding: 10px; background-color: #fff; box-sizing: border-box; z-index: 3; box-shadow: 0px 2px 9px 0 #ccc; }
    .ly_download_list_1 .download_list_box .box.hasCategory .category_box .list a{ color: #000; }
}
@media screen and (max-width: 450px) {
    .ly_download_list_1 .download_list_box .download_item .info_box .tips{ font-size: 12px; }
}