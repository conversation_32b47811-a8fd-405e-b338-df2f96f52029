{"Settings": {"ContainerWidth": {"type": "select", "options": ["full", "standard", "1200"], "value": "standard"}, "Blog": {"type": "panel", "expand": {"editlink": "/manage/plugins/blog/blog", "deletebutton": false, "hint": "tips"}, "value": 1}, "ButtonText": {"type": "input", "value": "Read More"}, "FillingMethod": {"type": "select", "options": ["ratio", "tiled"], "linkage": {"tiled": ["PicDisplayArea"]}, "value": "tiled"}, "PicDisplayArea": {"type": "select", "options": ["top", "center", "bottom"], "value": "center"}, "BriefIntroduction": {"type": "switch", "value": "0"}, "ButtonTextColor": {"type": "color", "value": "#FFFFFF"}, "ButtonHoverTextColor": {"type": "color", "value": "#FFFFFF"}, "ButtonBgColor": {"type": "color", "value": "#175cff"}, "ButtonBgHoverColor": {"type": "color", "value": "#ffcc00"}, "ButtonBorderColor": {"type": "color", "value": "#00000000"}, "ButtonBorderHoverColor": {"type": "color", "value": "#00000000"}, "ColumnsPc": {"type": "select", "options": ["1", "2", "3"], "value": "1"}, "ColumnsMobile": {"type": "select", "options": ["1", "2"], "value": "1"}, "PageStyleMobile": {"type": "select", "options": ["number", "dropdown"], "value": "number"}, "HotBlog": {"type": "switch", "linkage": {"1": ["HotBlogSelect"]}, "value": "0"}, "HotBlogSelect": {"type": "panel", "expand": {"link": "javascript:;", "hint": "tips", "IsPopSelect": true, "appType": "blog", "multi": 3, "deletebutton": false, "manageLink": "/manage/plugins/blog/blog"}, "value": true}, "Month": {"type": "switch", "value": "1"}, "Category": {"type": "switch", "value": "1"}, "UpperSpacing": {"type": "input", "expand": {"suffix": "px"}, "value": "50"}, "LowerSpacing": {"type": "input", "expand": {"suffix": "px"}, "value": "70"}, "UpperSpacingMobile": {"type": "input", "expand": {"suffix": "px"}, "value": "20"}, "LowerSpacingMobile": {"type": "input", "expand": {"suffix": "px"}, "value": "30"}, "ColorFont": {"type": "word", "style": "notice", "hint": "hint"}}, "Config": {"PagePermit": ["blog"], "AllowDelete": false}, "Translation": {"Settings": ["ButtonText"]}}