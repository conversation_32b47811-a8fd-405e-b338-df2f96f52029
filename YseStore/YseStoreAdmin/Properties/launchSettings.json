{"$schema": "http://json.schemastore.org/launchsettings.json", "iisSettings": {"windowsAuthentication": false, "anonymousAuthentication": true, "iisExpress": {"applicationUrl": "http://localhost:41034", "sslPort": 0}}, "profiles": {"http": {"commandName": "Project", "dotnetRunMessages": true, "hotReloadEnabled": true, "launchBrowser": false, "applicationUrl": "http://localhost:5236", "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Development"}}, "IIS Express": {"commandName": "IISExpress", "launchBrowser": true, "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Development"}}}}