@page "/common/mail/order_message"
@model YseStoreAdmin.Pages.Mail.Template.order_message
@{
    Layout = null;
}


<div style="width:700px; margin:10px auto;">
    <_header></_header>
    <div style="margin:40px 0;color: #333;">
        <div style="font-size: 30px;margin-bottom:20px;">@T["email_template.orderMessageTitle"]</div>
        <div style="font-size: 14px;line-height: 20px;margin-bottom:15px;">@T["email_template.orderMessageDesc"]:</div>
        <div style="font-size: 14px;padding: 20px;background-color:#f7f7f7;border-radius: 5px;line-height: 22px;margin-bottom:15px;">{{NewOrderMessage}}</div>
        <a href="{{FullDomain}}/account/orders/contact{{OrderNum}}.html?JumpUrl=/account/orders/contact{{OrderNum}}.html" style="display: inline-block;padding:10px 45px;background-color:#0baf4d;color:#fff;border-radius: 50px;text-decoration: none;">@T["email_template.userInboxReply"]</a>
        <div style="margin:15px 0;">@T["email_template.userInboxRecord"]:</div>
        <div style="font-size:12px;">{{RecentOrderMessage}}</div>
    </div>
    <_footer></_footer>
</div>
