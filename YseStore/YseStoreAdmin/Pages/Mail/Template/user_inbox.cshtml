@page "/common/mail/user_inbox"
@model YseStoreAdmin.Pages.Mail.Template.user_inbox
@{
    Layout = null;

    // 会员站内信
    string replyUrl = "{{Domain}}/manage/user/inbox";
    string tplTitle = T["email_template.inboxMessageTitle"];
    string inboxInfoTxt = T["email_template.userInboxInfo"];

    if (Model.messageType == "orders")
    {
        replyUrl = $"{{Domain}}/manage/orders/orders?Keyword={Model.OId}";
        tplTitle = T["email_template.orderMessageTitle"];
        inboxInfoTxt = T["email_template.userOrdersInfo"];
    }
}
<div style="width:700px; margin:0 auto; background-color:#fff;">
    <div style="width:640px; padding:0 30px;">
        <_header></_header>
        <div style="padding:20px 0">
            <div style="height:75px; line-height:75px; font-size:26px; font-weight:bold; color:#000; font-family: Microsoft YaHei;">@tplTitle</div>
            <div style="line-height:30px; padding:11px 0; font-size:14px; color:#333;">@inboxInfoTxt</div>
            <div style="margin:10px 0; line-height:34px; background-color:#dff2ff; border-radius:6px; padding:27px 30px 35px; color:#000;">{{InboxMessage}}{{InboxPicture}}</div>
            <div style="padding:20px 0;">
                <a href="@replyUrl" style="display:block; width:150px; height:36px; line-height:36px; background-color:#4c8df1; border-radius:18px; text-align:center; text-decoration:none; font-size:14px; color:#fff;">
                    @T["email_template.userInboxReply"]
                </a>
            </div>
        </div>
        <div style="margin:0px auto; clear:both; height:19px; font-size:0px; overflow:hidden;"></div>

        <user_inbox_comp params="new {messageType=Model.messageType,OId= Model.OId,UserId=Model.UserId}"></user_inbox_comp>

    </div>
</div>