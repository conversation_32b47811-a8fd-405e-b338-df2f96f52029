@page "/common/mail/arrival_notice"
@model YseStoreAdmin.Pages.Mail.Template.arrival_notice
@{
    Layout = null;
}

<div style="width:700px;padding:0 30px;margin:0 auto;box-sizing:border-box;">

    <_header></_header>

    <div style="padding:24px 0 45px;">
        <div style="line-height:24px;font-size:13px;color:#555555;">@T["email_template.arrivalTitle"] </div>
        <div style="margin-top:43px;padding:50px;text-align:center;background-color:#f5f7f6;">
            <p style="line-height:24px;padding-top:14px;font-size:16px;color:#333333;">{{ProductName}}</p>
            <p style="line-height:21px;padding-top:10px;font-size:12px;color:#878988;">{{ProductVariants}}</p>
            <div style="width:250px;height:250px;text-align:center;margin:35px auto;background-color:#ffffff;">
                <img src="{{ProductPicture}}" alt="{{ProductName}}" style="max-width:100%;max-height:100%;" />
            </div>
            <a href="{{ProductUrl}}" style="font-family:Arial;display:block;width:60%;font-size:24px;color:#ffffff;background-color:#e53935;text-align:center;text-decoration:unset;line-height:1.3;padding:22px 0;border-radius:4px;margin:3px auto 0;min-height:70px;box-sizing:border-box;">@T["email_template.buyItNow"] </a>
            <div style="text-align: center;font-size:0;margin-top:27px;">
                <span style="font-family:Arial; color:#0d0d0d;display:inline-block;vertical-align:middle;font-size:16px;line-height:1.3;padding-right:5px;">or</span> <a href="{{FullDomain}}" style="font-family:Arial;display:inline-block;vertical-align:middle;line-height:1.3;color:#008cfb;text-decoration:unset;font-size:16px;padding-left:2px;">@T["email_template.visitOurStore"]  </a>
            </div>
        </div>
    </div>

    <_footer></_footer>
</div>