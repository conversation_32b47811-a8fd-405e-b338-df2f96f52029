@page "/common/mail/shopping_cart_recall"
@model YseStoreAdmin.Pages.Mail.Template.shopping_cart_recall
@{
    Layout = null;
}
<div style="width:100%; background-color:#f3f3f3; padding:30px 0;">
    <div style="width:550px; background-color:#fff; margin:0 auto; padding:0 40px 40px; font-family:Calibri;">
        <div style="height:110px; text-align:center;">
            <a href="javascript:;">{{Logo}}<span style="display:inline-block; vertical-align:middle; height:100%;"></span></a>
        </div>
        <div style="line-height:60px; margin-top:15px;text-align:center;">
            <strong style="font-size:36px; font-weight:bold; color:#333;">@T["email_template.recall_0"]</strong>
        </div>
        <div style="line-height:42px;">
            @if (Model.IsType == "IsItem")
            {
                <span style="margin-top:18px; font-size:28px; font-weight:bold; color:#e63837;">@T["email_template.bigSale"]</span>
            }
            else
            {
                <span style="text-align:center;margin-bottom:20px;margin-top:15px;line-height:normal;">{{DiscountText}}</span>
            }
            <div style="font-size:22px; color:#888;text-align:center;">@T["email_template.recall_1"]</div>
        </div>
        {{ProductsList}}
    </div>
</div>