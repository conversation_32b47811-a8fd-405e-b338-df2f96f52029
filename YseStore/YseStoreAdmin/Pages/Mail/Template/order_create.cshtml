@page "/common/mail/order_create"
@model YseStoreAdmin.Pages.Mail.Template.order_create
@{
	Layout = null;
}

<div style="width:700px; margin:0 auto; background-color:#fff;">
	<div style="width:640px; padding:0 30px;">
		<_header></_header>
		<div style="padding:20px 0">
			<div style="height:75px; line-height:75px; font-size:35px; font-weight:bold; color:#000;">@T["email_template.orderCreateTitle"]</div>
			<div style="display: flex; padding:15px 0;line-height:22px; font-size:14px;">
				<div style="width: 15%;">@T["email_template.oid"]:</div>
				<div>{{OrderNum}}</div>
			</div>
			<div style="display: flex; padding:15px 0;line-height:22px; font-size:14px;">
				<div style="width: 15%;">@T["email_template.orderAmount"]:</div>
				<div>{{OrderPrice}}</div>
			</div>
			<div style="display: flex; padding:15px 0;line-height:22px; font-size:14px;">
				<div style="width: 15%;">@T["email_template.paymentStatus"]:</div>
				<div>{{PaymentStatus}}</div>
			</div>
			<div style="display: flex; padding:15px 0;line-height:22px; font-size:14px;">
				<div style="width: 15%;">@T["email_template.paymentTime"]:</div>
				<div>{{PayTime}}</div>
			</div>
			<div style="padding:18px 0 27px;"><a href="{{OrderManageUrl}}" style="display:block; width:150px; height:36px; line-height:36px; background-color:#4c8df1; border-radius:18px; text-align:center; text-decoration:none; font-size:14px; color:#fff;">@T["email_template.orderCreateAction"]</a></div>
		</div>

		{{ProductsList}}

	</div>
</div>
