@model YseStoreAdmin.Pages.Mail.Template.Comp.user_inbox_comp
@{
}

<div>

    <div style="border-bottom:1px #e6e6e6 solid; padding-bottom:50px;">
        <div style="height:75px; line-height:75px; font-size:14px; color:#000;">
            @T["email_template.userInboxRecord"]:
        </div>
        @foreach (var item in Model.msgList)
        {
            <div style="margin-bottom:10px;">
                <div style="height:31px; line-height:31px; font-size:12px; color:#9b9b9b;@(item.Type==true?"text-align:right;":"")">
                    @(item.Type == true ? "admin" : Model.UserName)
                    @DateTimeHelper.ConvertUnixTimestampToLocalTime(item.AccTime.Value)
                </div>
                <div style="line-height:24px; padding:15px 20px; background-color:#@(item.Type==true?"ececec":"dff2ff"); border-radius:6px;@(item.Type==true?" float:right;":" float:left;") font-size:12px; color:#000;">
                    @item.Content
                    @if (!item.PicPath.IsNullOrEmpty())
                    {
                        <div style="width:300px; height:300px; margin-top:13px;"><img src="{{Domain}}@item.PicPath" style="max-width:100%; max-height:100%;" /></div>
                    }
                </div>
                <div style="margin:0px auto; clear:both; height:20px; font-size:0px; overflow:hidden;"></div>
            </div>
        }
    </div>
</div>