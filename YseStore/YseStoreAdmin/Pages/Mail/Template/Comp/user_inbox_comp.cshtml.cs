using Entitys;
using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using YseStore.IService;
using YseStore.IService.User;

namespace YseStoreAdmin.Pages.Mail.Template.Comp
{
    public class user_inbox_comp : MComponent
    {
        private readonly IUserMessageServices _userMessageServices;
        private readonly IUserService _userService;
        public user_inbox_comp(IUserMessageServices userMessageServices,IUserService userService)
        {
            _userMessageServices = userMessageServices;
            _userService = userService;


        }

        [Parameter]
        public string messageType { get; set; } = "orders";

        [Parameter]
        public string OId { get; set; } = string.Empty;

        [Parameter]
        public int UserId { get; set; } = 0;


        public user user { get; set; }= new user(); 

        public List<user_message> msgList { get; set; } = new List<user_message>();

        public string UserName { get; set; }

        /// <summary>
        /// 
        /// </summary>
        /// <returns></returns>
        public override async Task MountAsync()
        {
            await base.MountAsync();

            //用户
            user = await _userService.QueryByClauseAsync(it => it.UserId == UserId);
            if (user != null)
            {
                UserName = $"{user.FirstName} {user.LastName}";
            }


            msgList= await _userMessageServices.Query(it=>it.UserId == UserId && it.Module==messageType);

        }
    }
}
