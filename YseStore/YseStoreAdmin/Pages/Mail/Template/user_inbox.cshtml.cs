using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;

namespace YseStoreAdmin.Pages.Mail.Template
{
    /// <summary>
    /// 站内信
    /// </summary>
    [AllowAnonymous]
    public class user_inbox : PageModel
    {

        /**
 * 获取站内信通知的邮件标题和邮件内容 或 邮件内容
 *
 * @param array $data			源数据，可传入:
 * 								● messageType	站内信类型
 * 								● UserId	会员Id
 * 								● messageRow	站内信数据
 * 								● messageTxt	站内信内容
 * 								● messagePicture	站内信图片
 * 								● OId	订单Id
 * 								● userRow 会员数据
 * @param integer $preview		是否预览
 * @param string $language		邮件内容
 * @return array|string			邮件标题和邮件内容 | 邮件内容
 */

        public string messageType { get; set; } = "orders";
        public string OId { get; set; } = string.Empty;
        public int UserId { get; set; } = 0;
        //public string messageRow { get; set; } = string.Empty;
        //public string userRow { get; set; } = string.Empty;
        public void OnGet(string messageType, string OId, int UserId)
        {
            this.messageType = messageType ?? "orders";
            this.OId = OId;
            this.UserId = UserId;
    
    
        }

    }
}
