@page "/common/mail/order_refund"
@model YseStoreAdmin.Pages.Mail.Template.order_refund
@{
    Layout = null;
}
<div style="width:700px; margin: 10px auto;box-sizing: border-box;padding:50px 30px;">
    <table width="700" border="0" cellspacing="0" cellpadding="0" style="border:0;">
        <tr style="border:0px;border-color:transparent;">
            <td width="350" style="padding-bottom:40px;border:0;border-bottom:1px solid #e6e6e6"><a href="{{FullDomain}}" target="_blank">{{Logo}}</a></td>
        </tr>
    </table>
    <div style="margin-top:40px;line-height:40px;font-size:36px;font-weight:bold;font-family:Arial;">Some items in your order have been refunded.</div>
    <div style="margin-top:30px;font-size:16px;color:#666;">Order #{{OrderNum}}</div>
    <div style="margin-top:30px;font-size:16px;color:#666;">Total amout refunded: <b style="font-size:20px;font-weight:bold;color:#222222;font-family:Arial;">{{OrderRefundPrice}}</b> ({{OrderFefundMethod}})</div>

    <div style="margin:65px 0 80px;">
        {{OrderRefundDetail}}
    </div>
    <span style="display:block;font-family:Arial;font-size:12px;line-height:22px;color:#666666;padding:15px 0;"> Yours sincerely,<br> {{Domain}} Customer Care Team</span>
    <span style="display:block;font-family:Arial;font-size:12px;line-height:22px;color:#666666;padding:15px 0;border-top:solid 1px #e6e6e6;">
        You have received this email because you are a registered member of the {{Domain}} website.<br>
        For further information, log in to your account at: <a href="{{FullDomain}}" target="_blank" style="color:#1e5494;text-decoration: unset;">{{Domain}}</a><br> and submit your request or use live chat.
    </span>
</div>


