@page "/manage/user/user/add-user-form"
@model YseStoreAdmin.Pages.Customer.Partial.AddUserFormModel
@{
}
<div id="user_add_form">
	<form id="user_edit_form">
		<div class="global_form">
			<div class="blank20"></div>
			<div class="rows clean">
				<label>客户类型</label>
				<div class="input">
					<div class="blank10"></div>
					<div class="box_type_menu">
						<span class="item checked">
							<input type="radio" name="user_type" value="member" checked="checked">
							会员
						</span>
						<span class="item">
							<input type="radio" name="user_type" value="tourists">
							游客
						</span>
					</div>
				</div>
			</div>
			<div class="rows clean">
				<label>名</label>
				<div class="input">
					<input type="text" id="FirstName" class="box_input" name="FirstName" value="" size="30" maxlength="100">
				</div>
			</div>
			<div class="rows clean">
				<label>姓</label>
				<div class="input">
					<input type="text" id="LastName" class="box_input" name="LastName" value="" size="30" maxlength="100">
				</div>
			</div>
			<div class="rows clean">
				<label>邮箱</label>
				<div class="input">
					<input type="text" id="Email" class="box_input" name="Email" value="" size="40" maxlength="100" notnull="">
				</div>
			</div>
			<div class="rows clean tourists_hide" style="display: block;">
				<label>密码</label>
				<div class="input">
					<input type="password" id="Password" class="box_input" name="Password" value="" size="40" notnull="notnull">
				</div>
			</div>
			<div class="rows clean tourists_hide" style="display: block;">
				<label>确认密码</label>
				<div class="input">
					<input type="password" id="Password2" class="box_input" name="Password2" value="" size="40" notnull="notnull">
				</div>
			</div>
			<div class="rows clean tourists_show hide" style="display: none;">
				<div class="input">
					<span class="input_checkbox_box">
						<span class="input_checkbox">
							<input type="checkbox" name="ShowPop" value="1">
						</span>
						发送会员邀请到此客户的邮箱
					</span>
				</div>
			</div>
		</div>
		<div class="rows clean fixed_btn_submit">
			<label></label>
			<div class="input input_button">
				<input type="hidden" name="do_action" value="/api/CustomerList/SaveCustomerList">
				<input type="submit" class="btn_global btn_submit" value="保存">			
				<input type="button" class="btn_global btn_cancel" value="返回">
			</div>
		</div>
	</form>
</div>