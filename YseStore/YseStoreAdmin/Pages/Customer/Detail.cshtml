@page
@model YseStoreAdmin.Pages.Customer.DetailModel
@{
}

@section Styles {
    <link href="/assets/css/user_level.css" rel="stylesheet" >
    <link href="/assets/css/user.css" rel="stylesheet" >
       
}

@Html.AntiForgeryToken()
<customer-detail />
@section Scripts {
    <script src="/assets/js/plugin/daterangepicker/moment.min.js" ></script>
    <script src="/assets/js/plugin/daterangepicker/daterangepicker.js" ></script>
    <script src="/assets/js/app/user_level.js" ></script>
    <script src="/assets/js/member.js?v=20250714"></script>
        
    <script>
               jQuery(function ($) {
        $(document).ready(function(){user_obj.user_base_edit_init()});
        $(document).ready(() => { user_level_obj.user_view_init() })
        jQuery('#edit_form').yiiActiveForm([], []);
        jQuery('#user_invite_form').yiiActiveForm([], []);
        jQuery('#password_edit').yiiActiveForm([], []);
        });
    </script>

   
}


