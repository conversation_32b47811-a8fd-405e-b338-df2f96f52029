@page
@{
    Layout = "_LayoutBase";
}
@model YseStoreAdmin.Pages.Account.LoginModel
@inject Microsoft.Extensions.Configuration.IConfiguration Configuration
@{
}
<div id="login" style="margin-top: 291.5px;">
    <div class="lbar">
        <h1><img src="/assets/images/account/login_logo.png"></h1>
    
    </div>
    <div class="rbar">
        <form id="login-form" method="post">
            <input type="hidden" name="_csrf-manage" value="C3rC-zQZhJxoaRtbCCrlFljOMx7jogI6D--AJVQTJj5-CfqXcnrWqiEZXAxFSaElN50HaajINGtFrudRM1VEUg==">
            @Html.AntiForgeryToken()
            <input type="hidden" id="captchaVerifyParam" name="CaptchaVerifyParam" value="">
            <h2><img src="/assets/images/account/login_logo2.png"></h2>
            <div class="wel_txt"></div>
            <div class="main">
                <div class="input username">
                    <div class="form-group field-UserName required has-success">
                        <label class="control-label" for="UserName"><span></span></label>
                        <input type="text" id="UserName" class="form-control" name="UserName" placeholder="用户名" aria-required="true" aria-invalid="false">

                        <p class="help-block help-block-error"></p>
                    </div>
                </div>
                <div class="input password">
                    <div class="form-group field-Password required has-success">
                        <label class="control-label" for="Password"><span></span></label>
                        <input type="password" id="Password" class="form-control" name="Password" placeholder="密码" aria-required="true" aria-invalid="false">

                        <p class="help-block help-block-error"></p>
                    </div>
                </div>
                
                <!-- 验证码容器 -->
                <div id="captcha-element"></div>
                
                <!-- 错误消息显示 -->
                @if (!string.IsNullOrEmpty(TempData["ErrorMessage"] as string))
                {
                    <div class="tips error">@TempData["ErrorMessage"]</div>
                }
                else
                {
                    <div class="tips"></div>
                }
                
                <input type="button" id="login-button" class="submit" value="登 录">
            </div>
        </form>
    </div>
    <div class="clear"></div>
</div>

@section Scripts {
    <!-- 引入阿里云验证码2.0 SDK -->
    <script src="/businessJs/AliyunCaptcha.js"></script>
    @* <script type="text/javascript" src="https://o.alicdn.com/captcha-frontend/aliyunCaptcha/AliyunCaptcha.js"></script> *@
    
    <!-- 使用ES模块格式导入我们的脚本 -->
    <script type="module">
        // 导入验证码服务和API
        import * as CaptchaService from '/businessJs/captcha-service.js';
        import { verifyCaptcha } from "/businessJs/captcha-api.js";
        
        // 验证码实例
        let captcha = null;
        
        // 当文档就绪时初始化验证码
        $(document).ready(function() {
            console.log("开始初始化验证码...");
            
            // 准备验证码配置
            const captchaConfig = {
                SceneId: "@Configuration["AliCloudCaptcha:SceneId"]",
                prefix: "@Configuration["AliCloudCaptcha:Prefix"]",
                region: "@Configuration["AliCloudCaptcha:region"]",
                language: "@Configuration["AliCloudCaptcha:Language"]"
            };
            
            console.log("验证码配置:", captchaConfig);
            
            // 设置验证码配置
            CaptchaService.setConfig({
                region: captchaConfig.region,
                prefix: captchaConfig.prefix
            });
            
            // 初始化验证码
            CaptchaService.init({
                // 场景ID，从验证码控制台获取
                SceneId: captchaConfig.SceneId,
                // 验证码模式，popup表示弹出式
                mode: "popup",
                // 验证码渲染的元素
                element: "#captcha-element",
                // 触发验证码弹窗的元素
                button: "#login-button",
                // 业务验证回调
                captchaVerifyCallback: captchaVerifyCallback,
                // 业务结果回调
                onBizResultCallback: onBizResultCallback,
                // 滑块验证码样式
                slideStyle: {
                    width: 360,
                    height: 40,
                },
                // 验证码语言
                language: captchaConfig.language,
                // 错误处理配置
                errorTips: {
                    // 重试按钮文本
                    maskButtonText: "重试",
                    // 允许重试
                    allowRetry: true,
                    // 验证失败提示
                    verifyFailed: "验证失败，请重试",
                    // 网络错误提示
                    networkError: "网络错误，请重试",
                    // 点击重试按钮后关闭错误提示框
                    closeMaskOnClick: true
                }
            }).then(instance => {
                captcha = instance;
                console.log("验证码初始化完成");
            });
            
            // 阻止表单默认提交，由验证码成功后触发
            $("#login-form").on("submit", function(e) {
                if (!$("#captchaVerifyParam").val()) {
                    console.log("表单提交被拦截，等待验证码验证");
                    e.preventDefault();
                    // 如果有验证码实例，触发验证
                    if (captcha) {
                        captcha.verify();
                    }
                    return false;
                }
                return true;
            });
        });
        
        // 验证码验证回调
        async function captchaVerifyCallback(captchaVerifyParam) {
            console.log("验证码验证回调被调用，参数:", captchaVerifyParam);
            
            try {
                // 保存验证参数但不立即提交表单
                $("#captchaVerifyParam").val(captchaVerifyParam);
                
                // 使用验证码API验证验证码
                console.log("调用verifyCaptcha API...");
                const result = await verifyCaptcha(captchaVerifyParam);
                console.log("后端验证结果:", result);
                
                // 检查响应格式是否符合预期
                if (result === undefined || result === null) {
                    console.error("API返回空响应");
                    return {
                        captchaResult: false,
                        bizResult: false,
                        message: "服务器响应无效"
                    };
                }
                
                // 根据验证结果决定是否关闭验证框
                const response = {
                    captchaResult: Boolean(result.success),
                    bizResult: Boolean(result.success),
                    message: result.success ? undefined : (result.message || "验证失败，请重试")
                };
                
                console.log("返回验证结果:", response);
                return response;
            } catch (error) {
                console.error("验证过程中发生错误:", error);
                // 显示错误信息
                $(".tips").addClass("error").text(`验证错误: ${error.message}`);
                
                return {
                    captchaResult: false,
                    bizResult: false,
                    message: "验证过程中发生错误，请重试: " + error.message
                };
            }
        }
        
        // 业务结果回调
        function onBizResultCallback(bizResult) {
            console.log("业务结果回调:", bizResult);
            
            if (bizResult === true) {
                console.log("验证通过，提交表单");
                // 提交表单
                $("#login-form").submit();
            } else {
                console.log("验证未通过");
                // 显示错误消息
                $(".tips").addClass("error").text("验证未通过，请重试");
            }
        }
    </script>
}