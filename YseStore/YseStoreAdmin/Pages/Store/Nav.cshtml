@page
@model YseStoreAdmin.Pages.Store.NavModel
@{
}

@section Styles {

    <link href="/assets/css/view.css" rel="stylesheet">
}

<store-nav/>



@section Scripts {

    <script src="/assets/js/view.js"></script>
    <script src="/businessJs/Common/ApiMonitor.js"></script>
    <script>
        jQuery(function ($) {
            $(document).ready(function () {
                view_obj.nav_init();

                // 初始化API监控器，监控导航菜单创建接口和编辑接口
                ApiMonitor.init({
                    monitoredApis: ['/manage/view/nav/create', '/manage/view/nav/edit'],
                    redirectUrl: '', // 空字符串表示刷新当前页面
                    successCode: 1,
                    successCallback: function (data) {
                        // 延迟1秒后刷新页面
                        setTimeout(function () {
                            window.location.reload();
                        }, 1000);
                        return false; // 返回false阻止默认的重定向行为
                    }
                });
            });
            jQuery('#create_nav_form').yiiActiveForm([], []);
        });
    </script>
}