@page
@model YseStoreAdmin.Pages.Store.VisualEditModel
@{
}

@section Styles {
    <link href="/assets/css/view-v2.css" rel="stylesheet">
    <link href="/assets/js/plugin/pickr/nano.min.css" rel="stylesheet">
}
<visual-edit />
@section Scripts {
    <script type="text/javascript">$(document).ready(function(){if (window.frames.name) {let customEvent = new CustomEvent('pageReady' , {detail : {iframeUrl : '/?editMode=1&visualVersion=2&DraftsId=350&client=website',DraftsId : '350',PagesId : '1532',Module : 'index',Action : 'index',}});let customInterval = 0;let customEventFunc = () => {parent.window.dispatchEvent(customEvent);};customInterval = setInterval(customEventFunc, 1000);window.addEventListener('pageReadySuccess', function (event) {clearInterval(customInterval);});}})</script>
    <script src="/assets/js/plugin/pickr/pickr.es5.min.js" ></script>
    <script src="/assets/js/plugin/ckeditor/ckeditor.js" ></script>
    <script src="/assets/js/plugin/daterangepicker/moment.min.js" ></script>
    <script src="/assets/js/plugin/daterangepicker/daterangepicker.js" ></script>
    <script src="/assets/js/view-v2.js" ></script>
    <script>
                jQuery(function ($) {
        $(function(){view_obj.visual_init();})
        jQuery('#form_visual').yiiActiveForm([], []);
        });
    </script>
    
}