@page
@model YseStoreAdmin.Pages.Store.SinglePageEditModel
@{
}
@section Styles {
    <link href="/assets/css/view.css" rel="stylesheet" >
}

<store-page-edit />


@section Scripts {


	<script src="/assets/js/plugin/tinymce/js/tinymce/tinymces.min.js" ></script>
	<script src="/assets/js/view.js" ></script>
	<script src="/businessJs/Pop-ups/frame-message.js" ></script>
	<!-- 富文本编辑器图片上传修复脚本 -->
	<script src="/businessJs/Products/EditorImageUpload/editorImageHandler.js" ></script>
	<script src="/businessJs/Products/EditorImageUpload/fileUploadHandler.js" ></script>
	<script src="/businessJs/Products/EditorImageUpload/photoFormHandler.js" ></script>
	<!-- 引入tinymce图片上传修复脚本 -->
	<script src="/businessJs/Products/EditorImageUpload/tinymceImageUploadFix.js" ></script>
    <script>
				  jQuery(function ($) {
		frame_obj.tinymceEditorInit('Content_en',{
			min_height: 500,
			max_height: 500,
			plugins: `template advtemplate  fullscreen table print preview searchreplace autolink directionality visualblocks visualchars localimg_Content_en image link anchor media code advlist lists textpattern autosave autoresize paste`,
			templates: [
				{title: '上文下图', description: '上文下图', url: '/manage/web/assets/template/product_temp1.html'}
				,{title: '上图下文', description: '上图下文', url: '/manage/web/assets/template/product_temp2.html'}
				,{title: '左文右图', description: '左文右图', url: '/manage/web/assets/template/product_temp3.html'}
				,{title: '左图右文', description: '左图右文', url: '/manage/web/assets/template/product_temp4.html'}
				,{title: '一行双图', description: '一行双图', url: '/manage/web/assets/template/product_temp5.html'}
				,{title: '一行三图文', description: '一行三图文', url: '/manage/web/assets/template/product_temp6.html'}
				,{title: '轮播带按钮', description: '轮播带按钮', url: '/manage/web/assets/template/product_temp7.html'}
				,{title: '轮播展示', description: '轮播展示', url: '/manage/web/assets/template/product_temp8.html'}
			],
			toolbar: 'code undo redo | fullscreen | table localimg_Content_en media| formatselect fontselect fontsizeselect lineheight | forecolor backcolor | bold italic underline strikethrough| link anchor removeformat | bullist numlist | alignleft aligncenter alignright alignjustify outdent indent  | cut copy paste pastetext| template'


		});
		frame_obj.tinymceEditorInit('MobileDescription',{
			min_height: 500,
			max_height: 500,
			plugins: `template advtemplate  fullscreen table print preview searchreplace autolink directionality visualblocks visualchars localimg_MobileDescription image link anchor media code advlist lists textpattern autosave autoresize paste`,
			templates: [
				{title: '上文下图', description: '上文下图', url: '/manage/web/assets/template/product_temp1.html'}
				,{title: '上图下文', description: '上图下文', url: '/manage/web/assets/template/product_temp2.html'}
				,{title: '左文右图', description: '左文右图', url: '/manage/web/assets/template/product_temp3.html'}
				,{title: '左图右文', description: '左图右文', url: '/manage/web/assets/template/product_temp4.html'}
				,{title: '一行双图', description: '一行双图', url: '/manage/web/assets/template/product_temp5.html'}
				,{title: '一行三图文', description: '一行三图文', url: '/manage/web/assets/template/product_temp6.html'}
				,{title: '轮播带按钮', description: '轮播带按钮', url: '/manage/web/assets/template/product_temp7.html'}
				,{title: '轮播展示', description: '轮播展示', url: '/manage/web/assets/template/product_temp8.html'}
			],
			toolbar: 'code undo redo | fullscreen | table localimg_MobileDescription media| formatselect fontselect fontsizeselect lineheight | forecolor backcolor | bold italic underline strikethrough| link anchor removeformat | bullist numlist | alignleft aligncenter alignright alignjustify outdent indent  | cut copy paste pastetext| template'



		});
		$(document).ready(function(){
		view_obj.page_edit_init()
		});
		jQuery('#edit_form').yiiActiveForm([], []);
		});
    </script>


}