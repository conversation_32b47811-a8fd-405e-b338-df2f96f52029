using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;

namespace YseStoreAdmin.Pages
{
 
    public class IndexModel : PageModel
    {
        private readonly ILogger<IndexModel> _logger;

        public IndexModel(ILogger<IndexModel> logger)
        {
            _logger = logger;
        }

        //public IActionResult OnGet()
        //{
        //    //return 
        //   //return Redirect("/CodeEdit/EditIndex");
        //}
    }
}
