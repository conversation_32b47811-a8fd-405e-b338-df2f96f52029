using Entitys;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using YseStore.IService.Customer;
using YseStore.IService.Products;

namespace YseStoreAdmin.Pages.Components.Customer
{
    public class CustomerDetail : MComponent
    {
        public ICustomerListService _customerListService { get; }
        public UserResponse UserModel { get; set; }
        public List<user_tags>? User_tagsList { get; set; }
        public List<user_label_collection>? UserLabelCollection { get; set; }
        public List<user_remark_log>? UserRemarkLog { get; set; }
        public List<UserAddressBookResponse>? UserAddressBook { get; set; }
        public products_review_reply ProductsReviewReply { get; set; }

        public List<UserShoppingCartResponse>? ShoppingCart { get; set; }
        public List<UserFavoriteResponse>? FavoriteProducts { get; set; }
        public List<UserOrdersResponse>? UserOrdersProductsList { get; set; }

        public int UserOrdersProductCount { get; set; }
        public decimal OrderSum { get; set; }
        public string OrderSymbol { get; set; }
        public CustomerDetail(ICustomerListService customerListService)
        {
            _customerListService = customerListService;
        }
        public override async Task MountAsync()
        {
            var id = HttpContext.Request.Query["UserId"].ToString();
            var userid = Convert.ToInt32(id);
            UserModel = await _customerListService.GetUserDetailAsync(userid);
            User_tagsList = await _customerListService.GetUser_Tags();
            UserLabelCollection= await _customerListService.GetUserLabelCollectionById(userid);
            UserRemarkLog = await _customerListService.GetUserRemarkLogById(userid);
            UserAddressBook = await _customerListService.GetUserAddressBook(userid);
            ShoppingCart = await _customerListService.GetShoppingCart(userid);
            FavoriteProducts= await _customerListService.GetFavoriteProducts(userid);
            UserOrdersProductsList = await _customerListService.GetUserOrdersProductsList(userid);
            UserOrdersProductCount = await _customerListService.GetUserOrdersProductsCount(userid);
           var  UserOrdersSum = await _customerListService.GetUserOrdersSum(userid);
            OrderSum= UserOrdersSum.Item1;
            OrderSymbol = UserOrdersSum.Item2;
        }
    }
}
