@model YseStoreAdmin.Pages.Components.Customer.FormToolData
@{
}
<div>
    <div id="form_tool" class="r_con_wrap plugins_app_box form_tool_data" style="height: 482px;">
        <div class="return_title new_return_title">
            <a href="/Customer/FormTool">
                <span class="return">客户</span>
                <span class="s_return"> / 表单工具</span><span class="s_return"> / 数据</span>
            </a>
        </div>
        <div class="inside_container plugins_app_small_title"><div class="app_icon icon_item_form_tool"></div><h1>表单工具</h1></div>
        <div class="inside_table">
            <div class="list_menu">
                <ul class="list_menu_button fr">
                    <li><a href="javascript:;" class="btn_set_fielId add">字段设置</a></li>
                </ul>
            </div>

            <form-tool-data-table params="new {Id=Model.Id}"></form-tool-data-table>

            @if (Model.PagerOptions.TotalCount > 0)
            {
                <mx-pager name="manlog" total="@Model.PagerOptions.TotalCount" page-size="@Model.PagerOptions.PageSize" page-num="@Model.PagerOptions.TotalPages" />
            }
        </div>
    </div>

    <div id="fixed_right">
        <div class="global_container fixed_field_sort" data-width="336">
            <div class="top_title">字段设置 <a href="javascript:;" class="close"></a></div>
            <form class="global_form" id="field_sort_form">
                <div class="rows clean">
                    <div class="input">
                        <div class="field_list_wrapper">
                            @foreach (var item in Model.FieldList)
                            {
                                <div class="set_field_item">
                                    <em></em>
                                    <span class="field_name">@item.Name</span>
                                    <input type="hidden" name="positionFieldId[]" value="@item.FieldId">
                                    <span class="input_checkbox_box @(item.IsShow==true?"checked":"")">
                                        <span class="input_checkbox">
                                            <input type="checkbox" name="IsShow[@item.FieldId]" value="1" @(item.IsShow == true ? "checked" : "")>
                                        </span>
                                    </span>

                                </div>
                            }

                        </div>
                    </div>
                </div>
                <div class="rows clean box_button box_submit">
                    <div class="input">
                        <input type="submit" class="btn_global btn_submit" value="保存">
                        <input type="button" class="btn_global btn_cancel" value="取消">
                    </div>
                </div>
                <input type="hidden" name="do_action" value="/manage/plugins/form-tool/sort-field/">
            </form>
        </div>
    </div>

</div>