@using YseStore.Common
@model YseStoreAdmin.Pages.Components.Customer.FormToolEdit

@{
}
<div>
    <style>
        .link_box {
            display: flex;
        }

            .link_box .link {
                flex: 1;
                height: 38px;
                line-height: 38px;
                padding: 0 10px;
                border-radius: 5px;
                border: 1px solid var(--GlobalBorderColor);
                color: var(--GlobalAssistColor);
                font-size: 12px;
            }

            .link_box .copy {
                height: 40px;
                line-height: 40px;
                color: #fff;
                background-color: var(--GlobalMainColor);
                border-radius: 5px;
                font-size: 14px;
                padding: 0 20px;
                margin-left: 8px;
                border: none;
            }
    </style>
    <div class="r_con_wrap form_tool_edit" style="height: 430px;">
        <div class="center_container">
            <div class="return_title">
                <a href="/Customer/FormTool">
                    <span class="return">表单工具</span>
                    <span class="s_return">/ 修改</span>
                </a>
            </div>
            <form id="edit_form" class="field_edit_form global_form">
                <div class="global_container">
                    <div class="rows clean">
                        <label>表单名称</label>
                        <div class="input">
                            @{
                                var title = Model.formTool.Name;
                                if (string.IsNullOrEmpty(title))
                                {
                                    title = Model.defaultFormData.Name;
                                }
                            }
                            <input name="Title" value="@title" type="text" class="box_input full_input" size="42"
                                   notnull="">
                        </div>

                    </div>
                </div>
                @if (Model.formTool.FId > 0)
                {
                    string formCode = "{% getFormTool  %} {{'" + Model.formTool.FId + "'}} {% endgetFormTool %}";

                    <div class="global_container">
                        <div class="big_title">表单引用代码</div>
                        <div class="content">
                            <div class="link_box">
                                <input type="text" value="@formCode" readonly="" class="link">
                                <button class="copy btn_copy" data-clipboard-action="copy" data-clipboard-text="@formCode">复制代码</button>
                            </div>
                        </div>
                    </div>

                }

                <div class="global_container">
                    <div class="big_title">表单字段</div>
                    <div class="field_list" data-listidx="0">
                        @foreach (var v in Model.formFieldList)
                        {
                            <div class="field_item" data-id="@v.FieldId">
                                <em></em>
                                <span class="field_name">@v.Name</span>
                                <div>
                                    <a href="javascript:;" class="field_opt field_del_btn">删除</a>
                                    <a href="javascript:;" class="field_opt field_edit_btn">编辑</a>
                                    <input type="hidden" name="positionFieldId[]" value="@v.FieldId">
                                </div>
                            </div>
                        }
                    </div>
                    <a href="javascript:;" class="btn_global btn_item_sec btn_add btn_field_add">添加字段</a>
                </div>
                <div class="global_container close_hidden">
                    @{
                        var MethodExpansion = Model.formTool.MethodExpansion != null ? Model.formTool.MethodExpansion.JsonToObj<Dictionary<string, string>>() : new Dictionary<string, string>();
                        string BtnText = MethodExpansion.ContainsKey("BtnText") ? MethodExpansion["BtnText"] : "Submit";
                        string BtnBg = MethodExpansion.ContainsKey("BtnBg") ? MethodExpansion["BtnBg"] : "#000000";
                        string BtnWord = MethodExpansion.ContainsKey("BtnWord") ? MethodExpansion["BtnWord"] : "#ffffff";
                    }
                    <div class="rows btn_text_box  clean ">
                        <label>按钮文案</label>
                        <div class="input">
                            <input name="MethodExpansion[BtnText]" value="@BtnText" type="text" class="box_input full_input"
                                   size="42">
                        </div>
                    </div>
                    <div class="rows btn_set_box  clean ">
                        <label>按钮颜色设置</label>
                        <div class="input">
                            <div class="color_row_box">
                                <div class="color_rows">
                                    <input class="color_style" name="MethodExpansion[BtnBg]" value="@BtnBg"
                                           style="background-color:@BtnBg;" data-bindclass="" data-type="">
                                    <span>背景</span>
                                </div>
                                <div class="color_rows">
                                    <input class="color_style" name="MethodExpansion[BtnWord]" value="@BtnWord"
                                           style="background-color:@BtnWord;" data-bindclass="" data-type="">
                                    <span>文字</span>
                                </div>
                            </div>
                        </div>
                    </div>

                </div>

                <input type="hidden" name="FId" value="@Model.FId">
                <input type="hidden" name="Type" value="@Model.Type">
                <input type="hidden" name="do_action" value="/manage/plugins/form-tool/update-form/">
            </form>
        </div>
    </div>
    <div id="fixed_right">
        <div class="global_container fixed_field" data-width="450">
            <div class="top_title">添加 <a href="javascript:;" class="close"></a></div>
            <form class="global_form" id="field_form">
                <div class="rows clean">
                    <label>标题</label>
                    <div class="input">
                        <input name="Name" value="" maxlength="255" type="text" class="box_input full_input" size="42"
                               notnull="">
                    </div>
                </div>
                <div class="rows clean">
                    <label>
                        类型
                    </label>
                    <div class="input">
                        <div class="global_select_box">
                            <div class="input_case">
                                <input class="imitation_select box_input full_input" type="text" placeholder="请选择"
                                       value="" readonly="">
                                <input type="hidden" name="Type" value="">
                                <i></i>
                            </div>
                            <ul class="select_ul drop_down">
                                <li class="item" data-value="0">单行输入</li>
                                <li class="item" data-value="1">多行输入</li>
                                <li class="item" data-value="2">下拉</li>
                                <li class="item" data-value="3">单选</li>
                                <li class="item" data-value="4">多选</li>
                                <li class="item" data-value="5">图片</li>
                                <li class="item" data-value="6">文件</li>
                            </ul>
                        </div>
                    </div>
                </div>
                <div class="rows clean field_box placeholder_box">
                    <label>框内提示语</label>
                    <div class="input">
                        <input name="FieldSetting[placeholder]" value="" type="text" class="box_input full_input"
                               size="42">
                    </div>
                </div>
                <div class="rows clean field_box required_box">
                    <label>必填</label>
                    <div class="input">
                        <div class="switchery required_switchery checked">
                            <input type="checkbox" value="1" name="FieldSetting[required]">
                            <div class="switchery_toggler"></div>
                            <div class="switchery_inner">
                                <div class="switchery_state_on"></div>
                                <div class="switchery_state_off"></div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="rows clean field_box format_box">
                    <label>格式</label>
                    <div class="input">
                        <div class="box_type_menu">
                            <span class="item checked">
                                <input type="radio" name="FieldSetting[format]" value="0">
                                普通
                            </span>
                            <span class="item">
                                <input type="radio" name="FieldSetting[format]" value="1">
                                邮件
                            </span>
                        </div>
                    </div>
                </div>
                <div class="rows clean field_box tip_box">
                    <label>提示</label>
                    <div class="input">
                        <input name="FieldSetting[tip]" value="" type="text" class="box_input full_input" size="42">
                    </div>
                </div>
                <div class="rows clean field_box limit_box">
                    <label>限制字数</label>
                    <div class="input">
                        <input name="FieldSetting[limit]" value="" rel="amount" type="text" class="box_input" size="16"
                               notnull="">
                    </div>
                </div>
                <div class="rows clean field_box format_box">
                    <label>宽度</label>
                    <div class="input">
                        <div class="box_type_menu">
                            <span class="item checked">
                                <input type="radio" name="FieldSetting[colums]" value="1">
                                100%
                            </span>
                            <span class="item">
                                <input type="radio" name="FieldSetting[colums]" value="2">
                                50%
                            </span>
                            <span class="item">
                                <input type="radio" name="FieldSetting[colums]" value="3">
                                33%
                            </span>
                        </div>
                    </div>
                </div>
                <div class="rows clean field_box content_box">
                    <label>内容</label>
                    <div class="input"></div>
                </div>
                <div class="rows clean box_button box_submit">
                    <div class="input">
                        <input type="submit" class="btn_global btn_submit" value="保存">
                        <input type="button" class="btn_global btn_cancel" value="取消">
                    </div>
                </div>
                <input type="hidden" name="FieldId" value="">
                <input type="hidden" name="FId" value="@Model.FId">
                <input type="hidden" name="FormType" value="@Model.Type">
                <input type="hidden" name="IsAdd" value="">
                <input type="hidden" name="do_action" value="/manage/plugins/form-tool/update-field/">
            </form>
        </div>
    </div>

    <div class="rows clean fixed_btn_submit" style="width: 1868px; left: 180px;">
        <div class="center_container">
            <div class="input input_button">
                <input type="button" class="btn_global btn_submit" value="保存">
                <a href="/Customer/FormTool"><input type="button" class="btn_global btn_cancel" value="返回"></a>
            </div>
        </div>
    </div>

</div>