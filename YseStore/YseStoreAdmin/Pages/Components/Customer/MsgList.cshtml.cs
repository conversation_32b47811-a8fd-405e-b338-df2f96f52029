using Entitys;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using OfficeOpenXml.FormulaParsing.Excel.Functions.Math;
using YseStore.IService.Customer;

namespace YseStoreAdmin.Pages.Components.Customer
{
    public class MsgList : MComponent
    {
        public ICustomerMsgService _customerMsgService { get; }
        public List<UserMessageResponse> UserMessageModel { get; set; }
        public products_review_reply ProductsReviewReply { get; set; }
        public int sum = 0;
        public MsgList(ICustomerMsgService customerMsgService)
        {
            _customerMsgService = customerMsgService;
        }
        public override async Task MountAsync()
        {

            //UserMessageModel = await _customerMsgService.GetUserMessageResponseAsync();
            sum = await _customerMsgService.GetunreadCountAsync();

        }
    }
}
