using Entitys;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using YseStore.IService;
using YseStore.IService.Customer;
using YseStore.Model.Event;
using YseStore.Model.VM;
using static Microsoft.AspNetCore.Razor.Language.TagHelperMetadata;

namespace YseStoreAdmin.Pages.Components.Customer
{
    public class CustomerListTable : MComponent
    {
        public readonly ICustomerListService _customerListService;
        public string UserType = "0";
        public string keywords { get; set; }
        public string Tags { get; set; }
        public string OrderNumber { get; set; }
        public string OrderPrice { get; set; }
        public string RegTime { get; set; }
        public string IsNewsletter { get; set; }
        public string IsTaxExempt { get; set; }
        public string ReviewStatus { get; set; }
        public string MenuSort = "";
        public PagedList<UserResponse>? PhotosReviewList { get; set; }

        public CustomerListTable(ICustomerListService customerListService)
        {

            Subscribe<UserTurnPageEvent>(HandleUserTurnPage);
            _customerListService = customerListService;
        }
        public async Task HandleUserTurnPage(UserTurnPageEvent evt)
        {
            if (evt.Name == "manlog")
            {
                await BindData(evt.PageNum);
            }
        }

        public override async Task MountAsync()
        {
            keywords = HttpContext.Request.Query["keyword"].ToString();
            UserType = HttpContext.Request.Query["UserType"].ToString();
            Tags = HttpContext.Request.Query["Tags"].ToString();
            OrderNumber = HttpContext.Request.Query["OrderNumber"].ToString();
            OrderPrice = HttpContext.Request.Query["OrderPrice"].ToString();
            RegTime = HttpContext.Request.Query["RegTime"].ToString();
            IsNewsletter = HttpContext.Request.Query["IsNewsletter"].ToString();
            IsTaxExempt = HttpContext.Request.Query["IsTaxExempt"].ToString();
            ReviewStatus = HttpContext.Request.Query["ReviewStatus"].ToString();
            MenuSort = HttpContext.Request.Query["MenuSort"].ToString();
            await BindData();
        }
        [SkipOutput]
        public async Task BindData(int page = 1)
        {
            PhotosReviewList = await GetLogs(page);
            DispatchGlobal<PageEvent>(new PageEvent(PhotosReviewList.TotalCount, PhotosReviewList.PageSize, PhotosReviewList.PageIndex + 1, "manlog"), null, true);

        }
        public async Task<PagedList<UserResponse>> GetLogs(int page = 1)
        {
           


            var result = await _customerListService.QueryAsync(keywords, UserType, Tags, OrderNumber, OrderPrice, RegTime, IsNewsletter,
                IsTaxExempt, ReviewStatus, MenuSort, page, 20);

            return result;
        }
    }
}
