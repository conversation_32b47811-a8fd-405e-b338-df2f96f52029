@using Newtonsoft.Json.Linq;
@using YseStore.Model.Enums;
@using YseStore.Common;
@model YseStoreAdmin.Pages.Components.Customer.CustomerDetail
@{
}
<div>

    <div id="user_inside" class="r_con_wrap" style="height: 325px;">
        <div class="user_container">
            <div class="inside_container inside_menu_right">
                <div class="return_title fl">
                    <a href="/customer/list">
                        <span class="return">客户 / 基本信息</span>
                    </a>
                </div>
                <div class="clear"></div>
            </div>
            <div class="user_menu">
                @if (Model.UserModel != null && Model.UserModel.Locked)
                {
                    <div class="menu_item">
                        <a href="/api/CustomerList/CustomerListLocked?UserId=@(Model.UserModel.UserId)" data-status="0" class="locked no_load">
                            <i class="iconfont icon-shield1"></i>
                            取消屏蔽
                        </a>
                    </div>
                }
                else
                {
                    <div class="menu_item">
                        <a href="/api/CustomerList/CustomerListLocked?UserId=@(Model.UserModel.UserId)" data-status="1" class="locked no_load">
                            <i class="iconfont icon-shield1"></i>
                            屏蔽
                        </a>
                    </div>

                }

                <div class="menu_item">
                    <a href="javascript:;" class="password_edit">
                        <i class="iconfont icon-xiugaimima"></i>
                        修改密码
                    </a>
                </div>
                <div class="menu_item">
                    <a href="javascript:;" class="edit_newsletter" data="@(Model.UserModel.UserId)">
                        <i class="iconfont icon-subscribe1"></i>
                        修改订阅状态
                    </a>
                </div>
                <div class="menu_item">
                    <a href="javascript:;" class="tax_exempt btn_tax_exempt">
                        <i class="iconfont icon-dollars1"></i>
                        设置免征税
                    </a>
                </div>
                <div class="menu_item">
                    <a href="/api/CustomerList/DelCustomerListDetail?UserId=@(Model.UserModel.UserId)" class="del" rel="del">
                        <i class="iconfont icon-delete1"></i>
                        删除
                    </a>
                </div>
               @*  <div class="menu_item user_menu_item">
                    <button class="btn_menu_app"><em></em><span>我的应用</span><i></i></button>
                    <div class="box_my_app" style="display: none; top: 15px; opacity: 0;">
                        <dl class="drop_down">
                            <dt class="item" data-type="user_level"><span>会员体系</span></dt>
                        </dl>
                    </div>
                </div> *@
            </div>
            <form id="edit_form" class="global_form center_container" action="/Customer/detail?UserId=@(Model.UserModel.UserId)" method="post">
                <input type="hidden" name="_csrf-manage" value="oC9W-UWIog8rrCGxAX4zkOS43nR41j5_UGIgEh1_lv7nWCOyduLUWXrodvhbGXygu8-HDlWBeh1pMFRgWTvVnQ==">			<div class="left_container">
                    <div class="left_container_side">
                        <div class="global_container base_info_v2">
                            <div class="big_title">基本信息 <a href="javascript:;" class="fr info_edit">修改</a></div>
                            <div class="name">@(Model.UserModel.FirstName) @(Model.UserModel.LastName)</div>
                            <div class="email color_555">
                                @(Model.UserModel.Email)
                                <span class="icon">
                                    <span class="clean">
                                        @if (!Convert.ToBoolean(Model.UserModel.IsRegistered))
                                        {
                                            <div class="non_user">游客</div>
                                            <div class="non_user">待验证邮箱</div>
                                        }
                                        @if (Convert.ToBoolean(Model.UserModel.IsNewsletter))
                                        {
                                            <div class="non_user">已订阅</div>
                                        }
                                        @if (Model.UserModel.tagsList != null)
                                        {
                                            foreach (var item in Model.UserModel.tagsList)
                                            {
                                                <div class="non_user">@item.Value</div>
                                            }
                                        }
                                        @*   <span class="non_user">直接访问</span>
                                        <div class="non_user">批发商</div> *@
                                    </span>
                                </span>
                            </div>
                            <input type="hidden" name="UserId" value="@(Model.UserModel.UserId)">
                            <div class="border_line"></div>
                            <div class="info_row">
                                <div class="row">
                                    <label>姓名</label>
                                    <span>@(Model.UserModel.FirstName) @(Model.UserModel.LastName)</span>
                                </div>
                                <div class="row">
                                    <label>邮箱</label>
                                    <span>@(Model.UserModel.Email)</span>
                                </div>
                                <div class="row">
                                    <label>Nickname</label>
                                    <span>
                                        @(Model.UserModel.NickName)
                                    </span>
                                </div>
                             @*    0:默认网站注册，1：Google授权，2：领英授权，3：Facebook授权 *@

                                <div class="row">
                                    <label>注册方式</label>
                                    @if (Model.UserModel.AccountType==1)
                                    {
                                        <span>Google授权</span>
                                    }
                                    else if (Model.UserModel.AccountType == 2)
                                    {
                                        <span>领英授权</span>
                                    }
                                    else if (Model.UserModel.AccountType == 3)
                                    {
                                        <span>Facebook授权</span>
                                    }
                                    else
                                    {
                                        <span>默认网站注册</span>
                                    }
                                    
                                </div>
                                <div class="row">
                                    <label>注册URL</label>
                                    <span style="word-break: break-all;">
                                        @(Model.UserModel.RegisterUrl)
                                    </span>
                                </div>
                                <div class="row">
                                    <label>注册时间/注册IP</label>
                                    <span>
                                        @(DateTimeHelper.ConvertUnixTimestampToBeijingTime(Convert.ToInt64(Model.UserModel.RegTime))) @(Model.UserModel.RegIp) @(Model.UserModel.RegIpName)
                                    </span>
                                </div>
                                <div class="row">
                                    <label>渠道来源</label>
                                    <span>@(Model.UserModel.RefererName)</span>
                                </div>
                            </div>
                        </div>

                        <div class="global_container shopping_info_v2">
                            <div class="big_title">购买概况</div>
                            <div class="info_list">
                                <div class="info_item fl">
                                    <div class="title">购买次数</div>
                                    <div class="text">@(Model.UserModel.ConsumptionTime)</div>
                                </div>
                                <div class="info_item fl">
                                    <div class="title">产品数量</div>
                                    <div class="text">@(Model.UserOrdersProductCount)</div>
                                </div>
                                <div class="info_item fl">
                                    <div class="title">累计金额</div>
                                    <div class="text">@(Model.OrderSymbol)@(Model.UserModel.Consumption)</div>
                                </div>
                                <div class="clear"></div>
                            </div>
                        </div>

                        <div class="global_container orders_info_v2">
                            <div class="big_title">最近订单</div>
                            <div class="table">
                                <table border="0" cellpadding="5" cellspacing="0" class="r_con_table">
                                    <thead>
                                        <tr>
                                            <td width="25%" nowrap="nowrap">订单号</td>
                                            <td width="20%" nowrap="nowrap">订单总额</td>
                                            <td width="20%" nowrap="nowrap">付款/发货</td>
                                            <td width="20%" nowrap="nowrap">产品</td>
                                            <td width="15%" nowrap="nowrap">创建时间</td>
                                            <td width="60" nowrap="nowrap"></td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @if (Model.UserOrdersProductsList!=null)
                                        {
                                            foreach (var item in Model.UserOrdersProductsList)
                                            {
                                                <tr>
                                                    <td nowrap="nowrap">@(item.OId)<div class="non_order">@(item.Type == "system" ? "客户下单" : "手动创建")</div></td>
                                                    <td nowrap="nowrap">@(item.OrderSymbol)@(item.OrderSum)</td>
                                                    <td nowrap="nowrap">
                                                        @if (item.PaymentStatus == PaymentStatusEnum.已付款.GetDescription())
                                                        {
                                                            <span class="status ing">@(EnumExtensions.FromDescription<PaymentStatusEnum>(item.PaymentStatus))</span>
                                                        }else {
                                                            <span class="status">@(EnumExtensions.FromDescription<PaymentStatusEnum>(item.PaymentStatus))</span>
                                                        }
                                                        @if (item.ShippingStatus == ShippingStatusEnum.已发货.GetDescription())
                                                        {
                                                            <span class="status ing" style="margin-left:5px;">@(EnumExtensions.FromDescription<ShippingStatusEnum>(item.ShippingStatus))</span>
                                                        }
                                                        else
                                                        {
                                                            <span class="status" style="margin-left:5px;">@(EnumExtensions.FromDescription<ShippingStatusEnum>(item.ShippingStatus))</span>
                                                        }
                                                    </td>
                                                    <td nowrap="nowrap">
                                                        <div class="products_number" data-id="@(item.OrderId)">
                                                            <div class="products_txt">
                                                                @(item.Orders_Products_Lists.Sum(x=>x.Qty)??0)个产品	<i></i>
                                                            </div>
                                                            <div class="products_container"><div class="products_box"></div></div>
                                                        </div>
                                                        <div class="products_container"><div class="products_box"></div></div>

                                                    </td>
                                                    <td nowrap="nowrap">
                                                        @(DateTimeHelper.ConvertUnixTimestampToBeijingTime(Convert.ToInt64(item.OrderTime), "yyyy-MM-dd"))
                                                        <br>@(DateTimeHelper.ConvertUnixTimestampToBeijingTime(Convert.ToInt64(item.OrderTime), "HH:mm:ss"))
                                                    </td>
                                                    <td nowrap="nowrap"><a class="oper_icon icon_file button_tips" target="_blank" href="/manage/orders/orders/view?id=@(item.OrderId)&query_string=">详细</a></td>
                                                </tr>
                                            }
                                        }

                                        
                                        @* <tr>
                                            <td nowrap="nowrap">0506371<div class="non_order">手动创建</div></td>
                                            <td nowrap="nowrap">$154.03</td>
                                            <td nowrap="nowrap">
                                                <span class="status">未付款</span>
                                                <span class="status" style="margin-left:5px;">未发货</span>
                                            </td>
                                            <td nowrap="nowrap">
                                                <div class="products_number" data-id="27">
                                                    <div class="products_txt">
                                                        2个产品														<i></i>
                                                    </div>
                                                    <div class="products_container"><div class="products_box"></div></div>
                                                </div>
                                                <div class="products_container"><div class="products_box"></div></div>

                                            </td>
                                            <td nowrap="nowrap">2025-02-11<br>14:24:23</td>
                                            <td nowrap="nowrap"><a class="oper_icon icon_file button_tips" target="_blank" href="/manage/orders/orders/view?id=27&amp;query_string=">详细</a></td>
                                        </tr> *@
                                    </tbody>
                                </table>
                            </div>
                        </div>

                    </div>
                </div>
                <div class="right_container">
                    <div class="global_container behavior_box">
                        <div class="big_title">客户行为</div>
                        <div class="info_list">
                            <div class="item">
                                <div class="title fl">
                                    加入购物车<span>(产品)</span>
                                </div>
                                <div class="detail fr">
                                    <a href="javascript:;" class="add_cart">详细</a>
                                </div>
                                <div class="clear"></div>
                                <div class="number">@(Model.ShoppingCart.Count)</div>
                            </div>
                            <div class="item">
                                <div class="title fl">
                                    加入收藏<span>(产品)</span>
                                </div>
                                <div class="detail fr">
                                    <a href="javascript:;" class="add_favorite">详细</a>
                                </div>
                                <div class="clear"></div>
                                <div class="number">@(Model.FavoriteProducts.Count)</div>
                            </div>
                        </div>
                    </div>
                    <div class="global_container">
                        <div class="big_title">登录信息</div>
                        <div class="rows clean">
                            <div class="login_tips">共登录@(Model.UserModel.LoginTimes)次</div>
                        </div>
                        <table class="u_orders_info">
                            <tbody>
                                <tr>
                                    <th>最近登录时间</th>
                                    <td>时间: @(DateTimeHelper.ConvertUnixTimestampToBeijingTime(Convert.ToInt64(Model.UserModel.LastLoginTime))) </td>
                                </tr>
                                <tr>
                                    <th>最近登录IP</th>
                                    <td>@(Model.UserModel.LastLoginIp)<br />@(Model.UserModel.LastLoginIpCountry)</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div class="global_container address_info_v2">
                        <div class="tabs">
                            <span class="current">收货地址</span>
                            <span>账单地址</span>
                        </div>
                        <div class="address_info">
                            <div class="address_list current">
                                @{
                                    var UserIsBillingAddress = Model.UserAddressBook.Where(uab => uab.IsBillingAddress == false).ToList();
                                    if (UserIsBillingAddress != null)
                                    {
                                        int j = 0;
                                        for (int i = 0; i < UserIsBillingAddress.Count; i++)
                                        {
                                            if (j == i)
                                            {
                                                <div class="addr_box ">
                                                    <ul>
                                                        <li><span class="th">姓名:</span><span class="desc">@(UserIsBillingAddress[i].FirstName) @(UserIsBillingAddress[i].LastName)</span></li>
                                                        <li><span class="th">电话:</span><span class="desc">+@(UserIsBillingAddress[i].CountryCode) @(UserIsBillingAddress[i].PhoneNumber)</span></li>
                                                        <li><span class="th">国家/地区:</span><span class="desc">@(UserIsBillingAddress[i].CountryName)</span></li>
                                                        <li><span class="th">州/省/地区:</span><span class="desc">@(UserIsBillingAddress[i].State)</span></li>
                                                        <li><span class="th">城市:</span><span class="desc">@(UserIsBillingAddress[i].City)</span></li>
                                                        <li><span class="th">街道:</span><span class="desc">@(UserIsBillingAddress[i].AddressLine1)</span></li>
                                                        <li><span class="th">寓所:</span><span class="desc">@(UserIsBillingAddress[i].AddressLine2)</span></li>
                                                        <li><span class="th">邮政编码:</span><span class="desc">@(UserIsBillingAddress[i].ZipCode)</span></li>
                                                    </ul>
                                                    <a href="javascript:;" class="more">更多</a>
                                                </div>
                                            }
                                        }
                                        for (int i = 0; i < UserIsBillingAddress.Count; i++)
                                        {
                                            if (j != i)
                                            {
                                                <div class="addr_box hide">
                                                    <ul>
                                                        <li><span class="th">姓名:</span><span class="desc">@(UserIsBillingAddress[i].FirstName) @(UserIsBillingAddress[i].LastName)</span></li>
                                                        <li><span class="th">电话:</span><span class="desc">+@(UserIsBillingAddress[i].CountryCode) @(UserIsBillingAddress[i].PhoneNumber)</span></li>
                                                        <li><span class="th">国家/地区:</span><span class="desc">@(UserIsBillingAddress[i].CountryName)</span></li>
                                                        <li><span class="th">州/省/地区:</span><span class="desc">@(UserIsBillingAddress[i].State)</span></li>
                                                        <li><span class="th">城市:</span><span class="desc">@(UserIsBillingAddress[i].City)</span></li>
                                                        <li><span class="th">街道:</span><span class="desc">@(UserIsBillingAddress[i].AddressLine1)</span></li>
                                                        <li><span class="th">寓所:</span><span class="desc">@(UserIsBillingAddress[i].AddressLine2)</span></li>
                                                        <li><span class="th">邮政编码:</span><span class="desc">@(UserIsBillingAddress[i].ZipCode)</span></li>
                                                    </ul>
                                                </div>

                                            }
                                        }
                                    }
                                }
                                @* <div class="addr_box hide">
                                <ul>
                                <li><span class="th">姓名:</span><span class="desc">CC adc</span></li>
                                <li><span class="th">电话:</span><span class="desc">+86 12212111</span></li>
                                <li><span class="th">国家/地区:</span><span class="desc">China</span></li>
                                <li><span class="th">州/省/地区:</span><span class="desc">Anhui</span></li>
                                <li><span class="th">城市:</span><span class="desc">22</span></li>
                                <li><span class="th">街道:</span><span class="desc">22</span></li>
                                <li><span class="th">寓所:</span><span class="desc">22</span></li>
                                <li><span class="th">邮政编码:</span><span class="desc">91724</span></li>
                                </ul>
                                </div> *@
                            </div>
                            <div class="address_list ">
                                @{
                                    var UserIsBillingAddressTrue = Model.UserAddressBook.Where(uab => uab.IsBillingAddress == true).ToList();
                                    if (UserIsBillingAddressTrue != null)
                                    {
                                        int j = 0;
                                        for (int i = 0; i < UserIsBillingAddressTrue.Count; i++)
                                        {
                                            if (j == i)
                                            {
                                                <div class="addr_box ">
                                                    <ul>
                                                        <li><span class="th">姓名:</span><span class="desc">@(UserIsBillingAddressTrue[i].FirstName) @(UserIsBillingAddressTrue[i].LastName)</span></li>
                                                        <li><span class="th">电话:</span><span class="desc">+@(UserIsBillingAddressTrue[i].CountryCode) @(UserIsBillingAddressTrue[i].PhoneNumber)</span></li>
                                                        <li><span class="th">国家/地区:</span><span class="desc">@(UserIsBillingAddressTrue[i].CountryName)</span></li>
                                                        <li><span class="th">州/省/地区:</span><span class="desc">@(UserIsBillingAddressTrue[i].State)</span></li>
                                                        <li><span class="th">城市:</span><span class="desc">@(UserIsBillingAddressTrue[i].City)</span></li>
                                                        <li><span class="th">街道:</span><span class="desc">@(UserIsBillingAddressTrue[i].AddressLine1)</span></li>
                                                        <li><span class="th">寓所:</span><span class="desc">@(UserIsBillingAddressTrue[i].AddressLine2)</span></li>
                                                        <li><span class="th">邮政编码:</span><span class="desc">@(UserIsBillingAddressTrue[i].ZipCode)</span></li>
                                                    </ul>
                                                    <a href="javascript:;" class="more">更多</a>
                                                </div>
                                            }
                                        }
                                        for (int i = 0; i < UserIsBillingAddressTrue.Count; i++)
                                        {
                                            if (j != i)
                                            {
                                                <div class="addr_box hide">
                                                    <ul>
                                                        <li><span class="th">姓名:</span><span class="desc">@(UserIsBillingAddressTrue[i].FirstName) @(UserIsBillingAddressTrue[i].LastName)</span></li>
                                                        <li><span class="th">电话:</span><span class="desc">+@(UserIsBillingAddressTrue[i].CountryCode) @(UserIsBillingAddressTrue[i].PhoneNumber)</span></li>
                                                        <li><span class="th">国家/地区:</span><span class="desc">@(UserIsBillingAddressTrue[i].CountryName)</span></li>
                                                        <li><span class="th">州/省/地区:</span><span class="desc">@(UserIsBillingAddressTrue[i].State)</span></li>
                                                        <li><span class="th">城市:</span><span class="desc">@(UserIsBillingAddressTrue[i].City)</span></li>
                                                        <li><span class="th">街道:</span><span class="desc">@(UserIsBillingAddressTrue[i].AddressLine1)</span></li>
                                                        <li><span class="th">寓所:</span><span class="desc">@(UserIsBillingAddressTrue[i].AddressLine2)</span></li>
                                                        <li><span class="th">邮政编码:</span><span class="desc">@(UserIsBillingAddressTrue[i].ZipCode)</span></li>
                                                    </ul>
                                                </div>

                                            }
                                        }
                                    }
                                }

                                @*  <div class="addr_box">
                                <ul>
                                <li><span class="th">姓名:</span><span class="desc">abc 222</span></li>
                                <li><span class="th">电话:</span><span class="desc">+86 2333</span></li>
                                <li><span class="th">国家/地区:</span><span class="desc">China</span></li>
                                <li><span class="th">州/省/地区:</span><span class="desc">beijing</span></li>
                                <li><span class="th">城市:</span><span class="desc">111</span></li>
                                <li><span class="th">街道:</span><span class="desc">123456</span></li>
                                <li><span class="th">寓所:</span><span class="desc">123456</span></li>
                                <li><span class="th">邮政编码:</span><span class="desc">10000</span></li>
                                </ul>
                                </div> *@
                            </div>
                            <div class="clear"></div>
                        </div>
                    </div>
                    <div class="global_container user_remark_log">
                        <div class="big_title">备注&nbsp;&nbsp;<span class="box_explain">( 此备注仅提供后台管理员查看 )</span></div>
                        <div class="form_remark_log">
                            <div class="form_box">
                                <div class="remark_left"><div><input type="text" class="box_input" name="Log" placeholder="请输入 ..."></div></div>
                                <input type="button" class="btn_save" value="发送">
                            </div>
                        </div>
                        <ul class="log_list">
                            @if (Model.UserRemarkLog != null)
                            {
                                foreach (var item in Model.UserRemarkLog)
                                {
                                    <li>
                                        <div class="time">@(DateTimeHelper.ConvertUnixTimestampToBeijingTime(Convert.ToInt64(item.AccTime), "yyyy-MM-dd"))<br>@(DateTimeHelper.ConvertUnixTimestampToBeijingTime(Convert.ToInt64(item.AccTime), "HH:mm:ss"))</div>
                                        <div class="information">
                                            <i class="radio"></i>
                                            <div class="log">@(item.Log)</div>
                                            <div class="user">@(item.UserId)</div>
                                        </div>
                                    </li>
                                }
                            }

                        </ul>
                    </div>
                    <div class="global_container">
                        <div class="big_title no_pointer" style="margin-bottom:0;">
                            标签						<span class="box_explain">(仅后台可见)</span>
                            <a href="javascript:;" class="edit_tags fr" data="@(Model.UserModel.UserId)">修改</a>
                            <div class="clear"></div>
                        </div>
                    </div>
                </div>
                <div class="clear"></div>
                <input type="hidden" id="UserId" value="@(Model.UserModel.UserId)">
            </form>
        </div>
    </div>
    <div class="pop_form user_invite_box">
        <form id="user_invite_form" class="global_form" action="/Customer/detail?UserId=@(Model.UserModel.UserId)" method="post">
            <input type="hidden" name="_csrf-manage" value="oC9W-UWIog8rrCGxAX4zkOS43nR41j5_UGIgEh1_lv7nWCOyduLUWXrodvhbGXygu8-HDlWBeh1pMFRgWTvVnQ==">		<div class="r_con_form">
                <div class="pop_box_left">
                    <div class="title"><strong>发送会员邀请</strong></div>
                    <div class="content">
                        <div class="label">优惠券</div>
                        <div class="rows clean">
                            <div class="input coupon_box">
                                <div class="simulate_select_box" data-type="checkbox">
                                    <div class="select">
                                        <span class="placeholder ">请选择</span>
                                    </div>
                                    <div class="option_box drop_down" style="display: none;">
                                        <div class="global_app_tips obvious">
                                            <em></em><span>仅支持“自动投放-完成会员注册，自动投放-登录成功”类型的优惠劵</span>
                                        </div>
                                        <div class="option_list overflow_y_auto hide" data-name="Coupon" style="display: none;">
                                            <a href="javascript:;" data-page="2" data-per-page="20" class="btn_load_more" style="display: none;">加载更多</a>
                                        </div>
                                        <div class="no_data" style="display: block;">
                                            暂无优惠劵
                                        </div>
                                        <div class="foot">
                                            <a href="javascript:;" class="btn_refresh"><span class="df">刷新</span><span class="ing">正在刷新...</span></a>
                                            <a href="/manage/sales/coupon/" target="_blank" class="btn_set">设置</a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <input type="hidden" name="Type" value="invite">
                        <input type="hidden" name="UserId" value="@(Model.UserModel.UserId)">
                        <input type="hidden" name="do_action" value="/manage/user/user/verify">
                    </div>
                    <div class="button"><input type="submit" class="btn_global btn_submit" id="button_add" name="submit_button" value="发送"><input type="button" class="btn_global btn_cancel" value="取消"></div>
                </div>
                <div class="pop_box_right">
                    <div class="title t"><h2>×</h2></div>
                    <div class="content">
                        <div style="width: 100%;max-width: 700px;margin:auto;">
                            <div style="width:100%; max-width:700px; margin:10px auto;">
                                <div style="font-family:Arial; padding:15px 0; line-height:1.3; min-height:100px; _height:100px; color:#333; font-size:12px;">
                                    <table width="700" border="0" cellspacing="0" cellpadding="0" style="border:none;">
                                        <tbody>
                                            <tr style="border:none;">
                                                <td width="350" style="padding-bottom:8px;border:none;"><a href="http://shoptest.yisaier.net" target="_blank"><img src="http://test.retevis.fr/u_file/2412/12/photo/retevis-logo.png" style="max-width:350px;max-height:100%;" border="0"></a></td>
                                            </tr>
                                        </tbody>
                                    </table>
                                    <div style="margin-bottom:50px;padding: 72px 30px 62px;width: 100%; background-color: #f4f7f6;box-sizing:border-box;text-align:center;">
                                        <div style="font-size:36px;color:#000000;font-weight:bold;text-align:center;line-height:1.5;">Customer account activation</div>
                                        <div style="margin-top:25px;padding:0 60px;line-height: 1.3;font-size:16px; color:#666666; text-align:center;">You have created a customer account in <span style="color:#e53935;">Retevis Two Way Radio</span>. <br> Do you want to activate it now?</div>
                                        <div style="margin-top: 25px; text-align: center;"></div>
                                        <a href="http://test.retevis.fr/account/verify.html?userType=invitation&amp;u=17&amp;p=7f856006fa50b94a03ce35a61818580b&amp;userTypeBase=Reseller&amp;uniqueid=6abb84a1025c9ca19c550c9bd1bc86" target="_blank" style="display:inline-block;margin:25px auto 0;padding: 0 30px;width:auto;height:60px;line-height:60px;border-radius:5px;background-color:#1F2227;color:#fff;font-size:18px;text-align:center;text-decoration:none;">Activate Your Account</a>
                                    </div>
                                    <span style="display:block;font-family:Arial;font-size:12px;line-height:22px;color:#666666;padding:15px 0;"> Yours sincerely,<br> shoptest.yisaier.net Customer Care Team</span>
                                    <span style="display:block;font-family:Arial;font-size:12px;line-height:22px;color:#666666;padding:15px 0;">
                                        <div style="padding:20px 0; line-height:180%; font-family:Arial; font-size:12px; color:#000; border-top:1px solid #e6e6e6; border-bottom:1px solid #e6e6e6;">
                                            You have received this email because you are a registered member of the shoptest.yisaier.net website.<br>
                                            For further information, log in to your account at: <a href="http://shoptest.yisaier.net" target="_blank" style="font-family:Arial; font-size:12px; color:#1E5494; text-decoration:underline;">http://shoptest.yisaier.net</a> and submit your request or use live chat.
                                        </div>
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </form>
    </div>
    <div id="fixed_right">
        <div class="global_container fixed_user_view fixed_favorite_box" data-width="470">
            <div class="top_title">收藏<a href="javascript:;" class="close"></a></div>
            <div class="box_table">
                <table border="0" cellpadding="5" cellspacing="0" class="r_con_table">
                    <thead>
                        <tr>
                            <td width="70%">产品</td>
                            <td width="30%">创建时间</td>
                        </tr>
                    </thead>
                    <tbody></tbody>
                

                </table>
            </div>
            <div class="submit_box">
                <button class="btn_global btn_submit">返回</button>
            </div>
        </div>
        <div class="global_container fixed_user_view fixed_cart_box" data-width="960">
            <div class="top_title">购物车<a href="javascript:;" class="close"></a></div>
            <div class="box_table">
                <table border="0" cellpadding="5" cellspacing="0" class="r_con_table">
                    <thead>
                        <tr>
                            <td width="17"></td>
                            <td width="27%">产品</td>
                            <td width="25%">属性</td>
                            <td width="12%">价格</td>
                            <td width="12%">数量</td>
                            <td width="12%">小计</td>
                            <td width="12%">添加时间</td>
                        </tr>
                    </thead>
                     <tbody></tbody>
                  
                </table>
            </div>
            <div class="rows clean box_button box_submit">
                <div class="input">
                    <sapn class="btn_checkbox select_all"><em class="button"></em> 全选<input type="checkbox" name="select_all" value=""></sapn>
                    <input type="button" class="btn_global btn_submit btn_create_order" value="创建订单">
                    <input type="button" class="btn_global btn_cancel" value="返回">
                </div>
            </div>
        </div>
        <div class="global_container fixed_user_newsletter_box" data-width="396">
            <div class="top_title">修改订阅状态<a href="javascript:;" class="close"></a></div>
            <div class="blank20"></div>
            <form id="edit_newsletter_form">
                <div class="status_box">
                    <span class="input_checkbox_box ">
                        <span class="input_checkbox">
                            <input type="checkbox" name="IsNewsletter" value="1">
                        </span>
                    </span>
                    客户同意接收营销电子邮件
                </div>
                <div class="blank10"></div>
                <div class="global_app_tips hide">
                    <em></em>客户订阅营销电子邮件前，应先征得客户同意
                </div>
                <div class="rows clean box_button box_submit">
                    <div class="input">
                        <input type="hidden" name="id" value="@(Model.UserModel.UserId)">
                        <input type="hidden" name="do_action" value="/api/CustomerList/CustomerListIsNewsletterDetail">
                        <input type="submit" class="btn_global btn_submit" value="保存">
                        <input type="button" class="btn_global btn_cancel" value="取消">
                    </div>
                </div>
            </form>
        </div>
        <div class="global_container box_tax_exempt" data-width="396">
            <div class="top_title">设置免征税<a href="javascript:;" class="close"></a></div>
            <div class="blank20"></div>
            <form id="form_tax_exempt">
                <div class="status_box">
                    <span class="input_checkbox_box @(Model.UserModel.IsTaxExempt?"checked":"")">
                        <span class="input_checkbox">
                            <input type="checkbox" name="IsTaxExempt" value="1">
                        </span>
                    </span>
                    设置为免征税客户
                </div>
                <div class="blank10"></div>
                <div class="global_app_tips global_color">
                    <em></em>设置为免征税客户后，客户可以享受免税服务，订单总额将自动减免应收税款
                </div>
                <div class="rows clean box_button box_submit">
                    <div class="input">
                        <input type="hidden" name="id" value="@(Model.UserModel.UserId)">
                        <input type="hidden" name="do_action" value="/api/CustomerList/CustomerListIsTaxExempt">
                        <input type="submit" class="btn_global btn_submit" value="保存">
                        <input type="button" class="btn_global btn_cancel" value="取消">
                    </div>
                </div>
            </form>
        </div>
        <div class="global_container password_edit_box" data-width="350">
            <div class="top_title">修改密码<a href="javascript:;" class="close"></a></div>
            <form id="password_edit" class="global_form" action="/Customer/detail?UserId=@(Model.UserModel.UserId)" method="post">
                <input type="hidden" name="_csrf-manage" value="oC9W-UWIog8rrCGxAX4zkOS43nR41j5_UGIgEh1_lv7nWCOyduLUWXrodvhbGXygu8-HDlWBeh1pMFRgWTvVnQ==">
                <div class="rows clean">
                    <label>新密码</label>
                    <div class="input">
                        <input type="password" class="box_input full_input" name="NewPassword" value="" size="25" maxlength="16" notnull="notnull">
                    </div>
                </div>
                <div class="rows clean">
                    <label>确认密码</label>
                    <div class="input">
                        <input type="password" class="box_input full_input" name="ReNewPassword" value="" size="25" maxlength="16" notnull="notnull">
                    </div>
                </div>
                <div class="rows clean box_submit">
                    <label></label>
                    <div class="input input_button">
                        <input type="submit" class="btn_global btn_submit" value="保存">
                        <input type="button" class="btn_global btn_cancel" value="取消">
                    </div>
                </div>
                <input type="hidden" name="UserId" value="@(Model.UserModel.UserId)">
                <input type="hidden" name="do_action" value="/api/CustomerList/CustomerListPasswordEditt">
            </form>
        </div>


        <div class="global_container fixed_tags_box" data-width="350">
            <div class="top_title">修改标签<a href="javascript:;" class="close"></a></div>
            <div id="tags_box"></div>  
           
        </div>

        <div class="global_container user_info_edit" data-width="370">
            <div class="top_title">修改<a href="javascript:;" class="close"></a></div>
            <form id="user_info_edit_form" class="global_form">
                <div class="rows clean">
                    <label>名字</label>
                    <div class="input">
                        <input class="box_input" type="text" name="FirstName" maxlength="100" value="@(Model.UserModel.FirstName)">
                    </div>
                </div>

                <div class="rows clean">
                    <label>姓氏</label>
                    <div class="input">
                        <input class="box_input" type="text" name="LastName" maxlength="100" value="@(Model.UserModel.LastName)">
                    </div>
                </div>

                <div class="rows clean">
                    <label>邮箱</label>
                    <div class="input">
                        <input class="box_input" type="text" name="Email" value="@(Model.UserModel.Email)" notnull="">
                    </div>
                </div>

                <div class="rows clean box_submit">
                    <label></label>
                    <div class="input input_button">
                        <input type="hidden" name="origin_email" value="@(Model.UserModel.Email)">
                        <input type="submit" class="btn_global btn_submit" value="保存">
                        <input type="button" class="btn_global btn_cancel" value="取消">
                    </div>
                </div>
                <input type="hidden" name="UserId" value="@(Model.UserModel.UserId)">
                <input type="hidden" name="do_action" value="/api/CustomerList/UserInfoEditForm">
            </form>
        </div>
    </div>

</div>