@model YseStoreAdmin.Pages.Components.Customer.FormToolTable
@{
}

<div>
    @if (Model.PagerOptions.TotalCount > 0)
    {
        <table border="0" cellpadding="5" cellspacing="0" class="r_con_table new">
            <thead>
                <tr>
                    <td width="8%" nowrap="" class="pos">
                        <ul class="table_menu_button global_menu_button">
                            <li>
                                <div class="btn_checkbox ">
                                    <em class="button"></em>
                                    <input type="checkbox"
                                    name="select_all" value="">
                                </div>
                            </li>
                            <li class="open">已选择<span></span>个</li>
                            <li><a class="del" href="javascript:;">删除</a></li>
                        </ul>
                    </td>
                    <td width="15%" nowrap="">ID</td>
                    <td width="15%" nowrap="">表单名称</td>
                    <td width="50%" nowrap="">表单字段</td>
                    <td width="20%" nowrap="">表单内容</td>
                    <td width="200" nowrap="" class="operation"></td>
                </tr>
            </thead>
            <tbody>
                @foreach (var item in Model.FromList.data)
                {
                    <tr>
                        <td>
                            <div class="btn_checkbox ">
                                <em class="button"></em><input type="checkbox" name="select" value="@item.FId">
                            </div>
                        </td>
                        <td>@item.FId</td>
                        <td>@item.Name</td>
                        <td>
                            @foreach (var field in item.fields)
                            {
                                <span class="field_item">@field.Name</span>
                            }

                        </td>
                        <td>
                            共@(item.dataCount)条记录<a href="/Customer/FormToolData/?id=@item.FId" class="data_view_btn">查看</a>
                            @if (item.noReadCount > 0)
                            {
                                <span class="unread_count">@item.noReadCount</span>
                            }
                        </td>
                        <td class="operation">
                            <a class="icon_edit oper_icon button_tips"
                               href="/Customer/FormToolEdit/?id=@item.FId">修改</a>
                        </td>
                    </tr>
                }


            </tbody>
        </table>
    }
    else
    {
        <div class="bg_no_table_data" style="height: 469px;">
            <div class="content" style="top: 194.5px;">
                <p>当前暂时没有数据</p>
            </div><span></span>
        </div>
    }
</div>