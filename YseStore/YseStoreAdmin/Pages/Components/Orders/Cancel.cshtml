@using Newtonsoft.Json.Linq;
@using YseStore.Model.Enums;
@using Newtonsoft.Json;
@using YseStore.Common;
@using Entitys;
@model YseStoreAdmin.Pages.Components.Orders.Cancel
@{
}
<div>
    <div id="orders_inside" class="r_con_wrap" style="height: 747px;">
        <div class="center_container_1200 orders_cancel clean">
            <div class="return_title">
                <a href="/Orders/Detail?id=@(Model.OrderDetailModel.OrderId)&query_string=">
                    <span class="return">取消订单</span>
                    <span class="s_return">/ NO.#@(Model.OrderDetailModel.OId)</span>
                </a>
            </div>
            <div class="left_container">
                <div class="left_container_side">
                    <form class="global_form" id="edit_form">
                        <div class="global_container products_box">
                            <div class="big_title">产品<span class="explain">（共@(Model.ProductCount)个）</span></div>
                            <div class="restock_box">
                                <span class="input_checkbox_box checked">
                                    <span class="input_checkbox">
                                        <input type="checkbox" name="Restock" value="" checked="">
                                    </span>所有产品重新入库
                                </span>
                            </div>
                        </div>
                        <div class="global_container reason_box">
                            <div class="big_title">取消原因<span class="box_explain">（仅后台可见）</span></div>
                            <div class="rows clean reason_input">
                                <div class="input"><textarea type="text" class="box_textarea" name="cancelReason"></textarea></div>
                            </div>

                            <input type="hidden" name="OrderId" value="@(Model.OrderDetailModel.OrderId)">
                        </div>
                    </form>
                </div>
            </div>
            <div class="right_container">
                <div class="global_container">
                    <div class="big_title">退款</div>
                    <div class="rows clean">
                        <label>退款金额</label>
                        <div class="input">
                            <span class="amount_content">@(Model.OrderSymbol)@(Model.OrderSum)</span>
                        </div>
                    </div>

                    <div class="rows method_box clean @(Model.isRefund?"":"hide")">
                        <label>退款方式</label>
                        <div class="input">
                            <div class="box_type_menu fl">
                                <span class="item checked">
                                    <input type="radio" name="RefundType" value="original" checked="checked">原路退回
                                </span>
                                <span class="item">
                                    <input type="radio" name="RefundType" value="offline">线下退回
                                </span>
                            </div>
                        </div>
                    </div>
                    <div class="rows clean return_method">
                        <label>
                            <span class="online" style="display: inline;">原路退回方式</span>
                            <span class="offline" style="display: none;">线下返回方式(选填)</span>
                        </label>
                        <div class="input">
                            <div class="refundBox">
                                <div class="box tips" data-method="original" style="display: block;">
                                    @(Model.paymentMethod)
                                    @if (!string.IsNullOrEmpty(Model.originalTips))
                                    {
                                        <div class="global_app_tips obvious">
                                            <em></em>
                                            @(Model.originalTips)
                                        </div>
                                    }
                                   

                                </div>
                                <div class="blank6"></div>
                                <div class="box" data-method="offline" >
                                    <input type="text" class="box_input left_radius full_input" name="PaymentMethod" value="" maxlength="100" placeholder="@(Model.paymentMethod)" rel="amount" data-currency-code="@(Model.OrderDetailModel.Currency)">
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="rows amount_box clean @(Model.isRefund?"":"just_show")" style="display: none;">
                        <div class="amount_title"><span>退款金额</span></div>
                        <div class="amount_text" data-symbol="@(Model.OrderSymbol)" data-price="@(Model.OrderSum)"></div>
                        <div class="amount_input">
                            <div class="input">
                                <div>
                                    <span class="unit_input">
                                        <b>@(Model.OrderDetailModel.Currency) @(Model.OrderSymbol)</b>
                                        <input type="text" class="box_input left_radius" name="cancelAmount" value="0.00" maxlength="24" placeholder="0" rel="amount">
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="rows clean box_submit fixed_btn_submit" style="width: 1160px; left: 180px;">
        <div class="center_container_1200">
            <div class="input input_button">
                <input type="submit" class="btn_global btn_submit btn_cancel_submit" value="取消订单并退款@(Model.OrderSymbol)0.00" disabled="">
                <a href="/Orders/Detail?id=@(Model.OrderDetailModel.OrderId)&query_string=" class="btn_global btn_cancel">返回</a>
            </div>
        </div>
    </div>
</div>
