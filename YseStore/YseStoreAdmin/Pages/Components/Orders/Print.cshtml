@using Newtonsoft.Json.Linq;
@using YseStore.Model.Enums;
@using Newtonsoft.Json;
@using YseStore.Common;
@using Entitys;
@using YseStore.IService;
@inject IHelpsCartService _helpsCartService
@model YseStoreAdmin.Pages.Components.Orders.Print
@{

}


<div id="orders_inside" class="r_con_wrap">
    <div id="orders_print">
        @if (Model.PrintDataList != null && Model.PrintDataList.Count > 0)
        {
            foreach (var itemPrint in Model.PrintDataList)
            {


                @if (Model.printType == "picking")
                {
                    <div class="orders_print" style="margin: 20px auto; page-break-after:always;">
                        <div class="head_table">
                            <div class="right" style="float: right; min-width: 290px; text-align: right;margin-bottom:10px;">
                                <div class="barcode" style="width: 100%; margin-bottom: 5px;">
                                    <img src="@(itemPrint.barcode)">
                                </div>
                                <div class="item" style="width: 100%">NO.: @(itemPrint.OrderDetailModel.OId)</div>
                                <div class="item">Date:&nbsp;&nbsp;@(DateTimeHelper.ConvertUnixTimestampToBeijingTime((long)itemPrint.OrderDetailModel.OrderTime, "dd/MM/yyy"))</div>
                            </div>
                            <div class="clear"></div>
                        </div>
                        <div class="address_list">
                            <dl>
                                <dt>发货地址</dt>
                                <dd>@(itemPrint.Compeny)</dd>
                                <dd>@(itemPrint.Address)</dd>
                                <dd>Email: @(itemPrint.AdminEmail)</dd>
                                <dd>Tel: @(itemPrint.Telephone)</dd>
                            </dl>
                            <dl>
                                <dt>收货地址</dt>
                                <dd>@(itemPrint.OrderDetailModel.ShippingFirstName) @(itemPrint.OrderDetailModel.ShippingLastName)</dd>
                                <dd>@(itemPrint.OrderDetailModel.ShippingAddressLine1), @(itemPrint.OrderDetailModel.ShippingAddressLine2), @(itemPrint.OrderDetailModel.ShippingCity), @(itemPrint.OrderDetailModel.ShippingState), @(itemPrint.OrderDetailModel.ShippingZipCode), @(itemPrint.OrderDetailModel.ShippingCountry)@(!string.IsNullOrEmpty(itemPrint.OrderDetailModel.ShippingTaxCode) ? "#: " + itemPrint.OrderDetailModel.ShippingTaxCode : "")</dd>
                                <dd>@(itemPrint.OrderDetailModel.ShippingCountryCode)-@(itemPrint.OrderDetailModel.ShippingPhoneNumber)</dd>
                            </dl>
                        </div>
                        @if (itemPrint.OrderPackage != null)
                        {
                            List<orders_package> OrdersPackageList = new List<orders_package>();
                            OrdersPackageList = itemPrint.OrderPackage.Where(x => x.ParentId != 0 && x.ProInfo != "[]").ToList();
                            if (OrdersPackageList.Count == 0)
                            {
                                OrdersPackageList = itemPrint.OrderPackage.Where(x => x.ParentId == 0 && x.ProInfo != "[]").ToList();
                            }
                            int PackageCount = 1;
                            foreach (var item in OrdersPackageList)
                            {
                                if (!string.IsNullOrEmpty(item.ProInfo) && item.ProInfo != "[]")
                                {
                                    <div class="box_package">
                                        @{
                                            var dict = JsonConvert.DeserializeObject<Dictionary<string, int>>(item.ProInfo);
                                            List<int> productIds = dict.Keys.Select(int.Parse).ToList();
                                            dynamic dataCarrier = new JObject();
                                            if (!string.IsNullOrEmpty(item.Carrier) && item.Carrier != null)
                                            {
                                                dataCarrier = JObject.Parse(item.Carrier);
                                            }
                                        }
                                        @if (Convert.ToBoolean(item.Status))
                                        {
                                            <div class="package_title">
                                                已发货包裹@(PackageCount)
                                            </div>
                                            <div class="package_info">
                                                <span>物流商: @(dataCarrier.name)</span>
                                                <span>运单号: @(item.TrackingNumber)</span>
                                                @if (!string.IsNullOrEmpty(item.Warehouse))
                                                {
                                                    <span>仓库: @(item.Warehouse)</span>
                                                }

                                            </div>
                                        }
                                        else
                                        {
                                            <div class="package_title">
                                                未发货包裹@(PackageCount)
                                            </div>
                                            <div class="package_info">
                                                @if (!string.IsNullOrEmpty(item.Warehouse))
                                                {
                                                    <span>仓库: @(item.Warehouse)</span>
                                                }
                                            </div>
                                        }
                                        <table class="print_table" width="612" border="0" align="center">
                                            <thead>
                                                <tr>
                                                    <th width="7%">编号</th>
                                                    <th width="51%">产品</th>
                                                    <th width="21%">属性</th>
                                                    <th width="21%">数量</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                @if (itemPrint.OrderProductsLists != null)
                                                {
                                                    var j = 1;
                                                    var ConformProductsLists = itemPrint.OrderProductsLists.Where(opl => productIds.Contains(opl.LId)).ToList();
                                                    foreach (var items in ConformProductsLists)
                                                    {
                                                        <tr>
                                                            <td>@j</td>
                                                            <td>
                                                                <dl>
                                                                    <dt><img src="@(!string.IsNullOrEmpty(items.PicPath)?items.PicPath+"?x-oss-process=image/format,webp/resize,m_lfit,h_120,w_120":"")" title="@items.Name" alt="@items.Name"></dt>
                                                                    <dd>
                                                                        <h4>@items.Name</h4>
                                                                        @if (!string.IsNullOrEmpty(items.SKU))
                                                                        {
                                                                            <p class="pro_attr">SKU: @(items.SKU)</p>
                                                                        }
                                                                        <p></p>
                                                                    </dd>
                                                                </dl>
                                                            </td>
                                                            <td>
                                                                @if (!string.IsNullOrEmpty(items.Property) && items.Property != "[]")
                                                                {
                                                                    var dictProperty = JsonConvert.DeserializeObject<Dictionary<string, string>>(items.Property);
                                                                    @foreach (var kvp in dictProperty)
                                                                    {
                                                                        <p class="pro_attr">@(kvp.Key): @(kvp.Value)</p>
                                                                    }
                                                                }
                                                                <div class="custom_attr"></div>
                                                            </td>
                                                            @{
                                                                int value = dict.ContainsKey(items.LId.ToString()) ? dict[items.LId.ToString()] : 0;
                                                            }
                                                            @if (value != 0)
                                                            {
                                                                <td>@(value)</td>
                                                            }

                                                        </tr>
                                                        j++;
                                                    }
                                                }

                                            </tbody>
                                        </table>
                                    </div>
                                    PackageCount++;
                                }
                            }

                        }


                        <div class="comments">
                            <div class="title">客户备注</div>
                            <div class="content">@(itemPrint.OrderDetailModel.Comments)</div>
                        </div>
                        <div class="tips">* All transactions are subject to the Company's Standard Trading Conditions (copy is available upon request), which in certain circumstances, limit or exempt the Company's liability.</div>
                    </div>
                }
                else if (Model.printType == "delivery")
                {
                    @if (itemPrint.OrderPackage != null)
                    {
                        List<orders_package> OrdersPackageList = new List<orders_package>();
                        OrdersPackageList = itemPrint.OrderPackage.Where(x => x.ParentId != 0 && x.ProInfo != "[]").ToList();
                        if (OrdersPackageList.Count == 0)
                        {
                            OrdersPackageList = itemPrint.OrderPackage.Where(x => x.ParentId == 0 && x.ProInfo != "[]").ToList();
                        }
                        int PackageCount = 1;
                        foreach (var item in OrdersPackageList)
                        {
                            if (!string.IsNullOrEmpty(item.ProInfo) && item.ProInfo != "[]")
                            {

                                <div class="orders_print" style="margin: 20px auto; page-break-after:always;">
                                    <div class="head_table">
                                        <div class="right" style="float: right; min-width: 290px; text-align: right;margin-bottom:10px;">
                                            <div class="barcode" style="width: 100%; margin-bottom: 5px;">
                                                <img src="@(itemPrint.barcode)">
                                            </div>
                                            <div class="item" style="width: 100%">NO.: @(itemPrint.OrderDetailModel.OId)</div>
                                            <div class="item">Date:&nbsp;&nbsp;@(DateTimeHelper.ConvertUnixTimestampToBeijingTime((long)itemPrint.OrderDetailModel.OrderTime, "dd/MM/yyy"))</div>
                                        </div>
                                        <div class="left" style="float: left;"><img src="@(itemPrint.orderPrintLogo)" alt="" style="max-width: 200px;"></div>
                                        <div class="clear"></div>
                                        <div class="" style=" margin: 10px 0 20px; font-size: 24px;">@(itemPrint.siteName)</div>
                                    </div>
                                    <div class="address_list">
                                        <dl>
                                            <dt>Sender Address</dt>
                                            <dd>@(itemPrint.Compeny)</dd>
                                            <dd>@(itemPrint.Address)</dd>
                                            <dd>Email: @(itemPrint.AdminEmail)</dd>
                                            <dd>Tel: @(itemPrint.Telephone)</dd>
                                        </dl>
                                        <dl>
                                            <dt>Shipping address</dt>
                                            <dd>@(itemPrint.OrderDetailModel.ShippingFirstName) @(itemPrint.OrderDetailModel.ShippingLastName)</dd>
                                            <dd>@(itemPrint.OrderDetailModel.ShippingAddressLine1), @(itemPrint.OrderDetailModel.ShippingAddressLine2), @(itemPrint.OrderDetailModel.ShippingCity), @(itemPrint.OrderDetailModel.ShippingState), @(itemPrint.OrderDetailModel.ShippingZipCode), @(itemPrint.OrderDetailModel.ShippingCountry)@(!string.IsNullOrEmpty(itemPrint.OrderDetailModel.ShippingTaxCode) ? "#: " + itemPrint.OrderDetailModel.ShippingTaxCode : "")</dd>
                                            <dd>@(itemPrint.OrderDetailModel.ShippingCountryCode)-@(itemPrint.OrderDetailModel.ShippingPhoneNumber)</dd>
                                        </dl>
                                    </div>
                                    @{
                                        var dict = JsonConvert.DeserializeObject<Dictionary<string, int>>(item.ProInfo);
                                        List<int> productIds = dict.Keys.Select(int.Parse).ToList();
                                    }
                                    <table class="print_table" width="612" border="0" align="center" style="margin:0 auto;">
                                        <thead>
                                            <tr>
                                                <th width="5%">No.</th>
                                                <th width="50%">Product</th>
                                                <th width="15%">Price</th>
                                                <th width="15%">Quantity</th>
                                                <th width="15%">Amount</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @if (itemPrint.OrderProductsLists != null)
                                            {
                                                var j = 1;
                                                var ConformProductsLists = itemPrint.OrderProductsLists.Where(opl => productIds.Contains(opl.LId)).ToList();
                                                foreach (var items in ConformProductsLists)
                                                {
                                                    <tr>
                                                        <td>@j</td>
                                                        <td>
                                                            <dl>
                                                                <dt><img src="@(!string.IsNullOrEmpty(items.PicPath)?items.PicPath+"?x-oss-process=image/format,webp/resize,m_lfit,h_120,w_120":"")" title="@items.Name" alt="@items.Name"></dt>
                                                                <dd>
                                                                    <h4>@items.Name</h4>
                                                                    @if (!string.IsNullOrEmpty(items.SKU))
                                                                    {
                                                                        <p class="pro_attr">No: @(items.SKU)</p>
                                                                        <p class="pro_attr">SKU: @(items.SKU)</p>
                                                                    }
                                                                  
                                                                    @if (!string.IsNullOrEmpty(items.Property) && items.Property != "[]")
                                                                    {
                                                                        var dictProperty = JsonConvert.DeserializeObject<Dictionary<string, string>>(items.Property);
                                                                        @foreach (var kvp in dictProperty)
                                                                        {
                                                                            <p class="pro_attr">@(kvp.Key): @(kvp.Value)</p>
                                                                        }

                                                                    }
                                                                    <div class="custom_attr"></div>
                                                                    <p></p>
                                                                </dd>
                                                            </dl>
                                                        </td>
                                                        <td>
                                                            @if (Model.currency == "ManageCurrency")
                                                            {
                                                                @(itemPrint.OrderSymbol)
                                                                @(items.Price)
                                                            }
                                                            else
                                                            {
                                                                @(await _helpsCartService.IconvPriceFormat(items.Price.Value, 0, Model.OrderDetailModel.Currency, Model.OrderDetailModel.Rate))
                                                            }
                                                                                                                      
                                                        </td>
                                                        @{
                                                            int value = dict.ContainsKey(items.LId.ToString()) ? dict[items.LId.ToString()] : 0;
                                                        }
                                                        @if (value != 0)
                                                        {
                                                            <td>@(value)</td>
                                                            <td>
                                                                @if (Model.currency == "ManageCurrency")
                                                                {
                                                                    @(itemPrint.OrderSymbol)
                                                                    @(items.Price * value)
                                                                }
                                                                else
                                                                {
                                                                    @(_helpsCartService.IconvPrice((items.Price.Value * value), 0, Model.OrderDetailModel.Currency, Model.OrderDetailModel.Rate))
                                                                
                                                                }
                                                               
                                                                </td>
                                                        }
                                                    </tr>
                                                    j++;
                                                }
                                            }

                                            <tr class="collect">
                                                <td colspan="4" class="big" align="right">SubTotal:</td>
                                                <td class="big">@(itemPrint.OrderSymbol)@(itemPrint.TotalProductPrice)</td>
                                            </tr>
                                            <!-- 折扣 -->
                                            @if (itemPrint.DiscountPrice > 0)
                                            {
                                                <tr class="collect">
                                                    <td colspan="4" class="big" align="right">Discount:</td>
                                                    <td class="big">-@(itemPrint.OrderSymbol)@(itemPrint.DiscountPrice)</td>
                                                </tr>
                                            }
                                            @if (itemPrint.CouponPrice > 0)
                                            {
                                                <tr class="collect">
                                                    <td colspan="4" class="big" align="right">Coupon:</td>
                                                    <td class="big">-@(itemPrint.OrderSymbol)@(itemPrint.CouponPrice)</td>
                                                </tr>
                                            }
                                            @if (itemPrint.points > 0)
                                            {
                                                <tr class="collect">
                                                    <td colspan="4" class="big" align="right">Points:</td>
                                                    <td class="big">-@(itemPrint.OrderSymbol)@(itemPrint.points)</td>
                                                </tr>
                                            }

                                            <!-- 运费 -->
                                            <tr class="collect">
                                                <td colspan="4" class="big" align="right">Shipping:</td>
                                                <td class="big">@(itemPrint.OrderSymbol)@(itemPrint.shippingFee)</td>
                                            </tr>
                                            <!-- 税费 -->
                                            <tr class="collect">
                                                <td colspan="4" class="big" align="right">Taxes:</td>
                                                <td class="big">@(itemPrint.OrderSymbol)@(Math.Round(itemPrint.OrderDetailModel.Tax, 2))</td>
                                            </tr>
                                            <!-- 手续费 -->
                                            <tr>
                                                <td colspan="4" class="big" align="right">Handling Fee:</td>
                                                <td class="big">@(itemPrint.OrderSymbol)@(itemPrint.commission)</td>
                                            </tr>
                                            <!-- 删除产品 -->
                                            <!-- 退款产品 -->
                                            @if (itemPrint.refundedTotalPrice > 0)
                                            {
                                                <tr class="collect">
                                                    <td colspan="4" class="big" align="right">Refunded:</td>
                                                    <td class="big">-@(itemPrint.OrderSymbol)@(itemPrint.refundedTotalPrice)</td>
                                                </tr>
                                            }

                                            <!-- 订单总额 -->
                                            <tr class="collect">
                                                <td colspan="4" class="big" align="right">Grand Total:</td>
                                                <td class="big">@(itemPrint.OrderSymbol)@(itemPrint.OrderSum)</td>
                                            </tr>
                                        </tbody>
                                    </table>
                                    <div class="comments">
                                        <div class="title">Special Instructions</div>
                                        <div class="content">@(itemPrint.OrderDetailModel.Comments)</div>
                                    </div>
                                    <div class="tips">* All transactions are subject to the Company's Standard Trading Conditions (copy is available upon request), which in certain circumstances, limit or exempt the Company's liability.</div>
                                </div>

                                PackageCount++;
                            }
                        }
                    }


                }
                else
                {
                    <div class="orders_print" style="margin: 20px auto; page-break-after:always;">
                        <div class="head_table">
                            <div class="right" style="float: right; min-width: 290px; text-align: right;margin-bottom:10px;">
                                <div class="barcode" style="width: 100%; margin-bottom: 5px;">
                                    <img src="@(itemPrint.barcode)">
                                </div>
                                <div class="item" style="width: 100%">NO.: @(itemPrint.OrderDetailModel.OId)</div>
                                <div class="item">Date:&nbsp;&nbsp;@(DateTimeHelper.ConvertUnixTimestampToBeijingTime((long)itemPrint.OrderDetailModel.OrderTime, "dd/MM/yyy"))</div>
                            </div>
                            @if (Model.printconfig.Contains("store_logo"))
                            {
                                <div class="left" style="float: left;"><img src="@(itemPrint.orderPrintLogo)" alt="" style="max-width: 200px;"></div>
                            }
                            <div class="clear"></div>

                            @if (Model.printconfig.Contains("store_name"))
                            {
                                <div class="" style=" margin: 10px 0 20px; font-size: 24px;">@(itemPrint.siteName)</div>
                            }

                        </div>
                        <div class="address_list">
                            <dl>
                                <dt>Sender Address</dt>
                                <dd>@(itemPrint.Compeny)</dd>
                                <dd>@(itemPrint.Address)</dd>
                                <dd>Email: @(itemPrint.AdminEmail)</dd>
                                <dd>Tel: @(itemPrint.Telephone)</dd>
                            </dl>
                            <dl>
                                <dt>Shipping address</dt>
                                <dd>@(itemPrint.OrderDetailModel.ShippingFirstName) @(itemPrint.OrderDetailModel.ShippingLastName)</dd>
                                <dd>@(itemPrint.OrderDetailModel.ShippingAddressLine1), @(itemPrint.OrderDetailModel.ShippingAddressLine2), @(itemPrint.OrderDetailModel.ShippingCity), @(itemPrint.OrderDetailModel.ShippingState), @(itemPrint.OrderDetailModel.ShippingZipCode), @(itemPrint.OrderDetailModel.ShippingCountry)@(!string.IsNullOrEmpty(itemPrint.OrderDetailModel.ShippingTaxCode) ? "#: " + itemPrint.OrderDetailModel.ShippingTaxCode : "")</dd>
                                <dd>@(itemPrint.OrderDetailModel.ShippingCountryCode)-@(itemPrint.OrderDetailModel.ShippingPhoneNumber)</dd>
                            </dl>
                        </div>
                        <table class="print_table" width="612" border="0" align="center" style="margin:0 auto;">
                            <thead>
                                <tr>
                                    @if (Model.printconfig.Contains("proserial"))
                                    {
                                        <th width="5%">No.</th>
                                    }
                                    @if (Model.printconfig.Contains("propic") || Model.printconfig.Contains("proname") || Model.printconfig.Contains("pronumber")
                                   || Model.printconfig.Contains("prosku") || Model.printconfig.Contains("proattr"))
                                    {
                                        <th width="50%">Product</th>
                                    }


                                    @if (Model.printconfig.Contains("item_price"))
                                    {
                                        <th width="15%">Price</th>
                                    }
                                    @if (Model.printconfig.Contains("proqty"))
                                    {
                                        <th width="15%">Quantity</th>
                                    }
                                    @if (Model.printconfig.Contains("total_price"))
                                    {
                                        <th width="15%">Amount</th>
                                    }

                                </tr>
                            </thead>
                            <tbody>
                                @if (itemPrint.OrderPackage != null)
                                {
                                    var OrdersPackageList = itemPrint.OrderPackage.Where(x => x.ParentId == 0 && x.ProInfo != "[]").ToList();
                                    var dict = new Dictionary<string, int>();
                                    if (OrdersPackageList!=null&&OrdersPackageList.Count>0)
                                    {
                                        foreach (var item in OrdersPackageList)
                                        {

                                            // 反序列化当前项的ProInfo
                                            var currentDict = JsonConvert.DeserializeObject<Dictionary<string, int>>(item.ProInfo);

                                            // 合并到主字典（根据业务需求选择合并策略）
                                            foreach (var kvp in currentDict)
                                            {
                                                // 策略1：覆盖已存在的键
                                                dict[kvp.Key] = kvp.Value;

                                                // 策略2：保留已存在的键（取消注释使用）
                                                // if (!dict.ContainsKey(kvp.Key))
                                                // {
                                                //     dict.Add(kvp.Key, kvp.Value);
                                                // }
                                            }
                                            
                                        }
                                    }
                                     @* dict = JsonConvert.DeserializeObject<Dictionary<string, int>>(OrdersPackageFirst.ProInfo); *@
                                    List<int> productIds = dict.Keys.Select(int.Parse).ToList();
                                    @if (itemPrint.OrderProductsLists != null)
                                    {
                                        var j = 1;
                                        var ConformProductsLists = itemPrint.OrderProductsLists.Where(opl => productIds.Contains(opl.LId)).ToList();
                                        foreach (var items in ConformProductsLists)
                                        {
                                            <tr>
                                                @if (Model.printconfig.Contains("proserial"))
                                                {
                                                    <td>@j</td>
                                                }

                                                <td>
                                                    <dl>
                                                        @if (Model.printconfig.Contains("propic"))
                                                        {
                                                            <dt><img src="@(!string.IsNullOrEmpty(items.PicPath)?items.PicPath+"?x-oss-process=image/format,webp/resize,m_lfit,h_120,w_120":"")" title="@items.Name" alt="@items.Name"></dt>
                                                        }

                                                        <dd>
                                                            @if (Model.printconfig.Contains("proname"))
                                                            {
                                                                <h4>@items.Name</h4>
                                                            }
                                                            @if (Model.printconfig.Contains("pronumber"))
                                                            {
                                                                @if (!string.IsNullOrEmpty(items.SKU))
                                                                {
                                                                    <p class="pro_attr">No: @(items.SKU)</p>
                                                                }
                                                            }
                                                            @if (Model.printconfig.Contains("prosku"))
                                                            {
                                                                @if (!string.IsNullOrEmpty(items.SKU))
                                                                {
                                                                    <p class="pro_attr">SKU: @(items.SKU)</p>
                                                                }
                                                            }
                                                            @if (Model.printconfig.Contains("proattr"))
                                                            {
                                                                if (!string.IsNullOrEmpty(items.Property) && items.Property != "[]")
                                                                {
                                                                    var dictProperty = JsonConvert.DeserializeObject<Dictionary<string, string>>(items.Property);
                                                                    @foreach (var kvp in dictProperty)
                                                                    {
                                                                        <p class="pro_attr">@(kvp.Key):  @(kvp.Value)</p>
                                                                    }
                                                                }
                                                            }

                                                            <div class="custom_attr"></div>
                                                            <p></p>
                                                        </dd>
                                                    </dl>
                                                </td>

                                                @if (Model.printconfig.Contains("item_price"))
                                                {
                                                    <td>
                                                        @if (Model.currency == "ManageCurrency")
                                                        {
                                                            @(itemPrint.OrderSymbol)
                                                            @(items.Price)
                                                        }
                                                        else
                                                        {
                                                            @(await _helpsCartService.IconvPriceFormat(items.Price.Value, 0, Model.OrderDetailModel.Currency, Model.OrderDetailModel.Rate))
                                                        }
                                                    </td>
                                                }

                                                @{
                                                    int value = dict.ContainsKey(items.LId.ToString()) ? dict[items.LId.ToString()] : 0;
                                                }
                                                @if (value != 0)
                                                {
                                                    @if (Model.printconfig.Contains("proqty"))
                                                    {
                                                        <td>@(value)</td>
                                                    }
                                                    @if (Model.printconfig.Contains("total_price"))
                                                    {
                                                        <td> 
                                                            @if (Model.currency == "ManageCurrency")
                                                            {
                                                                @(itemPrint.OrderSymbol)
                                                                @(items.Price * value)
                                                            }
                                                            else
                                                            {
                                                                @(_helpsCartService.IconvPrice((items.Price.Value * value), 0, Model.OrderDetailModel.Currency, Model.OrderDetailModel.Rate))

                                                            }
                                                        </td>
                                                    }
                                                }

                                            </tr>
                                            j++;
                                        }
                                    }

                                }
                                @if (Model.printconfig.Contains("sub_price"))
                                {
                                    <tr class="collect">
                                        <td colspan="4" class="big" align="right">SubTotal:</td>
                                        <td class="big">@(itemPrint.OrderSymbol)@(itemPrint.TotalProductPrice)</td>
                                    </tr>
                                }
                                @if (Model.printconfig.Contains("discount"))
                                {
                                    @if (itemPrint.DiscountPrice>0)
                                    {
                                        <tr class="collect">
                                            <td colspan="4" class="big" align="right">Discount:</td>
                                            <td class="big">-@(itemPrint.OrderSymbol)@(itemPrint.DiscountPrice)</td>
                                        </tr>
                                    }
                                    @if (itemPrint.CouponPrice>0)
                                    {
                                        <tr class="collect">
                                            <td colspan="4" class="big" align="right">Coupon:</td>
                                            <td class="big">-@(itemPrint.OrderSymbol)@(itemPrint.CouponPrice)</td>
                                        </tr>
                                    }
                                    @if (itemPrint.points>0)
                                    {
                                        <tr class="collect">
                                            <td colspan="4" class="big" align="right">Points:</td>
                                            <td class="big">-@(itemPrint.OrderSymbol)@(itemPrint.points)</td>
                                        </tr>
                                    }
                                }
                                <!-- 运费 -->
                                @if (Model.printconfig.Contains("freight"))
                                {
                                    <tr class="collect">
                                        <td colspan="4" class="big" align="right">Shipping:</td>
                                        <td class="big">@(itemPrint.OrderSymbol)@(itemPrint.shippingFee)</td>
                                    </tr>
                                }
                                <!-- 税费 -->
                                @if (Model.printconfig.Contains("taxes"))
                                {
                                    <tr class="collect">
                                        <td colspan="4" class="big" align="right">Taxes:</td>
                                        <td class="big">@(itemPrint.OrderSymbol)@(Math.Round(itemPrint.OrderDetailModel.Tax, 2))</td>
                                    </tr>
                                }

                                <!-- 手续费 -->
                                @if (Model.printconfig.Contains("fee_price"))
                                {
                                    <tr>
                                        <td colspan="4" class="big" align="right">Handling Fee:</td>
                                        <td class="big">@(itemPrint.OrderSymbol)@(itemPrint.commission)</td>
                                    </tr>
                                }

                                <!-- 删除产品 -->
                                <!-- 退款产品 -->
                                <!-- 订单总额 -->
                                @if (Model.printconfig.Contains("order_total"))
                                {
                                    @if (itemPrint.refundedTotalPrice > 0)
                                    {
                                        <tr class="collect">
                                            <td colspan="4" class="big" align="right">Refunded:</td>
                                            <td class="big">-@(itemPrint.OrderSymbol)@(itemPrint.refundedTotalPrice)</td>
                                        </tr>
                                    }
                                    <tr class="collect">
                                        <td colspan="4" class="big" align="right">Grand Total:</td>
                                        <td class="big">@(itemPrint.OrderSymbol)@(itemPrint.OrderSum)</td>
                                    </tr>
                                }

                            </tbody>
                        </table>
                        <div class="comments">
                            <div class="title">Special Instructions</div>
                            <div class="content">@(itemPrint.OrderDetailModel.Comments)</div>
                        </div>
                        <div class="tips">* All transactions are subject to the Company's Standard Trading Conditions (copy is available upon request), which in certain circumstances, limit or exempt the Company's liability.</div>
                    </div>
                }


            }
        }

    </div>

</div>


























