 
@model YseStoreAdmin.Pages.Components.Orders.OrderProducts 
@{
}
<div>
    <mx-drawer-layout close="@(() => Model.Close())" id="@(Model.DrawerId)">
        <slot name="title">My title</slot>

        <div>
            11
        </div>

        <slot name="actions">

            <mx-button primary click="@(() => Model.Save())">Save</mx-button>

            <mx-button click="@(() => Model.Close())">取消</mx-button>
        </slot>
    </mx-drawer-layout>
</div>