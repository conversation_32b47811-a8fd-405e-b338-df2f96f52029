 
@model YseStoreAdmin.Pages.Components.Orders.OrderCreate3 
@{
}










<div>

		<div id="orders_create" class="r_con_wrap orders_create" style="height: 488px;">
			<div class="center_container_1000 authorization_container">
				<div class="return_title"><a href="/manage/orders/orders"> <span class="return">订单管理 / 添加订单</span></a></div>
				<div class="step_box">
					<div class="list ">
						<div class="num"><span>1</span></div>
						<div class="tit">添加产品清单</div>
					</div>
					<div class="list ">
						<div class="num"><span>2</span></div>
						<div class="tit">添加客户收货信息</div>
					</div>
					<div class="list current">
						<div class="num"><span>3</span></div>
						<div class="tit">计算价格和付款</div>
					</div>
				</div>
				<form class="global_form" id="create_third_submit" data-fixed-submit="true">
					<div class="global_container cost ">
						<div class="big_title">价格</div>
						<div class="rows th">
							<label>费用</label>
							<div class="r_box">价格</div>
							<div class="operate"></div>
						</div>
						<div class="rows">
							<label>
								产品总价							<div class="p_num">
									<div class="mid">1个产品<i></i></div>
									<div class="box_products">
										<div class="list_item">
											<div class="item_img">
												<img src="">
											</div>
											<div class="item_info">
												<div class="info_name">000</div>
												<div class="info_attr">
												</div>
											</div>
										</div>
									</div>
								</div>
							</label>
							<div class="r_box pro_price">
								<div class="to_price">$0.00<span class="web_rate">$0.00</span></div>
							</div>
							<div class="operate"></div>
						</div>
						<div class="rows">
							<label>折扣</label>
							<div class="r_box flex_box">
								<div class="box_left">
									<div id="OrderDiscount" class="discount">
										<span data-discount-type="0">-$<span id="DiscountPrice">0.00</span><span id="rateDiscountPrice" class="web_rate">$0.00</span></span>
										<span class="hide" data-discount-type="1"><span id="Discount">0</span> % off</span>
									</div>
								</div>
							</div>
							<div class="operate"><a href="javascript:;" class="btn_rig btn_set_discount">修改</a></div>
						</div>
						<div class="rows">
							<label>
								包裹1运费								<div class="p_num">
									<div class="mid">1个产品<i></i></div>
									<div class="box_products">
										<div class="list_item">
											<div class="item_img">
												<img src="">
											</div>
											<div class="item_info">
												<div class="info_name">000</div>
												<div class="info_attr">
												</div>
											</div>
										</div>
									</div>
								</div>
							</label>
							<div class="r_box package" data-id="0-0">
								<input type="hidden" name="ShippingMethod[0-0]" value="">
								<input type="hidden" name="ShippingName[0-0]" value="">
								<input type="hidden" name="ShippingPrice[0-0]" value="0.00">
								<div class="flex_box">
									<div class="box_left">
										<div class="tit"></div>
										<div class="desc no_shipping">暂无运费</div>
										<div class="desc has_shipping hide">
											<span>$<em class="pack_ship_price"></em></span>
											<span class="web_rate rate_pack_ship_price">$0.00</span>
										</div>
										<span class="hide">物流方式: <em class="pack_ship_name"></em></span>
										<span class="hide">重量: 1KG</span>
									</div>
								</div>
							</div>
							<div class="operate"><a href="javascript:;" class="btn_rig btn_set_shipping" data-id="0-0">修改</a></div>
						</div>

						<div class="rows">
							<label>税费</label>
							<div class="r_box flex_box">
								<div class="tax">$<span id="TaxPrice" data-tax-type="0" data-tax="0" data-tax-threshold="0">0.00</span><span id="rateTaxPrice" class="web_rate">$0.00</span></div>
							</div>
							<div class="operate"></div>
						</div>
						<div class="rows fee_box">
							<label>手续费</label>
							<div class="r_box flex_box">
								<div class="fee">$<span id="PayMentFee">0.00</span><span id="ratePayMentFee" class="web_rate">$0.00</span></div>
							</div>
							<div class="operate"></div>
						</div>
						<div class="rows total_box">
							<label>订单总额：</label>
							<div class="total_price">$<span id="OrdersPrice">0.00</span><span id="rateOrdersPrice" class="web_rate">$0.00</span></div>
							<div class="operate"></div>
						</div>
						<div class="rows box_order_rate">
							<label>汇率</label>
							<div class="text_rate">1 USD = 1.0000 USD</div>
							<div class="operate"></div>
						</div>
					</div>
					<div class="global_container">
						<div class="big_title">付款</div>
						<div class="rows clean">
							<label>付款状态</label>
							<div class="input">
								<div class="box_type_menu">
									<span class="item checked"><input type="radio" name="PaymentStatus" value="unpaid" checked="">未付款</span>
									<span class="item "><input type="radio" name="PaymentStatus" value="paid">已付款</span>
								</div>
								<div class="box_type_menu_content">
									<div class="item "></div>
									<div class="item hide">
										<div class="box_filter">
											<div class="filter_list current">
												<div class="filter_title">
													<span class="radio_box">
														<span class="input_radio_box checked">
															<span class="input_radio">
																<input type="radio" name="Type" value="picture" checked="checked">
															</span>
														</span>
													</span>
													上传凭证
												</div>
												<div class="filter_option">
													<div class="input img_box">
														<div class="multi_img upload_file_multi " id="PicPath_0"><dl class="img " num="0"><dt class="upload_box preview_pic"><input type="button" class="btn_ok upload_btn" name="submit_button" value="上传图片" tips=""><input type="hidden" name="PicPath_0" value="" data-value="" save="0"></dt><dd class="pic_btn"><a href="javascript:;" class="zoom" target="_blank"><i class="icon_multi_view"></i></a><a href="javascript:;" class="del" rel="del"><i class="icon_multi_delete"></i></a></dd></dl></div>
													</div>
												</div>
											</div>
											<div class="filter_list">
												<div class="filter_title">
													<span class="radio_box">
														<span class="input_radio_box">
															<span class="input_radio">
																<input type="radio" name="Type" value="info">
															</span>
														</span>
													</span>
													填写凭证
												</div>
												<div class="filter_option">
													<div class="rows clean">
														<label>付款帐号</label>
														<div class="blank5"></div>
														<div class="input">
															<input type="text" class="box_input full_input" name="Account" value="">
														</div>
													</div>
													<div class="rows clean">
														<label>付款流水号</label>
														<div class="blank5"></div>
														<div class="input">
															<input type="text" class="box_input full_input" name="MTCNNumber" value="">
														</div>
													</div>

													<div class="rows clean">
														<label>付款金额</label>
														<div class="blank5"></div>
														<div class="input unit_input" style="font-size: 0">
															<b class="first">
																<div class="box_select">
																	<select name="Currency">
																		<option value="USD">USD</option>
																		<option value="GBP">GBP</option>
																	</select>
																</div>
															</b>
															<input type="text" class="box_input left_radius" rel="float" name="SentMoney" size="32" value="">
														</div>
													</div>

													<div class="rows clean">
														<label>付款时间</label>
														<div class="blank5"></div>
														<div class="input">
															<input type="text" class="box_input full_input input_time" name="AccTime" value="">
														</div>
													</div>

													<div class="rows clean">
														<label>备注</label>
														<div class="blank5"></div>
														<div class="input">
															<input type="text" class="box_input full_input" name="Contents" value="">
														</div>
													</div>
												</div>
											</div>
										</div>
									</div>
								</div>
							</div>
						</div>
						<div class="rows">
							<label>付款方式</label>
							<div class="input">
								<div class="box_select full_select sold_status">
									<select name="PId">
										<option value="" data-fee="0" disabled="disabled" class="hide">请选择</option>
										<option value="154" data-is-online="1" data-fee="4" data-min="0.00" data-max="0.00" no-max-limit="1">Payoneer</option>
										<option value="1" data-is-online="1" data-fee="4" data-min="0.00" data-max="0.00" no-max-limit="1">PayPal</option>
										<option value="9" data-is-online="0" data-fee="0.04" data-min="0.00" data-max="0.00" no-max-limit="1">Cash On Delivery</option>
									</select>
								</div>
							</div>
						</div>
					</div>

					<input type="hidden" name="do_action" value="/manage/orders/orders/create-order">
					<input type="hidden" name="ProductPrice" value="0.00" data-rate="0.00">
					<input type="hidden" name="DiscountType" value="0">
					<input type="hidden" name="Discount" value="0">
					<input type="hidden" name="DiscountPrice" value="0">
				</form>
			</div>
			<div id="fixed_right">
				<div class="global_container box_set_shipping" data-width="440">
					<div class="top_title" data-title="包裹{{number}}选择物流"><strong></strong> <a href="javascript:;" class="close"></a></div>
					<form class="global_form" id="order_package_edit_form">
						<div class="rows">
							<label>物流方式</label>
							<div class="input">
								<div class="global_select_box">
									<div class="input_case">
										<input class="imitation_select box_input full_input" type="text" placeholder="请选择" value="" readonly="" notnull="">
										<input type="hidden" name="ShippingMethod" value="0">
										<i></i>
									</div>
									<ul class="select_ul drop_down">
										<li class="no_data">
											当前暂时没有数据
										</li>
									</ul>
								</div>
							</div>
						</div>
						<div class="rows clean">
							<label>运费</label>
							<div class="input">
								<span class="unit_input"><b>$</b><input type="text" class="box_input left_radius" name="ShippingPrice" value="" size="10" rel="amount" maxlength="10" placeholder="0.00"></span>
							</div>
							<span class="web_rate rateShippingPrice" style="display:none;">$0.00</span>
						</div>
						<input type="hidden" name="PackageId" value="0">
						<div class="rows clean box_button box_submit">
							<div class="input">
								<input type="button" class="btn_global btn_submit" value="保存">
								<input type="button" class="btn_global btn_cancel" value="取消">
							</div>
						</div>
					</form>
				</div>
				<div class="global_container box_set_discount" data-width="440">
					<div class="top_title">修改折扣<a href="javascript:;" class="close"></a></div>
					<div class="global_form">
						<div class="rows">
							<label>折扣</label>
							<div class="input">
								<div class="box_type_menu fl">
									<span class="item checked"><input type="radio" checked="checked" name="DiscountType" value="0">现金减价</span>
									<span class="item"><input type="radio" name="DiscountType" value="1">百分比</span>
								</div>
								<div class="box_type_menu_content fl">
									<div class="item">
										<span class="unit_input"><b>$<div class="arrow"><em></em><i></i></div></b><input type="text" class="box_input left_radius" name="DiscountPrice" value="0" size="10" maxlength="10" rel="amount"></span>
										<span class="web_rate modifyRateDiscountPrice" style="display:none;">$0.00</span>
									</div>
									<div class="item hide">
										<span class="unit_input"><input type="text" class="box_input right_radius" name="Discount" value="10" size="10" maxlength="2" rel="int"><b class="last">% off</b></span>
									</div>
								</div>
								<div class="clear"></div>
							</div>
						</div>
					</div>
					<div class="rows clean box_button box_submit">
						<div class="input">
							<input type="button" class="btn_global btn_submit" value="保存">
							<input type="button" class="btn_global btn_cancel" value="取消">
						</div>
					</div>
				</div>
			</div>
		</div>		<div class="rows clean third_btn_submit fixed_btn_submit" style="width: 2096px; left: 180px;">
			<div class="center_container_1000">
				<div class="input">
					<input type="button" class="btn_global btn_submit" value="保存">
				<a href="/Orders/Create2" class="fl"><input type="button" class="btn_global btn_cancel" value="上一步"></a>
				</div>
			</div>
		</div>
	
</div>