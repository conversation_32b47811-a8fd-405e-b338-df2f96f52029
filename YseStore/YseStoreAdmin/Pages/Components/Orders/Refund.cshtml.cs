using Entitys;
using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using SqlSugar.Extensions;
using YseStore.Common;
using YseStore.IService;
using YseStore.IService.Customer;
using YseStore.IService.Order;
using YseStore.Model.Enums;
using YseStore.Model.VM.Payment.Paypal;
using YseStore.Service;
using YseStoreAdmin.Pages.Mta;

namespace YseStoreAdmin.Pages.Components.Orders
{
    public class Refund : MComponent
    {
        [Parameter]
        [SupplyParameterFromQuery(Name = "Id")]
        public string? Id { get; set; }
        //public int OrderId { get; set; }
        public IOrderListService _orderListService { get; }
        public ICustomerListService _customerListService { get; }
        private readonly IHelpsCartService _helpsCartService;
        public IHelpsUserService _helpsUserService;
        public IHelpsManageService _helpsManageService;
        public ICurrencyService _currencyService;



        public UserResponse UserModel { get; set; }
        public orders OrderDetailModel { get; set; }
        public orders_refund_info OrderRefund { get; set; } = new orders_refund_info();
        public decimal OrderRefundAmount { get; set; }

        public int prevOrder { get; set; }
        public int nextOrder { get; set; }
        public bool isRefund { get; set; }
        public string originalTips { get; set; } = "";


        public List<orders_package>? OrderPackage { get; set; }
        public List<orders_products_list>? OrderProductsLists { get; set; }
        public List<orders_log>? OrdersLogs { get; set; }
        public List<orders_remark_log>? OrdersRemarkLogs { get; set; }
        public List<orders_tags>? OrdersTags { get; set; }
        public List<orders_tags>? Orders_TagsBy { get; set; }



        public List<user_tags>? User_tagsList { get; set; }
        public List<user_label_collection>? UserLabelCollection { get; set; }
        public List<user_remark_log>? UserRemarkLog { get; set; }
        public List<UserAddressBookResponse>? UserAddressBook { get; set; }
        public products_review_reply ProductsReviewReply { get; set; }

        public List<UserShoppingCartResponse>? ShoppingCart { get; set; }
        public List<UserFavoriteResponse>? FavoriteProducts { get; set; }
        public List<UserOrdersResponse>? UserOrdersProductsList { get; set; }

        public int UserOrdersProductCount { get; set; }
        public int ProductCount { get; set; }
        public decimal TotalProductPrice { get; set; }
        public decimal points { get; set; }
        public decimal shippingFee { get; set; }
        public string shippingFeeTwo { get; set; }
        public decimal commission { get; set; }
        public decimal OrderSum { get; set; }
        public string OrderSymbol { get; set; }
        public string paymentMethod { get; set; }
        public int OrdersCount { get; set; }
        public int PaymentCount { get; set; }
        public decimal refundedShippingAmount { get; set; }

        public List<OrderCountryResponse>? OrderCountryResponseList { get; set; }
        public string OrderCountryJson { get; set; }

        public List<country_states> CountryStatesList { get; set; }
        public Refund(IOrderListService orderListService, ICustomerListService customerListService,
            IHelpsCartService helpsCartService, IHelpsUserService helpsUserService,
            IHelpsManageService helpsManageService, ICurrencyService currencyService)
        {
            _orderListService = orderListService;
            _customerListService = customerListService;
            _helpsCartService = helpsCartService;
            _helpsUserService = helpsUserService;
            _helpsManageService = helpsManageService;
            _currencyService = currencyService;
        }
        //public override Task MountAsync()
        //{
        //    OrderId = HttpContext.Request.Query["id"].ObjToInt();

        //    return base.MountAsync();
        //}
        public override async Task MountAsync()
        {
            //var id = HttpContext.Request.Query["id"].ToString();
            var orderid = Convert.ToInt32(Id);
            if (orderid>0)
            {
                var userid = await _orderListService.GetUserIdByOrderIdAsync(orderid);
                UserModel = await _customerListService.GetUserDetailAsync(userid);
                OrderDetailModel = await _orderListService.GetOrderDetailByOrderIdAsync(orderid);
                var paymentRow = await _orderListService.GetpaymentByid(Convert.ToInt32(OrderDetailModel.PId));
                var IsOnline = paymentRow.IsOnline;
                // 是否已付款
                var paidStatus = (OrderDetailModel.OrderStatus > 3 && (OrderDetailModel.PaymentStatus == PaymentStatusEnum.已付款.GetDescription() || OrderDetailModel.PaymentStatus == PaymentStatusEnum.部分退款.GetDescription())) ? true : false;


                isRefund = false;
                originalTips = "";
                if (!string.IsNullOrEmpty(OrderDetailModel.PaymentId) && OrderDetailModel.PaymentStatus == PaymentStatusEnum.已付款.GetDescription() ||
                    OrderDetailModel.OrderStatus >= 3)
                {
                    isRefund = true;
                }
                if (!Convert.ToBoolean(IsOnline) && string.IsNullOrEmpty(OrderDetailModel.PaymentId))// 非线上支付的情况下.// 没有PaymentId
                {
                    originalTips = "此方式不支持原路退回";
                }
                if (paidStatus && Convert.ToBoolean(IsOnline) && string.IsNullOrEmpty(OrderDetailModel.PaymentId))// 线上支付的情况下// 没有PaymentId
                {
                    originalTips = "退款需核对支付凭证，由于此订单缺少支付ID，无法原路退回";
                }



                prevOrder = await _orderListService.getprevOrder(orderid);
                nextOrder = await _orderListService.getnextOrder(orderid);
                var paymentPrice = await _orderListService.actualPayment(OrderDetailModel, 0);
                OrderSum = paymentPrice;
                var OrdersAmount = await _orderListService.GetOrdersAmount(orderid);
                ProductCount = OrdersAmount.Item1;
                TotalProductPrice = OrdersAmount.Item2;
                points = OrdersAmount.Item3;
                shippingFee = OrdersAmount.Item4;
                commission = OrdersAmount.Item5;
                //OrderSum = OrdersAmount.Item6;
                OrderSymbol = OrdersAmount.Item7;
                paymentMethod = OrdersAmount.Item8;
                OrderRefundAmount = await _orderListService.GetOrderRefundByOrderIdAsync(orderid);
                //OrderRefundAmount = OrderRefund?.Amount ?? 0;
                var OrdersPayment = await _orderListService.GetOrdersPayment(userid);
                OrdersCount = OrdersPayment.Item1;
                PaymentCount = OrdersPayment.Item2;

                OrderPackage = await _orderListService.GetPackageAsync(orderid);
                OrderProductsLists = await _orderListService.GetProducts_Lists(orderid);
                OrdersLogs = await _orderListService.GetOrders_Logs(orderid);
                OrdersRemarkLogs = await _orderListService.GetOrders_Remark_Logs(orderid);
                OrdersTags = await _orderListService.GetOrders_Tags(orderid);
                Orders_TagsBy = await _orderListService.GetOrders_TagsBy(orderid);
                UserLabelCollection = await _customerListService.GetUserLabelCollectionById(userid);


                OrderCountryResponseList = await _orderListService.GetCountryData();
                OrderCountryJson = System.Text.Json.JsonSerializer.Serialize(OrderCountryResponseList);
                CountryStatesList = await _orderListService.GetCountryStatesData();

                refundedShippingAmount = await _orderListService.getrefundedShippingAmount(orderid);
                var currencyList = await _currencyService.GetAllCurrencyCache();
                if (OrderDetailModel.Currency != OrderDetailModel.ManageCurrency)
                {
                    var currencyFirst = currencyList.Where(x => x.Currency == OrderDetailModel.Currency).First();
                    OrderSymbol = currencyFirst?.Symbol;
                   
                    shippingFeeTwo = await _helpsCartService.IconvPriceFormat(shippingFee, 2, OrderDetailModel.Currency, OrderDetailModel.Rate);
                    refundedShippingAmount = Convert.ToDecimal(await _helpsCartService.IconvPriceFormat(refundedShippingAmount, 2, OrderDetailModel.Currency, OrderDetailModel.Rate));
                }
                else
                {
                    shippingFeeTwo = shippingFee.ToString();
                }
            }
           

            //User_tagsList = await _customerListService.GetUser_Tags();
            //UserLabelCollection = await _customerListService.GetUserLabelCollectionById(userid);
            //UserRemarkLog = await _customerListService.GetUserRemarkLogById(userid);
            //UserAddressBook = await _customerListService.GetUserAddressBook(userid);
            //ShoppingCart = await _customerListService.GetShoppingCart(userid);
            //FavoriteProducts = await _customerListService.GetFavoriteProducts(userid);
            //UserOrdersProductsList = await _customerListService.GetUserOrdersProductsList(userid);
            //UserOrdersProductCount = await _customerListService.GetUserOrdersProductsCount(userid);
            //var UserOrdersSum = await _customerListService.GetUserOrdersSum(userid);
            //OrderSum = UserOrdersSum.Item1;
            //OrderSymbol = UserOrdersSum.Item2;

            //var sdfsa = OrderDetailModel.ShippingStatus;

            //var sdf = ShippingStatusEnum.未发货.GetDescription();
            //var sdfsdf = EnumExtensions.FromDescription<ShippingStatusEnum>(OrderDetailModel.ShippingStatus);










        }

    }
}
