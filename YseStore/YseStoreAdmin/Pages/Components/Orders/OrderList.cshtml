@using Newtonsoft.Json.Linq;
@using YseStore.Model.Enums;
@using YseStore.Common;
@model YseStoreAdmin.Pages.Components.Orders.OrderList
@{
}
<div>
    <mx-drawer />
    <div id="orders" class="r_con_wrap " style="height: 329px;">
        <div class="inside_container inside_menu_right">
            <h1>订单管理  </h1>
            <div class="inside_menu">
                @* <a on:click="() => Model.ShowOrderProducts()">test</a> *@
                <div class="inside_title"><span>所有</span><i></i></div>
                <div class="inside_body">
                    <ul>
                        <li><a href="/Orders/List" class="@(Model.PaymentStatus==""&&Model.ShippingStatus==""&&Model.OrderStatus==""?"current":"")">所有</a></li>
                        <li><a href="/Orders/List?type=tab&PaymentStatus=pending" status="pending" class="@(Model.PaymentStatus=="pending"?"current":"")">待处理</a></li>
                        <li><a href="/Orders/List?type=tab&ShippingStatus=unshipped" status="unshipped" class="@(Model.ShippingStatus=="unshipped"?"current":"")">未发货</a></li>
                        <li><a href="/Orders/List?type=tab&PaymentStatus=unpaid" status="unpaid" class="@(Model.PaymentStatus=="unpaid"?"current":"")">未付款</a></li>
                        <li><a href="/Orders/List?type=tab&PaymentStatus=paid" status="paid" class="@(Model.PaymentStatus=="paid"?"current":"")">已付款</a></li>
                        <li><a href="/Orders/List?type=tab&ShippingStatus=shipped" status="shipped" class="@(Model.ShippingStatus=="shipped"?"current":"")">已发货</a></li>
                        <li><a href="/Orders/List?type=tab&OrderStatus=finished" status="finished" class="@(Model.OrderStatus=="finished"?"current":"")">已完成</a></li>
                        <li><a href="/Orders/List?type=tab&PaymentStatus=voided" status="voided" class="@(Model.PaymentStatus=="voided"?"current":"")">已作废</a></li>
                    </ul>
                    <div class="inside_menu_current" style="left: 0px;"></div>
                 
                </div>
            </div>
        </div>
        <div class="inside_table">
            <div class="list_menu">
                <div class="search_box fl search_filter">
                    <form method="get" action="?" id="order_export_filter_value">
                        <div class="k_input">
                            <input type="text" class="form_input" name="Keyword" value="@(Model.keywords)" size="15" autocomplete="off" placeholder="请输入产品名称、SKU、订单号、运单号或订单标签">
                            <input type="submit" class="search_btn" value="&#xe600;">
                        </div>
                        <input type="button" class="filter_btn" value="筛选">
                        <a class="explode" href="javascript:;">导出</a>
                        <div class="clear"></div>
                        <input type="hidden" name="OrderStatus" value="@(Model.OrderStatus)">
                        <input type="hidden" name="PaymentStatus" value="@(Model.PaymentStatus)">
                        <input type="hidden" name="ShippingStatus" value="@(Model.ShippingStatus)">
                        <input type="hidden" name="OrderTime" value="@(Model.OrderTime)">
                        <input type="hidden" name="PaymentTime" value="@(Model.PaymentTime)">
                        <input type="hidden" name="PId" value="@(Model.PId)">
                        <input type="hidden" name="SId" value="@(Model.SId)">
                        <input type="hidden" name="TId" value="@(Model.TId)">
                        <input type="hidden" name="CreateType" value="@(Model.CreateType)">
                        <input type="hidden" name="StoreSource" value="">
                        <input type="hidden" name="MenuSort" value="@(Model.MenuSort)">
                    </form>
                </div>
                <a href="/Orders/Create" class="fr g_btn g_btn_main btn_create">创建订单</a>
                <div class="clear"></div>
                <div class="search_box_selected">
                    @if (!string.IsNullOrEmpty(Model.keywords))
                    {
                        <span class="btn_item_choice current" data-name="Keyword"><b>搜索词: @(Model.keywords) </b><i></i></span>
                    }
                    @if (!string.IsNullOrEmpty(Model.OrderStatus))
                    {
                        <span class="btn_item_choice current" data-name="OrderStatus"><b>订单状态: @(Model.OrderStatusName) </b><i></i></span>
                    }
                    @if (!string.IsNullOrEmpty(Model.PaymentStatus))
                    {
                        <span class="btn_item_choice current" data-name="PaymentStatus"><b>付款状态: @(Model.PaymentStatusName) </b><i></i></span>
                    }
                    @if (!string.IsNullOrEmpty(Model.ShippingStatus))
                    {
                        <span class="btn_item_choice current" data-name="ShippingStatus"><b>发货状态: @(Model.ShippingStatusName) </b><i></i></span>
                    }
                    @if (!string.IsNullOrEmpty(Model.OrderTime))
                    {
                        <span class="btn_item_choice current" data-name="OrderTime"><b>创建时间: @(Model.OrderTime) </b><i></i></span>
                    }
                    @if (!string.IsNullOrEmpty(Model.PId))
                    {
                        <span class="btn_item_choice current" data-name="PId"><b>付款方式: @(Model.PIdName) </b><i></i></span>
                    }
                    @if (!string.IsNullOrEmpty(Model.PaymentTime))
                    {
                        <span class="btn_item_choice current" data-name="PaymentTime"><b>付款时间: @(Model.PaymentTime) </b><i></i></span>
                    }
                    @if (!string.IsNullOrEmpty(Model.SId))
                    {
                        <span class="btn_item_choice current" data-name="SId"><b>物流方式: @(Model.SIdName) </b><i></i></span>
                    }
                    @if (!string.IsNullOrEmpty(Model.CreateType))
                    {
                        <span class="btn_item_choice current" data-name="CreateType"><b>订单类型: @(Model.CreateTypeName) </b><i></i></span>
                    }
                    @if (!string.IsNullOrEmpty(Model.TId))
                    {
                        <span class="btn_item_choice current" data-name="TId"><b>标签: @(Model.TId) </b><i></i></span>
                    }
                </div>
            </div>
            <div class="box_table">
                <order-list-table />

            </div>
            <div class="scroll_sticky">
                <div class="scroll_sticky_content"><div style="width: 1808px; height: 1px;"></div></div>
            </div>
            <mx-pager name="manlog" total="@Model.TotalCount" page-size="@Model.PageSize" page-num="@Model.PageNum" />
            @* <div id="turn_page" data-current="0" data-count="1">
            <div class="total_page">共 3 条</div>
            </div> *@
        </div>
    </div>
    <div id="fixed_right">
        <div class="global_container fixed_order_print" data-width="350">
            <div class="top_title">打印 <a href="javascript:;" class="close"></a></div>
            <form id="print_config_form">

                <div class="input_box_label">类型</div>

                <div class="rows clean type_box">
                    <div class="input">
                        <span class="input_radio_box input_radio_side_box tab_option">
                            <span class="input_radio"><input type="radio" name="Type" value="picking"></span>
                            <strong class="fs14">拣货单</strong>
                            <p class="fs12">专门用于仓库拣货的单据</p>
                        </span>
                        <span class="input_radio_box input_radio_side_box tab_option">
                            <span class="input_radio"><input type="radio" name="Type" value="delivery"></span>
                            <strong class="fs14">出货单</strong>
                            <p class="fs12">作为交易凭证，放在包裹里寄送给客户</p>
                        </span>
                        <span class="input_radio_box input_radio_side_box tab_option">
                            <span class="input_radio"><input type="radio" name="Type" value="custom"></span>
                            <strong class="fs14">自定义</strong>
                            <p class="fs12">选择需要打印的内容，生成打印单</p>
                        </span>
                    </div>
                </div>

                <div class="rows clean currency_box">
                    <div class="config_title">货币单位</div>
                    <div class="box">
                        <div class="input">
                            <div class="item" data-id="ManageCurrency">
                                <span class="input_radio_box checked">
                                    <span class="input_radio">
                                        <input type="radio" name="currency" value="ManageCurrency" checked="checked">
                                    </span>
                                    后台默认货币
                                </span>
                            </div>
                            <div class="item" data-id="Currency">
                                <span class="input_radio_box ">
                                    <span class="input_radio">
                                        <input type="radio" name="currency" value="Currency">
                                    </span>
                                    结账货币
                                </span>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="blank20"></div>
                <div class="type_box_tab">
                    <div class="rows clean" data-type="picking"></div>
                    <div class="rows clean" data-type="delivery"></div>
                    <div class="rows clean custom_config" data-type="custom">
                        <div class="config_title">打印内容</div>
                        <span class="select_all_box">
                            <span class="input_checkbox_box checked">
                                <span class="input_checkbox"><input type="checkbox" name="select_all_item" value="1"></span>全选
                            </span>
                        </span>
                        <div class="blank15"></div>
                        <div class="config_box">
                            <div class="config_list">
                                <div class="info_name">
                                    <span class="input_checkbox_box checked" data-type="partial">
                                        <span class="input_checkbox"></span>
                                        店铺Logo<em></em>
                                    </span>
                                    <i></i>
                                </div>
                                <div class="input">
                                    <div class="multi_img upload_file_multi " id="Detail">
                                        <dl class="img isfile" num="0">
                                            <dt class="upload_box preview_pic">
                                                <input type="button" class="btn_ok upload_btn" name="submit_button"
                                                       value="上传图片" tips="" style="display: none;"><input type="hidden" name="OrderPrintLogo"
                                                                                                          value="/u_file/2412/12/photo/retevis-logo.png" data-value="" save="1"><a href="javascript:;">
                                                    <img src="/u_file/2412/12/photo/retevis-logo.png"><em></em>
                                                </a>
                                            </dt>
                                            <dd class="pic_btn">
                                                <a href="/u_file/2412/12/photo/retevis-logo.png" class="zoom" target="_blank">
                                                    <i class="icon_multi_view"></i>
                                                </a><a href="javascript:;" class="del" rel="del">
                                                    <i class="icon_multi_delete"></i>
                                                </a>
                                            </dd>
                                        </dl>
                                    </div>
                                </div>
                                <div class="second_box hide">
                                    <div class="info_name last">
                                        <span class="input_checkbox_box checked" data-type="partial">
                                            <span class="input_checkbox"><input type="checkbox" name="printMenu[store_logo]" value="1" checked=""></span>
                                            <em>店铺Logo<em></em></em>
                                        </span>
                                    </div>
                                    <div class="clear"></div>
                                </div>
                            </div>
                            <div class="config_list">
                                <div class="info_name">
                                    <span class="input_checkbox_box checked" data-type="partial">
                                        <span class="input_checkbox"></span>
                                        店铺名称<em></em>
                                    </span>
                                    <i></i>
                                </div>
                                <div class="second_box hide">
                                    <div class="info_name last">
                                        <span class="input_checkbox_box checked" data-type="partial">
                                            <span class="input_checkbox"><input type="checkbox" name="printMenu[store_name]" value="1" checked=""></span>
                                            <em>店铺名称<em></em></em>
                                        </span>
                                    </div>
                                    <div class="clear"></div>
                                </div>
                            </div>
                            <div class="config_list">
                                <div class="info_name">
                                    <span class="input_checkbox_box checked" data-type="partial">
                                        <span class="input_checkbox"></span>
                                        产品信息<em></em>
                                    </span>
                                    <i></i>
                                </div>
                                <div class="second_box ">
                                    <div class="info_name ">
                                        <span class="input_checkbox_box checked" data-type="partial">
                                            <span class="input_checkbox"><input type="checkbox" name="printMenu[proserial]" value="1" checked=""></span>
                                            <em>序号<em></em></em>
                                        </span>
                                    </div>
                                    <div class="info_name ">
                                        <span class="input_checkbox_box checked" data-type="partial">
                                            <span class="input_checkbox"><input type="checkbox" name="printMenu[propic]" value="1" checked=""></span>
                                            <em>产品图片<em></em></em>
                                        </span>
                                    </div>
                                    <div class="info_name ">
                                        <span class="input_checkbox_box checked" data-type="partial">
                                            <span class="input_checkbox"><input type="checkbox" name="printMenu[proname]" value="1" checked=""></span>
                                            <em>产品名称<em></em></em>
                                        </span>
                                    </div>
                                    <div class="info_name ">
                                        <span class="input_checkbox_box checked" data-type="partial">
                                            <span class="input_checkbox"><input type="checkbox" name="printMenu[pronumber]" value="1" checked=""></span>
                                            <em>产品编号<em></em></em>
                                        </span>
                                    </div>
                                    <div class="info_name ">
                                        <span class="input_checkbox_box checked" data-type="partial">
                                            <span class="input_checkbox"><input type="checkbox" name="printMenu[prosku]" value="1" checked=""></span>
                                            <em>产品SKU<em></em></em>
                                        </span>
                                    </div>
                                    <div class="info_name ">
                                        <span class="input_checkbox_box checked" data-type="partial">
                                            <span class="input_checkbox"><input type="checkbox" name="printMenu[proattr]" value="1" checked=""></span>
                                            <em>产品属性<em></em></em>
                                        </span>
                                    </div>
                                    <div class="info_name last">
                                        <span class="input_checkbox_box checked" data-type="partial">
                                            <span class="input_checkbox"><input type="checkbox" name="printMenu[proqty]" value="1" checked=""></span>
                                            <em>产品数量<em></em></em>
                                        </span>
                                    </div>
                                    <div class="clear"></div>
                                </div>
                            </div>
                            <div class="config_list">
                                <div class="info_name">
                                    <span class="input_checkbox_box checked" data-type="partial">
                                        <span class="input_checkbox"></span>
                                        产品单价<em></em>
                                    </span>
                                    <i></i>
                                </div>
                                <div class="second_box hide">
                                    <div class="info_name last">
                                        <span class="input_checkbox_box checked" data-type="partial">
                                            <span class="input_checkbox"><input type="checkbox" name="printMenu[item_price]" value="1" checked=""></span>
                                            <em>产品单价<em></em></em>
                                        </span>
                                    </div>
                                    <div class="clear"></div>
                                </div>
                            </div>
                            <div class="config_list">
                                <div class="info_name">
                                    <span class="input_checkbox_box checked" data-type="partial">
                                        <span class="input_checkbox"></span>
                                        产品小计<em></em>
                                    </span>
                                    <i></i>
                                </div>
                                <div class="second_box hide">
                                    <div class="info_name last">
                                        <span class="input_checkbox_box checked" data-type="partial">
                                            <span class="input_checkbox"><input type="checkbox" name="printMenu[sub_price]" value="1" checked=""></span>
                                            <em><em></em></em>
                                        </span>
                                    </div>
                                    <div class="clear"></div>
                                </div>
                            </div>
                            <div class="config_list">
                                <div class="info_name">
                                    <span class="input_checkbox_box checked" data-type="partial">
                                        <span class="input_checkbox"></span>
                                        产品总价<em></em>
                                    </span>
                                    <i></i>
                                </div>
                                <div class="second_box hide">
                                    <div class="info_name last">
                                        <span class="input_checkbox_box checked" data-type="partial">
                                            <span class="input_checkbox"><input type="checkbox" name="printMenu[total_price]" value="1" checked=""></span>
                                            <em>产品总价<em></em></em>
                                        </span>
                                    </div>
                                    <div class="clear"></div>
                                </div>
                            </div>
                            <div class="config_list">
                                <div class="info_name">
                                    <span class="input_checkbox_box checked" data-type="partial">
                                        <span class="input_checkbox"></span>
                                        折扣<em></em>
                                    </span>
                                    <i></i>
                                </div>
                                <div class="second_box hide">
                                    <div class="info_name last">
                                        <span class="input_checkbox_box checked" data-type="partial">
                                            <span class="input_checkbox"><input type="checkbox" name="printMenu[discount]" value="1" checked=""></span>
                                            <em>折扣<em></em></em>
                                        </span>
                                    </div>
                                    <div class="clear"></div>
                                </div>
                            </div>
                            <div class="config_list">
                                <div class="info_name">
                                    <span class="input_checkbox_box checked" data-type="partial">
                                        <span class="input_checkbox"></span>
                                        运费<em></em>
                                    </span>
                                    <i></i>
                                </div>
                                <div class="second_box hide">
                                    <div class="info_name last">
                                        <span class="input_checkbox_box checked" data-type="partial">
                                            <span class="input_checkbox"><input type="checkbox" name="printMenu[freight]" value="1" checked=""></span>
                                            <em>运费<em></em></em>
                                        </span>
                                    </div>
                                    <div class="clear"></div>
                                </div>
                            </div>
                            <div class="config_list">
                                <div class="info_name">
                                    <span class="input_checkbox_box checked" data-type="partial">
                                        <span class="input_checkbox"></span>
                                        税费<em></em>
                                    </span>
                                    <i></i>
                                </div>
                                <div class="second_box hide">
                                    <div class="info_name last">
                                        <span class="input_checkbox_box checked" data-type="partial">
                                            <span class="input_checkbox"><input type="checkbox" name="printMenu[taxes]" value="1" checked=""></span>
                                            <em>税费<em></em></em>
                                        </span>
                                    </div>
                                    <div class="clear"></div>
                                </div>
                            </div>
                            <div class="config_list">
                                <div class="info_name">
                                    <span class="input_checkbox_box checked" data-type="partial">
                                        <span class="input_checkbox"></span>
                                        手续费<em></em>
                                    </span>
                                    <i></i>
                                </div>
                                <div class="second_box hide">
                                    <div class="info_name last">
                                        <span class="input_checkbox_box checked" data-type="partial">
                                            <span class="input_checkbox"><input type="checkbox" name="printMenu[fee_price]" value="1" checked=""></span>
                                            <em>手续费<em></em></em>
                                        </span>
                                    </div>
                                    <div class="clear"></div>
                                </div>
                            </div>
                            <div class="config_list">
                                <div class="info_name">
                                    <span class="input_checkbox_box checked" data-type="partial">
                                        <span class="input_checkbox"></span>
                                        订单总额<em></em>
                                    </span>
                                    <i></i>
                                </div>
                                <div class="second_box hide">
                                    <div class="info_name last">
                                        <span class="input_checkbox_box checked" data-type="partial">
                                            <span class="input_checkbox"><input type="checkbox" name="printMenu[order_total]" value="1" checked=""></span>
                                            <em>订单总额<em></em></em>
                                        </span>
                                    </div>
                                    <div class="clear"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="rows clean box_button box_submit">
                    <div class="input">
                        <input type="button" class="btn_global btn_submit print_btn" name="submit_button" value="打印">			<input type="button" class="btn_global btn_cancel" value="返回">
                    </div>
                </div>
            </form>
        </div>
        <div class="global_container fixed_search_filter" data-width="396">
            <div class="top_title">筛选 <a href="javascript:;" class="close"></a></div>
            <div class="global_form">
                <div class="box_filter">
                    <div class="filter_list">
                        <div class="filter_title">订单状态</div>
                        <div class="filter_option">
                            <div class="filter_option_list">
                                @foreach (OrderStatusEnum status in Enum.GetValues(typeof(OrderStatusEnum)))
                                {
                                    <div class="filter_option_item" data-type="checkbox">
                                        <span class="input_checkbox_box"><span class="input_checkbox"><input type="checkbox" name="OrderStatus" value="@EnumExtensions.GetDescription(status)"></span></span>
                                        <span class="filter_option_title">@status.ToString()</span>
                                    </div>
                                }

                                @* <div class="filter_option_item" data-type="checkbox">
                                <span class="input_checkbox_box"><span class="input_checkbox"><input type="checkbox" name="OrderStatus" value="unfinished"></span></span>
                                <span class="filter_option_title">未完成</span>
                                </div>
                                <div class="filter_option_item" data-type="checkbox">
                                <span class="input_checkbox_box"><span class="input_checkbox"><input type="checkbox" name="OrderStatus" value="finished"></span></span>
                                <span class="filter_option_title">已完成</span>
                                </div>
                                <div class="filter_option_item" data-type="checkbox">
                                <span class="input_checkbox_box"><span class="input_checkbox"><input type="checkbox" name="OrderStatus" value="voided"></span></span>
                                <span class="filter_option_title">已作废</span>
                                </div> *@
                            </div>
                            <div class="filter_clean"><button>清除</button></div>
                        </div>
                    </div>
                    <div class="filter_list">
                        <div class="filter_title">付款状态</div>
                        <div class="filter_option">
                            <div class="filter_option_list">
                                @foreach (PaymentStatusEnum status in Enum.GetValues(typeof(PaymentStatusEnum)))
                                {
                                    <div class="filter_option_item" data-type="checkbox">
                                        <span class="input_checkbox_box"><span class="input_checkbox"><input type="checkbox" name="PaymentStatus" value="@EnumExtensions.GetDescription(status)"></span></span>
                                        <span class="filter_option_title">@status.ToString()</span>
                                    </div>

                                }

                                @*  <div class="filter_option_item" data-type="checkbox">
                                <span class="input_checkbox_box"><span class="input_checkbox"><input type="checkbox" name="PaymentStatus" value="pending"></span></span>
                                <span class="filter_option_title">待处理</span>
                                </div>
                                <div class="filter_option_item" data-type="checkbox">
                                <span class="input_checkbox_box"><span class="input_checkbox"><input type="checkbox" name="PaymentStatus" value="paid"></span></span>
                                <span class="filter_option_title">已付款</span>
                                </div>
                                <div class="filter_option_item" data-type="checkbox">
                                <span class="input_checkbox_box"><span class="input_checkbox"><input type="checkbox" name="PaymentStatus" value="partially_refunded"></span></span>
                                <span class="filter_option_title">部分退款</span>
                                </div>
                                <div class="filter_option_item" data-type="checkbox">
                                <span class="input_checkbox_box"><span class="input_checkbox"><input type="checkbox" name="PaymentStatus" value="refunded"></span></span>
                                <span class="filter_option_title">已退款</span>
                                </div> *@
                            </div>
                            <div class="filter_clean"><button>清除</button></div>
                        </div>
                    </div>
                    <div class="filter_list">
                        <div class="filter_title">发货状态</div>
                        <div class="filter_option">
                            <div class="filter_option_list">
                                @foreach (ShippingStatusEnum status in Enum.GetValues(typeof(ShippingStatusEnum)))
                                {
                                    <div class="filter_option_item" data-type="checkbox">
                                        <span class="input_checkbox_box"><span class="input_checkbox"><input type="checkbox" name="ShippingStatus" value="@EnumExtensions.GetDescription(status)"></span></span>
                                        <span class="filter_option_title">@status.ToString()</span>
                                    </div>

                                }
                                @* <div class="filter_option_item" data-type="checkbox">
                                <span class="input_checkbox_box"><span class="input_checkbox"><input type="checkbox" name="ShippingStatus" value="unshipped"></span></span>
                                <span class="filter_option_title">未发货</span>
                                </div>
                                <div class="filter_option_item" data-type="checkbox">
                                <span class="input_checkbox_box"><span class="input_checkbox"><input type="checkbox" name="ShippingStatus" value="partial"></span></span>
                                <span class="filter_option_title">部分发货</span>
                                </div>
                                <div class="filter_option_item" data-type="checkbox">
                                <span class="input_checkbox_box"><span class="input_checkbox"><input type="checkbox" name="ShippingStatus" value="shipped"></span></span>
                                <span class="filter_option_title">已发货</span>
                                </div> *@
                            </div>
                            <div class="filter_clean"><button>清除</button></div>
                        </div>
                    </div>
                    <div class="filter_list">
                        <div class="filter_title">创建时间</div>
                        <div class="filter_option">
                            <div class="filter_option_input">
                                <input name="OrderTime" value="" type="text" class="box_input full_input input_time" size="55" readonly="" notnull="">
                            </div>
                            <div class="filter_clean"><button>清除</button></div>
                        </div>
                    </div>
                    <div class="filter_list">
                        <div class="filter_title">付款方式</div>
                        <div class="filter_option">
                            <div class="filter_option_list">
                                @if (Model.PaymentMethods != null && Model.PaymentMethods.Count > 0)
                                {
                                    foreach (var payment in Model.PaymentMethods)
                                    {
                                        <div class="filter_option_item" data-type="checkbox">
                                            <span class="input_checkbox_box">
                                                <span class="input_checkbox">
                                                    <input type="checkbox" name="PId" value="@(payment.PId)">
                                                </span>
                                            </span>
                                            <span class="filter_option_title">@(payment.Name_en)</span>
                                        </div>

                                    }
                                }


                               @*  <div class="filter_option_item" data-type="checkbox">
                                    <span class="input_checkbox_box"><span class="input_checkbox"><input type="checkbox" name="PId" value="1"></span></span>
                                    <span class="filter_option_title">PayPal</span>
                                </div>
                                <div class="filter_option_item" data-type="checkbox">
                                    <span class="input_checkbox_box"><span class="input_checkbox"><input type="checkbox" name="PId" value="9"></span></span>
                                    <span class="filter_option_title">Cash On Delivery</span>
                                </div>
                                <div class="filter_option_item" data-type="checkbox">
                                    <span class="input_checkbox_box"><span class="input_checkbox"><input type="checkbox" name="PId" value="154"></span></span>
                                    <span class="filter_option_title">Payoneer</span>
                                </div> *@
                            </div>
                            <div class="filter_clean"><button>清除</button></div>
                        </div>
                    </div>
                    <div class="filter_list">
                        <div class="filter_title">付款时间</div>
                        <div class="filter_option">
                            <div class="filter_option_input">
                                <input name="PaymentTime" value="" type="text" class="box_input full_input input_time" size="55" readonly="" notnull="">
                            </div>
                            <div class="filter_clean"><button>清除</button></div>
                        </div>
                    </div>
                    <div class="filter_list">
                        <div class="filter_title">物流方式</div>
                        <div class="filter_option">
                            <div class="filter_option_list">
                                @if (Model.Ordershipping != null)
                                {
                                    foreach (var item in Model.Ordershipping)
                                    {
                                        <div class="filter_option_item" data-type="checkbox">
                                            <span class="input_checkbox_box"><span class="input_checkbox"><input type="checkbox" name="SId" value="@item.SId"></span></span>
                                            <span class="filter_option_title">@item.Express</span>
                                        </div>
                                    }
                                }


                                @* <div class="filter_option_item" data-type="checkbox">
                                <span class="input_checkbox_box"><span class="input_checkbox"><input type="checkbox" name="SId" value="33"></span></span>
                                <span class="filter_option_title">Free Shipping</span>
                                </div>
                                <div class="filter_option_item" data-type="checkbox">
                                <span class="input_checkbox_box"><span class="input_checkbox"><input type="checkbox" name="SId" value="36"></span></span>
                                <span class="filter_option_title">Battery Shipping</span>
                                </div> *@
                            </div>
                            <div class="filter_clean"><button>清除</button></div>
                        </div>
                    </div>
                    <div class="filter_list">
                        <div class="filter_title">订单类型</div>
                        <div class="filter_option">
                            <div class="filter_option_list">
                                <div class="filter_option_item" data-type="checkbox">
                                    <span class="input_checkbox_box"><span class="input_checkbox"><input type="checkbox" name="CreateType" value="system"></span></span>
                                    <span class="filter_option_title">客户下单</span>
                                </div>
                                <div class="filter_option_item" data-type="checkbox">
                                    <span class="input_checkbox_box"><span class="input_checkbox"><input type="checkbox" name="CreateType" value="custom"></span></span>
                                    <span class="filter_option_title">手动创建</span>
                                </div>
                            </div>
                            <div class="filter_clean"><button>清除</button></div>
                        </div>
                    </div>
                    <div class="filter_list">
                        <div class="filter_title">标签</div>
                        <div class="filter_option">
                            <div class="filter_option_input">
                                <dl class="box_drop_double" data-checkbox="1" data-showadd="0">
                                    <dt class="box_checkbox_list">
                                        <div class="select_placeholder">请选择</div>
                                        <div class="select_list"></div>
                                        <input type="text" class="box_input check_input" name="Select" value="" placeholder="" size="30" maxlength="255" autocomplete="off">
                                        <input type="hidden" name="TId" value="" class="hidden_value"><input type="hidden" name="TIdType" value="" class="hisdden_type">
                                    </dt>
                                    <dd class="drop_down">
                                        <div class="drop_menu" data-type="Select">
                                            <a href="javascript:;" class="btn_back" data-value="" data-type="" data-table="" data-top="0" data-all="0" style="display:none;">返回</a>
                                            <div class="drop_skin" style="display: none;"></div>
                                            <div class="drop_list" data="@Model.OrderTagsJson" data-more="none">
                                                @if (Model.orders_tagsList != null)
                                                {
                                                    foreach (var item in Model.orders_tagsList)
                                                    {


                                                        <div class="item" data-name="@item.Name" data-value="@item.Name" data-type="orders_tags" data-table="undefined" data-top="0" data-all="1" data-check-all="0" data-custom="undefined">
                                                            <span class="input_checkbox_box">
                                                                <span class="input_checkbox"><input type="checkbox" name="_DoubleOption[]" value="0"></span>
                                                            </span>
                                                            <span class="item_name">@item.Name</span>
                                                        </div>
                                                    }
                                                }


                                                @* <div class="item" data-name="Pre-order" data-value="pre_order" data-type="orders_tags" data-table="undefined" data-top="0" data-all="1" data-check-all="0" data-custom="undefined">
                                                <span class="input_checkbox_box">
                                                <span class="input_checkbox">
                                                <input type="checkbox" name="_DoubleOption[]" value="0">
                                                </span>
                                                </span>
                                                <span class="item_name">Pre-order</span>
                                                </div>
                                                <div class="item" data-name="1221" data-value="1" data-type="orders_tags" data-table="undefined" data-top="0" data-all="1" data-check-all="0" data-custom="undefined">
                                                <span class="input_checkbox_box">
                                                <span class="input_checkbox"><input type="checkbox" name="_DoubleOption[]" value="1"></span>
                                                </span>
                                                <span class="item_name">1221</span>
                                                </div> *@

                                            </div>
                                            <a href="javascript:;" class="btn_load_more" data-value="orders_tags" data-type="orders_tags" data-table="orders_tags" data-top="0" data-all="1" data-check-all="0" data-start="1" style="display:none;">加载更多</a>
                                        </div>
                                    </dd>
                                </dl>
                            </div>
                            <div class="filter_clean"><button>清除</button></div>
                        </div>
                    </div>
                    @* <div class="filter_list">
                    <div class="filter_title">店铺来源</div>
                    <div class="filter_option">
                    <div class="filter_option_list">
                    <div class="filter_option_item" data-type="checkbox">
                    <span class="input_checkbox_box"><span class="input_checkbox"><input type="checkbox" name="StoreSource" value="test.retevis.fr"></span></span>
                    <span class="filter_option_title">test.retevis.fr</span>
                    </div>
                    <div class="filter_option_item" data-type="checkbox">
                    <span class="input_checkbox_box"><span class="input_checkbox"><input type="checkbox" name="StoreSource" value="www.retevis.fr"></span></span>
                    <span class="filter_option_title">www.retevis.fr</span>
                    </div>
                    </div>
                    <div class="filter_clean"><button>清除</button></div>
                    </div>
                    </div> *@
                </div>
                <div class="rows clean box_button box_submit">
                    <div class="input">
                        <input type="button" class="btn_global btn_submit" value="筛选">
                        <input type="button" class="btn_global btn_cancel" value="取消">
                    </div>
                </div>
            </div>
        </div>
        <div class="global_container fixed_order_export" data-width="400">
            <div class="top_title">订单导出 <a href="javascript:;" class="close"></a></div>
            <form class="global_form order_export_form" id="order_export_form">
                <div class="export_content_box">
                    <div class="rows clean export_range">
                        <label>导出范围</label>
                        <div class="input">
                            <div class="item" data-count="0">
                                <span class="input_radio_box  checked">
                                    <span class="input_radio">
                                        <input type="radio" name="Type" value="0" checked="checked">
                                    </span>已勾选的订单
                                </span>
                            </div>
                            <div class="item" data-count="25">
                                <span class="input_radio_box ">
                                    <span class="input_radio">
                                        <input type="radio" name="Type" value="1">
                                    </span>当前页面的订单
                                </span>
                            </div>
                            <div class="item" data-count="3">
                                <span class="input_radio_box ">
                                    <span class="input_radio">
                                        <input type="radio" name="Type" value="2">
                                    </span>全站订单
                                </span>
                            </div>
                            <div class="item" data-count="3">
                                <span class="input_radio_box ">
                                    <span class="input_radio">
                                        <input type="radio" name="Type" value="3">
                                    </span>所有搜索结果
                                </span>
                            </div>
                        </div>
                    </div>
                    <div class="rows clean email_box hide">
                        <label>导出到邮箱</label>
                        <div class="input">
                            <input type="text" name="Email" class="box_input full_input" size="25" maxlength="255" format="Email">
                            <div class="email_tips">由于导出的文件较大，需发送到指定邮箱</div>
                        </div>
                    </div>
                    <div class="rows clean exclude_products hide_detail has_detail">
                        <label>排除产品<em></em></label>
                        <div class="input">
                            <div class="filed_tips">导出的文件不显示以下产品</div>
                            <div class="exclude_list">
                                <div class="exclude_item">
                                    <span class="input_checkbox_box">
                                        <span class="input_checkbox"><input type="checkbox" name="Exclude[refunded]" value="1"></span>
                                        <em>已退款的产品</em>
                                    </span>
                                </div>
                                <div class="exclude_item">
                                    <span class="input_checkbox_box">
                                        <span class="input_checkbox"><input type="checkbox" name="Exclude[shipped]" value="1"></span>
                                        <em>已发货的产品</em>
                                    </span>
                                </div>
                                <div class="exclude_item">
                                    <span class="input_checkbox_box">
                                        <span class="input_checkbox"><input type="checkbox" name="Exclude[outOfStock]" value="1"></span>
                                        <em>库存&lt;=0的产品</em>
                                    </span>
                                </div>
                                <div class="exclude_item" style="display:none;">
                                    <span class="input_checkbox_box">
                                        <span class="input_checkbox"><input type="checkbox" name="Exclude[preSale]" value="1"></span>
                                        <em>预售产品</em>
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="rows export_filed hide_detail has_detail">
                        <label>导出字段<span>（@(Model.Order_ExportMenuList.Where(kvp => kvp.Value == "1").Count())/@(Model.Order_ExportMenuList.Count())）</span><em></em></label>
                        <div class="input">
                            <div class="filed_tips">导出的文件将按照以下顺序显示字段</div>
                            <a href="javascript:;" class="filed_edit" id="set_edit">修改导出字段</a>
                            <ul class="export_menu" data-listidx="0">
                                @if (Model.filteredFields != null)
                                {
                                    int j = 0;
                                    foreach (var item in Model.filteredFields)
                                    {
                                        if (j < 10)
                                        {
                                            <li data-id="@item.Id"><em></em><span>@item.Text</span></li>
                                        }
                                        else
                                        {
                                            <li data-id="@item.Id" class="hide"><em></em><span>@item.Text</span></li>
                                        }
                                        j++;
                                    }
                                }


                                @*  <li data-id="A"><em></em><span>订单 ID</span></li>
                                <li data-id="C"><em></em><span>订单号</span></li>
                                <li data-id="D"><em></em><span>邮箱</span></li>
                                <li data-id="E"><em></em><span>产品总价</span></li>
                                <li data-id="F"><em></em><span>运费</span></li>
                                <li data-id="H"><em></em><span>订单总额</span></li>
                                <li data-id="I"><em></em><span>重量</span></li>
                                <li data-id="K"><em></em><span>订单状态</span></li>
                                <li data-id="L"><em></em><span>配送信息</span></li>
                                <li data-id="M"><em></em><span>付款方式</span></li>
                                <li data-id="N" class="hide"><em></em><span>创建时间</span></li>
                                <li data-id="O" class="hide"><em></em><span>优惠券</span></li>
                                <li data-id="P" class="hide"><em></em><span>收货姓名</span></li>
                                <li data-id="Q" class="hide"><em></em><span>收货街道</span></li>
                                <li data-id="R" class="hide"><em></em><span>收货寓所</span></li>
                                <li data-id="S" class="hide"><em></em><span>收货国家</span></li>
                                <li data-id="T" class="hide"><em></em><span>收货省份</span></li>
                                <li data-id="U" class="hide"><em></em><span>收货城市</span></li>
                                <li data-id="V" class="hide"><em></em><span>收货邮编</span></li>
                                <li data-id="W" class="hide"><em></em><span>收货电话</span></li>
                                <li data-id="X" class="hide"><em></em><span>账单姓名</span></li>
                                <li data-id="Y" class="hide"><em></em><span>账单街道</span></li>
                                <li data-id="Z" class="hide"><em></em><span>账单寓所</span></li>
                                <li data-id="AA" class="hide"><em></em><span>账单国家</span></li>
                                <li data-id="AB" class="hide"><em></em><span>账单省份</span></li>
                                <li data-id="BD" class="hide"><em></em><span>积分</span></li> *@
                            </ul>
                            <a id="see_all" href="javascript:;">显示全部</a>
                        </div>
                    </div>
                    <div class="rows currency_unit hide_detail has_detail">
                        <label>货币单位<em></em></label>
                        <div class="input">
                            <div class="item">
                                <span class="input_radio_box  checked">
                                    <span class="input_radio">
                                        <input type="radio" name="CurrencyUnit" value="manage" checked="checked">
                                    </span>后台默认货币
                                </span>
                            </div>
                            <div class="item">
                                <span class="input_radio_box ">
                                    <span class="input_radio">
                                        <input type="radio" name="CurrencyUnit" value="checkout">
                                    </span>结账货币
                                </span>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="rows clean box_submit">
                    <label></label>
                    <div class="input">
                        <input type="submit" class="btn_global btn_submit" value="导出">
                        <input type="button" class="btn_global btn_cancel" value="取消">
                    </div>
                </div>
                <input type="hidden" name="keyword" value="@(Model.keywords)">
                <input type="hidden" name="OrderStatus" value="@(Model.OrderStatus)">
                <input type="hidden" name="PaymentStatus" value="@(Model.PaymentStatus)">
                <input type="hidden" name="ShippingStatus" value="@(Model.ShippingStatus)">
                <input type="hidden" name="OrderTime" value="@(Model.OrderTime)">
                <input type="hidden" name="PaymentTime" value="@(Model.PaymentTime)">
                <input type="hidden" name="PId" value="@(Model.PId)">
                <input type="hidden" name="SId" value="@(Model.SId)">
                <input type="hidden" name="TId" value="@(Model.TId)">
                <input type="hidden" name="CreateType" value="@(Model.CreateType)">
                <input type="hidden" name="StoreSource" value="">
                <input type="hidden" name="MenuSort" value="@(Model.MenuSort)">
                <input type="hidden" name="currentPageCount" value="20">
                <input type="hidden" name="currentPage" value="1">
                <input type="hidden" name="id_list" id="id_list" value="">
                <input type="hidden" name="do_action" value="/api/OrderList/GetOrderExplode">
                <input type="hidden" name="exportService" id="exportService" value="website">
            </form>
        </div>
        <div class="global_container fixed_export_config" data-width="450">
            <div class="top_title">修改导出字段 <a href="javascript:;" class="close"></a></div>
            <form id="w0" class="global_form export_config_form" action="/manage/orders/orders" method="post">
                <input type="hidden" name="_csrf-manage" value="Za0ze0avbDlBcbN2RUJBMxs0ZoC9Rf2jLUG8cCVo2FEPw3w2HJoHAQYXxS8nBgRXIgYU5c5orsFJDdsnbSabfA==">			<div class="config_box">
                    <div class="config_list current">
                        <div class="info_name" data-menu="orders">
                            <span class="input_checkbox_box">
                                <span class="input_checkbox"></span>
                                订单信息<em></em>
                            </span>
                            <i></i>
                        </div>
                        <div class="second_box">
                            @if (Model.filteredFieldsOrder != null)
                            {
                                foreach (var item in Model.filteredFieldsOrder)
                                {
                                    if (!item.IsHidden)
                                    {
                                        <div class="info_name" data-menu="<EMAIL>">
                                            <span class="input_checkbox_box checked">
                                                <span class="input_checkbox"><input type="checkbox" name="Menu[@item.Id]" value="1" checked=""></span>
                                                <em>@item.Text</em>
                                            </span>
                                        </div>
                                    }
                                    else
                                    {
                                        <div class="info_name" data-menu="<EMAIL>">
                                            <span class="input_checkbox_box ">
                                                <span class="input_checkbox"><input type="checkbox" name="Menu[@item.Id]" value="1"></span>
                                                <em>@item.Text</em>
                                            </span>
                                        </div>
                                    }
                                }
                            }


                            @*      <div class="info_name" data-menu="orders-0">
                            <span class="input_checkbox_box checked">
                            <span class="input_checkbox"><input type="checkbox" name="Menu[A]" value="1" checked=""></span>
                            <em>订单 ID</em>
                            </span>
                            </div>
                            <div class="info_name" data-menu="orders-1">
                            <span class="input_checkbox_box checked">
                            <span class="input_checkbox"><input type="checkbox" name="Menu[C]" value="1" checked=""></span>
                            <em>订单号</em>
                            </span>
                            </div>
                            <div class="info_name" data-menu="orders-2">
                            <span class="input_checkbox_box checked">
                            <span class="input_checkbox"><input type="checkbox" name="Menu[K]" value="1" checked=""></span>
                            <em>订单状态</em>
                            </span>
                            </div> *@
                            <div class="clear"></div>
                        </div>
                    </div>
                    <div class="config_list ">
                        <div class="info_name" data-menu="products">
                            <span class="input_checkbox_box">
                                <span class="input_checkbox"></span>
                                产品信息<em></em>
                            </span>
                            <i></i>
                        </div>
                        <div class="second_box">
                            @if (Model.filteredFieldsPro != null)
                            {
                                foreach (var item in Model.filteredFieldsPro)
                                {
                                    if (!item.IsHidden)
                                    {
                                        <div class="info_name" data-menu="<EMAIL>">
                                            <span class="input_checkbox_box checked">
                                                <span class="input_checkbox"><input type="checkbox" name="Menu[@item.Id]" value="1" checked=""></span>
                                                <em>@item.Text</em>
                                            </span>
                                        </div>
                                    }
                                    else
                                    {
                                        <div class="info_name" data-menu="<EMAIL>">
                                            <span class="input_checkbox_box ">
                                                <span class="input_checkbox"><input type="checkbox" name="Menu[@item.Id]" value="1"></span>
                                                <em>@item.Text</em>
                                            </span>
                                        </div>
                                    }
                                }
                            }


                            @*   <div class="info_name" data-menu="products-0">
                            <span class="input_checkbox_box checked">
                            <span class="input_checkbox"><input type="checkbox" name="Menu[E]" value="1" checked=""></span>
                            <em>产品总价</em>
                            </span>
                            </div>
                            <div class="info_name" data-menu="products-1">
                            <span class="input_checkbox_box ">
                            <span class="input_checkbox"><input type="checkbox" name="Menu[AH]" value="1"></span>
                            <em>产品名称</em>
                            </span>
                            </div>
                            <div class="info_name" data-menu="products-2">
                            <span class="input_checkbox_box ">
                            <span class="input_checkbox"><input type="checkbox" name="Menu[AI]" value="1"></span>
                            <em>产品单价</em>
                            </span>
                            </div> *@
                            <div class="clear"></div>
                        </div>
                    </div>
                    <div class="config_list ">
                        <div class="info_name" data-menu="payment">
                            <span class="input_checkbox_box">
                                <span class="input_checkbox"></span>
                                支付信息<em></em>
                            </span>
                            <i></i>
                        </div>
                        <div class="second_box">
                            @if (Model.filteredFieldsPay != null)
                            {
                                foreach (var item in Model.filteredFieldsPay)
                                {
                                    if (!item.IsHidden)
                                    {
                                        <div class="info_name" data-menu="<EMAIL>">
                                            <span class="input_checkbox_box checked">
                                                <span class="input_checkbox"><input type="checkbox" name="Menu[@item.Id]" value="1" checked=""></span>
                                                <em>@item.Text</em>
                                            </span>
                                        </div>
                                    }
                                    else
                                    {
                                        <div class="info_name" data-menu="<EMAIL>">
                                            <span class="input_checkbox_box ">
                                                <span class="input_checkbox"><input type="checkbox" name="Menu[@item.Id]" value="1"></span>
                                                <em>@item.Text</em>
                                            </span>
                                        </div>
                                    }
                                }
                            }
                            @*  <div class="info_name" data-menu="payment-0">
                            <span class="input_checkbox_box checked">
                            <span class="input_checkbox"><input type="checkbox" name="Menu[M]" value="1" checked=""></span>
                            <em>付款方式</em>
                            </span>
                            </div>
                            <div class="info_name" data-menu="payment-1">
                            <span class="input_checkbox_box ">
                            <span class="input_checkbox"><input type="checkbox" name="Menu[AN]" value="1"></span>
                            <em>付款时间</em>
                            </span>
                            </div>
                            <div class="info_name" data-menu="payment-2">
                            <span class="input_checkbox_box ">
                            <span class="input_checkbox"><input type="checkbox" name="Menu[AS]" value="1"></span>
                            <em>支付流水号</em>
                            </span>
                            </div> *@
                            <div class="clear"></div>
                        </div>
                    </div>
                    <div class="config_list ">
                        <div class="info_name" data-menu="user">
                            <span class="input_checkbox_box">
                                <span class="input_checkbox"></span>
                                客户信息<em></em>
                            </span>
                            <i></i>
                        </div>
                        <div class="second_box">
                            @if (Model.filteredFieldsCus != null)
                            {
                                foreach (var item in Model.filteredFieldsCus)
                                {
                                    if (!item.IsHidden)
                                    {
                                        <div class="info_name" data-menu="<EMAIL>">
                                            <span class="input_checkbox_box checked">
                                                <span class="input_checkbox"><input type="checkbox" name="Menu[@item.Id]" value="1" checked=""></span>
                                                <em>@item.Text</em>
                                            </span>
                                        </div>
                                    }
                                    else
                                    {
                                        <div class="info_name" data-menu="<EMAIL>">
                                            <span class="input_checkbox_box ">
                                                <span class="input_checkbox"><input type="checkbox" name="Menu[@item.Id]" value="1"></span>
                                                <em>@item.Text</em>
                                            </span>
                                        </div>
                                    }
                                }
                            }

                            @* <div class="info_name" data-menu="user-0">
                            <span class="input_checkbox_box checked">
                            <span class="input_checkbox"><input type="checkbox" name="Menu[P]" value="1" checked=""></span>
                            <em>收货姓名</em>
                            </span>
                            </div>
                            <div class="info_name" data-menu="user-1">
                            <span class="input_checkbox_box checked">
                            <span class="input_checkbox"><input type="checkbox" name="Menu[S]" value="1" checked=""></span>
                            <em>收货国家</em>
                            </span>
                            </div>
                            <div class="info_name" data-menu="user-2">
                            <span class="input_checkbox_box checked">
                            <span class="input_checkbox"><input type="checkbox" name="Menu[T]" value="1" checked=""></span>
                            <em>收货省份</em>
                            </span>
                            </div> *@
                            <div class="clear"></div>
                        </div>
                    </div>
                </div>
                <div class="rows clean box_button box_submit">
                    <div class="input">
                        <span class="input_checkbox_box select_all">
                            <span class="input_checkbox"></span>
                            全选
                        </span>
                        <input type="button" class="btn_global btn_submit" value="保存">
                        <input type="button" class="btn_global btn_cancel" value="取消">
                    </div>
                </div>
            </form>
        </div>
        <div class="global_container fixed_order_export_alone" data-width="400">
            <div class="top_title">订单导出 <a href="javascript:;" class="close"></a></div>
            <form class="global_form order_export_form" id="order_export_alone_form">
                <div class="export_content_box">
                    <div class="rows currency_unit has_detail">
                        <label>货币单位</label>
                        <div class="input">
                            <div class="item">
                                <span class="input_radio_box  checked">
                                    <span class="input_radio">
                                        <input type="radio" name="CurrencyUnitOnly" value="manage" checked="checked">
                                    </span>后台默认货币
                                </span>
                            </div>
                            <div class="item">
                                <span class="input_radio_box ">
                                    <span class="input_radio">
                                        <input type="radio" name="CurrencyUnitOnly" value="checkout">
                                    </span>结账货币
                                </span>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="rows clean box_submit">
                    <label></label>
                    <div class="input">
                        <input type="submit" class="btn_global btn_submit" value="导出">
                        <input type="button" class="btn_global btn_cancel" value="取消">
                    </div>
                </div>
                <input type="hidden" name="OrderId" value="">
                <input type="hidden" name="do_action" value="/manage/orders/orders/export-products">
            </form>
        </div>


        <div class="global_container fixed_order_products_list" data-width="450" >
            <div class="top_title">产品明细 <a href="javascript:;" class="close"></a></div>

            <div id="order_products_list">
       
                    @*  <label>货币单位</label> *@

                    <div class="products_container" >
                <div class="products_box">
                            @* <div class="products_list">
                                <div class="list_title">已发货</div>
                                <div class="list_item">
                                    <div class="item_img fl"><img src=""></div><div class="item_info fr">
                                        <div class="info_name">MyTestData</div>
                                        <div class="info_attr">No. 11</div><div class="info_attr">SKU: 11</div><div class="info_attr">测试: 11</div>
                                        <div class="custom_attr"></div>
                                    </div><div class="clear"></div>
                                </div><div class="list_item">
                                    <div class="item_img fl">
                                        <img src="/u_file/202504/25/photo/5d2fc547482c4630a54785c4cf001a9b.jpg"><i>2</i>
                                    </div><div class="item_info fr">
                                        <div class="info_name">产品名称</div><div class="custom_attr"></div>
                                    </div><div class="clear"></div>
                                </div>
                            </div>
                            <div class="products_list_line"></div><div class="products_list">
                                <div class="list_title">未发货</div><div class="list_item">
                                    <div class="item_img fl"><img src=""></div><div class="item_info fr">
                                        <div class="info_name">MyTestData</div>
                                        <div class="info_attr">No. 11</div><div class="info_attr">SKU: 11</div><div class="info_attr">测试: 11</div>
                                        <div class="custom_attr"></div>
                                    </div>
                                    <div class="clear"></div>
                                </div>
                            </div> *@




                        </div>
                    </div>

            </div>




        </div>






    </div>

</div>