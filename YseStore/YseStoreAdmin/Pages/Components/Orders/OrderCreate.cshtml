@model YseStoreAdmin.Pages.Components.Orders.OrderCreate
@using Newtonsoft.Json.Linq;
@{
}

@if (Model.step == "third")
{
    <div>

        <div id="orders_create" class="r_con_wrap orders_create" style="height: 488px;">
            <div class="center_container_1000 authorization_container">
                <div class="return_title"><a href="/manage/orders/orders"> <span class="return">订单管理 / 添加订单</span></a></div>
                <div class="step_box">
                    <div class="list ">
                        <div class="num"><span>1</span></div>
                        <div class="tit">添加产品清单</div>
                    </div>
                    <div class="list ">
                        <div class="num"><span>2</span></div>
                        <div class="tit">添加客户收货信息</div>
                    </div>
                    <div class="list current">
                        <div class="num"><span>3</span></div>
                        <div class="tit">计算价格和付款</div>
                    </div>
                </div>
                <form class="global_form " id="create_third_submit" data-fixed-submit="true">
                    <div class="global_container cost @(Model.manageDefaultCurrency==Model.rateCurrency?"":"show_rate") ">
                        <div class="big_title">价格</div>
                        <div class="rows th">
                            <label>费用</label>
                            <div class="r_box">价格</div>
                            <div class="operate"></div>
                        </div>
                        @* 产品总价区域 *@
                        <div class="rows">
                            <label>
                                产品总价
                                <div class="p_num">
                                    <div class="mid">@(Model.ProductsQty) 个产品<i></i></div>
                                    <div class="box_products">
                                        @foreach (var pro in Model.Products)
                                        {
                                            <div class="list_item">
                                                <div class="item_img">
                                                    <img src="@Url.Content(pro.PicPath)">
                                                    @if (pro.Qty > 1)
                                                    {
                                                        <i>@pro.Qty</i>
                                                    }
                                                </div>
                                                <div class="item_info">
                                                    <div class="info_name">@pro.Name</div>
                                                    <div class="info_attr">
                                                        @foreach (var attr in pro.Attributes)
                                                        {
                                                            <p>@attr.Key: @attr.Value</p>
                                                        }
                                                    </div>
                                                </div>
                                            </div>
                                        }
                                    </div>
                                </div>
                            </label>
                            <div class="r_box pro_price">
                                <div class="to_price">
                                    @<EMAIL>("F2")
                                    <span class="web_rate">@<EMAIL>("F2")</span>
                                </div>
                            </div>
                            <div class="operate"></div>
                        </div>

                        @* 折扣区域 *@
                        <div class="rows">
                            <label>折扣</label>
                            <div class="r_box flex_box">
                                <div class="box_left">
                                    <div id="OrderDiscount" class="discount">
                                        <span data-discount-type="0">
                                            -@Model.CurrencySymbol<span id="DiscountPrice">@Model.DiscountPrice.ToString("F2")</span>
                                            <span id="rateDiscountPrice" class="web_rate">@<EMAIL>("F2")</span>
                                        </span>
                                    </div>
                                </div>
                            </div>
                            <div class="operate">
                                <a href="javascript:;" class="btn_rig btn_set_discount">修改</a>
                            </div>
                        </div>

                        @* 运费区域 *@
                        @{
                            int packageIndex = 0;
                        }
                        @foreach (var package in Model.Packages)
                        {
                            <div class="rows">
                                <label>
                                    包裹@(packageIndex + 1)运费
                                    <div class="p_num">
                                        <div class="mid">@package.TotalQuantity 个产品<i></i></div>
                                        <div class="box_products">
                                            @foreach (var item in package.Items)
                                            {
                                                <div class="list_item">
                                                    <div class="item_img">
                                                        <img src="@Url.Content(item.PicPath)">
                                                        @if (item.Qty > 1)
                                                        {
                                                            <i>@item.Qty</i>
                                                        }
                                                    </div>
                                                    <div class="item_info">
                                                        <div class="info_name">@item.Name</div>
                                                        <div class="info_attr">
                                                            @if (item.Attributes!=null)
                                                            {
                                                                foreach (var attr in item.Attributes)
                                                                {
                                                                    <p>@attr.Key: @attr.Value</p>
                                                                }
                                                            }
                                                            
                                                        </div>
                                                    </div>
                                                </div>
                                            }
                                        </div>
                                    </div>
                                </label>
                                <div class="r_box package" data-id="@package.PackageId">
                                    <input type="hidden" name="ShippingMethod[@package.PackageId]" value="@package.ShippingMethod">
                                    <input type="hidden" name="ShippingName[@package.PackageId]" value="@package.ShippingName">
                                    <input type="hidden" name="ShippingPrice[@package.PackageId]" value="@package.ShippingPrice">
                                    <div class="flex_box">
                                        <div class="box_left">
                                            <div class="tit"></div>
                                        @*     @if (string.IsNullOrEmpty(package.ShippingName))
                                            {
                                                <div class="desc no_shipping">暂无运费</div>
                                            }
                                            else
                                            { *@
                                                <div class="desc has_shipping hide">
                                                    <span>@Model.CurrencySymbol<em class="pack_ship_price">@package.ShippingPrice.ToString("F2")</em></span>
                                                    <span class="web_rate rate_pack_ship_price">@<EMAIL>("F2")</span>
                                                </div>
                                            @* } *@
                                            <span class="hide">物流方式: <em class="pack_ship_name">@package.ShippingName</em></span>
                                            <span class="hide">重量: @package.TotalWeight KG</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="operate">
                                    <a href="javascript:;" class="btn_rig btn_set_shipping" data-id="@package.PackageId">修改</a>
                                </div>
                            </div>
                            packageIndex++;
                        }

                        @* 税费区域 *@
                        <div class="rows">
                            <label>税费</label>
                            <div class="r_box flex_box">
                                <div class="tax">
                                    @Model.CurrencySymbol<span id="TaxPrice"
                                                               data-tax-type="@Model.TaxType"
                                                               data-tax="@Model.TaxRate"
                                                               data-tax-threshold="@Model.TaxThreshold">
                                        @Model.TaxAmount.ToString("F2")
                                    </span>
                                    <span id="rateTaxPrice" class="web_rate">@<EMAIL>("F2")</span>
                                </div>
                            </div>
                            <div class="operate"></div>
                        </div>

                        @* 手续费区域 *@
                        <div class="rows fee_box">
                            <label>手续费</label>
                            <div class="r_box flex_box">
                                <div class="fee">
                                    @Model.CurrencySymbol<span id="PayMentFee">@Model.PaymentFee.ToString("F2")</span>
                                    <span id="ratePayMentFee" class="web_rate">@<EMAIL>("F2")</span>
                                </div>
                            </div>
                            <div class="operate"></div>
                        </div>

                        @* 总价区域 *@
                        <div class="rows total_box">
                            <label>订单总额：</label>
                            <div class="total_price">
                                @Model.CurrencySymbol<span id="OrdersPrice">@Model.TotalPrice.ToString("F2")</span>
                                <span id="rateOrdersPrice" class="web_rate">@<EMAIL>("F2")</span>
                            </div>
                            <div class="operate"></div>
                        </div>

                        @* 汇率区域 *@
                        <div class="rows box_order_rate">
                            <label>汇率</label>
                            <div class="text_rate">1 @Model.BaseCurrency = @Model.ExchangeRate @Model.TargetCurrency</div>
                            <div class="operate"></div>
                        </div>
                    </div>
                    <div class="global_container">
                        <div class="big_title">付款</div>
                        @* 付款状态 *@
                        <div class="rows clean">
                            <label>付款状态</label>
                            <div class="input">
                                <div class="box_type_menu">
                                    <span class="item checked"><input type="radio" name="PaymentStatus" value="unpaid" checked="">未付款</span>
                                    <span class="item "><input type="radio" name="PaymentStatus" value="paid">已付款</span>
                                </div>

                            </div>
                        </div>

                        @* 付款方式 *@

                        <div class="rows">
                            <label>付款方式</label>
                            <div class="input">
                                <div class="box_select full_select sold_status">
                                    <select name="PId">
                                        <option value="" data-fee="0" disabled="disabled" class="hide">请选择</option>
                                        @if (Model.PaymentMethods != null && Model.PaymentMethods.Count > 0)
                                        {
                                            foreach (var payment in Model.PaymentMethods)
                                            {
                                                <option value="@payment.PId"
                                                        data-is-online="@payment.IsOnline"
                                                        data-fee="@payment.AdditionalFee"
                                                        data-min="@payment.MinPrice.ToString("F2")"
                                                        data-max="@payment.MaxPrice.ToString("F2")"
                                                        no-max-limit="@payment.NoMaxLimit.ToString().ToLower()">
                                                    @payment.Name_en
                                                </option>
                                            }
                                        }

                                    
                                       
                                    </select>
                                </div>
                            </div>
                        </div>
         


                    </div>
                    @* 隐藏字段 *@
                    <input type="hidden" name="do_action" value="/api/OrderList/OrdersCreateOrder">
                    <input type="hidden" name="ProductPrice" value="@Model.ProductsPrice.ToString("F2")" data-rate="@Model.RateProductsPrice.ToString("F2")">
                    <input type="hidden" name="DiscountType" value="@Model.DiscountType">
                    <input type="hidden" name="Discount" value="@Model.Discount">
                    <input type="hidden" name="DiscountPrice" value="@Model.DiscountPrice.ToString("F2")">

                </form>
            </div>
            <div id="fixed_right">
                <div class="global_container box_set_shipping" data-width="440">
                    <div class="top_title" data-title="包裹{{number}}选择物流"><strong></strong> <a href="javascript:;" class="close"></a></div>
                    <form class="global_form" id="order_package_edit_form">
                        <div class="rows">
                            <label>物流方式</label>
                            <div class="input">
                                <div class="global_select_box">
                                    <div class="input_case">
                                        <input class="imitation_select box_input full_input" type="text" placeholder="请选择" value="" readonly="" notnull="">
                                        <input type="hidden" name="ShippingMethod" value="0">
                                        <i></i>
                                    </div>
                                    <ul class="select_ul drop_down">
                                        <li class="no_data">
                                            当前暂时没有数据
                                        </li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                        <div class="rows clean">
                            <label>运费</label>
                            <div class="input">
                                <span class="unit_input">
                                    <b>$</b>
                                    <input type="text" class="box_input left_radius" name="ShippingPrice" value="" size="10" rel="amount" maxlength="10" placeholder="0.00">
                                </span>
                            </div>
                            <span class="web_rate rateShippingPrice" style="display:@(Model.manageDefaultCurrency==Model.rateCurrency?"none":"block");">$0.00</span>
                        </div>
                        <input type="hidden" name="PackageId" value="0">
                        <div class="rows clean box_button box_submit">
                            <div class="input">
                                <input type="button" class="btn_global btn_submit" value="保存">
                                <input type="button" class="btn_global btn_cancel" value="取消">
                            </div>
                        </div>
                    </form>
                </div>
                <div class="global_container box_set_discount" data-width="440">
                    <div class="top_title">修改折扣<a href="javascript:;" class="close"></a></div>
                    <div class="global_form">
                        <div class="rows">
                            <label>折扣</label>
                            <div class="input">
                                <div class="box_type_menu fl">
                                    <span class="item checked"><input type="radio" checked="checked" name="DiscountType" value="0">现金减价</span>
                                    <span class="item"><input type="radio" name="DiscountType" value="1">百分比</span>
                                </div>
                                <div class="box_type_menu_content fl">
                                    <div class="item">
                                        <span class="unit_input"><b>$<div class="arrow"><em></em><i></i></div></b><input type="text" class="box_input left_radius" name="DiscountPrice" value="0" size="10" maxlength="10" rel="amount"></span>
                                        <span class="web_rate modifyRateDiscountPrice" style="display:none;">$0.00</span>
                                    </div>
                                    <div class="item hide">
                                        <span class="unit_input"><input type="text" class="box_input right_radius" name="Discount" value="10" size="10" maxlength="2" rel="int"><b class="last">% off</b></span>
                                    </div>
                                </div>
                                <div class="clear"></div>
                            </div>
                        </div>
                    </div>
                    <div class="rows clean box_button box_submit">
                        <div class="input">
                            <input type="button" class="btn_global btn_submit" value="保存">
                            <input type="button" class="btn_global btn_cancel" value="取消">
                        </div>
                    </div>
                </div>
            </div>
        </div>	
        
        <div class="rows clean third_btn_submit fixed_btn_submit" style="width: 2096px; left: 180px;">
            <div class="center_container_1000">
                <div class="input">
                    <input type="button" class="btn_global btn_submit" value="保存">
                    <a href="/Orders/Create?step=second" class="fl"><input type="button" class="btn_global btn_cancel" value="上一步"></a>
                </div>
            </div>
        </div>

    </div>
}
else if (Model.step == "second")
{
    <div>

        <div id="orders_create" class="r_con_wrap orders_create" style="height: 332px;">
            <div class="center_container_1000 authorization_container">
                <div class="return_title"><a href="/manage/orders/orders"> <span class="return">订单管理 / 添加订单</span></a></div>
                <div class="step_box">
                    <div class="list ">
                        <div class="num"><span>1</span></div>
                        <div class="tit">添加产品清单</div>
                    </div>
                    <div class="list current">
                        <div class="num"><span>2</span></div>
                        <div class="tit">添加客户收货信息</div>
                    </div>
                    <div class="list ">
                        <div class="num"><span>3</span></div>
                        <div class="tit">计算价格和付款</div>
                    </div>
                </div>
                <form class="global_form" id="create_second_submit" data-fixed-submit="true">
                    <div class="global_container step_second">
                        <div class="big_title">客户</div>
                        <div class="box_customer">
                            <div class="rows box_customer_email" style="display: none;">
                                <div class="form_box clean">
                                    <div class="box">
                                        <label class="input_box">
                                            <span class="input_box_label labels_text">邮箱</span>
                                            <dl class="box_drop_double edit_box" data-checkbox="0" data-showadd="1">
                                                <dt>
                                                    <input type="text" class="box_input" name="CustomerValue" placeholder="请选择或填写" value="" autocomplete="off" notnull="notnull">
                                                    <input type="hidden" name="Customer" value="0" class="hidden_value">
                                                    <input type="hidden" name="CustomerType" value="user" class="hidden_type">
                                                </dt>
                                                <dd class="drop_down">
                                                    <div class="drop_menu" data-type="">
                                                        <a href="javascript:;" class="btn_back" data-value="" data-type="" data-table="" data-top="0" data-all="0" style="display:none;">返回</a>
                                                        <div class="drop_skin" style="display: none;"></div>
                                                        <div class="drop_list" data="@(Model.UserBookJson)" data-more="none">
                                                            @if (Model.UserBookResultResponseList != null)
                                                            {
                                                                foreach (var item in Model.UserBookResultResponseList)
                                                                {
                                                                    <div class="item" data-name="@(item.Name)" data-value="@(item.Value)" data-type="customer" data-table="undefined" data-top="0" data-all="1" data-check-all="0" data-custom="undefined">
                                                                        <span>@(item.Name)</span>
                                                                    </div>
                                                                }
                                                            }

                                                            @*  <div class="item" data-name="<EMAIL>" data-value="20" data-type="customer" data-table="undefined" data-top="0" data-all="1" data-check-all="0" data-custom="undefined">
                                                        <span><EMAIL></span>
                                                        </div>
                                                        <div class="item" data-name="<EMAIL>" data-value="19" data-type="customer" data-table="undefined" data-top="0" data-all="1" data-check-all="0" data-custom="undefined">
                                                        <span><EMAIL></span>
                                                        </div>
                                                        <div class="item" data-name="<EMAIL>" data-value="18" data-type="customer" data-table="undefined" data-top="0" data-all="1" data-check-all="0" data-custom="undefined">
                                                        <span><EMAIL></span>
                                                        </div>
                                                        <div class="item" data-name="<EMAIL>" data-value="16" data-type="customer" data-table="undefined" data-top="0" data-all="1" data-check-all="0" data-custom="undefined">
                                                        <span><EMAIL></span>
                                                        </div>
                                                        <div class="item" data-name="<EMAIL>" data-value="15" data-type="customer" data-table="undefined" data-top="0" data-all="1" data-check-all="0" data-custom="undefined">
                                                        <span><EMAIL></span>
                                                        </div><div class="item" data-name="<EMAIL>" data-value="14" data-type="customer" data-table="undefined" data-top="0" data-all="1" data-check-all="0" data-custom="undefined">
                                                        <span><EMAIL></span>
                                                        </div>
                                                        <div class="item" data-name="<EMAIL>" data-value="13" data-type="customer" data-table="undefined" data-top="0" data-all="1" data-check-all="0" data-custom="undefined">
                                                        <span><EMAIL></span>
                                                        </div> *@

                                                        </div>
                                                        <a href="javascript:;" class="btn_load_more" data-value="customer" data-type="customer" data-table="customer" data-top="0" data-all="1" data-check-all="0" data-start="1" style="display:block;">加载更多</a>
                                                    </div>
                                                </dd>
                                            </dl>

                                        </label>
                                    </div>
                                    <div class="box"></div>
                                </div>
                            </div>
                            <div class="box_customer_address">
                                <div class="address_head"><strong>配送地址</strong><a href="javascript:;" class="btn_global btn_select_address">选择配送地址</a></div>
                                <div class="address_content">
                                    <div class="rows  hide order-1 col-2" data-field="firstName" style="display: block;">
                                        <div class="box">
                                            <label class="input_box">
                                                <span class="input_box_label labels_text">名</span>
                                                <input type="text" autocomplete="new-password" class="box_input submit_param" name="FirstName" placeholder="名" maxlength="100" notnull="notnull">
                                            </label>
                                            <p class="error"></p>
                                        </div>
                                    </div>
                                    <div class="rows  hide order-2 col-2" data-field="lastName" style="display: block;">
                                        <div class="box">
                                            <label class="input_box">
                                                <span class="input_box_label labels_text">姓</span>
                                                <input type="text" autocomplete="new-password" class="box_input submit_param" name="LastName" placeholder="姓" maxlength="100" notnull="notnull">
                                            </label>
                                            <p class="error"></p>
                                        </div>
                                    </div>
                                    <div class="rows  hide order-4" data-field="address1" style="display: block;">
                                        <div class="input clean">
                                            <label class="input_box">
                                                <span class="input_box_label labels_text">地址</span>
                                                <input type="text" autocomplete="new-password" class="box_input submit_param" name="AddressLine1" placeholder="地址" maxlength="200" notnull="notnull">
                                            </label>
                                            <p class="error"></p>
                                        </div>
                                    </div>
                                    <div class="rows  hide order-5" data-field="address2" style="display: block;">
                                        <div class="input clean">
                                            <label class="input_box">
                                                <span class="input_box_label labels_text">公寓、套房等 （可选的）</span>
                                                <input type="text" autocomplete="new-password" class="box_input submit_param" name="AddressLine2" placeholder="公寓、套房等" maxlength="200">
                                            </label>
                                            <p class="error"></p>
                                        </div>
                                    </div>
                                    <div class="rows  hide order-6" data-field="city" style="display: block;">
                                        <div class="box">
                                            <label class="input_box">
                                                <span class="input_box_label labels_text">城市</span>
                                                <input type="text" autocomplete="new-password" class="box_input submit_param" name="City" placeholder="城市" maxlength="30" notnull="notnull">
                                            </label>
                                            <p class="error"></p>
                                        </div>
                                    </div>
                                    <div class="rows  hide order-7 col-3" data-field="country" style="display: block;">
                                        <div class="box">
                                            <label class="input_box">
                                                <span class="input_box_label labels_text">国家/地区</span>
                                                <dl class="box_drop_double edit_box" data-checkbox="0" data-showadd="0">
                                                    <dt>
                                                        <input type="text" class="box_input" name="country_id_input" placeholder="请选择或填写" value="" autocomplete="off" notnull="">
                                                        <input type="hidden" name="country_id" value="226" class="hidden_value">
                                                        <input type="hidden" name="country_idType" value="country" class="hidden_type">
                                                    </dt>
                                                    <dd class="drop_down" style="display: none;">
                                                        <div class="drop_menu" data-type="Select">
                                                            <a href="javascript:;" class="btn_back" data-value="" data-type="" data-table="" data-top="0" data-all="0" style="display:none;">返回</a>
                                                            <div class="drop_skin" style="display: none;"></div>
                                                            <div class="drop_list" data="@(Model.OrderCountryJson)" data-more="none">

                                                                @if (Model.OrderCountryResponseList != null)
                                                                {
                                                                    foreach (var item in Model.OrderCountryResponseList)
                                                                    {
                                                                        <div class="item" data-name="@(item.Name)" data-value="@(item.Value)" data-type="country" data-table="undefined" data-top="0" data-all="1" data-check-all="0" data-custom="undefined">
                                                                            <span class="input_radio_box">
                                                                                <span class="input_radio">
                                                                                    <input type="radio" name="_DoubleOption[]" value="0">
                                                                                </span>
                                                                            </span>
                                                                            <span class="item_name">@(item.Name)</span>
                                                                        </div>
                                                                    }
                                                                }


                                                                @*  <div class="item" data-name="Afghanistan" data-value="1" data-type="country" data-table="undefined" data-top="0" data-all="1" data-check-all="0" data-custom="undefined">
                                                            <span class="input_radio_box">
                                                            <span class="input_radio">
                                                            <input type="radio" name="_DoubleOption[]" value="0">
                                                            </span>
                                                            </span>
                                                            <span class="item_name">Afghanistan</span>
                                                            </div> *@


                                                            </div>
                                                            <a href="javascript:;" class="btn_load_more" data-value="" data-type="" data-table="" data-top="0" data-all="0" data-check-all="" data-start="0" style="display:none;">加载更多</a>
                                                        </div>
                                                    </dd>
                                                </dl>
                                            </label>
                                        </div>
                                    </div>
                                    <div class="rows  hide order-8 col-3" data-field="province" style="display: no;">
                                        <div class="box">
                                            <label class="input_box clean">
                                                <span class="input_box_label labels_text zindex">州</span>
                                                <div class="province_choose input_box box_select">
                                                    <select name="Province" placeholder="州" class="chzn-done submit_param labels_placeholder" notnull="notnull">
                                                        <option value="126" data-tax="0.00000">Alabama</option>
                                                        <option value="127" data-tax="0.00000">Alaska</option>
                                                        <option value="128" data-tax="0.00000">Arizona</option>
                                                        <option value="129" data-tax="0.00000">Arkansas</option>
                                                        <option value="136" data-tax="0.00000">California</option>
                                                        <option value="137" data-tax="0.00000">Colorado</option>
                                                        <option value="138" data-tax="0.00000">Connecticut</option>
                                                        <option value="139" data-tax="0.00000">Delaware</option>
                                                        <option value="141" data-tax="0.00000">Florida</option>
                                                        <option value="142" data-tax="0.00000">Georgia</option>
                                                        <option value="143" data-tax="0.00000">Hawaii</option>
                                                        <option value="144" data-tax="0.00000">Idaho</option>
                                                        <option value="145" data-tax="0.00000">Illinois</option>
                                                        <option value="146" data-tax="0.00000">Indiana</option>
                                                        <option value="147" data-tax="0.00000">Iowa</option>
                                                        <option value="148" data-tax="0.00000">Kansas</option>
                                                        <option value="149" data-tax="0.00000">Kentucky</option>
                                                        <option value="150" data-tax="0.00000">Louisiana</option>
                                                        <option value="151" data-tax="0.00000">Maine</option>
                                                        <option value="152" data-tax="0.00000">Maryland</option>
                                                        <option value="153" data-tax="0.00000">Massachusetts</option>
                                                        <option value="154" data-tax="0.00000">Michigan</option>
                                                        <option value="155" data-tax="0.00000">Minnesota</option>
                                                        <option value="156" data-tax="0.00000">Mississippi</option>
                                                        <option value="157" data-tax="0.00000">Missouri</option>
                                                        <option value="158" data-tax="0.00000">Montana</option>
                                                        <option value="159" data-tax="0.00000">Nebraska</option>
                                                        <option value="160" data-tax="0.00000">Nevada</option>
                                                        <option value="161" data-tax="0.00000">New Hampshire</option>
                                                        <option value="162" data-tax="0.00000">New Jersey</option>
                                                        <option value="163" data-tax="0.00000">New Mexico</option>
                                                        <option value="164" data-tax="0.00000">New York</option>
                                                        <option value="165" data-tax="0.00000">North Carolina</option>
                                                        <option value="166" data-tax="0.00000">North Dakota</option>
                                                        <option value="167" data-tax="0.00000">Ohio</option>
                                                        <option value="168" data-tax="0.00000">Oklahoma</option>
                                                        <option value="169" data-tax="0.00000">Oregon</option>
                                                        <option value="170" data-tax="0.00000">Pennsylvania</option>
                                                        <option value="172" data-tax="0.00000">South Carolina</option>
                                                        <option value="173" data-tax="0.00000">South Dakota</option>
                                                        <option value="174" data-tax="0.00000">Tennessee</option>
                                                        <option value="175" data-tax="0.00000">Texas</option>
                                                        <option value="176" data-tax="0.00000">Utah</option>
                                                        <option value="177" data-tax="0.00000">Vermont</option>
                                                        <option value="178" data-tax="0.00000">Virginia</option>
                                                        <option value="179" data-tax="0.00000">Washington</option>
                                                        <option value="180" data-tax="0.00000">West Virginia</option>
                                                        <option value="181" data-tax="0.00000">Wisconsin</option>
                                                        <option value="182" data-tax="0.00000">Wyoming</option>
                                                        <option value="202" data-tax="0.00000">District of Columbia</option>
                                                        <option value="203" data-tax="0.00000">Puerto Rico</option>
                                                        <option value="204" data-tax="0.00000">Rhode Island</option>
                                                        <option value="1030" data-tax="0.00000">American Samoa</option>
                                                        <option value="1031" data-tax="0.00000">Micronesia</option>
                                                        <option value="1032" data-tax="0.00000">Guam</option>
                                                        <option value="1033" data-tax="0.00000">Marshall Islands</option>
                                                        <option value="1034" data-tax="0.00000">Northern Mariana Islands</option>
                                                        <option value="1035" data-tax="0.00000">Palau</option>
                                                        <option value="1036" data-tax="0.00000">U.S. Virgin Islands</option>
                                                        <option value="1037" data-tax="0.00000">Armed Forces Americas</option>
                                                        <option value="1038" data-tax="0.00000">Armed Forces Europe</option>
                                                        <option value="1039" data-tax="0.00000">Armed Forces Pacific</option>
                                                    </select>
                                                </div>
                                                <p class="error"></p>
                                            </label>
                                        </div>
                                    </div>
                                    <div class="rows  hide order-9 col-3" data-field="zip" style="display: block;">
                                        <div class="input clean">
                                            <label class="input_box">
                                                <span class="input_box_label labels_text">邮政编码</span>
                                                <input type="text" autocomplete="new-password" class="box_input input_box_txt submit_param labels_placeholder" name="ZipCode" placeholder="邮政编码" maxlength="20" notnull="notnull">
                                            </label>
                                            <p class="error" style="display: none;"></p>
                                        </div>
                                    </div>
                                    <div class="rows  hide order-10" data-field="phone" style="display: block;">
                                        <div class="input clean">
                                            <label class="input_box">
                                                <span class="input_box_label labels_text">电话</span>
                                                <div class="box_input_group clearfix">
                                                    <input name="CountryCode" type="text" value="+0000" class="country_code box_input input_group_addon" readonly="">
                                                    <input type="text" name="PhoneNumber" placeholder="电话" class="box_input input_box_txt submit_param labels_placeholder input_group" notnull="notnull">
                                                </div>
                                            </label>
                                            <p class="error"></p>
                                        </div>
                                    </div>
                                    <div class="tax_code rows hide" data-field="taxId" style="display: none;">
                                        <div class="blank10"></div>
                                        <span class="input_box_label labels_text">税号</span>
                                        <div class="blank6"></div>
                                        <div class="input_box form_box clean">
                                            <div class="tax_code_option_box box" style="display: none;">
                                                <div class="box_select">
                                                    <select name="tax_code_type" class="tax_code_option addr_select"></select>
                                                </div>
                                            </div>
                                            <div class="box">
                                                <label class="input_box">
                                                    <input type="text" name="tax_code_value" placeholder="税号" maxlength="14" class="box_input tax_code_value input_box_txt submit_param labels_placeholder" disabled="disabled">
                                                </label>
                                                <p class="error"></p>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="box_additional_html hide">
                                        <div class="rows" data-field="{{Name}}-{{Id}}" style="display: none;">
                                            <div class="input clean">
                                                <label class="input_box">
                                                    <span class="input_box_label labels_text">{{Name}}</span>
                                                    <input type="text" class="box_input input_box_txt submit_param labels_placeholder" name="AdditionalInfoData[{{Id}}]" placeholder="{{Name}}" value="{{Value}}" disabled="disabled">
                                                </label>
                                                <p class="error"></p>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="rows_address_save">
                                        <span class="input_checkbox_box">
                                            <span class="input_checkbox">
                                                <input type="checkbox" name="SaveAddress" value="1">
                                            </span> 保存到地址薄
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <input type="hidden" name="AddressId" value="0">
                    <input type="hidden" name="do_action" value="/api/OrderList/OrdersEnterThird">
                </form>
            </div>
            <div id="fixed_right">
                <div class="global_container fixed_select_address" data-width="440">
                    <div class="top_title"><span>选择配送地址</span> <a href="javascript:;" class="close"></a></div>
                    <div class="rows clean box_select_address">
                        <div class="input"></div>
                    </div>
                    <div class="no_data"><div class="bg_no_table_data" style="height: 464px;"><div class="content" style="top: 192px;"><p>当前暂时没有数据</p></div><span></span></div></div>
                    <div class="rows clean box_button box_submit">
                        <div class="input">
                            <input type="button" class="btn_global btn_submit" value="保存">
                            <input type="button" class="btn_global btn_cancel" value="取消">
                        </div>
                    </div>
                </div>
            </div>
        </div>		
        <div class="rows clean second_btn_submit fixed_btn_submit" style="width: 2096px; left: 180px;">
            <div class="center_container_1000">
                <div class="input">
                    <input type="button" class="btn_global btn_submit" value="下一步">
                    <a href="/Orders/Create" class="fl"><input type="button" class="btn_global btn_cancel" value="上一步"></a>
                </div>
            </div>
        </div>

    </div>
}
else
{
    <div>

        <div id="orders_create" class="r_con_wrap orders_create" style="height: 461px;">
            <div class="center_container_1000 authorization_container">
                <div class="return_title"><a href="/manage/orders/orders"> <span class="return">订单管理 / 添加订单</span></a></div>
                <div class="step_box">
                    <div class="list current">
                        <div class="num"><span>1</span></div>
                        <div class="tit">添加产品清单</div>
                    </div>
                    <div class="list ">
                        <div class="num"><span>2</span></div>
                        <div class="tit">添加客户收货信息</div>
                    </div>
                    <div class="list ">
                        <div class="num"><span>3</span></div>
                        <div class="tit">计算价格和付款</div>
                    </div>
                </div>
                <form class="global_form" id="create_first_submit" data-fixed-submit="true">
                    <div class="global_container box_currency">
                        <div class="t_tit">结账货币</div>
                        <div class="flex">
                            <div class="box_select">
                                <select name="Currency">
                                    @if (Model.Currencys != null)
                                    {
                                        foreach (var item in Model.Currencys)
                                        {
                                            if (Convert.ToBoolean(item.IsDefault))
                                            {
                                                <option value="@item.CId" selected>
                                                    @(JObject.Parse(item.Name)["zh-cn"].ToString()) (@(item.Currency)/@(item.Symbol))
                                                </option>
                                            }
                                            else
                                            {
                                                <option value="@item.CId">
                                                    @(JObject.Parse(item.Name)["zh-cn"].ToString()) (@(item.Currency)/@(item.Symbol))
                                                </option>
                                            }
                                        }
                                    }


                                    @* <option value="3">英镑 (GBP/￡)</option>
                                    <option value="1" selected="">美元 (USD/$)</option> *@
                                </select>
                            </div>
                            <div class="box_rate">1 @(Model.box_currencyBox_rate.Currency) = @(Model.box_currencyBox_rate.Rate * 1) @(Model.box_currencyBox_rate.Currency)</div>
                        </div>
                        <div class="global_app_tips"><em></em>如需其他货币，请前往 [<a href="/Setting/Basis" target="_blank"> 基础设置 - 货币 </a>] 进行添加</div>
                    </div>
                    <div class="global_container create_products_list">
                        <div class="t_tit">
                            <a class="fr g_btn g_btn_main add_pro" href="javascript:;">添加产品</a>
                            <a class="fr g_btn g_btn_sec btn_customize_products" href="javascript:;">自定义产品</a>
                            产品
                        </div>
                        <table border="0" cellpadding="5" cellspacing="0" class="r_con_table">
                            <thead>
                                <tr>
                                    <td width="50%" nowrap="nowrap">产品</td>
                                    <td width="10%" nowrap="nowrap">价格</td>
                                    <td width="10%" nowrap="nowrap">购买数量/库存</td>
                                    <td width="10%" nowrap="nowrap">总计</td>
                                    <td width="74" nowrap="nowrap" class="operation"></td>
                                </tr>
                            </thead>
                            <tbody></tbody>
                        </table>
                        <div class="no_data">
                            <div class="bg_no_table_data" style="height: 200px;"><div class="content" style="top: 48px;"><p>当前暂时没有数据</p></div><span></span></div>
                        </div>
                    </div>

                    <input type="hidden" name="do_action" value="/api/OrderList/OrdersEnterSecond">
                </form>
            </div>
            <div id="fixed_right">
                <div class="global_container box_select_attribute" data-width="350" style="display: none;">
                    <div class="top_title">设置规格<a href="javascript:;" class="close"></a></div>
                    <div class="global_form form_select_attribute"></div>
                    <input type="hidden" name="TrIndex">
                    <div class="rows clean box_button box_submit">
                        <div class="input">
                            <input type="button" class="btn_global btn_submit" value="保存">
                            <input type="button" class="btn_global btn_cancel" value="取消">
                        </div>
                    </div>
                </div>
                <div class="global_container box_customize_products" data-width="350" style="display: none; height: 991px;">
                    <div class="top_title">自定义产品<a href="javascript:;" class="close"></a></div>
                    <form class="global_form" id="form_customize_products">
                        <div class="rows clean">
                            <label>产品名称</label>
                            <div class="input">
                                <div class="number_limit_relative" style="position:relative"><div class="number_limit"><span> 0 </span> / 255</div><textarea name="Name" class="box_textarea full_textarea" maxlength="255" number_limit="" notnull=""></textarea></div>
                            </div>
                        </div>
                        <div class="rows clean">
                            <label>产品主图</label>
                            <div class="input">
                                <div class="multi_img upload_file_multi " id="ordersPicDetail"><dl class="img" num="0"><dt class="upload_box preview_pic"><input type="button" class="btn_ok upload_btn" name="submit_button" value="" tips="" save="0"><input type="hidden" name="ordersPicPath" value="" data-value="" save="0"></dt><dd class="pic_btn"><a href="javascript:;" class="zoom" target="_blank"><i class="icon_multi_view"></i></a><a href="javascript:;" class="del" rel="del"><i class="icon_multi_delete"></i></a></dd></dl></div>
                            </div>
                        </div>
                        <div class="rows clean">
                            <label>价格</label>
                            <div class="input">
                                <span class="unit_input"><b>$<div class="arrow"><em></em><i></i></div></b><input autocomplete="off" name="Price" value="0.00" type="text" class="box_input left_radius" rel="amount" maxlength="10" size="14" notnull=""></span>
                            </div>
                        </div>
                        <div class="rows clean">
                            <label>购买数量</label>
                            <div class="input">
                                <input type="text" class="box_input" name="Qty" value="1" size="21" maxlength="10" rel="int" autocomplete="off" notnull="">
                            </div>
                        </div>
                        <div class="rows clean">
                            <label>重量</label>
                            <div class="input">
                                <span class="unit_input" parent_null=""><input autocomplete="off" name="Weight" value="" type="text" class="box_input right_radius" rel="amount" maxlength="10" size="13" notnull="" parent_null="1"><b class="last">KG</b></span>
                            </div>
                        </div>
                        <input type="hidden" name="ProId" value="Add_1745986188025">
                        <input type="hidden" name="do_action" value="/api/OrderList/OrdersAddCustomizeProducts">
                        <div class="rows clean box_button box_submit">
                            <div class="input">
                                <input type="button" class="btn_global btn_submit" value="保存">
                                <input type="button" class="btn_global btn_cancel" value="取消">
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>		<div class="rows clean fixed_btn_submit" style="width: 2096px; left: 180px;">
            <div class="center_container_1000">
                <div class="input">
                    <input type="button" class="btn_global btn_submit" value="下一步" disabled="disabled">
                    <a href="/Orders/List"><input type="button" class="btn_global btn_cancel" value="返回"></a>
                </div>
            </div>
        </div>

    </div>

}


