using Entitys;
using MailKit.Search;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.CodeAnalysis;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using OfficeOpenXml.FormulaParsing.Excel.Functions.Text;
using Org.BouncyCastle.Asn1.Cms;
using Org.BouncyCastle.Utilities;
using SqlSugar;
using System;
using YseStore.IService;
using YseStore.IService.Customer;
using YseStore.IService.Order;
using YseStore.IService.Products;
using YseStore.Model.VM;
using YseStore.Model.VM.Payment.Paypal;
using YseStore.Service;
using YseStore.Service.Products;
using YseStoreAdmin.Pages.Setting;

namespace YseStoreAdmin.Pages.Components.Orders
{
    public class OrderCreate : MComponent
    {
        public string step { get; set; }
        public ISettingBasisService _settingBasisService { get; }
        public IOrderListService _orderListService { get; }
        public ICustomerListService _customerListService { get; }
        public IProductService _productService { get; }
        private IPaymentService _paymentService { get; }
        private ICountryService _countryService { get; }
        private IUserService _userService { get; }
        public IHelpsCartService _helpsCartService;
        public IHelpsUserService _helpsUserService;
        public IHelpsManageService _helpsManageService;

        public List<CarrierResultResponse>? UserBookResultResponseList { get; set; }
        public string UserBookJson { get; set; }
        public List<OrderCountryResponse>? OrderCountryResponseList { get; set; }
        public string OrderCountryJson { get; set; }



        // 基础信息
        public string CurrencySymbol { get; set; }
        public string RateSymbol { get; set; }
        public string BaseCurrency { get; set; }
        public string TargetCurrency { get; set; }
        public decimal ExchangeRate { get; set; }

        // 产品信息
        public int ProductsQty { get; set; }
        public decimal ProductsPrice { get; set; }
        public decimal RateProductsPrice { get; set; }
        public List<ProductItem> Products { get; set; }

        // 折扣信息
        public int DiscountType { get; set; }
        public decimal Discount { get; set; }
        public decimal DiscountPrice { get; set; }
        public decimal RateDiscountPrice { get; set; }

        // 包裹信息
        public List<ShippingPackage> Packages { get; set; }

        // 税费信息
        public int TaxType { get; set; }
        public decimal TaxRate { get; set; }
        public decimal TaxThreshold { get; set; }
        public decimal TaxAmount { get; set; }
        public decimal RateTaxAmount { get; set; }

        // 付款信息
        public string PaymentStatus { get; set; }
        public List<PaymentMethod> PaymentMethods { get; set; }
        public decimal PaymentFee { get; set; }
        public decimal RatePaymentFee { get; set; }

        // 总价信息
        public decimal TotalPrice { get; set; }
        public decimal RateTotalPrice { get; set; }

        public class ProductItem
        {
            public string PicPath { get; set; }
            public string Name { get; set; }
            public int Qty { get; set; }
            public Dictionary<string, string> Attributes { get; set; }
        }

        public class ShippingPackage
        {
            public string PackageId { get; set; }
            public int TotalQuantity { get; set; }
            public decimal TotalWeight { get; set; }
            public string ShippingMethod { get; set; }
            public string ShippingName { get; set; }
            public decimal ShippingPrice { get; set; }
            public decimal RateShippingPrice { get; set; }
            public List<ProductItem> Items { get; set; }
        }

        public class PaymentMethod
        {
            public string PId { get; set; }
            public string Name_en { get; set; }
            public decimal AdditionalFee { get; set; }
            public decimal MinPrice { get; set; }
            public decimal MaxPrice { get; set; }
            public bool NoMaxLimit { get; set; }
            public bool IsOnline { get; set; }
        }
        public IList<Entitys.currency> Currencys { get; set; }
        public Entitys.currency box_currencyBox_rate { get; set; }
        public string manageDefaultCurrency { get; set; }
        public string rateCurrency { get; set; }
        public OrderCreate(IOrderListService orderListService, ICustomerListService customerListService, IProductService productService,
            IPaymentService paymentService, ICountryService countryService, IUserService userService, ISettingBasisService settingBasisService,
            IHelpsCartService helpsCartService, IHelpsUserService helpsUserService,
            IHelpsManageService helpsManageService)
        {
            _orderListService = orderListService;
            _customerListService = customerListService;
            _productService = productService;
            _paymentService = paymentService;
            _countryService = countryService;
            _userService = userService;
            _settingBasisService = settingBasisService;
            _helpsCartService = helpsCartService;
            _helpsUserService = helpsUserService;
            _helpsManageService = helpsManageService;
        }
        public override async Task MountAsync()
        {
            Currencys = await _settingBasisService.GetCurrencyAry();
            box_currencyBox_rate = await _settingBasisService.GetCurrencyManageDefaultAry();
            step = HttpContext.Request.Query["step"].ToString();
            UserBookResultResponseList = await _orderListService.GetUserBookData();

            UserBookJson = System.Text.Json.JsonSerializer.Serialize(UserBookResultResponseList);
            OrderCountryResponseList = await _orderListService.GetCountryData();
            OrderCountryJson = System.Text.Json.JsonSerializer.Serialize(OrderCountryResponseList);
            if (step == "third")
            {
                await GetLogs();
                GetLogsTwo();
            }

        }

        public async Task<IActionResult> GetLogs()
        {
            try
            {
                // 从Session获取存储的数据
                var orderCreate = HttpContext.Session.GetString("OrderCreate") != null
                    ? JsonConvert.DeserializeObject<Dictionary<string, object>>(HttpContext.Session.GetString("OrderCreate"))
                    : new Dictionary<string, object>();

                var productsList = orderCreate.ContainsKey("first")
                    ? JsonConvert.DeserializeObject<List<Dictionary<string, object>>>(orderCreate["first"].ToString())
                    : new List<Dictionary<string, object>>();

                var orderAddress = orderCreate.ContainsKey("second")
                    ? JsonConvert.DeserializeObject<Dictionary<string, object>>(orderCreate["second"].ToString())
                    : new Dictionary<string, object>();

                // 验证必要数据
                if (!productsList.Any()) return new JsonResult(new { Success = 0, Message = "缺少产品数据" });
                if (!orderAddress.Any()) return new JsonResult(new { Success = 0, Message = "缺少地址数据" });

                var currency = orderCreate.ContainsKey("currency")
                    ? Convert.ToInt32(orderCreate["currency"])
                    : 1;
                var CurrencyList = await _orderListService.getCurrencyList();

                // 初始化配置参数
                var c = new { manage = new { web_lang = "en", currency_symbol = "$" } }; // 示例配置
                var lang = c.manage.web_lang;
                var manageCurrency = CurrencyList.Where(x => x.ManageDefault == true).FirstOrDefault();
                manageDefaultCurrency = manageCurrency.Currency;
                var CurrencyData = CurrencyList.Where(x => x.CId == currency).FirstOrDefault();
                RateSymbol = CurrencyData.Symbol;
                rateCurrency = CurrencyData.Currency;
                //var rate = CurrencyData.Rate;
                var rate = Math.Round(CurrencyData.Rate.Value / manageCurrency.Rate.Value, 4);


                // 产品处理
                var packAry = new Dictionary<string, List<Dictionary<string, object>>>();
                var packWeightAry = new Dictionary<string, decimal>();
                decimal productsPrice = 0, rateProductsPrice = 0;
                int productsQty = 0;

                foreach (var pro in productsList)
                {
                    var proId = pro["ProId"].ToString();
                    var data = new Dictionary<string, object>();
                    var tIds = 0;
                    var ovIds = 0;
                    var Weight = 0.0m;
                    if (proId.Contains("Add_"))
                    {
                        // 构建产品数据
                        data = new Dictionary<string, object>
                        {
                            { "BuyType", 0 },
                            { "Name", pro["Name"] },
                            { "ProId", proId },
                            { "KeyId", proId },
                            { "IsFreeShipping", "0" },
                            { "SKU", "" },
                            { "PicPath", pro["PicPath_0"] },
                            { "StartFrom", 1 },
                            { "Weight", pro["Weight"] },
                            { "Volume", 0 },
                            { "Price", pro["Price"] },
                            { "PropertyPrice", 0 },
                            { "CustomPrice", 0 },
                            { "Discount", 100 },
                            { "Qty", pro["Qty"] },
                            { "Property", new Dictionary<string, object>() },
                            { "Attr", new Dictionary<string, object>() },
                            { "VariantsId", pro["VariantsId"] },
                            { "OvId", 0 },
                            { "Language", lang },
                            { "AddTime", DateTime.Now },
                            { "IsCombination", true},
                            { "isVirtual", false }
                        };
                    }
                    else
                    {
                        var PropertyDict = new Dictionary<string, string>();
                        var attrDict = new Dictionary<string, string>();
                        foreach (var keys in pro.Keys)
                        {
                            if (keys.StartsWith("Attr_"))
                            {
                                var parts = keys.Split(new[] { '_' }, StringSplitOptions.RemoveEmptyEntries);
                                if (parts.Length >= 3)
                                {
                                    // 提取第三部分作为属性ID（例如 "470"）
                                    var attrId = parts[2];
                                    
                                    if (Convert.ToInt32(attrId) > 0)
                                    {
                                        var Name_en = await _orderListService.getproducts_attributeByAttrId(Convert.ToInt32(attrId));
                                        var sdfsadf= pro[keys];
                                        PropertyDict[Name_en] = pro[keys].ToString();
                                        attrDict[Name_en] = pro[keys].ToString(); // 添加到 Attr 字典
                                    }
                                    else
                                    {
                                        // 获取原始属性值
                                        var rawValue = pro[keys]?.ToString() ?? string.Empty;
                                        string mappedValue = await _orderListService.getshipping_overseasByOvId(Convert.ToInt32(rawValue));
                                        attrDict["发货地"] = mappedValue;
                                        ovIds = Convert.ToInt32(pro[keys]);
                                    }

                                }
                            }
                        }

                        var product = await _orderListService.GetproductById(Convert.ToInt32(proId));
                        var rawData = await _orderListService.getAttributeTwoByProId(Convert.ToInt32(proId));
                        var SKU = "";
                        //if (!string.IsNullOrEmpty(pro["VariantsId"].ToString()))
                        //{
                        //    var VariantsIds = pro["VariantsId"].ToString().Split(',').ToList();

                        //    var AttributeByProId = rawData.Where(x => VariantsIds.Contains(x.VariantsId)).ToList();
                        //    if (AttributeByProId != null)
                        //    {
                        //        foreach (var item in AttributeByProId)
                        //        {
                        //            tIds += item?.OvId ?? 0;
                        //            Weight += Convert.ToDecimal(item?.Weight);
                        //            var _sku = item?.SKU ?? "";
                        //            if (!string.IsNullOrEmpty(_sku))
                        //            {
                        //                SKU = _sku;
                        //            }
                        //        }
                        //    }
                        //}
                        //else
                        //{
                        //    Weight = Convert.ToDecimal(product.Weight);
                        //}
            //            规格模式
            //The combination type of product attributes
            //0 = No combination(single)
            //1 = Full combination(多规格)
            //2 = Addition combination(多规格加价)
                        if (product.IsCombination==0|| product.IsCombination == 2)
                        {
                            var AttributeFirst = rawData.Where(x => x.VariantsId == "").First();
                            Weight = Convert.ToDecimal(AttributeFirst.Weight);
                            //ovIds = AttributeFirst?.OvId ?? 0;
                            var _sku = AttributeFirst?.SKU ?? "";
                            if (!string.IsNullOrEmpty(_sku))
                            {
                                SKU = _sku;
                            }
                        }
                        else
                        {
                            if (!string.IsNullOrEmpty(pro["VariantsId"].ToString()))
                            {
                                var VariantsIds = pro["VariantsId"].ToString().Split(',').ToList();
                                var AttributeByProId = rawData.Where(x => VariantsIds.Contains(x.VariantsId)).First();
                                Weight = Convert.ToDecimal(AttributeByProId.Weight);
                                ovIds = AttributeByProId?.OvId ?? 0;
                                var _sku = AttributeByProId?.SKU ?? "";
                                if (!string.IsNullOrEmpty(_sku))
                                {
                                    SKU = _sku;
                                }
                            }
                        }


                        if (product == null) continue;
                        tIds=product.TId;
                        // 构建产品数据
                        data = new Dictionary<string, object>
                        {
                            { "BuyType", 0 },
                            { "Name", product.Name_en },
                            { "ProId", product.ProId },
                            { "KeyId", product.ProId },
                            { "IsFreeShipping", Convert.ToString(product.IsFreeShipping) },
                            { "SKU", SKU },
                            { "PicPath", product.PicPath_0 },
                            { "StartFrom", 1 },
                            { "Weight", Weight },
                            { "Volume", 0 },
                            { "Price", pro["Price"] },
                            { "PropertyPrice", 0 },
                            { "CustomPrice", 0 },
                            { "Discount", 100 },
                            { "Qty", pro["Qty"] },
                            { "Property", PropertyDict },
                            { "Attr", attrDict },
                            { "VariantsId", pro["VariantsId"] },
                            { "OvId", ovIds },
                            { "Language", lang },
                            { "AddTime", DateTime.Now },
                            { "IsCombination", product.IsCombination },
                            { "isVirtual", product.isVirtual }
                        };
                    }



                    // 计算价格和数量
                    var qty = Convert.ToInt32(pro["Qty"]);
                    var price = Convert.ToDecimal(pro["Price"]);
                    productsPrice += _helpsCartService.CeilPrice(price * qty);
                    rateProductsPrice += _helpsCartService.CeilPrice(_helpsCartService.CurrencyFloatPrice(_helpsCartService.CeilPrice(price*rate), rateCurrency) * qty);
                    productsQty += qty;

                    // 运费分组处理
                    var tId = tIds;
                    var ovId = ovIds;
                    var key = $"{ovId}-{tId}";

                    if (!packAry.ContainsKey(key))
                    {
                        packAry[key] = new List<Dictionary<string, object>>();
                        packWeightAry[key] = 0;
                    }

                    packAry[key].Add(data);
                    packWeightAry[key] += Convert.ToDecimal(data["Weight"]) * qty;
                }

                // 存储处理结果到Session
                orderCreate["third"] = new Dictionary<string, object>
                {
                    { "package", packAry },
                    { "totalPrice", productsPrice },
                    { "totalQty", productsQty },
                    { "rateTotalPrice", rateProductsPrice }
                };

                // 更新Session
                HttpContext.Session.SetString("OrderCreate", JsonConvert.SerializeObject(orderCreate));

                // 处理付款方式
                var countryId = Convert.ToInt32(orderAddress["CountryId"]);
                var payments = await _paymentService.Getpayment();
                var filteredPayments = payments
                    .Where(p => Convert.ToBoolean(p.IsUsed) && p.PId != 2)
                    .ToList();

                // 处理税率
                var country = await _countryService.GetCountryAsync(countryId);
                var provinceId = Convert.ToInt32(orderAddress["Province"]);
                var province = new country_states();
                if (provinceId != 0)
                {
                    province = await _countryService.GetProvinceAsync(provinceId);
                }

                //var taxAry = new
                //{
                //    taxType = 1,
                //    taxThreshold = country?.TaxThreshold ?? 0,
                //    tax = province?.Tax > 0 ? province.Tax : country?.Tax ?? 0
                //};


                var taxAry = new TaxInfo
                {
                    taxType = 1,
                    taxThreshold = country?.TaxThreshold ?? 0,
                    tax = province?.Tax > 0 ? province.Tax : country?.Tax ?? 0
                };

                // 用户免税检查
                var userEmail = orderAddress["Email"].ToString();
                var user = await _paymentService.GetUserByEmailAsync(userEmail);
                if (user?.IsTaxExempt == true)
                {
                    taxAry.tax = 0;
                }

                // 存储附加信息到Session
                orderCreate["taxInfo"] = taxAry;
                orderCreate["payments"] = filteredPayments;
                HttpContext.Session.SetString("OrderCreate", JsonConvert.SerializeObject(orderCreate));

                return new JsonResult(new { Success = 1 });
            }
            catch (System.Exception ex)
            {
                return new JsonResult(new { Success = 0, Message = ex.Message });
            }
        }



        public async void GetLogsTwo()
        {
            try
            {
                // 从Session获取存储的数据
                var orderCreate = HttpContext.Session.GetString("OrderCreate") != null
                    ? JsonConvert.DeserializeObject<Dictionary<string, object>>(HttpContext.Session.GetString("OrderCreate"))
                    : new Dictionary<string, object>();

                var currency = orderCreate.ContainsKey("currency")
                    ? Convert.ToInt32(orderCreate["currency"])
                    : 1;
                var CurrencyList = await _orderListService.getCurrencyList();

                var manageDefaultData = CurrencyList.Where(x => x.ManageDefault == true).FirstOrDefault();
                var CurrencyData = CurrencyList.Where(x => x.CId == currency).FirstOrDefault();
                
                // ===== 基础信息赋值 =====
                var manageConfig = new
                {
                    currency_symbol = manageDefaultData.Symbol,
                    rate_symbol = CurrencyData.Symbol,
                    base_currency = manageDefaultData.Currency,
                    target_currency = CurrencyData.Currency,
                    exchange_rate = Math.Round(CurrencyData.Rate.Value / manageDefaultData.Rate.Value, 4)
                };
                CurrencySymbol = manageConfig.currency_symbol;
                RateSymbol = manageConfig.rate_symbol;
                BaseCurrency = manageConfig.base_currency;
                TargetCurrency = manageConfig.target_currency;
                ExchangeRate =manageConfig.exchange_rate;

                // ===== 产品信息赋值 =====
                var thirdData = orderCreate.ContainsKey("third")
                    ? JsonConvert.DeserializeObject<Dictionary<string, object>>(orderCreate["third"].ToString())
                    : new Dictionary<string, object>();

                ProductsQty = Convert.ToInt32(thirdData["totalQty"]);
                ProductsPrice = Convert.ToDecimal(thirdData["totalPrice"]);
                RateProductsPrice = Convert.ToDecimal(thirdData["rateTotalPrice"]);

                // 构建产品列表
                var productsList = orderCreate.ContainsKey("first")
                    ? JsonConvert.DeserializeObject<List<Dictionary<string, object>>>(orderCreate["first"].ToString())
                    : new List<Dictionary<string, object>>();
                Products = new List<ProductItem>();
                foreach (var pro in productsList)
                {
                    var proId = pro["ProId"].ToString();
                    if (proId.Contains("Add_"))
                    {

                        Products.Add(new ProductItem()
                        {
                            PicPath = pro["PicPath_0"].ToString(),
                            Name = pro["Name"].ToString(),
                            Qty = Convert.ToInt32(pro["Qty"]),
                            Attributes = new Dictionary<string, string>()
                            //Attributes = pro["Attr"] as Dictionary<string, string> ?? new Dictionary<string, string>()
                        });
                    }
                    else
                    {
                        var product = await _orderListService.GetproductById(Convert.ToInt32(proId));
                        if (product == null) continue;
                        var Attributes = new Dictionary<string, string>();
                        
                            // 遍历产品属性字典
                            foreach (var key in pro.Keys)
                            {
                                // 仅处理以"Attr_"开头的属性
                                if (!key.StartsWith("Attr_")) continue;

                                // 解析属性键结构（示例：Attr_48_44 → [Attr,48,44]）
                                var keyParts = key.Split('_', StringSplitOptions.RemoveEmptyEntries);
                                if (keyParts.Length < 3) continue;

                                // 验证产品ID匹配（防止跨产品数据污染）
                                if (!int.TryParse(keyParts[1], out int currentProId) || currentProId != Convert.ToInt32(proId))
                                    continue;

                                // 解析属性ID
                                if (!int.TryParse(keyParts[2], out int attrId))
                                    continue;

                                // 获取原始属性值
                                var rawValue = pro[key]?.ToString() ?? string.Empty;

                                // 处理不同属性类型
                                if (attrId > 0)
                                {
                                    // 处理常规属性（示例：44→选项，45→颜色）
                                    string attrName = await _orderListService.getproducts_attributeByAttrId(attrId);
                                    if (!string.IsNullOrEmpty(attrName))
                                    {
                                        Attributes.Add(attrName, rawValue);
                                    }
                                }
                                else
                                {
                                    // 处理特殊属性（示例：0→发货地）

                                    string mappedValue = await _orderListService.getshipping_overseasByOvId(Convert.ToInt32(rawValue));
                                    Attributes.Add("发货地", mappedValue);
                                }
                            }

                        

                     






                        Products.Add(new ProductItem()
                        {
                            PicPath = product.PicPath_0,
                            Name = product.Name_en,
                            Qty = Convert.ToInt32(pro["Qty"]),
                            Attributes = Attributes
                            //Attributes = pro["Attr"] as Dictionary<string, string> ?? new Dictionary<string, string>()
                        });
                    }



                    //Products = productsList.Select(pro => new ProductItem
                    //{
                    //    PicPath = pro["PicPath"].ToString(),
                    //    Name = product.Name_en,
                    //    Qty = Convert.ToInt32(pro["Qty"]),
                    //    Attributes = pro["Attr"] as Dictionary<string, string> ?? new Dictionary<string, string>()
                    //}).ToList();


                }




                // ===== 包裹信息赋值 =====

                //var packArys = thirdData["package"];

                //var packAryss = thirdData["package"] as Dictionary<string, object>;

                var packAry = JsonConvert.DeserializeObject<Dictionary<string, object>>(thirdData["package"].ToString());

                Packages = new List<ShippingPackage>();


                foreach (var packageData in packAry)
                {
                    var packageKey = packageData.Key;
                    //var jsonArray = JArray.Parse(packageData.Value.ToString());
                    //var items = jsonArray.ToObject<List<ProductItemTwo>>();
                    var jsonData = packageData.Value.ToString();
                    var items = JsonConvert.DeserializeObject<List<ProductItemTwo>>(jsonData);

                    //var weightData = thirdData["packWeightAry"] as Dictionary<string, object>;
                    //根据包裹的分组进行----------

                    Packages.Add(new ShippingPackage
                    {
                        PackageId = packageKey,
                        TotalQuantity = items.Sum(i => Convert.ToInt32(i.Qty)),
                        TotalWeight = items.Sum(i => Convert.ToDecimal(i.Qty * i.Weight)),
                        //TotalWeight = Convert.ToDecimal(weightData[packageKey]),
                        Items = items.Select(i => new ProductItem
                        {
                            PicPath = i.PicPath.ToString(),
                            Name = i.Name.ToString(),
                            Qty = Convert.ToInt32(i.Qty),
                            Attributes=i.Attr
                        }).ToList()
                    });


                    //                   public class ShippingPackage
                    //{
                    //    public string PackageId { get; set; }
                    //    public int TotalQuantity { get; set; }
                    //    public decimal TotalWeight { get; set; }
                    //    public string ShippingMethod { get; set; }
                    //    public string ShippingName { get; set; }
                    //    public decimal ShippingPrice { get; set; }
                    //    public decimal RateShippingPrice { get; set; }
                    //    public List<ProductItem> Items { get; set; }
                    //}

                    #region
                    //foreach (var item in items)
                    //{
                    //    Packages.Add(new ShippingPackage
                    //    {
                    //        PackageId = packageKey,
                    //        TotalQuantity = item.Qty,
                    //        TotalWeight = item.Qty * Convert.ToDecimal(item.Weight),
                    //        //TotalWeight = Convert.ToDecimal(weightData[packageKey]),
                    //        Items = items.Select(i => new ProductItem()
                    //        {
                    //            PicPath = i.PicPath.ToString(),
                    //            Name = i.Name.ToString(),
                    //            Qty = Convert.ToInt32(i.Qty),
                    //            //Attributes = i.Property
                    //        }).ToList()


                    //        //Items = new ProductItem()
                    //        //{
                    //        //    PicPath = item.PicPath.ToString(),
                    //        //    Name = item.Name.ToString(),
                    //        //    Qty = Convert.ToInt32(item.Qty)
                    //        //}
                    //    });
                    //}
                    //foreach (var item in items)
                    //{
                    //    Packages.Add(new ShippingPackage
                    //    {
                    //        PackageId = packageKey,
                    //        TotalQuantity = items.Sum(i => Convert.ToInt32(i.Qty)),
                    //        TotalWeight = items.Sum(i => Convert.ToDecimal(i.Qty * i.Weight)),
                    //        //TotalWeight = Convert.ToDecimal(weightData[packageKey]),
                    //        Items = items.Select(i => new ProductItem
                    //        {
                    //            PicPath = i.PicPath.ToString(),
                    //            Name = i.Name.ToString(),
                    //            Qty = Convert.ToInt32(i.Qty)
                    //        }).ToList()
                    //    });
                    //}

                    //Packages.Add(new ShippingPackage
                    //{
                    //    PackageId = packageKey,
                    //    TotalQuantity = items.Sum(i => Convert.ToInt32(i.Qty)),
                    //    TotalWeight = items.Sum(i => Convert.ToDecimal(i.Qty*i.Weight)),
                    //    //TotalWeight = Convert.ToDecimal(weightData[packageKey]),
                    //    Items = items.Select(i => new ProductItem
                    //    {
                    //        PicPath = i.PicPath.ToString(),
                    //        Name = i.Name.ToString(),
                    //        Qty = Convert.ToInt32(i.Qty)
                    //    }).ToList()
                    //});
                    #endregion



                }

                // ===== 税费信息赋值 =====
                var taxInfo = orderCreate.ContainsKey("taxInfo")
                    ? JsonConvert.DeserializeObject<TaxInfo>(orderCreate["taxInfo"].ToString())
                    : new TaxInfo();

                TaxType = taxInfo.taxType;
                TaxRate = taxInfo.tax;
                TaxThreshold = taxInfo.taxThreshold;
                TaxAmount = taxInfo.tax; // 实际需要计算，此处示例直接使用税率值

                // ===== 付款信息赋值 =====
                var payments = orderCreate.ContainsKey("payments")
                    ? JsonConvert.DeserializeObject<List<PaymentMethod>>(orderCreate["payments"].ToString())
                    : new List<PaymentMethod>();

                PaymentMethods = payments
                    .OrderByDescending(p=>p.Name_en)
                    .Select(p => new PaymentMethod
                {
                    PId = p.PId,
                    Name_en = p.Name_en,
                    AdditionalFee = p.AdditionalFee,
                    MinPrice = p.MinPrice,
                    MaxPrice = p.MaxPrice,
                    NoMaxLimit = p.NoMaxLimit,
                    IsOnline = p.IsOnline
                }).ToList();

                // ===== 折扣信息赋值 =====
                // 折扣信息需要从Session或单独接口获取，此处示例留空
                DiscountType = 0;
                Discount = 0;
                DiscountPrice = 0;
                RateDiscountPrice = 0;

                // ===== 总价计算 =====
                TotalPrice = ProductsPrice
                                     + Packages.Sum(p => p.ShippingPrice)
                                     + TaxAmount
                                     + PaymentFee;

                RateTotalPrice = RateProductsPrice
                                        + Packages.Sum(p => p.RateShippingPrice)
                                        + RateTaxAmount
                                        + RatePaymentFee;


            }
            catch (System.Exception ex)
            {

            }
        }



        public class ProductItemTwo
        {
            public int BuyType { get; set; }
            public string Name { get; set; }
            public string ProId { get; set; }
            public string KeyId { get; set; }
            public string SKU { get; set; }
            public string PicPath { get; set; }
            public int StartFrom { get; set; }
            public double Weight { get; set; }
            public int Volume { get; set; }
            public decimal Price { get; set; }
            public decimal PropertyPrice { get; set; }
            public decimal CustomPrice { get; set; }
            public int Discount { get; set; }
            public int Qty { get; set; }
            public Dictionary<string, string> Property { get; set; } = new Dictionary<string, string>();
            public Dictionary<string, string> Attr { get; set; } = new Dictionary<string, string>();
            public string VariantsId { get; set; }
            public int OvId { get; set; }
            public string Language { get; set; }
            public DateTime AddTime { get; set; }
            public bool IsCombination { get; set; }
            public bool isVirtual { get; set; }
        }


        public class TaxInfo
        {
            public int taxType { get; set; }
            public decimal taxThreshold { get; set; }
            public decimal tax { get; set; }
        }

    }

}
