@model YseStoreAdmin.Pages.Components.Orders.OrderCreate2
@{
}
<div>

    <div id="orders_create" class="r_con_wrap orders_create" style="height: 332px;">
        <div class="center_container_1000 authorization_container">
            <div class="return_title"><a href="/manage/orders/orders"> <span class="return">订单管理 / 添加订单</span></a></div>
            <div class="step_box">
                <div class="list ">
                    <div class="num"><span>1</span></div>
                    <div class="tit">添加产品清单</div>
                </div>
                <div class="list current">
                    <div class="num"><span>2</span></div>
                    <div class="tit">添加客户收货信息</div>
                </div>
                <div class="list ">
                    <div class="num"><span>3</span></div>
                    <div class="tit">计算价格和付款</div>
                </div>
            </div>
            <form class="global_form" id="create_second_submit" data-fixed-submit="true">
                <div class="global_container step_second">
                    <div class="big_title">客户</div>
                    <div class="box_customer">
                        <div class="rows box_customer_email" style="display: none;">
                            <div class="form_box clean">
                                <div class="box">
                                    <label class="input_box">
                                        <span class="input_box_label labels_text">邮箱</span>
                                        <dl class="box_drop_double edit_box" data-checkbox="0" data-showadd="1">
                                            <dt>
                                                <input type="text" class="box_input" name="CustomerValue" placeholder="请选择或填写" value="" autocomplete="off" notnull="notnull">
                                                <input type="hidden" name="Customer" value="0" class="hidden_value">
                                                <input type="hidden" name="CustomerType" value="user" class="hidden_type">
                                            </dt>
                                            <dd class="drop_down">
                                                <div class="drop_menu" data-type="">
                                                    <a href="javascript:;" class="btn_back" data-value="" data-type="" data-table="" data-top="0" data-all="0" style="display:none;">返回</a>
                                                    <div class="drop_skin" style="display: none;"></div>
                                                    <div class="drop_list" data="@(Model.UserBookJson)" data-more="none">
                                                        @if (Model.UserBookResultResponseList != null)
                                                        {
                                                            foreach (var item in Model.UserBookResultResponseList)
                                                            {
                                                                <div class="item" data-name="@(item.Name)" data-value="@(item.Value)" data-type="customer" data-table="undefined" data-top="0" data-all="1" data-check-all="0" data-custom="undefined">
                                                                    <span>@(item.Name)</span>
                                                                </div>
                                                            }
                                                        }

                                                        @*  <div class="item" data-name="<EMAIL>" data-value="20" data-type="customer" data-table="undefined" data-top="0" data-all="1" data-check-all="0" data-custom="undefined">
                                                        <span><EMAIL></span>
                                                        </div>
                                                        <div class="item" data-name="<EMAIL>" data-value="19" data-type="customer" data-table="undefined" data-top="0" data-all="1" data-check-all="0" data-custom="undefined">
                                                        <span><EMAIL></span>
                                                        </div>
                                                        <div class="item" data-name="<EMAIL>" data-value="18" data-type="customer" data-table="undefined" data-top="0" data-all="1" data-check-all="0" data-custom="undefined">
                                                        <span><EMAIL></span>
                                                        </div>
                                                        <div class="item" data-name="<EMAIL>" data-value="16" data-type="customer" data-table="undefined" data-top="0" data-all="1" data-check-all="0" data-custom="undefined">
                                                        <span><EMAIL></span>
                                                        </div>
                                                        <div class="item" data-name="<EMAIL>" data-value="15" data-type="customer" data-table="undefined" data-top="0" data-all="1" data-check-all="0" data-custom="undefined">
                                                        <span><EMAIL></span>
                                                        </div><div class="item" data-name="<EMAIL>" data-value="14" data-type="customer" data-table="undefined" data-top="0" data-all="1" data-check-all="0" data-custom="undefined">
                                                        <span><EMAIL></span>
                                                        </div>
                                                        <div class="item" data-name="<EMAIL>" data-value="13" data-type="customer" data-table="undefined" data-top="0" data-all="1" data-check-all="0" data-custom="undefined">
                                                        <span><EMAIL></span>
                                                        </div> *@

                                                    </div><a href="javascript:;" class="btn_load_more" data-value="customer" data-type="customer" data-table="customer" data-top="0" data-all="1" data-check-all="0" data-start="1" style="display:none;">加载更多</a>
                                                </div>
                                            </dd>
                                        </dl>

                                    </label>
                                </div>
                                <div class="box"></div>
                            </div>
                        </div>
                        <div class="box_customer_address">
                            <div class="address_head"><strong>配送地址</strong><a href="javascript:;" class="btn_global btn_select_address">选择配送地址</a></div>
                            <div class="address_content">
                                <div class="rows  hide order-1 col-2" data-field="firstName" style="display: block;">
                                    <div class="box">
                                        <label class="input_box">
                                            <span class="input_box_label labels_text">名</span>
                                            <input type="text" autocomplete="new-password" class="box_input submit_param" name="FirstName" placeholder="名" maxlength="100" notnull="notnull">
                                        </label>
                                        <p class="error"></p>
                                    </div>
                                </div>
                                <div class="rows  hide order-2 col-2" data-field="lastName" style="display: block;">
                                    <div class="box">
                                        <label class="input_box">
                                            <span class="input_box_label labels_text">姓</span>
                                            <input type="text" autocomplete="new-password" class="box_input submit_param" name="LastName" placeholder="姓" maxlength="100" notnull="notnull">
                                        </label>
                                        <p class="error"></p>
                                    </div>
                                </div>
                                <div class="rows  hide order-4" data-field="address1" style="display: block;">
                                    <div class="input clean">
                                        <label class="input_box">
                                            <span class="input_box_label labels_text">地址</span>
                                            <input type="text" autocomplete="new-password" class="box_input submit_param" name="AddressLine1" placeholder="地址" maxlength="200" notnull="notnull">
                                        </label>
                                        <p class="error"></p>
                                    </div>
                                </div>
                                <div class="rows  hide order-5" data-field="address2" style="display: block;">
                                    <div class="input clean">
                                        <label class="input_box">
                                            <span class="input_box_label labels_text">公寓、套房等 （可选的）</span>
                                            <input type="text" autocomplete="new-password" class="box_input submit_param" name="AddressLine2" placeholder="公寓、套房等" maxlength="200">
                                        </label>
                                        <p class="error"></p>
                                    </div>
                                </div>
                                <div class="rows  hide order-6" data-field="city" style="display: block;">
                                    <div class="box">
                                        <label class="input_box">
                                            <span class="input_box_label labels_text">城市</span>
                                            <input type="text" autocomplete="new-password" class="box_input submit_param" name="City" placeholder="城市" maxlength="30" notnull="notnull">
                                        </label>
                                        <p class="error"></p>
                                    </div>
                                </div>
                                <div class="rows  hide order-7 col-3" data-field="country" style="display: block;">
                                    <div class="box">
                                        <label class="input_box">
                                            <span class="input_box_label labels_text">国家/地区</span>
                                            <dl class="box_drop_double edit_box" data-checkbox="0" data-showadd="0">
                                                <dt>
                                                    <input type="text" class="box_input" name="country_id_input" placeholder="请选择或填写" value="" autocomplete="off" notnull="">
                                                    <input type="hidden" name="country_id" value="226" class="hidden_value">
                                                    <input type="hidden" name="country_idType" value="country" class="hidden_type">
                                                </dt>
                                                <dd class="drop_down" style="display: none;">
                                                    <div class="drop_menu" data-type="Select">
                                                        <a href="javascript:;" class="btn_back" data-value="" data-type="" data-table="" data-top="0" data-all="0" style="display:none;">返回</a>
                                                        <div class="drop_skin" style="display: none;"></div>
                                                        <div class="drop_list" data="@(Model.OrderCountryJson)" data-more="none">

                                                            @if (Model.OrderCountryResponseList != null)
                                                            {
                                                                foreach (var item in Model.OrderCountryResponseList)
                                                                {
                                                                    <div class="item" data-name="@(item.Name)" data-value="@(item.Value)" data-type="country" data-table="undefined" data-top="0" data-all="1" data-check-all="0" data-custom="undefined">
                                                                        <span class="input_radio_box">
                                                                            <span class="input_radio">
                                                                                <input type="radio" name="_DoubleOption[]" value="0">
                                                                            </span>
                                                                        </span>
                                                                        <span class="item_name">@(item.Name)</span>
                                                                    </div>
                                                                }
                                                            }


                                                           @*  <div class="item" data-name="Afghanistan" data-value="1" data-type="country" data-table="undefined" data-top="0" data-all="1" data-check-all="0" data-custom="undefined">
                                                                <span class="input_radio_box">
                                                                    <span class="input_radio">
                                                                        <input type="radio" name="_DoubleOption[]" value="0">
                                                                    </span>
                                                                </span>
                                                                <span class="item_name">Afghanistan</span>
                                                            </div> *@


                                                        </div>
                                                        <a href="javascript:;" class="btn_load_more" data-value="" data-type="" data-table="" data-top="0" data-all="0" data-check-all="" data-start="0" style="display:none;">加载更多</a>
                                                    </div>
                                                </dd>
                                            </dl>
                                        </label>
                                    </div>
                                </div>
                                <div class="rows  hide order-8 col-3" data-field="province" style="display: block;">
                                    <div class="box">
                                        <label class="input_box clean">
                                            <span class="input_box_label labels_text zindex">州</span>
                                            <div class="province_choose input_box box_select">
                                                <select name="Province" placeholder="州" class="chzn-done submit_param labels_placeholder" notnull="notnull">
                                                    <option value="126" data-tax="0.00000">Alabama</option>
                                                    <option value="127" data-tax="0.00000">Alaska</option>
                                                    <option value="128" data-tax="0.00000">Arizona</option>
                                                    <option value="129" data-tax="0.00000">Arkansas</option>
                                                    <option value="136" data-tax="0.00000">California</option>
                                                    <option value="137" data-tax="0.00000">Colorado</option>
                                                    <option value="138" data-tax="0.00000">Connecticut</option>
                                                    <option value="139" data-tax="0.00000">Delaware</option>
                                                    <option value="141" data-tax="0.00000">Florida</option>
                                                    <option value="142" data-tax="0.00000">Georgia</option>
                                                    <option value="143" data-tax="0.00000">Hawaii</option>
                                                    <option value="144" data-tax="0.00000">Idaho</option>
                                                    <option value="145" data-tax="0.00000">Illinois</option>
                                                    <option value="146" data-tax="0.00000">Indiana</option>
                                                    <option value="147" data-tax="0.00000">Iowa</option>
                                                    <option value="148" data-tax="0.00000">Kansas</option>
                                                    <option value="149" data-tax="0.00000">Kentucky</option>
                                                    <option value="150" data-tax="0.00000">Louisiana</option>
                                                    <option value="151" data-tax="0.00000">Maine</option>
                                                    <option value="152" data-tax="0.00000">Maryland</option>
                                                    <option value="153" data-tax="0.00000">Massachusetts</option>
                                                    <option value="154" data-tax="0.00000">Michigan</option>
                                                    <option value="155" data-tax="0.00000">Minnesota</option>
                                                    <option value="156" data-tax="0.00000">Mississippi</option>
                                                    <option value="157" data-tax="0.00000">Missouri</option>
                                                    <option value="158" data-tax="0.00000">Montana</option>
                                                    <option value="159" data-tax="0.00000">Nebraska</option>
                                                    <option value="160" data-tax="0.00000">Nevada</option>
                                                    <option value="161" data-tax="0.00000">New Hampshire</option>
                                                    <option value="162" data-tax="0.00000">New Jersey</option>
                                                    <option value="163" data-tax="0.00000">New Mexico</option>
                                                    <option value="164" data-tax="0.00000">New York</option>
                                                    <option value="165" data-tax="0.00000">North Carolina</option>
                                                    <option value="166" data-tax="0.00000">North Dakota</option>
                                                    <option value="167" data-tax="0.00000">Ohio</option>
                                                    <option value="168" data-tax="0.00000">Oklahoma</option>
                                                    <option value="169" data-tax="0.00000">Oregon</option>
                                                    <option value="170" data-tax="0.00000">Pennsylvania</option>
                                                    <option value="172" data-tax="0.00000">South Carolina</option>
                                                    <option value="173" data-tax="0.00000">South Dakota</option>
                                                    <option value="174" data-tax="0.00000">Tennessee</option>
                                                    <option value="175" data-tax="0.00000">Texas</option>
                                                    <option value="176" data-tax="0.00000">Utah</option>
                                                    <option value="177" data-tax="0.00000">Vermont</option>
                                                    <option value="178" data-tax="0.00000">Virginia</option>
                                                    <option value="179" data-tax="0.00000">Washington</option>
                                                    <option value="180" data-tax="0.00000">West Virginia</option>
                                                    <option value="181" data-tax="0.00000">Wisconsin</option>
                                                    <option value="182" data-tax="0.00000">Wyoming</option>
                                                    <option value="202" data-tax="0.00000">District of Columbia</option>
                                                    <option value="203" data-tax="0.00000">Puerto Rico</option>
                                                    <option value="204" data-tax="0.00000">Rhode Island</option>
                                                    <option value="1030" data-tax="0.00000">American Samoa</option>
                                                    <option value="1031" data-tax="0.00000">Micronesia</option>
                                                    <option value="1032" data-tax="0.00000">Guam</option>
                                                    <option value="1033" data-tax="0.00000">Marshall Islands</option>
                                                    <option value="1034" data-tax="0.00000">Northern Mariana Islands</option>
                                                    <option value="1035" data-tax="0.00000">Palau</option>
                                                    <option value="1036" data-tax="0.00000">U.S. Virgin Islands</option>
                                                    <option value="1037" data-tax="0.00000">Armed Forces Americas</option>
                                                    <option value="1038" data-tax="0.00000">Armed Forces Europe</option>
                                                    <option value="1039" data-tax="0.00000">Armed Forces Pacific</option>
                                                </select>
                                            </div>
                                            <p class="error"></p>
                                        </label>
                                    </div>
                                </div>
                                <div class="rows  hide order-9 col-3" data-field="zip" style="display: block;">
                                    <div class="input clean">
                                        <label class="input_box">
                                            <span class="input_box_label labels_text">邮政编码</span>
                                            <input type="text" autocomplete="new-password" class="box_input input_box_txt submit_param labels_placeholder" name="ZipCode" placeholder="邮政编码" maxlength="20" notnull="notnull">
                                        </label>
                                        <p class="error" style="display: none;"></p>
                                    </div>
                                </div>
                                <div class="rows  hide order-10" data-field="phone" style="display: block;">
                                    <div class="input clean">
                                        <label class="input_box">
                                            <span class="input_box_label labels_text">电话</span>
                                            <div class="box_input_group clearfix">
                                                <input name="CountryCode" type="text" value="+0000" class="country_code box_input input_group_addon" readonly="">
                                                <input type="text" name="PhoneNumber" placeholder="电话" class="box_input input_box_txt submit_param labels_placeholder input_group" notnull="notnull">
                                            </div>
                                        </label>
                                        <p class="error"></p>
                                    </div>
                                </div>
                                <div class="tax_code rows hide" data-field="taxId" style="display: none;">
                                    <div class="blank10"></div>
                                    <span class="input_box_label labels_text">税号</span>
                                    <div class="blank6"></div>
                                    <div class="input_box form_box clean">
                                        <div class="tax_code_option_box box" style="display: none;">
                                            <div class="box_select">
                                                <select name="tax_code_type" class="tax_code_option addr_select"></select>
                                            </div>
                                        </div>
                                        <div class="box">
                                            <label class="input_box">
                                                <input type="text" name="tax_code_value" placeholder="税号" maxlength="14" class="box_input tax_code_value input_box_txt submit_param labels_placeholder" disabled="disabled">
                                            </label>
                                            <p class="error"></p>
                                        </div>
                                    </div>
                                </div>
                                <div class="box_additional_html hide">
                                    <div class="rows" data-field="{{Name}}-{{Id}}" style="display: none;">
                                        <div class="input clean">
                                            <label class="input_box">
                                                <span class="input_box_label labels_text">{{Name}}</span>
                                                <input type="text" class="box_input input_box_txt submit_param labels_placeholder" name="AdditionalInfoData[{{Id}}]" placeholder="{{Name}}" value="{{Value}}" disabled="disabled">
                                            </label>
                                            <p class="error"></p>
                                        </div>
                                    </div>
                                </div>
                                <div class="rows_address_save">
                                    <span class="input_checkbox_box">
                                        <span class="input_checkbox">
                                        <input type="checkbox" name="SaveAddress" value="1">
                                        </span> 保存到地址薄</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <input type="hidden" name="AddressId" value="0">
                <input type="hidden" name="do_action" value="/api/OrderList/OrdersEnterThird">
            </form>
        </div>
        <div id="fixed_right">
            <div class="global_container fixed_select_address" data-width="440">
                <div class="top_title"><span>选择配送地址</span> <a href="javascript:;" class="close"></a></div>
                <div class="rows clean box_select_address">
                    <div class="input"></div>
                </div>
                <div class="no_data"><div class="bg_no_table_data" style="height: 464px;"><div class="content" style="top: 192px;"><p>当前暂时没有数据</p></div><span></span></div></div>
                <div class="rows clean box_button box_submit">
                    <div class="input">
                        <input type="button" class="btn_global btn_submit" value="保存">
                        <input type="button" class="btn_global btn_cancel" value="取消">
                    </div>
                </div>
            </div>
        </div>
    </div>		<div class="rows clean second_btn_submit fixed_btn_submit" style="width: 2096px; left: 180px;">
        <div class="center_container_1000">
            <div class="input">
                <input type="button" class="btn_global btn_submit" value="下一步">
                <a href="/Orders/Create" class="fl"><input type="button" class="btn_global btn_cancel" value="上一步"></a>
            </div>
        </div>
    </div>

</div>