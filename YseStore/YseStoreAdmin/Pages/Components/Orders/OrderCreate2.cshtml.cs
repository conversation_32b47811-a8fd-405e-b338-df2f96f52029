using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using YseStore.IService.Customer;
using YseStore.IService.Order;
using YseStore.Service.Order;
using YseStoreAdmin.Pages.Components.Setting;

namespace YseStoreAdmin.Pages.Components.Orders
{

    public class OrderCreate2 : MComponent
    {
        public IOrderListService _orderListService { get; }
        public ICustomerListService _customerListService { get; }
        public List<CarrierResultResponse>? UserBookResultResponseList { get; set; }
        public string UserBookJson { get; set; }
        public List<OrderCountryResponse>? OrderCountryResponseList { get; set; }
        public string OrderCountryJson { get; set; }
        public OrderCreate2(IOrderListService orderListService, ICustomerListService customerListService)
        {
            _orderListService = orderListService;
            _customerListService = customerListService;
        }

        public override async Task MountAsync()
        {
            UserBookResultResponseList = await _orderListService.GetUserBookData();
            UserBookJson = System.Text.Json.JsonSerializer.Serialize(UserBookResultResponseList);
            OrderCountryResponseList = await _orderListService.GetCountryData();
            OrderCountryJson = System.Text.Json.JsonSerializer.Serialize(OrderCountryResponseList);
        }


    }
}
