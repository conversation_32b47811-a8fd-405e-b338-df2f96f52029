@using Newtonsoft.Json.Linq;
@using YseStore.Model.Enums;
@using Newtonsoft.Json;
@using YseStore.Common;
@using Entitys;
@model YseStoreAdmin.Pages.Components.Orders.WayBillListTable
@{
}

<div>



    @if (Model.WayBillReviewList?.Any() == true)
    {
        foreach (var item in Model.WayBillReviewList)
        {
            <div class="global_container">
                <div class="orders_info"><span class="orders_number color_000">No.#@(item.OId)</span></div>
                @{                    
                    // var WIdPackage = item.Orders_Package_List.FirstOrDefault(x => x.ParentId == 0);
                    var OrdersPackageList = item.Orders_Package_List
                    .Where(x => x.ProInfo != "[]" && x.ParentId != 0)
                    .ToList();
                    if (!OrdersPackageList.Any())
                    {
                        OrdersPackageList = item.Orders_Package_List
                        .Where(x => x.ParentId == 0 && x.ProInfo != "[]")
                        .ToList();
                    }
                    var duplicateOvIds = OrdersPackageList
                    .Where(it => it.Status==0)
                    .GroupBy(it => it.OvId)
                    .Where(g => g.Count() >= 2)
                    .Select(g => g.Key)          
                    .ToList();


                }
               
                    @if (OrdersPackageList.Any())
                    {
                        int PackageCount = 1;
                        foreach (var itemOne in OrdersPackageList)
                        {
                            if (!string.IsNullOrEmpty(itemOne.ProInfo) && itemOne.ProInfo != "[]")
                            {
                                var dict = JsonConvert.DeserializeObject<Dictionary<string, int>>(itemOne.ProInfo);
                                List<int> productIds = dict.Keys.Select(int.Parse).ToList();
                                dynamic dataCarrier = new JObject();
                                if (!string.IsNullOrEmpty(itemOne.Carrier) && itemOne.Carrier != null)
                                {
                                    dataCarrier = JObject.Parse(itemOne.Carrier);
                                }

                            <div class="waybill_list" data-ovid="0" data-orderid="@(item.OrderId)" data-wid="@(itemOne.WId)" data-virtual="0">
                                <div class="waybill_title clean">
                                    <div class="package_name color_000">包裹@(PackageCount)</div>
                                    <div class="package_view">
                                        <span class="package_view_item">包裹ID: @(itemOne.WId)</span>
                                        @if (!string.IsNullOrEmpty(itemOne.Warehouse))
                                        {
                                            <span class="package_view_item">发货地: @(itemOne.Warehouse)</span>
                                        }
                                       
                                        <span class="package_view_item">配送方式：@(itemOne.ShippingExpress)</span>
                                    </div>

                                    <div class="package_button" data-apitype="0">
                                        @if (!Convert.ToBoolean(itemOne.Status))
                                        {
                                            <input type="button" class="btn_global btn_delivery" value="发货">
                                        }

                                        @if (OrdersPackageList.Count > 1 && !Convert.ToBoolean(itemOne.Status))
                                        {
                                            if (duplicateOvIds.Contains(itemOne.OvId))
                                            {
                                                <input type="button" class="btn_global btn_merge" value="合单">
                                            }
                                           
                                        }
                                        @if (item.Orders_Products_List != null)
                                        {
                                            var ProductsCount = item.Orders_Products_List.Where(opl => productIds.Contains(opl.LId)).Count();
                                            if (ProductsCount>1)
                                            {
                                                @if (!Convert.ToBoolean(itemOne.Status))
                                                {
                                                    <input type="button" class="btn_global btn_separate" value="拆单">
                                                }
                                               
                                            }
                                        }

                                      
                                    </div>
                                    <a href="/Orders/PrintWaybill?OrderId=@(item.OrderId)&WId=@(itemOne.WId)" target="_blank" class="btn_print">打印</a>
                                    <div class="clean"></div>
                                    <div class="package_track color_888">
                                        @if (!string.IsNullOrEmpty(itemOne.Carrier) && itemOne.Carrier != null)
                                        {
                                            <span> 物流商:<span class="query ">@(dataCarrier.name)</span>&nbsp;&nbsp;&nbsp;&nbsp;</span>

                                        }
                                        @if (Convert.ToBoolean(itemOne.Status))
                                        {
                                       
                                            <span>
                                                运单号:
                                                <span class="query  tracking_number not-enabled-api"
                                                      data-id="@(itemOne.TrackingNumber)" data-oid="@(item.OId)"
                                                      data-carrier="@(dataCarrier.key)" id="@(itemOne.TrackingNumber)">@(itemOne.TrackingNumber)</span>
                                                &nbsp;&nbsp;&nbsp;&nbsp;
                                                发货时间:  @(DateTimeHelper.ConvertUnixTimestampToBeijingTimeWithUtcOffset(Convert.ToInt64(itemOne.ShippingTime), "yyyy-MM-dd"))

                                            </span>
                                        }

                                       
                                    </div>
                                </div>
                                <table border="0" cellpadding="5" cellspacing="0" class="r_con_table">
                                    <thead>
                                        <tr>
                                            <td width="5%" nowrap="nowrap">序号</td>
                                            <td width="50%" nowrap="nowrap">产品</td>
                                            <td width="37%" nowrap="nowrap">属性</td>
                                            <td width="8%" nowrap="nowrap">数量</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @if (item.Orders_Products_List != null)
                                        {
                                            var j = 1;
                                            var ConformProductsLists = item.Orders_Products_List.Where(opl => productIds.Contains(opl.LId)).ToList();
                                            foreach (var itemTwo in ConformProductsLists)
                                            {

                                                <tr>
                                                    <td>@(j)</td>
                                                    <td>
                                                        <dl>
                                                            <dt>
                                                                <a href="" target="_blank" class="pic_box">
                                                                    <img src="@(!string.IsNullOrEmpty(itemTwo.PicPath)?itemTwo.PicPath+"?x-oss-process=image/format,webp/resize,m_lfit,h_120,w_120":"")" title="@itemTwo.Name">

                                                                   @*  <img src="@(itemTwo.PicPath)" title="@(itemTwo.Name)"> *@
                                                                    <span></span>
                                                                </a>
                                                            </dt>
                                                            <dd>
                                                                <div class="order_tag">
                                                                </div>
                                                                <h4>
                                                                    <a href="" title="@(itemTwo.Name)" class="green" target="_blank">
                                                                        @(itemTwo.Name)
                                                                    </a>
                                                                </h4>
                                                            </dd>
                                                        </dl>
                                                    </td>
                                                    <td>
                                                        @if (!string.IsNullOrEmpty(itemTwo.SKU))
                                                        {
                                                            <p>SKU: @(itemTwo.SKU)</p>
                                                        }

                                                        @if (!string.IsNullOrEmpty(itemTwo.Property) && itemTwo.Property != "[]")
                                                        {
                                                            var dictProperty = JsonConvert.DeserializeObject<Dictionary<string, string>>(itemTwo.Property);
                                                            @foreach (var kvp in dictProperty)
                                                            {
                                                                <p>
                                                                    @(kvp.Key):
                                                                    @(kvp.Value)
                                                                </p>
                                                            }

                                                        }
                                                        <div class="custom_attr"></div>
                                                    </td>
                                                    @{
                                                        int value = dict.ContainsKey(itemTwo.LId.ToString()) ? dict[itemTwo.LId.ToString()] : 0;
                                                    }
                                                    @if (value != 0)
                                                    {
                                                        <td class="last">@(value)</td>
                                                    }

                                                </tr>

                                                j++;
                                            }

                                        }
                                    </tbody>
                                </table>
                            </div>
                            }
                            PackageCount++;
                        }
                    }





              

            </div>
        }
    }



@*     <div class="global_container">
        <div class="orders_info"><span class="orders_number color_000">No.#RFR9201341</span></div>
        <div class="waybill_list" data-ovid="0" data-orderid="28" data-wid="27" data-virtual="0">
            <div class="waybill_title clean">
                <div class="package_name color_000">包裹1</div>
                <div class="package_view">
                    <span class="package_view_item">包裹ID: 27</span>
                    <span class="package_view_item">配送方式：Expedited Shipping</span>
                </div>
                <a href="/manage/orders/waybill/print?OrderId=28&amp;WId=27" target="_blank" class="btn_print">打印</a>
                <div class="clean"></div>
                <div class="package_track color_888">
                    物流商:
                    <span class="query ">17EXP</span>
                    &nbsp;&nbsp;&nbsp;&nbsp;
                    运单号:
                    <span class="query  tracking_number not-enabled-api" data-id="CI045503246DE" data-oid="RFR9201341" data-carrier="{&quot;17track&quot;:190659}" id="CI045503246DE">CI045503246DE</span>
                    &nbsp;&nbsp;&nbsp;&nbsp;
                    发货时间: 2025-02-19
                </div>
            </div>
            <table border="0" cellpadding="5" cellspacing="0" class="r_con_table">
                <thead>
                    <tr>
                        <td width="5%" nowrap="nowrap">序号</td>
                        <td width="50%" nowrap="nowrap">产品</td>
                        <td width="37%" nowrap="nowrap">属性</td>
                        <td width="8%" nowrap="nowrap">数量</td>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>1</td>
                        <td>
                            <dl>
                                <dt><a href="http://retevis.fr/products/retevis-nr30d-aes256-radio-professionnelle-dmr-étanche-longue-portée-avec-réduction-du-bruit" target="_blank" class="pic_box"><img src="/u_file/2501/22/products/d021c2f1c7.jpg" title="Retevis NR30D AES256 Radio professionnelle DMR étanche longue portée avec réduction du bruit"><span></span></a></dt>
                                <dd>
                                    <div class="order_tag">
                                    </div>
                                    <h4><a href="http://retevis.fr/products/retevis-nr30d-aes256-radio-professionnelle-dmr-étanche-longue-portée-avec-réduction-du-bruit" title="Retevis NR30D AES256 Radio professionnelle DMR étanche longue portée avec réduction du bruit" class="green" target="_blank">Retevis NR30D AES256 Radio professionnelle DMR étanche longue portée avec réduction du bruit</a></h4>
                                </dd>
                            </dl>
                        </td>
                        <td>
                            <p>SKU: A9299A-C9034A-J9131P</p><div class="custom_attr"></div>
                        </td>
                        <td class="last">1</td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div> *@


</div>

