using Aop.Api.Domain;
using Entitys;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using YseStore.Common.Helper;
using YseStore.IService;
using YseStore.IService.Customer;
using YseStore.IService.Order;
using YseStore.Model.RequestModels.Set;
using YseStoreAdmin.Pages.Components.Setting;
using YseStoreAdmin.Pages.Setting;
using ZXing.QrCode.Internal;

namespace YseStoreAdmin.Pages.Components.Orders
{
    public class Print : MComponent
    {
        public IOrderListService _orderListService { get; }
        public ICustomerListService _customerListService { get; }
        public IHelpsCartService _helpsCartService { get; }
        public UserResponse UserModel { get; set; }
        public orders OrderDetailModel { get; set; }
        public string iframe { get; set; }
        public string printType { get; set; }
        public string currency { get; set; }
        public string printconfig { get; set; }


        public string barcode { get; set; } = "";
        public string orderPrintLogo { get; set; } = "";
        public string siteName { get; set; } = "";
        public string Compeny { get; set; } = "";
        public string Address { get; set; } = "";
        public string AdminEmail { get; set; } = "";
        public string Telephone { get; set; } = "";
        public List<orders_package>? OrderPackage { get; set; } = new List<orders_package>();
        public List<orders_products_list>? OrderProductsLists { get; set; } = new List<orders_products_list>();
        public int ProductCount { get; set; } = 0;
        public decimal TotalProductPrice { get; set; } = 0;
        public decimal points { get; set; } = 0;
        public decimal shippingFee { get; set; } = 0;
        public decimal commission { get; set; } = 0;
        public decimal OrderSum { get; set; } = 0;
        public string OrderSymbol { get; set; } = "";
        public string paymentMethod { get; set; }= "";
        public orders_refund_info OrderRefund { get; set; } = new orders_refund_info();
        public decimal OrderRefundAmount { get; set; } = 0;
        public int OrdersCount { get; set; } = 0;
        public int PaymentCount { get; set; } = 0;
        public decimal paymentPrice = 0m;

        public decimal ManagerefundAmount = 0m;
        public decimal refundAmountTwo = 0m;

        public List<PrintListResponse> PrintDataList { get; set; }= new List<PrintListResponse>();
        public Print(IOrderListService orderListService, ICustomerListService customerListService, IHelpsCartService helpsCartService)
        {
            _orderListService = orderListService;
            _customerListService = customerListService;
            _helpsCartService = helpsCartService;
        }
        public override async Task MountAsync()
        {
            iframe = HttpContext.Request.Query["iframe"].ToString();
            printType = HttpContext.Request.Query["printType"].ToString();
            var id = HttpContext.Request.Query["id"].ToString();
            currency = HttpContext.Request.Query["currency"].ToString();
            printconfig = HttpContext.Request.Query["printconfig"].ToString();//��ѡ�ֶ�

            List<int> ids = new List<int>();
            if (!string.IsNullOrWhiteSpace(id))
            {
                ids = id.Split('-').Select(int.Parse).ToList();
            }
           
            var orderid = 0;
            if (ids != null && ids.Count > 0)
            {
               
                foreach (var item in ids)
                {
                    orderid = item;

                    OrderDetailModel = await _orderListService.GetOrderDetailByOrderIdAsync(orderid);
                    barcode = new BarcodeGeneratorHelper().GenerateBarcode(Convert.ToString(OrderDetailModel.OId));
                    orderPrintLogo = await _orderListService.GetorderPrintLogo();
                    siteName = await _orderListService.GetSiteName();
                    Compeny = await _orderListService.GetCompeny();
                    Address = await _orderListService.GetAddress();
                    AdminEmail = await _orderListService.GetAdminEmail();
                    Telephone = await _orderListService.GetTelephone();

                    OrderPackage = await _orderListService.GetPackageAsync(orderid);
                    OrderProductsLists = await _orderListService.GetProducts_Lists(orderid);
                    var orders_Refund_Infos = await _orderListService.GetOrderRefundByOrderIdList(orderid);
                    if (orders_Refund_Infos != null && orders_Refund_Infos.Count > 0)
                    {
                        foreach (var itemRefund in orders_Refund_Infos)
                        {
                            var refundAmount = _helpsCartService.CurrencyFloatPrice(_helpsCartService.CeilPrice(itemRefund.Amount / OrderDetailModel.Rate), OrderDetailModel.ManageCurrency);
                            ManagerefundAmount += _helpsCartService.CurrencyFormat(refundAmount, 0, OrderDetailModel.ManageCurrency);
                            refundAmountTwo += itemRefund.Amount;


                        }
                    }
                    if (currency=="ManageCurrency")
                    {
                        var OrdersAmount = await _orderListService.GetOrdersAmount(orderid);
                        ProductCount = OrdersAmount.Item1;
                        TotalProductPrice = OrdersAmount.Item2;
                        points = OrdersAmount.Item3;
                        shippingFee = OrdersAmount.Item4;
                        commission = OrdersAmount.Item5;
                        OrderSum = OrdersAmount.Item6;
                        OrderSymbol = OrdersAmount.Item7;
                        paymentMethod = OrdersAmount.Item8;
                        //ʵ�ʸ���
                        paymentPrice = await _orderListService.actualPayment(OrderDetailModel);
                        paymentPrice = _helpsCartService.CurrencyFloatPrice(paymentPrice, OrderDetailModel.ManageCurrency);
                        //OrderRefund = await _orderListService.GetOrderRefundByOrderIdAsync(orderid);
                        //OrderRefundAmount = OrderRefund?.Amount ?? 0;
                        //��Ʒ���� - ��Ʒ�۸� - ���ּ۸� - �˷� - ������ - �����ܼ۸� - ���ҷ��� - ֧����ʽ
                        PrintDataList.Add(new PrintListResponse()
                        {
                            OrderDetailModel = OrderDetailModel,
                            barcode = barcode,
                            orderPrintLogo = orderPrintLogo,
                            siteName = siteName,
                            Compeny = Compeny,
                            Address = Address,
                            AdminEmail = AdminEmail,
                            Telephone = Telephone,
                            OrderPackage = OrderPackage,
                            OrderProductsLists = OrderProductsLists,

                            ProductCount = ProductCount,
                            TotalProductPrice = TotalProductPrice,
                            points = points,
                            shippingFee = shippingFee,
                            commission = commission,
                            OrderSum = paymentPrice,
                            OrderSymbol = OrderSymbol,
                            paymentMethod = paymentMethod,
                            DiscountPrice = OrdersAmount.Item9,
                            CouponPrice= OrdersAmount.Item10,
                            refundedTotalPrice= ManagerefundAmount


                        });

                    }
                    else
                    {

                        var replaceData = new ReplaceDataModel
                        {
                            shippingPrice = -1
                        };
                        /// <param name="data">��������</param>
                        /// <param name="method">��ʾ��ʽ 0 ����+�۸�1 �۸�</param>
                        /// <param name="isManage">��̨���� 0 ������ 1 ����</param>
                        /// <param name="isRefund">�Ƿ�����ȥ�˿��� true-���� false-������</param>
                        /// <param name="isDelete">�Ƿ�����ȥ��ɾ����� true-���� false-������</param>
                        /// <param name="replaceData">�滻���� �ɴ���:�� shippingPrice �˷ѡ� lineItems ��Ʒ����</param>
                        var priceData = await _orderListService.OrdersDetailPrice(OrderDetailModel, 1, 0, false, true, replaceData);
                        /**
                         * �������ּ۸��ʽ��ʾ
                         * @return array				�������ּ۸�
                         * 								�� ProductPrice ��Ʒ�۸�
                         * 								�� DiscountPrice �����ۿ۽��
                         * 								�� CouponPrice �Ż�ȯ�ۿ۽��
                         * 								�� PointsPrice ����
                         * 								�� ShippingPrice �˷�
                         * 								�� FeePrice ������
                         * 								�� TaxPrice ˰��
                         * 								�� TotalPrice �����ܼ۸�
                         */
                        //ʵ�ʸ�����
                        //var paymentPrice = await _orderListService.actualPayment(OrderDetailModel, 0);


                        PrintDataList.Add(new PrintListResponse()
                        {
                            OrderDetailModel = OrderDetailModel,
                            barcode = barcode,
                            orderPrintLogo = orderPrintLogo,
                            siteName = siteName,
                            Compeny = Compeny,
                            Address = Address,
                            AdminEmail = AdminEmail,
                            Telephone = Telephone,
                            OrderPackage = OrderPackage,
                            OrderProductsLists = OrderProductsLists,

                            ProductCount = ProductCount,
                            TotalProductPrice = priceData.ProductPrice,
                            points = priceData.PointsPrice,
                            shippingFee = priceData.ShippingPrice,
                            commission = priceData.FeePrice,
                            OrderSum = _helpsCartService.CurrencyFloatPrice(priceData.TotalPrice - refundAmountTwo, OrderDetailModel.Currency) ,
                            OrderSymbol = await _orderListService.getCurrencySymbol(OrderDetailModel.Currency),
                            paymentMethod = paymentMethod,
                            DiscountPrice = priceData.DiscountPrice,
                            CouponPrice = priceData.CouponPrice,
                            refundedTotalPrice = refundAmountTwo

                        });
                    }
                    


                }
            }


            //var orderid = Convert.ToInt32(id);
            var userid = await _orderListService.GetUserIdByOrderIdAsync(orderid);
            UserModel = await _customerListService.GetUserDetailAsync(userid);









            var OrdersPayment = await _orderListService.GetOrdersPayment(userid);
            OrdersCount = OrdersPayment.Item1;
            PaymentCount = OrdersPayment.Item2;


        }






    }
}
