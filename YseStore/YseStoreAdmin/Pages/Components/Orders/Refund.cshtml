@using Newtonsoft.Json.Linq;
@using YseStore.Model.Enums;
@using Newtonsoft.Json;
@using YseStore.Common;
@using Entitys;
@using YseStore.IService;
@inject IHelpsCartService _helpsCartService
@model YseStoreAdmin.Pages.Components.Orders.Refund
@{
}
<div>

    <div id="orders_inside" class="r_con_wrap" style="height: 430px;">
        <div class="center_container_1200 orders_refund">
            <div class="return_title">
                <a href="/Orders/Detail?id=@(Model.OrderDetailModel.OrderId)&query_string=">
                    <span class="return">退款</span>
                    <span class="s_return">/ NO.#@(Model.OrderDetailModel.OId)</span>
                </a>
            </div>
            <div class="left_container">
                <div class="left_container_side">
                    <form class="global_form" id="edit_form">
                        @if (Model.OrderPackage != null)
                        {

                            List<orders_package> OrdersPackageList = new List<orders_package>();
                            OrdersPackageList = Model.OrderPackage.Where(x => x.ParentId != 0 && x.ProInfo != "[]").ToList();
                            if (OrdersPackageList.Count == 0)
                            {
                                OrdersPackageList = Model.OrderPackage.Where(x => x.ParentId == 0 && x.ProInfo != "[]").ToList();
                            }

                            int PackageCount = 1;
                            foreach (var item in OrdersPackageList)
                            {
                                if (!string.IsNullOrEmpty(item.ProInfo) && item.ProInfo != "[]")
                                {
                                    <div class="global_container products_box">
                                        @{
                                            var dict = JsonConvert.DeserializeObject<Dictionary<string, int>>(item.ProInfo);
                                            List<int> productIds = dict.Keys.Select(int.Parse).ToList();
                                        }
                                        @if (Convert.ToBoolean(item.Status))
                                        {
                                            <div class="big_title">
                                                已发货包裹@(PackageCount)
                                            </div>
                                        }
                                        else
                                        {
                                            <div class="big_title">
                                                未发货包裹@(PackageCount)
                                            </div>
                                            <div class="box_explain">已退款的商品将从订单中删除</div>
                                        }
                                        
                                        
                                        <table border="0" cellpadding="5" cellspacing="0" class="r_con_table orders_products_list"
                                               data-status="@(Convert.ToBoolean(item.Status) ? "Shipped" : "UnShipped")" data-id="@(item.WId)">
                                            <thead>
                                                <tr>
                                                    <td nowrap="nowrap">产品</td>
                                                    <td nowrap="nowrap">单价</td>
                                                    <td nowrap="nowrap">数量</td>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                @if (Model.OrderProductsLists != null)
                                                {
                                                    var j = 1;
                                                    var ConformProductsLists = Model.OrderProductsLists.Where(opl => productIds.Contains(opl.LId)).ToList();
                                                    foreach (var items in ConformProductsLists)
                                                    {
                                                     
                                                        <tr>
                                                            <td>
                                                                <dl>
                                                                    <dt>
                                                                        <a href="" target="_blank">
                                                                            <img src="@(!string.IsNullOrEmpty(items.PicPath)?items.PicPath+"?x-oss-process=image/format,webp/resize,m_lfit,h_120,w_120":"")"
                                                                                 title="@items.Name">
                                                                        </a>
                                                                    </dt>
                                                                    <dd>
                                                                        <h4>
                                                                            <a href="" title="@items.Name"
                                                                               class="green" target="_blank">@items.Name</a>
                                                                        </h4>
                                                                        <p> SKU : @(items.SKU)</p>
                                                                        @if (!string.IsNullOrEmpty(items.Property) && items.Property != "[]")
                                                                        {
                                                                            var dictProperty = JsonConvert.DeserializeObject<Dictionary<string, string>>(items.Property);
                                                                            @foreach (var kvp in dictProperty)
                                                                            {
                                                                                <p> @(kvp.Key):  @(kvp.Value)</p>
                                                                            }
                                                                        }
                                                                        <div class="custom_attr"></div>
                                                                    </dd>
                                                                </dl>
                                                            </td>
                                                            <td data-type="price">
                                                                <div class="show_txt">
                                                                    
                                                                    @if (Model.OrderDetailModel.Currency != Model.OrderDetailModel.ManageCurrency)
                                                                    {
                                                                        @(Model.OrderSymbol)@(await _helpsCartService.IconvPriceFormat(items.Price.Value, 2, Model.OrderDetailModel.Currency, Model.OrderDetailModel.Rate))
                                                                    }else
                                                                    {
                                                                       @(Model.OrderSymbol)@(items.Price)
                                                                    }
                                                                    </div>
                                                            </td>
                                                            <td data-type="qty">
                                                                <span class="show_input">
                                                                    <input type="text" class="box_input" name="Qty[]" value="0" size="4"
                                                                           maxlength="7" rel="int" data-id="@(items.LId)" data-max="2"><input type="hidden" name="LId[]" value="9">
                                                                    @{
                                                                        int value = dict.ContainsKey(items.LId.ToString()) ? dict[items.LId.ToString()] : 0;
                                                                    }
                                                                    <input type="hidden" name="LId[]" value="@(items.LId)">
                                                                    <span class="show_txt">&nbsp;&nbsp;/&nbsp;&nbsp;@(value)</span>
                                                                </span>
                                                            </td>
                                                        </tr>


                                                        j++;
                                                    }
                                                }



                                               
                                            </tbody>
                                        </table>
                                        <div class="restock_box">
                                            <span class="input_checkbox_box checked">
                                                <span class="input_checkbox">
                                                    <input type="checkbox" name="Restock[]" value="@(Convert.ToBoolean(item.Status)?"Shipped":"UnShipped")"
                                                           checked="">
                                                </span>产品重新入库
                                            </span>
                                        </div>
                                    </div>


                                    PackageCount++;
                                }

                            }
                        }


                       



                        <div class="global_container shipping_price_box">
                            <div class="big_title">退还运费</div>
                            <div class="shipping_price_txt"><b>运费：</b>@(string.Join("/", Model.OrderPackage.Select(x => x.ShippingExpress))) 
                                (@(Model.OrderSymbol)@(Model.shippingFeeTwo))</div>
                            <div class="rows clean shipping_price_input">
                                <label></label>
                                <div class="input">
                                    <div>
                                        <span class="unit_input">
                                            <b>@(Model.OrderDetailModel.Currency) @(Model.OrderSymbol)</b>
                                            <input type="text"
                                                               class="box_input left_radius" name="refundShipping" value="0.00"
                                                               size="46" maxlength="24" placeholder="0" rel="amount"
                                                                data-currency-code="@(Model.OrderDetailModel.Currency)" 
                                                                data-max="@(Model.shippingFeeTwo)">
                                        </span>
                                    </div>
                                    <span class="tips_txt">已退回 @(Model.OrderSymbol)@(Model.refundedShippingAmount)。</span>
                                </div>
                            </div>
                        </div>
                        <div class="global_container reason_box">
                            <div class="big_title">退款原因<span class="box_explain">（仅后台可见）</span></div>
                            <div class="rows clean reason_input">
                                <div class="input">
                                    <input type="text" class="box_input" name="refundReason" value=""
                                           data-required="0">
                                </div>
                            </div>

                            <input type="hidden" name="OrderId" value="@(Model.OrderDetailModel.OrderId)">
                        </div>
                    </form>
                </div>
            </div>
            <div class="right_container">
                <div class="global_container">
                    <div class="big_title">退款明细</div>
                    <ul class="list subtotal">
                        <li class="clean no_data_tips">未选择任何产品。</li>
                        <li class="clean price_show" style="display: none;">
                            <strong>产品总价:</strong>
                            <span class="order_product_price">@(Model.OrderSymbol)@(Model.TotalProductPrice)</span>
                        </li>
                        <li class="clean price_show" style="display: none;">
                            <div class="pro_count"></div>
                        </li>
                        <li class="clean price_show" style="display: none;">
                            <strong>运费:</strong>
                            <span class="order_shipping_price">@(Model.OrderSymbol)@(Model.shippingFeeTwo)</span>
                        </li>
                        <li class="clean price_show" style="display: none;">
                            <strong>税费:</strong>
                            <span>
                                <span class="order_tax_price">$0.00</span>&nbsp;&nbsp;<span id="order_tax">
                                    (0%)
                                </span>
                            </span>
                        </li>
                        <li class="clean price_show" style="display: none;">
                            <strong>折扣:</strong>
                            <span class="order_discount_price">-$0.00</span>
                        </li>
                        <li class="clean price_show box_points_price" style="display:none;">
                            <strong>积分:</strong>
                            <span class="order_points_price">-@(Model.OrderSymbol)@(Model.points)</span>
                        </li>
                        <li class="clean price_show box_price_fee" style="display:none;">
                            <strong>手续费:</strong>
                            <span class="order_handing_fee">@(Model.OrderSymbol)@(Model.commission)</span>
                        </li>

                        <li class="clean price_show" style="display: none;">
                            <strong>优惠券:</strong>
                            <span class="order_coupon">-0%</span>
                        </li>
                        <li class="clean price_show total_amount" style="display: none;">
                            <strong>订单总额</strong>
                            <span class="order_total_price">@(Model.OrderSymbol)@(Model.OrderSum)</span>
                        </li>
                        <li class="clean method_box">
                            <div class="rows clean">
                                <label>退款方式</label>
                                <div class="input">
                                    <div class="box_type_menu fl">
                                        <span class="item checked">
                                            <input type="radio" name="RefundType" checked
                                                   value="original">原路退回
                                        </span>
                                        <span class="item">
                                            <input type="radio" name="RefundType"
                                                   value="offline">线下退回
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </li>
                        <li class="clean return_method">
                            <div class="blank20"></div>
                            <label>
                                <span class="online" style="display: none;">原路退回方式</span> 
                                <span class="offline"  style="display: inline;">线下返回方式(选填)</span>
                            </label>
                            <div class="input">
                                <div class="refundBox">
                                    <div class="box tips" data-method="original" style="display: none;">
                                        
                                        @(Model.OrderDetailModel.PaymentMethod)
                                        @if (!string.IsNullOrEmpty(Model.originalTips))
                                        {
                                            <div class="global_app_tips obvious">
                                                <em></em>
                                                @(Model.originalTips)
                                            </div>
                                        }
                                    </div>
                                    <div class="blank6"></div>
                                    <div class="box" data-method="offline" style="display: block;">
                                        <input type="text" class="box_input left_radius full_input" name="PaymentMethod"
                                               value="" maxlength="100" placeholder="@(Model.paymentMethod)" rel="amount"
                                               data-currency-code="@(Model.OrderDetailModel.Currency)">
                                    </div>
                                </div>
                            </div>
                        </li>
                        <li class="clean amount_box  @(Model.isRefund?"":"just_show")" style="display: block;">
                            <div class="amount_title"><span>退款金额</span></div>
                            <div class="rows clean amount_input">
                                <div class="input">
                                    <div>
                                        <span class="unit_input">
                                            <b>@(Model.OrderDetailModel.Currency) @(Model.OrderSymbol)</b>
                                            <input type="text" class="box_input left_radius" name="refundAmount" value="0.00"
                                                               maxlength="24" placeholder="0" rel="amount" data-currency-code="@(Model.OrderDetailModel.Currency)"
                                                   data-max="@(Model.OrderSymbol)@(Model.OrderSum)">
                                        </span>
                                    </div>
                                    <span class="tips_txt">可退款额：@(Model.OrderSymbol)@(Model.OrderSum)</span>
                                </div>
                            </div>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
    <div class="rows clean box_submit fixed_btn_submit" style="width: 2096px; left: 180px;">
        <div class="center_container_1200">
            <div class="input input_button">
                <input type="submit" class="btn_global btn_submit btn_refund_submit" value="退款 @(Model.OrderDetailModel.Currency) @(Model.OrderSymbol)0.00">
                <a href="/Orders/Detail?id=@(Model.OrderDetailModel.OrderId)&query_string=" class="btn_global btn_cancel">取消</a>
            </div>
        </div>
    </div>

</div>