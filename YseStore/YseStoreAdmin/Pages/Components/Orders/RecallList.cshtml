 
@model YseStoreAdmin.Pages.Components.Orders.RecallList 
@{
}
<div>

	<div id="orders_recall" class="r_con_wrap" style="height: 329px;">
		<div class="inside_container new_inside_container inside_menu_right">
			<h1>弃单管理</h1>
			<div class="inside_menu">
				<div class="inside_title"><span>所有</span><i></i></div>
				<div class="inside_body">
					<ul>
						<li><a href="/Orders/Recall" class="@(Model.Status==""||Model.Status=="0"?"current":"")">所有</a></li>
						<li><a href="/Orders/Recall?Status=1" status="1" class="@(Model.Status=="1"?"current":"")">未召回</a></li>
						<li><a href="/Orders/Recall?Status=2" status="2" class="@(Model.Status=="2"?"current":"")">客户进入网站</a></li>
						<li><a href="/Orders/Recall?Status=3" status="3" class="@(Model.Status=="3"?"current":"")">召回失败</a></li>
						<li><a href="/Orders/Recall?Status=4" status="4" class="@(Model.Status=="4"?"current":"")">召回成功</a></li>
					</ul>
					<div class="inside_menu_current" style="left: 0px;"></div>
				</div>
			</div>
		</div>
		<div class="inside_table">
			<div class="list_menu">
				<div class="search_box fl">
					<form id="w0" action="/Orders/Recall" method="get">
						<div class="k_input">
							<input type="text" class="form_input" name="Keyword" size="15" autocomplete="off" value="@(Model.keywords)" placeholder="请输入订单号、邮箱、收件人姓名或SKU">					
							<input type="submit" class="search_btn" value="&#xe600;">
						</div>
						<input type="button" class="filter_btn" value="筛选">
						<div class="clear"></div>
						<input type="hidden" name="CId" value="@(Model.CId)">
						<input type="hidden" name="Status" value="@(Model.Status)">
					</form>
				</div>
				<div class="global_tips no_icon">未付款订单 @(Model.HowLongStr) 后会自动发送召回邮件，邮件包含 @(Model.UsedOfferStr) 优惠 <a target="_blank" href="/Setting/Shopping">设置</a></div>
				<div class="clear"></div>
				<div class="search_box_selected">
					@if (!string.IsNullOrEmpty(Model.keywords))
                    {
                        <span class="btn_item_choice current" data-name="Keyword"><b>搜索词: @(Model.keywords) </b><i></i></span>
                    }
					@if (!string.IsNullOrEmpty(Model.CId))
					{
						<span class="btn_item_choice current" data-name="CId"><b>国家及地区: @(Model.CIdName) </b><i></i></span>
					}
				</div>

			</div>
			<div class="box_table">
				<recall-list-table />
				<mx-pager name="manlog" total="@Model.TotalCount" page-size="@Model.PageSize" page-num="@Model.PageNum" />
				@* <div id="turn_page" data-current="0" data-count="1"><div class="total_page">共 2 条</div></div> *@
			</div>
		</div>
		<div class="pop_form orders_recall_sendemail">
			<form id="recall_edit_form" class="global_form" action="/Orders/Recall" method="post">
				<input type="hidden" name="_csrf-manage" value="DqPOMtf02xK4FtMFa8k6JnOh8Vu6bnSVPaOR8_IBnq1kzYF_jcGwKv9wpVwJjX9CSpODPslDJ_dZ7_akuk_dgA==">			<div class="r_con_form">
					<div class="pop_box_left">
						<div class="title"><strong>发送邮件</strong></div>
						<div class="content">
							<div class="label">使用优惠</div>
							<div class="switchery switchery_big">
								<input type="checkbox" value="1" name="UsedOffer">
								<div class="switchery_toggler"></div>
								<div class="switchery_inner">
									<div class="switchery_state_on"></div>
									<div class="switchery_state_off"></div>
								</div>
							</div>
							<div class="blank20"></div>
							<div class="discount_box">
								<div class="label">优惠类型</div>
								<div class="box_type_menu">
									<span class="item checked"><input type="radio" name="Type" value="0" checked="">折扣券</span>
									<span class="item"><input type="radio" name="Type" value="1">现金券</span>
								</div>
								<div class="box_type_menu_content">
									<div class="item">
										<span class="unit_input"><input name="Discount" value="0" type="text" class="box_input right_radius" maxlength="2" size="24" rel="int" autocomplete="off"><b class="last">% off</b></span>
										<div class="unit_tips">例如：20% off 代表 八折</div>
									</div>
									<div class="item none">
										<span class="unit_input"><b>$</b><input name="Price" value="0" type="text" class="box_input left_radius" maxlength="10" size="25" autocomplete="off" rel="amount"></span>
									</div>
								</div>
							</div>
							<input type="hidden" name="OrderId" value="0">
							<input type="hidden" name="Currency" value="">
							<input type="hidden" name="do_action" value="/api/RecallList/RecallSave">
						</div>
						<div class="tips_box">
							<span class="global_app_tips"><em></em><span class="tips">两次发送邮件至少间隔1天，最多发送5次</span></span>
							<div class="tips_bottom">
								上次发送时间: <span class="recall_time"></span>
							</div>
						</div>
						<div class="button"><input type="submit" class="btn_global btn_submit" id="button_add" name="submit_button" value="发送 (%Times%/5)"><input type="button" class="btn_global btn_cancel" value="取消"></div>
					</div>
					<div class="pop_box_right">
						<div class="title t"><span>邮件预览</span><h2>×</h2></div>
						<div class="content"></div>
					</div>
				</div>
			</form>
		</div>
		<div id="fixed_right">
			<div class="global_container fixed_search_filter" data-width="396">
				<div class="top_title">筛选 <a href="javascript:;" class="close"></a></div>
				<div class="global_form">
					<div class="box_filter">
						<div class="filter_list">
							<div class="filter_title">国家及地区</div>
							<div class="filter_option">
								<div class="filter_option_list">
									@if (Model.countries!=null)
									{
										foreach (var item in Model.countries)
										{
											<div class="filter_option_item" data-type="radio">
												<input type="radio" name="CId" value="@(item.CId)">
												<span class="filter_option_title">@(item.Country)</span>
											</div>
										}
									}

									@* <div class="filter_option_item" data-type="radio">
										<input type="radio" name="CId" value="74">
										<span class="filter_option_title">France</span>
									</div> *@
								</div>
								<div class="filter_clean"><button>清除</button></div>
							</div>
						</div>
					</div>
					<div class="rows clean box_button box_submit">
						<div class="input">
							<input type="button" class="btn_global btn_submit" value="筛选">
							<input type="button" class="btn_global btn_cancel" value="取消">
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>

</div>