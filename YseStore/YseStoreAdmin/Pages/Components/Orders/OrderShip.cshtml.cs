using Entitys;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using MiniExcelLibs.Utils;
using SqlSugar.Extensions;
using YseStore.IService.Customer;
using YseStore.IService.Order;
using YseStore.Service.Order;
using YseStoreAdmin.Pages.Components.Setting;
using YseStoreAdmin.Pages.Setting;

namespace YseStoreAdmin.Pages.Components.Orders
{
    public class OrderShip : MComponent
    {
        public IOrderListService _orderListService { get; }
        public ICustomerListService _customerListService { get; }
        public IWayBillListService _wayBillListService { get; }
        public orders_package? OrderPackageFirst { get; set; }
        public List<orders_products_list>? OrderProductsLists { get; set; }
        public orders OrderDetailModel { get; set; }
        public int ProductCount { get; set; }
        public decimal TotalProductPrice { get; set; }
        public decimal points { get; set; }
        public decimal shippingFee { get; set; }
        public decimal commission { get; set; }
        public decimal OrderSum { get; set; }
        public string OrderSymbol { get; set; }
        public string paymentMethod { get; set; }
        public int OrdersCount { get; set; }
        public int PaymentCount { get; set; }

        public List<CarrierResultResponse>? CarrierResultResponseList { get; set; }
        public string ShippingCarrierJson { get; set; }

        public OrderShip(IOrderListService orderListService, ICustomerListService customerListService, IWayBillListService wayBillListService)
        {
            _orderListService = orderListService;
            _customerListService = customerListService;
            _wayBillListService = wayBillListService;
        }

        public override async Task MountAsync()
        {
            var orderid = HttpContext.Request.Query["id"].ObjToInt();
            var WId = HttpContext.Request.Query["WId"].ObjToInt();
            var userid = await _orderListService.GetUserIdByOrderIdAsync(orderid);
            OrderDetailModel = await _orderListService.GetOrderDetailByOrderIdAsync(orderid);
            var OrdersAmount = await _orderListService.GetOrdersAmount(orderid);
            ProductCount = OrdersAmount.Item1;
            TotalProductPrice = OrdersAmount.Item2;
            points = OrdersAmount.Item3;
            shippingFee = OrdersAmount.Item4;
            commission = OrdersAmount.Item5;
            OrderSum = OrdersAmount.Item6;
            OrderSymbol = OrdersAmount.Item7;
            paymentMethod = OrdersAmount.Item8;


            OrderPackageFirst = await _orderListService.GetPackageByWIdAsync(WId);
            if (OrderPackageFirst !=null&& OrderPackageFirst.ShippingTime==0)
            {
                OrderPackageFirst.ShippingTime = YseStore.Common.Helper.DateTimeHelper.ConvertToUnixTimestamp(DateTime.Now);
            }
            OrderProductsLists = await _orderListService.GetProducts_Lists(orderid);

            CarrierResultResponseList = await _wayBillListService.GetShippingCarrierData();
            ShippingCarrierJson = System.Text.Json.JsonSerializer.Serialize(CarrierResultResponseList);


        }




    }
}
