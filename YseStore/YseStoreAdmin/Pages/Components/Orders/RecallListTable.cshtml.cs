using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using YseStore.IService.Order;
using YseStore.Model.Event;
using YseStore.Model.VM;

namespace YseStoreAdmin.Pages.Components.Orders
{

    public class RecallListTable : MComponent
    {
        public readonly IRecallListService _recallListService;
        public string keywords { get; set; }
		public string CId = "";
		public string Status = "";

		public PagedList<RecallListResponse>? RecallListReviewList { get; set; }

        public RecallListTable(IRecallListService recallListService)
        {

            Subscribe<UserTurnPageEvent>(HandleUserTurnPage);
			_recallListService = recallListService;
        }
        public async Task HandleUserTurnPage(UserTurnPageEvent evt)
        {
            if (evt.Name == "manlog")
            {
                await BindData(evt.PageNum);
            }
        }

        public override async Task MountAsync()
        {
            keywords = HttpContext.Request.Query["Keyword"].ToString();
			CId = HttpContext.Request.Query["CId"].ToString();
			Status = HttpContext.Request.Query["Status"].ToString();
			await BindData();
        }
        [SkipOutput]
        public async Task BindData(int page = 1)
        {
			RecallListReviewList = await GetLogs(page);
            DispatchGlobal<PageEvent>(new PageEvent(RecallListReviewList.TotalCount, RecallListReviewList.PageSize, RecallListReviewList.PageIndex + 1, "manlog"), null, true);

        }
        public async Task<PagedList<RecallListResponse>> GetLogs(int page = 1)
        {
            var result = await _recallListService.QueryAsync(keywords, CId, Status, page, 20);
            return result;
        }
    }

}
