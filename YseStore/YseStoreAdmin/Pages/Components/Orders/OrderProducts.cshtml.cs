using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using YseStoreControls.Pages.Components.Drawer;

namespace YseStoreAdmin.Pages.Components.Orders
{
    public class OrderProducts :  DrawerBase
    {
        public override void Mount()
        {
            base.Mount();
        }
        public string? Name { get; set; }
        public int? Age { get; set; }
        [SkipOutput]
        public async Task Save()
        {
            Task.Delay(1000).Wait();
            base.Close();
            await Task.CompletedTask;
        }
    }
}
