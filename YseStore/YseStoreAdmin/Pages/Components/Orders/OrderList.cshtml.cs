using Entitys;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using YseStoreControls.Pages.Components.Drawer;
using System.Collections.Generic;
using YseStore.Common;
using YseStore.IService.Customer;
using YseStore.IService.Order;
using YseStore.Model.Enums;
using YseStore.Model.Event;
using YseStore.Model.VM;
using YseStore.Model.VM.Payment.Paypal;


namespace YseStoreAdmin.Pages.Components.Orders;

public class OrderList : MComponent
{
    public int TotalCount { get; set; }
    public int PageSize { get; set; }
    public int PageNum { get; set; }
    public string PaymentStatus = "";
    public string ShippingStatus = "";
    public string OrderStatus = "";
    public string OrderStatusName = "";
    public string PaymentStatusName = "";
    public string ShippingStatusName = "";
    public string MenuSort = "";
    public string keywords { get; set; }
    public string OrderTime { get; set; }
    public string PaymentTime { get; set; }
    public string PId { get; set; }
    public string PIdName { get; set; }
    public string SId { get; set; }
    public string TId { get; set; }
    public string CreateType { get; set; }
    public string StoreSource { get; set; }
    public string SIdName { get; set; }
    public string CreateTypeName { get; set; }

    public IOrderListService _orderListService { get; }
    public List<orders_tags>? orders_tagsList { get; set; }
    public List<payment> PaymentMethods { get; set; }
    public string OrderTagsJson { get; set; }
    public List<shipping> Ordershipping { get; set; }

    public PagedList<OrderResponse>? OrderReviewList { get; set; }


    public Dictionary<string, string> Order_ExportMenuList { get; set; }
    public List<MenuItem> filteredFields { get; set; }

    public List<MenuItem> filteredFieldsOrder { get; set; }
    public List<MenuItem> filteredFieldsPro { get; set; }
    public List<MenuItem> filteredFieldsPay { get; set; }
    public List<MenuItem> filteredFieldsCus { get; set; }

    public OrderList(IOrderListService orderListService)
    {
        _orderListService = orderListService;
    }
    public override void Mount()
    {
        base.Mount();

    }
    public void HandlePageEvent(PageEvent evt)
    {
        TotalCount = evt.Total;
        PageSize = evt.PageSize;
        PageNum = evt.PageNum;

    }
    public async Task ChangePage1()
    {
        await Task.CompletedTask;
    }
    [SkipOutput]
    public async Task TurnPage(int page)
    {
        DispatchGlobal<UserTurnPageEvent>(new UserTurnPageEvent("manlog", page));
    }
    public override async Task MountAsync()
    {
        keywords = HttpContext.Request.Query["Keyword"].ToString();
        //UserType = HttpContext.Request.Query["UserType"].ToString();
        OrderStatus = HttpContext.Request.Query["OrderStatus"].ToString();
        PaymentStatus = HttpContext.Request.Query["PaymentStatus"].ToString();
        ShippingStatus = HttpContext.Request.Query["ShippingStatus"].ToString();
        OrderTime = HttpContext.Request.Query["OrderTime"].ToString();
        PaymentTime = HttpContext.Request.Query["PaymentTime"].ToString();
        PId = HttpContext.Request.Query["PId"].ToString();
        SId = HttpContext.Request.Query["SId"].ToString();
        TId = HttpContext.Request.Query["TId"].ToString();
        CreateType = HttpContext.Request.Query["CreateType"].ToString();
        StoreSource = HttpContext.Request.Query["StoreSource"].ToString();
        MenuSort = HttpContext.Request.Query["MenuSort"].ToString();
        //OrderReviewList = await GetLogs(keywords, OrderStatus, PaymentStatus, ShippingStatus, OrderTime, PaymentTime,
        //         PId, SId, TId, CreateType, StoreSource);
        // 使用延迟确保DOM渲染完成
        //await Task.Delay(100);
        orders_tagsList = await _orderListService.GetOrders_Tags();
        PaymentMethods = await _orderListService.GetpaymentList();



        //User_tagsList = await _orderListService.GetUser_Tags();
        var result = await _orderListService.GetFormattedOrdersTags();
        OrderTagsJson = System.Text.Json.JsonSerializer.Serialize(result);

        Ordershipping = await _orderListService.GetShipping();


        #region 搜索栏
        if (!string.IsNullOrEmpty(OrderStatus))
        {
            List<string> OrderStatusList = OrderStatus.Split(",").ToList();
            if (OrderStatusList != null && OrderStatusList.Count > 0)
            {
                foreach (var item in OrderStatusList)
                {
                    OrderStatusName += EnumExtensions.FromDescription<OrderStatusEnum>(item) + "~";
                }
                OrderStatusName = OrderStatusName.Substring(0, OrderStatusName.Length - 1);
            }
        }
        if (!string.IsNullOrEmpty(PaymentStatus))
        {
            List<string> PaymentStatusList = PaymentStatus.Split(",").ToList();
            if (PaymentStatusList != null && PaymentStatusList.Count > 0)
            {
                foreach (var item in PaymentStatusList)
                {
                    PaymentStatusName += EnumExtensions.FromDescription<PaymentStatusEnum>(item) + "~";
                }
                PaymentStatusName = PaymentStatusName.Substring(0, PaymentStatusName.Length - 1);
            }
        }
        if (!string.IsNullOrEmpty(ShippingStatus))
        {
            List<string> ShippingStatusList = ShippingStatus.Split(",").ToList();
            if (ShippingStatusList != null && ShippingStatusList.Count > 0)
            {
                foreach (var item in ShippingStatusList)
                {
                    ShippingStatusName += EnumExtensions.FromDescription<ShippingStatusEnum>(item) + "~";
                }
                ShippingStatusName = ShippingStatusName.Substring(0, ShippingStatusName.Length - 1);
            }
        }
        if (!string.IsNullOrEmpty(PId) && PaymentMethods != null && PaymentMethods.Count > 0)
        {
            var PIds = PId.Split(',').Select(int.Parse).ToList();
            var Name = PaymentMethods.Where(x => PIds.Contains(x.PId)).Select(x => x.Name_en).ToList();
            PIdName = string.Join("-", Name);
        }
        if (!string.IsNullOrEmpty(SId) && Ordershipping != null && Ordershipping.Count > 0)
        {
            var SIds = SId.Split(',').Select(int.Parse).ToList();
            var Name = Ordershipping.Where(x => SIds.Contains(x.SId)).Select(x => x.Express).ToList();
            SIdName = string.Join("-", Name);
        }
        if (!string.IsNullOrEmpty(CreateType))
        {
            var CreateTypes = CreateType.Split(',').ToList();
            if (CreateTypes != null && CreateTypes.Count > 0)
            {
                foreach (var item in CreateTypes)
                {
                    if (item == "system")
                    {
                        CreateTypeName += "客户下单~";
                    }
                    else
                    {
                        CreateTypeName += "手动创建~";
                    }
                }
                CreateTypeName = CreateTypeName.Substring(0, CreateTypeName.Length - 1);
            }
        }

        #endregion



        Order_ExportMenuList = await _orderListService.GetOrder_ExportMenuList();


        filteredFields = OrderExportMenuHelper.AllMenuItemsText()
          .Where(kvp => Order_ExportMenuList.ContainsKey(kvp.Key))
          .Select(kvp => new MenuItem
          {
              Id = kvp.Key,
              Text = kvp.Value,
              IsHidden = Order_ExportMenuList.ContainsKey(kvp.Key) && Order_ExportMenuList[kvp.Key] != "1"
          })
          .ToList();

        filteredFieldsOrder = OrderExportMenuHelper.AllMenuItemsTextOrder
          .Where(kvp => Order_ExportMenuList.ContainsKey(kvp.Key))
          .Select(kvp => new MenuItem
          {
              Id = kvp.Key,
              Text = kvp.Value,
              IsHidden = Order_ExportMenuList.ContainsKey(kvp.Key) && Order_ExportMenuList[kvp.Key] != "1"
          })
          .ToList();
        filteredFieldsPro = OrderExportMenuHelper.AllMenuItemsTextPro
          .Where(kvp => Order_ExportMenuList.ContainsKey(kvp.Key))
          .Select(kvp => new MenuItem
          {
              Id = kvp.Key,
              Text = kvp.Value,
              IsHidden = Order_ExportMenuList.ContainsKey(kvp.Key) && Order_ExportMenuList[kvp.Key] != "1"
          })
          .ToList();
        filteredFieldsPay = OrderExportMenuHelper.AllMenuItemsTextPay
          .Where(kvp => Order_ExportMenuList.ContainsKey(kvp.Key))
          .Select(kvp => new MenuItem
          {
              Id = kvp.Key,
              Text = kvp.Value,
              IsHidden = Order_ExportMenuList.ContainsKey(kvp.Key) && Order_ExportMenuList[kvp.Key] != "1"
          })
          .ToList();
        filteredFieldsCus = OrderExportMenuHelper.AllMenuItemsTextCus
          .Where(kvp => Order_ExportMenuList.ContainsKey(kvp.Key))
          .Select(kvp => new MenuItem
          {
              Id = kvp.Key,
              Text = kvp.Value,
              IsHidden = Order_ExportMenuList.ContainsKey(kvp.Key) && Order_ExportMenuList[kvp.Key] != "1"
          })
          .ToList();

    }






    //public async Task<PagedList<OrderResponse>> GetLogs(string keywords, string OrderStatus, string PaymentStatus, string ShippingStatus, string OrderTime, string PaymentTime,
    //        string PId, string SId, string TId, string CreateType, string StoreSource, int page = 1)
    //{
    //    var result = await _orderListService.QueryAsync(keywords, OrderStatus, PaymentStatus, ShippingStatus, OrderTime, PaymentTime,
    //        PId, SId, TId, CreateType, StoreSource, page, 20);
    //    return result;
    //}
    public class MenuItem
    {
        public string Id { get; set; }
        public string Text { get; set; }
        public bool IsHidden { get; set; }
    }



    public async Task ShowOrderProducts()
    {
        DispatchGlobal(new OpenDrawer(nameof(OrderProducts)));
        await Task.CompletedTask;
    }

}
