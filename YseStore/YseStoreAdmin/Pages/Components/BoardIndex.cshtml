
@model YseStoreAdmin.Pages.Components.BoardIndex
@{
}
<div>
    <div id="account" class="r_con_wrap">

        <div class="home_container clean">
            <div class="greeting">
                <div class="datepicker" id="index" x-data="{showdatepane:false}">
                    <div class="inner selector" x-on:click.away="showdatepane=false">
                        <div class="text" x-on:mouseenter="showdatepane=true"><span>今天</span><i class="icon iconfont icon_menu_downarrow"></i></div>
                        <div class="pane" x-show="showdatepane">
                            <div class="form">
                                <div class="item">
                                    <div class="label">日期范围</div>
                                    <div class="input">
                                        <select name="date">
                                            <option value="0" data-index="today">今天</option>
                                            <option value="1" data-index="yesterday">昨天</option>
                                            <option value="7" data-index="week">过去7天</option>
                                            <option value="14" data-index="month">过去14天</option>
                                            <option value="thisWeek" data-index="thisWeek">本周</option>
                                            <option value="thisMonth" data-index="thisMonth">本月</option>
                                            <option value="lastWeek" data-index="lastWeek">上周</option>
                                            <option value="lastMonth" data-index="lastMonth">上月</option>
                                            <option value="custom" data-index="custom">自定义</option>
                                        </select>
                                        <i class="icon iconfont icon_menu_downarrow"></i>
                                    </div>
                                </div>
                                <div class="custom_date item">
                                    <div class="label">开始时间</div>
                                    <div class="input">
                                        <input name="beginAt" value="" type="text" class="date" readonly />
                                    </div>
                                </div>
                                <div class="custom_date item">
                                    <div class="label">结束时间</div>
                                    <div class="input">
                                        <input name="endAt" value="" type="text" class="date" readonly />
                                    </div>
                                </div>
                                <div class="item btn_group">
                                    <button class="submit btn_global btn_submit">确认</button>
                                    <button class="cancel btn_global btn_cancel">取消</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="dashboard">
                <div class="left">
                    <!-- 销售数据: 销售额, 订单数 -->
                    <div class="pannel today-sales">
                        <div class="head">
                            成交数据                    <div class="detail"><a href="/manage/mta/orders-mta">详细</a></div>
                        </div>
                        <div class="content">
                            <div class="row amount">销售额<span class="val">--</span></div>
                            <div class="row count">订单数<span class="val">--</span></div>
                        </div>
                    </div>

                    <!-- 待办事项 -->
                    <div class="pannel todo">
                        <div class="head">待办事项</div>
                        <div class="content">
                            <div class="row">
                                <a class="item unship" href="/manage/orders/orders?type=tab&ShippingStatus=unshipped">
                                    <span class="val">--</span>个订单待发货                        <i class="icon iconfont icon-arrow3"></i>
                                </a>
                            </div>
                            <div class="row">
                                <a class="item unpay" href="/manage/orders/orders?PaymentStatus=unpaid">
                                    <span class="val">--</span>个订单待付款                        <i class="icon iconfont icon-arrow3"></i>
                                </a>
                            </div>
                        </div>
                    </div>

                    <!-- 产品数据 -->
                    <div class="pannel products">
                        <div class="content">
                            <div class="tabs">
                                <div class="tab-header">
                                    <span class="nav active" data-index="purchase">销量排行</span>
                                    <span class="nav" data-index="cart">加购产品</span>
                                    <span class="nav" data-index="visit">浏览产品</span>
                                </div>
                                <div class="tab-content">
                                    <div class="tabpanel active" data-index="purchase">
                                        <div class="no_data">暂无数据</div>
                                        <div class="product-header">
                                            <div class="info">产品</div>
                                            <div class="num">销量</div>
                                        </div>
                                        <div class="product-list"></div>
                                    </div>
                                    <div class="tabpanel" data-index="cart">
                                        <div class="no_data">暂无数据</div>
                                        <div class="product-header">
                                            <div class="info">产品</div>
                                            <div class="num">加购次数</div>
                                        </div>
                                        <div class="product-list"></div>
                                    </div>
                                    <div class="tabpanel" data-index="visit">
                                        <div class="no_data">暂无数据</div>
                                        <div class="product-header">
                                            <div class="info">产品</div>
                                            <div class="num">浏览次数</div>
                                        </div>
                                        <div class="product-list"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="right">
                    <!-- 访问数据: 转化率, 访客数 -->
                    <div class="pannel">
                        <div class="head">
                            <div class="detail"><a href="/manage/mta/visits-conversion">详细</a></div>
                        </div>
                        <div class="content">
                            <!-- 转化率 -->
                            <div class="report conver">
                                <div class="title"><p>转化率</p><span class="val">--</span></div>
                                <div class="chart conversion" id="converChart" data-checkout-mode="single">
                                    <ul>
                                        <li class="operate header">
                                            <div class="action"></div>
                                            <div class="num">访客</div>
                                            <div class="rate">转化率</div>
                                        </li>
                                        <li class="operate enter">
                                            <div class="action">进入店铺</div>
                                            <div class="num"><span class="val">--</span></div>
                                            <div class="rate"></div>
                                        </li>
                                        <li class="operate addcart">
                                            <div class="action">加入购物车</div>
                                            <div class="num"><span class="val">--</span></div>
                                            <div class="rate"><span class="val">--</span></div>
                                        </li>
                                        <li class="operate addpayment">
                                            <div class="action">添加支付信息</div>
                                            <div class="num"><span class="val">--</span></div>
                                            <div class="rate"><span class="val">--</span></div>
                                        </li>
                                        <li class="operate placeorder">
                                            <div class="action">提交结账</div>
                                            <div class="num"><span class="val">--</span></div>
                                            <div class="rate"><span class="val">--</span></div>
                                        </li>
                                        <li class="operate complete">
                                            <div class="action">付款成功</div>
                                            <div class="num"><span class="val">--</span></div>
                                            <div class="rate"><span class="val">--</span></div>
                                        </li>
                                    </ul>
                                </div>
                            </div>
                            <!-- 访客 -->
                            <div class="report uv">
                                <div class="title"><p>访客</p><span class="val">--</span></div>
                                <div class="chart" id="uvChart"></div>
                            </div>
                        </div>
                    </div>
                    <!-- 流量数据: 浏览量, 流量来源 -->
                    <div class="pannel">
                        <div class="head">
                            <div class="detail"><a href="/manage/mta/visits">详细</a></div>
                        </div>
                        <div class="content">
                            <!-- 浏览 -->
                            <div class="report pv">
                                <div class="title"><p>浏览量</p><span class="val">--</span></div>
                                <div class="chart" id="pvChart"></div>
                            </div>
                            <!-- 流量 -->
                            <div class="report referrer">
                                <div class="title"><p>流量来源</p><span class="val">&nbsp;</span></div>
                                <div class="chart" id="referrerChart"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>