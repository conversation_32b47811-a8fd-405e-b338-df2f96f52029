using Entitys;
using YseStore.IService.Products;
using YseStore.Model.Event;
using YseStore.Model.RequestModels.Products;
using Newtonsoft.Json;
using Newtonsoft.Json;

namespace YseStoreAdmin.Pages.Components.Products
{
    /// <summary>
    /// 推荐产品表格组件
    /// </summary>
    public class RecommendTable : MComponent
    {
        public int TotalCount { get; set; }
        public int PageSize { get; set; }
        public int PageNum { get; set; }
        public string _keyword { get; set; }
        public string _productsType { get; set; }
        public string _productsScope { get; set; }

        /// <summary>
        /// 排序字段
        /// </summary>
        public string SortField { get; set; } = "AccTime";

        /// <summary>
        /// 排序方向
        /// </summary>
        public string SortDirection { get; set; } = "DESC";

        private readonly IProductRecommendService _productRecommendService;
        private readonly ILogger<RecommendTable> _logger;

        public List<products_recommend> Recommends { get; set; } = new();

        public RecommendTable(IProductRecommendService productRecommendService, ILogger<RecommendTable> logger)
        {
            _productRecommendService = productRecommendService ??
                                       throw new ArgumentNullException(nameof(productRecommendService));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));

            // 订阅分页事件
            Subscribe<UserTurnPageEvent>(HandleUserTurnPage);
        }

        // 处理用户分页事件
        public async Task HandleUserTurnPage(UserTurnPageEvent evt)
        {
            if (evt.Name == "recommendList")
            {
                await BindData(evt.PageNum);
            }
        }

        public override async Task MountAsync()
        {
            InitializeFilters();

            // 从URL查询字符串中获取筛选条件
            _keyword = HttpContext.Request.Query["keyword"].ToString();
            _productsType = HttpContext.Request.Query["productsType"].ToString();
            _productsScope = HttpContext.Request.Query["productsScope"].ToString();

            // 处理排序参数
            var sortParam = HttpContext.Request.Query["sort"].ToString();
            if (!string.IsNullOrEmpty(sortParam))
            {
                ParseSortParameter(sortParam);
            }

            await BindData();
        }

        public void InitializeFilters()
        {
            _keyword = string.Empty;
            _productsType = string.Empty;
            _productsScope = string.Empty;
        }

        [SkipOutput]
        public async Task BindData(int page = 1)
        {
            try
            {
                // 构建查询请求
                var request = new ProductRecommendQueryRequest
                {
                    PageIndex = page,
                    PageSize = PageSize > 0 ? PageSize : 10,
                    OrderByFileds = $"{SortField} {SortDirection}", // 使用动态排序
                    QueryType = "recommend" // 设置查询类型为推荐产品
                };

                // 添加筛选条件
                if (!string.IsNullOrEmpty(_keyword))
                {
                    request.Keyword = _keyword;
                }

                if (!string.IsNullOrEmpty(_productsType))
                {
                    request.ProductsType = _productsType;
                }

                if (!string.IsNullOrEmpty(_productsScope))
                {
                    request.ProductsScope = _productsScope;
                }

                var result = await _productRecommendService.GetRecommendList(request);
                Recommends = result?.data ?? new List<products_recommend>();
                TotalCount = result?.dataCount ?? 0;
                PageNum = page;
                PageSize = request.PageSize;

                // 发送分页事件到全局
                DispatchGlobal<PageEvent>(new PageEvent(TotalCount, PageSize, PageNum, "recommendList"), null, true);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "加载推荐产品表格数据失败");
            }
        }

        public void HandlePageEvent(PageEvent evt)
        {
            TotalCount = evt.Total;
            PageSize = evt.PageSize;
            PageNum = evt.PageNum;
        }

        [SkipOutput]
        public async Task TurnPage(int page)
        {
            DispatchGlobal<UserTurnPageEvent>(new UserTurnPageEvent("recommendList", page));
        }

        /// <summary>
        /// 解析排序参数
        /// </summary>
        /// <param name="sortParam">排序参数，格式如：AccTime_ASC 或 AccTime_DESC</param>
        private void ParseSortParameter(string sortParam)
        {
            if (string.IsNullOrEmpty(sortParam))
                return;

            var parts = sortParam.Split('_');
            if (parts.Length == 2)
            {
                SortField = parts[0];
                SortDirection = parts[1].ToUpper();
            }
        }

        /// <summary>
        /// 获取推荐类型显示文本
        /// </summary>
        /// <param name="productsType">推荐类型</param>
        /// <returns>显示文本</returns>
        public string GetProductsTypeText(string productsType)
        {
            return productsType switch
            {
                "browse" => "最近浏览的产品",
                "new" => "新品",
                "sales" => "畅销产品",
                "hot" => "热门产品",
                "special" => "特色产品",
                "related" => "相关产品",
                "recommended_set" => "推荐集",
                _ => productsType ?? "未知"
            };
        }

        /// <summary>
        /// 获取配置方式显示文本
        /// </summary>
        /// <param name="showType">配置方式</param>
        /// <returns>显示文本</returns>
        public string GetProductsScopeText(string showType)
        {


            // 其他类型的配置方式
            return showType switch
            {
                "auto" => "自动添加",
                "manual" => "手动添加",
                "products" => "指定产品",
                _ => showType ?? ""
            };
        }
        

        /// <summary>
        /// 获取显示页面文本
        /// </summary>
        /// <param name="pages">页面配置</param>
        /// <returns>显示文本</returns>
        public string GetPagesText(string pages)
        {
            if (string.IsNullOrEmpty(pages))
                return "";

            try
            {
                List<string> pageArray;

                // 尝试解析JSON数组格式
                if (pages.StartsWith("[") && pages.EndsWith("]"))
                {
                    var jsonArray = JsonConvert.DeserializeObject<string[]>(pages);
                    pageArray = jsonArray?.ToList() ?? new List<string>();
                }
                else
                {
                    pageArray = pages.Split('、').ToList();
                }

                var pageTexts = new List<string>();

                foreach (var page in pageArray)
                {
                    var pageText = page.Trim().Trim('"') switch
                    {
                        "index" => "首页",
                        "list" => "产品列表页",
                        "goods" => "产品详细页",
                        "cart" => "购物车页",
                        "favorite" => "我的收藏页",
                        "search" => "搜索结果页",
                        "add_purchase_pop" => "加购弹窗",
                        "blog-list" => "博客列表页",
                        "blog-detail" => "博客详细页",
                        _ => page.Trim().Trim('"')
                    };
                    if (!string.IsNullOrEmpty(pageText))
                    {
                        pageTexts.Add(pageText);
                    }
                }

                return pageTexts.Any() ? string.Join("、", pageTexts) : "";
            }
            catch
            {
                return pages;
            }
        }

        /// <summary>
        /// 从Data字段中获取标题
        /// </summary>
        /// <param name="data">Data JSON字符串</param>
        /// <returns>标题</returns>
        public string GetTitleFromData(string data)
        {
            try
            {
                if (string.IsNullOrEmpty(data))
                    return "";

                var dataObj = JsonConvert.DeserializeObject<dynamic>(data);
                return dataObj?.Title?.ToString() ?? "";
            }
            catch
            {
                return "";
            }
        }
    }
}