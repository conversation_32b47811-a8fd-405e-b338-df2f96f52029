
@model YseStoreAdmin.Pages.Components.Products.SaleStickerEdit 
@{
}
<div>

    <div id="sale_sticker_edit" class="r_con_wrap" style="height: 310px;">
        <div class="center_container_1043">
            <div class="return_title">
                <a href="/Products/SaleSticker/">
                    <span class="return">促销标签</span>
                    <span class="s_return">/ 添加</span>
                </a>
            </div>
            <form id="edit_form" class="global_form">
                <div class="left_container">
                    <div class="left_container_side">
                        <div class="global_container">
                            <div class="big_title">内容</div>
                            <div class="rows clean">
                                <label>名称</label>
                                <div class="input">
                                    <input name="Name" value="@Model.StickerDetail.Name" type="text" maxlength="50" class="box_input full_input"
                                    size="42" notnull="">
                                </div>
                            </div>
                            <div class="rows clean">
                                <label>样式</label>
                                <div class="input">
                                    <div class="style_item  @(Model.StickerDetail.StickerContentData.Style == "oval" ? "cur" : "")" data-value="oval">
                                        <div class="style_info style_oval"></div>
                                        <input type="radio" name="ContentData[Style]" checked="" value="oval">
                                    </div>
                                    <div class="style_item @(Model.StickerDetail.StickerContentData.Style == "round" ? "cur" : "")" data-value="round">
                                        <div class="style_info style_round"></div>
                                        <input type="radio" name="ContentData[Style]" value="round">
                                    </div>
                                    <div class="style_item @(Model.StickerDetail.StickerContentData.Style == "square" ? "cur" : "")" data-value="square">
                                        <div class="style_info style_square"></div>
                                        <input type="radio" name="ContentData[Style]" value="square">
                                    </div>
                                    <div class="multi_img upload_file_multi " id="PicDetail_en" style="display:inline-block">
                                        <input type="hidden" name="stickerimgdeletecallback" value="sale_sticker_obj.deleteimgCallback();">

                                        <dl class="img   @(string.IsNullOrEmpty(Model.StickerDetail.StickerContentData.BgImgPath)?"":"isfile") " num="0">


                                            <dt class="upload_box preview_pic">


                                                @if (!string.IsNullOrEmpty(Model.StickerDetail.StickerContentData.BgImgPath))
                                                {
                                                    <input type="button" class="btn_ok upload_btn" name="submit_button" value="上传图片" tips=""  style="display:none">
                                                    <input type="hidden" name="Data[en][Pic]" value="@Model.StickerDetail.StickerContentData.BgImgPath" data-value="" save="1">

                                                    <a href="javascript:;">
                                                        <img src="@Model.StickerDetail.StickerContentData.BgImgPath"><em></em>
                                                    </a>
                                                }
                                                else
                                                {
                                                    <input type="button" class="btn_ok upload_btn" name="submit_button" value="上传图片" tips="" >
                                                    <input type="hidden" name="Data[en][Pic]" value="" data-value="" save="0">
                                                }
                                            </dt>


                                            <dd class="pic_btn">
                                                @if (string.IsNullOrEmpty(Model.StickerDetail.StickerContentData.BgImgPath))
                                                {
                                                    <a href="javascript:;" class="preview" rel="preview">
                                                        <i class="icon_multi_preview"></i>
                                                    </a>
                                                }
                                                else {
                                                    <a href="@Model.StickerDetail.StickerContentData.BgImgPath" class="zoom"
                                                    target="_blank"><i class="icon_multi_view"></i></a>
                                                }
                                                <a href="javascript:;" class="del" rel="del">
                                                    <i class="icon_multi_delete"></i>
                                                </a>
                                            </dd>
                                        </dl>

                                    </div>
                                </div>
                            </div>

                            <div class="rows clean">
                                <label>颜色</label>
                                <div class="input">
                                    <div class="color_row_box">
                                        <div class="color_rows">
                                            <input class="color_style text_color" name="ContentData[TextColor]"
                                            value="@Model.StickerDetail.StickerContentData.TextColor" style="background-color:@Model.StickerDetail.StickerContentData.TextColor" data-bindclass=""
                                            data-type="">
                                            <span>文字</span>
                                        </div>
                                        <div class="color_rows">
                                            <input class="color_style bg_color" name="ContentData[BgColor]"
                                            value="@Model.StickerDetail.StickerContentData.BgColor" style="background-color:@Model.StickerDetail.StickerContentData.BgColor" data-bindclass=""
                                            data-type="">
                                            <span>样式背景</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="global_container">
                            <div class="big_title">展示位置</div>
                            <div class="box_type_menu position_box">
                                <span class="item @(Model.StickerDetail.Position == "left-top" ? "checked" : "")">
                                    <input type="radio" name="Position" value="left-top" @(Model.StickerDetail.Position == "left-top" ? "checked" : "")>
                                    产品图左上角
                                </span>
                                <span class="item @(Model.StickerDetail.Position == "right-top" ? "checked" : "")">
                                    <input type="radio" name="Position" value="right-top" @(Model.StickerDetail.Position == "right-top" ? "checked" : "")>
                                    产品图右上角
                                </span>
                            </div>
                        </div>
                        <div class="global_container">
                            <div class="big_title">适用范围</div>
                            <div class="rows clean type_box">
                                <div class="list">
                                    <span class="input_radio_box input_radio_side_box tab_option fl @(Model.StickerDetail.StickerScopeData.Type == "new" ? "checked" : "")"
                                    data-type="new">
                                        <span class="input_radio">
                                            <input type="radio" name="ScopeData[Type]" value="new">
                                        </span>
                                        <strong class="fs14">新品</strong>
                                        <p class="fs12">店铺新上架的产品</p>
                                    </span>
                                    <span class="input_radio_box input_radio_side_box tab_option fl @(Model.StickerDetail.StickerScopeData.Type == "sales" ? "checked" : "")" data-type="sales">
                                        <span class="input_radio">
                                            <input type="radio" name="ScopeData[Type]" value="sales">
                                        </span>
                                        <strong class="fs14">畅销产品</strong>
                                        <p class="fs12">销售次数最多的产品</p>
                                    </span>
                                    <span class="input_radio_box input_radio_side_box tab_option fl @(Model.StickerDetail.StickerScopeData.Type == "hot" ? "checked" : "")" data-type="hot">
                                        <span class="input_radio">
                                            <input type="radio" name="ScopeData[Type]" value="hot">
                                        </span>
                                        <strong class="fs14">热门产品</strong>
                                        <p class="fs12">浏览次数最多的产品</p>
                                    </span>
                                    <span class="input_radio_box input_radio_side_box tab_option fl @(Model.StickerDetail.StickerScopeData.Type == "discount" ? "checked" : "")"
                                    data-type="discount">
                                        <span class="input_radio">
                                            <input type="radio" name="ScopeData[Type]" value="discount">
                                        </span>
                                        <strong class="fs14">折扣产品</strong>
                                        <p class="fs12">已添加折扣的产品</p>
                                    </span>
                                    <span class="input_radio_box input_radio_side_box tab_option fl @(Model.StickerDetail.StickerScopeData.Type == "specify" ? "checked" : "")"
                                    data-type="specify">
                                        <span class="input_radio">
                                            <input type="radio" name="ScopeData[Type]" value="specify">
                                        </span>
                                        <strong class="fs14">指定分类/产品</strong>
                                        <p class="fs12">自定义添加产品</p>
                                    </span>
                                </div>
                            </div>
                            <!-- 新品 - 上架时间 -->
                            <div class="rows clean scope_box  time_rows  @(Model.StickerDetail.StickerScopeData.Type== "new" ? "" : "hide")" style="display: @(Model.StickerDetail.StickerScopeData.Type== "new" ? "block" : "none");" data-type="new">
                                <label>上架时间</label>
                                <div class="time_info">
                                    <div class="fl">
                                        <label>开始时间</label>
                                        <div class="input">
                                            <input name="ScopeData[Data][StartTime]" value="" type="text"
                                            class="box_input input_time" size="30" readonly="">
                                        </div>
                                    </div>
                                    <div class="fl">
                                        <label>&nbsp;</label>
                                        <div class="input"><span class="line">~</span></div>
                                    </div>
                                    <div class="fl">
                                        <label>结束时间</label>
                                        <div class="input">
                                            <input name="ScopeData[Data][EndTime]" value="" type="text"
                                            class="box_input input_time" size="30" readonly="">
                                        </div>
                                    </div>
                                    <div class="clear"></div>
                                    <div class="zone">
                                        当前时区 @Model.SelectedTimeZone <a href="/Setting/Basis">设置时区</a>
                                    </div>
                                    <div class="clear"></div>
                                </div>
                            </div>
                            <!-- 指定产品 -->
                            <div class="rows clean scope_box specify_rows  @(Model.StickerDetail.StickerScopeData.Type== "specify" ? "" : "hide")" style="display: @(Model.StickerDetail.StickerScopeData.Type== "specify" ? "block" : "none");" data-type="specify">
                                <label>产品</label>
                                <div class="input">
                                    <div class="box_type_menu specify_box">
                                        <span class="item @(Model.StickerDetail.StickerScopeData.Data.specifyType == "all" ? "checked" : "")">
                                            <input type="radio" name="ScopeData[Data][specifyType]" value="all"
                                            @(Model.StickerDetail.StickerScopeData.Data.specifyType == "all" ? "checked='checked'" : "")>
                                            全部产品
                                        </span>
                                        <span class="item @(Model.StickerDetail.StickerScopeData.Data.specifyType == "category" ? "checked" : "")">
                                            <input type="radio" name="ScopeData[Data][specifyType]" value="category" @(Model.StickerDetail.StickerScopeData.Data.specifyType == "category" ? "checked='checked'" : "")>
                                            指定分类
                                        </span>
                                        <span class="item @(Model.StickerDetail.StickerScopeData.Data.specifyType == "products" ? "checked" : "")">
                                            <input type="radio" name="ScopeData[Data][specifyType]" value="products" @(Model.StickerDetail.StickerScopeData.Data.specifyType == "products" ? "checked='checked'" : "")>
                                            指定产品
                                        </span>
                                    </div>
                                    <div class="use_group_box" data-value="category" style="display: @(Model.StickerDetail.StickerScopeData.Data.specifyType == "category" ? "block" : "none");">
                                        <div class="global_select_category_btn_box">
                                            <div class="title_box">
                                                <span>指定分类</span>
                                                <a href="javascript:;" data-type=""
                                                data-input-name="products_categoryCurrent"
                                                class="global_select_category_btn">选择</a>
                                            </div>
                                            <div class="global_select_category_value_box">
                                                <div class="category_value_box @(Model.StickerDetail.StickerScopeData.Data.specifyType == "category" ? "" : "hide")">
                                                    @foreach (var item in Model.ProductCategorySelected)
                                                    {
                                                        <div class="category_item" data-cateid="@item.Value">
                                                            <div class="cname">@item.Name</div>
                                                            <div class="cdel"><span class="icon iconfont icon_menu_close"></span></div>
                                                            <input type="hidden" name="products_categoryCurrent[]" value="@item.Value">
                                                        </div>
                                                    }

                                                </div>
                                                @if (Model.ProductCategorySelected.Count == 0)
                                                {
                                                    <div class="no-data ">当前暂时没有数据</div>
                                                }
                                            </div>
                                        </div>
                                    </div>
                                    <div class="use_group_box" data-value="products" style="display: @(Model.StickerDetail.StickerScopeData.Data.specifyType == "products" ? "block" : "none");">
                                        <dl class="box_drop_double" data-checkbox="1" data-showadd="1">
                                            <dt class="box_checkbox_list">
                                                @{
                                                    if (Model.ProductSelected != null && Model.ProductSelected.Count > 0)
                                                    {
                                                        <div class="select_list">
                                                            @foreach (var item in Model.ProductSelected)
                                                            {
                                                                <span class="btn_attr_choice current" data-type="products">
                                                                    <em class="icon icon_products pic_box">
                                                                        <img src="/u_file/2505/08/products/8ac6823ba5.jpg?x-oss-process=image/format,webp/resize,m_lfit,,h_240,w_240">
                                                                        <span></span>
                                                                    </em> <b>@item.Name</b>
                                                                    <input type="checkbox" name="productsCurrent[]" value="@item.Value" class="option_current" checked="">
                                                                    <input type="hidden" name="productsOption[]" value="@item.Value">
                                                                    <input type="hidden" name="productsName[]" value="@item.Name"><i></i>
                                                                </span>
                                                            }
                                                        </div>
                                                    }
                                                    else
                                                    {
                                                        <div class="select_placeholder">请选择</div>
                                                        <div class="select_list">
                                                        </div>
                                                    }
                                                }
                                                <input type="text"   class="box_input check_input"      name="ScopeData[Data][specifyData]Value" value="" placeholder=""
                                                size="30" maxlength="255" autocomplete="off">
                                                <input type="hidden"  name="ScopeData[Data][specifyData]" value=""  class="hidden_value"><input type="hidden"   name="ScopeData[Data][specifyData]Type" value=""
                                                class="hisdden_type">
                                            </dt>
                                            <dd class="drop_down" style="display: none;">
                                                <div class="drop_menu" data-type="">
                                                    <a href="javascript:;"
                                                    class="btn_back" data-value="" data-type="" data-table=""
                                                    data-top="0" data-all="0" style="display:none;">返回</a>
                                                    <div class="drop_skin" style="display: none;"></div>
                                                    <div class="drop_list"
                                                    data="@Model.ProductSelect" data-more="block">
                                                    </div>@* <a href="javascript:;" class="btn_load_more"
                                                             data-value="products" data-type="products" data-table="products"
                                                             data-top="0" data-all="1" data-check-all="0" data-start="1"
                                                             style="display:block;">加载更多</a> *@
                                                </div>
                                            </dd>
                                        </dl>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="right_container">
                    <div class="global_container">
                        <div class="big_title">预览</div>
                        @if (!string.IsNullOrEmpty(Model.StickerDetail.StickerContentData.BgImgPath))
                        {
                            <div class="rows clean preview_row" style="background: url(@Model.StickerDetail.StickerContentData.BgImgPath) center center no-repeat;">
                                <div class="preview_sticker @(Model.IsEdit?"show":"")" style="" data="@Model.StickerDetail.StickerContentData.Style">@Model.StickerDetail.Name</div>
                            </div>
                        }
                        else { 
                            <div class="rows clean preview_row">
                                <div class="preview_sticker @(Model.IsEdit?"show":"")" style="" data="@Model.StickerDetail.StickerContentData.Style">@Model.StickerDetail.Name</div>
                            </div>
                        }
                        
                    </div>
                </div>

                <input type="hidden" name="SId" value="@Model.Id">
                <input type="hidden" name="do_action" value="/api/SlaeSticker/savesalesticker">
            </form>
        </div>
    </div>
    <div class="pop_form global_select_category_popup_box">
        <div class="t">
            <h1>选择分类</h1>
            <h2>×</h2>
        </div>
        <div class="search_form">
            <div class="k_input">
                <input type="text" name="Keyword" value="" class="form_input" size="15" autocomplete="off"
                       placeholder="请输入分类名称">
            </div>
            <input type="button" value="搜索" class="search_btn">
            <div class="category_num">
                已选择 <span class="num">0</span> 个分类
            </div>
        </div>
        <div class="category_content">
            <div class="category_table select_category_table">
                <div class="thead">
                    <div class="tr">
                        <div class="td">
                            多选<i class="tool_tips_ico" content="批量多选指定分类及其子分类"> </i>
                            &nbsp;&nbsp;分类名称
                        </div>
                    </div>
                </div>
                @Html.Raw(Model.CategoryHtml)
            </div>
            <div class="category_table search_category_table">
                <div class="thead">
                    <div class="tr">
                        <div class="td">
                            分类名称
                        </div>
                    </div>
                </div>
                <div class="tbody"></div>
                <div class="bg_no_table_data" style="height: 442px;">
                    <div class="content" style="top: 181px;">
                        <p>当前暂时没有数据</p>
                    </div><span></span>
                </div>
            </div>
        </div>
        <div class="button">
            <input type="submit" class="btn_global btn_submit" name="submit_button" value="保存">
            <input type="button" class="btn_global btn_cancel" value="取消">
        </div>
    </div>
    <div class="rows clean fixed_btn_submit" style="width: 1868px; left: 180px;">
        <div class="center_container">
            <div class="input input_button">
                <input type="button" class="btn_global btn_submit" value="保存">
                <a href="/Products/SaleSticker">
                    <input type="button" class="btn_global btn_cancel"
                           value="返回">
                </a>
            </div>
        </div>
    </div>

</div>