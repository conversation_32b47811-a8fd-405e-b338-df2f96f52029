using Entitys;
using YseStore.IService;
using YseStore.IService.Products;
using YseStore.Model.Event;
using YseStore.Model.RequestModels.Products;

namespace YseStoreAdmin.Pages.Components.Products
{
    public class ProductTable : MComponent
    {
        public string Currency { get; set; }
        public int TotalCount { get; set; }
        public int PageSize { get; set; }
        public int PageNum { get; set; }
        public string _keyword { get; set; }
        public string _cateId { get; set; }
        public string _tagId { get; set; }
        public string _minPrice { get; set; }
        public string _maxPrice { get; set; }
        public string _status { get; set; } = "0";

        /// <summary>
        /// 排序字段
        /// </summary>
        public string SortField { get; set; } = "ModifyTime";

        /// <summary>
        /// 排序方向
        /// </summary>
        public string SortDirection { get; set; } = "DESC";

        /// <summary>
        /// 网站基础URL，用于生成预览链接
        /// </summary>
        public string WebSiteUrl { get; set; }

        private readonly IProductService _productService;
        private readonly ICurrencyService _currencyService;
        private readonly ILogger<ProductTable> _logger;

        public List<products> Products { get; set; } = new();

        public ProductTable(IProductService productService, ILogger<ProductTable> logger,
            ICurrencyService currencyService)
        {
            _productService = productService ?? throw new ArgumentNullException(nameof(productService));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _currencyService = currencyService ?? throw new ArgumentNullException(nameof(currencyService));

            // 订阅分页事件
            Subscribe<UserTurnPageEvent>(HandleUserTurnPage);
        }

        // 处理用户分页事件
        public async Task HandleUserTurnPage(UserTurnPageEvent evt)
        {
            if (evt.Name == "productlist")
            {
                await BindData(evt.PageNum);
            }
        }

        public override async Task MountAsync()
        {
            InitializeFilters();
            var currency = await _currencyService.GetManageDefaultCurrency();
            Currency = currency.Symbol;
            // 从配置中获取网站基础URL
            WebSiteUrl = AppSettingsConstVars.WebSiteUrl ?? "http://localhost:5195/";

            // 从URL查询字符串中获取筛选条件
            _keyword = HttpContext.Request.Query["keyword"].ToString();
            _cateId = HttpContext.Request.Query["cateId"].ToString();
            _tagId = HttpContext.Request.Query["tagId"].ToString();
            _minPrice = HttpContext.Request.Query["minPrice"].ToString();
            _maxPrice = HttpContext.Request.Query["maxPrice"].ToString();
            _status = HttpContext.Request.Query["status"].ToString() ?? "0";

            // 处理排序参数
            var sortParam = HttpContext.Request.Query["sort"].ToString();
            if (!string.IsNullOrEmpty(sortParam))
            {
                ParseSortParameter(sortParam);
            }

            await BindData();
        }

        public void InitializeFilters()
        {
            _keyword = string.Empty;
            _cateId = string.Empty;
            _tagId = string.Empty;
            _minPrice = string.Empty;
            _maxPrice = string.Empty;
            _status = "0";
        }

        [SkipOutput]
        public async Task BindData(int page = 1)
        {
            try
            {
                // 构建查询请求
                var request = new ProductQueryRequest
                {
                    PageIndex = page,
                    Status = 0,
                    PageSize = PageSize > 0 ? PageSize : 10,
                    OrderByFileds = $"{SortField} {SortDirection}" // 使用动态排序
                };

                // 添加筛选条件
                if (!string.IsNullOrEmpty(_keyword))
                {
                    request.Keyword = _keyword;
                }

                if (!string.IsNullOrEmpty(_cateId) && int.TryParse(_cateId, out int cateIdValue))
                {
                    request.CateId = cateIdValue;
                }

                if (!string.IsNullOrEmpty(_tagId))
                {
                    request.TagId = _tagId;
                }

                if (!string.IsNullOrEmpty(_minPrice) && decimal.TryParse(_minPrice, out decimal minPriceValue))
                {
                    request.MinPrice = minPriceValue;
                }

                if (!string.IsNullOrEmpty(_maxPrice) && decimal.TryParse(_maxPrice, out decimal maxPriceValue))
                {
                    request.MaxPrice = maxPriceValue;
                }

                if (!string.IsNullOrEmpty(_status) && int.TryParse(_status, out int statusValue))
                {
                    request.Status = statusValue;
                }

                var result = await _productService.GetProductList(request);
                Products = result?.data ?? new List<products>();
                TotalCount = result?.dataCount ?? 0;
                PageNum = page;
                PageSize = request.PageSize;

                // 发送分页事件到全局
                DispatchGlobal<PageEvent>(new PageEvent(TotalCount, PageSize, PageNum, "productlist"), null, true);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "加载产品表格数据失败");
            }
        }

        public void HandlePageEvent(PageEvent evt)
        {
            TotalCount = evt.Total;
            PageSize = evt.PageSize;
            PageNum = evt.PageNum;
        }

        [SkipOutput]
        public async Task TurnPage(int page)
        {
            DispatchGlobal<UserTurnPageEvent>(new UserTurnPageEvent("productlist", page));
        }

        /// <summary>
        /// 解析排序参数
        /// </summary>
        /// <param name="sortParam">排序参数，格式如：AccTime_ASC 或 ModifyTime_DESC</param>
        private void ParseSortParameter(string sortParam)
        {
            if (string.IsNullOrEmpty(sortParam))
                return;

            var parts = sortParam.Split('_');
            if (parts.Length == 2)
            {
                SortField = parts[0];
                SortDirection = parts[1].ToUpper();
            }
        }
    }
}