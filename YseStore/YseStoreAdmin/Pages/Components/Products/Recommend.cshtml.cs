using Entitys;
using YseStore.IService.Products;

namespace YseStoreAdmin.Pages.Components.Products
{
    public class Recommend : MComponent
    {
        private readonly IProductRecommendService _productRecommendService;
        private readonly ILogger<Recommend> _logger;
        private readonly IHttpContextAccessor _httpContextAccessor;

        // 推荐产品列表属性，将在视图中使用
        public List<products_recommend> Recommends { get; set; } = new();

        // 分页信息
        public int PageNum { get; set; } = 1;
        public int PageSize { get; set; } = 10;
        public int TotalCount { get; set; } = 0;
        public int TotalPages { get; set; } = 0;

        // 搜索关键词
        public string Keyword { get; set; } = string.Empty;

        // 推荐类型
        public string ProductsType { get; set; } = string.Empty;

        // 配置方式
        public string ProductsScope { get; set; } = string.Empty;

        public Recommend(
            IProductRecommendService productRecommendService,
            ILogger<Recommend> logger,
            IHttpContextAccessor httpContextAccessor)
        {
            _productRecommendService = productRecommendService ?? throw new ArgumentNullException(nameof(productRecommendService));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _httpContextAccessor = httpContextAccessor ?? throw new ArgumentNullException(nameof(httpContextAccessor));
        }

        // 组件异步挂载时调用
        public override async Task MountAsync()
        {
            try
            {
                // 从URL参数中获取筛选条件
                var request = _httpContextAccessor.HttpContext.Request;
                if (request.Query.ContainsKey("keyword"))
                {
                    Keyword = request.Query["keyword"].ToString();
                }

                if (request.Query.ContainsKey("productsType"))
                {
                    ProductsType = request.Query["productsType"].ToString();
                }

                if (request.Query.ContainsKey("productsScope"))
                {
                    ProductsScope = request.Query["productsScope"].ToString();
                }

                await base.MountAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "异步加载推荐产品列表时出错");
                Console.WriteLine("异步加载推荐产品列表时出错: " + ex.Message);
            }
        }
    }
}
