@model YseStoreAdmin.Pages.Components.Products.ProductSwitchContentView
@{
    var isEdit = Model.CurrentContent != null && Model.CurrentContent.CId > 0;
    var title = isEdit ? "编辑" : "添加";
}
<div>

    <div id="products_switch" class="r_con_wrap plugins_app_box" style="height: 292px;">
        <div class="center_container">
            <div class="return_new_title">
                <a href="/Products/SwitchContent@(Model.BackSwitchId > 0 ? "?id=" + Model.BackSwitchId : "")">
                    <span class="return">内容</span>
                    <span class="s_return">/ @title</span>
                </a>
            </div>
            <form id="edit_form" class="global_form" method="post">
                <input type="hidden" name="_csrf-manage"
                       value="62yTETBIQGyEy_KSPuGJbsXCrJ0EemLvZt01Z85psRavXPdnVD92M9OUs_N5p-UlnPbbwjcUJKcn6wQ1tyvLZQ==">
                <div class="global_container">
                    <div class="rows clean">
                        <label>名称 <span class="fs12 explain">(必填)</span></label>
                        <div class="input">
                            <input name="Name" id="Name" value="@Model.CurrentContent?.Name" type="text"
                                   class="box_input full_input" maxlength="20"
                                   size="20" notnull="">
                        </div>
                    </div>
                    <div class="rows clean box_switch_select">
                        <label>隶属切换卡</label>
                        <div class="input">
                            <div class="box_select">
                                <select name="SwitchID" id="SwitchID">
                                    <option value="0" data-apply="">请选择</option>
                                    @if (Model.AllSwitches != null && Model.AllSwitches.Any())
                                    {
                                        foreach (var switchItem in Model.AllSwitches)
                                        {
                                            <option value="@switchItem.SId" data-apply="@switchItem.Apply"
                                                    selected="@(Model.CurrentContent?.SId == switchItem.SId)">@switchItem.Name</option>
                                        }
                                    }
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="rows clean use_products_box @(Model.CurrentSwitch?.Apply == "Products" ? "" : "hide")"
                         data-apply="Products">
                        <label>指定产品</label>
                        <div class="input">
                            <dl class="box_drop_double" data-checkbox="1" data-showadd="1">
                                <dt class="box_checkbox_list">
                                    <div class="select_placeholder"
                                         style="@(!string.IsNullOrEmpty(Model.CurrentContent?.ApplyID) && Model.CurrentSwitch?.Apply == "Products" ? "display:none;" : "")">
                                        请选择
                                    </div>
                                    <div class="select_list">
                                        @if (!string.IsNullOrEmpty(Model.CurrentContent?.ApplyID) && Model.CurrentSwitch?.Apply == "Products")
                                        {
                                            var selectedIds = Model.CurrentContent.ApplyID.Split('|');
                                            var selectedItems = Model.ProductSelectItems.Where(item => selectedIds.Contains(item.Value.ToString())).ToList();
                                            foreach (var item in selectedItems)
                                            {
                                                <span class="btn_attr_choice current" data-type="@item.Type"
                                                      data-value="@item.Value">
                                                    <b>@item.Name</b>
                                                    <input type="checkbox" name="ProductCurrent[]" value="@item.Value"
                                                           class="option_current" checked>
                                                    <input type="hidden" name="ProductOption[]" value="@item.Value">
                                                    <input type="hidden" name="ProductName[]" value="@item.Name">
                                                    <i></i>
                                                </span>
                                            }
                                        }
                                    </div>
                                    <input type="hidden" id="ApplyValue" name="ApplyValue"
                                           value="@Model.CurrentContent?.ApplyID">
                                    <input type="hidden" name="Apply"
                                           value="@Model.CurrentSwitch?.Apply"
                                           class="hidden_value"><input type="hidden"
                                                                       name="ApplyType"
                                                                       value=""
                                                                       class="hisdden_type">
                                </dt>
                                <dd class="drop_down" style="display: none;">
                                    <div class="drop_menu" data-type="">
                                        <a href="javascript:;" class="btn_back"
                                           data-value="" data-type="" data-table="" data-top="0" data-all="0"
                                           style="display:none;">返回</a>
                                        <div class="drop_skin" style="display: none;"></div>
                                        <div class="drop_list"
                                             data="@System.Text.Json.JsonSerializer.Serialize(Model.ProductSelectItems.Select((item, index) => new { Name = item.Name, Type = item.Type, Value = item.Value }))"
                                             data-more="none">
                                            @if (Model.ProductSelectItems.Any())
                                            {
                                                @for (int i = 0; i < Model.ProductSelectItems.Count; i++)
                                                {
                                                    var item = Model.ProductSelectItems[i];
                                                    var isSelected = !string.IsNullOrEmpty(Model.CurrentContent?.ApplyID) &&
                                                                     Model.CurrentContent.ApplyID.Split(',').Contains(item.Value.ToString());
                                                    <span class="btn_attr_choice @(isSelected ? "current" : "")"
                                                          data-name="@item.Name" data-value="@item.Value"
                                                          data-type="@item.Type">
                                                        <b>@item.Name</b>
                                                        <input type="checkbox" name="_DoubleOption[]" value="@i"
                                                               class="option_current" @(isSelected ? "checked" : "")>
                                                        <input type="hidden" name="ProductOption[]" value="@item.Value">
                                                        <input type="hidden" name="ProductName[]" value="@item.Name">
                                                        <i></i>
                                                    </span>
                                                }
                                            }
                                        </div>
                                        <a href="javascript:;" class="btn_load_more" data-value="products"
                                           data-type="products" data-table="products" data-top="0" data-all="1"
                                           data-check-all="0" data-start="1" style="display:none;">加载更多</a>
                                    </div>
                                </dd>
                            </dl>
                        </div>
                    </div>
                    <div class="rows clean use_products_box @(Model.CurrentSwitch?.Apply == "Categories" ? "" : "hide")"
                         data-apply="Categories">
                        <div class="input">
                            <div class="global_select_category_btn_box">
                                <div class="title_box">
                                    <span>指定分类</span>
                                    <a href="javascript:;" data-type="" data-input-name="products_categoryCurrent"
                                       class="global_select_category_btn">选择</a>
                                </div>
                                <div class="global_select_category_value_box">
                                    <dl class="box_drop_double" data-checkbox="1" data-showadd="1">
                                        <dt class="box_checkbox_list">
                                            <div class="select_placeholder"
                                                 style="@(!string.IsNullOrEmpty(Model.CurrentContent?.ApplyID) && Model.CurrentSwitch?.Apply == "Categories" ? "display:none;" : "")">
                                                请选择
                                            </div>
                                            <div class="select_list category_select_list">
                                                @if (!string.IsNullOrEmpty(Model.CurrentContent?.ApplyID) && Model.CurrentSwitch?.Apply == "Categories")
                                                {
                                                    var selectedIds = Model.CurrentContent.ApplyID.Split('|');
                                                    foreach (var categoryId in selectedIds)
                                                    {
                                                        if (int.TryParse(categoryId.Trim(), out int id))
                                                        {
                                                            var categoryName = Model.FindCategoryNameById(id);
                                                            if (!string.IsNullOrEmpty(categoryName))
                                                            {
                                                                <span class="btn_attr_choice current"
                                                                      data-type="Categories" data-value="@id">
                                                                    <b>@categoryName</b>
                                                                    <input type="checkbox" name="CategoryCurrent[]"
                                                                           value="@id" class="option_current" checked>
                                                                    <input type="hidden" name="CategoryOption[]"
                                                                           value="@id">
                                                                    <input type="hidden" name="CategoryName[]"
                                                                           value="@categoryName">
                                                                    <i></i>
                                                                </span>
                                                            }
                                                        }
                                                    }
                                                }
                                            </div>
                                            <input type="hidden" id="CategoryApplyValue" name="ApplyValue"
                                                   value="@Model.CurrentContent?.ApplyID">
                                            <input type="hidden" name="Apply"
                                                   value="@Model.CurrentSwitch?.Apply"
                                                   class="hidden_value"><input type="hidden"
                                                                               name="ApplyType"
                                                                               value=""
                                                                               class="hisdden_type">
                                        </dt>
                                    </dl>
                                    <div
                                        class="no-data @(!string.IsNullOrEmpty(Model.CurrentContent?.ApplyID) && Model.CurrentSwitch?.Apply == "Categories" ? "hide" : "")">
                                        当前暂时没有数据
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="rows clean box_content">
                        <label>内容</label>
                        <span class="input">
                            <textarea id="Content" name="Content" data-change="0"
                                      aria-hidden="true">@Model.CurrentContent?.Content</textarea>
                          
                        </span>
                        <div class="clear"></div>
                    </div>
                    <div class="rows clean">
                        <label>移动端详细描述</label>
                        <div class="box_explain">开启后，移动端的详细描述将替换成以下内容</div>
                        <div class="switchery @(Model.CurrentContent?.UsedMobile == true ? "checked" : "")">
                            <input type="checkbox" id="UsedMobile" name="UsedMobile" value="1"
                                   @(Model.CurrentContent?.UsedMobile == true ? "checked" : "")>
                            <div class="switchery_toggler"></div>
                            <div class="switchery_inner">
                                <div class="switchery_state_on"></div>
                                <div class="switchery_state_off"></div>
                            </div>
                        </div>
                        <div class="blank15"></div>
                        <div class="input mobile_description @(Model.CurrentContent?.UsedMobile != true ? "hide" : "")">
                            <textarea id="MobileDescription"
                                      name="MobileDescription" data-change="0" aria-hidden="true"
                            >@Model.CurrentContent?.MobileDescription</textarea>

                        </div>
                    </div>
                </div>

                <input type="hidden" id="Id" name="id" value="@(Model.CurrentContent?.CId ?? 0)">
            </form>
        </div>
    </div>
    <div class="pop_form global_select_category_popup_box">
        <div class="t">
            <h1>选择分类</h1>
            <h2>×</h2>
        </div>
        <div class="search_form">
            <div class="k_input">
                <input type="text" name="Keyword" value="" class="form_input" size="15" autocomplete="off"
                       placeholder="请输入分类名称">
            </div>
            <input type="button" value="搜索" class="search_btn">
            <div class="category_num">
                已选择 <span class="num">0</span> 个分类
            </div>
        </div>
        <div class="category_content">
            <div class="category_table select_category_table">
                <div class="thead">
                    <div class="tr">
                        <div class="td">
                            多选<i class="tool_tips_ico" content="批量多选指定分类及其子分类"> </i>
                            &nbsp;&nbsp;分类名称
                        </div>
                    </div>
                </div>
                <div class="tbody">
                    @if (Model.CategoryTree.ContainsKey(0))
                    {
                        foreach (var rootCategory in Model.CategoryTree[0])
                        {
                            <div class="first_box">
                                <div
                                    class="tr @(Model.CategoryTree.ContainsKey(rootCategory.CateId) ? "haschild" : "")">
                                    <div class="td btn_select"><span class="icon iconfont icon_menu_listselect"></span>
                                    </div>
                                    <div class="td c_name">
                                        <span class="input_checkbox_box">
                                            <span class="input_checkbox">
                                                <input type="checkbox" name="GlobalSelectCateId"
                                                       data-alias="@rootCategory.Category_en"
                                                       value="@rootCategory.CateId">
                                            </span>@rootCategory.Category_en
                                        </span>
                                    </div>
                                    <div class="td btn_sub"><span class="icon iconfont icon_menu_downarrow"></span>
                                    </div>
                                </div>
                                @if (Model.CategoryTree.ContainsKey(rootCategory.CateId))
                                {
                                    <div class="second_box box_sub" data-child="@rootCategory.CateId">
                                        @foreach (var secondCategory in Model.CategoryTree[rootCategory.CateId])
                                        {
                                            <div
                                                class="tr @(Model.CategoryTree.ContainsKey(secondCategory.CateId) ? "haschild" : "")">
                                                <div class="td btn_select"><span
                                                        class="icon iconfont icon_menu_listselect"></span></div>
                                                <div class="td c_name">
                                                    <span class="input_checkbox_box">
                                                        <span class="input_checkbox">
                                                            <input type="checkbox" name="GlobalSelectCateId"
                                                                   data-alias="@rootCategory.Category_en > @secondCategory.Category_en"
                                                                   value="@secondCategory.CateId">
                                                        </span>@secondCategory.Category_en
                                                    </span>
                                                </div>
                                                <div class="td btn_sub"><span
                                                        class="icon iconfont icon_menu_downarrow"></span>
                                                </div>
                                            </div>
                                            @if (Model.CategoryTree.ContainsKey(secondCategory.CateId))
                                            {
                                                <div class="third_box box_sub" data-child="@secondCategory.CateId">
                                                    @foreach (var thirdCategory in Model.CategoryTree[secondCategory.CateId])
                                                    {
                                                        <div class="tr">
                                                            <div class="td btn_select"><span
                                                                    class="icon iconfont icon_menu_listselect"></span>
                                                            </div>
                                                            <div class="td c_name">
                                                                <span class="input_checkbox_box">
                                                                    <span class="input_checkbox">
                                                                        <input type="checkbox" name="GlobalSelectCateId"
                                                                               data-alias="@rootCategory.Category_en > @secondCategory.Category_en > @thirdCategory.Category_en"
                                                                               value="@thirdCategory.CateId">
                                                                    </span>@thirdCategory.Category_en
                                                                </span>
                                                            </div>
                                                            <div class="td btn_sub"><span
                                                                    class="icon iconfont icon_menu_downarrow"></span>
                                                            </div>
                                                        </div>
                                                    }
                                                </div>
                                            }
                                        }
                                    </div>
                                }
                            </div>
                        }
                    }
                </div>
            </div>
            <div class="category_table search_category_table">
                <div class="thead">
                    <div class="tr">
                        <div class="td">
                            分类名称
                        </div>
                    </div>
                </div>
                <div class="tbody"></div>
                <div class="bg_no_table_data" style="height: 404px;">
                    <div class="content" style="top: 162px;">
                        <p>当前暂时没有数据</p>
                    </div>
                    <span></span>
                </div>
            </div>
        </div>
        <div class="button">
            <input type="submit" class="btn_global btn_submit" name="submit_button" value="保存">
            <input type="button" class="btn_global btn_cancel" value="取消">
        </div>
    </div>
    <div class="rows fixed_btn_submit" style="width: 2096px; left: 180px;">
        <div class="center_container">
            <div class="input input_button">
                <input type="button" class="btn_global btn_submit" onclick="submitForm(); return false" value="保存">
                <a href="/Products/SwitchContent@(Model.BackSwitchId > 0 ? "?id=" + Model.BackSwitchId : "")">
                    <input type="button" class="btn_global btn_cancel"
                           value="返回">
                </a>
            </div>
        </div>
    </div>
    <script>
        // 全局提交方法
        function submitForm() {
            // 获取表单数据
            var formData = {
                Id: parseInt($('#Id').val()) || 0,
                Name: $('#Name').val(),
                SwitchID: parseInt($('#SwitchID').val()) || 0,
                ApplyValue: getSelectedApplyValue(),
                Content: $('#Content').val(),
                UsedMobile: $('#UsedMobile').is(':checked'),
                MobileDescription: $('#MobileDescription').val()
            };
            // 表单验证
            if (!formData.Name) {
                customize_pop.warning('名称不能为空', function () {
                }, 2000);
                return;
            }

            if (formData.SwitchID <= 0) {
                customize_pop.warning('请选择隶属切换卡', function () {
                }, 2000);
                return;
            }

            // 检查分类选择
            var apply = $('#SwitchID').find('option:selected').data('apply');
            if (apply === 'Categories') {
                // 检查是否有分类标签
                var categoryCount = $('.category_select_list .btn_attr_choice').length;
                if (categoryCount === 0 && (!formData.ApplyValue || formData.ApplyValue.trim() === '')) {
                    customize_pop.warning('请选择分类', function () {
                    }, 2000);
                    return;
                }
            }

            // 发送AJAX请求
            $.ajax({
                url: '/manage/plugins/product/switch-content-' + (formData.Id > 0 ? 'edit' : 'add'),
                type: 'POST',
                contentType: 'application/json',
                data: JSON.stringify(formData),
                success: function (response) {
                    if (response.ret === 1) {
                        customize_pop.success(response.msg, function () {
                            if (response.url) {
                                window.location.href = response.url;
                            }
                        }, 2000);
                    } else {
                        customize_pop.error(response.msg || '操作失败', function () {
                        }, 2000);
                    }
                },
                error: function () {
                    customize_pop.error('请求失败，请稍后再试', function () {
                    }, 2000);
                }
            });
        }

        // 获取当前选中的应用值（产品或分类）
        function getSelectedApplyValue() {
            var apply = $('#SwitchID').find('option:selected').data('apply');

            // 处理Products类型
            if (apply === 'Products') {
                // 收集所有选中的值
                var values = [];

                // 检查已选中的项
                var selectedItem = $('.box_drop_double .item.selected');
                if (selectedItem.length > 0) {
                    selectedItem.each(function () {
                        var value = $(this).data('value');
                        if (value && values.indexOf(value) === -1) {
                            values.push(value);
                        }
                    });
                }

                // 检查选中的复选框
                var checkedBoxes = $('.box_drop_double input[type="checkbox"]:checked');
                if (checkedBoxes.length > 0) {
                    checkedBoxes.each(function () {
                        var item = $(this).closest('.item');
                        var value = item.data('value');
                        if (value && values.indexOf(value) === -1) {
                            values.push(value);
                        }
                    });
                }

                // 如果收集到值，返回逗号分隔的字符串
                if (values.length > 0) {
                    return values.join(',');
                }

                // 如果没有从UI元素获取到值，返回输入框的值（去除前导逗号）
                var inputValue = $.trim($('#ApplyValue').val());
                if (inputValue) {
                    // 移除可能存在的前导逗号
                    return inputValue.replace(/^,+/, '');
                }
            }
            // 处理Categories类型
            else if (apply === 'Categories') {
                // 直接从当前显示的分类标签收集数据
                var selectedCategories = [];
                $('.category_select_list .btn_attr_choice').each(function () {
                    var categoryId = $(this).data('value');
                    if (categoryId && selectedCategories.indexOf(categoryId.toString()) === -1) {
                        selectedCategories.push(categoryId.toString());
                    }
                });

                if (selectedCategories.length > 0) {
                    return selectedCategories.join(',');
                }

                // 如果没有标签，检查隐藏字段的值
                var hiddenValue = $('#CategoryApplyValue').val();
                if (hiddenValue) {
                    return hiddenValue;
                }
            }
            // 其他情况直接返回对应的输入框的值（去除前导逗号）
            var apply = $('#SwitchID').find('option:selected').data('apply');
            var inputSelector = apply === 'Categories' ? '#CategoryApplyValue' : '#ApplyValue';
            var value = $.trim($(inputSelector).val());
            return value ? value.replace(/^,+/, '') : '';
        }

        $(function () {

            // 初始化页面，根据当前切换卡类型显示对应的输入区域
            function initPageByApplyType() {
                var apply = $('#SwitchID').find('option:selected').data('apply');
                if (!apply) return;

                // 隐藏所有的选择框，然后只显示当前选中类型的选择框
                $('.use_products_box').addClass('hide');
                $('.use_products_box[data-apply="' + apply + '"]').removeClass('hide');
                $('input.hidden_value').val(apply);

                // 根据不同类型初始化UI
                if (apply === 'Products') {
                    // 如果有ApplyValue值，尝试回显产品选择
                    var applyValue = $('#ApplyValue').val();
                    if (applyValue) {
                        $('.select_placeholder').hide();
                    } else {
                        $('.select_placeholder').show();
                        $('.select_list').empty();
                    }
                } else if (apply === 'Categories') {
                    // 如果有CategoryApplyValue值，尝试回显分类选择
                    var applyValue = $('#CategoryApplyValue').val();
                    if (applyValue) {
                        // 预选中对应的分类复选框，正确处理管道符格式
                        var categoryIds = [];
                        if (applyValue.startsWith('|') && applyValue.endsWith('|')) {
                            // 管道符格式 |11|123|456|
                            categoryIds = applyValue.slice(1, -1).split('|').map(function(id) {
                                return id.trim();
                            }).filter(function(id) {
                                return id && id !== '';
                            });
                        } else {
                            // 逗号分隔格式 11,123,456
                            categoryIds = applyValue.split(',').map(function(id) {
                                return id.trim();
                            }).filter(function(id) {
                                return id && id !== '';
                            });
                        }

                        categoryIds.forEach(function (id) {
                            var checkbox = $('input[name="GlobalSelectCateId"][value="' + id + '"]');
                            if (checkbox.length > 0) {
                                checkbox.prop('checked', true)
                                    .parents('.input_checkbox_box').addClass('checked');
                            }
                        });

                        // 显示分类选择列表（已经在服务器端渲染好了）
                        $('.select_placeholder').hide();
                        $('.no-data').addClass('hide');

                        // 更新已选择分类数量
                        $('.category_num .num').text(categoryIds.length);
                    } else {
                        $('.select_placeholder').show();
                        $('.category_select_list').empty();
                        $('.no-data').removeClass('hide');
                    }
                }
            }

            // 页面加载时初始化
            initPageByApplyType();

            // 绑定主提交按钮点击事件
            $('#submit_btn').click(function () {
                submitForm();
            });

            // 防止表单直接提交
            $('#edit_form').submit(function (e) {
                e.preventDefault();
                submitForm();
            });

            // 添加ApplyValue的值检查和处理
            $('.box_drop_double').on('click', '.item', function () {
                var value = $(this).data('value');
                // 设置选中状态
                if ($(this).hasClass('selected')) {
                    $(this).removeClass('selected');
                } else {
                    $(this).addClass('selected');
                }

                // 重新计算并设置ApplyValue
                var newValue = getSelectedApplyValue();
                $('#ApplyValue').val(newValue);
            });

            // 分类选择弹窗打开事件
            $('.global_select_category_btn').click(function (e) {
                e.preventDefault();
                e.stopPropagation();

                // 清空弹窗中的所有选择
                $('.global_select_category_popup_box input[name="GlobalSelectCateId"]').prop('checked', false)
                    .parents('.input_checkbox_box').removeClass('checked');

                // 获取当前的分类选择状态，优先从分类标签获取，如果没有则从隐藏字段获取
                var selectedCategoryIds = [];

                // 先从当前显示的分类标签获取
                $('.category_select_list .btn_attr_choice').each(function () {
                    var categoryId = $(this).data('value');
                    if (categoryId && selectedCategoryIds.indexOf(categoryId.toString()) === -1) {
                        selectedCategoryIds.push(categoryId.toString());
                    }
                });

                // 如果没有分类标签，则从隐藏字段获取（用于页面初始化时的回显）
                if (selectedCategoryIds.length === 0) {
                    var hiddenValue = $('#CategoryApplyValue').val();
                    if (hiddenValue) {
                        // 处理管道符格式 |11|123|456| 或逗号分隔格式 11,123,456
                        var ids = [];
                        if (hiddenValue.startsWith('|') && hiddenValue.endsWith('|')) {
                            // 管道符格式
                            ids = hiddenValue.slice(1, -1).split('|').map(function(id) {
                                return id.trim();
                            }).filter(function(id) {
                                return id && id !== '';
                            });
                        } else {
                            // 逗号分隔格式
                            ids = hiddenValue.split(',').map(function(id) {
                                return id.trim();
                            }).filter(function(id) {
                                return id && id !== '';
                            });
                        }
                        selectedCategoryIds = ids;
                    }
                }

                // 显示弹窗
                $('.global_select_category_popup_box').show();

                // 延迟设置复选框状态，确保弹窗完全显示后再设置
                setTimeout(function() {
                    // 根据获取到的分类ID来恢复弹窗中的选择状态
                    selectedCategoryIds.forEach(function(categoryId) {
                        var checkbox = $('.global_select_category_popup_box input[name="GlobalSelectCateId"][value="' + categoryId + '"]');
                        var checkboxContainer = checkbox.parents('.input_checkbox_box');

                        // 设置复选框选中状态
                        checkbox.prop('checked', true);
                        checkboxContainer.addClass('checked');
                    });

                    // 更新分类数量显示
                    $('.category_num .num').text(selectedCategoryIds.length);

                    // 触发全选按钮状态更新
                    if (typeof component_obj !== 'undefined' && component_obj.global_select_category) {
                        // 如果存在component.js的全选状态检查函数，调用它
                        setTimeout(function() {
                            $('.global_select_category_popup_box .select_category_table .haschild .btn_select').each(function(){
                                let obj = $(this).parents('.haschild');
                                let subObj = obj.next('.box_sub');
                                if (obj.find('.input_checkbox_box:not(".disabled") input').length + subObj.find('.input_checkbox_box:not(".disabled") input').length == obj.find('.input_checkbox_box:not(".disabled") input:checked').length + subObj.find('.input_checkbox_box:not(".disabled") input:checked').length) {
                                    $(this).addClass('selectall');
                                } else {
                                    $(this).removeClass('selectall');
                                }
                            });
                        }, 100);
                    }
                }, 100);

                return false;
            });

            // 分类选择事件处理
            $('.global_select_category_popup_box').on('change', 'input[name="GlobalSelectCateId"]', function () {
                // 收集所有选中的分类ID
                var selectedCategories = [];
                $('input[name="GlobalSelectCateId"]:checked').each(function () {
                    selectedCategories.push($(this).val());
                });

                // 更新分类数量显示
                $('.category_num .num').text(selectedCategories.length);

                // 当点击保存按钮时会更新category_value_box的内容
            });

            // 分类选择弹窗保存按钮点击事件
            $('.global_select_category_popup_box .btn_submit').click(function () {
                // 收集弹窗中所有选中的分类
                var selectedCategories = [];
                var selectedCategoryData = [];
                $('input[name="GlobalSelectCateId"]:checked').each(function () {
                    var categoryId = $(this).val();
                    var categoryName = $(this).attr('data-alias') || $(this).closest('.input_checkbox_box').text().trim();
                    selectedCategories.push(categoryId);
                    selectedCategoryData.push({
                        id: categoryId,
                        name: categoryName
                    });
                });

                // 直接使用弹窗中选中的分类数据，不需要合并现有分类
                var allCategories = selectedCategories;
                var allCategoryData = selectedCategoryData;

                if (allCategories.length > 0) {
                    // 清空并重新创建所有分类标签
                    var $selectList = $('.category_select_list');
                    $selectList.empty();

                    // 为所有分类创建带样式的标签
                    allCategoryData.forEach(function (category) {
                        var categoryTagHtml = '<span class="btn_attr_choice current" data-type="Categories" data-value="' + category.id + '">' +
                            '<b>' + category.name + '</b>' +
                            '<input type="checkbox" name="CategoryCurrent[]" value="' + category.id + '" class="option_current" checked>' +
                            '<input type="hidden" name="CategoryOption[]" value="' + category.id + '">' +
                            '<input type="hidden" name="CategoryName[]" value="' + category.name + '">' +
                            '<i></i>' +
                            '</span>';
                        $selectList.append(categoryTagHtml);
                    });

                    // 显示分类选择列表，隐藏占位符和无数据提示
                    $('.select_placeholder').hide();
                    $('.no-data').addClass('hide');

                    // 将分类ID保存到隐藏字段，以便表单提交
                    var categoryValueStr = allCategories.join(',');
                    $('#CategoryApplyValue').val(categoryValueStr);
                } else {
                    $('.category_select_list').empty();
                    $('.select_placeholder').show();
                    $('.no-data').removeClass('hide');
                    $('#CategoryApplyValue').val('');
                }

                // 关闭弹窗
                $('.global_select_category_popup_box').hide();
            });

            // 表单提交前检查ApplyValue
            $('#submit_btn, .btn_submit').on('click', function () {
                // 检查当前选中的切换卡类型
                var apply = $('#SwitchID').find('option:selected').data('apply');
                if (apply === 'Products') {
                    // 如果是产品类型，确保ApplyValue有值
                    var applyValue = $.trim($('#ApplyValue').val());

                    // 如果没有值但有选中的产品，尝试从选中项获取值
                    if (!applyValue) {
                        var selectedItem = $('.box_drop_double .item.selected');
                        if (selectedItem.length > 0) {
                            var selectedValue = selectedItem.data('value');
                            $('#ApplyValue').val(selectedValue);
                        }
                    }
                } else if (apply === 'Categories') {
                    // 如果是分类类型，确保CategoryApplyValue与当前显示的分类标签同步
                    var currentCategories = [];
                    $('.category_select_list .btn_attr_choice').each(function () {
                        currentCategories.push($(this).data('value').toString());
                    });

                    if (currentCategories.length > 0) {
                        var categoryValueStr = currentCategories.join(',');
                        $('#CategoryApplyValue').val(categoryValueStr);
                    } else {
                        // 如果没有分类标签，清空CategoryApplyValue
                        $('#CategoryApplyValue').val('');
                    }
                }
            });

            // 切换卡选择变化时更新相关UI
            $('#SwitchID').change(function () {
                var apply = $(this).find('option:selected').data('apply');

                // 隐藏所有的选择框，然后只显示当前选中类型的选择框
                $('.use_products_box').addClass('hide');
                $('.use_products_box[data-apply="' + apply + '"]').removeClass('hide');
                $('input.hidden_value').val(apply);

                // 清空ApplyValue和相关UI
                $('#ApplyValue').val('');
                $('#CategoryApplyValue').val('');

                // 重置Products相关UI
                if (apply === 'Products') {
                    // 清空产品选择
                    $('.box_drop_double .btn_attr_choice').removeClass('current');
                    $('.box_drop_double input[type="checkbox"]').prop('checked', false);
                    $('.select_placeholder').show();
                    $('.select_list').empty();
                }
                // 重置Categories相关UI
                else if (apply === 'Categories') {
                    // 清空分类选择
                    $('input[name="GlobalSelectCateId"]').prop('checked', false);
                    $('.category_num .num').text('0');
                    $('.category_select_list').empty();
                    $('.select_placeholder').show();
                    $('.no-data').removeClass('hide');
                }
            });

            // 移动端描述开关
            $('#UsedMobile').change(function () {
                if ($(this).is(':checked')) {
                    $('.mobile_description').removeClass('hide');
                } else {
                    $('.mobile_description').addClass('hide');
                }
            });

            // 添加复选框的事件处理
            $('.box_drop_double').on('change', 'input[type="checkbox"]', function () {
                // 重新计算并设置ApplyValue
                var newValue = getSelectedApplyValue();
                var apply = $('#SwitchID').find('option:selected').data('apply');
                if (apply === 'Categories') {
                    $('#CategoryApplyValue').val(newValue);
                } else {
                    $('#ApplyValue').val(newValue);
                }
            });

            // 添加分类标签删除功能
            $(document).on('click', '.category_select_list .btn_attr_choice i', function () {
                var $tag = $(this).closest('.btn_attr_choice');
                var categoryId = $tag.data('value');

                // 从UI中移除标签
                $tag.remove();

                // 取消对应的复选框选中状态
                $('input[name="GlobalSelectCateId"][value="' + categoryId + '"]').prop('checked', false)
                    .parents('.input_checkbox_box').removeClass('checked');

                // 更新ApplyValue
                var remainingCategories = [];
                $('.category_select_list .btn_attr_choice').each(function () {
                    remainingCategories.push($(this).data('value'));
                });

                if (remainingCategories.length > 0) {
                    $('#CategoryApplyValue').val(remainingCategories.join(','));
                } else {
                    $('#CategoryApplyValue').val('');
                    $('.select_placeholder').show();
                    $('.no-data').removeClass('hide');
                }

                // 更新分类数量显示
                $('.category_num .num').text(remainingCategories.length);
            });
        });

        // 覆盖全局product.js中的分类验证逻辑，防止干扰
        $(document).ready(function () {
            // 移除可能存在的全局表单验证事件
            $('.use_products_box').off('DOMSubtreeModified');

            // 如果存在全局验证函数，重写分类部分的验证逻辑
            if (typeof window.validateProductForm === 'function') {
                var originalValidate = window.validateProductForm;
                window.validateProductForm = function () {
                    // 对于分类选择，使用我们自己的验证逻辑
                    var apply = $('#SwitchID').find('option:selected').data('apply');
                    if (apply === 'Categories') {
                        var categoryCount = $('.category_select_list .btn_attr_choice').length;
                        if (categoryCount === 0) {
                            return false;
                        }
                        return true;
                    }
                    // 其他情况调用原始验证函数
                    return originalValidate.apply(this, arguments);
                };
            }
        });
    </script>
</div>

