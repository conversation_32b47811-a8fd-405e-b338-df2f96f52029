 
@model YseStoreAdmin.Pages.Components.Products.CustomComments 
@{
}
<div>

    <div id="custom_comments" class="r_con_wrap plugins_app_box" style="height: 504px;">
       
     
        <div class="inside_container inside_menu_right clean">
            <h1>自定义评论</h1>
            <ul class="inside_menu">
                <div class="inside_title"><span>上架</span><i></i></div>
                <div class="inside_body">
                    <ul>
                        <li><a href="/manage/plugins/custom-comments/index?status=1" class="current">上架</a></li>
                        <li><a href="/manage/plugins/custom-comments/index?status=2">下架</a></li>
                    </ul>
                </div>
            </ul>
        </div>
        <div class="inside_table">
            <div class="list_menu">
                <div class="search_form">
                    <form id="w0" action="/manage/plugins/custom-comments/index" method="get">
                        <div class="k_input">
                            <input type="text" name="Keyword" value="" class="form_input" size="15" autocomplete="off"
                                   placeholder="请输入产品名称、SKU或产品编号">
                            <input type="button" value="" class="more">
                        </div>
                        <input type="submit" class="search_btn" value="搜索">
                        <div class="ext drop_down">
                            <div class="rows item clean">
                                <label>分类</label>
                                <div class="input">
                                    <div class="box_select">
                                        <select name="CateId">
                                            <option value="">请选择</option>
                                            <option value="1">├Amateur Radios / HAM Radios</option>
                                            <option value="7">｜├Analog Radios</option>
                                            <option value="8">｜｜└Digital Radios</option>
                                            <option value="9">｜├Base Station Radios</option>
                                            <option value="11">｜└HF Radios</option>
                                            <option value="15">├Professional DMR radios</option>
                                            <option value="16">｜├Portable DMR radios</option>
                                            <option value="17">｜├Mobile DMR radios</option>
                                            <option value="18">｜└DMR Repeaters</option>
                                            <option value="2">├On-site business radios</option>
                                            <option value="19">｜├Analog Business Radios</option>
                                            <option value="20">｜├GMRS Business Radios</option>
                                            <option value="14">｜└License-free Business Radios</option>
                                            <option value="3">├Outdoor Two Way Radios</option>
                                            <option value="21">｜├License Free Consumer Two Way Radios</option>
                                            <option value="22">｜├Handheld GMRS Outdoor Radios</option>
                                            <option value="23">｜├GMRS Mobile Radios</option>
                                            <option value="24">｜└Family Walkie talkies</option>
                                            <option value="4">├Mobile Two-Way Radios</option>
                                            <option value="5">├Radio Repeaters</option>
                                            <option value="6">├Radio Accessories</option>
                                            <option value="12">├Deals</option>
                                            <option value="13">├Activity</option>
                                            <option value="25">└test</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="clear"></div>
                        <input type="hidden" name="status" value="1"> <input type="hidden" name="cateId">
                    </form>
                </div>
            </div>
            <form id="list_bat_edit" class="global_form">
                <div class="box_table">
                    <table border="0" cellpadding="5" cellspacing="0" class="r_con_table">
                        <thead>
                            <tr>
                                <td width="40" class="pos">
                                    <ul class="table_menu_button global_menu_button">
                                        <li>
                                            <div class="btn_checkbox ">
                                                <em class="button"></em><input type="checkbox"
                                                                               name="select_all" value="">
                                            </div>
                                        </li>
                                        <li><a class="edit btn_batch_edit" href="javascript:;">批量添加虚拟数据</a></li>
                                    </ul>
                                </td>
                                <td width="6%" nowrap="">图片</td>
                                <td width="36%" nowrap="">名称 / 分类</td>
                                <td width="" nowrap="">产品销量</td>
                                <td width="" nowrap="">评论平均分</td>
                                <td width="" nowrap="">评论次数</td>
                                <td width="" nowrap="">收藏数量</td>
                                <td width="12%" nowrap="">使用虚拟数据</td>
                                <td width="120" nowrap="">操作</td>
                            </tr>
                        </thead>
                        <tbody>
                            <tr data-id="47">
                                <td>
                                    <div class="btn_checkbox ">
                                        <em class="button"></em><input type="checkbox"
                                                                       name="select" value="47">
                                    </div>
                                </td>
                                <td class="img pic_box">
                                    <img src="/u_file/2505/22/products/9deb13b928.png?x-oss-process=image/format,webp/resize,m_lfit,,h_240,w_240"><span></span>
                                </td>
                                <td class="info">
                                    <div class="name">123123</div>
                                    <div class="classify color_888" cateid="1">
                                        Amateur Radios / HAM Radios
                                    </div>
                                </td>
                                <td nowrap="">0</td>
                                <td nowrap="">0.0</td>
                                <td nowrap="">0</td>
                                <td nowrap="">0</td>
                                <td nowrap="">否</td>
                                <td class="operation">
                                    <a class="development_btn" data-proid="47" href="javascript:;">添加虚拟数据</a> <br>
                                    <a href="/manage/plugins/custom-comments/list/?ProId=47">自定义评论列表</a>
                                </td>
                            </tr>
                            <tr data-id="45">
                                <td>
                                    <div class="btn_checkbox ">
                                        <em class="button"></em><input type="checkbox"
                                                                       name="select" value="45">
                                    </div>
                                </td>
                                <td class="img pic_box">
                                    <img src="/u_file/2505/08/products/8ac6823ba5.jpg?x-oss-process=image/format,webp/resize,m_lfit,,h_240,w_240"><span></span>
                                </td>
                                <td class="info">
                                    <div class="name">MyTestData</div>
                                    <div class="classify color_888" cateid="15">
                                        Professional DMR radios
                                    </div>
                                </td>
                                <td nowrap="">1</td>
                                <td nowrap="">0.0</td>
                                <td nowrap="">0</td>
                                <td nowrap="">0</td>
                                <td nowrap="">否</td>
                                <td class="operation">
                                    <a class="development_btn" data-proid="45" href="javascript:;">添加虚拟数据</a> <br>
                                    <a href="/manage/plugins/custom-comments/list/?ProId=45">自定义评论列表</a>
                                </td>
                            </tr>
                            <tr data-id="38">
                                <td>
                                    <div class="btn_checkbox ">
                                        <em class="button"></em><input type="checkbox"
                                                                       name="select" value="38">
                                    </div>
                                </td>
                                <td class="img pic_box">
                                    <img src="/u_file/2502/11/products/da7fe9c5dd.jpg?x-oss-process=image/format,webp/resize,m_lfit,,h_240,w_240"><span></span>
                                </td>
                                <td class="info">
                                    <div class="name">
                                        Retevis EZTalk 62 Dual Band VHF/UHF Waterproof 2 Way Radio for
                                        Hunting
                                    </div>
                                    <div class="classify color_888" cateid="3">
                                        Outdoor Two Way Radios
                                    </div>
                                </td>
                                <td nowrap="">2</td>
                                <td nowrap="">0.0</td>
                                <td nowrap="">0</td>
                                <td nowrap="">0</td>
                                <td nowrap="">否</td>
                                <td class="operation">
                                    <a class="development_btn" data-proid="38" href="javascript:;">添加虚拟数据</a> <br>
                                    <a href="/manage/plugins/custom-comments/list/?ProId=38">自定义评论列表</a>
                                </td>
                            </tr>
                            <tr data-id="37">
                                <td>
                                    <div class="btn_checkbox ">
                                        <em class="button"></em><input type="checkbox"
                                                                       name="select" value="37">
                                    </div>
                                </td>
                                <td class="img pic_box">
                                    <img src="/u_file/2502/11/products/1322ced26c.jpg?x-oss-process=image/format,webp/resize,m_lfit,,h_240,w_240"><span></span>
                                </td>
                                <td class="info">
                                    <div class="name">RA79 50-600MHz RX</div>
                                    <div class="classify color_888" cateid="7">
                                        Amateur Radios / HAM Radios &gt; Analog Radios
                                    </div>
                                </td>
                                <td nowrap="">5</td>
                                <td nowrap="">0.0</td>
                                <td nowrap="">0</td>
                                <td nowrap="">0</td>
                                <td nowrap="">否</td>
                                <td class="operation">
                                    <a class="development_btn" data-proid="37" href="javascript:;">添加虚拟数据</a> <br>
                                    <a href="/manage/plugins/custom-comments/list/?ProId=37">自定义评论列表</a>
                                </td>
                            </tr>
                            <tr data-id="36">
                                <td>
                                    <div class="btn_checkbox ">
                                        <em class="button"></em><input type="checkbox"
                                                                       name="select" value="36">
                                    </div>
                                </td>
                                <td class="img pic_box">
                                    <img src="/u_file/2501/08/products/85b382aa32.jpg?x-oss-process=image/format,webp/resize,m_lfit,,h_240,w_240"><span></span>
                                </td>
                                <td class="info">
                                    <div class="name">产品详情样式-轮播展示</div>
                                    <div class="classify color_888" cateid="0">
                                    </div>
                                </td>
                                <td nowrap="">0</td>
                                <td nowrap="">0.0</td>
                                <td nowrap="">0</td>
                                <td nowrap="">0</td>
                                <td nowrap="">否</td>
                                <td class="operation">
                                    <a class="development_btn" data-proid="36" href="javascript:;">添加虚拟数据</a> <br>
                                    <a href="/manage/plugins/custom-comments/list/?ProId=36">自定义评论列表</a>
                                </td>
                            </tr>
                            <tr data-id="35">
                                <td>
                                    <div class="btn_checkbox ">
                                        <em class="button"></em><input type="checkbox"
                                                                       name="select" value="35">
                                    </div>
                                </td>
                                <td class="img pic_box">
                                    <img src="/u_file/2501/08/products/f8ebbee207.jpg?x-oss-process=image/format,webp/resize,m_lfit,,h_240,w_240"><span></span>
                                </td>
                                <td class="info">
                                    <div class="name">产品详情样式-按钮带轮播</div>
                                    <div class="classify color_888" cateid="2">
                                        On-site business radios
                                    </div>
                                </td>
                                <td nowrap="">0</td>
                                <td nowrap="">0.0</td>
                                <td nowrap="">0</td>
                                <td nowrap="">0</td>
                                <td nowrap="">否</td>
                                <td class="operation">
                                    <a class="development_btn" data-proid="35" href="javascript:;">添加虚拟数据</a> <br>
                                    <a href="/manage/plugins/custom-comments/list/?ProId=35">自定义评论列表</a>
                                </td>
                            </tr>
                            <tr data-id="34">
                                <td>
                                    <div class="btn_checkbox ">
                                        <em class="button"></em><input type="checkbox"
                                                                       name="select" value="34">
                                    </div>
                                </td>
                                <td class="img pic_box">
                                    <img src="/u_file/2501/07/products/34271c9b53.jpg?x-oss-process=image/format,webp/resize,m_lfit,,h_240,w_240"><span></span>
                                </td>
                                <td class="info">
                                    <div class="name">产品详情样式-一行三图文</div>
                                    <div class="classify color_888" cateid="16">
                                        Professional DMR radios &gt; Portable DMR radios
                                    </div>
                                </td>
                                <td nowrap="">0</td>
                                <td nowrap="">0.0</td>
                                <td nowrap="">0</td>
                                <td nowrap="">0</td>
                                <td nowrap="">否</td>
                                <td class="operation">
                                    <a class="development_btn" data-proid="34" href="javascript:;">添加虚拟数据</a> <br>
                                    <a href="/manage/plugins/custom-comments/list/?ProId=34">自定义评论列表</a>
                                </td>
                            </tr>
                            <tr data-id="33">
                                <td>
                                    <div class="btn_checkbox ">
                                        <em class="button"></em><input type="checkbox"
                                                                       name="select" value="33">
                                    </div>
                                </td>
                                <td class="img pic_box">
                                    <img src="/u_file/2501/07/products/14208bc024.jpg?x-oss-process=image/format,webp/resize,m_lfit,,h_240,w_240"><span></span>
                                </td>
                                <td class="info">
                                    <div class="name">产品详情样式-一行双图</div>
                                    <div class="classify color_888" cateid="0">
                                    </div>
                                </td>
                                <td nowrap="">0</td>
                                <td nowrap="">0.0</td>
                                <td nowrap="">0</td>
                                <td nowrap="">0</td>
                                <td nowrap="">否</td>
                                <td class="operation">
                                    <a class="development_btn" data-proid="33" href="javascript:;">添加虚拟数据</a> <br>
                                    <a href="/manage/plugins/custom-comments/list/?ProId=33">自定义评论列表</a>
                                </td>
                            </tr>
                            <tr data-id="32">
                                <td>
                                    <div class="btn_checkbox ">
                                        <em class="button"></em><input type="checkbox"
                                                                       name="select" value="32">
                                    </div>
                                </td>
                                <td class="img pic_box">
                                    <img src="/u_file/2501/06/products/5dc16ed96f.jpg?x-oss-process=image/format,webp/resize,m_lfit,,h_240,w_240"><span></span>
                                </td>
                                <td class="info">
                                    <div class="name">产品详情样式-图文（左图右文）</div>
                                    <div class="classify color_888" cateid="14">
                                        On-site business radios &gt; License-free Business Radios
                                    </div>
                                </td>
                                <td nowrap="">0</td>
                                <td nowrap="">0.0</td>
                                <td nowrap="">0</td>
                                <td nowrap="">0</td>
                                <td nowrap="">否</td>
                                <td class="operation">
                                    <a class="development_btn" data-proid="32" href="javascript:;">添加虚拟数据</a> <br>
                                    <a href="/manage/plugins/custom-comments/list/?ProId=32">自定义评论列表</a>
                                </td>
                            </tr>
                            <tr data-id="31">
                                <td>
                                    <div class="btn_checkbox ">
                                        <em class="button"></em><input type="checkbox"
                                                                       name="select" value="31">
                                    </div>
                                </td>
                                <td class="img pic_box">
                                    <img src="/u_file/2501/06/products/4733985178.jpg?x-oss-process=image/format,webp/resize,m_lfit,,h_240,w_240"><span></span>
                                </td>
                                <td class="info">
                                    <div class="name">产品详情样式-图文（左文右图）</div>
                                    <div class="classify color_888" cateid="19">
                                        On-site business radios &gt; Analog Business Radios
                                    </div>
                                </td>
                                <td nowrap="">0</td>
                                <td nowrap="">0.0</td>
                                <td nowrap="">0</td>
                                <td nowrap="">0</td>
                                <td nowrap="">否</td>
                                <td class="operation">
                                    <a class="development_btn" data-proid="31" href="javascript:;">添加虚拟数据</a> <br>
                                    <a href="/manage/plugins/custom-comments/list/?ProId=31">自定义评论列表</a>
                                </td>
                            </tr>
                            <tr data-id="30">
                                <td>
                                    <div class="btn_checkbox ">
                                        <em class="button"></em><input type="checkbox"
                                                                       name="select" value="30">
                                    </div>
                                </td>
                                <td class="img pic_box">
                                    <img src="/u_file/2501/06/products/7745a0689f.jpg?x-oss-process=image/format,webp/resize,m_lfit,,h_240,w_240"><span></span>
                                </td>
                                <td class="info">
                                    <div class="name">产品详情样式-图文（上图下文）</div>
                                    <div class="classify color_888" cateid="9">
                                        Amateur Radios / HAM Radios &gt; Base Station Radios
                                    </div>
                                </td>
                                <td nowrap="">0</td>
                                <td nowrap="">0.0</td>
                                <td nowrap="">0</td>
                                <td nowrap="">0</td>
                                <td nowrap="">否</td>
                                <td class="operation">
                                    <a class="development_btn" data-proid="30" href="javascript:;">添加虚拟数据</a> <br>
                                    <a href="/manage/plugins/custom-comments/list/?ProId=30">自定义评论列表</a>
                                </td>
                            </tr>
                            <tr data-id="29">
                                <td>
                                    <div class="btn_checkbox ">
                                        <em class="button"></em><input type="checkbox"
                                                                       name="select" value="29">
                                    </div>
                                </td>
                                <td class="img pic_box">
                                    <img src="/u_file/2501/06/products/fb28eedef7.jpg?x-oss-process=image/format,webp/resize,m_lfit,,h_240,w_240"><span></span>
                                </td>
                                <td class="info">
                                    <div class="name">产品详情样式-图文（上文下图）</div>
                                    <div class="classify color_888" cateid="7">
                                        Amateur Radios / HAM Radios &gt; Analog Radios
                                    </div>
                                </td>
                                <td nowrap="">0</td>
                                <td nowrap="">0.0</td>
                                <td nowrap="">0</td>
                                <td nowrap="">0</td>
                                <td nowrap="">否</td>
                                <td class="operation">
                                    <a class="development_btn" data-proid="29" href="javascript:;">添加虚拟数据</a> <br>
                                    <a href="/manage/plugins/custom-comments/list/?ProId=29">自定义评论列表</a>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                <div class="scroll_sticky">
                    <div class="scroll_sticky_content">
                        <div style="width: 1976px; height: 1px;"></div>
                    </div>
                </div>
                <div id="turn_page" data-current="0" data-count="1">
                    <div class="total_page">共 12 条</div>
                </div>
            </form>
        </div>
    </div>
    <div id="fixed_right">
        <div class="global_container products_development" data-width="350">
            <div class="top_title">添加虚拟数据 <a href="javascript:;" class="close"></a></div>
            <form class="global_form" id="products_development_form">
                <div class="box_explain">当真实数据大于填写的数据时,使用真实的数据</div>
                <div class="rows clean">
                    <label>使用虚拟数据代替真实的数据</label>
                    <div class="input">
                        <div class="blank6"></div>
                        <div class="switchery">
                            <input type="checkbox" name="IsVirtual" value="1">
                            <div class="switchery_toggler"></div>
                            <div class="switchery_inner">
                                <div class="switchery_state_on"></div>
                                <div class="switchery_state_off"></div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="virtual_box">
                    <div class="rows clean">
                        <label>产品销量</label>
                        <div class="input">
                            <input type="text" class="box_input full_input" name="Sales" value="" size="10"
                                   maxlength="255" rel="amount">
                        </div>
                    </div>
                    <div class="rows clean">
                        <label>评论平均分</label>
                        <div class="input">
                            <input type="text" class="box_input full_input" name="DefaultReviewRating" value=""
                                   size="10" maxlength="4" rel="amount">
                            <div class="error_tips"></div>
                        </div>
                    </div>
                    <div class="rows clean">
                        <label>评论次数</label>
                        <div class="input">
                            <input type="text" class="box_input full_input" name="DefaultReviewTotalRating" value=""
                                   size="10" maxlength="255" rel="amount">
                        </div>
                    </div>
                    <div class="rows clean">
                        <label>收藏数量</label>
                        <div class="input">
                            <input type="text" class="box_input full_input" name="FavoriteCount" value="" size="10"
                                   maxlength="255" rel="amount">
                        </div>
                    </div>
                </div>
                <div class="rows clean box_submit">
                    <label></label>
                    <div class="input">
                        <input type="submit" class="btn_global btn_submit" value="保存">
                        <input type="button" class="btn_global btn_cancel" value="取消">
                    </div>
                </div>
                <input type="hidden" name="ProId" value="">
                <input type="hidden" name="do_action" value="/manage/plugins/custom-comments/products-development">
            </form>
        </div>
    </div>

</div>