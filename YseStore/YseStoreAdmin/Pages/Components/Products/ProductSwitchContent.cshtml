@model YseStoreAdmin.Pages.Components.Products.ProductSwitchContent
@{
}
<div>

    <div id="products_switch" class="r_con_wrap plugins_app_box" style="height: 409px;">

        <div class="inside_container inside_menu_right clean">

            <h1>详情切换卡</h1>
        </div>
        <div>
            <div class="inside_table radius">
                <ul class="new_plugins_app_menu">
                    <li><a href="/Products/Switch">切换卡</a></li>
                    <li><a href="javascript:;" class="current">内容</a></li>
                </ul>
                <div class="list_menu app_list_menu">
                    <ul class="list_menu_button fr">
                        <li>
                            <a class="add btn_switch_edit"
                               href="/Products/SwitchContentView?id=0">添加</a>
                        </li>
                    </ul>
                    <div class="search_form fl">
                        <form id="w0" method="get">
                            <div class="k_input">
                                <input type="text" name="Keyword" value="@Model.Keyword" placeholder="请输入关键词"
                                       class="form_input long_form_input" size="15" autocomplete="off">
                            </div>
                            <input type="submit" class="search_btn" value="搜索"
                                   onclick="searchSwitchContent(); return false;">
                            <div class="clear"></div>
                        </form>
                    </div>
                </div>
                <table border="0" cellpadding="5" cellspacing="0" class="r_con_table">
                    <thead>
                    <tr>
                        <td width="7%" nowrap="nowrap" class="pos">
                            <ul class="table_menu_button global_menu_button">
                                <li>
                                    <div class="btn_checkbox ">
                                        <em class="button"></em><input type="checkbox"
                                                                       name="select_all" value="">
                                    </div>
                                </li>
                                <li class="open">已选择<span></span>个</li>
                                <li><a class="del btn_switch_content_delete" href="javascript:;">删除</a></li>
                            </ul>
                        </td>
                        <td width="45%" nowrap="nowrap">名称</td>
                        <td width="45%" nowrap="nowrap">隶属切换卡</td>
                        <td width="200" nowrap="nowrap" class="operation"></td>
                    </tr>
                    </thead>
                    <tbody data-listidx="0">
                    @foreach (var item in Model.ProductSwitchContents)
                    {
                        <tr style="cursor: pointer;">
                            <td nowrap="nowrap">
                                <div class="btn_checkbox ">
                                    <em class="button"></em><input type="checkbox" name="select"
                                                                   value="@item.CId">
                                </div>
                            </td>
                            <td nowrap="nowrap" class="name">@item.Name</td>
                            <td nowrap="nowrap" class="apply">
                                @Model.GetSwitchName(item.SId)
                            </td>
                            <td nowrap="nowrap" class="operation tar">
                                <a class="icon_edit oper_icon button_tips btn_switch_edit"
                                   href="/Products/SwitchContentView?id=@item.CId&backSwitchId=@Model.BackSwitchId">修改</a>
                            </td>
                        </tr>
                    }
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <script>
        function searchSwitchContent() {
            var keyword = document.querySelector('input[name="Keyword"]').value;

            // 调用搜索API
            window.location.href = '/Products/SwitchContent?keyword=' + encodeURIComponent(keyword);

            return false;
        }
    </script>
</div>