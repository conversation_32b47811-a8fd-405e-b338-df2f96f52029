
@model YseStoreAdmin.Pages.Components.Products.RecommendSetEdit
@{
}
<div>

    <div id="products_recommend_inside" class="r_con_wrap blog plugins_app_box" style="height: 358px;">
        <div class="return_title">
            <a href="/Products/RecommendSet">
                <span class="return">推荐产品</span>
                <span class="s_return">/ @(Model.IsEdit ? "编辑" : "添加")</span>
            </a>
        </div>
        <div class="inside_table">
            <form class="global_form" id="edit_form">
                <div class="global_container">
                    <div class="recommend_top_title">推荐集</div>
                    <div class="rows clean">
                        <label>标题</label>
                        <div class="input">
                            <input type="text" name="Data[Title]" class="box_input full_input" maxlength="255"
                                   value="@Model.Title"
                                   notnull="" size="20">
                        </div>
                    </div>
                    <div class="blank20"></div>
                    <div class="products_box rows clean">
                        <label>
                            产品
                            <div id="add_btn">
                                添加
                            </div>
                        </label>
                        <div id="products_box">
                            <div class="products_list">
                                @if (Model.RecommendSetProductsWithImages.Any())
                                {
                                    @foreach (var product in Model.RecommendSetProductsWithImages)
                                    {
                                        <div class="item" data-proid="@product.ProId">
                                            <div class="img">
                                                @{
                                                    var imageUrl = product.ImagePath;
                                                    // 为非默认图片添加OSS处理参数
                                                    if (!string.IsNullOrEmpty(imageUrl) && !imageUrl.StartsWith("/assets/"))
                                                    {
                                                        imageUrl += "?x-oss-process=image/format,webp/resize,m_lfit,h_120,w_120/quality,q_90";
                                                    }
                                                }
                                                <img src="@imageUrl" alt="@product.Name">
                                                <span></span>
                                            </div>
                                            <div class="name">@product.Name</div>
                                            <a href="javascript:;" class="icon_multi_delete p_del"></a>
                                            <a href="javascript:;" class="p_top"></a>
                                            <input type="hidden" name="ProId[]" value="@product.ProId">
                                        </div>
                                    }
                                }
                            </div>
                            <div class="no_data"
                                 style="display: @(Model.RecommendSetProductsWithImages == null || !Model.RecommendSetProductsWithImages.Any() ? "block" : "none")">
                                当前暂时没有数据
                            </div>
                        </div>
                    </div>

                </div>

                <div class="global_container">
                    <div class="recommend_top_title">推荐产品</div>
                    <div class="rows clean type_box">
                        <label>类型</label>
                        <div class="clear"></div>
                        <div class="list fill">
                            <span
                                class="input_radio_box input_radio_side_box tab_option fl @(Model.RecommendData?.ProductsScope != "associate" ? "checked" : "")"
                                data-type="cross"
                                title="">
                                <span class="input_radio">
                                    <input type="radio" name="ProductsScope" value="cross"
                                           @(Model.RecommendData?.ProductsScope != "associate" ? "checked" : "")>
                                </span>
                                <strong class="fs14">交叉推荐</strong>
                                <p class="fs12">推荐集里的产品互相推荐</p>
                            </span>
                            <span
                                class="input_radio_box input_radio_side_box tab_option fl show_scope @(Model.RecommendData?.ProductsScope == "associate" ? "checked" : "")"
                                data-type="associate" title="">
                                <span class="input_radio">
                                    <input type="radio" name="ProductsScope" value="associate"
                                           @(Model.RecommendData?.ProductsScope == "associate" ? "checked" : "")>
                                </span>
                                <strong class="fs14">关联推荐</strong>
                                <p class="fs12">将推荐集关联到指定产品</p>
                            </span>
                        </div>
                        <div class="clear"></div>
                    </div>


                    <div class="rows clean scope_box" data-type="cross"
                         style="display: @(Model.RecommendData?.ProductsScope != "associate" ? "block" : "none");">
                    </div>
                    <div class="rows clean scope_box" data-type="associate"
                         style="display: @(Model.RecommendData?.ProductsScope == "associate" ? "block" : "none");">
                        <div class="rows clean">
                            <label>指定产品</label>
                            <div class="tags_row clean">
                                <dl class="box_drop_double" data-checkbox="1" data-showadd="1">
                                    <dt class="box_checkbox_list">
                                        <div class="select_placeholder"
                                             style="@(Model.ProductSelected != null && Model.ProductSelected.Any() ? "display:none" : "")">
                                            请选择
                                        </div>
                                        <div class="select_list">
                                            @if (Model.ProductSelected != null && Model.ProductSelected.Any())
                                            {
                                                @foreach (var product in Model.ProductSelected)
                                                {
                                                    <span class="btn_attr_choice current" data-value="@product.Value"
                                                          data-type="@product.Type">
                                                        <b>@product.Name</b>
                                                        <input type="checkbox" name="productsCurrent[]"
                                                               value="@product.Value" class="option_current" checked/>
                                                        <input type="hidden" name="productsOption[]"
                                                               value="@product.Value"/>
                                                        <i class="del">×</i>
                                                    </span>
                                                }
                                            }
                                        </div>
                                        <input type="text" class="box_input check_input"
                                               name="ProductsValue" value="" placeholder="" size="30" maxlength="255"
                                               autocomplete="off">
                                        <input type="hidden" name="Products" value="" class="hidden_value">
                                        <input type="hidden" name="ProductsType" value="" class="hisdden_type">
                                    </dt>
                                    <dd class="drop_down" style="display: none;">
                                        <div class="drop_menu" data-type="">
                                            <a href="javascript:;" class="btn_back"
                                               data-value="" data-type="" data-table="" data-top="0" data-all="0"
                                               style="display:none;">返回</a>
                                            <div class="drop_skin" style="display: none;"></div>
                                            <div class="drop_list"
                                                 data="@Model.ProductSelect"
                                                 data-more="@(Model.ProductSelectItems != null && Model.ProductSelectItems.Count >= 20 ? "block" : "none")">


                                            </div>
                                            <a href="javascript:;" class="btn_load_more" data-value="products"
                                               data-type="products" data-table="products" data-top="0" data-all="1"
                                               data-check-all="0" data-start="1"
                                               style="display:@(Model.ProductSelectItems != null && Model.ProductSelectItems.Count >= 20 ? "block" : "none");">加载更多</a>
                                        </div>
                                    </dd>
                                </dl>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="global_container">
                    <div class="recommend_top_title">页面展示</div>

                    <div class="rows clean">
                        <label>位置</label>
                        <div class="input position_box">
                            <span
                                class="input_radio_box input_radio_side_box tab_option fl @(Model.ShowPosition == "themes_global_point" ? "checked" : "")">
                                <span class="input_radio">
                                    <input type="radio" name="ShowPosition" value="themes_global_point"
                                           @(Model.ShowPosition == "themes_global_point" ? "checked" : "")>
                                </span>
                                <strong class="fs14">产品详细页的底部</strong>
                            </span>
                            <span
                                class="input_radio_box input_radio_side_box tab_option fl @(Model.ShowPosition == "themes_pdetial_attr_button" ? "checked" : "")">
                                <span class="input_radio">
                                    <input type="radio" name="ShowPosition" value="themes_pdetial_attr_button"
                                           @(Model.ShowPosition == "themes_pdetial_attr_button" ? "checked" : "")>
                                </span>
                                <strong class="fs14">产品详细页的产品购买模块</strong>
                            </span>
                        </div>
                    </div>

                    <div class="rows clean style_box"
                         style="display: @(Model.ShowPosition == "themes_global_point" ? "none" : "block");">
                        <label>风格</label>
                        <div class="input">
                            <span
                                class="input_radio_box input_radio_side_box tab_option fl @(Model.Mode == "mode_1" ? "checked" : "")">
                                <span class="input_radio">
                                    <input type="radio" name="Mode" value="mode_1"
                                           @(Model.Mode == "mode_1" ? "checked" : "")>
                                </span>
                                <strong class="fs14">产品图片</strong>
                                <p class="fs12">
                                    <img src="/manage/web/shop/images/plugins/app/products_recommend/style-mode_1.jpg">
                                </p>
                            </span>
                            <span
                                class="input_radio_box input_radio_side_box tab_option fl @(Model.Mode == "mode_2" ? "checked" : "")">
                                <span class="input_radio">
                                    <input type="radio" name="Mode" value="mode_2"
                                           @(Model.Mode == "mode_2" ? "checked" : "")>
                                </span>
                                <strong class="fs14">产品图片+产品名称</strong>
                                <p class="fs12">
                                    <img src="/manage/web/shop/images/plugins/app/products_recommend/style-mode_2.jpg">
                                </p>
                            </span>
                        </div>
                    </div>

                </div>


                <input type="hidden" name="Type" value="recommended_set">
                <input type="hidden" name="ShowType" value="recommended_set">
                <input type="hidden" name="Page[]" value="goods">
                <input type="hidden" name="id" id="id" value="@Model.RecommendId">
                <input type="hidden" name="do_action"
                       value="/manage/plugins/products-recommend/update?id=@Model.RecommendId">
                <input type="hidden" name="backUrl" value="/Products/RecommendSet">
            </form>
        </div>


    </div>

    <div id="fixed_right">
        <div class="global_container fixed_right_products_choice" data-width="350">
            <div class="top_title">添加产品<a href="javascript:;" class="close"></a></div>
            <div class="search_menu">
                <div class="search_form">
                    <form method="get" action="?" style="border-radius: 4px;">
                        <div class="k_input">
                            <input type="text" name="Keyword" value="" class="form_input" size="15"
                                   autocomplete="off" placeholder="请输入关键词/SKU"><input type="button" value=""
                                                                                            class="more"><input
                                type="submit" class="search_btn" value="">
                        </div>
                        <input type="button" class="new_filter_btn" value="筛选">
                        <div class="ext drop_down" style="display: none;">
                            <div class="rows item">
                                <label>产品分类</label><span class="input">
                                    <div class="box_select">
                                        <select name="CateId">
                                            <option value="">全部分类</option>
                                            <option value="15">├Radioguide</option>
                                            <option value="20">｜├T130P Radioguide per Gruppi</option>
                                            <option value="21">｜├T130S Whisper Tour Guide System</option>
                                            <option value="31">｜├TT106 Auricolari per Gruppi Turistici</option>
                                            <option value="28">｜├TT116 Attrezzature per Guide Turistiche</option>
                                            <option value="22">｜├TT127 Radio Guide Turistiche con Lettore MP3</option>
                                            <option value="23">
                                                ｜├TT126 Sistema di Comunicazione Radio Guida
                                                Bidirezionale
                                            </option>
                                            <option value="24">｜├T130U Guida Radio Digitale UHF</option>
                                            <option value="25">｜├TT128 Audioguide Musei</option>
                                            <option value="30">｜├T130 Radioguide per Gruppi</option>
                                            <option value="32">｜├TT112 Radioguida</option>
                                            <option value="34">｜├TT122 Sistemi Audio per Guide Turistiche</option>
                                            <option value="33">｜├TT120 Sistema Radio per Allenamento di Nuoto</option>
                                            <option value="29">｜└Accessori</option>
                                            <option value="16">├Sistema di Cercapersone</option>
                                            <option value="18">├Sistema di Chiamata</option>
                                            <option value="19">｜└cercapersone di base</option>
                                            <option value="26">├Ham Radio</option>
                                            <option value="27">├Nuove Uscite</option>
                                            <option value="35">└Hearing Aids</option>
                                        </select>
                                    </div>
                                </span>
                                <div class="clear"></div>
                            </div>
                        </div>
                        <div class="clear"></div>
                        <input type="hidden" name="m" value="plugins"><input type="hidden"
                                                                             name="a" value="products-recommend"><input
                            type="hidden" name="d" value="search"><input type="hidden" name="FilterCateId"
                                                                         value=""><input type="hidden"
                                                                                         name="FilterTagId" value="">
                    </form>
                </div>
            </div>
            <div class="clear"></div>
            <form id="fixed_right_products_choice_form">
                <div class="fixed_right_products_choice_jsppane">
                    <div class="fixed_right_products_choice_list"></div>
                </div>
                <div class="rows box_submit">
                    <label></label>
                    <div class="input">
                        <span class="select_all_box">
                            <span class="input_checkbox_box">
                                <span class="input_checkbox">
                                    <input type="checkbox" name="select_all_item"
                                           value="1">
                                </span> 全选
                            </span>
                        </span><input type="button"
                                      class="btn_global btn_submit" name="submit_button" value="添加"><span
                            class="hide">
                            <input type="button" class="btn_global btn_cancel" value="返回">
                        </span>
                    </div>
                </div>
                <input type="hidden" name="do_action" value="/manage/plugins/products-recommend/add-products/">
            </form>
        </div>
        <div class="global_container fixed_search_filter" data-width="350">
            <div class="top_title">筛选 <a href="javascript:;" class="close"></a></div>
            <div class="global_form">
                <div class="box_filter">
                    <div class="filter_list">
                        <div class="filter_title">分类</div>
                        <div class="filter_option">
                            <div class="filter_option_input">
                                <dl class="box_drop_double edit_box" data-checkbox="0" data-showadd="0">
                                    <dt>
                                        <input type="text" class="box_input" name="Select" placeholder="请选择或填写"
                                               value=""
                                               autocomplete="off"><input type="hidden" name="FilterCateId" value=""
                                                                         class="hidden_value"><input type="hidden"
                                                                                                     name="FilterCateIdType"
                                                                                                     value=""
                                                                                                     class="hidden_type">
                                    </dt>
                                    <dd class="drop_down" style="display: none;">
                                        <div class="drop_menu" data-type="Select">
                                            <a href="javascript:;"
                                               class="btn_back" data-value="" data-type="" data-table="" data-top="0"
                                               data-all="0" style="display:none;">返回</a>
                                            <div class="drop_skin" style="display: none;"></div>
                                            <div class="drop_list"
                                                 data="[{&quot;Name&quot;:&quot;Radioguide&quot;,&quot;Value&quot;:15,&quot;Type&quot;:&quot;products_category&quot;,&quot;Disabled&quot;:false,&quot;Table&quot;:&quot;products_category&quot;},{&quot;Name&quot;:&quot;Sistema di Cercapersone&quot;,&quot;Value&quot;:16,&quot;Type&quot;:&quot;products_category&quot;,&quot;Disabled&quot;:false},{&quot;Name&quot;:&quot;Sistema di Chiamata&quot;,&quot;Value&quot;:18,&quot;Type&quot;:&quot;products_category&quot;,&quot;Disabled&quot;:false,&quot;Table&quot;:&quot;products_category&quot;},{&quot;Name&quot;:&quot;Ham Radio&quot;,&quot;Value&quot;:26,&quot;Type&quot;:&quot;products_category&quot;,&quot;Disabled&quot;:false},{&quot;Name&quot;:&quot;Nuove Uscite&quot;,&quot;Value&quot;:27,&quot;Type&quot;:&quot;products_category&quot;,&quot;Disabled&quot;:false},{&quot;Name&quot;:&quot;Hearing Aids&quot;,&quot;Value&quot;:35,&quot;Type&quot;:&quot;products_category&quot;,&quot;Disabled&quot;:false}]"
                                                 data-more="none">
                                                <div class="item children" data-name="Radioguide" data-value="15"
                                                     data-type="products_category" data-table="products_category"
                                                     data-top="0" data-all="1" data-check-all="0"
                                                     data-custom="undefined">
                                                    <span class="input_radio_box">
                                                        <span class="input_radio">
                                                            <input type="radio"
                                                                   name="_DoubleOption[]" value="0">
                                                        </span>
                                                    </span><span class="item_name">Radioguide</span><em></em>
                                                </div>
                                                <div class="item" data-name="Sistema di Cercapersone" data-value="16"
                                                     data-type="products_category" data-table="undefined" data-top="0"
                                                     data-all="1" data-check-all="0" data-custom="undefined">
                                                    <span class="input_radio_box">
                                                        <span class="input_radio">
                                                            <input type="radio" name="_DoubleOption[]"
                                                                   value="1">
                                                        </span>
                                                    </span><span class="item_name">
                                                        Sistema
                                                        di Cercapersone
                                                    </span>
                                                </div>
                                                <div class="item children" data-name="Sistema di Chiamata"
                                                     data-value="18" data-type="products_category"
                                                     data-table="products_category" data-top="0" data-all="1"
                                                     data-check-all="0" data-custom="undefined">
                                                    <span class="input_radio_box">
                                                        <span class="input_radio">
                                                            <input type="radio" name="_DoubleOption[]"
                                                                   value="2">
                                                        </span>
                                                    </span><span class="item_name">
                                                        Sistema
                                                        di Chiamata
                                                    </span><em></em>
                                                </div>
                                                <div class="item" data-name="Ham Radio" data-value="26"
                                                     data-type="products_category" data-table="undefined" data-top="0"
                                                     data-all="1" data-check-all="0" data-custom="undefined">
                                                    <span class="input_radio_box">
                                                        <span class="input_radio">
                                                            <input type="radio" name="_DoubleOption[]"
                                                                   value="3">
                                                        </span>
                                                    </span><span class="item_name">
                                                        Ham
                                                        Radio
                                                    </span>
                                                </div>
                                                <div class="item" data-name="Nuove Uscite" data-value="27"
                                                     data-type="products_category" data-table="undefined" data-top="0"
                                                     data-all="1" data-check-all="0" data-custom="undefined">
                                                    <span class="input_radio_box">
                                                        <span class="input_radio">
                                                            <input type="radio" name="_DoubleOption[]"
                                                                   value="4">
                                                        </span>
                                                    </span><span class="item_name">
                                                        Nuove
                                                        Uscite
                                                    </span>
                                                </div>
                                                <div class="item" data-name="Hearing Aids" data-value="35"
                                                     data-type="products_category" data-table="undefined" data-top="0"
                                                     data-all="1" data-check-all="0" data-custom="undefined">
                                                    <span class="input_radio_box">
                                                        <span class="input_radio">
                                                            <input type="radio" name="_DoubleOption[]"
                                                                   value="5">
                                                        </span>
                                                    </span><span class="item_name">
                                                        Hearing
                                                        Aids
                                                    </span>
                                                </div>
                                            </div>
                                            <a href="javascript:;" class="btn_load_more"
                                               data-value="products_category" data-type="products_category"
                                               data-table="products_category" data-top="0" data-all="1"
                                               data-check-all="0" data-start="1" style="display:none;">加载更多</a>
                                        </div>
                                    </dd>
                                </dl>
                            </div>
                            <div class="filter_clean">
                                <button>清除</button>
                            </div>
                        </div>
                    </div>
                    <div class="filter_list">
                        <div class="filter_title">标签</div>
                        <div class="filter_option">
                            <div class="filter_option_input">
                                <dl class="box_drop_double" data-checkbox="1" data-showadd="0">
                                    <dt class="box_checkbox_list">
                                        <div class="select_placeholder">请选择</div>
                                        <div class="select_list"></div>
                                        <input type="text" class="box_input check_input"
                                               name="Select" value="" placeholder="" size="30" maxlength="255"
                                               autocomplete="off"><input type="hidden" name="FilterTagId" value=""
                                                                         class="hidden_value"><input type="hidden"
                                                                                                     name="FilterTagIdType"
                                                                                                     value=""
                                                                                                     class="hisdden_type">
                                    </dt>
                                    <dd class="drop_down" style="display: none;">
                                        <div class="drop_menu" data-type="Select">
                                            <a href="javascript:;"
                                               class="btn_back" data-value="" data-type="" data-table="" data-top="0"
                                               data-all="0" style="display:none;">返回</a>
                                            <div class="drop_skin" style="display: none;"></div>
                                            <div class="drop_list"
                                                 data="[{&quot;Name&quot;:&quot;TT106S&quot;,&quot;Value&quot;:132,&quot;Type&quot;:&quot;products_tags&quot;},{&quot;Name&quot;:&quot;T131P&quot;,&quot;Value&quot;:131,&quot;Type&quot;:&quot;products_tags&quot;},{&quot;Name&quot;:&quot;cuffie a cancellazione di rumore&quot;,&quot;Value&quot;:130,&quot;Type&quot;:&quot;products_tags&quot;},{&quot;Name&quot;:&quot;TT126&quot;,&quot;Value&quot;:129,&quot;Type&quot;:&quot;products_tags&quot;},{&quot;Name&quot;:&quot;TT034&quot;,&quot;Value&quot;:128,&quot;Type&quot;:&quot;products_tags&quot;},{&quot;Name&quot;:&quot;apparecchi acustici invisibili&quot;,&quot;Value&quot;:127,&quot;Type&quot;:&quot;products_tags&quot;},{&quot;Name&quot;:&quot;apparecchi acustici intelligenti&quot;,&quot;Value&quot;:126,&quot;Type&quot;:&quot;products_tags&quot;},{&quot;Name&quot;:&quot;apparecchi acustici RIC&quot;,&quot;Value&quot;:125,&quot;Type&quot;:&quot;products_tags&quot;},{&quot;Name&quot;:&quot;Apparecchi acustici&quot;,&quot;Value&quot;:124,&quot;Type&quot;:&quot;products_tags&quot;},{&quot;Name&quot;:&quot;TE201&quot;,&quot;Value&quot;:123,&quot;Type&quot;:&quot;products_tags&quot;},{&quot;Name&quot;:&quot;apparecchi acustici da banco autoadattanti&quot;,&quot;Value&quot;:122,&quot;Type&quot;:&quot;products_tags&quot;},{&quot;Name&quot;:&quot;dispositivi acustici tws&quot;,&quot;Value&quot;:121,&quot;Type&quot;:&quot;products_tags&quot;},{&quot;Name&quot;:&quot;dispositivi acustici OTC&quot;,&quot;Value&quot;:120,&quot;Type&quot;:&quot;products_tags&quot;},{&quot;Name&quot;:&quot;apparecchi acustici economici&quot;,&quot;Value&quot;:119,&quot;Type&quot;:&quot;products_tags&quot;},{&quot;Name&quot;:&quot;apparecchi acustici senza prescrizione&quot;,&quot;Value&quot;:118,&quot;Type&quot;:&quot;products_tags&quot;},{&quot;Name&quot;:&quot;tt120&quot;,&quot;Value&quot;:117,&quot;Type&quot;:&quot;products_tags&quot;},{&quot;Name&quot;:&quot;tt116&quot;,&quot;Value&quot;:116,&quot;Type&quot;:&quot;products_tags&quot;},{&quot;Name&quot;:&quot;td029&quot;,&quot;Value&quot;:115,&quot;Type&quot;:&quot;products_tags&quot;},{&quot;Name&quot;:&quot;td113&quot;,&quot;Value&quot;:114,&quot;Type&quot;:&quot;products_tags&quot;},{&quot;Name&quot;:&quot;tt127&quot;,&quot;Value&quot;:113,&quot;Type&quot;:&quot;products_tags&quot;}]"
                                                 data-more="block">
                                                <div class="item" data-name="TT106S" data-value="132"
                                                     data-type="products_tags" data-table="undefined" data-top="0"
                                                     data-all="1" data-check-all="0" data-custom="undefined">
                                                    <span class="input_checkbox_box">
                                                        <span class="input_checkbox">
                                                            <input type="checkbox" name="_DoubleOption[]"
                                                                   value="0">
                                                        </span>
                                                    </span><span class="item_name">TT106S</span>
                                                </div>
                                                <div class="item" data-name="T131P" data-value="131"
                                                     data-type="products_tags" data-table="undefined" data-top="0"
                                                     data-all="1" data-check-all="0" data-custom="undefined">
                                                    <span class="input_checkbox_box">
                                                        <span class="input_checkbox">
                                                            <input type="checkbox" name="_DoubleOption[]"
                                                                   value="1">
                                                        </span>
                                                    </span><span class="item_name">T131P</span>
                                                </div>
                                                <div class="item" data-name="cuffie a cancellazione di rumore"
                                                     data-value="130" data-type="products_tags" data-table="undefined"
                                                     data-top="0" data-all="1" data-check-all="0"
                                                     data-custom="undefined">
                                                    <span class="input_checkbox_box">
                                                        <span class="input_checkbox">
                                                            <input type="checkbox"
                                                                   name="_DoubleOption[]" value="2">
                                                        </span>
                                                    </span><span
                                                        class="item_name">cuffie a cancellazione di rumore</span>
                                                </div>
                                                <div class="item" data-name="TT126" data-value="129"
                                                     data-type="products_tags" data-table="undefined" data-top="0"
                                                     data-all="1" data-check-all="0" data-custom="undefined">
                                                    <span class="input_checkbox_box">
                                                        <span class="input_checkbox">
                                                            <input type="checkbox" name="_DoubleOption[]"
                                                                   value="3">
                                                        </span>
                                                    </span><span class="item_name">TT126</span>
                                                </div>
                                                <div class="item" data-name="TT034" data-value="128"
                                                     data-type="products_tags" data-table="undefined" data-top="0"
                                                     data-all="1" data-check-all="0" data-custom="undefined">
                                                    <span class="input_checkbox_box">
                                                        <span class="input_checkbox">
                                                            <input type="checkbox" name="_DoubleOption[]"
                                                                   value="4">
                                                        </span>
                                                    </span><span class="item_name">TT034</span>
                                                </div>
                                                <div class="item" data-name="apparecchi acustici invisibili"
                                                     data-value="127" data-type="products_tags" data-table="undefined"
                                                     data-top="0" data-all="1" data-check-all="0"
                                                     data-custom="undefined">
                                                    <span class="input_checkbox_box">
                                                        <span class="input_checkbox">
                                                            <input type="checkbox"
                                                                   name="_DoubleOption[]" value="5">
                                                        </span>
                                                    </span><span class="item_name">apparecchi acustici invisibili</span>
                                                </div>
                                                <div class="item" data-name="apparecchi acustici intelligenti"
                                                     data-value="126" data-type="products_tags" data-table="undefined"
                                                     data-top="0" data-all="1" data-check-all="0"
                                                     data-custom="undefined">
                                                    <span class="input_checkbox_box">
                                                        <span class="input_checkbox">
                                                            <input type="checkbox"
                                                                   name="_DoubleOption[]" value="6">
                                                        </span>
                                                    </span><span
                                                        class="item_name">apparecchi acustici intelligenti</span>
                                                </div>
                                                <div class="item" data-name="apparecchi acustici RIC" data-value="125"
                                                     data-type="products_tags" data-table="undefined" data-top="0"
                                                     data-all="1" data-check-all="0" data-custom="undefined">
                                                    <span class="input_checkbox_box">
                                                        <span class="input_checkbox">
                                                            <input type="checkbox" name="_DoubleOption[]"
                                                                   value="7">
                                                        </span>
                                                    </span><span class="item_name">apparecchi acustici RIC</span>
                                                </div>
                                                <div class="item" data-name="Apparecchi acustici" data-value="124"
                                                     data-type="products_tags" data-table="undefined" data-top="0"
                                                     data-all="1" data-check-all="0" data-custom="undefined">
                                                    <span class="input_checkbox_box">
                                                        <span class="input_checkbox">
                                                            <input type="checkbox" name="_DoubleOption[]"
                                                                   value="8">
                                                        </span>
                                                    </span><span class="item_name">Apparecchi acustici</span>
                                                </div>
                                                <div class="item" data-name="TE201" data-value="123"
                                                     data-type="products_tags" data-table="undefined" data-top="0"
                                                     data-all="1" data-check-all="0" data-custom="undefined">
                                                    <span class="input_checkbox_box">
                                                        <span class="input_checkbox">
                                                            <input type="checkbox" name="_DoubleOption[]"
                                                                   value="9">
                                                        </span>
                                                    </span><span class="item_name">TE201</span>
                                                </div>
                                                <div class="item" data-name="apparecchi acustici da banco autoadattanti"
                                                     data-value="122" data-type="products_tags" data-table="undefined"
                                                     data-top="0" data-all="1" data-check-all="0"
                                                     data-custom="undefined">
                                                    <span class="input_checkbox_box">
                                                        <span class="input_checkbox">
                                                            <input type="checkbox"
                                                                   name="_DoubleOption[]" value="10">
                                                        </span>
                                                    </span><span class="item_name">
                                                        apparecchi acustici da banco
                                                        autoadattanti
                                                    </span>
                                                </div>
                                                <div class="item" data-name="dispositivi acustici tws" data-value="121"
                                                     data-type="products_tags" data-table="undefined" data-top="0"
                                                     data-all="1" data-check-all="0" data-custom="undefined">
                                                    <span class="input_checkbox_box">
                                                        <span class="input_checkbox">
                                                            <input type="checkbox" name="_DoubleOption[]"
                                                                   value="11">
                                                        </span>
                                                    </span><span class="item_name">dispositivi acustici tws</span>
                                                </div>
                                                <div class="item" data-name="dispositivi acustici OTC" data-value="120"
                                                     data-type="products_tags" data-table="undefined" data-top="0"
                                                     data-all="1" data-check-all="0" data-custom="undefined">
                                                    <span class="input_checkbox_box">
                                                        <span class="input_checkbox">
                                                            <input type="checkbox" name="_DoubleOption[]"
                                                                   value="12">
                                                        </span>
                                                    </span><span class="item_name">dispositivi acustici OTC</span>
                                                </div>
                                                <div class="item" data-name="apparecchi acustici economici"
                                                     data-value="119" data-type="products_tags" data-table="undefined"
                                                     data-top="0" data-all="1" data-check-all="0"
                                                     data-custom="undefined">
                                                    <span class="input_checkbox_box">
                                                        <span class="input_checkbox">
                                                            <input type="checkbox"
                                                                   name="_DoubleOption[]" value="13">
                                                        </span>
                                                    </span><span class="item_name">apparecchi acustici economici</span>
                                                </div>
                                                <div class="item" data-name="apparecchi acustici senza prescrizione"
                                                     data-value="118" data-type="products_tags" data-table="undefined"
                                                     data-top="0" data-all="1" data-check-all="0"
                                                     data-custom="undefined">
                                                    <span class="input_checkbox_box">
                                                        <span class="input_checkbox">
                                                            <input type="checkbox"
                                                                   name="_DoubleOption[]" value="14">
                                                        </span>
                                                    </span><span class="item_name">apparecchi acustici senza prescrizione</span>
                                                </div>
                                                <div class="item" data-name="tt120" data-value="117"
                                                     data-type="products_tags" data-table="undefined" data-top="0"
                                                     data-all="1" data-check-all="0" data-custom="undefined">
                                                    <span class="input_checkbox_box">
                                                        <span class="input_checkbox">
                                                            <input type="checkbox" name="_DoubleOption[]"
                                                                   value="15">
                                                        </span>
                                                    </span><span class="item_name">tt120</span>
                                                </div>
                                                <div class="item" data-name="tt116" data-value="116"
                                                     data-type="products_tags" data-table="undefined" data-top="0"
                                                     data-all="1" data-check-all="0" data-custom="undefined">
                                                    <span class="input_checkbox_box">
                                                        <span class="input_checkbox">
                                                            <input type="checkbox" name="_DoubleOption[]"
                                                                   value="16">
                                                        </span>
                                                    </span><span class="item_name">tt116</span>
                                                </div>
                                                <div class="item" data-name="td029" data-value="115"
                                                     data-type="products_tags" data-table="undefined" data-top="0"
                                                     data-all="1" data-check-all="0" data-custom="undefined">
                                                    <span class="input_checkbox_box">
                                                        <span class="input_checkbox">
                                                            <input type="checkbox" name="_DoubleOption[]"
                                                                   value="17">
                                                        </span>
                                                    </span><span class="item_name">td029</span>
                                                </div>
                                                <div class="item" data-name="td113" data-value="114"
                                                     data-type="products_tags" data-table="undefined" data-top="0"
                                                     data-all="1" data-check-all="0" data-custom="undefined">
                                                    <span class="input_checkbox_box">
                                                        <span class="input_checkbox">
                                                            <input type="checkbox" name="_DoubleOption[]"
                                                                   value="18">
                                                        </span>
                                                    </span><span class="item_name">td113</span>
                                                </div>
                                                <div class="item" data-name="tt127" data-value="113"
                                                     data-type="products_tags" data-table="undefined" data-top="0"
                                                     data-all="1" data-check-all="0" data-custom="undefined">
                                                    <span class="input_checkbox_box">
                                                        <span class="input_checkbox">
                                                            <input type="checkbox" name="_DoubleOption[]"
                                                                   value="19">
                                                        </span>
                                                    </span><span class="item_name">tt127</span>
                                                </div>
                                            </div>
                                            <a href="javascript:;" class="btn_load_more"
                                               data-value="products_tags" data-type="products_tags"
                                               data-table="products_tags" data-top="0" data-all="1" data-check-all="0"
                                               data-start="1" style="display:block;">加载更多</a>
                                        </div>
                                    </dd>
                                </dl>
                            </div>
                            <div class="filter_clean">
                                <button>清除</button>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="rows clean box_button box_submit">
                    <div class="input">
                        <input type="button" class="btn_global btn_submit" value="筛选"> <input type="button"
                                                                                                class="btn_global btn_cancel"
                                                                                                value="取消">
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="rows clean fixed_btn_submit" style="width: 1868px; left: 180px;">
        <div class="center_container">
            <div class="input input_button">
                <input type="button" class="btn_global btn_submit" value="保存">
                <a href="/Products/RecommendSet">
                    <input type="button"
                           class="btn_global btn_cancel" value="返回">
                </a>
            </div>
        </div>
    </div>

</div>