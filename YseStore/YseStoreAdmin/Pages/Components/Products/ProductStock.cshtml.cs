using YseStore.IService.Products;
using YseStore.Model.RequestModels.Products;
using YseStore.Model.Response.Products;
using Microsoft.AspNetCore.Mvc.ViewFeatures;
using Microsoft.AspNetCore.Mvc.Rendering;

namespace YseStoreAdmin.Pages.Components.Products
{
    public class ProductStock : MComponent
    {
        private readonly IProductStockService _productStockService;
        private readonly ITempDataDictionaryFactory _tempDataDictionaryFactory;
        private readonly IHttpContextAccessor _httpContextAccessor;
        
        // 库存列表
        public PageModel<ProductStockResponse> StockData { get; set; }
        
        // 仓库列表，用于下拉选择
        public List<SelectListItem> WarehouseList { get; set; } = new List<SelectListItem>();
        
        // 搜索参数
        public string Keyword { get; set; } = "";
        public string Type { get; set; } = "";
        public int OvId { get; set; } = 0;
        public int Min { get; set; } = 0;
        public int Max { get; set; } = 0;
        public int Status { get; set; } = 1;
        
        // 分页参数
        public int PageNum { get; set; } = 1;
        public int PageSize { get; set; } = 10;
        
        public ProductStock(
            IProductStockService productStockService, 
            ITempDataDictionaryFactory tempDataDictionaryFactory,
            IHttpContextAccessor httpContextAccessor)
        {
            _productStockService = productStockService;
            _tempDataDictionaryFactory = tempDataDictionaryFactory;
            _httpContextAccessor = httpContextAccessor;

            // 确保StockData不为null
            StockData = new PageModel<ProductStockResponse>
            {
                data = new List<ProductStockResponse>(),
                dataCount = 0,
                page = 1,
                PageSize = 20
            };
        }
        
        public override async Task MountAsync()
        {
            // 获取TempData中的搜索参数
            var tempData = _tempDataDictionaryFactory.GetTempData(_httpContextAccessor.HttpContext);
            
            // 从URL参数中获取所有参数
            var request = _httpContextAccessor.HttpContext.Request;
            
            // 获取关键词
            if (request.Query.TryGetValue("keyword", out var keywordValue))
            {
                Keyword = keywordValue.ToString();
            }
            else
            {
                Keyword = tempData["StockSearchKeyword"]?.ToString() ?? "";
            }
            
            // 获取类型
            if (request.Query.TryGetValue("type", out var typeValue))
            {
                Type = typeValue.ToString();
            }
            else
            {
                Type = tempData["StockType"]?.ToString() ?? "";
            }
            
            // 获取仓库ID
            if (request.Query.TryGetValue("ovid", out var ovidValue) && int.TryParse(ovidValue, out int ovidFromUrl))
            {
                OvId = ovidFromUrl;
            }
            else if (int.TryParse(tempData["StockOvId"]?.ToString(), out int ovid))
            {
                OvId = ovid;
            }
            
            // 获取最小库存
            if (request.Query.TryGetValue("min", out var minValue) && int.TryParse(minValue, out int minFromUrl))
            {
                Min = minFromUrl;
            }
            else if (int.TryParse(tempData["StockMin"]?.ToString(), out int min))
            {
                Min = min;
            }
            
            // 获取最大库存
            if (request.Query.TryGetValue("max", out var maxValue) && int.TryParse(maxValue, out int maxFromUrl))
            {
                Max = maxFromUrl;
            }
            else if (int.TryParse(tempData["StockMax"]?.ToString(), out int max))
            {
                Max = max;
            }
            
            // 获取状态
            if (request.Query.TryGetValue("status", out var statusValue) && int.TryParse(statusValue, out int statusFromUrl))
            {
                Status = statusFromUrl;
            }
            else if (int.TryParse(tempData["StockStatus"]?.ToString(), out int statusFromTemp))
            {
                Status = statusFromTemp;
            }
            
            // 获取页码
            if (request.Query.TryGetValue("page", out var pageValue) && int.TryParse(pageValue, out int pageFromUrl))
            {
                PageNum = pageFromUrl;
            }
            else if (int.TryParse(tempData["StockPage"]?.ToString(), out int pageFromTemp))
            {
                PageNum = pageFromTemp;
            }
            
            // 获取页大小
            if (request.Query.TryGetValue("pagesize", out var pageSizeValue) && int.TryParse(pageSizeValue, out int pageSizeFromUrl))
            {
                PageSize = pageSizeFromUrl;
            }
            else if (int.TryParse(tempData["StockPageSize"]?.ToString(), out int pageSizeFromTemp))
            {
                PageSize = pageSizeFromTemp;
            }
            
            // 加载仓库列表
            await LoadWarehouseListAsync();
            
            // 创建查询参数对象
            var queryRequest = new ProductStockQueryRequest
            {
                Keyword = Keyword,
                Type = Type,
                OvId = OvId,
                Min = Min,
                Max = Max,
                Status = Status.ToString(),
                PageIndex = PageNum,
                PageSize = PageSize
            };
            
            // 加载库存数据
            await LoadStockDataAsync(queryRequest);
            
            await base.MountAsync();
        }
        
        /// <summary>
        /// 加载库存数据
        /// </summary>
        /// <param name="request">查询请求参数</param>
        /// <returns>异步任务</returns>
        public async Task LoadStockDataAsync(ProductStockQueryRequest request)
        {
            // 调用服务获取库存数据
            StockData = await _productStockService.GetProductStockList(request);
            
            // 确保StockData不为空
            if (StockData == null)
            {
                StockData = new PageModel<ProductStockResponse>
                {
                    data = new List<ProductStockResponse>(),
                    dataCount = 0,
                    page = PageNum,
                    PageSize = PageSize
                };
            }
            
            // 更新当前页码和页大小
            PageNum = StockData.page;
            PageSize = StockData.PageSize;
        }
        
        /// <summary>
        /// 加载仓库列表
        /// </summary>
        /// <returns>异步任务</returns>
        private async Task LoadWarehouseListAsync()
        {
            try
            {
                // 调用服务获取仓库列表
                WarehouseList = await _productStockService.GetWarehouseListAsync(OvId);
            }
            catch (Exception ex)
            {
            }
        }
    }
}
