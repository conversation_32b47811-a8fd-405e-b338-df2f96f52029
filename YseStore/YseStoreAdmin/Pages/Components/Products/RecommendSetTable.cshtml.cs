using Entitys;
using YseStore.IService.Products;
using YseStore.Model.Event;
using YseStore.Model.RequestModels.Products;
using Newtonsoft.Json;

namespace YseStoreAdmin.Pages.Components.Products
{
    /// <summary>
    /// 推荐集表格组件
    /// </summary>
    public class RecommendSetTable : MComponent
    {
        public int TotalCount { get; set; }
        public int PageSize { get; set; } = 10;
        public int PageNum { get; set; }
        public string _keyword { get; set; }
        public string _productsType { get; set; }
        public string _productsScope { get; set; }

        /// <summary>
        /// 排序字段
        /// </summary>
        public string SortField { get; set; } = "AccTime";

        /// <summary>
        /// 排序方向
        /// </summary>
        public string SortDirection { get; set; } = "DESC";

        private readonly IProductRecommendService _productRecommendService;
        private readonly ILogger<RecommendSetTable> _logger;

        public List<products_recommend> Recommends { get; set; } = new();

        public RecommendSetTable(
            IProductRecommendService productRecommendService,
            ILogger<RecommendSetTable> logger)
        {
            _productRecommendService = productRecommendService ??
                                       throw new ArgumentNullException(nameof(productRecommendService));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        // 处理用户分页事件
        public async Task HandleUserTurnPage(UserTurnPageEvent evt)
        {
            if (evt.Name == "recommendSetList")
            {
                await BindData(evt.PageNum);
            }
        }

        public override async Task MountAsync()
        {
            // 从查询参数获取筛选条件
            _keyword = Request.Query["keyword"].ToString();
            _productsType = Request.Query["productsType"].ToString();
            _productsScope = Request.Query["productsScope"].ToString();

            // 处理排序参数
            var sortParam = Request.Query["sort"].ToString();
            if (!string.IsNullOrEmpty(sortParam))
            {
                ParseSortParameter(sortParam);
            }

            await BindData();
        }

        [SkipOutput]
        public async Task BindData(int page = 1)
        {
            try
            {
                // 构建查询请求
                var request = new ProductRecommendQueryRequest
                {
                    PageIndex = page,
                    PageSize = PageSize > 0 ? PageSize : 10,
                    OrderByFileds = $"{SortField} {SortDirection}", // 使用动态排序
                    QueryType = "recommend_set" // 设置查询类型为推荐集
                };

                // 添加筛选条件
                if (!string.IsNullOrEmpty(_keyword))
                {
                    request.Keyword = _keyword;
                }

                if (!string.IsNullOrEmpty(_productsType))
                {
                    request.ProductsType = _productsType;
                }

                if (!string.IsNullOrEmpty(_productsScope))
                {
                    request.ProductsScope = _productsScope;
                }

                var result = await _productRecommendService.GetRecommendList(request);
                Recommends = result?.data ?? new List<products_recommend>();
                TotalCount = result?.dataCount ?? 0;
                PageNum = page;
                PageSize = request.PageSize;

                // 发送分页事件到全局
                DispatchGlobal<PageEvent>(new PageEvent(TotalCount, PageSize, PageNum, "recommendSetList"), null, true);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "加载推荐集表格数据失败");
            }
        }

        public void HandlePageEvent(PageEvent evt)
        {
            TotalCount = evt.Total;
            PageSize = evt.PageSize;
            PageNum = evt.PageNum;
        }

        [SkipOutput]
        public async Task TurnPage(int page)
        {
            DispatchGlobal<UserTurnPageEvent>(new UserTurnPageEvent("recommendSetList", page));
        }

        /// <summary>
        /// 解析排序参数
        /// </summary>
        /// <param name="sortParam">排序参数</param>
        private void ParseSortParameter(string sortParam)
        {
            if (string.IsNullOrEmpty(sortParam)) return;

            var parts = sortParam.Split('_');
            if (parts.Length == 2)
            {
                SortField = parts[0];
                SortDirection = parts[1].ToUpper() == "ASC" ? "ASC" : "DESC";
            }
        }

        /// <summary>
        /// 从Data字段中提取标题
        /// </summary>
        /// <param name="data">JSON数据</param>
        /// <returns>标题</returns>
        public string GetTitleFromData(string data)
        {
            try
            {
                if (string.IsNullOrEmpty(data))
                    return "未设置标题";

                var dataObj = JsonConvert.DeserializeObject<dynamic>(data);
                return dataObj?.Title?.ToString() ?? "未设置标题";
            }
            catch
            {
                return "未设置标题";
            }
        }

        /// <summary>
        /// ProductsValue字段中提取产品数量文本（用于products_txt显示）
        /// </summary>
        /// <param name="productsValue">ProductsValue字段值，格式如 |48|47|</param>
        /// <returns>产品数量文本，格式如 "2个产品"</returns>
        public string GetProductCountText(string productsValue)
        {
            try
            {
                if (string.IsNullOrEmpty(productsValue))
                    return "0个产品";

                int count = 0;

                // 处理 |48|47| 这种格式
                if (productsValue.StartsWith("|") && productsValue.EndsWith("|"))
                {
                    // 移除首尾的 | 符号，然后按 | 分割
                    var trimmed = productsValue.Trim('|');
                    if (!string.IsNullOrEmpty(trimmed))
                    {
                        var productIds = trimmed.Split('|', StringSplitOptions.RemoveEmptyEntries);
                        count = productIds.Length;
                    }
                }
                // 处理逗号分隔的格式
                else if (productsValue.Contains(","))
                {
                    var productIds = productsValue.Split(',', StringSplitOptions.RemoveEmptyEntries);
                    count = productIds.Length;
                }
                // 处理单个产品ID的情况
                else if (int.TryParse(productsValue, out _))
                {
                    count = 1;
                }

                return $"{count}个产品";
            }
            catch (Exception ex)
            {
                // 记录错误日志以便调试
                Console.WriteLine($"解析产品数量时出错: {ex.Message}, ProductsValue: {productsValue}");
                return "0个产品";
            }
        }

        /// <summary>
        /// 获取配置方式显示文本
        /// </summary>
        /// <param name="productsScope">配置方式</param>
        /// <returns>显示文本</returns>
        public string GetProductsTypeText(string productsScope)
        {
            return productsScope switch
            {
                "cross" => "交叉推荐",
                "associate" => "关联推荐",
                _ => productsScope ?? "未知"
            };
        }

        /// <summary>
        /// 获取显示页面文本
        /// </summary>
        /// <param name="pages">页面配置</param>
        /// <returns>页面文本</returns>
        public string GetPagesText(string pages)
        {
            if (string.IsNullOrEmpty(pages))
                return "未设置";

            try
            {
                List<string> pageArray;

                // 尝试解析JSON数组格式
                if (pages.StartsWith("[") && pages.EndsWith("]"))
                {
                    var jsonArray = JsonConvert.DeserializeObject<string[]>(pages);
                    pageArray = jsonArray?.ToList() ?? new List<string>();
                }
                else
                {
                    // 如果不是JSON格式，按逗号分隔
                    pageArray = pages.Split(',').ToList();
                }

                var pageTexts = new List<string>();

                foreach (var page in pageArray)
                {
                    var pageText = page.Trim().Trim('"') switch
                    {
                        "index" => "首页",
                        "list" => "产品列表页",
                        "goods" => "产品详细页",
                        "cart" => "购物车页",
                        "favorite" => "我的收藏页",
                        "search" => "搜索结果页",
                        "add_purchase_pop" => "加购弹窗",
                        "blog-list" => "博客列表页",
                        "blog-detail" => "博客详细页",
                        _ => page.Trim().Trim('"')
                    };
                    if (!string.IsNullOrEmpty(pageText))
                    {
                        pageTexts.Add(pageText);
                    }
                }

                return pageTexts.Any() ? string.Join(", ", pageTexts) : "未设置";
            }
            catch
            {
                return pages;
            }
        }
    }
}