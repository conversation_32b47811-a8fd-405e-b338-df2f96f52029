@model YseStoreAdmin.Pages.Components.Products.ProductList
@{
}
<div class="product-list-container">
	<div id="products" class="r_con_wrap" style="height: 380px;">
		<div class="inside_container inside_menu_right">
			<h1>产品管理</h1>
			<div class="inside_menu">
				<div class="inside_title"><span>上架</span><i></i></div>
				<div class="inside_body">
					<ul>
						<li><a href="/Products/Index?status=0" class="@((string.IsNullOrEmpty(Model.Status) || Model.Status == "0") ? "current" : "")">上架</a></li>
						<li><a href="/Products/Index?status=1" class="@(Model.Status == "1" ? "current" : "")">下架</a></li>
					</ul>
					<div class="inside_menu_current" style="left: @(Model.Status == "1" ? "50px" : "0px");"></div>
				</div>
			</div>
		</div>
		<div class="inside_table">
			<div class="list_menu">
				<ul class="list_menu_button fr">
					<li><a class="add" href="/Products/Edit?id=0">添加</a></li>
				</ul>
				<div class="search_box fl">
					<form id="searchForm">
						<div class="k_input">
							<input type="text" id="searchKeyword" class="form_input" name="keyword"
							       value="@Model.Keyword" size="15" autocomplete="off"
							       placeholder="请输入产品名称、SKU、产品编号、简介或产品标签">
							<button type="button" class="search_btn iconfont"
							        onclick="productSearch.searchToProducts(); return false">
								&#xe600;
							</button>
						</div>
						<input type="button" class="filter_btn" value="筛选">
						<div class="clear"></div>
						<input type="hidden" name="status" value="@Model.Status">
						<input type="hidden" name="cateId" value="@Model.CateId">
						<input type="hidden" name="tagId" value="@Model.TagId">
						<input type="hidden" name="minPrice" value="@Model.MinPrice">
						<input type="hidden" name="maxPrice" value="@Model.MaxPrice">
					</form>
				</div>
				<dl class="more_feat fl">
					<dt><a class="oper_icon icon_more" href="javascript:;"></a></dt>
					<dd class="drop_down">
						<a class="import item" href="javascript:;">导入</a>
						<a class="export item" href="javascript:;">导出</a>
						<a class="batch-price item" href="javascript:;">批量改价</a>
					</dd>
				</dl>
				<div class="clear"></div>


				<div class="search_box_selected">
					@if (!string.IsNullOrEmpty(Model.CateId))
					{
						<span class="btn_item_choice current" data-name="cateId"><b>分类: @(Model.SelectedCategoryName)</b><i></i></span>
					}
					@if (!string.IsNullOrEmpty(Model.TagId))
					{
						<span class="btn_item_choice current" data-name="tagId"><b>标签: @(string.Join(", ", Model.SelectedTagNames))</b><i></i></span>
					}
					@if ((!string.IsNullOrEmpty(Model.MinPrice) && Model.MinPrice != "0") || 
					    (!string.IsNullOrEmpty(Model.MaxPrice) && Model.MaxPrice != "0") ||
					    (Model.MinPrice == "0" && Model.MaxPrice != "0") || 
					    (Model.MinPrice != "0" && Model.MaxPrice == "0"))
					{
						<span class="btn_item_choice current" data-name="minPrice|maxPrice"><b>价格: $@(Model.MinPrice ?? "0") ~ $@(Model.MaxPrice ?? "∞")</b><i></i></span>
					}
				</div>
			</div>

			<div class="box_table">
				<product-table/>
			</div>
			<div class="scroll_sticky">
				<div class="scroll_sticky_content">
					<div style="width: 1800px; height: 1px;"></div>
				</div>
			</div>
			<mx-pager name="productlist" total="@Model.TotalCount" page-size="@Model.PageSize"
			          page-num="@Model.PageNum"/>
		</div>
	</div>
	<div id="fixed_right">
		<div class="global_container fixed_products_export" data-width="400">
			<div class="top_title">产品导出 <a href="javascript:;" class="close"></a></div>
			<form class="global_form" id="products_export_form">
				<div class="export_content_box">
					<div class="rows clean export_range">
						<label>导出范围</label>
						<div class="input">
							<div class="item" data-count="0">
									<span class="input_radio_box checked">
										<span class="input_radio">
											<input type="radio" name="Type" value="0" checked="checked">
										</span>已勾选的产品
									</span>
							</div>
							<div class="item" data-count="50">
									<span class="input_radio_box">
										<span class="input_radio">
											<input type="radio" name="Type" value="1">
										</span>当前页面的产品
									</span>
							</div>
							<div class="item" data-count="178">
									<span class="input_radio_box">
										<span class="input_radio">
											<input type="radio" name="Type" value="2">
										</span>全站产品
									</span>
							</div>
							<div class="item" data-count="177">
									<span class="input_radio_box">
										<span class="input_radio">
											<input type="radio" name="Type" value="3">
										</span>所有搜索结果
									</span>
							</div>
						</div>
					</div>
					@* <div class="rows clean email_box hide"> *@
					@* 	<label>导出到邮箱</label> *@
					@* 	<div class="input"> *@
					@* 		<input type="text" name="Email" class="box_input full_input" size="25" maxlength="255" *@
					@* 		       format="Email"> *@
					@* 		<div class="email_tips">由于导出的文件较大，需发送到指定邮箱</div> *@
					@* 	</div> *@
					@* </div> *@
				</div>
				<div class="rows clean box_submit">
					<label></label>
					<div class="input">
						<input type="button" class="btn_global btn_submit" value="导出"
						       onclick="productExport.exportProducts(); return false;">
						<input type="button" class="btn_global btn_cancel" value="取消">
					</div>
				</div>
				<input type="hidden" name="keyword" value="">
				<input type="hidden" name="cateId" value="0">
				<input type="hidden" name="status" value="1">
				<input type="hidden" name="tagId" value="">
				<input type="hidden" name="minPrice" value="">
				<input type="hidden" name="maxPrice" value="">
				<input type="hidden" name="currentPageCount" value="50">
				<input type="hidden" name="currentPage" value="0">
				<input type="hidden" name="IdList" id="id_list" value="">
				<input type="hidden" name="exportService" id="exportService" value="website">
			</form>
		</div>
		<div class="upload_products_box global_form global_container" data-width="480">
			<div class="top_title">导入产品 <a href="javascript:;" class="close"></a></div>
			<form id="upload_edit_form" class="global_form" name="upload_form"
			      action="//jquery-file-upload.appspot.com/" method="POST" enctype="multipart/form-data">
				<div class="form_container">
					<div class="explode_box">
						<div class="step_box">
							<div class="list current" data-number="1">
								<div class="num"><span>1</span></div>
								<div class="tit">下载模板</div>
							</div>
							<div class="list" data-number="2">
								<div class="num"><span>2</span></div>
								<div class="tit">填写内容</div>
							</div>
							<div class="list" data-number="3">
								<div class="num"><span>3</span></div>
								<div class="tit">上传文件</div>
							</div>
						</div>
						<div class="step_content_box">
							<div class="step_item current" data-number="1">
								<div class="rows">
									<div class="tit">下载<a href="/api/ProductImport/template"
									                        class="no_load">示例CSV模版</a></div>
								</div>
							</div>
							<div class="step_item" data-number="2">
								<div class="rows">
									<div class="tit">请填写表格内容，如需导入产品图片，你可以：</div>
									<div class="input">
										<button type="button" class="btn_global btn_upload_photo">上传图片</button>
										<button type="button" class="btn_global btn_photo_gallery">从图片库中选择
										</button>
									</div>
								</div>
							</div>
							<div class="step_item" data-number="3">
								<div class="rows">
									<div class="tit">上传CSV文件</div>
									<div class="input upload_file">
										<input name="ExcelFile" value="" type="text" class="box_input" id="excel_path"
										       size="50" maxlength="100" readonly="" notnull="">
										<noscript><input type="hidden" name="redirect"
										                 value="https://blueimp.github.io/jQuery-File-Upload/">
										</noscript>
										<div class="row fileupload-buttonbar">
												<span class="btn_file btn-success fileinput-button">
													<i class="glyphicon glyphicon-plus"></i>
													<span>选择文件</span>
													<input type="file" name="Filedata" multiple="">
												</span>
											<div class="fileupload-progress fade">
												<div class="progress-extended"></div>
											</div>
											<div class="clear"></div>
											<div class="photo_multi_img template-box files"></div>
											<div class="photo_multi_img" id="PicDetail"></div>
										</div>
										<script id="template-upload" type="text/x-tmpl">
											{% for (var i=0, file; file=o.files[i]; i++) { %}
                                                <div class="template-upload fade">
                                                    <div class="clear"></div>
                                                    <div class="items">
                                                        <p class="name">{%=file.name%}</p>
                                                        <strong class="error text-danger"></strong>
                                                    </div>
                                                    <div class="items">
                                                        <p class="size">Processing...</p>
                                                        <div class="progress progress-striped active" role="progressbar" aria-valuemin="0" aria-valuemax="100" aria-valuenow="0"><div class="progress-bar progress-bar-success" style="width:0%;"></div></div>
                                                    </div>
                                                    <div class="items">
                                                        {% if (!i) { %}
                                                            <button class="btn_file btn-warning cancel">
                                                                <i class="glyphicon glyphicon-ban-circle"></i>
                                                                <span>取消</span>
                                                            </button>
                                                        {% } %}
                                                    </div>
                                                    <div class="clear"></div>
                                                </div>
                                            {% } %}
										</script>
										<script id="template-download" type="text/x-tmpl">
											{% for (var i=0, file; file=o.files[i]; i++) { %}
                                                {% if (file.thumbnailUrl) { %}
                                                    <div class="pic template-download fade hide">
                                                        <div>
                                                            <a href="javascript:;" title="{%=file.name%}" download="{%=file.name%}" data-gallery><img src="{%=file.thumbnailUrl%}" /><em></em></a>
                                                            <a href="{%=file.url%}" class="zoom" target="_blank"></a>
                                                            {% if (file.deleteUrl) { %}
                                                                <button class="btn-danger delete" data-type="{%=file.deleteType%}" data-url="{%=file.deleteUrl%}"{% if (file.deleteWithCredentials) { %} data-xhr-fields='{"withCredentials":true}'{% } %}>删除</button>
                                                                <input type="checkbox" name="delete" value="1" class="toggle" style="display:none;">
                                                            {% } %}
                                                            <input type="hidden" name="PicPath[]" value="{%=file.url%}" disabled />
                                                        </div>
                                                        <input type="text" maxlength="30" class="form_input" value="{%=file.name%}" name="Name[]" placeholder="'+lang_obj.global.picture_name+'" disabled notnull />
                                                    </div>
                                                {% } else { %}
                                                    <div class="template-download fade hide">
                                                        <div class="clear"></div>
                                                        <div class="items">
                                                            <p class="name">
                                                                {% if (file.url) { %}
                                                                    <a href="{%=file.url%}" title="{%=file.name%}" download="{%=file.name%}" {%=file.thumbnailUrl?'data-gallery':''%}>{%=file.name%}</a>
                                                                {% } else { %}
                                                                    <span>{%=file.name%}</span>
                                                                {% } %}
                                                            </p>
                                                            {% if (file.error) { %}
                                                                <div><span class="label label-danger">Error</span> {%=file.error%}</div>
                                                            {% } %}
                                                        </div>
                                                        <div class="items">
                                                            <span class="size">{%=o.formatFileSize(file.size)%}</span>
                                                        </div>
                                                        <div class="items">
                                                            {% if (file.deleteUrl) { %}
                                                                <button class="btn_file btn-danger delete" data-type="{%=file.deleteType%}" data-url="{%=file.deleteUrl%}"{% if (file.deleteWithCredentials) { %} data-xhr-fields='{"withCredentials":true}'{% } %}>
                                                                    <i class="glyphicon glyphicon-trash"></i>
                                                                    <span>删除</span>
                                                                </button>
                                                                <input type="checkbox" name="delete" value="1" class="toggle" style="display:none;">
                                                            {% } else { %}
                                                                <button class="btn_file btn-warning cancel">
                                                                    <i class="glyphicon glyphicon-ban-circle"></i>
                                                                    <span>取消</span>
                                                                </button>
                                                            {% } %}
                                                        </div>
                                                        <div class="clear"></div>
                                                    </div>
                                                {% } %}
                                            {% } %}
										</script>
									</div>
								</div>
								<div class="rows">
									<div class="input box_default_price">
										<div class="btn_checkbox  current"><em class="button"></em><input
												type="checkbox" checked="" name="DefaultPrice" value="1"></div>
										<span>产品未设置默认规格时，自动设置最低价为默认规格</span>
									</div>
								</div>
							</div>
						</div>
						<div class="rows clean box_submit">
							<label></label>
							<div class="input input_button">
								<input type="button" class="btn_global btn_step_prev" value="上一步"> <input
									type="button" class="btn_global btn_step_next" value="下一步"> <input type="button"
								                                                                          class="btn_global btn_submit"
								                                                                          value="保存"
								                                                                          onclick="productImport.ToImportProductCsv(); return false;">
								<input type="button" class="btn_global btn_cancel" value="取消">
							</div>
						</div>

					</div>
				</div>
				<div id="box_circle_container">
					<div class="box_circle_progress" data-percent="0" data-animate="0">
						<div class="wrapper circle_progress_right">
							<div class="circle_progress rightcircle"></div>
						</div>
						<div class="wrapper circle_progress_left">
							<div class="circle_progress leftcircle"></div>
						</div>
						<div class="circle_progress_number"><span>0</span>%</div>
						<div class="circle_progress_completed"></div>
					</div>
					<div class="circle_progress_text"></div>
				</div>
				<div class="container_close_tips hide">关闭此弹窗不影响产品继续导入</div>
				<div class="box_import_completed">
					<div class="title">产品已完成导入</div>
					<div class="item item_done">
						<div class="item_title">导入成功：<span>0</span> 个产品</div>
					</div>
					<div class="item item_fail">
						<div class="item_title">导入失败：<span>0</span> 个产品</div>
						<div class="fail_list">
							<div class="list_title">请下载失败列表，并修复以下错误后继续导入：</div>
							<div class="list_content"></div>
						</div>
						<div class="btn_import_fail">下载失败列表</div>
					</div>
				</div>
			</form>
		</div>

		<div class="global_container batch_price_by_cate" data-width="350">
			<div class="top_title">
				<div>批量改价</div>
				<a href="javascript:;" class="close"></a>
			</div>
			<form class="global_form" id="batchPriceForm" method="post" name="batchPriceForm"
			      action="/manage/products/products/batch-price-by-cate">
				<input name="_csrf" type="hidden" id="_csrf"
				       value="rEukjExZrEJva9LHMXa3s3lxshviX3kw6gvwNYwnkQDrKvyhA23kdxkh4b5UD-faQBXfULFuL1apOKpe-mjHVA==">
				<div class="rows clean">
					<label rel="cate_id" data-tip="请选择产品分类">
						分类
					</label>
					<div class="input">
						<div class="box_select"><select name="cate_id">
								<option value="">请选择</option>
								<option value="0">全部分类</option>
								@if (Model.CategoryTree.ContainsKey(0) && Model.CategoryTree[0].Any())
								{
									var rootCategories = Model.CategoryTree[0].OrderBy(c => c.MyOrder).ToList();
									for (int i = 0; i < rootCategories.Count; i++)
									{
										var category = rootCategories[i];
										var isLastRoot = i == rootCategories.Count - 1;
										<option value="@category.CateId">@(isLastRoot ? "└" : "├")@category.Category_en</option>
										
										if (Model.CategoryTree.ContainsKey(category.CateId))
										{
											var subCategories = Model.CategoryTree[category.CateId].OrderBy(c => c.MyOrder).ToList();
											for (int j = 0; j < subCategories.Count; j++)
											{
												var subCategory = subCategories[j];
												var isLastSub = j == subCategories.Count - 1;
												var prefix = isLastRoot ? "　" : "｜";
												
												<option value="@subCategory.CateId">@(prefix)@(isLastSub ? "└" : "├")@subCategory.Category_en</option>
												
												if (Model.CategoryTree.ContainsKey(subCategory.CateId))
												{
													var subSubCategories = Model.CategoryTree[subCategory.CateId].OrderBy(c => c.MyOrder).ToList();
													for (int k = 0; k < subSubCategories.Count; k++)
													{
														var subSubCategory = subSubCategories[k];
														var isLastSubSub = k == subSubCategories.Count - 1;
														var subPrefix = isLastSub ? "　" : "｜";
														
														<option value="@subSubCategory.CateId">@(prefix)@(subPrefix)@(isLastSubSub ? "└" : "├")@subSubCategory.Category_en</option>
													}
												}
											}
										}
									}
								}
							</select></div>
					</div>
				</div>

				<div class="rows clean ">
					<label>价格类型</label>
					<div class="box_select price_type">
						<select name="price_type">
							<option value="origin_price">原价</option>
							<option value="shop_price">价格</option>
							<option value="cost_price">成本价</option>
						</select>
					</div>
				</div>

				<div class="rows clean">
					<label>改价方式</label>
					<div class="input price_method">
							<span class="input_radio_box input_radio_radius_box tab_option fl">
								<span class="input_radio">
									<input type="radio" name="calc_method" value="percent">
								</span>
								<strong class="fs14">百分比</strong>
								<p class="fs12">调整后价格 = 调整前价格 * ( 1 ± 百分比 )</p>
							</span>
						<span class="input_radio_box input_radio_radius_box tab_option fr">
								<span class="input_radio">
									<input type="radio" name="calc_method" value="value">
								</span>
								<strong class="fs14">价格</strong>
								<p class="fs12">调整后价格 = 调整前价格 ± 数值</p>
							</span>
						<div class="clear"></div>
					</div>
				</div>

				<div class="rows clean">
					<label>
						调整价格
					</label>
					<div class="input price_value">
						<div class="price_type_text"></div>
						<div class="box_select">
							<select name="calc_symbol">
								<option value="add">加</option>
								<option value="min">减</option>
							</select>
						</div>
						<div>
							<label class="hide" rel="calc_value" data-tip="请输入要修改的价格"></label>
							<span class="unit_input calc_value">
									<b class="price">€</b>
									<input type="text" class="box_input" name="calc_value" value="0" maxlength="255"
									       notnull="" rel="amount" decimal="2" autocomplete="off">
									<b class="last percent">%</b>
								</span>
						</div>
					</div>
					<div class="price_value warning">
						<div class="global_app_tips tips"><em></em>若调整后，价格出现零或负数，则不改价</div>
					</div>
				</div>

				<div class="rows clean box_submit">
					<div class="input">
						<input type="button" class="btn_global btn_submit" value="保存" onclick="productBatchPrice.submitBatchPrice(); return false;">
						<input type="button" class="btn_global btn_cancel" value="取消">
					</div>
				</div>
			</form>
		</div>

		<div class="global_container fixed_search_filter" data-width="396">
			<div class="top_title">筛选 <a href="javascript:;" class="close"></a></div>
			<div class="global_form">
				<div class="box_filter">
					<div class="filter_list">
						<div class="filter_title">分类</div>
						<div class="filter_option">
							<div class="filter_option_input">
								<dl class="box_drop_double edit_box" data-checkbox="0" data-showadd="0">
									<dt><input type="text" class="box_input" name="Select" placeholder="请选择或填写"
									           value="" autocomplete="off"><input type="hidden" name="cateId" value=""
									                                              class="hidden_value"><input
											type="hidden" name="cateIdType" value="" class="hidden_type"></dt>
									<dd class="drop_down" style="display: none;">
										<div class="drop_menu" data-type="Select"><a href="javascript:;"
										                                             class="btn_back" data-value=""
										                                             data-type="" data-table=""
										                                             data-top="0" data-all="0"
										                                             style="display:none;">返回</a>
											<div class="drop_skin" style="display: none;"></div>
											<div class="drop_list"
											     data='@(Model.CategoryTree.ContainsKey(0) ? "[{\"Name\":\"未分类\",\"Value\":-1,\"Type\":\"products_category\"}," + string.Join(",", 
											     Model.CategoryTree[0].Select(c => $"{{\"Name\":\"{c.Category_en}\",\"Value\":{c.CateId},\"Type\":\"products_category\",\"Table\":\"products_category\",\"Children\":{(Model.CategoryTree.ContainsKey(c.CateId) ? "true" : "false")}}}"))
											     + "]" : "[]")'
											     data-more="none">
												<!-- 这里的内容由JavaScript动态生成 -->
											</div>
											<a href="javascript:;" class="btn_load_more" data-value="products_category"
											   data-type="products_category" data-table="products_category" data-top="0"
											   data-all="1" data-check-all="0" data-start="1"
											   style="display:none;">加载更多</a></div>
									</dd>
								</dl>
							</div>
							<div class="filter_clean">
								<button>清除</button>
							</div>
						</div>
					</div>
					<div class="filter_list">
						<div class="filter_title">标签</div>
						<div class="filter_option">
							<div class="filter_option_input">
								<dl class="box_drop_double" data-checkbox="1" data-showadd="0">
									<dt class="box_checkbox_list">
										<div class="select_placeholder">请选择</div>
										<div class="select_list"></div>
										<input type="text" class="box_input check_input" name="Select" value=""
										       placeholder="" size="30" maxlength="255" autocomplete="off"><input
											type="hidden" name="tagId" value="" class="hidden_value"><input
											type="hidden" name="tagIdType" value="" class="hidden_type"></dt>
									<dd class="drop_down" style="display: none;">
										<div class="drop_menu" data-type="Select"><a href="javascript:;"
										                                             class="btn_back" data-value=""
										                                             data-type="" data-table=""
										                                             data-top="0" data-all="0"
										                                             style="display:none;">返回</a>
											<div class="drop_skin" style="display: none;"></div>
											<div class="drop_list"
											     data='@(Model.AllTags?.Any() == true ? "[" + string.Join(",", Model.AllTags.Select(t => $"{{\"Name\":\"{t.Name_en}\",\"Value\":{t.TId},\"Type\":\"products_tags\"}}")) + "]" : "[]")'
											     data-more="none">
												<!-- 这里的内容由JavaScript动态生成 -->
											</div>
											<a href="javascript:;" class="btn_load_more" data-value="products_tags"
											   data-type="products_tags" data-table="products_tags" data-top="0"
											   data-all="1" data-check-all="0" data-start="1"
											   style="display:none;">加载更多</a></div>
									</dd>
								</dl>
							</div>
							<div class="filter_clean">
								<button>清除</button>
							</div>
						</div>
					</div>
					<div class="filter_list">
						<div class="filter_title">价格</div>
						<div class="filter_option">
							<div class="filter_option_price filter_range_price clean">
								<span class="unit_input"><b>€</b><input type="text" class="box_input left_radius"
								                                        name="MinPrice" id="MinPrice" value="0"
								                                        size="10" maxlength="15" rel="amount"
								                                        autocomplete="off"></span>
								<span class="num_to">~</span>
								<span class="unit_input"><b>€</b><input type="text" class="box_input left_radius"
								                                        name="MaxPrice" id="MaxPrice" value="0"
								                                        size="10" maxlength="15" rel="amount"
								                                        autocomplete="off"></span>
							</div>
							<div class="filter_clean">
								<button>清除</button>
							</div>
						</div>
					</div>
				</div>
				<div class="rows clean box_button box_submit">
					<div class="input">
						<input type="button" class="btn_global btn_submit" value="筛选"
						       onclick="productSearch.filterProducts()">
						<input type="button" class="btn_global btn_cancel" value="取消">
					</div>
				</div>
			</div>
		</div>

		<div class="global_container box_products_tags_bat" data-width="400">
			<div class="top_title">添加标签 <a href="javascript:;" class="close"></a></div>
			<form id="products_tags_edit_form" class="global_form" action="/manage/products/products" method="post">
				<input type="hidden" name="_csrf-manage"
				       value="rEukjExZrEJva9LHMXa3s3lxshviX3kw6gvwNYwnkQDrKvyhA23kdxkh4b5UD-faQBXfULFuL1apOKpe-mjHVA==">
				<div class="rows clean">
					<label>标签</label>
					<div class="input">
						<div class="box_option_list"></div>
					</div>
				</div>
				<div class="rows clean box_submit">
					<label></label>
					<div class="input input_button">
						<input type="button" class="btn_global btn_submit" value="保存"> <input type="button"
						                                                                        class="btn_global btn_cancel"
						                                                                        value="取消">
					</div>
				</div>
				<input type="hidden" name="PId" value=""> <input type="hidden" name="Type" value=""> <input
					type="hidden" name="do_action" value="/manage/products/products/tags-update">
			</form>
		</div>
		<div class="global_container box_products_tags_del_bat fixed_right_products_choice " data-width="400">
			<div class="top_title">删除标签 <a href="javascript:;" class="close"></a></div>
			<div class="search_menu">
				<div class="search_form">
					<form method="get" class="all_tags_search_form" action="?" style="border-radius: 4px;">
						<div class="k_input">
							<input type="text" name="Keyword" value="" class="form_input" size="15" autocomplete="off"
							       placeholder="请输入关键词">
							<input type="submit" class="search_btn btn_submit" value="">
						</div>
						<input type="hidden" name="do_action" value="/manage/products/products/tags-del-list">
					</form>
				</div>
			</div>
			<form id="products_tags_del_form" class="global_form" action="/manage/products/products" method="post">
				<input type="hidden" name="_csrf-manage"
				       value="rEukjExZrEJva9LHMXa3s3lxshviX3kw6gvwNYwnkQDrKvyhA23kdxkh4b5UD-faQBXfULFuL1apOKpe-mjHVA==">
				<div class="fixed_right_products_choice_jsppane all_tags_list"></div>
				<div class="rows clean box_submit">
					<label></label>
					<div class="input input_button">
						<input type="button" class="btn_global btn_submit" value="删除"> <input type="button"
						                                                                        class="btn_global btn_cancel"
						                                                                        value="取消">
					</div>
				</div>
				<input type="hidden" name="PId" value=""> <input type="hidden" name="do_action"
				                                                 value="/manage/products/products/tags-del">
			</form>
		</div>
		<div class="global_container fixed_translation" data-width="400">
			<div class="top_title"><strong>翻译</strong><a href="javascript:;" class="close"></a></div>
			<div class="content_box" data-module="products" data-related-id="" data-shop-language="en" data-len="1"
			     data-track-id="0"></div>
		</div>
		<div class="btn_translation"></div>
		<div class="btn_translation"></div>
	</div>
	<div id="copy_set_box">
		<div class="copy_mask"></div>
		<div class="copy_box">
			<div class="title">复制产品<i class="close iconfont icon_menu_close"></i></div>
			<form id="copy_box_form" class="global_form">
				<div class="box">
					<div class="subtitle">选择需要复制的应用信息</div>
					<div class="list">
							<span class="input_checkbox_box" data-type="wholesale">
								<span class="input_checkbox">
									<input type="checkbox" name="copyApp[]" value="wholesale">
								</span>
								批发							<em></em>
							</span>
						<span class="input_checkbox_box" data-type="shipping_template">
								<span class="input_checkbox">
									<input type="checkbox" name="copyApp[]" value="shipping_template">
								</span>
								运费模板							<em></em>
							</span>
						<span class="input_checkbox_box" data-type="custom_attributes">
								<span class="input_checkbox">
									<input type="checkbox" name="copyApp[]" value="custom_attributes">
								</span>
								定制属性							<em></em>
							</span>
						<span class="input_checkbox_box" data-type="screening">
								<span class="input_checkbox">
									<input type="checkbox" name="copyApp[]" value="screening">
								</span>
								条件筛选							<em></em>
							</span>
					</div>
				</div>
				<div class="btn_row rows clean">
					<div class="input">
						<input type="hidden" name="copyId" value="0">
						<input type="submit" class="btn_global btn_submit" value="复制产品">
						<input type="button" class="btn_global btn_cancel" value="取消">
					</div>
				</div>
			</form>
		</div>
	</div>
</div>


