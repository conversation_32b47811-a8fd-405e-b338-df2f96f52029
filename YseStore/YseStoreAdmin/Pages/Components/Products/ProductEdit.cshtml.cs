using YseStore.IService.Products;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.AspNetCore.Mvc.ViewFeatures;
using YseStore.Model.RequestModels.Products;
using YseStore.Model.Response.Products;
using Entitys;
using Microsoft.AspNetCore.Razor.TagHelpers;
using YseStore.Model.Utils;
using YseStore.IService;

namespace YseStoreAdmin.Pages.Components.Products
{
    public class ProductEdit : MComponent
    {
        private readonly IProductService _productService;
        private readonly IProductCategoryService _productCategoryService;
        private readonly IProductStockService _productStockService;
        private readonly ITempDataDictionaryFactory _tempDataDictionaryFactory;
        private readonly IConfiguration _configuration;
        private readonly ICurrencyService _currencyService;

        /// <summary>
        /// 所有产品标签列表
        /// </summary>
        public List<products_tags> AllTags { get; private set; } = new List<products_tags>();

        public List<products_tags> AllTagsByProId { get; private set; } = new List<products_tags>();

        // 存储分类树形结构
        public Dictionary<int, List<products_category>> CategoryTree { get; set; } =
            new Dictionary<int, List<products_category>>();

        public List<products_category> Categories { get; set; } = new List<products_category>();

        /// <summary>
        /// 库存列表
        /// </summary>
        public List<SelectListItem> StockList { get; private set; } = new List<SelectListItem>();

        /// <summary>
        /// 当前产品关联的标签
        /// </summary>
        public List<products_tags> ProductTags { get; private set; } = new List<products_tags>();

        /// <summary>
        /// 当前编辑的产品ID
        /// </summary>
        [HtmlAttributeName("product-id")]
        public int ProductId { get; set; }

        /// <summary>
        /// 分类名称
        /// </summary>
        public string? CateName { get; private set; }

        /// <summary>
        /// 产品详情数据
        /// </summary>
        public ProductDetailWithAttributesResponse ProductDetail { get; private set; }

        /// <summary>
        /// 产品URL基础地址
        /// </summary>
        public string ProductUrlBase { get; set; }

        /// <summary>
        /// 产品页面URL路径
        /// </summary>
        public string PageUrl { get; set; }

        /// <summary>
        /// 已选择的产品分类
        /// </summary>
        public List<SelectListItem> ProductCategorySelected { get; private set; } = new List<SelectListItem>();

        /// <summary>
        /// 币种符号
        /// </summary>
        public string Currency { get; private set; } = "$";

        public ProductEdit(IProductService productService, IProductStockService productStockService,
            ITempDataDictionaryFactory tempDataDictionaryFactory,
            IProductCategoryService productCategoryService,
            IConfiguration configuration,
            ICurrencyService currencyService)
        {
            _productService = productService;
            _productStockService = productStockService;
            _productCategoryService = productCategoryService;
            _tempDataDictionaryFactory = tempDataDictionaryFactory;
            _configuration = configuration;
            _currencyService = currencyService;
        }

        /// <summary>
        /// 组件挂载
        /// </summary>
        public override async Task MountAsync()
        {
            // 获取币种符号
            var defaultCurrency = await _currencyService.GetManageDefaultCurrency();
            Currency = defaultCurrency.Symbol;

            // 从配置中获取产品URL基础地址
            ProductUrlBase = AppSettingsConstVars.WebSiteUrl + "/products/" ?? "https://www.retekess.it";

            // 创建查询请求
            var request = new ProductCategoryQueryRequest();
            request.Keyword = CateName;
            request.PageSize = 50; //先默认一个

            // 获取所有标签数据
            AllTags = await _productService.GetAllProductTags();

            //获取仓库列表，用于下拉选择
            StockList = await _productStockService.GetWarehouseListAsync();

            // 加载产品分类数据
            await LoadCategoriesAsync(request);

            // 如果是编辑模式，获取产品关联的标签
            if (ProductId > 0 && ProductTags.Count == 0)
            {
                // 获取当前产品关联的标签，而不是所有标签
                ProductTags = await _productService.GetProductTagsByProductId(ProductId);

                // 获取产品详情，包括PageUrl
                ProductDetail = await _productService.GetProductDetailWithAttributes(ProductId);
                if (ProductDetail != null && ProductDetail.Product != null)
                {
                    PageUrl = ProductDetail.Product.PageUrl;

                    // 获取已选择的分类
                    await LoadSelectedCategories();
                }
            }

            await base.MountAsync();
        }

        /// <summary>
        /// 加载产品分类数据
        /// </summary>
        /// <param name="request">查询请求参数</param>
        /// <returns>异步任务</returns>
        public async Task LoadCategoriesAsync(ProductCategoryQueryRequest request)
        {
            // 调用服务获取所有产品分类
            PageModel<products_category> pageModel = await _productCategoryService.GetAllProductCategories(request);

            // 检查pageModel是否为空，如果为空则初始化一个空的PageModel对象
            if (pageModel == null)
            {
                pageModel = new PageModel<products_category>
                {
                    data = new List<products_category>(),
                    dataCount = 0,
                    page = 1,
                    PageSize = 50
                };
            }

            Categories = pageModel.data;

            // 使用通用方法构建分类树
            CategoryTree = CategoryTreeBuilder.BuildCategoryTreeGeneric(Categories);
        }

        /// <summary>
        /// 加载已选择的分类
        /// </summary>
        private async Task LoadSelectedCategories()
        {
            if (ProductDetail.CategoryIds.Any())
            {
                // 清空之前的选择
                ProductCategorySelected.Clear();

                // 如果Categories为空，先加载所有分类
                if (Categories == null || !Categories.Any())
                {
                    var request = new ProductCategoryQueryRequest { PageSize = 500 }; // 加载足够多的分类
                    await LoadCategoriesAsync(request);
                }

                // 遍历所有分类ID，找到对应的分类名称
                foreach (var categoryId in ProductDetail.CategoryIds)
                {
                    var category = Categories.FirstOrDefault(c => c.CateId == categoryId);
                    if (category != null)
                    {
                        ProductCategorySelected.Add(new SelectListItem
                        {
                            Value = categoryId.ToString(),
                            Text = category.Category_en
                        });
                    }
                }
            }
        }
    }
}