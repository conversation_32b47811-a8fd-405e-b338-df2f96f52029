using Entitys;
using YseStore.IService.Products;
using YseStore.Model.RequestModels.Products;
using YseStore.Model.Utils;
using Microsoft.AspNetCore.Mvc.ViewFeatures;

namespace YseStoreAdmin.Pages.Components.Products
{
    public class ProductCate : MComponent
    {
        private readonly IProductCategoryService _productCategoryService;
        private readonly IHttpContextAccessor _httpContextAccessor;
        private readonly ITempDataDictionaryFactory _tempDataDictionaryFactory;

        public List<products_category> Categories { get; set; } = new List<products_category>();

        // 存储分类树形结构
        public Dictionary<int, List<products_category>> CategoryTree { get; set; } =
            new Dictionary<int, List<products_category>>();

        // 搜索关键词
        public string Keyword { get; set; } = "";
        
        // 是否显示已下架的分类
        public bool ShowSoldOutCategory { get; set; } = true;

        public ProductCate(
            IProductCategoryService productCategoryService, 
            IHttpContextAccessor httpContextAccessor,
            ITempDataDictionaryFactory tempDataDictionaryFactory)
        {
            _productCategoryService = productCategoryService;
            _httpContextAccessor = httpContextAccessor;
            _tempDataDictionaryFactory = tempDataDictionaryFactory;
        }

        public override async Task MountAsync()
        {
            // 创建查询请求
            var request = new ProductCategoryQueryRequest();
            
            // 获取HttpContext
            var httpContext = _httpContextAccessor.HttpContext;
            if (httpContext != null)
            {
                // 从Cookie中读取"显示已下架分类"设置
                if (httpContext.Request.Cookies.TryGetValue("SoldOutCategoryShow", out string showSoldOutValue))
                {
                    // 解析布尔值
                    if (bool.TryParse(showSoldOutValue, out bool showSoldOut))
                    {
                        ShowSoldOutCategory = showSoldOut;
                    }
                }
                
                // 设置查询请求中的"显示已下架分类"参数
                request.IncludeSoldOut = ShowSoldOutCategory;
                request.PageSize = 50;//先默认一个
                
                // 获取TempData
                var tempData = _tempDataDictionaryFactory.GetTempData(httpContext);
                
                // 从TempData中获取搜索关键词
                if (tempData.ContainsKey("CategorySearchKeyword"))
                {
                    Keyword = tempData["CategorySearchKeyword"]?.ToString() ?? "";
                    
                    // 设置查询请求中的关键词
                    if (!string.IsNullOrEmpty(Keyword))
                    {
                        request.Keyword = Keyword;
                    }
                }
            }
            
            // 加载产品分类数据
            await LoadCategoriesAsync(request);
            await base.MountAsync();
        }
        
        /// <summary>
        /// 加载产品分类数据
        /// </summary>
        /// <param name="request">查询请求参数</param>
        /// <returns>异步任务</returns>
        public async Task LoadCategoriesAsync(ProductCategoryQueryRequest request)
        {
            // 调用服务获取所有产品分类
            PageModel<products_category> pageModel = await _productCategoryService.GetAllProductCategories(request);
            
            // 检查pageModel是否为空，如果为空则初始化一个空的PageModel对象
            if (pageModel == null)
            {
                pageModel = new PageModel<products_category>
                {
                    data = new List<products_category>(),
                    dataCount = 0,
                    page = 1,
                    PageSize = 50
                };
            }
            
            Categories = pageModel.data;
            
            // 使用通用方法构建分类树
            CategoryTree = CategoryTreeBuilder.BuildCategoryTreeGeneric(Categories);
        }
    }
}