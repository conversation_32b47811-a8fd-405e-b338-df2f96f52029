using Entitys;
using YseStore.IService.Products;

namespace YseStoreAdmin.Pages.Components.Products
{
    public class ProductSwitchContent : MComponent
    {
        private readonly IProductsSwitchContentService _productsSwitchContentService;
        private readonly IProductsSwitchService _productsSwitchService;

        public List<products_switch_content> ProductSwitchContents { get; set; }
        public products_switch CurrentSwitch { get; set; }
        public int SwitchId { get; set; }

        /// <summary>
        /// 用于返回到上一个切换卡的ID
        /// </summary>
        public int BackSwitchId { get; set; }

        public Dictionary<int, products_switch> SwitchesDict { get; set; } = new Dictionary<int, products_switch>();
        public string Keyword { get; set; } = string.Empty;

        public ProductSwitchContent(IProductsSwitchContentService productsSwitchContentService,
            IProductsSwitchService productsSwitchService)
        {
            _productsSwitchContentService = productsSwitchContentService;
            _productsSwitchService = productsSwitchService;
        }

        public override async Task MountAsync()
        {
            // 获取搜索关键词
            var keywordParam = HttpContext.Request.Query["keyword"];
            if (!string.IsNullOrEmpty(keywordParam))
            {
                Keyword = keywordParam;
            }

            // 获取URL参数id
            var idParam = HttpContext.Request.Query["id"];
            if (!string.IsNullOrEmpty(idParam))
            {
                SwitchId = int.Parse(idParam);
                BackSwitchId = int.Parse(idParam);

                // 获取切换卡内容列表
                ProductSwitchContents = await _productsSwitchContentService.GetBySIdAsync(SwitchId);

                // 如果有搜索关键词，进行过滤
                if (!string.IsNullOrEmpty(Keyword))
                {
                    ProductSwitchContents = ProductSwitchContents
                        .Where(c => c.Name.Contains(Keyword, StringComparison.OrdinalIgnoreCase))
                        .ToList();
                }

                // 获取当前切换卡信息
                CurrentSwitch = await _productsSwitchService.GetByIdAsync(SwitchId);

                // 将当前切换卡添加到字典中
                if (CurrentSwitch != null)
                {
                    SwitchesDict[CurrentSwitch.SId] = CurrentSwitch;
                }
            }
            else
            {
                // 如果没有指定id，则获取所有内容
                ProductSwitchContents = await _productsSwitchContentService.GetAllAsync();

                // 如果有搜索关键词，进行过滤
                if (!string.IsNullOrEmpty(Keyword))
                {
                    ProductSwitchContents = ProductSwitchContents
                        .Where(c => c.Name.Contains(Keyword, StringComparison.OrdinalIgnoreCase))
                        .ToList();
                }

                // 获取所有内容对应的切换卡信息
                if (ProductSwitchContents != null && ProductSwitchContents.Count > 0)
                {
                    // 收集所有切换卡ID
                    var switchIds = ProductSwitchContents.Select(c => c.SId).Distinct().ToList();

                    // 获取所有切换卡信息
                    var allSwitches = await _productsSwitchService.GetAllAsync();

                    // 将切换卡信息添加到字典中
                    foreach (var switchItem in allSwitches)
                    {
                        if (switchIds.Contains(switchItem.SId))
                        {
                            SwitchesDict[switchItem.SId] = switchItem;
                        }
                    }
                }
            }
        }

        // 获取切换卡名称的辅助方法
        public string GetSwitchName(int switchId)
        {
            if (SwitchesDict.ContainsKey(switchId))
            {
                return SwitchesDict[switchId].Name;
            }

            return $"切换卡 ID: {switchId}";
        }
    }
}