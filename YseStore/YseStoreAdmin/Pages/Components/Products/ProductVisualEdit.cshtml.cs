using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using YseStore.Common;
using YseStore.Common.Helper;
using YseStore.IService.manage;
using YseStore.Model.Event;
using YseStore.Model.VM;

namespace YseStoreAdmin.Pages.Components.Products
{
    public class ProductVisualEdit : MComponent
    {
        public int DraftsId { get; set; }
        public int PublicFull { get; set; }

        public string WebSiteUrl { get; set; }

        public override Task MountAsync()
        {
            WebSiteUrl = AppSettings.GetValue("WebSiteUrl");
            var draftsId = Request.Query["draftsId"].ToString();
            if (!string.IsNullOrEmpty(draftsId))
            {
                DraftsId = draftsId.ObjToInt();
            }
            var publicFull = Request.Query["publicFull"].ToString();
            if (!string.IsNullOrEmpty(publicFull))
            {
                PublicFull = publicFull.ObjToInt();
            }
            return base.MountAsync();
        }

    }
}
