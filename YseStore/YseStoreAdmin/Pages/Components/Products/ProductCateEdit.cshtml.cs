using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Razor.TagHelpers;
using YseStore.IService.Products;

namespace YseStoreAdmin.Pages.Components.Products
{
    public class ProductCateEdit : MComponent
    {
        private readonly IConfiguration _configuration;
        private readonly IProductCategoryService _productCategoryService;

        // 从URL查询字符串获取id参数
        [FromQuery(Name = "id")] public int Id { get; set; }

        // 从TagHelper属性query-id获取id参数
        [HtmlAttributeName("query-id")] public int QueryId { get; set; }

        // 用于组件内部和视图使用的CateId
        public int CateId { get; set; }

        // 网站基础URL
        public string BaseDomainUrl { get; set; }

        // 产品ID列表，用于前端JavaScript使用
        public List<int> ProductIds { get; set; } = new List<int>();

        // 分类排序方式，用于前端JavaScript使用
        public string OrderType { get; set; } = "time_desc";

        public ProductCateEdit(IConfiguration configuration, IProductCategoryService productCategoryService)
        {
            _configuration = configuration;
            _productCategoryService = productCategoryService;
        }

        public override async Task MountAsync()
        {
            // 优先使用QueryId (来自TagHelper属性)
            if (QueryId > 0)
            {
                CateId = QueryId;
            }

            // 从配置中获取网站基础URL
            BaseDomainUrl = AppSettingsConstVars.WebSiteUrl + "collections" ?? "https://www.retekess.it";

            // 如果是编辑模式（CateId > 0），获取分类详情和关联的产品列表
            if (CateId > 0)
            {
                try
                {
                    // 获取分类详情
                    var (category, descriptions) = await _productCategoryService.GetProductCategoryById(CateId);

                    // 设置排序方式
                    if (category != null && !string.IsNullOrEmpty(category.OrderType))
                    {
                        OrderType = category.OrderType;
                    }

                    // 获取产品ID列表
                    ProductIds = await _productCategoryService.GetProductIdsByCategoryId(CateId);
                }
                catch (Exception ex)
                {
                    // 记录错误但不影响页面加载
                    Console.WriteLine($"获取分类数据时出错: {ex.Message}");
                    ProductIds = new List<int>();
                    OrderType = "time_desc"; // 使用默认排序
                }
            }

            await base.MountAsync();
        }
    }
}