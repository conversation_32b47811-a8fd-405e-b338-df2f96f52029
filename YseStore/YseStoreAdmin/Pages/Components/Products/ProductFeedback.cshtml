@model YseStoreAdmin.Pages.Components.Products.ProductFeedback
@{
}
<div>

    <div id="review" class="r_con_wrap product_feedback_index" style="height: 526px;">
        <div class="inside_container inside_menu_right clean">
            <h1>评论</h1>
      
        </div>
        <div class="inside_table">
            <div class="list_menu">
                <div class="search_box fl">
                    <form action="?" method="get">
                        <div class="k_input">
                            <input type="text" class="form_input" name="Keyword" size="15" value="@Model.keywords" autocomplete="off" placeholder="请输入产品名称或SKU或评论内容">
                            <input type="submit" class="search_btn" value="&#xe600;">
                        </div>

                        <input type="hidden" name="MenuSort" value="@(Model.MenuSort)">
                    </form>
                </div>
                <div class="menu_tips fr">
                    <div class="global_app_tips">
                        <em></em>
                        当前的发布方式为手动发布，管理员审核发布后C端客户才能进行查看。<a href="/Setting/Basis" target="_blank">设置</a>
                    </div>
                </div>
                <div class="clear"></div>

                <div class="search_box_selected">
                    @if (!string.IsNullOrEmpty(Model.keywords))
                    {
                        <span class="btn_item_choice current" data-name="Keyword"><b>搜索词: @(Model.keywords) </b><i></i></span>
                    }
                   
                </div>
            </div>
           
            <div class="box_table">
                <product-feedback-table />
            </div>
            <div class="scroll_sticky"><div class="scroll_sticky_content"><div style="width: 1800px; height: 1px;"></div></div></div>
            <mx-pager name="manlog" total="@Model.TotalCount" page-size="@Model.PageSize" page-num="@Model.PageNum" />
          @*   <div id="turn_page" data-current="0" data-count="24">
                <div class="total_page">共 473 条</div><ul class="pagination">
                    <li class="first disabled"><span>首页</span></li>
                    <li class="prev disabled"><span></span></li>
                    <li class="active"><a href="/manage/products/review/index?page=1" data-page="0">1</a></li>
                    <li><a href="/manage/products/review/index?page=2" data-page="1">2</a></li>
                    <li><a href="/manage/products/review/index?page=3" data-page="2">3</a></li>
                    <li><a href="/manage/products/review/index?page=4" data-page="3">4</a></li>
                    <li><a href="/manage/products/review/index?page=5" data-page="4">5</a></li>
                    <li><a href="/manage/products/review/index?page=6" data-page="5">6</a></li>
                    <li><a href="/manage/products/review/index?page=7" data-page="6">7</a></li>
                    <li><a href="/manage/products/review/index?page=8" data-page="7">8</a></li>
                    <li><a href="/manage/products/review/index?page=9" data-page="8">9</a></li>
                    <li><a href="/manage/products/review/index?page=10" data-page="9">10</a></li>
                    <li class="next"><a href="/manage/products/review/index?page=2" data-page="1"></a></li>
                    <li class="last"><a href="/manage/products/review/index?page=24" data-page="23">尾页</a></li>
                </ul>
            </div> *@
        </div>
    </div>

</div>