
@model YseStoreAdmin.Pages.Components.Products.Recommend
@{
}
<div>

    <div id="products_recommend" class="r_con_wrap plugins_app_box" style="height: 322px;">
        @* <div class="return_title"> *@
        @*     <a href="/manage/plugins/my-app"> *@
        @*         <span class="return">我的应用</span> *@
        @*     </a> *@
        @* </div> *@
        <div class="inside_container plugins_app_title">
            <div class="app_icon icon_item_products_recommend"></div>
            <h1>推荐产品</h1>
            <div class="box_explain">推荐不同的产品到店铺的指定页面，提高产品曝光率，吸引客户购物，增加销量</div>
        </div>
        <div class="inside_table">
            <div class="inside_box">
                <div class="module_menu">
                    <ul>
                        <li class="current"><a href="/Products/Recommend">推荐产品</a></li>
                        <li><a href="/Products/RecommendSet">推荐集</a></li>
                    </ul>
                </div>
                <div class="list_menu">
                    <ul class="list_menu_button fr">
                        <li><a class="add" href="/Products/RecommendEdit?id=0">添加</a></li>
                    </ul>
                </div>
                <div class="box_table">
                    <recommend-table/>
                </div>
                <div class="scroll_sticky">
                    <div class="scroll_sticky_content">
                        <div style="width: 1800px; height: 1px;"></div>
                    </div>
                </div>
                <mx-pager name="recommendList" total="@Model.TotalCount" page-size="@Model.PageSize"
                          page-num="@Model.PageNum"/>
            </div>
        </div>
    </div>

</div>