using Azure;
using Entitys;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using YseStore.IService;
using YseStore.IService.Products;
using YseStore.Model.Event;
using YseStore.Model.VM;
using YseStore.Service;

namespace YseStoreAdmin.Pages.Components.Products
{
    public class ProductFeedback : MComponent
    {
        public int TotalCount { get; set; }
        public int PageSize { get; set; }
        public int PageNum { get; set; }
        public string keywords { get; set; }
        public string MenuSort = "";
        public IProductFeedbackService _productFeedbackService { get; }

        public PagedList<ProductsReviewResponse>? PhotosReviewList { get; set; }

        public ProductFeedback(IProductFeedbackService productFeedbackService)
        {

            _productFeedbackService = productFeedbackService;
        }
        public override void Mount()
        {
            base.Mount();

        }

        public void HandlePageEvent(PageEvent evt)
        {
            TotalCount = evt.Total;
            PageSize = evt.PageSize;
            PageNum = evt.PageNum;

        }
        public async Task ChangePage1()
        {
            await Task.CompletedTask;
        }
        [SkipOutput]
        public async Task TurnPage(int page)
        {
           
            DispatchGlobal<UserTurnPageEvent>(new UserTurnPageEvent("manlog", page));

        }
        public override async Task MountAsync()
        {
            keywords = HttpContext.Request.Query["keyword"].ToString();
            MenuSort = HttpContext.Request.Query["MenuSort"].ToString();
            //PhotosReviewList = await GetLogs();
        }

        //public async Task<PagedList<ProductsReviewResponse>> GetLogs(int page = 1)
        //{
            
        //    var result = await _productFeedbackService.QueryAsync(keywords, page, 10);

        //    return result;
        //}





    }
}
