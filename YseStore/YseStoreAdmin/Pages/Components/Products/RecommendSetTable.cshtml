@model YseStoreAdmin.Pages.Components.Products.RecommendSetTable
@{
}

<div>
    <table border="0" cellpadding="5" cellspacing="0" class="r_con_table new">
        <thead>
        <tr>
            <td width="10%" nowrap="nowrap" class="pos">
                <ul class="table_menu_button global_menu_button">
                    <li>
                        <div class="btn_checkbox ">
                            <em class="button"></em><input type="checkbox"
                                                           name="select_all" value="">
                        </div>
                    </li>
                    <li class="open">已选择<span></span>个</li>
                    <li><a class="del" href="javascript:;">删除</a></li>
                </ul>
            </td>
            <td width="25%" nowrap="nowrap">标题</td>
            <td width="25%" nowrap="nowrap">产品</td>
            <td width="28%" nowrap="nowrap">推荐类型</td>
            <td width="110" nowrap="nowrap"></td>
        </tr>
        </thead>
        <tbody>
        @if (Model.Recommends.Any())
        {
            @foreach (var recommend in Model.Recommends)
            {
                <tr data-id="@recommend.RId">
                    <td>
                        <div class="btn_checkbox ">
                            <em class="button"></em><input type="checkbox"
                                                           name="select" value="@recommend.RId">
                        </div>
                    </td>
                    <td>@Model.GetTitleFromData(recommend.Data)</td>
                    <td>
                        <div class="products_number" data-id="@recommend.RId">
                            <div class="products_txt">@Model.GetProductCountText(recommend.ProductsValue) <i></i></div>
                        </div>
                    </td>
                    <td>@Model.GetProductsTypeText(recommend.ProductsScope)</td>
                    <td nowrap="nowrap" class="operation tar">
                        <a class="icon_edit oper_icon button_tips"
                           href="/Products/RecommendSetEdit?id=@recommend.RId">修改</a>
                        <a class="icon_view oper_icon button_tips"
                           href="javascript:;">预览</a>
                    </td>
                </tr>
            }
        }
        else
        {
            <tr>
                <td colspan="5" class="text-center">暂无推荐集数据</td>
            </tr>
        }
        </tbody>
    </table>
</div>