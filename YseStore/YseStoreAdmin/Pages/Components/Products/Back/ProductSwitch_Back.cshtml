 
@model YseStoreAdmin.Pages.Components.Products.ProductSwitch 
@{
}
<div>

    <div id="products_switch" class="r_con_wrap plugins_app_box" style="height: 409px;">
        <div class="return_title new_return_title">
            <a href="/manage/plugins/my-app"><span class="return">我的应用</span></a>
        </div>
        <div class="inside_container inside_menu_right clean">
           
            <h1>详情切换卡</h1>
        </div>
        <div>
            <div class="inside_table radius">
                <ul class="new_plugins_app_menu">
                    <li><a href="javascript:;" class="current">切换卡</a></li>
                    <li><a href="/Products/SwitchContent">内容</a></li>
                </ul>
                <div class="list_menu app_list_menu">
                    <ul class="list_menu_button fr">
                        <li><a href="javascript:;" class="set btn_switch_set">设置</a></li>
                        <li><a href="javascript:;" class="add btn_switch_edit" data-id="0">添加</a></li>
                    </ul>
                    <div class="search_form fl">
                        <form id="w0" action="/manage/plugins/product/switch" method="get">
                            <div class="k_input">
                                <input type="text" name="keyword" value="" placeholder="请输入关键词"
                                       class="form_input long_form_input" size="15" autocomplete="off">
                            </div>
                            <input type="submit" class="search_btn" value="搜索">
                            <div class="clear"></div>
                        </form>
                    </div>
                </div>
                <table border="0" cellpadding="5" cellspacing="0" class="r_con_table">
                    <thead>
                        <tr>
                            <td nowrap="" width="7%" class="pos">
                                <ul class="table_menu_button global_menu_button">
                                    <li>
                                        <div class="btn_checkbox ">
                                            <em class="button"></em><input type="checkbox"
                                                                           name="select_all" value="">
                                        </div>
                                    </li>
                                    <li class="open">已选择<span></span>个</li>
                                    <li><a class="del btn_switch_delete" href="javascript:;">删除</a></li>
                                </ul>
                            </td>
                            <td nowrap="" width="5%" class="myorder"></td>
                            <td nowrap="" width="30%">名称</td>
                            <td nowrap="" width="30%">适用范围</td>
                            <td nowrap="" width="30%">关联内容</td>
                            <td nowrap="" width="200" class="operation"></td>
                        </tr>
                    </thead>
                    <tbody data-listidx="0">
                        <tr data-id="4" data-apply="Website" style="cursor: pointer;">
                            <td nowrap="">
                                <div class="btn_checkbox ">
                                    <em class="button"></em><input type="checkbox" name="select"
                                                                   value="4">
                                </div>
                            </td>
                            <td nowrap="" align="center" class="myorder move_myorder" data="move_myorder">
                                <i class="icon_myorder"></i>
                            </td>
                            <td nowrap="" class="name">FAQ</td>
                            <td nowrap="" class="apply">全场产品</td>
                            <td nowrap="">
                                <div class="box_related_info">
                                    <div class="related_txt">关联全部产品</div>
                                    <div class="related_container">
                                        <div class="related_box" data-page="1"></div>
                                    </div>
                                </div>
                            </td>
                            <td nowrap="" class="operation tar">
                                <a class="icon_edit oper_icon button_tips btn_switch_edit" href="javascript:;"
                                   data-id="4">修改</a>
                                <a class="icon_content oper_icon button_tips"
                                   href="/manage/plugins/product/switch-content?id=4">内容</a>
                            </td>
                        </tr>
                        <tr data-id="7" data-apply="Website" style="cursor: pointer;">
                            <td nowrap="">
                                <div class="btn_checkbox ">
                                    <em class="button"></em><input type="checkbox" name="select"
                                                                   value="7">
                                </div>
                            </td>
                            <td nowrap="" align="center" class="myorder move_myorder" data="move_myorder">
                                <i class="icon_myorder"></i>
                            </td>
                            <td nowrap="" class="name">Aiuto Allo Shopping</td>
                            <td nowrap="" class="apply">全场产品</td>
                            <td nowrap="">
                                <div class="box_related_info">
                                    <div class="related_txt">关联全部产品</div>
                                    <div class="related_container">
                                        <div class="related_box" data-page="1"></div>
                                    </div>
                                </div>
                            </td>
                            <td nowrap="" class="operation tar">
                                <a class="icon_edit oper_icon button_tips btn_switch_edit" href="javascript:;"
                                   data-id="7">修改</a>
                                <a class="icon_content oper_icon button_tips"
                                   href="/manage/plugins/product/switch-content?id=7">内容</a>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
        <div class="fixed_box_popup box_switch_edit" data-width="720">
            <div class="box_middle">
                <a href="javascript:;" class="close"></a>
                <div class="title">添加</div>
                <div class="content">
                    <form id="form_switch_edit" class="global_form" action="/manage/plugins/product/switch-edit"
                          method="post">
                        <div class="rows clean">
                            <label>名称<span class="fs12 color_888">(必填)</span></label>
                            <div class="input">
                                <input name="Name" value="" type="text" class="box_input full_input" maxlength="20"
                                       size="20" notnull="">
                            </div>
                        </div>
                        <div class="rows clean">
                            <label>适用范围</label>
                            <div class="input">
                                <div class="box_type_menu fl">
                                    <span class="item checked">
                                        <input type="radio" name="Apply" value="Website"
                                               checked="">全场产品
                                    </span>
                                    <span class="item"><input type="radio" name="Apply" value="Products">指定产品</span>
                                    <span class="item"><input type="radio" name="Apply" value="Categories">指定分类</span>
                                </div>
                                <div class="clear"></div>
                            </div>
                        </div>
                        <div class="rows clean box_submit">
                            <label></label>
                            <div class="input input_button">
                                <input type="submit" class="btn_global btn_submit" value="保存">
                                <input type="button" class="btn_global btn_cancel" value="取消">
                            </div>
                        </div>
                        <input type="hidden" name="id" value="0">
                        <input type="hidden" name="do_action" value="/manage/plugins/product/switch-edit">
                    </form>
                </div>
            </div>
        </div>
        <div id="fixed_right">
            <div class="global_container box_switch_set" data-width="350">
                <div class="top_title"><span>设置</span><a href="javascript:;" class="close"></a></div>
                <form id="form_switch_set" class="global_form" action="/manage/plugins/product/switch" method="post">
                    <input type="hidden" name="_csrf-manage"
                           value="RECKe7HF8XpA5TczOXbikh65m2AIohdKqdt1PNq5VSAQJuYQxJaIOwS1YVpWI9Tlevz4AjvhZizz6yYMs4BsEQ==">
                    <div class="rows clean box_set_list">
                        <label></label>
                        <div class="input">
                            <div class="set_item current">
                                <input type="checkbox" name="type" value="horizontal" checked="">
                                <div class="set_item_title">横排</div>
                                <img src="/manage/web/shop/images/plugins/app/products_switch_type_horizontal.png">
                            </div>
                            <div class="set_item">
                                <input type="checkbox" name="type" value="vertical">
                                <div class="set_item_title">竖排</div>
                                <img src="/manage/web/shop/images/plugins/app/products_switch_type_vertical.png">
                            </div>
                        </div>
                    </div>
                    <div class="rows clean box_submit">
                        <label></label>
                        <div class="input input_button">
                            <input type="submit" class="btn_global btn_submit" value="保存"> <input type="button"
                                                                                                  class="btn_global btn_cancel" value="取消">
                        </div>
                    </div>
                    <input type="hidden" name="do_action" value="/manage/plugins/product/switch-set">
                </form>
            </div>
        </div>
    </div>

</div>