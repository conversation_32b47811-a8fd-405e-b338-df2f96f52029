 
@model YseStoreAdmin.Pages.Components.Products.ProductSwitchContent
@{
}
<div>

    <div id="products_switch" class="r_con_wrap plugins_app_box" style="height: 409px;">
        <div class="return_title new_return_title">
            <a href="/manage/plugins/my-app"><span class="return">我的应用</span></a>
        </div>
        <div class="inside_container inside_menu_right clean">
           
            <h1>详情切换卡</h1>
        </div>
        <div>
            <div class="inside_table radius">
                <ul class="new_plugins_app_menu">
                    <li><a href="/Products/Switch">切换卡</a></li>
                    <li><a href="javascript:;" class="current">内容</a></li>
                </ul>
                <div class="list_menu app_list_menu">
                    <ul class="list_menu_button fr">
                        <li>
                            <a class="add btn_switch_edit"
                               href="/manage/plugins/product/switch-content-view?id=0">添加</a>
                        </li>
                    </ul>
                    <div class="search_form fl">
                        <form id="w0" action="/manage/plugins/product/switch-content" method="get">
                            <div class="k_input">
                                <input type="text" name="keyword" value="" placeholder="请输入关键词"
                                       class="form_input long_form_input" size="15" autocomplete="off">
                            </div>
                            <input type="submit" class="search_btn" value="搜索">
                            <div class="clear"></div>
                        </form>
                    </div>
                </div>
                <table border="0" cellpadding="5" cellspacing="0" class="r_con_table">
                    <thead>
                        <tr>
                            <td width="7%" nowrap="nowrap" class="pos">
                                <ul class="table_menu_button global_menu_button">
                                    <li>
                                        <div class="btn_checkbox ">
                                            <em class="button"></em><input type="checkbox"
                                                                           name="select_all" value="">
                                        </div>
                                    </li>
                                    <li class="open">已选择<span></span>个</li>
                                    <li><a class="del btn_switch_content_delete" href="javascript:;">删除</a></li>
                                </ul>
                            </td>
                            <td width="45%" nowrap="nowrap">名称</td>
                            <td width="45%" nowrap="nowrap">隶属切换卡</td>
                            <td width="200" nowrap="nowrap" class="operation"></td>
                        </tr>
                    </thead>
                    <tbody data-listidx="0">
                        <tr style="cursor: pointer;">
                            <td nowrap="nowrap">
                                <div class="btn_checkbox ">
                                    <em class="button"></em><input type="checkbox" name="select"
                                                                   value="6">
                                </div>
                            </td>
                            <td nowrap="nowrap" class="name">Aiuto Allo Shopping</td>
                            <td nowrap="nowrap" class="apply">Aiuto Allo Shopping</td>
                            <td nowrap="nowrap" class="operation tar">
                                <a class="icon_edit oper_icon button_tips btn_switch_edit"
                                   href="/manage/plugins/product/switch-content-view?id=6">修改</a>
                            </td>
                        </tr>
                        <tr style="cursor: pointer;">
                            <td nowrap="nowrap">
                                <div class="btn_checkbox ">
                                    <em class="button"></em><input type="checkbox" name="select"
                                                                   value="4">
                                </div>
                            </td>
                            <td nowrap="nowrap" class="name">Post-Vendita</td>
                            <td nowrap="nowrap" class="apply">FAQ</td>
                            <td nowrap="nowrap" class="operation tar">
                                <a class="icon_edit oper_icon button_tips btn_switch_edit"
                                   href="/manage/plugins/product/switch-content-view?id=4">修改</a>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

</div>