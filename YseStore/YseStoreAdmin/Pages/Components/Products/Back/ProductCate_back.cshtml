
@model YseStoreAdmin.Pages.Components.Products.ProductCate
@{
}
<div>

    <div id="category" class="r_con_wrap" style="height: 626px;">
        <div class="inside_container">
            <h1>
                <ul class="list_menu_button fr">
                    <li><a class="g_btn_sec btn_category_search" href="javascript:;">搜索</a></li>
                    <li><a class="add" href="/Products/CateEdit?id=0">添加</a></li>
                </ul>
                <div class="menu_tips fr">
                    <div class="global_app_tips">
                        <em></em>
                        后台显示已下架的分类 <a href="javascript:;" class="btn_manage_category_show">设置</a>
                    </div>
                </div>
                产品分类
            </h1>
        </div>
        <div class="inside_table">
            <div class="r_con_table mock_table new">
                <div class="thead">
                    <div class="tr">
                        <div class="td c_order myorder"></div>
                        <div class="td c_select pos">
                            <ul class="table_menu_button global_menu_button left_10">
                                <li>
                                    <div class="btn_checkbox "><em class="button"></em><input type="checkbox"
                                                                                              name="select_all"
                                                                                              value=""></div>
                                </li>
                                <li class="open">已选择<span></span>个</li>
                                <li><a class="del" href="javascript:;">删除</a></li>
                            </ul>
                        </div>
                        <div class="td c_name">分类名称</div>
                        <div class="td c_id">分类ID</div>
                        <div class="td c_method">产品添加方式</div>
                        <div class="td operation"></div>
                    </div>
                </div>
                <div class="tbody" data-listidx="0">
                    <div class="first_box">
                        <div class="tr">
                            <div class="td c_order" data-id="0"></div>
                            <div class="td c_select"></div>
                            <div class="td c_name">
                                <a class="name">全场产品</a>
                            </div>
                            <div class="td c_id">-</div>
                            <div class="td c_method">-</div>
                            <div class="td operation tar">
                                <a class="oper_icon icon_sort button_tips"
                                   href="/manage/products/category/products-sorting">排序</a>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="tbody" data-listidx="1">
                    <div class="first_box">
                        <div class="tr">
                            <div class="td c_order myorder move_myorder" data="move_myorder" data-id="15"><i
                                    class="icon_myorder"></i></div>
                            <div class="td c_select">
                                <div class="btn_checkbox "><em class="button"></em><input type="checkbox" name="select"
                                                                                          value="15"></div>
                            </div>
                            <div class="td c_name">
                                <div class="nav_ext current"></div>
                                <a class="name" href="/manage/products/category/view/?id=15">Radioguide</a>
                            </div>
                            <div class="td c_id">15</div>
                            <div class="td c_method">手动添加</div>
                            <div class="td operation tar">
                                <a class="oper_icon icon_edit button_tips" href="/manage/products/category/view/?id=15">修改</a>
                                <a class="oper_icon icon_sort button_tips"
                                   href="/manage/products/category/view/?id=15&amp;goSort=1">排序</a>
                                <a class="oper_icon icon_sub button_tips"
                                   href="/manage/products/category/view/?id=0&amp;Level=1&amp;SupCateId=15">添加子分类</a>
                                <a class="oper_icon icon_transfer button_tips btn_products_transfer " data-cateid="15"
                                   data-name="Radioguide" href="javascript:;">产品转移</a>
                                <a class="oper_icon icon_soldout btn_soldout_category button_tips" data-id="15"
                                   href="javascript:;">下架</a>
                            </div>
                        </div>
                        <div class="second_box" data-child="15" data-listidx="0">
                            <div class="second_item">
                                <div class="tr">
                                    <div class="td c_order myorder move_myorder" data-id="20" data="move_myorder"><i
                                            class="icon_myorder"></i></div>
                                    <div class="td c_select">
                                        <div class="btn_checkbox "><em class="button"></em><input type="checkbox"
                                                                                                  name="select"
                                                                                                  value="20"></div>
                                    </div>
                                    <div class="td c_name">
                                        <a class="name" href="/manage/products/category/view/?id=20">T130P Radioguide
                                            per Gruppi</a>
                                    </div>
                                    <div class="td c_id">20</div>
                                    <div class="td c_method">手动添加</div>
                                    <div class="td operation tar">
                                        <a class="oper_icon icon_edit button_tips"
                                           href="/manage/products/category/view/?id=20">修改</a>
                                        <a class="oper_icon icon_sort button_tips"
                                           href="/manage/products/category/view/?id=20&amp;goSort=1">排序</a>
                                        <a class="oper_icon icon_sub button_tips"
                                           href="/manage/products/category/view/?id=0&amp;Level=2&amp;SupCateId=20">添加子分类</a>
                                        <a class="oper_icon icon_transfer button_tips btn_products_transfer "
                                           data-cateid="20" data-name="Radioguide > T130P Radioguide per Gruppi"
                                           href="javascript:;">产品转移</a>
                                        <a class="oper_icon icon_soldout btn_soldout_category button_tips" data-id="20"
                                           href="javascript:;">下架</a>
                                    </div>
                                </div>
                            </div>
                            <div class="second_item">
                                <div class="tr">
                                    <div class="td c_order myorder move_myorder" data-id="21" data="move_myorder"><i
                                            class="icon_myorder"></i></div>
                                    <div class="td c_select">
                                        <div class="btn_checkbox "><em class="button"></em><input type="checkbox"
                                                                                                  name="select"
                                                                                                  value="21"></div>
                                    </div>
                                    <div class="td c_name">
                                        <a class="name" href="/manage/products/category/view/?id=21">T130S Whisper Tour
                                            Guide System</a>
                                    </div>
                                    <div class="td c_id">21</div>
                                    <div class="td c_method">手动添加</div>
                                    <div class="td operation tar">
                                        <a class="oper_icon icon_edit button_tips"
                                           href="/manage/products/category/view/?id=21">修改</a>
                                        <a class="oper_icon icon_sort button_tips"
                                           href="/manage/products/category/view/?id=21&amp;goSort=1">排序</a>
                                        <a class="oper_icon icon_sub button_tips"
                                           href="/manage/products/category/view/?id=0&amp;Level=2&amp;SupCateId=21">添加子分类</a>
                                        <a class="oper_icon icon_transfer button_tips btn_products_transfer "
                                           data-cateid="21" data-name="Radioguide > T130S Whisper Tour Guide System"
                                           href="javascript:;">产品转移</a>
                                        <a class="oper_icon icon_soldout btn_soldout_category button_tips" data-id="21"
                                           href="javascript:;">下架</a>
                                    </div>
                                </div>
                            </div>
                            <div class="second_item">
                                <div class="tr">
                                    <div class="td c_order myorder move_myorder" data-id="31" data="move_myorder"><i
                                            class="icon_myorder"></i></div>
                                    <div class="td c_select">
                                        <div class="btn_checkbox "><em class="button"></em><input type="checkbox"
                                                                                                  name="select"
                                                                                                  value="31"></div>
                                    </div>
                                    <div class="td c_name">
                                        <a class="name" href="/manage/products/category/view/?id=31">TT106 Auricolari
                                            per Gruppi Turistici</a>
                                    </div>
                                    <div class="td c_id">31</div>
                                    <div class="td c_method">手动添加</div>
                                    <div class="td operation tar">
                                        <a class="oper_icon icon_edit button_tips"
                                           href="/manage/products/category/view/?id=31">修改</a>
                                        <a class="oper_icon icon_sort button_tips"
                                           href="/manage/products/category/view/?id=31&amp;goSort=1">排序</a>
                                        <a class="oper_icon icon_sub button_tips"
                                           href="/manage/products/category/view/?id=0&amp;Level=2&amp;SupCateId=31">添加子分类</a>
                                        <a class="oper_icon icon_transfer button_tips btn_products_transfer "
                                           data-cateid="31"
                                           data-name="Radioguide > TT106 Auricolari per Gruppi Turistici"
                                           href="javascript:;">产品转移</a>
                                        <a class="oper_icon icon_soldout btn_soldout_category button_tips" data-id="31"
                                           href="javascript:;">下架</a>
                                    </div>
                                </div>
                            </div>
                            <div class="second_item">
                                <div class="tr">
                                    <div class="td c_order myorder move_myorder" data-id="28" data="move_myorder"><i
                                            class="icon_myorder"></i></div>
                                    <div class="td c_select">
                                        <div class="btn_checkbox "><em class="button"></em><input type="checkbox"
                                                                                                  name="select"
                                                                                                  value="28"></div>
                                    </div>
                                    <div class="td c_name">
                                        <a class="name" href="/manage/products/category/view/?id=28">TT116 Attrezzature
                                            per Guide Turistiche</a>
                                    </div>
                                    <div class="td c_id">28</div>
                                    <div class="td c_method">手动添加</div>
                                    <div class="td operation tar">
                                        <a class="oper_icon icon_edit button_tips"
                                           href="/manage/products/category/view/?id=28">修改</a>
                                        <a class="oper_icon icon_sort button_tips"
                                           href="/manage/products/category/view/?id=28&amp;goSort=1">排序</a>
                                        <a class="oper_icon icon_sub button_tips"
                                           href="/manage/products/category/view/?id=0&amp;Level=2&amp;SupCateId=28">添加子分类</a>
                                        <a class="oper_icon icon_transfer button_tips btn_products_transfer "
                                           data-cateid="28"
                                           data-name="Radioguide > TT116 Attrezzature per Guide Turistiche"
                                           href="javascript:;">产品转移</a>
                                        <a class="oper_icon icon_soldout btn_soldout_category button_tips" data-id="28"
                                           href="javascript:;">下架</a>
                                    </div>
                                </div>
                            </div>
                            <div class="second_item">
                                <div class="tr">
                                    <div class="td c_order myorder move_myorder" data-id="22" data="move_myorder"><i
                                            class="icon_myorder"></i></div>
                                    <div class="td c_select">
                                        <div class="btn_checkbox "><em class="button"></em><input type="checkbox"
                                                                                                  name="select"
                                                                                                  value="22"></div>
                                    </div>
                                    <div class="td c_name">
                                        <a class="name" href="/manage/products/category/view/?id=22">TT127 Radio Guide
                                            Turistiche con Lettore MP3</a>
                                    </div>
                                    <div class="td c_id">22</div>
                                    <div class="td c_method">手动添加</div>
                                    <div class="td operation tar">
                                        <a class="oper_icon icon_edit button_tips"
                                           href="/manage/products/category/view/?id=22">修改</a>
                                        <a class="oper_icon icon_sort button_tips"
                                           href="/manage/products/category/view/?id=22&amp;goSort=1">排序</a>
                                        <a class="oper_icon icon_sub button_tips"
                                           href="/manage/products/category/view/?id=0&amp;Level=2&amp;SupCateId=22">添加子分类</a>
                                        <a class="oper_icon icon_transfer button_tips btn_products_transfer "
                                           data-cateid="22"
                                           data-name="Radioguide > TT127 Radio Guide Turistiche con Lettore MP3"
                                           href="javascript:;">产品转移</a>
                                        <a class="oper_icon icon_soldout btn_soldout_category button_tips" data-id="22"
                                           href="javascript:;">下架</a>
                                    </div>
                                </div>
                            </div>
                            <div class="second_item">
                                <div class="tr">
                                    <div class="td c_order myorder move_myorder" data-id="23" data="move_myorder"><i
                                            class="icon_myorder"></i></div>
                                    <div class="td c_select">
                                        <div class="btn_checkbox "><em class="button"></em><input type="checkbox"
                                                                                                  name="select"
                                                                                                  value="23"></div>
                                    </div>
                                    <div class="td c_name">
                                        <a class="name" href="/manage/products/category/view/?id=23">TT126 Sistema di
                                            Comunicazione Radio Guida Bidirezionale</a>
                                    </div>
                                    <div class="td c_id">23</div>
                                    <div class="td c_method">手动添加</div>
                                    <div class="td operation tar">
                                        <a class="oper_icon icon_edit button_tips"
                                           href="/manage/products/category/view/?id=23">修改</a>
                                        <a class="oper_icon icon_sort button_tips"
                                           href="/manage/products/category/view/?id=23&amp;goSort=1">排序</a>
                                        <a class="oper_icon icon_sub button_tips"
                                           href="/manage/products/category/view/?id=0&amp;Level=2&amp;SupCateId=23">添加子分类</a>
                                        <a class="oper_icon icon_transfer button_tips btn_products_transfer "
                                           data-cateid="23"
                                           data-name="Radioguide > TT126 Sistema di Comunicazione Radio Guida Bidirezionale"
                                           href="javascript:;">产品转移</a>
                                        <a class="oper_icon icon_soldout btn_soldout_category button_tips" data-id="23"
                                           href="javascript:;">下架</a>
                                    </div>
                                </div>
                            </div>
                            <div class="second_item">
                                <div class="tr">
                                    <div class="td c_order myorder move_myorder" data-id="24" data="move_myorder"><i
                                            class="icon_myorder"></i></div>
                                    <div class="td c_select">
                                        <div class="btn_checkbox "><em class="button"></em><input type="checkbox"
                                                                                                  name="select"
                                                                                                  value="24"></div>
                                    </div>
                                    <div class="td c_name">
                                        <a class="name" href="/manage/products/category/view/?id=24">T130U Guida Radio
                                            Digitale UHF</a>
                                    </div>
                                    <div class="td c_id">24</div>
                                    <div class="td c_method">手动添加</div>
                                    <div class="td operation tar">
                                        <a class="oper_icon icon_edit button_tips"
                                           href="/manage/products/category/view/?id=24">修改</a>
                                        <a class="oper_icon icon_sort button_tips"
                                           href="/manage/products/category/view/?id=24&amp;goSort=1">排序</a>
                                        <a class="oper_icon icon_sub button_tips"
                                           href="/manage/products/category/view/?id=0&amp;Level=2&amp;SupCateId=24">添加子分类</a>
                                        <a class="oper_icon icon_transfer button_tips btn_products_transfer "
                                           data-cateid="24" data-name="Radioguide > T130U Guida Radio Digitale UHF"
                                           href="javascript:;">产品转移</a>
                                        <a class="oper_icon icon_soldout btn_soldout_category button_tips" data-id="24"
                                           href="javascript:;">下架</a>
                                    </div>
                                </div>
                            </div>
                            <div class="second_item">
                                <div class="tr">
                                    <div class="td c_order myorder move_myorder" data-id="25" data="move_myorder"><i
                                            class="icon_myorder"></i></div>
                                    <div class="td c_select">
                                        <div class="btn_checkbox "><em class="button"></em><input type="checkbox"
                                                                                                  name="select"
                                                                                                  value="25"></div>
                                    </div>
                                    <div class="td c_name">
                                        <a class="name" href="/manage/products/category/view/?id=25">TT128 Audioguide
                                            Musei</a>
                                    </div>
                                    <div class="td c_id">25</div>
                                    <div class="td c_method">手动添加</div>
                                    <div class="td operation tar">
                                        <a class="oper_icon icon_edit button_tips"
                                           href="/manage/products/category/view/?id=25">修改</a>
                                        <a class="oper_icon icon_sort button_tips"
                                           href="/manage/products/category/view/?id=25&amp;goSort=1">排序</a>
                                        <a class="oper_icon icon_sub button_tips"
                                           href="/manage/products/category/view/?id=0&amp;Level=2&amp;SupCateId=25">添加子分类</a>
                                        <a class="oper_icon icon_transfer button_tips btn_products_transfer "
                                           data-cateid="25" data-name="Radioguide > TT128 Audioguide Musei"
                                           href="javascript:;">产品转移</a>
                                        <a class="oper_icon icon_soldout btn_soldout_category button_tips" data-id="25"
                                           href="javascript:;">下架</a>
                                    </div>
                                </div>
                            </div>
                            <div class="second_item">
                                <div class="tr">
                                    <div class="td c_order myorder move_myorder" data-id="30" data="move_myorder"><i
                                            class="icon_myorder"></i></div>
                                    <div class="td c_select">
                                        <div class="btn_checkbox "><em class="button"></em><input type="checkbox"
                                                                                                  name="select"
                                                                                                  value="30"></div>
                                    </div>
                                    <div class="td c_name">
                                        <a class="name" href="/manage/products/category/view/?id=30">T130 Radioguide per
                                            Gruppi</a>
                                    </div>
                                    <div class="td c_id">30</div>
                                    <div class="td c_method">手动添加</div>
                                    <div class="td operation tar">
                                        <a class="oper_icon icon_edit button_tips"
                                           href="/manage/products/category/view/?id=30">修改</a>
                                        <a class="oper_icon icon_sort button_tips"
                                           href="/manage/products/category/view/?id=30&amp;goSort=1">排序</a>
                                        <a class="oper_icon icon_sub button_tips"
                                           href="/manage/products/category/view/?id=0&amp;Level=2&amp;SupCateId=30">添加子分类</a>
                                        <a class="oper_icon icon_transfer button_tips btn_products_transfer "
                                           data-cateid="30" data-name="Radioguide > T130 Radioguide per Gruppi"
                                           href="javascript:;">产品转移</a>
                                        <a class="oper_icon icon_soldout btn_soldout_category button_tips" data-id="30"
                                           href="javascript:;">下架</a>
                                    </div>
                                </div>
                            </div>
                            <div class="second_item">
                                <div class="tr">
                                    <div class="td c_order myorder move_myorder" data-id="32" data="move_myorder"><i
                                            class="icon_myorder"></i></div>
                                    <div class="td c_select">
                                        <div class="btn_checkbox "><em class="button"></em><input type="checkbox"
                                                                                                  name="select"
                                                                                                  value="32"></div>
                                    </div>
                                    <div class="td c_name">
                                        <a class="name" href="/manage/products/category/view/?id=32">TT112
                                            Radioguida</a>
                                    </div>
                                    <div class="td c_id">32</div>
                                    <div class="td c_method">手动添加</div>
                                    <div class="td operation tar">
                                        <a class="oper_icon icon_edit button_tips"
                                           href="/manage/products/category/view/?id=32">修改</a>
                                        <a class="oper_icon icon_sort button_tips"
                                           href="/manage/products/category/view/?id=32&amp;goSort=1">排序</a>
                                        <a class="oper_icon icon_sub button_tips"
                                           href="/manage/products/category/view/?id=0&amp;Level=2&amp;SupCateId=32">添加子分类</a>
                                        <a class="oper_icon icon_transfer button_tips btn_products_transfer "
                                           data-cateid="32" data-name="Radioguide > TT112 Radioguida"
                                           href="javascript:;">产品转移</a>
                                        <a class="oper_icon icon_soldout btn_soldout_category button_tips" data-id="32"
                                           href="javascript:;">下架</a>
                                    </div>
                                </div>
                            </div>
                            <div class="second_item">
                                <div class="tr">
                                    <div class="td c_order myorder move_myorder" data-id="34" data="move_myorder"><i
                                            class="icon_myorder"></i></div>
                                    <div class="td c_select">
                                        <div class="btn_checkbox "><em class="button"></em><input type="checkbox"
                                                                                                  name="select"
                                                                                                  value="34"></div>
                                    </div>
                                    <div class="td c_name">
                                        <a class="name" href="/manage/products/category/view/?id=34">TT122 Sistemi Audio
                                            per Guide Turistiche</a>
                                    </div>
                                    <div class="td c_id">34</div>
                                    <div class="td c_method">手动添加</div>
                                    <div class="td operation tar">
                                        <a class="oper_icon icon_edit button_tips"
                                           href="/manage/products/category/view/?id=34">修改</a>
                                        <a class="oper_icon icon_sort button_tips"
                                           href="/manage/products/category/view/?id=34&amp;goSort=1">排序</a>
                                        <a class="oper_icon icon_sub button_tips"
                                           href="/manage/products/category/view/?id=0&amp;Level=2&amp;SupCateId=34">添加子分类</a>
                                        <a class="oper_icon icon_transfer button_tips btn_products_transfer "
                                           data-cateid="34"
                                           data-name="Radioguide > TT122 Sistemi Audio per Guide Turistiche"
                                           href="javascript:;">产品转移</a>
                                        <a class="oper_icon icon_soldout btn_soldout_category button_tips" data-id="34"
                                           href="javascript:;">下架</a>
                                    </div>
                                </div>
                            </div>
                            <div class="second_item">
                                <div class="tr">
                                    <div class="td c_order myorder move_myorder" data-id="33" data="move_myorder"><i
                                            class="icon_myorder"></i></div>
                                    <div class="td c_select">
                                        <div class="btn_checkbox "><em class="button"></em><input type="checkbox"
                                                                                                  name="select"
                                                                                                  value="33"></div>
                                    </div>
                                    <div class="td c_name">
                                        <a class="name" href="/manage/products/category/view/?id=33">TT120 Sistema Radio
                                            per Allenamento di Nuoto</a>
                                    </div>
                                    <div class="td c_id">33</div>
                                    <div class="td c_method">手动添加</div>
                                    <div class="td operation tar">
                                        <a class="oper_icon icon_edit button_tips"
                                           href="/manage/products/category/view/?id=33">修改</a>
                                        <a class="oper_icon icon_sort button_tips"
                                           href="/manage/products/category/view/?id=33&amp;goSort=1">排序</a>
                                        <a class="oper_icon icon_sub button_tips"
                                           href="/manage/products/category/view/?id=0&amp;Level=2&amp;SupCateId=33">添加子分类</a>
                                        <a class="oper_icon icon_transfer button_tips btn_products_transfer "
                                           data-cateid="33"
                                           data-name="Radioguide > TT120 Sistema Radio per Allenamento di Nuoto"
                                           href="javascript:;">产品转移</a>
                                        <a class="oper_icon icon_soldout btn_soldout_category button_tips" data-id="33"
                                           href="javascript:;">下架</a>
                                    </div>
                                </div>
                            </div>
                            <div class="second_item">
                                <div class="tr">
                                    <div class="td c_order myorder move_myorder" data-id="29" data="move_myorder"><i
                                            class="icon_myorder"></i></div>
                                    <div class="td c_select">
                                        <div class="btn_checkbox "><em class="button"></em><input type="checkbox"
                                                                                                  name="select"
                                                                                                  value="29"></div>
                                    </div>
                                    <div class="td c_name">
                                        <a class="name" href="/manage/products/category/view/?id=29">Accessori</a>
                                    </div>
                                    <div class="td c_id">29</div>
                                    <div class="td c_method">手动添加</div>
                                    <div class="td operation tar">
                                        <a class="oper_icon icon_edit button_tips"
                                           href="/manage/products/category/view/?id=29">修改</a>
                                        <a class="oper_icon icon_sort button_tips"
                                           href="/manage/products/category/view/?id=29&amp;goSort=1">排序</a>
                                        <a class="oper_icon icon_sub button_tips"
                                           href="/manage/products/category/view/?id=0&amp;Level=2&amp;SupCateId=29">添加子分类</a>
                                        <a class="oper_icon icon_transfer button_tips btn_products_transfer "
                                           data-cateid="29" data-name="Radioguide > Accessori"
                                           href="javascript:;">产品转移</a>
                                        <a class="oper_icon icon_soldout btn_soldout_category button_tips" data-id="29"
                                           href="javascript:;">下架</a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="first_box">
                        <div class="tr">
                            <div class="td c_order myorder move_myorder" data="move_myorder" data-id="16"><i
                                    class="icon_myorder"></i></div>
                            <div class="td c_select">
                                <div class="btn_checkbox "><em class="button"></em><input type="checkbox" name="select"
                                                                                          value="16"></div>
                            </div>
                            <div class="td c_name">
                                <a class="name" href="/manage/products/category/view/?id=16">Sistema di Cercapersone</a>
                            </div>
                            <div class="td c_id">16</div>
                            <div class="td c_method">手动添加</div>
                            <div class="td operation tar">
                                <a class="oper_icon icon_edit button_tips" href="/manage/products/category/view/?id=16">修改</a>
                                <a class="oper_icon icon_sort button_tips"
                                   href="/manage/products/category/view/?id=16&amp;goSort=1">排序</a>
                                <a class="oper_icon icon_sub button_tips"
                                   href="/manage/products/category/view/?id=0&amp;Level=1&amp;SupCateId=16">添加子分类</a>
                                <a class="oper_icon icon_transfer button_tips btn_products_transfer " data-cateid="16"
                                   data-name="Sistema di Cercapersone" href="javascript:;">产品转移</a>
                                <a class="oper_icon icon_soldout btn_soldout_category button_tips" data-id="16"
                                   href="javascript:;">下架</a>
                            </div>
                        </div>
                    </div>
                    <div class="first_box">
                        <div class="tr">
                            <div class="td c_order myorder move_myorder" data="move_myorder" data-id="18"><i
                                    class="icon_myorder"></i></div>
                            <div class="td c_select">
                                <div class="btn_checkbox "><em class="button"></em><input type="checkbox" name="select"
                                                                                          value="18"></div>
                            </div>
                            <div class="td c_name">
                                <div class="nav_ext current"></div>
                                <a class="name" href="/manage/products/category/view/?id=18">Sistema di Chiamata</a>
                            </div>
                            <div class="td c_id">18</div>
                            <div class="td c_method">手动添加</div>
                            <div class="td operation tar">
                                <a class="oper_icon icon_edit button_tips" href="/manage/products/category/view/?id=18">修改</a>
                                <a class="oper_icon icon_sort button_tips"
                                   href="/manage/products/category/view/?id=18&amp;goSort=1">排序</a>
                                <a class="oper_icon icon_sub button_tips"
                                   href="/manage/products/category/view/?id=0&amp;Level=1&amp;SupCateId=18">添加子分类</a>
                                <a class="oper_icon icon_transfer button_tips btn_products_transfer " data-cateid="18"
                                   data-name="Sistema di Chiamata" href="javascript:;">产品转移</a>
                                <a class="oper_icon icon_soldout btn_soldout_category button_tips" data-id="18"
                                   href="javascript:;">下架</a>
                            </div>
                        </div>
                        <div class="second_box" data-child="18" data-listidx="1">
                            <div class="second_item">
                                <div class="tr">
                                    <div class="td c_order myorder move_myorder" data-id="19" data="move_myorder"><i
                                            class="icon_myorder"></i></div>
                                    <div class="td c_select">
                                        <div class="btn_checkbox "><em class="button"></em><input type="checkbox"
                                                                                                  name="select"
                                                                                                  value="19"></div>
                                    </div>
                                    <div class="td c_name">
                                        <a class="name" href="/manage/products/category/view/?id=19">cercapersone di
                                            base</a>
                                    </div>
                                    <div class="td c_id">19</div>
                                    <div class="td c_method">手动添加</div>
                                    <div class="td operation tar">
                                        <a class="oper_icon icon_edit button_tips"
                                           href="/manage/products/category/view/?id=19">修改</a>
                                        <a class="oper_icon icon_sort button_tips"
                                           href="/manage/products/category/view/?id=19&amp;goSort=1">排序</a>
                                        <a class="oper_icon icon_sub button_tips"
                                           href="/manage/products/category/view/?id=0&amp;Level=2&amp;SupCateId=19">添加子分类</a>
                                        <a class="oper_icon icon_transfer button_tips btn_products_transfer "
                                           data-cateid="19" data-name="Sistema di Chiamata > cercapersone di base"
                                           href="javascript:;">产品转移</a>
                                        <a class="oper_icon icon_soldout btn_soldout_category button_tips" data-id="19"
                                           href="javascript:;">下架</a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="first_box">
                        <div class="tr">
                            <div class="td c_order myorder move_myorder" data="move_myorder" data-id="26"><i
                                    class="icon_myorder"></i></div>
                            <div class="td c_select">
                                <div class="btn_checkbox "><em class="button"></em><input type="checkbox" name="select"
                                                                                          value="26"></div>
                            </div>
                            <div class="td c_name">
                                <a class="name" href="/manage/products/category/view/?id=26">Ham Radio</a>
                            </div>
                            <div class="td c_id">26</div>
                            <div class="td c_method">手动添加</div>
                            <div class="td operation tar">
                                <a class="oper_icon icon_edit button_tips" href="/manage/products/category/view/?id=26">修改</a>
                                <a class="oper_icon icon_sort button_tips"
                                   href="/manage/products/category/view/?id=26&amp;goSort=1">排序</a>
                                <a class="oper_icon icon_sub button_tips"
                                   href="/manage/products/category/view/?id=0&amp;Level=1&amp;SupCateId=26">添加子分类</a>
                                <a class="oper_icon icon_transfer button_tips btn_products_transfer " data-cateid="26"
                                   data-name="Ham Radio" href="javascript:;">产品转移</a>
                                <a class="oper_icon icon_soldout btn_soldout_category button_tips" data-id="26"
                                   href="javascript:;">下架</a>
                            </div>
                        </div>
                    </div>
                    <div class="first_box">
                        <div class="tr">
                            <div class="td c_order myorder move_myorder" data="move_myorder" data-id="27"><i
                                    class="icon_myorder"></i></div>
                            <div class="td c_select">
                                <div class="btn_checkbox "><em class="button"></em><input type="checkbox" name="select"
                                                                                          value="27"></div>
                            </div>
                            <div class="td c_name">
                                <a class="name" href="/manage/products/category/view/?id=27">Nuove Uscite</a>
                            </div>
                            <div class="td c_id">27</div>
                            <div class="td c_method">手动添加</div>
                            <div class="td operation tar">
                                <a class="oper_icon icon_edit button_tips" href="/manage/products/category/view/?id=27">修改</a>
                                <a class="oper_icon icon_sort button_tips"
                                   href="/manage/products/category/view/?id=27&amp;goSort=1">排序</a>
                                <a class="oper_icon icon_sub button_tips"
                                   href="/manage/products/category/view/?id=0&amp;Level=1&amp;SupCateId=27">添加子分类</a>
                                <a class="oper_icon icon_transfer button_tips btn_products_transfer " data-cateid="27"
                                   data-name="Nuove Uscite" href="javascript:;">产品转移</a>
                                <a class="oper_icon icon_soldout btn_soldout_category button_tips" data-id="27"
                                   href="javascript:;">下架</a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div id="fixed_right" class="category-index-right">
        <div class="global_container box_products_transfer" data-width="360">
            <div class="top_title">产品转移<a href="javascript:;" class="close"></a></div>
            <form class="global_form form_products_transfer">
                <div class="rows clean">
                    <label>当前分类</label>
                    <div class="input">
                        <div class="cur_category"></div>
                    </div>
                </div>
                <div class="rows clean">
                    <label>目标分类</label>
                    <div class="input">
                        <dl class="box_drop_double" data-checkbox="0" data-showadd="0">
                            <dt>
                                <div class="box_select"><span>请选择</span><input type="hidden" name="TransferCateId"
                                                                                  value="" class="hidden_value"><input
                                        type="hidden" name="TransferCateIdType" value="" class="hidden_type"></div>
                            </dt>
                            <dd class="drop_down" style="display: none;">
                                <div class="drop_menu" data-type="productsEdit">
                                    <div class="global_app_tips obvious">
                                        <em></em><span>仅支持“手动添加”方式的分类</span></div>
                                    <a href="javascript:;" class="btn_back" data-value="" data-type="" data-table=""
                                       data-top="0" data-all="0" style="display:none;">返回</a>
                                    <div class="drop_skin" style="display: none;"></div>
                                    <div class="drop_list"
                                         data="[{&quot;Name&quot;:&quot;Radioguide&quot;,&quot;Value&quot;:15,&quot;Type&quot;:&quot;products_category&quot;,&quot;Table&quot;:&quot;products_category&quot;},{&quot;Name&quot;:&quot;Sistema di Cercapersone&quot;,&quot;Value&quot;:16,&quot;Type&quot;:&quot;products_category&quot;},{&quot;Name&quot;:&quot;Sistema di Chiamata&quot;,&quot;Value&quot;:18,&quot;Type&quot;:&quot;products_category&quot;,&quot;Table&quot;:&quot;products_category&quot;},{&quot;Name&quot;:&quot;Ham Radio&quot;,&quot;Value&quot;:26,&quot;Type&quot;:&quot;products_category&quot;},{&quot;Name&quot;:&quot;Nuove Uscite&quot;,&quot;Value&quot;:27,&quot;Type&quot;:&quot;products_category&quot;}]"
                                         data-more="none">
                                        <div class="item children" data-name="Radioguide" data-value="15"
                                             data-type="products_category" data-table="products_category" data-top="0"
                                             data-all="1" data-check-all="0" data-custom="undefined"><span
                                                class="input_radio_box"><span class="input_radio"><input type="radio"
                                                                                                         name="_DoubleOption[]"
                                                                                                         value="0"></span></span><span
                                                class="item_name">Radioguide</span><em></em></div>
                                        <div class="item" data-name="Sistema di Cercapersone" data-value="16"
                                             data-type="products_category" data-table="undefined" data-top="0"
                                             data-all="1" data-check-all="0" data-custom="undefined"><span
                                                class="input_radio_box"><span class="input_radio"><input type="radio"
                                                                                                         name="_DoubleOption[]"
                                                                                                         value="1"></span></span><span
                                                class="item_name">Sistema di Cercapersone</span></div>
                                        <div class="item children" data-name="Sistema di Chiamata" data-value="18"
                                             data-type="products_category" data-table="products_category" data-top="0"
                                             data-all="1" data-check-all="0" data-custom="undefined"><span
                                                class="input_radio_box"><span class="input_radio"><input type="radio"
                                                                                                         name="_DoubleOption[]"
                                                                                                         value="2"></span></span><span
                                                class="item_name">Sistema di Chiamata</span><em></em></div>
                                        <div class="item" data-name="Ham Radio" data-value="26"
                                             data-type="products_category" data-table="undefined" data-top="0"
                                             data-all="1" data-check-all="0" data-custom="undefined"><span
                                                class="input_radio_box"><span class="input_radio"><input type="radio"
                                                                                                         name="_DoubleOption[]"
                                                                                                         value="3"></span></span><span
                                                class="item_name">Ham Radio</span></div>
                                        <div class="item" data-name="Nuove Uscite" data-value="27"
                                             data-type="products_category" data-table="undefined" data-top="0"
                                             data-all="1" data-check-all="0" data-custom="undefined"><span
                                                class="input_radio_box"><span class="input_radio"><input type="radio"
                                                                                                         name="_DoubleOption[]"
                                                                                                         value="4"></span></span><span
                                                class="item_name">Nuove Uscite</span></div>
                                    </div>
                                    <a href="javascript:;" class="btn_load_more" data-value="products_category"
                                       data-type="products_category" data-table="products_category" data-top="0"
                                       data-all="1" data-check-all="0" data-start="1" style="display:none;">加载更多</a>
                                </div>
                            </dd>
                        </dl>
                    </div>
                </div>
                <div class="rows clean box_button box_submit">
                    <div class="input">
                        <input type="submit" class="btn_global btn_submit" value="保存">
                        <input type="button" class="btn_global btn_cancel" value="取消">
                    </div>
                </div>
                <input type="hidden" name="CateId" value="">
                <input type="hidden" name="do_action" value="/manage/products/category/transfer">
            </form>
        </div>
        <div class="global_container box_category_search" data-width="600">
            <div class="top_title">搜索分类<a href="javascript:;" class="close"></a></div>
            <div class="search_box">
                <form method="get" action="?">
                    <div class="k_input">
                        <input type="text" class="form_input" name="keyword" size="15" autocomplete="off"
                               placeholder="请输入关键词"> <input type="submit" class="search_btn" value="&#xe600;">
                    </div>
                    <div class="clear"></div>
                </form>
            </div>
            <table border="0" cellpadding="5" cellspacing="0" class="r_con_table search_con_table">
                <thead>
                <tr>
                    <td width="85" nowrap="" class="pos flex_item">
                        <ul class="table_menu_button global_menu_button left_10">
                            <li>
                                <div class="btn_checkbox "><em class="button"></em><input type="checkbox"
                                                                                          name="select_all" value="">
                                </div>
                            </li>
                            <li class="open">已选择<span></span>个</li>
                            <li><a class="del" href="javascript:;">删除</a></li>
                        </ul>
                    </td>
                    <td width="60%" nowrap="">分类名称</td>
                    <td width="136" nowrap="" class="operation flex_item last"></td>
                </tr>
                </thead>
                <tbody></tbody>
            </table>
            <div class="bg_no_table_data" style="height: 692px;">
                <div class="content" style="top: 306px;"><p>当前暂时没有数据</p></div>
                <span></span></div>
        </div>
        <div class="global_container box_manage_category_show" data-width="400">
            <div class="top_title">设置<a href="javascript:;" class="close"></a></div>
            <form class="global_form form_manage_category_show">
                <div class="rows clean">
                    <label>后台显示已下架的分类</label>
                    <div class="input">
                        <div class="blank6"></div>
                        <div class="switchery checked">
                            <input type="checkbox" name="SoldOutCategoryShow" value="1" checked="checked">
                            <div class="switchery_toggler"></div>
                            <div class="switchery_inner">
                                <div class="switchery_state_on"></div>
                                <div class="switchery_state_off"></div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="rows clean box_button box_submit">
                    <div class="input">
                        <input type="submit" class="btn_global btn_submit" value="保存">
                        <input type="button" class="btn_global btn_cancel" value="取消">
                    </div>
                </div>
                <input type="hidden" name="do_action" value="/manage/products/category/soldout-category-show">
            </form>
        </div>
        <div class="global_container global_sold_category box_soldout_category" data-width="400">
            <div class="top_title">下架分类<a href="javascript:;" class="close"></a></div>
            <form class="global_form form_soldout_category">
                <div class="list">
						<span class="input_checkbox_box checked disabled">
							<span class="input_checkbox">
								<input type="checkbox" checked="checked" disabled="" name="" value="1">
							</span>下架分类
						</span>
                </div>
                <div class="list">
						<span class="input_checkbox_box checked disabled">
							<span class="input_checkbox">
								<input type="checkbox" checked="checked" disabled="" name="" value="1">
							</span>下架子分类
						</span>
                </div>
                <div class="list sold_product">
						<span class="input_checkbox_box">
							<span class="input_checkbox">
								<input type="checkbox" name="soldProduct" value="1">
							</span>下架属于此分类和子分类的产品
						</span>
                    <div class="products_select">
							<span class="input_radio_box checked">
								<span class="input_radio"><input type="radio" checked="checked" name="productRange"
                                                                 value="all"></span>
								下架所有产品
							</span>
                        <span class="input_radio_box">
								<span class="input_radio"><input type="radio" name="productRange" value="only"></span>
								下架仅属于此分类及其子分类的产品
							</span>
                    </div>
                </div>
                <div class="list out_nav">
						<span class="input_checkbox_box checked">
							<span class="input_checkbox">
								<input type="checkbox" checked="checked" name="soldNav" value="1">
							</span>同步删除对应导航与子导航
						</span>
                    <div class="products_select">
							<span class="input_radio_box checked">
								<span class="input_radio"><input type="radio" name="outWithNavRadio" value="1"></span>
								同步删除对应导航与子导航
							</span>
                        <span class="input_radio_box">
								<span class="input_radio"><input type="radio" checked="checked" name="outWithNavRadio"
                                                                 value="0"></span>
								同步删除对应导航与子导航
							</span>
                    </div>
                </div>
                <div class="rows clean box_button box_submit">
                    <div class="input">
                        <input type="submit" class="btn_global btn_submit" value="保存">
                        <input type="button" class="btn_global btn_cancel" value="取消">
                    </div>
                </div>
                <input type="hidden" name="id" value="">
                <input type="hidden" name="soldOut" value="1">
                <input type="hidden" name="do_action" value="/manage/products/category/sold-out-batch">
            </form>
        </div>
        <div class="global_container global_sold_category box_soldin_category" data-width="400">
            <div class="top_title">上架分类<a href="javascript:;" class="close"></a></div>
            <form class="global_form form_soldin_category">
                <div class="list">
						<span class="input_checkbox_box checked disabled">
							<span class="input_checkbox">
								<input type="checkbox" checked="checked" disabled="" name="" value="1">
							</span>上架分类
						</span>
                </div>
                <div class="list">
						<span class="input_checkbox_box checked disabled">
							<span class="input_checkbox">
								<input type="checkbox" checked="checked" disabled="" name="" value="1">
							</span>上架父分类
						</span>
                </div>
                <div class="list sold_product">
						<span class="input_checkbox_box">
							<span class="input_checkbox">
								<input type="checkbox" name="soldProduct" value="1">
							</span>上架属于此分类和父分类的产品
						</span>
                    <div class="products_select">
							<span class="input_radio_box checked">
								<span class="input_radio"><input type="radio" checked="checked" name="productRange"
                                                                 value="all"></span>
								上架所有产品
							</span>
                        <span class="input_radio_box">
								<span class="input_radio"><input type="radio" name="productRange" value="only"></span>
								上架仅属于此分类及其父分类的产品
							</span>
                    </div>
                </div>
                <div class="rows clean box_button box_submit">
                    <div class="input">
                        <input type="submit" class="btn_global btn_submit" value="保存">
                        <input type="button" class="btn_global btn_cancel" value="取消">
                    </div>
                </div>
                <input type="hidden" name="id" value="">
                <input type="hidden" name="soldOut" value="0">
                <input type="hidden" name="do_action" value="/manage/products/category/sold-out-batch">
            </form>
        </div>
    </div>

</div>