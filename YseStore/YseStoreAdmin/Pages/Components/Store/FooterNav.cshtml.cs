using System.Collections.Generic;
using System.Threading.Tasks;
using YseStore.IService.Store;
using YseStore.Model.Response.Store;

namespace YseStoreAdmin.Pages.Components.Store
{
    public class FooterNav : MComponent
    {
        private readonly IMenuService _menuService;

        public FooterNav(IMenuService menuService)
        {
            _menuService = menuService;
        }

        /// <summary>
        /// 底部导航菜单列表
        /// </summary>
        public List<MenuResponse> FooterMenus { get; set; } = new List<MenuResponse>();

        /// <summary>
        /// 挂载函数
        /// </summary>
        /// 
        public override async Task MountAsync()
        {
            // 获取底部导航菜单数据
            FooterMenus = await _menuService.GetFooterNavMenusAsync();
        }
    }
}
