@model YseStoreAdmin.Pages.Components.Store.SalesAreasInfoComp
@{
}

<div>
    <div id="shipping" class="r_con_wrap" style="height: 374px;">
        <div class="center_container_816">
            <div class="inside_container bgc_unset inside_container_small">
                <div class="return_title">
                    <a href="/store/SalesAreas">
                        <span class="return">店铺</span>
                        <span class="return">/ 销售分区</span>
                        <span class="s_return">/ @(Model.AId > 0 ? "修改" : "添加")</span>
                    </a>
                </div>
            </div>

            <form id="edit_form" class="global_form area_edit_box" method="post">
                @Html.AntiForgeryToken()
                <div class="global_container box_info">
                    <div class="big_title">销售分区名称</div>
                    <div class="rows clean">
                        <label>分区名称 <span class="fs12 color_888"></span></label>
                        <div class="input">
                            <input type="text" name="Name" value="@Model.sales_Areas.Name"
                                   class="box_input full_input" size="25" placeholder="例如：USA - United States"
                                   maxlength="100">
                        </div>
                    </div>
                    <div class="rows clean">
                        <label>分区简码</label>
                        <div class="input">
                            <input type="text" placeholder="例如：USA,NA,EUA" name="Code"
                                   value="@Model.sales_Areas.Code"
                                   class="box_input full_input" size="25" maxlength="10" @(Model.AId > 0 ? "readonly" : "")>
                            <div class="external-placeholder" style="color: #999; padding: 5px 0; font-size: 13px; clear: both;">分区简码保存后不能更改</div>
                        </div>
                    </div>

                    <div class="rows clean">
                        <label>默认分区</label>
                        <div class="switchery default_button  @(Model.sales_Areas.IsDefault ? "checked" : "") ">
                            <input type="checkbox" value="1" name="IsDefault" @(Model.sales_Areas.IsDefault ? "checked" : "")>
                            <div class="switchery_toggler"></div>
                            <div class="switchery_inner">
                                <div class="switchery_state_on"></div>
                                <div class="switchery_state_off"></div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="global_container box_area">
                    <div class="big_title">国家地区</div>
                    <div class="box_button">
                        <input type="button"
                               class="btn_global btn_add_area"
                               value="添加">
                    </div>
                    <div class="content" data-range="country" style="@(Model.sales_Areas.Counntries.Count == 0 ? "display: none;" : "")">
                        @if (Model.CountryList.Any())
                        {
                            foreach (var country in Model.CountryList)
                            {
                                // 查找国家信息
                                var countryInfo = Model.sales_Areas.Counntries.Contains(country.CId.ToString());

                                if (countryInfo)
                                {
                                    <div class="item">
                                        <div class="img">
                                            <div class="icon_flag flag_@(country.Acronym?.ToLower())"></div>
                                        </div>
                                        <div class="name">@country.Country</div>
                                        <div class="area_box_del"></div>
                                        <div class="clear"></div>
                                        <input type="checkbox" name="CId[]" value="@country.CId" checked>
                                        <input type="checkbox" name="CName[]" value="@country.Country" checked>
                                    </div>

                                }
                            }
                        }
                    </div>
                </div>

                <div class="global_container box_currency">
                    <div class="big_title">币种</div>
                    <div class="rows clean">
                        <label>分区币种 <span class="fs12 color_888"></span></label>
                        <div class="currency_box" data-value="currency">
                            <dl class="box_drop_double" data-checkbox="1" data-showadd="0">
                                <dt class="box_checkbox_list">
                                    <input type="hidden" class="box_input check_input" name="currency" value=""
                                           placeholder="" size="30" maxlength="255" autocomplete="off">
                                    @{
                                        if (Model.sales_Areas.Currencies != null && Model.sales_Areas.Currencies.Count > 0)
                                        {
                                            <div class="select_list">
                                                @foreach (var item in Model.sales_Areas.CurrencyList)
                                                {
                                                    var cry = $"{item.Name.JsonToObj<Dictionary<string, string>>()["zh-cn"]}({item.Currency}/{item.Symbol})";

                                                    <span class="btn_attr_choice current" data-type="currency">
                                                        <b>@cry</b>
                                                        <input type="hidden" name="currencyOption[]" value="@item.Currency">
                                                        <input type="hidden" name="currencyName[]" value="@item.Name">
                                                        <input type="checkbox" name="currencyCurrent[]" value="@item.Currency" class="option_current" checked>
                                                        <i></i>
                                                    </span>
                                                }
                                            </div>
                                        }
                                        else
                                        {
                                            <div class="select_placeholder">请选择</div>
                                            <div class="select_list"> </div>
                                        }
                                    }
                                </dt>
                                <dd class="drop_down">
                                    <div class="drop_menu" data-type="">
                                        <a href="javascript:;" class="btn_back"
                                           data-value="" data-type="" data-table="" data-top="0" data-all="0"
                                           style="display:none;">返回</a>
                                        <div class="drop_skin" style="display: none;"></div>
                                        <div class="drop_list"
                                             data="@(Model.Currencies.ToJson())"
                                             data-more="none">
                                        </div>
                                    </div>
                                </dd>
                            </dl>
                        </div>
                    </div>

                    <div class="rows clean">
                        <label>默认币种 <span class="fs12 color_888"></span></label>
                        <div class="currency_box" data-value="defaultCurrency">
                            <div class="input">
                                <div class="box_select">
                                    <select name="DefaultCurrency">

                                        @foreach (var item in Model.Currencies)
                                        {
                                            if (item.Value == Model.sales_Areas.DefaultCurrency)
                                            {
                                                <option value="@item.Value" selected>
                                                    @item.Name
                                                </option>
                                            }
                                            else
                                            {
                                                <option value="@item.Value">
                                                    @item.Name
                                                </option>
                                            }
                                        }

                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="global_container box_language">
                    <div class="big_title">语言</div>

                    <div class="rows clean">
                        <label>分区语言  <span class="fs12 color_888"></span></label>
                        <div class="language_box" data-value="language">
                            <dl class="box_drop_double" data-checkbox="1" data-showadd="0">
                                <dt class="box_checkbox_list">
                                    <input type="hidden" class="box_input check_input" name="language" value=""
                                           placeholder="" size="30" maxlength="255" autocomplete="off">
                                    @{
                                        if (Model.sales_Areas.Languages != null && Model.sales_Areas.Languages.Count > 0)
                                        {
                                            <div class="select_list">
                                                @foreach (var item in Model.sales_Areas.Languages)
                                                {
                                                    <span class="btn_attr_choice current" data-type="language">
                                                        <b>@T[$"language.{item}"]</b>
                                                        <input type="hidden" name="languageOption[]" value="@item">
                                                        <input type="hidden" name="languageName[]" value="@T[$"language.{item}"]">
                                                        <input type="checkbox" name="languageCurrent[]" value="@item" class="option_current" checked>
                                                        <i></i>
                                                    </span>

                                                }
                                            </div>
                                        }
                                        else
                                        {
                                            <div class="select_placeholder">请选择</div>
                                            <div class="select_list"></div>
                                        }
                                    }

                                </dt>
                                <dd class="drop_down">
                                    <div class="drop_menu" data-type="">
                                        <a href="javascript:;" class="btn_back"
                                           data-value="" data-type="" data-table="" data-top="0" data-all="0"
                                           style="display:none;">返回</a>
                                        <div class="drop_skin" style="display: none;"></div>
                                        <div class="drop_list"
                                             data="@(Model.Languages.ToJson())"
                                             data-more="none">
                                        </div>
                                    </div>
                                </dd>
                            </dl>
                        </div>
                    </div>

                    <div class="rows clean">
                        <label>默认语言 <span class="fs12 color_888"></span></label>
                        <div class="language_box" data-value="defaultLanguage">
                            <div class="input">
                                <div class="box_select">
                                    <select name="DefaultLanguage">

                                        @foreach (var item in Model.Languages)
                                        {

                                            if (item.Value == Model.sales_Areas.DefaultLanguage)
                                            {
                                                <option value="@item.Value" selected>
                                                    @T[$"language.{item.Value}"]
                                                </option>
                                            }
                                            else
                                            {
                                                <option value="@item.Value">
                                                    @T[$"language.{item.Value}"]
                                                </option>
                                            }
                                        }

                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>




                @* <div class="global_container box_timezone">
                    <div class="big_title">时区</div>
                    <div class="timezone_box" data-value="timezone">
                        <div class="input">
                            <div class="box_select">
                                <select name="TimeZone">
                                    @if (Model.sales_Areas.Timezone != null)
                                    {
                                        foreach (var item in Model.TimeZones)
                                        {
                                            if (item.Value == Model.sales_Areas.Timezone)
                                            {
                                                <option value="@item.Value" selected>
                                                    @item.Text
                                                </option>
                                            }
                                            else
                                            {
                                                <option value="@item.Value">
                                                    @item.Text
                                                </option>
                                            }
                                        }
                                    }
                                </select>
                            </div>
                        </div>
                    </div>
                </div> *@

                <input type="hidden" name="DeliveryRange" value="country">
                <input type="hidden" name="isContinue" value="0">
                <input type="hidden" name="SId" value="@Model.sales_Areas.SId" />
                <input type="hidden" name="do_action" value="/api/setting/SaveSalesArea">
                @* <input type="text" class="box_input check_input"
                       id="currencyCurrent[]" name="currencyCurrent[]" value="@(string.Join(',', Model.sales_Areas.Currencies))"
                       placeholder="" size="30" maxlength="255" style="display: none;"
                       autocomplete="off">
                <input type="text" class="box_input check_input"
                       id="languageCurrent[]" name="languageCurrent[]" value="@(string.Join(',', Model.sales_Areas.Languages))"
                       placeholder="" size="30" maxlength="255" style="display: none;"
                       autocomplete="off"> *@
            </form>
            <div id="fixed_right">
                <div class="global_container country_area_box" data-width="500" style="display: none;">
                    <div class="country_area">
                        <div class="top_title">添加销售分区<a href="javascript:;" class="close"></a></div>
                        <div class="search_menu">
                            <div class="search_form">
                                <form method="get" action="?" style="border-radius: 4px;">
                                    <div class="k_input">
                                        <input type="text" name="Keyword" value="" class="form_input"
                                               size="15" autocomplete="off" placeholder="请输入关键词">
                                        <input type="button" value="" class="more">
                                        <input type="submit" class="search_btn" value="">
                                    </div>
                                    <input type="button" class="new_filter_btn" value="筛选">

                                    <div class="clear"></div>
                                    <input type="hidden" name="m" value="">
                                    <input type="hidden" name="a" value="">
                                    <input type="hidden" name="d" value="">
                                    <input type="hidden" name="FilterCateId" value="">
                                    <input type="hidden" name="FilterTagId" value="">
                                </form>
                            </div>
                        </div>
                        <div class="continent_area">
                            @foreach (var continent in Model.ShippingAreaResponse.Continents)
                            {
                                <div class="continent" continent="@continent.ContinentId">
                                    <span class="input_checkbox_box">
                                        <span class="input_checkbox">
                                            <input type="checkbox" name="OrdersSmsStatus[]"
                                                   value="@continent.ContinentId">
                                        </span>@continent.ContinentName
                                    </span>
                                    <a href="javascript:;" class="down"></a>
                                </div>
                                <div class="country_item">
                                    @foreach (var country in continent.Countries)
                                    {
                                        <div class="country_item_sec ">
                                            <span class="input_checkbox_box" data-cid="@country.CId">
                                                <span class="input_checkbox">
                                                    <input type="checkbox" name="CId" value="@country.CId"
                                                           data-acronym="@country.Acronym"
                                                           data-flagpath="@country.FlagPath"
                                                           data-country="@country.Country">
                                                </span>
                                                <span class="img">
                                                    <div class="icon_flag <EMAIL>"></div>
                                                </span>
                                                @country.Country
                                            </span>

                                        </div>

                                    }
                                </div>
                            }
                        </div>
                    </div>
                    <div class="clear"></div>
                    <input type="hidden" name="AId">
                    <div class="rows clean box_submit">
                        <div class="input_button">
                            <div class="input">
                                <input type="button" class="btn_global btn_submit" value="添加">
                                <input type="button" class="btn_global btn_cancel" value="取消">
                            </div>
                        </div>
                    </div>
                </div>

            </div>
        </div>


        <div class="rows clean box_submit fixed_btn_submit">
            <label></label>
            <div class="center_container_816">
                <div class="input input_button">
                    <input type="button" class="btn_global btn_submit" value="保存">
                    <input type="button" class="btn_global btn_submit btn_continue" value="保存并继续添加">
                    <a href="/store/SalesAreas" class="btn_global btn_cancel">返回</a>
                </div>
            </div>
        </div>
    </div>

</div>

