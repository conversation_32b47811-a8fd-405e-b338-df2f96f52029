@model YseStoreAdmin.Pages.Components.Store.CustomCode
@{
    var CodeTypeArray = new List<string> { "PC端和手机端", "仅PC端", "仅手机端" };
}
<div>

    <div id="third" class="r_con_wrap plugins_app_box" style="height: 437px;">
       
        <div class="inside_container">
            <h1>
                <ul class="list_menu_button fr">
                
                    <li>  <a class="add" href="/Store/CustomCodeEdit?TId=0">添加</a></li>
                </ul>
                
                自定义代码</h1>
          
        </div>
        <div class="inside_table ">
          
            <div class="box_table">
                @if (Model.thirds != null && Model.thirds.Count > 0)
                {
                    <table border="0" cellpadding="5" cellspacing="0" class="r_con_table">
                        <thead>
                            <tr>
                                <td width="1%" nowrap="">序号</td>
                                <td width="20%" nowrap="">标题</td>
                                <td width="15%" nowrap="">终端</td>
                                <td width="20%">触发页面</td>
                                <td width="20%" nowrap="">位置</td>
                                <td width="10%" nowrap="">是否启用</td>
                                <td width="10%" nowrap=""></td>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach (var item in Model.thirds)
                            {
                                var index = Model.thirds.IndexOf(item) + 1;
                                <tr>
                                    <td nowrap="">@index</td>
                                    <td>@item.Title</td>
                                    <td nowrap="">@CodeTypeArray[item.CodeType ?? 0]</td>
                                    <td>
                                        @if (item.Trigger == "all")
                                        {
                                            <div>全部页面</div>
                                        }
                                        else
                                        {

                                        }
                                    </td>

                                    <td nowrap="">
                                        @(item.IsMeta == true ? "<head></head>之间" : "")
                                        @(item.IsBody == true ? "<body></body>之间" : "")
                                    </td>
                                    <td nowrap="" class="used_checkbox">
                                        <div class="switchery @(item.IsUsed==true ?"checked":"")" data-tid="@item.TId">
                                            <div class="switchery_toggler"></div>
                                            <div class="switchery_inner">
                                                <div class="switchery_state_on"></div>
                                                <div class="switchery_state_off"></div>
                                            </div>
                                        </div>
                                    </td>
                                    <td nowrap="" class="operation side_by_side">
                                        <a class="icon_edit oper_icon button_tips" href="/Store/CustomCodeEdit?TId=@item.TId"
                                           data-id="@item.TId">修改</a>
                                        <a class="icon_del oper_icon button_tips del"
                                           href="/manage/plugins/third/del?TId=@item.TId">删除</a>
                                    </td>
                                </tr>
                            }
                        </tbody>
                    </table>
                }
                else
                {
                    <div class="bg_no_table_data" style="height: 469px;">
                        <div class="content" style="top: 194.5px;">
                            <p>当前暂时没有数据</p>
                        </div><span></span>
                    </div>
                }

            </div>
            <div class="scroll_sticky">
                <div class="scroll_sticky_content">
                    <div style="width: 1748px; height: 1px;"></div>
                </div>
            </div>
        </div>
    </div>
    <div id="fixed_right">
        <div class="global_container fixed_edit_third" data-width="460">
            <div class="top_title"><span>添加</span> <a href="javascript:;" class="close"></a></div>
            <form id="third_edit_form" class="global_form" action="/Store/CustomCode" method="post">
                @Html.AntiForgeryToken()
                <div class="rows">
                    <label>标题</label>
                    <div class="input">
                        <input type="text" name="Third[Title]" value="" class="box_input full_input" size="53"
                               maxlength="150" notnull="">
                    </div>
                </div>
                <div class="rows">
                    <label>代码内容</label>
                    <div class="input">
                        <textarea name="Third[Code]" class="box_textarea full_textarea"
                                  notnull=""></textarea>
                    </div>
                </div>
                <div class="rows">
                    <label>终端</label>
                    <div class="input">
                        <div class="box_select">
                            <select name="Third[CodeType]">
                                <option value="0">PC端和手机端</option>
                                <option value="1">仅PC端</option>
                                <option value="2">仅手机端</option>
                            </select>
                        </div>
                    </div>
                </div>
                <div class="rows">
                    <label>位置</label>
                    <div class="input">
                        <div class="box_select" style="width:210px;">
                            <select name="Position">
                                <option value="IsMeta">&lt;head&gt;&lt;/head&gt;之间</option>
                                <option value="IsBody">&lt;body&gt;&lt;/body&gt;之间</option>
                            </select>
                        </div>
                    </div>
                </div>
                <div class="rows box_submit">
                    <label></label>
                    <div class="input input_button">
                        <input type="button" class="btn_global btn_submit" value="保存">
                        <input type="button" class="btn_global btn_cancel" value="取消">
                    </div>
                </div>
                <input type="hidden" name="TId" value="0">
                <input type="hidden" name="Third[IsUsed]" value="1">
                <input type="hidden" name="do_action" value="/Store/CustomCodeEdit">
            </form>
        </div>
    </div>

</div>