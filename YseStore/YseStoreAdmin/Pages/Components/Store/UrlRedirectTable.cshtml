@model YseStoreAdmin.Pages.Components.Store.UrlRedirectTable
@{
}

<div>
    @if (Model.PagerOptions.TotalCount > 0)
    {

        <table border="0" cellpadding="5" cellspacing="0" class="r_con_table">
            <thead>
                <tr>
                    <td width="12%" class="pos">
                        <ul class="table_menu_button global_menu_button">
                            <li>
                                <div class="btn_checkbox ">
                                    <em class="button"></em><input type="checkbox"
                                                                   name="select_all" value="">
                                </div>
                            </li>
                            <li class="open">已选择<span></span>个</li>
                            <li><a href="javascript:;" class="del">删除</a></li>
                        </ul>
                    </td>
                    <td width="38%" nowrap="">源url</td>
                    <td width="38%" nowrap="">重新跳转到</td>
                    <td width="12%" nowrap="" class="operation"></td>
                </tr>
            </thead>
            <tbody>
                @foreach (var item in Model.UrlList.data)
                {
                    <tr data-id="@item.Id">
                        <td>
                            <div class="btn_checkbox ">
                                <em class="button"></em><input type="checkbox"
                                                               name="select" value="@item.Id">
                            </div>
                        </td>
                        <td data-type="url">
                            <a href="javascript:;" class="color_555 btn_edit_url"
                               data-id="@item.Id">@item.Url</a>
                        </td>
                        <td data-type="redirecturl">
                            @{
                                string recirectUrl = AppSettingsConstVars.WebSiteUrl.TrimEnd('/') + item.RedirectUrl;
                            }
                            <a href="recirectUrl"
                               target="_blank"
                               class="color_555">@item.RedirectUrl</a>
                        </td>
                        <td nowrap="" class="operation tar">
                            <a href="javascript:;" class="oper_icon icon_edit btn_edit_url button_tips"
                               data-id="@item.Id">修改</a>
                            <input type="hidden" name="id[]" value="@item.Id">
                            <input type="hidden" name="url[]"
                                   value="@item.Url">
                            <input type="hidden" name="redirectUrl[]"
                                   value="@item.RedirectUrl">
                        </td>
                    </tr>
                }


            </tbody>
        </table>

    }
    else
    {
        <div class="bg_no_table_data" style="height: 469px;">
            <div class="content" style="top: 194.5px;">
                <p>当前暂时没有数据</p>
            </div><span></span>
        </div>
    }


</div>