using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Html;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.Extensions.Localization;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using StackExchange.Profiling.Internal;
using System.Text;
using YseStoreAdmin.Pages.Components.Setting;

namespace YseStoreAdmin.Pages.Components.Store.VisualComp
{
    public class PluginsItem : MComponent
    {
        private readonly IStringLocalizer<PluginsItem> T;
        public PluginsItem(IStringLocalizer<PluginsItem> t)
        {
            this.T = t;
        }


        [Parameter]
        public string Name { get; set; } = "";

        [Parameter]
        public string InputName { get; set; } = "";


        [Parameter]
        public string Value { get; set; } = "";

        [Parameter]
        public JObject Config { get; set; } = new JObject();



        /// <summary>
        /// 下拉 文本框 混合
        /// </summary>
        /// <param name="boxId">图片框ID</param>
        /// <param name="inputName">保存图片的字段</param>
        /// <param name="picpath">图片路径</param>
        /// <param name="imgSize">用于多图片上传</param>
        /// <param name="isMove">是否带有拖动功能</param>
        /// <param name="attr">附加属性</param>
        /// <param name="s_picpath">缩略图片路径</param>
        /// <param name="box_class">CSS类名</param>
        /// <returns>生成的HTML字符串</returns>
        public static string MultiImg(string boxId, string inputName, string picpath = "", string imgSize = "", int isMove = 0, string attr = "", string s_picpath = "", string box_class = "", string k1 = "", string k2 = "")
        {
            int isFile = !string.IsNullOrEmpty(picpath) ? 1 : 0; // 默认都显示，主要是针对复制网站的时候，图片路径非本网站目录，不能正常显示
            StringBuilder html = new StringBuilder();

            html.Append($"<div class=\"multi_img upload_file_multi {box_class}\" id=\"{boxId}\">");
            html.Append($"<dl class=\"img {(isFile != 0 ? "isfile" : "")}\" num=\"0\">");
            html.Append("<dt class=\"upload_box preview_pic\">");
            html.Append($"<input type=\"button\" class=\"btn_ok upload_btn\" name=\"submit_button\" value=\"{k1}\" tips=\"\" />");
            html.Append($"<input type=\"hidden\" name=\"{inputName}\" value=\"{picpath}\" data-value=\"{s_picpath}\" save=\"{isFile}\" {attr} />");
            html.Append("</dt>");
            html.Append("<dd class=\"pic_btn\">");

            if (isMove == 1)
            {
                html.Append("<a href=\"javascript:;\" class=\"myorder\"><i class=\"icon_multi_myorder\"></i></a>");
            }

            html.Append($"<a href=\"{(isFile != 0 ? picpath : "javascript:;")}\" class=\"zoom\" target=\"_blank\"><i class=\"icon_multi_view\"></i></a>");
            html.Append("<a href=\"javascript:;\" class=\"del\" rel=\"del\"><i class=\"icon_multi_delete\"></i></a>");
            html.Append("</dd>");
            html.Append("</dl>");
            html.Append("</div>");

            if (!string.IsNullOrEmpty(imgSize))
            {
                html.Append($"<div class=\"tips\" {(string.IsNullOrEmpty(imgSize) ? "" : $"data=\"{imgSize}\"")}>{string.Format(k2, imgSize)}</div>");
            }

            return html.ToString();
        }



        /// <summary>
        /// 下拉文本框混合控件
        /// </summary>
        /// <param name="selectName">下拉名称</param>
        /// <param name="inputName">文本名称</param>
        /// <param name="dataAry">产品属性</param>
        /// <param name="value">数值 (单选:[Select]=>下拉值,[Input]=>文本值,[Type]=>类型值 多选:[Name]=>名称,[Value]=>数值,[Type]=>类型)</param>
        /// <param name="type">产品类型 (0:下拉+文本，1:下拉)</param>
        /// <param name="inputAttr">文本额外参数</param>
        /// <param name="isCheckbox">是否为多选</param>
        /// <param name="isMore">显示加载更多</param>
        /// <param name="placeholder">默认形式的文字提示</param>
        /// <param name="topType">默认内容的类型 (products_category:产品分类)</param>
        /// <param name="isShowAdd">默认显示添加选项（针对下拉）</param>
        /// <param name="tips">提示信息</param>
        /// <returns>生成的HTML字符串</returns>
        public static string BoxDropDouble(
            string selectName,
            string inputName,
            JObject dataAry = null,
            object value = null,
            int type = 0,
            string inputAttr = "",
            int isCheckbox = 0,
            int isMore = 0,
            string placeholder = "",
            string topType = "",
            int isShowAdd = 1,
            string tips = "")
        {
            dataAry ??= new JObject();
            var isEdit = (type == 0) ? 1 : 0;
            var isDataTip = dataAry.Count == 0 ? 1 : 0;

            var jsonData = dataAry.ToJson();
            var moreAry = new Dictionary<string, object>
            {
                ["value"] = "",
                ["type"] = "",
                ["table"] = "",
                ["top"] = 0,
                ["all"] = 0,
                ["check-all"] = "",
                ["start"] = 0
            };

            if (isMore == 1 && dataAry.Count > 0)
            {
                var first = dataAry[0].ToObject<JObject>();
                var typeVal = first.ContainsKey("Type") ? first["Type"].ToString() : "";
                var tableVal = first.ContainsKey("OtherTable") && first["OtherTable"] != null
                    ? first["OtherTable"].ToString()
                    : typeVal;

                if (typeVal == "products")
                {
                    moreAry["value"] = moreAry["type"] = moreAry["table"] = "products";
                    moreAry["all"] = 1;
                    moreAry["start"] = 1;
                }
                else
                {
                    moreAry["value"] = typeVal;
                    moreAry["type"] = typeVal;
                    moreAry["table"] = tableVal;
                    moreAry["all"] = 1;
                    moreAry["start"] = 1;
                }
            }

            var sb = new StringBuilder();
            sb.Append($"<dl class=\"box_drop_double{(isEdit == 1 ? " edit_box" : "")}\" data-checkbox=\"{(isCheckbox == 1 ? 1 : 0)}\" data-showadd=\"{(isShowAdd == 1 ? 1 : 0)}\">");

            if (isCheckbox == 1)
            {
                // 多选
                sb.Append("<dt class=\"box_checkbox_list\">");
                sb.Append($"<div class=\"select_placeholder\">{placeholder ?? "请选择"}</div>");
                sb.Append("<div class=\"select_list\">");
                // 多选项目需你自行实现渲染逻辑（如通过 value 解析每个项）
                sb.Append("</div>");
                sb.Append($"<input type=\"text\" class=\"box_input check_input\" {inputAttr} name=\"{inputName}\" value=\"\" placeholder=\"{placeholder}\" size=\"30\" maxlength=\"255\" autocomplete=\"off\" />");
                sb.Append($"<input type=\"hidden\" name=\"{selectName}\" value=\"\" class=\"hidden_value\" />");
                sb.Append($"<input type=\"hidden\" name=\"{selectName}Type\" value=\"\" class=\"hisdden_type\" />");
                sb.Append("</dt>");
            }
            else
            {
                if (isEdit == 1)
                {
                    // 带有文本框
                    var inputVal = GetDictValue(value, "Input");
                    var selectVal = GetDictValue(value, "Select");
                    var typeVal = GetDictValue(value, "Type");

                    sb.Append($"<dt><input type=\"text\" class=\"box_input\" name=\"{inputName}\" placeholder=\"{(string.IsNullOrEmpty(placeholder) ? "请输入内容" : placeholder)}\" value=\"{inputVal}\" autocomplete=\"off\" {inputAttr} />");
                    sb.Append($"<input type=\"hidden\" name=\"{selectName}\" value=\"{selectVal}\" class=\"hidden_value\" />");
                    sb.Append($"<input type=\"hidden\" name=\"{selectName}Type\" value=\"{typeVal}\" class=\"hidden_type\" /></dt>");
                }
                else
                {
                    // 仅下拉
                    var name = GetDictValue(value, "Name");
                    var selectVal = GetDictValue(value, "Select");
                    var typeVal = GetDictValue(value, "Type");

                    sb.Append($"<dt><div class=\"box_select\"><span>{(string.IsNullOrEmpty(name) ? "请选择" : name)}</span>");
                    sb.Append($"<input type=\"hidden\" name=\"{selectName}\" value=\"{selectVal}\" {inputAttr} class=\"hidden_value\" />");
                    sb.Append($"<input type=\"hidden\" name=\"{selectName}Type\" value=\"{typeVal}\" class=\"hidden_type\" /></div></dt>");
                }
            }

            // 下拉内容区域
            sb.Append("<dd class=\"drop_down\">");
            sb.Append($"<div class=\"drop_menu\" data-type=\"{topType}\">");

            if (!string.IsNullOrEmpty(tips))
            {
                sb.Append($"<div class=\"global_app_tips obvious\"><em></em><span>{tips}</span></div>");
            }

            sb.Append("<a href=\"javascript:;\" class=\"btn_back\" data-value=\"\" data-type=\"\" data-table=\"\" data-top=\"0\" data-all=\"0\" style=\"display:none;\">返回</a>");
            sb.Append("<div class=\"drop_skin\" style=\"display:none;\"></div>");
            sb.Append($"<div class=\"drop_list\" data='{jsonData}' data-more=\"{(isMore == 1 && dataAry.Count >= 20 ? "block" : "none")}\"></div>");
            sb.Append($"<a href=\"javascript:;\" class=\"btn_load_more\" data-value=\"{moreAry["value"]}\" data-type=\"{moreAry["type"]}\" data-table=\"{moreAry["table"]}\" data-top=\"{moreAry["top"]}\" data-all=\"{moreAry["all"]}\" data-check-all=\"{moreAry["check-all"]}\" data-start=\"{moreAry["start"]}\" style=\"display:{(isMore == 1 && dataAry.Count >= 20 ? "block" : "none")}\">加载更多</a>");

            if (isDataTip == 1)
            {
                sb.Append("<div class=\"drop_nodata_tip\" style=\"display:none;\">暂无数据</div>");
            }

            sb.Append("</div></dd></dl>");

            return sb.ToString();
        }

        private static string GetDictValue(object obj, string key)
        {
            if (obj is JObject dict && dict.ContainsKey(key))
                return dict[key]?.ToString() ?? "";
            return "";
        }



    }
}
