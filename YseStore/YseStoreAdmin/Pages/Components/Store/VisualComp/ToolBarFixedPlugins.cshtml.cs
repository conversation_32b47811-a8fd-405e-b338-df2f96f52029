using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Newtonsoft.Json.Linq;


namespace YseStoreAdmin.Pages.Components.Store.VisualComp
{
    public class ToolBarFixedPlugins : MComponent
    {
        [Parameter]
        public JObject obj { get; set; } = new JObject();



        /**
   * 后台可视化渲染Blocks内容导航卡HTML
   * 
   * <AUTHOR>
   * @params string key 需渲染Blocks的键
   * @params JObject pluginsConfig 该插件配置
   * @params JObject pluginsRow 该插件记录
   */
        public  static string BlocksMenuHtml(string key, JObject pluginsConfig, JObject pluginsRow)
        {
            // Exploding the key
            var expName = key.Split('-')[0];

            var name = GlobalLangVars.ViewBlockName[expName];

            var html = $"<div class='menu_item' data-fixed-plugins='{pluginsRow["PId"]}-{key}'>";

            // Checking if BlocksAdd config is true
            if (pluginsConfig["Config"]?["BlocksAdd"] !=null && pluginsConfig["Config"]?["BlocksAdd"]?.ToObject<bool>() == true)
                html += "<div class='item_move'></div>";

            html += $"<div class='item_icon icon_{expName}'></div>";
            html += $"<div class='item_name' data-name='{name}'>{name}</div>";

            // Check if BlocksAdd config is true for Copy button
            if (pluginsConfig["Config"]?["BlocksAdd"] != null && pluginsConfig["Config"]?["BlocksAdd"]?.ToObject<bool>() == true)
                html += "<div class='item_copy'><i class='iconfont icon-copy1'></i></div>";

            html += "<div class='clear'></div>";
            html += "</div>";

            return html;
        }

    }
}
