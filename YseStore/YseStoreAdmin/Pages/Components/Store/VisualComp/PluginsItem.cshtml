@using Newtonsoft.Json.Linq
@model YseStoreAdmin.Pages.Components.Store.VisualComp.PluginsItem
@{
    var Name = Model.Name;
    var Value = Model.Value;
    var Config = Model.Config;
    var InputName = Model.InputName;

    if (Config["value"] != null && Value == null)
    {
        Value = Config["value"].ToString();
    }

    string type = Config["type"]?.ToString();
    List<string> typArray = new List<string> { "color", "font", "select", "progress", "textalign" };

    if (typArray.Contains(type) && Value == null)
    {
        Value = Config["value"].ToString();
    }
    string style = "";
    //解决关联插件无法隐藏的问题
    var visibilityAttr = "";

    if (Config["expand"] != null && Config["expand"]["visibility"] != null && Config["expand"]["visibility"].ToString() == "hidden")
    {
        style = "display: none;";
        visibilityAttr = "visibility='hidden'";
    }




}




<div>
    @{



        switch (type)
        {
            case "color":// 颜色组件
                {
                    <div class="component_color" data-component="@type">
                        <div class="color_row">
                            <div class="color_name plugins_name">@T[$"variable.{Name}.name"].Value</div>
                            <div class="color_box" data-colpickId="@InputName">
                                <div class="color_button"></div>
                                <input class="color_color" type="hidden" name="@InputName" value="@Value" />
                            </div>
                            <div class="clear"></div>
                        </div>
                    </div>
                    break;
                }

            case "font":// 字体组件
                {
                    <div class="component_font" data-component="@type">
                        <div class="font_title plugins_name">>@T[$"variable.{Name}.name"].Value</div>
                        <div class="font_box">
                            <div class="font_name" style="font-family: @(Value)">@Value</div>
                            <div class="font_btn" data-fixed-plugins="font-select">编辑</div>
                            <input type="hidden" name="@InputName" value="@Value" />
                        </div>
                    </div>
                    break;
                }
            case "switch":// 开关组件
                {
                    string linkage = "";
                    if (Config["linkage"] != null)
                    {
                        linkage += " data-linkage='true'";
                        foreach (var item in Config["linkage"].ToObject<JObject>
                        ().Properties())
                        {
                            var itemValue = item.Value.ToObject<List<string>>();
                            string itemValueStr = string.Join(',', itemValue);

                            linkage += $" data-linkage-${item.Name}='{itemValueStr}'";
                        }
                    }


                    if (Name == "VideoAutoPlayMute")// // 自动播放是否静音暂时隐藏功能
                    {
                        Value = "0";
                        break;
                    }
                    if (Name == "Search" ||
                    Name == "User" ||
                    Name == "ShoppingCart" ||
                    Name == "LanguageSwitch" ||
                    Name == "CurrencySwitch" ||
                    Name == "Favorites")
                    {
                        style = "display: none;";
                    }

                    <div class="component_switch" data-component="@type" @linkage @visibilityAttr style="@(style)">
                        <div class="switch_row">
                            <div class="switch_name plugins_name">@T[$"variable.{Name}.name"].Value</div>
                            <div class="switch_box">
                                <div class="switchery  @( Value =="1" ? "checked" : "")">
                                    <div class="switchery_toggler"></div>
                                    <div class="switchery_inner">
                                        <div class="switchery_state_on"></div>
                                        <div class="switchery_state_off"></div>
                                    </div>
                                </div>
                            </div>
                            <div class="clear"></div>
                            @if (Config["expand"] != null && Config["expand"]["hint"] != null)
                            {
                                <div class="switch_hint">@T[$"variable.{Name}.{Config["expand"]["hint"]}"] </div>
                            }

                            <input type="hidden" name="@InputName" value="@Value" />
                        </div>
                    </div>

                    break;
                }
            case "select":// 下拉框组件
                {
                    string linkage = "";
                    if (Config["linkage"] != null)
                    {
                        linkage += " data-linkage='true'";

                        foreach (var item in Config["linkage"].ToObject<JObject>().Properties())
                        {
                            var itemValue = item.Value.ToObject<List<string>>();
                            string itemValueStr = string.Join(',', itemValue);

                            linkage += $" data-linkage-${item.Name}='{itemValueStr}'";
                        }
                    }


                    var selectAry = new JObject();
                    var options = Config["options"].ToObject<List<string>>();

                    foreach (var kvp in options)
                    {
                        var k = options.IndexOf(kvp).ToString();
                        // var v = kvp.Value.ToString();

                        selectAry[k] = new JObject
        {
        { "Name", T[$"variable.{Name}.options.{kvp}"].Value },
        { "Value", kvp },
        { "Type", "Select" }
        };
                    }

                    var valueAry = new JObject
        {
        {"Name", T[$"variable.{Name}.options.{Value}"].Value },
        {"Select",Value},
        {"Type","Select"}

        };
                    if (Name == "ProductsMainPicScale") // 产品主图比例隐藏设置
                    {
                        style = "display: none;";
                    }

                    if (Name == "FillingMethodPc" || Name == "FillingMethodMobile") // 轮播图隐藏填充方式和展示区域
                    {
                        style = "display: none;";
                    }

                    if ((Name == "PosterPicHeight" || Name == "HeightPc" || Name == "HeightMobile") && (Config["expand"] == null || Config["expand"]["visibility"] == null || Config["expand"]["visibility"].ToString() != "show")) // 轮播图隐藏填充方式和展示区域
                    {
                        style = "display: none;";
                    }

                    <div class="component_select" data-component="@type" @linkage @visibilityAttr style="@(style)">
                        <div class="select_name plugins_name">@T[$"variable.{Name}.name"].Value</div>
                        @if (Config["expand"] != null && Config["expand"]["hint"] != null)
                        {
                            <div class="select_hint">@T[$"variable.{Name}.{Config["expand"]["hint"].ToString()}"]</div>
                        }
                        <div class="select_box">
                            @{
                                string html = PluginsItem.BoxDropDouble(InputName, "", selectAry, valueAry, 1, "", 0, 0, "");

                            }
                            @Html.Raw(html)


                        </div>
                    </div>

                    break;
                }
            case "input":// 单行文本组件
                {
                    if (Name == "SearchPlaceholder")
                    {
                        style = "margin-top: 0;";
                    }

                    string placeholder = "";

                    <div class="component_input" data-component="@type" @visibilityAttr style="@(style)">
                        <div class="input_name plugins_name">@T[$"variable.{Name}.name"] </div>
                        <div class="input_box">
                            <input class="box_input full_input" type="text" name="@InputName" value="@Value" @placeholder />
                            @if (Config["expand"] != null && Config["expand"]["suffix"] != null)
                            {
                                <i>@Config["expand"]["suffix"].ToString()</i>
                            }

                        </div>
                        @if (Config["expand"] != null && Config["expand"]["hint"] != null)
                        {
                            <div class="input_hint">@T[$"variable.{Name}.{Config["expand"]["hint"].ToString()}"]</div>
                        }


                    </div>
                    break;
                }
            case "progress":// 进度条组件
                {
                    decimal width = 0;
                    if (Value.IsNullOrEmpty())
                    {
                        Value = Config["value"].ToString();
                    }
                    var value = Value.Replace(Config["suffix"].ToString(), "");
                    if (int.TryParse(value, out int val))
                    {
                        if (val > 0)
                        {
                            width = Math.Floor((decimal)(val - Config["options"][0].ObjToInt()) / (decimal)(Config["options"][1].ObjToInt() - Config["options"][0].ObjToInt()) * 100);
                        }
                    }

                    <div class="component_progress" data-component="@type" onselectstart="return false">
                        <div class="progress_name plugins_name">
                            @T[$"variable.{Name}.name"]
                            <span>@Value</span>
                        </div>
                        <div class="progress_box">
                            <div class="progress_bar" data-min="@Config["options"][0].ToString()" data-max="@Config["options"][1].ToString()" data-suffix="@Config["suffix"].ToString()">
                                <div class="progress_belt" style="width:@(width)%">
                                    <input type="hidden" name="@InputName" value="@Value" />
                                </div>
                            </div>
                        </div>
                    </div>

                    break;
                }
            case "textarea":// 多行文本组件
                {

                    <div class="component_textarea" data-component="@type">
                        <div class="textarea_name plugins_name">@T[$"variable.{Name}.name"]</div>
                        <div class="textarea_box"><textarea class="box_textarea" name="@InputName"> @Value</textarea></div>

                        @if (Config["expand"]["hint"] != null)
                        {
                            <div class="textarea_hint">@T[$"variable.{Name}.{Config["expand"]["hint"].ToString()}"]</div>
                        }
                    </div>
                    break;
                }
            case "image":// 上传图片组件
                {
                    int width = Config["expand"]["width"].ObjToInt(0);
                    int height = Config["expand"]["height"].ObjToInt(0);
                    var format = Config["expand"]["width"].ObjToString();
                    // 创建Random实例
                    Random random = new Random();
                    var rm = random.Next(10000000, 99999999);
                    // 获取当前时间的 Unix 时间戳（秒级）
                    long timestamp = (long)(DateTime.UtcNow - new DateTime(1970, 1, 1)).TotalSeconds;

                    string imgHtml = PluginsItem.MultiImg($"PicDetail_{Name}_{rm}{timestamp}", InputName, Value, k1: T["global.upload_pic"], k2: T["notes.pic_size_tips"]);

                    <div class="component_image" data-component="@type" data-width="@width" data-height="@height">
                        <div class="image_name plugins_name">@T[$"variable.{Name}.name"]</div>
                        <div class="image_box">@Html.Raw(imgHtml)</div>




                        @if (!format.IsNullOrEmpty())
                        {
                            string format_pic_tips = T["themes.format_pic_tips"].Value.Replace("%width%", width.ToString()).Replace("%height%", height.ToString()).Replace("%format%", format.ToString());
                            <div class="image_tips">
                                @format_pic_tips
                            </div>
                        }
                        else
                        {
                            string pic_tips = T["themes.pic_tips"].Value.Replace("%width%", width.ToString()).Replace("%height%", height.ToString()).Replace("%format%", format.ToString());
                            <div class="image_tips">
                                @pic_tips
                            </div>
                        }

                    </div>

                    break;
                }
            case "panel":// 面板按钮组件---
                {
                    break;
                }
            case "link":// 链接组件
                {

                    var link = new List<string>();
                    if (!Value.IsNullOrEmpty())
                    {
                        if (Value.Contains("["))
                        {
                            link = Value.JsonToObj<List<string>>();
                        }
                        else
                        {
                            link.Add(Value);
                            link.Add("");
                        }
                    }
                    else
                    {
                        link.Add("");
                        link.Add("");
                    }


                    <div class="component_link" data-component="@type">
                        <div class="link_name plugins_name">@T[$"variable.{Name}.name"]</div>
                        <div class="link_box">
                            <input type="hidden" name="@(InputName)[]" value="add" />
                            <input class="box_input full_input" type="text" name="@(InputName)[]" value="@link[1]" />
                        </div>
                    </div>

                    break;
                }
            case "richtext":// 富文本组件
                {

                    <div class="component_richtext" data-component="@type">
                        <div class="richtext_name plugins_name">@T[$"variable.{Name}.name"]</div>
                        <div class="richtext_box">
                            <textarea id='@InputName' name='@InputName' data-change='0'>@Value</textarea>
                        </div>
                        @if (Config["expand"]?["hint"] != null)
                        {
                            <div class="richtext_hint">@T[$"variable.{Name}.{Config["expand"]["hint"].ToString()}"].ToString()</div>
                        }
                    </div>

                    break;
                }
            case "textalign":// 文本对齐方式组件
                {
                    <div class="component_textalign" data-component="@type">
                        <div class="textalign_name plugins_name">@T[$"variable.{Name}.name"]</div>
                        <div class="textalign_box">
                            <div class="textalign_item left @(Value == "left" ? "current" : "")" data-value="left"></div>
                            <div class="textalign_item center  @(Value == "center" ? "current" : "")" data-value="center"></div>
                            <div class="textalign_item right  @(Value == "right" ? "current" : "")" data-value="right"></div>
                        </div>
                        <input type="hidden" name="@InputName" value="@Value" />
                    </div>
                    break;
                }
            case "products":// 产品组件
                {

                    break;
                }
            case "word":// 文字组件
                {
                    <div class="component_word" data-component="@type">
                        @if (Config["style"].ToString() == "desc")//描述
                        {
                            <div class="word_desc_name plugins_name">@T[$"variable.{Name}.name"]</div>
                            <div class="word_desc_tips">

                                @T[$"variable.{Name}.{Config["hint"].ToString()}"]
                            </div>
                        }
                        else if (Config["style"].ToString() == "notice")// 提示
                        {
                            <div class="word_notice_tips">
                                @T[$"variable.{Name}.{Config["hint"].ToString()}"]
                            </div>
                        }
                        <input type="hidden" name="expand[word][@Name]" />
                    </div>
                    break;
                }
            case "effect":// 效果组件
                {
                    string options = "[]";
                    if (Config["expand"]?["options"] != null)
                    {
                        options = Config["expand"]["options"].ToString();
                    }


                    <div class="component_effect" data-component="@type">
                        <div class="effect_name plugins_name">@T[$"variable.{Name}.name"]</div>
                        <div class="effect_box">
                            <div class="effect_name">
                                <i class="effect_icon effect_icon_@Value"></i>
                                <span>
                                    @T[$"variable.{Name}.options.{Value}"].Value

                                </span>
                            </div>
                            <div class="effect_btn" data-fixed-plugins="effect-select" data-options="@options">@T["global.edit"]</div>
                            <input type="hidden" name="@InputName" value="@Value" />
                        </div>
                    </div>

                    break;
                }
            case "category":// 分类组件---
                {
                    break;
                }
            case "hidden":// 隐藏组件
                {
                    <div class="component_hidden" data-component="@type">
                        <input type="hidden" name="@InputName" value="@Value" />
                    </div>
                    break;
                }
            case "time":// 时间选择组件--
                {


                    break;
                }
            case "nav":// 导航组件--
                {
                    break;
                }
            case "video":// 视频上传---
                {
                    // <div class="component_video" data-component="@type">
                    //     <div class="video_name plugins_name">@T[$"variable.{Name}.name"]</div>
                    //     <div class="video_box"><?=HelpsManage::multiFile("VideoDetail_{$Name}_" . mt_rand(1, 9999) . time(), $InputName, $Value);?></div>
                    // </div>
                    break;
                }
            case "news":// 新闻组件---
                {
                    break;
                }
            case "coupon":// 优惠券下拉---
                {
                    break;
                }
            case "seo":// SEO组件---
                {
                    break;
                }

        }
    }
</div>







