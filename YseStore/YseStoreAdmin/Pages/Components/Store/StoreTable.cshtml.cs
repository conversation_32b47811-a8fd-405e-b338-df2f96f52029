using YseStore.Model.Event;
using YseStore.IService.Store;
using YseStore.Model.RequestModels.Store;
using Entitys;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;

namespace YseStoreAdmin.Pages.Components.Store
{
    public class StoreTable : MComponent
    {
        public int TotalCount { get; set; }
        public int PageSize { get; set; }
        public int PageNum { get; set; }
        public string _keyword { get; set; }
        
        private readonly IArticleService _articleService;
        private readonly ILogger<StoreTable> _logger;

        public List<article> Articles { get; set; } = new();

        public StoreTable(IArticleService articleService, ILogger<StoreTable> logger)
        {
            _articleService = articleService ?? throw new ArgumentNullException(nameof(articleService));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            
            // 订阅分页事件
            Subscribe<UserTurnPageEvent>(HandleUserTurnPage);
        }

        // 处理用户分页事件
        public async Task HandleUserTurnPage(UserTurnPageEvent evt)
        {
            if (evt.Name == "storepage")
            {
                await BindData(evt.PageNum);
            }
        }
        
        public override async Task MountAsync()
        {
            // 从URL查询字符串中获取关键词
            _keyword = HttpContext.Request.Query["Keyword"].ToString();
            
            await BindData();
        }

        [SkipOutput]
        public async Task BindData(int page = 1)
        {
            try
            {
                // 构建查询请求
                var request = new ArticleQueryRequest
                {
                    Keyword = _keyword ?? "",
                    PageIndex = page,
                    PageSize = PageSize > 0 ? PageSize : 10
                };
                
                var result = await _articleService.GetArticleListAsync(request);
                Articles = result?.data ?? new List<article>();
                TotalCount = result?.dataCount ?? 0;
                PageNum = page;
                PageSize = request.PageSize;
                
                // 发送分页事件到全局
                DispatchGlobal<PageEvent>(new PageEvent(TotalCount, PageSize, PageNum, "storepage"), null, true);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "加载单页文章表格数据失败");
            }
        }

        public void HandlePageEvent(PageEvent evt)
        {
            TotalCount = evt.Total;
            PageSize = evt.PageSize;
            PageNum = evt.PageNum;
        }

        [SkipOutput]
        public async Task TurnPage(int page)
        {
            DispatchGlobal<UserTurnPageEvent>(new UserTurnPageEvent("storepage", page));
        }
    }
} 