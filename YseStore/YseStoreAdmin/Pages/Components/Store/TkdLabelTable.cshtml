 
@model YseStoreAdmin.Pages.Components.Store.TkdLabelTable 
@{
}
<div>
    <table border="0" cellpadding="5" cellspacing="0" class="r_con_table new" width="100%">
        <thead>
            <tr>
                <td width="6%" nowrap="nowrap" class="pos">
                    <ul class="table_menu_button global_menu_button">
                        <li><div class="btn_checkbox "><em class="button"></em><input type="checkbox" name="select_all" value=""></div></li>
                        <li class="open">已选择<span></span>个</li>
                    </ul>
                </td>
                <td width="23.5%" nowrap="nowrap">页面</td>
                <td width="23.5%" nowrap="nowrap">标题</td>
                <td width="23.5%" nowrap="nowrap">关键词</td>
                <td width="23.5%" nowrap="nowrap">描述</td>
            </tr>
        </thead>
        <tbody>
            @if (Model.TkdLableList != null && Model.TkdLableList.Any())
            {
                foreach (var item in Model.TkdLableList)
                {
                    <tr data-id="@item.Id">
                        <td nowrap="nowrap"><div class="btn_checkbox "><em class="button"></em><input type="checkbox" name="select[]" value="@item.Id"></div></td>
                        <td><div class="title">@item.Page</div></td>
                        <td>
                            @{
                                
                                <div class="number_limit_relative" style="position:relative">
                                    <div class="number_limit ing"></div> 
                                    <textarea name="SeoTitle[@item.Id]" maxlength="255" disabled="disabled" oninput="this.style.height = (this.scrollHeight+2)+'px'"
                                              number_limit="" placeholder="" data-placeholder="请输入标题">@item.Title</textarea>
                                </div>
                            }

                        </td>
                        <td>
                            <div class="rows keys_row clean">
                                <div class="input">
                                    <div class="box_option_list">
                                        <div class="option_selected" data-type="seo_keyword">
                                            <div class="select_list">
                                                @if (!string.IsNullOrEmpty(item.Keyword))
                                                {
                                                    List<string> arrs = item.Keyword.Split(',', StringSplitOptions.RemoveEmptyEntries).ToList();
                                                    foreach (var key in arrs)
                                                    {
                                                        <span class="btn_attr_choice current" data-type="keys">
                                                            <b>@key</b>
                                                            <input type="checkbox" name="keysCurrent[@item.Id][]" value="@key" checked="" class="option_current">
                                                            <input type="hidden" name="keysOption[@item.Id][]" value="@key">
                                                            <input type="hidden" name="keysName[@item.Id][]" value="@key"><i></i>
                                                        </span>
                                                    }
                                                }
                                            </div>
                                            <input type="text" class="box_input" name="_Option" value="" size="30" maxlength="255" placeholder="" data-placeholder="请输入关键词，按回车键或者输入英文逗号保存">
                                        </div>
                                        <div class="option_button_menu" style="display:none;">
                                            <a href="javascript:;" data-type="keys" class="current">标签</a>
                                        </div>
                                        <input type="hidden" class="option_max_number" value="0">
                                    </div>
                                </div>
                            </div>
                        </td>
                        <td>
                            @{
                               
                                <div class="number_limit_relative" style="position:relative">
                                    <div class="number_limit ing"></div>
                                    <textarea name="SeoDescription[@item.Id]" maxlength="500" disabled="disabled" oninput="this.style.height = (this.scrollHeight+2)+'px'"
                                              number_limit="" placeholder="" data-placeholder="请输入描述">@item.Description </textarea>
                                </div>
                            }

                        </td>
                    </tr>
                }
            }

        </tbody>
    </table>
</div>