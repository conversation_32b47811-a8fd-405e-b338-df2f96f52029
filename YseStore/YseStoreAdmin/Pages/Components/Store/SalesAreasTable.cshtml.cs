using Entitys;
using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using YseStore.Common.Helper;
using YseStore.IService.Customer;
using YseStore.IService.Store;
using YseStore.Model.Event;

namespace YseStoreAdmin.Pages.Components.Store
{
    public class SalesAreasTable : MComponent
    {

        private readonly ISalesAreasService _areasService; // 销售分区服务接口
        public SalesAreasTable(ISalesAreasService urlService)
        {
            this._areasService = urlService;

            Subscribe<UserTurnPageEvent>(HandleUserTurnPage);
        }

        /// <summary>
        /// 搜索关键词
        /// </summary>
        [Parameter]
        public string keywords { get; set; } = "";


        /// <summary>
        /// 
        /// </summary>
        /// <returns></returns>
        public override async Task MountAsync()
        {
            await base.MountAsync();

            //获取表单数据
            //await BindData();

            await GetFormList();
        }


        /// <summary>
        /// 数据
        /// </summary>
        public List<sales_areas> FromList { get; set; } = new List<sales_areas>();


        public async Task HandleUserTurnPage(UserTurnPageEvent evt)
        {
            //if (evt.Name == "manlog")
            //{
            //    await BindData(evt.PageNum);
            //}
        }

        //[SkipOutput]
        //public async Task BindData(int page = 1)
        //{
        //    PagerOptions.PageIndex = page;
        //    //获取重定向列表
        //    await GetFormList();
        //    DispatchGlobal<PageEvent>(new PageEvent(PagerOptions.TotalCount, PagerOptions.PageSize, PagerOptions.PageIndex, "manlog"), null, true);
        //}



        /// <summary>
        /// 获取列表
        /// </summary>
        /// <param name="page"></param>
        /// <param name="limit"></param>
        /// <returns></returns>
        public async Task GetFormList()
        {
            var SearchModel = GetSearchMode();

            var list = await _areasService.GetSalesAreasCache();

            //搜索参数
            if (SearchModel.ContainsKey("keywords"))
            {
                var kw = SearchModel["keywords"].ToString().Trim();
                FromList = list.Where(it => it.Name.Contains(kw) || it.Code.Contains(kw)).ToList();
            }
            else
            {
                FromList = list;
            }

            


        }

        /// <summary>
        /// 获取搜索条件
        /// </summary>
        /// <returns></returns>
        public Dictionary<string, string> GetSearchMode()
        {
            Dictionary<string, string> dic = new Dictionary<string, string>();
            if (!keywords.IsNullOrEmpty())
            {
                dic.Add("keywords", keywords);
            }

            return dic;

        }

    }
}
