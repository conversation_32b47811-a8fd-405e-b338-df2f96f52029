@model YseStoreAdmin.Pages.Components.Store.StoreNav 
@{
}
<div>

    <div id="nav" class="r_con_wrap" style="height: 470px;">
        <div class="center_container">
            <h1>
                <a class="fr nav_btn" href="/Store/UrlRedirect" target="_blank">重定向</a>
                @* <a class="fr nav_btn set_nav_themes" data-type="nav_themes" data-install="1" *@
                @*    href="javascript:;">设置风格</a> *@
                <a href="javascript:;" class=" fr btn_global btn_submit btn_nav_create menu_add"
                   data-url="/manage/view/nav/edit/" data-id="0" data-level="1">添加一级菜单</a>
                导航
            </h1>
            <div class="nav_main_box" data-listidx="0">
                @foreach (var menu in Model.NavMenus.OrderBy(m => m.MyOrder))
                {
                    <div class="nav_dra_item" style="cursor: pointer;">
                        <div class="nav_item" data-id="@menu.MId">
                            @if (menu.Children != null && menu.Children.Any())
                            {
                                <div class="nav_ext current"></div>
                            }
                            <div class="nav_myorder fl"></div>
                            <div class="nav_name fl">@menu.GetDisplayName()</div>
                            <a href="/manage/view/nav/delete?MId=@menu.MId&Type=nav" class="nav_del del fr nav_set "></a>
                            <div class="nav_set fr btn_nav_edit" data-url="/manage/view/nav/edit?MId=@menu.MId"></div>
                            <div class="@(menu.IsHidden ? "hidden " : " ")fr nav_set btn_nav_hidden nav_preview" data-mid="@menu.MId"></div>
                            <div class="nav_add_sub fr btn_nav_create" data-url="/manage/view/nav/edit?ParentId=@menu.MId"
                                 data-id="@menu.MId" data-level="2">
                                添加子菜单
                            </div>
                        </div>
                        @if (menu.Children != null && menu.Children.Any())
                        {
                            <div class="nav_second_box" data-id="@menu.MId" data-listidx="0" style="display: none;">
                                @{
                                    await RenderMenuLevel(menu.Children.OrderBy(m => m.MyOrder).ToList(), 2);
                                }
                            </div>
                        }
                    </div>
                }
            </div>

            <input type="hidden" name="Type" value="nav">
            <input type="hidden" name="do_action" value="/manage/view/nav/order/">
        </div>
    </div>
    <div id="fixed_right" class="">
        <div class="global_container nav_edit" data-width="350" style="display: none; height: 1052px;">
            <div class="top_title">修改一级菜单 <a href="javascript:;" class="close"></a></div>
            <form id="nav_edit_form" class="global_form nav_edit_box" action="/manage/view/nav/edit?MId=45"
                  method="post">
                <input type="hidden" name="_csrf-manage"
                       value="xtMe9C0qkX7eMZ3e_zvObEb40QQs0Zjuq2dKMM8-2I-hp0uWbGT0S6Zz9bCZcoEpNs66UXPly6jZNwt0-VOR4Q==">
                <div class="rows clean">
                    <label>名称<span>（必填）</span></label>
                    <div class="input">
                        <input type="text" name="Name" value="Features" class="box_input" size="46" maxlength="255"
                               notnull="">
                    </div>
                </div>
                <div class="rows clean">
                    <label>页面打开方式</label>
                    <div class="input">
                        <div class="box_type_menu">
                            <span class="item checked">
                                <input type="radio" name="NewTarget" value="0" checked="">
                                当前窗口打开页面
                            </span>
                            <span class="item">
                                <input type="radio" name="NewTarget" value="1">
                                新窗口打开页面
                            </span>
                            <span class="item">
                                <input type="radio" name="NewTarget" value="2">
                                不打开页面，仅展示子菜单
                            </span>
                        </div>
                        <span class="global_app_tips hide">
                            <em></em><span class="tips">部分移动端浏览器不支持新窗口打开页面，<br>将在当前窗口打开</span>
                        </span>
                    </div>
                </div>
                <div class="rows clean " id="box_url">
                    <label>链接</label>
                    <div class="input">
                        <dl class="box_drop_double edit_box" data-checkbox="0" data-showadd="1">
                            <dt>
                                <input type="text" class="box_input" name="UnitValue" placeholder="可选择下拉页面链接" value="/"
                                       autocomplete="off" readonly="readonly"><input type="hidden" name="Unit" value="/"
                                                                                     class="hidden_value"><input type="hidden" name="UnitType" value="add"
                                                                                                                 class="hidden_type">
                            </dt>
                            <dd class="drop_down" style="display: none;">
                                <div class="drop_menu" data-type="nav">
                                    <a href="javascript:;" class="btn_back"
                                       data-value="" data-type="" data-table="" data-top="0" data-all="0"
                                       style="display:none;">返回</a>
                                    <div class="drop_skin" style="display: none;"></div>
                                    <div class="drop_list"
                                         data="{&quot;index&quot;:{&quot;Name&quot;:&quot;首页&quot;,&quot;Type&quot;:&quot;index&quot;,&quot;Table&quot;:&quot;&quot;,&quot;Disabled&quot;:false},&quot;article&quot;:{&quot;Name&quot;:&quot;单页&quot;,&quot;Type&quot;:&quot;article&quot;,&quot;Table&quot;:&quot;article&quot;,&quot;Disabled&quot;:true},&quot;category&quot;:{&quot;Name&quot;:&quot;产品分类&quot;,&quot;Type&quot;:&quot;category&quot;,&quot;Table&quot;:&quot;products_category&quot;,&quot;Disabled&quot;:true},&quot;special_offer&quot;:{&quot;Name&quot;:&quot;促销产品&quot;,&quot;Type&quot;:&quot;special_offer&quot;,&quot;Table&quot;:&quot;&quot;,&quot;Disabled&quot;:false},&quot;products&quot;:{&quot;Name&quot;:&quot;产品&quot;,&quot;Type&quot;:&quot;products&quot;,&quot;Table&quot;:&quot;products&quot;,&quot;Disabled&quot;:true},&quot;blog&quot;:{&quot;Name&quot;:&quot;博客&quot;,&quot;Type&quot;:&quot;blog&quot;,&quot;Table&quot;:&quot;&quot;,&quot;Disabled&quot;:false},&quot;custom&quot;:{&quot;Name&quot;:&quot;自定义&quot;,&quot;Value&quot;:&quot;自定义&quot;,&quot;Type&quot;:&quot;add&quot;,&quot;Table&quot;:&quot;&quot;,&quot;KeyId&quot;:0,&quot;PageId&quot;:0,&quot;Disabled&quot;:false}}"
                                         data-more="none">
                                        <div class="item" data-name="首页" data-value="index" data-type="index"
                                             data-table="" data-top="0" data-all="1" data-check-all="0"
                                             data-custom="undefined">
                                            <span class="input_radio_box">
                                                <span class="input_radio">
                                                    <input type="radio" name="_DoubleOption[]"
                                                           value="index">
                                                </span>
                                            </span><span class="item_name">首页</span>
                                        </div>
                                        <div class="item children" data-name="单页" data-value="article"
                                             data-type="article" data-table="article" data-top="0" data-all="1"
                                             data-check-all="0" data-custom="undefined">
                                            <span class="input_radio_box disabled">
                                                <span class="input_radio">
                                                    <input type="radio" name="_DoubleOption[]" value="article"
                                                           disabled="">
                                                </span>
                                            </span><span class="item_name">单页</span><em></em>
                                        </div>
                                        <div class="item children" data-name="产品分类" data-value="category"
                                             data-type="category" data-table="products_category" data-top="0"
                                             data-all="1" data-check-all="0" data-custom="undefined">
                                            <span class="input_radio_box disabled">
                                                <span class="input_radio">
                                                    <input type="radio" name="_DoubleOption[]" value="category"
                                                           disabled="">
                                                </span>
                                            </span><span class="item_name">产品分类</span><em></em>
                                        </div>
                                        <div class="item" data-name="促销产品" data-value="special_offer"
                                             data-type="special_offer" data-table="" data-top="0" data-all="1"
                                             data-check-all="0" data-custom="undefined">
                                            <span class="input_radio_box">
                                                <span class="input_radio">
                                                    <input type="radio"
                                                           name="_DoubleOption[]" value="special_offer">
                                                </span>
                                            </span><span class="item_name">促销产品</span>
                                        </div>
                                        <div class="item children" data-name="产品" data-value="products"
                                             data-type="products" data-table="products" data-top="0" data-all="1"
                                             data-check-all="0" data-custom="undefined">
                                            <span class="input_radio_box disabled">
                                                <span class="input_radio">
                                                    <input type="radio" name="_DoubleOption[]" value="products"
                                                           disabled="">
                                                </span>
                                            </span><span class="item_name">产品</span><em></em>
                                        </div>
                                        <div class="item" data-name="博客" data-value="blog" data-type="blog"
                                             data-table="" data-top="0" data-all="1" data-check-all="0"
                                             data-custom="undefined">
                                            <span class="input_radio_box">
                                                <span class="input_radio">
                                                    <input type="radio" name="_DoubleOption[]"
                                                           value="blog">
                                                </span>
                                            </span><span class="item_name">博客</span>
                                        </div>
                                        <div class="item drop_add" data-name="自定义" data-value="自定义" data-type="add"
                                             data-table="" data-top="0" data-all="1" data-check-all="0"
                                             data-custom="undefined">
                                            <span class="input_radio_box">
                                                <span class="input_radio">
                                                    <input type="radio" name="_DoubleOption[]"
                                                           value="custom">
                                                </span>
                                            </span><span class="item_name">自定义</span>
                                        </div>
                                    </div><a href="javascript:;" class="btn_load_more" data-value="" data-type=""
                                             data-table="" data-top="0" data-all="0" data-check-all="" data-start="1"
                                             style="display:none;">加载更多</a>
                                </div>
                            </dd>
                        </dl>
                    </div>
                </div>
                <div class="rows clean " id="custom_link" style="">
                    <label>自定义链接</label>
                    <div class="input">
                        <textarea name="Link" class="box_textarea full_textarea" maxlength="255" notnull="">/</textarea>
                    </div>
                </div>
                <div class="rows clean box_button box_submit">
                    <div class="input">
                        <input type="button" class="btn_global btn_submit" value="保存">
                        <input type="button" class="btn_global btn_cancel" value="取消">
                    </div>
                </div>
                <input type="hidden" name="MId" value="45">
                <input type="hidden" name="ParentId" value="0">
                <input type="hidden" name="Type" value="nav">
                <input type="hidden" name="do_action" value="/manage/view/nav/edit">
            </form>
        </div>
    </div>
    <div class="fixed_box_popup box_create_nav" data-width="1340" style="display: none;">
        <div class="box_middle" style="width: 1340px; top: -30px; opacity: 0;">
            <a href="javascript:;" class="close"></a>
            <div class="title">添加子菜单</div>
            <div class="content">
                <form id="create_nav_form" class="global_form" action="/manage/view/nav" method="post" data-level="4">
                    <input type="hidden" name="_csrf-manage"
                           value="LEhmVoFPW547JEsQmUq0yM-8S1HmhSIGhQ21PpkxewpLPDM0wAE-q0NmI37_A_uNv4ogBLmxcUD3XfR6r1wyZA==">
                    <div class="nav_container">
                        <div class="left_menu">
                            <div class="menu_head">页面链接</div>
                            <div class="menu_list">
                                <div class="menu_main_item">
                                    <div class="menu_item" data-type="index" data-table="" data-crumb="首页"
                                         data-link="/">
                                        <div class="collapse disabled"></div>
                                        <div class="name">首页</div>
                                        <a href="javascript:;" class="btn_create_menu">添加</a>
                                    </div>
                                    <div class="menu_sub_list"></div>
                                </div>
                                <div class="menu_main_item">
                                    <div class="menu_item" data-type="article" data-table="article" data-crumb="单页"
                                         data-link="javascript:;">
                                        <div class="collapse"></div>
                                        <div class="name">单页</div>
                                    </div>
                                    <div class="menu_sub_list" ></div>
                                </div>
                                <div class="menu_main_item">
                                    <div class="menu_item" data-type="category" data-table="products_category"
                                         data-crumb="产品分类" data-link="/products/">
                                        <div class="collapse"></div>
                                        <div class="name">产品分类</div>
                                    </div>
                                    <div class="menu_sub_list"></div></div>
                                
                                <div class="menu_main_item">
                                    <div class="menu_item" data-type="special_offer" data-table="" data-crumb="促销产品"
                                         data-link="/Special-Offer/">
                                        <div class="collapse disabled"></div>
                                        <div class="name">促销产品</div>
                                        <a href="javascript:;" class="btn_create_menu">添加</a>
                                    </div>
                                    <div class="menu_sub_list"></div>
                                </div>
                                <div class="menu_main_item">
                                    <div class="menu_item" data-type="products" data-table="products" data-crumb="产品"
                                         data-link="/products/">
                                        <div class="collapse "></div>
                                        <div class="name">产品</div>
                                    </div>
                                    <div class="menu_sub_list"></div>
                                </div>
                                <div class="menu_main_item">
                                    <div class="menu_item" data-type="blog" data-table="" data-crumb="博客"
                                         data-link="/blog/">
                                        <div class="collapse disabled"></div>
                                        <div class="name">博客</div>
                                        <a href="javascript:;" class="btn_create_menu">添加</a>
                                    </div>
                                    <div class="menu_sub_list"></div>
                                </div>
                                <div class="menu_main_item">
                                    <div class="menu_item" data-type="add" data-table="" data-crumb="自定义" data-link="">
                                        <div class="collapse disabled"></div>
                                        <div class="name">自定义</div>
                                        <a href="javascript:;" class="btn_create_menu">添加</a>
                                    </div>
                                    <div class="menu_sub_list"></div>
                                </div>
                            </div>
                        </div>
                        <div class="right_content">
                            <div class="nav_table" style="display: block;">
                                <div class="nav_head">
                                    <div class="nav_th">导航名称</div>
                                    <div class="nav_th">页面打开方式</div>
                                    <div class="nav_th">链接</div>
                                    <div class="nav_th">图标<em>26*26px</em></div>
                                    <div class="nav_th">添加子分类</div>
                                    <div class="nav_th"></div>
                                </div>
                                <div class="nav_body">
                                    <div class="nav_item">
                                        <div class="item_cell item_name">
                                            <input type="text" name="Name[]" value="促销产品" class="box_input" size="46"
                                                   maxlength="255" notnull="">
                                        </div>
                                        <div class="item_cell item_target">
                                            <div class="box_select">
                                                <select name="NewTarget[]">

                                                    <option value="0">当前窗口打开页面</option>
                                                    <option value="1">新窗口打开页面</option>
                                                    <option value="2">不打开页面，仅展示子菜单</option>

                                                </select>
                                            </div>
                                        </div>
                                        <div class="item_cell item_link">
                                            <span>促销产品</span><input type="hidden" name="Link[]" value="/Special-Offer/">
                                        </div>
                                        <div class="item_cell item_icon">
                                            <div class="fixed_icon"></div>
                                            <div class="multi_img upload_file_multi global_upload_box nav_icon_upload"
                                                 id="IcoDetail_1" data-id="1">
                                                <dl class="img shop_language_enable" num="0">
                                                    <dt class="upload_box preview_pic">
                                                        <input type="button" class="btn_ok upload_btn"
                                                               name="submit_button" value="上传图片" tips="">
                                                        <input type="hidden" name="Icon[]" value="" data-value=""
                                                               save="0">
                                                    </dt>
                                                    <dd class="pic_btn">
                                                        <a href="javascript:;" class="zoom" target="_blank"
                                                           title="picture">
                                                            <i class="icon_multi_view"></i>
                                                        </a>
                                                        <a href="javascript:;" class="del" rel="del">
                                                            <i class="icon_multi_delete"></i>
                                                        </a>
                                                    </dd>
                                                </dl>
                                            </div>
                                        </div>
                                        <div class="item_cell item_subcate">
                                            <input type="hidden" name="SubCate[]" value="0">
                                        </div>
                                        <div class="item_cell item_delete">
                                            <a href="javascript:;" class="btn_menu_del"></a>
                                            <input type="hidden" name="type[]" value="special_offer">
                                            <input type="hidden" name="id[]" value="0">
                                        </div>
                                    </div>

                                    <div class="nav_item">
                                        <div class="item_cell item_name">
                                            <input type="text" name="Name[]" value="促销产品" class="box_input" size="46"
                                                   maxlength="255" notnull="">
                                        </div>
                                        <div class="item_cell item_target">
                                            <div class="box_select">
                                                <select name="NewTarget[]">

                                                    <option value="0">当前窗口打开页面</option>
                                                    <option value="1">新窗口打开页面</option>
                                                    <option value="2">不打开页面，仅展示子菜单</option>

                                                </select>
                                            </div>
                                        </div>
                                        <div class="item_cell item_link">
                                            <span>促销产品</span><input type="hidden" name="Link[]" value="/Special-Offer/">
                                        </div>
                                        <div class="item_cell item_icon">
                                            <div class="fixed_icon"></div>
                                            <div class="multi_img upload_file_multi global_upload_box nav_icon_upload"
                                                 id="IcoDetail_2" data-id="2">
                                                <dl class="img shop_language_enable" num="0">
                                                    <dt class="upload_box preview_pic">
                                                        <input type="button" class="btn_ok upload_btn"
                                                               name="submit_button" value="上传图片" tips="">
                                                        <input type="hidden" name="Icon[]" value="" data-value=""
                                                               save="0">
                                                    </dt>
                                                    <dd class="pic_btn">
                                                        <a href="javascript:;" class="zoom" target="_blank"
                                                           title="picture">
                                                            <i class="icon_multi_view"></i>
                                                        </a>
                                                        <a href="javascript:;" class="del" rel="del">
                                                            <i class="icon_multi_delete"></i>
                                                        </a>
                                                    </dd>
                                                </dl>
                                            </div>
                                        </div>
                                        <div class="item_cell item_subcate">
                                            <input type="hidden" name="SubCate[]" value="0">
                                        </div>
                                        <div class="item_cell item_delete">
                                            <a href="javascript:;" class="btn_menu_del"></a>
                                            <input type="hidden" name="type[]" value="special_offer">
                                            <input type="hidden" name="id[]" value="0">
                                        </div>
                                    </div>

                                    <div class="nav_item">
                                        <div class="item_cell item_name">
                                            <input type="text" name="Name[]" value="促销产品" class="box_input" size="46"
                                                   maxlength="255" notnull="">
                                        </div>
                                        <div class="item_cell item_target">
                                            <div class="box_select">
                                                <select name="NewTarget[]">

                                                    <option value="0">当前窗口打开页面</option>
                                                    <option value="1">新窗口打开页面</option>
                                                    <option value="2">不打开页面，仅展示子菜单</option>

                                                </select>
                                            </div>
                                        </div>
                                        <div class="item_cell item_link">
                                            <span>促销产品</span><input type="hidden" name="Link[]" value="/Special-Offer/">
                                        </div>
                                        <div class="item_cell item_icon">
                                            <div class="fixed_icon"></div>
                                            <div class="multi_img upload_file_multi global_upload_box nav_icon_upload"
                                                 id="IcoDetail_3" data-id="3">
                                                <dl class="img shop_language_enable" num="0">
                                                    <dt class="upload_box preview_pic">
                                                        <input type="button" class="btn_ok upload_btn"
                                                               name="submit_button" value="上传图片" tips="">
                                                        <input type="hidden" name="Icon[]" value="" data-value=""
                                                               save="0">
                                                    </dt>
                                                    <dd class="pic_btn">
                                                        <a href="javascript:;" class="zoom" target="_blank"
                                                           title="picture">
                                                            <i class="icon_multi_view"></i>
                                                        </a>
                                                        <a href="javascript:;" class="del" rel="del">
                                                            <i class="icon_multi_delete"></i>
                                                        </a>
                                                    </dd>
                                                </dl>
                                            </div>
                                        </div>
                                        <div class="item_cell item_subcate">
                                            <input type="hidden" name="SubCate[]" value="0">
                                        </div>
                                        <div class="item_cell item_delete">
                                            <a href="javascript:;" class="btn_menu_del"></a>
                                            <input type="hidden" name="type[]" value="special_offer">
                                            <input type="hidden" name="id[]" value="0">
                                        </div>
                                    </div>

                                    <div class="nav_item">
                                        <div class="item_cell item_name">
                                            <input type="text" name="Name[]" value="自定义" class="box_input" size="46"
                                                   maxlength="255" notnull="">
                                        </div>
                                        <div class="item_cell item_target">
                                            <div class="box_select">
                                                <select name="NewTarget[]">

                                                    <option value="0">当前窗口打开页面</option>
                                                    <option value="1">新窗口打开页面</option>
                                                    <option value="2">不打开页面，仅展示子菜单</option>

                                                </select>
                                            </div>
                                        </div>
                                        <div class="item_cell item_link">
                                            <input type="text" name="Link[]" value="" class="box_input" size="46"
                                                   maxlength="255" notnull="">
                                        </div>
                                        <div class="item_cell item_icon">
                                            <div class="fixed_icon"></div>
                                            <div class="multi_img upload_file_multi global_upload_box nav_icon_upload"
                                                 id="IcoDetail_4" data-id="4">
                                                <dl class="img shop_language_enable" num="0">
                                                    <dt class="upload_box preview_pic">
                                                        <input type="button" class="btn_ok upload_btn"
                                                               name="submit_button" value="上传图片" tips="">
                                                        <input type="hidden" name="Icon[]" value="" data-value=""
                                                               save="0">
                                                    </dt>
                                                    <dd class="pic_btn">
                                                        <a href="javascript:;" class="zoom" target="_blank"
                                                           title="picture">
                                                            <i class="icon_multi_view"></i>
                                                        </a>
                                                        <a href="javascript:;" class="del" rel="del">
                                                            <i class="icon_multi_delete"></i>
                                                        </a>
                                                    </dd>
                                                </dl>
                                            </div>
                                        </div>
                                        <div class="item_cell item_subcate">
                                            <input type="hidden" name="SubCate[]" value="0">
                                        </div>
                                        <div class="item_cell item_delete">
                                            <a href="javascript:;" class="btn_menu_del"></a>
                                            <input type="hidden" name="type[]" value="add">
                                            <input type="hidden" name="id[]" value="0">
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="no_data" style="display: none;">
                                <p>暂无数据</p>
                            </div>
                        </div>
                    </div>
                    <div class="button">
                        <input type="button" class="btn_global btn_submit" value="保存"> <input type="button"
                                                                                              class="btn_global btn_cancel" value="取消"> <input type="hidden" name="ParentId" value="45">
                        <input type="hidden" name="Level" value="2"> <input type="hidden" name="NavType" value="nav">
                        <input type="hidden" name="do_action" value="/manage/view/nav/create">
                    </div>
                </form>
            </div>
        </div>
    </div>

</div>

@{
    async Task RenderMenuLevel(List<YseStore.Model.Response.Store.MenuResponse> menus, int level)
    {
        foreach (var subMenu in menus)
        {
            var levelClass = level == 2 ? "second" : $"level-{level}";
            var nextLevel = level + 1;

            <div class="nav_dra_second_item" style="cursor: pointer; margin-left: @((level - 2) * 20)px;">
                <div class="nav_item @levelClass" data-id="mid@(subMenu.MId)">
                    @if (subMenu.Children != null && subMenu.Children.Any())
                    {
                        <div class="nav_ext current"></div>
                    }
                    <div class="nav_myorder fl"></div>
                    <div class="nav_name fl">@subMenu.GetDisplayName()</div>
                    <a href="/manage/view/nav/delete?MId=@subMenu.MId&Type=nav"
                       class="nav_del del fr nav_set "></a>
                    <div class="nav_set fr btn_nav_edit"
                         data-url="/manage/view/nav/edit?MId=@subMenu.MId&NavType=sub"></div>
                    <div class="@(subMenu.IsHidden ? "hidden " : "")fr nav_set btn_nav_hidden nav_preview" data-mid="@subMenu.MId"></div>
                    <div class="nav_add_sub fr btn_nav_create" data-url="/manage/view/nav/edit?ParentId=@subMenu.MId"
                         data-id="@subMenu.MId" data-level="@nextLevel">
                        添加子菜单
                    </div>
                </div>
                @if (subMenu.Children != null && subMenu.Children.Any())
                {
                    <div class="nav_third_box" data-id="@subMenu.MId" data-listidx="0" style="display: none;">
                        @{
                            await RenderMenuLevel(subMenu.Children.OrderBy(m => m.MyOrder).ToList(), nextLevel);
                        }
                    </div>
                }
            </div>
        }
    }
}