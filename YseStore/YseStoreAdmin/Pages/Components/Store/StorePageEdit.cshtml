@model YseStoreAdmin.Pages.Components.Store.StorePageEdit
@{
    // 从配置中获取URL前缀，添加collections路径
    var urlPrefix = (Model.PageUrlBase ?? "https://www.retekess.it").TrimEnd('/') + "/pages";
}
<div class="page-container">
    <div id="page_inside" class="r_con_wrap" style="height: 401px;">
        <div class="center_container_1200">
            <div class="return_title">
                <a href="/Store/SinglePages">
                    <span class="return">单页</span>
                    <span class="s_return">/ @(Model.AId.HasValue && Model.AId.Value > 0 ? "编辑" : "添加")</span>
                </a>
            </div>
        </div>
        <form id="edit_form" class="global_form wrap_content center_container_1200" onsubmit="return false;">
            <input type="hidden" name="_csrf-manage"
                   value="B367BT2KaDhQg9nfVtkXO7hUq6obuv6UgwvcxdMeRIV1Nvk3CtMxd2jatZEJgyBUzh_O7lz-r9fFRq-8insV1Q==">
            <div class="left_container">
                <div class="left_container_side">
                    <div class="global_container">
                        <div class="rows clean">
                            <label>页面名称</label>
                            <div class="input multi_lang">
                                <div class="lang_txt lang_txt_en" style="display:block;" data-default="1" lang="en">
                                    <span class="unit_input" parent_null="">
                                        <input data-auto-change="SeoTitle_en"
                                               type="text" class="box_input" name="Title_en"
                                               value="@(Model.Article?.Title_en ?? "")" size="60"
                                               autocomplete="off" maxlength="100" notnull="" parent_null="1"
                                               style="width: 856px;">
                                    </span>
                                </div>
                            </div>
                        </div>
                        <div class="rows clean">
                            <label>详细描述</label>
                            <span class="input">
                                <div class="tab_txt tab_txt_en" style="display:block;" lang="en">
                                    <div class="fl"></div><textarea id="Content_en" name="Content_en" data-change="1"
                                                                    aria-hidden="true">@(Model.ArticleContent?.Content_en ?? "")</textarea>
                                  
                                </div>
                            </span>
                            <div class="clear"></div>
                        </div>
                        <div class="rows clean">
                            <label>移动端详细描述</label>
                            <div class="box_explain">开启后，移动端的详细描述将替换成以下内容</div>
                            <div
                                class="switchery @(Model.ArticleContent != null && Model.ArticleContent.UsedMobile ? "active" : "")">
                                <input type="checkbox" name="UsedMobile" value="1"
                                       @(Model.ArticleContent != null && Model.ArticleContent.UsedMobile ? "checked" : "")>
                                <div class="switchery_toggler"></div>
                                <div class="switchery_inner">
                                    <div class="switchery_state_on"></div>
                                    <div class="switchery_state_off"></div>
                                </div>
                            </div>
                            <div class="blank15"></div>
                            <div
                                class="input mobile_description @(Model.ArticleContent == null || !Model.ArticleContent.UsedMobile ? "hide" : "")">
                                <textarea id="MobileDescription"
                                          name="MobileDescription" data-change="0" aria-hidden="true"
                                >@(Model.ArticleContent?.MobileDescription ?? "")</textarea>

                            </div>
                        </div>

                    </div>
                </div>
            </div>
            <div class="right_container">
                <div class="global_container global_seo_box">
                    <div class="big_title">SEO</div>
                    <div class="rows clean multi_lang" data-name="title">
                        <label>
                            标题<span class="tool_tips_ico"
                                      content="即TKD中的Title.每个页面都有自己的Title,作为搜索引擎对页面内容主题判断的第一个标识.建议包含页面相关关键词并提出当前页面内容价值.字符数控制在70以内.">
                            </span>
                        </label>
                        <div class="input">
                            <div class="lang_txt lang_txt_en" style="display:block;" data-default="1" lang="en">
                                <span class="unit_input">
                                    <div class="number_limit_relative" style="position:relative">
                                        <div
                                            class="number_limit"><span> @(Model.Article?.SeoTitle_en?.Length ?? 0) </span> / 255</div><input
                                            data-auto-change="_en" type="text" class="box_input" name="SeoTitle_en"
                                            value="@(Model.Article?.SeoTitle_en ?? "")" size="48" autocomplete="off"
                                            maxlength="255" number_limit=""
                                            style="width: 336px;">
                                    </div>
                                </span>
                            </div>
                        </div>
                    </div>
                    <div class="rows clean multi_lang" data-name="description">
                        <label>
                            描述<span class="tool_tips_ico"
                                      content="即TKD中的Description.对页面内容的描述,可以理解为对Title的补充,建议出现三次关键词,内容要精简具有可读性.字符数控制在220以内.">
                            </span>
                        </label>
                        <div class="input">
                            <div class="lang_txt lang_txt_en" style="display:block;" data-default="1" lang="en">
                                <span class="unit_input unit_textarea" parent_null="">
                                    <div class="number_limit_relative" style="position:relative">
                                        <div
                                            class="number_limit"><span> @(Model.Article?.SeoDescription_en?.Length ?? 0) </span> / 500</div><textarea
                                            data-auto-change="_en" name="SeoDescription_en" maxlength="500" cols="50"
                                            class="box_textarea" number_limit=""
                                            style="width: 336px;">@(Model.Article?.SeoDescription_en ?? "")</textarea>
                                    </div>
                                </span>
                            </div>
                        </div>
                    </div>
                    <div class="rows clean multi_lang" data-name="keyword">
                        <label>
                            关键词<span class="tool_tips_ico"
                                        content="即TKD中的Keywords.建议放上与页面内容相关的核心关键词和对应的长尾关键词, 建议根据关键词长度设置8-10个关键词.">
                            </span>
                        </label>
                        <dl class="box_basic_more box_seo_basic_more">
                            <dt><a href="javascript:;" class="btn_basic_more"><i></i></a></dt>
                            <dd class="drop_down">
                                <a href="javascript:;" class="item input_checkbox_box btn_open_attr"
                                   id="edit_keyword"><span>修改</span></a>
                            </dd>
                        </dl>
                        <div class="rows keys_row clean">
                            <div class="input">
                                <div class="box_option_list">
                                    <div class="option_selected" data-type="seo_keyword">
                                        <div class="select_list">
                                        </div>
                                        <input type="text" class="box_input" name="_Option" value="" size="30"
                                               maxlength="255">
                                        <span class="placeholder">填写好内容，按回车键或者输入英文逗号保存</span>
                                    </div>
                                    <div class="option_button_menu" style="display:none;">
                                        <a href="javascript:;"
                                           data-type="keys" class="current">标签</a>
                                    </div>
                                    <input type="hidden"
                                           class="option_max_number" value="0">
                                </div>
                            </div>
                        </div>

                        @if (Model.AId > 0)
                        {
                            <div class="rows custom_row clean" style="margin-top: 6%" data-name="url">
                                <label class="fl">自定义地址</label>
                                <a class="btn_copy fr" href="javascript:;" data-clipboard-action="copy"
                                   data-clipboard-text="@(string.IsNullOrEmpty(Model.Article?.PageUrl) ? $"{urlPrefix}/{Model.Article?.Title_en ?? ""}" : $"{urlPrefix}/{Model.Article?.PageUrl}")">复制链接</a>
                                <div class="clear"></div>
                                <div class="input">
                                    <div class="prefix_textarea">
                                        <div class="prefix">@(urlPrefix)/<i></i></div>
                                        <textarea name="PageUrl" class="box_textarea"
                                                  data-domain="@(urlPrefix)/"
                                                  style="top: 11px; text-indent: 211.875px; height: 99.3333px;">@(string.IsNullOrEmpty(Model.Article?.PageUrl) ? Model.Article?.Title_en ?? "" : Model.Article?.PageUrl)</textarea>
                                    </div>
                                </div>
                            </div>
                        }
                    </div>

                </div>

                <!-- 页面设置 -->
                <div class="global_container" style="margin-top: 20px;">
                    <div class="big_title">页面设置</div>

                    <!-- 是否启用 -->
                    <div class="rows clean">
                        <label>是否启用</label>
                        <div class="box_explain">启用后页面将对外可见，禁用后页面将不可访问</div>
                        <div class="switchery @(Model.Article?.Status == 1 ? "active" : "")">
                            <input type="checkbox" name="Status" value="1"
                                   @(Model.Article?.Status == 1 ? "checked" : "")>
                            <div class="switchery_toggler"></div>
                            <div class="switchery_inner">
                                <div class="switchery_state_on"></div>
                                <div class="switchery_state_off"></div>
                            </div>
                        </div>
                    </div>

                    <!-- 不含页头 -->
                    <div class="rows clean">
                        <label>不含页头</label>
                        <div class="box_explain">开启后页面将不显示网站头部导航</div>
                        <div class="switchery @(Model.Article?.NotIncludedHeader == true ? "active" : "")">
                            <input type="checkbox" name="NotIncludedHeader" value="true"
                                   @(Model.Article?.NotIncludedHeader == true ? "checked" : "")>
                            <div class="switchery_toggler"></div>
                            <div class="switchery_inner">
                                <div class="switchery_state_on"></div>
                                <div class="switchery_state_off"></div>
                            </div>
                        </div>
                    </div>

                    <!-- 不含页脚 -->
                    <div class="rows clean">
                        <label>不含页脚</label>
                        <div class="box_explain">开启后页面将不显示网站底部信息</div>
                        <div class="switchery @(Model.Article?.NotIncludedFooter == true ? "active" : "")">
                            <input type="checkbox" name="NotIncludedFooter" value="true"
                                   @(Model.Article?.NotIncludedFooter == true ? "checked" : "")>
                            <div class="switchery_toggler"></div>
                            <div class="switchery_inner">
                                <div class="switchery_state_on"></div>
                                <div class="switchery_state_off"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <input type="hidden" id="AId" name="AId" value="@Model.AId">
        </form>
        <div id="fixed_right">
            <div class="global_container fixed_edit_keyword" data-width="350">
                <div class="top_title"><strong>修改关键词</strong><a href="javascript:;" class="close"></a></div>
                <form class="global_form" id="edit_keyword_form">
                    <div class="edit_keyword_list"></div>
                    <div class="rows clean box_button box_submit">
                        <div class="input">
                            <input type="submit" class="btn_global btn_submit" value="确定"><input type="button"
                                                                                                   class="btn_global btn_cancel"
                                                                                                   value="取消">
                        </div>
                    </div>
                    <div class="bg_no_table_data bg_no_table_fixed">
                        <div class="content">
                            <p class="color_000">您当前没有添加选项</p><a href="javascript:;"
                                                                          class="btn_global btn_add_item btn_cancel">关闭</a>
                        </div>
                    </div>
                    <input type="hidden" name="Type" value="article"><input type="hidden" name="field"
                                                                            value="AId"><input type="hidden" name="Id"
                                                                                               value="@Model.AId"><input
                        type="hidden" name="do_action"
                        value="/manage/action/seo-keyword-edit">
                </form>
            </div>
        </div>
    </div>
    <div class="rows clean fixed_btn_submit" style="width: 2096px; left: 180px;">
        <div class="center_container_1200">
            <div class="input">
                <input type="button" class="btn_global btn_submit" value="提交" onclick="submitForm(); return false;">
                <a href="/Store/SinglePages"><input type="button" class="btn_global btn_cancel" value="返回"></a>
            </div>
        </div>
    </div>

    <script>
        // 确保在页面完全加载后执行
        window.onload = function () {
            // 获取关键词字符串
            var seoKeyword = "@Html.Raw(Model.Article?.SeoKeyword_en ?? "")";

            // 如果有关键词，处理并显示
            if (seoKeyword && seoKeyword.trim() !== "") {
                // 分割关键词（以逗号分隔）
                var keywords = seoKeyword.split(',');

                // 获取关键词容器
                var keywordContainer = document.querySelector('.option_selected[data-type="seo_keyword"] .select_list');

                if (!keywordContainer) {
                    return;
                }

                // 清空现有内容
                keywordContainer.innerHTML = '';

                // 确保隐藏字段存在
                var hiddenField = document.querySelector('input[name="SeoKeyword_en"]');
                if (!hiddenField) {
                    hiddenField = document.createElement('input');
                    hiddenField.type = 'hidden';
                    hiddenField.name = 'SeoKeyword_en';
                    document.getElementById('edit_form').appendChild(hiddenField);
                } else {
                    hiddenField.value = seoKeyword;
                }

                // 添加每个关键词
                keywords.forEach(function (keyword, index) {
                    keyword = keyword.trim();
                    if (keyword !== '') {
                        // 创建关键词元素
                        var keywordSpan = document.createElement('span');
                        keywordSpan.className = 'btn_attr_choice current';
                        keywordSpan.setAttribute('data-type', 'seo_keyword');

                        // 设置关键词内容
                        keywordSpan.innerHTML =
                            '<b>' + keyword + '</b>' +
                            '<input type="checkbox" name="seo_keywordCurrent[]" value="ADD:' + (index + 1) + '" class="option_current" checked>' +
                            '<input type="hidden" name="seo_keywordOption[]" value="ADD:' + (index + 1) + '">' +
                            '<input type="hidden" name="seo_keywordName[]" value="' + keyword + '">' +
                            '<i></i>';

                        // 添加到容器
                        keywordContainer.appendChild(keywordSpan);
                    }
                });

                // 如果有关键词，隐藏占位符
                if (keywords.length > 0) {
                    var placeholder = document.querySelector('.option_selected[data-type="seo_keyword"] .placeholder');
                    if (placeholder) {
                        placeholder.style.display = 'none';
                    }
                }

                // 绑定关键词删除事件
                document.addEventListener('click', function (e) {
                    if (e.target && e.target.tagName === 'I' &&
                        e.target.parentNode &&
                        e.target.parentNode.classList.contains('btn_attr_choice') &&
                        e.target.parentNode.getAttribute('data-type') === 'seo_keyword') {

                        // 删除关键词元素
                        e.target.parentNode.remove();

                        // 更新隐藏字段
                        updateKeywords();

                        // 如果没有关键词了，显示占位符
                        var remainingKeywords = document.querySelectorAll('.option_selected[data-type="seo_keyword"] .btn_attr_choice');
                        if (remainingKeywords.length === 0) {
                            var placeholder = document.querySelector('.option_selected[data-type="seo_keyword"] .placeholder');
                            if (placeholder) {
                                placeholder.style.display = '';
                            }
                        }
                    }
                });

                // 处理输入框事件 - 添加新关键词
                var optionInput = document.querySelector('.option_selected[data-type="seo_keyword"] input[name="_Option"]');
                if (optionInput) {
                    optionInput.addEventListener('keyup', function (e) {
                        var value = this.value.trim();

                        // 回车键或逗号
                        if ((e.keyCode === 13 || value.endsWith(',')) && value.length > 0) {
                            e.preventDefault();

                            // 移除末尾的逗号
                            if (value.endsWith(',')) {
                                value = value.slice(0, -1).trim();
                            }

                            if (value) {
                                // 获取当前关键词数量作为索引
                                var index = document.querySelectorAll('.option_selected[data-type="seo_keyword"] .btn_attr_choice').length;

                                // 创建关键词元素
                                var keywordSpan = document.createElement('span');
                                keywordSpan.className = 'btn_attr_choice current';
                                keywordSpan.setAttribute('data-type', 'seo_keyword');

                                // 设置关键词内容
                                keywordSpan.innerHTML =
                                    '<b>' + value + '</b>' +
                                    '<input type="checkbox" name="seo_keywordCurrent[]" value="ADD:' + (index + 1) + '" class="option_current" checked>' +
                                    '<input type="hidden" name="seo_keywordOption[]" value="ADD:' + (index + 1) + '">' +
                                    '<input type="hidden" name="seo_keywordName[]" value="' + value + '">' +
                                    '<i></i>';

                                // 添加到容器
                                keywordContainer.appendChild(keywordSpan);

                                // 清空输入框
                                this.value = '';

                                // 隐藏占位符
                                var placeholder = document.querySelector('.option_selected[data-type="seo_keyword"] .placeholder');
                                if (placeholder) {
                                    placeholder.style.display = 'none';
                                }

                                // 更新隐藏字段
                                updateKeywords();
                            }
                        }
                    });
                }
            }

            // 提交表单前确保关键词被正确保存
            var editForm = document.getElementById('edit_form');
            if (editForm) {
                editForm.addEventListener('submit', function () {
                    updateKeywords();
                });
            }

            // 立即执行一次更新，确保关键词字段已正确设置
            updateKeywords();

            // 处理移动端详细描述开关状态
            initMobileDescriptionSwitch();

            // 初始化页面设置开关
            initPageSettingSwitches();

            // 处理Title_en和PageUrl的关联（仅在编辑模式下）
            if (@(Model.AId > 0 ? "true" : "false")) {
                var titleInput = document.querySelector('input[name="Title_en"]');
                var pageUrlTextarea = document.querySelector('textarea[name="PageUrl"]');
                var copyBtn = document.querySelector('.btn_copy');

                if (titleInput && pageUrlTextarea) {
                    // 当PageUrl变化时更新复制按钮
                    pageUrlTextarea.addEventListener('input', function () {
                        if (copyBtn) {
                            copyBtn.setAttribute('data-clipboard-text', '@(urlPrefix)/' + this.value.trim());
                        }
                    });
                }
            }

            // 初始化复制链接功能
            initCopyLinkFeature();
        };

        // 更新隐藏的SeoKeyword字段 - 移到全局作用域
        function updateKeywords() {
            var keywordValues = [];
            var keywordElements = document.querySelectorAll('.option_selected[data-type="seo_keyword"] .btn_attr_choice b');

            keywordElements.forEach(function (element) {
                keywordValues.push(element.textContent);
            });

            // 更新或创建隐藏字段
            var hiddenField = document.querySelector('input[name="SeoKeyword_en"]');
            if (!hiddenField) {
                hiddenField = document.createElement('input');
                hiddenField.type = 'hidden';
                hiddenField.name = 'SeoKeyword_en';
                document.getElementById('edit_form').appendChild(hiddenField);
            }

            hiddenField.value = keywordValues.join(',');
        }

        // 初始化移动端详细描述开关状态
        function initMobileDescriptionSwitch() {
            // 获取UsedMobile的值
            var usedMobile = "@(Model.ArticleContent != null && Model.ArticleContent.UsedMobile ? "true" : "false")";
            console.log("UsedMobile value:", usedMobile);
            // 获取开关元素
            var switchery = document.querySelector('.switchery');
            var mobileDescription = document.querySelector('.mobile_description');

            if (usedMobile === "true") {
                // 如果开启了移动端描述，确保开关状态正确
                if (switchery) {
                    switchery.classList.add('checked');
                    switchery.classList.add('active'); // 保留active类以保持兼容性
                }

                // 确保复选框被选中
                var checkbox = switchery ? switchery.querySelector('input[type="checkbox"]') : null;
                if (checkbox) {
                    checkbox.checked = true;
                }

                // 显示移动端描述编辑区域
                if (mobileDescription) {
                    mobileDescription.classList.remove('hide');
                    mobileDescription.style.display = 'block'; // 确保显示
                }
            } else {
                // 如果未开启移动端描述，确保开关状态正确
                if (switchery) {
                    switchery.classList.remove('active');
                    switchery.classList.remove('checked');
                }

                // 确保复选框未被选中
                var checkbox = switchery ? switchery.querySelector('input[type="checkbox"]') : null;
                if (checkbox) {
                    checkbox.checked = false;
                }

                // 隐藏移动端描述编辑区域
                if (mobileDescription) {
                    mobileDescription.classList.add('hide');
                    mobileDescription.style.display = 'none';
                }
            }

            // 移除可能已存在的点击事件监听器（为了避免多次绑定）
            if (switchery) {
                // 使用克隆节点替换原节点的方式移除所有事件监听器
                var newSwitchery = switchery.cloneNode(true);
                switchery.parentNode.replaceChild(newSwitchery, switchery);
                switchery = newSwitchery;

                // 重新获取checkbox
                var checkbox = switchery.querySelector('input[type="checkbox"]');

                // 添加新的点击事件监听器
                switchery.addEventListener('click', function (e) {
                    // 阻止事件冒泡，防止触发父元素的点击事件
                    e.stopPropagation();

                    // 切换开关的样式类
                    this.classList.toggle('active');
                    this.classList.toggle('checked');

                    // 切换复选框的选中状态
                    if (checkbox) {
                        checkbox.checked = !checkbox.checked;
                    }

                    // 切换移动端描述编辑区域的显示状态
                    if (mobileDescription) {
                        if (checkbox && checkbox.checked) {
                            mobileDescription.classList.remove('hide');
                            mobileDescription.style.display = 'block';
                        } else {
                            mobileDescription.classList.add('hide');
                            mobileDescription.style.display = 'none';
                        }
                    }
                });
            }
        }

        // 初始化复制链接功能
        function initCopyLinkFeature() {
            var copyBtn = document.querySelector('.btn_copy');
            if (copyBtn) {
                copyBtn.addEventListener('click', function (e) {
                    e.preventDefault();

                    // 获取要复制的文本
                    var textToCopy = this.getAttribute('data-clipboard-text');

                    // 创建临时输入框
                    var tempInput = document.createElement('input');
                    tempInput.value = textToCopy;
                    document.body.appendChild(tempInput);

                    // 选择并复制文本
                    tempInput.select();
                    document.execCommand('copy');

                    // 移除临时输入框
                    document.body.removeChild(tempInput);

                    // 显示复制成功提示
                    customize_pop.success('链接已复制到剪贴板');
                });
            }
        }

        // 初始化页面设置开关
        function initPageSettingSwitches() {
            // 处理Status开关
            initSingleSwitch('Status', "@(Model.Article?.Status == 1 ? "true" : "false")");

            // 处理NotIncludedHeader开关
            initSingleSwitch('NotIncludedHeader', "@(Model.Article?.NotIncludedHeader == true ? "true" : "false")");

            // 处理NotIncludedFooter开关
            initSingleSwitch('NotIncludedFooter', "@(Model.Article?.NotIncludedFooter == true ? "true" : "false")");
        }

        // 初始化单个开关
        function initSingleSwitch(fieldName, initialValue) {
            var checkbox = document.querySelector('input[name="' + fieldName + '"]');
            if (!checkbox) return;

            var switchery = checkbox.closest('.switchery');
            if (!switchery) return;

            // 设置初始状态
            var isActive = initialValue === "true";
            if (isActive) {
                switchery.classList.add('active');
                switchery.classList.add('checked');
                checkbox.checked = true;
            } else {
                switchery.classList.remove('active');
                switchery.classList.remove('checked');
                checkbox.checked = false;
            }

            // 移除可能已存在的点击事件监听器
            var newSwitchery = switchery.cloneNode(true);
            switchery.parentNode.replaceChild(newSwitchery, switchery);
            switchery = newSwitchery;

            // 重新获取checkbox
            checkbox = switchery.querySelector('input[name="' + fieldName + '"]');

            // 添加点击事件监听器
            switchery.addEventListener('click', function (e) {
                e.stopPropagation();

                // 切换开关的样式类
                this.classList.toggle('active');
                this.classList.toggle('checked');

                // 切换复选框的选中状态
                if (checkbox) {
                    checkbox.checked = !checkbox.checked;
                }
            });
        }
    </script>

    <!-- 添加提交处理脚本 -->
    <script>
        // 提交表单处理函数
        function submitForm() {
            // 更新关键词字段
            updateKeywords();

            // 获取表单数据
            const formData = new FormData(document.getElementById('edit_form'));
            const aId = document.getElementById('AId').value;

            // 获取PageUrl值，确保不包含前缀
            let pageUrl = formData.get('PageUrl') || '';
            const urlPrefixWithSlash = '@(urlPrefix)/';
            if (pageUrl.startsWith(urlPrefixWithSlash)) {
                pageUrl = pageUrl.substring(urlPrefixWithSlash.length);
            }

            // 如果PageUrl为空，则使用Title_en
            if (!pageUrl.trim()) {
                pageUrl = formData.get('Title_en') || '';
            }

            // 构建请求参数
            const articleDetail = {
                Article: {
                    AId: aId ? parseInt(aId) : 0,
                    Title_en: formData.get('Title_en'),
                    SeoTitle_en: formData.get('SeoTitle_en'),
                    SeoKeyword_en: formData.get('SeoKeyword_en'),
                    SeoDescription_en: formData.get('SeoDescription_en'),
                    PageUrl: pageUrl, // 使用处理后的URL
                    Status: formData.get('Status') === '1' ? 1 : 0,
                    NotIncludedHeader: formData.get('NotIncludedHeader') === 'true',
                    NotIncludedFooter: formData.get('NotIncludedFooter') === 'true'
                },
                Content: {
                    AId: aId ? parseInt(aId) : 0,
                    Content_en: tinymce.get('Content_en').getContent(),
                    UsedMobile: formData.get('UsedMobile') === '1',
                    MobileDescription: tinymce.get('MobileDescription') ? tinymce.get('MobileDescription').getContent() : ''
                }
            };

            // 显示加载提示
            // customize_pop.loading('正在提交数据，请稍候...');

            // 发送请求
            fetch('/manage/view/page/update/', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(articleDetail)
            })
                .then(response => {
                    if (!response.ok) {
                        throw new Error('网络请求失败');
                    }
                    return response.json();
                })
                .then(data => {
                    // 关闭加载提示
                    // customize_pop.loadingClose();

                    if (data.success) {
                        // 成功处理
                        customize_pop.success('保存成功');

                        // 如果是新增，则跳转到编辑页面
                        // if (!aId || aId === '0') {
                        //     setTimeout(() => {
                        //         window.location.href = `/Store/SinglePageEdit?AId=${data.data}`;
                        //     }, 1500);
                        // } else {
                        //     setTimeout(() => {
                        //         window.location.href = `/Store/SinglePageEdit?AId=${aId}`;
                        //     })
                        window.location.href = `/Store/SinglePages`;
                        //     }
                        // } else {
                        //     // 失败处理
                        //     customize_pop.error(data.message || '保存失败');
                    }
                })
                .catch(error => {
                    // 关闭加载提示
                    customize_pop.loadingClose();
                    customize_pop.error(error.message || '请求发生错误');
                    console.error('提交错误:', error);
                });
        }
    </script>
</div>