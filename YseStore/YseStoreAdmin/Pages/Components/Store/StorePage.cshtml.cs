using Entitys;
using YseStore.IService.Store;

namespace YseStoreAdmin.Pages.Components.Store
{
    public class StorePage : MComponent
    {
        private readonly IArticleService _articleService;
        private readonly ILogger<StorePage> _logger;
        private readonly IHttpContextAccessor _httpContextAccessor;

        // 分页信息
        public int PageNum { get; set; } = 1;
        public int PageSize { get; set; } = 10;
        public int TotalCount { get; set; } = 0;
        public int TotalPages { get; set; } = 0;

        // 搜索关键词
        public string Keyword { get; set; } = string.Empty;

        public List<article> Articles { get; set; } = new();

        public StorePage(
            IArticleService articleService, 
            ILogger<StorePage> logger,
            IHttpContextAccessor httpContextAccessor)
        {
            _articleService = articleService ?? throw new ArgumentNullException(nameof(articleService));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _httpContextAccessor = httpContextAccessor ?? throw new ArgumentNullException(nameof(httpContextAccessor));
        }

        // 组件异步挂载时调用
        public override async Task MountAsync()
        {
            try
            {
                // 从URL参数中获取keyword值
                var request = _httpContextAccessor.HttpContext.Request;
                if (request.Query.ContainsKey("Keyword"))
                {
                    Keyword = request.Query["Keyword"].ToString();
                }
                
                await base.MountAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "异步加载单页文章列表时出错");
            }
        }
    }
}
