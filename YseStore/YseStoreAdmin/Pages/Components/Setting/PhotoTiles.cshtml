@model YseStoreAdmin.Pages.Components.Setting.PhotoTiles

<div id="photo-tiles" class="photo-tiles">
    <div id="photo-tiles" class="photo-tiles-list">

        @if (Model.PhotosList != null && Model.PhotosList.Count > 0)
        {
            foreach (var (item, idx) in Model.PhotosList.WithIndex())
            {
                <div class="photo-tiles-item trans">
                    @* style="width:@(item.Width)px !important;" *@
                    <img class="prlightbox" data-slick-index="@idx" src="@item.PicPath?x-oss-process=image/format,webp/resize,m_lfit,h_220,w_220" data-width="@item.Width" data-height="@item.Height" />
                    <div class="photo-info">
                        <div class="title"><span>名称: </span>@item.OriName</div>
                        <div class="size"><span>尺寸：</span>@(item.Width)px * @(item.Height)px </div>
                        <div class="big"><span>大小：</span>@(item.Size.ToFileSize()) <a class="btn_copy" href="javascript:;" data-clipboard-action="copy" data-clipboard-text="@item.PicPath">复制链接</a></div>
                    </div>
                </div>
            }
        }


    </div>

    <div class="lightboximages">

        @if (Model.PhotosList != null && Model.PhotosList.Count > 0)
        {
            foreach (var item in Model.PhotosList)
            {
                <a href="@(item.PicPath)?x-oss-process=image/format,webp/resize,m_lfit,h_1000,w_1280" data-bigImage="@(item.PicPath)?x-oss-process=image/format,webp/resize,m_lfit,h_1000,w_1280" data-size="@(item.Width)x@(item.Height)"></a>
            }
        }
    </div>
    <div class="pswp" tabindex="-1" role="dialog" aria-hidden="true">
        <div class="pswp__bg"></div>
        <div class="pswp__scroll-wrap">
            <div class="pswp__container">
                <div class="pswp__item"></div>
                <div class="pswp__item"></div>
                <div class="pswp__item"></div>
            </div>
            <div class="pswp__ui pswp__ui--hidden">
                <div class="pswp__top-bar">
                    <div class="pswp__counter"></div>
                    <button class="pswp__button pswp__button--close" title="Close (Esc)"></button>
                    <button class="pswp__button pswp__button--share" title="Share"></button>
                    <button class="pswp__button pswp__button--fs" title="Toggle fullscreen"></button>
                    <button class="pswp__button pswp__button--zoom" title="Zoom in/out"></button>
                    <div class="pswp__preloader">
                        <div class="pswp__preloader__icn">
                            <div class="pswp__preloader__cut">
                                <div class="pswp__preloader__donut"></div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="pswp__share-modal pswp__share-modal--hidden pswp__single-tap">
                    <div class="pswp__share-tooltip"></div>
                </div>
                <button class="pswp__button pswp__button--arrow--left" title="Previous (arrow left)"></button>
                <button class="pswp__button pswp__button--arrow--right" title="Next (arrow right)"></button>
                <div class="pswp__caption"><div class="pswp__caption__center"></div></div>
            </div>
        </div>
    </div>
</div>