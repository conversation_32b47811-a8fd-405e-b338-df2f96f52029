@using YseStore.Model.VM.Country
@model YseStoreAdmin.Pages.Components.Setting.Tax
@{
}

<div>
    <div id="tax" class="r_con_wrap" style="height: 444px;">
        <div class="center_container_1200">
            <div class="blank20"></div>
            <div class="bread_nav">
                <a href="/Setting/Index" class="return_title o_return_title">@T["set.module_name"]</a>
                <h1>税费</h1>
            </div>
            <div class="clear"></div>
            <div class="tax_right fr">
                <div class="global_container">
                    <div class="top_title">计算方式</div>
                    <div class="sub_title">税费 = ( 商品价值<span class="tax_type_parameter @(Model.isUsed == "0" ? "hide" : "") "> + 运费 </span> ) * 税率 </div>
                    <div class="btn">
                        <div class="switchery @(Model.isUsed == "1" ? "checked" : "") tax_type">
                            <div class="switchery_toggler"></div>
                            <div class="switchery_inner">
                                <div class="switchery_state_on"></div>
                                <div class="switchery_state_off"></div>
                            </div>
                        </div>
                        <span>包含运费</span>
                    </div>
                    <div class="blank6"></div>
                    <div class="global_app_tips obvious"><em></em><span>当 (商品价值<span class="tax_type_parameter @(Model.isUsed == "0" ? "hide" : "") ">+运费</span>) 的金额大于起征点，则需收取税费</span></div>
                </div>
                <div class="global_tips no_icon">此页面的国家/地区来自运费管理设置的配送地区，若需要添加或删除，请到<a target="_blank" href="/Setting/Shopping">物流</a>进行设置。</div>
            </div>
            <div class="tax_left fl">
                <div class="inside_table pt0">
                    <ul class="plugins_app_menu">
                        <li><a href="javascript:;" data-id="2" data-url="/Setting/Tax?Continent=2" class="@(Model.CurrentContinent == 2 ? "current" : "")">欧洲</a></li>
                        <li><a href="javascript:;" data-id="4" data-url="/Setting/Tax?Continent=4" class="@(Model.CurrentContinent == 4 ? "current" : "")">北美洲</a></li>
                        <li><a href="javascript:;" data-id="6" data-url="/Setting/Tax?Continent=6" class="@(Model.CurrentContinent == 6 ? "current" : "")">大洋洲</a></li>
                        <li><a href="javascript:;" data-id="5" data-url="/Setting/Tax?Continent=5" class="@(Model.CurrentContinent == 5 ? "current" : "")">南美洲</a></li>
                        <li><a href="javascript:;" data-id="1" data-url="/Setting/Tax?Continent=1" class="@(Model.CurrentContinent == 1 ? "current" : "")">亚洲</a></li>
                        <li><a href="javascript:;" data-id="3" data-url="/Setting/Tax?Continent=3" class="@(Model.CurrentContinent == 3 ? "current" : "")">非洲</a></li>
                        <li><a href="javascript:;" data-id="7" data-url="/Setting/Tax?Continent=7" class="@(Model.CurrentContinent == 7 ? "current" : "")">南极洲</a></li>
                    </ul>
                    <table border="0" cellpadding="5" cellspacing="0" class="r_con_table country_list radius">
                        <thead>
                            <tr>
                                <td width="40" class="pos">
                                    <ul class="table_menu_button global_menu_button">
                                        <li><div class="btn_checkbox"><em class="button"></em><input type="checkbox" name="select_all" value=""></div></li>
                                        <li class="open no_select">已选择<span></span>个</li>
                                        <li><a class="edit btn_tax_bat_edit" href="javascript:;">批量修改</a></li>
                                    </ul>
                                </td>
                                <td width="60%" nowrap="nowrap">国家及地区</td>
                                <td width="20%" nowrap="nowrap">税率</td>
                                <td width="20%" nowrap="nowrap">起征点</td>
                                <td nowrap="nowrap">操作</td>
                            </tr>
                        </thead>
                        <tbody>


                            @foreach (var item in Model.Countries)
                            {
                                <tr data-cid="@item.CId" data-id="@item.Continent" class="@(item.Continent != Model.CurrentContinent ? "hide" : "")" style="display:@(item.Continent != Model.CurrentContinent ? "none" : "table-row") ;">
                                    <td><div class="btn_checkbox"><em class="button"></em><input type="checkbox" name="select" value="@item.CId"></div></td>
                                    <td nowrap="nowrap" class="country_name">
                                        <div class="icon_flag_big flag_big_@(item.Acronym.ToLower())"> </div>
                                        <div class="name">
                                            @item.Country
                                            @{
                                                string countryCN = "";
                                                var CountryData = item.CountryData.JsonToObj<Dictionary<string, string>>();
                                                if (CountryData.ContainsKey("zh-cn"))
                                                {
                                                    countryCN = CountryData["zh-cn"];
                                                }
                                            }
                                            <br>@countryCN
                                            <div class="tags_box">
                                                @if (item.IsDefault==true)
                                                {
                                                    <span class="c_tags">默认选项</span>
                                                }
                                                @if (item.IsHot == true)
                                                {
                                                    <span class="c_tags">靠前显示</span>
                                                }
                                            </div>
                                        </div>
                                    </td>
                                    <td nowrap="nowrap">@Math.Round(item.Tax * 100, 2)%</td>
                                    <td nowrap="nowrap">@Model.currencySymbol @item.TaxThreshold</td>
                                    <td nowrap="nowrap" class="operation">
                                        <a class="" href="/Setting/TaxEdit?id=@(item.CId)">修改</a>
                                    </td>
                                </tr>
                            }

                        </tbody>
                    </table>
                </div>
            </div>
            <div class="clear"></div>
        </div>
    </div>
    <div id="fixed_right">
        <div class="global_container box_tax_bat_edit" data-width="330">
            <div class="top_title"><span>批量修改</span> <a href="javascript:;" class="close"></a></div>
            <form class="global_form" id="form_tax_bat">
                <div class="rows input_tax">
                    <label>税率</label>
                    <div class="input">
                        <span class="unit_input"><input type="text" rel="amount" name="Tax" value="0" class="box_input" size="5" maxlength="8"><b class="last">%</b></span>
                    </div>
                </div>
                <div class="rows input_tax_threshold">
                    <label>起征点</label>
                    <div class="input">
                        <span class="unit_input"><b>@Model.currencySymbol<div class="arrow"><em></em><i></i></div></b><input type="text" rel="amount" name="TaxThreshold" value="0.00" class="box_input" size="5" maxlength="8"></span>
                    </div>
                </div>
                <div class="rows clean box_button box_submit">
                    <div class="input">
                        <input type="submit" class="btn_global btn_submit" value="保存">
                        <input type="button" class="btn_global btn_cancel" value="取消">
                    </div>
                </div>
                <input type="hidden" name="id_list" id="id_list" value="">
                <input type="hidden" name="do_action" value="/manage/set/taxes/update-bat">
            </form>
        </div>
    </div>
</div>