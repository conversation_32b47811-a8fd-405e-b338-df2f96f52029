using Entitys;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using SqlSugar.Extensions;
using YseStore.IService;
using YseStore.Model.Event;
using YseStore.Model.VM;

namespace YseStoreAdmin.Pages.Components.Setting
{
    public class ChoicePhoto : CompBase
    {
 
        public int Maxpic{ get; set; }

        
        public string Type { get; set; }

        
        public string PicDetail { get; set; }
        public int TotalCount { get; set; }
        public int PageSize { get; set; }
        public int PageNum { get; set; }

        public PagedList<PhotoResponse>? PhotosList { get; set; }
        public List<photo_tags>? Photo_tagsList { get; set; }

        public string PhotoTagsJson { get; set; }
        public ISettingPhotosService _settingPhotosService { get; }
        public ChoicePhoto(ISettingPhotosService settingPhotosService)
        {
        
            _settingPhotosService = settingPhotosService;
        }

       
        //public async Task OnGetAsync()
        //{
        //    PhotosList = await GetLogs(Keyword, TagsId);
        //    Photo_tagsList = await GetPhoto_Tags();
        //}


        public void HandlePageEvent(PageEvent evt)
        {
            TotalCount = evt.Total;
            PageSize = evt.PageSize;
            PageNum = evt.PageNum;

        }
        public async Task ChangePage1()
        {
            await Task.CompletedTask;
        }
        [SkipOutput]
        public async Task TurnPage(int page)
        {
            DispatchGlobal<UserTurnPageEvent>(new UserTurnPageEvent("manlog", page));

        }
        public override async Task MountAsync()
        {
            PicDetail = HttpContext.Request.Query["id"].ToString();
            Type = HttpContext.Request.Query["type"].ToString();
            Maxpic = HttpContext.Request.Query["maxpic"].ObjToInt();
            var keywords = HttpContext.Request.Query["keyword"].ToString();
            var tagsIds = HttpContext.Request.Query["TagsId"].ToString();

            PhotosList = await GetLogs(keywords, tagsIds);
            Photo_tagsList = await GetPhoto_Tags();
            var result = await _settingPhotosService.GetFormattedPhotoTags();
            PhotoTagsJson = System.Text.Json.JsonSerializer.Serialize(result);

        }
        public async Task<List<photo_tags>> GetPhoto_Tags()
        {
            var result = await _settingPhotosService.GetPhoto_Tags();
            return result;
        }



        public async Task<PagedList<PhotoResponse>> GetLogs(string Keyword, string TagsId, int page = 1)
        {
            var result = await _settingPhotosService.QueryAsync(Keyword, TagsId,"", page, 20);

            return result;
        }
    }
}
