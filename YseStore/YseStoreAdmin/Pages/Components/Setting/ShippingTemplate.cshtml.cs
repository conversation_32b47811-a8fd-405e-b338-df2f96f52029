using Entitys;
using YseStore.IService.Set;

namespace YseStoreAdmin.Pages.Components.Setting
{
    public class ShippingTemplate : MComponent
    {
        private readonly IShippingTemplateService _shippingTemplateService;
        private readonly ISettingShippingService _shippingService;

        public List<shipping_template> CustomTemplates { get; set; } = new List<shipping_template>();
        public shipping_template DefaultTemplate { get; set; }
        public Dictionary<int, int> TemplateProductCounts { get; set; } = new Dictionary<int, int>();

        public Dictionary<int, List<string>> TemplateShippingMethods { get; set; } =
            new Dictionary<int, List<string>>();

        public ShippingTemplate(
            IShippingTemplateService shippingTemplateService,
            ISettingShippingService shippingService)
        {
            _shippingTemplateService = shippingTemplateService;
            _shippingService = shippingService;
        }

        public override async Task MountAsync()
        {
            await LoadShippingTemplateData();
        }

        private async Task LoadShippingTemplateData()
        {
            try
            {
                // 获取默认运费模板
                DefaultTemplate = await _shippingTemplateService.GetDefaultTemplateAsync();

                // 获取自定义运费模板列表
                CustomTemplates = await _shippingTemplateService.GetCustomTemplatesAsync();

                var defaultCount = await _shippingTemplateService.GetTemplateProductCountAsync(DefaultTemplate.TId);
                TemplateProductCounts[DefaultTemplate.TId] = defaultCount;

                // 获取默认模板关联的物流方式
                var defaultShippings =
                    await _shippingService.GetShippingsByTemplateIdAsync(DefaultTemplate.TId.ToString());
                if (defaultShippings.Any())
                {
                    TemplateShippingMethods[DefaultTemplate.TId] = defaultShippings.Select(s => s.Express).ToList();
                }
                else
                {
                    TemplateShippingMethods[DefaultTemplate.TId] = new List<string>();
                }


                // 获取每个自定义模板关联的产品数量和物流方式
                foreach (var template in CustomTemplates)
                {
                    var count = await _shippingTemplateService.GetTemplateProductCountAsync(template.TId);
                    TemplateProductCounts[template.TId] = count;

                    // 获取每个模板关联的物流方式
                    var shippings = await _shippingService.GetShippingsByTemplateIdAsync(template.TId.ToString());
                    if (shippings != null && shippings.Any())
                    {
                        TemplateShippingMethods[template.TId] = shippings.Select(s => s.Express).ToList();
                    }
                    else
                    {
                        TemplateShippingMethods[template.TId] = new List<string>();
                    }
                }
            }
            catch (Exception ex)
            {
                // 记录异常信息，便于诊断
                Console.WriteLine($"加载运费模板数据时出错: {ex.Message}");
                throw; // 重新抛出异常以便上层处理
            }
        }
    }
}