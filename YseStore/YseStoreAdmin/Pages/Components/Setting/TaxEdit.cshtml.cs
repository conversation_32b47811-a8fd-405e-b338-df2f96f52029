using Entitys;
using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using YseStore.IService;
using YseStore.IService.Order;

namespace YseStoreAdmin.Pages.Components.Setting
{
    public class TaxEdit : MComponent
    {
        public ICountryService _countryService { get; }

        private ICurrencyService _currencyService { get; set; }

        public TaxEdit(ICountryService countryService, ICurrencyService currencyService)
        {
            _countryService = countryService;
            _currencyService = currencyService;
        }


        /// <summary>
        /// 
        /// </summary>
        [Parameter]
        public int id { get; set; }

        /// <summary>
        /// 币种
        /// </summary>
        public string currencySymbol { get; set; } = AppSettingsConstVars.CurrentDefaultSymbol;

        /// <summary>
        /// 
        /// </summary>
        public country countries { get; set; } = new country();

        /// <summary>
        /// 
        /// </summary>
        public List<country_states> states { get; set; } = new List<country_states>();

        /// <summary>
        /// 
        /// </summary>
        /// <returns></returns>
        public override async Task MountAsync()
        {
            await base.MountAsync();

            //后台币种
            var manageCurrency = await _currencyService.GetManageDefaultCurrency();
            if (manageCurrency != null)
            {
                currencySymbol = manageCurrency.Symbol;
            }

            countries = await _countryService.GetCountryAsync(id);

            var stateList = await _countryService.GetCountryStatesAsync();

            states = stateList.Where(it => it.CId == id).ToList();



        }


    }
}
