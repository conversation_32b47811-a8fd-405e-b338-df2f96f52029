using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using YseStore.IService;
using YseStore.Model.Event;
using YseStore.Model.VM;

namespace YseStoreAdmin.Pages.Components.Setting
{
    public class SettingFilesTable : MComponent
    {
        public ISettingFilesService _settingPhotosService { get; }
        public string keywords { get; set; }
        public string tagsIds { get; set; }

        public PagedList<FilesResponse>? PhotosList { get; set; }

        public SettingFilesTable(ISettingFilesService settingPhotosService)
        {

            Subscribe<UserTurnPageEvent>(HandleUserTurnPage);
            _settingPhotosService = settingPhotosService;
        }
        public async Task HandleUserTurnPage(UserTurnPageEvent evt)
        {
            if (evt.Name == "manlog")
            {
                await BindData(evt.PageNum);
            }
        }

        public override async Task MountAsync()
        {
            keywords = HttpContext.Request.Query["keyword"].ToString();
            tagsIds = HttpContext.Request.Query["tagsId"].ToString();
            await BindData();
        }
        [SkipOutput]
        public async Task BindData(int page = 1)
        {

            PhotosList = await GetLogs(page);
            DispatchGlobal<PageEvent>(new PageEvent(PhotosList.TotalCount, PhotosList.PageSize, PhotosList.PageIndex + 1, "manlog"), null, true);

        }
        //public async Task OnGetAsync()
        //{
        //    PhotosList = await GetLogs(Keyword, TagsId);
        //    DispatchGlobal<PageEvent>(new PageEvent(PhotosList.TotalCount, PhotosList.PageSize, PhotosList.PageIndex + 1, "manlog"), null, true);
        //}
        public async Task<PagedList<FilesResponse>> GetLogs(int page = 1)
        {
          
            var result = await _settingPhotosService.QueryAsync(keywords, tagsIds, page, 50);

            return result;
        }
    }
}
