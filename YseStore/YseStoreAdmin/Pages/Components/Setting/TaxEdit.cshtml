@model YseStoreAdmin.Pages.Components.Setting.TaxEdit
@{
}
<div>
    <div id="tax" class="r_con_wrap" style="height: 372px;">
        <div class="center_container">
            <div class="return_title">
                <a href="/Setting/Tax?Continent=@Model.countries.Continent">
                    <span class="return">税费</span>
                    <span class="s_return">/ 修改</span>
                </a>
            </div>
            <form id="edit_form" class="global_form states_box" action="/manage/set/taxes/edit?id=@Model.countries.CId" method="post">

                <div class="country_name">
                    <div class="icon_flag_big <EMAIL>()"></div>

                    @{
                        string countryCN = "";
                        var CountryData = Model.countries.CountryData.JsonToObj<Dictionary<string, string>>();
                        if (CountryData.ContainsKey("zh-cn"))
                        {
                            countryCN = CountryData["zh-cn"];
                        }
                    }
                    @($"{Model.countries.Country} / {countryCN}")
                </div>
                <div class="global_container">
                    <div class="clean box_tax_set">
                        <div class="rows float_rows fl input_tax">
                            <label>税率</label>
                            <div class="input">
                                <span class="unit_input">
                                    <input type="text" rel="amount" name="Tax" value="@Math.Round(Model.countries.Tax * 100, 2)" class="box_input" size="5" maxlength="8"><b class="last">%</b>
                                </span>
                            </div>
                        </div>
                        <div class="rows float_rows fl input_tax_threshold">
                            <label>起征点</label>
                            <div class="input">
                                <span class="unit_input"><b>@Model.currencySymbol<div class="arrow"><em></em><i></i></div></b><input type="text" rel="amount" name="TaxThreshold" value="@Model.countries.TaxThreshold" class="box_input" size="5" maxlength="8"></span>
                            </div>
                        </div>
                    </div>
                </div>
                @if (Model.states.Any())
                {
                    <div class="states_title">
                        对特定省份的税率进行单独设置：
                    </div>
                    <table border="0" cellpadding="5" cellspacing="0" class="r_con_table states_edit radius">
                        <thead>
                            <tr>
                                <td width="100%" nowrap="nowrap">省份</td>
                                <td nowrap="nowrap">税率</td>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach (var item in Model.states)
                            {
                                <tr data-cid="@Model.countries.CId" data-id="@item.SId">
                                    <td nowrap="nowrap">[@item.AcronymCode] @item.States</td>
                                    <td nowrap="nowrap">
                                        <span class="unit_input">
                                            <input type="text" rel="amount" name="StatesTax[@item.SId]" value="@Math.Round(item.Tax * 100, 2)" class="box_input" size="5" maxlength="8"><b class="last">%</b>
                                        </span>
                                    </td>
                                </tr>
                            }

                        </tbody>
                    </table>
                }
                <input type="hidden" name="CId" value="@Model.countries.CId">
                <input type="hidden" name="do_action" value="/manage/set/taxes/operation">
            </form>
        </div>
    </div>

    <div class="rows clean fixed_btn_submit" style="width: 1953px; left: 180px;">
        <div class="center_container_1200">
            <div class="input input_button">
                <input type="button" class="btn_global btn_submit" value="保存">
                <a href="/Setting/Tax?Continent=@Model.countries.Continent" title="返回"><input type="button" class="btn_global btn_cancel" value="返回"></a>
            </div>
        </div>
    </div>
</div>