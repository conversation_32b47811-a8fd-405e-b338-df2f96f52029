 
@model YseStoreAdmin.Pages.Components.Setting.ChoicePhoto 
@{
}
<div>
    <div id="photo" class="r_con_wrap photo_choice_box auto_box_choice photo_choice_">
        <div class="inside_table">
            <div class="list_menu list_menu_photo">
                <div class="search_form">
                    <form id="w0" action="/manage/set/photo/choice" method="get">
                        <div class="k_input">
                            <input type="text" class="form_input" name="Keyword" value="" size="15" autocomplete="off">
                            <input type="button" value="" class="more" />
                        </div>
                        <input type="submit" class="search_btn" value="搜索">
                        <div class="ext drop_down">
                            <div class="rows item clean">
                                <label>标签</label>
                                <div class="input">
                                    <div class="box_select">
                                        <select name="TagsId">
                                            <option value="">请选择</option>

                                            @if (Model.Photo_tagsList != null)
                                            {
                                                foreach (var item in Model.Photo_tagsList)
                                                {
                                                    <option value="@item.Id">@item.Name</option>
                                                }
                                            }

                                            
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="clear"></div>
                        <input type="hidden" name="id" value="@Model.PicDetail"> <input type="hidden" name="type" value="">
                        <input type="hidden" name="maxpic" value="@Model.Maxpic"> <input type="hidden" name="obj" value=""> <input type="hidden" name="save" value=""> <input type="hidden" name="iframe" value="1"> <input type="hidden" name="file2BigName_hidden_text" value="">
                    </form>
                </div>
                <div class="upload">
                    <form id="w1" class="up_input" name="upload_form" action="//jquery-file-upload.appspot.com/"
                          method="post" enctype="multipart/form-data">
                        <input type="hidden" name="_csrf-manage"
                               value="mVJoOT1OAbIiF-z_DDCX5w2doGcpR8IV_HC9GYS29tfeID9mdC1i62Ehpr5fSN_SIP_RJR1-70y5L99w4fOYiA==">
                        <noscript>
                            <input type="hidden" name="redirect" value="https://blueimp.github.io/jQuery-File-Upload/">
                        </noscript>
                        <div class="fileupload-buttonbar">
                            <span class="btn_file btn-success fileinput-button">
                                <i class="glyphicon glyphicon-plus"></i>
                                <span>本地上传</span>
                                <input type="file" name="Filedata" value="" multiple="">
                            </span>
                            <div class="fileupload-progress fade">
                                <div class="progress-extended">&nbsp;</div>
                            </div>
                            <div class="clear"></div>
                            <div class="photo_multi_img template-box files hide"></div>
                        </div>
                        <script id="template-upload" type="text/x-tmpl">
                            {% for (var i=0, file; file=o.files[i]; i++) { %}
                                <div class="template-upload fade">
                                    <div class="clear"></div>
                                    <div class="items">
                                        <p class="name">{%=file.name%}</p>
                                        <strong class="error text-danger"></strong>
                                    </div>
                                    <div class="items">
                                        <p class="size">Processing...</p>
                                        <div class="progress progress-striped active" role="progressbar" aria-valuemin="0" aria-valuemax="100" aria-valuenow="0"><div class="progress-bar progress-bar-success" style="width:0%;"></div></div>
                                    </div>
                                    <div class="items">
                                        {% if (!i) { %}
                                            <button class="btn_file btn-warning cancel">
                                                <i class="glyphicon glyphicon-ban-circle"></i>
                                                <span>取消</span>
                                            </button>
                                        {% } %}
                                    </div>
                                    <div class="clear"></div>
                                </div>
                            {% } %}
                        </script>
                        <script id="template-download" type="text/x-tmpl">
                            {% for (var i=0, file; file=o.files[i]; i++) { %}
                                {% if (file.thumbnailUrl) { %}
                                    <div class="pic template-download fade">
                                        <div>
                                            <a href="javascript:;" title="{%=file.name%}" download="{%=file.name%}" data-gallery><img src="{%=file.thumbnailUrl%}" /><em></em></a>
                                            <a href="{%=file.url%}" class="zoom" target="_blank"></a>
                                            {% if (file.deleteUrl) { %}
                                                <button class="btn-danger delete" data-type="{%=file.deleteType%}" data-url="{%=file.deleteUrl%}"{% if (file.deleteWithCredentials) { %} data-xhr-fields='{"withCredentials":true}'{% } %}>删除</button>
                                                <input type="checkbox" name="delete" value="1" class="toggle" style="display:none;">
                                            {% } %}
                                            <input type="hidden" name="PicPath[]" value="{%=file.url%}" disabled />
                                        </div>
                                        <input type="text" maxlength="30" class="box_input" value="{%=file.name%}" name="Name[]" placeholder="'+lang_obj.global.picture_name+'" disabled notnull />
                                    </div>
                                {% } else { %}
                                    <div class="template-download fade">
                                        <div class="clear"></div>
                                        <div class="items">
                                            <p class="name">
                                                {% if (file.url) { %}
                                                    <a href="{%=file.url%}" title="{%=file.name%}" download="{%=file.name%}" {%=file.thumbnailUrl?'data-gallery':''%}>{%=file.name%}</a>
                                                {% } else { %}
                                                    <span>{%=file.name%}</span>
                                                {% } %}
                                            </p>
                                            {% if (file.error) { %}
                                                <div><span class="label label-danger">Error</span> {%=file.error%}</div>
                                            {% } %}
                                        </div>
                                        <div class="items">
                                            <span class="size">{%=o.formatFileSize(file.size)%}</span>
                                        </div>
                                        <div class="items">
                                            {% if (file.deleteUrl) { %}
                                                <button class="btn_file btn-danger delete" data-type="{%=file.deleteType%}" data-url="{%=file.deleteUrl%}"{% if (file.deleteWithCredentials) { %} data-xhr-fields='{"withCredentials":true}'{% } %}>
                                                    <i class="glyphicon glyphicon-trash"></i>
                                                    <span>删除</span>
                                                </button>
                                                <input type="checkbox" name="delete" value="1" class="toggle" style="display:none;">
                                            {% } else { %}
                                                <button class="btn_file btn-warning cancel">
                                                    <i class="glyphicon glyphicon-ban-circle"></i>
                                                    <span>取消</span>
                                                </button>
                                            {% } %}
                                        </div>
                                        <div class="clear"></div>
                                    </div>
                                {% } %}
                            {% } %}
                        </script>
                    </form>
                    <div class="upload_file_tips">仅支持jpg、jpeg、png、gif、ico、webp，建议2MB，最大不超过10MB</div>
                </div>
            </div>
        </div>
        <div class="clear"></div>
        <div class="wrap_content photo_list auto_load_photo" data-page="1" data-total-pages="48">
            <form id="photo_list_form"
                  action="/manage/set/photo/choice?id=@Model.PicDetail&type=@Model.Type&maxpic=@Model.Maxpic&iframe=1&r=0.7668721779766718"
                  method="post">
                <input type="hidden" name="_csrf-manage"
                       value="mVJoOT1OAbIiF-z_DDCX5w2doGcpR8IV_HC9GYS29tfeID9mdC1i62Ehpr5fSN_SIP_RJR1-70y5L99w4fOYiA==">
                <choice-photo-table />
                <div class="photo_choose_box">
                    <div class="bg_no_table_data">
                        <div class="content">
                            <p>当前暂时没有数据</p>
                        </div><span></span>
                    </div>
                </div>
                <input type="hidden" name="id" value="@Model.PicDetail"> <input type="hidden" name="type" value="@Model.Type"> <input type="hidden" name="maxpic" value="@Model.Maxpic"> <input type="hidden" name="obj" value=""> <input type="hidden"
                                                                                                                                                                                                                 name="save" value=""> <input type="hidden" name="sort" value="|">
            </form>
        </div>
        <div class="turn_page_box">
            <mx-pager name="manlog" total="@Model.TotalCount" page-size="@Model.PageSize" page-num="@Model.PageNum" />
         @*    <div id="turn_page" data-current="0" data-count="48">
                <div class="total_page">共 1525 条</div>
                <ul class="pagination">
                    <li class="first disabled"><span>首页</span></li>
                    <li class="prev disabled"><span></span></li>
                    <li class="active">
                        <a href="/manage/set/photo/choice?id=@Model.PicDetail&amp;type=@Model.Type&amp;maxpic=@Model.Maxpic&amp;iframe=1&amp;page=1&amp;per-page=32"
                           data-page="0">1</a>
                    </li>
                    <li>
                        <a href="/manage/set/photo/choice?id=@Model.PicDetail&amp;type=@Model.Type&amp;maxpic=@Model.Maxpic&amp;iframe=1&amp;page=2&amp;per-page=32"
                           data-page="1">2</a>
                    </li>
                    <li>
                        <a href="/manage/set/photo/choice?id=@Model.PicDetail&amp;type=@Model.Type&amp;maxpic=@Model.Maxpic&amp;iframe=1&amp;page=3&amp;per-page=32"
                           data-page="2">3</a>
                    </li>
                    <li>
                        <a href="/manage/set/photo/choice?id=@Model.PicDetail&amp;type=@Model.Type&amp;maxpic=@Model.Maxpic&amp;iframe=1&amp;page=4&amp;per-page=32"
                           data-page="3">4</a>
                    </li>
                    <li>
                        <a href="/manage/set/photo/choice?id=@Model.PicDetail&amp;type=@Model.Type&amp;maxpic=@Model.Maxpic&amp;iframe=1&amp;page=5&amp;per-page=32"
                           data-page="4">5</a>
                    </li>
                    <li class="next">
                        <a href="/manage/set/photo/choice?id=@Model.PicDetail&amp;type=@Model.Type&amp;maxpic=@Model.Maxpic&amp;iframe=1&amp;page=2&amp;per-page=32"
                           data-page="1"></a>
                    </li>
                    <li class="last">
                        <a href="/manage/set/photo/choice?id=@Model.PicDetail&amp;type=@Model.Type&amp;maxpic=@Model.Maxpic&amp;iframe=1&amp;page=48&amp;per-page=32"
                           data-page="47">尾页</a>
                    </li>
                </ul>
            </div> *@
        </div>
    </div>

</div>