using Entitys;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Newtonsoft.Json.Linq;
using Serilog.Parsing;
using YseStore.IService;
using YseStore.Model.VM;
using YseStore.Service;

namespace YseStoreAdmin.Pages.Components.Setting
{
    public class SettingAgreement : CompBase
    {    
        public ISettingService _settingService { get; }
        public IConfigService _configService { get; }
        public IList<policies> Policies { get; set; }
        public bool IsBottom { get; set; }
        public SettingAgreement(ISettingService settingService, IConfigService configService)
        {
            
            _settingService = settingService;
            _configService = configService;
        }

        public override async Task MountAsync()
        {
            Policies = await _settingService.GetPoliciesAry();
            IsBottom = false;
            var  Config= await _configService.GetConfigAry();
            if (Config!=null)
            {
                JObject obj = JObject.Parse(Config.Value);
                string position = (string)obj["Cookies"]["position"];
                if (position== "bottom")
                {
                    IsBottom = true;
                }
            }
            
        }

        public async Task Save()
        {
            
        }
    }
}
