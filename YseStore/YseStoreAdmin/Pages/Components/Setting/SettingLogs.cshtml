@model YseStoreAdmin.Pages.Components.Setting.SettingLogs 
@{
}

<div>
	<div id="config" class="r_con_wrap" style="height: 87px;">
		<div class="logs index">
			<div class="bread_nav">
				<a href="/Setting/Index" class="return_title o_return_title">设置</a>
				<h1>
					系统日志  
					 
				</h1>
			</div>
			<div class="clear"></div>
			<div class="global_container">
				<div class="list_menu">
					<div class="search_box fl search_filter">
						<form method="get" action="?">
							<div class="k_input">
								<input type="text" class="form_input" name="Keyword" size="15" autocomplete="off" placeholder="请输入关键词">							<input type="submit" class="search_btn" value="&#xe600;">
							</div>
							<input type="button" class="filter_btn" value="筛选">
							<div class="clear"></div>
							<input type="hidden" name="AccTime" value="0">						<input type="hidden" name="UserName" value="">						<input type="hidden" name="Module" value="">
						</form>
					</div>
					<div class="clear"></div>
				</div>
				<div class="box_table"> 
					<setting-logs-table />
				</div>
				<div class="scroll_sticky"><div class="scroll_sticky_content"><div style="width: 1270px; height: 1px;"></div></div></div>

				<mx-pager name="manlog" total="@Model.TotalCount" page-size="@Model.PageSize" page-num="@Model.PageNum"  /> 

			</div>
		</div>
		<div id="fixed_right">
			<div class="global_container fixed_search_filter" data-width="396" style="display: none; height: 786px;">
				<div class="top_title">筛选 <a href="javascript:;" class="close"></a></div>
				<div class="global_form">
					<div class="box_filter">
						<div class="filter_list current">
							<div class="filter_title">操作时间</div>
							<div class="filter_option">
								<div class="filter_option_input">
									<input name="AccTime" value="" type="text" class="box_input full_input input_time" size="55" readonly="" notnull="">
								</div>
								<div class="filter_clean"><button>清除</button></div>
							</div>
						</div>
						<div class="filter_list current">
							<div class="filter_title">用户名</div>
							<div class="filter_option">
								<div class="filter_option_input">
									<dl class="box_drop_double edit_box" data-checkbox="0" data-showadd="0"><dt class=""><input type="text" class="box_input" name="" placeholder="请选择或填写" value="" autocomplete="off"><input type="hidden" name="UserName" value="" class="hidden_value"><input type="hidden" name="UserNameType" value="" class="hidden_type"></dt><dd class="drop_down" style="display: none; top: 34px;"><div class="drop_menu" data-type="Select"><a href="javascript:;" class="btn_back" data-value="" data-type="" data-table="" data-top="0" data-all="0" style="display:none;">返回</a><div class="drop_skin" style="display: none;"></div><div class="drop_list" data="[{&quot;Name&quot;:&quot;u&quot;,&quot;Value&quot;:-1,&quot;Type&quot;:&quot;manage&quot;},{&quot;Name&quot;:&quot;yisaier&quot;,&quot;Value&quot;:85902,&quot;Type&quot;:&quot;manage&quot;},{&quot;Name&quot;:&quot;yseadmin&quot;,&quot;Value&quot;:85903,&quot;Type&quot;:&quot;manage&quot;}]" data-more="none"><div class="item" data-name="u" data-value="-1" data-type="manage" data-table="undefined" data-top="0" data-all="1" data-check-all="0" data-custom="undefined"><span class="input_radio_box"><span class="input_radio"><input type="radio" name="_DoubleOption[]" value="0"></span></span><span class="item_name">u</span></div><div class="item" data-name="yisaier" data-value="85902" data-type="manage" data-table="undefined" data-top="0" data-all="1" data-check-all="0" data-custom="undefined"><span class="input_radio_box"><span class="input_radio"><input type="radio" name="_DoubleOption[]" value="1"></span></span><span class="item_name">yisaier</span></div><div class="item" data-name="yseadmin" data-value="85903" data-type="manage" data-table="undefined" data-top="0" data-all="1" data-check-all="0" data-custom="undefined"><span class="input_radio_box"><span class="input_radio"><input type="radio" name="_DoubleOption[]" value="2"></span></span><span class="item_name">yseadmin</span></div></div><a href="javascript:;" class="btn_load_more" data-value="" data-type="" data-table="" data-top="0" data-all="0" data-check-all="" data-start="1" style="display:none;">加载更多</a></div></dd></dl>
								</div>
								<div class="filter_clean"><button>清除</button></div>
							</div>
						</div>
						<div class="filter_list current">
							<div class="filter_title">功能模块</div>
							<div class="filter_option">
								<div class="filter_option_input">
									<dl class="box_drop_double" data-checkbox="1" data-showadd="0"><dt class="box_checkbox_list"><div class="select_placeholder" style="display: none;">请选择</div><div class="select_list"></div><input type="text" class="box_input check_input" name="" value="" placeholder="" size="30" maxlength="255" autocomplete="off" style="display: inline-block; width: 174.303px;"><input type="hidden" name="Module" value="" class="hidden_value"><input type="hidden" name="ModuleType" value="" class="hisdden_type"></dt><dd class="drop_down" style="display: none; top: 50px;"><div class="drop_menu" data-type=""><a href="javascript:;" class="btn_back" data-value="" data-type="" data-table="" data-top="0" data-all="0" style="display:none;">返回</a><div class="drop_skin" style="display: none;"></div><div class="drop_list" data="[{&quot;Name&quot;:&quot;我的帐号&quot;,&quot;Value&quot;:&quot;account&quot;,&quot;Type&quot;:&quot;module&quot;},{&quot;Name&quot;:&quot;订单&quot;,&quot;Value&quot;:&quot;orders&quot;,&quot;Type&quot;:&quot;module&quot;},{&quot;Name&quot;:&quot;产品&quot;,&quot;Value&quot;:&quot;products&quot;,&quot;Type&quot;:&quot;module&quot;},{&quot;Name&quot;:&quot;客户&quot;,&quot;Value&quot;:&quot;user&quot;,&quot;Type&quot;:&quot;module&quot;},{&quot;Name&quot;:&quot;营销&quot;,&quot;Value&quot;:&quot;sales&quot;,&quot;Type&quot;:&quot;module&quot;},{&quot;Name&quot;:&quot;分析&quot;,&quot;Value&quot;:&quot;mta&quot;,&quot;Type&quot;:&quot;module&quot;},{&quot;Name&quot;:&quot;店铺&quot;,&quot;Value&quot;:&quot;view&quot;,&quot;Type&quot;:&quot;module&quot;},{&quot;Name&quot;:&quot;应用&quot;,&quot;Value&quot;:&quot;plugins&quot;,&quot;Type&quot;:&quot;module&quot;},{&quot;Name&quot;:&quot;设置&quot;,&quot;Value&quot;:&quot;set&quot;,&quot;Type&quot;:&quot;module&quot;},{&quot;Name&quot;:&quot;API&quot;,&quot;Value&quot;:&quot;api&quot;,&quot;Type&quot;:&quot;module&quot;}]" data-more="none"><div class="item" data-name="我的帐号" data-value="account" data-type="module" data-table="undefined" data-top="0" data-all="1" data-check-all="0" data-custom="undefined"><span class="input_checkbox_box"><span class="input_checkbox"><input type="checkbox" name="_DoubleOption[]" value="0"></span></span><span class="item_name">我的帐号</span></div><div class="item" data-name="订单" data-value="orders" data-type="module" data-table="undefined" data-top="0" data-all="1" data-check-all="0" data-custom="undefined"><span class="input_checkbox_box"><span class="input_checkbox"><input type="checkbox" name="_DoubleOption[]" value="1"></span></span><span class="item_name">订单</span></div><div class="item" data-name="产品" data-value="products" data-type="module" data-table="undefined" data-top="0" data-all="1" data-check-all="0" data-custom="undefined"><span class="input_checkbox_box"><span class="input_checkbox"><input type="checkbox" name="_DoubleOption[]" value="2"></span></span><span class="item_name">产品</span></div><div class="item" data-name="客户" data-value="user" data-type="module" data-table="undefined" data-top="0" data-all="1" data-check-all="0" data-custom="undefined"><span class="input_checkbox_box"><span class="input_checkbox"><input type="checkbox" name="_DoubleOption[]" value="3"></span></span><span class="item_name">客户</span></div><div class="item" data-name="营销" data-value="sales" data-type="module" data-table="undefined" data-top="0" data-all="1" data-check-all="0" data-custom="undefined"><span class="input_checkbox_box"><span class="input_checkbox"><input type="checkbox" name="_DoubleOption[]" value="4"></span></span><span class="item_name">营销</span></div><div class="item" data-name="分析" data-value="mta" data-type="module" data-table="undefined" data-top="0" data-all="1" data-check-all="0" data-custom="undefined"><span class="input_checkbox_box"><span class="input_checkbox"><input type="checkbox" name="_DoubleOption[]" value="5"></span></span><span class="item_name">分析</span></div><div class="item" data-name="店铺" data-value="view" data-type="module" data-table="undefined" data-top="0" data-all="1" data-check-all="0" data-custom="undefined"><span class="input_checkbox_box"><span class="input_checkbox"><input type="checkbox" name="_DoubleOption[]" value="6"></span></span><span class="item_name">店铺</span></div><div class="item" data-name="应用" data-value="plugins" data-type="module" data-table="undefined" data-top="0" data-all="1" data-check-all="0" data-custom="undefined"><span class="input_checkbox_box"><span class="input_checkbox"><input type="checkbox" name="_DoubleOption[]" value="7"></span></span><span class="item_name">应用</span></div><div class="item" data-name="设置" data-value="set" data-type="module" data-table="undefined" data-top="0" data-all="1" data-check-all="0" data-custom="undefined"><span class="input_checkbox_box"><span class="input_checkbox"><input type="checkbox" name="_DoubleOption[]" value="8"></span></span><span class="item_name">设置</span></div><div class="item" data-name="API" data-value="api" data-type="module" data-table="undefined" data-top="0" data-all="1" data-check-all="0" data-custom="undefined"><span class="input_checkbox_box"><span class="input_checkbox"><input type="checkbox" name="_DoubleOption[]" value="9"></span></span><span class="item_name">API</span></div></div><a href="javascript:;" class="btn_load_more" data-value="" data-type="" data-table="" data-top="0" data-all="0" data-check-all="" data-start="1" style="display:none;">加载更多</a></div></dd></dl>
								</div>
								<div class="filter_clean"><button>清除</button></div>
							</div>
						</div>
					</div>
					<div class="rows clean box_button box_submit">
						<div class="input">
							<input type="button" class="btn_global btn_submit" value="筛选">
							<input type="button" class="btn_global btn_cancel" value="取消">
						</div>
					</div>
				</div>
			</div>
			<div class="global_container fixed_view_detail" data-width="396" style="display: none;">
				<div class="top_title">详细 <a href="javascript:;" class="close"></a></div>
				<div class="global_form box_view_detail"></div>
				<div class="rows clean box_button box_submit">
					<div class="input">
						<input type="button" class="btn_global btn_cancel" value="知道了">
					</div>
				</div>
			</div>
		</div>
	</div>
</div>