@model YseStoreAdmin.Pages.Components.Setting.SettingEmail
@{
}
<div>

    <div id="config" class="r_con_wrap" style="height: 444px;">
        <div class="center_container email">
            <div class="bread_nav">
                <a href="/Setting/Index" class="return_title o_return_title">设置</a>
                <h1>
                    邮件通知&amp;设置
                </h1>
            </div>
            <div class="clear"></div>
            <div class="global_container email_config_box">
                <strong>发件人</strong>
                <span class="email_tips number_hide">
                    <div>发件邮箱：<span class="from_email">@Model.EmailConfig.FromEmail</span></div>
                    <div>发件人：<span class="from_name">@Model.EmailConfig.FromName</span></div>
                    <div>
                        邮件服务： <span class="from_name">@Model.EmailConfig.EmailType</span>
                        <span class="EmailNumber">
                            (剩余可发送<span class="email_num">
                            </span>封)
                        </span>
                    </div>
                </span>
                <a class="btn_global btn_submit btn_height send_config_set" href="javascript:;">设置</a>
            </div>
            <div class="global_container customer_notice">
                <div class="config_top_title email_customer_title"><span class="return">客户通知</span></div>
                <div class="config_table_body config_table_body_set email_customer_box">
                    <div class="email_module">
                        <div class="module_title color_000">订单</div>
                        <div class="table_item">
                            <table border="0" cellpadding="5" cellspacing="0" class="config_table email_config_table">
                                <tbody>
                                    <tr>
                                        <td width="80%" nowrap="nowrap"><span class="color_000">付款成功</span></td>
                                        <td width="30%" nowrap="nowrap">
                                            <a class="edit" href="/manage/set/email/edit?v=order_payment">修改邮件内容</a>
                                        </td>
                                        <td width="50" nowrap="nowrap" align="center">
                                            <div class="used_checkbox">
                                                <div class="switchery @(Model.EmailNotice.order_payment==1?"checked":"")" data-type="order_payment">
                                                    <div class="switchery_toggler"></div>
                                                    <div class="switchery_inner">
                                                        <div class="switchery_state_on"></div>
                                                        <div class="switchery_state_off"></div>
                                                    </div>
                                                </div>
                                            </div>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                        <div class="table_item">
                            <table border="0" cellpadding="5" cellspacing="0" class="config_table email_config_table">
                                <tbody>
                                    <tr>
                                        <td width="80%" nowrap="nowrap"><span class="color_000">订单发货</span></td>
                                        <td width="30%" nowrap="nowrap">
                                            <a class="edit" href="/manage/set/email/edit?v=order_shipped">修改邮件内容</a>
                                        </td>
                                        <td width="50" nowrap="nowrap" align="center">
                                            <div class="used_checkbox">
                                                <div class="switchery @(Model.EmailNotice.order_shipped==1?"checked":"")" data-type="order_shipped">
                                                    <div class="switchery_toggler"></div>
                                                    <div class="switchery_inner">
                                                        <div class="switchery_state_on"></div>
                                                        <div class="switchery_state_off"></div>
                                                    </div>
                                                </div>
                                            </div>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                        <div class="table_item">
                            <table border="0" cellpadding="5" cellspacing="0" class="config_table email_config_table">
                                <tbody>
                                    <tr>
                                        <td width="80%" nowrap="nowrap"><span class="color_000">订单完成</span></td>
                                        <td width="30%" nowrap="nowrap">
                                            <a class="edit" href="/manage/set/email/edit?v=order_change">修改邮件内容</a>
                                        </td>
                                        <td width="50" nowrap="nowrap" align="center">
                                            <div class="used_checkbox">
                                                <div class="switchery @(Model.EmailNotice.order_change==1?"checked":"")" data-type="order_change">
                                                    <div class="switchery_toggler"></div>
                                                    <div class="switchery_inner">
                                                        <div class="switchery_state_on"></div>
                                                        <div class="switchery_state_off"></div>
                                                    </div>
                                                </div>
                                            </div>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                        <div class="table_item">
                            <table border="0" cellpadding="5" cellspacing="0" class="config_table email_config_table">
                                <tbody>
                                    <tr>
                                        <td width="80%" nowrap="nowrap"><span class="color_000">订单取消</span></td>
                                        <td width="30%" nowrap="nowrap">
                                            <a class="edit" href="/manage/set/email/edit?v=order_cancel">修改邮件内容</a>
                                        </td>
                                        <td width="50" nowrap="nowrap" align="center">
                                            <div class="used_checkbox">
                                                <div class="switchery @(Model.EmailNotice.order_cancel==1?"checked":"")" data-type="order_cancel">
                                                    <div class="switchery_toggler"></div>
                                                    <div class="switchery_inner">
                                                        <div class="switchery_state_on"></div>
                                                        <div class="switchery_state_off"></div>
                                                    </div>
                                                </div>
                                            </div>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                        <div class="table_item">
                            <table border="0" cellpadding="5" cellspacing="0" class="config_table email_config_table">
                                <tbody>
                                    <tr>
                                        <td width="80%" nowrap="nowrap"><span class="color_000">订单退款</span></td>
                                        <td width="30%" nowrap="nowrap">
                                            <a class="edit" href="/manage/set/email/edit?v=order_refund">修改邮件内容</a>
                                        </td>
                                        <td width="50" nowrap="nowrap" align="center">
                                            <div class="used_checkbox">
                                                <div class="switchery @(Model.EmailNotice.order_refund==1?"checked":"")" data-type="order_refund">
                                                    <div class="switchery_toggler"></div>
                                                    <div class="switchery_inner">
                                                        <div class="switchery_state_on"></div>
                                                        <div class="switchery_state_off"></div>
                                                    </div>
                                                </div>
                                            </div>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                        <div class="table_item">
                            <table border="0" cellpadding="5" cellspacing="0" class="config_table email_config_table">
                                <tbody>
                                    <tr>
                                        <td width="80%" nowrap="nowrap"><span class="color_000">账单通知</span></td>
                                        <td width="30%" nowrap="nowrap">
                                            <a class="edit" href="/manage/set/email/edit?v=bill_notification">修改邮件内容</a>
                                        </td>
                                        <td width="50" nowrap="nowrap" align="center">
                                            <div class="switchery empty"></div>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                        <div class="table_item">
                            <table border="0" cellpadding="5" cellspacing="0" class="config_table email_config_table">
                                <tbody>
                                    <tr>
                                        <td width="80%" nowrap="nowrap"><span class="color_000">购物车召回</span></td>
                                        <td width="30%" nowrap="nowrap">
                                            <a class="edit" href="/manage/set/email/edit?v=shopping_cart_recall">修改邮件内容</a>
                                        </td>
                                        <td width="50" nowrap="nowrap" align="center">
                                            <div class="switchery empty"></div>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                        <div class="table_item">
                            <table border="0" cellpadding="5" cellspacing="0" class="config_table email_config_table">
                                <tbody>
                                    <tr>
                                        <td width="80%" nowrap="nowrap"><span class="color_000">订单召回</span></td>
                                        <td width="30%" nowrap="nowrap">
                                            <a class="edit" href="/manage/set/email/edit?v=order_recall">修改邮件内容</a>
                                        </td>
                                        <td width="50" nowrap="nowrap" align="center">
                                            <div class="switchery empty"></div>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                        <div class="table_item">
                            <table border="0" cellpadding="5" cellspacing="0" class="config_table email_config_table">
                                <tbody>
                                    <tr>
                                        <td width="80%" nowrap="nowrap"><span class="color_000">订单消息</span></td>
                                        <td width="30%" nowrap="nowrap">
                                            <a class="edit" href="/manage/set/email/edit?v=order_message">修改邮件内容</a>
                                        </td>
                                        <td width="50" nowrap="nowrap" align="center">
                                            <div class="switchery empty"></div>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                    <div class="email_module">
                        <div class="module_title color_000">客户</div>
                        <div class="table_item">
                            <table border="0" cellpadding="5" cellspacing="0" class="config_table email_config_table">
                                <tbody>
                                    <tr>
                                        <td width="80%" nowrap="nowrap"><span class="color_000">会员注册</span></td>
                                        <td width="30%" nowrap="nowrap">
                                            <a class="edit" href="/manage/set/email/edit?v=create_account">修改邮件内容</a>
                                        </td>
                                        <td width="50" nowrap="nowrap" align="center">
                                            <div class="used_checkbox">
                                                <div class="switchery @(Model.EmailNotice.create_account==1?"checked":"")" data-type="create_account">
                                                    <div class="switchery_toggler"></div>
                                                    <div class="switchery_inner">
                                                        <div class="switchery_state_on"></div>
                                                        <div class="switchery_state_off"></div>
                                                    </div>
                                                </div>
                                            </div>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>

                        <div class="table_item">
                            <table border="0" cellpadding="5" cellspacing="0" class="config_table email_config_table">
                                <tbody>
                                    <tr>
                                        <td width="80%" nowrap="nowrap"><span class="color_000">注册验证邮箱</span></td>
                                        <td width="30%" nowrap="nowrap">
                                            <a class="edit" href="/manage/set/email/edit?v=validate_mail">修改邮件内容</a>
                                        </td>
                                        <td width="50" nowrap="nowrap" align="center">
                                            <div class="used_checkbox">
                                                <div class="switchery @(Model.EmailNotice.validate_mail==1?"checked":"")" data-type="validate_mail">
                                                    <div class="switchery_toggler"></div>
                                                    <div class="switchery_inner">
                                                        <div class="switchery_state_on"></div>
                                                        <div class="switchery_state_off"></div>
                                                    </div>
                                                </div>
                                            </div>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>

                        <div class="table_item">
                            <table border="0" cellpadding="5" cellspacing="0" class="config_table email_config_table">
                                <tbody>
                                    <tr>
                                        <td width="80%" nowrap="nowrap"><span class="color_000">会员邀请</span></td>
                                        <td width="30%" nowrap="nowrap">
                                            <a class="edit" href="/manage/set/email/edit?v=user_invitation">修改邮件内容</a>
                                        </td>
                                        <td width="50" nowrap="nowrap" align="center">
                                            <div class="switchery empty"></div>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                        <div class="table_item">
                            <table border="0" cellpadding="5" cellspacing="0" class="config_table email_config_table">
                                <tbody>
                                    <tr>
                                        <td width="80%" nowrap="nowrap"><span class="color_000">忘记密码</span></td>
                                        <td width="30%" nowrap="nowrap">
                                            <a class="edit" href="/manage/set/email/edit?v=forgot_password">修改邮件内容</a>
                                        </td>
                                        <td width="50" nowrap="nowrap" align="center">
                                            <div class="used_checkbox">
                                                <div class="switchery @(Model.EmailNotice.forgot_password==1?"checked":"")" data-type="forgot_password">
                                                    <div class="switchery_toggler"></div>
                                                    <div class="switchery_inner">
                                                        <div class="switchery_state_on"></div>
                                                        <div class="switchery_state_off"></div>
                                                    </div>
                                                </div>
                                            </div>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                        <div class="table_item">
                            <table border="0" cellpadding="5" cellspacing="0" class="config_table email_config_table">
                                <tbody>
                                    <tr>
                                        <td width="80%" nowrap="nowrap"><span class="color_000">邮件订阅成功</span></td>
                                        <td width="30%" nowrap="nowrap">
                                            <a class="edit" href="/manage/set/email/edit?v=email_subscriptions">修改邮件内容</a>
                                        </td>
                                        <td width="50" nowrap="nowrap" align="center">
                                            <div class="used_checkbox">
                                                <div class="switchery @(Model.EmailNotice.email_subscriptions==1?"checked":"")" data-type="email_subscriptions">
                                                    <div class="switchery_toggler"></div>
                                                    <div class="switchery_inner">
                                                        <div class="switchery_state_on"></div>
                                                        <div class="switchery_state_off"></div>
                                                    </div>
                                                </div>
                                            </div>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                        <div class="table_item">
                            <table border="0" cellpadding="5" cellspacing="0" class="config_table email_config_table">
                                <tbody>
                                    <tr>
                                        <td width="80%" nowrap="nowrap"><span class="color_000">投放优惠券</span></td>
                                        <td width="30%" nowrap="nowrap">
                                            <a class="edit" href="/manage/set/email/edit?v=post_coupon">修改邮件内容</a>
                                        </td>
                                        <td width="50" nowrap="nowrap" align="center">
                                            <div class="switchery empty"></div>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                        <div class="table_item">
                            <table border="0" cellpadding="5" cellspacing="0" class="config_table email_config_table">
                                <tbody>
                                    <tr>
                                        <td width="80%" nowrap="nowrap"><span class="color_000">站内信通知</span></td>
                                        <td width="30%" nowrap="nowrap">
                                            <a class="edit" href="/manage/set/email/edit?v=inbox_message">修改邮件内容</a>
                                        </td>
                                        <td width="50" nowrap="nowrap" align="center">
                                            <div class="switchery empty"></div>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
            <div class="global_container email_config_box">
                <div class="config_top_title email_merchant_title">
                    <span class="return">商家通知</span>
                    <a href="/manage/set/email/recipients" class="global_color fr">收件人管理</a>
                </div>
                <div class="config_table_body config_table_body_set email_merchant_box">
                    <div class="table_item " data-type="order_create">
                        <div class="item_name">
                            <span class="merchant_title color_000">付款通知</span>
                            <span class="merchant_note"></span>
                        </div>
                        @if (Model.NerchantNoticeCount.ContainsKey("order_create"))
                        {
                            if (Model.NerchantNoticeCount["order_create"] == 0)
                            {
                                <div class="global_app_tips insufficient "><em></em>暂无收件人，建议尽快添加</div>
                            }
                        }
                        <a href="javascript:;" class="btn_merchant_detail global_color" data-type="order_create">详细</a>
                    </div>
                    <div class="table_item " data-type="user_inbox">
                        <div class="item_name">
                            <span class="merchant_title color_000">站内信通知</span>
                            <span class="merchant_note"></span>
                        </div>
                        @if (Model.NerchantNoticeCount.ContainsKey("user_inbox"))
                        {
                            if (Model.NerchantNoticeCount["user_inbox"] == 0)
                            {
                                <div class="global_app_tips insufficient "><em></em>暂无收件人，建议尽快添加</div>
                            }
                        }
                        <a href="javascript:;" class="btn_merchant_detail global_color" data-type="user_inbox">详细</a>
                    </div>
                    <div class="table_item " data-type="order_notification">
                        <div class="item_name">
                            <span class="merchant_title color_000">下单通知</span>
                            <span class="merchant_note"></span>
                        </div>
                        @if (Model.NerchantNoticeCount.ContainsKey("order_notification"))
                        {
                            if (Model.NerchantNoticeCount["order_notification"] == 0)
                            {
                                <div class="global_app_tips insufficient "><em></em>暂无收件人，建议尽快添加</div>
                            }
                        }
                        <a href="javascript:;" class="btn_merchant_detail global_color" data-type="order_notification">详细</a>
                    </div>
                    <div class="table_item hide" data-type="review_member">
                        <div class="item_name">
                            <span class="merchant_title color_000">审核会员注册</span>
                            <span class="merchant_note"></span>
                        </div>
                        @if (Model.NerchantNoticeCount.ContainsKey("review_member"))
                        {
                            if (Model.NerchantNoticeCount["review_member"] == 0)
                            {
                                <div class="global_app_tips insufficient "><em></em>暂无收件人，建议尽快添加</div>
                            }
                        }
                        <a href="javascript:;" class="btn_merchant_detail global_color" data-type="review_member">详细</a>
                    </div>
                    <div class="table_item " data-type="form_tool">
                        <div class="item_name">
                            <span class="merchant_title color_000">表单消息通知</span>
                            <span class="merchant_note">来自 [ 表单工具 ] 应用</span>
                        </div>
                        @if (Model.NerchantNoticeCount.ContainsKey("form_tool"))
                        {
                            if (Model.NerchantNoticeCount["form_tool"] == 0)
                            {
                                <div class="global_app_tips insufficient "><em></em>暂无收件人，建议尽快添加</div>
                            }
                        }
                        <a href="javascript:;" class="btn_merchant_detail global_color" data-type="form_tool">详细</a>
                    </div>
                    <div class="table_item hide" data-type="dist_apply">
                        <div class="item_name">
                            <span class="merchant_title color_000">审核分销商申请</span>
                            <span class="merchant_note">来自 [ 分销 ] 应用</span>
                        </div>
                        @if (Model.NerchantNoticeCount.ContainsKey("dist_apply"))
                        {
                            if (Model.NerchantNoticeCount["dist_apply"] == 0)
                            {
                                <div class="global_app_tips insufficient "><em></em>暂无收件人，建议尽快添加</div>
                            }
                        }
                        <a href="javascript:;" class="btn_merchant_detail global_color" data-type="dist_apply">详细</a>
                    </div>
                    <div class="table_item hide" data-type="dist_withdraw">
                        <div class="item_name">
                            <span class="merchant_title color_000">提现申请</span>
                            <span class="merchant_note">来自 [ 分销 ] 应用</span>
                        </div>
                        @if (Model.NerchantNoticeCount.ContainsKey("dist_withdraw"))
                        {
                            if (Model.NerchantNoticeCount["dist_withdraw"] == 0)
                            {
                                <div class="global_app_tips insufficient "><em></em>暂无收件人，建议尽快添加</div>
                            }
                        }
                        <a href="javascript:;" class="btn_merchant_detail global_color" data-type="dist_withdraw">详细</a>
                    </div>
                </div>
            </div>
            <div id="fixed_right">
                <div class="global_container fixed_send_person" data-width="437">
                    <div class="top_title">发件人设置 <a href="javascript:;" class="close"></a></div>
                    <form id="send_person_form" class="global_form" action="/manage/set/email" method="post">
                        @Html.AntiForgeryToken()
                        <div class="rows clean">
                            <label>发件邮箱</label>
                            <div class="input">
                                <input type="text" class="box_input" name="FromEmail" value="@Model.EmailConfig.FromEmail" size="60" maxlength="100" format="Email">
                            </div>
                        </div>
                        <div class="rows clean">
                            <label>发件人</label>
                            <div class="input">
                                <input type="text" class="box_input" name="FromName" value="@Model.EmailConfig.FromName" size="60" maxlength="100">
                            </div>
                        </div>
                        <div class="rows clean switch_box">
                            <div class="title global_app_tips">
                                <span class="fl">
                                    <em></em>
                                    由@(Model.EmailConfig.EmailType)邮件服务发送邮件发给所有客户
                                </span>
                                <a href="javascript:;" class="switch_btn open fr">
                                    切换
                                </a>
                                <a href="javascript:;" class="switch_btn fr hide">
                                    收起
                                </a>
                                <div class="clear">
                                </div>
                            </div>
                            <ul class="list">
                                <li>
                                    @{
                                        bool sendcloud = Model.EmailConfig.EmailType == "Sendcloud";
                                        string block = sendcloud ? "block" : "none";
                                    }
                                    <div class="box_type_menu fl">
                                        <span class="item  @(sendcloud ?"checked":"") ">
                                            <input type="radio" name="EmailType" value="Sendcloud" @(sendcloud ? "checked" : "") />
                                            <img src="/assets/images/set/logo_sendcloud.png" alt="">
                                        </span>
                                    </div>
                                    <div class="clear"></div>
                                    <div class="clear"></div>
                                    <div class="sendcloud_box" style="display:@block">
                                        <div class="rows clean">
                                            <label>API_User</label>
                                            <div class="input">
                                                <input type="text" class="box_input" name="SC_ApiUser" value="@Model.EmailConfig.SC_ApiUser" size="60" maxlength="300">
                                            </div>
                                        </div>

                                        <div class="rows clean">
                                            <label>API_Key</label>
                                            <div class="input">
                                                <input type="text" class="box_input" name="SC_ApiKey" value="@Model.EmailConfig.SC_ApiKey" size="60" maxlength="300">
                                            </div>
                                        </div>
                                    </div>

                                </li>
                                <li>
                                    @{
                                        bool customize = Model.EmailConfig.EmailType == "Customize";
                                        string block01 = customize ? "block" : "none";
                                    }
                                    <div class="box_type_menu fl">
                                        <span class="item  @(customize ?"checked":"")">
                                            <input type="radio" name="EmailType" value="Customize" @(customize ? "checked" : "") />
                                            自定义邮件服务
                                        </span>
                                    </div>
                                    <div class="clear"></div>
                                    <div class="clear"></div>

                                    <div class="customize_box" style="display:@block01">
                                        <div class="rows clean">
                                            <label>邮箱帐号</label>
                                            <div class="input">
                                                <input type="text" class="box_input" name="SmtpUserName" value="@Model.EmailConfig.SmtpUserName" size="60" maxlength="100">
                                            </div>
                                        </div>

                                        <div class="rows clean">
                                            <label>邮箱密码</label>
                                            <div class="input">
                                                <input type="password" class="box_input" name="SmtpPassword" value="@Model.EmailConfig.SmtpPassword" size="60" maxlength="100">
                                            </div>
                                        </div>

                                        <div class="rows clean">
                                            <label>邮箱类型</label>
                                            <div class="box_select">
                                                <select name="SmtpType" asp-for="EmailConfig.SmtpType">
                                                    <option value="aliyun" data-host="smtp.mxhichina.com" data-port="465">aliyun</option>
                                                    <option value="gmail" data-host="smtp.gmail.com" data-port="465">gmail</option>
                                                    <option value="sina" data-host="smtp.sina.com" data-port="25">sina</option>
                                                    <option value="163" data-host="smtp.qiye.163.com" data-port="25">163</option>
                                                    <option value="126" data-host="smtp.126.com" data-port="25">126</option>
                                                    <option value="qq" data-host="smtp.exmail.qq.com" data-port="465">qq</option>
                                                    <option value="foxmail" data-host="smtp.foxmail.com" data-port="25">foxmail</option>
                                                    <option value="outlook" data-host="tls://smtp.office365.com" data-port="587">outlook</option>
                                                    <option value="customize">其他</option>
                                                </select>
                                            </div>
                                        </div>

                                        <div class="rows clean">
                                            <label>SMTP地址</label>
                                            <div class="input">
                                                <input type="text" class="box_input" name="SmtpHost" value="@Model.EmailConfig.SmtpHost" size="60" maxlength="100">
                                            </div>
                                        </div>

                                        <div class="rows clean">
                                            <label>SMTP端口</label>
                                            <div class="input">
                                                <input type="text" class="box_input" name="SmtpPort" value="@Model.EmailConfig.SmtpPort" size="60" maxlength="100">
                                            </div>
                                        </div>

                                    </div>
                                </li>
                            </ul>
                        </div>
                        <div class="rows clean box_button box_submit">
                            <div class="input">
                                <input type="hidden" name="do_action" value="/api/email/set">
                                <input type="button" class="btn_global btn_submit" value="保存">
                                <input type="button" class="btn_global btn_cancel" value="取消">
                            </div>
                        </div>
                    </form>
                </div>
                <div class="global_container fixed_manager_send" data-width="400">
                    <div class="top_title">添加收件人 <a href="javascript:;" class="close"></a></div>
                    <form id="managerAddForm" class="global_form" name="managerAddForm">
                        <div class="rows clean">
                            <label>收件人</label>
                            <div class="blank3"></div>
                            <div class="input">
                                <div class="global_select_box">
                                    <div class="input_case">
                                        <input class="imitation_select box_input full_input" type="text" placeholder="请选择" value="" notnull="">
                                        <input type="hidden" name="UserId" value="">
                                        <i></i>
                                    </div>
                                    <ul class="select_ul drop_down">
                                        <li class="item" data-value="-1">u</li>
                                        @foreach (var item in Model.ManageList)
                                        {
                                            <div class="item" data-value="@item.UserId">@item.UserName</div>
                                        }
                                    </ul>
                                </div>
                            </div>
                        </div>
                        <div class="rows clean">
                            <label>邮箱</label>
                            <div class="blank3"></div>
                            <div class="input">
                                <input type="text" class="box_input" name="Email" notnull="" value="">
                            </div>
                        </div>
                        <div class="rows clean">
                            <label>权限</label>
                            <div class="permit_list">
                                <div class="item">
                                    <span class="input_checkbox_box">
                                        <span class="input_checkbox"><input type="checkbox" name="Permit[]" value="order_notification"></span>
                                        <em>下单通知</em>
                                    </span>
                                </div>
                                <div class="item">
                                    <span class="input_checkbox_box">
                                        <span class="input_checkbox"><input type="checkbox" name="Permit[]" value="payment_notification"></span>
                                        <em>付款通知</em>
                                    </span>
                                </div>
                                <div class="item">
                                    <span class="input_checkbox_box">
                                        <span class="input_checkbox"><input type="checkbox" name="Permit[]" value="inbox_notification"></span>
                                        <em>站内信通知</em>
                                    </span>
                                </div>
                                <div class="item">
                                    <span class="input_checkbox_box">
                                        <span class="input_checkbox"><input type="checkbox" name="Permit[]" value="order_message_notification"></span>
                                        <em>订单消息通知</em>
                                    </span>
                                </div>
                                <div class="item">
                                    <span class="input_checkbox_box">
                                        <span class="input_checkbox"><input type="checkbox" name="Permit[]" value="form_message_notification"></span>
                                        <em>表单消息通知</em>
                                    </span>
                                </div>
                            </div>
                        </div>
                        <div class="rows clean box_button box_submit">
                            <div class="input">
                                <input type="hidden" name="do_action" value="/api/email/manage-add-email">
                                <input type="button" class="btn_global btn_submit" value="保存">
                                <input type="button" class="btn_global btn_cancel" value="取消">
                            </div>
                        </div>
                    </form>
                </div>
                <div class="global_container fixed_merchant_view" data-width="470">
                    <div class="top_title"><strong></strong><a href="javascript:;" class="close"></a></div>
                    <form id="merchant_edit_form" class="global_form" action="/manage/set/email" method="post">
                        @Html.AntiForgeryToken()
                        <div class="global_app_tips insufficient"><em></em>如需更多收件人，请前往 <a href="/manage/set/email/recipients" target="_blank">收件人管理</a> 进行添加</div>
                        <div class="box_table">
                            <table border="0" cellpadding="5" cellspacing="0" class="r_con_table">
                                <thead>
                                    <tr>
                                        <td width="70%">收件人</td>
                                        <td width="30%">允许接收</td>
                                    </tr>
                                </thead>
                                <tbody>
                                </tbody>
                            </table>
                        </div>
                        <div class="bg_no_table_data" style="height: 510px;">
                            <div class="content" style="top: 215px;">
                                <div class="table_title">当前暂时没有数据</div>
                                <p>请前往 <a href="/manage/set/email/recipients" target="_blank">收件人管理</a> 进行添加</p>
                            </div><span></span>
                        </div>
                        <div class="rows clean box_button box_submit">
                            <div class="input">
                                <input type="button" class="btn_global btn_submit" value="保存">
                                <input type="button" class="btn_global btn_cancel" value="取消">
                            </div>
                        </div>
                        <input type="hidden" name="type" value="">
                        <input type="hidden" name="do_action" value="/api/email/merchant-update">
                    </form>
                </div>
            </div>
        </div>
    </div>

</div>