using Entitys;
using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using YseStore.Common;
using YseStore.IService;
using YseStore.IService.Pay;

namespace YseStoreAdmin.Pages.Components.Setting.Pay
{
    public class AddOther : CompBase
    {

        private readonly IPaymentService _paymentService;
        private readonly IConfigService _configService;
        private readonly ICurrencyService _currencyService;
        public AddOther(IPaymentService paymentService, IConfigService configService, ICurrencyService currencyService)
        {
            _paymentService = paymentService;
            _configService = configService;
            _currencyService = currencyService;
        }


        [Parameter]
        public int PId { get; set; }

        [Parameter]
        public string actionType { get; set; }


        [Parameter]
        public int isCustom { get; set; }

        [Parameter]
        public int showType { get; set; }
        
        /// <summary>
        /// 
        /// </summary>
        /// <returns></returns>
        public override async Task MountAsync()
        {
            await base.MountAsync();

            //获取详情
            await GetPayment();
        }

        /// <summary>
        /// 
        /// </summary>
        public payment payment { get; set; } = new payment();


        /// <summary>
        /// 
        /// </summary>
        public Dictionary<string, string> capabilities { get; set; } = new Dictionary<string, string>();

        /// <summary>
        /// 
        /// </summary>
        public payment IsExpressCheckout { get; set; } = new payment();


        public IList<currency> currencyRow { get; set; } = new List<currency>();

        /// <summary>
        /// 默认币种
        /// </summary>
        public currency defaultCurrency { get; set; } = new currency();



        /// <summary>
        /// 默认语言
        /// </summary>
        public string ManageLanguage { get; set; } = "zh-cn";

        /// <summary>
        /// 
        /// </summary>
        public List<string> card_ary { get; set; } = new List<string>();

        /// <summary>
        /// 
        /// </summary>
        /// <returns></returns>
        public async Task GetPayment()
        {
            //获取付款方式详情
            payment = await _paymentService.QueryById(PId);
            if (payment == null)
            {
                payment = new payment();
            }


            //capabilities
            var paypalCapabilities = await _configService.GetConfigByGroup("paypal", "capabilities");
            if (paypalCapabilities != null)
            {
                capabilities = paypalCapabilities.Value.JsonToObj<Dictionary<string, string>>();
            }

            //Excheckout
            IsExpressCheckout = await _paymentService.QueryByClauseAsync(it => it.Method == "Excheckout");

            var manageLanguageConfig = await _configService.GetConfigByGroup("global", "ManageLanguage");
            if (manageLanguageConfig != null)
            {
                ManageLanguage = manageLanguageConfig.Value;
            }

            //默认币种
            defaultCurrency = await _currencyService.GetDefaultCurrency();


            if (payment != null)
            {
                card_ary = GetCartAry(payment.Method);
            }
        }




        public List<string> GetCartAry(string method = null)
        {
            var cardAry = new List<string> {
            "AmericanExpress", "Maestro", "Mastercard", "Visa", "Jcb",
            "Discover", "Dinersclub", "CartesBancaires", "UnionPayInternational",
            "ApplePay", "GooglePay"
        };

            var methodAry = new Dictionary<string, List<string>>
            {
                ["OceanPayment"] = new List<string> {
                "AmericanExpress", "Maestro", "Mastercard", "Visa", "Jcb",
                "Discover", "Dinersclub", "CartesBancaires", "UnionPayInternational"
            },
                ["Payoneer"] = new List<string> {
                "Visa", "Mastercard", "AmericanExpress", "Jcb"
            },
                ["Airwallex"] = new List<string> {
                "AmericanExpress", "Maestro", "Mastercard", "Visa",
                "Jcb", "UnionPayInternational"
            },
                ["Asiabill"] = new List<string> {
                "AmericanExpress", "Maestro", "Mastercard", "Visa", "Jcb",
                "Discover", "Dinersclub", "CartesBancaires"
            },
                ["ApiUseepay"] = new List<string> {
                "AmericanExpress", "Maestro", "Mastercard", "Visa", "Jcb",
                "Discover", "Dinersclub", "CartesBancaires", "UnionPayInternational"
            },
                ["ApiBringallpay"] = new List<string> {
                "Mastercard", "Visa", "Jcb"
            },
                ["LianlianPay"] = new List<string> {
                "AmericanExpress", "Maestro", "Mastercard", "Visa", "Jcb",
                "Discover", "Dinersclub", "UnionPayInternational"
            },
                ["ApiIpaylinks"] = new List<string> {
                "AmericanExpress", "Mastercard", "Visa", "Discover", "Dinersclub"
            },
                ["ApiGLocash"] = new List<string> {
                "Mastercard", "Visa"
            },
                ["ApiLampay"] = new List<string> {
                "Mastercard", "Visa"
            },
                ["WooshPay"] = new List<string> {
                "Maestro", "Mastercard", "Visa"
            },
                ["Onlypay"] = new List<string> {
                "AmericanExpress", "Mastercard", "Visa", "Jcb", "ApplePay"
            },
                ["ApiAllinpay"] = new List<string> {
                "AmericanExpress", "Mastercard", "Visa", "Jcb", "Discover"
            },
                ["ApiSinopay"] = new List<string> {
                "Visa", "Mastercard", "AmericanExpress", "Jcb",
                "Dinersclub", "Discover"
            },
                ["Komoju"] = new List<string> {
                "AmericanExpress", "Mastercard", "Visa", "Jcb",
                "Discover", "Dinersclub", "UnionPayInternational"
            },
                ["Stripe"] = new List<string> {
                "AmericanExpress", "Mastercard", "Visa", "Jcb",
                "Discover", "Dinersclub", "CartesBancaires", "UnionPayInternational"
            },
                ["WorldPay"] = new List<string> {
                "AmericanExpress", "Maestro", "Mastercard", "Visa", "Jcb",
                "Discover", "Dinersclub"
            },
                ["Ebanx"] = new List<string> {
                "AmericanExpress", "Mastercard", "Visa", "Dinersclub"
            },
                ["PingPong"] = new List<string> {
                "AmericanExpress", "Maestro", "Mastercard", "Visa", "Jcb",
                "Discover", "UnionPayInternational"
            },
                ["ApiSteadypay"] = new List<string> {
                "Visa", "Maestro", "Mastercard"
            }
            };

            if (method != null && methodAry.ContainsKey(method))
            {
                cardAry = methodAry[method];
            }

            return cardAry;
        }



    }
}
