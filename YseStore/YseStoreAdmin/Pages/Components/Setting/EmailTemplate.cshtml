@model YseStoreAdmin.Pages.Components.Setting.EmailTemplate
@{
}
<div>
    <style>
        #email_edit_form .<EMAIL> {
            display: block;
        }
    </style>

    <div id="config" class="r_con_wrap" >
        <div class=" email">
            <div class="center_container_1200">
                <div class="return_title">
                    <a href="/Setting/Email">
                        <span class="return">邮件通知&amp;设置</span>
                        <span class="s_return">/ @Model.EmailNotificationMessage</span>
                    </a>
                </div>
            </div>
            <form id="email_edit_form" class="global_form center_container_1200"
                  action="/manage/set/email/edit?v=@Model.EmailType" method="post">
                @Html.AntiForgeryToken()
                @* @T["email_template.my_account"] *@
                <div class="left_container">
                    <div class="left_container_side">
                        <div class="global_container">
                            <div class="big_title">
                                内容 <div class="toggle_language">
                                    <span class="toggle_title">切换语言</span>
                                    <div class="box_select">
                                        <select class="box_input" name="EmailLanguage">
                                            @* <option value="en" selected="">英语</option> *@
                                            @foreach (var item in Model.langeList)
                                            {
                                                <option value="@item.Language">@item.Name</option>
                                            }
                                        </select>
                                       
                                    </div>
                                </div>
                            </div>
                            <div class="rows">
                                <label>主题</label>
                                <div class="input">
                                    <input type="text" name="Title" value="@Model.EmailTpl.Title" class="box_input"
                                           size="70" maxlength="150">
                                </div>
                            </div>
                            <div class="rows clean">
                                <label>邮件内容</label>
                                <div class="input">
                                    <textarea id="Content" name="Content" style="visibility: hidden; display: none;">@Model.EmailTpl.Content</textarea>

                                </div>
                            </div>
                            <div class="rows clean">
                                <label></label>
                                <div class="input input_button">
                                    <input type="button" class="btn_global btn_submit" value="保存">
                                    <input type="button" class="btn_global btn_pointless btn_manage_color mail_preview" value="预览">
                                    <input type="button" class="btn_global btn_pointless btn_manage_color mail_reset"
                                           value="重置">
                                    <a href="/Setting/Email" title="返回">
                                        <input type="button" class="btn_global btn_cancel" value="返回">
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="right_container">
                    <div class="global_container">
                        <div class="big_title">变量</div>
                        <div class="rows clean sys_remark">
                            <div class="input">
                                <strong class="fc_gory">全局变量，所有模板可用：</strong><br>
                                <span class="fc_red">{{Logo}}</span>: 网站Logo图片<br>
                                <span class="fc_red">{{SiteName}}</span>: 网站店铺名称<br>
                                <span class="fc_red">{{Domain}}</span>: 网站域名，不包含http或https<br>
                                <span class="fc_red">{{FullDomain}}</span>: 网站完整域名，包含http或https，适用于超链接URL设置<br>
                                <span class="fc_red">{{Time}}</span>: 邮件发送时间<br>
                                <span class="fc_red">{{UserName}}</span>: 客户姓名<br>
                                <span class="fc_red">{{Email}}</span>: 客户邮箱<br>
                                <span class="fc_red">{{Password}}</span>: 客户密码<br>
                                <div class="tpl_tips order_create order_payment order_shipped order_change order_cancel order_refund bill_notification">
                                    <strong class="fc_gory">订单模板变量：</strong><br>
                                    <span class="fc_red">{{OrderNum}}</span>: 订单号<br>
                                    <span class="fc_red">{OrderDetails}</span>: 订单详情表格，包含订单价格，产品等信息<br>
                                    <span class="fc_red">{{OrderUrl}}</span>: 查看订单链接，适用于超链接URL设置<br>
                                    <span class="fc_red">{{OrderStatus}}</span>: 订单状态<br>
                                    <span class="fc_red">{{OrderPrice}}</span>: 订单价格<br>
                                    <span class="fc_red">{{OrderFefundMethod}} </span>: 订单退款方式<br>
                                    <span class="fc_red">{{OrderRefundPrice}}</span>: 订单退款价格<br>
                                    <span class="fc_red">{{OrderRefundDetail}}</span>: 订单退款详情<br>
                                </div>
                                <div class="tpl_tips order_create">
                                    <strong class="fc_gory">创建订单模板：</strong><br>
                                    <span class="fc_red">{{OrderPaymentUrl}}</span>: 订单支付链接，适用于超链接URL设置<br>
                                    <span class="fc_red">{{OrderPaymentName}}</span>: 订单付款方式<br>
                                </div>
                                <div class="tpl_tips validate_mail">
                                    <strong class="fc_gory">注册验证邮箱模板：</strong><br>
                                    <span class="fc_red">{{VerUrl}}</span>: 验证邮箱链接，适用于超链接URL设置<br>
                                </div>
                                <div class="tpl_tips forgot_password">
                                    <strong class="fc_gory">忘记密码模板：</strong><br>
                                    <span class="fc_red">{{ForgotUrl}}</span>: 忘记密码链接，适用于超链接URL设置<br>
                                </div>
                                <div class="tpl_tips shopping_cart_recall order_recall">
                                    <strong class="fc_gory">召回模板：</strong><br>
                                    <span class="fc_red">{{DiscountText}}</span>: 优惠内容<br>
                                    <span class="fc_red">{{ProductsList}}</span>: 产品列表<br>
                                </div>
                                <div class="tpl_tips arrival_notice">
                                    <strong class="fc_gory">到货通知模板：</strong><br>
                                    <span class="fc_red">{{ProductName}}</span>: 产品名称<br>
                                    <span class="fc_red">{{ProductVariants}}</span>: 产品变体<br>
                                    <span class="fc_red">{{ProductPicture}}</span>: 产品主图<br>
                                    <span class="fc_red">{{ProductUrl}}</span>: 产品链接<br>
                                </div>
                                <div class="tpl_tips inbox_message">
                                    <strong class="fc_gory">站内信消息变量：</strong><br>
                                    <span class="fc_red">{{NewMessage}}</span>: 最新一条消息<br>
                                    <span class="fc_red">{{RecentMessage}}</span>: 最近十条消息<br>
                                </div>
                                <div class="tpl_tips order_message">
                                    <strong class="fc_gory">订单消息变量：</strong><br>
                                    <span class="fc_red">{{OrderNum}}</span>: 订单号<br>
                                    <span class="fc_red">{{NewOrderMessage}}</span>: 最新一条消息<br>
                                    <span class="fc_red">{{RecentOrderMessage}}</span>: 最近十条消息<br>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div id="mail_preview_box">
                    <a href="javascript:;" class="btn_close"></a>
                    <div class="overflow_y_auto"></div>
                </div>
                <input type="hidden" name="template" value="@Model.EmailType">
                <input type="hidden" name="language_default"
                       value="en">
            </form>
        </div>
    </div>

</div>