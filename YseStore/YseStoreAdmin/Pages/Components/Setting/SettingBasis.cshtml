@model YseStoreAdmin.Pages.Components.Setting.SettingBasis
@using Newtonsoft.Json;
@{

}
<div>



    <div id="config" class="r_con_wrap" style="height: 155px;">
        <div class="center_container basis">
            <form id="edit_form" class="global_form basis_edit_form" action="/api/SettingBasis/EditConfigs" method="post">
                <input type="hidden" name="_csrf-manage" value="9CujLIPatn2yY_ZiomaCj-NR_nw5BS88GhKbzmHcOb6zadB9zKnbIuoolxDWN9K4gBCOE01xbmxJQu-FV5dhzw==">
                <div class="bread_nav">
                    <a href="/Setting/Index" class="return_title o_return_title">设置</a>
                    <h1>
                        基本设置
                    </h1>
                </div>
                <div class="clear"></div>
                <div class="global_container store">
                    <div class="config_top_title"><span class="return">店铺信息</span></div>
                    <!-- 店铺名称 -->
                    <div class="rows clean">
                        <label>店铺名称</label>
                        <div class="input">
                            <input type="text" class="box_input full_input" name="SiteName" value="@Model.Configs.Where(c=>c.GroupId=="global"&&c.Variable=="SiteName").FirstOrDefault()?.Value" size="50" maxlength="255">
                        </div>
                    </div>

                    <!-- icon图标 -->
                    <div class="rows clean">
                        <label>Ico图标</label>
                        <div class="input">
                            <div class="tips">图片大小建议: 16x16像素</div>
                            <div class="multi_img upload_file_multi " id="IcoDetail">
                                <dl class="img isfile" num="0">
                                    <dt class="upload_box preview_pic">
                                        <input type="button" class="btn_ok upload_btn" name="submit_button" value="上传图片" tips="" style="display: none;">
                                        <input type="hidden" name="IcoPath" value="@Model.Configs.Where(c=>c.GroupId=="global"&&c.Variable=="IcoPath").FirstOrDefault()?.Value" data-value="" save="1">
                                        <a href="javascript:;"><img src="@Model.Configs.Where(c=>c.GroupId=="global"&&c.Variable=="IcoPath").FirstOrDefault()?.Value"><em></em></a>
                                    </dt>
                                    <dd class="pic_btn">
                                        <a href="@Model.Configs.Where(c=>c.GroupId=="global"&&c.Variable=="IcoPath").FirstOrDefault()?.Value" class="zoom" target="_blank">
                                            <i class="icon_multi_view"></i>
                                        </a><a href="javascript:;" class="del" rel="del">
                                            <i class="icon_multi_delete"></i>
                                        </a>
                                    </dd>
                                </dl>
                            </div>
                        </div>
                    </div>

                    <!-- 版权信息 -->
                    <div class="rows clean">
                        <label>
                            版权信息
                        </label>
                        <div class="input">
                            <div class="tab_txt tab_txt_en" style="display:block;" lang="en">
                                <input data-auto-change="_en" type="text" name="CopyRight_en" value="@Model.Configs.Where(c=>c.GroupId=="global"&&c.Variable=="Blog").FirstOrDefault()?.Value" class="box_input full_input" size="70" maxlength="255">
                            </div>
                        </div>
                    </div>

                    <!-- 免邮额度 -->
                    <div class="rows clean">
                        <label>
                            免邮额度
                        </label>
                        <div class="input">
                            <input type="text" class="box_input full_input" name="FreeShippingPrice" value="@Model.Configs.Where(c=>c.GroupId=="global"&&c.Variable=="FreeShippingPrice").FirstOrDefault()?.Value" size="30" maxlength="100">
                        </div>
                    </div>

                    <!-- 博客右侧图片 -->
                    <div class="rows clean">
                        <label>博客右侧图片</label>
                        <div class="input">
                            <div class="tips">图片大小建议: 290x365像素</div>
                            <div class="multi_img upload_file_multi " id="BlogDetail">
                                <dl class="img isfile" num="0">
                                    <dt class="upload_box preview_pic">
                                        <input type="button" class="btn_ok upload_btn" name="submit_button" value="上传图片" tips="" style="display: none;">
                                        <input type="hidden" name="BlogPath" value="@Model.Configs.Where(c=>c.GroupId=="global"&&c.Variable=="BlogPath").FirstOrDefault()?.Value" data-value="" save="1">
                                        <a href="javascript:;"><img src="@Model.Configs.Where(c=>c.GroupId=="global"&&c.Variable=="BlogPath").FirstOrDefault()?.Value"><em></em></a>
                                    </dt>
                                    <dd class="pic_btn">
                                        <a href="@Model.Configs.Where(c=>c.GroupId=="global"&&c.Variable=="BlogPath").FirstOrDefault()?.Value" class="zoom" target="_blank"><i class="icon_multi_view"></i></a>
                                        <a href="javascript:;" class="del" rel="del"><i class="icon_multi_delete"></i></a>
                                    </dd>
                                </dl>
                            </div>
                        </div>
                    </div>

                    <!-- 博客右侧介绍标题 -->
                    <div class="rows clean">
                        <label>
                            博客右侧介绍标题
                        </label>
                        <div class="input">
                            <input type="text" class="box_input full_input" name="BlogTitle" value="@Model.Configs.Where(c=>c.GroupId=="global"&&c.Variable=="BlogTitle").FirstOrDefault()?.Value" size="30" maxlength="100">
                        </div>
                    </div>

                    <!-- 博客右侧介绍描述 -->
                    <div class="rows clean">
                        <label>
                            博客右侧介绍描述
                        </label>
                        <div class="input">
                            <textarea class="box_textarea" name="BlogBrief">@Model.Configs.Where(c => c.GroupId == "global" && c.Variable == "BlogBrief").FirstOrDefault()?.Value</textarea>
                        </div>
                    </div>
                </div>
                <div class="global_container footer_msg">
                    <div class="config_top_title"><span class="return">产品详情底部模块信息</span></div>
                    <div class="rows clean">
                        <label>标题1</label>
                        <div class="input">
                            <input type="text" class="box_input full_input" name="GoodsFooterModeTopTitle1" value="@Model.Configs.Where(c => c.GroupId == "global" && c.Variable == "GoodsFooterModeTopTitle1").FirstOrDefault()?.Value" maxlength="20">
                        </div>
                    </div>
                    <div class="rows clean">
                        <label>标题2</label>
                        <div class="input">
                            <input type="text" class="box_input full_input" name="GoodsFooterModeTopTitle2" value="@Model.Configs.Where(c => c.GroupId == "global" && c.Variable == "GoodsFooterModeTopTitle2").FirstOrDefault()?.Value" maxlength="20">
                        </div>
                    </div>

                    @if (Model.Configs != null)
                    {

                        for (int i = 1; i <= Model.Configs.Where(c => c.GroupId == "global" && c.Variable.Contains("GoodsFooterModeTitle")).Count(); i++)
                        {
                            <div class="rows clean">
                                <label>模块@(i)-图片</label>
                                <div class="input">
                                    <div class="tips">图片大小建议: 50xauto像素</div>
                                    <div class="multi_img upload_file_multi " id="GoodsFooterMode@(i)">
                                        <dl class="img " num="0">
                                            <dt class="upload_box preview_pic">
                                                <input type="button" class="btn_ok upload_btn" name="submit_button" value="上传图片" tips="" style="display: none;">
                                                <input type="hidden" name="GoodsFooterModePath@(i)" value="@Model.Configs.Where(c=>c.GroupId=="global"&&c.Variable=="GoodsFooterModePath"+i+"").FirstOrDefault()?.Value" data-value="" save="1">
                                                <a href="javascript:;"><img src="@Model.Configs.Where(c=>c.GroupId=="global"&&c.Variable=="GoodsFooterModePath"+i+"").FirstOrDefault()?.Value"><em></em></a>
                                            </dt>
                                            <dd class="pic_btn">
                                                <a href="@Model.Configs.Where(c=>c.GroupId=="global"&&c.Variable=="GoodsFooterModePath"+i+"").FirstOrDefault()?.Value" class="zoom" target="_blank"><i class="icon_multi_view"></i></a>
                                                <a href="javascript:;" class="del" rel="del"><i class="icon_multi_delete"></i></a>
                                            </dd>
                                        </dl>
                                    </div>
                                </div>
                            </div>
                            <div class="rows clean">
                                <label>模块@(i)-标题</label>
                                <div class="input">
                                    <input type="text" class="box_input full_input" name="GoodsFooterModeTitle@(i)" value="@Model.Configs.Where(c => c.GroupId == "global" && c.Variable == "GoodsFooterModeTitle"+i+"").FirstOrDefault()?.Value" maxlength="100">
                                </div>
                            </div>
                            <div class="rows clean">
                                <label>
                                    模块@(i)-描述
                                </label>
                                <div class="input">
                                    <textarea class="box_textarea" name="GoodsFooterModeBrief@(i)">@Model.Configs.Where(c => c.GroupId == "global" && c.Variable == "GoodsFooterModeBrief" + i + "").FirstOrDefault()?.Value</textarea>
                                </div>
                            </div>
                        }
                    }

                </div>
                <div class="global_container footer_msg">
                    <div class="config_top_title"><span class="return">底部信息</span></div>

                    @if (Model.Configs != null)
                    {
                        for (int i = 1; i <= Model.Configs.Where(c => c.GroupId == "global" && c.Variable.StartsWith("FooterModeTitle")).Count(); i++)
                        {
                            <div class="rows clean">
                                <label>模块@(i)-图片</label>
                                <div class="input">
                                    <div class="tips">图片大小建议: 50xauto像素</div>
                                    <div class="multi_img upload_file_multi " id="FooterMode@(i)">
                                        <dl class="img isfile" num="0">
                                            <dt class="upload_box preview_pic">
                                                <input type="button" class="btn_ok upload_btn" name="submit_button" value="上传图片" tips="" style="display: none;">
                                                <input type="hidden" name="FooterModePath@(i)" value="@Model.Configs.Where(c=>c.GroupId=="global"&&c.Variable=="FooterModePath"+i+"").FirstOrDefault()?.Value" data-value="" save="1">
                                                <a href="javascript:;"><img src="@Model.Configs.Where(c=>c.GroupId=="global"&&c.Variable=="FooterModePath"+i+"").FirstOrDefault()?.Value"><em></em></a>
                                            </dt>
                                            <dd class="pic_btn">
                                                <a href="@Model.Configs.Where(c=>c.GroupId=="global"&&c.Variable=="FooterModePath"+i+"").FirstOrDefault()?.Value" class="zoom" target="_blank"><i class="icon_multi_view"></i></a>
                                                <a href="javascript:;" class="del" rel="del"><i class="icon_multi_delete"></i></a>
                                            </dd>
                                        </dl>
                                    </div>
                                </div>
                            </div>
                            <div class="rows clean">
                                <label>模块@(i)-标题</label>
                                <div class="input">
                                    <input type="text" class="box_input full_input" name="FooterModeTitle@(i)" value="@Model.Configs.Where(c => c.GroupId == "global" && c.Variable == "FooterModeTitle" + i + "").FirstOrDefault()?.Value" maxlength="100">
                                </div>
                            </div>
                            <div class="rows clean">
                                <label>
                                    模块@(i)-描述
                                </label>
                                <div class="input">
                                    <textarea class="box_textarea" name="FooterModeBrief@(i)">@Model.Configs.Where(c => c.GroupId == "global" && c.Variable == "FooterModeBrief" + i + "").FirstOrDefault()?.Value</textarea>
                                </div>
                            </div>
                        }
                    }




                    <!-- 底部Logo -->
                    <div class="rows clean">
                        <label>底部Logo</label>
                        <div class="input">
                            <div class="tips">图片大小建议: 150x25像素</div>
                            <div class="multi_img upload_file_multi " id="FooterDetail">
                                <dl class="img isfile" num="0">
                                    <dt class="upload_box preview_pic">
                                        <input type="button" class="btn_ok upload_btn" name="submit_button" value="上传图片" tips="" style="display: none;">
                                        <input type="hidden" name="FooterPath" value="@Model.Configs.Where(c=>c.GroupId=="global"&&c.Variable=="FooterPath").FirstOrDefault()?.Value" data-value="" save="1">
                                        <a href="javascript:;"><img src="@Model.Configs.Where(c=>c.GroupId=="global"&&c.Variable=="FooterPath").FirstOrDefault()?.Value"><em></em></a>
                                    </dt>
                                    <dd class="pic_btn">
                                        <a href="@Model.Configs.Where(c=>c.GroupId=="global"&&c.Variable=="FooterPath").FirstOrDefault()?.Value" class="zoom" target="_blank"><i class="icon_multi_view"></i></a>
                                        <a href="javascript:;" class="del" rel="del"><i class="icon_multi_delete"></i></a>
                                    </dd>
                                </dl>
                            </div>
                        </div>
                    </div>
                    <!-- 底部网站介绍 -->
                    <div class="rows clean">
                        <label>
                            底部网站介绍
                        </label>
                        <div class="input">
                            <textarea class="box_textarea" name="FooterBrief">@Model.Configs.Where(c => c.GroupId == "global" && c.Variable == "FooterBrief").FirstOrDefault()?.Value</textarea>
                        </div>
                    </div>
                    <div class="rows">
                        <label>底部联系地址</label>
                        <div class="input">
                            <input type="text" class="box_input full_input" name="FooterAddress" value="@Model.Configs.Where(c => c.GroupId == "global" && c.Variable == "FooterAddress").FirstOrDefault()?.Value" maxlength="255">
                        </div>
                    </div>
                    <!-- 邮箱 -->
                    <div class="rows">
                        <label>底部联系邮箱</label>
                        <div class="input">
                            <input type="text" class="box_input full_input" name="FooterEmail" value="@Model.Configs.Where(c => c.GroupId == "global" && c.Variable == "FooterEmail").FirstOrDefault()?.Value" size="50" maxlength="255">
                        </div>
                    </div>
                    <div class="rows clean">
                        <label>底部联系电话</label>
                        <div class="input">
                            <input type="text" class="box_input full_input" name="FooterTelephone" value="@Model.Configs.Where(c => c.GroupId == "global" && c.Variable == "FooterTelephone").FirstOrDefault()?.Value" maxlength="255">
                        </div>
                        <div class="clear"></div>
                    </div>
                </div>
                <div class="global_container normal">
                    <div class="config_top_title"><span class="return">常规设置</span></div>
                    <div class="rows fl half_rows">
                        <label>店铺时区</label>
                        <div class="input">
                            <div class="box_select">
                                <select name="TimeZone">
                                    @if (Model.TimeZones != null)
                                    {
                                        foreach (var item in Model.TimeZones)
                                        {
                                            if (item.Value == Model.SelectedTimeZone)
                                            {
                                                <option value="@item.Value" selected>
                                                    @item.Text
                                                </option>
                                            }
                                            else
                                            {
                                                <option value="@item.Value">
                                                    @item.Text
                                                </option>
                                            }
                                        }
                                    }

                                </select>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="global_container currency" id="position-currency">
                    <div class="config_top_title"><span class="return">货币</span></div>
                    <a href="javascript:;" class="btn_global btn_add_currency">添加</a>
                    <div class="currency_container">
                        <div class="currency_list">
                            <table border="0" cellpadding="5" cellspacing="0" class="r_con_table">
                                <colgroup>
                                    <col style="width:35%;">
                                    <col style="width:35%;">
                                    <col style="width:20%;">
                                    <col style="width:10%;">
                                </colgroup>
                                <thead>
                                    <tr>
                                        <td>货币</td>
                                        <td>汇率 (1 USD)</td>
                                        <td>前台显示</td>
                                        <td nowrap="" class="operation"></td>
                                    </tr>
                                </thead>
                                <tbody>
                                    @if (Model.Currencys != null)
                                    {
                                        foreach (var item in Model.Currencys)
                                        {

                                            <tr data-id="@item.CId">
                                                @{
                                                    var nameData = JsonConvert.DeserializeObject<Dictionary<string, string>>(item.Name);
                                                    var name = nameData.TryGetValue("zh-cn", out var value) ? value : "";
                                                }
                                                <td>@(name) (@(item.Currency)/@(item.Symbol))</td>
                                                <td>@item.ExchangeRate</td>
                                                <td>
                                                    <div class="switchery @(Convert.ToBoolean(item.IsWebShow) ? "checked" : "")">
                                                        <input type="checkbox" @(Convert.ToBoolean(item.IsWebShow) ? "checked" : "") name="IsWebShow[@item.CId]" value="@item.IsWebShow"><div class="switchery_toggler"></div>
                                                        <div class="switchery_inner">
                                                            <div class="switchery_state_on"></div>
                                                            <div class="switchery_state_off"></div>
                                                        </div>
                                                    </div>
                                                </td>
                                                <td nowrap="" class="operation tar">
                                                    <a href="javascript:;" class="oper_icon icon_edit edit button_tips">修改</a>
                                                    <a href="javascript:;" class="oper_icon icon_del del button_tips">删除</a>
                                                    <input type="hidden" class="CurrencyRate" name="CurrencyRate[@item.CId]" value="@item.Rate">
                                                    <input type="hidden" class="CurrencyMethod" name="CurrencyMethod[@item.CId]" value="@item.Method">
                                                    <input type="hidden" class="CurrencyProportion" name="CurrencyProportion[@item.CId]" value="@item.Proportion">
                                                    <input type="hidden" class="CurrencyProportionStatus" name="CurrencyProportionStatus[@item.CId]" value="@item.ProportionStatus">
                                                </td>
                                            </tr>
                                        }
                                    }


                                    @*  <tr data-id="2">
                                    <td>欧元 (EUR/€)</td>
                                    <td>0.9636</td>
                                    <td>
                                    <div class="switchery checked">
                                    <input type="checkbox" checked="checked" name="IsWebShow[2]" value="1"><div class="switchery_toggler"></div>
                                    <div class="switchery_inner">
                                    <div class="switchery_state_on"></div>
                                    <div class="switchery_state_off"></div>
                                    </div>
                                    </div>
                                    </td>
                                    <td nowrap="" class="operation tar">
                                    <a href="javascript:;" class="oper_icon icon_edit edit button_tips">修改</a>
                                    <a href="javascript:;" class="oper_icon icon_del del button_tips">删除</a>
                                    <input type="hidden" class="CurrencyRate" name="CurrencyRate[2]" value="0.9636">
                                    <input type="hidden" class="CurrencyMethod" name="CurrencyMethod[2]" value="manual">
                                    <input type="hidden" class="CurrencyProportion" name="CurrencyProportion[2]" value="0">
                                    <input type="hidden" class="CurrencyProportionStatus" name="CurrencyProportionStatus[2]" value="0">
                                    </td>
                                    </tr> *@
                                </tbody>
                            </table>
                            <div id="btn_edit_global"></div>
                        </div>
                        <div class="scroll_sticky">
                            <div class="scroll_sticky_content">
                                <div style="width: 1px; height: 129px;">
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="box_currency_select clean">
                        <div class="rows clean">
                            <label>后台默认货币</label>
                            <div class="input">
                                <div class="box_select">
                                    <select name="ManageDefaultCurrency">
                                        @if (Model.Currencys != null)
                                        {
                                            foreach (var item in Model.Currencys)
                                            {
                                                var nameData = JsonConvert.DeserializeObject<Dictionary<string, string>>(item.Name);
                                                var name = nameData.TryGetValue("zh-cn", out var value) ? value : "";

                                                if (Convert.ToBoolean(item.ManageDefault))
                                                {
                                                    <option value="@item.CId" selected>
                                                        @(name) (@(item.Currency)/@(item.Symbol))
                                                    </option>
                                                }
                                                else
                                                {
                                                    <option value="@item.CId">
                                                        @(name) (@(item.Currency)/@(item.Symbol))
                                                    </option>
                                                }
                                            }
                                        }


                                        @*   <option value="1" >元 (EUR/€)</option>
                                        <option value="2" selected="">欧元 (EUR/€)</option> *@
                                    </select>
                                </div>
                            </div>
                            <div class="select_tips">切换后台默认货币仅会切换货币单位，不会进行汇率换算，请切换后检查并调整相关价格。</div>
                        </div>
                        <div class="rows clean">
                            <label>前台默认货币</label>
                            <div class="input">
                                <div class="box_select">
                                    <select class="box_input" name="DefaultCurrency">
                                        @if (Model.Currencys != null)
                                        {
                                            foreach (var item in Model.Currencys)
                                            {

                                                var nameData = JsonConvert.DeserializeObject<Dictionary<string, string>>(item.Name);
                                                var name = nameData.TryGetValue("zh-cn", out var value) ? value : "";

                                                if (Convert.ToBoolean(item.IsDefault))
                                                {
                                                    <option value="@item.CId" selected>
                                                        @(name) (@(item.Currency)/@(item.Symbol))
                                                    </option>
                                                }
                                                else
                                                {
                                                    <option value="@item.CId">
                                                        @(name) (@(item.Currency)/@(item.Symbol))
                                                    </option>
                                                }
                                            }
                                        }

                                        @*     <option value="1" >元 (EUR/€)</option>
                                        <option value="2" selected="">欧元 (EUR/€)</option> *@
                                    </select>
                                </div>
                                <div class="blank10"></div>
                                <span class="input_checkbox_box ">
                                    <span class="input_checkbox">
                                        <input type="checkbox" name="AutoIpAddressCurrency" value="1">
                                    </span>
                                    自动根据客户的IP地址展示对应国家/地区的货币；若无适用货币，则展示前台默认货币
                                </span>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="global_container contact" id="company_info" name="company_info">
                    <div class="config_top_title">
                        <span class="return">联系方式</span>
                        <div class="config_sub_title">用于订单打印信息和店铺页尾Contact信息</div>
                    </div>
                    <div class="rows">
                        <label>公司名称</label>
                        <div class="input">
                            <input type="text" class="box_input full_input" name="Compeny" value="@Model.Configs.Where(c => c.GroupId == "print" && c.Variable == "Compeny").FirstOrDefault()?.Value" maxlength="255">
                        </div>
                    </div>

                    <!-- 邮箱 -->
                    <div class="rows">
                        <label id="admin_email">店主邮箱</label>
                        <div class="input">
                            <input type="text" class="box_input full_input" name="AdminEmail" value="@Model.Configs.Where(c => c.GroupId == "global" && c.Variable == "AdminEmail").FirstOrDefault()?.Value" size="50" maxlength="255">
                            <div class="global_app_tips">
                                <em></em>如需更改商家通知的接收邮箱，请前往
                                <a href="/manage/set/email/recipients" target="_blank">邮件通知&amp;设置</a> 管理收件人
                            </div>
                        </div>
                    </div>

                    <div class="rows">
                        <label>公司地址</label>
                        <div class="input">
                            <input type="text" class="box_input full_input" name="Address" value="@Model.Configs.Where(c => c.GroupId == "print" && c.Variable == "Address").FirstOrDefault()?.Value" maxlength="255">
                        </div>
                    </div>
                    <div class="rows fl half_rows">
                        <label>城市</label>
                        <div class="input">
                            <input type="text" class="box_input full_input" name="City" value="@Model.Configs.Where(c => c.GroupId == "print" && c.Variable == "City").FirstOrDefault()?.Value" maxlength="255">
                        </div>
                        <div class="clear"></div>
                    </div>
                    <div class="rows fr half_rows clean">
                        <label>州/省/地区</label>
                        <div class="input">
                            <input type="text" class="box_input full_input" name="State" value="@Model.Configs.Where(c => c.GroupId == "print" && c.Variable == "State").FirstOrDefault()?.Value" maxlength="255">
                        </div>
                        <div class="clear"></div>
                    </div>
                    <div class="rows fl half_rows">
                        <label>邮政编码</label>
                        <div class="input">
                            <input type="text" class="box_input full_input" name="ZipCode" value="@Model.Configs.Where(c => c.GroupId == "print" && c.Variable == "ZipCode").FirstOrDefault()?.Value" maxlength="255">
                        </div>
                        <div class="clear"></div>
                    </div>
                    <div class="rows fr half_rows clean">
                        <label>电话</label>
                        <div class="input">
                            <input type="text" class="box_input full_input" name="Telephone" value="@Model.Configs.Where(c => c.GroupId == "print" && c.Variable == "Telephone").FirstOrDefault()?.Value" maxlength="255">
                        </div>
                        <div class="clear"></div>
                    </div>
                    <div class="clear"></div>
                </div>


                <div class="global_container config_table_body config_table_body_set review">
                    <div class="config_top_title"><span class="return">产品评论</span></div>
                    <div class="rows fl half_rows">
                        <label>发表评论</label>
                        <div class="box_type_menu">
                            <span class="item @(Model.configReview.range == 0 ? "checked" : "")">
                                <input type="radio" name="range" value="0" @(Model.configReview.range == 0 ? "checked" : "")>所有客户
                            </span>
                            <span class="item @(Model.configReview.range == 1 ? "checked" : "")">
                                <input type="radio" name="range" value="1" @(Model.configReview.range == 1 ? "checked" : "")>已完成订单的客户
                            </span>
                        </div>
                    </div>
                    <div class="rows fl half_rows">
                        <label>发布方式</label>
                        <div class="box_type_menu">
                            <span class="item @(Model.configReview.display == 0 ? "checked" : "")">
                                <input type="radio" name="display" value="0" @(Model.configReview.display == 0 ? "checked" : "")>自动发布
                            </span>
                            <span class="item @(Model.configReview.display == 1 ? "checked" : "")">
                                <input type="radio" name="display" value="1" @(Model.configReview.display == 1 ? "checked" : "")>手动发布
                            </span>
                        </div>
                    </div>
                    <div class="clear"></div>
                </div>
                <input type="hidden" name="do_action" value="/api/SettingBasis/EditConfigs">
            </form>
        </div>
    </div>
    <div id="fixed_right">
        <div class="global_container fixed_add_currency" data-width="380">
            <div class="top_title"><span class="add">添加货币</span><span class="edit">修改货币</span> <a href="javascript:;" class="close"></a></div>
            <div class="global_form">
                <div class="rows clean box_currency">
                    <label>货币</label>
                    <div class="input"></div>
                </div>
                <div class="rows clean box_currency_hide">
                    <label>货币</label>
                    <div class="input">
                        <input type="text" class="box_input full_input" name="CurrencyHide" value="" readonly="" size="150" autocomplete="off" notnull="">
                    </div>
                </div>
                <div class="rows clean box_rate">
                    <label>汇率</label>
                    <div class="input">
                        <span class="usd_text usd_only">1 USD</span>
                        <span class="usd_text usd_compare">1 USD = </span>
                        <div class="box_exchange_rate">
                            <span class="unit_input">
                                <input type="text" class="box_input right_radius" name="ExchangeRate" value="" size="5" maxlength="10" rel="amount">							<b class="last"></b>
                            </span>
                        </div>
                    </div>
                </div>
                <div class="rows clean box_payment_method">
                    <label>该货币支持的支付方式：</label>
                    <ul></ul>
                </div>
                <div class="rows clean box_button box_submit">
                    <div class="input">
                        <input type="button" class="btn_global btn_submit" value="添加">
                        <input type="button" class="btn_global btn_cancel" value="取消">
                    </div>
                </div>
                <input type="hidden" name="id" value="0">
            </div>
        </div>
    </div>
    <div class="rows fixed_btn_submit" style="width: 1527px; left: 180px;">
        <div class="center_container">
            <div class="input input_button">
                <input type="button" class="btn_global btn_submit" form="edit_form" value="保存">
                <a href="/Setting/Index" title="返回">
                    <input type="button" class="btn_global btn_cancel" value="返回">
                </a>
            </div>
        </div>
    </div>


</div>
