@model YseStoreAdmin.Pages.Components.Setting.SettingLanguage
@{
}
<div>
    <div id="config" class="r_con_wrap" style="height: 378px;">
        <div class="center_container language">
            <div id="language">
                <div class="bread_nav">
                    <a href="/Setting/Index" class="return_title o_return_title">设置</a>
                    <h1>
                        店铺语言
                    </h1>
                </div>
                <div class="clear"></div>
                <form id="edit_form" class="global_form" action="/Setting/Language" method="post">

                    <div class="global_container">
                        <div class="config_top_title">
                            <span class="return">后台语言</span>
                        </div>
                        <div class="rows clean">
                            <div class="input">
                                <div class="box_select">
                                    <select class="box_input" name="ManageLanguage">
                                        <option value="zh-cn" selected="">简体中文</option>
                                        @* <option value="en">英文</option> *@

                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="global_container">
                        <div class="config_top_title">
                            <span class="return">前台语言</span>
                            <ul class="list_menu_button">
                                <li><a class="add" href="javascript:;">添加</a></li>
                            </ul>

                        </div>
                        <div class="rows clean box_background">
                            <label>
                                默认语言
                                @* <a class="g_link lang_jump_btn" href="/manage/set/language/langpack/">修改语言内容</a> *@
                            </label>
                            <div class="input">
                                <div class="box_select">
                                    <select class="box_input" name="DefaultLanguage">
                                        @foreach (var item in Model.DefalutLanguages)
                                        {
                                            @if (Model.webLanguage?.Language == item.Language)
                                            {
                                                <option value="@item.Language" selected>@T[$"language.{item.Language}"]</option>
                                            }
                                            else
                                            {
                                                <option value="@item.Language">@T[$"language.{item.Language}"]</option>
                                            }

                                        }

                                    </select>
                                </div>
                            </div>
                        </div>
                        <input type="hidden" name="do_action" value="/manage/set/language/edit">
                        <div class="rows clean">
                            <div class="language_padding">
                                <div class="switch_box">
                                    <div class="switch_item">
                                        <div class="btn_checkbox IsBrowerChange @(Model.IsBrowerChange == "1" ? "current" : "") fl ">
                                            <em class="button"></em>
                                            <input type="checkbox" name="IsBrowerChange" @(Model.IsBrowerChange == "1" ? "checked" : "") value="1">
                                        </div>
                                        <div class="content fl">
                                            <div class="content_item_title">根据浏览器语言自动切换</div>
                                            <div class="content_item_tips">如果无法切换成浏览器语言，将自动展示默认语言</div>
                                        </div>
                                        <div class="clear"></div>

                                    </div>
                                </div>
                            </div>
                            <div class="language_box">
                                <div class="inside_table">
                                    @if (Model.LanguageRow.Any())
                                    {
                                        <table border="0" cellpadding="5" cellspacing="0" class="r_con_table new">
                                            <thead>
                                                <tr>
                                                    <td width="25%" nowrap="nowrap">其他语言</td>
                                                    <td width="25%" nowrap="nowrap">方式</td>
                                                    <td>状态</td>
                                                    <td width="100" nowrap="nowrap" class="operation"></td>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                @foreach (var item in Model.LanguageRow)
                                                {
                                                    <tr data-lid="@item.LId">
                                                        <td>@T[$"translate.{item.Language}_{Model.LangKey}"]</td>
                                                        <td>@(item.Type == "translate" ? "翻译" : item.Type == "url" ? "跳转" : "")</td>
                                                        <td>
                                                            @if (item.Status == true)
                                                            {
                                                                <span class="status status1">已发布</span>
                                                            }
                                                            else
                                                            {
                                                                <span class="status status0">未发布</span>
                                                            }

                                                        </td>
                                                        <td nowrap="nowrap" class="operation tar">
                                                            @if (item.Status == false)
                                                            {
                                                                <a class="oper_icon icon_publish publish button_tips" href="javascript:;">发布</a>
                                                            }
                                                            <a class="oper_icon icon_edit button_tips" href="javascript:;">修改</a>
                                                            <a class="oper_icon icon_del del button_tips" href="/manage/set/language/del?LId=@item.LId">删除</a>

                                                        </td>
                                                    </tr>
                                                }

                                            </tbody>
                                        </table>
                                    }
                                    else
                                    {
                                        <div class="bg_no_table_data" style="height: 200px;"><div class="content" style="top: 48px;"><p>当前暂时没有数据</p></div><span></span></div>
                                    }

                                </div>
                            </div>
                        </div>
                    </div>



                </form>
            </div>
        </div>
        <div id="fixed_right">
            <div class="global_container fixed_language" data-width="350">
                <div class="top_title"><span>添加其他语言</span> <a href="javascript:;" class="close"></a></div>
                <form id="add_form" class="global_form" action="/Setting/Language" method="post">

                    <div class="rows clean language_select">
                        <label>语言</label>
                        <div class="input">
                            <div class="box_select"></div>
                        </div>
                    </div>
                    <div class="rows clean type_box">
                        <label>方式</label>
                        <div class="input">
                            <span class="input_radio_box input_radio_side_box tab_option">
                                <span class="input_radio">
                                    <input type="radio" name="Type" value="translate">
                                </span>
                                <strong class="fs14">翻译</strong>
                                <p class="fs12">前端语言切换时，自动使用Google翻译</p>
                            </span>
                            <span class="input_radio_box input_radio_side_box tab_option">
                                <span class="input_radio">
                                    <input type="radio" name="Type" value="url">
                                </span>
                                <strong class="fs14">跳转</strong>
                                <p class="fs12">前端语言切换时，跳转到对应的url</p>
                            </span>
                        </div>
                    </div>
                    <div class="type_box_tab">
                        <div class="rows clean app_box" data-type="translate">
                            <div class="app_select hide"><input type="radio" name="App" value="translate"></div>
                        </div>
                        <div class="rows clean url_box" data-type="url">
                            <label>URL</label>
                            <div class="input">
                                <textarea class="box_textarea" name="Url" notnull="notnull"></textarea>
                            </div>
                        </div>
                    </div>
                    <div class="rows clean box_button box_submit">
                        <div class="input">
                            <input type="submit" class="btn_global btn_submit" value="提交">
                            <input type="button" class="btn_global btn_cancel" value="取消">
                        </div>
                    </div>
                    <input type="hidden" name="LId">
                    <input type="hidden" name="do_action" value="/manage/set/language/add">
                </form>
            </div>
            <div class="global_container fixed_language_view" data-width="350">
                <div class="top_title"><span></span> <a href="javascript:;" class="close"></a></div>
                <form id="add_form" class="global_form" action="/Setting/Language" method="post">

                    <div class="rows clean type_box">
                        <label>方式</label>
                        <div class="input">
                            <span class="input_radio_box input_radio_side_box tab_option checked">
                                <span class="input_radio">
                                    <input type="radio" name="Type" value="url" 0="checked">
                                </span>
                                <strong class="fs14">跳转</strong>
                                <p class="fs12">前端语言切换时，跳转到对应的url</p>
                            </span>
                        </div>
                    </div>
                    <div class="type_box_tab">
                        <div class="rows clean url_box" data-type="url">
                            <label>URL</label>
                            <div class="input"></div>
                        </div>
                    </div>
                    <div class="rows clean box_button box_submit">
                        <div class="input">
                            <input type="button" class="btn_global btn_cancel" value="取消">
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
    <div class="rows clean fixed_btn_submit" style="width: 1860px; left: 180px;">
        <div class="center_container">
            <div class="input input_button">
                <input type="submit" class="btn_global btn_submit" name="submit_button" value="保存">
                <a href="/Setting/Index"><input type="button" class="btn_global btn_cancel" value="返回"></a>
            </div>
        </div>
    </div>

</div>