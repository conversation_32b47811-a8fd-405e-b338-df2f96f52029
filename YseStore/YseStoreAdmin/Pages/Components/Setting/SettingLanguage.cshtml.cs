using Entitys;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using YseStore.IService;
using YseStore.IService.SiteSystem;
using YseStore.Model.VM;
using YseStore.Service;

namespace YseStoreAdmin.Pages.Components.Setting
{
    /// <summary>
    /// 语言设置
    /// </summary>
    public class SettingLanguage : MComponent
    {


        private readonly ILanguageServices _languageServices;
        private readonly IConfigService _configService;

        public SettingLanguage(ILanguageServices languageServices, IConfigService configService)
        {
            _languageServices = languageServices;
            _configService = configService;
        }

        public override async Task MountAsync()
        {
            await base.MountAsync();

            await GetData();
        }


        public IList<VM_Assignment> Assignment { get; set; }

        /// <summary>
        /// 所有语言
        /// </summary>
        public List<language> Languages { get; set; } = new List<language>();

        /// <summary>
        /// 默认语言
        /// </summary>
        public List<language> DefalutLanguages { get; set; } = new List<language>();
        /// <summary>
        /// 获取开启语言列表
        /// </summary>
        public List<language> LanguageRow { get; set; } = new List<language>();

        /// <summary>
        /// 前台默认语言
        /// </summary>
        public language webLanguage { get; set; } = new language();

        public int LangKey { get; set; } = 0;

        //浏览器语言自动切换
        public string IsBrowerChange { get; set; } = "0";
        /// <summary>
        /// 获取数据
        /// </summary>
        /// <returns></returns>
        public async Task GetData()
        {
            //所有语言
            Languages = await _languageServices.GetAllLangsCache();

            //获取开启语言列表
            LanguageRow = await _languageServices.GetUsedLangsCache();
            LanguageRow.RemoveAll(it => it.Type == "default");

            //默认语言
            DefalutLanguages = Languages.Where(it => it.Type == "default").ToList();

            webLanguage = await _languageServices.GetDefaultLang();

            // 后台显示语言
            var ManageLanguage = await _configService.GetConfigValueByGroup("global", "ManageLanguage");
            if (ManageLanguage == "en")
            {
                LangKey = 1;
            }

            IsBrowerChange = await _configService.GetConfigValueByGroup("global", "IsBrowerChange");


        }


    }
}
