using Entitys;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Newtonsoft.Json.Linq;
using YseStore.Common;
using YseStore.Common.Helper;
using YseStore.IService;
using YseStore.IService.Order;
using YseStore.IService.Sales;
using YseStore.IService.Set;
using YseStore.IService.SiteSystem;
using YseStore.Model.Enums;
using YseStore.Model.Response.Set;
using YseStore.Model.VM.Common;
using YseStore.Model.VM.Sales;

namespace YseStoreAdmin.Pages.Components.Setting
{
    public class Shopping : MComponent
    {
        private IConfigService _configService { get; set; }
        private ICurrencyService _currencyService { get; set; }
        private ICountryService _countryService { get; set; }
        private ILanguageServices _languageServices { get; set; }

        private IAppConfigService _appConfigService { get; set; }

        private readonly ISalesCouponService _salesCouponService;
        private readonly IShippingAreaService _shippingAreaService;

        public Shopping(IConfigService configService, ICurrencyService currencyService, ICountryService countryService, ILanguageServices languageServices, IAppConfigService appConfigService, ISalesCouponService salesCouponService, IShippingAreaService shippingAreaService)
        {
            _configService = configService;
            _currencyService = currencyService;
            _countryService = countryService;
            _languageServices = languageServices;
            _appConfigService = appConfigService;
            _salesCouponService = salesCouponService;
            _shippingAreaService = shippingAreaService;
        }


        public override async Task MountAsync()
        {

            //获取详情数据
            await GetInfo();

        }


        /// <summary>
        /// 详细数据
        /// </summary>
        public Dictionary<string, object> configAry { get; set; } = new Dictionary<string, object>();

        /// <summary>
        /// 
        /// </summary>
        public List<string> globalParam { get; set; } = new List<string> { "hide", "optional", "required" };

        public List<string> applicableAry { get; set; } = new List<string> { "all", "products", "category" };


        /// <summary>
        /// 后台币种
        /// </summary>
        public currency manageCurrendy { get; set; } = new currency();

        public language web_lang { get; set; } = new language();

        /// <summary>
        /// 地址附加字段
        /// </summary>
        public List<address_expand> addressesExpandList { get; set; } = new List<address_expand>();

        /// <summary>
        /// 启用的国家列表
        /// </summary>
        public List<country> countryList { get; set; } = new List<country>();

        /// <summary>
        /// 订单召回
        /// </summary>
        public JObject OrdersRecallObj { get; set; } = new JObject();

        /// <summary>
        /// 订单自动任务
        /// </summary>
        public JObject OrderAutomationObj { get; set; } = new JObject();

        public JObject acronymCountryAry { get; set; } = new JObject();
        public Dictionary<byte, List<JObject>> ContinentCountryAry { get; set; } = new Dictionary<byte, List<JObject>>();

        /// <summary>
        /// 适用产品
        /// </summary>
        public JObject forConsultationOnly { get; set; } = new JObject();

        /// <summary>
        /// 
        /// </summary>
        public string ProductHtml { get; set; } = string.Empty;


        public string CategoryHtml { get; set; } = string.Empty;
        /// <summary>
        /// 产品分类下拉框数据
        /// </summary>
        public List<SelectItem<int>> ProductCategorySelected { get; set; } = new List<SelectItem<int>>();

        /// <summary>
        /// 币种
        /// </summary>
        public string currencySymbol { get; set; } = AppSettingsConstVars.CurrentDefaultSymbol;


        /// <summary>
        /// 
        /// </summary>
        public ContinentShippingAreaResponse ShippingAreaResponse { get; set; }


        /// <summary>
        /// 
        /// </summary>
        public JObject add_to_cart { get; set; } = new JObject();


        /// <summary>
        /// 获取详情
        /// </summary>
        /// <returns></returns>
        public async Task GetInfo()
        {
            var configList = await _configService.GetConfig();
            configList = configList.Where(it => it.GroupId == "global").ToList();
            foreach (var config in configList)
            {
                configAry[config.Variable] = config.Value;
            }


            configAry["TouristsShopping"] = await _configService.GetConfigValueByGroup("global", "TouristsShopping");
            configAry["PricesOnlyVisibleMembers"] = await _configService.GetConfigValueByGroup("global", "PricesOnlyVisibleMembers");
            configAry["ShopCartCheck"] = await _configService.GetConfigValueByGroup("global", "ShopCartCheck");
            configAry["BuyLogo"] = await _configService.GetConfigValueByGroup("global", "BuyLogo");
            configAry["OrderPrefix"] = await _configService.GetConfigValueByGroup("global", "OrderPrefix");
            configAry["CustomScript"] = await _configService.GetConfigValueByGroup("global", "CustomScript");
            configAry["CustomerRemarks"] = await _configService.GetConfigValueByGroup("global", "CustomerRemarks");

            configAry["WeightDisplay"] = await _configService.GetConfigValueByGroup("global", "WeightDisplay");
            configAry["OrdersRecall"] = await _configService.GetConfigValueByGroup("global", "OrdersRecall");
            configAry["CheckoutMode"] = await _configService.GetConfigValueByGroup("global", "CheckoutMode");

            configAry["DraftsVersion"] = await _configService.GetConfigValueByGroup("global", "DraftsVersion");


            var OrdersRecall = await _configService.GetConfigValueByGroup("global", "OrdersRecall");//订单召回


            if (!OrdersRecall.IsNullOrEmpty())
            {
                OrdersRecallObj = OrdersRecall.JsonToObj<JObject>();
            }
            else
            {
                OrdersRecallObj.Add("HowLong", 10 * 60 * 60);
                OrdersRecallObj.Add("UsedOffer", 0);

                OrdersRecallObj.Add("OfferType", 0);
                OrdersRecallObj.Add("Offer", 0);
                OrdersRecallObj.Add("OfferPrice", 0);
            }


            var OrderAutomation = await _configService.GetConfigValueByGroup("global", "OrderAutomation");//自动订单

            if (!OrderAutomation.IsNullOrEmpty())
            {
                OrderAutomationObj = OrderAutomation.JsonToObj<JObject>();
            }
            else
            {
                OrderAutomationObj.Add("cancel", new JObject
                {
                    ["IsUsed"] = 1,
                    ["Data"] = new JObject
                    {
                        ["HowLong"] = 720,
                        ["Unit"] = "hour",
                        ["Custom"] = 24,
                    },

                });

                OrderAutomationObj.Add("complete", new JObject
                {
                    ["IsUsed"] = 0,
                    ["Data"] = new JObject
                    {
                        ["Custom"] = 30,
                    },
                });
            }

            //后台币种
            manageCurrendy = await _currencyService.GetManageDefaultCurrency();

            //地址附加字段
            addressesExpandList = await _countryService.GetAddressExpandAsync();

            //启用的国家
            countryList = await _countryService.GetUsedCountriesAsync();

            var ManageLanguage = await _configService.GetConfigValueByGroup("global", "ManageLanguage");

            //国家数据
            foreach (var cty in countryList)
            {
                var ctyData = cty.CountryData.JsonToObj<Dictionary<string, string>>();

                string countryName = cty.Country;
                if (ctyData != null && ctyData.ContainsKey("zh-cn"))
                {
                    countryName = ctyData["zh-cn"];
                }

                JObject obj = new JObject
                {
                    ["CId"] = cty.CId,
                    ["Country"] = cty.Country,
                    ["CountryData"] = cty.CountryData,
                    ["Continent"] = cty.Continent,
                    ["Acronym"] = cty.Acronym,
                    ["FlagPath"] = cty.FlagPath,
                    ["Name"] = ManageLanguage == "zh-cn" ? countryName : cty.Country
                };

                acronymCountryAry.Add(cty.Acronym, obj);

                if (ContinentCountryAry.ContainsKey(cty.Continent.Value))
                {
                    ContinentCountryAry[cty.Continent.Value].Add(obj);
                }
                else
                {
                    ContinentCountryAry[cty.Continent.Value] = new List<JObject> { obj };
                }
            }

            //默认语言
            web_lang = await _languageServices.GetDefaultLang();


            #region 适用产品

            //适用产品
            var appValue = await _appConfigService.GetAppConfigValueByClassName("configForConsultationOnlyData");
            if (!appValue.IsNullOrEmpty())
            {
                forConsultationOnly = appValue.JsonToObj<JObject>();
            }

            //产品下拉数据
            List<Dictionary<string, object>> boxDropDoubleSelectItems = new List<Dictionary<string, object>>();
            List<Dictionary<string, object>> boxDropDoubleItems = new List<Dictionary<string, object>>();
            var productArr = await _salesCouponService.GetProductsRowAsync("products");
            boxDropDoubleItems = productArr.Select(c => new Dictionary<string, object>()
            {
                { "Name", c.Name },
                { "Value", c.Value.ToString() },
                { "Type", c.Type },
                { "Icon", $"<em class='icon icon_products pic_box'><img src='{c.Icon}' /><span></span></em>" }

            }).ToList();

            if (forConsultationOnly != null)
            {

                var useProductsstrValue = new List<string>();
                if (forConsultationOnly.ContainsKey("ProductsCurrent") && !string.IsNullOrEmpty(forConsultationOnly["ProductsCurrent"].ToString()))
                {

                    useProductsstrValue = forConsultationOnly["ProductsCurrent"].ToString().JsonToObj<List<string>>();
                }

                var useProductsCateValue = new List<int>();
                if (forConsultationOnly.ContainsKey("ProductsCategoryCurrent") && !string.IsNullOrEmpty(forConsultationOnly["ProductsCategoryCurrent"].ToString()))
                {
                    useProductsCateValue = forConsultationOnly["ProductsCategoryCurrent"].ToString().JsonToObj<List<int>>();
                }

                if (forConsultationOnly["ApplicableProducts"] != null)
                {
                    if (forConsultationOnly["ApplicableProducts"].ToString() == "products")
                    {
                        boxDropDoubleSelectItems = boxDropDoubleItems.Where(c => useProductsstrValue.Contains(c["Value"])).ToList();
                    }

                    if (forConsultationOnly["ApplicableProducts"].ToString() == "category")
                    {
                        ProductCategorySelected = await _salesCouponService.GetCategoryCheckedAsync(useProductsCateValue);

                    }
                }

                // 生成HTML
                ProductHtml = HtmlCategoryBuilder.BoxDropDouble("Apply", "ApplyValue", boxDropDoubleItems, boxDropDoubleSelectItems, 0, isCheckbox: true, isMore: true);


                //分类
                var catelist = await _salesCouponService.GetCategoryTreeAsync();
                if (catelist != null && catelist.Count > 0)
                {
                    CategoryHtml = HtmlCategoryBuilder.BuildCategoryHtml(catelist);
                }

                #endregion



                #region 购物车悬浮

                var addToCartJson = await _configService.GetConfigValueByGroup("products_show", "add_to_cart");
                if (!addToCartJson.IsNullOrEmpty())
                {
                    add_to_cart = addToCartJson.JsonToObj<JObject>();
                }
                else
                {
                    add_to_cart = new JObject
                    {
                        ["AddToCartFlow"]="1",
                        ["FloatingButton"] = new JArray("add_to_cart", "buy_now"),
                        ["Pc"] = "1",
                        ["PcPosition"] = "top",
                        ["Mobile"] = "1",
                        ["MobilePosition"] = "down"
                    };
                }



                #endregion

            }


            var manageCurrency = await _currencyService.GetManageDefaultCurrency();
            if (manageCurrency != null)
            {
                currencySymbol = manageCurrency.Symbol;
            }


            // 首先获取配送区域数据，确保在使用前已初始化
            ShippingAreaResponse = await _shippingAreaService.GetShippingAreaAsync();

        }

        public Dictionary<string, string> zipPatterns { get; set; } = new Dictionary<string, string>
            {
                {"zipUS", "5位数字"},
                {"zipGB", "5~7位字母和数字"},
                {"zipJP", "7位数字"},
                {"zipAU", "4位数字"}
            };
        //
        public List<string> time_ary_value { get; set; } = new List<string>
        {
            "300","900","3600","21600","36000","86400"
        };
        public List<string> timeAryTime { get; set; } = new List<string>
        {
            "5分钟",
            "15分钟",
            "1小时",
            "6小时",
            "10小时",
            "24小时"
        };


        public List<string> cancelTimeValue { get; set; } = new List<string> { "0.5", "24", "48", "720" };
        public List<string> cancelTimeTime { get; set; } = new List<string> { "30分钟", "24小时", "48小时", "30天" };
    }
}
