@model YseStoreAdmin.Pages.Components.Setting.ShippingWarehouseSet 
@{
}
<div>
 
        <div id="shipping" class="r_con_wrap" style="height: 460px;">
            <div class="center_container_1400">
                <div class="inside_container bgc_unset inside_container_small inside_menu_right">
                    <h1>
                        <a href="/Setting/Index" class="return_title fl o_return_title">物流&amp;仓库</a>
                    </h1>
                    <div class="inside_menu">
                        <div class="inside_title"><span>仓库</span><i></i></div>
                        <div class="inside_body">
                            <ul>
                                <li><a href="/Setting/Shipping">物流</a></li>
                                <li><a href="/Setting/ShippingWarehouse" class="current">仓库</a></li>
                            </ul>
                            <div class="inside_menu_current" style="left: 87px;"></div>
                        </div>
                    </div>
                </div>
                <div class="inside_table radius">
                    <div class="warehouse_title no-flex">
                        <strong>默认仓库</strong>
                        <div class="global_app_tips"><em></em><span>隶属默认仓库的产品，产品详细页不显示仓库选项</span></div>
                    </div>
                    <table border="0" cellpadding="5" cellspacing="0" class="r_con_table warehouse_table">
                        <colgroup>
                            <col style="width:153px; min-width:153px;">
                            <col>
                            <col style="width:30%;">
                            <col style="width:20%;">
                            <col style="width:80px;">
                        </colgroup>
                        <thead>
                            <tr>
                                <td nowrap="" class="pos"></td>
                                <td nowrap="">仓库</td>
                                <td nowrap="">物流</td>
                                <td nowrap="">关联产品</td>
                                <td nowrap="" class="operation"></td>
                            </tr>
                        </thead>
                        <tbody>
                            <tr data-id="@Model.DefaultWarehouse?.Id" data-name="@Model.DefaultWarehouse?.Name">
                                <td nowrap=""></td>
                                <td nowrap="" class="name">@Model.DefaultWarehouse?.Name</td>
                                <td nowrap="" class="logistics" data-json="@Model.DefaultWarehouse?.ShippingJson">@Model.DefaultWarehouse?.ShippingNames</td>
                                <td nowrap="" class="related">所有未选择仓库的产品</td>
                                <td nowrap="" class="operation tar">
                                    <a class="icon_edit oper_icon button_tips" href="javascript:;">修改</a>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                    <div class="btn_warehouse_delete"></div>
                </div>
            <div class="inside_table customize radius">
                <div class="warehouse_title">
                    <strong>自定义仓库</strong>
                    <ul class="list_menu_button fr">
                        <li><a class="add" href="javascript:;">添加</a></li>
                    </ul>
                </div>
                <div class="r_con_table mock_table">
                    <div class="thead">
                        <div class="tr">
                            <div class="td c_select pos">
                                <ul class="table_menu_button global_menu_button left_10">
                                    <li><div class="btn_checkbox "><em class="button"></em><input type="checkbox" name="select_all" value=""></div></li>
                                    <li class="open">已选择<span></span>个</li>
                                    <li><a class="del" href="javascript:;">删除</a></li>
                                </ul>
                            </div>
                            <div class="td c_order myorder"></div>
                            <div class="td c_warehouse">仓库</div>
                            <div class="td c_logistics">物流</div>
                            <div class="td c_related">关联产品</div>
                            <div class="td operation"></div>
                        </div>
                    </div>
                    <div class="tbody" data-listidx="0">
                        @foreach (var warehouse in Model.CustomWarehouses)
                        {
                        <div class="first_box">
                            <div class="tr" data-id="@warehouse.Id" data-name="@warehouse.Name">
                                <div class="td c_select"><div class="btn_checkbox "><em class="button"></em><input type="checkbox" name="select" value="@warehouse.Id"></div></div>
                                <div class="td c_order myorder move_myorder" data="move_myorder" data-id="@warehouse.Id"><i class="icon_myorder"></i></div>
                                <div class="td c_warehouse name">@warehouse.Name</div>
                                <div class="td c_logistics logistics" data-json="@warehouse.ShippingJson">@warehouse.ShippingNames</div>
                                <div class="td c_related related">
                                    <div class="related_products" data-id="@warehouse.Id" data-page="0" data-total-page="1">
                                        <div class="products_txt">@(warehouse.ProductCount > 0 ? warehouse.ProductCount.ToString() + "个产品" : "无产品")<i></i></div>
                                        <div class="products_container" style="display: none;">
                                            <div class="products_box"></div>
                                        </div>
                                    </div>
                                </div>
                                <div class="td operation tar">
                                    <a class="icon_edit oper_icon button_tips" href="javascript:;">修改</a>
                                </div>
                            </div>
                        </div>
                        }
                    </div>
                </div>
                <div class="btn_warehouse_delete"></div>
            </div>
                <div id="fixed_right">
                    <div class="global_container warehouse_add_box" data-width="350">
                        <div class="top_title">
                            <span class="add">添加仓库</span>
                            <span class="edit">修改仓库</span>
                            <a href="javascript:;" class="close"></a>
                        </div>
                        <form id="warehouse_edit" class="global_form" action="/manage/set/shipping/warehouse" method="post">
                            <input type="hidden" name="_csrf-manage" value="v3lPCEaRHm0ryVGR8IuAtVVaQLsZwgrz26M3A4uoQjHHMyYlKstPN0mBK9mmvvPEMDZw03KyeaWflXMxuuEXUw==">                    <div class="rows clean">
                                <label>仓库</label>
                                <div class="input">
                                    <input type="text" class="box_input full_input" name="Name" size="150" autocomplete="off" placeholder="输入仓库名称" notnull="">
                                </div>
                            </div>
                            <div class="rows clean">
                                <label>物流</label>
                                <div class="input logistics_list">
                                    @foreach (var shipping in Model.AllShippings)
                                    {
                                    <span class="input_checkbox_box">
                                        <span class="input_checkbox"><input type="checkbox" name="SId[]" value="@shipping.SId"></span>
                                        @shipping.Express
                                    </span>
                                    }
                                </div>
                            </div>
                            <div class="rows clean box_submit">
                                <label></label>
                                <div class="input input_button">
                                    <input type="submit" class="btn_global btn_submit" value="保存">                            <input type="button" class="btn_global btn_cancel" value="取消">
                                </div>
                            </div>
                            <input type="hidden" name="id" value="0">                    <input type="hidden" name="do_action" value="/manage/set/shipping/warehouse-update">
                        </form>
                    </div>
                    <div class="global_container warehouse_reset_box" data-width="336">
                        <div id="box_circle_container">
                            <div class="box_circle_progress" data-percent="0" data-animate="0">
                                <div class="wrapper circle_progress_right">
                                    <div class="circle_progress rightcircle"></div>
                                </div>
                                <div class="wrapper circle_progress_left">
                                    <div class="circle_progress leftcircle"></div>
                                </div>
                                <div class="circle_progress_number"><span>0</span>%</div>
                                <div class="circle_progress_completed"></div>
                            </div>
                            <div class="circle_progress_text"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
   
    <script type="text/javascript">
        // 使用Fetch API和XMLHttpRequest拦截仓库相关请求
        (function() {
            // 保存原始的fetch方法
            const originalFetch = window.fetch;
            
            // 重写fetch方法
            window.fetch = function(url, options) {
                // 调用原始fetch方法
                return originalFetch(url, options).then(response => {
                    // 检查是否是仓库相关接口的请求
                    if (typeof url === 'string' && 
                        (url.includes('/manage/set/shipping/warehouse-delete') || 
                         url.includes('/manage/set/shipping/warehouse-update'))) {
                        
                        // 克隆响应，因为response只能被使用一次
                        const clonedResponse = response.clone();
                        
                        // 处理响应
                        clonedResponse.json().then(data => {
                            // 如果操作成功，刷新页面
                            if (data && data.ret === 1) {
                                window.location.href = '/Setting/ShippingWarehouse';
                            } else if (data && data.ret > 1) {
                                // 对于批量删除，当ret>1且percent=100时表示完成
                                if (data.msg && data.msg.percent === 100) {
                                    window.location.href = '/Setting/ShippingWarehouse';
                                }
                            }
                        }).catch(error => {
                            console.error('解析响应失败:', error);
                            // 如果解析失败，也刷新页面（可能是因为返回的不是JSON格式）
                            window.location.href = '/Setting/ShippingWarehouse';
                        });
                    }
                    
                    // 返回原始响应
                    return response;
                });
            };
            
            // 监听XMLHttpRequest
            const originalXhrOpen = XMLHttpRequest.prototype.open;
            const originalXhrSend = XMLHttpRequest.prototype.send;
            
            XMLHttpRequest.prototype.open = function(method, url, async, user, password) {
                // 保存URL以便在send中使用
                this._url = url;
                return originalXhrOpen.apply(this, arguments);
            };
            
            XMLHttpRequest.prototype.send = function(body) {
                if (this._url && 
                    (this._url.includes('/manage/set/shipping/warehouse-delete') || 
                     this._url.includes('/manage/set/shipping/warehouse-update'))) {
                    
                    // 添加响应处理程序
                    this.addEventListener('load', function() {
                        if (this.status >= 200 && this.status < 300) {
                            try {
                                const data = JSON.parse(this.responseText);
                                if (data && data.ret === 1) {
                                    window.location.href = '/Setting/ShippingWarehouse';
                                } else if (data && data.ret > 1) {
                                    // 对于批量删除，当ret>1且percent=100时表示完成
                                    if (data.msg && data.msg.percent === 100) {
                                        window.location.href = '/Setting/ShippingWarehouse';
                                    }
                                }
                            } catch (e) {
                                // 如果不是JSON格式，直接刷新页面
                                window.location.href = '/Setting/ShippingWarehouse';
                            }
                        }
                    });
                }
                
                return originalXhrSend.apply(this, arguments);
            };
        })();
    </script>
</div>