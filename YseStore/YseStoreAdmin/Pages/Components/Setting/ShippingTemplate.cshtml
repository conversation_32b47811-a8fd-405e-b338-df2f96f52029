@model YseStoreAdmin.Pages.Components.Setting.ShippingTemplate 
@{
}
<div>
    <div id="shipping_template" class="r_con_wrap plugins_app_box" style="height: 469px;">
        <div class="return_title"><a href="/Setting/Shipping"> <span class="return">物流&仓库</span></a></div>
        <div class="inside_container inside_menu_right clean">
            <h1>运费模板</h1>
        </div>
        <div class="inside_table radius">
            <div class="template_title">默认运费模板</div>
            <table border="0" cellpadding="5" cellspacing="0" class="r_con_table">
                <thead>
                    <tr>
                        <td width="10%" nowrap="nowrap" class="pos"></td>
                        <td width="20%" nowrap="nowrap">模板名称</td>
                        <td width="" nowrap="nowrap">物流</td>
                        <td width="115" nowrap="nowrap">产品</td>
                        <td width="115" nowrap="nowrap" class="operation">操作</td>
                    </tr>
                </thead>
                <tbody>
                    @if (Model.DefaultTemplate != null)
                    {
                        <tr tid="@Model.DefaultTemplate.TId">
                            <td nowrap="nowrap"></td>
                            <td nowrap="nowrap">@Model.DefaultTemplate.Name</td>
                            <td nowrap="nowrap">
                                @if (Model.TemplateShippingMethods.ContainsKey(Model.DefaultTemplate.TId) && 
                                    Model.TemplateShippingMethods[Model.DefaultTemplate.TId].Any())
                                {
                                    @Html.Raw(string.Join("<br>", Model.TemplateShippingMethods[Model.DefaultTemplate.TId]))
                                }
                            </td>
                            <td nowrap="nowrap">
                                @(Model.TemplateProductCounts.ContainsKey(Model.DefaultTemplate.TId) ? 
                                    Model.TemplateProductCounts[Model.DefaultTemplate.TId] : 0)件
                            </td>
                            <td nowrap="nowrap" class="operation side_by_side">
                                <a class="icon_edit oper_icon button_tips"
                                    href="/Setting/ShippingTemplateEdit?id=default">修改</a>
                            </td>
                        </tr>
                    }
                    else
                    {
                        <tr>
                            <td colspan="5" nowrap="nowrap" class="text-center">暂无默认运费模板</td>
                        </tr>
                    }
                </tbody>
            </table>
        </div>
        <div class="inside_table customize radius">
            <div class="template_title">
                <ul class="list_menu_button fr">
                    <li><a class="add" href="/Setting/ShippingTemplateEdit?id=0">添加</a></li>
                </ul>
                自定义运费模板
            </div>
            <table border="0" cellpadding="5" cellspacing="0" class="r_con_table">
                <thead>
                    <tr>
                        <td width="10%" nowrap="nowrap" class="pos">
                            <ul class="table_menu_button global_menu_button">
                                <li><div class="btn_checkbox "><em class="button"></em><input type="checkbox" name="select_all" value=""></div></li>
                                <li class="open">已选择<span></span>个</li>
                                <li><a class="del" href="javascript:;">删除</a></li>
                            </ul>
                        </td>
                        <td width="20%" nowrap="nowrap">模板名称</td>
                        <td width="" nowrap="nowrap">物流</td>
                        <td width="115" nowrap="nowrap">产品</td>
                        <td width="115" nowrap="nowrap" class="operation">操作</td>
                    </tr>
                </thead>
                <tbody>
                    @if (Model.CustomTemplates != null && Model.CustomTemplates.Any())
                    {
                        @foreach (var template in Model.CustomTemplates)
                        {
                            <tr tid="@template.TId">
                                <td nowrap="nowrap"><div class="btn_checkbox "><em class="button"></em><input type="checkbox" name="select" value="@template.TId"></div></td>
                                <td nowrap="nowrap">@template.Name</td>
                                <td nowrap="nowrap">
                                    @if (Model.TemplateShippingMethods.ContainsKey(template.TId) && 
                                        Model.TemplateShippingMethods[template.TId].Any())
                                    {
                                        @Html.Raw(string.Join("<br>", Model.TemplateShippingMethods[template.TId]))
                                    }
                                </td>
                                <td nowrap="nowrap">
                                    @(Model.TemplateProductCounts.ContainsKey(template.TId) ? 
                                        Model.TemplateProductCounts[template.TId] : 0)件
                                </td>
                                <td nowrap="nowrap" class="operation side_by_side">
                                    <a class="icon_edit oper_icon button_tips" href="/Setting/ShippingTemplateEdit?id=@template.TId">修改</a>
                                </td>
                            </tr>
                        }
                    }
                    else
                    {
                        <tr>
                            <td colspan="5" nowrap="nowrap" class="text-center">暂无自定义运费模板</td>
                        </tr>
                    }
                </tbody>
            </table>
        </div>
    </div>
</div>