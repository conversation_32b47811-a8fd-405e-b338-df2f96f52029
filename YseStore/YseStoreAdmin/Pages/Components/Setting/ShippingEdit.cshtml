@model YseStoreAdmin.Pages.Components.Setting.ShippingEdit
@{
}
<div>

    <div id="shipping" class="r_con_wrap" style="height: 434px;">
        <div class="center_container_786">
            <div class="return_title">
                <a href="/Setting/Shipping">
                    <span class="return">设置</span>
                    <span class="return">/ 物流&amp;仓库</span>
                    <span class="s_return">/ @(Model.IsEdit ? "编辑" : "添加")</span>
                </a>
            </div>
            <form id="edit_form" class="global_form updata_form">
                <div class="global_container">
                    <div class="big_title">快递名称</div>
                    <div class="rows clean">
                        <div class="input">
                            <input type="text" name="Express" value="@Model.ShippingInfo.Express"
                                   class="box_input full_input"
                                   size="25" maxlength="100" notnull="">
                        </div>
                    </div>
                </div>
                <div class="global_container">
                    <div class="big_title">限制条件</div>
                    <div class="rows clean" id="WeightBetween" style="display: block;">
                        <div class="input">
                            <div class="box_type_menu">
                                <span class="item @(Model.ShippingInfo.UseCondition == 0 ? "checked" : "")">
                                    <input type="radio" name="UseCondition" value="0"
                                           @(Model.ShippingInfo.UseCondition == 0 ? "checked" : "")>
                                    没有限制
                                </span>
                                <span class="item @(Model.ShippingInfo.UseCondition == 1 ? "checked" : "")">
                                    <input type="radio" name="UseCondition" value="1"
                                           @(Model.ShippingInfo.UseCondition == 1 ? "checked" : "")>
                                    产品总重量
                                </span>
                                <span class="item @(Model.ShippingInfo.UseCondition == 2 ? "checked" : "")">
                                    <input type="radio" name="UseCondition" value="2"
                                           @(Model.ShippingInfo.UseCondition == 2 ? "checked" : "")>
                                    产品总数量
                                </span>
                                <span class="item @(Model.ShippingInfo.UseCondition == 3 ? "checked" : "")">
                                    <input type="radio" name="UseCondition" value="3"
                                           @(Model.ShippingInfo.UseCondition == 3 ? "checked" : "")>
                                    产品总价
                                </span>
                            </div>
                            <div class="bg_between" style="display: none;">
                                <div class="box_type_menu_content">
                                    <div class="item " style="display: block;">
                                        <div class="box_explain"></div>
                                    </div>
                                    <div class="item none" style="display: none;">
                                        <div class="box_explain">产品总重量满足以下范围，才能使用此快递方式来计算运费
                                        </div>
                                    </div>
                                    <div class="item none" style="display: none;">
                                        <div class="box_explain">产品总数量满足以下范围，才能使用此快递方式来计算运费
                                        </div>
                                    </div>
                                    <div class="item none" style="display: none;">
                                        <div class="box_explain">产品总价满足以下范围，才能使用此快递方式来计算运费</div>
                                    </div>
                                </div>
                                <span class="unit_input range" style="display: none;">
                                    <b class="box_type_index" data-index="3" style="display: none;">$</b>
                                    <div class="input_item left_radius">
                                        <input type="text" name="MinWeight" id="MinWeight"
                                               value="@Model.ShippingInfo.MinWeight" class="box_input"
                                               size="40" maxlength="10" rel="amount">
                                    </div>
                                    <div class="input_item right_radius">
                                        <input type="text" name="MaxWeight" id="MaxWeight"
                                               value="@Model.ShippingInfo.MaxWeight"
                                               class="box_input box_max" size="40" maxlength="10" rel="amount">
                                        <div class="error_tips"></div>
                                    </div>
                                    <input type="text" name="" id="" value="以上" readonly="" disabled=""
                                           class="box_input box_unlimited" size="40" maxlength="10"
                                           style="display: none;">
                                    <b class="last box_type_index box_select_down" data-index="1"
                                       style="display: none;">
                                        <span
                                            class="head">@(string.IsNullOrEmpty(Model.ShippingInfo.WeightUnit) ? "kg" : Model.ShippingInfo.WeightUnit)</span>
                                        <ul class="list">
                                            <li data-value="kg"
                                                class="@(Model.ShippingInfo.WeightUnit == "kg" ? "current" : "")">kg</li>
                                            <li data-value="g"
                                                class="@(Model.ShippingInfo.WeightUnit == "g" ? "current" : "")">g</li>
                                            <li data-value="lb"
                                                class="@(Model.ShippingInfo.WeightUnit == "lb" ? "current" : "")">lb</li>
                                            <li data-value="oz"
                                                class="@(Model.ShippingInfo.WeightUnit == "oz" ? "current" : "")">oz</li>
                                        </ul>
                                        <input type="hidden" class="unit_input_select_input" name="WeightUnit"
                                               value="@(string.IsNullOrEmpty(Model.ShippingInfo.WeightUnit) ? "kg" : Model.ShippingInfo.WeightUnit)">
                                    </b>
                                </span>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="global_container">
                    <div class="big_title">体积重系数</div>
                    <div class="rows clean">
                        <div class="input">
                            <input type="text" name="VolumeWeightCoefficient"
                                   value="@(Model.ShippingInfo.VolumeWeightCoefficient ?? 5000)"
                                   class="box_input full_input"
                                   size="25" maxlength="100" notnull="">
                        </div>
                    </div>
                </div>
                <div class="global_container">
                    <div class="big_title">计费方式</div>
                    <div class="rows clean">
                        <div class="input">
                            <div class="weightarea_box">
                                <div class="item item_5 @(Model.ShippingInfo.IsWeightArea == 5 ? "cur" : "")">
                                    <input type="radio" name="IsWeightArea" value="5"
                                           @(Model.ShippingInfo.IsWeightArea == 5 ? "checked" : "")>
                                    <i></i>
                                    <div class="name">固定费用</div>
                                </div>
                                <div class="item item_0 @(Model.ShippingInfo.IsWeightArea == 0 ? "cur" : "")">
                                    <input type="radio" name="IsWeightArea" value="0"
                                           @(Model.ShippingInfo.IsWeightArea == 0 ? "checked" : "")>
                                    <i></i>
                                    <div class="name">按重量</div>
                                </div>
                                <div class="item item_3 @(Model.ShippingInfo.IsWeightArea == 3 ? "cur" : "")">
                                    <input type="radio" name="IsWeightArea" value="3"
                                           @(Model.ShippingInfo.IsWeightArea == 3 ? "checked" : "")>
                                    <i></i>
                                    <div class="name">按数量</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <input type="hidden" name="IsUsed" value="@Model.ShippingInfo.IsUsed">
                <input type="hidden" name="SId" value="@Model.ShippingInfo.SId">
            </form>
        </div>
    </div>
    <a class="tip_min_ico add item btn_add_area hide" data-sid="0" href="javascript:;">添加分区</a>
    <div class="fixed_box_popup box_area_choose_range" data-width="690">
        <div class="box_middle">
            <div class="title">分区配送范围</div>
            <div class="content">
                <div class="delivery_box">
                    <a href="/manage/set/shipping/area?id=0&amp;sid={{sid}}&amp;DeliveryRange=country">
                        <span class="input_radio_box input_radio_side_box">
                            <span class="input_radio">
                                <input type="radio"
                                       name="DeliveryMethod" value="country">
                            </span><strong class="fs14">指定国家/地区</strong>
                            <p class="fs12">根据指定国家/地区计算运费</p>
                        </span>
                    </a><a href="/manage/set/shipping/area?id=0&amp;sid={{sid}}&amp;DeliveryRange=zipCode">
                        <span class="input_radio_box input_radio_side_box">
                            <span class="input_radio">
                                <input type="radio"
                                       name="DeliveryMethod" value="zipCode">
                            </span><strong class="fs14">指定邮政编码</strong>
                            <p class="fs12">根据指定国家/地区的邮政编码计算运费</p>
                        </span>
                    </a>
                </div>
            </div>
        </div>
    </div>
    <div class="rows clean fixed_btn_submit" style="width: 2096px; left: 180px;">
        <label></label>
        <div class="center_container_786">
            <div class="input input_button">
                <input type="button" class="btn_global btn_submit" value="下一步">
                <a href="/Setting/Shipping"><input type="button" class="btn_global btn_cancel" value="返回"></a>
            </div>
        </div>
    </div>

</div>