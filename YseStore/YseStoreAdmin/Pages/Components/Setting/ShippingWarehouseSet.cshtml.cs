using Entitys;
using YseStore.IService.Set;
using YseStore.Model.Response.Set;

namespace YseStoreAdmin.Pages.Components.Setting
{
    public class ShippingWarehouseSet : CompBase
    {
        private readonly IShippingWarehouseService _warehouseService;
        private readonly ISettingShippingService _shippingService;

        public ShippingWarehouseSet(
            IShippingWarehouseService warehouseService,
            ISettingShippingService shippingService)
        {
            _warehouseService = warehouseService;
            _shippingService = shippingService;
        }

        /// <summary>
        /// 默认仓库
        /// </summary>
        public WarehouseViewModel DefaultWarehouse { get; set; }

        /// <summary>
        /// 自定义仓库列表
        /// </summary>
        public List<WarehouseViewModel> CustomWarehouses { get; set; } = new List<WarehouseViewModel>();

        /// <summary>
        /// 所有物流列表
        /// </summary>
        public List<shipping> AllShippings { get; set; } = new List<shipping>();

        public override async Task MountAsync()
        {
            await LoadWarehouseDataAsync();
        }

        private async Task LoadWarehouseDataAsync()
        {
            // 获取所有物流
            AllShippings = await _shippingService.GetAllShippingAsync();

            // 获取默认仓库
            var defaultWarehouse = await _warehouseService.GetDefaultWarehouseAsync();
            if (defaultWarehouse != null)
            {
                // 获取默认仓库关联的物流
                var defaultShippings = await _warehouseService.GetWarehouseShippingsAsync(-1);
                DefaultWarehouse = new WarehouseViewModel
                {
                    Id = defaultWarehouse.OvId,
                    Name = defaultWarehouse.Name,
                    ShippingIds = defaultShippings.Select(s => (int)s.SId).ToList(),
                    ShippingNames = string.Join("、", defaultShippings.Select(s => s.Express)),
                    ShippingJson =
                        System.Text.Json.JsonSerializer.Serialize(defaultShippings.Select(s => (int)s.SId).ToList())
                };
            }

            // 获取自定义仓库
            var warehouses = await _warehouseService.GetAllWarehousesAsync();
            foreach (var warehouse in warehouses.Where(w => w.OvId != -1))
            {
                var shippings = await _warehouseService.GetWarehouseShippingsAsync(warehouse.OvId);
                
                // 获取关联产品数量
                var productCount = await _warehouseService.GetWarehouseProductCountAsync(warehouse.OvId);
                
                CustomWarehouses.Add(new WarehouseViewModel
                {
                    Id = warehouse.OvId,
                    Name = warehouse.Name,
                    ShippingIds = shippings.Select(s => (int)s.SId).ToList(),
                    ShippingNames = string.Join("、", shippings.Select(s => s.Express)),
                    ShippingJson = System.Text.Json.JsonSerializer.Serialize(shippings.Select(s => (int)s.SId).ToList()),
                    ProductCount = productCount
                });
            }
        }
    }
    

}