using Azure;
using Entitys;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using StackExchange.Redis;
using System.Diagnostics;
using System.Text.Json;
using YseStore.IService;
using YseStore.Model;
using YseStore.Model.Event;
using YseStore.Model.VM;

namespace YseStoreAdmin.Pages.Components.Setting
{
    public class SettingPhotos : CompBase
    {
        public int TotalCount { get; set; }
        public int PageSize { get; set; }
        public int PageNum { get; set; }

        [BindProperty(SupportsGet = true)]

        public string keywords { get; set; }


        public string TagsId { get; set; }
        public string TagsIdName { get; set; }

        public PagedList<PhotoResponse>? PhotosList { get; set; }
        public List<photo_tags>? Photo_tagsList { get; set; }

        public string PhotoTagsJson { get; set; }
        public ISettingPhotosService _settingPhotosService { get; }

        public string ShowType { get; set; }
        public SettingPhotos(ISettingPhotosService settingPhotosService)
        {

            _settingPhotosService = settingPhotosService;
        }



        //public async Task OnGetAsync()
        //{
        //    PhotosList = await GetLogs(Keyword, TagsId);
        //    Photo_tagsList = await GetPhoto_Tags();
        //}


        public void HandlePageEvent(PageEvent evt)
        {
            TotalCount = evt.Total;
            PageSize = evt.PageSize;
            PageNum = evt.PageNum;

        }
        public async Task ChangePage1()
        {
            await Task.CompletedTask;
        }
        [SkipOutput]
        public async Task TurnPage(int page)
        {
            DispatchGlobal<UserTurnPageEvent>(new UserTurnPageEvent("manlog", page));

        }
        public override async Task MountAsync()
        {
            var showType = HttpContext.Request.Query["showType"].ToString();
            if (showType == "tile")
            {
                ShowType = "tile";
            }
            else
            {
                ShowType = "list";
            }
            keywords = HttpContext.Request.Query["keyword"].ToString();
            TagsId = HttpContext.Request.Query["tagsId"].ToString();

            PhotosList = await GetLogs(keywords, TagsId);
            Photo_tagsList = await GetPhoto_Tags();

            List<int> TagsIds = new List<int>();
            if (!string.IsNullOrEmpty(TagsId) && Photo_tagsList != null && Photo_tagsList.Count > 0)
            {
                TagsIds = TagsId.Split(',').Select(int.Parse).ToList();
                var Name = Photo_tagsList.Where(x => TagsIds.Contains(x.Id)).Select(x => x.Name).ToList();
                TagsIdName = string.Join("-", Name);
            }

            var result = await _settingPhotosService.GetFormattedPhotoTags();
            PhotoTagsJson = System.Text.Json.JsonSerializer.Serialize(result);

        }
        public async Task<List<photo_tags>> GetPhoto_Tags()
        {
            var result = await _settingPhotosService.GetPhoto_Tags();
            return result;
        }



        public async Task<PagedList<PhotoResponse>> GetLogs(string Keyword, string TagsId, int page = 1)
        {

            var result = await _settingPhotosService.QueryAsync(Keyword, TagsId, "", page, 20);

            return result;
        }
    }
}
