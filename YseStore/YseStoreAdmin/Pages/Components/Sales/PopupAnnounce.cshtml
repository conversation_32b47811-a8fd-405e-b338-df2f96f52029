 
@model YseStoreAdmin.Pages.Components.Sales.PopupAnnounce 
@{
}
<div>

        <div class="r_con_wrap operation-activities operation-activities-index" style="height: 463px;">
            <div class="inside_container inside_menu_right">
                <h1>
                    弹窗公告
                </h1>
                <div class="inside_menu">
                    <div class="inside_title"><span>所有</span><i></i></div>
                    <div class="inside_body">
                        <ul>
                        <li><a href="/Sales/PopupAnnounce" class=" @(string.IsNullOrEmpty(Model.Status)?"current":"") ">所有</a></li>
                        <li><a href="/Sales/PopupAnnounce?Status=1" class=" @(Model.Status=="1"?"current":"") ">未开始</a></li>
                        <li><a href="/Sales/PopupAnnounce?Status=2" class=" @(Model.Status=="2"?"current":"") ">进行中</a></li>
                        <li><a href="/Sales/PopupAnnounce?Status=3" class=" @(Model.Status=="3"?"current":"") ">已结束</a></li>
                        </ul>
                        <div class="inside_menu_current" style="left: 0px;"></div>
                    </div>
                </div>
            </div>
            <div class="inside_table">
                <div class="list_menu">
                    <ul class="list_menu_button fr">
                        <li><a class="add btn_add_activities" href="javascript:;">添加</a></li>
                    </ul>
                </div>
                <div class="box_table">
                <popup-announce-table/>
                </div>
                <div class="scroll_sticky">
                    <div class="scroll_sticky_content">
                        <div style="width: 2046px; height: 1px;"></div>
                    </div>
                </div>
            <mx-pager name="popupannounce" total="@Model.TotalCount" page-size="@Model.PageSize" page-num="@Model.PageNum" />
            </div>
        </div>
        <div id="operation-activities_preview">
            <div class="top_bar">
                <a class="go_home" href="javascript:;"><i></i>返回</a>
                <div class="go_screen">
                    <i class="current" data-client="PC"></i>
                    <i data-client="Mobile"></i>
                </div>
                <div class="go_btn go_save btn_save">保存</div>
                <div class="go_btn btn_mod">编辑</div>
            </div>
            <div class="preview_box">
                <div class="preview_wrapper"></div>
                <input type="hidden" name="OId" value="">
            </div>
        </div>
        <div class="fixed_box_popup box_add_activities" data-width="1020">
            <div class="box_middle">
                <a href="javascript:;" class="close"></a>
                <div class="title">添加弹窗公告</div>
                <div class="content">
                    <div class="cate_list">
                    <a href="/Sales/PopupAnnounceEdit?id=0&amp;category=newsletter">
                            <span class="input_radio_box input_radio_side_box">
                                <span class="input_radio">
                                    <input type="radio"
                                           name="category" value="newsletter">
                                </span><strong class="fs14">用户订阅</strong>
                                <p class="fs12">吸引客户填写邮箱地址，并且发放优惠券，促使客户购物</p>
                            </span>
                    </a><a href="/Sales/PopupAnnounceEdit?id=0&amp;category=user">
                            <span class="input_radio_box input_radio_side_box">
                                <span class="input_radio">
                                    <input type="radio"
                                           name="category" value="user">
                                </span><strong class="fs14">注册会员</strong>
                                <p class="fs12">吸引客户注册成为会员，增加会员数量，并且发放优惠券，促使客户购物</p>
                            </span>
                    </a><a href="/Sales/PopupAnnounceEdit?id=0&amp;category=retain">
                            <span class="input_radio_box input_radio_side_box">
                                <span class="input_radio">
                                    <input type="radio"
                                           name="category" value="retain">
                                </span><strong class="fs14">购物车挽留购买</strong>
                                <p class="fs12">向已将产品加入购物车的客户发放优惠券，促使下单</p>
                            </span>
                    </a><a href="/Sales/PopupAnnounceEdit?id=0&amp;category=discount">
                            <span class="input_radio_box input_radio_side_box">
                                <span class="input_radio">
                                    <input type="radio"
                                           name="category" value="discount">
                                </span><strong class="fs14">普通弹窗</strong>
                                <p class="fs12">设置不同的优惠活动，吸引客户点击，促使客户购物</p>
                            </span>
                    </a><a href="/Sales/PopupAnnounceEdit?id=0&amp;category=head">
                            <span class="input_radio_box input_radio_side_box">
                                <span class="input_radio">
                                    <input type="radio"
                                           name="category" value="head">
                                </span><strong class="fs14">头部公告</strong>
                                <p class="fs12">将优惠信息显示在店铺页面的顶部</p>
                            </span>
                    </a><a href="/Sales/PopupAnnounceEdit?id=0&amp;category=minor">
                            <span class="input_radio_box input_radio_side_box">
                                <span class="input_radio">
                                    <input type="radio"
                                           name="category" value="minor">
                                </span><strong class="fs14">未成年提醒</strong>
                                <p class="fs12">弹窗询问访客是否成年，保护未成年的访客</p>
                            </span>
                    </a><a href="/Sales/PopupAnnounceEdit?id=0&amp;category=coupon">
                            <span class="input_radio_box input_radio_side_box">
                                <span class="input_radio">
                                    <input type="radio"
                                           name="category" value="coupon">
                                </span><strong class="fs14">优惠券投放</strong>
                                <p class="fs12">客户进入店铺就能从弹窗上领取优惠券，然后去下单</p>
                            </span>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    
</div>