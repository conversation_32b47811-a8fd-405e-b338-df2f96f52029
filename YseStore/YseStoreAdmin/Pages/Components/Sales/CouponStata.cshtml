 
@model YseStoreAdmin.Pages.Components.Sales.CouponStata 
@{
}
<div>

        <div class="r_con_wrap sales-coupon sales-coupon-analytics" style="height: 494px;">
            <div class="center_container_1200">
                <div class="return_title">
                <a href="/Sales/Coupon">
                        <span class="return">优惠码</span>
                        <span class="s_return">/ 数据统计</span>
                    </a>
                </div>
               @*  <div class="box_data_menu">
                    <div class="dateselector" id="analyticsCoupon"
                         data-value="{&quot;date&quot;:[&quot;2025-03-25&quot;,&quot;2025-04-23&quot;],&quot;diff&quot;:{&quot;ms&quot;:2505600000,&quot;days&quot;:29},&quot;compare&quot;:{&quot;checked&quot;:false,&quot;date&quot;:[]}}">
                        <div class="inner selector">
                            <div class="text main"><span>过去30天</span><i class="icon iconfont icon_menu_downarrow"></i></div>
                            <div class="pane">
                                <div class="form">
                                    <div class="item">
                                        <div class="label">日期范围</div>
                                        <div class="input">
                                            <select name="date" class="">
                                                <option value="0" data-index="today">今天</option>
                                                <option value="1" data-index="yesterday">昨天</option>
                                                <option value="7" data-index="week">过去7天</option>
                                                <option value="14" data-index="14days">过去14天</option>
                                                <option value="30" data-index="month">过去30天</option>
                                                <option value="thisWeek" data-index="thisWeek">本周</option>
                                                <option value="thisMonth" data-index="thisMonth">本月</option>
                                                <option value="lastWeek" data-index="lastWeek">上周</option>
                                                <option value="lastMonth" data-index="lastMonth">上月</option>
                                                <option value="custom" data-index="custom">自定义</option>
                                            </select>
                                            <i class="icon iconfont icon_menu_downarrow"></i>
                                        </div>
                                    </div>

                                    <div class="custom_date item" style="display: none;">
                                        <div class="label">开始时间</div>
                                        <div class="input">
                                            <input name="beginAt" value="" type="text" class="date" readonly="">
                                        </div>
                                    </div>
                                    <div class="custom_date item" style="display: none;">
                                        <div class="label">结束时间</div>
                                        <div class="input">
                                            <input name="endAt" value="" type="text" class="date" readonly="">
                                        </div>
                                    </div>
                                    <div class="item btn_group">
                                        <button class="submit btn_global btn_submit">确认</button>
                                        <button class="cancel btn_global btn_cancel">取消</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div> *@
                <div class="box_data_list clean">
                    <div class="box_data">
                        <div class="item_info">
                            <div class="item_info_title">订单数</div>
                            <div class="item_info_data" data-type="order_count">1</div>
                        </div>
                        <div class="item_info">
                            <div class="item_info_title">订单占比</div>
                            <div class="item_info_data" data-type="order_ratio">100.00%</div>
                        </div>
                        <div class="item_info">
                            <div class="item_info_title">销售额</div>
                            <div class="item_info_data" data-type="sale_amount">€605.99</div>
                        </div>
                        <div class="item_info">
                            <div class="item_info_title">销售额占比</div>
                            <div class="item_info_data" data-type="sale_amount_ratio">100.00%</div>
                        </div>
                        <div class="item_info">
                            <div class="item_info_title">客单价</div>
                            <div class="item_info_data" data-type="customer_price">€605.99</div>
                        </div>
                    </div>
                </div>
                <div class="global_container coupons_list" style="position: relative;">
                    <div class="big_title">优惠券概况</div>
                    <table border="0" cellpadding="5" cellspacing="0" class="r_con_table" style="display: table;">
                        <colgroup>
                            <col style="width:18%;">
                            <col style="width:22%;">
                            <col style="width:13%;">
                            <col style="width:13%;">
                            <col style="width:13%;">
                            <col style="width:13%;">
                            <col style="width:8%;">
                        </colgroup>
                        <thead>
                            <tr>
                                <td nowrap="">优惠码</td>
                                <td nowrap="">优惠规则</td>
                                <td nowrap="">订单数</td>
                                <td nowrap="">优惠金额</td>
                                <td nowrap="">销售额</td>
                                <td nowrap="">客单价</td>
                                <td nowrap="" class="operation"></td>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td nowrap="">welcome20</td>
                                <td nowrap="">满€299.00, 减 €20.00</td>
                                <td nowrap="">1</td>
                                <td nowrap="">€20.00</td>
                                <td nowrap="">€605.99</td>
                                <td nowrap="">€605.99</td>
                                <td nowrap="" class="operation">
                                    <a class="oper_icon icon_file button_tips"
                                       href="/manage/sales/coupon/statistics?code=welcome20" target="_blank">详细</a>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                    <div class="pagination">
                        <div id="turn_page" data-current="0" data-count="1">
                            <div class="total_page">共 1 条</div>
                        </div>
                    </div>
                    <div class="bg_no_table_data" style="height: 200px; display: none;">
                        <div class="content" style="top: 48px;">
                            <p>当前暂时没有数据</p>
                        </div><span></span>
                    </div>
                </div>
            </div>
        </div>
    
</div>