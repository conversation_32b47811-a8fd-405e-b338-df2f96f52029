using Entitys;
using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using YseStore.IService.manage;
using YseStore.IService.Sales;
using YseStore.Model.Event;
using YseStore.Model.VM;

namespace YseStoreAdmin.Pages.Components.Sales
{
    public class OrderTable : MComponent
    {

        private readonly ISalesCouponService _salesCouponService;
        public OrderTable(ISalesCouponService salesCouponService)
        {
            Subscribe<UserTurnPageEvent>(HandleUserTurnPage);
            _salesCouponService = salesCouponService;
        }

        [Parameter]
        [SupplyParameterFromQuery(Name = "CouponNumber")]
        public string? CouponNumber { get; set; }

        public PagedList<orders>? OrdersList { get; set; }

        public int TotalCount { get; set; }
        public int PageSize { get; set; } = 6;
        public int PageNum { get; set; }
        public async Task HandleUserTurnPage(UserTurnPageEvent evt)
        {
            if (evt.Name == "couponorder")
            {
                await BindData(evt.PageNum);
            }
        }

        public override async Task MountAsync()
        {
            await BindData();
        }
        [SkipOutput]
        public async Task BindData(int page = 1)
        {
            OrdersList = await GetOrdersList(page);
            DispatchGlobal<PageEvent>(new PageEvent(OrdersList.TotalCount, OrdersList.PageSize, OrdersList.PageIndex + 1, "couponorder"), null, true);

        }

        public async Task<PagedList<orders>> GetOrdersList(int page = 1)
        {
            var result = await _salesCouponService.QueryCouponOrderAsync(CouponNumber,pageNum: page,pageSize: PageSize);

            return result;
        }
        [SkipOutput]
        public async Task TurnPage(int page)
        {
            DispatchGlobal<UserTurnPageEvent>(new UserTurnPageEvent("couponorder", page));
            await Task.CompletedTask;
        }




    }
}
