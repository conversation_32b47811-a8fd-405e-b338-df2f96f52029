 
@model YseStoreAdmin.Pages.Components.Sales.Discount 
@{
}
<div>
    <div id="sales" class="r_con_wrap" style="height: 373px;">
        <div class="inside_container inside_menu_right">
            <h1>满减活动</h1>
        </div>
        <div class="inside_table">
            <div class="list_menu">
                <ul class="list_menu_button fr">
                    <li><a class="add" href="/Sales/DiscountEdit">添加</a></li>
                </ul>
                <div class="search_box fl">
                    <form id="w0" action="/Sales/Discount" method="get">
                        <div class="k_input">
                            <input  type="text" class="form_input" name="Keyword" size="15" autocomplete="off"
                                   placeholder="请输入关键词"> <input type="submit" class="search_btn" value="&#xe600;"  >
                        </div>
                        <div class="clear"></div>
                    </form>
                </div>
            </div>
            <div class="box_table">
                <discount-table params="new{Keyword=Model.Keyword}"></discount-table>
               
            </div>
            <div class="scroll_sticky">
                <div class="scroll_sticky_content">
                    <div style="width: 1808px; height: 1px;"></div>
                </div>
            </div>
           @*  <div id="turn_page" data-current="0" data-count="1">
                <div class="total_page">共 1 条</div>
            </div> *@
            <mx-pager name="discounttable" total="@Model.TotalCount" page-size="@Model.PageSize" page-num="@Model.PageNum" />
        </div>
    </div>

</div>