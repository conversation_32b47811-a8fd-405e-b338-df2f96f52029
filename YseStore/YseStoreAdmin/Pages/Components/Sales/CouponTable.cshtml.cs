using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using YseStore.IService.manage;
using YseStore.IService.Sales;
using YseStore.Model.Event;
using YseStore.Model.Response.Sales;
using YseStore.Model.VM;

namespace YseStoreAdmin.Pages.Components.Sales
{
    public class CouponTable : MComponent
    {

        private readonly ISalesCouponService  _salesCouponService;


        public PagedList<SalesCouponResponse>? SalesCouponList { get; set; }

        public CouponTable(ISalesCouponService salesCouponService )
        {

            Subscribe<UserTurnPageEvent>(HandleUserTurnPage);
            _salesCouponService = salesCouponService;
        }
        public async Task HandleUserTurnPage(UserTurnPageEvent evt)
        {
            if (evt.Name == "manlog")
            {
                await BindData(evt.PageNum);
            }
        }
        public string? CouponType { get; set; } = "";
        public string? Keyword { get; set; } = "";
        public string? DeliveryMethod { get; set; } = "";

        public override async Task MountAsync()
        {
            Request.Query.TryGetValue("CouponType", out var couponType);
            CouponType = couponType;
            Request.Query.TryGetValue("Keyword", out var keyword);
            Keyword = keyword;
            Request.Query.TryGetValue("DeliveryMethod", out var deliveryMethod);
            DeliveryMethod = deliveryMethod;
            await BindData();
        }
        [SkipOutput]
        public async Task BindData(int page = 1)
        {
            SalesCouponList = await GetSalesCoupon(page);
            DispatchGlobal<PageEvent>(new PageEvent(SalesCouponList.TotalCount, SalesCouponList.PageSize, SalesCouponList.PageIndex + 1, "manlog"), null, true);

        }

        public async Task<PagedList<SalesCouponResponse>> GetSalesCoupon(int page = 1)
        {
            var result = await _salesCouponService.QueryAsync(Keyword,CouponType,DeliveryMethod, page, 20);

            return result;
        }

    }
}
