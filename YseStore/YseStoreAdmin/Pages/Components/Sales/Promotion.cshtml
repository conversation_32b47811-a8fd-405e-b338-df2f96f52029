 
@model YseStoreAdmin.Pages.Components.Sales.Promotion 
@{
}
<div>

        <div id="sales" class="r_con_wrap" style="height: 348px;">

            <div class="inside_container inside_menu_right">
                <h1>限时促销</h1>
            </div>
            <div class="inside_table">
                <div class="list_menu">
                    <ul class="list_menu_button fr">
                    <li><a href="/Sales/PromotionSetting" class="set_btn">设置</a></li>
                    <li><a class="add" href="/Sales/PromotionEdit?id=0">添加</a></li>
                    </ul>
                    <div class="search_box fl">
                        <form method="get" action="?">
                            <div class="k_input">
                                <input type="text" class="form_input" name="Keyword" size="15" autocomplete="off"
                                   placeholder="请输入关键词"> <input type="submit" class="search_btn" value="&#xe600;">
                            </div>
                            <div class="clear"></div>
                        </form>
                    </div>
                    <div class="clear"></div>
                </div>
                <div class="box_table">
                <promotion-table></promotion-table>
                    @* <table border="0" cellpadding="5" cellspacing="0" class="r_con_table">
                        <thead>
                            <tr>
                                <td width="20%" nowrap="">活动名称</td>
                                <td width="20%" nowrap="">活动时间</td>
                                <td width="20%" nowrap="">限购规则</td>
                                <td width="20%" nowrap="">优惠规则</td>
                                <td width="14%" nowrap="">活动状态</td>
                                <td width="125" nowrap="" class="operation"></td>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td nowrap="">test</td>
                                <td nowrap="">2025-04-23 00:00:00 <br> 2025-04-30 00:00:00</td>
                                <td nowrap="">不限购</td>
                                <td nowrap="">固定折扣</td>
                                <td nowrap=""><span class="status ing">进行中</span></td>
                                <td nowrap="" class="operation tar">
                                    <a href="/manage/sales/sales/edit?id=5"
                                       class="oper_icon icon_edit edit button_tips">修改</a>
                                    <a href="http://test.retevis.fr/Special-Offer/5" target="_blank"
                                       class="icon_view oper_icon button_tips ">预览</a>
                                    <a href="javascript:;" class="icon_export oper_icon button_tips sales_export_btn"
                                       data-id="5">导出</a>
                                    <a href="javascript:;" class="oper_icon icon_del del button_tips disabled">删除</a>
                                </td>
                            </tr>
                        </tbody>
                    </table> *@
                </div>
                <div class="scroll_sticky">
                    <div class="scroll_sticky_content">
                        <div style="width: 1808px; height: 1px;"></div>
                    </div>
                </div>
               @*  <div id="turn_page" data-current="0" data-count="1">
                    <div class="total_page">共 1 条</div>
                </div> *@
            <mx-pager name="flashsale" total="@Model.TotalCount" page-size="@Model.PageSize" page-num="@Model.PageNum" />
            </div>
        </div>
    
</div>