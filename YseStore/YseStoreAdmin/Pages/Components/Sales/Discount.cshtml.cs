using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using YseStore.Model.Event;

namespace YseStoreAdmin.Pages.Components.Sales
{
    public class Discount : MComponent
    {
        [Parameter]
        [SupplyParameterFromQuery(Name = "Keyword")]
        public string? Keyword { get; set; }
        public int TotalCount { get; set; }
        public int PageSize { get; set; } = 20;
        public int PageNum { get; set; }
        public override void Mount()
        {
            base.Mount();

        }
        public void HandlePageEvent(PageEvent evt)
        {
            TotalCount = evt.Total;
            PageSize = evt.PageSize;
            PageNum = evt.PageNum;

        }
        public async Task ChangePage1()
        {
            await Task.CompletedTask;
        }
        [SkipOutput]
        public async Task TurnPage(int page)
        {
            DispatchGlobal<UserTurnPageEvent>(new UserTurnPageEvent("discounttable", page));
            await Task.CompletedTask;
        }



        public override async Task MountAsync()
        {
            //LogList=await GetLogs();
            await base.MountAsync();
        }

    }
}
