
@model YseStoreAdmin.Pages.Components.Sales.OrderTable 
@{
}

<div>
	@if (Model.OrdersList != null && Model.OrdersList.Count > 0)
	{
		<table border="0" cellpadding="5" cellspacing="0" class="r_con_table">
			<thead>
				<tr>
					<td width="15%" nowrap="nowrap">订单号</td>
					<td width="15%" nowrap="nowrap">付款状态</td>
					<td width="15%" nowrap="nowrap">产品总价</td>
					<td width="15%" nowrap="nowrap">运费</td>
					<td width="15%" nowrap="nowrap">优惠</td>
					<td width="10%" nowrap="nowrap">创建时间</td>
				</tr>
			</thead>
			<tbody>
				@if (Model.OrdersList != null && Model.OrdersList.Count > 0)
				{
					foreach (var item in Model.OrdersList)
					{
						var showTip = ((item.TradeStatus == "pending" && item.PaymentStatus != "paid") ||
						item.PaymentStatus == "pending") &&
						(item.PId == 1 || item.PId == 2);
						string iconPaymentStatus = "";
						if (item.PaymentStatus == "paid")
						{
							iconPaymentStatus = " ing";
						}
						else if (item.PaymentStatus == "pending")
						{
							iconPaymentStatus = " process";
						}
						else if (item.PaymentStatus == "refunded" || item.PaymentStatus == "voided")
						{
							iconPaymentStatus = " end";
						}
						<tr>
							<td nowrap="nowrap">
								<a href="/manage/orders/orders/view?id=25&amp;query_string=" title="@item.OId" class="green">@item.OId</a>
							</td>
							<td nowrap="nowrap">
								<span class="status @iconPaymentStatus">@Consts.PayStatusDic[item.PaymentStatus]</span>
								@if (showTip)
								{
									<div class="global_app_tips">
										<em></em>
										<span>需确认交易</span>
									</div>
								}
							</td>
							<td nowrap="nowrap">$@item.ProductPrice</td>
							<td nowrap="nowrap">$@item.ShippingPrice</td>
							<td nowrap="nowrap">$@item.CouponPrice</td>
							<td nowrap="nowrap">
								@{
									var str = $"{DateTimeHelper.ConvertUnixTimestampToBeijingTime((long)item.OrderTime)}";
									@str
								}
							</td>
						</tr>
					}
				}

			</tbody>
		</table>
		<mx-pager name="couponorder" total="@Model.TotalCount" page-size="@Model.PageSize" page-num="@Model.PageNum" />
	}
	else
	{
		<div class="bg_no_table_data" style="height: 200px;"><div class="content" style="top: 48px;"><p>当前暂时没有数据</p></div><span></span></div>
	}
	
</div>