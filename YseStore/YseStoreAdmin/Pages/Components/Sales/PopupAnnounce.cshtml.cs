using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using YseStore.Model.Event;

namespace YseStoreAdmin.Pages.Components.Sales
{
    public class PopupAnnounce : MComponent
    {
        public int TotalCount { get; set; }
        public int PageSize { get; set; } = 20;
        public int PageNum { get; set; }
        public string Status { get; set; } = "";
        public override void Mount()
        {
            Request.Query.TryGetValue("Status", out var status);
            Status=status;
            base.Mount();

        }
        public void HandlePageEvent(PageEvent evt)
        {
            TotalCount = evt.Total;
            PageSize = evt.PageSize;
            PageNum = evt.PageNum;

        }
        [SkipOutput]
        public async Task TurnPage(int page)
        {
            DispatchGlobal<UserTurnPageEvent>(new UserTurnPageEvent("popupannounce", page));
            await Task.CompletedTask;
        }

    }
}
