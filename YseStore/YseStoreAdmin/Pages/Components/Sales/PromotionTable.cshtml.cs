using Entitys;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using YseStore.IService.manage;
using YseStore.IService.Sales;
using YseStore.Model.Event;
using YseStore.Model.Response.Sales;
using YseStore.Model.VM;

namespace YseStoreAdmin.Pages.Components.Sales
{
    public class PromotionTable : MComponent
    {

        private readonly IFlashSaleService _flashSaleService;


        public PagedList<FlashSalesResponse>? FlashSaleList { get; set; }

        public PromotionTable(IFlashSaleService flashSaleService)
        {

            Subscribe<UserTurnPageEvent>(HandleUserTurnPage);
            _flashSaleService = flashSaleService;
        }
        public async Task HandleUserTurnPage(UserTurnPageEvent evt)
        {
            if (evt.Name == "flashsale")
            {
                await BindData(evt.PageNum);
            }
        }

        public override async Task MountAsync()
        {
            await BindData();
        }
        [SkipOutput]
        public async Task BindData(int page = 1)
        {
            FlashSaleList = await GetFlashSale(page);
            DispatchGlobal<PageEvent>(new PageEvent(FlashSaleList.TotalCount, FlashSaleList.PageSize, FlashSaleList.PageIndex + 1, "flashsale"), null, true);

        }

        public async Task<PagedList<FlashSalesResponse>> GetFlashSale(int page = 1)
        {
            Request.Query.TryGetValue("keyword", out var keyword);
            var result = await _flashSaleService.QueryAsync(keyword,page, 50);

            return result;
        }

    }
}
