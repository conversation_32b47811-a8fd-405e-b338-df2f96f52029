using Entitys;
using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Newtonsoft.Json;
using SkiaSharp;
using YseStore.Common.Helper;
using YseStore.IService;
using YseStore.IService.Customer;
using YseStore.IService.Products;
using YseStore.IService.Sales;
using YseStore.Model.Enums;
using YseStore.Model.Response.Sales;
using YseStore.Model.VM.Common;
using YseStore.Model.VM.Sales;

namespace YseStoreAdmin.Pages.Components.Sales
{
    public class PromotionEdit : MComponent
    {
        private readonly ICustomerListService _customerListService;
        private readonly IProductService _productService;
        private readonly ISalesCouponService _salesCouponService;
        private readonly ISalesDiscountService _salesDiscountService;
        private readonly  IFlashSaleService _salesFlashSaleService;
        private readonly ICurrencyService _currencyService;
        public PromotionEdit(ICustomerListService customerListService, IProductService productService, ISalesCouponService salesCoupon, ISalesDiscountService salesDiscountService, IFlashSaleService salesFlashSaleService,ICurrencyService currencyService)
        {
            _customerListService = customerListService;
            _productService = productService;
            _salesCouponService = salesCoupon;
            _salesDiscountService = salesDiscountService;
            _salesFlashSaleService = salesFlashSaleService;
            _currencyService = currencyService;
        }
        [Parameter]
        [SupplyParameterFromQuery(Name = "Id")]
        public string? Id { get; set; }

        public string CategoryHtml { get; set; } = string.Empty;
        public bool IsEdit { get; set; } = false;
        public flash_sale FlashSale  { get; set; } = new flash_sale() { LimitType =false, OfferType = OfferTypeEnum.Fixed,ApplicableCustomers="all",UseProductsType=UseProductsEnum.AllProducts };
        public int LimitCount { get; set; } = 0;
        public List<SelectItem<int>> ProductCategorySelected { get; set; } = new List<SelectItem<int>>();

        public decimal DiscountValue { get; set; } = 0;

        public string FilterCategoryHtml { get; set; } = string.Empty;

        public string FilterTagsHtml { get; set; } = string.Empty;
        public string CurrencySymbol { get; set; } = "$";
        public override async Task MountAsync()
        {
            var defaultCurrency = await _currencyService.GetManageDefaultCurrency();
            CurrencySymbol = defaultCurrency.Symbol;
            var catelist = await _salesCouponService.GetCategoryTreeAsync();
            if (catelist != null && catelist.Count > 0)
            {
                CategoryHtml =  HtmlCategoryBuilder.BuildCategoryHtml(catelist);
            }
            //筛选产品分类
            var categories = await _salesCouponService.GetAllCategoryListAsync();
            //产品筛选产品分类下拉数据
            List<Dictionary<string, object>> boxDropDoubleSelectItems = new List<Dictionary<string, object>>();
            List<Dictionary<string, object>> boxDropDoubleItems = new List<Dictionary<string, object>>();
            var categoryGroups = categories.GroupBy(c => c.UId).ToDictionary(g => g.Key, g => g.ToList());

            boxDropDoubleItems = categories.Where(c=>c.UId== "0,").Select(c =>
            {
                var item = new Dictionary<string, object>
                {
                    ["Name"] = c.Category_en,
                    ["Value"] = c.CateId.ToString(),
                    ["Type"] = "products_category",
                    ["Disabled"] = false
                };
                string childKey = $"{c.UId}{c.CateId},";
                if (categoryGroups.ContainsKey(childKey))
                {
                    item["Table"] = "product_category";
                }
                return item;
            } ).ToList();
            FilterCategoryHtml = HtmlCategoryBuilder.BoxDropDouble("FilterCateId", "Select", boxDropDoubleItems, boxDropDoubleSelectItems,0,isCheckbox:false,isMore:false,topType: "Select",isShowAdd:false);
            List<Dictionary<string, object>> tagsboxDropDoubleItems = new List<Dictionary<string, object>>();
            var producttagsArr = await _salesCouponService.GetProductsTagsRowAsync("products_tags");
            tagsboxDropDoubleItems = producttagsArr.Select(c => new Dictionary<string, object>()
            {
                { "Name", c.Name },{ "Value", c.Value.ToString() },{ "Type", c.Type }

            }).ToList();
            FilterTagsHtml = HtmlCategoryBuilder.BoxDropDouble("FilterTagId", "Select", tagsboxDropDoubleItems, boxDropDoubleSelectItems, 0, isCheckbox: true, isMore: true, topType: "Select", isShowAdd: false);
            if (!string.IsNullOrEmpty(Id) && int.Parse(Id) > 0)//编辑
            {
                IsEdit = true;
                FlashSale = await _salesFlashSaleService.GetFlashsaleInfoAsync(int.Parse(Id));
                if (FlashSale.StartTime != null && FlashSale.StartTime > 0)
                {
                    string start = DateTimeHelper.ConvertToBeijingTime((int)FlashSale.StartTime).ToString("yyyy-MM-dd HH:mm:ss");
                    Client.ExecuteJs($$"""
                                // 开始时间
                $("input[name=StartTime]").val('{{start}}'); 
               
                """);
                }
                if (FlashSale.EndTime != null && FlashSale.EndTime > 0)
                {
                    string end = DateTimeHelper.ConvertToBeijingTime((int)FlashSale.EndTime).ToString("yyyy-MM-dd HH:mm:ss");
                    Client.ExecuteJs($$"""
                                // 结束时间
                $("input[name=EndTime]").val('{{end}}'); 
               
                """);
                    if (!string.IsNullOrEmpty(FlashSale.LimitCondition))
                    {
                        var dict = JsonConvert.DeserializeObject<Dictionary<string, int>>(FlashSale.LimitCondition);
                        LimitCount = dict["Count"];
                    }
                }
                if (!string.IsNullOrEmpty(FlashSale.UseProducts) && FlashSale.UseProductsType != UseProductsEnum.AllProducts)
                {
                    var useProductsValue = JsonConvert.DeserializeObject<List<int>>(FlashSale.UseProducts);
                    switch (FlashSale.UseProductsType)
                    {
                        //case UseProductsEnum.SpecificProducts:
                        //    ProductSelected = ProductSelectItems.Where(c => useProductsValue.Contains(c.Value)).ToList();
                        //    break;
                        case UseProductsEnum.SpecificCategory:
                            ProductCategorySelected = await _salesCouponService.GetCategoryCheckedAsync(useProductsValue);
                            break;
                    }
                }
                if (!string.IsNullOrEmpty(FlashSale.UseProducts) && (FlashSale.UseProductsType == UseProductsEnum.AllProducts || FlashSale.UseProductsType == UseProductsEnum.SpecificCategory))
                {
                    DiscountValue= JsonConvert.DeserializeObject<List<decimal>>(FlashSale.UseProductsData)[0];
                }
            }
        }
    }
}
