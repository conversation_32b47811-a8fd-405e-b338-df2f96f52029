@using Newtonsoft.Json
@using YseStore.Common
@using YseStore.Model.Enums

@model YseStoreAdmin.Pages.Components.Sales.DiscountTable 
@{
}
<div>
     @if ( Model.SalesDiscountList != null && Model.SalesDiscountList.Count > 0)
            {
    <table border="0" cellpadding="5" cellspacing="0" class="r_con_table">
        <thead>
            <tr>
                <td width="10%" nowrap="" class="pos flex_item" style="left: 0px;">
                    <ul class="table_menu_button global_menu_button">
                        <li>
                            <div class="btn_checkbox ">
                                <em class="button"></em><input type="checkbox"
                                name="select_all" value="">
                            </div>
                        </li>
                        <li class="open">已选择<span></span>个</li>
                        <li><a class="del" href="javascript:;">删除</a></li>
                    </ul>
                </td>
                <td width="20%" nowrap="">活动名称</td>
                <td width="20%" nowrap="">优惠规则</td>
                <td width="15%" nowrap="">适用产品</td>
                <td width="15%" nowrap="">活动时间</td>
                <td width="15%" nowrap="">活动状态</td>
                <td width="115" nowrap="" class="operation flex_item last" style="right: 0px;"></td>
            </tr>
        </thead>
        <tbody>
            
                @foreach (var item in Model.SalesDiscountList)
                {
                    int type = (int)item.Type;
                    var ruletype = Consts.RuleTypeAry[type].Split('-').ToArray();
                    ActConditionEnum actCondition = (ActConditionEnum)int.Parse(ruletype[0]); ActTypeEnum actType = (ActTypeEnum)int.Parse(ruletype[1]); ActRuleEnum actRule = (ActRuleEnum)int.Parse(ruletype[2]);
                    string rules = "";
                   
                    if (Consts.ProductRuleTypeAry.Contains(item.Type))
                    {
                        var ruledicts = JsonConvert.DeserializeObject<Dictionary<string, List<Dictionary<string, string>>>>(item.Rule);
                        if (ruledicts != null)
                        {
                            foreach (var rule in ruledicts)
                            {
                                string ac = actCondition == ActConditionEnum.FullAmountReduce ? $"满${rule.Key}" : $"满{rule.Key}件";

                                rules += $"{ac} 赠送 {rule.Value.SelectMany(dict => dict.Where(kv => kv.Key != "-1")).Count() } 件产品<br>";
                            }
                        }
                    }
                    else {
                        var ruledict = JsonConvert.DeserializeObject<Dictionary<string, string>>(item.Rule);
                        if (ruledict != null)
                        {
                            foreach (var rule in ruledict)
                            {
                                string ac = actCondition == ActConditionEnum.FullAmountReduce ? $"满${rule.Key}" : $"满{rule.Key}件";
                                if (actType == ActTypeEnum.Discount)
                                {
                                    rules += $"{ac} 减{rule.Value}%<br>";
                                }
                                else
                                {
                                    rules += $"{ac} 减${rule.Value}<br>";
                                }
                            }
                        }
                    }

                    var currentTime = DateTimeOffset.UtcNow.ToUnixTimeSeconds();
                    ActiveStatusEnum activeStatus = ActiveStatusEnum.已结束;
                    string icon = "end";
                    if (item.StartTime > currentTime )
                    {
                        activeStatus = ActiveStatusEnum.未开始;
                        icon = "";
                    }
                    else if (item.StartTime < currentTime && item.EndTime >= currentTime)
                    {
                        activeStatus = ActiveStatusEnum.进行中;
                        icon = "ing";
                    }
                    <tr>
                        <td class="flex_item" nowrap="" style="left: 0px;">
                            <div class="btn_checkbox ">
                                <em class="button"></em><input type="checkbox" name="select"
                                                               value="@item.FId">
                            </div>
                        </td>
                        <td nowrap="">@item.Name</td>
                        <td nowrap="">
                            @Html.Raw(rules)
                        </td>
                        <td>@(item.UseProducts == null ? "" : ((UseProductsEnum)(item.UseProducts ?? 0)).GetDescription())  </td>
                        <td nowrap="">
                            @{
                                string str = string.Empty;
                                str = $"{DateTimeHelper.ConvertUnixTimestampToLocalTime((long)item.StartTime)} <br>{DateTimeHelper.ConvertUnixTimestampToLocalTime((long)item.EndTime)}";
                                @Html.Raw(str)
                            }
                           </td>
                        <td><span class="status @icon">@activeStatus.ToString()</span></td>
                        <td nowrap="" class="operation tar flex_item last" style="right: 0px;">
                            <a class="oper_icon icon_edit button_tips"
                               href="/Sales/DiscountEdit?id=@item.FId">修改</a>
                           @*  <a class="oper_icon icon_statistics button_tips"
                               href="/manage/sales/discount/statistics?FId=5">统计</a> *@
                        </td>
                    </tr>
                }
            
           
        </tbody>
    </table>
    }
    else{
        <div class="bg_no_table_data" style="height: 200px;"><div class="content" style="top: 48px;"><p>当前暂时没有数据</p></div><span></span></div>
    }


    
</div>