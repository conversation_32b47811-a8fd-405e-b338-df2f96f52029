 
@model YseStoreAdmin.Pages.Components.Sales.PromotionSetting 
@{
}
<div>
    <div id="righter" class="righter home_righter" style="width: 2096px;">
        <div id="sales" class="r_con_wrap" style="height: 428px;">
            <div class="center_container center_container_750">
                <div class="return_title">
                    <a href="/Sales/Promotion">
                        <span class="return">限时促销</span>
                        <span class="s_return">/ 设置</span>
                    </a>
                </div>
                <form id="edit_form" class="global_form sales_set_form" action="/manage/sales/sales/set" method="post">
                    <input type="hidden" name="_csrf-manage"
                           value="6YLqMTq3tgzG13iSWNPEML9UiyKm3HJMkxr859aBFmqs0oEcY_PPS_GPFco-47cA2xrPd9e7BRWlUp-j4dB_JQ==">
                    <div class="global_container">
                        <div class="rows">
                            <div class="big_title">活动项目</div>
                            <div class="input activities_box">
                                <span class="input_checkbox_box @(Model.IsIng?"checked":"")" data-type="ing">
                                    <span class="input_checkbox">
                                        <input type="checkbox" name="Activities[]" value="ing" @(Model.IsIng ? "checked" : "")>
                                    </span>
                                    进行中
                                </span>
                                <span class="input_checkbox_box  @(Model.IsNoStart?"checked":"")" data-type="no_start">
                                    <span class="input_checkbox">
                                        <input type="checkbox" name="Activities[]" value="no_start" @(Model.IsNoStart ? "checked" : "")>
                                    </span>
                                    未开始
                                </span>
                            </div>
                        </div>
                    </div>
                    <div class="global_container">
                        <div class="rows">
                            <div class="big_title">活动展示设置</div>
                            <div class="tips">店铺将展示状态为进行中的所有限时促销产品</div>
                        </div>
                        <div class="rows">
                            <label>预览效果</label>
                            <div class="countdown_box">

                                <div class="countdown_item item_3" data-field="selectedstatus-bgcolor"
                                     style="background-color:@Model.FlashSalesSetting.SelectedStatus.BgColor">
                                    <div class="activity_title" data-field="selectedstatus-titlecolor"
                                         style="color:@Model.FlashSalesSetting.SelectedStatus.TitleColor">
                                        Ends in
                                    </div>
                                    <div class="countdown_time" data-field="selectedstatus-countdowntxtcolor"
                                         style="color:@Model.FlashSalesSetting.SelectedStatus.CountdownTxtColor">
                                        <div class="time_span" data-field="selectedstatus-countdownbgcolor"
                                             style="background-color:@Model.FlashSalesSetting.SelectedStatus.CountdownBgColor">
                                            15
                                        </div>
                                        <i>:</i>
                                        <div class="time_span" data-field="selectedstatus-countdownbgcolor"
                                             style="background-color:@Model.FlashSalesSetting.SelectedStatus.CountdownBgColor">
                                            38
                                        </div>
                                        <i>:</i>
                                        <div class="time_span" data-field="selectedstatus-countdownbgcolor"
                                             style="background-color:@Model.FlashSalesSetting.SelectedStatus.CountdownBgColor">
                                            08
                                        </div>
                                    </div>
                                </div>

                                <div class="countdown_item item_3" data-field="defaultstatus-bgcolor"
                                     style="background-color:@Model.FlashSalesSetting.DefaultStatus.BgColor">
                                    <div class="activity_title" data-field="defaultstatus-titlecolor" style="color:@Model.FlashSalesSetting.DefaultStatus.TitleColor">
                                        Ends in
                                    </div>
                                    <div class="countdown_time" data-field="defaultstatus-countdowntxtcolor"
                                         style="@Model.FlashSalesSetting.DefaultStatus.CountdownTxtColor">
                                        <div class="time_span" data-field="defaultstatus-countdownbgcolor"
                                             style="background-color: @(string.IsNullOrEmpty(Model.FlashSalesSetting.DefaultStatus.CountdownBgColor)?"unset;": Model.FlashSalesSetting.DefaultStatus.CountdownBgColor)  ">
                                            2d
                                        </div>
                                        <i>:</i>
                                        <div class="time_span" data-field="defaultstatus-countdownbgcolor"
                                             style="background-color:@(string.IsNullOrEmpty(Model.FlashSalesSetting.DefaultStatus.CountdownBgColor)?"unset;": Model.FlashSalesSetting.DefaultStatus.CountdownBgColor)">
                                            23
                                        </div>
                                        <i>:</i>
                                        <div class="time_span" data-field="defaultstatus-countdownbgcolor"
                                             style="background-color:@(string.IsNullOrEmpty(Model.FlashSalesSetting.DefaultStatus.CountdownBgColor)?"unset;": Model.FlashSalesSetting.DefaultStatus.CountdownBgColor)">
                                            25
                                        </div>
                                        <i>:</i>
                                        <div class="time_span" data-field="defaultstatus-countdownbgcolor"
                                             style="background-color:@(string.IsNullOrEmpty(Model.FlashSalesSetting.DefaultStatus.CountdownBgColor)?"unset;": Model.FlashSalesSetting.DefaultStatus.CountdownBgColor)">
                                            58
                                        </div>
                                    </div>
                                </div>

                                <div class="countdown_item item_3" data-field="defaultstatus-bgcolor"
                                     style="background-color:@Model.FlashSalesSetting.DefaultStatus.BgColor">
                                    <div class="activity_title" data-field="defaultstatus-titlecolor" style="color:@Model.FlashSalesSetting.DefaultStatus.TitleColor">
                                        Coming soon
                                    </div>
                                    <div class="countdown_time" data-field="defaultstatus-countdowntxtcolor"
                                         style="color:@Model.FlashSalesSetting.DefaultStatus.CountdownTxtColor">
                                        <div class="time_span" data-field="defaultstatus-countdownbgcolor"
                                             style="background-color:@(string.IsNullOrEmpty(Model.FlashSalesSetting.DefaultStatus.CountdownBgColor)?"unset;": Model.FlashSalesSetting.DefaultStatus.CountdownBgColor)">
                                            07/20
                                        </div>
                                        <div class="time_span" data-field="defaultstatus-countdownbgcolor"
                                             style="background-color:@(string.IsNullOrEmpty(Model.FlashSalesSetting.DefaultStatus.CountdownBgColor)?"unset;": Model.FlashSalesSetting.DefaultStatus.CountdownBgColor)">
                                            09
                                        </div>
                                        <i>:</i>
                                        <div class="time_span" data-field="defaultstatus-countdownbgcolor"
                                             style="background-color:@(string.IsNullOrEmpty(Model.FlashSalesSetting.DefaultStatus.CountdownBgColor)?"unset;": Model.FlashSalesSetting.DefaultStatus.CountdownBgColor)">
                                            00
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="rows">
                            <label>默认状态</label>
                            <div class="color_box">
                                <div class="color_rows">
                                    <input class="color_style" name="DefaultStatus[BgColor]" data-type="background"
                                           data-field="defaultstatus-bgcolor" value="@Model.FlashSalesSetting.DefaultStatus.BgColor" style="background-color:@Model.FlashSalesSetting.DefaultStatus.BgColor;"
                                           data-color="@(string.IsNullOrEmpty(Model.FlashSalesSetting.DefaultStatus.BgColor) ? "" : Model.FlashSalesSetting.DefaultStatus.BgColor.Replace("#",""))">
                                    <span>背景颜色</span>
                                </div>
                                <div class="color_rows">
                                    <input class="color_style" name="DefaultStatus[TitleColor]" data-type="color"
                                           data-field="defaultstatus-titlecolor" value="@Model.FlashSalesSetting.DefaultStatus.TitleColor"
                                           style="background-color:@Model.FlashSalesSetting.DefaultStatus.TitleColor" data-color="@(string.IsNullOrEmpty(Model.FlashSalesSetting.DefaultStatus.TitleColor) ? "" : Model.FlashSalesSetting.DefaultStatus.TitleColor.Replace("#", ""))">
                                    <span>标题颜色</span>
                                </div>
                                <div class="color_rows">
                                    <input class="color_style" name="DefaultStatus[CountdownTxtColor]" data-type="color"
                                           data-field="defaultstatus-countdowntxtcolor" value="@Model.FlashSalesSetting.DefaultStatus.CountdownTxtColor"
                                           style="background-color:@Model.FlashSalesSetting.DefaultStatus.CountdownTxtColor" data-color="@(string.IsNullOrEmpty(Model.FlashSalesSetting.DefaultStatus.CountdownTxtColor) ? "" : Model.FlashSalesSetting.DefaultStatus.CountdownTxtColor.Replace("#", ""))">
                                    <span>倒计时文案颜色</span>
                                </div>
                            </div>
                        </div>
                        <div class="rows">
                            <label>选中状态</label>
                            <div class="color_box">
                                <div class="color_rows">
                                    <input class="color_style" name="SelectedStatus[BgColor]" data-type="background"
                                           data-field="selectedstatus-bgcolor" value="@Model.FlashSalesSetting.SelectedStatus.BgColor" style="background-color:@Model.FlashSalesSetting.SelectedStatus.BgColor"
                                           data-color="@(string.IsNullOrEmpty(Model.FlashSalesSetting.SelectedStatus.BgColor) ? "" : Model.FlashSalesSetting.SelectedStatus.BgColor.Replace("#", ""))">
                                    <span>背景颜色</span>
                                </div>
                                <div class="color_rows">
                                    <input class="color_style" name="SelectedStatus[TitleColor]" data-type="color"
                                           data-field="selectedstatus-titlecolor" value="@Model.FlashSalesSetting.SelectedStatus.TitleColor"
                                           style="background-color:@Model.FlashSalesSetting.SelectedStatus.TitleColor" data-color="@(string.IsNullOrEmpty(Model.FlashSalesSetting.SelectedStatus.TitleColor) ? "" : Model.FlashSalesSetting.SelectedStatus.TitleColor.Replace("#", ""))">
                                    <span>标题颜色</span>
                                </div>
                                <div class="color_rows">
                                    <input class="color_style" name="SelectedStatus[CountdownTxtColor]" data-type="color"
                                           data-field="selectedstatus-countdowntxtcolor" value="@Model.FlashSalesSetting.SelectedStatus.CountdownTxtColor"
                                           style="background-color:@Model.FlashSalesSetting.SelectedStatus.CountdownTxtColor" data-color="@(string.IsNullOrEmpty(Model.FlashSalesSetting.SelectedStatus.CountdownTxtColor) ? "" : Model.FlashSalesSetting.SelectedStatus.CountdownTxtColor.Replace("#", ""))">
                                    <span>倒计时文案颜色</span>
                                </div>
                                <div class="color_rows">
                                    <input class="color_style" name="SelectedStatus[CountdownBgColor]"
                                           data-type="background" data-field="selectedstatus-countdownbgcolor" value="@Model.FlashSalesSetting.SelectedStatus.CountdownBgColor"
                                           style="background-color:@Model.FlashSalesSetting.SelectedStatus.CountdownBgColor" data-color="@(string.IsNullOrEmpty(Model.FlashSalesSetting.SelectedStatus.CountdownBgColor) ? "" : Model.FlashSalesSetting.SelectedStatus.CountdownBgColor.Replace("#", ""))">
                                    <span>倒计时背景颜色</span>
                                </div>
                            </div>
                        </div>

                    </div>
                    <input type="hidden" name="do_action" value="/api/FlashSales/setupdate">
                    <input type="hidden" id="back_action" value="/Sales/Promotion">
                </form>
            </div>
        </div>
        <div class="rows clean fixed_btn_submit" style="width: 2096px; left: 180px;">
            <div class="center_container center_container_750">
                <div class="input input_button">
                    <input type="button" class="btn_global btn_submit" value="保存">
                    <a href="/Sales/Promotion"><input type="button" class="btn_global btn_cancel" value="返回"></a>
                </div>
            </div>
        </div>
    </div>
</div>