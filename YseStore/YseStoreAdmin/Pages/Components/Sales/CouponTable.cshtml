@using YseStore.Common
@using YseStore.Model.Enums
 
@model YseStoreAdmin.Pages.Components.Sales.CouponTable 
@{
}
<div>
	<table border="0" cellpadding="5" cellspacing="0" class="r_con_table">
		<thead>
			<tr>
				<td width="7.5%" nowrap="" class="pos flex_item" style="left: 0px;">
					<ul class="table_menu_button global_menu_button">
						<li>
							<div class="btn_checkbox ">
								<em class="button"></em><input type="checkbox"
													   name="select_all" value="">
							</div>
						</li>
						<li class="open">已选择<span></span>个</li>
						<li><a class="del" href="javascript:;">删除</a></li>
					</ul>
				</td>
				<td width="1%" nowrap="nowrap">序号</td>
				<td width="13.7%" nowrap="">优惠码</td>
				<td width="17.4%" nowrap="">优惠规则</td>
				<td width="14%" nowrap="">有效期</td>
				<td width="10%" nowrap="">投放方式</td>
				<td width="14%" nowrap="">已领取/发放量</td>
				<td width="11.1%" nowrap="">已使用次数</td>
				<td width="8%" nowrap="">活动状态</td>
				<td width="" nowrap="" class="operation flex_item last" style="right: 0px;"></td>
			</tr>
		</thead>
		<tbody>

			@if (Model.SalesCouponList != null && Model.SalesCouponList.Count > 0)
			{
				foreach (var (item,idx) in Model.SalesCouponList.WithIndex())
				{
					<tr>
						<td class="flex_item" nowrap="" style="left: 0px;">
							<div class="btn_checkbox ">
								<em class="button"></em><input type="checkbox" name="select"
														 value="@item.CId">
							</div>
						</td>
						<td nowrap="nowrap">@(Model.SalesCouponList.PageIndex * Model.SalesCouponList.PageSize + idx + 1)</td>
						<td>@item.CouponNumber</td>
						<td nowrap="">
							@item.Rules
						</td>
						<td nowrap="">
							@{
                                string str = string.Empty;
								if (item.ValidityType == ValidityTypeEnum.Fixed.ToString().ToLower())
								{
									str = $"{DateTimeHelper.ConvertUnixTimestampToLocalTime((long)item.StartTime)} <br>{DateTimeHelper.ConvertUnixTimestampToLocalTime((long)item.EndTime)}";
								}
								else
								{
									str = $"{item.Duration}{item.TimeType}";
								}
								@Html.Raw(str)
							}
						</td>
						<td>@{
                                DeliveryMethodEnum deliveryMethodEnum = (DeliveryMethodEnum)Enum.Parse(typeof(DeliveryMethodEnum), item.DeliveryMethod);
								string desc = deliveryMethodEnum.GetDescription();
								@desc
									}</td>
						<td>@item.GetNum / @(item.UnLmQty==true?"不限":item.Qty)</td>
						<td>@item.BeUseTimes</td>
						<td nowrap="">
							<span class="status @item.ActiveStatus.Item2">
                                @item.ActiveStatus.Item1.ToString()
							</span>
						</td>
						<td nowrap="" class="operation tar flex_item last" style="right: 0px;">
							<a class="oper_icon icon_edit button_tips"
							   href="/Sales/CouponEdit?id=@item.CId&DeliveryMethod=@item.DeliveryMethod">修改</a> 
							   <a class="oper_icon icon_statistics button_tips"	   href="/Sales/CouponStatistics?code=@item.CouponNumber">统计</a>
							@if (item.ActiveStatus.Item1 == ActiveStatusEnum.进行中|| item.ActiveStatus.Item1 == ActiveStatusEnum.未开始)
{
								<a class="oper_icon icon_finish button_tips" href="javascript:;" data-id="@item.CId">提前结束</a>
}
							@if (item.ActiveStatus.Item1 == ActiveStatusEnum.已结束)
							{
								<a class="oper_icon icon_activate button_tips" href="/Sales/CouponEdit?id=@item.CId&activate=1" data-id="@item.CId">激活</a>
							}
							
							
						</td>
						
						
					</tr>
				}

			}

		</tbody>
	</table>
</div>