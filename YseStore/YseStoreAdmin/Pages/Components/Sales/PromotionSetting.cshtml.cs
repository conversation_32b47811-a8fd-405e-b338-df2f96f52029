using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using YseStore.Common;
using YseStore.IService.Customer;
using YseStore.IService.Products;
using YseStore.IService.Sales;
using YseStoreAdmin.Parameters.Sales;

namespace YseStoreAdmin.Pages.Components.Sales
{
    public class PromotionSetting : MComponent
    {
       
        private readonly IFlashSaleService _flashSaleService;
        public PromotionSetting(IFlashSaleService flashSaleService)
        {
            _flashSaleService = flashSaleService;
        }

        public FlashSalesSetting FlashSalesSetting { get; set; } = new FlashSalesSetting();

        public bool IsNoStart { get; set; } = false;
        public bool IsIng { get; set; } = false;
        public override async Task MountAsync()
        {
           var model= await _flashSaleService.GetFlashsaleSetInfoAsync("flash_sale", "Setting");
            if (model!= null && !string.IsNullOrEmpty(model.Value))
            {
                FlashSalesSetting =model.Value.JsonToObj<FlashSalesSetting>();
                if (FlashSalesSetting.Activities != null && FlashSalesSetting.Activities.Count > 0)
                {
                    if (FlashSalesSetting.Activities.Contains("ing"))
                    {
                        IsIng=true;
                    }
                    if (FlashSalesSetting.Activities.Contains("no_start"))
                    {
                        IsNoStart = true;
                    }
                }
            }
            await base.MountAsync();
        }
    }
}
