@model YseStoreAdmin.Pages.Components.Mta.UserMta
@{
}
<div>

    <div id="mta" class="r_con_wrap" style="height: 396px;">
        <div class="user_mta">
            <div class="inside_container">
                <h1>客户概况</h1>
            </div>
            <div class="inside_table">
                <div class="pannel">
                    <div class="head">
                        <div class="dateselector" id="user"
                             data-value="{&quot;date&quot;:[&quot;2025-05-06&quot;,&quot;2025-05-06&quot;],&quot;diff&quot;:{&quot;ms&quot;:0,&quot;days&quot;:0},&quot;compare&quot;:{&quot;checked&quot;:true,&quot;date&quot;:[&quot;2025-05-05&quot;,&quot;2025-05-05&quot;]}}">
                            <div class="inner selector">
                                <div class="text main"><span>今天</span><i class="icon iconfont icon_menu_downarrow"></i></div>
                                <div class="pane">
                                    <div class="form">
                                        <div class="item">
                                            <div class="label">日期范围</div>
                                            <div class="input">
                                                <select name="date">
                                                    <option value="0" data-index="today">今天</option>
                                                    <option value="1" data-index="yesterday">昨天</option>
                                                    <option value="7" data-index="week">过去7天</option>
                                                    <option value="14" data-index="14days">过去14天</option>
                                                    <option value="30" data-index="month">过去30天</option>
                                                    <option value="thisWeek" data-index="thisWeek">本周</option>
                                                    <option value="thisMonth" data-index="thisMonth">本月</option>
                                                    <option value="lastWeek" data-index="lastWeek">上周</option>
                                                    <option value="lastMonth" data-index="lastMonth">上月</option>
                                                    <option value="custom" data-index="custom">自定义</option>
                                                </select>
                                                <i class="icon iconfont icon_menu_downarrow"></i>
                                            </div>
                                        </div>

                                        <div class="custom_date item">
                                            <div class="label">开始时间</div>
                                            <div class="input">
                                                <input name="beginAt" value="" type="text" class="date" readonly="">
                                            </div>
                                        </div>
                                        <div class="custom_date item">
                                            <div class="label">结束时间</div>
                                            <div class="input">
                                                <input name="endAt" value="" type="text" class="date" readonly="">
                                            </div>
                                        </div>

                                        <div class="item compare">
                                            <div class="label">
                                                <div class="checkbox">
                                                    <input type="checkbox" id="toggle" checked="">
                                                    <label for="toggle">对比</label>
                                                </div>
                                            </div>
                                            <div class="input" style="display: block;">
                                                <select name="compare_type">
                                                    <option value="term" data-index="">上一期</option>
                                                    <option value="year" data-index="">去年</option>
                                                </select>
                                                <i class="icon iconfont icon_menu_downarrow"></i>
                                            </div>
                                        </div>
                                        <div class="item btn_group">
                                            <button class="submit btn_global btn_submit">确认</button>
                                            <button class="cancel btn_global btn_cancel">取消</button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="text compare" style="display: block;">对比<span>2025-05-05</span></div>
                        </div>
                    </div>
                    <ul class="box_data_list data_list clean report">
                        <li>
                            <div class="item new_item" style="position: relative;">
                                <h1>新客户</h1>
                                <div class="top_info">
                                    <h2><span class="new_member">0</span></h2>
                                    <span class="rate tag down">0%</span>
                                </div>
                                <div id="new_charts" class="contents" data-highcharts-chart="0">
                                    <div id="highcharts-2k6bvbd-0" dir="ltr" class="highcharts-container "
                                         style="position: relative; overflow: hidden; width: 566px; height: 400px; text-align: left; line-height: normal; z-index: 0; -webkit-tap-highlight-color: rgba(0, 0, 0, 0);">
                                        <svg version="1.1" class="highcharts-root"
                                             style="font-family:&quot;Lucida Grande&quot;, &quot;Lucida Sans Unicode&quot;, Arial, Helvetica, sans-serif;font-size:12px;"
                                             xmlns="http://www.w3.org/2000/svg" width="566" height="400"
                                             viewBox="0 0 566 400">
                                            <desc>Created with Highcharts 6.1.1</desc>
                                            <defs>
                                                <clipPath id="highcharts-2k6bvbd-1">
                                                    <rect x="0" y="0" width="510" height="312" fill="none"></rect>
                                                </clipPath>
                                            </defs>
                                            <rect fill="#ffffff" class="highcharts-background" x="0" y="0" width="566"
                                                  height="400" rx="0" ry="0"></rect>
                                            <rect fill="none" class="highcharts-plot-background" x="46" y="10"
                                                  width="510" height="312"></rect>
                                            <g class="highcharts-grid highcharts-xaxis-grid " data-z-index="1">
                                                <path fill="none" data-z-index="1" class="highcharts-grid-line"
                                                      d="M 50.5 10 L 50.5 322" opacity="1"></path>
                                                <path fill="none" data-z-index="1" class="highcharts-grid-line"
                                                      d="M 113.5 10 L 113.5 322" opacity="1"></path>
                                                <path fill="none" data-z-index="1" class="highcharts-grid-line"
                                                      d="M 175.5 10 L 175.5 322" opacity="1"></path>
                                                <path fill="none" data-z-index="1" class="highcharts-grid-line"
                                                      d="M 238.5 10 L 238.5 322" opacity="1"></path>
                                                <path fill="none" data-z-index="1" class="highcharts-grid-line"
                                                      d="M 300.5 10 L 300.5 322" opacity="1"></path>
                                                <path fill="none" data-z-index="1" class="highcharts-grid-line"
                                                      d="M 363.5 10 L 363.5 322" opacity="1"></path>
                                                <path fill="none" data-z-index="1" class="highcharts-grid-line"
                                                      d="M 425.5 10 L 425.5 322" opacity="1"></path>
                                                <path fill="none" data-z-index="1" class="highcharts-grid-line"
                                                      d="M 488.5 10 L 488.5 322" opacity="1"></path>
                                                <path fill="none" data-z-index="1" class="highcharts-grid-line"
                                                      d="M 550.5 10 L 550.5 322" opacity="1"></path>
                                            </g>
                                            <g class="highcharts-grid highcharts-yaxis-grid " data-z-index="1">
                                                <path fill="none" stroke="#e6e6e6" stroke-width="1" data-z-index="1"
                                                      class="highcharts-grid-line" d="M 46 322.5 L 556 322.5" opacity="1">
                                                </path>
                                                <path fill="none" stroke="#e6e6e6" stroke-width="1" data-z-index="1"
                                                      class="highcharts-grid-line" d="M 46 244.5 L 556 244.5" opacity="1">
                                                </path>
                                                <path fill="none" stroke="#e6e6e6" stroke-width="1" data-z-index="1"
                                                      class="highcharts-grid-line" d="M 46 166.5 L 556 166.5" opacity="1">
                                                </path>
                                                <path fill="none" stroke="#e6e6e6" stroke-width="1" data-z-index="1"
                                                      class="highcharts-grid-line" d="M 46 88.5 L 556 88.5" opacity="1">
                                                </path>
                                                <path fill="none" stroke="#e6e6e6" stroke-width="1" data-z-index="1"
                                                      class="highcharts-grid-line" d="M 46 9.5 L 556 9.5" opacity="1">
                                                </path>
                                            </g>
                                            <rect fill="none" class="highcharts-plot-border" data-z-index="1" x="46"
                                                  y="10" width="510" height="312"></rect>
                                            <g class="highcharts-axis highcharts-xaxis " data-z-index="2">
                                                <path fill="none" class="highcharts-tick" stroke="#ccd6eb"
                                                      stroke-width="1" d="M 50.5 322 L 50.5 332" opacity="1"></path>
                                                <path fill="none" class="highcharts-tick" stroke="#ccd6eb"
                                                      stroke-width="1" d="M 113.5 322 L 113.5 332" opacity="1"></path>
                                                <path fill="none" class="highcharts-tick" stroke="#ccd6eb"
                                                      stroke-width="1" d="M 175.5 322 L 175.5 332" opacity="1"></path>
                                                <path fill="none" class="highcharts-tick" stroke="#ccd6eb"
                                                      stroke-width="1" d="M 238.5 322 L 238.5 332" opacity="1"></path>
                                                <path fill="none" class="highcharts-tick" stroke="#ccd6eb"
                                                      stroke-width="1" d="M 300.5 322 L 300.5 332" opacity="1"></path>
                                                <path fill="none" class="highcharts-tick" stroke="#ccd6eb"
                                                      stroke-width="1" d="M 363.5 322 L 363.5 332" opacity="1"></path>
                                                <path fill="none" class="highcharts-tick" stroke="#ccd6eb"
                                                      stroke-width="1" d="M 425.5 322 L 425.5 332" opacity="1"></path>
                                                <path fill="none" class="highcharts-tick" stroke="#ccd6eb"
                                                      stroke-width="1" d="M 488.5 322 L 488.5 332" opacity="1"></path>
                                                <path fill="none" class="highcharts-tick" stroke="#ccd6eb"
                                                      stroke-width="1" d="M 550.5 322 L 550.5 332" opacity="1"></path>
                                                <path fill="none" class="highcharts-axis-line" stroke="#ccd6eb"
                                                      stroke-width="1" data-z-index="7" d="M 46 322.5 L 556 322.5"></path>
                                            </g>
                                            <g class="highcharts-axis highcharts-yaxis " data-z-index="2">
                                                <path fill="none" class="highcharts-axis-line" data-z-index="7"
                                                      d="M 46 10 L 46 322"></path>
                                            </g>
                                            <g class="highcharts-series-group" data-z-index="3">
                                                <g data-z-index="1"
                                                   class="highcharts-series highcharts-series-1 highcharts-line-series highcharts-color-1 "
                                                   transform="translate(46,10) scale(1 1)"
                                                   clip-path="url(#highcharts-2k6bvbd-1)">
                                                    <path fill="none"
                                                          d="M 5 312 L 67.5 312 L 130 312 L 192.5 312 L 255 312 L 317.5 312 L 380 312 L 442.5 312 L 505 312"
                                                          class="highcharts-graph" data-z-index="1" stroke="#c9d1d9"
                                                          stroke-width="2" stroke-linejoin="round" stroke-linecap="round">
                                                    </path>
                                                    <path fill="none"
                                                          d="M -5 312 L 5 312 L 67.5 312 L 130 312 L 192.5 312 L 255 312 L 317.5 312 L 380 312 L 442.5 312 L 505 312 L 515 312"
                                                          stroke-linejoin="round" visibility="visible"
                                                          stroke="rgba(192,192,192,0.0001)" stroke-width="22"
                                                          data-z-index="2" class="highcharts-tracker"></path>
                                                </g>
                                                <g data-z-index="1"
                                                   class="highcharts-markers highcharts-series-1 highcharts-line-series highcharts-color-1  highcharts-tracker"
                                                   transform="translate(46,10) scale(1 1)"></g>
                                                <g data-z-index="2"
                                                   class="highcharts-series highcharts-series-0 highcharts-line-series highcharts-color-0 "
                                                   transform="translate(46,10) scale(1 1)"
                                                   clip-path="url(#highcharts-2k6bvbd-1)">
                                                    <path fill="none"
                                                          d="M 5 312 L 67.5 312 L 130 312 L 192.5 312 L 255 312 L 317.5 312 L 380 312 L 442.5 312 L 505 312"
                                                          class="highcharts-graph" data-z-index="1" stroke="#397eda"
                                                          stroke-width="2" stroke-linejoin="round" stroke-linecap="round">
                                                    </path>
                                                    <path fill="none"
                                                          d="M -5 312 L 5 312 L 67.5 312 L 130 312 L 192.5 312 L 255 312 L 317.5 312 L 380 312 L 442.5 312 L 505 312 L 515 312"
                                                          stroke-linejoin="round" visibility="visible"
                                                          stroke="rgba(192,192,192,0.0001)" stroke-width="22"
                                                          data-z-index="2" class="highcharts-tracker"></path>
                                                </g>
                                                <g data-z-index="2"
                                                   class="highcharts-markers highcharts-series-0 highcharts-line-series highcharts-color-0  highcharts-tracker"
                                                   transform="translate(46,10) scale(1 1)"></g>
                                            </g>
                                            <text x="10" text-anchor="start" class="highcharts-title"
                                                  data-z-index="4" style="color:#333333;font-size:18px;fill:#333333;"
                                                  y="24"></text>
                                            <text x="283" text-anchor="middle"
                                                  class="highcharts-subtitle" data-z-index="4"
                                                  style="color:#666666;fill:#666666;" y="24"></text>
                                            <g class="highcharts-axis-labels highcharts-xaxis-labels " data-z-index="7">
                                                <text x="48"
                                                      style="color:#666666;cursor:default;font-size:11px;fill:#666666;"
                                                      text-anchor="middle" transform="translate(0,0)" y="341"
                                                      opacity="1">00:00</text><text x="110.5"
                                                                                    style="color:#666666;cursor:default;font-size:11px;fill:#666666;"
                                                                                    text-anchor="middle" transform="translate(0,0)" y="341"
                                                                                    opacity="1">03:00</text><text x="173"
                                                                                                                  style="color:#666666;cursor:default;font-size:11px;fill:#666666;"
                                                                                                                  text-anchor="middle" transform="translate(0,0)" y="341"
                                                                                                                  opacity="1">06:00</text><text x="235.5"
                                                                                                                                                style="color:#666666;cursor:default;font-size:11px;fill:#666666;"
                                                                                                                                                text-anchor="middle" transform="translate(0,0)" y="341"
                                                                                                                                                opacity="1">09:00</text><text x="298"
                                                                                                                                                                              style="color:#666666;cursor:default;font-size:11px;fill:#666666;"
                                                                                                                                                                              text-anchor="middle" transform="translate(0,0)" y="341"
                                                                                                                                                                              opacity="1">12:00</text><text x="360.5"
                                                                                                                                                                                                            style="color:#666666;cursor:default;font-size:11px;fill:#666666;"
                                                                                                                                                                                                            text-anchor="middle" transform="translate(0,0)" y="341"
                                                                                                                                                                                                            opacity="1">15:00</text><text x="423"
                                                                                                                                                                                                                                          style="color:#666666;cursor:default;font-size:11px;fill:#666666;"
                                                                                                                                                                                                                                          text-anchor="middle" transform="translate(0,0)" y="341"
                                                                                                                                                                                                                                          opacity="1">18:00</text><text x="485.5"
                                                                                                                                                                                                                                                                        style="color:#666666;cursor:default;font-size:11px;fill:#666666;"
                                                                                                                                                                                                                                                                        text-anchor="middle" transform="translate(0,0)" y="341"
                                                                                                                                                                                                                                                                        opacity="1">21:00</text><text x="540.2264156341553"
                                                                                                                                                                                                                                                                                                      style="color:#666666;cursor:default;font-size:11px;fill:#666666;"
                                                                                                                                                                                                                                                                                                      text-anchor="middle" transform="translate(0,0)" y="341"
                                                                                                                                                                                                                                                                                                      opacity="1">23:00</text>
                                            </g>
                                            <g class="highcharts-axis-labels highcharts-yaxis-labels " data-z-index="7">
                                                <text x="31"
                                                      style="color:#666666;cursor:default;font-size:11px;fill:#666666;"
                                                      text-anchor="end" transform="translate(0,0)" y="326"
                                                      opacity="1">0</text><text x="31"
                                                                                style="color:#666666;cursor:default;font-size:11px;fill:#666666;"
                                                                                text-anchor="end" transform="translate(0,0)" y="248"
                                                                                opacity="1">50</text><text x="31"
                                                                                                           style="color:#666666;cursor:default;font-size:11px;fill:#666666;"
                                                                                                           text-anchor="end" transform="translate(0,0)" y="170"
                                                                                                           opacity="1">100</text><text x="31"
                                                                                                                                       style="color:#666666;cursor:default;font-size:11px;fill:#666666;"
                                                                                                                                       text-anchor="end" transform="translate(0,0)" y="92"
                                                                                                                                       opacity="1">150</text><text x="31"
                                                                                                                                                                   style="color:#666666;cursor:default;font-size:11px;fill:#666666;"
                                                                                                                                                                   text-anchor="end" transform="translate(0,0)" y="14"
                                                                                                                                                                   opacity="1">200</text>
                                            </g>
                                            <g class="highcharts-legend" data-z-index="7"
                                               transform="translate(306,356)">
                                                <rect fill="none" class="highcharts-legend-box" rx="0" ry="0" x="0"
                                                      y="0" width="250" height="29" visibility="visible"></rect>
                                                <g data-z-index="1">
                                                    <g>
                                                        <g class="highcharts-legend-item highcharts-line-series highcharts-color-0 highcharts-series-0"
                                                           data-z-index="1" transform="translate(8,3)">
                                                            <path fill="none" d="M 0 11 L 16 11"
                                                                  class="highcharts-graph" stroke="#397eda"
                                                                  stroke-width="2"></path>
                                                        </g>
                                                        <g class="highcharts-legend-item highcharts-line-series highcharts-color-1 highcharts-series-1"
                                                           data-z-index="1" transform="translate(135,3)">
                                                            <path fill="none" d="M 0 11 L 16 11"
                                                                  class="highcharts-graph" stroke="#c9d1d9"
                                                                  stroke-width="2"></path>
                                                        </g>
                                                    </g>
                                                </g>
                                            </g>
                                        </svg>
                                        <div class="highcharts-legend"
                                             style="position: absolute; left: 306px; top: 356px; opacity: 1;">
                                            <div style="position: absolute; left: 0px; top: 0px; opacity: 1;">
                                                <div style="position: absolute; left: 0px; top: 0px; opacity: 1;">
                                                    <div class="highcharts-legend-item highcharts-line-series highcharts-color-0 highcharts-series-0"
                                                         style="position: absolute; left: 8px; top: 3px; opacity: 1;">
                                                        <span data-z-index="2"
                                                              style="font-family: &quot;Lucida Grande&quot;, &quot;Lucida Sans Unicode&quot;, Arial, Helvetica, sans-serif; font-size: 12px; position: absolute; white-space: nowrap; color: rgb(51, 51, 51); font-weight: bold; text-overflow: ellipsis; cursor: pointer; overflow: hidden; margin-left: 0px; margin-top: 0px; left: 21px; top: 3px; fill: rgb(51, 51, 51);">
                                                            <span style="margin-right:3px;display:inline-block; width:8px; height:8px; border-radius:50%; background-color:#397eda">
                                                            </span><span style="color:#7d8d9e; font-size:12px; font-weight:normal;">2025-05-06</span>
                                                        </span>
                                                    </div>
                                                    <div class="highcharts-legend-item highcharts-line-series highcharts-color-1 highcharts-series-1"
                                                         style="position: absolute; left: 135px; top: 3px; opacity: 1;">
                                                        <span data-z-index="2"
                                                              style="font-family: &quot;Lucida Grande&quot;, &quot;Lucida Sans Unicode&quot;, Arial, Helvetica, sans-serif; font-size: 12px; position: absolute; white-space: nowrap; color: rgb(51, 51, 51); font-weight: bold; text-overflow: ellipsis; cursor: pointer; overflow: hidden; margin-left: 0px; margin-top: 0px; left: 21px; top: 3px; fill: rgb(51, 51, 51);">
                                                            <span style="margin-right:3px;display:inline-block; width:8px; height:8px; border-radius:50%; background-color:#c9d1d9">
                                                            </span><span style="color:#7d8d9e; font-size:12px; font-weight:normal;">2025-05-05</span>
                                                        </span>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </li>
                        <li>
                            <div class="item active_item" style="position: relative;">
                                <h1>已购买客户</h1>
                                <div class="top_info">
                                    <h2><span class="active_member">0</span></h2>
                                    <span class="rate tag down">0%</span>
                                </div>
                                <div id="active_charts" class="contents" data-highcharts-chart="1">
                                    <div id="highcharts-2k6bvbd-4" dir="ltr" class="highcharts-container "
                                         style="position: relative; overflow: hidden; width: 566px; height: 400px; text-align: left; line-height: normal; z-index: 0; -webkit-tap-highlight-color: rgba(0, 0, 0, 0);">
                                        <svg version="1.1" class="highcharts-root"
                                             style="font-family:&quot;Lucida Grande&quot;, &quot;Lucida Sans Unicode&quot;, Arial, Helvetica, sans-serif;font-size:12px;"
                                             xmlns="http://www.w3.org/2000/svg" width="566" height="400"
                                             viewBox="0 0 566 400">
                                            <desc>Created with Highcharts 6.1.1</desc>
                                            <defs>
                                                <clipPath id="highcharts-2k6bvbd-5">
                                                    <rect x="0" y="0" width="510" height="312" fill="none"></rect>
                                                </clipPath>
                                            </defs>
                                            <rect fill="#ffffff" class="highcharts-background" x="0" y="0" width="566"
                                                  height="400" rx="0" ry="0"></rect>
                                            <rect fill="none" class="highcharts-plot-background" x="46" y="10"
                                                  width="510" height="312"></rect>
                                            <g class="highcharts-grid highcharts-xaxis-grid " data-z-index="1">
                                                <path fill="none" data-z-index="1" class="highcharts-grid-line"
                                                      d="M 50.5 10 L 50.5 322" opacity="1"></path>
                                                <path fill="none" data-z-index="1" class="highcharts-grid-line"
                                                      d="M 113.5 10 L 113.5 322" opacity="1"></path>
                                                <path fill="none" data-z-index="1" class="highcharts-grid-line"
                                                      d="M 175.5 10 L 175.5 322" opacity="1"></path>
                                                <path fill="none" data-z-index="1" class="highcharts-grid-line"
                                                      d="M 238.5 10 L 238.5 322" opacity="1"></path>
                                                <path fill="none" data-z-index="1" class="highcharts-grid-line"
                                                      d="M 300.5 10 L 300.5 322" opacity="1"></path>
                                                <path fill="none" data-z-index="1" class="highcharts-grid-line"
                                                      d="M 363.5 10 L 363.5 322" opacity="1"></path>
                                                <path fill="none" data-z-index="1" class="highcharts-grid-line"
                                                      d="M 425.5 10 L 425.5 322" opacity="1"></path>
                                                <path fill="none" data-z-index="1" class="highcharts-grid-line"
                                                      d="M 488.5 10 L 488.5 322" opacity="1"></path>
                                                <path fill="none" data-z-index="1" class="highcharts-grid-line"
                                                      d="M 550.5 10 L 550.5 322" opacity="1"></path>
                                            </g>
                                            <g class="highcharts-grid highcharts-yaxis-grid " data-z-index="1">
                                                <path fill="none" stroke="#e6e6e6" stroke-width="1" data-z-index="1"
                                                      class="highcharts-grid-line" d="M 46 322.5 L 556 322.5" opacity="1">
                                                </path>
                                                <path fill="none" stroke="#e6e6e6" stroke-width="1" data-z-index="1"
                                                      class="highcharts-grid-line" d="M 46 244.5 L 556 244.5" opacity="1">
                                                </path>
                                                <path fill="none" stroke="#e6e6e6" stroke-width="1" data-z-index="1"
                                                      class="highcharts-grid-line" d="M 46 166.5 L 556 166.5" opacity="1">
                                                </path>
                                                <path fill="none" stroke="#e6e6e6" stroke-width="1" data-z-index="1"
                                                      class="highcharts-grid-line" d="M 46 88.5 L 556 88.5" opacity="1">
                                                </path>
                                                <path fill="none" stroke="#e6e6e6" stroke-width="1" data-z-index="1"
                                                      class="highcharts-grid-line" d="M 46 9.5 L 556 9.5" opacity="1">
                                                </path>
                                            </g>
                                            <rect fill="none" class="highcharts-plot-border" data-z-index="1" x="46"
                                                  y="10" width="510" height="312"></rect>
                                            <g class="highcharts-axis highcharts-xaxis " data-z-index="2">
                                                <path fill="none" class="highcharts-tick" stroke="#ccd6eb"
                                                      stroke-width="1" d="M 50.5 322 L 50.5 332" opacity="1"></path>
                                                <path fill="none" class="highcharts-tick" stroke="#ccd6eb"
                                                      stroke-width="1" d="M 113.5 322 L 113.5 332" opacity="1"></path>
                                                <path fill="none" class="highcharts-tick" stroke="#ccd6eb"
                                                      stroke-width="1" d="M 175.5 322 L 175.5 332" opacity="1"></path>
                                                <path fill="none" class="highcharts-tick" stroke="#ccd6eb"
                                                      stroke-width="1" d="M 238.5 322 L 238.5 332" opacity="1"></path>
                                                <path fill="none" class="highcharts-tick" stroke="#ccd6eb"
                                                      stroke-width="1" d="M 300.5 322 L 300.5 332" opacity="1"></path>
                                                <path fill="none" class="highcharts-tick" stroke="#ccd6eb"
                                                      stroke-width="1" d="M 363.5 322 L 363.5 332" opacity="1"></path>
                                                <path fill="none" class="highcharts-tick" stroke="#ccd6eb"
                                                      stroke-width="1" d="M 425.5 322 L 425.5 332" opacity="1"></path>
                                                <path fill="none" class="highcharts-tick" stroke="#ccd6eb"
                                                      stroke-width="1" d="M 488.5 322 L 488.5 332" opacity="1"></path>
                                                <path fill="none" class="highcharts-tick" stroke="#ccd6eb"
                                                      stroke-width="1" d="M 550.5 322 L 550.5 332" opacity="1"></path>
                                                <path fill="none" class="highcharts-axis-line" stroke="#ccd6eb"
                                                      stroke-width="1" data-z-index="7" d="M 46 322.5 L 556 322.5"></path>
                                            </g>
                                            <g class="highcharts-axis highcharts-yaxis " data-z-index="2">
                                                <path fill="none" class="highcharts-axis-line" data-z-index="7"
                                                      d="M 46 10 L 46 322"></path>
                                            </g>
                                            <g class="highcharts-series-group" data-z-index="3">
                                                <g data-z-index="1"
                                                   class="highcharts-series highcharts-series-1 highcharts-line-series highcharts-color-1 "
                                                   transform="translate(46,10) scale(1 1)"
                                                   clip-path="url(#highcharts-2k6bvbd-5)">
                                                    <path fill="none"
                                                          d="M 5 312 L 67.5 312 L 130 312 L 192.5 312 L 255 312 L 317.5 312 L 380 312 L 442.5 312 L 505 312"
                                                          class="highcharts-graph" data-z-index="1" stroke="#c9d1d9"
                                                          stroke-width="2" stroke-linejoin="round" stroke-linecap="round">
                                                    </path>
                                                    <path fill="none"
                                                          d="M -5 312 L 5 312 L 67.5 312 L 130 312 L 192.5 312 L 255 312 L 317.5 312 L 380 312 L 442.5 312 L 505 312 L 515 312"
                                                          stroke-linejoin="round" visibility="visible"
                                                          stroke="rgba(192,192,192,0.0001)" stroke-width="22"
                                                          data-z-index="2" class="highcharts-tracker"></path>
                                                </g>
                                                <g data-z-index="1"
                                                   class="highcharts-markers highcharts-series-1 highcharts-line-series highcharts-color-1  highcharts-tracker"
                                                   transform="translate(46,10) scale(1 1)"></g>
                                                <g data-z-index="2"
                                                   class="highcharts-series highcharts-series-0 highcharts-line-series highcharts-color-0 "
                                                   transform="translate(46,10) scale(1 1)"
                                                   clip-path="url(#highcharts-2k6bvbd-5)">
                                                    <path fill="none"
                                                          d="M 5 312 L 67.5 312 L 130 312 L 192.5 312 L 255 312 L 317.5 312 L 380 312 L 442.5 312 L 505 312"
                                                          class="highcharts-graph" data-z-index="1" stroke="#397eda"
                                                          stroke-width="2" stroke-linejoin="round" stroke-linecap="round">
                                                    </path>
                                                    <path fill="none"
                                                          d="M -5 312 L 5 312 L 67.5 312 L 130 312 L 192.5 312 L 255 312 L 317.5 312 L 380 312 L 442.5 312 L 505 312 L 515 312"
                                                          stroke-linejoin="round" visibility="visible"
                                                          stroke="rgba(192,192,192,0.0001)" stroke-width="22"
                                                          data-z-index="2" class="highcharts-tracker"></path>
                                                </g>
                                                <g data-z-index="2"
                                                   class="highcharts-markers highcharts-series-0 highcharts-line-series highcharts-color-0  highcharts-tracker"
                                                   transform="translate(46,10) scale(1 1)"></g>
                                            </g>
                                            <text x="10" text-anchor="start" class="highcharts-title"
                                                  data-z-index="4" style="color:#333333;font-size:18px;fill:#333333;"
                                                  y="24"></text>
                                            <text x="283" text-anchor="middle"
                                                  class="highcharts-subtitle" data-z-index="4"
                                                  style="color:#666666;fill:#666666;" y="24"></text>
                                            <g class="highcharts-axis-labels highcharts-xaxis-labels " data-z-index="7">
                                                <text x="48"
                                                      style="color:#666666;cursor:default;font-size:11px;fill:#666666;"
                                                      text-anchor="middle" transform="translate(0,0)" y="341"
                                                      opacity="1">00:00</text><text x="110.5"
                                                                                    style="color:#666666;cursor:default;font-size:11px;fill:#666666;"
                                                                                    text-anchor="middle" transform="translate(0,0)" y="341"
                                                                                    opacity="1">03:00</text><text x="173"
                                                                                                                  style="color:#666666;cursor:default;font-size:11px;fill:#666666;"
                                                                                                                  text-anchor="middle" transform="translate(0,0)" y="341"
                                                                                                                  opacity="1">06:00</text><text x="235.5"
                                                                                                                                                style="color:#666666;cursor:default;font-size:11px;fill:#666666;"
                                                                                                                                                text-anchor="middle" transform="translate(0,0)" y="341"
                                                                                                                                                opacity="1">09:00</text><text x="298"
                                                                                                                                                                              style="color:#666666;cursor:default;font-size:11px;fill:#666666;"
                                                                                                                                                                              text-anchor="middle" transform="translate(0,0)" y="341"
                                                                                                                                                                              opacity="1">12:00</text><text x="360.5"
                                                                                                                                                                                                            style="color:#666666;cursor:default;font-size:11px;fill:#666666;"
                                                                                                                                                                                                            text-anchor="middle" transform="translate(0,0)" y="341"
                                                                                                                                                                                                            opacity="1">15:00</text><text x="423"
                                                                                                                                                                                                                                          style="color:#666666;cursor:default;font-size:11px;fill:#666666;"
                                                                                                                                                                                                                                          text-anchor="middle" transform="translate(0,0)" y="341"
                                                                                                                                                                                                                                          opacity="1">18:00</text><text x="485.5"
                                                                                                                                                                                                                                                                        style="color:#666666;cursor:default;font-size:11px;fill:#666666;"
                                                                                                                                                                                                                                                                        text-anchor="middle" transform="translate(0,0)" y="341"
                                                                                                                                                                                                                                                                        opacity="1">21:00</text><text x="540.2264156341553"
                                                                                                                                                                                                                                                                                                      style="color:#666666;cursor:default;font-size:11px;fill:#666666;"
                                                                                                                                                                                                                                                                                                      text-anchor="middle" transform="translate(0,0)" y="341"
                                                                                                                                                                                                                                                                                                      opacity="1">23:00</text>
                                            </g>
                                            <g class="highcharts-axis-labels highcharts-yaxis-labels " data-z-index="7">
                                                <text x="31"
                                                      style="color:#666666;cursor:default;font-size:11px;fill:#666666;"
                                                      text-anchor="end" transform="translate(0,0)" y="326"
                                                      opacity="1">0</text><text x="31"
                                                                                style="color:#666666;cursor:default;font-size:11px;fill:#666666;"
                                                                                text-anchor="end" transform="translate(0,0)" y="248"
                                                                                opacity="1">50</text><text x="31"
                                                                                                           style="color:#666666;cursor:default;font-size:11px;fill:#666666;"
                                                                                                           text-anchor="end" transform="translate(0,0)" y="170"
                                                                                                           opacity="1">100</text><text x="31"
                                                                                                                                       style="color:#666666;cursor:default;font-size:11px;fill:#666666;"
                                                                                                                                       text-anchor="end" transform="translate(0,0)" y="92"
                                                                                                                                       opacity="1">150</text><text x="31"
                                                                                                                                                                   style="color:#666666;cursor:default;font-size:11px;fill:#666666;"
                                                                                                                                                                   text-anchor="end" transform="translate(0,0)" y="14"
                                                                                                                                                                   opacity="1">200</text>
                                            </g>
                                            <g class="highcharts-legend" data-z-index="7"
                                               transform="translate(306,356)">
                                                <rect fill="none" class="highcharts-legend-box" rx="0" ry="0" x="0"
                                                      y="0" width="250" height="29" visibility="visible"></rect>
                                                <g data-z-index="1">
                                                    <g>
                                                        <g class="highcharts-legend-item highcharts-line-series highcharts-color-0 highcharts-series-0"
                                                           data-z-index="1" transform="translate(8,3)">
                                                            <path fill="none" d="M 0 11 L 16 11"
                                                                  class="highcharts-graph" stroke="#397eda"
                                                                  stroke-width="2"></path>
                                                        </g>
                                                        <g class="highcharts-legend-item highcharts-line-series highcharts-color-1 highcharts-series-1"
                                                           data-z-index="1" transform="translate(135,3)">
                                                            <path fill="none" d="M 0 11 L 16 11"
                                                                  class="highcharts-graph" stroke="#c9d1d9"
                                                                  stroke-width="2"></path>
                                                        </g>
                                                    </g>
                                                </g>
                                            </g>
                                        </svg>
                                        <div class="highcharts-legend"
                                             style="position: absolute; left: 306px; top: 356px; opacity: 1;">
                                            <div style="position: absolute; left: 0px; top: 0px; opacity: 1;">
                                                <div style="position: absolute; left: 0px; top: 0px; opacity: 1;">
                                                    <div class="highcharts-legend-item highcharts-line-series highcharts-color-0 highcharts-series-0"
                                                         style="position: absolute; left: 8px; top: 3px; opacity: 1;">
                                                        <span data-z-index="2"
                                                              style="font-family: &quot;Lucida Grande&quot;, &quot;Lucida Sans Unicode&quot;, Arial, Helvetica, sans-serif; font-size: 12px; position: absolute; white-space: nowrap; color: rgb(51, 51, 51); font-weight: bold; text-overflow: ellipsis; cursor: pointer; overflow: hidden; margin-left: 0px; margin-top: 0px; left: 21px; top: 3px; fill: rgb(51, 51, 51);">
                                                            <span style="margin-right:3px;display:inline-block; width:8px; height:8px; border-radius:50%; background-color:#397eda">
                                                            </span><span style="color:#7d8d9e; font-size:12px; font-weight:normal;">2025-05-06</span>
                                                        </span>
                                                    </div>
                                                    <div class="highcharts-legend-item highcharts-line-series highcharts-color-1 highcharts-series-1"
                                                         style="position: absolute; left: 135px; top: 3px; opacity: 1;">
                                                        <span data-z-index="2"
                                                              style="font-family: &quot;Lucida Grande&quot;, &quot;Lucida Sans Unicode&quot;, Arial, Helvetica, sans-serif; font-size: 12px; position: absolute; white-space: nowrap; color: rgb(51, 51, 51); font-weight: bold; text-overflow: ellipsis; cursor: pointer; overflow: hidden; margin-left: 0px; margin-top: 0px; left: 21px; top: 3px; fill: rgb(51, 51, 51);">
                                                            <span style="margin-right:3px;display:inline-block; width:8px; height:8px; border-radius:50%; background-color:#c9d1d9">
                                                            </span><span style="color:#7d8d9e; font-size:12px; font-weight:normal;">2025-05-05</span>
                                                        </span>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </li>
                        <li>
                            <div class="item core_item" style="position: relative;">
                                <h1>复购客户</h1>
                                <div class="top_info">
                                    <h2><span class="core_member">0</span></h2>
                                    <span class="rate tag down">0%</span>
                                </div>
                                <div id="core_charts" class="contents" data-highcharts-chart="2">
                                    <div id="highcharts-2k6bvbd-8" dir="ltr" class="highcharts-container "
                                         style="position: relative; overflow: hidden; width: 566px; height: 400px; text-align: left; line-height: normal; z-index: 0; -webkit-tap-highlight-color: rgba(0, 0, 0, 0);">
                                        <svg version="1.1" class="highcharts-root"
                                             style="font-family:&quot;Lucida Grande&quot;, &quot;Lucida Sans Unicode&quot;, Arial, Helvetica, sans-serif;font-size:12px;"
                                             xmlns="http://www.w3.org/2000/svg" width="566" height="400"
                                             viewBox="0 0 566 400">
                                            <desc>Created with Highcharts 6.1.1</desc>
                                            <defs>
                                                <clipPath id="highcharts-2k6bvbd-9">
                                                    <rect x="0" y="0" width="510" height="312" fill="none"></rect>
                                                </clipPath>
                                            </defs>
                                            <rect fill="#ffffff" class="highcharts-background" x="0" y="0" width="566"
                                                  height="400" rx="0" ry="0"></rect>
                                            <rect fill="none" class="highcharts-plot-background" x="46" y="10"
                                                  width="510" height="312"></rect>
                                            <g class="highcharts-grid highcharts-xaxis-grid " data-z-index="1">
                                                <path fill="none" data-z-index="1" class="highcharts-grid-line"
                                                      d="M 50.5 10 L 50.5 322" opacity="1"></path>
                                                <path fill="none" data-z-index="1" class="highcharts-grid-line"
                                                      d="M 113.5 10 L 113.5 322" opacity="1"></path>
                                                <path fill="none" data-z-index="1" class="highcharts-grid-line"
                                                      d="M 175.5 10 L 175.5 322" opacity="1"></path>
                                                <path fill="none" data-z-index="1" class="highcharts-grid-line"
                                                      d="M 238.5 10 L 238.5 322" opacity="1"></path>
                                                <path fill="none" data-z-index="1" class="highcharts-grid-line"
                                                      d="M 300.5 10 L 300.5 322" opacity="1"></path>
                                                <path fill="none" data-z-index="1" class="highcharts-grid-line"
                                                      d="M 363.5 10 L 363.5 322" opacity="1"></path>
                                                <path fill="none" data-z-index="1" class="highcharts-grid-line"
                                                      d="M 425.5 10 L 425.5 322" opacity="1"></path>
                                                <path fill="none" data-z-index="1" class="highcharts-grid-line"
                                                      d="M 488.5 10 L 488.5 322" opacity="1"></path>
                                                <path fill="none" data-z-index="1" class="highcharts-grid-line"
                                                      d="M 550.5 10 L 550.5 322" opacity="1"></path>
                                            </g>
                                            <g class="highcharts-grid highcharts-yaxis-grid " data-z-index="1">
                                                <path fill="none" stroke="#e6e6e6" stroke-width="1" data-z-index="1"
                                                      class="highcharts-grid-line" d="M 46 322.5 L 556 322.5" opacity="1">
                                                </path>
                                                <path fill="none" stroke="#e6e6e6" stroke-width="1" data-z-index="1"
                                                      class="highcharts-grid-line" d="M 46 244.5 L 556 244.5" opacity="1">
                                                </path>
                                                <path fill="none" stroke="#e6e6e6" stroke-width="1" data-z-index="1"
                                                      class="highcharts-grid-line" d="M 46 166.5 L 556 166.5" opacity="1">
                                                </path>
                                                <path fill="none" stroke="#e6e6e6" stroke-width="1" data-z-index="1"
                                                      class="highcharts-grid-line" d="M 46 88.5 L 556 88.5" opacity="1">
                                                </path>
                                                <path fill="none" stroke="#e6e6e6" stroke-width="1" data-z-index="1"
                                                      class="highcharts-grid-line" d="M 46 9.5 L 556 9.5" opacity="1">
                                                </path>
                                            </g>
                                            <rect fill="none" class="highcharts-plot-border" data-z-index="1" x="46"
                                                  y="10" width="510" height="312"></rect>
                                            <g class="highcharts-axis highcharts-xaxis " data-z-index="2">
                                                <path fill="none" class="highcharts-tick" stroke="#ccd6eb"
                                                      stroke-width="1" d="M 50.5 322 L 50.5 332" opacity="1"></path>
                                                <path fill="none" class="highcharts-tick" stroke="#ccd6eb"
                                                      stroke-width="1" d="M 113.5 322 L 113.5 332" opacity="1"></path>
                                                <path fill="none" class="highcharts-tick" stroke="#ccd6eb"
                                                      stroke-width="1" d="M 175.5 322 L 175.5 332" opacity="1"></path>
                                                <path fill="none" class="highcharts-tick" stroke="#ccd6eb"
                                                      stroke-width="1" d="M 238.5 322 L 238.5 332" opacity="1"></path>
                                                <path fill="none" class="highcharts-tick" stroke="#ccd6eb"
                                                      stroke-width="1" d="M 300.5 322 L 300.5 332" opacity="1"></path>
                                                <path fill="none" class="highcharts-tick" stroke="#ccd6eb"
                                                      stroke-width="1" d="M 363.5 322 L 363.5 332" opacity="1"></path>
                                                <path fill="none" class="highcharts-tick" stroke="#ccd6eb"
                                                      stroke-width="1" d="M 425.5 322 L 425.5 332" opacity="1"></path>
                                                <path fill="none" class="highcharts-tick" stroke="#ccd6eb"
                                                      stroke-width="1" d="M 488.5 322 L 488.5 332" opacity="1"></path>
                                                <path fill="none" class="highcharts-tick" stroke="#ccd6eb"
                                                      stroke-width="1" d="M 550.5 322 L 550.5 332" opacity="1"></path>
                                                <path fill="none" class="highcharts-axis-line" stroke="#ccd6eb"
                                                      stroke-width="1" data-z-index="7" d="M 46 322.5 L 556 322.5"></path>
                                            </g>
                                            <g class="highcharts-axis highcharts-yaxis " data-z-index="2">
                                                <path fill="none" class="highcharts-axis-line" data-z-index="7"
                                                      d="M 46 10 L 46 322"></path>
                                            </g>
                                            <g class="highcharts-series-group" data-z-index="3">
                                                <g data-z-index="1"
                                                   class="highcharts-series highcharts-series-1 highcharts-line-series highcharts-color-1 "
                                                   transform="translate(46,10) scale(1 1)"
                                                   clip-path="url(#highcharts-2k6bvbd-9)">
                                                    <path fill="none"
                                                          d="M 5 312 L 67.5 312 L 130 312 L 192.5 312 L 255 312 L 317.5 312 L 380 312 L 442.5 312 L 505 312"
                                                          class="highcharts-graph" data-z-index="1" stroke="#c9d1d9"
                                                          stroke-width="2" stroke-linejoin="round" stroke-linecap="round">
                                                    </path>
                                                    <path fill="none"
                                                          d="M -5 312 L 5 312 L 67.5 312 L 130 312 L 192.5 312 L 255 312 L 317.5 312 L 380 312 L 442.5 312 L 505 312 L 515 312"
                                                          stroke-linejoin="round" visibility="visible"
                                                          stroke="rgba(192,192,192,0.0001)" stroke-width="22"
                                                          data-z-index="2" class="highcharts-tracker"></path>
                                                </g>
                                                <g data-z-index="1"
                                                   class="highcharts-markers highcharts-series-1 highcharts-line-series highcharts-color-1  highcharts-tracker"
                                                   transform="translate(46,10) scale(1 1)"></g>
                                                <g data-z-index="2"
                                                   class="highcharts-series highcharts-series-0 highcharts-line-series highcharts-color-0 "
                                                   transform="translate(46,10) scale(1 1)"
                                                   clip-path="url(#highcharts-2k6bvbd-9)">
                                                    <path fill="none"
                                                          d="M 5 312 L 67.5 312 L 130 312 L 192.5 312 L 255 312 L 317.5 312 L 380 312 L 442.5 312 L 505 312"
                                                          class="highcharts-graph" data-z-index="1" stroke="#397eda"
                                                          stroke-width="2" stroke-linejoin="round" stroke-linecap="round">
                                                    </path>
                                                    <path fill="none"
                                                          d="M -5 312 L 5 312 L 67.5 312 L 130 312 L 192.5 312 L 255 312 L 317.5 312 L 380 312 L 442.5 312 L 505 312 L 515 312"
                                                          stroke-linejoin="round" visibility="visible"
                                                          stroke="rgba(192,192,192,0.0001)" stroke-width="22"
                                                          data-z-index="2" class="highcharts-tracker"></path>
                                                </g>
                                                <g data-z-index="2"
                                                   class="highcharts-markers highcharts-series-0 highcharts-line-series highcharts-color-0  highcharts-tracker"
                                                   transform="translate(46,10) scale(1 1)"></g>
                                            </g>
                                            <text x="10" text-anchor="start" class="highcharts-title"
                                                  data-z-index="4" style="color:#333333;font-size:18px;fill:#333333;"
                                                  y="24"></text>
                                            <text x="283" text-anchor="middle"
                                                  class="highcharts-subtitle" data-z-index="4"
                                                  style="color:#666666;fill:#666666;" y="24"></text>
                                            <g class="highcharts-axis-labels highcharts-xaxis-labels " data-z-index="7">
                                                <text x="48"
                                                      style="color:#666666;cursor:default;font-size:11px;fill:#666666;"
                                                      text-anchor="middle" transform="translate(0,0)" y="341"
                                                      opacity="1">00:00</text><text x="110.5"
                                                                                    style="color:#666666;cursor:default;font-size:11px;fill:#666666;"
                                                                                    text-anchor="middle" transform="translate(0,0)" y="341"
                                                                                    opacity="1">03:00</text><text x="173"
                                                                                                                  style="color:#666666;cursor:default;font-size:11px;fill:#666666;"
                                                                                                                  text-anchor="middle" transform="translate(0,0)" y="341"
                                                                                                                  opacity="1">06:00</text><text x="235.5"
                                                                                                                                                style="color:#666666;cursor:default;font-size:11px;fill:#666666;"
                                                                                                                                                text-anchor="middle" transform="translate(0,0)" y="341"
                                                                                                                                                opacity="1">09:00</text><text x="298"
                                                                                                                                                                              style="color:#666666;cursor:default;font-size:11px;fill:#666666;"
                                                                                                                                                                              text-anchor="middle" transform="translate(0,0)" y="341"
                                                                                                                                                                              opacity="1">12:00</text><text x="360.5"
                                                                                                                                                                                                            style="color:#666666;cursor:default;font-size:11px;fill:#666666;"
                                                                                                                                                                                                            text-anchor="middle" transform="translate(0,0)" y="341"
                                                                                                                                                                                                            opacity="1">15:00</text><text x="423"
                                                                                                                                                                                                                                          style="color:#666666;cursor:default;font-size:11px;fill:#666666;"
                                                                                                                                                                                                                                          text-anchor="middle" transform="translate(0,0)" y="341"
                                                                                                                                                                                                                                          opacity="1">18:00</text><text x="485.5"
                                                                                                                                                                                                                                                                        style="color:#666666;cursor:default;font-size:11px;fill:#666666;"
                                                                                                                                                                                                                                                                        text-anchor="middle" transform="translate(0,0)" y="341"
                                                                                                                                                                                                                                                                        opacity="1">21:00</text><text x="540.2264156341553"
                                                                                                                                                                                                                                                                                                      style="color:#666666;cursor:default;font-size:11px;fill:#666666;"
                                                                                                                                                                                                                                                                                                      text-anchor="middle" transform="translate(0,0)" y="341"
                                                                                                                                                                                                                                                                                                      opacity="1">23:00</text>
                                            </g>
                                            <g class="highcharts-axis-labels highcharts-yaxis-labels " data-z-index="7">
                                                <text x="31"
                                                      style="color:#666666;cursor:default;font-size:11px;fill:#666666;"
                                                      text-anchor="end" transform="translate(0,0)" y="326"
                                                      opacity="1">0</text><text x="31"
                                                                                style="color:#666666;cursor:default;font-size:11px;fill:#666666;"
                                                                                text-anchor="end" transform="translate(0,0)" y="248"
                                                                                opacity="1">50</text><text x="31"
                                                                                                           style="color:#666666;cursor:default;font-size:11px;fill:#666666;"
                                                                                                           text-anchor="end" transform="translate(0,0)" y="170"
                                                                                                           opacity="1">100</text><text x="31"
                                                                                                                                       style="color:#666666;cursor:default;font-size:11px;fill:#666666;"
                                                                                                                                       text-anchor="end" transform="translate(0,0)" y="92"
                                                                                                                                       opacity="1">150</text><text x="31"
                                                                                                                                                                   style="color:#666666;cursor:default;font-size:11px;fill:#666666;"
                                                                                                                                                                   text-anchor="end" transform="translate(0,0)" y="14"
                                                                                                                                                                   opacity="1">200</text>
                                            </g>
                                            <g class="highcharts-legend" data-z-index="7"
                                               transform="translate(306,356)">
                                                <rect fill="none" class="highcharts-legend-box" rx="0" ry="0" x="0"
                                                      y="0" width="250" height="29" visibility="visible"></rect>
                                                <g data-z-index="1">
                                                    <g>
                                                        <g class="highcharts-legend-item highcharts-line-series highcharts-color-0 highcharts-series-0"
                                                           data-z-index="1" transform="translate(8,3)">
                                                            <path fill="none" d="M 0 11 L 16 11"
                                                                  class="highcharts-graph" stroke="#397eda"
                                                                  stroke-width="2"></path>
                                                        </g>
                                                        <g class="highcharts-legend-item highcharts-line-series highcharts-color-1 highcharts-series-1"
                                                           data-z-index="1" transform="translate(135,3)">
                                                            <path fill="none" d="M 0 11 L 16 11"
                                                                  class="highcharts-graph" stroke="#c9d1d9"
                                                                  stroke-width="2"></path>
                                                        </g>
                                                    </g>
                                                </g>
                                            </g>
                                        </svg>
                                        <div class="highcharts-legend"
                                             style="position: absolute; left: 306px; top: 356px; opacity: 1;">
                                            <div style="position: absolute; left: 0px; top: 0px; opacity: 1;">
                                                <div style="position: absolute; left: 0px; top: 0px; opacity: 1;">
                                                    <div class="highcharts-legend-item highcharts-line-series highcharts-color-0 highcharts-series-0"
                                                         style="position: absolute; left: 8px; top: 3px; opacity: 1;">
                                                        <span data-z-index="2"
                                                              style="font-family: &quot;Lucida Grande&quot;, &quot;Lucida Sans Unicode&quot;, Arial, Helvetica, sans-serif; font-size: 12px; position: absolute; white-space: nowrap; color: rgb(51, 51, 51); font-weight: bold; text-overflow: ellipsis; cursor: pointer; overflow: hidden; margin-left: 0px; margin-top: 0px; left: 21px; top: 3px; fill: rgb(51, 51, 51);">
                                                            <span style="margin-right:3px;display:inline-block; width:8px; height:8px; border-radius:50%; background-color:#397eda">
                                                            </span><span style="color:#7d8d9e; font-size:12px; font-weight:normal;">2025-05-06</span>
                                                        </span>
                                                    </div>
                                                    <div class="highcharts-legend-item highcharts-line-series highcharts-color-1 highcharts-series-1"
                                                         style="position: absolute; left: 135px; top: 3px; opacity: 1;">
                                                        <span data-z-index="2"
                                                              style="font-family: &quot;Lucida Grande&quot;, &quot;Lucida Sans Unicode&quot;, Arial, Helvetica, sans-serif; font-size: 12px; position: absolute; white-space: nowrap; color: rgb(51, 51, 51); font-weight: bold; text-overflow: ellipsis; cursor: pointer; overflow: hidden; margin-left: 0px; margin-top: 0px; left: 21px; top: 3px; fill: rgb(51, 51, 51);">
                                                            <span style="margin-right:3px;display:inline-block; width:8px; height:8px; border-radius:50%; background-color:#c9d1d9">
                                                            </span><span style="color:#7d8d9e; font-size:12px; font-weight:normal;">2025-05-05</span>
                                                        </span>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </li>
                        <li>
                            <div class="item total_item" style="position: relative;">
                                <h1>客户总数</h1>
                                <div class="top_info">
                                    <h2><span class="total_member">35</span></h2>
                                    <span class="rate tag up">2.94%</span>
                                </div>
                                <div id="total_charts" class="contents" data-highcharts-chart="3">
                                    <div id="highcharts-2k6bvbd-12" dir="ltr" class="highcharts-container "
                                         style="position: relative; overflow: hidden; width: 566px; height: 400px; text-align: left; line-height: normal; z-index: 0; -webkit-tap-highlight-color: rgba(0, 0, 0, 0);">
                                        <svg version="1.1" class="highcharts-root"
                                             style="font-family:&quot;Lucida Grande&quot;, &quot;Lucida Sans Unicode&quot;, Arial, Helvetica, sans-serif;font-size:12px;"
                                             xmlns="http://www.w3.org/2000/svg" width="566" height="400"
                                             viewBox="0 0 566 400">
                                            <desc>Created with Highcharts 6.1.1</desc>
                                            <defs>
                                                <clipPath id="highcharts-2k6bvbd-13">
                                                    <rect x="0" y="0" width="510" height="312" fill="none"></rect>
                                                </clipPath>
                                            </defs>
                                            <rect fill="#ffffff" class="highcharts-background" x="0" y="0" width="566"
                                                  height="400" rx="0" ry="0"></rect>
                                            <rect fill="none" class="highcharts-plot-background" x="46" y="10"
                                                  width="510" height="312"></rect>
                                            <g class="highcharts-grid highcharts-xaxis-grid " data-z-index="1">
                                                <path fill="none" data-z-index="1" class="highcharts-grid-line"
                                                      d="M 50.5 10 L 50.5 322" opacity="1"></path>
                                                <path fill="none" data-z-index="1" class="highcharts-grid-line"
                                                      d="M 133.5 10 L 133.5 322" opacity="1"></path>
                                                <path fill="none" data-z-index="1" class="highcharts-grid-line"
                                                      d="M 217.5 10 L 217.5 322" opacity="1"></path>
                                                <path fill="none" data-z-index="1" class="highcharts-grid-line"
                                                      d="M 300.5 10 L 300.5 322" opacity="1"></path>
                                                <path fill="none" data-z-index="1" class="highcharts-grid-line"
                                                      d="M 383.5 10 L 383.5 322" opacity="1"></path>
                                                <path fill="none" data-z-index="1" class="highcharts-grid-line"
                                                      d="M 467.5 10 L 467.5 322" opacity="1"></path>
                                                <path fill="none" data-z-index="1" class="highcharts-grid-line"
                                                      d="M 550.5 10 L 550.5 322" opacity="1"></path>
                                            </g>
                                            <g class="highcharts-grid highcharts-yaxis-grid " data-z-index="1">
                                                <path fill="none" stroke="#e6e6e6" stroke-width="1" data-z-index="1"
                                                      class="highcharts-grid-line" d="M 46 322.5 L 556 322.5" opacity="1">
                                                </path>
                                                <path fill="none" stroke="#e6e6e6" stroke-width="1" data-z-index="1"
                                                      class="highcharts-grid-line" d="M 46 244.5 L 556 244.5" opacity="1">
                                                </path>
                                                <path fill="none" stroke="#e6e6e6" stroke-width="1" data-z-index="1"
                                                      class="highcharts-grid-line" d="M 46 166.5 L 556 166.5" opacity="1">
                                                </path>
                                                <path fill="none" stroke="#e6e6e6" stroke-width="1" data-z-index="1"
                                                      class="highcharts-grid-line" d="M 46 88.5 L 556 88.5" opacity="1">
                                                </path>
                                                <path fill="none" stroke="#e6e6e6" stroke-width="1" data-z-index="1"
                                                      class="highcharts-grid-line" d="M 46 9.5 L 556 9.5" opacity="1">
                                                </path>
                                            </g>
                                            <rect fill="none" class="highcharts-plot-border" data-z-index="1" x="46"
                                                  y="10" width="510" height="312"></rect>
                                            <g class="highcharts-axis highcharts-xaxis " data-z-index="2">
                                                <path fill="none" class="highcharts-tick" stroke="#ccd6eb"
                                                      stroke-width="1" d="M 50.5 322 L 50.5 332" opacity="1"></path>
                                                <path fill="none" class="highcharts-tick" stroke="#ccd6eb"
                                                      stroke-width="1" d="M 133.5 322 L 133.5 332" opacity="1"></path>
                                                <path fill="none" class="highcharts-tick" stroke="#ccd6eb"
                                                      stroke-width="1" d="M 217.5 322 L 217.5 332" opacity="1"></path>
                                                <path fill="none" class="highcharts-tick" stroke="#ccd6eb"
                                                      stroke-width="1" d="M 300.5 322 L 300.5 332" opacity="1"></path>
                                                <path fill="none" class="highcharts-tick" stroke="#ccd6eb"
                                                      stroke-width="1" d="M 383.5 322 L 383.5 332" opacity="1"></path>
                                                <path fill="none" class="highcharts-tick" stroke="#ccd6eb"
                                                      stroke-width="1" d="M 467.5 322 L 467.5 332" opacity="1"></path>
                                                <path fill="none" class="highcharts-tick" stroke="#ccd6eb"
                                                      stroke-width="1" d="M 550.5 322 L 550.5 332" opacity="1"></path>
                                                <path fill="none" class="highcharts-axis-line" stroke="#ccd6eb"
                                                      stroke-width="1" data-z-index="7" d="M 46 322.5 L 556 322.5"></path>
                                            </g>
                                            <g class="highcharts-axis highcharts-yaxis " data-z-index="2">
                                                <path fill="none" class="highcharts-axis-line" data-z-index="7"
                                                      d="M 46 10 L 46 322"></path>
                                            </g>
                                            <g class="highcharts-series-group" data-z-index="3">
                                                <g data-z-index="1"
                                                   class="highcharts-series highcharts-series-1 highcharts-line-series highcharts-color-1 "
                                                   transform="translate(46,10) scale(1 1)"
                                                   clip-path="url(#highcharts-2k6bvbd-13)">
                                                    <path fill="none"
                                                          d="M 5 307.32 L 88.333333333333 301.08 L 171.66666666667 301.08 L 255 302.64 L 338.33333333333 302.64 L 421.66666666667 304.2"
                                                          class="highcharts-graph" data-z-index="1" stroke="#c9d1d9"
                                                          stroke-width="2" stroke-linejoin="round" stroke-linecap="round">
                                                    </path>
                                                    <path fill="none"
                                                          d="M -5 307.32 L 5 307.32 L 88.333333333333 301.08 L 171.66666666667 301.08 L 255 302.64 L 338.33333333333 302.64 L 421.66666666667 304.2 L 431.66666666667 304.2"
                                                          stroke-linejoin="round" visibility="visible"
                                                          stroke="rgba(192,192,192,0.0001)" stroke-width="22"
                                                          data-z-index="2" class="highcharts-tracker"></path>
                                                </g>
                                                <g data-z-index="1"
                                                   class="highcharts-markers highcharts-series-1 highcharts-line-series highcharts-color-1  highcharts-tracker"
                                                   transform="translate(46,10) scale(1 1)"></g>
                                                <g data-z-index="2"
                                                   class="highcharts-series highcharts-series-0 highcharts-line-series highcharts-color-0 "
                                                   transform="translate(46,10) scale(1 1)"
                                                   clip-path="url(#highcharts-2k6bvbd-13)">
                                                    <path fill="none"
                                                          d="M 5 310.44 L 88.333333333333 307.32 L 171.66666666667 301.08 L 255 301.08 L 338.33333333333 302.64 L 421.66666666667 302.64 L 505 304.2"
                                                          class="highcharts-graph" data-z-index="1" stroke="#397eda"
                                                          stroke-width="2" stroke-linejoin="round" stroke-linecap="round">
                                                    </path>
                                                    <path fill="none"
                                                          d="M -5 310.44 L 5 310.44 L 88.333333333333 307.32 L 171.66666666667 301.08 L 255 301.08 L 338.33333333333 302.64 L 421.66666666667 302.64 L 505 304.2 L 515 304.2"
                                                          stroke-linejoin="round" visibility="visible"
                                                          stroke="rgba(192,192,192,0.0001)" stroke-width="22"
                                                          data-z-index="2" class="highcharts-tracker"></path>
                                                </g>
                                                <g data-z-index="2"
                                                   class="highcharts-markers highcharts-series-0 highcharts-line-series highcharts-color-0  highcharts-tracker"
                                                   transform="translate(46,10) scale(1 1)"></g>
                                            </g>
                                            <text x="10" text-anchor="start" class="highcharts-title"
                                                  data-z-index="4" style="color:#333333;font-size:18px;fill:#333333;"
                                                  y="24"></text>
                                            <text x="283" text-anchor="middle"
                                                  class="highcharts-subtitle" data-z-index="4"
                                                  style="color:#666666;fill:#666666;" y="24"></text>
                                            <g class="highcharts-axis-labels highcharts-xaxis-labels " data-z-index="7">
                                                <text x="48"
                                                      style="color:#666666;cursor:default;font-size:11px;fill:#666666;"
                                                      text-anchor="middle" transform="translate(0,0)" y="341"
                                                      opacity="1">2024-11</text><text x="131.33333333333"
                                                                                      style="color:#666666;cursor:default;font-size:11px;fill:#666666;"
                                                                                      text-anchor="middle" transform="translate(0,0)" y="341"
                                                                                      opacity="1">2024-12</text><text x="214.66666666667"
                                                                                                                      style="color:#666666;cursor:default;font-size:11px;fill:#666666;"
                                                                                                                      text-anchor="middle" transform="translate(0,0)" y="341"
                                                                                                                      opacity="1">2025-01</text><text x="298"
                                                                                                                                                      style="color:#666666;cursor:default;font-size:11px;fill:#666666;"
                                                                                                                                                      text-anchor="middle" transform="translate(0,0)" y="341"
                                                                                                                                                      opacity="1">2025-02</text><text x="381.33333333333"
                                                                                                                                                                                      style="color:#666666;cursor:default;font-size:11px;fill:#666666;"
                                                                                                                                                                                      text-anchor="middle" transform="translate(0,0)" y="341"
                                                                                                                                                                                      opacity="1">2025-03</text><text x="464.66666666667"
                                                                                                                                                                                                                      style="color:#666666;cursor:default;font-size:11px;fill:#666666;"
                                                                                                                                                                                                                      text-anchor="middle" transform="translate(0,0)" y="341"
                                                                                                                                                                                                                      opacity="1">2025-04</text><text x="531.9499988555908"
                                                                                                                                                                                                                                                      style="color:#666666;cursor:default;font-size:11px;fill:#666666;"
                                                                                                                                                                                                                                                      text-anchor="middle" transform="translate(0,0)" y="341"
                                                                                                                                                                                                                                                      opacity="1">2025-05</text>
                                            </g>
                                            <g class="highcharts-axis-labels highcharts-yaxis-labels " data-z-index="7">
                                                <text x="31"
                                                      style="color:#666666;cursor:default;font-size:11px;fill:#666666;"
                                                      text-anchor="end" transform="translate(0,0)" y="326"
                                                      opacity="1">0</text><text x="31"
                                                                                style="color:#666666;cursor:default;font-size:11px;fill:#666666;"
                                                                                text-anchor="end" transform="translate(0,0)" y="248"
                                                                                opacity="1">50</text><text x="31"
                                                                                                           style="color:#666666;cursor:default;font-size:11px;fill:#666666;"
                                                                                                           text-anchor="end" transform="translate(0,0)" y="170"
                                                                                                           opacity="1">100</text><text x="31"
                                                                                                                                       style="color:#666666;cursor:default;font-size:11px;fill:#666666;"
                                                                                                                                       text-anchor="end" transform="translate(0,0)" y="92"
                                                                                                                                       opacity="1">150</text><text x="31"
                                                                                                                                                                   style="color:#666666;cursor:default;font-size:11px;fill:#666666;"
                                                                                                                                                                   text-anchor="end" transform="translate(0,0)" y="14"
                                                                                                                                                                   opacity="1">200</text>
                                            </g>
                                            <g class="highcharts-legend" data-z-index="7"
                                               transform="translate(146,356)">
                                                <rect fill="none" class="highcharts-legend-box" rx="0" ry="0" x="0"
                                                      y="0" width="410" height="29" visibility="visible"></rect>
                                                <g data-z-index="1">
                                                    <g>
                                                        <g class="highcharts-legend-item highcharts-line-series highcharts-color-0 highcharts-series-0"
                                                           data-z-index="1" transform="translate(8,3)">
                                                            <path fill="none" d="M 0 11 L 16 11"
                                                                  class="highcharts-graph" stroke="#397eda"
                                                                  stroke-width="2"></path>
                                                        </g>
                                                        <g class="highcharts-legend-item highcharts-line-series highcharts-color-1 highcharts-series-1"
                                                           data-z-index="1" transform="translate(215,3)">
                                                            <path fill="none" d="M 0 11 L 16 11"
                                                                  class="highcharts-graph" stroke="#c9d1d9"
                                                                  stroke-width="2"></path>
                                                        </g>
                                                    </g>
                                                </g>
                                            </g>
                                        </svg>
                                        <div class="highcharts-legend"
                                             style="position: absolute; left: 146px; top: 356px; opacity: 1;">
                                            <div style="position: absolute; left: 0px; top: 0px; opacity: 1;">
                                                <div style="position: absolute; left: 0px; top: 0px; opacity: 1;">
                                                    <div class="highcharts-legend-item highcharts-line-series highcharts-color-0 highcharts-series-0"
                                                         style="position: absolute; left: 8px; top: 3px; opacity: 1;">
                                                        <span data-z-index="2"
                                                              style="font-family: &quot;Lucida Grande&quot;, &quot;Lucida Sans Unicode&quot;, Arial, Helvetica, sans-serif; font-size: 12px; position: absolute; white-space: nowrap; color: rgb(51, 51, 51); font-weight: bold; text-overflow: ellipsis; cursor: pointer; overflow: hidden; margin-left: 0px; margin-top: 0px; left: 21px; top: 3px; fill: rgb(51, 51, 51);">
                                                            <span style="margin-right:3px;display:inline-block; width:8px; height:8px; border-radius:50%; background-color:#397eda">
                                                            </span><span style="color:#7d8d9e; font-size:12px; font-weight:normal;">2024-11-26/2025-05-06</span>
                                                        </span>
                                                    </div>
                                                    <div class="highcharts-legend-item highcharts-line-series highcharts-color-1 highcharts-series-1"
                                                         style="position: absolute; left: 215px; top: 3px; opacity: 1;">
                                                        <span data-z-index="2"
                                                              style="font-family: &quot;Lucida Grande&quot;, &quot;Lucida Sans Unicode&quot;, Arial, Helvetica, sans-serif; font-size: 12px; position: absolute; white-space: nowrap; color: rgb(51, 51, 51); font-weight: bold; text-overflow: ellipsis; cursor: pointer; overflow: hidden; margin-left: 0px; margin-top: 0px; left: 21px; top: 3px; fill: rgb(51, 51, 51);">
                                                            <span style="margin-right:3px;display:inline-block; width:8px; height:8px; border-radius:50%; background-color:#c9d1d9">
                                                            </span><span style="color:#7d8d9e; font-size:12px; font-weight:normal;">2024-11-26/2025-05-05</span>
                                                        </span>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </li>
                    </ul>
                </div>
                <div class="blank20"></div>
                <div class="pannel">
                    <div class="head">
                        <div class="dateselector" id="country"
                             data-value="{&quot;date&quot;:[&quot;2025-05-06&quot;,&quot;2025-05-06&quot;],&quot;diff&quot;:{&quot;ms&quot;:0,&quot;days&quot;:0},&quot;compare&quot;:{&quot;checked&quot;:false,&quot;date&quot;:[]}}">
                            <div class="inner selector">
                                <div class="text main"><span>今天</span><i class="icon iconfont icon_menu_downarrow"></i></div>
                                <div class="pane">
                                    <div class="form">
                                        <div class="item">
                                            <div class="label">日期范围</div>
                                            <div class="input">
                                                <select name="date">
                                                    <option value="0" data-index="today">今天</option>
                                                    <option value="1" data-index="yesterday">昨天</option>
                                                    <option value="7" data-index="week">过去7天</option>
                                                    <option value="14" data-index="14days">过去14天</option>
                                                    <option value="30" data-index="month">过去30天</option>
                                                    <option value="thisWeek" data-index="thisWeek">本周</option>
                                                    <option value="thisMonth" data-index="thisMonth">本月</option>
                                                    <option value="lastWeek" data-index="lastWeek">上周</option>
                                                    <option value="lastMonth" data-index="lastMonth">上月</option>
                                                    <option value="custom" data-index="custom">自定义</option>
                                                </select>
                                                <i class="icon iconfont icon_menu_downarrow"></i>
                                            </div>
                                        </div>

                                        <div class="custom_date item">
                                            <div class="label">开始时间</div>
                                            <div class="input">
                                                <input name="beginAt" value="" type="text" class="date" readonly="">
                                            </div>
                                        </div>
                                        <div class="custom_date item">
                                            <div class="label">结束时间</div>
                                            <div class="input">
                                                <input name="endAt" value="" type="text" class="date" readonly="">
                                            </div>
                                        </div>

                                        <div class="item btn_group">
                                            <button class="submit btn_global btn_submit">确认</button>
                                            <button class="cancel btn_global btn_cancel">取消</button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="box_table" style="position: relative;">
                        <div class="small_title">客户国家/地区分布</div>
                        <div class="country_table">
                            <div class="thead">
                                <div class="topTitle country">国家/地区</div>
                                <div class="topTitle new">新客户</div>
                                <div class="topTitle core">复购客户</div>
                                <div class="topTitle active">已购买客户</div>
                                <div class="topTitle total">客户总数</div>
                            </div>
                            <div class="tbody">
                                <div class="no_data">暂无数据</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div id="helper_btn" class="helper"></div>

    <!--右侧栏-->
    <div class="drawer help_frame" id="helper_frame">
        <div class="mask"></div>
        <div class="container">
            <div class="header">
                <div class="inner">
                    <div class="title">
                        服务与支持
                    </div>
                    <span class="close"></span>
                </div>
            </div>

            <div class="content">
                <div>
                    <dl class="term">
                        <dt class="label">新客户</dt>
                        <dd class="text">在所选时间段内，所有新注册的会员和已提交订单的游客的总数</dd>
                    </dl>
                    <dl class="term">
                        <dt class="label">已购买客户</dt>
                        <dd class="text">在所选时间段内，所有已付款且次数大于一次的客户总数</dd>
                    </dl>
                    <dl class="term">
                        <dt class="label">复购客户</dt>
                        <dd class="text">在所选时间段内，所有已付款且次数大于两次的客户总数</dd>
                    </dl>
                    <dl class="term">
                        <dt class="label">客户总数</dt>
                        <dd class="text">从开店时间到所选日期的结束时间内，所有会员和已提交订单的游客的总数</dd>
                    </dl>
                </div>
            </div>

            <div class="footer">
                <div class="inner">
                    <button class="close btn_global btn_submit">知道了</button>

                </div>
            </div>
        </div>
    </div>


</div>
