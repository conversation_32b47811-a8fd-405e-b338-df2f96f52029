@page
@model YseStoreAdmin.Pages.Setting.FilesModel
@{
}

@section Styles {
    <link href="/assets/css/set.css" rel="stylesheet" asp-append-version="true">
}
<setting-files />


@section Scripts {


    <script src="/assets/js/plugin/clipboard/clipboard.min.js" asp-append-version="true" ></script>
    <script src="/assets/js/plugin/file_upload/js/vendor/jquery.ui.widget.js" asp-append-version="true" ></script>
    <script src="/assets/js/plugin/file_upload/js/external/tmpl.js" asp-append-version="true" ></script>
    <script src="/assets/js/plugin/file_upload/js/external/load-image.js" asp-append-version="true" ></script>
    <script src="/assets/js/plugin/file_upload/js/external/canvas-to-blob.js" asp-append-version="true" ></script>
    <script src="/assets/js/plugin/file_upload/js/external/jquery.blueimp-gallery.js" asp-append-version="true" ></script>
    <script src="/assets/js/plugin/file_upload/js/jquery.iframe-transport.js" asp-append-version="true" ></script>
    <script src="/assets/js/plugin/file_upload/js/jquery.fileupload.js" asp-append-version="true" ></script>
    <script src="/assets/js/plugin/file_upload/js/jquery.fileupload-process.js" asp-append-version="true" ></script>
    <script src="/assets/js/plugin/file_upload/js/jquery.fileupload-image.js" asp-append-version="true" ></script>
    <script src="/assets/js/plugin/file_upload/js/jquery.fileupload-audio.js" asp-append-version="true" ></script>
    <script src="/assets/js/plugin/file_upload/js/jquery.fileupload-video.js" asp-append-version="true" ></script>
    <script src="/assets/js/plugin/file_upload/js/jquery.fileupload-validate.js" asp-append-version="true" ></script>
    <script src="/assets/js/plugin/file_upload/js/jquery.fileupload-ui.js" asp-append-version="true" ></script>
    <script src="/assets/js/products.js" asp-append-version="true" ></script>
    <script src="/assets/font/global/iconfont.js" asp-append-version="true" ></script>
    <script src="/assets/js/set.js" asp-append-version="true" ></script>

    <script>
               jQuery(function ($) {
        $(document).ready(function(){set_obj.file_init()});
        jQuery('#w0').yiiActiveForm([], []);
        jQuery('#box_photo_edit').yiiActiveForm([], []);
        jQuery('#photo_tags_edit_form').yiiActiveForm([], []);
        });
        
    </script>

} 