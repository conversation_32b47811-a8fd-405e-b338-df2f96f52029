@page "/manage/set/photo/file-choice"
@model YseStoreAdmin.Pages.Setting.FileChoiceModel
@{
    Layout = "_LayoutFile";
}
@section Styles {
    <link href="/assets/css/set.css"  rel="stylesheet">
}
 

<choice-file />
@section Scripts {

    <script src="/assets/js/plugin/clipboard/clipboard.min.js" ></script>
    <script src="/assets/js/plugin/file_upload/js/vendor/jquery.ui.widget.js" ></script>
    <script src="/assets/js/plugin/file_upload/js/external/tmpl.js" ></script>
    <script src="/assets/js/plugin/file_upload/js/external/load-image.js" ></script>
    <script src="/assets/js/plugin/file_upload/js/external/canvas-to-blob.js" ></script>
    <script src="/assets/js/plugin/file_upload/js/external/jquery.blueimp-gallery.js" ></script>
    <script src="/assets/js/plugin/file_upload/js/jquery.iframe-transport.js" ></script>
    <script src="/assets/js/plugin/file_upload/js/jquery.fileupload.js" ></script>
    <script src="/assets/js/plugin/file_upload/js/jquery.fileupload-process.js" ></script>
    <script src="/assets/js/plugin/file_upload/js/jquery.fileupload-image.js" ></script>
    <script src="/assets/js/plugin/file_upload/js/jquery.fileupload-audio.js" ></script>
    <script src="/assets/js/plugin/file_upload/js/jquery.fileupload-video.js" ></script>
    <script src="/assets/js/plugin/file_upload/js/jquery.fileupload-validate.js" ></script>
    <script src="/assets/js/plugin/file_upload/js/jquery.fileupload-ui.js" ></script>
    <script src="/assets/js/products.js" ></script>
    <script src="/assets/font/global/iconfont.js" ></script>
    <script src="/assets/js/set.js" ></script>
    <script>
        jQuery(function ($) {
        $(document).ready(function(){set_obj.file_choice_init();})
        jQuery('#w0').yiiActiveForm([], []);
        jQuery('#w1').yiiActiveForm([], []);
        jQuery('#file_list_form').yiiActiveForm([], []);
        });</script>

}
