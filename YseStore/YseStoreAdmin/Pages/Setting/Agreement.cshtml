@page
@model YseStoreAdmin.Pages.Setting.AgreementModel
@{
}
 
@section Styles {
    <link href="/assets/css/set.css" rel="stylesheet" asp-append-version="true">
}
<setting-agreement />



@section Scripts {
    <script src="/assets/js/set.js" asp-append-version="true"></script>
    <script src="/assets/js/plugin/ckeditor/ckeditor.js" asp-append-version="true"></script>
    <script>
               jQuery(function ($) {
        CKEDITOR.replace('Content[service]', {'language':'zh-cn'});
        CKEDITOR.replace('Content[refund]', {'language':'zh-cn'});
        CKEDITOR.replace('Content[privacy]', {'language':'zh-cn'});
        CKEDITOR.replace('Content[cookies]', {'language':'zh-cn'});
        CKEDITOR.replace('Content[shipping]', {'language':'zh-cn'});
        $(document).ready(function(){
        set_obj.agreement_init();
        });
        });

    </script>
}