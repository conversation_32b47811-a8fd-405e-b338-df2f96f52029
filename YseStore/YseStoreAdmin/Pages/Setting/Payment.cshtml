@page
@model YseStoreAdmin.Pages.Setting.PaymentModel
@{
}
 


@section Styles {

    <link href="/assets/css/set.css"  rel="stylesheet">
}
<payment />


@section Scripts {

    <script src="/assets/js/set.js" ></script>
    <script>
        jQuery(function ($) {
        $(function(){set_obj.payment_init();set_obj.payment_ppcp_init();})
        jQuery('#paypal_try_form').yiiActiveForm([], []);

        $(document).ready(function() {
            $('#btn_online').on('click', () => {
                global_obj.div_mask();
                $('#temp_system_box').show();
                $('#temp_system_box .system_step_1, #temp_system_box .system_subtitle').show();
                $('#temp_system_box .system_step_2').hide();
            });

            $('#temp_system_box').on('click', '.system_close', () => {
                $('#temp_system_box').hide();
                global_obj.div_mask(1);
            }).on('click', '.system_know', () => {
                $('#temp_system_box').hide();
                global_obj.div_mask(1);
                $.get('/manage/action/notice-paypal-dont-need');
            }).on('click', '.system_form', () => {
                $('#temp_system_box .system_step_1, #temp_system_box .system_subtitle').hide();
                $('#temp_system_box .system_step_2').show();
            }).on('click', '.system_back', () => {
                $('#temp_system_box .system_step_1, #temp_system_box .system_subtitle').show();
                $('#temp_system_box .system_step_2').hide();
            }).on('click', '#btn_notice_disclaimer', () => {
                $('#notice_disclaimer_popup').show();
            });

            $(document).on('click', (e) => {
                let target = $(e.target);
                if (target.attr('id') == 'btn_notice_notes' || target.attr('id') == 'btn_notice_disclaimer' || target.attr('id') == 'notice_disclaimer_popup') {
                    return false;
                }
                if (!target.parents('#notice_disclaimer_popup').length) {
                    $('#notice_disclaimer_popup').hide();
                }
            });

            frame_obj.submit_form_init($('#notice_paypal_form'), '', '', '', (result) => {
                if (result.ret == 1) {
                    $('#temp_system_box').addClass('success');
                    $('#temp_system_box .system_item').hide();
                    $('#temp_system_box .system_success').addClass('show');
                } else {
                    global_obj.win_alert_auto_close(result.msg, 'fail', 2000, '', false);
                }
            });
        });

        jQuery('#notice_paypal_form').yiiActiveForm([], []);
        });</script>

    <script type="text/javascript">
        $(document).ready(function(){
            frame_obj.page_init();
            frame_obj.windows_init();
            frame_obj.web_name_credential();
        });
    </script>
}