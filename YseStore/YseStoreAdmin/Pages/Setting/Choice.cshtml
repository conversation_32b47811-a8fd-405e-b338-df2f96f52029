@page "/manage/set/photo/choice"
@model YseStoreAdmin.Pages.Setting.ChoiceModel
@{
    Layout = "_LayoutFile";
}

@section Styles {
    <link href="/assets/css/set.css" rel="stylesheet">
}

<choice-photo/>

@section Scripts {

    <script src="/assets/js/plugin/clipboard/clipboard.min.js"></script>
    <script src="/assets/js/plugin/file_upload/js/vendor/jquery.ui.widget.js"></script>
    <script src="/assets/js/plugin/file_upload/js/external/tmpl.js"></script>
    <script src="/assets/js/plugin/file_upload/js/external/load-image.js"></script>
    <script src="/assets/js/plugin/file_upload/js/external/canvas-to-blob.js"></script>
    <script src="/assets/js/plugin/file_upload/js/external/jquery.blueimp-gallery.js"></script>
    <script src="/assets/js/plugin/file_upload/js/jquery.iframe-transport.js"></script>
    <script src="/assets/js/plugin/file_upload/js/jquery.fileupload.js"></script>
    <script src="/assets/js/plugin/file_upload/js/jquery.fileupload-process.js"></script>
    <script src="/assets/js/plugin/file_upload/js/jquery.fileupload-image.js"></script>
    <script src="/assets/js/plugin/file_upload/js/jquery.fileupload-audio.js"></script>
    <script src="/assets/js/plugin/file_upload/js/jquery.fileupload-video.js"></script>
    <script src="/assets/js/plugin/file_upload/js/jquery.fileupload-validate.js"></script>
    <script src="/assets/js/plugin/file_upload/js/jquery.fileupload-ui.js"></script>
    <script src="/assets/js/products.js"></script>
    <script src="/assets/font/global/iconfont.js"></script>
    <script src="/assets/js/set.js"></script>
    <script>
        jQuery(function ($) {
            $(document).ready(function () {
                if (typeof set_obj !== 'undefined') {
                    set_obj.photo_choice_init();
                }
            })
            jQuery('#w0').yiiActiveForm([], []);
            jQuery('#w1').yiiActiveForm([], []);
            jQuery('#photo_list_form').yiiActiveForm([], []);
        });</script>


}