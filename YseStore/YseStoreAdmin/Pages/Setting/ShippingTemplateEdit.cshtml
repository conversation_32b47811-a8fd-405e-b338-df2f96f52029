@page
@model YseStoreAdmin.Pages.Setting.ShippingTemplateEditModel
@{
}

@section Styles {

    <link href="/assets/css/plugins.css"  rel="stylesheet">
    <link href="/assets/css/app/shipping_template.css"  rel="stylesheet">
}

<shipping-template-edit/>


@section Scripts {

    <script src="/assets/js/app/shipping_template.js" ></script>
    <script>
        jQuery(function ($) {
            $(document).ready(function () {
                shipping_template_obj.init()
            });
            jQuery('#edit_form').yiiActiveForm([{
                "id": "shippingtemplate-name",
                "name": "Name",
                "container": ".field-shippingtemplate-name",
                "input": "#shippingtemplate-name",
                "error": ".help-block.help-block-error",
                "validate": function (attribute, value, messages, deferred, $form) {
                    yii.validation.string(value, messages, {
                        "message": "Name必须是一条字符串。",
                        "max": 100,
                        "tooLong": "Name只能包含至多100个字符。",
                        "skipOnEmpty": 1
                    });
                }
            }], []);


            // 手动绑定保存按钮点击事件
            $('#btn_submit').on('click', function () {
                var formData = new FormData(document.getElementById('edit_form'));

                $.ajax({
                    url: '/manage/plugins/shipping-template/save',
                    type: 'POST',
                    data: formData,
                    processData: false,
                    contentType: false,
                    success: function (data) {
                        if (data.ret == 1) {
                            location.href = '/Setting/ShippingTemplate';
                        } else {
                            global_obj.win_alert_auto_close(data.msg, 'await', 1000, '8%');
                        }
                    },
                    error: function (xhr, status, error) {
                        global_obj.win_alert_auto_close('保存失败: ' + error, 'await', 1000, '8%');
                    }
                });
            });
        });
    </script>
}
