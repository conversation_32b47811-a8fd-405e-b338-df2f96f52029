@page "/manage/set/email/edit"
@model YseStoreAdmin.Pages.Setting.EmailEditModel
@{
}


@section Styles {

    <link href="/assets/css/set.css"  rel="stylesheet">
    <style>
        /* #email_edit_form .tpl_tips.inbox_message { */
        /*     display: block; */
        /* } */
        .cke {
            visibility: hidden;
        }</style>
    <script src="/assets/js/plugin/ckeditor/ckeditor.js" ></script>
    <script type="text/javascript" src="/assets/js/plugin/ckeditor/config.js" ></script>
    <link rel="stylesheet" type="text/css" href="/assets/js/plugin/ckeditor/skins/office2013/editor.css" >
    <script type="text/javascript" src="/assets/js/plugin/ckeditor/lang/zh-cn.js" ></script>
    <script type="text/javascript" src="/assets/js/plugin/ckeditor/styles.js" ></script>
    <script type="text/javascript" src="/assets/js/plugin/ckeditor/plugins/shopimage/plugin.js" ></script>
}
<email-template params="new { EmailType = Model.EmailType }" />


@section Scripts {

    <script src="/assets/js/set.js" ></script>
    <script>
               jQuery(function ($) {
        $(document).ready(function(){set_obj.config_email_templete_edit()});
        CKEDITOR.replace('Content', {'language':'zh-cn'});
        jQuery('#email_edit_form').yiiActiveForm([], []);
        });
    </script>
}