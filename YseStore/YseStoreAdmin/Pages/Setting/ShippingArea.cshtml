@page
@model YseStoreAdmin.Pages.Setting.ShippingAreaModel
@{
}
@section Styles {

    <link href="/assets/css/set.css"  rel="stylesheet">
}
<shipping-area />


@section Scripts {
    <script src="/businessJs/Common/ApiMonitor.js" ></script>
    <script src="/assets/js/set.js" ></script>
    <script>
        jQuery(function ($) {
            $(document).ready(function(){
                set_obj.shipping_express_area_init();
                
                // 初始化免费额度内容区域的显示状态
                var $freeShippingSwitch = $('.box_free .switchery');
                var $freeShippingCheckbox = $freeShippingSwitch.find('input[name="IsFreeShipping"]');
                
                // 检查内容是否显示，如果显示则确保开关也处于开启状态
                if($('.box_free_content:visible').length > 0) {
                    $freeShippingSwitch.addClass('switchery_checked checked');
                    $freeShippingCheckbox.prop('checked', true);
                } else if($freeShippingCheckbox.is(':checked')) {
                    // 如果复选框是选中状态，但内容没显示，则显示内容
                    $('.box_free').find('.box_free_content').show();
                    $freeShippingSwitch.addClass('switchery_checked checked');
                }
                
                // 处理配送地区的显示和交互
                // 如果有选择的国家数据，则显示国家数据区域
                if ($('.content[data-range="country"] .item').length > 0) {
                    $('.content[data-range="country"]').show();
                    // console.log('显示国家数据区域，找到' + $('.content[data-range="country"] .item').length + '个国家');
                } else {
                    // console.log('未找到国家数据，隐藏国家数据区域');
                }

                // 删除国家按钮点击事件
                $(document).on('click', '.area_box_del', function () {
                    var $item = $(this).closest('.item');
                    $item.remove();

                    // 如果没有国家了，隐藏整个区域
                    if ($('.content[data-range="country"] .item').length === 0) {
                        $('.content[data-range="country"]').hide();
                    }
                });

                // 删除州/省按钮点击事件
                $(document).on('click', '.state_item .btn_remove', function () {
                    $(this).closest('.state_item').remove();
                });
                
                // 添加国家按钮点击事件
                $('.btn_add_area').click(function() {
                    // 这里会调用已有的添加国家功能
                    // 确保添加完成后显示国家数据区域
                    if ($('.content[data-range="country"]').is(':hidden')) {
                        $('.content[data-range="country"]').show();
                    }
                });
            });
            jQuery('#freight_edit_form').yiiActiveForm([], []);
            jQuery('#freight_import_form').yiiActiveForm([], []);
        });
    </script>
    <script type="text/javascript">
    // 初始化API监控
    ApiMonitor.init({
        monitoredApis: ['/Setting/ShippingArea'],
        redirectUrl: '/Setting/Shipping',
        successCode: 1
    });
</script>
}