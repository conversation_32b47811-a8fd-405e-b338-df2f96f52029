@page
@model YseStoreAdmin.Pages.Setting.ShippingEditModel
@{
}


@section Styles {

    <link href="/assets/css/set.css"  rel="stylesheet">
}
<shipping-edit />


@section Scripts {

    <script src="/assets/js/set.js" ></script>
    <script>
        jQuery(function ($) {
            $(document).ready(function(){
                set_obj.shipping_express_init();
                set_obj.shipping_express_edit_init();
                
                // 修复表单样式问题
                setTimeout(function() {
                    // 获取当前选中的条件
                    var selectedCondition = $('input[name="UseCondition"]:checked').val();
                    
                    // 根据选中的条件显示相应的输入框和说明
                    if (selectedCondition > 0) {
                        $('.bg_between').show();
                        $('.box_type_menu_content .item').hide();
                        $('.box_type_menu_content .item:eq(' + selectedCondition + ')').show();
                        $('.unit_input.range').show();
                        
                        // 根据条件类型显示相应的单位
                        if (selectedCondition == 1) { // 产品总重量
                            $('.box_type_index[data-index="3"]').hide();
                            $('.box_type_index[data-index="1"]').show();
                        } else if (selectedCondition == 3) { // 产品总价
                            $('.box_type_index[data-index="1"]').hide();
                            $('.box_type_index[data-index="3"]').show();
                        } else { // 产品总数量
                            $('.box_type_index[data-index="1"]').hide();
                            $('.box_type_index[data-index="3"]').hide();
                        }
                    } else {
                        $('.bg_between').hide();
                    }
                }, 100);
                
                // 监控表单提交
                $('.btn_submit').click(function() {
                    var form = $('#edit_form');
                    var formData = new FormData(form[0]);
                    
                    $.ajax({
                        url: '/Setting/ShippingEditSave',
                        type: 'POST',
                        data: formData,
                        processData: false,
                        contentType: false,
                        success: function(response) {
                            if (response && response.ret === 1) {
                                // alert(response.msg);
                                window.location.href = '/Setting/Shipping';
                            } else {
                                // alert(response.msg || '保存失败');
                            }
                        },
                        error: function() {
                            // alert('请求失败，请稍后重试');
                        }
                    });
                    
                    return false;
                });
            });
        });
    </script>
}