using Entitys;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using YseStore.IService;

namespace YseStoreAdmin.Pages.Setting.Partial
{
    public class TagModel : PageModel
    {
        public List<photo_tags>? Photo_tagsListidString { get; set; }
        public List<photo_tags>? Photo_tagsList { get; set; }
        public ISettingPhotosService _settingPhotosService { get; }
        public TagModel(ISettingPhotosService settingPhotosService)
        {

            _settingPhotosService = settingPhotosService;
        }
        public async void OnGet(string? id,string? type)
        {
            Photo_tagsListidString = await _settingPhotosService.GetPhoto_Tags_Tags(id);
            Photo_tagsList = await _settingPhotosService.GetPhoto_Tags();
        }
    }
}
