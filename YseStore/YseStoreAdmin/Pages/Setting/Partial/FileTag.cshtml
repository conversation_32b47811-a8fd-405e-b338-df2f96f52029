@page "/manage/set/photo/file-tags"

@model YseStoreAdmin.Pages.Setting.Partial.FileTagModel
@{
}




<div class="option_selected">
    <div class="select_list">
        @if (Model.Photo_tagsListidString != null)
        {
            foreach (var item in Model.Photo_tagsListidString)
            {
                <span class="btn_attr_choice current" data-type="tags">
                    <b>@item.Name</b>
                    <input type="checkbox" name="tagsCurrent[]" value="@item.TagId" checked="" class="option_current">
                    <input type="hidden" name="tagsOption[]" value="@item.TagId">
                    <input type="hidden" name="tagsName[]" value="@item.Name"><i></i>
                </span>

            }
        }


    </div>
    <input type="text" class="box_input" name="_Option" value="" size="30" maxlength="255">
    <span class="placeholder hide">填写好内容，按回车键或者输入英文逗号保存</span>
</div>
<div class="option_not_yet">
    <input type="hidden" name="tagsName[]" value="截至-·11">
    <div class="select_list" data-type="tags" style="display: block;">

        @if (Model.Photo_tagsList != null)
        {
            foreach (var item in Model.Photo_tagsList)
            {
                <span class="btn_attr_choice"
                      data-type="tags">
                    <b>@item.Name</b>
                    <input type="checkbox" name="tagsCurrent[]" value="@item.TagId" class="option_current">
                    <input type="hidden" name="tagsOption[]" value="@item.TagId">
                    <input type="hidden" name="tagsName[]" value="@item.Name"><i></i>
                </span>

            }
        }


    </div>
</div>
<div class="option_button">
    <a href="javascript:;" class="select_all">全选</a>
    <div class="option_button_menu" style="display:none;">
        <a href="javascript:;" data-type="tags"
           class="current">标签</a>
    </div>
</div>
<input type="hidden" class="option_max_number" value="0">
