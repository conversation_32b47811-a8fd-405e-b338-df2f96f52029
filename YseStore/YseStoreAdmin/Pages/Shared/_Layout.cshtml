<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>@ViewData["Title"] - YseStoreAdmin</title>
    <link rel="shortcut icon" href="/assets/images/favico.ico" />
    <meta name="m-config" />
    <script src="/assets/js/jquery-3.5.1.js"  asp-append-version="true"></script>
    <script src="/assets/js/bootstrap.min.js" asp-append-version="true"></script>
    <script defer src="/assets/js/alpine.js" asp-append-version="true"></script>
    <meta content="telephone=no" name="format-detection" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="robots" content="noindex,nofollow">
    <meta name="renderer" content="webkit">
    <link href="/assets/css/global.css" rel="stylesheet" asp-append-version="true">
    <link href="/assets/js/plugin/toast/css/jquery.toast.css" rel="stylesheet" asp-append-version="true">
    <script src="/assets/js/plugin/toast/jquery.toast.js" asp-append-version="true" ></script>
    <link href="/assets/css/frame.css" rel="stylesheet" asp-append-version="true">
    <link href="/assets/css/animate.css" rel="stylesheet" asp-append-version="true">
    <link href="/assets/font/global/iconfont.css" rel="stylesheet" asp-append-version="true" >
    <link href="/assets/js/plugin/daterangepicker/daterangepicker.css" rel="stylesheet" asp-append-version="true" >
    <script src="/assets/js/main.js" asp-append-version="true"></script>
    @await RenderSectionAsync("Styles", required: false)
    <script src="/assets/js/lang/zh-CN.js" asp-append-version="true"></script>
    <script>document.domain = '@(AppSettings.GetValue("WebSiteDomain"))';</script>
    <style type="text/css">
        body, html, h1, h2, h3, h4, h5, h6, input, select, textarea {
            font-family: "微软雅黑";
        }</style>
</head>
<body class="zh-cn ">
    <header />
    <div id="main">
        <div class="fixed_loading" style="left: 180px; ">
            <div class="load">
                <div><div class="load_image"></div><div class="load_loader"></div></div>
            </div>
        </div>
        <side-bar />
        <div id="righter" class="righter home_righter">
            @RenderBody()
        </div>
        <div class="clear"></div>
    </div>
    <system-bar />
    <script>
       
                var shop_config={
            "domain":"@(AppSettings.GetValue("WebSiteDomain"))",
            "curDate":"2025/03/30 11:15:18",
            "lang":"en",
            "manage_language":"zh-cn",
            "currency":"€",
            "currSymbol":"€",
            "language":["en"],
            "UserName":"yisaier",
            "prodImageCount":"15",
            "FunVersion":"2",
            "Number":"UP",
            "cdn":"",
            "newSystem":1,
            "CDN_URL":"/assets/",
            "CDN_STATIC_URL":"/assets/",
            'attrOptionMax' :300,
            'offsetTime': 60,
            'exportCount': {
                "product": 100,
                "user": 1000,
                "order": 100	}
        };
</script>
  
    <script src="/assets/js/yii2/yii.js" asp-append-version="true"></script>
    <script src="/assets/js/yii2/yii.validation.js" asp-append-version="true"></script>
    <script src="/assets/js/yii2/yii.activeForm.js" asp-append-version="true"></script>
    <script src="/assets/js/global.js" asp-append-version="true"></script>
    <script src="/assets/js/frame.js" asp-append-version="true"></script>
    <script src="/assets/js/plugin/tool_tips/tool_tips.js" asp-append-version="true"></script>
    <script src="/assets/js/plugin/dragsort/dragsort-0.5.1.min.js" asp-append-version="true"></script>

    @await RenderSectionAsync("Scripts", required: false)
    <script type="text/javascript">
        $(document).ready(function(){
            frame_obj.page_init();
            frame_obj.windows_init();
            frame_obj.web_name_credential();
        });
    </script>
    <script type="text/javascript">
        $(document).ready(function(){
            $('#global_commission_box .box_close').click(function(){
                $.post('/manage/set/commission/commission-close', '', function(){
                    $('#global_commission_box').hide();
                });
            });
        });
    </script>
  
</body>
</html>