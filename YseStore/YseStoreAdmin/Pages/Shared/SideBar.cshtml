@model YseStoreAdmin.Pages.Shared.SideBar
@{
    // Func<dynamic, object> petTemplate = @<p>You have a pet named <strong>@item.Name</strong>.</p>;
}

<div class="menu free_version_menu">
    <div class="new_menu_list">
        @{
            foreach (var item in Model.SideMenus)
            {
                var showChild = item.ShowChild && item.Children != null;    
                <div class="menu_item @(item.UrlAry.Contains(Model.CurrentPage,StringComparer.OrdinalIgnoreCase)?"active open":"")">
                    <div class="item_name @(!showChild?"index":"")">
                        <a href="@(showChild?"javascript:;":item.Url)">
                            <i class="iconfont <EMAIL> "></i>
                            @T[item.MenuName!]
                        </a>
                    </div>
                    @if (showChild)
                    {
                        <ul>
                            @foreach (var subitem in item.Children)
                            {
                                <li class="@(subitem.UrlAry.Contains(Model.CurrentPage,StringComparer.OrdinalIgnoreCase)?"current":"")">
                                    <a href="@subitem.Url"><span> @T[subitem.MenuName!]</span></a>
                                </li>
                                
                            }
                        </ul>
                    }
                </div>
            }
        }
    </div>
</div>