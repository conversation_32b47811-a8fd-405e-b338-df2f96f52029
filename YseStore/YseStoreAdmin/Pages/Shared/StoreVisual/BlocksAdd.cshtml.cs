using Aliyun.OSS;
using Aop.Api.Domain;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Newtonsoft.Json.Linq;
using YseStore.Common;
using YseStore.Common.Helper;
using YseStore.IService;
using YseStore.IService.Visual;
using YseStore.Model.VM.Payment.Paypal;
using YseStoreAdmin.Pages.Components.Store.VisualComp;

namespace YseStoreAdmin.Pages.Shared.StoreVisual
{
    public class BlocksAddModel : PageModel
    {

        private readonly IViewRenderService _viewRenderService;
        private readonly IConfigService _configService;
        private readonly IVisualDraftsService _visualDraftsService;
        public readonly IVisualPagesService _visualPagesService;
        public readonly IVisualPluginsService _visualPluginsService;

        public BlocksAddModel(IViewRenderService viewRenderService, IConfigService configService, IVisualDraftsService visualDraftsService, IVisualPagesService visualPagesService, IVisualPluginsService visualPluginsService)
        {
            _viewRenderService = viewRenderService;
            _configService = configService;
            _visualDraftsService = visualDraftsService;
            _visualPagesService = visualPagesService;
            _visualPluginsService = visualPluginsService;
        }

        public void OnGet()
        {
        }

       

        /// <summary>
        /// 添加模块
        /// </summary>
        /// <returns></returns>
        public async Task<IActionResult> OnPostAsync()
        {
            try
            {
                int PId = Request.GetFormInt("PId", 0);
                int DraftsId = Request.GetFormInt("DraftsId", 0);
                string Blocks = Request.GetFormString("Blocks", string.Empty);

                var pluginsRow = await _visualPluginsService.QueryById(PId);
                if (pluginsRow == null)
                {
                    return new JsonResult(new
                    {
                        msg = new { Error = "插件不存在" },
                        ret = 0
                    });
                }

                var draft = await _visualDraftsService.QueryById(DraftsId);
                if (draft == null)
                {
                    return new JsonResult(new
                    {
                        msg = new { Error = "草稿不存在" },
                        ret = 0
                    });
                }

                string type = pluginsRow.Type;
                string mode = pluginsRow.Mode;

                JObject blocks = pluginsRow.Blocks.JsonToObj<JObject>();


                var config = await _visualPluginsService.GetPluginsConfig(type, mode, draft);
                if (config == null)
                {
                    return new JsonResult(new
                    {
                        msg = new { Error = "插件配置不存在" },
                        ret = 0
                    });
                }

                // 添加内容默认为复制第一个
                var firstProperty = config["Blocks"].ToObject<JObject>().First as JProperty;

                string key = firstProperty?.Name;

                if (!Blocks.IsNullOrEmpty())// 特殊内容类型(如页尾)
                {
                    if (config["Blocks"][Blocks] != null)
                    {
                        key = Blocks;
                    }
                    else
                    {
                        key = Blocks + "-1";
                    }

                    if (config["Blocks"][key] == null)
                    {
                        return new JsonResult(new
                        {
                            msg = new { Error = "pluginsConfig not find key" },
                            ret = 0
                        });
                    }
                }

                var menuHtml = ToolBarFixedPlugins.BlocksMenuHtml(key, config, JObject.FromObject(pluginsRow));

                JObject blocksValue = new JObject();

                foreach (var item in config["Blocks"][key].ToObject<JObject>().Properties())
                {
                    string k = item.Name;
                    var value = item.Value.ToObject<JObject>();
                    if (item.Value["value"] != null)
                    {
                        var v = BlocksAddModel.BlocksAddfilter(k, value);
                        blocksValue[k] = v;
                    }
                }

                var data = new
                {
                    BlocksName = key,
                    BlocksValue = blocksValue,
                    pluginsConfig = config,
                    pluginsRow = pluginsRow,
                };

                ViewData["data"] = data;
                string blocksHtml = await _viewRenderService.RenderToStringAsync("StoreVisual/BlocksAdd", data.ToJson());


                string blocksName = "";
                // 计算分配没有用到的键值
                string keyName = key.Split("-")[0];

                for (var i = 1; i < 100; i++)
                {
                    blocksName = $"{keyName}-{i}";
                    if (!blocks.ContainsKey(blocksName))
                    {
                        break;
                    }
                }

                // 把复制的关键字替换成新分配的键值
                menuHtml = menuHtml.Replace($"-{key}", $"-{blocksName}");
                blocksHtml = blocksHtml.Replace($"-{key}", $"-{blocksName}");
                blocksHtml = blocksHtml.Replace($"[{key}]", $"[{blocksName}]");

                var result = new
                {
                    menuHtml = menuHtml,
                    blocksHtml = blocksHtml,
                };


                return new JsonResult(new
                {
                    msg = result,
                    ret = 1

                });

            }
            catch (Exception ex)
            {
                return new JsonResult(new
                {
                    msg = new { Error = ex.Message },
                    ret = 0
                });
            }

        }


        /// <summary>
        /// 添加Blocks时候清空内容
        /// </summary>
        /// <param name="keyName">配置表的变量名称</param>
        /// <param name="data">配置表的变量名称的值</param>
        /// <returns>处理后的值</returns>
        public static string BlocksAddfilter(string keyName, JObject data)
        {
            // 获取value值，如果不存在则返回空字符串
            var value = data.ContainsKey("value") ? data["value"].ToString() : "";

            // 检查type字段是否存在
            if (data.ContainsKey("type"))
            {
                var type = data["type"].ToString();
                var typesToClear = new[] { "input", "textarea", "link", "richtext", "image" };

                // 如果是特定类型，清空值
                if (typesToClear.Contains(type))
                {
                    value = "";
                }

                // 特殊处理MaskColorOpacity
                if (keyName == "MaskColorOpacity" && type == "progress")
                {
                    value = "0%";
                }
            }

            return value;
        }
    }
}
