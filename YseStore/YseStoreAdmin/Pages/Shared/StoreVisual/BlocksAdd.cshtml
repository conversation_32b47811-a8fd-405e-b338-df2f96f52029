@page "/manage/view/visual-v2/blocks-add"
@using Newtonsoft.Json.Linq
@{
    Layout = null;
}
@model YseStoreAdmin.Pages.Shared.StoreVisual.BlocksAddModel
@{

    string dataString = ViewBag.data;
    if (dataString != null)
    {
        var data = dataString.JsonToObj<JObject>();
        var BlocksName = data["BlocksName"].ToString();
        var BlocksValue = data["BlocksValue"].ToObject<JObject>();
        var pluginsConfig = data["pluginsConfig"].ToObject<JObject>();
        var pluginsRow = data["pluginsRow"].ToObject<JObject>();



        JObject itemParam = new JObject
                {
                    ["BlocksName"] = BlocksName,
                    ["BlocksValue"] = BlocksValue,
                    ["pluginsConfig"] = pluginsConfig,
                    ["pluginsRow"] = pluginsRow,
                };

        //plugins-blocks
        <plugins-blocks params="new { obj = itemParam }"></plugins-blocks>
    }
}
