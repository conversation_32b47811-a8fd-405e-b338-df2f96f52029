using Aliyun.OSS;
using Entitys;
using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Newtonsoft.Json.Linq;
using YseStore.Common;
using YseStore.Common.Helper;
using YseStore.IService;
using YseStore.IService.Visual;
using YseStore.Service.Visual;

namespace YseStoreAdmin.Pages.StoreVisual;

public class EditToolBarPageModel : PageModel
{
    private readonly IViewRenderService _viewRenderService;
    private readonly IConfigService _configService;
    private readonly IVisualDraftsService _visualDraftsService;
    public readonly IVisualPagesService _visualPagesService;
    public readonly IVisualPluginsService _visualPluginsService;

    public EditToolBarPageModel(IViewRenderService viewRenderService, IConfigService configService, IVisualDraftsService visualDraftsService, IVisualPagesService visualPagesService, IVisualPluginsService visualPluginsService)
    {
        _viewRenderService = viewRenderService;
        _configService = configService;
        _visualDraftsService = visualDraftsService;
        _visualPagesService = visualPagesService;
        _visualPluginsService = visualPluginsService;
    }
    public void OnGet()
    {

    }

    [Parameter]
    public JObject data { get; set; } = new JObject();

    public async Task<IActionResult> OnPostJsonOutput()
    {
        try
        {
            string page = Request.GetFormString("page");
            int DraftsId = Request.GetFormInt("DraftsId");
            int PagesId = Request.GetFormInt("PagesId");
            int id = Request.GetFormInt("id");
            if (DraftsId == 0)
            {
                var drfId = await _configService.GetConfigValueByGroup("global", "DraftsIdV2");
                if (!string.IsNullOrEmpty(drfId))
                {
                    DraftsId = drfId.ObjToInt();
                }
            }



            List<string> pageAry = new List<string>
            {
                "index",
                "list",
                "goods",
                "article",
                "landing_page",
                "cod_page",
                "blog",
                "blog-detail",
                "cases",
                "cases-detail",
                "news",
                "news-detail",
                "download"
            };

            List<string> singlePage = new List<string> {
                "landing_page","cod_page"
            };

            JObject data = new JObject();
            JObject pluginsIdRow = new JObject();

            if (!page.IsNullOrEmpty() && pageAry.Contains(page))
            {
                var draft = await _visualDraftsService.QueryById(DraftsId);
                if (draft == null)
                {
                    return Content("");
                }


                var themesConfig = draft.Config.JsonToObj<JObject>();

                var themesConfigJson = await System.IO.File.ReadAllTextAsync(System.IO.Path.Combine(System.Environment.CurrentDirectory, $"wwwroot/template/default/inc/themes.json"));
                var themesConfigModel = themesConfigJson.JsonToObj<JObject>();
                themesConfigModel = themesConfigModel["Config"].ToObject<JObject>();

                // 全局设置->产品->产品图->产品图比例, 虚构产品图比例设置, ProductImage实际控制ProductsPicScale与ProductsMainPicScale By Ziruo
                JObject producImage = themesConfigModel["ProductsPicScale"].ToObject<JObject>();
                //producImage["expand"] = producImage["expand"] ?? new JObject();
                //producImage["expand"]["hint"] = "tips";

                themesConfigModel["ProductImage"] = producImage;
                themesConfigModel["ProductImage"]["expand"] = themesConfigModel["ProductImage"]["expand"] ?? new JObject();
                themesConfigModel["ProductImage"]["expand"]["hint"] = "tips";


                //页面
                var pageRow = await _visualPagesService.QueryByClauseAsync(it => it.PagesId == PagesId && it.DraftsId == DraftsId && it.Pages == page);
                if (id > 0 && singlePage.Contains(page))//单个页面模式
                {
                    pageRow = await _visualPagesService.QueryByClauseAsync(it => it.PagesId == PagesId && it.DraftsId == 0 && it.Pages == page);
                }

                if (pageRow == null)
                {
                    return Content("");
                }

                var pluginsIdAry = pageRow.Plugins.JsonToObj<List<int>>();
                if (id > 0 && singlePage.Contains(page))//单个页面模式
                {
                    var curIndexPagePlugins = await _visualPagesService.QueryByClauseAsync(it => it.DraftsId == DraftsId && it.Pages == "index");
                    if (curIndexPagePlugins != null)
                    {
                        //组件id 
                        var curIndexPluginsIdAry = curIndexPagePlugins.Plugins.JsonToObj<List<int>>();

                        int headerId = curIndexPluginsIdAry.FirstOrDefault();
                        int footerId = curIndexPluginsIdAry.LastOrDefault();

                        pluginsIdAry.Insert(0, headerId);
                        pluginsIdAry.Add(footerId);
                    }

                }

                //组件列表
                var pluginsRow = await _visualPluginsService.Query(it => pluginsIdAry.Contains(it.PId));

                string headerMenuHtml = "";
                string pluginsMenuHtml = "";
                string footerMenuHtml = "";

                JObject pluginsIdConfig = new JObject();
                Dictionary<string, string> pluginsIdUsed = new Dictionary<string, string>();

                //遍历组件id
                foreach (var pid in pluginsIdAry)
                {
                    var row = pluginsRow.Where(it => it.PId == pid).FirstOrDefault();
                    if (row == null)
                    {
                        continue;
                    }

                    pluginsIdRow.Add(pid.ToString(), JObject.FromObject(row));

                    string jsonPath = System.IO.Path.Combine(System.Environment.CurrentDirectory, $"wwwroot/template/default/cusvis_mode/{row.Type}/{row.Mode}/config.json");
                    if (!System.IO.File.Exists(jsonPath))
                    {
                        continue;
                    }

                    string config = await System.IO.File.ReadAllTextAsync(jsonPath);
                    if (!string.IsNullOrEmpty(config))
                    {
                        //配置
                        var configModel = config.JsonToObj<JObject>();
                        int status = 0;
                  
                        if (configModel["Blocks"] != null)
                        {
                            var blocks_row = configModel["Blocks"].ToObject<JObject>();
                            foreach (var item in blocks_row.Properties())
                            {
                                if (item.Value == null || item.Value["expand"] == null || item.Value["expand"]["visibility"] == null || item.Value["expand"]["visibility"].ToString() == "hidden")
                                {
                                    continue;
                                }
                                status += 1;
                            }
                        }

                        if(configModel["Settings"] != null)
                        {
                            var settings_row = configModel["Settings"].ToObject<JObject>();

                            foreach (var item in settings_row.Properties())
                            {
                                if (item.Value == null || item.Value["expand"] == null || item.Value["expand"]["visibility"] == null || item.Value["expand"]["visibility"].ToString() == "hidden")
                                {
                                    continue;
                                }
                                status += 1;
                            }
                        }

                        pluginsIdUsed.Add(pid.ToString(), status.ToString());


                        if (row.Type == "header")
                        {
                            headerMenuHtml = await _visualPluginsService.MenuHtml(row);
                        }
                        else if (row.Type == "footer")
                        {
                            footerMenuHtml = await _visualPluginsService.MenuHtml(row);
                        }
                        else
                        {
                            pluginsMenuHtml += await _visualPluginsService.MenuHtml(row);
                        }

                        var pluginsConfig = await _visualPluginsService.GetPluginsConfig(row.Type, row.Mode, draft);
                        if (pluginsConfig == null)
                        {
                            continue;
                        }
                        pluginsIdConfig[pid.ToString()] = pluginsConfig;

                    }



                }


                // 产品详细页模板关联产品


                // 加载网站使用的字体

                string themesFontHtml = "";
                JObject relatedProducts = new JObject();

                data["page"] = page;
                data["headerMenuHtml"] = headerMenuHtml;
                data["pluginsMenuHtml"] = pluginsMenuHtml;
                data["footerMenuHtml"] = footerMenuHtml;
                data["pluginsIdAry"] = JArray.FromObject(pluginsIdAry);
                data["pluginsIdRow"] = pluginsIdRow;
                data["pluginsIdConfig"] = pluginsIdConfig;
                data["themesConfigJson"] = themesConfigJson;
                data["themesConfig"] = themesConfig;
                data["themesFontHtml"] = themesFontHtml;
                data["relatedProducts"] = relatedProducts;
                data["pluginsIdUsed"] = JObject.FromObject(pluginsIdUsed);
                data["permitAry"] = "";


            }

            JObject strRePlaceData = new JObject();

            List<string> plugType = new List<string> { "Icon", "Pic", "PicPc", "PicMobile" };
            foreach (var item in pluginsIdRow.Properties())
            {
                var PId = item.Value["PId"];
        


                var _data = new JObject();
                if (item.Value["Settings"] != null)
                {
                    var Settings = item.Value["Settings"].ToString().JsonToObj<JObject>();
                    if (Settings == null)
                    {
                        continue;
                    }
                    foreach (var setting in Settings.Properties())
                    {
                        if (plugType.Contains(setting.Name) && setting.Value != null && _data["image"] == null)
                        {
                            _data["image"] = setting.Value;
                        }
                        if (setting.Name == "Title" && setting.Value != null && _data["title"] == null)
                        {
                            _data["title"] = setting.Value;

                        }
                    }


                    if (item.Value["Blocks"] != null)
                    {
                        var Blocks = item.Value["Blocks"].ToString().JsonToObj<JObject>();
                        if(Blocks == null)
                        {
                            continue;
                        }
                        foreach (var block in Blocks.Properties())
                        {
                            if (plugType.Contains(block.Name) && block.Value != null && _data["image"] == null)
                            {
                                _data["image"] = block.Value;
                            }
                            if (block.Name == "Title" && block.Value != null && _data["title"] == null)
                            {
                                _data["title"] = block.Value;

                            }
                        }
                    }

                    strRePlaceData[PId.ToString()] = _data;
                }
            }
            var replaceData = strRePlaceData.ToJson();

            this.data = data;
            ViewData["data"] = data;

            //// 1.  Razor  HTML 
            string htmlContent = await _viewRenderService.RenderToStringAsync("StoreVisual/EditToolBarPage", data.ToJson());

            //// 2. ׼���滻���ݣ��������������̬���ɣ�
            //var replaceData = """
            //    {"18609":[],"18610":{"image":"/u_file/2410/25/photo/banner.jpg"},"18611":{"image":"/u_file/2410/25/photo/smallad1.jpg"},"18612":{"title":"POPULAR","image":"/u_file/2410/28/photo/part1img1.jpg"},"18613":{"image":"/u_file/2410/28/photo/banner1.jpg"},"18614":{"image":"/u_file/2410/28/photo/part4img.jpg"},"18615":{"title":"BESTSELLER"},"18616":{"title":"LATEST "},"18617":{"image":"/u_file/2410/28/photo/part6img1.png","title":"Waterproof Commercial Walkie Talkie For Construction Sites"},"18618":{"image":"/u_file/2410/25/photo/indeximg1.jpg"},"18619":{"title":"FRESH FROM"},"18620":[],"18621":{"title":"NEWSLETTER SIGN UP"}}
            //    """;

            //// 3. �������� JSON ����
            var result = new
            {
                msg = new
                {
                    Html = htmlContent,
                    ReplaceData = replaceData.ToString()
                },
                ret = 1
            };

            return new JsonResult(result);


        }
        catch (Exception ex)
        {
            return new JsonResult(new
            {
                msg = new { Error = ex.Message },
                ret = 0
            });
        }
    }


}

