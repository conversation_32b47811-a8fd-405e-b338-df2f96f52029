@page
@model YseStoreAdmin.Pages.Products.CateEditModel
@{
}
@section Styles {
	<link href="/assets/css/products.css" rel="stylesheet" >


	<link rel="stylesheet" type="text/css" id="mce-u0" href="/assets/js/plugin/tinymce/js/tinymce/skins/ui/oxide/skin.min.css"  />
}

<product-cate-edit  query-id="@Model.CateId" />
@section Scripts {
	<script src="/assets/js/plugin/clipboard/clipboard.min.js" ></script>
	<script src="/assets/js/plugin/tinymce/js/tinymce/tinymces.min.js" ></script>
	<script src="/assets/js/products.js"></script>
	<script src="/assets/js/plugin/layer/layer.js" ></script>
	<script src="/businessJs/Pop-ups/frame-message.js" ></script>
	<script src="/businessJs/Products/productCate.js" ></script>
	<!-- 引入全选复选框修复脚本 -->
	<script src="/businessJs/Products/Fix/checkboxSelectAllFix.js" ></script>
	<script src="/businessJs/Products/Fix/buttonClickFix.js" ></script>
	<script src="/businessJs/Products/Fix/productCateEditCheckboxFix.js" ></script>
	<!-- 引入custom_sort类修复脚本 -->
	<script src="/businessJs/Products/Fix/customSortClassFixV2.js" ></script>
	<!-- 引入ProId顺序修复脚本（整合版） -->
	<script src="/businessJs/Products/Fix/productCateEditProIdOrderFix.js" ></script>
	<script>
		jQuery(function ($) {
			$(document).ready(function(){
	
				// 首先初始化我们的categoryModule
				if (typeof categoryModule !== 'undefined') {
					categoryModule.init();
					console.log('CategoryModule已初始化');
				}

				// 阻止分类名称变动确认弹窗
				if (typeof categoryModule !== 'undefined' && categoryModule.preventCategoryNameChangeAlert) {
					categoryModule.preventCategoryNameChangeAlert();
				}

				// 初始化产品分类编辑功能，包括关联产品按钮事件绑定
				if (typeof products_obj !== 'undefined' && products_obj.category_edit_init) {
					products_obj.category_edit_init();
				}

				// 多次延迟绑定我们的分页事件，确保覆盖其他脚本的事件绑定
				setTimeout(function() {
					if (typeof categoryModule !== 'undefined') {
						categoryModule.bindPaginationEvents();
						console.log('CategoryModule分页事件已重新绑定(1秒后)');
					}
				}, 1000);

				setTimeout(function() {
					if (typeof categoryModule !== 'undefined') {
						categoryModule.bindPaginationEvents();
						console.log('CategoryModule分页事件已重新绑定(2秒后)');
					}

					// 确保custom_sort类修复脚本正常工作
					if (typeof window.checkAndRestoreCustomSort === 'function') {
						window.checkAndRestoreCustomSort();
						console.log('CustomSortClassFixV2: 手动触发修复');
					}
				}, 2000);

				// 额外的custom_sort类修复检查
				setTimeout(function() {
					if (typeof window.manualRestoreCustomSort === 'function') {
						window.manualRestoreCustomSort();
						console.log('CustomSortClassFixV2: 执行全面检查修复');
					}
				}, 3000);
			});
		});
	</script>
}

@* 添加防伪令牌 *@
<form method="post">
    <input type="hidden" name="CateId" value="@Model.CateId" />
    @Html.AntiForgeryToken()
</form>