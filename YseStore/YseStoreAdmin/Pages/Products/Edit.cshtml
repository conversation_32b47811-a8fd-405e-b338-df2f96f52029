@page
@model YseStoreAdmin.Pages.Products.EditModel
@{
}

@section Styles{
    <link href="/assets/css/app/wholesale.css" rel="stylesheet" >
    <link href="/assets/css/app/shipping_template.css" rel="stylesheet" >
    <link href="/assets/css/app/custom_attributes.css" rel="stylesheet" >
    <link href="/assets/css/app/screening.css" rel="stylesheet" >
    <link href="/assets/css/products.css" rel="stylesheet" >
    <!-- 添加颜色选择器CSS -->
    <link href="/assets/js/plugin/colpick/colpick.css" rel="stylesheet" >
    <!-- 添加图片回显功能CSS -->
    <link href="/assets/css/imageEcho.css" rel="stylesheet" >
    <!-- 添加上传按钮样式修复CSS -->
    <link href="/assets/css/uploadButtonFix.css" rel="stylesheet" >
    <!-- 添加图片预览修复CSS -->
    <link href="/assets/css/imagePreviewFix.css" rel="stylesheet" >
    <!-- 添加上传加载状态修复CSS -->
    <link href="/assets/css/uploadLoadingFix.css" rel="stylesheet" >

}

@Html.AntiForgeryToken()

<!-- 添加隐藏字段存储产品详情数据 -->
<input type="hidden" id="productDetailData" value="@ViewData["ProductDetail"]"/>
<!-- 添加产品ID隐藏字段 -->
<input type="hidden" id="ProId" value="@Model.ProductId"/>

<product-edit product-id="@Model.ProductId"/>

@section Scripts {
    <script>
        // 检查隐藏字段中的数据
        var detailDataStr = document.getElementById('productDetailData').value;
        // 传递产品ID到前端
        var product_data = {};
        product_data["switch_url"] = "";
        product_data["product_id"] = @(Model.ProductId);

        // 初始化产品详情数据
        try {
            if (detailDataStr && detailDataStr.trim() !== '') {
                product_data["product_detail"] = JSON.parse(detailDataStr);
            } else {
                product_data["product_detail"] = null;
            }
        } catch (e) {
            console.error("解析产品详情数据时出错:", e);
            product_data["product_detail"] = null;
        }
        // 确保全局变量可访问
        window.product_data = product_data;
    </script>
    <script src="/assets/js/plugin/colpick/colpick.js" ></script>
    <script src="/assets/js/plugin/clipboard/clipboard.min.js" ></script>
    <script src="/assets/js/plugin/facebook/store.js" ></script>
    <script src="/assets/js/plugin/file_upload/js/vendor/jquery.ui.widget.js" ></script>
    <script src="/assets/js/plugin/file_upload/js/external/tmpl.js" ></script>
    <script src="/assets/js/plugin/file_upload/js/external/load-image.js" ></script>
    <script src="/assets/js/plugin/file_upload/js/external/canvas-to-blob.js" ></script>
    <script src="/assets/js/plugin/file_upload/js/external/jquery.blueimp-gallery.js"></script>
    <script src="/assets/js/plugin/file_upload/js/jquery.iframe-transport.js" ></script>
    <script src="/assets/js/plugin/file_upload/js/jquery.fileupload.js" ></script>
    <script src="/assets/js/plugin/file_upload/js/jquery.fileupload-process.js" ></script>
    <script src="/assets/js/plugin/file_upload/js/jquery.fileupload-image.js" ></script>
    <script src="/assets/js/plugin/file_upload/js/jquery.fileupload-audio.js" ></script>
    <script src="/assets/js/plugin/file_upload/js/jquery.fileupload-video.js" ></script>
    <script src="/assets/js/plugin/file_upload/js/jquery.fileupload-validate.js" ></script>
    <script src="/assets/js/plugin/file_upload/js/jquery.fileupload-ui.js" ></script>

    <script src="/assets/js/plugin/daterangepicker/moment.min.js" ></script>
    <script src="/assets/js/plugin/daterangepicker/daterangepicker.js" ></script>
    <script src="/assets/js/app/wholesale.js" ></script>
    <script src="/assets/js/app/shipping_template.js" ></script>
    <script src="/assets/js/app/custom_attributes.js" ></script>
    <script src="/assets/js/app/screening.js" ></script>
    <script src="/assets/js/plugin/tinymce/js/tinymce/tinymces.min.js" ></script>
    <script src="/assets/js/component.js" ></script>
    <script src="/assets/js/products.js" ></script>

    <!-- 添加弹窗提示框 -->
    <script src="/businessJs/Pop-ups/frame-message.js" ></script>
    <!-- 添加产品编辑JS引用 -->
    <script src="/businessJs/Products/productEditSave.js" ></script>
    <script src="/businessJs/Products/attributeTableOperations.js" ></script>
    <!-- 引入标签和关键词处理模块 -->
    <script src="/businessJs/Common/tagsKeywordsHandler.js"></script>
    <!-- 引入关键词渲染器 -->
    <script src="/businessJs/Common/keywordRenderer.js"></script>
    <!-- 引入图片URL处理助手 -->
    <script src="/businessJs/Common/imageUrlHelper.js"></script>
    <!-- 引入产品关键词修复脚本 -->
    <script src="/businessJs/Products/Fix/productKeywordFix.js" ></script>
    <script src="/businessJs/Products/Fix/tagDeleteFix.js" ></script>
    <script src="/businessJs/Products/Fix/productFilterFix.js" ></script>
    <!-- 引入产品标签修复脚本 -->
    <script src="/businessJs/Products/Fix/productTagFix.js" ></script>
    <!-- 引入颜色选择器修复脚本 -->
    <script src="/businessJs/Products/Fix/colorPickerFix.js" ></script>
    <!-- 修复按钮点击事件 -->
    <script src="/businessJs/Products/Fix/buttonClickFix.js" ></script>
    <script src="/businessJs/Products/productDetailLoader.js" ></script>
    <!-- 引入图片回显功能脚本 -->
    <script src="/businessJs/Products/Images/imageEcho.js" ></script>
    <!-- 引入图片回显集成脚本 -->
    <script src="/businessJs/Products/Images/imageEchoIntegration.js"></script>
    <!-- 引入图片库调试助手 -->
    @* <script src="/businessJs/Products/Images/galleryDebugHelper.js"></script> *@
    <!-- 引入图片上传修复脚本 -->
    <script src="/businessJs/Products/Images/imageUploadFix.js"></script>
    <!-- 引入图片预览修复脚本 -->
    <script src="/businessJs/Products/Images/imagePreviewFix.js"></script>
    <!-- 引入上传加载状态修复脚本 -->
    <script src="/businessJs/Products/Images/uploadLoadingFix.js"></script>
    <!-- 引入tinymce编辑器初始化脚本 -->
    <script src="/businessJs/Products/tinymceInit.js" ></script>

    <!-- 富文本编辑器图片上传修复脚本 -->
    <script src="/businessJs/Products/EditorImageUpload/editorImageHandler.js" ></script>
    <script src="/businessJs/Products/EditorImageUpload/fileUploadHandler.js" ></script>
    <script src="/businessJs/Products/EditorImageUpload/photoFormHandler.js" ></script>
    <!-- 引入tinymce图片上传修复脚本 -->
    <script src="/businessJs/Products/EditorImageUpload/tinymceImageUploadFix.js" ></script>

    <!-- 添加仓库设置数据回显处理脚本 -->
    <script type="text/javascript">
        // 从后端获取WarehouseSet数据并设置为全局变量
        @if (Model.ProductDetail?.WarehouseSet != null && Model.ProductDetail.WarehouseSet.Any())
        {
            <text>
                var warehouseSetDataFromServer = [@string.Join(",", Model.ProductDetail.WarehouseSet)];
            </text>
        }
        else
        {
            <text>
                var warehouseSetDataFromServer = [];
            </text>
        }
    </script>
    <!-- 引入仓库相关脚本 -->
    <script src="/businessJs/Products/Warehouse/warehouseDataEcho.js" ></script>
    <script src="/businessJs/Products/Warehouse/warehouseSwitchFix.js" ></script>
    <script src="/businessJs/Products/Warehouse/specificationModeInit.js" ></script>
    <!-- 引入仓库设置时属性数据保护脚本 -->
    <script src="/businessJs/Products/Fix/warehouseAttributeProtection.js" ></script>
    <!-- 引入SEO初始化脚本 -->
    <script src="/businessJs/Products/seoInit.js" ></script>
    <!-- 引入全选复选框修复脚本 -->
    <script src="/businessJs/Products/Fix/checkboxSelectAllFix.js" ></script>
    <!-- 引入默认库存区域显示控制修复脚本 -->
    <script src="/businessJs/Products/Fix/defaultInventoryFix.js" ></script>
    <script>
        // 确保所有JS加载完成后再次检查产品数据
        $(document).ready(function () {
            if (window.product_data && window.product_data.product_detail) {
                // 确保标签和关键词处理模块被初始化
                if (typeof TagsKeywordsHandler !== 'undefined') {
                    TagsKeywordsHandler.init();
                }
            }

            // 初始化全选复选框修复
            if (typeof CheckboxSelectAllFix !== 'undefined') {
                CheckboxSelectAllFix.init();
            }

            // 初始化图片预览修复
            if (typeof ImagePreviewFix !== 'undefined') {
                ImagePreviewFix.init();
            }

            // 初始化上传加载状态修复
            if (typeof UploadLoadingFix !== 'undefined') {
                UploadLoadingFix.init();
            }

            // 监听图片上传完成事件
            $('.upload_file_box').on('fileuploadstop', function() {
                setTimeout(function() {
                    if (window.ImagePreviewFix) {
                        ImagePreviewFix.ensurePreviewVisible();
                        ImagePreviewFix.fixAllImagePreviews();
                    }
                }, 500);
            });

            // 监听图片库选择完成
            $(document).on('imageChanged', function() {
                setTimeout(function() {
                    if (window.ImagePreviewFix) {
                        ImagePreviewFix.ensurePreviewVisible();
                        ImagePreviewFix.fixAllImagePreviews();
                    }
                }, 300);
            });

            // 页面加载完毕后清除控制台所有输出
            if (console && console.clear) {
                setTimeout(function () {
                    // console.clear();
                }, 2000);
            }
        });
    </script>

    <script type="text/javascript">
        $(document).ready(function () {
            // 注释掉重复的frame_obj.page_init()调用，避免左侧菜单事件重复绑定
            // frame_obj.page_init();
            frame_obj.windows_init();
            frame_obj.web_name_credential();
        });
    </script>


    <script type="text/javascript">
        $(document).ready(function () {
            $('#global_commission_box .box_close').click(function () {
                $.post('/manage/set/commission/commission-close', '', function () {
                    $('#global_commission_box').hide();
                });
            });
        });
    </script>

    <div class="tox tox-silver-sink tox-tinymce-aux" style="position: relative;"></div>
    <div class="tox tox-silver-sink tox-tinymce-aux" style="position: relative;"></div>
    <div class="tox tox-silver-sink tox-tinymce-aux" style="position: relative;"></div>
    <div class="tox tox-silver-sink tox-tinymce-aux" style="position: relative;"></div>




}

