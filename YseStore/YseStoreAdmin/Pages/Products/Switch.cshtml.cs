using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.AspNetCore.Antiforgery;

namespace YseStoreAdmin.Pages.Products
{
    public class SwitchModel : PageModel
    {
        // 存储搜索关键词的静态变量，用于组件间通信
        public static string SearchKeyword { get; set; }

        public void OnGet()
        {
        }

        // 添加搜索API接口，添加防伪造令牌验证豁免
        [IgnoreAntiforgeryToken]
        public IActionResult OnGetSearch(string keyword)
        {
            // 保存关键词到静态变量
            SearchKeyword = keyword;
            return new JsonResult(new { success = true });
        }
    }
}
