@page
@model YseStoreAdmin.Pages.Products.RecommendEditModel

@section Styles {
	<link href="/assets/css/app/products_recommend.css" rel="stylesheet">
}

<recommend-edit/>


@section Scripts {
	<!-- 添加缺失的依赖项 -->
	<script src="/assets/js/component.js"></script>
	<script src="/assets/js/plugin/clipboard/clipboard.js"></script>
	<script src="/assets/js/app/products_recommend.js"></script>
	<script src="/businessJs/Products/Fix/recommendEditCategoryFix.js"></script>
	<script>
		jQuery(function ($) {
			$(document).ready(function () {
				component_obj.global_select_category()
			});
			$(document).ready(function () {
				$(function () {
					recommend_obj.recommend_edit_init()
				});
			});
			// 初始化分类选择修复
			$(document).ready(function () {
				if (typeof RecommendEditCategoryFix !== 'undefined') {
					RecommendEditCategoryFix.init();
				}
			});
		});
	</script>
}

