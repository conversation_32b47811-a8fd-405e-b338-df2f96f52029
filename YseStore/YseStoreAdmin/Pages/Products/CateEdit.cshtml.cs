using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using YseStore.IService.Products;
using System;
using System.Threading.Tasks;
using Entitys;

namespace YseStoreAdmin.Pages.Products
{
    public class CateEditModel : PageModel
    {
        private readonly IProductCategoryService _categoryService;
        private readonly ICategorySeoService _categorySeoService;

        public int CateId { get; set; }
        public products_category Category { get; set; }
        public products_category_description Description { get; set; }
        public List<products_category_description> Descriptions { get; set; }

        // 添加子分类相关属性
        [BindProperty(SupportsGet = true)] public int Level { get; set; }

        [BindProperty(SupportsGet = true)] public int SupCateId { get; set; }

        public string PageUrl { get; set; }

        public CateEditModel(IProductCategoryService categoryService, ICategorySeoService categorySeoService)
        {
            _categoryService = categoryService;
            _categorySeoService = categorySeoService;
        }

        public async Task OnGetAsync(int id = 0)
        {
            CateId = id;

            if (id > 0)
            {
                var result = await _categoryService.GetProductCategoryById(id);
                Category = result.category;
                Descriptions = result.descriptions;
                Description = result.descriptions?.FirstOrDefault(); // 兼容旧版本
            }
            else
            {
                Category = new products_category();
                Category.UId = "0,"; // 设置默认值为"0,"

                // 初始化默认的四个描述记录
                Descriptions = new List<products_category_description>
                {
                    new products_category_description { PositionType = 0, Description_en = "" },
                    new products_category_description { PositionType = 1, Core_top = "" },
                    new products_category_description { PositionType = 2, Core_bottom = "" },
                    new products_category_description { PositionType = 3, Description_en_bottom = "" }
                };
                Description = Descriptions.FirstOrDefault(); // 兼容旧版本

                // 如果是创建子分类，设置Level和SupCateId
                if (SupCateId > 0)
                {
                    Category.UId = Level == 1 ? $"0,{SupCateId}" : await GetParentUid(SupCateId);
                }
            }
        }

        // 获取父分类的UId，用于构建子分类的UId
        private async Task<string> GetParentUid(int parentId)
        {
            if (parentId <= 0) return "0,";

            var parentResult = await _categoryService.GetProductCategoryById(parentId);
            if (parentResult.category != null)
            {
                // 如果父分类已有UId，则基于父分类UId构建子分类UId
                var parentUid = !string.IsNullOrEmpty(parentResult.category.UId)
                    ? parentResult.category.UId.TrimEnd(',') // 移除末尾可能存在的逗号
                    : "0"; // 修改为"0"

                // 确保UId以逗号结尾，然后添加父分类ID
                if (!parentUid.EndsWith(","))
                {
                    parentUid += ",";
                }

                // 添加父ID
                parentUid += parentId.ToString();

                return parentUid;
            }

            return "0,";
        }

        public async Task<IActionResult> OnGetCategoryAsync(int id)
        {
            try
            {
                var result = await _categoryService.GetProductCategoryById(id);

                // 获取分类关联的产品ID列表
                var productIds = new List<int>();
                if (id > 0)
                {
                    productIds = await _categoryService.GetProductIdsByCategoryId(id);
                }

                return new JsonResult(new
                {
                    success = true,
                    message = "获取成功",
                    data = new
                    {
                        category = result.category,
                        descriptions = result.descriptions, // 新的多描述字段结构
                        description = result.descriptions?.FirstOrDefault(), // 兼容旧版本
                        productIds = productIds
                    }
                });
            }
            catch (Exception ex)
            {
                return new JsonResult(new { success = false, message = $"获取失败: {ex.Message}" });
            }
        }

        /// <summary>
        /// 检查分类PageUrl是否可用
        /// </summary>
        /// <param name="pageUrl">要检查的PageUrl</param>
        /// <param name="cateId">当前分类ID（编辑时传入，新增时为0）</param>
        /// <returns>检查结果</returns>
        public async Task<IActionResult> OnGetCheckPageUrlAsync(string pageUrl, int cateId = 0)
        {
            try
            {
                if (string.IsNullOrEmpty(pageUrl))
                {
                    return new JsonResult(new { success = false, message = "PageUrl不能为空" });
                }

                // 检查PageUrl是否已存在
                bool exists = await _categorySeoService.IsCategoryPageUrlExistAsync(pageUrl, cateId);

                if (exists)
                {
                    // 如果存在，生成一个建议的唯一PageUrl
                    string suggestedUrl = await _categorySeoService.GenerateUniqueCategoryPageUrlAsync(pageUrl, cateId);

                    return new JsonResult(new
                    {
                        success = false,
                        message = "PageUrl已存在",
                        data = new { suggestedUrl = suggestedUrl }
                    });
                }
                else
                {
                    return new JsonResult(new { success = true, message = "PageUrl可用" });
                }
            }
            catch (Exception ex)
            {
                return new JsonResult(new { success = false, message = $"检查失败: {ex.Message}" });
            }
        }
    }

    public class SaveCategoryRequest
    {
        public int CateId { get; set; }
        public string UId { get; set; }
        public string Category_en { get; set; }
        public string PicPath { get; set; }
        public string SeoTitle_en { get; set; }
        public string SeoDescription_en { get; set; }
        public string SeoKeyword_en { get; set; }
        public string Description_en { get; set; }
        public string AddMethod { get; set; }
        public string OrderType { get; set; }
        public string FilterCondition { get; set; }
        public string FilterConditionValue { get; set; }

        // 添加子分类相关属性
        public int Level { get; set; }
        public int SupCateId { get; set; }
        public string PageUrl { get; set; }

        // 添加产品ID数组
        public List<int> ProId { get; set; } = new List<int>();

        // 添加更新后的产品ID数组（由前端拖拽排序修复脚本生成）
        public List<int> UpdatedProId { get; set; } = new List<int>();
    }
}