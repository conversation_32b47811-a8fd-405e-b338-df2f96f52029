using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using YseStore.IService;
using YseStore.IService.Order;
using YseStore.Service.Order;

namespace YseStoreAdmin.Pages.Orders
{
    public class RefundModel : PageModel
    {
        private readonly ICurrencyService _currencyService;
        public IOrderListService _orderListService { get; }

        public RefundModel(ICurrencyService currencyService, IOrderListService orderListService)
        {
            _currencyService = currencyService;
            _orderListService = orderListService;
        }

        public int? Id { get; set; }

        public string currency_symbols { get; set; } = "$";
        public string currency { get; set; } = "$";
        public string currency_string { get; set; } = "USD";
        public async Task OnGetAsync(int id)
        {
            Id = id;
            if (id>0)
            {
                var currencyList = await _currencyService.GetAllCurrencyCache();
                var OrderDetailModel = await _orderListService.GetOrderDetailByOrderIdAsync(id);
                if (OrderDetailModel.Currency!= OrderDetailModel.ManageCurrency)
                {
                    var currencyFirst = currencyList.Where(x => x.Currency == OrderDetailModel.Currency).First();
                    currency_symbols = currencyFirst?.Symbol;
                    currency = currencyFirst?.Symbol;
                    currency_string = currencyFirst?.Currency;
                }
                else
                {
                    var currencyFirst = currencyList.Where(x => x.Currency == OrderDetailModel.ManageCurrency).First();
                    currency_symbols = currencyFirst?.Symbol;
                    currency = currencyFirst?.Symbol;
                    currency_string = currencyFirst?.Currency;
                }

            }
            

            //CurrencySymbols = (await _currencyService.GetDefaultCurrency()).Symbol;
        }


        //public void OnGet(int id)
        //{
        //    Id = id;
        //    //OrderDetailModel = await _orderListService.GetOrderDetailByOrderIdAsync(orderid);
        //}


    }
}
