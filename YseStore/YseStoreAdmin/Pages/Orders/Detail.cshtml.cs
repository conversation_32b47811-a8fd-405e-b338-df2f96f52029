using Entitys;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using YseStore.IService.Customer;
using YseStore.IService.Order;

namespace YseStoreAdmin.Pages.Orders
{
    public class DetailModel : PageModel
    {
        public int? Id { get; set; }
        public IOrderListService _orderListService { get; }
        public ICustomerListService _customerListService { get; }
        public string CountryStatesList { get; set; }
        public DetailModel(IOrderListService orderListService, ICustomerListService customerListService)
        {
            _orderListService = orderListService;
            _customerListService = customerListService;
        }

        public void OnGet(int id)
        {
            Id = id;
            CountryStatesList = System.Text.Json.JsonSerializer.Serialize(_orderListService.GetCountryStatesDataTwo());
        }
    }
}
