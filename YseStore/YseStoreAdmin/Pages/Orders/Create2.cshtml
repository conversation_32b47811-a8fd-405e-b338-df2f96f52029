@page
@model YseStoreAdmin.Pages.Orders.Create2Model
@{
}
@section Styles {
    <link href="/assets/css/orders.css" rel="stylesheet" >
}
<order-create2 />
@section Scripts {

    <script src="/assets/js/orders.js" ></script>
    <script>
               jQuery(function ($) {

            window.usedCurrencyAry = {"3":{"CId":3,"Name":"{\"en\":\"Pound Sterling\",\"zh-cn\":\"英镑\"}","Currency":"GBP","Symbol":"￡","ExchangeRate":"0.7687","Rate":"0.7687","IsUsed":1,"IsDefault":0,"ManageDefault":0,"FlagPath":"//ueeshop.ly200-cdn.com/static/images/currency/GBP.jpg","IsFixed":1,"MyOrder":2,"Method":"manual","AccTime":1742197533,"Proportion":0,"ProportionStatus":0,"IsWebShow":1},"1":{"CId":1,"Name":"{\"en\":\"US Dollar\",\"zh-cn\":\"美元\"}","Currency":"USD","Symbol":"$","ExchangeRate":"1.0000","Rate":"1.0000","IsUsed":1,"IsDefault":1,"ManageDefault":1,"FlagPath":"//ueeshop.ly200-cdn.com/static/images/currency/USD.jpg","IsFixed":1,"MyOrder":1,"Method":"manual","AccTime":1742197533,"Proportion":2,"ProportionStatus":1,"IsWebShow":1}};
            window.currentCurrecy = 1;
            window.manageDefaultCurrency = 1;
            $(document).ready(function(){orders_obj.orders_create_second()});


                window.langPackObj = {
            "web": {
                "global": {
                    "help": "帮助",
                    "category": "分类",
                    "all_category": "所有分类",
                    "all_products": "所有产品",
                    "view_all": "查看全部",
                    "view_more": "查看更多",
                    "view_more_s": "查看更多",
                    "view_product": "查看产品",
                    "viewMorePro": "查看更多产品",
                    "readMore": "查看更多",
                    "view_less": "查看更少",
                    "more": "更多",
                    "shopTheSale": "购买特卖",
                    "viewDetail": "查看详情",
                    "show_now": "现在显示",
                    "viewAllCollections": "查看所有收藏",
                    "let_go": "去吧",
                    "buy_it_now": "立即购买",
                    "seeAllPro": "查看全部产品",
                    "default": "默认",
                    "delete": "删除",
                    "edit": "编辑",
                    "cancel": "取消",
                    "save": "保存",
                    "back": "返回",
                    "clear": "清除",
                    "submit": "提交",
                    "change": "更改",
                    "i_accept": "我接受",
                    "reject": "拒绝",
                    "confirm": "确认",
                    "upload": "上传",
                    "send": "发送",
                    "close": "关闭",
                    "sContinue": "继续",
                    "returnHome": "返回首页",
                    "sReturnHome": "返回主页",
                    "clear_all": "清除所有",
                    "select_all": "全选",
                    "remove": "删除",
                    "removeitem": "删除产品",
                    "add": "添加",
                    "remove_all": "全部清除",
                    "view_cart": "查看购物车",
                    "pleaseSelect": "请选择",
                    "quick_find": "快速查找",
                    "more_options": "更多选项",
                    "quickView": "快速查看",
                    "submit_success": "提交成功",
                    "fullDetail": "完整细节",
                    "index_view": "查看此产品",
                    "more_products": "更多产品",
                    "seeAll": "查看全部",
                    "see_more": "查看更多",
                    "related_category": "相关类别",
                    "no_data": "沒有数据",
                    "pending_five_min": "当前暂无数据，请稍后5分钟查看",
                    "load_more": "加载更多",
                    "learnMore": "了解详情",
                    "shopAll": "商店所有优惠",
                    "date": "日期",
                    "day": "天",
                    "hours": "时",
                    "mins": "分",
                    "secs": "秒",
                    "remark": "备注",
                    "subtotal": "小计",
                    "grandTotal": "总计",
                    "deleteAmount": "已删除",
                    "refundAmount": "已退款",
                    "item": "产品",
                    "price": "价格",
                    "qty": "数量",
                    "total": "总计",
                    "action": "操作",
                    "items": "件",
                    "image": "图片",
                    "user_discount": "会员折扣",
                    "full_discount": "满减优惠",
                    "recall_discount": "召回折扣",
                    "10_digits": "10位数",
                    "8_digits": "8位数",
                    "previous": "上一页",
                    "next": "下一页",
                    "SecurityCode": "验证码",
                    "by": "来源",
                    "order_live": "最近的订单",
                    "facebookStr": "Facebook",
                    "twitterStr": "Twitter",
                    "pinterestStr": "Pinterest",
                    "youtubeStr": "YouTube",
                    "googleStr": "Google",
                    "position": "您的位置",
                    "home": "首页",
                    "popular_search": "热门搜索",
                    "detail": "详情",
                    "details": "详情",
                    "collections": "分类",
                    "backToTop": "回到顶部",
                    "shield": "默认网站页面",
                    "shieldSorry": "对不起！",
                    "shieldTitle_0": "如果你是这个网站的站长，请联系您的服务器商",
                    "shieldTitle_1": "如果您看到此页面，可能是因为",
                    "shieldInfoHD_0": "IP地址已经改变了",
                    "shieldInfoBD_0": "IP地址最近有变动。请检查您的DNS设置，以验证域设置是否正确。可能需要8-24小時让DNS设置生效。可以通过以下方式來清除您的DNS緩存以便恢复对这个网站的访问。",
                    "shieldInfoHD_1": "服务器配置错误",
                    "shieldInfoBD_1": "请检查您的服务器商已经正确设置当前网站，DNS配置到正确的IP地址。进行以上设置后，可能需要重启服务器才能生效。",
                    "shieldInfoHD_2": "该网站可能已经被转移到另外一台服务器上",
                    "shieldInfoBD_2": "该域名可能已更改或者服务器商转移到另外一台服务器上。",
                    "blank_hostname_text": "当前访问的是临时域名，如需调试网站，请<a href=\"https:\/\/member.ueeshop.com\/login.html\">登录<\/a>店铺",
                    "contact": "联系我们",
                    "no_thanks": "不用了，谢谢",
                    "or": "或",
                    "ok": "OK",
                    "go": "走",
                    "goTo": "去",
                    "gly_tit": "卖家秀",
                    "gly_more": "显示更多风格",
                    "show_more": "展示更多",
                    "del_confirm": "您确定删除此产品?",
                    "selected": "请选择",
                    "loading": "数据加载中...",
                    "n_y_0": "否",
                    "n_y_1": "是",
                    "set_error": "设置失败，出现未知错误！",
                    "copy_complete": "已复制好，可贴粘",
                    "filter": "筛选",
                    "format_mobilephone": "请正确填写手机号码！",
                    "format_telephone": "请正确填写电话号码！",
                    "format_fax": "请正确填写传真号码！",
                    "format_email": "请正确填写邮箱地址！",
                    "format_length": "长度不正确！需要填写{Length}位。",
                    "newsletter_success": "新增订阅成功!",
                    "newsletter_exists": "此邮箱已经存在订阅！",
                    "newsletter_your_questions": "请写下你的问题......",
                    "newsletter_your_email": "你的邮件",
                    "ver_click": "点击验证",
                    "ver_slide": "滑动验证",
                    "ver_error": "验证码错误!",
                    "drag_picture": "拖动图片完成验证",
                    "ver_successed": "验证成功",
                    "ver_failed": "验证失败",
                    "please_verification": "请完成图片验证",
                    "processing": "处理中，请稍候...",
                    "processing_str": "处理中",
                    "select_y_country": "选择您的国家",
                    "batch_buy_select": "请选择您要购买的产品！",
                    "batch_buy_option": "请选择产品选项！",
                    "batch_remove_select": "请选择您要删除的产品！",
                    "batch_remove_success": "产品删除成功!",
                    "batch_remove_error": "产品删除失败!",
                    "form_submit_tip": "感谢您提交!",
                    "orderMail": "订单邮箱",
                    "orderNumber": "订单号",
                    "orderNotFind": "抱歉，找不到订单",
                    "orderFindContact": "如果您在查找订单详情时遇到困难，请联系我们。",
                    "querying": "查询...",
                    "total_pages": "共 {num} 页",
                    "change_currency": "更改货币",
                    "change_language": "改变语言",
                    "coupon_discount": "优惠券折扣",
                    "usage_condition": "使用条件",
                    "validity_eriod": "有效期",
                    "comming_soon": "即将推出",
                    "not_enough_now": "现在还不够",
                    "news": "新闻",
                    "cases": "案例",
                    "introduction": "介绍",
                    "low_price": "低价",
                    "higt_price": "高价",
                    "download": "下载",
                    "download_protected_tips": "受密码保护的文件",
                    "download_enter_pwd": "输入密码解锁并下载该文件",
                    "coupon_operation_receive_tips": "兑换成功后，有效期为 {Duration} {Time} 秒"
                },
                "not_fount": {
                    "errorWarning": "对不起!您请求的页面无法找到...",
                    "goBack": "返回",
                    "homePage": "首页"
                },
                "tracking": {
                    "tracking_title": "订单追踪",
                    "tracking_tips": "请输入您下单时成功页显示的订单编号即可查询订单状态",
                    "orders_info": "订单信息",
                    "orders_number": "请输入订单编号",
                    "orders_detail": "查看订单详情",
                    "pro_list": "商品清单",
                    "no_orders": "暂时没有订单信息",
                    "no_tracking": "暂时没有物流信息"
                },
                "header": {
                    "currency": "货币",
                    "language": "语言",
                    "chooseCurrency": "选择货币",
                    "search_history": "搜索历史记录",
                    "yLikePro": "你也许会喜欢",
                    "select_your": "请选择你的",
                    "itemes": "件",
                    "cartStr": "购物车",
                    "shoppingCartStr": "购物车",
                    "MyCartStr": "购物车",
                    "yourCartStr": "您的购物车",
                    "sCreate": "创建账号",
                    "recentlySearch": "最近搜索",
                    "hotSearch": "热门搜索"
                },
                "footer": {
                    "contactUs": "联系我们",
                    "connected": "保持联系",
                    "followUs": "关注我们",
                    "subscribe": "订阅",
                    "our_partners": "我们的合作伙伴",
                    "getInTouch": "保持联系",
                    "submit": "提交",
                    "links": "友情链接",
                    "sign_up": "注册",
                    "email": "邮箱",
                    "emailAddress": "您的邮箱地址",
                    "enter_email": "输入您的邮箱地址",
                    "newsletter_title": "订阅我们的最新资讯",
                    "newsletter_notes": "了解我們的最新产品和促销信息",
                    "newsletter": "订阅",
                    "newsletter_btn": "订阅",
                    "newsletterWTips": "订阅获取当天最新的价格以及优惠！",
                    "unsubscribedTitle": "您已取消订阅！",
                    "unsubscribedTxt": "您不会收到我们的任何营销电子邮件。 您可能仍会收到有关您的帐户和帐单的电子邮件。",
                    "newsletter_tips_0": "请正确填写电子邮件地址！",
                    "newsletter_tips_1": "感谢您的订阅",
                    "newsletter_tips_2": "您的电子邮件已加入我们的列表。",
                    "customTips": "请输入您的邮箱地址"
                }
            },
            "cart": {
                "global": {
                    "shoppingCart": "购物车",
                    "yShoppingCart": "您的购物车",
                    "shoppingBagStr": "购物袋",
                    "checkOut": "结算",
                    "continue": "继续购物",
                    "total_count": "购物车总数",
                    "empty": "您的购物车是空的。",
                    "invalid": "无效",
                    "re_invalid": "删除无效的产品",
                    "free_gift": "赠品",
                    "tax_ship": "结帐时计算税金和运费",
                    "grand_total": "总计",
                    "checkout_str": "进行结算",
                    "checked_error": "请至少选择一个产品",
                    "pre_order": "预购",
                    "mixed_wholesale": "混合批发",
                    "addSuccess": "成功加入购物车。",
                    "viewCartCheckout": "查看购物车和结帐",
                    "cartTotal": "购物车总计",
                    "invalidProduct": "无效产品",
                    "clearInvalidProduct": "清除所有无效产品",
                    "moreLike": "更多此类"
                },
                "tips": {
                    "less_amount": "混批最低可购金额{{amount}}。",
                    "less_quantity": "混批最低可购数量{{quantity}}件。",
                    "less_tips": "混合批发起订量为{{amount}}或{{quantity}}件"
                }
            },
            "blog": {
                "global": {
                    "searchNote": "搜索",
                    "searchBtn": "搜索",
                    "latestBlog": "最新博客",
                    "allblog": "全部博客",
                    "popular_blog": "热门博客",
                    "archives": "归档",
                    "blog_category": "分类",
                    "tags": "标签",
                    "by": "来自",
                    "comments": "评论",
                    "leaveAReply": "发表评论",
                    "commentsTips": "您的电子邮件地址将不会被公开。必填字段已标记。 *",
                    "name": "名称",
                    "email": "电子邮件",
                    "content": "内容",
                    "verificationCode": "验证码",
                    "writeReview": "写评论",
                    "recentlyReviews": "最近评论",
                    "codeError": "验证码错误！",
                    "submitSuccess": "提交成功",
                    "recentPosts": "最近的帖子",
                    "review": "{{count}} 条评论",
                    "readMore": "阅读更多",
                    "view": "{{count}} 次观看"
                }
            },
            "user": {
                "global": {
                    "Unknown": "未知",
                    "Ms": "女",
                    "Mr": "男",
                    "sign_up": "注册",
                    "sign_in": "登录",
                    "JoinFree": "注册",
                    "login_tips": "请先登录",
                    "welcome": "欢迎",
                    "firstname": "名",
                    "your_fir_name": "您的名字",
                    "lastname": "姓",
                    "surname": "姓",
                    "your_last_name": "您的姓氏",
                    "you_domain_com": "<EMAIL>",
                    "at_6_char": "至少6个字符",
                    "password": "密码",
                    "confirm_label": "确认",
                    "confirm_pwd": "确认密码",
                    "email": "邮箱",
                    "returnHome": "返回首页页面",
                    "productName": "产品名称",
                    "distributorEmail": "经销商邮箱",
                    "apply": "申请",
                    "customer": "客户",
                    "use": "使用"
                },
                "login": {
                    "log_in": "登录",
                    "login_brief": "访问您的帐户和订单历史记录。",
                    "remember_me": "记住账号",
                    "forgetpwd": "忘记密码？",
                    "new_customer": "新客户 ？ <a href='\/account\/sign-up.html' class='FontColor'>从这里开始<\/a>",
                    "new_customer_notes": "新客户 ？ <a href='{{url}}' class='FontColor'>从这里开始<\/a>",
                    "emailAddress": "您的邮箱地址",
                    "signInWith": "第三方登录",
                    "popups_title": "登录",
                    "error_note": "不正确的电子邮件地址或密码，请重试<br>在你输入密码之前，请确认Caps Lock键已经关闭",
                    "forgot": "<a href='\/account\/forgot.html' class='forgot'>忘记密码<\/a>？",
                    "stay_note": "保持登录状态<span>为了保护您的隐私建议你注销登录<\/span>"
                },
                "register": {
                    "already": "已经有一个账号？",
                    "signInNow": "立即登录",
                    "use_with": "使用您的帐户",
                    "register_title": "创建帐号",
                    "register_now": "现在注册",
                    "incorrect": "您输入的邮箱地址不正确。",
                    "createPWD": "创建您的密码",
                    "tryAgain": "密码不匹配。请重试。",
                    "createInfo0": "如果同意请点击\"创建我的账号\"：",
                    "createInfo2": "我可能会收到來自{SiteName}有关订单和送货的邮件。",
                    "createInfo3": "我可能会收到來自{SiteName}有关新产品和促销的邮件",
                    "createAccount": "创建我的账号",
                    "verTitle": "恭喜您，您已经成功完成{Domain}的账号注册！",
                    "verInfo": "您可以在网站的<a href=\"\/account\/\" class=\"FontColor\">账号页面<\/a>，编辑您的昵称<strong>{UserName}<\/strong>。",
                    "varInfo_0": "请验证您的邮箱地址",
                    "varInfo_1": "验证您的邮件可以提高您账号的安全性。",
                    "varInfo_2": "要获取有关网站的更多信息，请访问",
                    "varInfo_3": "您也可以",
                    "varInfo_4": "单击我们发送给您的电子邮件中的链接",
                    "verifyNow": "现在验证",
                    "reviewInfo_0": "待批准",
                    "reviewInfo_1": "您的帐户已创建，现在正在等待批准。",
                    "reviewInfo_2": "我们的管理团队可能需要与您联系以获取更多信息以完成您的帐户申请。",
                    "resendEmail": "重发邮件",
                    "newBuyerGuide": "新买家指南",
                    "purchaseFlow": "购买流程",
                    "returnPage": "回到上一页",
                    "homePage": "主页去",
                    "varComTitle": "恭喜您， <strong class=\"FontColor\">{UserName}<\/strong>， 您的邮箱地址已经验证。",
                    "varComTitle_1": "恭喜！ 您的电子邮件地址已通过验证",
                    "varComTitle_error": "您的电子邮件地址尚未经过验证",
                    "varComInfo": "您下一步想去那里",
                    "reviewInfo_error": "您的帐户已创建，现在正在等待批准。 我们的管理团队可能需要与您联系以获取更多信息以完成您的帐户申请。"
                },
                "forgot": {
                    "resetPWD": "重设密码",
                    "newPWD": "新密码",
                    "ConfirmPWD": "确认密码",
                    "sentEmail": "我们已经发送了一封邮件到您的邮箱，请按照该邮件中的说明重置密码。",
                    "receivedEmail": "沒有收到邮件？",
                    "checkEmail": "检查垃圾邮件文件夹，如果仍然找不到密码重置邮件，请联系我们的客服，谢谢！",
                    "continueShopping": "继续购物",
                    "successfully": "您已成功重置您的密码。",
                    "signIndex": "登录我的账号",
                    "enterEmail": "请输入您的邮箱地址",
                    "enteredEmail": "此內容跟您输入的邮箱地址不正确。",
                    "sentReset": "现在，我们可以重新设置密码了，请您在下面输入您的邮箱地址。然后点击提交，您将收到一封邮件，按照邮件说明重置您的密码。",
                    "contactServices": "如果您不记得您注册的邮箱地址或有其他登录账号问题，请联系我们的客服。",
                    "sendEmail": "发送邮件",
                    "matchPWD": "密码不匹配。请重试。",
                    "enterPWD": "要重置密码，请在下面输入您的新密码。"
                },
                "account": {
                    "basicTitle": "基本信息",
                    "indexTitle": "我的账号",
                    "orderTitle": "我的订单",
                    "reviewTitle": "我的评论",
                    "favoriteTitle": "我的收藏夹",
                    "couponTitle": "我的优惠劵",
                    "addressTitle": "管理地址簿",
                    "settingTitle": "账号设置",
                    "inboxTitle": "我的收件箱",
                    "distributionTitle": "分销",
                    "productsTitle": "问题与解答",
                    "messageTitle": "系统消息",
                    "signOut": "退出",
                    "logOut": "退出",
                    "binding_completed": "恭喜，您已完成{OauthTitle}授权。",
                    "binding_finish": "要完成注册，请提供邮箱地址。",
                    "binding_note": "请注册，所有重要的邮件都将发送到此邮箱。",
                    "binding_tips": "邮箱地址不正确，请重试。",
                    "time": "时间",
                    "Content": "内容",
                    "newMessage": "你有新消息",
                    "enter_message": "这里写上您的消息...",
                    "reply_content": "回复內容",
                    "addReply": "在这里填写回复内容...",
                    "email_addr": "邮箱地址",
                    "reply_btn": "回复",
                    "sLikeProd": "你也许会喜欢",
                    "recentlyViewed": "最近浏览过的",
                    "ship_addr": "收货地址",
                    "bill_addr": "帐单地址",
                    "add": "添加新地址",
                    "name": "姓名",
                    "address1": "地址",
                    "phoneNum": "电话",
                    "province_state": "省\/州",
                    "city": "城市",
                    "state": "州\/省\/地区",
                    "countrySelect": "国家\/地区",
                    "cpf_cnpj": "CPF或CNPJ代码",
                    "CPF": "CPF (个人订购)",
                    "CNPJ": "CNPJ (公司订购)",
                    "cpf": "CPF",
                    "cnpj": "CNPJ",
                    "per_order": "个人订购",
                    "com_order": "公司订购",
                    "per_id_num": "个人ID号",
                    "vat_id_num": "增值税号",
                    "zip_code": "邮编",
                    "addr_line_1": "地址",
                    "addr_line_2": "公寓，套房，门牌号等 （可选的）",
                    "discount": "折扣",
                    "status": "状态",
                    "package": "包裹",
                    "orders_no": "订单号：",
                    "orderDate": "订单日期",
                    "orderTotal": "订单总计",
                    "orderStatus": "订单状态",
                    "printOrder": "打印订单",
                    "cancelReason": "取消原因",
                    "cancelOrder": "确认取消订单",
                    "successDel": "您已经成功地刪除了您的订单。",
                    "backReturn": "点击<a class=\"blue u\" href=\"\/account\/orders\/\">此处<\/a>回到我的订单，进行其他操作或继续购物。",
                    "shippedTo": "运送",
                    "shippingMethod": "送货方式",
                    "total_weight": "总重量",
                    "billedTo": "账单",
                    "paymentMethod": "付款方式",
                    "trackNo": "物流编号",
                    "paymentInfo": "付款信息",
                    "senderName": "发件人姓名",
                    "referenceNumber": "参考编号",
                    "country": "国家",
                    "subtotal": "小计",
                    "handingFee": "手续费",
                    "couponSavings": "优惠劵优惠",
                    "writeReview": "撰写评论",
                    "receiving": "确认收货",
                    "shippedTime": "发货时间",
                    "tracking": "物流跟踪",
                    "shipment": "发货",
                    "trackItem": "发货{Item}-{Count}",
                    "OrderStatusAry_1": "等待支付",
                    "OrderStatusAry_2": "等待确认支付",
                    "OrderStatusAry_3": "支付错误",
                    "OrderStatusAry_4": "等待发货",
                    "OrderStatusAry_5": "已发货",
                    "OrderStatusAry_6": "完成",
                    "OrderStatusAry_7": "取消",
                    "changeProfile": "更改您的个人资料",
                    "changeEmail": "更改邮箱地址",
                    "existingPWD": "现有密码",
                    "newAddress": "新邮箱地址",
                    "changePWD": "更改密码",
                    "rePWD": "重新输入密码",
                    "error_Email": "邮箱不正确。 请再试一次。",
                    "error_LoginStatus": "此账号还没通过审核。<br \/> 如有疑问，请联系客服。",
                    "error_Password": "邮箱或者密码不正确。<br \/> 请重试。在您输入密码之前请检查Caps Lock键是否关闭。",
                    "error_Code": "验证码错误。",
                    "error_EmailEntered": "您输入的邮箱地址不正确。",
                    "error_Name": "您的名字不能为空。",
                    "error_Exists": "邮箱地址已存在，请更换或登录账号。",
                    "error_EmailBeen": "对不起，这个邮箱地址已经被使用。",
                    "error_EmailSuccess": "邮箱地址修改成功！",
                    "error_PWDWrong": "对不起，现有的密碼是错误的！",
                    "error_PWDBeen": "对不起，您的密码不匹配，请重试！",
                    "error_PWDSuccess": "更改密码成功！",
                    "error_Forgot": "对不起，这个邮箱地址还没有注册。",
                    "error_Incomplete": "邮箱地址不正确。 请重试。",
                    "error_Error": "出现未知错误！",
                    "error_pic": "请上传图片",
                    "error_file": "请上传文件",
                    "awaiting_review": "等待评论",
                    "coupons": "优惠劵",
                    "noCoupons": "没有优惠券",
                    "order_over": "订单超过{OrderPrice}",
                    "order_item": "订单购买{OrderCount}件产品以上",
                    "already_received": "已领取",
                    "expired": "已过期",
                    "code": "码",
                    "order_details": "订单详细信息",
                    "new_coupon": "你有新的优惠劵",
                    "DIST_make_money": "分销",
                    "DIST_total_comm": "总佣金",
                    "DIST_comm_balance": "您的当前余额",
                    "DIST_withdraw": "提取",
                    "DIST_referred_mem": "我的朋友",
                    "DIST_how_to_share": "分享",
                    "DIST_copy": "复制",
                    "DIST_copy_tips": "复制此链接并在您的社交媒体渠道上分享",
                    "DIST_click_tips": "点击下面的按钮并分享到",
                    "DIST_products_tips1": "喜欢这个产品吗？",
                    "DIST_products_tips2": "<a href=\"\/account\/\">加入我们<\/a>，通过独家优惠码促销该产品！",
                    "DIST_how_mak_money": "如何赚钱",
                    "DIST_info_title_0": "分享链接",
                    "DIST_info_title_1": "邀请",
                    "DIST_info_title_2": "加入",
                    "DIST_info_title_3": "得到佣金",
                    "DIST_info_txt_0": "在您的社交媒体渠道上复制并分享此链接。",
                    "DIST_info_txt_1": "邀请朋友通过您的链接注册并完成任意订单。",
                    "DIST_info_txt_2": "加入我们的每位朋友都会从您的链接中获得优惠劵。",
                    "DIST_info_txt_3": "获得的佣金可以提取到您的帐户中。",
                    "DIST_comm_detail": "佣金详情",
                    "DIST_record": "记录",
                    "DIST_level": "级别",
                    "DIST_total": "合计",
                    "DIST_amount": "金额",
                    "DIST_card_num": "Paypal邮箱地址",
                    "DIST_card_acc": "Paypal账号名称",
                    "DIST_status": "状态",
                    "DIST_status_0": "待定佣金",
                    "DIST_status_1": "已返佣",
                    "DIST_secondary": "二級经销商",
                    "DIST_tertiary": "三級经销商",
                    "reg_time": "注册时间",
                    "withdrawTip0": "没有相关会员信息",
                    "withdrawTip1": "提现金额必须大于0",
                    "withdrawTip2": "提现金额不能大于余额",
                    "friends": "朋友",
                    "order_info": "订单信息",
                    "orderNo": "订单号",
                    "orderstatus": "订单状态",
                    "address_book": "地址簿",
                    "enjoyDiscount": "享受{Discount}优惠",
                    "freeShipping": "免运费",
                    "enjoyFreeShipping": "享受免运费",
                    "cancel_order": "取消订单",
                    "total_price": "总计",
                    "coupon_tips": "恭喜，您获得了优惠券：{CouponNumber}。 <br>要了解更多信息，请访问我的优惠券。",
                    "order_no": "订单号",
                    "commission": "佣金",
                    "distributor": "经销商",
                    "order_status": "订单状态",
                    "commission_status": "佣金状态",
                    "order_total": "累计",
                    "send_email_ture": "电子邮件已成功发送给您。请输入您的电子邮件地址，并检查确认电子邮件。",
                    "send_email_false": "很抱歉，您一分钟只能点击一次，请一分钟后再点击。",
                    "go_to_view": "去查看",
                    "order_cancel": "你确定要删除这个订单？",
                    "sure": "你确定？",
                    "delete_shipping": "你确定要删除这个送货地址？",
                    "send": "发送",
                    "reg_err_Email": "请输入电子邮件地址。",
                    "reg_err_EmailFormat": "您输入的电子邮件无效。 请检查您的电子邮件，然后重试。",
                    "reg_err_PWDConfirm": "请重新输入您的新密码。",
                    "reg_err_PWDNotMatch": "您的密码不匹配，请重试。",
                    "address_tips_PleaseEnter": "请输入您的{Field}。",
                    "address_tips_taxcode_length": "您的{Field}必须至少包含{TaxLength}数字。",
                    "address_tips_phone": "请输入您的手机号码。",
                    "address_tips_phone_format": "请输入一个有效的电话号码。",
                    "address_tips_phone_length": "您的电话号码必须至少为7位数",
                    "distibutor_detail": "经销商详情",
                    "share_store": "分享商店",
                    "share_products": "分享产品",
                    "choose_way_to_share": "选择分享方式",
                    "share_to_social_media": "分享到社交媒体",
                    "copy": "复制",
                    "copy_success": "复制成功",
                    "total_order": "总订单",
                    "sentMoney": "汇钱",
                    "contents": "內容",
                    "MTCN": "MTCN# No.",
                    "maxAmount": "最高折扣价：{{amount}}",
                    "thanks_you": "谢谢你！",
                    "review_success": "您的评论已成功提交。",
                    "review_verify": "您的评论已成功提交。 我们会尽快处理。",
                    "review_coupon": "您的评论已成功提交，优惠券已发送至您的帐户。",
                    "go_to_account": "前往账户",
                    "expected_earning": "预期收益",
                    "email_account": "帐户",
                    "email_name": "姓名",
                    "email_distributor": "经销商",
                    "email_amount": "数量",
                    "apply_to_be_distributor": "申请成为分销商",
                    "pending_review": "待审核",
                    "thanks_apply_tips": "感谢您的申请。我们将尽快审核并通过电子邮件回复您。",
                    "apply_been_rejected_title": "申请已被拒绝",
                    "apply_been_rejected_subtitle": "抱歉，由于以下原因，您的经销商申请被拒绝。您可以在修改信息后重新申请。",
                    "apply_again": "再次申请",
                    "fail_reason": "失败原因",
                    "couponTimes-day": "{{num}}天",
                    "couponTimes-hour": "{{num}}小时"
                },
                "verify": {
                    "activate_account": "激活账户",
                    "current_account_at": "当前帐户 {Email}",
                    "link_expired": "链接已过期"
                },
                "orders": {
                    "removed": "已删除",
                    "products_num": "{num} 个产品",
                    "buy_again": "再次购买",
                    "buy_again_failed": "无法添加到购物车",
                    "buy_again_success": "{{number}} 个产品已成功添加到购物车",
                    "buy_again_cart": "以下产品无法添加到购物车"
                },
                "points": {
                    "title": "我的积分",
                    "wayEarn": "赚取方式",
                    "wayRedeem": "兑换方式",
                    "points": "积分",
                    "numPoints": "{{num}} 积分",
                    "expired": "{{points}} 积分将于 {{time}} 到期",
                    "redeem": "赎回",
                    "all": "全部",
                    "earned": "已赚取",
                    "used": "用过的",
                    "fluctuation": "积分波动",
                    "source": "来源",
                    "date": "日期",
                    "order": "下订单",
                    "orderDesc": "每消费 {{amount}} 可获得 {{num}} 积分",
                    "coupon": "{{text}} 优惠券",
                    "canRedeem": "您可以用 {{num}} 积分兑换优惠券：",
                    "suffPoins": "积分不足，请多加积分。",
                    "reward": "奖励 {{points}} 积分",
                    "maxPoints": "每个订单最多 {{points}} 点"
                },
                "pointsLogsType": {
                    "register": "成功注册",
                    "newsletter": "邮件订阅",
                    "placeorder": "成功下单",
                    "return": "返还积分",
                    "adjust": "调整积分",
                    "expired": "积分过期",
                    "cancel": "取消积分",
                    "deduction": "购物抵扣金额",
                    "exchange": "兑换优惠券"
                }
            },
            "products": {
                "global": {
                    "prodDetail": "产品详细信息",
                    "lowPrice": "{{lowPrice}}<span class=\"g_low_price\">起<\/span>",
                    "end_in": "结束于"
                },
                "lists": {
                    "item_column": "第{A}-{B}条，共{C}件“{K}”产品",
                    "sort_by": "排序方式",
                    "sort_by_ary_0": "全部",
                    "sort_by_ary_1": "时间（从新到旧）",
                    "sort_by_ary_2": "时间（从旧到新）",
                    "sort_by_ary_3": "价格（从低到高）",
                    "sort_by_ary_4": "价格（从高到低）",
                    "sort_by_ary_5": "销量（从高到低）",
                    "sale": "出售",
                    "ends_in": "结束于",
                    "coming_soon": "即将推出"
                },
                "goods": {
                    "buyNow": "立即购买",
                    "shopNow": "现在购买",
                    "inquiry_now": "现在询价",
                    "addToBag": "添加至购物袋",
                    "addToCart": "添加到购物车",
                    "addToFavorites": "添加到收藏夹",
                    "new": "新品",
                    "quickShop": "快速购买",
                    "freeShipping": "免运费",
                    "limited_offer": "限时优惠！",
                    "sold_number": "已售出{SoldNumber}",
                    "sold": "销量",
                    "qty": "数量",
                    "qty_text": "数量",
                    "soldout": "售罄",
                    "inStock": "有现货",
                    "outStock": "缺货",
                    "num_inStock": "{Num} 库存",
                    "availability": "可用性",
                    "itemCode": "产品编号",
                    "available": "剩余{AvailableNum}{AvailablePieces}",
                    "notice": "到货通知",
                    "save": "优惠",
                    "customer_review": "客戶评价",
                    "reviews": "评论",
                    "writeReview": "写评论",
                    "mayLike": "你可能会喜欢",
                    "free_shipping": "免运费",
                    "write_a_review": "评论这个商品",
                    "your_rating": "你的评分",
                    "write_your_name": "你的名字",
                    "write_your_review": "写下您的评论",
                    "write_review_tip": "您的评论很棒：描述您喜欢什么、不喜欢什么以及购物者应该知道的其他关键事项（至少 1 个字符）",
                    "your_experience": "描述你的经历...",
                    "add_your_images": "添加您的图像",
                    "name": "名称",
                    "review_content": "评论内容",
                    "review_tips_0": "产品评论仅对已经购买并收到此产品的客户有效。",
                    "picture_tips": "请上传JPG\/GIF\/PNG文件。",
                    "description": "描述",
                    "pro_inquiry": "产品查询",
                    "inquiry_title": "查询",
                    "i_number": "编号。",
                    "helpful": "这条评论有帮助？",
                    "units": "单位",
                    "platform_tips": "或者在其他网站上购买",
                    "realOrderInfo": "{Country}{UserName}购买",
                    "realOrderSec": "{Time}秒前",
                    "realOrderSecNum": "{Time}秒前",
                    "realOrderMin": "{Time}分钟前",
                    "realOrderMinNum": "{Time}分钟前",
                    "realOrderHour": "{Time}小时前",
                    "realOrderHourNum": "{Time}小时前",
                    "realOrderDay": "{Time}天前",
                    "realOrderDayNum": "{Time}天前",
                    "realOrderMon": "{Time}个月前",
                    "realOrderMonNum": "{Time}个月前",
                    "new_arrival": "新货",
                    "hotProd": "热销产品",
                    "sale": "销售",
                    "special_offer": "特价",
                    "whats_hot": "热销产品",
                    "select": "选择{SelectName}",
                    "full_distance": "产品达到 {ConditionalPrice} 时，您会获得 {DiscountedPrices} 的优惠",
                    "wholesale": "批发价",
                    "full_txt_0": "满{K}立减{V}",
                    "full_txt_1": "满{K}立减{V}",
                    "full_txt_2": "满{K}件立减{V}",
                    "full_txt_3": "满{K}件立减{V}",
                    "full_txt_4": "每满{K}立减{V}",
                    "full_txt_5": "每满{K}件立减{V}",
                    "full_txt_6": "消费满{K}可获得<span>免费<\/span>礼品",
                    "full_txt_7": "购买超过{K}件产品可获得<span>免费<\/span>礼品",
                    "full_txt_8": "每购买{K}即可获得<span>免费<\/span>礼品",
                    "full_txt_9": "每购买{K}件即可获得<span>免费<\/span>礼品",
                    "attributes_tips": "请选择您想要的信息",
                    "more_details": "查看详细内容",
                    "plsChooseOption": "请选择选项。",
                    "total_extras": "总额外费用",
                    "full_name": "全名",
                    "whatsapp_phone": "Whatsapp \/电话",
                    "i_success_tip": "询盘提交成功，谢谢您的支持！",
                    "cod_sevenday": "7天鉴赏期",
                    "cod_cod": "货到付款",
                    "cod_freeshipping": "免运费",
                    "cod_orders_tracking": "订单追踪",
                    "cod_notice": "用户须知",
                    "cod_message": "留言",
                    "cod_submit_orders": "提交订单",
                    "cod_successful_purchase": "购买",
                    "mapbox_recommended": "受到推崇的",
                    "mapbox_streets_water_parks": "街道+水+公园",
                    "mapbox_streets_waters": "街道+水",
                    "mapbox_streets_parks": "街道+公园",
                    "mapbox_streets_only": "仅街道",
                    "mapbox_water_only": "仅水",
                    "mapbox_map_view": "地图检视",
                    "mapbox_jewelry_view": "珠宝视图",
                    "pro_to_check": "前往付款",
                    "re_to_shop": "返回购物",
                    "cart_add": "添加到购物车成功！",
                    "cart_items": "{CartNum}件商品在购物车中",
                    "product_error": "此产品的数据已经丢失，请删除后重新购买",
                    "no_review_data": "还没有客户评论",
                    "warning_number": "购买量相比库存量更多或者起订量更少",
                    "warning_MOQ": "请输入 {{qty}} 或更多的数量。",
                    "warning_Max": "对不起，你只能买不超过{{qty}}。",
                    "warning_stock": "对不起，你只能买不超过{{qty}}。",
                    "select_country": "--请选择您所在的国家--",
                    "sign_in": "请您先登录！",
                    "free": "免费",
                    "via": "通过",
                    "unavailable": "不可购买",
                    "shipEstimated": "预计送达时间",
                    "freight_country_region": "国家\/地区",
                    "freight_shipping": "配送",
                    "freight_ship_from": "发货地",
                    "freight_ship_to": "配送至",
                    "freight_ship_method": "物流方式",
                    "freight_not_delivery_to": "<strong>无法配送<\/strong>至{Country}",
                    "freight_delivery_to": "通过{Ship}配送至{Country}",
                    "freight_no_support": "该供应商\/物流公司不向您选择的国家\/地区交货",
                    "freight_estimated_delivery": "预计送达时间",
                    "freight_cost": "运费",
                    "freight_carrier": "物流公司",
                    "freight_memo": "估计费用，实际金额请以结帐时为准",
                    "review_rating": "您的评分是必需的。",
                    "review_title": "你的名字是必需的。",
                    "review_max": "剩余的字符:请不要超过5000字。",
                    "arrival_info_0": "已成功提交的业务，产品涵盖将立即通知客户。",
                    "arrival_info_1": "请登录申请到货通知。",
                    "arrival_info_2": "该产品已申请到货通知！",
                    "shipping_method_tips": "请选择送货方式！",
                    "checkout": "結算",
                    "card_type": "请选择卡类型",
                    "card_num": "卡号不能为空，必须为数字，长度必须为16或15。",
                    "card_month": "卡有效期不可为空。",
                    "card_year": "卡有效期不可为空。",
                    "card_cvv": "CVV2 \/ CSC错误，必须为数字，长度必须为3！",
                    "card_bank": "开证行不能为空！",
                    "group": "团购",
                    "chooseOptions": "选择选项",
                    "couponDiscount": "{Discount} 折扣",
                    "couponPrice": "节省 {Price}",
                    "getIt": "得到它",
                    "receiveSucc": "成功接收",
                    "combination_save": "一起购买节省{Price}",
                    "combinationTitle": "经常一起购买",
                    "sku": "SKU",
                    "selected_list": "选定列表",
                    "num_pieces": "{Num} 件",
                    "min_scope": "最小起订：{Min} 件",
                    "max_scope": "每个订单限制 {Max}",
                    "max_qty_tips": "该产品的最大购买量为 {Max}",
                    "no_products": "没有产品",
                    "review_thank": "感谢您的评论",
                    "review_manual_thank": "谢谢！ 请稍后刷新页面以查看您的评论",
                    "video_tips": "您的浏览器不支持 video 标签。",
                    "max": "最大限度",
                    "invalidOptions": "请选择您要购买的选项",
                    "upload_max_tips": "抱歉，您只能上传不大于 20 MB 的文件",
                    "savePrice": "节省 {Price}",
                    "LoginSeePrice": "登录查看价格",
                    "withPhotos": "有媒体",
                    "member_free_tips": "会员可享受免费送货",
                    "add_your_video": "添加您的视频",
                    "upload_video_tips": "请仅提供不大于 20MB 的 MP4 文件。",
                    "upload_video_limit": "抱歉，您只能上传不大于20MB的文件"
                },
                "app": {
                    "arrival_notice_enter_email": "请输入您的电子邮件地址",
                    "arrival_notice_enter_email_tips": "您输入的电子邮件地址不正确。",
                    "arrival_notice_result_text": "商品有货时，我们将通过电子邮件发送至 <span class=\"fc_red\">{{email}}<\/span>",
                    "guide_title": "尺码表"
                }
            },
            "checkout": {
                "global": {
                    "contents": "內容",
                    "summary": "小计",
                    "totalamount": "总金额",
                    "status": "订单状态",
                    "paynow": "立即付款",
                    "incompatible": "有 {{qty}} 种产品不符合批发订单要求"
                },
                "checkout": {
                    "step_ary_0": "下订单",
                    "step_ary_1": "付款",
                    "step_ary_2": "完成",
                    "step_infomation": "信息",
                    "step_shipping": "运输",
                    "step_payment": "支付",
                    "expressCheckout": "Express Checkout",
                    "customerInfo": "联系信息",
                    "emailNewsOffers": "通过电子邮件向我发送新闻和优惠",
                    "paymethod": "付款方式",
                    "secureTips": "所有交易都是安全和加密的",
                    "products": "产品信息",
                    "shipTariff": "收货税号",
                    "moreAddress": "更多地址",
                    "CreditOrDebit": "信用卡或借记卡",
                    "shipsFrom": "发货地",
                    "shipmethod": "送货方式",
                    "cost": "运费",
                    "estimated_delivery": "预计送达时间",
                    "carrier": "物流公司",
                    "special": "特别说明",
                    "special_df": "卖家特别说明",
                    "apply": "验证",
                    "coupon_code": "优惠劵",
                    "enter_code": "输入或选择优惠券代码",
                    "code": "代码",
                    "coupon_error": "优惠劵\"<strong><\/strong>\"无效。它不存在或者尚未激活或者已過期。",
                    "subtotal": "小计",
                    "code_save": "优惠劵优惠",
                    "fee": "服务费",
                    "shipcharge": "运费",
                    "grandTotal": "总计",
                    "place_order": "完成订单",
                    "shipPhone": "收货电话",
                    "phoneTips": "请输入您的手机号码。",
                    "zipTips": "输入 {Country} 的有效邮政编码。",
                    "weight": "重量",
                    "ship_insur": "运费及保险",
                    "tax": "税收",
                    "discount": "折扣",
                    "coupon_save": "优惠劵优惠",
                    "return_to_cart": "返回购物车",
                    "sentMoney": "汇钱",
                    "MTCN": "MTCN# No.",
                    "bankSlipCode": "银行转账码",
                    "orderInfo": "订单信息",
                    "orderNo": "订单号",
                    "amount": "金额",
                    "orgCode": "卡的种类",
                    "CardNo": "卡号",
                    "expirationDate": "到期时间",
                    "month": "月",
                    "year": "年",
                    "CVV2": "CVV2\/CSC",
                    "issuingBank": "发卡银行",
                    "yBillAddress": "您的账单地址",
                    "yShipAddress": "您的收货地址",
                    "pName": "名称",
                    "pAddress": "地址",
                    "pPhoneNo": "电话号码",
                    "makePayment": "付款",
                    "EnterCardInfo": "输入您的卡信息",
                    "required_fields_tips": "有必填项为空",
                    "already_paid_tips": "已经付款过",
                    "abnormal_tips": "付款方式处理异常，建议更换其他付款方式",
                    "cancel_tips": "确认，就会放弃这次付款，无法再次返回继续付款",
                    "continue_str": "继续结账",
                    "coupon_price_tips": "产品总价没达到优惠劵使用范围，系统自动取消此优惠劵",
                    "tips_no_delivery": "我们不支持发货到 {Country}.",
                    "tips_contact_us": "联系我们: <a href='mailto:{Email}'>{Email}<\/a>",
                    "tips_pay_loading_0": "我们正在重定向您现在的付款",
                    "tips_pay_loading_1": "这将需要几秒钟或者更多时间",
                    "coupon_tips_0": "优惠劵<strong>{CouponCode}<\/strong> 无效. 此优惠劵不存在.",
                    "coupon_tips_-1": "优惠劵<strong>{CouponCode}<\/strong> 无效. 此优惠劵还不可用.",
                    "coupon_tips_-2": "优惠劵<strong>{CouponCode}<\/strong> 无效. 此优惠劵已过期.",
                    "coupon_tips_-3": "优惠劵<strong>{CouponCode}<\/strong> 无效. 此优惠劵已使用过.",
                    "coupon_tips_-4": "优惠劵<strong>{CouponCode}<\/strong> 无效. 订单总额要达到 <strong>{Price}<\/strong> 才可以使用.",
                    "coupon_tips_-5": "优惠劵<strong>{CouponCode}<\/strong> 无效. 此优惠券该客户没有领取。",
                    "coupon_tips_-6": "优惠劵<strong>{CouponCode}<\/strong> 无效. 部分产品不属于此优惠券的优惠范围内。",
                    "coupon_tips_-7": "优惠券<strong>{CouponCode}<\/strong>无效。 当订单数量达到<strong>{Num}<\/strong>时，您可以使用优惠券。",
                    "coupon_tips_-8": "此优惠券不可与其他优惠或折扣同时使用",
                    "coupon_tips_register": "优惠码<strong>{CouponCode}<\/strong>无效。该优惠码仅适用于新会员。",
                    "coupon_tips_member": "优惠码<strong>{CouponCode}<\/strong>无效。登录后即可获取。",
                    "coupon_tips_review": "优惠码<strong>{CouponCode}<\/strong>无效。成功留下评价后即可获得。",
                    "coupon_tips_confirmReceipt": "优惠码<strong>{CouponCode}<\/strong>无效。确认收货后即可获得。",
                    "coupon_tips_orderCompleted": "优惠码<strong>{CouponCode}<\/strong>无效。确认收货后即可获得。",
                    "method": "方法",
                    "return_shipping": "返回航运",
                    "continue_payment": "继续付款",
                    "return_infomation": "返回信息",
                    "continue_shipping": "继续发货",
                    "show_summary": "显示订单摘要",
                    "hide_summary": "隐藏订单摘要",
                    "next_step": "下一步计算",
                    "calculatelater": "稍后计算",
                    "default_payment_notes": "单击“完成订单”后，您将被重定向到信用卡或借记卡以安全地完成购买",
                    "invalidCountry": "当前选择的国家无效，请重新选择",
                    "invalidRegion": "当前选择的区域无效，请重新选择",
                    "invalidCart": "购物车产品无效，请到购物车查看",
                    "waitingPayment": "等待付款",
                    "addTransferConfirm": "请添加银行转账凭证，我们将确认。",
                    "addPaymentProof": "添加付款证明",
                    "uploadPaymentProof": "上传付款证明",
                    "editPaymentProof": "编辑付款证明",
                    "excheckout_complete_tips": "请点击此页面上的 PayPal 支付按钮完成购买。",
                    "billAddress": "帐单地址",
                    "billAddressSame": "与收货地址相同",
                    "billAddressDifferent": "使用不同的账单地址",
                    "confirmPayment": "确认付款",
                    "changePayMethod": "更改付款方式",
                    "saved_addresses": "已保存地址",
                    "use_new_address": "使用新地址",
                    "errorTaxCodeFormat": "输入{CountryName}的有效{TaxCodeName}代码",
                    "zipVerifying": "邮政编码正在验证。请等待。",
                    "savePaymentMethod": "安全存储付款详细信息以供将来购买",
                    "paypalVaultItem": "尾号 {{last_digits}} 的 {{brand}}（{{expiry}} 到期）",
                    "useNewPaymentMethod": "使用新的付款方式",
                    "securityCode": "安全码"
                },
                "result": {
                    "error_reason": "错误原因: ",
                    "sCreate": "创建账号",
                    "win_coupon": "立即创建一个账号并获得 {CouponPrice} 优惠劵！",
                    "sCreateTips": "为了帮助您轻松找到订单。",
                    "sUAccount": "你的账号",
                    "consumption": "最低消費金额是{MinPrice}，你现在还差{Price}",
                    "quota": "最高限制金额是{MaxPrice}，您现在超出{Price}",
                    "not_accept": "对不起，我们不支持该货币支付！",
                    "paymentInfo": "当您的订单付款成功后，您将收到一封确认的邮件。 您的订单详细信息会通过安全传输。 由于汇率问题，结算金额可能略有不同。 非常感谢您在我们的商城购物",
                    "paySent": "发送付款信息",
                    "successfully": "您的订单已支付成功。",
                    "errorfully": "您的订单付款失败。",
                    "sStatusAwait": "等待确认付款。",
                    "sStatusOk": "谢谢 ！ 您的付款已完成。",
                    "sStatusThank": "谢谢！",
                    "sOrderNumber": "您的订单号码是 {OrderNum}。",
                    "sAwaitTips1": "如果您已付款，我们将自动更改订单状态。",
                    "sAwaitTips2": "如有疑问，请联系",
                    "sThankTips1": "感谢您与我们购物！ 您的订单已收到！",
                    "sThankTips2": "感谢您与我们购物！ 您的信息已经收到！",
                    "sCreateMy": "創建我的賬戶",
                    "errorTitle": "哎呀！ 支付失败",
                    "errorNote": "No. {OrderNum} 订单的付款无法进行。 再试一次。",
                    "error_additem_stock": "该产品已经断货了！",
                    "cancelTitle": "对不起！ 您的订单已被取消。",
                    "cancelNote": "您的订单号是 {OrderNum}",
                    "attribute_error": "有部分产品资料出错，请麻烦清除此产品再进行重新购买",
                    "address_error": "请先选择\"国家\/地区\"和'\"州\"",
                    "shipping_error": "请选择送货方式",
                    "payment_error": "请选择付款方式",
                    "product_error": "您的购物车是空的",
                    "low_error": "您的产品总额尚未达到网站的最低消费金额",
                    "stock_error": "有部分产品的库存不足，不能正常创建订单，请调整购买数量",
                    "no_delivery": "对不起，没有快递到您选择的国家\/地区",
                    "shippingCompleteError": "请选择送货方式",
                    "paymentMin": "当前选择的付款方式的最低消費金额是{{MinPrice}}，你现在还差{{Price}}。",
                    "paymentMax": "当前选择的付款方式的最高限制金额是{{MaxPrice}}，你现在超出{{Price}}。"
                },
                "points": {
                    "points": "积分",
                    "maximum": "您输入的数字超出了允许的最大积分数。",
                    "maxAva": "最大可用"
                }
            },
            "protocol": {
                "global": {
                    "cookies_agreement": "我们使用cookie来改善您的在线体验。 通过继续浏览本网站，我们假设您同意我们使用cookie。"
                }
            },
            "area": {
                "country": {
                    "AF": "阿富汗",
                    "AL": "阿尔巴尼亚",
                    "DZ": "阿尔及利亚",
                    "AS": "美属萨摩亚",
                    "AD": "安道尔",
                    "AO": "安哥拉",
                    "AI": "安圭拉",
                    "AG": "安提瓜和巴布达",
                    "AR": "阿根廷",
                    "AM": "亚美尼亚",
                    "AW": "阿鲁巴岛",
                    "AU": "澳大利亚",
                    "AT": "奥地利",
                    "AZ": "阿塞拜疆",
                    "BS": "巴哈马",
                    "BH": "巴林",
                    "BD": "孟加拉国",
                    "BB": "巴巴多斯",
                    "BY": "白俄罗斯",
                    "BE": "比利时",
                    "BZ": "伯利兹",
                    "BJ": "贝宁",
                    "BM": "百慕大",
                    "BT": "不丹",
                    "BO": "玻利维亚",
                    "BA": "波斯尼亚和黑塞哥维那",
                    "BW": "博茨瓦纳",
                    "BV": "布韦岛",
                    "BR": "巴西",
                    "IO": "英属印度洋领地",
                    "BN": "文莱达鲁萨兰国",
                    "BG": "保加利亚",
                    "BF": "布基纳法索",
                    "BI": "布隆迪",
                    "KH": "柬埔寨",
                    "CM": "喀麦隆",
                    "CA": "加拿大",
                    "CV": "佛得角",
                    "KY": "开曼群岛",
                    "CF": "中非共和国",
                    "TD": "乍得",
                    "CL": "智利",
                    "CN": "中国",
                    "CX": "圣诞岛",
                    "CC": "科科斯（基林）群岛",
                    "CO": "哥伦比亚",
                    "KM": "科摩罗",
                    "CG": "刚果",
                    "CK": "库克群岛",
                    "CR": "哥斯达黎加",
                    "CI": "科特迪瓦",
                    "HR": "克罗地亚",
                    "CU": "古巴",
                    "CY": "塞浦路斯",
                    "CZ": "捷克共和国",
                    "DK": "丹麦",
                    "DJ": "吉布提",
                    "DO": "多明尼加共和国",
                    "TL": "东帝汶",
                    "EC": "厄瓜多尔",
                    "EG": "埃及",
                    "SV": "萨尔瓦多",
                    "GQ": "赤道几内亚",
                    "ER": "厄立特里亚",
                    "EE": "爱沙尼亚",
                    "ET": "埃塞俄比亚",
                    "FK": "福克兰群岛（马尔维纳斯）",
                    "FO": "法罗群岛",
                    "FJ": "斐济",
                    "FI": "芬兰",
                    "FR": "法国",
                    "GF": "法属圭亚那",
                    "PF": "法属波利尼西亚",
                    "TF": "法属南部领地",
                    "GA": "加蓬",
                    "GM": "冈比亚",
                    "GE": "乔治亚州",
                    "DE": "德国",
                    "GH": "加纳",
                    "GI": "直布罗陀",
                    "GR": "希腊",
                    "GL": "格陵兰",
                    "GD": "格林纳达",
                    "GP": "法国，DOM-TOM Guadeloupe",
                    "GU": "美国关岛",
                    "GT": "危地马拉",
                    "GN": "几内亚",
                    "GW": "几内亚比绍",
                    "GY": "圭亚那",
                    "HT": "海地",
                    "HM": "赫德岛和麦当劳群岛",
                    "HN": "洪都拉斯",
                    "HK": "中国香港",
                    "HU": "匈牙利",
                    "IS": "冰岛",
                    "IN": "印度",
                    "ID": "印度尼西亚",
                    "IR": "伊朗（伊斯兰共和国）",
                    "IQ": "伊拉克",
                    "IE": "爱尔兰",
                    "IL": "以色列",
                    "IT": "意大利",
                    "JM": "牙买加",
                    "JP": "日本",
                    "JO": "约旦",
                    "KZ": "哈萨克斯坦",
                    "KE": "肯尼亚",
                    "KI": "基里巴斯",
                    "KR": "大韩民国",
                    "KP": "朝鲜民主主义人民共和国",
                    "KW": "科威特",
                    "KG": "吉尔吉斯斯坦",
                    "LA": "老挝人民民主共和国",
                    "LV": "拉脱维亚",
                    "LB": "黎巴嫩",
                    "LS": "莱索托",
                    "LR": "利比里亚",
                    "LY": "阿拉伯利比亚民众国",
                    "LI": "列支敦士登",
                    "LT": "立陶宛",
                    "LU": "卢森堡",
                    "MO": "中国澳门",
                    "MK": "马其顿，F.Y.R.O.M",
                    "MG": "马达加斯加",
                    "MW": "马拉维",
                    "MY": "马来西亚",
                    "MV": "马尔代夫",
                    "ML": "马里",
                    "MT": "马耳他",
                    "MH": "马绍尔群岛",
                    "MQ": "法国，DOM-TOM 马提尼克",
                    "MR": "毛里塔尼亚",
                    "MU": "毛里求斯",
                    "YT": "法国，DOM-TOM 马约特岛",
                    "MX": "墨西哥",
                    "FM": "密克罗尼西亚联邦",
                    "MD": "摩尔多瓦共和国",
                    "MC": "摩纳哥",
                    "MN": "蒙古",
                    "MS": "蒙特塞拉特",
                    "MA": "摩洛哥",
                    "MZ": "莫桑比克",
                    "MM": "缅甸",
                    "NA": "纳米比亚",
                    "NR": "瑙鲁",
                    "NP": "尼泊尔",
                    "NL": "荷兰",
                    "NC": "法国，DOM-TOM 新喀里多尼亚",
                    "NZ": "新西兰",
                    "NI": "尼加拉瓜",
                    "NE": "尼日尔",
                    "NG": "尼日利亚",
                    "NU": "纽埃",
                    "NF": "诺福克岛",
                    "MP": "北马里亚纳群岛",
                    "NO": "挪威",
                    "OM": "阿曼",
                    "PK": "巴基斯坦",
                    "PW": "帕劳",
                    "PS": "巴勒斯坦",
                    "PA": "巴拿马",
                    "PG": "巴布亚新几内亚",
                    "PY": "巴拉圭",
                    "PE": "秘鲁",
                    "PH": "菲律宾",
                    "PN": "皮特凯恩",
                    "PL": "波兰",
                    "PT": "葡萄牙",
                    "PR": "波多黎各",
                    "QA": "卡塔尔",
                    "RE": "法国，DOM-TOM 留尼汪",
                    "RO": "罗马尼亚",
                    "RU": "俄罗斯",
                    "RW": "卢旺达",
                    "SH": "圣赫勒拿",
                    "KN": "圣基茨和尼维斯",
                    "LC": "圣卢西亚",
                    "PM": "法国、圣皮埃尔和密克隆",
                    "SM": "圣马力诺",
                    "ST": "圣多美和普林西比",
                    "SA": "沙特阿拉伯",
                    "SN": "塞内加尔",
                    "RS": "塞尔维亚",
                    "SC": "塞舌尔",
                    "SL": "塞拉利昂",
                    "SG": "新加坡",
                    "SK": "斯洛伐克（斯洛伐克共和国）",
                    "SI": "斯洛文尼亚",
                    "SB": "所罗门群岛",
                    "SO": "索马里",
                    "ZA": "南非",
                    "GS": "南乔治亚岛和南桑威奇群岛",
                    "ES": "西班牙",
                    "LK": "斯里兰卡",
                    "SD": "苏丹",
                    "SR": "苏里南",
                    "SJ": "斯瓦尔巴群岛和扬马延群岛",
                    "SZ": "斯威士兰",
                    "SE": "瑞典",
                    "CH": "瑞士",
                    "SY": "阿拉伯叙利亚共和国",
                    "TW": "中国台湾",
                    "TJ": "塔吉克斯坦",
                    "TZ": "坦桑尼亚联合共和国",
                    "TH": "泰国",
                    "TG": "多哥",
                    "TK": "托克劳",
                    "TO": "汤加",
                    "TT": "特立尼达和多巴哥",
                    "TN": "突尼斯",
                    "TR": "火鸡",
                    "TM": "土库曼斯坦",
                    "TC": "特克斯和凯科斯群岛",
                    "TV": "图瓦卢",
                    "UG": "乌干达",
                    "UA": "乌克兰",
                    "AE": "阿拉伯联合酋长国",
                    "GB": "英国",
                    "UM": "美国本土外小岛屿",
                    "US": "美国",
                    "UY": "乌拉圭",
                    "VI": "维尔京群岛（美国）",
                    "UZ": "乌兹别克斯坦",
                    "VU": "瓦努阿图",
                    "VE": "委内瑞拉",
                    "VN": "越南",
                    "WF": "法国、DOM-TOM 瓦利斯和富图纳",
                    "EH": "西撒哈拉",
                    "WS": "萨摩亚",
                    "YE": "也门",
                    "VA": "梵蒂冈城邦（教廷）",
                    "ZM": "赞比亚",
                    "ZW": "津巴布韦",
                    "VC": "圣文森特和格林纳丁斯",
                    "CD": "刚果民主共和国",
                    "AX": "奥兰群岛",
                    "MF": "圣马丁",
                    "AN": "荷属安的列斯",
                    "BQ": "加勒比海荷兰",
                    "SX": "圣马丁岛",
                    "BL": "圣巴泰勒米",
                    "VG": "英属维尔京群岛",
                    "GG": "根西岛",
                    "ME": "黑山",
                    "XK": "科索沃",
                    "CW": "库拉索",
                    "IM": "马恩岛",
                    "AC": "阿森松岛",
                    "DM": "多米尼加",
                    "JE": "泽西岛",
                    "SS": "南苏丹",
                    "TA": "特里斯坦-达库尼亚"
                },
                "AR": {
                    "AUTONOMOUSCITYOFBUENOSAIRES": "布宜诺斯艾利斯（Ciudad），",
                    "BUENOSAIRES": "布宜诺斯艾利斯（省），",
                    "CATAMARCA": "卡塔马卡",
                    "CHACO": "查科",
                    "CHUBUT": "丘布特",
                    "CORRIENTES": "科连特斯",
                    "CORDOVA": "科尔多瓦",
                    "BETWEENRIVERS": "进入里奥斯",
                    "FORMOSA": "福尔摩沙",
                    "JUJUY": "胡胡伊",
                    "LAPAMPA": "拉潘帕",
                    "LARIOJA": "拉里奥哈",
                    "MENDOZA": "门多萨",
                    "MISIONES": "米西奥内斯",
                    "NEUQUEN": "内乌肯",
                    "RIOONEGRO": "里奥内格罗",
                    "SALTA": "萨尔塔",
                    "SANJUAN": "圣胡安",
                    "SANLUIS": "圣路易斯",
                    "SANTACRUZ": "圣诞老人",
                    "SANTAFE": "圣达菲",
                    "SANTIAGODELESTERO": "圣地亚哥德尔埃斯特罗",
                    "TIERRADELFUEGO": "火地岛",
                    "TUCUMAN": "图库曼"
                },
                "AU": {
                    "ACT": "澳大利亚首都直辖区",
                    "NSW": "新南威尔士",
                    "NT": "北方领土",
                    "QLD": "昆士兰",
                    "SA": "南澳大利亚",
                    "TAS": "塔斯马尼亚",
                    "VIC": "维多利亚",
                    "WA": "澳洲西部"
                },
                "AT": {
                    "Oberosterreich": "上帝国",
                    "Salzburg": "萨尔茨堡",
                    "Karnten": "卡恩滕",
                    "Steiermark": "施泰尔马克",
                    "Burgenland": "布尔根兰",
                    "Voralberg": "福拉尔贝格",
                    "Wien": "维也纳",
                    "Niedersterreich": "下斯特赖希",
                    "Tirol": "蒂罗尔州"
                },
                "BR": {
                    "AC": "英亩",
                    "AL": "阿拉戈斯",
                    "AP": "阿马帕",
                    "AM": "亚马逊",
                    "BA": "巴伊亚",
                    "CE": "塞阿拉",
                    "DF": "联邦区",
                    "ES": "圣埃斯皮里图",
                    "GO": "戈亚斯",
                    "MA": "马拉尼昂",
                    "MT": "马托格罗索州",
                    "MS": "南马托格罗索州",
                    "MG": "米纳斯吉拉斯州",
                    "PR": "巴拉那",
                    "PB": "帕拉伊巴",
                    "PA": "帕拉",
                    "PE": "伯南布哥",
                    "PI": "皮奥伊",
                    "RN": "北里奥格兰德",
                    "RS": "南里奥格兰德",
                    "RJ": "里约热内卢",
                    "RO": "朗多尼亚",
                    "RR": "罗赖马",
                    "SC": "圣卡塔琳娜",
                    "SE": "塞尔吉佩",
                    "SP": "圣保罗",
                    "TO": "托坎廷斯"
                },
                "CA": {
                    "AB": "艾伯塔省",
                    "BC": "不列颠哥伦比亚省",
                    "MB": "曼尼托巴省",
                    "NL": "纽芬兰和拉布拉多",
                    "NB": "新不伦瑞克",
                    "NS": "新斯科舍省",
                    "NT": "西北地区",
                    "NU": "努纳武特",
                    "ON": "安大略",
                    "PE": "爱德华王子岛",
                    "QC": "魁北克",
                    "SK": "萨斯喀彻温省",
                    "YT": "育空地区"
                },
                "CL": {
                    "AP": "阿里卡和帕里纳科塔",
                    "TA": "塔拉帕卡",
                    "AN": "安托法加斯塔",
                    "AT": "阿塔卡马",
                    "CO": "科金博",
                    "VS": "瓦尔帕莱索",
                    "RM": "圣地亚哥大都会",
                    "LI": "解放者将军贝尔纳多·奥希金斯",
                    "ML": "毛莱",
                    "NB": "Ñuble",
                    "BI": "比奥比奥",
                    "AR": "阿劳卡尼亚",
                    "LR": "洛斯里奥斯",
                    "LL": "洛斯拉各斯",
                    "AI": "艾森",
                    "MA": "麦哲伦地区"
                },
                "CN": {
                    "GD": "粤",
                    "SH": "上海",
                    "SZ": "深圳",
                    "BJ": "北京",
                    "YN": "云南",
                    "NM": "内蒙古",
                    "QH": "青海",
                    "JL": "吉林",
                    "SC": "四川",
                    "TJ": "天津",
                    "NX": "宁夏",
                    "AH": "安徽",
                    "SD": "山东",
                    "SX": "山西",
                    "HL": "黑龙江",
                    "GX": "广西",
                    "XJ": "新疆",
                    "JS": "江苏",
                    "JX": "江西",
                    "HE": "河北",
                    "HA": "河南",
                    "ZJ": "浙江",
                    "HI": "海南",
                    "HB": "湖北",
                    "HN": "湖南",
                    "GS": "甘肃",
                    "FJ": "福建",
                    "YZ": "西藏",
                    "GZ": "贵州",
                    "LN": "辽宁",
                    "CQ": "重庆",
                    "SN": "陕西"
                },
                "CO": {
                    "DC": "首都区",
                    "AMA": "亚马逊",
                    "ANT": "安蒂奥基亚",
                    "ARA": "阿劳卡",
                    "ATL": "大西洋",
                    "BOL": "玻利瓦尔",
                    "BOY": "博亚卡",
                    "CAL": "卡尔达斯",
                    "CAQ": "卡克塔",
                    "CAS": "卡萨纳雷",
                    "CAU": "考卡",
                    "CES": "塞萨尔",
                    "CHO": "乔科",
                    "CUN": "科尔多瓦",
                    "GUA": "昆迪纳马卡",
                    "GUV": "瓜维亚雷",
                    "HUI": "威拉",
                    "LAG": "拉瓜希拉",
                    "MAG": "马格达莱纳",
                    "MET": "元",
                    "NAR": "纳里尼奥",
                    "NSA": "北桑坦德",
                    "PUT": "普图马约",
                    "QUI": "金迪奥",
                    "RIS": "里萨拉尔达",
                    "SAP": "圣安德烈斯和普罗维登西亚",
                    "SAN": "桑坦德",
                    "SUC": "苏克雷",
                    "TOL": "托利马",
                    "VAC": "考卡山谷",
                    "VAU": "沃佩斯",
                    "VID": "比查达"
                },
                "EG": {
                    "SHR": "沙迦",
                    "ALX": "亚历山大",
                    "ASN": "阿斯旺",
                    "AST": "阿斯尤特",
                    "BH": "贝海拉",
                    "BNS": "贝尼苏夫",
                    "C": "开罗",
                    "DK": "达卡利亚",
                    "DT": "达米埃塔",
                    "FYM": "飞宇",
                    "GH": "加尔比亚",
                    "GZ": "吉萨",
                    "HU": "赫尔万",
                    "IS": "伊斯梅利亚",
                    "KFS": "卡夫尔谢赫",
                    "LX": "卢克索",
                    "MT": "马特鲁",
                    "MN": "明雅",
                    "MNF": "莫努菲亚",
                    "WAD": "新谷",
                    "SIN": "北西奈",
                    "PTS": "塞得港",
                    "KB": "卡柳比亚",
                    "KN": "凯娜",
                    "BA": "红海",
                    "SHG": "索哈格",
                    "JS": "南西奈",
                    "SUZ": "苏伊士"
                },
                "DE": {
                    "Baden-Wrttemberg": "巴登-符腾堡州",
                    "Bayern": "拜仁",
                    "Berlin": "柏林",
                    "Brandenburg": "勃兰登堡",
                    "Bremen": "不来梅",
                    "Hamburg": "汉堡",
                    "Hessen": "黑森州",
                    "Mecklenburg-Vorpommern": "梅克伦堡-前波美拉尼亚州",
                    "Niedersachsen": "下萨克森州",
                    "Nordrhein-Westfalen": "北莱茵-威斯特法伦州",
                    "Rheinland-Pfalz": "莱茵兰-普法尔茨州",
                    "Saarland": "萨尔州",
                    "Sachsen": "萨克森",
                    "Sachsen-Anhalt": "萨克森-安哈特",
                    "Schleswig-Holstein": "石勒苏益格-荷尔斯泰因",
                    "Thuringen": "图林根"
                },
                "GT": {
                    "AVE": "上韦拉帕斯",
                    "BVE": "维拉帕斯钢",
                    "CMT": "奇马尔特南戈",
                    "CQM": "奇基穆拉",
                    "EPR": "进步报",
                    "ESC": "埃斯昆特拉",
                    "GUA": "危地马拉",
                    "HUE": "韦韦特南戈",
                    "IZA": "伊萨巴尔",
                    "JAL": "哈拉帕",
                    "JUT": "百万",
                    "PET": "佩滕",
                    "QUE": "克萨尔特南戈",
                    "QUI": "乳蛋饼",
                    "RET": "雷塔胡勒",
                    "SAC": "萨卡特佩克斯",
                    "SMA": "圣马科斯",
                    "SRO": "圣罗莎",
                    "SOL": "索罗拉",
                    "SUC": "苏奇特佩克斯",
                    "TOT": "托托尼卡潘",
                    "ZAC": "萨卡帕"
                },
                "IN": {
                    "ANDAMANANDNICOBARISLANDS": "安达曼和尼科巴群岛",
                    "ANDHRAPRADESH": "安德拉邦",
                    "APO": "陆军邮局",
                    "ARUNACHALPRADESH": "阿鲁纳恰尔邦",
                    "ASSAM": "阿萨姆邦",
                    "BIHAR": "比哈尔邦",
                    "CHANDIGARH": "昌迪加尔",
                    "CHHATTISGARH": "恰蒂斯加尔邦",
                    "DADRAANDNAGARHAVELI": "达德拉和纳加尔哈维利",
                    "DAMANANDDIU": "达曼和迪乌",
                    "DELHI": "德里",
                    "GOA": "洞穴",
                    "GUJARAT": "古吉拉特邦",
                    "HARYANA": "哈里亚纳邦",
                    "HIMACHALPRADESH": "喜马偕尔邦",
                    "JAMMUANDKASHMIR": "查谟和克什米尔",
                    "JHARKHAND": "贾坎德邦",
                    "KARNATAKA": "卡纳塔克邦",
                    "KERALA": "喀拉拉邦",
                    "LAKSHADWEEP": "拉克沙威",
                    "MADHYAPRADESH": "中央邦",
                    "MAHARASHTRA": "马哈拉施特拉邦",
                    "MANIPUR": "曼尼普尔邦",
                    "MEGHALAYA": "梅加拉亚邦",
                    "MIZORAM": "米佐拉姆语",
                    "NAGALAND": "那加兰邦",
                    "ODISHA": "奥里萨邦",
                    "PUDUCHERRY": "本地治里",
                    "PUNJAB": "旁遮普",
                    "RAJASTHAN": "拉贾斯坦邦",
                    "SIKKIM": "锡金",
                    "TAMILNADU": "泰米尔纳德邦",
                    "TELANGANA": "特兰加纳",
                    "TRIPURA": "特里普拉",
                    "UTTARPRADESH": "北方邦",
                    "UTTARAKHAND": "北阿坎德邦",
                    "WESTBENGAL": "西孟加拉邦"
                },
                "ID": {
                    "ID-BA": "巴厘岛",
                    "ID-BB": "邦加勿里洞",
                    "ID-BT": "万丹",
                    "ID-BE": "班加罗尔",
                    "ID-YO": "在日惹",
                    "ID-JK": "DKI雅加达",
                    "ID-GO": "戈龙塔洛",
                    "ID-JA": "占碑",
                    "ID-JB": "西爪哇",
                    "ID-JT": "中爪哇",
                    "ID-JI": "东爪哇",
                    "ID-KB": "西加里曼丹",
                    "ID-KS": "南婆罗洲",
                    "ID-KT": "中加里曼丹",
                    "ID-KI": "东加里曼丹",
                    "ID-KU": "北加里曼丹",
                    "ID-KR": "廖内群岛",
                    "ID-LA": "楠榜",
                    "ID-MA": "马鲁古",
                    "ID-MU": "北马鲁古",
                    "ID-AC": "南格罗亚齐达鲁萨兰国",
                    "ID-NB": "西努沙登加拉",
                    "ID-NT": "东努沙登加拉",
                    "ID-PA": "巴布亚",
                    "ID-PB": "西巴布亚",
                    "ID-RI": "廖内",
                    "ID-SR": "西苏拉威西",
                    "ID-SN": "南苏拉威西",
                    "ID-ST": "中苏拉威西",
                    "ID-SG": "东南苏拉威西",
                    "ID-SA": "北苏拉威西",
                    "ID-SB": "西苏门答腊",
                    "ID-SS": "南苏门答腊",
                    "ID-SU": "北苏门答腊"
                },
                "IE": {
                    "CW": "卡罗",
                    "CN": "卡文",
                    "CE": "克莱尔",
                    "CO": "软木",
                    "DL": "多尼戈尔",
                    "D": "都柏林",
                    "G": "戈尔韦",
                    "KY": "凯瑞",
                    "KE": "基尔代尔",
                    "KK": "基尔肯尼",
                    "LS": "老挝",
                    "LM": "利特里姆",
                    "LK": "利默里克",
                    "LD": "朗福德",
                    "LH": "劳斯",
                    "MO": "梅奥",
                    "MH": "米斯",
                    "MN": "莫纳汉",
                    "OY": "奥法利",
                    "RN": "罗斯康芒",
                    "SO": "斯莱戈",
                    "TA": "蒂珀雷里",
                    "WD": "沃特福德",
                    "WH": "韦斯特米斯",
                    "WX": "韦克斯福德",
                    "WW": "威克洛"
                },
                "IT": {
                    "AG": "阿格里真托",
                    "AL": "亚历山德里亚",
                    "AN": "安科纳",
                    "AO": "奥斯塔",
                    "AR": "阿雷佐",
                    "AP": "阿斯科利皮切诺",
                    "AT": "阿斯蒂",
                    "AV": "阿韦利诺",
                    "BA": "巴里",
                    "BT": "巴列塔-安德里亚-特拉尼",
                    "BL": "贝卢诺",
                    "BN": "贝内文托",
                    "BG": "贝加莫",
                    "BI": "比耶拉",
                    "BO": "博洛尼亚",
                    "BZ": "博尔扎诺",
                    "BS": "布雷西亚",
                    "BR": "布林迪西",
                    "CA": "卡利亚里",
                    "CL": "卡尔塔尼塞塔",
                    "CB": "坎波巴索",
                    "CI": "卡波尼亚-伊格莱西亚斯",
                    "CE": "卡塞塔",
                    "CT": "卡塔尼亚",
                    "CZ": "卡坦扎罗",
                    "CH": "基耶蒂",
                    "CO": "科莫",
                    "CS": "科森扎",
                    "CR": "克雷莫纳",
                    "KR": "巴豆",
                    "CN": "库内奥",
                    "EN": "恩纳",
                    "FM": "费尔莫",
                    "FE": "费拉拉",
                    "FI": "佛罗伦萨",
                    "FG": "福贾",
                    "FC": "弗利-切塞纳",
                    "FR": "弗罗西诺内",
                    "GE": "热那亚",
                    "GO": "戈里齐亚",
                    "GR": "格罗塞托",
                    "IM": "帝国",
                    "IS": "伊塞尔尼亚",
                    "AQ": "拉奎拉",
                    "SP": "拉斯佩齐亚",
                    "LT": "拉丁裔",
                    "LE": "莱切",
                    "LC": "莱科",
                    "LI": "利沃诺",
                    "LO": "洛迪",
                    "LU": "卢卡",
                    "MC": "马切拉塔",
                    "MN": "曼托娃",
                    "MS": "马萨-卡拉拉",
                    "MT": "马泰拉",
                    "VS": "中场坎皮达诺",
                    "ME": "墨西拿",
                    "MI": "米兰",
                    "MO": "摩德纳",
                    "MB": "蒙扎和德拉布里安扎",
                    "NA": "那不勒斯",
                    "NO": "诺瓦拉",
                    "NU": "诺罗",
                    "OG": "奥利亚斯特拉",
                    "OT": "奥尔比亚-坦皮奥",
                    "OR": "奥里斯塔诺",
                    "PD": "帕多瓦",
                    "PA": "巴勒莫",
                    "PR": "帕尔马",
                    "PV": "帕维亚",
                    "PG": "佩鲁贾",
                    "PU": "佩萨罗和乌尔比诺",
                    "PE": "佩斯卡拉",
                    "PC": "皮亚琴察",
                    "PI": "比萨",
                    "PT": "皮斯托亚",
                    "PN": "地酮",
                    "PO": "普拉托",
                    "RG": "拉古萨",
                    "RA": "拉文纳",
                    "RC": "雷焦卡拉布里亚",
                    "RE": "雷焦艾米利亚",
                    "RI": "列蒂",
                    "RN": "里米尼",
                    "RM": "罗马",
                    "RO": "罗维戈",
                    "SA": "萨莱诺",
                    "SS": "萨萨里",
                    "SV": "萨沃纳",
                    "SI": "锡耶纳",
                    "SR": "雪城",
                    "SO": "松德里奥",
                    "TA": "塔兰托",
                    "TE": "泰拉莫",
                    "TR": "特尼",
                    "TO": "都灵",
                    "TP": "特拉帕尼",
                    "TN": "特伦托",
                    "TV": "特雷维索",
                    "TS": "的里雅斯特",
                    "UD": "乌迪内",
                    "VA": "瓦雷泽",
                    "VE": "威尼斯",
                    "VB": "韦尔巴诺-库西奥-奥索拉",
                    "VC": "韦尔切利",
                    "VR": "维罗纳",
                    "VV": "维博瓦伦蒂亚",
                    "VI": "维琴察",
                    "VT": "维泰博",
                    "PZ": "波坦察"
                },
                "JP": {
                    "AICHI-KEN": "爱知",
                    "AKITA-KEN": "秋田",
                    "AOMORI-KEN": "青森",
                    "CHIBA-KEN": "千叶",
                    "EHIME-KEN": "爱媛",
                    "FUKUI-KEN": "福井",
                    "FUKUOKA-KEN": "福冈",
                    "FUKUSHIMA-KEN": "福岛",
                    "GIFU-KEN": "岐阜",
                    "GUNMA-KEN": "群马",
                    "HIROSHIMA-KEN": "广岛",
                    "HOKKAIDO": "北海道",
                    "HYOGO-KEN": "兵库",
                    "IBARAKI-KEN": "茨城",
                    "ISHIKAWA-KEN": "石川",
                    "IWATE-KEN": "岩手",
                    "KAGAWA-KEN": "香川",
                    "KAGOSHIMA-KEN": "鹿儿岛",
                    "KANAGAWA-KEN": "神奈川",
                    "KOCHI-KEN": "高知",
                    "KUMAMOTO-KEN": "熊本",
                    "KYOTO-FU": "京都",
                    "MIE-KEN": "三重",
                    "MIYAGI-KEN": "宫城",
                    "MIYAZAKI-KEN": "宫崎",
                    "NAGANO-KEN": "长野",
                    "NAGASAKI-KEN": "长崎",
                    "NARA-KEN": "奈良",
                    "NIIGATA-KEN": "新泻",
                    "OITA-KEN": "大分",
                    "OKAYAMA-KEN": "冈山",
                    "OKINAWA-KEN": "冲绳",
                    "OSAKA-FU": "大阪",
                    "SAGA-KEN": "佐贺",
                    "SAITAMA-KEN": "埼玉",
                    "SHIGA-KEN": "滋贺",
                    "SHIMANE-KEN": "岛根",
                    "SHIZUOKA-KEN": "静冈",
                    "TOCHIGI-KEN": "枥木",
                    "TOKUSHIMA-KEN": "德岛",
                    "TOKYO-TO": "东京",
                    "TOTTORI-KEN": "鸟取",
                    "TOYAMA-KEN": "富山",
                    "WAKAYAMA-KEN": "和歌山",
                    "YAMAGATA-KEN": "山形",
                    "YAMAGUCHI-KEN": "山口",
                    "YAMANASHI-KEN": "山梨"
                },
                "KR": {
                    "Busan": "釜山",
                    "NorthChungcheong": "忠清北",
                    "SouthChungcheong": "忠清南区",
                    "Daegu": "大邱",
                    "Daejeon": "大田",
                    "Gangwon": "江原道",
                    "GwangjuCity": "光州市",
                    "NorthGyeongsang": "庆尚北道",
                    "Gyeonggi": "京畿道",
                    "SouthGyeongsang": "庆尚南道",
                    "Incheon": "仁川",
                    "Jeju": "济州",
                    "NorthJeolla": "全罗北",
                    "SouthJeolla": "全罗南道",
                    "Sejong": "世宗",
                    "Seoul": "汉城",
                    "Ulsan": "蔚山"
                },
                "MY": {
                    "JHR": "柔佛",
                    "KDH": "吉打",
                    "KTN": "吉兰丹",
                    "KUL": "吉隆坡",
                    "LBN": "纳闽",
                    "MLK": "马六甲",
                    "NSN": "森美兰州",
                    "PHG": "彭亨",
                    "PNG": "槟城",
                    "PRK": "霹雳州",
                    "PLS": "玻璃市",
                    "PJY": "布城",
                    "SBH": "沙巴",
                    "SWK": "砂拉越",
                    "SGR": "雪兰莪",
                    "TRG": "登嘉楼"
                },
                "MX": {
                    "AGS": "阿瓜斯卡连特斯",
                    "BC": "下加利福尼亚州",
                    "BCS": "南下加利福尼亚州",
                    "CAMP": "坎佩切州",
                    "CHIS": "恰帕斯州",
                    "CDMX": "墨西哥城",
                    "COAH": "科阿韦拉",
                    "COL": "科利马",
                    "DF": "联邦区",
                    "DGO": "杜兰戈",
                    "MEX": "墨西哥州",
                    "GTO": "瓜纳华托",
                    "GRO": "战士",
                    "HGO": "绅士",
                    "JAL": "哈利斯科州",
                    "MICH": "米却肯",
                    "MOR": "莫雷洛斯",
                    "NAY": "纳亚里特语",
                    "NL": "新狮",
                    "OAX": "瓦哈卡",
                    "PUE": "普埃布拉",
                    "QRO": "克雷塔罗",
                    "QROO": "金塔纳罗奥州",
                    "SLP": "圣路易斯波托西",
                    "SIN": "锡那罗亚",
                    "SON": "索诺拉",
                    "TAB": "塔巴斯科",
                    "TAMPS": "塔毛利帕斯州",
                    "TLAX": "特拉斯卡拉",
                    "VER": "韦拉克鲁斯",
                    "YUC": "尤卡坦",
                    "ZAC": "萨卡特卡斯",
                    "CHIH": "吉娃娃"
                },
                "NZ": {
                    "AUK": "奥克兰",
                    "BOP": "丰盛湾",
                    "CAN": "坎特伯雷",
                    "GIS": "吉斯伯恩",
                    "HKB": "霍克斯湾",
                    "MWT": "马纳瓦图-旺格努伊",
                    "MBH": "马尔堡",
                    "NSN": "纳尔逊",
                    "NTL": "北国",
                    "OTA": "奥塔哥",
                    "STL": "南国",
                    "TKI": "塔拉纳基",
                    "TAS": "塔斯曼",
                    "WKO": "怀卡托",
                    "WGN": "惠灵顿",
                    "WTC": "西海岸"
                },
                "NG": {
                    "AB": "阿比亚",
                    "FC": "联邦首都地区",
                    "AD": "阿达玛瓦",
                    "AK": "阿夸伊博姆",
                    "AN": "阿南布拉",
                    "BA": "包奇",
                    "BY": "巴耶尔萨",
                    "BE": "贝努埃",
                    "BO": "博尔诺",
                    "CR": "过河",
                    "DE": "三角洲",
                    "EB": "埃博尼",
                    "ED": "江户",
                    "EK": "埃基蒂",
                    "EN": "埃努古",
                    "GO": "贡贝",
                    "IM": "伊莫",
                    "JI": "地川",
                    "KD": "卡杜纳",
                    "KN": "卡诺",
                    "KT": "卡齐娜",
                    "KE": "凯比",
                    "KO": "小木",
                    "KW": "夸拉",
                    "LA": "湖泊",
                    "NA": "纳萨拉瓦",
                    "NI": "尼日尔",
                    "OG": "奥贡",
                    "ON": "翁多",
                    "OS": "欧尚",
                    "OY": "或者我",
                    "PL": "高原",
                    "RI": "河流",
                    "SO": "索科托",
                    "TA": "塔拉巴",
                    "YO": "约贝",
                    "ZA": "赞法拉"
                },
                "PA": {
                    "BocasdelToro": "公牛的嘴",
                    "Chiriqui": "奇里基",
                    "Cocle": "科克莱",
                    "Colon": "冒号",
                    "Darien": "达连",
                    "Embera": "恩贝拉",
                    "Herrera": "埃雷拉",
                    "GunaYala": "古娜亚拉",
                    "LosSantos": "圣徒",
                    "Ngöbe-Buglé": "恩戈贝-布格莱",
                    "Panama": "巴拿马",
                    "WestPanama": "西巴拿马",
                    "Veraguas": "贝拉瓜斯"
                },
                "PE": {
                    "AMA": "亚马逊",
                    "ANC": "无现金",
                    "APU": "阿普里马克",
                    "ARE": "阿雷基帕",
                    "AYA": "阿亚库乔",
                    "CAJ": "卡哈马卡",
                    "CAL": "卡亚俄",
                    "CUS": "库斯科",
                    "HUV": "万卡韦利察",
                    "HUC": "瓦努科",
                    "ICA": "伊卡",
                    "JUN": "胡宁",
                    "LAL": "自由",
                    "LAM": "兰巴耶克",
                    "LIM": "利马地区",
                    "LMA": "酸橙",
                    "LOR": "洛雷托",
                    "MDD": "圣母玛利亚",
                    "MOQ": "莫克瓜",
                    "PAS": "帕斯科",
                    "PIU": "皮乌拉",
                    "PUN": "拳头",
                    "SAM": "圣马丁",
                    "TAC": "塔克纳",
                    "TUM": "通贝斯",
                    "UCA": "乌卡亚利"
                },
                "PH": {
                    "PH-ABR": "打开",
                    "PH-AGN": "北阿古桑",
                    "PH-AGS": "南阿古桑",
                    "PH-AKL": "阿克兰",
                    "PH-ALB": "阿尔拜",
                    "PH-ANT": "古董",
                    "PH-APA": "阿帕尧",
                    "PH-AUR": "黎明",
                    "PH-BAS": "巴西兰",
                    "PH-BAN": "巴丹",
                    "PH-BTN": "丰满",
                    "PH-BTG": "八打雁",
                    "PH-BEN": "本格",
                    "PH-BIL": "比利兰",
                    "PH-BOH": "薄荷岛",
                    "PH-BUK": "布基农",
                    "PH-BUL": "布拉干",
                    "PH-CAG": "卡加延",
                    "PH-CAN": "北甘马缨",
                    "PH-CAS": "南甘马缨",
                    "PH-CAM": "甘米银",
                    "PH-CAP": "卡皮斯",
                    "PH-CAT": "卡坦端内斯",
                    "PH-CAV": "甲美地",
                    "PH-CEB": "瘤牛",
                    "PH-NCO": "哥打巴托",
                    "PH-DVO": "达沃西部",
                    "PH-DAO": "达沃东方",
                    "PH-COM": "孔波斯特拉山谷",
                    "PH-DAV": "北达沃",
                    "PH-DAS": "南达沃",
                    "PH-DIN": "迪纳加特群岛",
                    "PH-EAS": "东萨马",
                    "PH-GUI": "吉马拉斯",
                    "PH-IFU": "伊富高",
                    "PH-ILN": "北伊罗戈",
                    "PH-ILS": "伊罗戈南部",
                    "PH-ILI": "伊洛伊洛",
                    "PH-ISA": "伊莎贝拉",
                    "PH-KAL": "卡林加",
                    "PH-LUN": "工会",
                    "PH-LAG": "泻湖",
                    "PH-LAN": "北拉瑙",
                    "PH-LAS": "南拉瑙",
                    "PH-LEY": "莱特",
                    "PH-MAG": "马京达瑙",
                    "PH-MAD": "马林杜克",
                    "PH-MAS": "马斯巴特",
                    "PH-00": "马尼拉大都会",
                    "PH-MSC": "西米萨米斯",
                    "PH-MSR": "Misamis 东方",
                    "PH-MOU": "山",
                    "PH-NEC": "西内格罗斯",
                    "PH-NER": "内格罗斯东方",
                    "PH-NSA": "北萨马",
                    "PH-NUE": "新怡诗夏",
                    "PH-NUV": "新比斯开",
                    "PH-MDC": "西方民都洛",
                    "PH-MDR": "东方民都洛",
                    "PH-PLW": "巴拉望",
                    "PH-PAM": "邦板牙",
                    "PH-PAN": "邦阿西南",
                    "PH-QUE": "奎松",
                    "PH-QUI": "奎里诺",
                    "PH-RIZ": "黎刹",
                    "PH-ROM": "朗布隆",
                    "PH-WSA": "萨马",
                    "PH-SAR": "萨兰加尼",
                    "PH-SIG": "锡基霍尔",
                    "PH-SOR": "索索贡",
                    "PH-SCO": "南哥打巴托",
                    "PH-SLE": "南莱特",
                    "PH-SUK": "苏丹库达拉特",
                    "PH-SLU": "苏禄",
                    "PH-SUN": "北苏里高",
                    "PH-SUR": "苏里高",
                    "PH-TAR": "塔拉克",
                    "PH-TAW": "塔威塔威",
                    "PH-ZMB": "三描礼士",
                    "PH-ZSI": "三宝颜锡布盖",
                    "PH-ZAN": "北三宝颜",
                    "PH-ZAS": "南三宝颜"
                },
                "PT": {
                    "Azores": "亚速尔群岛",
                    "Aveiro": "阿威罗",
                    "Beja": "贝雅",
                    "Braga": "布拉加",
                    "Braganza": "布拉干萨",
                    "CasteloBranco": "布兰科堡",
                    "Coimbra": "科英布拉",
                    "Evora": "埃武拉",
                    "Faro": "灯塔",
                    "Guarda": "警卫",
                    "Leiria": "莱里亚",
                    "Lisbon": "里斯本",
                    "Madeira": "马德拉",
                    "Portalegre": "波塔莱格雷",
                    "Porto": "波尔图",
                    "Santarem": "圣塔伦",
                    "Setubal": "塞图巴尔",
                    "VianadoCastelo": "维亚纳堡",
                    "VilaReal": "维拉雷亚尔",
                    "Viseu": "维塞乌"
                },
                "RO": {
                    "AB": "日出",
                    "AR": "阿拉德",
                    "AG": "阿格什",
                    "BC": "巴丘",
                    "BH": "比霍尔",
                    "BN": "Bistriţa-Năsăud",
                    "BT": "博托萨尼",
                    "BR": "布瑞拉",
                    "BV": "布拉索夫",
                    "B": "布加勒斯特",
                    "BZ": "布祖乌",
                    "CS": "卡拉斯-塞维林",
                    "CJ": "克鲁日",
                    "CT": "不变",
                    "CV": "科瓦斯纳",
                    "CL": "卡拉拉西",
                    "DJ": "多利",
                    "DB": "丹博维塔",
                    "GL": "加拉太",
                    "GR": "久尔久",
                    "GJ": "戈尔吉",
                    "HR": "哈吉塔",
                    "HD": "胡内多阿拉",
                    "IL": "伊洛米沙",
                    "IS": "IASI",
                    "IF": "伊尔福夫",
                    "MM": "马拉穆列斯",
                    "MH": "MEHEDINŢI",
                    "MS": "穆雷斯",
                    "NT": "德语",
                    "OT": "奥尔特",
                    "PH": "普拉霍娃",
                    "SJ": "萨拉伊",
                    "SM": "萨图·马雷",
                    "SB": "锡比乌",
                    "SV": "苏恰瓦",
                    "TR": "电工",
                    "TM": "蒂米斯",
                    "TL": "图尔恰",
                    "VL": "戴尔",
                    "VS": "排",
                    "VN": "弗兰察"
                },
                "RU": {
                    "ALT": "阿尔泰边疆区",
                    "AL": "阿尔泰",
                    "AMU": "阿穆尔",
                    "ARK": "阿尔汉格尔斯克",
                    "AST": "阿斯特拉罕",
                    "BEL": "别尔哥罗德",
                    "BRY": "布良斯克",
                    "CE": "车臣",
                    "CHE": "车里雅宾斯克",
                    "CHU": "楚科奇自治区",
                    "CU": "楚瓦什",
                    "IRK": "伊尔库茨克",
                    "IVA": "伊万诺沃",
                    "YEV": "犹太人",
                    "KB": "卡巴尔达-巴尔卡尔",
                    "KGD": "加里宁格勒",
                    "KLU": "卡卢加",
                    "KAM": "堪察加边疆区",
                    "KC": "卡拉恰伊-切尔克斯",
                    "KEM": "克麦罗沃",
                    "KHA": "哈巴罗夫斯克边疆区",
                    "KHM": "汉特-曼西",
                    "KIR": "基洛夫",
                    "KO": "科米",
                    "KOS": "科斯特罗马",
                    "KDA": "克拉斯诺达尔边疆区",
                    "KYA": "克拉斯诺亚尔斯克边疆区",
                    "KGN": "库尔干",
                    "KRS": "库尔斯克",
                    "LEN": "列宁格勒",
                    "LIP": "利佩茨克",
                    "MAG": "马加丹",
                    "ME": "玛丽艾尔",
                    "MOW": "莫斯科",
                    "MOS": "莫斯科省",
                    "MUR": "摩尔曼斯克",
                    "NIZ": "下诺夫哥罗德",
                    "NGR": "诺夫哥罗德",
                    "NVS": "新西伯利亚",
                    "OMS": "鄂木斯克",
                    "ORE": "奥伦堡",
                    "ORL": "奥廖尔",
                    "PNZ": "奔萨",
                    "PER": "彼尔姆边疆区",
                    "PRI": "滨海边疆区",
                    "PSK": "普斯科夫",
                    "AD": "阿迪格",
                    "BA": "巴什科尔托斯坦",
                    "BU": "布里亚特",
                    "DA": "达吉斯坦",
                    "IN": "印古什",
                    "KL": "卡尔梅克",
                    "KR": "卡累利阿",
                    "KK": "哈卡斯语",
                    "MO": "莫尔多维亚",
                    "SE": "北奥塞梯-阿兰",
                    "TA": "鞑靼斯坦",
                    "ROS": "罗斯托夫",
                    "RYA": "梁赞",
                    "SPE": "圣彼得堡",
                    "SA": "萨哈",
                    "SAK": "库页岛",
                    "SAM": "马鞍",
                    "SAR": "萨拉托夫",
                    "SMO": "斯摩棱斯克",
                    "STA": "斯塔夫罗波尔边疆区",
                    "SVE": "斯维尔德洛夫斯克",
                    "TAM": "坦波夫",
                    "TOM": "托木斯克",
                    "TUL": "图拉",
                    "TVE": "特维尔",
                    "TYU": "秋明",
                    "TY": "图瓦",
                    "UD": "乌德穆尔特",
                    "ULY": "乌里扬诺夫斯克",
                    "VLA": "弗拉基米尔",
                    "VGG": "伏尔加格勒",
                    "VLG": "沃洛格达",
                    "VOR": "沃罗涅日",
                    "YAN": "亚马洛-涅涅茨州",
                    "YAR": "雅罗斯拉夫尔",
                    "ZAB": "后贝加尔边疆区"
                },
                "ZA": {
                    "EC": "东开普省",
                    "FS": "自由邦省",
                    "GT": "豪登省",
                    "NL": "夸祖鲁-纳塔尔省",
                    "LP": "林波波",
                    "MP": "普马兰加省",
                    "NW": "西北",
                    "NC": "北开普省",
                    "WC": "西开普省"
                },
                "ES": {
                    "Teruel": "特鲁埃尔",
                    "Toledo": "托莱多",
                    "Valencia": "瓦伦西亚",
                    "Valladolid": "巴利亚多利德",
                    "Vizcaya": "比斯开",
                    "Zamora": "萨莫拉",
                    "Zaragoza": "萨拉戈萨",
                    "ACorua": "科鲁阿",
                    "Alava": "阿拉瓦",
                    "Albacete": "阿尔巴塞特",
                    "Alicante": "阿利坎特",
                    "Almeria": "阿尔梅里亚",
                    "Asturias": "阿斯图里亚斯",
                    "Avila": "阿维拉",
                    "Badajoz": "巴达霍斯",
                    "Baleares": "巴利阿里",
                    "Barcelona": "巴塞罗那",
                    "Burgos": "伯格",
                    "Caceres": "卡塞雷斯",
                    "Cadiz": "加的斯",
                    "Cantabria": "坎塔布里亚",
                    "Castellon": "卡斯特利翁",
                    "Ceuta": "休达",
                    "CiudadReal": "皇家城",
                    "Cordoba": "科尔多瓦",
                    "Cuenca": "昆卡",
                    "Girona": "赫罗纳",
                    "Granada": "格拉纳达",
                    "Guadalajara": "瓜达拉哈拉",
                    "Guipuzcoa": "吉普斯夸",
                    "Huelva": "韦尔瓦",
                    "Huesca": "韦斯卡",
                    "Jaen": "哈恩",
                    "LaRioja": "拉里奥哈",
                    "LasPalmas": "拉斯帕尔马斯",
                    "Leon": "利昂",
                    "Lleida": "莱里达",
                    "Lugo": "卢戈",
                    "Madrid": "马德里",
                    "Malaga": "马拉加",
                    "Melilla": "梅利利亚",
                    "Murcia": "穆尔西亚",
                    "Navarra": "纳瓦拉",
                    "Ourense": "欧伦塞",
                    "Palencia": "帕伦西亚",
                    "Pontevedra": "庞特维德拉",
                    "Salamanca": "萨拉曼卡",
                    "SantaCruzdeTenerife": "圣克鲁斯-德特内里费",
                    "Segovia": "塞戈维亚",
                    "Sevilla": "塞维利亚",
                    "Soria": "索里亚",
                    "Tarragona": "塔拉戈纳",
                    "Andalusia": "安达卢西亚",
                    "Aragon": "阿拉贡",
                    "BalearicIslands": "巴利阿里群岛",
                    "Biscay": "比斯开",
                    "Canarias": "加那利群岛",
                    "CastillaLaMancha": "卡斯蒂利亚拉曼恰",
                    "CastillayLeon": "卡斯蒂利亚和莱昂",
                    "Cataluna": "加泰罗尼亚",
                    "ComunidadForaldeNavarra": "纳瓦拉社区",
                    "ComunidadValenciana": "瓦伦西亚社区",
                    "Extremadura": "埃斯特雷马杜拉",
                    "Galicia": "加利西亚",
                    "IllesBalears": "巴利阿里群岛"
                },
                "CH": {
                    "Aargau": "阿尔高",
                    "AppenzellInnerrhoden": "阿彭策尔内罗登",
                    "AppenzellAusserrhoden": "阿彭策尔州",
                    "Bern": "伯尔尼",
                    "Basel-Landschaft": "巴塞尔土地局",
                    "Basel-Stadt": "巴塞尔市",
                    "Freiburg": "弗莱堡",
                    "Genf": "根夫",
                    "Glarus": "格拉鲁斯",
                    "Graubnden": "格劳宾登",
                    "Jura": "发誓",
                    "Luzern": "卢塞恩",
                    "Neuenburg": "诺因堡",
                    "Nidwalden": "下瓦尔登湖",
                    "Obwalden": "上瓦尔登省",
                    "StGallen": "英石。圣加仑",
                    "Schaffhausen": "沙夫豪森",
                    "Solothurn": "索洛图恩",
                    "Schwyz": "施维茨",
                    "Thurgau": "图尔高",
                    "Tessin": "提契诺州",
                    "Uri": "恨",
                    "Waadt": "沃州",
                    "Wallis": "瓦利斯",
                    "Zug": "楚格",
                    "Zrich": "兹里奇"
                },
                "TH": {
                    "AmnatCharoen": "安纳特查龙",
                    "AngThong": "红通",
                    "Bangkok": "曼谷",
                    "BuengKan": "汶干",
                    "BuriRam": "武里南",
                    "Chachoengsao": "茶草骚",
                    "ChaiNat": "柴纳",
                    "Chaiyaphum": "猜也奔",
                    "Chanthaburi": "尖竹汶",
                    "ChiangMai": "清迈",
                    "ChiangRai": "清莱",
                    "ChonBuri": "春武里",
                    "Chumphon": "春蓬",
                    "Kalasin": "加拉信",
                    "KamphaengPhet": "甘烹碧",
                    "Kanchanaburi": "北碧府",
                    "KhonKaen": "孔敬",
                    "Krabi": "甲米",
                    "Lampang": "南邦",
                    "Lamphun": "南奔",
                    "Loei": "黎",
                    "LopBuri": "华富里",
                    "MaeHongSon": "夜丰颂",
                    "MahaSarakham": "玛哈沙拉堪",
                    "Mukdahan": "穆达汉",
                    "NakhonNayok": "那空那育",
                    "NakhonPathom": "佛统",
                    "NakhonPhanom": "那空拍侬",
                    "NakhonRatchasima": "呵叻",
                    "NakhonSawan": "那空沙旺",
                    "NakhonSiThammarat": "那空是贪玛叻",
                    "Nan": "在",
                    "Narathiwat": "那拉提瓦",
                    "NongBuaLamphu": "农布阿兰普",
                    "NongKhai": "廊开",
                    "Nonthaburi": "暖武里",
                    "PathumThani": "巴吞他尼",
                    "Pattani": "北大年",
                    "PhangNga": "攀牙府",
                    "Phatthalung": "博他仑",
                    "Phatthaya": "帕他亚",
                    "Phayao": "帕尧",
                    "Phetchabun": "碧差汶",
                    "Phetchaburi": "碧武里",
                    "Phichit": "披集",
                    "Phitsanulok": "彭世洛",
                    "PhraNakhonSiAyutthaya": "帕那空是大城府",
                    "Phrae": "帕",
                    "Phuket": "普吉岛",
                    "PrachinBuri": "巴真武里",
                    "PrachuapKhiriKhan": "巴蜀基里汗",
                    "Ranong": "拉廊",
                    "Ratchaburi": "叻丕府",
                    "Rayong": "罗勇",
                    "RoiEt": "国王埃特",
                    "SaKaeo": "在甲尾",
                    "SakonNakhon": "沙功那空",
                    "SamutPrakan": "北榄府",
                    "SamutSakhon": "沙木沙空",
                    "SamutSongkhram": "夜功",
                    "Saraburi": "沙拉武里",
                    "Satun": "沙敦",
                    "SiSaKet": "斯撒克",
                    "SingBuri": "信武里",
                    "Songkhla": "宋卡",
                    "Sukhothai": "素可泰",
                    "SuphanBuri": "素攀武里",
                    "SuratThani": "素叻他尼",
                    "Surin": "苏林",
                    "Tak": "德",
                    "Trang": "董里",
                    "Trat": "正方形",
                    "UbonRatchathani": "乌汶叻差他尼",
                    "UdonThani": "乌隆他尼",
                    "UthaiThani": "乌泰他尼",
                    "Uttaradit": "程逸",
                    "Yala": "锁",
                    "Yasothon": "益梭通"
                },
                "AE": {
                    "AZ": "阿布扎比",
                    "AJ": "阿治曼",
                    "DU": "迪拜",
                    "FU": "富查伊拉",
                    "RK": "拉斯海马",
                    "SH": "沙迦",
                    "UQ": "乌姆盖万"
                },
                "US": {
                    "AL": "阿拉巴马州",
                    "AK": "阿拉斯加州",
                    "AZ": "亚利桑那",
                    "AR": "阿肯色州",
                    "CA": "加利福尼亚州",
                    "CO": "科罗拉多州",
                    "CT": "康涅狄格",
                    "DE": "特拉华州",
                    "FL": "佛罗里达",
                    "GA": "乔治亚州",
                    "HI": "夏威夷",
                    "ID": "爱达荷州",
                    "IL": "伊利诺伊州",
                    "IN": "印度人",
                    "IA": "爱荷华州",
                    "KS": "堪萨斯州",
                    "KY": "肯塔基州",
                    "LA": "路易斯安那州",
                    "ME": "明天",
                    "MD": "马里兰州",
                    "MA": "马萨诸塞州",
                    "MI": "密歇根州",
                    "MN": "明尼苏达州",
                    "MS": "密西西比州",
                    "MO": "密苏里州",
                    "MT": "山",
                    "NE": "内布拉斯加州",
                    "NV": "内华达",
                    "NH": "新罕布什尔",
                    "NJ": "新泽西州",
                    "NM": "新墨西哥",
                    "NY": "纽约",
                    "NC": "北卡罗来纳",
                    "ND": "北达科他州",
                    "OH": "俄亥俄州",
                    "OK": "俄克拉荷马州",
                    "OR": "俄勒冈州",
                    "PA": "宾夕法尼亚州",
                    "SC": "南卡罗来纳",
                    "SD": "南达科他州",
                    "TN": "田纳西州",
                    "TX": "德克萨斯州",
                    "UT": "犹他州",
                    "VT": "佛蒙特",
                    "VA": "处女",
                    "WA": "华盛顿",
                    "WV": "西弗吉尼亚",
                    "WI": "威斯康星州",
                    "WY": "怀俄明州",
                    "DC": "哥伦比亚特区",
                    "PR": "波多黎各",
                    "RI": "罗德岛",
                    "AS": "美属萨摩亚",
                    "FM": "密克罗尼西亚",
                    "GU": "关岛",
                    "MH": "马绍尔群岛",
                    "MP": "北马里亚纳群岛",
                    "PW": "帕劳",
                    "VI": "我们。维尔京群岛",
                    "AA": "美洲武装部队",
                    "AE": "欧洲武装力量",
                    "AP": "太平洋武装部队"
                },
                "MM": {
                    "AYA": "伊洛瓦底省",
                    "BGO": "勃固省",
                    "MGY": "马圭省",
                    "MDY": "曼德勒省",
                    "SGG": "实皆省",
                    "TNT": "德林达依省",
                    "YGN": "仰光省",
                    "CHN": "钦邦",
                    "KCN": "克钦邦",
                    "KYR": "克耶邦",
                    "KYN": "克伦邦",
                    "MON": "孟邦",
                    "RKE": "若开邦",
                    "SHN": "掸邦",
                    "NPT": "内比都联邦区"
                },
                "HK": {
                    "HK": "香港岛",
                    "KL": "九龙",
                    "NT": "新界"
                },
                "TW": {
                    "Taipei": "台北市",
                    "Keelung": "基隆市",
                    "NewTaipei": "新北市",
                    "Yilan": "宜兰县",
                    "HsinchuCity": "新竹市",
                    "HsinchuCounty": "新竹县",
                    "Taoyuan": "桃园市",
                    "Miaoli": "苗栗县",
                    "Taichung": "台中市",
                    "Changhua": "彰化县",
                    "Nantou": "南投县",
                    "ChiayiCity": "嘉义市",
                    "ChiayiCounty": "嘉义县",
                    "Yunlin": "云林县",
                    "Tainan": "台南市",
                    "Kaohsiung": "高雄市",
                    "Pingtung": "屏东县",
                    "Taitung": "台东县",
                    "Hualien": "花莲县",
                    "Penghu": "澎湖县",
                    "Kinmen": "金门县",
                    "Lianjiang": "连江县"
                },
                "VN": {
                    "AnGiang": "安江省",
                    "BaRiaVungTau": "巴地头顿省",
                    "BacGiang": "北江省",
                    "BacKan": "北干省",
                    "BacLieu": "薄辽省",
                    "BacNinh": "北宁省",
                    "BenTre": "本特雷省",
                    "Pacifythe": "安抚全省",
                    "BinhDuong": "平阳省",
                    "BinhPhuoc": "平福",
                    "BinhThuan": "平顺省",
                    "CaMau": "金瓯省",
                    "CanTho": "芹苴市",
                    "CaoBang": "曹邦省",
                    "DaNang": "岘港市",
                    "DakLak": "多乐省",
                    "DakNong": "达农省",
                    "DienBien": "奠边省",
                    "Dongnai": "同奈省",
                    "DongThap": "同塔省",
                    "GiaLai": "嘉莱省",
                    "HaGiang": "河江省",
                    "HaNam": "河南省",
                    "Hanoi": "河内市",
                    "HaTinh": "河静省",
                    "HaiDuong": "海阳省",
                    "HaiPhong": "海防市",
                    "HauGiang": "后江省",
                    "HoChiMinh": "胡志明市",
                    "HoaBinh": "和平省",
                    "HungYen": "兴安省",
                    "KhanhHoa": "庆和省",
                    "KienGiang": "坚江省",
                    "KonTum": "昆嵩省",
                    "LaiChau": "荔洲省",
                    "LamDong": "林同省",
                    "LangSon": "谅山省",
                    "LaoCai": "老街省",
                    "LongAn": "隆安省",
                    "NamDinh": "南定",
                    "NgheAn": "义安省",
                    "NinhBinh": "宁平省",
                    "NinhThuan": "宁顺省",
                    "PhuTho": "富寿省",
                    "PhuYen": "富安省",
                    "QuangBinh": "广平省",
                    "QuangNam": "广南省",
                    "QuangNgai": "广义省",
                    "QuangNinh": "广宁省",
                    "QuangTri": "广治省",
                    "SocTrang": "朔庄省",
                    "SonLa": "山罗府",
                    "TayNinh": "西宁省",
                    "ThaiBinh": "太平省",
                    "ThaiNguyen": "太原省",
                    "ThanhHoa": "清化省",
                    "ThuaThienHue": "顺化省",
                    "TienGiang": "前江省",
                    "TraVinh": "茶荣省",
                    "TuyenQuang": "宣光省",
                    "VinhLong": "永隆省",
                    "VinhPhuc": "永福省",
                    "YenBai": "安拜省",
                    "ThuDoHaNoi": "周四做河内"
                },
                "NL": {
                    "Drenthe": "德伦特省",
                    "Flevoland": "弗菜沃兰省",
                    "Friesland": "弗里斯兰省",
                    "Gelderland": "赫尔德兰省",
                    "Groningen": "格罗宁根省",
                    "Limburg": "林堡省",
                    "NoordBrabant": "北布拉邦省",
                    "NoordHolland": "北荷兰省",
                    "Overijssel": "上艾索尔省",
                    "Utrecht": "乌特勒支省",
                    "Zeeland": "泽兰省",
                    "ZuidHolland": "南荷兰省"
                },
                "FR": {
                    "Ain": "艾因",
                    "Aisne": "艾纳",
                    "Allier": "结合",
                    "AlpesMaritimes": "阿尔卑斯滨海省",
                    "AlpesDeHauteProvence": "上普罗旺斯阿尔卑斯",
                    "Ardennes": "阿登",
                    "Ardecha": "阿尔代什",
                    "Ariege": "阿里热",
                    "Aube": "黎明",
                    "Aude": "奥德",
                    "AuvergneRhoneAlps": "奥弗涅罗纳-阿尔卑斯大区",
                    "Aveyron": "阿韦龙",
                    "BasRhin": "下莱茵",
                    "BouchesDuRhone": "Bouches-du-Rhone",
                    "BurgundyFrancheComte": "勃艮第弗朗什孔蒂",
                    "Bretagne": "布列塔尼",
                    "Calvados": "卡尔瓦多斯",
                    "Cantal": "康塔尔",
                    "CentreValDeLoire": "卢瓦尔河谷中心",
                    "Charente": "夏朗德",
                    "CharenteMaritime": "夏朗德海事",
                    "Cher": "昂贵的",
                    "Correze": "科雷泽",
                    "Corse": "科西嘉岛",
                    "CorseduSud": "南科西嘉",
                    "Creuse": "挖",
                    "GoldenCoast": "黄金海岸",
                    "CotesDArmor": "Cotes-d'Armor",
                    "TwoSevres": "两个服务器",
                    "Dordogne": "多尔多涅省",
                    "Doubs": "杜布斯",
                    "Drome": "德罗姆",
                    "Essonne": "埃松",
                    "Eure": "厄尔",
                    "EureEtLoir": "厄尔-卢瓦尔",
                    "Finistere": "菲尼斯特雷",
                    "Gard": "加德",
                    "Gers": "热尔",
                    "Gironde": "吉伦特",
                    "GrandEst": "大东方",
                    "Guadeloupe": "瓜德罗普",
                    "Guyane": "圭亚那",
                    "FrenchGuiana": "法属圭亚那）",
                    "HautRhin": "上莱茵",
                    "HauteCorse": "上科西嘉",
                    "HauteGaronne": "上加龙河",
                    "HauteLoire": "上卢瓦尔河",
                    "HauteMarne": "高级马恩",
                    "HauteSavoie": "上萨瓦省",
                    "HauteSaone": "上索恩",
                    "HauteVienne": "上维埃纳",
                    "HautesAlpes": "高山",
                    "HautesPyrenees": "上比利牛斯山脉",
                    "HautsDeFrance": "上法兰西",
                    "HautsDeSeine": "上塞纳河",
                    "Hérault": "埃罗",
                    "IlleEtVilaine": "伊勒和维莱讷",
                    "Indre": "安德尔",
                    "IndreEtLoire": "安德尔与卢瓦尔河",
                    "Isere": "伊泽尔",
                    "Jura": "汝拉",
                    "LaReunion": "会议",
                    "Landes": "兰德斯",
                    "LoirEtCher": "卢瓦尔和雪儿",
                    "Loire": "卢瓦尔河",
                    "LoireAtlantique": "大西洋卢瓦尔河",
                    "Loiret": "卢瓦尔河",
                    "Lot": "批",
                    "LotEtGaronne": "洛特和加龙",
                    "Lozere": "洛泽尔",
                    "MaineEtLoire": "缅因和卢瓦尔",
                    "Manche": "袖子",
                    "Marne": "马尔",
                    "Martinique": "马提尼克岛",
                    "Mayenne": "马延",
                    "Mayotte": "马约特岛",
                    "MeurtheEtMoselle": "默尔特和摩泽尔",
                    "Meuse": "默兹",
                    "Morbihan": "莫尔比昂",
                    "Moselle": "摩泽尔",
                    "MetropoleDeLyon": "里昂大都会",
                    "Nievre": "涅夫尔",
                    "Nord": "北",
                    "Normandie": "诺曼底",
                    "NouvelleAquitaine": "新阿基坦",
                    "Occitanie": "奥西塔尼亚",
                    "Oise": "瓦兹",
                    "Orne": "装饰",
                    "Paris": "巴黎",
                    "PasDeCalais": "加来海峡",
                    "PaysDeLaLoire": "支付卢瓦尔河",
                    "ProvenceAlpesCoteDAzur": "普罗旺斯-阿尔卑斯-蔚蓝海岸",
                    "PuyDeDome": "Puy de Dome",
                    "PyrEnEesAtlantiques": "比利牛斯-大西洋",
                    "PyrEnEesOrientales": "东比利牛斯山脉",
                    "Rhone": "罗纳河",
                    "SaintPierreEtMiquelon": "圣皮埃尔和密克隆",
                    "Sarthe": "萨尔特",
                    "Savoie": "萨沃伊",
                    "SaoneEtLoire": "索恩-卢瓦尔省",
                    "SeineMaritime": "塞纳海事",
                    "SeineSaintDenis": "塞纳-圣但尼",
                    "SeineEtMarne": "塞纳河与马恩河",
                    "Somme": "和",
                    "Tarn": "塔恩",
                    "TarnEtGaronne": "塔恩和加龙河",
                    "TerritoireDeBelfort": "贝尔福地区",
                    "ValDOise": "瓦兹河谷",
                    "ValDeMarne": "马恩河谷",
                    "Var": "变量",
                    "Vaucluse": "沃克吕兹",
                    "Vendee": "买主",
                    "Vienne": "维也纳",
                    "Vosges": "孚日",
                    "Yonne": "约讷",
                    "Yvelines": "伊夫林",
                    "IleDeFrance": "法兰西岛"
                },
                "SK": {
                    "BanskobystrickyKraj": "班斯卡比斯特里察地区",
                    "BratislavskyKraj": "布拉迪斯拉发地区",
                    "KosickyKraj": "科希策地区",
                    "NitrianskyKraj": "尼特拉地区",
                    "PresovskyKraj": "普雷索夫地区",
                    "TrencianskyKraj": "特伦钦地区",
                    "TrnavskyKraj": "特尔纳瓦地区",
                    "ZilinskyKraj": "日林斯基地区"
                }
            },
            "address": {
                "shipping": {
                    "address1": "地址",
                    "address2": "公寓、套房等",
                    "city": "城市",
                    "country": "国家\/地区",
                    "firstName": "名",
                    "lastName": "姓",
                    "phone": "电话",
                    "province": "地区",
                    "zip": "邮政编码",
                    "taxId": "税号",
                    "optional": "（可选的）",
                    "additional": "附加信息"
                },
                "AR": {
                    "province": "省"
                },
                "AU": {
                    "city": "市郊",
                    "province": "州\/领地",
                    "zip": "邮政编码"
                },
                "AT": {
                    "address1": "街道和门牌号",
                    "address2": "另外地址"
                },
                "BR": {
                    "address1": "街道和门牌号",
                    "province": "州",
                    "taxId": "CPF 或 CNPJ 代码",
                    "cpf": "CPF（个人订单）",
                    "cnpj": "CNPJ（公司订单）"
                },
                "CA": {
                    "province": "省"
                },
                "CN": {
                    "address1": "完整地址",
                    "province": "省"
                },
                "CL": {
                    "taxId": "唯一纳税人别号"
                },
                "CO": {
                    "province": "省"
                },
                "DK": {
                    "address1": "街道和门牌号"
                },
                "EG": {
                    "province": "省"
                },
                "DE": {
                    "address1": "街道和门牌号",
                    "address2": "另外地址"
                },
                "HK": {
                    "city": "区"
                },
                "IN": {
                    "province": "州",
                    "zip": "PIN码"
                },
                "ID": {
                    "province": "省"
                },
                "IE": {
                    "province": "县"
                },
                "IT": {
                    "address1": "街道和门牌号",
                    "province": "省"
                },
                "JP": {
                    "city": "市\/区\/镇\/村",
                    "province": "州"
                },
                "MY": {
                    "province": "州\/领地",
                    "zip": "邮政编码"
                },
                "MX": {
                    "address1": "街道和门牌号",
                    "province": "州"
                },
                "NL": {
                    "address1": "街道和门牌号",
                    "province": "省"
                },
                "NG": {
                    "province": "州"
                },
                "NO": {
                    "address1": "街道和门牌号"
                },
                "RO": {
                    "province": "县"
                },
                "ZA": {
                    "address2": "市郊",
                    "province": "省"
                },
                "KR": {
                    "province": "省"
                },
                "ES": {
                    "address1": "街道和门牌号",
                    "province": "省"
                },
                "SE": {
                    "address1": "街道和门牌号",
                    "city": "市\/镇"
                },
                "CH": {
                    "address1": "街道和门牌号",
                    "address2": "另外地址"
                },
                "TH": {
                    "province": "省",
                    "taxId": "个人或增值税号",
                    "personal": "个人身份证号码（个人订单）",
                    "vatId": "增值税号（公司订单）"
                },
                "AE": {
                    "province": "酋长国"
                },
                "GB": {
                    "zip": "邮政编码"
                },
                "US": {
                    "province": "州",
                    "zip": "邮政编码"
                },
                "TW": {
                    "city": "地区",
                    "province": "城市\/县"
                },
                "VN": {
                    "province": "省份"
                },
                "FR": {
                    "province": "州\/省"
                }
            },
            "currency": {
                "global": {
                    "USD": "美元",
                    "EUR": "欧元",
                    "GBP": "英镑",
                    "CAD": "加拿大元",
                    "AUD": "澳大利亚元",
                    "CHF": "瑞士法郎",
                    "HKD": "港元",
                    "JPY": "日圆",
                    "RUB": "俄罗斯卢布",
                    "BRL": "巴西雷亚尔",
                    "CLP": "智利比索",
                    "NOK": "挪威克朗",
                    "DKK": "丹麦克朗",
                    "SEK": "瑞典克朗",
                    "KRW": "韩圆",
                    "ILS": "以色列新谢克尔",
                    "MXN": "墨西哥比索",
                    "CNY": "人民币元",
                    "SAR": "沙特里亚尔",
                    "SGD": "新加坡元",
                    "NZD": "新西兰元",
                    "ARS": "阿根廷比索",
                    "INR": "印度卢比",
                    "COP": "哥伦比亚比索",
                    "AED": "阿联酋迪拉姆",
                    "AFN": "阿富汗尼",
                    "ALL": "阿尔巴尼亚列克",
                    "AMD": "亚美尼亚德拉姆",
                    "ANG": "荷属安的列斯盾",
                    "AOA": "安哥拉宽扎",
                    "AWG": "阿鲁巴弗罗林",
                    "AZN": "阿塞拜疆马纳特",
                    "BAM": "波斯尼亚和黑塞哥维那可兑换马克",
                    "BBD": "巴巴多斯元",
                    "BDT": "孟加拉塔卡",
                    "BGN": "保加利亚列弗",
                    "BIF": "布隆迪法郎",
                    "BMD": "百慕大元",
                    "BND": "文莱元",
                    "BOB": "玻利维亚诺",
                    "BOV": "Mvdol（资金代码）",
                    "BSD": "巴哈马元",
                    "BTN": "不丹努尔特鲁姆",
                    "BWP": "博茨瓦纳普拉",
                    "BYN": "白俄罗斯卢布",
                    "BZD": "伯利兹元",
                    "CDF": "刚果法郎",
                    "CRC": "哥斯达黎加科朗",
                    "CUC": "古巴可兑换比索",
                    "CUP": "古巴比索",
                    "CVE": "佛得角埃斯库多",
                    "CZK": "捷克克朗",
                    "DJF": "吉布提法郎",
                    "DOP": "多米尼加比索",
                    "DZD": "阿尔及利亚第纳尔",
                    "EGP": "埃及镑",
                    "ERN": "厄立特里亚纳克法",
                    "ETB": "埃塞俄比亚比尔",
                    "FJD": "斐济元",
                    "FKP": "福克兰群岛镑",
                    "GEL": "格鲁吉亚拉里",
                    "GHS": "加纳塞地",
                    "GIP": "直布罗陀镑",
                    "GMD": "冈比亚达拉西",
                    "GNF": "几内亚法郎",
                    "GTQ": "危地马拉格查尔",
                    "GYD": "圭亚那元",
                    "HNL": "洪都拉斯伦皮拉",
                    "HRK": "克罗地亚库纳",
                    "HTG": "海地古德",
                    "HUF": "匈牙利福林",
                    "IDR": "印尼盾",
                    "IRR": "伊朗里亚尔",
                    "ISK": "冰岛克朗",
                    "JMD": "牙买加元",
                    "KES": "肯尼亚先令",
                    "KGS": "吉尔吉斯斯坦索姆",
                    "KHR": "柬埔寨瑞尔",
                    "KMF": "科摩罗法郎",
                    "KPW": "朝鲜圆",
                    "KYD": "开曼群岛元",
                    "KZT": "哈萨克斯坦坚戈",
                    "LAK": "老挝基普",
                    "LBP": "黎巴嫩镑",
                    "LKR": "斯里兰卡卢比",
                    "LRD": "利比里亚元",
                    "LSL": "莱索托洛蒂",
                    "MAD": "摩洛哥迪尔汗",
                    "MDL": "摩尔多瓦列伊",
                    "MKD": "马其顿代纳尔",
                    "MMK": "缅元",
                    "MNT": "蒙古图格里克",
                    "MOP": "澳门币",
                    "MUR": "毛里求斯卢比",
                    "MVR": "马尔代夫拉菲亚",
                    "MWK": "马拉维克瓦查",
                    "MYR": "马来西亚令吉",
                    "MZN": "莫桑比克梅蒂卡尔",
                    "NAD": "纳米比亚元",
                    "NGN": "尼日利亚奈拉",
                    "NIO": "尼加拉瓜科多巴",
                    "NPR": "尼泊尔卢比",
                    "PAB": "巴拿马巴波亚",
                    "PEN": "秘鲁索尔",
                    "PGK": "巴布亚新几内亚基那",
                    "PHP": "菲律宾比索",
                    "PKR": "巴基斯坦卢比",
                    "PLN": "波兰兹罗提",
                    "PYG": "巴拉圭瓜拉尼",
                    "QAR": "卡塔尔里亚尔",
                    "RON": "罗马尼亚列伊",
                    "RSD": "塞尔维亚第纳尔",
                    "RWF": "卢旺达法郎",
                    "SBD": "所罗门群岛元",
                    "SCR": "塞舌尔卢比",
                    "SDG": "苏丹镑",
                    "SHP": "圣赫勒拿镑",
                    "SLL": "塞拉利昂利昂",
                    "SOS": "索马里先令",
                    "SRD": "苏里南元",
                    "SSP": "南苏丹镑",
                    "STN": "圣多美和普林西比多布拉",
                    "SYP": "叙利亚镑",
                    "SZL": "斯威士兰里兰吉尼",
                    "THB": "泰铢",
                    "TJS": "塔吉克斯坦索莫尼",
                    "TMT": "土库曼斯坦马纳特",
                    "TOP": "汤加潘加",
                    "TRY": "土耳其里拉",
                    "TTD": "特立尼达和多巴哥元",
                    "TWD": "新台币",
                    "TZS": "坦桑尼亚先令",
                    "UAH": "乌克兰格里夫纳",
                    "UGX": "乌干达先令",
                    "UYU": "乌拉圭比索",
                    "UZS": "乌兹别克斯坦索姆",
                    "VES": "委内瑞拉玻利瓦尔",
                    "VND": "越南盾",
                    "VUV": "瓦努阿图瓦图",
                    "WST": "萨摩亚塔拉",
                    "XAF": "中非法郎",
                    "XCD": "东加勒比元",
                    "XOF": "西非法郎",
                    "XPF": "太平洋法郎（francPacifique）",
                    "YER": "也门里亚尔",
                    "ZAR": "南非兰特",
                    "ZMW": "赞比亚克瓦查",
                    "KWD": "科威特第纳尔",
                    "OMR": "阿曼里亚尔",
                    "BHD": "巴林第纳尔",
                    "JEP": "泽西镑",
                    "JOD": "约旦第纳尔",
                    "LVL": "拉特",
                    "MGA": "马达加斯加阿里亚里",
                    "STD": "圣多美和普林西比多布拉",
                    "TND": "突尼斯第纳尔",
                    "VEF": "委内瑞拉玻利瓦尔"
                }
            },
            "orders": {
                "paymentStatus": {
                    "unpaid": "未付款",
                    "pending": "待处理",
                    "partially_paid": "部分付款",
                    "paid": "已付款",
                    "partially_refunded": "部分退款",
                    "refunded": "已退款",
                    "voided": "已作废",
                    "confirm": "待确认"
                }
            },
            "app": {
                "form_tool": {
                    "success_content": "提交成功！ 我们会尽快回复您。"
                }
            }
        };
                window.Countries = {"1":{"name":"Afghanistan","code":"AF","tax":"0.00000","threshold":"0.00","phone_code":93,"address_format":0,"labels":[],"provinces":[]},"13":{"name":"Australia","code":"AU","tax":"0.00000","threshold":"0.00","phone_code":61,"address_format":4,"labels":{"city":"市郊","province":"州/领地","zip":"邮政编码"},"provinces":{"_1":{"code":"ACT","name":"Australian Capital Territory","tax":"0.00000","SId":1},"_2":{"code":"NSW","name":"New South Wales","tax":"0.00000","SId":2},"_3":{"code":"NT","name":"Northern Territory","tax":"0.00000","SId":3},"_4":{"code":"QLD","name":"Queensland","tax":"0.00000","SId":4},"_5":{"code":"SA","name":"South Australia","tax":"0.00000","SId":5},"_6":{"code":"TAS","name":"Tasmania","tax":"0.00000","SId":6},"_7":{"code":"VIC","name":"Victoria","tax":"0.00000","SId":7},"_8":{"code":"WA","name":"Western Australia","tax":"0.00000","SId":8}},"field":{"zip":{"verification":false,"example":"2060"}}},"30":{"name":"Brazil","code":"BR","tax":"0.00000","threshold":"0.00","phone_code":55,"address_format":4,"labels":{"address1":"街道和门牌号","province":"州","taxId":"CPF 或 CNPJ 代码"},"provinces":{"_454":{"code":"AC","name":"Acre","tax":"0.00000","SId":454},"_455":{"code":"AL","name":"Alagoas","tax":"0.00000","SId":455},"_456":{"code":"AP","name":"Amapá","tax":"0.00000","SId":456},"_457":{"code":"AM","name":"Amazonas","tax":"0.00000","SId":457},"_458":{"code":"BA","name":"Bahia","tax":"0.00000","SId":458},"_459":{"code":"CE","name":"Ceará","tax":"0.00000","SId":459},"_460":{"code":"DF","name":"Distrito Federal","tax":"0.00000","SId":460},"_461":{"code":"ES","name":"Espírito Santo","tax":"0.00000","SId":461},"_462":{"code":"GO","name":"Goiás","tax":"0.00000","SId":462},"_463":{"code":"MA","name":"Maranhão","tax":"0.00000","SId":463},"_465":{"code":"MT","name":"Mato Grosso","tax":"0.00000","SId":465},"_467":{"code":"MS","name":"Mato Grosso do Sul","tax":"0.00000","SId":467},"_469":{"code":"MG","name":"Minas Gerais","tax":"0.00000","SId":469},"_471":{"code":"PR","name":"Paraná","tax":"0.00000","SId":471},"_472":{"code":"PB","name":"Paraíba","tax":"0.00000","SId":472},"_473":{"code":"PA","name":"Pará","tax":"0.00000","SId":473},"_474":{"code":"PE","name":"Pernambuco","tax":"0.00000","SId":474},"_475":{"code":"PI","name":"Piauí","tax":"0.00000","SId":475},"_476":{"code":"RN","name":"Rio Grande do Norte","tax":"0.00000","SId":476},"_477":{"code":"RS","name":"Rio Grande do Sul","tax":"0.00000","SId":477},"_478":{"code":"RJ","name":"Rio de Janeiro","tax":"0.00000","SId":478},"_479":{"code":"RO","name":"Rondônia","tax":"0.00000","SId":479},"_480":{"code":"RR","name":"Roraima","tax":"0.00000","SId":480},"_481":{"code":"SC","name":"Santa Catarina","tax":"0.00000","SId":481},"_482":{"code":"SE","name":"Sergipe","tax":"0.00000","SId":482},"_483":{"code":"SP","name":"São Paulo","tax":"0.00000","SId":483},"_484":{"code":"TO","name":"Tocantins","tax":"0.00000","SId":484}}},"38":{"name":"Canada","code":"CA","tax":"0.00000","threshold":"0.00","phone_code":1,"address_format":4,"labels":{"province":"省"},"provinces":{"_19":{"code":"AB","name":"Alberta","tax":"0.00000","SId":19},"_20":{"code":"BC","name":"British Columbia","tax":"0.00000","SId":20},"_21":{"code":"MB","name":"Manitoba","tax":"0.00000","SId":21},"_22":{"code":"NL","name":"Newfoundland and Labrador","tax":"0.00000","SId":22},"_23":{"code":"NB","name":"New Brunswick","tax":"0.00000","SId":23},"_24":{"code":"NS","name":"Nova Scotia","tax":"0.00000","SId":24},"_25":{"code":"NT","name":"Northwest Territories","tax":"0.00000","SId":25},"_26":{"code":"NU","name":"Nunavut","tax":"0.00000","SId":26},"_27":{"code":"ON","name":"Ontario","tax":"0.00000","SId":27},"_28":{"code":"PE","name":"Prince Edward Island","tax":"0.00000","SId":28},"_29":{"code":"QC","name":"Quebec","tax":"0.00000","SId":29},"_30":{"code":"SK","name":"Saskatchewan","tax":"0.00000","SId":30},"_31":{"code":"YT","name":"Yukon","tax":"0.00000","SId":31}}},"74":{"name":"France","code":"FR","tax":"0.00000","threshold":"0.00","phone_code":33,"address_format":11,"labels":{"province":"州/省"},"provinces":{"_1239":{"code":"Ain","name":"Ain","tax":"0.00000","SId":1239},"_1240":{"code":"Aisne","name":"Aisne","tax":"0.00000","SId":1240},"_1241":{"code":"Allier","name":"Allier","tax":"0.00000","SId":1241},"_1242":{"code":"AlpesMaritimes","name":"Alpes-Maritimes","tax":"0.00000","SId":1242},"_1243":{"code":"AlpesDeHauteProvence","name":"Alpes-de-Haute-Provence","tax":"0.00000","SId":1243},"_1244":{"code":"Ardennes","name":"Ardennes","tax":"0.00000","SId":1244},"_1245":{"code":"Ardecha","name":"Ardèche","tax":"0.00000","SId":1245},"_1246":{"code":"Ariege","name":"Ariège","tax":"0.00000","SId":1246},"_1247":{"code":"Aube","name":"Aube","tax":"0.00000","SId":1247},"_1248":{"code":"Aude","name":"Aude","tax":"0.00000","SId":1248},"_1249":{"code":"AuvergneRhoneAlps","name":"Auvergne Rhône-Alpes","tax":"0.00000","SId":1249},"_1250":{"code":"Aveyron","name":"Aveyron","tax":"0.00000","SId":1250},"_1251":{"code":"BasRhin","name":"Bas-Rhin","tax":"0.00000","SId":1251},"_1252":{"code":"BouchesDuRhone","name":"Bouches-du-Rhône","tax":"0.00000","SId":1252},"_1253":{"code":"BurgundyFrancheComte","name":"Bourgogne Franche-Comté","tax":"0.00000","SId":1253},"_1254":{"code":"Bretagne","name":"Bretagne","tax":"0.00000","SId":1254},"_1255":{"code":"Calvados","name":"Calvados","tax":"0.00000","SId":1255},"_1256":{"code":"Cantal","name":"Cantal","tax":"0.00000","SId":1256},"_1257":{"code":"CentreValDeLoire","name":"Centre Val de Loire","tax":"0.00000","SId":1257},"_1258":{"code":"Charente","name":"Charente","tax":"0.00000","SId":1258},"_1259":{"code":"CharenteMaritime","name":"Charente-Maritime","tax":"0.00000","SId":1259},"_1260":{"code":"Cher","name":"Cher","tax":"0.00000","SId":1260},"_1261":{"code":"Correze","name":"Corrèze","tax":"0.00000","SId":1261},"_1262":{"code":"Corse","name":"Corse","tax":"0.00000","SId":1262},"_1263":{"code":"CorseduSud","name":"Corse-du-Sud","tax":"0.00000","SId":1263},"_1264":{"code":"Creuse","name":"Creuse","tax":"0.00000","SId":1264},"_1265":{"code":"GoldenCoast","name":"Côte-d&#039;Or","tax":"0.00000","SId":1265},"_1266":{"code":"CotesDArmor","name":"Côtes-d&#039;Armor","tax":"0.00000","SId":1266},"_1267":{"code":"TwoSevres","name":"Deux-Sèvres","tax":"0.00000","SId":1267},"_1268":{"code":"Dordogne","name":"Dordogne","tax":"0.00000","SId":1268},"_1269":{"code":"Doubs","name":"Doubs","tax":"0.00000","SId":1269},"_1270":{"code":"Drome","name":"Drôme","tax":"0.00000","SId":1270},"_1271":{"code":"Essonne","name":"Essonne","tax":"0.00000","SId":1271},"_1272":{"code":"Eure","name":"Eure","tax":"0.00000","SId":1272},"_1273":{"code":"EureEtLoir","name":"Eure-et-Loir","tax":"0.00000","SId":1273},"_1274":{"code":"Finistere","name":"Finistère","tax":"0.00000","SId":1274},"_1275":{"code":"Gard","name":"Gard","tax":"0.00000","SId":1275},"_1276":{"code":"Gers","name":"Gers","tax":"0.00000","SId":1276},"_1277":{"code":"Gironde","name":"Gironde","tax":"0.00000","SId":1277},"_1278":{"code":"GrandEst","name":"Grand Est","tax":"0.00000","SId":1278},"_1279":{"code":"Guadeloupe","name":"Guadeloupe","tax":"0.00000","SId":1279},"_1280":{"code":"Guyane","name":"Guyane","tax":"0.00000","SId":1280},"_1281":{"code":"FrenchGuiana","name":"Guyane (française)","tax":"0.00000","SId":1281},"_1282":{"code":"HautRhin","name":"Haut-Rhin","tax":"0.00000","SId":1282},"_1283":{"code":"HauteCorse","name":"Haute-Corse","tax":"0.00000","SId":1283},"_1284":{"code":"HauteGaronne","name":"Haute-Garonne","tax":"0.00000","SId":1284},"_1285":{"code":"HauteLoire","name":"Haute-Loire","tax":"0.00000","SId":1285},"_1286":{"code":"HauteMarne","name":"Haute-Marne","tax":"0.00000","SId":1286},"_1287":{"code":"HauteSavoie","name":"Haute-Savoie","tax":"0.00000","SId":1287},"_1288":{"code":"HauteSaone","name":"Haute-Saône","tax":"0.00000","SId":1288},"_1289":{"code":"HauteVienne","name":"Haute-Vienne","tax":"0.00000","SId":1289},"_1290":{"code":"HautesAlpes","name":"Hautes-Alpes","tax":"0.00000","SId":1290},"_1291":{"code":"HautesPyrenees","name":"Hautes-Pyrénées","tax":"0.00000","SId":1291},"_1292":{"code":"HautsDeFrance","name":"Hauts-de-France","tax":"0.00000","SId":1292},"_1293":{"code":"HautsDeSeine","name":"Hauts-de-Seine","tax":"0.00000","SId":1293},"_1294":{"code":"Hérault","name":"Hérault","tax":"0.00000","SId":1294},"_1295":{"code":"IlleEtVilaine","name":"Ille-et-Vilaine","tax":"0.00000","SId":1295},"_1296":{"code":"Indre","name":"Indre","tax":"0.00000","SId":1296},"_1297":{"code":"IndreEtLoire","name":"Indre-et-Loire","tax":"0.00000","SId":1297},"_1298":{"code":"Isere","name":"Isère","tax":"0.00000","SId":1298},"_1299":{"code":"Jura","name":"Jura","tax":"0.00000","SId":1299},"_1300":{"code":"LaReunion","name":"La Réunion","tax":"0.00000","SId":1300},"_1301":{"code":"Landes","name":"Landes","tax":"0.00000","SId":1301},"_1302":{"code":"LoirEtCher","name":"Loir-et-Cher","tax":"0.00000","SId":1302},"_1303":{"code":"Loire","name":"Loire","tax":"0.00000","SId":1303},"_1304":{"code":"LoireAtlantique","name":"Loire-Atlantique","tax":"0.00000","SId":1304},"_1305":{"code":"Loiret","name":"Loiret","tax":"0.00000","SId":1305},"_1306":{"code":"Lot","name":"Lot","tax":"0.00000","SId":1306},"_1307":{"code":"LotEtGaronne","name":"Lot-et-Garonne","tax":"0.00000","SId":1307},"_1308":{"code":"Lozere","name":"Lozère","tax":"0.00000","SId":1308},"_1309":{"code":"MaineEtLoire","name":"Maine-et-Loire","tax":"0.00000","SId":1309},"_1310":{"code":"Manche","name":"Manche","tax":"0.00000","SId":1310},"_1311":{"code":"Marne","name":"Marne","tax":"0.00000","SId":1311},"_1312":{"code":"Martinique","name":"Martinique","tax":"0.00000","SId":1312},"_1313":{"code":"Mayenne","name":"Mayenne","tax":"0.00000","SId":1313},"_1314":{"code":"Mayotte","name":"Mayotte","tax":"0.00000","SId":1314},"_1315":{"code":"MeurtheEtMoselle","name":"Meurthe-et-Moselle","tax":"0.00000","SId":1315},"_1316":{"code":"Meuse","name":"Meuse","tax":"0.00000","SId":1316},"_1317":{"code":"Morbihan","name":"Morbihan","tax":"0.00000","SId":1317},"_1318":{"code":"Moselle","name":"Moselle","tax":"0.00000","SId":1318},"_1319":{"code":"MetropoleDeLyon","name":"Métropole de Lyon","tax":"0.00000","SId":1319},"_1320":{"code":"Nievre","name":"Nievre","tax":"0.00000","SId":1320},"_1321":{"code":"Nord","name":"Nord","tax":"0.00000","SId":1321},"_1322":{"code":"Normandie","name":"Normandie","tax":"0.00000","SId":1322},"_1323":{"code":"NouvelleAquitaine","name":"Nouvelle-Aquitaine","tax":"0.00000","SId":1323},"_1324":{"code":"Occitanie","name":"Occitanie","tax":"0.00000","SId":1324},"_1325":{"code":"Oise","name":"Oise","tax":"0.00000","SId":1325},"_1326":{"code":"Orne","name":"Orne","tax":"0.00000","SId":1326},"_1327":{"code":"Paris","name":"Paris","tax":"0.00000","SId":1327},"_1328":{"code":"PasDeCalais","name":"Pas-de-Calais","tax":"0.00000","SId":1328},"_1329":{"code":"PaysDeLaLoire","name":"Pays de la Loire","tax":"0.00000","SId":1329},"_1330":{"code":"ProvenceAlpesCoteDAzur","name":"Provence-Alpes-Côte d&#039;Azur","tax":"0.00000","SId":1330},"_1331":{"code":"PuyDeDome","name":"Puy-de-Dôme","tax":"0.00000","SId":1331},"_1332":{"code":"PyrEnEesAtlantiques","name":"Pyrénées-Atlantiques","tax":"0.00000","SId":1332},"_1333":{"code":"PyrEnEesOrientales","name":"Pyrénées-Orientales","tax":"0.00000","SId":1333},"_1334":{"code":"Rhone","name":"Rhône","tax":"0.00000","SId":1334},"_1335":{"code":"SaintPierreEtMiquelon","name":"Saint-Pierre-et-Miquelon","tax":"0.00000","SId":1335},"_1336":{"code":"Sarthe","name":"Sarthe","tax":"0.00000","SId":1336},"_1337":{"code":"Savoie","name":"Savoie","tax":"0.00000","SId":1337},"_1338":{"code":"SaoneEtLoire","name":"Saône-et-Loire","tax":"0.00000","SId":1338},"_1339":{"code":"SeineMaritime","name":"Seine-Maritime","tax":"0.00000","SId":1339},"_1340":{"code":"SeineSaintDenis","name":"Seine-Saint-Denis","tax":"0.00000","SId":1340},"_1341":{"code":"SeineEtMarne","name":"Seine-et-Marne","tax":"0.00000","SId":1341},"_1342":{"code":"Somme","name":"Somme","tax":"0.00000","SId":1342},"_1343":{"code":"Tarn","name":"Tarn","tax":"0.00000","SId":1343},"_1344":{"code":"TarnEtGaronne","name":"Tarn-et-Garonne","tax":"0.00000","SId":1344},"_1345":{"code":"TerritoireDeBelfort","name":"Territoire de Belfort","tax":"0.00000","SId":1345},"_1346":{"code":"ValDOise","name":"Val-d&#039;Oise","tax":"0.00000","SId":1346},"_1347":{"code":"ValDeMarne","name":"Val-de-Marne","tax":"0.00000","SId":1347},"_1348":{"code":"Var","name":"Var","tax":"0.00000","SId":1348},"_1349":{"code":"Vaucluse","name":"Vaucluse","tax":"0.00000","SId":1349},"_1350":{"code":"Vendee","name":"Vendée","tax":"0.00000","SId":1350},"_1351":{"code":"Vienne","name":"Vienne","tax":"0.00000","SId":1351},"_1352":{"code":"Vosges","name":"Vosges","tax":"0.00000","SId":1352},"_1353":{"code":"Yonne","name":"Yonne","tax":"0.00000","SId":1353},"_1354":{"code":"Yvelines","name":"Yvelines","tax":"0.00000","SId":1354},"_1355":{"code":"IleDeFrance","name":"Île-de-France","tax":"0.00000","SId":1355}},"field":{"taxId":{"select":{"6":"taxId"}}}},"81":{"name":"Germany","code":"DE","tax":"0.00000","threshold":"0.00","phone_code":49,"address_format":11,"labels":{"address1":"街道和门牌号","address2":"另外地址"},"provinces":{"_32":{"code":"Baden-Wrttemberg","name":"Baden-Wrttemberg","tax":"0.00000","SId":32},"_33":{"code":"Bayern","name":"Bayern","tax":"0.00000","SId":33},"_34":{"code":"Berlin","name":"Berlin","tax":"0.00000","SId":34},"_35":{"code":"Brandenburg","name":"Brandenburg","tax":"0.00000","SId":35},"_36":{"code":"Bremen","name":"Bremen","tax":"0.00000","SId":36},"_37":{"code":"Hamburg","name":"Hamburg","tax":"0.00000","SId":37},"_38":{"code":"Hessen","name":"Hessen","tax":"0.00000","SId":38},"_39":{"code":"Mecklenburg-Vorpommern","name":"Mecklenburg-Vorpommern","tax":"0.00000","SId":39},"_40":{"code":"Niedersachsen","name":"Niedersachsen","tax":"0.00000","SId":40},"_41":{"code":"Nordrhein-Westfalen","name":"Nordrhein-Westfalen","tax":"0.00000","SId":41},"_42":{"code":"Rheinland-Pfalz","name":"Rheinland-Pfalz","tax":"0.00000","SId":42},"_43":{"code":"Saarland","name":"Saarland","tax":"0.00000","SId":43},"_44":{"code":"Sachsen","name":"Sachsen","tax":"0.00000","SId":44},"_45":{"code":"Sachsen-Anhalt","name":"Sachsen-Anhalt","tax":"0.00000","SId":45},"_46":{"code":"Schleswig-Holstein","name":"Schleswig-Holstein","tax":"0.00000","SId":46},"_47":{"code":"Thuringen","name":"Thuringen","tax":"0.00000","SId":47}}},"106":{"name":"Italy","code":"IT","tax":"0.00000","threshold":"0.00","phone_code":39,"address_format":11,"labels":{"address1":"街道和门牌号","province":"省"},"provinces":{"_214":{"code":"AG","name":"Agrigento","tax":"0.00000","SId":214},"_215":{"code":"AL","name":"Alessandria","tax":"0.00000","SId":215},"_217":{"code":"AN","name":"Ancona","tax":"0.00000","SId":217},"_219":{"code":"AO","name":"Aosta","tax":"0.00000","SId":219},"_220":{"code":"AR","name":"Arezzo","tax":"0.00000","SId":220},"_222":{"code":"AP","name":"Ascoli Piceno","tax":"0.00000","SId":222},"_223":{"code":"AT","name":"Asti","tax":"0.00000","SId":223},"_225":{"code":"AV","name":"Avellino","tax":"0.00000","SId":225},"_226":{"code":"BA","name":"Bari","tax":"0.00000","SId":226},"_227":{"code":"BT","name":"Barletta-Andria-Trani","tax":"0.00000","SId":227},"_231":{"code":"BL","name":"Belluno","tax":"0.00000","SId":231},"_233":{"code":"BN","name":"Benevento","tax":"0.00000","SId":233},"_235":{"code":"BG","name":"Bergamo","tax":"0.00000","SId":235},"_236":{"code":"BI","name":"Biella","tax":"0.00000","SId":236},"_237":{"code":"BO","name":"Bologna","tax":"0.00000","SId":237},"_238":{"code":"BZ","name":"Bolzano","tax":"0.00000","SId":238},"_239":{"code":"BS","name":"Brescia","tax":"0.00000","SId":239},"_240":{"code":"BR","name":"Brindisi","tax":"0.00000","SId":240},"_241":{"code":"CA","name":"Cagliari","tax":"0.00000","SId":241},"_242":{"code":"CL","name":"Caltanissetta","tax":"0.00000","SId":242},"_243":{"code":"CB","name":"Campobasso","tax":"0.00000","SId":243},"_244":{"code":"CI","name":"Carbonia-Iglesias","tax":"0.00000","SId":244},"_245":{"code":"CE","name":"Caserta","tax":"0.00000","SId":245},"_246":{"code":"CT","name":"Catania","tax":"0.00000","SId":246},"_247":{"code":"CZ","name":"Catanzaro","tax":"0.00000","SId":247},"_248":{"code":"CH","name":"Chieti","tax":"0.00000","SId":248},"_249":{"code":"CO","name":"Como","tax":"0.00000","SId":249},"_250":{"code":"CS","name":"Cosenza","tax":"0.00000","SId":250},"_251":{"code":"CR","name":"Cremona","tax":"0.00000","SId":251},"_252":{"code":"KR","name":"Crotone","tax":"0.00000","SId":252},"_253":{"code":"CN","name":"Cuneo","tax":"0.00000","SId":253},"_254":{"code":"EN","name":"Enna","tax":"0.00000","SId":254},"_255":{"code":"FM","name":"Fermo","tax":"0.00000","SId":255},"_256":{"code":"FE","name":"Ferrara","tax":"0.00000","SId":256},"_257":{"code":"FI","name":"Firenze","tax":"0.00000","SId":257},"_258":{"code":"FG","name":"Foggia","tax":"0.00000","SId":258},"_259":{"code":"FC","name":"Forlì-Cesena","tax":"0.00000","SId":259},"_260":{"code":"FR","name":"Frosinone","tax":"0.00000","SId":260},"_261":{"code":"GE","name":"Genova","tax":"0.00000","SId":261},"_262":{"code":"GO","name":"Gorizia","tax":"0.00000","SId":262},"_263":{"code":"GR","name":"Grosseto","tax":"0.00000","SId":263},"_264":{"code":"IM","name":"Imperia","tax":"0.00000","SId":264},"_265":{"code":"IS","name":"Isernia","tax":"0.00000","SId":265},"_266":{"code":"AQ","name":"L&#039;Aquila","tax":"0.00000","SId":266},"_267":{"code":"SP","name":"La Spezia","tax":"0.00000","SId":267},"_268":{"code":"LT","name":"Latina","tax":"0.00000","SId":268},"_269":{"code":"LE","name":"Lecce","tax":"0.00000","SId":269},"_270":{"code":"LC","name":"Lecco","tax":"0.00000","SId":270},"_271":{"code":"LI","name":"Livorno","tax":"0.00000","SId":271},"_272":{"code":"LO","name":"Lodi","tax":"0.00000","SId":272},"_273":{"code":"LU","name":"Lucca","tax":"0.00000","SId":273},"_274":{"code":"MC","name":"Macerata","tax":"0.00000","SId":274},"_275":{"code":"MN","name":"Mantova","tax":"0.00000","SId":275},"_323":{"code":"MS","name":"Massa-Carrara","tax":"0.00000","SId":323},"_325":{"code":"MT","name":"Matera","tax":"0.00000","SId":325},"_330":{"code":"VS","name":"Medio Campidano","tax":"0.00000","SId":330},"_332":{"code":"ME","name":"Messina","tax":"0.00000","SId":332},"_337":{"code":"MI","name":"Milano","tax":"0.00000","SId":337},"_341":{"code":"MO","name":"Modena","tax":"0.00000","SId":341},"_345":{"code":"MB","name":"Monza e della Brianza","tax":"0.00000","SId":345},"_348":{"code":"NA","name":"Napoli","tax":"0.00000","SId":348},"_353":{"code":"NO","name":"Novara","tax":"0.00000","SId":353},"_357":{"code":"NU","name":"Nuoro","tax":"0.00000","SId":357},"_361":{"code":"OG","name":"Ogliastra","tax":"0.00000","SId":361},"_367":{"code":"OT","name":"Olbia-Tempio","tax":"0.00000","SId":367},"_371":{"code":"OR","name":"Oristano","tax":"0.00000","SId":371},"_374":{"code":"PD","name":"Padova","tax":"0.00000","SId":374},"_377":{"code":"PA","name":"Palermo","tax":"0.00000","SId":377},"_381":{"code":"PR","name":"Parma","tax":"0.00000","SId":381},"_383":{"code":"PV","name":"Pavia","tax":"0.00000","SId":383},"_390":{"code":"PG","name":"Perugia","tax":"0.00000","SId":390},"_393":{"code":"PU","name":"Pesaro e Urbino","tax":"0.00000","SId":393},"_397":{"code":"PE","name":"Pescara","tax":"0.00000","SId":397},"_425":{"code":"PC","name":"Piacenza","tax":"0.00000","SId":425},"_428":{"code":"PI","name":"Pisa","tax":"0.00000","SId":428},"_431":{"code":"PT","name":"Pistoia","tax":"0.00000","SId":431},"_441":{"code":"PN","name":"Pordenone","tax":"0.00000","SId":441},"_464":{"code":"PO","name":"Prato","tax":"0.00000","SId":464},"_466":{"code":"RG","name":"Ragusa","tax":"0.00000","SId":466},"_468":{"code":"RA","name":"Ravenna","tax":"0.00000","SId":468},"_470":{"code":"RC","name":"Reggio Calabria","tax":"0.00000","SId":470},"_533":{"code":"RE","name":"Reggio Emilia","tax":"0.00000","SId":533},"_534":{"code":"RI","name":"Rieti","tax":"0.00000","SId":534},"_535":{"code":"RN","name":"Rimini","tax":"0.00000","SId":535},"_536":{"code":"RM","name":"Roma","tax":"0.00000","SId":536},"_537":{"code":"RO","name":"Rovigo","tax":"0.00000","SId":537},"_538":{"code":"SA","name":"Salerno","tax":"0.00000","SId":538},"_539":{"code":"SS","name":"Sassari","tax":"0.00000","SId":539},"_540":{"code":"SV","name":"Savona","tax":"0.00000","SId":540},"_541":{"code":"SI","name":"Siena","tax":"0.00000","SId":541},"_542":{"code":"SR","name":"Siracusa","tax":"0.00000","SId":542},"_543":{"code":"SO","name":"Sondrio","tax":"0.00000","SId":543},"_544":{"code":"TA","name":"Taranto","tax":"0.00000","SId":544},"_545":{"code":"TE","name":"Teramo","tax":"0.00000","SId":545},"_546":{"code":"TR","name":"Terni","tax":"0.00000","SId":546},"_547":{"code":"TO","name":"Torino","tax":"0.00000","SId":547},"_548":{"code":"TP","name":"Trapani","tax":"0.00000","SId":548},"_549":{"code":"TN","name":"Trento","tax":"0.00000","SId":549},"_550":{"code":"TV","name":"Treviso","tax":"0.00000","SId":550},"_551":{"code":"TS","name":"Trieste","tax":"0.00000","SId":551},"_552":{"code":"UD","name":"Udine","tax":"0.00000","SId":552},"_553":{"code":"VA","name":"Varese","tax":"0.00000","SId":553},"_554":{"code":"VE","name":"Venezia","tax":"0.00000","SId":554},"_555":{"code":"VB","name":"Verbano-Cusio-Ossola","tax":"0.00000","SId":555},"_556":{"code":"VC","name":"Vercelli","tax":"0.00000","SId":556},"_557":{"code":"VR","name":"Verona","tax":"0.00000","SId":557},"_558":{"code":"VV","name":"Vibo Valentia","tax":"0.00000","SId":558},"_559":{"code":"VI","name":"Vicenza","tax":"0.00000","SId":559},"_560":{"code":"VT","name":"Viterbo","tax":"0.00000","SId":560},"_832":{"code":"PZ","name":"Potenza","tax":"0.00000","SId":832}}},"199":{"name":"Spain","code":"ES","tax":"0.00000","threshold":"0.00","phone_code":34,"address_format":11,"labels":{"address1":"街道和门牌号","province":"省"},"provinces":{"_48":{"code":"ACorua","name":"A Corua","tax":"0.00000","SId":48},"_49":{"code":"Alava","name":"Alava","tax":"0.00000","SId":49},"_50":{"code":"Albacete","name":"Albacete","tax":"0.00000","SId":50},"_51":{"code":"Alicante","name":"Alicante","tax":"0.00000","SId":51},"_52":{"code":"Almeria","name":"Almeria","tax":"0.00000","SId":52},"_53":{"code":"Asturias","name":"Asturias","tax":"0.00000","SId":53},"_54":{"code":"Avila","name":"Avila","tax":"0.00000","SId":54},"_55":{"code":"Badajoz","name":"Badajoz","tax":"0.00000","SId":55},"_56":{"code":"Baleares","name":"Baleares","tax":"0.00000","SId":56},"_57":{"code":"Barcelona","name":"Barcelona","tax":"0.00000","SId":57},"_58":{"code":"Burgos","name":"Burgos","tax":"0.00000","SId":58},"_59":{"code":"Caceres","name":"Caceres","tax":"0.00000","SId":59},"_60":{"code":"Cadiz","name":"Cadiz","tax":"0.00000","SId":60},"_61":{"code":"Cantabria","name":"Cantabria","tax":"0.00000","SId":61},"_62":{"code":"Castellon","name":"Castellon","tax":"0.00000","SId":62},"_63":{"code":"Ceuta","name":"Ceuta","tax":"0.00000","SId":63},"_64":{"code":"CiudadReal","name":"Ciudad Real","tax":"0.00000","SId":64},"_65":{"code":"Cordoba","name":"Cordoba","tax":"0.00000","SId":65},"_66":{"code":"Cuenca","name":"Cuenca","tax":"0.00000","SId":66},"_67":{"code":"Girona","name":"Girona","tax":"0.00000","SId":67},"_68":{"code":"Granada","name":"Granada","tax":"0.00000","SId":68},"_69":{"code":"Guadalajara","name":"Guadalajara","tax":"0.00000","SId":69},"_70":{"code":"Guipuzcoa","name":"Guipuzcoa","tax":"0.00000","SId":70},"_71":{"code":"Huelva","name":"Huelva","tax":"0.00000","SId":71},"_72":{"code":"Huesca","name":"Huesca","tax":"0.00000","SId":72},"_73":{"code":"Jaen","name":"Jaen","tax":"0.00000","SId":73},"_74":{"code":"LaRioja","name":"La Rioja","tax":"0.00000","SId":74},"_75":{"code":"LasPalmas","name":"Las Palmas","tax":"0.00000","SId":75},"_76":{"code":"Leon","name":"Leon","tax":"0.00000","SId":76},"_77":{"code":"Lleida","name":"Lleida","tax":"0.00000","SId":77},"_78":{"code":"Lugo","name":"Lugo","tax":"0.00000","SId":78},"_79":{"code":"Madrid","name":"Madrid","tax":"0.00000","SId":79},"_80":{"code":"Malaga","name":"Malaga","tax":"0.00000","SId":80},"_81":{"code":"Melilla","name":"Melilla","tax":"0.00000","SId":81},"_82":{"code":"Murcia","name":"Murcia","tax":"0.00000","SId":82},"_83":{"code":"Navarra","name":"Navarra","tax":"0.00000","SId":83},"_84":{"code":"Ourense","name":"Ourense","tax":"0.00000","SId":84},"_85":{"code":"Palencia","name":"Palencia","tax":"0.00000","SId":85},"_86":{"code":"Pontevedra","name":"Pontevedra","tax":"0.00000","SId":86},"_87":{"code":"Salamanca","name":"Salamanca","tax":"0.00000","SId":87},"_88":{"code":"SantaCruzdeTenerife","name":"Santa Cruz de Tenerife","tax":"0.00000","SId":88},"_89":{"code":"Segovia","name":"Segovia","tax":"0.00000","SId":89},"_90":{"code":"Sevilla","name":"Sevilla","tax":"0.00000","SId":90},"_91":{"code":"Soria","name":"Soria","tax":"0.00000","SId":91},"_92":{"code":"Tarragona","name":"Tarragona","tax":"0.00000","SId":92},"_93":{"code":"Teruel","name":"Teruel","tax":"0.00000","SId":93},"_94":{"code":"Toledo","name":"Toledo","tax":"0.00000","SId":94},"_95":{"code":"Valencia","name":"Valencia","tax":"0.00000","SId":95},"_96":{"code":"Valladolid","name":"Valladolid","tax":"0.00000","SId":96},"_97":{"code":"Vizcaya","name":"Vizcaya","tax":"0.00000","SId":97},"_98":{"code":"Zamora","name":"Zamora","tax":"0.00000","SId":98},"_99":{"code":"Zaragoza","name":"Zaragoza","tax":"0.00000","SId":99},"_1364":{"code":"Andalusia","name":"Andalusia","tax":"0.00000","SId":1364},"_1365":{"code":"Aragon","name":"Aragón","tax":"0.00000","SId":1365},"_1366":{"code":"BalearicIslands","name":"Balearic Islands","tax":"0.00000","SId":1366},"_1367":{"code":"Biscay","name":"Biscay","tax":"0.00000","SId":1367},"_1368":{"code":"Canarias","name":"Canarias","tax":"0.00000","SId":1368},"_1369":{"code":"CastillaLaMancha","name":"Castilla-La Mancha","tax":"0.00000","SId":1369},"_1370":{"code":"CastillayLeon","name":"Castilla y León","tax":"0.00000","SId":1370},"_1371":{"code":"Cataluna","name":"Cataluña","tax":"0.00000","SId":1371},"_1372":{"code":"ComunidadForaldeNavarra","name":"Comunidad Foral de Navarra","tax":"0.00000","SId":1372},"_1373":{"code":"ComunidadValenciana","name":"Comunidad Valenciana","tax":"0.00000","SId":1373},"_1374":{"code":"Extremadura","name":"Extremadura","tax":"0.00000","SId":1374},"_1375":{"code":"Galicia","name":"Galicia","tax":"0.00000","SId":1375},"_1376":{"code":"IllesBalears","name":"Illes Balears","tax":"0.00000","SId":1376}}},"206":{"name":"Switzerland","code":"CH","tax":"0.00000","threshold":"0.00","phone_code":41,"address_format":5,"labels":{"address1":"街道和门牌号","address2":"另外地址"},"provinces":{"_100":{"code":"Aargau","name":"Aargau","tax":"0.00000","SId":100},"_101":{"code":"AppenzellInnerrhoden","name":"Appenzell Innerrhoden","tax":"0.00000","SId":101},"_102":{"code":"AppenzellAusserrhoden","name":"Appenzell Ausserrhoden","tax":"0.00000","SId":102},"_103":{"code":"Bern","name":"Bern","tax":"0.00000","SId":103},"_104":{"code":"Basel-Landschaft","name":"Basel-Landschaft","tax":"0.00000","SId":104},"_105":{"code":"Basel-Stadt","name":"Basel-Stadt","tax":"0.00000","SId":105},"_106":{"code":"Freiburg","name":"Freiburg","tax":"0.00000","SId":106},"_107":{"code":"Genf","name":"Genf","tax":"0.00000","SId":107},"_108":{"code":"Glarus","name":"Glarus","tax":"0.00000","SId":108},"_109":{"code":"Graubnden","name":"Graubnden","tax":"0.00000","SId":109},"_110":{"code":"Jura","name":"Jura","tax":"0.00000","SId":110},"_111":{"code":"Luzern","name":"Luzern","tax":"0.00000","SId":111},"_112":{"code":"Neuenburg","name":"Neuenburg","tax":"0.00000","SId":112},"_113":{"code":"Nidwalden","name":"Nidwalden","tax":"0.00000","SId":113},"_114":{"code":"Obwalden","name":"Obwalden","tax":"0.00000","SId":114},"_115":{"code":"StGallen","name":"St. Gallen","tax":"0.00000","SId":115},"_116":{"code":"Schaffhausen","name":"Schaffhausen","tax":"0.00000","SId":116},"_117":{"code":"Solothurn","name":"Solothurn","tax":"0.00000","SId":117},"_118":{"code":"Schwyz","name":"Schwyz","tax":"0.00000","SId":118},"_119":{"code":"Thurgau","name":"Thurgau","tax":"0.00000","SId":119},"_120":{"code":"Tessin","name":"Tessin","tax":"0.00000","SId":120},"_121":{"code":"Uri","name":"Uri","tax":"0.00000","SId":121},"_122":{"code":"Waadt","name":"Waadt","tax":"0.00000","SId":122},"_123":{"code":"Wallis","name":"Wallis","tax":"0.00000","SId":123},"_124":{"code":"Zug","name":"Zug","tax":"0.00000","SId":124},"_125":{"code":"Zrich","name":"Zrich","tax":"0.00000","SId":125}}},"224":{"name":"United Kingdom","code":"GB","tax":"0.00000","threshold":"0.00","phone_code":44,"address_format":0,"labels":{"zip":"邮政编码"},"provinces":[],"field":{"zip":{"verification":false,"example":"M34 4AB"}}},"226":{"name":"United States","code":"US","tax":"0.00000","threshold":"0.00","phone_code":1,"address_format":4,"labels":{"province":"州","zip":"邮政编码"},"provinces":{"_126":{"code":"AL","name":"Alabama","tax":"0.00000","SId":126},"_127":{"code":"AK","name":"Alaska","tax":"0.00000","SId":127},"_128":{"code":"AZ","name":"Arizona","tax":"0.00000","SId":128},"_129":{"code":"AR","name":"Arkansas","tax":"0.00000","SId":129},"_136":{"code":"CA","name":"California","tax":"0.00000","SId":136},"_137":{"code":"CO","name":"Colorado","tax":"0.00000","SId":137},"_138":{"code":"CT","name":"Connecticut","tax":"0.00000","SId":138},"_139":{"code":"DE","name":"Delaware","tax":"0.00000","SId":139},"_141":{"code":"FL","name":"Florida","tax":"0.00000","SId":141},"_142":{"code":"GA","name":"Georgia","tax":"0.00000","SId":142},"_143":{"code":"HI","name":"Hawaii","tax":"0.00000","SId":143},"_144":{"code":"ID","name":"Idaho","tax":"0.00000","SId":144},"_145":{"code":"IL","name":"Illinois","tax":"0.00000","SId":145},"_146":{"code":"IN","name":"Indiana","tax":"0.00000","SId":146},"_147":{"code":"IA","name":"Iowa","tax":"0.00000","SId":147},"_148":{"code":"KS","name":"Kansas","tax":"0.00000","SId":148},"_149":{"code":"KY","name":"Kentucky","tax":"0.00000","SId":149},"_150":{"code":"LA","name":"Louisiana","tax":"0.00000","SId":150},"_151":{"code":"ME","name":"Maine","tax":"0.00000","SId":151},"_152":{"code":"MD","name":"Maryland","tax":"0.00000","SId":152},"_153":{"code":"MA","name":"Massachusetts","tax":"0.00000","SId":153},"_154":{"code":"MI","name":"Michigan","tax":"0.00000","SId":154},"_155":{"code":"MN","name":"Minnesota","tax":"0.00000","SId":155},"_156":{"code":"MS","name":"Mississippi","tax":"0.00000","SId":156},"_157":{"code":"MO","name":"Missouri","tax":"0.00000","SId":157},"_158":{"code":"MT","name":"Montana","tax":"0.00000","SId":158},"_159":{"code":"NE","name":"Nebraska","tax":"0.00000","SId":159},"_160":{"code":"NV","name":"Nevada","tax":"0.00000","SId":160},"_161":{"code":"NH","name":"New Hampshire","tax":"0.00000","SId":161},"_162":{"code":"NJ","name":"New Jersey","tax":"0.00000","SId":162},"_163":{"code":"NM","name":"New Mexico","tax":"0.00000","SId":163},"_164":{"code":"NY","name":"New York","tax":"0.00000","SId":164},"_165":{"code":"NC","name":"North Carolina","tax":"0.00000","SId":165},"_166":{"code":"ND","name":"North Dakota","tax":"0.00000","SId":166},"_167":{"code":"OH","name":"Ohio","tax":"0.00000","SId":167},"_168":{"code":"OK","name":"Oklahoma","tax":"0.00000","SId":168},"_169":{"code":"OR","name":"Oregon","tax":"0.00000","SId":169},"_170":{"code":"PA","name":"Pennsylvania","tax":"0.00000","SId":170},"_172":{"code":"SC","name":"South Carolina","tax":"0.00000","SId":172},"_173":{"code":"SD","name":"South Dakota","tax":"0.00000","SId":173},"_174":{"code":"TN","name":"Tennessee","tax":"0.00000","SId":174},"_175":{"code":"TX","name":"Texas","tax":"0.00000","SId":175},"_176":{"code":"UT","name":"Utah","tax":"0.00000","SId":176},"_177":{"code":"VT","name":"Vermont","tax":"0.00000","SId":177},"_178":{"code":"VA","name":"Virginia","tax":"0.00000","SId":178},"_179":{"code":"WA","name":"Washington","tax":"0.00000","SId":179},"_180":{"code":"WV","name":"West Virginia","tax":"0.00000","SId":180},"_181":{"code":"WI","name":"Wisconsin","tax":"0.00000","SId":181},"_182":{"code":"WY","name":"Wyoming","tax":"0.00000","SId":182},"_202":{"code":"DC","name":"District of Columbia","tax":"0.00000","SId":202},"_203":{"code":"PR","name":"Puerto Rico","tax":"0.00000","SId":203},"_204":{"code":"RI","name":"Rhode Island","tax":"0.00000","SId":204},"_1030":{"code":"AS","name":"American Samoa","tax":"0.00000","SId":1030},"_1031":{"code":"FM","name":"Micronesia","tax":"0.00000","SId":1031},"_1032":{"code":"GU","name":"Guam","tax":"0.00000","SId":1032},"_1033":{"code":"MH","name":"Marshall Islands","tax":"0.00000","SId":1033},"_1034":{"code":"MP","name":"Northern Mariana Islands","tax":"0.00000","SId":1034},"_1035":{"code":"PW","name":"Palau","tax":"0.00000","SId":1035},"_1036":{"code":"VI","name":"U.S. Virgin Islands","tax":"0.00000","SId":1036},"_1037":{"code":"AA","name":"Armed Forces Americas","tax":"0.00000","SId":1037},"_1038":{"code":"AE","name":"Armed Forces Europe","tax":"0.00000","SId":1038},"_1039":{"code":"AP","name":"Armed Forces Pacific","tax":"0.00000","SId":1039}},"field":{"zip":{"verification":false,"example":"90001 / 90001-1234"}}},"241":{"name":"Aland Islands","code":"AX","tax":"0.00000","threshold":"0.00","phone_code":358,"address_format":1,"labels":[],"provinces":[]},"2":{"name":"Albania","code":"AL","tax":"0.00000","threshold":"0.00","phone_code":355,"address_format":0,"labels":[],"provinces":[]},"3":{"name":"Algeria","code":"DZ","tax":"0.00000","threshold":"0.00","phone_code":213,"address_format":1,"labels":[],"provinces":[]},"4":{"name":"American Samoa","code":"AS","tax":"0.00000","threshold":"0.00","phone_code":1684,"address_format":0,"labels":[],"provinces":[]},"5":{"name":"Andorra","code":"AD","tax":"0.00000","threshold":"0.00","phone_code":376,"address_format":1,"labels":[],"provinces":[]},"6":{"name":"Angola","code":"AO","tax":"0.00000","threshold":"0.00","phone_code":244,"address_format":2,"labels":[],"provinces":[]},"7":{"name":"Anguilla","code":"AI","tax":"0.00000","threshold":"0.00","phone_code":1264,"address_format":2,"labels":[],"provinces":[]},"9":{"name":"Antigua and Barbuda","code":"AG","tax":"0.00000","threshold":"0.00","phone_code":1268,"address_format":2,"labels":[],"provinces":[]},"10":{"name":"Argentina","code":"AR","tax":"0.00000","threshold":"0.00","phone_code":54,"address_format":3,"labels":{"province":"省"},"provinces":{"_276":{"code":"AUTONOMOUSCITYOFBUENOSAIRES","name":"Buenos Aires (Ciudad)","tax":"0.00000","SId":276},"_277":{"code":"BUENOSAIRES","name":"Buenos Aires (Provincia)","tax":"0.00000","SId":277},"_278":{"code":"CATAMARCA","name":"Catamarca","tax":"0.00000","SId":278},"_279":{"code":"CHACO","name":"Chaco","tax":"0.00000","SId":279},"_280":{"code":"CHUBUT","name":"Chubut","tax":"0.00000","SId":280},"_281":{"code":"CORRIENTES","name":"Corrientes","tax":"0.00000","SId":281},"_282":{"code":"CORDOVA","name":"Córdoba","tax":"0.00000","SId":282},"_286":{"code":"BETWEENRIVERS","name":"Entre Ríos","tax":"0.00000","SId":286},"_288":{"code":"FORMOSA","name":"Formosa","tax":"0.00000","SId":288},"_290":{"code":"JUJUY","name":"Jujuy","tax":"0.00000","SId":290},"_292":{"code":"LAPAMPA","name":"La Pampa","tax":"0.00000","SId":292},"_295":{"code":"LARIOJA","name":"La Rioja","tax":"0.00000","SId":295},"_297":{"code":"MENDOZA","name":"Mendoza","tax":"0.00000","SId":297},"_299":{"code":"MISIONES","name":"Misiones","tax":"0.00000","SId":299},"_301":{"code":"NEUQUEN","name":"Neuquén","tax":"0.00000","SId":301},"_303":{"code":"RIOONEGRO","name":"Río Negro","tax":"0.00000","SId":303},"_305":{"code":"SALTA","name":"Salta","tax":"0.00000","SId":305},"_307":{"code":"SANJUAN","name":"San Juan","tax":"0.00000","SId":307},"_309":{"code":"SANLUIS","name":"San Luis","tax":"0.00000","SId":309},"_312":{"code":"SANTACRUZ","name":"Santa Cruz","tax":"0.00000","SId":312},"_314":{"code":"SANTAFE","name":"Santa Fe","tax":"0.00000","SId":314},"_316":{"code":"SANTIAGODELESTERO","name":"Santiago del Estero","tax":"0.00000","SId":316},"_318":{"code":"TIERRADELFUEGO","name":"Tierra del Fuego","tax":"0.00000","SId":318},"_320":{"code":"TUCUMAN","name":"Tucumán","tax":"0.00000","SId":320}}},"11":{"name":"Armenia","code":"AM","tax":"0.00000","threshold":"0.00","phone_code":374,"address_format":1,"labels":[],"provinces":[]},"12":{"name":"Aruba","code":"AW","tax":"0.00000","threshold":"0.00","phone_code":297,"address_format":2,"labels":[],"provinces":[]},"253":{"name":"Ascension Island","code":"AC","tax":"0.00000","threshold":"0.00","phone_code":247,"address_format":0,"labels":[],"provinces":[]},"14":{"name":"Austria","code":"AT","tax":"0.00000","threshold":"0.00","phone_code":43,"address_format":3,"labels":{"address1":"街道和门牌号","address2":"另外地址"},"provinces":{"_10":{"code":"Wien","name":"Wien","tax":"0.00000","SId":10},"_11":{"code":"Niedersterreich","name":"Niedersterreich","tax":"0.00000","SId":11},"_12":{"code":"Oberosterreich","name":"Oberosterreich","tax":"0.00000","SId":12},"_13":{"code":"Salzburg","name":"Salzburg","tax":"0.00000","SId":13},"_14":{"code":"Karnten","name":"Karnten","tax":"0.00000","SId":14},"_15":{"code":"Steiermark","name":"Steiermark","tax":"0.00000","SId":15},"_17":{"code":"Burgenland","name":"Burgenland","tax":"0.00000","SId":17},"_18":{"code":"Voralberg","name":"Voralberg","tax":"0.00000","SId":18},"_1377":{"code":"Tirol","name":"Tirol","tax":"0.00000","SId":1377}}},"15":{"name":"Azerbaijan","code":"AZ","tax":"0.00000","threshold":"0.00","phone_code":994,"address_format":1,"labels":[],"provinces":[]},"16":{"name":"Bahamas","code":"BS","tax":"0.00000","threshold":"0.00","phone_code":1242,"address_format":2,"labels":[],"provinces":[]},"17":{"name":"Bahrain","code":"BH","tax":"0.00000","threshold":"0.00","phone_code":973,"address_format":0,"labels":[],"provinces":[]},"18":{"name":"Bangladesh","code":"BD","tax":"0.00000","threshold":"0.00","phone_code":880,"address_format":0,"labels":[],"provinces":[]},"19":{"name":"Barbados","code":"BB","tax":"0.00000","threshold":"0.00","phone_code":1246,"address_format":0,"labels":[],"provinces":[]},"20":{"name":"Belarus","code":"BY","tax":"0.00000","threshold":"0.00","phone_code":375,"address_format":1,"labels":[],"provinces":[]},"21":{"name":"Belgium","code":"BE","tax":"0.00000","threshold":"0.00","phone_code":32,"address_format":1,"labels":[],"provinces":[]},"22":{"name":"Belize","code":"BZ","tax":"0.00000","threshold":"0.00","phone_code":501,"address_format":2,"labels":[],"provinces":[]},"23":{"name":"Benin","code":"BJ","tax":"0.00000","threshold":"0.00","phone_code":229,"address_format":2,"labels":[],"provinces":[]},"24":{"name":"Bermuda","code":"BM","tax":"0.00000","threshold":"0.00","phone_code":1441,"address_format":0,"labels":[],"provinces":[]},"25":{"name":"Bhutan","code":"BT","tax":"0.00000","threshold":"0.00","phone_code":975,"address_format":0,"labels":[],"provinces":[]},"26":{"name":"Bolivia","code":"BO","tax":"0.00000","threshold":"0.00","phone_code":591,"address_format":2,"labels":[],"provinces":[]},"27":{"name":"Bosnia and Herzegovina","code":"BA","tax":"0.00000","threshold":"0.00","phone_code":387,"address_format":1,"labels":[],"provinces":[]},"28":{"name":"Botswana","code":"BW","tax":"0.00000","threshold":"0.00","phone_code":267,"address_format":0,"labels":[],"provinces":[]},"29":{"name":"Bouvet Island","code":"BV","tax":"0.00000","threshold":"0.00","phone_code":0,"address_format":0,"labels":[],"provinces":[]},"31":{"name":"British Indian Ocean Territory","code":"IO","tax":"0.00000","threshold":"0.00","phone_code":246,"address_format":0,"labels":[],"provinces":[]},"247":{"name":"British Virgin Islands","code":"VG","tax":"0.00000","threshold":"0.00","phone_code":1284,"address_format":0,"labels":[],"provinces":[]},"32":{"name":"Brunei Darussalam","code":"BN","tax":"0.00000","threshold":"0.00","phone_code":673,"address_format":0,"labels":[],"provinces":[]},"33":{"name":"Bulgaria","code":"BG","tax":"0.00000","threshold":"0.00","phone_code":359,"address_format":1,"labels":[],"provinces":[]},"34":{"name":"Burkina Faso","code":"BF","tax":"0.00000","threshold":"0.00","phone_code":226,"address_format":2,"labels":[],"provinces":[]},"35":{"name":"Burundi","code":"BI","tax":"0.00000","threshold":"0.00","phone_code":257,"address_format":0,"labels":[],"provinces":[]},"36":{"name":"Cambodia","code":"KH","tax":"0.00000","threshold":"0.00","phone_code":855,"address_format":0,"labels":[],"provinces":[]},"37":{"name":"Cameroon","code":"CM","tax":"0.00000","threshold":"0.00","phone_code":237,"address_format":0,"labels":[],"provinces":[]},"39":{"name":"Cape Verde","code":"CV","tax":"0.00000","threshold":"0.00","phone_code":238,"address_format":1,"labels":[],"provinces":[]},"244":{"name":"Caribbean Netherlands","code":"BQ","tax":"0.00000","threshold":"0.00","phone_code":599,"address_format":0,"labels":[],"provinces":[]},"40":{"name":"Cayman Islands","code":"KY","tax":"0.00000","threshold":"0.00","phone_code":1345,"address_format":0,"labels":[],"provinces":[]},"41":{"name":"Central African Republic","code":"CF","tax":"0.00000","threshold":"0.00","phone_code":236,"address_format":0,"labels":[],"provinces":[]},"42":{"name":"Chad","code":"TD","tax":"0.00000","threshold":"0.00","phone_code":235,"address_format":2,"labels":[],"provinces":[]},"43":{"name":"Chile","code":"CL","tax":"0.00000","threshold":"0.00","phone_code":56,"address_format":5,"labels":{"taxId":"唯一纳税人别号"},"provinces":{"_953":{"code":"AP","name":"Arica y Parinacota","tax":"0.00000","SId":953},"_954":{"code":"TA","name":"Tarapacá","tax":"0.00000","SId":954},"_955":{"code":"AN","name":"Antofagasta","tax":"0.00000","SId":955},"_956":{"code":"AT","name":"Atacama","tax":"0.00000","SId":956},"_957":{"code":"CO","name":"Coquimbo","tax":"0.00000","SId":957},"_958":{"code":"VS","name":"Valparaíso","tax":"0.00000","SId":958},"_959":{"code":"RM","name":"Santiago Metropolitan","tax":"0.00000","SId":959},"_960":{"code":"LI","name":"Libertador General Bernardo O’Higgins","tax":"0.00000","SId":960},"_961":{"code":"ML","name":"Maule","tax":"0.00000","SId":961},"_962":{"code":"NB","name":"Ñuble","tax":"0.00000","SId":962},"_963":{"code":"BI","name":"Bío Bío","tax":"0.00000","SId":963},"_964":{"code":"AR","name":"Araucanía","tax":"0.00000","SId":964},"_965":{"code":"LR","name":"Los Ríos","tax":"0.00000","SId":965},"_966":{"code":"LL","name":"Los Lagos","tax":"0.00000","SId":966},"_967":{"code":"AI","name":"Aysén","tax":"0.00000","SId":967},"_968":{"code":"MA","name":"Magallanes Region","tax":"0.00000","SId":968}}},"44":{"name":"China","code":"CN","tax":"0.00000","threshold":"0.00","phone_code":86,"address_format":4,"labels":{"address1":"完整地址","province":"省"},"provinces":{"_198":{"code":"GD","name":"Guangdong","tax":"0.00000","SId":198},"_199":{"code":"SH","name":"Shanghai","tax":"0.00000","SId":199},"_201":{"code":"BJ","name":"Beijing","tax":"0.00000","SId":201},"_593":{"code":"YN","name":"Yunnan","tax":"0.00000","SId":593},"_594":{"code":"NM","name":"Inner Mongolia","tax":"0.00000","SId":594},"_595":{"code":"QH","name":"Qinghai","tax":"0.00000","SId":595},"_596":{"code":"JL","name":"Jilin","tax":"0.00000","SId":596},"_597":{"code":"SC","name":"Sichuan","tax":"0.00000","SId":597},"_598":{"code":"TJ","name":"Tianjin","tax":"0.00000","SId":598},"_599":{"code":"NX","name":"Ningxia","tax":"0.00000","SId":599},"_600":{"code":"AH","name":"Anhui","tax":"0.00000","SId":600},"_601":{"code":"SD","name":"Shandong","tax":"0.00000","SId":601},"_602":{"code":"SX","name":"Shanxi","tax":"0.00000","SId":602},"_603":{"code":"HL","name":"Heilongjiang","tax":"0.00000","SId":603},"_604":{"code":"GX","name":"Guangxi","tax":"0.00000","SId":604},"_605":{"code":"XJ","name":"Xinjiang","tax":"0.00000","SId":605},"_606":{"code":"JS","name":"Jiangsu","tax":"0.00000","SId":606},"_607":{"code":"JX","name":"Jiangxi","tax":"0.00000","SId":607},"_608":{"code":"HE","name":"Hebei","tax":"0.00000","SId":608},"_609":{"code":"HA","name":"Henan","tax":"0.00000","SId":609},"_610":{"code":"ZJ","name":"Zhejiang","tax":"0.00000","SId":610},"_611":{"code":"HI","name":"Hainan","tax":"0.00000","SId":611},"_612":{"code":"HB","name":"Hubei","tax":"0.00000","SId":612},"_613":{"code":"HN","name":"Hunan","tax":"0.00000","SId":613},"_614":{"code":"GS","name":"Gansu","tax":"0.00000","SId":614},"_615":{"code":"FJ","name":"Fujian","tax":"0.00000","SId":615},"_616":{"code":"YZ","name":"Tibet","tax":"0.00000","SId":616},"_617":{"code":"GZ","name":"Guizhou","tax":"0.00000","SId":617},"_618":{"code":"LN","name":"Liaoning","tax":"0.00000","SId":618},"_619":{"code":"CQ","name":"Chongqing","tax":"0.00000","SId":619},"_1378":{"code":"SN","name":"Shaanxi","tax":"0.00000","SId":1378}}},"45":{"name":"Christmas Island","code":"CX","tax":"0.00000","threshold":"0.00","phone_code":61,"address_format":0,"labels":[],"provinces":[]},"46":{"name":"Cocos Islands","code":"CC","tax":"0.00000","threshold":"0.00","phone_code":672,"address_format":0,"labels":[],"provinces":[]},"47":{"name":"Colombia","code":"CO","tax":"0.00000","threshold":"0.00","phone_code":57,"address_format":4,"labels":{"province":"省"},"provinces":{"_921":{"code":"DC","name":"Capital District","tax":"0.00000","SId":921},"_922":{"code":"AMA","name":"Amazonas","tax":"0.00000","SId":922},"_923":{"code":"ANT","name":"Antioquia","tax":"0.00000","SId":923},"_924":{"code":"ARA","name":"Arauca","tax":"0.00000","SId":924},"_925":{"code":"ATL","name":"Atlántico","tax":"0.00000","SId":925},"_926":{"code":"BOL","name":"Bolívar","tax":"0.00000","SId":926},"_927":{"code":"BOY","name":"Boyacá","tax":"0.00000","SId":927},"_928":{"code":"CAL","name":"Caldas","tax":"0.00000","SId":928},"_929":{"code":"CAQ","name":"Caquetá","tax":"0.00000","SId":929},"_930":{"code":"CAS","name":"Casanare","tax":"0.00000","SId":930},"_931":{"code":"CAU","name":"Cauca","tax":"0.00000","SId":931},"_932":{"code":"CES","name":"Cesar","tax":"0.00000","SId":932},"_933":{"code":"CHO","name":"Chocó","tax":"0.00000","SId":933},"_934":{"code":"CUN","name":"Córdoba","tax":"0.00000","SId":934},"_935":{"code":"GUA","name":"Cundinamarca","tax":"0.00000","SId":935},"_936":{"code":"GUV","name":"Guaviare","tax":"0.00000","SId":936},"_937":{"code":"HUI","name":"Huila","tax":"0.00000","SId":937},"_938":{"code":"LAG","name":"La Guajira","tax":"0.00000","SId":938},"_939":{"code":"MAG","name":"Magdalena","tax":"0.00000","SId":939},"_940":{"code":"MET","name":"Meta","tax":"0.00000","SId":940},"_941":{"code":"NAR","name":"Nariño","tax":"0.00000","SId":941},"_942":{"code":"NSA","name":"Norte de Santander","tax":"0.00000","SId":942},"_943":{"code":"PUT","name":"Putumayo","tax":"0.00000","SId":943},"_944":{"code":"QUI","name":"Quindío","tax":"0.00000","SId":944},"_945":{"code":"RIS","name":"Risaralda","tax":"0.00000","SId":945},"_946":{"code":"SAP","name":"San Andrés &amp; Providencia","tax":"0.00000","SId":946},"_947":{"code":"SAN","name":"Santander","tax":"0.00000","SId":947},"_948":{"code":"SUC","name":"Sucre","tax":"0.00000","SId":948},"_949":{"code":"TOL","name":"Tolima","tax":"0.00000","SId":949},"_950":{"code":"VAC","name":"Valle del Cauca","tax":"0.00000","SId":950},"_951":{"code":"VAU","name":"Vaupés","tax":"0.00000","SId":951},"_952":{"code":"VID","name":"Vichada","tax":"0.00000","SId":952}}},"48":{"name":"Comoros","code":"KM","tax":"0.00000","threshold":"0.00","phone_code":269,"address_format":0,"labels":[],"provinces":[]},"49":{"name":"Congo","code":"CG","tax":"0.00000","threshold":"0.00","phone_code":242,"address_format":0,"labels":[],"provinces":[]},"51":{"name":"Cook Islands","code":"CK","tax":"0.00000","threshold":"0.00","phone_code":682,"address_format":0,"labels":[],"provinces":[]},"52":{"name":"Costa Rica","code":"CR","tax":"0.00000","threshold":"0.00","phone_code":506,"address_format":0,"labels":[],"provinces":[]},"53":{"name":"Cote D&#039;ivoire","code":"CI","tax":"0.00000","threshold":"0.00","phone_code":225,"address_format":0,"labels":[],"provinces":[]},"55":{"name":"Cuba","code":"CU","tax":"0.00000","threshold":"0.00","phone_code":53,"address_format":1,"labels":[],"provinces":[]},"251":{"name":"Curaçao","code":"CW","tax":"0.00000","threshold":"0.00","phone_code":599,"address_format":2,"labels":[],"provinces":[]},"56":{"name":"Cyprus","code":"CY","tax":"0.00000","threshold":"0.00","phone_code":357,"address_format":1,"labels":[],"provinces":[]},"57":{"name":"Czech Republic","code":"CZ","tax":"0.00000","threshold":"0.00","phone_code":420,"address_format":1,"labels":[],"provinces":[]},"114":{"name":"Democratic People&#039;s Republic of Korea","code":"KP","tax":"0.00000","threshold":"0.00","phone_code":850,"address_format":0,"labels":[],"provinces":[]},"50":{"name":"Democratic Republic of the Congo","code":"CD","tax":"0.00000","threshold":"0.00","phone_code":243,"address_format":0,"labels":[],"provinces":[]},"58":{"name":"Denmark","code":"DK","tax":"0.00000","threshold":"0.00","phone_code":45,"address_format":1,"labels":{"address1":"街道和门牌号"},"provinces":[]},"59":{"name":"Djibouti","code":"DJ","tax":"0.00000","threshold":"0.00","phone_code":253,"address_format":2,"labels":[],"provinces":[]},"62":{"name":"East Timor","code":"TL","tax":"0.00000","threshold":"0.00","phone_code":670,"address_format":0,"labels":[],"provinces":[]},"63":{"name":"Ecuador","code":"EC","tax":"0.00000","threshold":"0.00","phone_code":593,"address_format":1,"labels":[],"provinces":[]},"64":{"name":"Egypt","code":"EG","tax":"0.00000","threshold":"0.00","phone_code":20,"address_format":4,"labels":{"province":"省"},"provinces":{"_767":{"code":"SHR","name":"Al Sharqia","tax":"0.00000","SId":767},"_768":{"code":"ALX","name":"Alexandria","tax":"0.00000","SId":768},"_769":{"code":"ASN","name":"Aswan","tax":"0.00000","SId":769},"_770":{"code":"AST","name":"Asyut","tax":"0.00000","SId":770},"_771":{"code":"BH","name":"Beheira","tax":"0.00000","SId":771},"_772":{"code":"BNS","name":"Beni Suef","tax":"0.00000","SId":772},"_773":{"code":"C","name":"Cairo","tax":"0.00000","SId":773},"_774":{"code":"DK","name":"Dakahlia","tax":"0.00000","SId":774},"_775":{"code":"DT","name":"Damietta","tax":"0.00000","SId":775},"_776":{"code":"FYM","name":"Faiyum","tax":"0.00000","SId":776},"_777":{"code":"GH","name":"Gharbia","tax":"0.00000","SId":777},"_778":{"code":"GZ","name":"Giza","tax":"0.00000","SId":778},"_779":{"code":"HU","name":"Helwan","tax":"0.00000","SId":779},"_780":{"code":"IS","name":"Ismailia","tax":"0.00000","SId":780},"_781":{"code":"KFS","name":"Kafr el-Sheikh","tax":"0.00000","SId":781},"_782":{"code":"LX","name":"Luxor","tax":"0.00000","SId":782},"_783":{"code":"MT","name":"Matrouh","tax":"0.00000","SId":783},"_784":{"code":"MN","name":"Minya","tax":"0.00000","SId":784},"_785":{"code":"MNF","name":"Monufia","tax":"0.00000","SId":785},"_786":{"code":"WAD","name":"New Valley","tax":"0.00000","SId":786},"_787":{"code":"SIN","name":"North Sinai","tax":"0.00000","SId":787},"_788":{"code":"PTS","name":"Port Said","tax":"0.00000","SId":788},"_789":{"code":"KB","name":"Qalyubia","tax":"0.00000","SId":789},"_790":{"code":"KN","name":"Qena","tax":"0.00000","SId":790},"_791":{"code":"BA","name":"Red Sea","tax":"0.00000","SId":791},"_792":{"code":"SHG","name":"Sohag","tax":"0.00000","SId":792},"_793":{"code":"JS","name":"South Sinai","tax":"0.00000","SId":793},"_794":{"code":"SUZ","name":"Suez","tax":"0.00000","SId":794}}},"65":{"name":"El Salvador","code":"SV","tax":"0.00000","threshold":"0.00","phone_code":503,"address_format":0,"labels":[],"provinces":[]},"66":{"name":"Equatorial Guinea","code":"GQ","tax":"0.00000","threshold":"0.00","phone_code":240,"address_format":0,"labels":[],"provinces":[]},"67":{"name":"Eritrea","code":"ER","tax":"0.00000","threshold":"0.00","phone_code":291,"address_format":0,"labels":[],"provinces":[]},"68":{"name":"Estonia","code":"EE","tax":"0.00000","threshold":"0.00","phone_code":372,"address_format":1,"labels":[],"provinces":[]},"69":{"name":"Ethiopia","code":"ET","tax":"0.00000","threshold":"0.00","phone_code":251,"address_format":0,"labels":[],"provinces":[]},"70":{"name":"Falkland Islands","code":"FK","tax":"0.00000","threshold":"0.00","phone_code":500,"address_format":0,"labels":[],"provinces":[]},"71":{"name":"Faroe Islands","code":"FO","tax":"0.00000","threshold":"0.00","phone_code":298,"address_format":1,"labels":[],"provinces":[]},"72":{"name":"Fiji","code":"FJ","tax":"0.00000","threshold":"0.00","phone_code":679,"address_format":2,"labels":[],"provinces":[]},"73":{"name":"Finland","code":"FI","tax":"0.00000","threshold":"0.00","phone_code":358,"address_format":1,"labels":[],"provinces":[]},"75":{"name":"French Guiana","code":"GF","tax":"0.00000","threshold":"0.00","phone_code":594,"address_format":1,"labels":[],"provinces":[]},"76":{"name":"French Polynesia","code":"PF","tax":"0.00000","threshold":"0.00","phone_code":689,"address_format":1,"labels":[],"provinces":[]},"77":{"name":"French Southern Territories","code":"TF","tax":"0.00000","threshold":"0.00","phone_code":0,"address_format":0,"labels":[],"provinces":[]},"78":{"name":"Gabon","code":"GA","tax":"0.00000","threshold":"0.00","phone_code":241,"address_format":0,"labels":[],"provinces":[]},"79":{"name":"Gambia","code":"GM","tax":"0.00000","threshold":"0.00","phone_code":220,"address_format":0,"labels":[],"provinces":[]},"80":{"name":"Georgia","code":"GE","tax":"0.00000","threshold":"0.00","phone_code":995,"address_format":1,"labels":[],"provinces":[]},"82":{"name":"Ghana","code":"GH","tax":"0.00000","threshold":"0.00","phone_code":233,"address_format":2,"labels":[],"provinces":[]},"83":{"name":"Gibraltar","code":"GI","tax":"0.00000","threshold":"0.00","phone_code":350,"address_format":7,"labels":[],"provinces":[]},"84":{"name":"Greece","code":"GR","tax":"0.00000","threshold":"0.00","phone_code":30,"address_format":1,"labels":[],"provinces":[]},"85":{"name":"Greenland","code":"GL","tax":"0.00000","threshold":"0.00","phone_code":299,"address_format":1,"labels":[],"provinces":[]},"86":{"name":"Grenada","code":"GD","tax":"0.00000","threshold":"0.00","phone_code":1473,"address_format":0,"labels":[],"provinces":[]},"87":{"name":"Guadeloupe","code":"GP","tax":"0.00000","threshold":"0.00","phone_code":0,"address_format":1,"labels":[],"provinces":[]},"88":{"name":"Guam","code":"GU","tax":"0.00000","threshold":"0.00","phone_code":1671,"address_format":0,"labels":[],"provinces":[]},"89":{"name":"Guatemala","code":"GT","tax":"0.00000","threshold":"0.00","phone_code":502,"address_format":4,"labels":[],"provinces":{"_995":{"code":"AVE","name":"Alta Verapaz","tax":"0.00000","SId":995},"_996":{"code":"BVE","name":"Baja Verapaz","tax":"0.00000","SId":996},"_997":{"code":"CMT","name":"Chimaltenango","tax":"0.00000","SId":997},"_998":{"code":"CQM","name":"Chiquimula","tax":"0.00000","SId":998},"_999":{"code":"EPR","name":"El Progreso","tax":"0.00000","SId":999},"_1000":{"code":"ESC","name":"Escuintla","tax":"0.00000","SId":1000},"_1001":{"code":"GUA","name":"Guatemala","tax":"0.00000","SId":1001},"_1002":{"code":"HUE","name":"Huehuetenango","tax":"0.00000","SId":1002},"_1003":{"code":"IZA","name":"Izabal","tax":"0.00000","SId":1003},"_1004":{"code":"JAL","name":"Jalapa","tax":"0.00000","SId":1004},"_1005":{"code":"JUT","name":"Jutiapa","tax":"0.00000","SId":1005},"_1006":{"code":"PET","name":"Petén","tax":"0.00000","SId":1006},"_1007":{"code":"QUE","name":"Quetzaltenango","tax":"0.00000","SId":1007},"_1008":{"code":"QUI","name":"Quiché","tax":"0.00000","SId":1008},"_1009":{"code":"RET","name":"Retalhuleu","tax":"0.00000","SId":1009},"_1010":{"code":"SAC","name":"Sacatepéquez","tax":"0.00000","SId":1010},"_1011":{"code":"SMA","name":"San Marcos","tax":"0.00000","SId":1011},"_1012":{"code":"SRO","name":"Santa Rosa","tax":"0.00000","SId":1012},"_1013":{"code":"SOL","name":"Sololá","tax":"0.00000","SId":1013},"_1014":{"code":"SUC","name":"Suchitepéquez","tax":"0.00000","SId":1014},"_1015":{"code":"TOT","name":"Totonicapán","tax":"0.00000","SId":1015},"_1016":{"code":"ZAC","name":"Zacapa","tax":"0.00000","SId":1016}}},"248":{"name":"Guernsey","code":"GG","tax":"0.00000","threshold":"0.00","phone_code":44,"address_format":0,"labels":[],"provinces":[]},"90":{"name":"Guinea","code":"GN","tax":"0.00000","threshold":"0.00","phone_code":224,"address_format":0,"labels":[],"provinces":[]},"91":{"name":"Guinea-Bissau","code":"GW","tax":"0.00000","threshold":"0.00","phone_code":245,"address_format":1,"labels":[],"provinces":[]},"92":{"name":"Guyana","code":"GY","tax":"0.00000","threshold":"0.00","phone_code":592,"address_format":0,"labels":[],"provinces":[]},"93":{"name":"Haiti","code":"HT","tax":"0.00000","threshold":"0.00","phone_code":509,"address_format":1,"labels":[],"provinces":[]},"94":{"name":"Heard Island and Mcdonald Islands","code":"HM","tax":"0.00000","threshold":"0.00","phone_code":0,"address_format":0,"labels":[],"provinces":[]},"96":{"name":"Honduras","code":"HN","tax":"0.00000","threshold":"0.00","phone_code":504,"address_format":1,"labels":[],"provinces":[]},"97":{"name":"Hong Kong, China","code":"HK","tax":"0.00000","threshold":"0.00","phone_code":852,"address_format":9,"labels":{"city":"区"},"provinces":{"_1138":{"code":"HK","name":"Hong Kong Island","tax":"0.00000","SId":1138},"_1139":{"code":"KL","name":"Kowloon","tax":"0.00000","SId":1139},"_1140":{"code":"NT","name":"New Territories","tax":"0.00000","SId":1140}}},"98":{"name":"Hungary","code":"HU","tax":"0.00000","threshold":"0.00","phone_code":36,"address_format":0,"labels":[],"provinces":[]},"99":{"name":"Iceland","code":"IS","tax":"0.00000","threshold":"0.00","phone_code":354,"address_format":1,"labels":[],"provinces":[]},"100":{"name":"India","code":"IN","tax":"0.00000","threshold":"0.00","phone_code":91,"address_format":4,"labels":{"province":"州","zip":"PIN码"},"provinces":{"_485":{"code":"ANDAMANANDNICOBARISLANDS","name":"Andaman and Nicobar Islands","tax":"0.00000","SId":485},"_486":{"code":"ANDHRAPRADESH","name":"Andhra Pradesh","tax":"0.00000","SId":486},"_487":{"code":"APO","name":"Army Post Office","tax":"0.00000","SId":487},"_488":{"code":"ARUNACHALPRADESH","name":"Arunachal Pradesh","tax":"0.00000","SId":488},"_489":{"code":"ASSAM","name":"Assam","tax":"0.00000","SId":489},"_490":{"code":"BIHAR","name":"Bihar","tax":"0.00000","SId":490},"_491":{"code":"CHANDIGARH","name":"Chandigarh","tax":"0.00000","SId":491},"_492":{"code":"CHHATTISGARH","name":"Chhattisgarh","tax":"0.00000","SId":492},"_493":{"code":"DADRAANDNAGARHAVELI","name":"Dadra and Nagar Haveli","tax":"0.00000","SId":493},"_494":{"code":"DAMANANDDIU","name":"Daman and Diu","tax":"0.00000","SId":494},"_495":{"code":"DELHI","name":"Delhi","tax":"0.00000","SId":495},"_496":{"code":"GOA","name":"Goa","tax":"0.00000","SId":496},"_497":{"code":"GUJARAT","name":"Gujarat","tax":"0.00000","SId":497},"_499":{"code":"HARYANA","name":"Haryana","tax":"0.00000","SId":499},"_500":{"code":"HIMACHALPRADESH","name":"Himachal Pradesh","tax":"0.00000","SId":500},"_501":{"code":"JAMMUANDKASHMIR","name":"Jammu and Kashmir","tax":"0.00000","SId":501},"_502":{"code":"JHARKHAND","name":"Jharkhand","tax":"0.00000","SId":502},"_503":{"code":"KARNATAKA","name":"Karnataka","tax":"0.00000","SId":503},"_504":{"code":"KERALA","name":"Kerala","tax":"0.00000","SId":504},"_505":{"code":"LAKSHADWEEP","name":"Lakshadweep","tax":"0.00000","SId":505},"_506":{"code":"MADHYAPRADESH","name":"Madhya Pradesh","tax":"0.00000","SId":506},"_507":{"code":"MAHARASHTRA","name":"Maharashtra","tax":"0.00000","SId":507},"_509":{"code":"MANIPUR","name":"Manipur","tax":"0.00000","SId":509},"_510":{"code":"MEGHALAYA","name":"Meghalaya","tax":"0.00000","SId":510},"_512":{"code":"MIZORAM","name":"Mizoram","tax":"0.00000","SId":512},"_513":{"code":"NAGALAND","name":"Nagaland","tax":"0.00000","SId":513},"_515":{"code":"ODISHA","name":"Odisha","tax":"0.00000","SId":515},"_517":{"code":"PUDUCHERRY","name":"Puducherry","tax":"0.00000","SId":517},"_519":{"code":"PUNJAB","name":"Punjab","tax":"0.00000","SId":519},"_520":{"code":"RAJASTHAN","name":"Rajasthan","tax":"0.00000","SId":520},"_522":{"code":"SIKKIM","name":"Sikkim","tax":"0.00000","SId":522},"_524":{"code":"TAMILNADU","name":"Tamil Nadu","tax":"0.00000","SId":524},"_525":{"code":"TELANGANA","name":"Telangana","tax":"0.00000","SId":525},"_527":{"code":"TRIPURA","name":"Tripura","tax":"0.00000","SId":527},"_528":{"code":"UTTARPRADESH","name":"Uttar Pradesh","tax":"0.00000","SId":528},"_530":{"code":"UTTARAKHAND","name":"Uttarakhand","tax":"0.00000","SId":530},"_531":{"code":"WESTBENGAL","name":"West Bengal","tax":"0.00000","SId":531}}},"101":{"name":"Indonesia","code":"ID","tax":"0.00000","threshold":"0.00","phone_code":62,"address_format":10,"labels":{"province":"省"},"provinces":{"_333":{"code":"ID-BA","name":"Bali","tax":"0.00000","SId":333},"_338":{"code":"ID-BB","name":"Bangka Belitung","tax":"0.00000","SId":338},"_346":{"code":"ID-BT","name":"Banten","tax":"0.00000","SId":346},"_351":{"code":"ID-BE","name":"Bengkulu","tax":"0.00000","SId":351},"_355":{"code":"ID-YO","name":"DI Yogyakarta","tax":"0.00000","SId":355},"_360":{"code":"ID-JK","name":"DKI Jakarta","tax":"0.00000","SId":360},"_363":{"code":"ID-GO","name":"Gorontalo","tax":"0.00000","SId":363},"_366":{"code":"ID-JA","name":"Jambi","tax":"0.00000","SId":366},"_387":{"code":"ID-JB","name":"Jawa Barat","tax":"0.00000","SId":387},"_391":{"code":"ID-JT","name":"Jawa Tengah","tax":"0.00000","SId":391},"_396":{"code":"ID-JI","name":"Jawa Timur","tax":"0.00000","SId":396},"_400":{"code":"ID-KB","name":"Kalimantan Barat","tax":"0.00000","SId":400},"_403":{"code":"ID-KS","name":"Kalimantan Selatan","tax":"0.00000","SId":403},"_404":{"code":"ID-KT","name":"Kalimantan Tengah","tax":"0.00000","SId":404},"_409":{"code":"ID-KI","name":"Kalimantan Timur","tax":"0.00000","SId":409},"_410":{"code":"ID-KU","name":"Kalimantan Utara","tax":"0.00000","SId":410},"_416":{"code":"ID-KR","name":"Kepulauan Riau","tax":"0.00000","SId":416},"_418":{"code":"ID-LA","name":"Lampung","tax":"0.00000","SId":418},"_421":{"code":"ID-MA","name":"Maluku","tax":"0.00000","SId":421},"_423":{"code":"ID-MU","name":"Maluku Utara","tax":"0.00000","SId":423},"_426":{"code":"ID-AC","name":"Nanggroe Aceh Darussalam","tax":"0.00000","SId":426},"_429":{"code":"ID-NB","name":"Nusa Tenggara Barat","tax":"0.00000","SId":429},"_432":{"code":"ID-NT","name":"Nusa Tenggara Timur","tax":"0.00000","SId":432},"_434":{"code":"ID-PA","name":"Papua","tax":"0.00000","SId":434},"_436":{"code":"ID-PB","name":"Papua Barat","tax":"0.00000","SId":436},"_438":{"code":"ID-RI","name":"Riau","tax":"0.00000","SId":438},"_440":{"code":"ID-SR","name":"Sulawesi Barat","tax":"0.00000","SId":440},"_442":{"code":"ID-SN","name":"Sulawesi Selatan","tax":"0.00000","SId":442},"_444":{"code":"ID-ST","name":"Sulawesi Tengah","tax":"0.00000","SId":444},"_446":{"code":"ID-SG","name":"Sulawesi Tenggara","tax":"0.00000","SId":446},"_447":{"code":"ID-SA","name":"Sulawesi Utara","tax":"0.00000","SId":447},"_448":{"code":"ID-SB","name":"Sumatera Barat","tax":"0.00000","SId":448},"_449":{"code":"ID-SS","name":"Sumatera Selatan","tax":"0.00000","SId":449},"_451":{"code":"ID-SU","name":"Sumatera Utara","tax":"0.00000","SId":451}}},"102":{"name":"Iran","code":"IR","tax":"0.00000","threshold":"0.00","phone_code":98,"address_format":1,"labels":[],"provinces":[]},"103":{"name":"Iraq","code":"IQ","tax":"0.00000","threshold":"0.00","phone_code":964,"address_format":0,"labels":[],"provinces":[]},"104":{"name":"Ireland","code":"IE","tax":"0.00000","threshold":"0.00","phone_code":353,"address_format":4,"labels":{"province":"县"},"provinces":{"_833":{"code":"CW","name":"Carlow","tax":"0.00000","SId":833},"_834":{"code":"CN","name":"Cavan","tax":"0.00000","SId":834},"_835":{"code":"CE","name":"Clare","tax":"0.00000","SId":835},"_836":{"code":"CO","name":"Cork","tax":"0.00000","SId":836},"_837":{"code":"DL","name":"Donegal","tax":"0.00000","SId":837},"_838":{"code":"D","name":"Dublin","tax":"0.00000","SId":838},"_839":{"code":"G","name":"Galway","tax":"0.00000","SId":839},"_840":{"code":"KY","name":"Kerry","tax":"0.00000","SId":840},"_841":{"code":"KE","name":"Kildare","tax":"0.00000","SId":841},"_842":{"code":"KK","name":"Kilkenny","tax":"0.00000","SId":842},"_843":{"code":"LS","name":"Laois","tax":"0.00000","SId":843},"_844":{"code":"LM","name":"Leitrim","tax":"0.00000","SId":844},"_845":{"code":"LK","name":"Limerick","tax":"0.00000","SId":845},"_846":{"code":"LD","name":"Longford","tax":"0.00000","SId":846},"_847":{"code":"LH","name":"Louth","tax":"0.00000","SId":847},"_848":{"code":"MO","name":"Mayo","tax":"0.00000","SId":848},"_849":{"code":"MH","name":"Meath","tax":"0.00000","SId":849},"_850":{"code":"MN","name":"Monaghan","tax":"0.00000","SId":850},"_851":{"code":"OY","name":"Offaly","tax":"0.00000","SId":851},"_852":{"code":"RN","name":"Roscommon","tax":"0.00000","SId":852},"_853":{"code":"SO","name":"Sligo","tax":"0.00000","SId":853},"_854":{"code":"TA","name":"Tipperary","tax":"0.00000","SId":854},"_855":{"code":"WD","name":"Waterford","tax":"0.00000","SId":855},"_856":{"code":"WH","name":"Westmeath","tax":"0.00000","SId":856},"_857":{"code":"WX","name":"Wexford","tax":"0.00000","SId":857},"_858":{"code":"WW","name":"Wicklow","tax":"0.00000","SId":858}}},"252":{"name":"Isle of Man","code":"IM","tax":"0.00000","threshold":"0.00","phone_code":44,"address_format":0,"labels":[],"provinces":[]},"105":{"name":"Israel","code":"IL","tax":"0.00000","threshold":"0.00","phone_code":972,"address_format":1,"labels":[],"provinces":[]},"107":{"name":"Jamaica","code":"JM","tax":"0.00000","threshold":"0.00","phone_code":1876,"address_format":2,"labels":[],"provinces":[]},"108":{"name":"Japan","code":"JP","tax":"0.00000","threshold":"0.00","phone_code":81,"address_format":12,"labels":{"city":"市/区/镇/村","province":"州"},"provinces":{"_205":{"code":"AICHI-KEN","name":"Aichi","tax":"0.00000","SId":205},"_206":{"code":"AKITA-KEN","name":"Akita","tax":"0.00000","SId":206},"_207":{"code":"AOMORI-KEN","name":"Aomori","tax":"0.00000","SId":207},"_208":{"code":"CHIBA-KEN","name":"Chiba","tax":"0.00000","SId":208},"_209":{"code":"EHIME-KEN","name":"Ehime","tax":"0.00000","SId":209},"_210":{"code":"FUKUI-KEN","name":"Fukui","tax":"0.00000","SId":210},"_211":{"code":"FUKUOKA-KEN","name":"Fukuoka","tax":"0.00000","SId":211},"_212":{"code":"FUKUSHIMA-KEN","name":"Fukushima","tax":"0.00000","SId":212},"_213":{"code":"GIFU-KEN","name":"Gifu","tax":"0.00000","SId":213},"_216":{"code":"GUNMA-KEN","name":"Gunma","tax":"0.00000","SId":216},"_218":{"code":"HIROSHIMA-KEN","name":"Hiroshima","tax":"0.00000","SId":218},"_221":{"code":"HOKKAIDO","name":"Hokkaido","tax":"0.00000","SId":221},"_224":{"code":"HYOGO-KEN","name":"Hyogo","tax":"0.00000","SId":224},"_228":{"code":"IBARAKI-KEN","name":"Ibaraki","tax":"0.00000","SId":228},"_229":{"code":"ISHIKAWA-KEN","name":"Ishikawa","tax":"0.00000","SId":229},"_230":{"code":"IWATE-KEN","name":"Iwate","tax":"0.00000","SId":230},"_232":{"code":"KAGAWA-KEN","name":"Kagawa","tax":"0.00000","SId":232},"_234":{"code":"KAGOSHIMA-KEN","name":"Kagoshima","tax":"0.00000","SId":234},"_328":{"code":"KANAGAWA-KEN","name":"Kanagawa","tax":"0.00000","SId":328},"_335":{"code":"KOCHI-KEN","name":"Kochi","tax":"0.00000","SId":335},"_339":{"code":"KUMAMOTO-KEN","name":"Kumamoto","tax":"0.00000","SId":339},"_343":{"code":"KYOTO-FU","name":"Kyoto","tax":"0.00000","SId":343},"_349":{"code":"MIE-KEN","name":"Mie","tax":"0.00000","SId":349},"_354":{"code":"MIYAGI-KEN","name":"Miyagi","tax":"0.00000","SId":354},"_359":{"code":"MIYAZAKI-KEN","name":"Miyazaki","tax":"0.00000","SId":359},"_365":{"code":"NAGANO-KEN","name":"Nagano","tax":"0.00000","SId":365},"_369":{"code":"NAGASAKI-KEN","name":"Nagasaki","tax":"0.00000","SId":369},"_372":{"code":"NARA-KEN","name":"Nara","tax":"0.00000","SId":372},"_375":{"code":"NIIGATA-KEN","name":"Niigata","tax":"0.00000","SId":375},"_379":{"code":"OITA-KEN","name":"Oita","tax":"0.00000","SId":379},"_382":{"code":"OKAYAMA-KEN","name":"Okayama","tax":"0.00000","SId":382},"_385":{"code":"OKINAWA-KEN","name":"Okinawa","tax":"0.00000","SId":385},"_389":{"code":"OSAKA-FU","name":"Osaka","tax":"0.00000","SId":389},"_394":{"code":"SAGA-KEN","name":"Saga","tax":"0.00000","SId":394},"_398":{"code":"SAITAMA-KEN","name":"Saitama","tax":"0.00000","SId":398},"_402":{"code":"SHIGA-KEN","name":"Shiga","tax":"0.00000","SId":402},"_498":{"code":"SHIMANE-KEN","name":"Shimane","tax":"0.00000","SId":498},"_508":{"code":"SHIZUOKA-KEN","name":"Shizuoka","tax":"0.00000","SId":508},"_511":{"code":"TOCHIGI-KEN","name":"Tochigi","tax":"0.00000","SId":511},"_514":{"code":"TOKUSHIMA-KEN","name":"Tokushima","tax":"0.00000","SId":514},"_516":{"code":"TOKYO-TO","name":"Tokyo","tax":"0.00000","SId":516},"_518":{"code":"TOTTORI-KEN","name":"Tottori","tax":"0.00000","SId":518},"_521":{"code":"TOYAMA-KEN","name":"Toyama","tax":"0.00000","SId":521},"_523":{"code":"WAKAYAMA-KEN","name":"Wakayama","tax":"0.00000","SId":523},"_526":{"code":"YAMAGATA-KEN","name":"Yamagata","tax":"0.00000","SId":526},"_529":{"code":"YAMAGUCHI-KEN","name":"Yamaguchi","tax":"0.00000","SId":529},"_532":{"code":"YAMANASHI-KEN","name":"Yamanashi","tax":"0.00000","SId":532}},"field":{"zip":{"verification":false,"example":"5400002 / 540-0002"}}},"255":{"name":"Jersey","code":"JE","tax":"0.00000","threshold":"0.00","phone_code":44,"address_format":0,"labels":[],"provinces":[]},"109":{"name":"Jordan","code":"JO","tax":"0.00000","threshold":"0.00","phone_code":962,"address_format":0,"labels":[],"provinces":[]},"110":{"name":"Kazakhstan","code":"KZ","tax":"0.00000","threshold":"0.00","phone_code":7,"address_format":0,"labels":[],"provinces":[]},"111":{"name":"Kenya","code":"KE","tax":"0.00000","threshold":"0.00","phone_code":254,"address_format":0,"labels":[],"provinces":[]},"112":{"name":"Kiribati","code":"KI","tax":"0.00000","threshold":"0.00","phone_code":686,"address_format":0,"labels":[],"provinces":[]},"113":{"name":"Korea","code":"KR","tax":"0.00000","threshold":"0.00","phone_code":82,"address_format":17,"labels":{"province":"省"},"provinces":{"_709":{"code":"Busan","name":"Busan","tax":"0.00000","SId":709},"_710":{"code":"NorthChungcheong","name":"North Chungcheong","tax":"0.00000","SId":710},"_711":{"code":"SouthChungcheong","name":"South Chungcheong","tax":"0.00000","SId":711},"_712":{"code":"Daegu","name":"Daegu","tax":"0.00000","SId":712},"_713":{"code":"Daejeon","name":"Daejeon","tax":"0.00000","SId":713},"_714":{"code":"Gangwon","name":"Gangwon","tax":"0.00000","SId":714},"_715":{"code":"GwangjuCity","name":"Gwangju City","tax":"0.00000","SId":715},"_716":{"code":"NorthGyeongsang","name":"North Gyeongsang","tax":"0.00000","SId":716},"_717":{"code":"Gyeonggi","name":"Gyeonggi","tax":"0.00000","SId":717},"_718":{"code":"SouthGyeongsang","name":"South Gyeongsang","tax":"0.00000","SId":718},"_719":{"code":"Incheon","name":"Incheon","tax":"0.00000","SId":719},"_720":{"code":"Jeju","name":"Jeju","tax":"0.00000","SId":720},"_721":{"code":"NorthJeolla","name":"North Jeolla","tax":"0.00000","SId":721},"_722":{"code":"SouthJeolla","name":"South Jeolla","tax":"0.00000","SId":722},"_723":{"code":"Sejong","name":"Sejong","tax":"0.00000","SId":723},"_724":{"code":"Seoul","name":"Seoul","tax":"0.00000","SId":724},"_725":{"code":"Ulsan","name":"Ulsan","tax":"0.00000","SId":725}}},"250":{"name":"Kosovo","code":"XK","tax":"0.00000","threshold":"0.00","phone_code":384,"address_format":1,"labels":[],"provinces":[]},"115":{"name":"Kuwait","code":"KW","tax":"0.00000","threshold":"0.00","phone_code":965,"address_format":0,"labels":[],"provinces":[]},"116":{"name":"Kyrgyzstan","code":"KG","tax":"0.00000","threshold":"0.00","phone_code":996,"address_format":13,"labels":[],"provinces":[]},"117":{"name":"Laos","code":"LA","tax":"0.00000","threshold":"0.00","phone_code":856,"address_format":0,"labels":[],"provinces":[]},"118":{"name":"Latvia","code":"LV","tax":"0.00000","threshold":"0.00","phone_code":371,"address_format":0,"labels":[],"provinces":[]},"119":{"name":"Lebanon","code":"LB","tax":"0.00000","threshold":"0.00","phone_code":961,"address_format":0,"labels":[],"provinces":[]},"120":{"name":"Lesotho","code":"LS","tax":"0.00000","threshold":"0.00","phone_code":266,"address_format":0,"labels":[],"provinces":[]},"121":{"name":"Liberia","code":"LR","tax":"0.00000","threshold":"0.00","phone_code":231,"address_format":1,"labels":[],"provinces":[]},"123":{"name":"Liechtenstein","code":"LI","tax":"0.00000","threshold":"0.00","phone_code":423,"address_format":1,"labels":[],"provinces":[]},"124":{"name":"Lithuania","code":"LT","tax":"0.00000","threshold":"0.00","phone_code":370,"address_format":0,"labels":[],"provinces":[]},"125":{"name":"Luxembourg","code":"LU","tax":"0.00000","threshold":"0.00","phone_code":352,"address_format":1,"labels":[],"provinces":[]},"126":{"name":"Macau, China","code":"MO","tax":"0.00000","threshold":"0.00","phone_code":853,"address_format":14,"labels":[],"provinces":[]},"127":{"name":"Macedonia","code":"MK","tax":"0.00000","threshold":"0.00","phone_code":389,"address_format":1,"labels":[],"provinces":[]},"128":{"name":"Madagascar","code":"MG","tax":"0.00000","threshold":"0.00","phone_code":261,"address_format":1,"labels":[],"provinces":[]},"129":{"name":"Malawi","code":"MW","tax":"0.00000","threshold":"0.00","phone_code":265,"address_format":2,"labels":[],"provinces":[]},"130":{"name":"Malaysia","code":"MY","tax":"0.00000","threshold":"0.00","phone_code":60,"address_format":3,"labels":{"province":"州/领地","zip":"邮政编码"},"provinces":{"_726":{"code":"JHR","name":"Johor","tax":"0.00000","SId":726},"_727":{"code":"KDH","name":"Kedah","tax":"0.00000","SId":727},"_728":{"code":"KTN","name":"Kelantan","tax":"0.00000","SId":728},"_729":{"code":"KUL","name":"Kuala Lumpur","tax":"0.00000","SId":729},"_730":{"code":"LBN","name":"Labuan","tax":"0.00000","SId":730},"_731":{"code":"MLK","name":"Malacca","tax":"0.00000","SId":731},"_732":{"code":"NSN","name":"Negeri Sembilan","tax":"0.00000","SId":732},"_733":{"code":"PHG","name":"Pahang","tax":"0.00000","SId":733},"_734":{"code":"PNG","name":"Penang","tax":"0.00000","SId":734},"_735":{"code":"PRK","name":"Perak","tax":"0.00000","SId":735},"_736":{"code":"PLS","name":"Perlis","tax":"0.00000","SId":736},"_737":{"code":"PJY","name":"Putrajaya","tax":"0.00000","SId":737},"_738":{"code":"SBH","name":"Sabah","tax":"0.00000","SId":738},"_739":{"code":"SWK","name":"Sarawak","tax":"0.00000","SId":739},"_740":{"code":"SGR","name":"Selangor","tax":"0.00000","SId":740},"_741":{"code":"TRG","name":"Terengganu","tax":"0.00000","SId":741}}},"131":{"name":"Maldives","code":"MV","tax":"0.00000","threshold":"0.00","phone_code":960,"address_format":0,"labels":[],"provinces":[]},"132":{"name":"Mali","code":"ML","tax":"0.00000","threshold":"0.00","phone_code":223,"address_format":2,"labels":[],"provinces":[]},"133":{"name":"Malta","code":"MT","tax":"0.00000","threshold":"0.00","phone_code":356,"address_format":0,"labels":[],"provinces":[]},"134":{"name":"Marshall Islands","code":"MH","tax":"0.00000","threshold":"0.00","phone_code":692,"address_format":0,"labels":[],"provinces":[]},"135":{"name":"Martinique","code":"MQ","tax":"0.00000","threshold":"0.00","phone_code":596,"address_format":1,"labels":[],"provinces":[]},"136":{"name":"Mauritania","code":"MR","tax":"0.00000","threshold":"0.00","phone_code":222,"address_format":0,"labels":[],"provinces":[]},"137":{"name":"Mauritius","code":"MU","tax":"0.00000","threshold":"0.00","phone_code":230,"address_format":0,"labels":[],"provinces":[]},"138":{"name":"Mayotte","code":"YT","tax":"0.00000","threshold":"0.00","phone_code":269,"address_format":1,"labels":[],"provinces":[]},"139":{"name":"Mexico","code":"MX","tax":"0.00000","threshold":"0.00","phone_code":52,"address_format":3,"labels":{"address1":"街道和门牌号","province":"州"},"provinces":{"_561":{"code":"AGS","name":"Aguascalientes","tax":"0.00000","SId":561},"_562":{"code":"BC","name":"Baja California","tax":"0.00000","SId":562},"_563":{"code":"BCS","name":"Baja California Sur","tax":"0.00000","SId":563},"_564":{"code":"CAMP","name":"Campeche","tax":"0.00000","SId":564},"_565":{"code":"CHIS","name":"Chiapas","tax":"0.00000","SId":565},"_566":{"code":"CDMX","name":"Ciudad de México","tax":"0.00000","SId":566},"_567":{"code":"COAH","name":"Coahuila","tax":"0.00000","SId":567},"_568":{"code":"COL","name":"Colima","tax":"0.00000","SId":568},"_569":{"code":"DF","name":"Distrito Federal","tax":"0.00000","SId":569},"_570":{"code":"DGO","name":"Durango","tax":"0.00000","SId":570},"_571":{"code":"MEX","name":"Estado de México","tax":"0.00000","SId":571},"_572":{"code":"GTO","name":"Guanajuato","tax":"0.00000","SId":572},"_573":{"code":"GRO","name":"Guerrero","tax":"0.00000","SId":573},"_574":{"code":"HGO","name":"Hidalgo","tax":"0.00000","SId":574},"_575":{"code":"JAL","name":"Jalisco","tax":"0.00000","SId":575},"_576":{"code":"MICH","name":"Michoacán","tax":"0.00000","SId":576},"_577":{"code":"MOR","name":"Morelos","tax":"0.00000","SId":577},"_578":{"code":"NAY","name":"Nayarit","tax":"0.00000","SId":578},"_579":{"code":"NL","name":"Nuevo León","tax":"0.00000","SId":579},"_580":{"code":"OAX","name":"Oaxaca","tax":"0.00000","SId":580},"_581":{"code":"PUE","name":"Puebla","tax":"0.00000","SId":581},"_582":{"code":"QRO","name":"Querétaro","tax":"0.00000","SId":582},"_583":{"code":"QROO","name":"Quintana Roo","tax":"0.00000","SId":583},"_584":{"code":"SLP","name":"San Luis Potosí","tax":"0.00000","SId":584},"_585":{"code":"SIN","name":"Sinaloa","tax":"0.00000","SId":585},"_586":{"code":"SON","name":"Sonora","tax":"0.00000","SId":586},"_587":{"code":"TAB","name":"Tabasco","tax":"0.00000","SId":587},"_588":{"code":"TAMPS","name":"Tamaulipas","tax":"0.00000","SId":588},"_589":{"code":"TLAX","name":"Tlaxcala","tax":"0.00000","SId":589},"_590":{"code":"VER","name":"Veracruz","tax":"0.00000","SId":590},"_591":{"code":"YUC","name":"Yucatán","tax":"0.00000","SId":591},"_592":{"code":"ZAC","name":"Zacatecas","tax":"0.00000","SId":592},"_1040":{"code":"CHIH","name":"Chihuahua","tax":"0.00000","SId":1040}}},"140":{"name":"Micronesia","code":"FM","tax":"0.00000","threshold":"0.00","phone_code":691,"address_format":0,"labels":[],"provinces":[]},"141":{"name":"Moldova","code":"MD","tax":"0.00000","threshold":"0.00","phone_code":373,"address_format":1,"labels":[],"provinces":[]},"142":{"name":"Monaco","code":"MC","tax":"0.00000","threshold":"0.00","phone_code":377,"address_format":1,"labels":[],"provinces":[]},"143":{"name":"Mongolia","code":"MN","tax":"0.00000","threshold":"0.00","phone_code":976,"address_format":0,"labels":[],"provinces":[]},"249":{"name":"Montenegro","code":"ME","tax":"0.00000","threshold":"0.00","phone_code":382,"address_format":1,"labels":[],"provinces":[]},"144":{"name":"Montserrat","code":"MS","tax":"0.00000","threshold":"0.00","phone_code":1664,"address_format":0,"labels":[],"provinces":[]},"145":{"name":"Morocco","code":"MA","tax":"0.00000","threshold":"0.00","phone_code":212,"address_format":1,"labels":[],"provinces":[]},"146":{"name":"Mozambique","code":"MZ","tax":"0.00000","threshold":"0.00","phone_code":258,"address_format":1,"labels":[],"provinces":[]},"147":{"name":"Myanmar","code":"MM","tax":"0.00000","threshold":"0.00","phone_code":95,"address_format":4,"labels":[],"provinces":{"_1123":{"code":"AYA","name":"Ayeyarwady","tax":"0.00000","SId":1123},"_1124":{"code":"BGO","name":"Bago","tax":"0.00000","SId":1124},"_1125":{"code":"MGY","name":"Magway","tax":"0.00000","SId":1125},"_1126":{"code":"MDY","name":"Mandalay","tax":"0.00000","SId":1126},"_1127":{"code":"SGG","name":"Sagaing","tax":"0.00000","SId":1127},"_1128":{"code":"TNT","name":"Tanintharyi","tax":"0.00000","SId":1128},"_1129":{"code":"YGN","name":"Yangon","tax":"0.00000","SId":1129},"_1130":{"code":"CHN","name":"Chin","tax":"0.00000","SId":1130},"_1131":{"code":"KCN","name":"Kachin","tax":"0.00000","SId":1131},"_1132":{"code":"KYR","name":"Kayah","tax":"0.00000","SId":1132},"_1133":{"code":"KYN","name":"Kayin","tax":"0.00000","SId":1133},"_1134":{"code":"MON","name":"Mon","tax":"0.00000","SId":1134},"_1135":{"code":"RKE","name":"Rakhine","tax":"0.00000","SId":1135},"_1136":{"code":"SHN","name":"Shan","tax":"0.00000","SId":1136},"_1137":{"code":"NPT","name":"Naypyitaw","tax":"0.00000","SId":1137}}},"148":{"name":"Namibia","code":"NA","tax":"0.00000","threshold":"0.00","phone_code":264,"address_format":0,"labels":[],"provinces":[]},"149":{"name":"Nauru","code":"NR","tax":"0.00000","threshold":"0.00","phone_code":674,"address_format":0,"labels":[],"provinces":[]},"150":{"name":"Nepal","code":"NP","tax":"0.00000","threshold":"0.00","phone_code":977,"address_format":0,"labels":[],"provinces":[]},"151":{"name":"Netherlands","code":"NL","tax":"0.00000","threshold":"0.00","phone_code":31,"address_format":11,"labels":{"address1":"街道和门牌号","province":"省"},"provinces":{"_1227":{"code":"Drenthe","name":"Drenthe","tax":"0.00000","SId":1227},"_1228":{"code":"Flevoland","name":"Flevoland","tax":"0.00000","SId":1228},"_1229":{"code":"Friesland","name":"Friesland","tax":"0.00000","SId":1229},"_1230":{"code":"Gelderland","name":"Gelderland","tax":"0.00000","SId":1230},"_1231":{"code":"Groningen","name":"Groningen","tax":"0.00000","SId":1231},"_1232":{"code":"Limburg","name":"Limburg","tax":"0.00000","SId":1232},"_1233":{"code":"NoordBrabant","name":"Noord-Brabant","tax":"0.00000","SId":1233},"_1234":{"code":"NoordHolland","name":"Noord-Holland","tax":"0.00000","SId":1234},"_1235":{"code":"Overijssel","name":"Overijssel","tax":"0.00000","SId":1235},"_1236":{"code":"Utrecht","name":"Utrecht","tax":"0.00000","SId":1236},"_1237":{"code":"Zeeland","name":"Zeeland","tax":"0.00000","SId":1237},"_1238":{"code":"ZuidHolland","name":"Zuid-Holland","tax":"0.00000","SId":1238}}},"243":{"name":"Netherlands Antilles","code":"AN","tax":"0.00000","threshold":"0.00","phone_code":599,"address_format":0,"labels":[],"provinces":[]},"153":{"name":"New Caledonia","code":"NC","tax":"0.00000","threshold":"0.00","phone_code":687,"address_format":1,"labels":[],"provinces":[]},"154":{"name":"New Zealand","code":"NZ","tax":"0.00000","threshold":"0.00","phone_code":64,"address_format":4,"labels":[],"provinces":{"_742":{"code":"AUK","name":"Auckland","tax":"0.00000","SId":742},"_743":{"code":"BOP","name":"Bay of Plenty","tax":"0.00000","SId":743},"_744":{"code":"CAN","name":"Canterbury","tax":"0.00000","SId":744},"_745":{"code":"GIS","name":"Gisborne","tax":"0.00000","SId":745},"_746":{"code":"HKB","name":"Hawke’s Bay","tax":"0.00000","SId":746},"_747":{"code":"MWT","name":"Manawatu-Wanganui","tax":"0.00000","SId":747},"_748":{"code":"MBH","name":"Marlborough","tax":"0.00000","SId":748},"_749":{"code":"NSN","name":"Nelson","tax":"0.00000","SId":749},"_750":{"code":"NTL","name":"Northland","tax":"0.00000","SId":750},"_751":{"code":"OTA","name":"Otago","tax":"0.00000","SId":751},"_752":{"code":"STL","name":"Southland","tax":"0.00000","SId":752},"_753":{"code":"TKI","name":"Taranaki","tax":"0.00000","SId":753},"_754":{"code":"TAS","name":"Tasman","tax":"0.00000","SId":754},"_755":{"code":"WKO","name":"Waikato","tax":"0.00000","SId":755},"_756":{"code":"WGN","name":"Wellington","tax":"0.00000","SId":756},"_757":{"code":"WTC","name":"West Coast","tax":"0.00000","SId":757}}},"155":{"name":"Nicaragua","code":"NI","tax":"0.00000","threshold":"0.00","phone_code":505,"address_format":15,"labels":[],"provinces":[]},"156":{"name":"Niger","code":"NE","tax":"0.00000","threshold":"0.00","phone_code":227,"address_format":1,"labels":[],"provinces":[]},"157":{"name":"Nigeria","code":"NG","tax":"0.00000","threshold":"0.00","phone_code":234,"address_format":4,"labels":{"province":"州"},"provinces":{"_795":{"code":"AB","name":"Abia","tax":"0.00000","SId":795},"_796":{"code":"FC","name":"Federal Capital Territory","tax":"0.00000","SId":796},"_797":{"code":"AD","name":"Adamawa","tax":"0.00000","SId":797},"_798":{"code":"AK","name":"Akwa Ibom","tax":"0.00000","SId":798},"_799":{"code":"AN","name":"Anambra","tax":"0.00000","SId":799},"_800":{"code":"BA","name":"Bauchi","tax":"0.00000","SId":800},"_801":{"code":"BY","name":"Bayelsa","tax":"0.00000","SId":801},"_802":{"code":"BE","name":"Benue","tax":"0.00000","SId":802},"_803":{"code":"BO","name":"Borno","tax":"0.00000","SId":803},"_804":{"code":"CR","name":"Cross River","tax":"0.00000","SId":804},"_805":{"code":"DE","name":"Delta","tax":"0.00000","SId":805},"_806":{"code":"EB","name":"Ebonyi","tax":"0.00000","SId":806},"_807":{"code":"ED","name":"Edo","tax":"0.00000","SId":807},"_808":{"code":"EK","name":"Ekiti","tax":"0.00000","SId":808},"_809":{"code":"EN","name":"Enugu","tax":"0.00000","SId":809},"_810":{"code":"GO","name":"Gombe","tax":"0.00000","SId":810},"_811":{"code":"IM","name":"Imo","tax":"0.00000","SId":811},"_812":{"code":"JI","name":"Jigawa","tax":"0.00000","SId":812},"_813":{"code":"KD","name":"Kaduna","tax":"0.00000","SId":813},"_814":{"code":"KN","name":"Kano","tax":"0.00000","SId":814},"_815":{"code":"KT","name":"Katsina","tax":"0.00000","SId":815},"_816":{"code":"KE","name":"Kebbi","tax":"0.00000","SId":816},"_817":{"code":"KO","name":"Kogi","tax":"0.00000","SId":817},"_818":{"code":"KW","name":"Kwara","tax":"0.00000","SId":818},"_819":{"code":"LA","name":"Lagos","tax":"0.00000","SId":819},"_820":{"code":"NA","name":"Nasarawa","tax":"0.00000","SId":820},"_821":{"code":"NI","name":"Niger","tax":"0.00000","SId":821},"_822":{"code":"OG","name":"Ogun","tax":"0.00000","SId":822},"_823":{"code":"ON","name":"Ondo","tax":"0.00000","SId":823},"_824":{"code":"OS","name":"Osun","tax":"0.00000","SId":824},"_825":{"code":"OY","name":"Oyo","tax":"0.00000","SId":825},"_826":{"code":"PL","name":"Plateau","tax":"0.00000","SId":826},"_827":{"code":"RI","name":"Rivers","tax":"0.00000","SId":827},"_828":{"code":"SO","name":"Sokoto","tax":"0.00000","SId":828},"_829":{"code":"TA","name":"Taraba","tax":"0.00000","SId":829},"_830":{"code":"YO","name":"Yobe","tax":"0.00000","SId":830},"_831":{"code":"ZA","name":"Zamfara","tax":"0.00000","SId":831}}},"158":{"name":"Niue","code":"NU","tax":"0.00000","threshold":"0.00","phone_code":683,"address_format":0,"labels":[],"provinces":[]},"159":{"name":"Norfolk Island","code":"NF","tax":"0.00000","threshold":"0.00","phone_code":672,"address_format":0,"labels":[],"provinces":[]},"160":{"name":"Northern Mariana Islands","code":"MP","tax":"0.00000","threshold":"0.00","phone_code":1670,"address_format":0,"labels":[],"provinces":[]},"161":{"name":"Norway","code":"NO","tax":"0.00000","threshold":"0.00","phone_code":47,"address_format":1,"labels":{"address1":"街道和门牌号"},"provinces":[]},"162":{"name":"Oman","code":"OM","tax":"0.00000","threshold":"0.00","phone_code":968,"address_format":15,"labels":[],"provinces":[]},"163":{"name":"Pakistan","code":"PK","tax":"0.00000","threshold":"0.00","phone_code":92,"address_format":0,"labels":[],"provinces":[]},"164":{"name":"Palau","code":"PW","tax":"0.00000","threshold":"0.00","phone_code":680,"address_format":0,"labels":[],"provinces":[]},"165":{"name":"Palestine","code":"PS","tax":"0.00000","threshold":"0.00","phone_code":970,"address_format":0,"labels":[],"provinces":[]},"166":{"name":"Panama","code":"PA","tax":"0.00000","threshold":"0.00","phone_code":507,"address_format":9,"labels":[],"provinces":{"_1017":{"code":"BocasdelToro","name":"Bocas del Toro","tax":"0.00000","SId":1017},"_1018":{"code":"Chiriqui","name":"Chiriquí","tax":"0.00000","SId":1018},"_1019":{"code":"Cocle","name":"Coclé","tax":"0.00000","SId":1019},"_1020":{"code":"Colon","name":"Colón","tax":"0.00000","SId":1020},"_1021":{"code":"Darien","name":"Darién","tax":"0.00000","SId":1021},"_1022":{"code":"Embera","name":"Emberá","tax":"0.00000","SId":1022},"_1023":{"code":"Herrera","name":"Herrera","tax":"0.00000","SId":1023},"_1024":{"code":"GunaYala","name":"Guna Yala","tax":"0.00000","SId":1024},"_1025":{"code":"LosSantos","name":"Los Santos","tax":"0.00000","SId":1025},"_1026":{"code":"Ngöbe-Buglé","name":"Ngöbe-Buglé","tax":"0.00000","SId":1026},"_1027":{"code":"Panama","name":"Panamá","tax":"0.00000","SId":1027},"_1028":{"code":"WestPanama","name":"West Panamá","tax":"0.00000","SId":1028},"_1029":{"code":"Veraguas","name":"Veraguas","tax":"0.00000","SId":1029}}},"167":{"name":"Papua New Guinea","code":"PG","tax":"0.00000","threshold":"0.00","phone_code":675,"address_format":0,"labels":[],"provinces":[]},"168":{"name":"Paraguay","code":"PY","tax":"0.00000","threshold":"0.00","phone_code":595,"address_format":1,"labels":[],"provinces":[]},"169":{"name":"Peru","code":"PE","tax":"0.00000","threshold":"0.00","phone_code":51,"address_format":4,"labels":[],"provinces":{"_969":{"code":"AMA","name":"Amazonas","tax":"0.00000","SId":969},"_970":{"code":"ANC","name":"Ancash","tax":"0.00000","SId":970},"_971":{"code":"APU","name":"Apurímac","tax":"0.00000","SId":971},"_972":{"code":"ARE","name":"Arequipa","tax":"0.00000","SId":972},"_973":{"code":"AYA","name":"Ayacucho","tax":"0.00000","SId":973},"_974":{"code":"CAJ","name":"Cajamarca","tax":"0.00000","SId":974},"_975":{"code":"CAL","name":"El Callao","tax":"0.00000","SId":975},"_976":{"code":"CUS","name":"Cusco","tax":"0.00000","SId":976},"_977":{"code":"HUV","name":"Huancavelica","tax":"0.00000","SId":977},"_978":{"code":"HUC","name":"Huánuco","tax":"0.00000","SId":978},"_979":{"code":"ICA","name":"Ica","tax":"0.00000","SId":979},"_980":{"code":"JUN","name":"Junín","tax":"0.00000","SId":980},"_981":{"code":"LAL","name":"La Libertad","tax":"0.00000","SId":981},"_982":{"code":"LAM","name":"Lambayeque","tax":"0.00000","SId":982},"_983":{"code":"LIM","name":"Lima Region","tax":"0.00000","SId":983},"_984":{"code":"LMA","name":"Lima","tax":"0.00000","SId":984},"_985":{"code":"LOR","name":"Loreto","tax":"0.00000","SId":985},"_986":{"code":"MDD","name":"Madre de Dios","tax":"0.00000","SId":986},"_987":{"code":"MOQ","name":"Moquegua","tax":"0.00000","SId":987},"_988":{"code":"PAS","name":"Pasco","tax":"0.00000","SId":988},"_989":{"code":"PIU","name":"Piura","tax":"0.00000","SId":989},"_990":{"code":"PUN","name":"Puno","tax":"0.00000","SId":990},"_991":{"code":"SAM","name":"San Martín","tax":"0.00000","SId":991},"_992":{"code":"TAC","name":"Tacna","tax":"0.00000","SId":992},"_993":{"code":"TUM","name":"Tumbes","tax":"0.00000","SId":993},"_994":{"code":"UCA","name":"Ucayali","tax":"0.00000","SId":994}}},"170":{"name":"Philippines","code":"PH","tax":"0.00000","threshold":"0.00","phone_code":63,"address_format":5,"labels":[],"provinces":{"_1041":{"code":"PH-ABR","name":"Abra","tax":"0.00000","SId":1041},"_1042":{"code":"PH-AGN","name":"Agusan del Norte","tax":"0.00000","SId":1042},"_1043":{"code":"PH-AGS","name":"Agusan del Sur","tax":"0.00000","SId":1043},"_1044":{"code":"PH-AKL","name":"Aklan","tax":"0.00000","SId":1044},"_1045":{"code":"PH-ALB","name":"Albay","tax":"0.00000","SId":1045},"_1046":{"code":"PH-ANT","name":"Antique","tax":"0.00000","SId":1046},"_1047":{"code":"PH-APA","name":"Apayao","tax":"0.00000","SId":1047},"_1048":{"code":"PH-AUR","name":"Aurora","tax":"0.00000","SId":1048},"_1049":{"code":"PH-BAS","name":"Basilan","tax":"0.00000","SId":1049},"_1050":{"code":"PH-BAN","name":"Bataan","tax":"0.00000","SId":1050},"_1051":{"code":"PH-BTN","name":"Batanes","tax":"0.00000","SId":1051},"_1052":{"code":"PH-BTG","name":"Batangas","tax":"0.00000","SId":1052},"_1053":{"code":"PH-BEN","name":"Benguet","tax":"0.00000","SId":1053},"_1054":{"code":"PH-BIL","name":"Biliran","tax":"0.00000","SId":1054},"_1055":{"code":"PH-BOH","name":"Bohol","tax":"0.00000","SId":1055},"_1056":{"code":"PH-BUK","name":"Bukidnon","tax":"0.00000","SId":1056},"_1057":{"code":"PH-BUL","name":"Bulacan","tax":"0.00000","SId":1057},"_1058":{"code":"PH-CAG","name":"Cagayan","tax":"0.00000","SId":1058},"_1059":{"code":"PH-CAN","name":"Camarines Norte","tax":"0.00000","SId":1059},"_1060":{"code":"PH-CAS","name":"Camarines Sur","tax":"0.00000","SId":1060},"_1061":{"code":"PH-CAM","name":"Camiguin","tax":"0.00000","SId":1061},"_1062":{"code":"PH-CAP","name":"Capiz","tax":"0.00000","SId":1062},"_1063":{"code":"PH-CAT","name":"Catanduanes","tax":"0.00000","SId":1063},"_1064":{"code":"PH-CAV","name":"Cavite","tax":"0.00000","SId":1064},"_1065":{"code":"PH-CEB","name":"Cebu","tax":"0.00000","SId":1065},"_1066":{"code":"PH-NCO","name":"Cotabato","tax":"0.00000","SId":1066},"_1067":{"code":"PH-DVO","name":"Davao Occidental","tax":"0.00000","SId":1067},"_1068":{"code":"PH-DAO","name":"Davao Oriental","tax":"0.00000","SId":1068},"_1069":{"code":"PH-COM","name":"Compostela Valley","tax":"0.00000","SId":1069},"_1070":{"code":"PH-DAV","name":"Davao del Norte","tax":"0.00000","SId":1070},"_1071":{"code":"PH-DAS","name":"Davao del Sur","tax":"0.00000","SId":1071},"_1072":{"code":"PH-DIN","name":"Dinagat Islands","tax":"0.00000","SId":1072},"_1073":{"code":"PH-EAS","name":"Eastern Samar","tax":"0.00000","SId":1073},"_1074":{"code":"PH-GUI","name":"Guimaras","tax":"0.00000","SId":1074},"_1075":{"code":"PH-IFU","name":"Ifugao","tax":"0.00000","SId":1075},"_1076":{"code":"PH-ILN","name":"Ilocos Norte","tax":"0.00000","SId":1076},"_1077":{"code":"PH-ILS","name":"Ilocos Sur","tax":"0.00000","SId":1077},"_1078":{"code":"PH-ILI","name":"Iloilo","tax":"0.00000","SId":1078},"_1079":{"code":"PH-ISA","name":"Isabela","tax":"0.00000","SId":1079},"_1080":{"code":"PH-KAL","name":"Kalinga","tax":"0.00000","SId":1080},"_1081":{"code":"PH-LUN","name":"La Union","tax":"0.00000","SId":1081},"_1082":{"code":"PH-LAG","name":"Laguna","tax":"0.00000","SId":1082},"_1083":{"code":"PH-LAN","name":"Lanao del Norte","tax":"0.00000","SId":1083},"_1084":{"code":"PH-LAS","name":"Lanao del Sur","tax":"0.00000","SId":1084},"_1085":{"code":"PH-LEY","name":"Leyte","tax":"0.00000","SId":1085},"_1086":{"code":"PH-MAG","name":"Maguindanao","tax":"0.00000","SId":1086},"_1087":{"code":"PH-MAD","name":"Marinduque","tax":"0.00000","SId":1087},"_1088":{"code":"PH-MAS","name":"Masbate","tax":"0.00000","SId":1088},"_1089":{"code":"PH-00","name":"Metro Manila","tax":"0.00000","SId":1089},"_1090":{"code":"PH-MSC","name":"Misamis Occidental","tax":"0.00000","SId":1090},"_1091":{"code":"PH-MSR","name":"Misamis Oriental","tax":"0.00000","SId":1091},"_1092":{"code":"PH-MOU","name":"Mountain","tax":"0.00000","SId":1092},"_1093":{"code":"PH-NEC","name":"Negros Occidental","tax":"0.00000","SId":1093},"_1094":{"code":"PH-NER","name":"Negros Oriental","tax":"0.00000","SId":1094},"_1095":{"code":"PH-NSA","name":"Northern Samar","tax":"0.00000","SId":1095},"_1096":{"code":"PH-NUE","name":"Nueva Ecija","tax":"0.00000","SId":1096},"_1097":{"code":"PH-NUV","name":"Nueva Vizcaya","tax":"0.00000","SId":1097},"_1098":{"code":"PH-MDC","name":"Occidental Mindoro","tax":"0.00000","SId":1098},"_1099":{"code":"PH-MDR","name":"Oriental Mindoro","tax":"0.00000","SId":1099},"_1100":{"code":"PH-PLW","name":"Palawan","tax":"0.00000","SId":1100},"_1101":{"code":"PH-PAM","name":"Pampanga","tax":"0.00000","SId":1101},"_1102":{"code":"PH-PAN","name":"Pangasinan","tax":"0.00000","SId":1102},"_1103":{"code":"PH-QUE","name":"Quezon","tax":"0.00000","SId":1103},"_1104":{"code":"PH-QUI","name":"Quirino","tax":"0.00000","SId":1104},"_1105":{"code":"PH-RIZ","name":"Rizal","tax":"0.00000","SId":1105},"_1106":{"code":"PH-ROM","name":"Romblon","tax":"0.00000","SId":1106},"_1107":{"code":"PH-WSA","name":"Samar","tax":"0.00000","SId":1107},"_1108":{"code":"PH-SAR","name":"Sarangani","tax":"0.00000","SId":1108},"_1109":{"code":"PH-SIG","name":"Siquijor","tax":"0.00000","SId":1109},"_1110":{"code":"PH-SOR","name":"Sorsogon","tax":"0.00000","SId":1110},"_1111":{"code":"PH-SCO","name":"South Cotabato","tax":"0.00000","SId":1111},"_1112":{"code":"PH-SLE","name":"Southern Leyte","tax":"0.00000","SId":1112},"_1113":{"code":"PH-SUK","name":"Sultan Kudarat","tax":"0.00000","SId":1113},"_1114":{"code":"PH-SLU","name":"Sulu","tax":"0.00000","SId":1114},"_1115":{"code":"PH-SUN","name":"Surigao del Norte","tax":"0.00000","SId":1115},"_1116":{"code":"PH-SUR","name":"Surigao del Sur","tax":"0.00000","SId":1116},"_1117":{"code":"PH-TAR","name":"Tarlac","tax":"0.00000","SId":1117},"_1118":{"code":"PH-TAW","name":"Tawi-Tawi","tax":"0.00000","SId":1118},"_1119":{"code":"PH-ZMB","name":"Zambales","tax":"0.00000","SId":1119},"_1120":{"code":"PH-ZSI","name":"Zamboanga Sibugay","tax":"0.00000","SId":1120},"_1121":{"code":"PH-ZAN","name":"Zamboanga del Norte","tax":"0.00000","SId":1121},"_1122":{"code":"PH-ZAS","name":"Zamboanga del Sur","tax":"0.00000","SId":1122}}},"171":{"name":"Pitcairn Islands","code":"PN","tax":"0.00000","threshold":"0.00","phone_code":64,"address_format":16,"labels":[],"provinces":[]},"172":{"name":"Poland","code":"PL","tax":"0.00000","threshold":"0.00","phone_code":48,"address_format":1,"labels":[],"provinces":[]},"173":{"name":"Portugal","code":"PT","tax":"0.00000","threshold":"0.00","phone_code":351,"address_format":4,"labels":[],"provinces":{"_901":{"code":"Azores","name":"Azores","tax":"0.00000","SId":901},"_902":{"code":"Aveiro","name":"Aveiro","tax":"0.00000","SId":902},"_903":{"code":"Beja","name":"Beja","tax":"0.00000","SId":903},"_904":{"code":"Braga","name":"Braga","tax":"0.00000","SId":904},"_905":{"code":"Braganza","name":"Bragança","tax":"0.00000","SId":905},"_906":{"code":"CasteloBranco","name":"Castelo Branco","tax":"0.00000","SId":906},"_907":{"code":"Coimbra","name":"Coimbra","tax":"0.00000","SId":907},"_908":{"code":"Evora","name":"Évora","tax":"0.00000","SId":908},"_909":{"code":"Faro","name":"Faro","tax":"0.00000","SId":909},"_910":{"code":"Guarda","name":"Guarda","tax":"0.00000","SId":910},"_911":{"code":"Leiria","name":"Leiria","tax":"0.00000","SId":911},"_912":{"code":"Lisbon","name":"Lisbon","tax":"0.00000","SId":912},"_913":{"code":"Madeira","name":"Madeira","tax":"0.00000","SId":913},"_914":{"code":"Portalegre","name":"Portalegre","tax":"0.00000","SId":914},"_915":{"code":"Porto","name":"Porto","tax":"0.00000","SId":915},"_916":{"code":"Santarem","name":"Santarém","tax":"0.00000","SId":916},"_917":{"code":"Setubal","name":"Setúbal","tax":"0.00000","SId":917},"_918":{"code":"VianadoCastelo","name":"Viana do Castelo","tax":"0.00000","SId":918},"_919":{"code":"VilaReal","name":"Vila Real","tax":"0.00000","SId":919},"_920":{"code":"Viseu","name":"Viseu","tax":"0.00000","SId":920}}},"174":{"name":"Puerto Rico","code":"PR","tax":"0.00000","threshold":"0.00","phone_code":1,"address_format":0,"labels":[],"provinces":[]},"175":{"name":"Qatar","code":"QA","tax":"0.00000","threshold":"0.00","phone_code":974,"address_format":2,"labels":[],"provinces":[]},"176":{"name":"Reunion","code":"RE","tax":"0.00000","threshold":"0.00","phone_code":262,"address_format":1,"labels":[],"provinces":[]},"177":{"name":"Romania","code":"RO","tax":"0.00000","threshold":"0.00","phone_code":40,"address_format":3,"labels":{"province":"县"},"provinces":{"_859":{"code":"AB","name":"Alba","tax":"0.00000","SId":859},"_860":{"code":"AR","name":"Arad","tax":"0.00000","SId":860},"_861":{"code":"AG","name":"Argeș","tax":"0.00000","SId":861},"_862":{"code":"BC","name":"Bacău","tax":"0.00000","SId":862},"_863":{"code":"BH","name":"Bihor","tax":"0.00000","SId":863},"_864":{"code":"BN","name":"Bistriţa-Năsăud","tax":"0.00000","SId":864},"_865":{"code":"BT","name":"Botoşani","tax":"0.00000","SId":865},"_866":{"code":"BR","name":"Brăila","tax":"0.00000","SId":866},"_867":{"code":"BV","name":"Braşov","tax":"0.00000","SId":867},"_868":{"code":"B","name":"Bucharest","tax":"0.00000","SId":868},"_869":{"code":"BZ","name":"Buzău","tax":"0.00000","SId":869},"_870":{"code":"CS","name":"Caraș-Severin","tax":"0.00000","SId":870},"_871":{"code":"CJ","name":"Cluj","tax":"0.00000","SId":871},"_872":{"code":"CT","name":"Constanța","tax":"0.00000","SId":872},"_873":{"code":"CV","name":"Covasna","tax":"0.00000","SId":873},"_874":{"code":"CL","name":"Călărași","tax":"0.00000","SId":874},"_875":{"code":"DJ","name":"Dolj","tax":"0.00000","SId":875},"_876":{"code":"DB","name":"Dâmbovița","tax":"0.00000","SId":876},"_877":{"code":"GL","name":"Galați","tax":"0.00000","SId":877},"_878":{"code":"GR","name":"Giurgiu","tax":"0.00000","SId":878},"_879":{"code":"GJ","name":"Gorj","tax":"0.00000","SId":879},"_880":{"code":"HR","name":"Harghita","tax":"0.00000","SId":880},"_881":{"code":"HD","name":"Hunedoara","tax":"0.00000","SId":881},"_882":{"code":"IL","name":"Ialomița","tax":"0.00000","SId":882},"_883":{"code":"IS","name":"Iași","tax":"0.00000","SId":883},"_884":{"code":"IF","name":"Ilfov","tax":"0.00000","SId":884},"_885":{"code":"MM","name":"Maramureş","tax":"0.00000","SId":885},"_886":{"code":"MH","name":"Mehedinți","tax":"0.00000","SId":886},"_887":{"code":"MS","name":"Mureş","tax":"0.00000","SId":887},"_888":{"code":"NT","name":"Neamţ","tax":"0.00000","SId":888},"_889":{"code":"OT","name":"Olt","tax":"0.00000","SId":889},"_890":{"code":"PH","name":"Prahova","tax":"0.00000","SId":890},"_891":{"code":"SJ","name":"Sălaj","tax":"0.00000","SId":891},"_892":{"code":"SM","name":"Satu Mare","tax":"0.00000","SId":892},"_893":{"code":"SB","name":"Sibiu","tax":"0.00000","SId":893},"_894":{"code":"SV","name":"Suceava","tax":"0.00000","SId":894},"_895":{"code":"TR","name":"Teleorman","tax":"0.00000","SId":895},"_896":{"code":"TM","name":"Timiș","tax":"0.00000","SId":896},"_897":{"code":"TL","name":"Tulcea","tax":"0.00000","SId":897},"_898":{"code":"VL","name":"Vâlcea","tax":"0.00000","SId":898},"_899":{"code":"VS","name":"Vaslui","tax":"0.00000","SId":899},"_900":{"code":"VN","name":"Vrancea","tax":"0.00000","SId":900}}},"178":{"name":"Russia","code":"RU","tax":"0.00000","threshold":"0.00","phone_code":7,"address_format":4,"labels":[],"provinces":{"_620":{"code":"ALT","name":"Altai Krai","tax":"0.00000","SId":620},"_621":{"code":"AL","name":"Altai","tax":"0.00000","SId":621},"_622":{"code":"AMU","name":"Amur","tax":"0.00000","SId":622},"_623":{"code":"ARK","name":"Arkhangelsk","tax":"0.00000","SId":623},"_624":{"code":"AST","name":"Astrakhan","tax":"0.00000","SId":624},"_625":{"code":"BEL","name":"Belgorod","tax":"0.00000","SId":625},"_626":{"code":"BRY","name":"Bryansk","tax":"0.00000","SId":626},"_627":{"code":"CE","name":"Chechen","tax":"0.00000","SId":627},"_628":{"code":"CHE","name":"Chelyabinsk","tax":"0.00000","SId":628},"_629":{"code":"CHU","name":"Chukotka Okrug","tax":"0.00000","SId":629},"_630":{"code":"CU","name":"Chuvash","tax":"0.00000","SId":630},"_631":{"code":"IRK","name":"Irkutsk","tax":"0.00000","SId":631},"_632":{"code":"IVA","name":"Ivanovo","tax":"0.00000","SId":632},"_633":{"code":"YEV","name":"Jewish","tax":"0.00000","SId":633},"_634":{"code":"KB","name":"Kabardino-Balkar","tax":"0.00000","SId":634},"_635":{"code":"KGD","name":"Kaliningrad","tax":"0.00000","SId":635},"_636":{"code":"KLU","name":"Kaluga","tax":"0.00000","SId":636},"_637":{"code":"KAM","name":"Kamchatka Krai","tax":"0.00000","SId":637},"_638":{"code":"KC","name":"Karachay-Cherkess","tax":"0.00000","SId":638},"_639":{"code":"KEM","name":"Kemerovo","tax":"0.00000","SId":639},"_640":{"code":"KHA","name":"Khabarovsk Krai","tax":"0.00000","SId":640},"_641":{"code":"KHM","name":"Khanty-Mansi","tax":"0.00000","SId":641},"_642":{"code":"KIR","name":"Kirov","tax":"0.00000","SId":642},"_643":{"code":"KO","name":"Komi","tax":"0.00000","SId":643},"_644":{"code":"KOS","name":"Kostroma","tax":"0.00000","SId":644},"_645":{"code":"KDA","name":"Krasnodar Krai","tax":"0.00000","SId":645},"_646":{"code":"KYA","name":"Krasnoyarsk Krai","tax":"0.00000","SId":646},"_647":{"code":"KGN","name":"Kurgan","tax":"0.00000","SId":647},"_648":{"code":"KRS","name":"Kursk","tax":"0.00000","SId":648},"_649":{"code":"LEN","name":"Leningrad","tax":"0.00000","SId":649},"_650":{"code":"LIP","name":"Lipetsk","tax":"0.00000","SId":650},"_651":{"code":"MAG","name":"Magadan","tax":"0.00000","SId":651},"_652":{"code":"ME","name":"Mari El","tax":"0.00000","SId":652},"_653":{"code":"MOW","name":"Moscow","tax":"0.00000","SId":653},"_654":{"code":"MOS","name":"Moscow Province","tax":"0.00000","SId":654},"_655":{"code":"MUR","name":"Murmansk","tax":"0.00000","SId":655},"_656":{"code":"NIZ","name":"Nizhny Novgorod","tax":"0.00000","SId":656},"_657":{"code":"NGR","name":"Novgorod","tax":"0.00000","SId":657},"_658":{"code":"NVS","name":"Novosibirsk","tax":"0.00000","SId":658},"_659":{"code":"OMS","name":"Omsk","tax":"0.00000","SId":659},"_660":{"code":"ORE","name":"Orenburg","tax":"0.00000","SId":660},"_661":{"code":"ORL","name":"Oryol","tax":"0.00000","SId":661},"_662":{"code":"PNZ","name":"Penza","tax":"0.00000","SId":662},"_663":{"code":"PER","name":"Perm Krai","tax":"0.00000","SId":663},"_664":{"code":"PRI","name":"Primorsky Krai","tax":"0.00000","SId":664},"_665":{"code":"PSK","name":"Pskov","tax":"0.00000","SId":665},"_666":{"code":"AD","name":"Adygea","tax":"0.00000","SId":666},"_667":{"code":"BA","name":"Bashkortostan","tax":"0.00000","SId":667},"_668":{"code":"BU","name":"Buryat","tax":"0.00000","SId":668},"_669":{"code":"DA","name":"Dagestan","tax":"0.00000","SId":669},"_670":{"code":"IN","name":"Ingushetia","tax":"0.00000","SId":670},"_671":{"code":"KL","name":"Kalmykia","tax":"0.00000","SId":671},"_672":{"code":"KR","name":"Karelia","tax":"0.00000","SId":672},"_673":{"code":"KK","name":"Khakassia","tax":"0.00000","SId":673},"_674":{"code":"MO","name":"Mordovia","tax":"0.00000","SId":674},"_675":{"code":"SE","name":"North Ossetia-Alania","tax":"0.00000","SId":675},"_676":{"code":"TA","name":"Tatarstan","tax":"0.00000","SId":676},"_677":{"code":"ROS","name":"Rostov","tax":"0.00000","SId":677},"_678":{"code":"RYA","name":"Ryazan","tax":"0.00000","SId":678},"_679":{"code":"SPE","name":"Saint Petersburg","tax":"0.00000","SId":679},"_680":{"code":"SA","name":"Sakha","tax":"0.00000","SId":680},"_681":{"code":"SAK","name":"Sakhalin","tax":"0.00000","SId":681},"_682":{"code":"SAM","name":"Samara","tax":"0.00000","SId":682},"_683":{"code":"SAR","name":"Saratov","tax":"0.00000","SId":683},"_684":{"code":"SMO","name":"Smolensk","tax":"0.00000","SId":684},"_685":{"code":"STA","name":"Stavropol Krai","tax":"0.00000","SId":685},"_686":{"code":"SVE","name":"Sverdlovsk","tax":"0.00000","SId":686},"_687":{"code":"TAM","name":"Tambov","tax":"0.00000","SId":687},"_688":{"code":"TOM","name":"Tomsk","tax":"0.00000","SId":688},"_689":{"code":"TUL","name":"Tula","tax":"0.00000","SId":689},"_690":{"code":"TVE","name":"Tver","tax":"0.00000","SId":690},"_691":{"code":"TYU","name":"Tyumen","tax":"0.00000","SId":691},"_692":{"code":"TY","name":"Tuva","tax":"0.00000","SId":692},"_693":{"code":"UD","name":"Udmurt","tax":"0.00000","SId":693},"_694":{"code":"ULY","name":"Ulyanovsk","tax":"0.00000","SId":694},"_695":{"code":"VLA","name":"Vladimir","tax":"0.00000","SId":695},"_696":{"code":"VGG","name":"Volgograd","tax":"0.00000","SId":696},"_697":{"code":"VLG","name":"Vologda","tax":"0.00000","SId":697},"_698":{"code":"VOR","name":"Voronezh","tax":"0.00000","SId":698},"_699":{"code":"YAN","name":"Yamalo-Nenets Okrug","tax":"0.00000","SId":699},"_700":{"code":"YAR","name":"Yaroslavl","tax":"0.00000","SId":700},"_701":{"code":"ZAB","name":"Zabaykalsky Krai","tax":"0.00000","SId":701}}},"179":{"name":"Rwanda","code":"RW","tax":"0.00000","threshold":"0.00","phone_code":250,"address_format":0,"labels":[],"provinces":[]},"246":{"name":"Saint Barthélemy","code":"BL","tax":"0.00000","threshold":"0.00","phone_code":590,"address_format":1,"labels":[],"provinces":[]},"180":{"name":"Saint Helena","code":"SH","tax":"0.00000","threshold":"0.00","phone_code":247,"address_format":0,"labels":[],"provinces":[]},"181":{"name":"Saint Kitts and Nevis","code":"KN","tax":"0.00000","threshold":"0.00","phone_code":1869,"address_format":0,"labels":[],"provinces":[]},"182":{"name":"Saint Lucia","code":"LC","tax":"0.00000","threshold":"0.00","phone_code":1758,"address_format":0,"labels":[],"provinces":[]},"242":{"name":"Saint Martin","code":"MF","tax":"0.00000","threshold":"0.00","phone_code":590,"address_format":1,"labels":[],"provinces":[]},"184":{"name":"Saint Pierre and Miquelon","code":"PM","tax":"0.00000","threshold":"0.00","phone_code":508,"address_format":1,"labels":[],"provinces":[]},"152":{"name":"Saint Vincent and the Grenadines","code":"VC","tax":"0.00000","threshold":"0.00","phone_code":784,"address_format":0,"labels":[],"provinces":[]},"185":{"name":"San Marino","code":"SM","tax":"0.00000","threshold":"0.00","phone_code":378,"address_format":1,"labels":[],"provinces":[]},"186":{"name":"Sao Tome and Principe","code":"ST","tax":"0.00000","threshold":"0.00","phone_code":239,"address_format":0,"labels":[],"provinces":[]},"187":{"name":"Saudi Arabia","code":"SA","tax":"0.00000","threshold":"0.00","phone_code":966,"address_format":2,"labels":[],"provinces":[]},"188":{"name":"Senegal","code":"SN","tax":"0.00000","threshold":"0.00","phone_code":221,"address_format":1,"labels":[],"provinces":[]},"189":{"name":"Serbia","code":"RS","tax":"0.00000","threshold":"0.00","phone_code":381,"address_format":1,"labels":[],"provinces":[]},"190":{"name":"Seychelles","code":"SC","tax":"0.00000","threshold":"0.00","phone_code":248,"address_format":0,"labels":[],"provinces":[]},"191":{"name":"Sierra Leone","code":"SL","tax":"0.00000","threshold":"0.00","phone_code":232,"address_format":2,"labels":[],"provinces":[]},"192":{"name":"Singapore","code":"SG","tax":"0.00000","threshold":"0.00","phone_code":65,"address_format":7,"labels":[],"provinces":[]},"245":{"name":"Sint Maarten","code":"SX","tax":"0.00000","threshold":"0.00","phone_code":599,"address_format":0,"labels":[],"provinces":[]},"193":{"name":"Slovakia","code":"SK","tax":"0.00000","threshold":"0.00","phone_code":421,"address_format":11,"labels":{"province":""},"provinces":{"_1356":{"code":"BanskobystrickyKraj","name":"Banskobystricky kraj","tax":"0.00000","SId":1356},"_1357":{"code":"BratislavskyKraj","name":"Bratislavsky kraj","tax":"0.00000","SId":1357},"_1358":{"code":"KosickyKraj","name":"Kosicky kraj","tax":"0.00000","SId":1358},"_1359":{"code":"NitrianskyKraj","name":"Nitriansky kraj","tax":"0.00000","SId":1359},"_1360":{"code":"PresovskyKraj","name":"Presovsky kraj","tax":"0.00000","SId":1360},"_1361":{"code":"TrencianskyKraj","name":"Trenciansky kraj","tax":"0.00000","SId":1361},"_1362":{"code":"TrnavskyKraj","name":"Trnavsky kraj","tax":"0.00000","SId":1362},"_1363":{"code":"ZilinskyKraj","name":"Zilinsky kraj","tax":"0.00000","SId":1363}}},"194":{"name":"Slovenia","code":"SI","tax":"0.00000","threshold":"0.00","phone_code":386,"address_format":1,"labels":[],"provinces":[]},"233":{"name":"Socialist Republic of Vietnam","code":"VN","tax":"0.00000","threshold":"0.00","phone_code":84,"address_format":3,"labels":{"province":"省份"},"provinces":{"_1163":{"code":"AnGiang","name":"An Giang province","tax":"0.00000","SId":1163},"_1164":{"code":"BaRiaVungTau","name":"Ba Ria Vung Tau Province","tax":"0.00000","SId":1164},"_1165":{"code":"BacGiang","name":"Bac Giang province","tax":"0.00000","SId":1165},"_1166":{"code":"BacKan","name":"Bac Kan Province","tax":"0.00000","SId":1166},"_1167":{"code":"BacLieu","name":"Bac Lieu Province","tax":"0.00000","SId":1167},"_1168":{"code":"BacNinh","name":"Bac Ninh province","tax":"0.00000","SId":1168},"_1169":{"code":"BenTre","name":"Ben Tre Province","tax":"0.00000","SId":1169},"_1170":{"code":"Pacifythe","name":"Pacify the province","tax":"0.00000","SId":1170},"_1171":{"code":"BinhDuong","name":"Binh Duong Province","tax":"0.00000","SId":1171},"_1172":{"code":"BinhPhuoc","name":"Binh Phuoc","tax":"0.00000","SId":1172},"_1173":{"code":"BinhThuan","name":"Binh Thuan Province","tax":"0.00000","SId":1173},"_1174":{"code":"CaMau","name":"Ca Mau Province","tax":"0.00000","SId":1174},"_1175":{"code":"CanTho","name":"Can Tho city","tax":"0.00000","SId":1175},"_1176":{"code":"CaoBang","name":"Cao Bang Province","tax":"0.00000","SId":1176},"_1177":{"code":"DaNang","name":"Da Nang city","tax":"0.00000","SId":1177},"_1178":{"code":"DakLak","name":"Dak Lak Province","tax":"0.00000","SId":1178},"_1179":{"code":"DakNong","name":"Dak Nong Province","tax":"0.00000","SId":1179},"_1180":{"code":"DienBien","name":"Dien Bien Province","tax":"0.00000","SId":1180},"_1181":{"code":"Dongnai","name":"Dongnai province","tax":"0.00000","SId":1181},"_1182":{"code":"DongThap","name":"Dong Thap Province","tax":"0.00000","SId":1182},"_1183":{"code":"GiaLai","name":"Gia Lai province","tax":"0.00000","SId":1183},"_1184":{"code":"HaGiang","name":"Ha Giang Province","tax":"0.00000","SId":1184},"_1185":{"code":"HaNam","name":"Ha Nam province","tax":"0.00000","SId":1185},"_1186":{"code":"Hanoi","name":"Hanoi City","tax":"0.00000","SId":1186},"_1187":{"code":"HaTinh","name":"Ha Tinh province","tax":"0.00000","SId":1187},"_1188":{"code":"HaiDuong","name":"Hai Duong Province","tax":"0.00000","SId":1188},"_1189":{"code":"HaiPhong","name":"Hai Phong city","tax":"0.00000","SId":1189},"_1190":{"code":"HauGiang","name":"Hau Giang province","tax":"0.00000","SId":1190},"_1191":{"code":"HoChiMinh","name":"Ho Chi Minh City","tax":"0.00000","SId":1191},"_1192":{"code":"HoaBinh","name":"Hoa Binh province","tax":"0.00000","SId":1192},"_1193":{"code":"HungYen","name":"Hung Yen province","tax":"0.00000","SId":1193},"_1194":{"code":"KhanhHoa","name":"Khanh Hoa province","tax":"0.00000","SId":1194},"_1195":{"code":"KienGiang","name":"Kien Giang Province","tax":"0.00000","SId":1195},"_1196":{"code":"KonTum","name":"Kon Tum Province","tax":"0.00000","SId":1196},"_1197":{"code":"LaiChau","name":"Lai Chau Province","tax":"0.00000","SId":1197},"_1198":{"code":"LamDong","name":"Lam Dong Province","tax":"0.00000","SId":1198},"_1199":{"code":"LangSon","name":"Lang Son Province","tax":"0.00000","SId":1199},"_1200":{"code":"LaoCai","name":"Lao Cai Province","tax":"0.00000","SId":1200},"_1201":{"code":"LongAn","name":"Long An Province","tax":"0.00000","SId":1201},"_1202":{"code":"NamDinh","name":"Nam Dinh","tax":"0.00000","SId":1202},"_1203":{"code":"NgheAn","name":"Nghe An province","tax":"0.00000","SId":1203},"_1204":{"code":"NinhBinh","name":"Ninh Binh province","tax":"0.00000","SId":1204},"_1205":{"code":"NinhThuan","name":"Ninh Thuan Province","tax":"0.00000","SId":1205},"_1206":{"code":"PhuTho","name":"Phu Tho province","tax":"0.00000","SId":1206},"_1207":{"code":"PhuYen","name":"Phu Yen Province","tax":"0.00000","SId":1207},"_1208":{"code":"QuangBinh","name":"Quang Binh Province","tax":"0.00000","SId":1208},"_1209":{"code":"QuangNam","name":"Quang Nam Province","tax":"0.00000","SId":1209},"_1210":{"code":"QuangNgai","name":"Quang Ngai province","tax":"0.00000","SId":1210},"_1211":{"code":"QuangNinh","name":"Quang Ninh Province","tax":"0.00000","SId":1211},"_1212":{"code":"QuangTri","name":"Quang Tri province","tax":"0.00000","SId":1212},"_1213":{"code":"SocTrang","name":"Soc Trang Province","tax":"0.00000","SId":1213},"_1214":{"code":"SonLa","name":"Son La Province","tax":"0.00000","SId":1214},"_1215":{"code":"TayNinh","name":"Tay Ninh province","tax":"0.00000","SId":1215},"_1216":{"code":"ThaiBinh","name":"Thai Binh Province","tax":"0.00000","SId":1216},"_1217":{"code":"ThaiNguyen","name":"Thai Nguyen province","tax":"0.00000","SId":1217},"_1218":{"code":"ThanhHoa","name":"Thanh Hoa province","tax":"0.00000","SId":1218},"_1219":{"code":"ThuaThienHue","name":"Thua Thien Hue province","tax":"0.00000","SId":1219},"_1220":{"code":"TienGiang","name":"Tien Giang Province","tax":"0.00000","SId":1220},"_1221":{"code":"TraVinh","name":"Tra Vinh province","tax":"0.00000","SId":1221},"_1222":{"code":"TuyenQuang","name":"Tuyen Quang Province","tax":"0.00000","SId":1222},"_1223":{"code":"VinhLong","name":"Vinh Long Province","tax":"0.00000","SId":1223},"_1224":{"code":"VinhPhuc","name":"Vinh Phuc Province","tax":"0.00000","SId":1224},"_1225":{"code":"YenBai","name":"Yen Bai Province","tax":"0.00000","SId":1225},"_1226":{"code":"ThuDoHaNoi","name":"Thu Do Ha Noi","tax":"0.00000","SId":1226}}},"195":{"name":"Solomon Islands","code":"SB","tax":"0.00000","threshold":"0.00","phone_code":677,"address_format":0,"labels":[],"provinces":[]},"196":{"name":"Somalia","code":"SO","tax":"0.00000","threshold":"0.00","phone_code":252,"address_format":0,"labels":[],"provinces":[]},"197":{"name":"South Africa","code":"ZA","tax":"0.00000","threshold":"0.00","phone_code":27,"address_format":4,"labels":{"address2":"市郊","province":"省"},"provinces":{"_758":{"code":"EC","name":"Eastern Cape","tax":"0.00000","SId":758},"_759":{"code":"FS","name":"Free State","tax":"0.00000","SId":759},"_760":{"code":"GT","name":"Gauteng","tax":"0.00000","SId":760},"_761":{"code":"NL","name":"KwaZulu-Natal","tax":"0.00000","SId":761},"_762":{"code":"LP","name":"Limpopo","tax":"0.00000","SId":762},"_763":{"code":"MP","name":"Mpumalanga","tax":"0.00000","SId":763},"_764":{"code":"NW","name":"North West","tax":"0.00000","SId":764},"_765":{"code":"NC","name":"Northern Cape","tax":"0.00000","SId":765},"_766":{"code":"WC","name":"Western Cape","tax":"0.00000","SId":766}}},"198":{"name":"South Georgia and The South Sandwich Islands","code":"GS","tax":"0.00000","threshold":"0.00","phone_code":0,"address_format":7,"labels":[],"provinces":[]},"256":{"name":"South Sudan","code":"SS","tax":"0.00000","threshold":"0.00","phone_code":211,"address_format":2,"labels":[],"provinces":[]},"200":{"name":"Sri Lanka","code":"LK","tax":"0.00000","threshold":"0.00","phone_code":94,"address_format":0,"labels":[],"provinces":[]},"122":{"name":"State of Libya","code":"LY","tax":"0.00000","threshold":"0.00","phone_code":218,"address_format":0,"labels":[],"provinces":[]},"201":{"name":"Sudan","code":"SD","tax":"0.00000","threshold":"0.00","phone_code":249,"address_format":1,"labels":[],"provinces":[]},"202":{"name":"Suriname","code":"SR","tax":"0.00000","threshold":"0.00","phone_code":597,"address_format":0,"labels":[],"provinces":[]},"203":{"name":"Svalbard and Jan Mayen","code":"SJ","tax":"0.00000","threshold":"0.00","phone_code":0,"address_format":1,"labels":[],"provinces":[]},"204":{"name":"Swaziland","code":"SZ","tax":"0.00000","threshold":"0.00","phone_code":268,"address_format":0,"labels":[],"provinces":[]},"205":{"name":"Sweden","code":"SE","tax":"0.00000","threshold":"0.00","phone_code":46,"address_format":1,"labels":{"address1":"街道和门牌号","city":"市/镇"},"provinces":[]},"207":{"name":"Syrian Arab Republic","code":"SY","tax":"0.00000","threshold":"0.00","phone_code":963,"address_format":0,"labels":[],"provinces":[]},"208":{"name":"TaiWan, China","code":"TW","tax":"0.00000","threshold":"0.00","phone_code":886,"address_format":4,"labels":{"city":"地区","province":"城市/县"},"provinces":{"_1141":{"code":"Taipei","name":"Taipei City","tax":"0.00000","SId":1141},"_1142":{"code":"Keelung","name":"Keelung City","tax":"0.00000","SId":1142},"_1143":{"code":"NewTaipei","name":"New Taipei City","tax":"0.00000","SId":1143},"_1144":{"code":"Yilan","name":"Yilan County","tax":"0.00000","SId":1144},"_1145":{"code":"HsinchuCity","name":"Hsinchu City","tax":"0.00000","SId":1145},"_1146":{"code":"HsinchuCounty","name":"Hsinchu County","tax":"0.00000","SId":1146},"_1147":{"code":"Taoyuan","name":"Taoyuan City","tax":"0.00000","SId":1147},"_1148":{"code":"Miaoli","name":"Miaoli County","tax":"0.00000","SId":1148},"_1149":{"code":"Taichung","name":"Taichung City","tax":"0.00000","SId":1149},"_1150":{"code":"Changhua","name":"Changhua County","tax":"0.00000","SId":1150},"_1151":{"code":"Nantou","name":"Nantou County","tax":"0.00000","SId":1151},"_1152":{"code":"ChiayiCity","name":"Chiayi City","tax":"0.00000","SId":1152},"_1153":{"code":"ChiayiCounty","name":"Chiayi County","tax":"0.00000","SId":1153},"_1154":{"code":"Yunlin","name":"Yunlin County","tax":"0.00000","SId":1154},"_1155":{"code":"Tainan","name":"Tainan City","tax":"0.00000","SId":1155},"_1156":{"code":"Kaohsiung","name":"Kaohsiung City","tax":"0.00000","SId":1156},"_1157":{"code":"Pingtung","name":"Pingtung County","tax":"0.00000","SId":1157},"_1158":{"code":"Taitung","name":"Taitung County","tax":"0.00000","SId":1158},"_1159":{"code":"Hualien","name":"Hualien County","tax":"0.00000","SId":1159},"_1160":{"code":"Penghu","name":"Penghu County","tax":"0.00000","SId":1160},"_1161":{"code":"Kinmen","name":"Kinmen County","tax":"0.00000","SId":1161},"_1162":{"code":"Lianjiang","name":"Lianjiang County","tax":"0.00000","SId":1162}}},"209":{"name":"Tajikistan","code":"TJ","tax":"0.00000","threshold":"0.00","phone_code":992,"address_format":1,"labels":[],"provinces":[]},"210":{"name":"Tanzania","code":"TZ","tax":"0.00000","threshold":"0.00","phone_code":255,"address_format":1,"labels":[],"provinces":[]},"211":{"name":"Thailand","code":"TH","tax":"0.00000","threshold":"0.00","phone_code":66,"address_format":4,"labels":{"province":"省","taxId":"个人或增值税号"},"provinces":{"_283":{"code":"AmnatCharoen","name":"Amnat Charoen","tax":"0.00000","SId":283},"_284":{"code":"AngThong","name":"Ang Thong","tax":"0.00000","SId":284},"_285":{"code":"Bangkok","name":"Bangkok","tax":"0.00000","SId":285},"_287":{"code":"BuengKan","name":"Bueng Kan","tax":"0.00000","SId":287},"_289":{"code":"BuriRam","name":"Buri Ram","tax":"0.00000","SId":289},"_291":{"code":"Chachoengsao","name":"Chachoengsao","tax":"0.00000","SId":291},"_293":{"code":"ChaiNat","name":"Chai Nat","tax":"0.00000","SId":293},"_294":{"code":"Chaiyaphum","name":"Chaiyaphum","tax":"0.00000","SId":294},"_296":{"code":"Chanthaburi","name":"Chanthaburi","tax":"0.00000","SId":296},"_298":{"code":"ChiangMai","name":"Chiang Mai","tax":"0.00000","SId":298},"_300":{"code":"ChiangRai","name":"Chiang Rai","tax":"0.00000","SId":300},"_302":{"code":"ChonBuri","name":"Chon Buri","tax":"0.00000","SId":302},"_304":{"code":"Chumphon","name":"Chumphon","tax":"0.00000","SId":304},"_306":{"code":"Kalasin","name":"Kalasin","tax":"0.00000","SId":306},"_308":{"code":"KamphaengPhet","name":"Kamphaeng Phet","tax":"0.00000","SId":308},"_310":{"code":"Kanchanaburi","name":"Kanchanaburi","tax":"0.00000","SId":310},"_311":{"code":"KhonKaen","name":"Khon Kaen","tax":"0.00000","SId":311},"_313":{"code":"Krabi","name":"Krabi","tax":"0.00000","SId":313},"_315":{"code":"Lampang","name":"Lampang","tax":"0.00000","SId":315},"_317":{"code":"Lamphun","name":"Lamphun","tax":"0.00000","SId":317},"_319":{"code":"Loei","name":"Loei","tax":"0.00000","SId":319},"_321":{"code":"LopBuri","name":"Lop Buri","tax":"0.00000","SId":321},"_322":{"code":"MaeHongSon","name":"Mae Hong Son","tax":"0.00000","SId":322},"_324":{"code":"MahaSarakham","name":"Maha Sarakham","tax":"0.00000","SId":324},"_326":{"code":"Mukdahan","name":"Mukdahan","tax":"0.00000","SId":326},"_327":{"code":"NakhonNayok","name":"Nakhon Nayok","tax":"0.00000","SId":327},"_329":{"code":"NakhonPathom","name":"Nakhon Pathom","tax":"0.00000","SId":329},"_331":{"code":"NakhonPhanom","name":"Nakhon Phanom","tax":"0.00000","SId":331},"_334":{"code":"NakhonRatchasima","name":"Nakhon Ratchasima","tax":"0.00000","SId":334},"_336":{"code":"NakhonSawan","name":"Nakhon Sawan","tax":"0.00000","SId":336},"_340":{"code":"NakhonSiThammarat","name":"Nakhon Si Thammarat","tax":"0.00000","SId":340},"_342":{"code":"Nan","name":"Nan","tax":"0.00000","SId":342},"_344":{"code":"Narathiwat","name":"Narathiwat","tax":"0.00000","SId":344},"_347":{"code":"NongBuaLamphu","name":"Nong Bua Lamphu","tax":"0.00000","SId":347},"_350":{"code":"NongKhai","name":"Nong Khai","tax":"0.00000","SId":350},"_352":{"code":"Nonthaburi","name":"Nonthaburi","tax":"0.00000","SId":352},"_356":{"code":"PathumThani","name":"Pathum Thani","tax":"0.00000","SId":356},"_358":{"code":"Pattani","name":"Pattani","tax":"0.00000","SId":358},"_362":{"code":"PhangNga","name":"Phang Nga","tax":"0.00000","SId":362},"_364":{"code":"Phatthalung","name":"Phatthalung","tax":"0.00000","SId":364},"_368":{"code":"Phatthaya","name":"Phatthaya","tax":"0.00000","SId":368},"_370":{"code":"Phayao","name":"Phayao","tax":"0.00000","SId":370},"_373":{"code":"Phetchabun","name":"Phetchabun","tax":"0.00000","SId":373},"_376":{"code":"Phetchaburi","name":"Phetchaburi","tax":"0.00000","SId":376},"_378":{"code":"Phichit","name":"Phichit","tax":"0.00000","SId":378},"_380":{"code":"Phitsanulok","name":"Phitsanulok","tax":"0.00000","SId":380},"_384":{"code":"PhraNakhonSiAyutthaya","name":"Phra Nakhon Si Ayutthaya","tax":"0.00000","SId":384},"_386":{"code":"Phrae","name":"Phrae","tax":"0.00000","SId":386},"_388":{"code":"Phuket","name":"Phuket","tax":"0.00000","SId":388},"_392":{"code":"PrachinBuri","name":"Prachin Buri","tax":"0.00000","SId":392},"_395":{"code":"PrachuapKhiriKhan","name":"Prachuap Khiri Khan","tax":"0.00000","SId":395},"_399":{"code":"Ranong","name":"Ranong","tax":"0.00000","SId":399},"_401":{"code":"Ratchaburi","name":"Ratchaburi","tax":"0.00000","SId":401},"_405":{"code":"Rayong","name":"Rayong","tax":"0.00000","SId":405},"_406":{"code":"RoiEt","name":"Roi Et","tax":"0.00000","SId":406},"_407":{"code":"SaKaeo","name":"Sa Kaeo","tax":"0.00000","SId":407},"_408":{"code":"SakonNakhon","name":"Sakon Nakhon","tax":"0.00000","SId":408},"_411":{"code":"SamutPrakan","name":"Samut Prakan","tax":"0.00000","SId":411},"_412":{"code":"SamutSakhon","name":"Samut Sakhon","tax":"0.00000","SId":412},"_413":{"code":"SamutSongkhram","name":"Samut Songkhram","tax":"0.00000","SId":413},"_414":{"code":"Saraburi","name":"Saraburi","tax":"0.00000","SId":414},"_415":{"code":"Satun","name":"Satun","tax":"0.00000","SId":415},"_417":{"code":"SiSaKet","name":"Si Sa Ket","tax":"0.00000","SId":417},"_419":{"code":"SingBuri","name":"Sing Buri","tax":"0.00000","SId":419},"_420":{"code":"Songkhla","name":"Songkhla","tax":"0.00000","SId":420},"_422":{"code":"Sukhothai","name":"Sukhothai","tax":"0.00000","SId":422},"_424":{"code":"SuphanBuri","name":"Suphan Buri","tax":"0.00000","SId":424},"_427":{"code":"SuratThani","name":"Surat Thani","tax":"0.00000","SId":427},"_430":{"code":"Surin","name":"Surin","tax":"0.00000","SId":430},"_433":{"code":"Tak","name":"Tak","tax":"0.00000","SId":433},"_435":{"code":"Trang","name":"Trang","tax":"0.00000","SId":435},"_437":{"code":"Trat","name":"Trat","tax":"0.00000","SId":437},"_439":{"code":"UbonRatchathani","name":"Ubon Ratchathani","tax":"0.00000","SId":439},"_443":{"code":"UdonThani","name":"Udon Thani","tax":"0.00000","SId":443},"_445":{"code":"UthaiThani","name":"Uthai Thani","tax":"0.00000","SId":445},"_450":{"code":"Uttaradit","name":"Uttaradit","tax":"0.00000","SId":450},"_452":{"code":"Yala","name":"Yala","tax":"0.00000","SId":452},"_453":{"code":"Yasothon","name":"Yasothon","tax":"0.00000","SId":453}}},"254":{"name":"The Commonwealth of Dominica","code":"DM","tax":"0.00000","threshold":"0.00","phone_code":1767,"address_format":0,"labels":[],"provinces":[]},"60":{"name":"The Dominican Republic","code":"DO","tax":"0.00000","threshold":"0.00","phone_code":1767,"address_format":1,"labels":[],"provinces":[]},"54":{"name":"The Republic of Croatia","code":"HR","tax":"0.00000","threshold":"0.00","phone_code":385,"address_format":1,"labels":[],"provinces":[]},"212":{"name":"Togo","code":"TG","tax":"0.00000","threshold":"0.00","phone_code":228,"address_format":2,"labels":[],"provinces":[]},"213":{"name":"Tokelau","code":"TK","tax":"0.00000","threshold":"0.00","phone_code":690,"address_format":0,"labels":[],"provinces":[]},"214":{"name":"Tonga","code":"TO","tax":"0.00000","threshold":"0.00","phone_code":676,"address_format":2,"labels":[],"provinces":[]},"215":{"name":"Trinidad and Tobago","code":"TT","tax":"0.00000","threshold":"0.00","phone_code":1868,"address_format":2,"labels":[],"provinces":[]},"257":{"name":"Tristan da Cunha","code":"TA","tax":"0.00000","threshold":"0.00","phone_code":290,"address_format":7,"labels":[],"provinces":[]},"216":{"name":"Tunisia","code":"TN","tax":"0.00000","threshold":"0.00","phone_code":216,"address_format":1,"labels":[],"provinces":[]},"217":{"name":"Turkey","code":"TR","tax":"0.00000","threshold":"0.00","phone_code":90,"address_format":1,"labels":[],"provinces":[]},"218":{"name":"Turkmenistan","code":"TM","tax":"0.00000","threshold":"0.00","phone_code":993,"address_format":18,"labels":[],"provinces":[]},"219":{"name":"Turks and Caicos Islands","code":"TC","tax":"0.00000","threshold":"0.00","phone_code":1649,"address_format":0,"labels":[],"provinces":[]},"220":{"name":"Tuvalu","code":"TV","tax":"0.00000","threshold":"0.00","phone_code":688,"address_format":2,"labels":[],"provinces":[]},"225":{"name":"U.S. Islands","code":"UM","tax":"0.00000","threshold":"0.00","phone_code":0,"address_format":0,"labels":[],"provinces":[]},"221":{"name":"Uganda","code":"UG","tax":"0.00000","threshold":"0.00","phone_code":256,"address_format":2,"labels":[],"provinces":[]},"222":{"name":"Ukraine","code":"UA","tax":"0.00000","threshold":"0.00","phone_code":380,"address_format":0,"labels":[],"provinces":[]},"223":{"name":"United Arab Emirates","code":"AE","tax":"0.00000","threshold":"0.00","phone_code":971,"address_format":9,"labels":{"province":"酋长国"},"provinces":{"_702":{"code":"AZ","name":"Abu Dhabi","tax":"0.00000","SId":702},"_703":{"code":"AJ","name":"Ajman","tax":"0.00000","SId":703},"_704":{"code":"DU","name":"Dubai","tax":"0.00000","SId":704},"_705":{"code":"FU","name":"Fujairah","tax":"0.00000","SId":705},"_706":{"code":"RK","name":"Ras al-Khaimah","tax":"0.00000","SId":706},"_707":{"code":"SH","name":"Sharjah","tax":"0.00000","SId":707},"_708":{"code":"UQ","name":"Umm al-Quwain","tax":"0.00000","SId":708}}},"227":{"name":"Uruguay","code":"UY","tax":"0.00000","threshold":"0.00","phone_code":598,"address_format":1,"labels":[],"provinces":[]},"228":{"name":"US Virgin Islands","code":"VI","tax":"0.00000","threshold":"0.00","phone_code":1340,"address_format":0,"labels":[],"provinces":[]},"230":{"name":"Uzbekistan","code":"UZ","tax":"0.00000","threshold":"0.00","phone_code":998,"address_format":0,"labels":[],"provinces":[]},"231":{"name":"Vanuatu","code":"VU","tax":"0.00000","threshold":"0.00","phone_code":678,"address_format":2,"labels":[],"provinces":[]},"238":{"name":"Vatican City State","code":"VA","tax":"0.00000","threshold":"0.00","phone_code":379,"address_format":8,"labels":[],"provinces":[]},"232":{"name":"Venezuela","code":"VE","tax":"0.00000","threshold":"0.00","phone_code":58,"address_format":0,"labels":[],"provinces":[]},"234":{"name":"Wallis and Futuna Islands","code":"WF","tax":"0.00000","threshold":"0.00","phone_code":681,"address_format":1,"labels":[],"provinces":[]},"235":{"name":"Western Sahara","code":"EH","tax":"0.00000","threshold":"0.00","phone_code":210,"address_format":0,"labels":[],"provinces":[]},"236":{"name":"Western Samoa","code":"WS","tax":"0.00000","threshold":"0.00","phone_code":685,"address_format":0,"labels":[],"provinces":[]},"237":{"name":"Yemen","code":"YE","tax":"0.00000","threshold":"0.00","phone_code":967,"address_format":2,"labels":[],"provinces":[]},"239":{"name":"Zambia","code":"ZM","tax":"0.00000","threshold":"0.00","phone_code":260,"address_format":1,"labels":[],"provinces":[]},"240":{"name":"Zimbabwe","code":"ZW","tax":"0.00000","threshold":"0.00","phone_code":263,"address_format":2,"labels":[],"provinces":[]}};
                window.AddressDefaultInfo = {"attributes":{"firstName":{"required":"required"},"address2":{"required":""},"phone":{"required":"required"}},"labels":{"address1":"地址","address2":"公寓、套房等","city":"城市","country":"国家/地区","firstName":"名","lastName":"姓","phone":"电话","province":"地区","zip":"邮政编码","taxId":"税号"},"address_format":["{firstName}{lastName}_{company}_{address1}_{address2}_{city}_{country}{zip}_{phone}","{firstName}{lastName}_{company}_{address1}_{address2}_{zip}{city}_{country}_{phone}","{firstName}{lastName}_{company}_{address1}_{address2}_{city}_{country}_{phone}","{firstName}{lastName}_{company}_{address1}_{address2}_{zip}{city}_{province}{country}_{phone}","{firstName}{lastName}_{company}_{address1}_{address2}_{city}_{country}{province}{zip}_{phone}","{firstName}{lastName}_{company}_{address1}_{address2}_{zip}{city}_{province}_{country}_{phone}","{firstName}{lastName}_{company}_{address1}_{address2}_{zip}{city}{country}_{phone}","{firstName}{lastName}_{company}_{address1}_{address2}_{country}{zip}_{phone}","{firstName}{lastName}_{company}_{address1}_{address2}_{zip}{country}_{phone}","{firstName}{lastName}_{company}_{address1}_{address2}_{city}_{country}{province}_{phone}","{firstName}{lastName}_{company}_{address1}_{address2}_{city}_{province}{zip}_{country}_{phone}","{firstName}{lastName}_{company}_{address1}_{address2}_{zip}{city}{province}_{country}_{phone}","{lastName}{firstName}_{company}_{country}{zip}_{province}_{city}_{address1}_{address2}_{phone}","{zip}{city}_{address2}_{address1}_{company}_{firstName}{lastName}_{country}_{phone}","{firstName}{lastName}_{company}_{address1}_{address2}_{city}{country}_{phone}","{firstName}{lastName}_{company}_{address1}_{address2}_{zip}_{city}_{country}_{phone}","{firstName}{lastName}_{company}_{address1}_{address2}_{zip}_{country}_{phone}","{company}_{lastName}{firstName}_{zip}_{country}_{province}{city}_{address1}_{address2}_{phone}","{zip}{city}_{country}_{firstName}{lastName}_{company}_{address1}_{address2}_{phone}","{country}_{firstName}{lastName}_{company}_{address1}_{address2}_{city}{zip}_{phone}","{country}_{firstName}{lastName}_{company}_{address1}_{address2}_{zip}{city}_{phone}","{country}_{firstName}{lastName}_{company}_{address1}_{address2}_{city}_{phone}","{country}_{firstName}{lastName}_{company}_{address1}_{address2}_{zip}{city}{province}_{phone}","{country}_{firstName}{lastName}_{company}_{address1}_{address2}_{city}{province}{zip}_{phone}","{country}_{firstName}{lastName}_{company}_{address1}_{address2}_{zip}{city}_{province}_{phone}","{country}_{firstName}{lastName}_{company}_{address1}_{address2}_{zip}_{phone}","{country}_{firstName}{lastName}_{company}_{address1}_{address2}_{city}{province}_{phone}","{country}_{firstName}{lastName}_{company}_{address1}_{address2}_{city}_{province}{zip}_{phone}","{country}_{lastName}{firstName}_{company}_{zip}{province}_{city}_{address1}_{address2}_{phone}","{country}_{zip}{city}_{address2}_{address1}_{company}_{firstName}{lastName}_{phone}","{country}_{firstName}{lastName}_{company}_{address1}_{address2}_{zip}_{city}_{phone}","{country}_{company}_{lastName}{firstName}_{zip}_{province}{city}_{address1}_{address2}_{phone}","{country}_{zip}{city}_{firstName}{lastName}_{company}_{address1}_{address2}_{phone}"]};
                window.ShippingInfo = '[]';

        });
    </script>

}