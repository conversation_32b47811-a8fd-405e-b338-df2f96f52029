@model YseStoreAdmin.Pages.CodeEdit.EditIndexModel

<ul class="list-group">
    @foreach (var history in Model.FileHistories)
    {
        <li class="list-group-item history-item" data-id="@history.Id">
            <div class="d-flex justify-content-between align-items-center flex-wrap">
                <div class="mb-2 mb-md-0">
                    <span class="badge badge-@(history.OperationType == "1" ? "success" : (history.OperationType == "2" ? "primary" : "danger"))">
                        @history.OperationTypeText
                    </span>
                    <span class="ml-2 text-nowrap">
                        <i class="far fa-clock mr-1" style="color: #6c757d;"></i>
                        <small>@history.CreatedAt.ToString("yyyy-MM-dd HH:mm:ss")</small>
                    </span>
                    <span class="ml-2 text-nowrap">
                        <i class="far fa-user mr-1" style="color: #6c757d;"></i>
                        <small>@history.CreatedBy</small>
                    </span>
                </div>
                <div class="d-flex">
                    @* <button type="button" class="btn btn-sm btn-outline-primary view-history mr-2" data-id="@history.Id"> *@
                    @*     <i class="far fa-eye mr-1"></i> 查看 *@
                    @* </button> *@
                    <button type="button" class="btn btn-sm btn-outline-success apply-history" data-id="@history.Id">
                        <i class="fas fa-arrow-right mr-1"></i> 应用/查看
                    </button>
                </div>
            </div>
            @if (!string.IsNullOrWhiteSpace(history.Comment))
            {
                <div class="history-comment mt-1">
                    <i class="far fa-comment-dots mr-1"></i>
                    <small>@history.Comment</small>
                </div>
            }
        </li>
    }
</ul> 