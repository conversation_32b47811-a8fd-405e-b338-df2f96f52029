@model List<YseStore.Model.VM.CodeFileTreeVM>

<div class="file-tree-panel">
    <div class="file-tree-header">
        <h5>文件浏览器</h5>
        <button type="button" class="btn-close" id="file-tree-close">×</button>
    </div>
    <div class="file-tree-search">
        <input type="text" class="form-control form-control-sm" placeholder="搜索文件..." id="file-tree-search-input">
    </div>
    <div class="file-tree-content">
        <ul class="file-tree">
            @foreach (var item in Model)
            {
                await RenderTreeNodeAsync(item, 0);
            }
        </ul>
    </div>
</div>

@functions {
    private async Task RenderTreeNodeAsync(YseStore.Model.VM.CodeFileTreeVM node, int level)
    {
        string indentClass = $"indent-level-{level}";
        string fileType = System.IO.Path.GetExtension(node.Name).ToLowerInvariant();
        
        <li class="@(node.IsDirectory ? "folder-item" : "file-item") @indentClass" @(node.IsDirectory ? "" : $"data-type=\"{fileType}\"") data-path="@node.Path">
            @if (node.IsDirectory)
            {
                <a href="javascript:void(0);" 
                   class="toggle-folder"
                   hx-get="/CodeEdit/EditIndex?handler=ToggleFolder&path=@Uri.EscapeDataString(node.Path)"
                   hx-target="closest li"
                   hx-swap="outerHTML"
                   hx-indicator=".folder-loading"
                   hx-trigger="none">
                    <span class="folder-toggle-icon">
                        <i class="fa fa-folder"></i>
                    </span>
                    <span class="file-name">@node.Name</span>
                    <span class="folder-loading"><i class="fa fa-spinner fa-spin"></i></span>
                </a>
                
                @if (node.Children != null && node.Children.Count > 0)
                {
                    <ul class="child-files" style="display: none;">
                        @foreach (var child in node.Children)
                        {
                            await RenderTreeNodeAsync(child, level + 1);
                        }
                    </ul>
                }
            }
            else
            {
                <a href="/CodeEdit/EditIndex?path=@Uri.EscapeDataString(node.Path)"
                   hx-get="/CodeEdit/EditIndex?path=@Uri.EscapeDataString(node.Path)"
                   hx-target=".editor-wrapper"
                   hx-swap="innerHTML"
                   hx-push-url="true"
                   data-path="@node.Path"
                   class="file-link">
                    <span class="file-icon">
                        <i class="fa fa-file-code-o"></i>
                    </span>
                    <span class="file-name">@node.Name</span>
                </a>
            }
        </li>
    }
} 