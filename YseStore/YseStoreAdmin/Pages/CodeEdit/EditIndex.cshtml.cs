using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using YseStore.Common.Cache;
using YseStore.IService;
using YseStore.Model.VM;

namespace YseStoreAdmin.Pages.CodeEdit;

public class EditIndexModel : PageModel
{
    private readonly ICodeEditService _codeEditService;
    private readonly ICaching _caching;

    // 缓存键常量
    private const string CACHE_KEY_FILE_CONTENT = "CodeEdit_FileContent";
    private const string CACHE_KEY_FILE_TREE = "CodeEdit_FileTree";
    private const string CACHE_KEY_FILE_HISTORY = "CodeEdit_FileHistory";

    [BindProperty]
    public string Path { get; set; }

    [BindProperty]
    public string FileName { get; set; }

    [BindProperty]
    public string FileType { get; set; }

    [BindProperty]
    public string FileContent { get; set; }

    [BindProperty]
    public string Comment { get; set; } = "通过在线编辑器保存";

    public bool IsSuccess { get; set; }
    public string StatusMessage { get; set; }
    public DateTime? LastModified { get; set; }
    public List<CodeFileHistoryVM> FileHistories { get; set; } = new List<CodeFileHistoryVM>();

    public EditIndexModel(ICodeEditService codeEditService, ICaching caching)
    {
        _codeEditService = codeEditService;
        _caching = caching;
    }

    public async Task<IActionResult> OnGetAsync(string path)
    {
        if (string.IsNullOrEmpty(path))
        {
            // 如果没有指定路径，提供一个示例内容
            FileContent = "// 这是一个示例代码文件\nfunction example() {\n    console.log('Hello, world!');\n}";
            FileType = ".js";
            return Page();
        }

        Path = path;
        
        try
        {
            // 尝试从缓存中获取文件内容
            var fileContent = _caching.HashGet<CodeFileContentVM>(CACHE_KEY_FILE_CONTENT, path);
            
            // 缓存中没有数据，从服务获取
            if (fileContent == null)
            {
                fileContent = await _codeEditService.GetFileContent(path);
                if (fileContent == null)
                {
                    StatusMessage = "文件不存在或无法读取";
                    return Page();
                }
                
                // 将文件内容存入缓存
                _caching.HashSet(CACHE_KEY_FILE_CONTENT, path, fileContent);
            }

            FileName = fileContent.Name;
            FileContent = fileContent.Content;
            FileType = fileContent.FileType;
            LastModified = fileContent.LastModified;
        }
        catch (Exception ex)
        {
            StatusMessage = $"读取文件时发生错误: {ex.Message}";
        }

        return Page();
    }

    public async Task<IActionResult> OnPostAsync()
    {
        if (string.IsNullOrEmpty(Path))
        {
            IsSuccess = false;
            StatusMessage = "未指定文件路径";
            return Page();
        }

        try
        {
            // 使用 CodeEditService 保存文件
            IsSuccess = await _codeEditService.SaveFileContent(Path, FileContent, Comment, User.Identity.Name ?? "system");
            StatusMessage = IsSuccess ? "文件已保存" : "保存文件失败";

            if (IsSuccess)
            {
                // 清除相关缓存
                _caching.HashRemove(CACHE_KEY_FILE_CONTENT, Path);
                await ClearFileTreeCacheAsync();
                await ClearFileHistoryCacheAsync(Path);
                
                // 重新获取文件信息
                var fileContent = await _codeEditService.GetFileContent(Path);
                if (fileContent != null)
                {
                    FileName = fileContent.Name;
                    FileType = fileContent.FileType;
                    LastModified = fileContent.LastModified;
                    
                    // 更新缓存
                    _caching.HashSet(CACHE_KEY_FILE_CONTENT, Path, fileContent);
                }
            }
        }
        catch (Exception ex)
        {
            IsSuccess = false;
            StatusMessage = $"保存文件时发生错误: {ex.Message}";
        }

        return Page();
    }

    // 获取文件历史记录
    public async Task<IActionResult> OnGetHistoryAsync(string path)
    {
        Console.WriteLine($"接收到历史记录请求，路径: {path}");
        
        if (string.IsNullOrEmpty(path))
        {
            Console.WriteLine("路径为空，返回提示");
            return Content("<div class=\"alert alert-warning\">未指定文件路径</div>");
        }

        try
        {
            Path = path;
            Console.WriteLine($"开始获取历史记录，路径: {path}");
            
            // 尝试从缓存获取历史记录
            var histories = _caching.HashGet<List<CodeFileHistoryVM>>(CACHE_KEY_FILE_HISTORY, path);
            
            // 缓存中没有数据，从服务获取
            if (histories == null)
            {
                histories = await _codeEditService.GetFileHistory(path);
                
                // 将历史记录存入缓存
                if (histories != null)
                {
                    _caching.HashSet(CACHE_KEY_FILE_HISTORY, path, histories);
                }
            }
            
            FileHistories = histories ?? new List<CodeFileHistoryVM>();
            Console.WriteLine($"获取到 {FileHistories?.Count ?? 0} 条历史记录");
            
            // 返回历史记录列表HTML，而不是整个分部视图
            if (FileHistories?.Any() == true)
            {
                Console.WriteLine("返回历史记录列表");
                return Partial("_HistoryListPartial", this);
            }
            else
            {
                Console.WriteLine("没有历史记录");
                return Content("<div class=\"alert alert-info\">没有历史记录</div>");
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"获取历史记录失败: {ex.Message}");
            return Content($"<div class=\"alert alert-danger\">获取历史记录失败: {ex.Message}</div>");
        }
    }

    // 获取历史版本内容
    public async Task<IActionResult> OnGetHistoryContentAsync(long historyId)
    {
        try
        {
            var content = await _codeEditService.GetHistoryContent(historyId);
            if (content == null)
            {
                return NotFound("历史版本不存在");
            }
            return Content(content);
        }
        catch (Exception ex)
        {
            return BadRequest($"获取历史版本内容失败: {ex.Message}");
        }
    }

    // 恢复到历史版本
    public async Task<IActionResult> OnPostRestoreHistoryAsync(long historyId)
    {
        try
        {
            IsSuccess = await _codeEditService.RestoreHistoryVersion(historyId, User.Identity.Name ?? "system");
            StatusMessage = IsSuccess ? "已恢复到历史版本" : "恢复历史版本失败";
            
            if (IsSuccess)
            {
                // 清除相关缓存
                _caching.HashRemove(CACHE_KEY_FILE_CONTENT, Path);
                await ClearFileHistoryCacheAsync(Path);
            }
        }
        catch (Exception ex)
        {
            IsSuccess = false;
            StatusMessage = $"恢复历史版本时发生错误: {ex.Message}";
        }

        // 重定向回编辑页面
        return RedirectToPage("./EditIndex", new { path = Path });
    }

    // 创建文件
    public async Task<IActionResult> OnPostCreateFileAsync(string path, string content)
    {
        if (string.IsNullOrEmpty(path))
        {
            return BadRequest("未指定文件路径");
        }

        try
        {
            IsSuccess = await _codeEditService.CreateFile(path, content ?? string.Empty, User.Identity.Name ?? "system");
            StatusMessage = IsSuccess ? "文件创建成功" : "文件创建失败";
            
            if (IsSuccess)
            {
                // 清除文件树缓存
                await ClearFileTreeCacheAsync();
            }
            
            return RedirectToPage("./EditIndex", new { path });
        }
        catch (Exception ex)
        {
            return BadRequest($"创建文件失败: {ex.Message}");
        }
    }

    // 创建目录
    public async Task<IActionResult> OnPostCreateDirectoryAsync(string path)
    {
        if (string.IsNullOrEmpty(path))
        {
            return BadRequest("未指定目录路径");
        }

        try
        {
            IsSuccess = await _codeEditService.CreateDirectory(path, User.Identity.Name ?? "system");
            StatusMessage = IsSuccess ? "目录创建成功" : "目录创建失败";
            
            if (IsSuccess)
            {
                // 清除文件树缓存
                await ClearFileTreeCacheAsync();
            }
            
            return RedirectToPage("./EditIndex");
        }
        catch (Exception ex)
        {
            return BadRequest($"创建目录失败: {ex.Message}");
        }
    }

    // 删除文件或目录
    public async Task<IActionResult> OnPostDeleteAsync(string path)
    {
        if (string.IsNullOrEmpty(path))
        {
            return BadRequest("未指定路径");
        }

        try
        {
            IsSuccess = await _codeEditService.DeleteFileOrDirectory(path, User.Identity.Name ?? "system");
            StatusMessage = IsSuccess ? "删除成功" : "删除失败";
            
            if (IsSuccess)
            {
                // 清除相关缓存
                _caching.HashRemove(CACHE_KEY_FILE_CONTENT, path);
                _caching.HashRemove(CACHE_KEY_FILE_HISTORY, path);
                await ClearFileTreeCacheAsync();
            }
            
            return RedirectToPage("./EditIndex");
        }
        catch (Exception ex)
        {
            return BadRequest($"删除失败: {ex.Message}");
        }
    }

    // 重命名文件或目录
    public async Task<IActionResult> OnPostRenameAsync(string oldPath, string newPath)
    {
        if (string.IsNullOrEmpty(oldPath) || string.IsNullOrEmpty(newPath))
        {
            return BadRequest("路径不能为空");
        }

        try
        {
            IsSuccess = await _codeEditService.RenameFileOrDirectory(oldPath, newPath, User.Identity.Name ?? "system");
            StatusMessage = IsSuccess ? "重命名成功" : "重命名失败";
            
            if (IsSuccess)
            {
                // 清除相关缓存
                _caching.HashRemove(CACHE_KEY_FILE_CONTENT, oldPath);
                _caching.HashRemove(CACHE_KEY_FILE_HISTORY, oldPath);
                await ClearFileTreeCacheAsync();
            }
            
            return RedirectToPage("./EditIndex", new { path = newPath });
        }
        catch (Exception ex)
        {
            return BadRequest($"重命名失败: {ex.Message}");
        }
    }

    // 历史部分视图
    public IActionResult OnGetHistoryPartialAsync()
    {
        try
        {
            return Partial("_HistoryPartial", this);
        }
        catch (Exception ex)
        {
            return Content($"<div class=\"alert alert-danger\">加载历史部分视图失败: {ex.Message}</div>");
        }
    }

    // 返回文件树部分视图
    public async Task<IActionResult> OnGetFileTreePartialAsync()
    {
        try
        {
            var fileTree = await GetFileTreeFromCacheAsync();
            return Partial("_FileTreePartial", fileTree);
        }
        catch (Exception ex)
        {
            return Content($"<div class=\"alert alert-danger\">获取文件树失败: {ex.Message}</div>");
        }
    }

    // 切换文件夹展开/折叠状态
    public async Task<IActionResult> OnGetToggleFolderAsync(string path)
    {
        if (string.IsNullOrEmpty(path))
        {
            return BadRequest("未指定文件夹路径");
        }

        try
        {
            // 获取完整的文件树
            var fileTree = await GetFileTreeFromCacheAsync();
            
            // 找到指定路径的节点
            var node = FindNodeByPath(fileTree, path);
            
            if (node == null || !node.IsDirectory)
            {
                return BadRequest("指定路径不是有效的文件夹");
            }
            
            // 渲染单个节点HTML
            return Partial("_FolderNodePartial", node);
        }
        catch (Exception ex)
        {
            return Content($"<div class=\"alert alert-danger\">切换文件夹状态失败: {ex.Message}</div>");
        }
    }

    // 辅助方法：根据路径查找节点
    private CodeFileTreeVM FindNodeByPath(List<CodeFileTreeVM> tree, string path)
    {
        foreach (var node in tree)
        {
            if (node.Path == path)
            {
                return node;
            }
            
            if (node.IsDirectory && node.Children != null && node.Children.Count > 0)
            {
                var found = FindNodeByPath(node.Children, path);
                if (found != null)
                {
                    return found;
                }
            }
        }
        
        return null;
    }

    // 获取文件树
    public async Task<IActionResult> OnGetFileTreeAsync()
    {
        try
        {
            var fileTree = await GetFileTreeFromCacheAsync();
            return new JsonResult(fileTree);
        }
        catch (Exception ex)
        {
            return BadRequest($"获取文件树失败: {ex.Message}");
        }
    }
    
    // 从缓存获取文件树，如果缓存不存在则从服务获取
    private async Task<List<CodeFileTreeVM>> GetFileTreeFromCacheAsync()
    {
        // 尝试从缓存获取文件树
        var fileTree = _caching.Get<List<CodeFileTreeVM>>(CACHE_KEY_FILE_TREE);
        
        // 缓存中没有数据，从服务获取
        if (fileTree == null)
        {
            fileTree = await _codeEditService.GetFileTree();
            
            // 将文件树存入缓存，有效期1小时
            if (fileTree != null)
            {
                _caching.Set(CACHE_KEY_FILE_TREE, fileTree, TimeSpan.FromHours(1));
            }
        }
        
        return fileTree ?? new List<CodeFileTreeVM>();
    }
    
    // 清除文件树缓存
    private async Task ClearFileTreeCacheAsync()
    {
        await _caching.DelByPatternAsync(CACHE_KEY_FILE_TREE);
    }
    
    // 清除文件历史记录缓存
    private async Task ClearFileHistoryCacheAsync(string path)
    {
        if (!string.IsNullOrEmpty(path))
        {
            _caching.HashRemove(CACHE_KEY_FILE_HISTORY, path);
        }
    }
}