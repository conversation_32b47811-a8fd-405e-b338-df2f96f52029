using Entitys;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using SqlSugar.Extensions;
using System.Text;
using YseStore.IService.Customer;

namespace YseStoreAdmin.Controllers.Customer
{
    [ApiController]
    [Route("api/[controller]")]
    public class ShippingController : ControllerBase
    {
        private readonly ICustomerListService _customerListService;
        private readonly ILogger<CodeEditController> _logger;

        public ShippingController(ICustomerListService customerListService, ILogger<CodeEditController> logger)
        {
            _customerListService = customerListService;
            _logger = logger;
        }
        [HttpGet("view-cart")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> EmailTemplatePreview()
        {
            var userId = Request.Query["UserId"].ObjToInt();
            StringBuilder content = new StringBuilder();
            var ShoppingCart = await _customerListService.GetShoppingCart(userId);
            if (ShoppingCart != null)
            {
                foreach (var item in ShoppingCart)
                {
                    var keyValues = ExtractKeyValues(item.Property);
                    content.AppendFormat(@"
                                            <tr>
                                                <td>
                                                    <div class='btn_checkbox'>
                                                        <em class='button'></em>
                                                        <input type='checkbox' name='select' value='{0}'>
                                                    </div>
                                                </td>
                                                <td class='info'>
                                                    <div class='img fl'>
                                                        <img src='{1}' alt='{2}'>
                                                    </div>
                                                    <div class='title fl'><p>{2}</p></div>
                                                    <div class='clear'></div>
                                                </td>
                                                <td>
                                                    <p>SKU:{3}</p>
                                                    <p>
                                                        {4}
                                                    </p>
                                                    <div class='custom_attr'></div>
                                                </td>
                                                <td>${5}</td>
                                                <td>{6}</td>
                                                <td>${7}</td>
                                                <td nowrap='nowrap'>
                                                    {8}<br>
                                                    {9}
                                                </td>
                                            </tr>",
                                            item.CId,
                                            item.PicPath,
                                            item.ProductsName,
                                            item.SKU,
                                            keyValues[0].Key+":"+keyValues[0].Value,
                                            item.Price.ObjToString("F2"),
                                            item.Qty,
                                            (item.Price * item.Qty).ObjToString("F2"),
                                            DateTimeOffset.FromUnixTimeSeconds(Convert.ToInt64(item.AddTime)).ToString("yyyy-MM-dd"),
                                            DateTimeOffset.FromUnixTimeSeconds(Convert.ToInt64(item.AddTime)).ToString("HH:mm:ss")
                                        );
                }
            }
            //var contents = "  <tr><td><div class=\"btn_checkbox \"><em class=\"button\"></em><input type=\"checkbox\" name=\"select\" value=\"49\" /></div></td><td class=\"info\"><div class=\"img fl\"><img src=\"/u_file/2502/11/products/da7fe9c5dd.jpg?x-oss-process=image/format,webp/resize,m_lfit,,h_168,w_168\" alt=\"Retevis EZTalk 62 Dual Band VHF/UHF Waterproof 2 Way Radio for Hunting\"></div><div class=\"title fl\"><p>Retevis EZTalk 62 Dual Band VHF/UHF Waterproof 2 Way Radio for Hunting</p></div><div class=\"clear\"></div></td><td><p>SKU: US-A9278B-C9034A</p><p>Service Type: 1 Pack(U/VHF)</p><div class=\"custom_attr\"></div></td><td>$55.99</td><td>3</td><td>$167.97</td><td nowrap=\"nowrap\">2025-02-26<br />13:48:05</td></tr><tr><td><div class=\"btn_checkbox \"><em class=\"button\"></em><input type=\"checkbox\" name=\"select\" value=\"47\" /></div></td><td class=\"info\"><div class=\"img fl\"><img src=\"/u_file/2502/11/products/5847970a73.jpg?x-oss-process=image/format,webp/resize,m_lfit,,h_168,w_168\" alt=\"RA79 50-600MHz RX Aviation Band 5W Ham Radio (US Ver.)\"></div><div class=\"title fl\"><p>RA79 50-600MHz RX Aviation Band 5W Ham Radio (US Ver.)</p></div><div class=\"clear\"></div></td><td><p>SKU: US-A9267A-C9034A</p><p>Bundle Sale: 1Pack</p><div class=\"custom_attr\"></div></td><td>$40.99</td><td>2</td><td>$81.98</td><td nowrap=\"nowrap\">2025-02-25<br />11:59:26</td></tr><tr><td><div class=\"btn_checkbox \"><em class=\"button\"></em><input type=\"checkbox\" name=\"select\" value=\"43\" /></div></td><td class=\"info\"><div class=\"img fl\"><img src=\"/u_file/2502/11/products/da7fe9c5dd.jpg?x-oss-process=image/format,webp/resize,m_lfit,,h_168,w_168\" alt=\"Retevis EZTalk 62 Dual Band VHF/UHF Waterproof 2 Way Radio for Hunting\"></div><div class=\"title fl\"><p>Retevis EZTalk 62 Dual Band VHF/UHF Waterproof 2 Way Radio for Hunting</p></div><div class=\"clear\"></div></td><td><p>SKU: US-A9278A-C9034A</p><p>Service Type: 1 Pack(GMRS)</p><div class=\"custom_attr\"></div></td><td>$49.99</td><td>2</td><td>$99.98</td><td nowrap=\"nowrap\">2025-02-11<br />14:44:05</td></tr> ";
            //var result = MessageModel<string>.Success(content.ToString());
            return Ok(new { ret = true, msg = new { Html = content.ToString() } });
            //return base.Ok(result);
        }



        [HttpGet("view-favorite")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> GetFavoriteProducts()
        {
            var userId = Request.Query["UserId"].ObjToInt();
            StringBuilder content = new StringBuilder();
            var UserOrdersProductsList = await _customerListService.GetFavoriteProducts(userId);
            if (UserOrdersProductsList != null)
            {
                foreach (var item in UserOrdersProductsList)
                {
                    content.AppendFormat(@"
                                            <tr>
                                                <td class='info'>
                                                    <div class='img fl'>
                                                        <img src='{0}' alt='{1}'>
                                                    </div>
                                                    <div class='title fl'>{1}</div>
                                                    <div class='clear'></div>
                                                </td>
                                                <td nowrap='nowrap'>
                                                    {2}<br>
                                                    {3}
                                                </td>
                                            </tr>",
                                             item.PicPath_0,
                                             item.ProductsName,
                                             DateTimeOffset.FromUnixTimeSeconds(Convert.ToInt64(item.AccTime)).ToString("yyyy-MM-dd"),
                                             DateTimeOffset.FromUnixTimeSeconds(Convert.ToInt64(item.AccTime)).ToString("HH:mm:ss")
                                         );
                }
            }
            return Ok(new { ret = true, msg = new { Html = content.ToString() } });
        }




        private string GetServiceType(string property)
        {
            if (string.IsNullOrEmpty(property)) return "";

            try
            {
                var json = JObject.Parse(property);
                return json["Service Type"]?.ToString() ?? "";
            }
            catch
            {
                return "";
            }
        }


        public static List<KeyValuePair<string, string>> ExtractKeyValues(string json)
        {
            var result = new List<KeyValuePair<string, string>>();

            if (string.IsNullOrEmpty(json))
            {
                return result; // 空值直接返回
            }

            try
            {
                var token = JToken.Parse(json);

                switch (token.Type)
                {
                    case JTokenType.Object:
                        // 处理 JSON 对象
                        foreach (var child in token.Children<JProperty>())
                        {
                            result.Add(new KeyValuePair<string, string>(
                                child.Name,
                                child.Value.ToString()
                            ));
                        }
                        break;

                    case JTokenType.Array:
                        // 处理 JSON 数组（遍历每个元素）
                        foreach (var item in token.Children())
                        {
                            result.AddRange(ExtractKeyValues(item.ToString()));
                        }
                        break;

                    default:
                        // 其他类型（如简单值）
                        // 这里可以根据需要添加处理逻辑
                        break;
                }
            }
            catch (JsonReaderException)
            {
                // 处理无效的 JSON 格式
                // 可以选择记录日志或返回空列表
            }

            return result;
        }
    }


}

