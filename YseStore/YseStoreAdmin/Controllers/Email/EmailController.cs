using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using YseStore.IService.manage;
using YseStore.IService;
using YseStoreAdmin.Controllers.Sales;
using YseStore.IService.Email;
using YseStore.Model.VM.Email;
using YseStore.IService.SiteSystem;
using YseStore.Common.Helper;
using YseStore.Common;
using YseStoreAdmin.Pages.Components.Orders;
using Entitys;
using FluentEmail.Core;
using ZXing;
using YseStoreAdmin.Pages.Components.Setting;
using YseStoreControls.Pages.Components.Pager;
using YseStore.Common.Cache;
using YseStore.IService.Email.SendCloud;
using Flurl.Http;

namespace YseStoreAdmin.Controllers.Email
{
    [Route("api/[controller]")]
    [ApiController]
    public class EmailController : ControllerBase
    {

        private readonly ISystemEmailTplServices _emailTplService;
        private readonly ILogger<CouponController> _logger;
        private readonly IConfigService _configService;
        private readonly IManageOperationLogService _logSer;
        private readonly IManageServices _manageServices;
        private readonly IEmailRecipientsServices _emailRecipientsServices;
        private readonly ILanguageServices _languageServices;
        private readonly ICaching _caching;
        private readonly IEmailService _emailService;

        public EmailController(ISystemEmailTplServices emailTplService, ILogger<CouponController> logger, IConfigService configService, IManageOperationLogService logSer, IManageServices manageServices, IEmailRecipientsServices emailRecipientsServices, ILanguageServices languageServices, ICaching caching, IEmailService emailService)
        {
            _emailTplService = emailTplService;
            _logger = logger;
            _configService = configService;
            _logSer = logSer;
            _manageServices = manageServices;
            _emailRecipientsServices = emailRecipientsServices;
            _languageServices = languageServices;
            _caching = caching;
            _emailService = emailService;
        }

        /*
         * 获取详情
         * 保存发件人
         * 保存邮件模板
         * 邮件模板开启关闭
         * 
         * 获取收件人列表
         * 
         */



        /// <summary>
        /// 保存发件人设置 
        /// </summary>
        /// <returns></returns>
        [HttpPost("set")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> SetEmail()
        {

            try
            {

                var FromEmail = Request.GetFormString("FromEmail");
                var FromName = Request.GetFormString("FromName");
                var EmailType = Request.GetFormString("EmailType");
                var SmtpUserName = Request.GetFormString("SmtpUserName");
                var SmtpPassword = Request.GetFormString("SmtpPassword");
                var SmtpHost = Request.GetFormString("SmtpHost");
                var SmtpPort = Request.GetFormString("SmtpPort");
                var SmtpType = Request.GetFormString("SmtpType");

                var SC_ApiUser = Request.GetFormString("SC_ApiUser");
                var SC_ApiKey = Request.GetFormString("SC_ApiKey");

                //
                Dictionary<string, string> dict = new Dictionary<string, string>
                {
                    ["FromEmail"] = FromEmail,
                    ["FromName"] = FromName,
                    ["EmailType"] = EmailType,
                };

                if (EmailType == "Sendcloud")
                {
                    dict.Add("SC_ApiUser", SC_ApiUser);
                    dict.Add("SC_ApiKey", SC_ApiKey);
                }
                else if (EmailType == "Customize")
                {
                    dict.Add("SmtpUserName", SmtpUserName);
                    dict.Add("SmtpPassword", SmtpPassword);
                    dict.Add("SmtpType", SmtpType);
                    dict.Add("SmtpHost", SmtpHost);
                    dict.Add("SmtpPort", SmtpPort);
                }


                var r = await _configService.SetConfig("email", "Config", dict.ToJson());


                //HelpsManage::operationLog('编辑邮箱设置');

                return Ok(new { ret = r, msg = r ? GlobalConstVars.OperatorSuccess : GlobalConstVars.OperatorFailure });

            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "发生未处理异常");
                return StatusCode(500, "服务器内部错误");
            }


        }

        /// <summary>
        /// 编辑邮件通知开关状态
        /// </summary>
        /// <returns></returns>
        [Route("/manage/set/email/used-edit")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> UsedEdit()
        {

            var template = Request.GetFormString("template");
            var IsUsed = Request.GetFormInt("IsUsed");

            var item = await _emailTplService.Query(it => it.Template == template);
            if (item == null)
            {
                return Ok(new { ret = false, msg = GlobalConstVars.DataisNo });
            }

            item.ForEach(it => it.IsUsed = IsUsed == 1 ? true : false);

            var update = await _emailTplService.Update(item);
            if (update)
            {
                //移除缓存
                foreach (var it in item)
                {
                    await _caching.DelByPatternAsync(Consts.KEY_EmailTemplate.FormatWith(template, it.Language));
                }


                //更新config
                var emailNotice = await _configService.GetConfigByGroup("email", "notice");
                if (emailNotice != null)
                {
                    Dictionary<string, int> noticeDict = Newtonsoft.Json.JsonConvert.DeserializeObject<Dictionary<string, int>>((string)emailNotice.Value);
                    if (noticeDict.ContainsKey(template))
                    {
                        noticeDict[template] = IsUsed;
                    }
                    else
                    {
                        noticeDict.Add(template, IsUsed);
                    }

                    //保存项
                    var set = await _configService.SetConfig("email", "notice", noticeDict.ToJson());
                }

                return Ok(new { ret = true, msg = GlobalConstVars.EditSuccess });
            }
            else
            {
                return Ok(new { ret = false, msg = GlobalConstVars.EditFailure });
            }


        }



        /// <summary>
        /// 保存收件人
        /// </summary>
        /// <returns></returns>
        [HttpPost("recipients-update")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> RecipientsEdit()
        {

            var Manager = Request.GetFormString("Manager");
            var Email = Request.GetFormString("Email");
            var Notice = Request.GetFormString("Notice[]");//
            var id = Request.GetFormInt("id");

            //判断管理员是否存在
            var manage = await _manageServices.QueryByClauseAsync(it => it.UserName == Manager);
            if (manage == null)
            {
                return Ok(new { ret = false, msg = GlobalConstVars.DataisNo });
            }

            //判断收件人是否重复
            var recipientsCount = await _emailRecipientsServices.GetCountAsync(it => it.RId != id && it.UserId == manage.UserId);
            if (recipientsCount > 0)
            {
                return Ok(new { ret = false, msg = "该管理员已存在收件人" });
            }

            //判断邮箱是否重复
            var emailCount = await _emailRecipientsServices.GetCountAsync(it => it.RId != id && it.Email == Email);
            if (emailCount > 0)
            {
                return Ok(new { ret = false, msg = "邮箱已存在" });
            }

            var list = Notice.Split(',').ToList();


            var emailRecipients = await _emailRecipientsServices.QueryById(id);
            if (emailRecipients == null)
            {
                emailRecipients = new Entitys.email_recipients
                {
                    Email = Email,
                    UserId = manage.UserId,
                    CreatedAt = DateTimeHelper.ConvertToUnixTimestamp(DateTime.Now),
                    OrderCreate = list.Contains("order_create"),
                    UserInbox = list.Contains("user_inbox"),
                    OrderNotice = list.Contains("order_notification"),
                    FormTool = list.Contains("form_tool"),
                    DistApply = list.Contains("dist_apply"),
                    DistWithdraw = list.Contains("dist_withdraw"),
                    ReviewMember = list.Contains("review_member"),
                };

                var inst = await _emailRecipientsServices.AddWithIntId(emailRecipients);
                if (inst > 0)
                {
                    return Ok(new { ret = true, msg = GlobalConstVars.InsertSuccess });
                }
                else
                {
                    return Ok(new { ret = true, msg = GlobalConstVars.InsertFailure });
                }
            }
            else
            {
                emailRecipients.Email = Email;
                emailRecipients.UserId = manage.UserId;
                emailRecipients.OrderCreate = list.Contains("order_create");
                emailRecipients.UserInbox = list.Contains("user_inbox");
                emailRecipients.OrderNotice = list.Contains("order_notification");
                emailRecipients.FormTool = list.Contains("form_tool");
                emailRecipients.DistApply = list.Contains("dist_apply");
                emailRecipients.DistWithdraw = list.Contains("dist_withdraw");
                emailRecipients.ReviewMember = list.Contains("review_member");

                var updt = await _emailRecipientsServices.Update(emailRecipients);
                if (updt)
                {
                    return Ok(new { ret = true, msg = GlobalConstVars.EditSuccess });
                }
                else
                {
                    return Ok(new { ret = true, msg = GlobalConstVars.EditFailure });
                }
            }



            //HelpsManage::operationLog(($id ? '修改' : '添加'). '收件人', "{$manager}: {$email}");
        }



        /// <summary>
        /// 管理员&业务员通知添加
        /// </summary>
        /// <returns></returns>
        [HttpPost("manage-add-email")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> RecipientsAddEmail()
        {

            var UserId = Request.GetFormInt("UserId");
            var Email = Request.GetFormString("Email");
            var Permit = Request.GetFormString("Permit[]");//


            var list = Permit.Split(',').ToList();

            if (UserId.IsNullOrEmpty() || Email.IsNullOrEmpty())
            {
                return Ok(new { ret = false, msg = GlobalConstVars.DataParameterError });
            }

            if (list.Count == 0)
            {
                return Ok(new { ret = false, msg = GlobalConstVars.DataParameterError });
            }

            //判断管理员是否存在
            var manage = await _manageServices.QueryByClauseAsync(it => it.UserId == UserId);
            if (manage == null)
            {
                return Ok(new { ret = false, msg = GlobalConstVars.DataisNo });
            }


            //判断收件人是否重复
            var recipientsCount = await _emailRecipientsServices.GetCountAsync(it => it.UserId == manage.UserId);
            if (recipientsCount > 0)
            {
                return Ok(new { ret = false, msg = "该管理员已存在收件人" });
            }

            //判断邮箱是否重复
            var emailCount = await _emailRecipientsServices.GetCountAsync(it => it.Email == Email);
            if (emailCount > 0)
            {
                return Ok(new { ret = false, msg = "邮箱已存在" });
            }

            var emailRecipients = new Entitys.email_recipients
            {
                Email = Email,
                UserId = manage.UserId,
                CreatedAt = DateTimeHelper.ConvertToUnixTimestamp(DateTime.Now),
                OrderCreate = list.Contains("order_create"),
                UserInbox = list.Contains("user_inbox"),
                OrderNotice = list.Contains("order_notification"),
                FormTool = list.Contains("form_tool"),
                DistApply = list.Contains("dist_apply"),
                DistWithdraw = list.Contains("dist_withdraw"),
                ReviewMember = list.Contains("review_member"),
            };

            var inst = await _emailRecipientsServices.AddWithIntId(emailRecipients);
            if (inst > 0)
            {
                return Ok(new { ret = true, msg = GlobalConstVars.InsertSuccess });
            }
            else
            {
                return Ok(new { ret = true, msg = GlobalConstVars.InsertFailure });
            }
        }



        /// <summary>
        /// 获取收件人详情
        /// </summary>
        /// <returns></returns>
        [Route("/manage/set/email/recipients-info")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> RecipientsInfo()
        {

            var id = Request.GetFormInt("id");


            var emailRecipients = await _emailRecipientsServices.QueryById(id);
            if (emailRecipients == null)
            {
                return Ok(new { ret = false, msg = GlobalConstVars.DataisNo });
            }

            //管理员
            var manage = await _manageServices.QueryById(emailRecipients.UserId);

            var recipientsNoticeAry = _emailTplService.GetEmailRecipientsNotice();

            Dictionary<string, int> premit = new Dictionary<string, int>();
            foreach (var item in recipientsNoticeAry)
            {
                switch (item.Key)
                {
                    case "order_create":
                        premit[item.Key] = emailRecipients.OrderCreate == true ? 1 : 0;
                        break;
                    case "user_inbox":
                        premit[item.Key] = emailRecipients.UserInbox == true ? 1 : 0;
                        break;
                    case "order_notification":
                        premit[item.Key] = emailRecipients.OrderNotice == true ? 1 : 0;
                        break;
                    case "review_member":
                        premit[item.Key] = emailRecipients.ReviewMember == true ? 1 : 0;
                        break;
                    case "form_tool":
                        premit[item.Key] = emailRecipients.FormTool == true ? 1 : 0;
                        break;
                    case "dist_apply":
                        premit[item.Key] = emailRecipients.DistApply == true ? 1 : 0;
                        break;
                    case "dist_withdraw":
                        premit[item.Key] = emailRecipients.DistWithdraw == true ? 1 : 0;
                        break;

                }

            }


            var r = new
            {
                id = emailRecipients.UserId,
                name = manage?.UserName,
                email = emailRecipients.Email,
                premit = premit,
            };

            return Ok(new { ret = true, msg = r });
        }



        /// <summary>
        /// 删除收件人
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [Route("/manage/set/email/recipients-delete")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> RecipientsDelete()
        {
            var id = Request.GetQueryInt("id");
            if (id == 0)
            {
                return Ok(new { ret = false, msg = GlobalConstVars.DeleteFailure });
            }

            var delete = await _emailRecipientsServices.DeleteById(id);
            if (delete)
            {
                return Ok(new { ret = true, msg = GlobalConstVars.DeleteSuccess });
            }
            else
            {
                return Ok(new { ret = false, msg = GlobalConstVars.DeleteFailure });
            }

            //HelpsManage::operationLog('删除收件人', "{$recipients['Email']}");
        }



        /// <summary>
        /// 邮件通知&设置 商家通知相关信息
        /// </summary>
        /// <returns></returns>
        [Route("/manage/set/email/merchant-info")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> MerchantInfo()
        {
            var type = Request.GetFormString("type");
            if (type.IsNullOrEmpty())
            {
                return Ok(new { ret = false, msg = GlobalConstVars.DataisNo });
            }

            List<Dictionary<string, object>> result = new List<Dictionary<string, object>>();
            //获取收件人
            var merchantRow = await _emailRecipientsServices.Query();
            foreach (var item in merchantRow)
            {
                Dictionary<string, object> model = new Dictionary<string, object>
                {
                    ["id"] = item.RId,
                    ["email"] = item.Email,
                    ["name"] = item.manage?.UserName,

                };

                switch (type)
                {
                    case "order_create":
                        model["isused"] = item.OrderCreate == true ? 1 : 0;
                        break;
                    case "user_inbox":
                        model["isused"] = item.UserInbox == true ? 1 : 0;
                        break;
                    case "order_notification":
                        model["isused"] = item.OrderNotice == true ? 1 : 0;
                        break;
                    case "review_member":
                        model["isused"] = item.ReviewMember == true ? 1 : 0;
                        break;
                    case "form_tool":
                        model["isused"] = item.FormTool == true ? 1 : 0;
                        break;
                    case "dist_apply":
                        model["isused"] = item.DistApply == true ? 1 : 0;
                        break;
                    case "dist_withdraw":
                        model["isused"] = item.DistWithdraw == true ? 1 : 0;
                        break;
                }

                result.Add(model);
            }


            return Ok(new { ret = true, msg = result });

        }




        /// <summary>
        /// 邮件通知&设置 商家通知相关信息
        /// </summary>
        /// <returns></returns>
        [HttpPost("merchant-update")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> MerchantUpdate()
        {
            var allow = Request.GetFormString("Allow[]");
            var type = Request.GetFormString("type");
            if (type.IsNullOrEmpty())
            {
                return Ok(new { ret = false, msg = GlobalConstVars.DataisNo });
            }

            //收件人
            var emailRecipients = await _emailRecipientsServices.Query();

            //更新的收件人
            var updateRecipients = new List<email_recipients>();


            var allowList = allow.Split(',');
            var checkedIdList = new List<int>();
            if (allowList.Length > 0)
            {
                checkedIdList = allowList.Select(it => int.Parse(it)).ToList();
            }


            //全部设置为false
            switch (type)
            {
                case "order_create":
                    emailRecipients.ForEach(it => it.OrderCreate = false);

                    //设置特定的位true
                    if (checkedIdList.Count > 0)
                    {
                        var checkList = emailRecipients.Where(it => checkedIdList.Contains(it.RId));
                        checkList.ForEach(it => it.OrderCreate = true);
                        updateRecipients.AddRange(checkList);
                    }
                    break;
                case "user_inbox":
                    emailRecipients.ForEach(it => it.UserInbox = false);
                    //设置特定的位true
                    if (checkedIdList.Count > 0)
                    {
                        var checkList = emailRecipients.Where(it => checkedIdList.Contains(it.RId));
                        checkList.ForEach(it => it.UserInbox = true);
                        updateRecipients.AddRange(checkList);
                    }
                    break;
                case "order_notification":
                    emailRecipients.ForEach(it => it.OrderNotice = false);
                    //设置特定的位true
                    if (checkedIdList.Count > 0)
                    {
                        var checkList = emailRecipients.Where(it => checkedIdList.Contains(it.RId));
                        checkList.ForEach(it => it.OrderNotice = true);
                        updateRecipients.AddRange(checkList);
                    }
                    break;
                case "review_member":
                    emailRecipients.ForEach(it => it.ReviewMember = false);
                    //设置特定的位true
                    if (checkedIdList.Count > 0)
                    {
                        var checkList = emailRecipients.Where(it => checkedIdList.Contains(it.RId));
                        checkList.ForEach(it => it.ReviewMember = true);
                        updateRecipients.AddRange(checkList);
                    }
                    break;
                case "form_tool":
                    emailRecipients.ForEach(it => it.FormTool = false);
                    //设置特定的位true
                    if (checkedIdList.Count > 0)
                    {
                        var checkList = emailRecipients.Where(it => checkedIdList.Contains(it.RId));
                        checkList.ForEach(it => it.FormTool = true);
                        updateRecipients.AddRange(checkList);
                    }
                    break;
                case "dist_apply":
                    emailRecipients.ForEach(it => it.DistApply = false);
                    //设置特定的位true
                    if (checkedIdList.Count > 0)
                    {
                        var checkList = emailRecipients.Where(it => checkedIdList.Contains(it.RId));
                        checkList.ForEach(it => it.DistApply = true);
                        updateRecipients.AddRange(checkList);
                    }
                    break;
                case "dist_withdraw":
                    emailRecipients.ForEach(it => it.DistWithdraw = false);
                    //设置特定的位true
                    if (checkedIdList.Count > 0)
                    {
                        var checkList = emailRecipients.Where(it => checkedIdList.Contains(it.RId));
                        checkList.ForEach(it => it.DistWithdraw = true);
                        updateRecipients.AddRange(checkList);
                    }
                    break;
            }


            //全部清空
            var update1 = await _emailRecipientsServices.Update(emailRecipients);

            //重新设置
            var update2 = await _emailRecipientsServices.Update(updateRecipients);


            return Ok(new { ret = true, msg = updateRecipients.Count });
        }


        //保存邮件模板
        [HttpPost("/manage/set/email/edit")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> SaveEmailTemp()
        {

            var EmailLanguage = Request.GetFormString("EmailLanguage");
            var template = Request.GetFormString("template");

            var Title = Request.GetFormString("Title");
            var Content = Request.GetFormString("Content");
            var language_default = Request.GetFormString("language_default");

            var defalutLang = await _languageServices.GetDefaultLang();
            if (defalutLang == null)
            {
                return Ok(new { ret = false, msg = GlobalConstVars.DataisNo });
            }

            //判断默认语言是否启用
            var templateUsed = await _emailTplService.QueryByClauseAsync(it => it.Template == template && it.Language == defalutLang.Language);
            bool used = false;
            if (templateUsed != null)
            {
                used = templateUsed.IsUsed ?? false;
            }

            var emailTemplate = await _emailTplService.QueryByClauseAsync(it => it.Template == template && it.Language == EmailLanguage);
            if (emailTemplate == null)
            {
                emailTemplate = new system_email_tpl
                {
                    Template = template,
                    IsUsed = used,
                    Title = Title,
                    Content = Content,
                    Language = EmailLanguage,

                };

                var inst = await _emailTplService.AddWithIntId(emailTemplate);
                if (inst > 0)
                {
                    //移除缓存
                    await _caching.DelByPatternAsync(Consts.KEY_EmailTemplate.FormatWith(template, EmailLanguage));

                    return Ok(new { ret = true, msg = new { content = GlobalConstVars.OperatorSuccess, jump = "/Setting/Email/" } });
                }
                else
                {
                    return Ok(new { ret = false, msg = GlobalConstVars.InsertFailure });
                }
            }
            else
            {
                emailTemplate.Title = Title;
                emailTemplate.Content = Content;

                var update = await _emailTplService.Update(emailTemplate);
                if (update)
                {
                    //移除缓存
                    await _caching.DelByPatternAsync(Consts.KEY_EmailTemplate.FormatWith(template, EmailLanguage));

                    return Ok(new { ret = true, msg = new { content = GlobalConstVars.OperatorSuccess, jump = "/Setting/Email/" } });
                }
                else
                {
                    return Ok(new { ret = false, msg = GlobalConstVars.EditFailure });
                }
            }

            //HelpsManage::operationLog('编辑邮件设置', $template);

        }


        //获取邮件模板详情
        [HttpPost("/manage/set/email/get-email-language-content")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> GetEmailTempLangeContent()
        {
            var EmailLanguage = Request.GetFormString("EmailLanguage");
            var Template = Request.GetFormString("Template");


            Dictionary<string, string> result = new Dictionary<string, string>();
            var temp = await _emailTplService.QueryByClauseAsync(it => it.Template == Template && it.Language == EmailLanguage);
            if (temp == null)
            {
                result["Title"] = "";
                result["Content"] = "";
            }
            else
            {
                result["Title"] = temp.Title;
                result["Content"] = temp.Content;
            }

            return Ok(new { ret = true, msg = result });

        }


        [HttpPost("/manage/set/email/templete-reset")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> ResetEmailTempLangeContent()
        {
            var template = Request.GetFormString("template");  //order_refund
            var language = Request.GetFormString("language");


            string url = AppSettingsConstVars.AdminSiteUrl + $"/common/mail/{template}?culture={language}";

            //重置的邮件模板内容
            string content = await url.GetAsync().ReceiveString();

            var temp = await _emailTplService.QueryByClauseAsync(it => it.Template == template && it.Language == language);
            if (temp != null)
            {
                temp.Content = content;

                //更新模板内容
                var updae = await _emailTplService.Update(temp);
                if (updae)
                {
                    //移除缓存
                    await _caching.DelByPatternAsync(Consts.KEY_EmailTemplate.FormatWith(template, temp.Language));
                }


            }

            return Ok(new { ret = true, msg = "" });
            //HelpsManage::operationLog('重置邮件模板', $template);

        }



        //#if DEBUG
        //        [HttpGet("SendEmail")]
        //        public async Task<IActionResult> SendEmail(string email, string title, string content)
        //        {

        //            List<string> toEmail = new List<string> { email };
        //            var e = await _emailService.SendEmail(toEmail, title, content);

        //            return Content(e.ToJson());
        //        }
        //#endif


    }
}
