using Microsoft.AspNetCore.Mvc;
using MiniExcelLibs;
using YseStore.IService.Products;
using System.Text.Json;
using System.Dynamic;
using SqlSugar;
using YseStore.Repo;
using Entitys;
using YseStoreAdmin.Controllers.Sales;

namespace YseStoreAdmin.Controllers.Products;

/// <summary>
/// 产品导出控制器
/// </summary>
[ApiController]
[Route("api/[controller]")]
public class ProductExportController : ControllerBase
{
    private readonly IProductService _productService;
    private readonly IBaseRepository<shipping_overseas> _warehouseRepository;
    private readonly ISqlSugarClient _db;

    public ProductExportController(
        IProductService productService,
        IBaseRepository<shipping_overseas> warehouseRepository,
        ISqlSugarClient db)
    {
        _productService = productService;
        _warehouseRepository = warehouseRepository;
        _db = db;
    }

    /// <summary>
    /// 导出产品CSV文件
    /// </summary>
    /// <returns>导出结果</returns>
    [HttpPost("ExportProducts")]
    public async Task<IActionResult> ExportProducts([FromForm] ProductExportRequest request)
    {
        try
        {
            // 根据不同的导出类型获取产品ID列表
            List<int> productIds = await GetProductIdsByExportType(request);

            if (productIds == null || productIds.Count == 0)
            {
                return BadRequest(new { ret = -1, msg = "没有选择要导出的产品" });
            }

            // 获取要导出的产品数据
            var products = await GetProductsData(productIds);

            if (products == null || products.Count == 0)
            {
                return BadRequest(new { ret = -2, msg = "未找到符合条件的产品" });
            }

            // 生成CSV文件名
            string fileName = $"products_export_{DateTime.Now:yyyyMMddHHmmss}.csv";
            string filePath = Path.Combine(Directory.GetCurrentDirectory(), "wwwroot", "exports", fileName);

            // 确保导出目录存在
            Directory.CreateDirectory(Path.Combine(Directory.GetCurrentDirectory(), "wwwroot", "exports"));

            // 导出产品数据到CSV文件
            await ExportToCsv(products, filePath);

            // 返回仅包含文件名的结果（不附加路径）
            return Ok(new
            {
                ret = 1,
                msg = new
                {
                    count = productIds.Count,  // 修改此处，返回产品ID数量而不是CSV行数
                    name = fileName  // 只返回文件名，不包含路径
                }
            });
        }
        catch (Exception ex)
        {
            return StatusCode(StatusCodes.Status500InternalServerError,
                new { ret = -2, msg = $"导出失败: {ex.Message}" });
        }
    }

    /// <summary>
    /// 下载导出的CSV文件
    /// </summary>
    [HttpGet("GetExportDown")]
    public IActionResult DownloadExport(string name)
    {
        try
        {
            if (string.IsNullOrEmpty(name))
            {
                return BadRequest("文件名不能为空");
            }

            // 确保文件名中不包含路径分隔符，以防止目录遍历攻击
            string fileName = name.Replace("\\", "").Replace("/", "");
            
            // 尝试不同的路径组合来查找文件
            string filePath = Path.Combine(Directory.GetCurrentDirectory(), "wwwroot", "exports", fileName);
            
            // 如果文件不存在，再尝试其他可能的路径
            if (!System.IO.File.Exists(filePath) && name.Contains("exports"))
            {
                // 尝试去掉"exports/"前缀
                string nameWithoutPrefix = name.Replace("exports/", "");
                filePath = Path.Combine(Directory.GetCurrentDirectory(), "wwwroot", "exports", nameWithoutPrefix);
            }

            // 如果仍然找不到文件
            if (!System.IO.File.Exists(filePath))
            {
                // 列出目录中的所有文件进行日志记录
                var files = Directory.GetFiles(Path.Combine(Directory.GetCurrentDirectory(), "wwwroot", "exports"));
                string filesList = string.Join(", ", files.Select(Path.GetFileName));
                Console.WriteLine($"找不到文件: {name}，目录中的文件有: {filesList}");
                
                return NotFound("文件不存在或已被删除");
            }

            byte[] fileBytes = System.IO.File.ReadAllBytes(filePath);
            return File(fileBytes, "text/csv", $"products_export_{DateTime.Now:yyyyMMdd}.csv");
        }
        catch (Exception ex)
        {
            return StatusCode(StatusCodes.Status500InternalServerError, $"下载失败: {ex.Message}");
        }
    }

    /// <summary>
    /// 根据导出类型获取产品ID列表
    /// </summary>
    private async Task<List<int>> GetProductIdsByExportType(ProductExportRequest request)
    {
        List<int> productIds = new List<int>();

        switch (request.Type)
        {
            case 0: // 根据指定ID列表导出（已勾选的产品）
            case 1: // 导出当前页面的产品（同样使用IdList字段）
            case 3: // 导出所有搜索结果（同样使用IdList字段）
                if (!string.IsNullOrEmpty(request.IdList))
                {
                    var ids = request.IdList.Split(',')
                        .Select(id => int.TryParse(id, out int result) ? result : 0)
                        .Where(id => id > 0)
                        .ToList();

                    if (ids.Count > 0)
                    {
                        productIds = await _productService.GetProductIdsBySpecifiedIds(ids);
                    }
                }
                break;

            case 2: // 导出全站产品
                productIds = await _productService.GetAllProductIds();
                break;
        }

        return productIds;
    }


    /// <summary>
    /// 获取产品详细数据
    /// </summary>
    private async Task<List<dynamic>> GetProductsData(List<int> productIds)
    {
        var result = new List<dynamic>();

        // 分批获取产品数据，避免一次性查询过多数据
        const int batchSize = 100;
        
        // 计算所有产品中的最大属性数量
        int maxAttributeCount = 0;
        
        // 先获取所有产品的属性，计算最大属性数量
        for (int i = 0; i < productIds.Count; i += batchSize)
        {
            var batch = productIds.Skip(i).Take(batchSize).ToList();
            
            // 获取每个产品的属性数量
            var attributeCounts = await _db.Queryable<products_attribute>()
                .Where(a => batch.Contains(a.ProId))
                .GroupBy(a => a.ProId)
                .Select(g => new { ProId = g.ProId, Count = SqlFunc.AggregateCount(g.AttrId) })
                .ToListAsync();
                
            // 更新最大属性数量
            if (attributeCounts.Any())
            {
                int batchMaxCount = attributeCounts.Max(a => a.Count);
                maxAttributeCount = Math.Max(maxAttributeCount, batchMaxCount);
            }
        }
        
        // 如果没有找到任何属性，设置默认值为1
        if (maxAttributeCount == 0)
        {
            maxAttributeCount = 1;
        }

        // 处理每批产品数据
        for (int i = 0; i < productIds.Count; i += batchSize)
        {
            var batch = productIds.Skip(i).Take(batchSize).ToList();

            // 获取产品基本信息
            var products = await _db.Queryable<products>()
                .Where(p => batch.Contains(p.ProId))
                .ToListAsync();

            // 获取产品描述
            var descriptions = await _db.Queryable<products_description>()
                .Where(d => batch.Contains(d.ProId??0))
                .ToListAsync();

            // 获取产品SEO信息
            var seos = await _db.Queryable<products_seo>()
                .Where(s => batch.Contains(s.ProId??0))
                .ToListAsync();

            // 获取产品分类
            var categories = await _db.Queryable<products_category_relate>()
                .Where(c => batch.Contains(c.ProId))
                .ToListAsync();

            // 获取产品属性
            var attributes = await _db.Queryable<products_attribute>()
                .Where(a => batch.Contains(a.ProId))
                .OrderBy(a => a.Position)
                .ToListAsync();

            // 获取产品变体
            var variants = await _db.Queryable<products_selected_attribute_combination>()
                .Where(v => batch.Contains(v.ProId??0))
                .ToListAsync();

            // 获取产品图片
            var images = await _db.Queryable<products_images>()
                .Where(i => batch.Contains(i.ProId))
                .OrderBy(i => i.Position)
                .ToListAsync();

            // 获取所有仓库信息
            var warehouses = await _db.Queryable<shipping_overseas>().ToListAsync();
            var warehouseDict = warehouses.ToDictionary(w => w.OvId, w => w.Name);

            // 处理每个产品
            foreach (var product in products)
            {
                // 获取相关数据
                var description = descriptions.FirstOrDefault(d => d.ProId == product.ProId);
                var seo = seos.FirstOrDefault(s => s.ProId == product.ProId);
                var productCategories = categories.Where(c => c.ProId == product.ProId).ToList();
                var productAttributes = attributes.Where(a => a.ProId == product.ProId).ToList();
                var productVariants = variants.Where(v => v.ProId == product.ProId).ToList();
                var productImages = images.Where(i => i.ProId == product.ProId).ToList();

                // 创建产品基本行
                var mainRow = CreateProductRow(product, description, seo, productCategories,
                    productAttributes, productVariants, productImages, warehouseDict, true, maxAttributeCount);

                result.Add(mainRow);

                // 创建产品变体行
                if (productVariants.Any())
                {
                    foreach (var variant in productVariants.Skip(1)) // 跳过第一个变体，因为它与主行相同
                    {
                        var variantRow = CreateProductRow(product, description, seo, productCategories,
                            productAttributes, new List<products_selected_attribute_combination> { variant },
                            productImages, warehouseDict, false, maxAttributeCount);

                        result.Add(variantRow);
                    }
                }
            }
        }

        return result;
    }

    /// <summary>
    /// 创建产品行数据
    /// </summary>
    private dynamic CreateProductRow(
        products product,
        products_description description,
        products_seo seo,
        List<products_category_relate> categories,
        List<products_attribute> attributes,
        List<products_selected_attribute_combination> variants,
        List<products_images> images,
        Dictionary<short, string> warehouseDict,
        bool isMainRow,
        int maxAttributeCount)
    {
        // 创建一个动态对象来存储行数据
        dynamic row = new ExpandoObject();
        var rowDict = (IDictionary<string, object>)row;

        // 创建JSON序列化选项，不转义非ASCII字符
        var jsonOptions = new JsonSerializerOptions
        {
            Encoder = System.Text.Encodings.Web.JavaScriptEncoder.UnsafeRelaxedJsonEscaping
        };

        // 设置基本信息
        rowDict["自定义地址"] = product.PageUrl ?? string.Empty;
        rowDict["产品名称"] = isMainRow ? product.Name_en : string.Empty;
        rowDict["分类ID"] = isMainRow ? (categories.FirstOrDefault()?.CateId.ToString() ?? string.Empty) : string.Empty;
        rowDict["产品编号"] = isMainRow ? product.Number : string.Empty;
        rowDict["SKU"] = isMainRow ? product.SKU : string.Empty;
        rowDict["原价"] = isMainRow ? product.Price_0.ToString() : string.Empty;
        rowDict["价格"] = isMainRow ? product.Price_1.ToString() : string.Empty;
        rowDict["成本价"] = isMainRow ? product.CostPrice.ToString() : string.Empty;

        // 设置产品主图
        var mainImage = isMainRow ? product.PicPath_0 : string.Empty;
        rowDict["产品主图"] = mainImage;

        // 确保所有产品行都有相同数量的属性列，即使该产品没有那么多属性
        if (isMainRow)
        {
            // 获取所有有效的属性（按Position排序）
            var validAttributes = attributes.OrderBy(a => a.Position).ToList();

            // 为每个有效属性添加相应的字段
            for (int i = 0; i < maxAttributeCount; i++)
            {
                int attrIndex = i + 1; // 属性索引从1开始
                
                if (i < validAttributes.Count)
                {
                    // 这个产品有这个属性，正常设置
                    var attribute = validAttributes[i];
                    
                    rowDict[$"属性{attrIndex}名称"] = attribute.Name_en;
                    rowDict[$"属性{attrIndex}选项类型"] = GetAttributeTypeDisplayName(attribute.Type);
                    
                    // 获取属性选项
                    var options = string.Empty;
                    if (attribute.Options != null)
                    {
                        try
                        {
                            var optionsList = JsonSerializer.Deserialize<List<string>>(attribute.Options, jsonOptions);
                            options = string.Join(",", optionsList);
                        }
                        catch
                        {
                        }
                    }
                    
                    rowDict[$"属性{attrIndex}选项名称"] = options;
                    rowDict[$"属性{attrIndex}默认选项"] = string.Empty;
                    
                    // 获取默认选项
                    var defaultVariant = variants.FirstOrDefault(v => v.IsDefault.GetValueOrDefault());
                    if (defaultVariant == null)
                    {
                        defaultVariant = variants.FirstOrDefault();
                    }
                    
                    if (defaultVariant != null && !string.IsNullOrEmpty(defaultVariant.Data))
                    {
                        try
                        {
                            var variantOptions = JsonSerializer.Deserialize<List<string>>(defaultVariant.Data, jsonOptions);
                            var attrNames = JsonSerializer.Deserialize<List<string>>(defaultVariant.AttrName, jsonOptions);
                            
                            int attrNameIndex = attrNames.IndexOf(attribute.Name_en);
                            if (attrNameIndex >= 0 && attrNameIndex < variantOptions.Count)
                            {
                                rowDict[$"属性{attrIndex}默认选项"] = variantOptions[attrNameIndex];
                            }
                        }
                        catch
                        {
                            // 解析失败时保持默认空值
                        }
                    }
                }
                else
                {
                    // 这个产品没有这个属性，设置为空
                    rowDict[$"属性{attrIndex}名称"] = string.Empty;
                    rowDict[$"属性{attrIndex}选项类型"] = string.Empty;
                    rowDict[$"属性{attrIndex}选项名称"] = string.Empty;
                    rowDict[$"属性{attrIndex}默认选项"] = string.Empty;
                }
            }
        }
        else if (!isMainRow && variants.Any())
        {
            // 变体行处理
            var variant1 = variants.FirstOrDefault();
            if (variant1 != null && !string.IsNullOrEmpty(variant1.Data) && !string.IsNullOrEmpty(variant1.AttrName))
            {
                try
                {
                    var variantOptions = JsonSerializer.Deserialize<List<string>>(variant1.Data, jsonOptions);
                    var attrNames = JsonSerializer.Deserialize<List<string>>(variant1.AttrName, jsonOptions);
                    
                    // 确保所有属性列都存在
                    for (int i = 0; i < maxAttributeCount; i++)
                    {
                        int attrIndex = i + 1;
                        rowDict[$"属性{attrIndex}名称"] = string.Empty;
                        rowDict[$"属性{attrIndex}选项类型"] = string.Empty;
                        rowDict[$"属性{attrIndex}选项名称"] = string.Empty;
                        rowDict[$"属性{attrIndex}默认选项"] = string.Empty;
                    }

                    // 为每个属性添加变体值
                    for (int i = 0; i < attrNames.Count && i < variantOptions.Count; i++)
                    {
                        string attrName = attrNames[i];
                        string attrValue = variantOptions[i];
                        
                        // 找到对应的属性索引位置
                        var attribute = attributes.FirstOrDefault(a => a.Name_en == attrName);
                        if (attribute != null)
                        {
                            int attrIndex = attributes.OrderBy(a => a.Position).ToList().IndexOf(attribute) + 1;
                            if (attrIndex > 0 && attrIndex <= maxAttributeCount)
                            {
                                rowDict[$"属性{attrIndex}选项名称"] = attrValue;
                            }
                        }
                    }
                }
                catch
                {
                    // 解析失败时确保所有属性列都存在
                    for (int i = 0; i < maxAttributeCount; i++)
                    {
                        int attrIndex = i + 1;
                        rowDict[$"属性{attrIndex}名称"] = string.Empty;
                        rowDict[$"属性{attrIndex}选项类型"] = string.Empty;
                        rowDict[$"属性{attrIndex}选项名称"] = string.Empty;
                        rowDict[$"属性{attrIndex}默认选项"] = string.Empty;
                    }
                }
            }
            else
            {
                // 确保所有属性列都存在
                for (int i = 0; i < maxAttributeCount; i++)
                {
                    int attrIndex = i + 1;
                    rowDict[$"属性{attrIndex}名称"] = string.Empty;
                    rowDict[$"属性{attrIndex}选项类型"] = string.Empty;
                    rowDict[$"属性{attrIndex}选项名称"] = string.Empty;
                    rowDict[$"属性{attrIndex}默认选项"] = string.Empty;
                }
            }
        }
        else
        {
            // 确保所有属性列都存在
            for (int i = 0; i < maxAttributeCount; i++)
            {
                int attrIndex = i + 1;
                rowDict[$"属性{attrIndex}名称"] = string.Empty;
                rowDict[$"属性{attrIndex}选项类型"] = string.Empty;
                rowDict[$"属性{attrIndex}选项名称"] = string.Empty;
                rowDict[$"属性{attrIndex}默认选项"] = string.Empty;
            }
        }

        // 设置仓库信息
        var variant = variants.FirstOrDefault();
        if (variant != null && variant.OvId > 0 && warehouseDict.TryGetValue(variant.OvId, out string warehouseName))
        {
            rowDict["仓库"] = warehouseName;
        }
        else
        {
            rowDict["仓库"] = string.Empty;
        }

        // 设置变体信息
        rowDict["规格SKU"] = variant?.SKU ?? string.Empty;
        rowDict["规格原价"] = variant?.OldPrice.ToString() ?? string.Empty;
        rowDict["规格价格"] = variant?.Price.ToString() ?? string.Empty;
        rowDict["规格成本价"] = variant?.CostPrice.ToString() ?? string.Empty;
        rowDict["规格库存"] = variant?.Stock.ToString() ?? string.Empty;
        rowDict["规格重量"] = variant?.Weight.ToString() ?? string.Empty;
        rowDict["重量单位（请填入kg/g/lb/oz，留空时默认为kg）"] = variant?.WeightUnit ?? "kg";
        rowDict["规格主图"] = string.Empty; // 已在产品主图中设置

        // 设置其他信息
        rowDict["规格模式"] = isMainRow ? product.IsCombination.ToString() : string.Empty;
        rowDict["产品重量"] = isMainRow ? product.Weight.ToString() : string.Empty;
        rowDict["产品总库存"] = isMainRow ? product.Stock.ToString() : string.Empty;
        rowDict["库存状态"] = isMainRow ? (variant?.StockStatus ?? string.Empty) : string.Empty;
        rowDict["跟踪库存"] = isMainRow ? product.SoldStatus : string.Empty;

        // 设置SEO信息
        rowDict["SEO标题"] = isMainRow ? (seo?.SeoTitle_en ?? string.Empty) : string.Empty;
        rowDict["SEO关键词"] = isMainRow ? (seo?.SeoKeyword_en ?? string.Empty) : string.Empty;
        rowDict["SEO描述"] = isMainRow ? (seo?.SeoDescription_en ?? string.Empty) : string.Empty;

        // 设置描述信息
        rowDict["简短介绍"] = isMainRow ? product.BriefDescription_en : string.Empty;
        rowDict["详细介绍"] = isMainRow ? (description?.Description_en ?? string.Empty) : string.Empty;

        // 设置标签
        rowDict["标签"] = isMainRow ? GetTagsString(product.Tags) : string.Empty;

        return row;
    }

    /// <summary>
    /// 获取属性类型的显示名称
    /// </summary>
    private string GetAttributeTypeDisplayName(string type)
    {
        switch (type)
        {
            case "text":
                return "文字";
            case "color":
                return "颜色";
            case "picture":
                return "图片";
            default:
                return "文字";
        }
    }

    /// <summary>
    /// 获取标签字符串
    /// </summary>
    private string GetTagsString(string tags)
    {
        if (string.IsNullOrEmpty(tags))
            return string.Empty;

        // 将"|tag1|tag2|tag3|"格式转换为"tag1,tag2,tag3"格式
        return string.Join(",", tags.Trim('|').Split('|', StringSplitOptions.RemoveEmptyEntries));
    }

    /// <summary>
    /// 导出数据到CSV文件
    /// </summary>
    private async Task ExportToCsv(List<dynamic> data, string filePath)
    {
        await MiniExcel.SaveAsAsync(filePath, data);
    }

    /// <summary>
    /// 获取所有产品ID
    /// </summary>
    /// <returns>所有产品ID列表</returns>
    [HttpGet("GetAllProductIds")]
    public async Task<IActionResult> GetAllProductIds()
    {
        try
        {
            var productIds = await _productService.GetAllProductIds();
            
            if (productIds == null || productIds.Count == 0)
            {
                return Ok(new { ret = 0, msg = "没有找到任何产品" });
            }
            
            return Ok(new { ret = 1, data = productIds });
        }
        catch (Exception ex)
        {
            return StatusCode(StatusCodes.Status500InternalServerError, 
                new { ret = -1, msg = $"获取产品ID失败: {ex.Message}" });
        }
    }
}

/// <summary>
/// 产品导出请求
/// </summary>
public class ProductExportRequest
{
    /// <summary>
    /// 导出类型：0-已勾选的产品，1-当前页面的产品，2-全站产品，3-所有搜索结果
    /// </summary>
    public int Type { get; set; }

    /// <summary>
    /// 已勾选的产品ID列表（英文逗号分隔）
    /// </summary>
    public string IdList { get; set; }
    
} 