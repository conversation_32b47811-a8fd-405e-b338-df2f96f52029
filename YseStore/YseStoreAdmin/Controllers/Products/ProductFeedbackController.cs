using Elasticsearch.Net;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using YseStore.IService;
using YseStore.IService.Products;
using YseStore.Model.Response;
using YseStore.Service;

namespace YseStoreAdmin.Controllers.Products
{
    [ApiController]
    [Route("api/[controller]")]
    public class ProductFeedbackController : ControllerBase
    {
        private readonly IProductFeedbackService _productFeedbackService;
        private readonly ILogger<CodeEditController> _logger;

        public ProductFeedbackController(IProductFeedbackService productFeedbackService, ILogger<CodeEditController> logger)
        {
            _productFeedbackService = productFeedbackService;
            _logger = logger;
        }
        [HttpGet("DelProductFeedback")]
        public async Task<IActionResult> DelProductFeedback(string id)
        {
            var b = _productFeedbackService.DelProductFeedback(id);
            return Ok(new { ret = true, msg = new { content = "删除成功", jump = "/Products/Feedback" } });
        }
        [HttpPost("EditProductFeedbackReply")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> SaveProductFeedbackReply()
        {
            try
            {
                var Content = Request.Form["Content"];
                var ReId = Request.Form["ReId"];
                var RId = Request.Form["RId"];
                await _productFeedbackService.UpdetProductFeedbackReply(Content, ReId, RId);
                return Ok(new { ret = true, msg = new { content = "保存成功", jump = "/Products/Feedback" } });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "根据ReId来判断插入/修改产品评论回复发生未处理异常");
                return StatusCode(500, "服务器内部错误");
            }
        }
        [HttpPost("ProductsReplyAudit")]
        public async Task<IActionResult> ProductsReplyAudit(
        [FromForm] int RId,         
        [FromForm] int Audit)    
        {
            try
            {
                var b= await _productFeedbackService.UpdetProductFeedbackAudit(RId, Audit);
                if (Audit == 1)
                {
                    return Ok(new { ret = b, msg = new { audit = 1, alert = "更改成功", text = "隐藏", publish = "已发布" } });
                   
                }
                else
                {
                    return Ok(new { ret = b, msg = new { audit = 0, alert = "更改成功", text = "发布", publish = "未发布" } });
                }
               
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "根据reId获取更改已发布/未发布发生未处理异常");
                return StatusCode(500, "服务器内部错误");
            }

        }


        }
}
