using Microsoft.AspNetCore.Mvc;
using YseStore.IService.Products;
using YseStore.Model.RequestModels.Products;
using Entitys;
using System.Text;
using SqlSugar;
using YseStore.Common.Helper;

namespace YseStoreAdmin.Controllers.Products;

[Route("manage/products/category")]
[ApiController]
public class ProductCategoryController : ControllerBase
{
    private readonly IProductService _productService;
    private readonly IProductCategoryService _productCategoryService;
    private readonly ILogger<ProductCategoryController> _logger;

    public ProductCategoryController(
        IProductService productService,
        IProductCategoryService productCategoryService,
        ILogger<ProductCategoryController> logger)
    {
        _productService = productService;
        _productCategoryService = productCategoryService;
        _logger = logger;
    }

    /// <summary>
    /// 获取分类下的产品列表
    /// </summary>
    /// <param name="page">页码</param>
    /// <param name="id">分类ID</param>
    /// <param name="order">排序方式</param>
    /// <param name="where">产品ID列表</param>
    /// <param name="totalCount">总数量</param>
    /// <param name="pageSize">每页大小</param>
    /// <returns>产品列表HTML和分页信息</returns>
    [HttpPost("get-products-list")]
    public async Task<IActionResult> GetProductsList(
        [FromQuery] int page = 1,
        [FromForm] int id = 0,
        [FromForm] string order = "price_desc",
        [FromForm] List<int> where = null,
        [FromForm] int totalCount = 0,
        [FromForm] int pageSize = 20)
    {
        try
        {
            _logger.LogInformation(
                $"GetProductsList 请求参数: page={page}, id={id}, order={order}, totalCount={totalCount}, pageSize={pageSize}, where.Count={where?.Count ?? 0}");

            // 验证参数
            if (where == null || !where.Any())
            {
                _logger.LogWarning("产品ID列表为空");
                return Ok(new
                {
                    ret = 1,
                    msg = new
                    {
                        html = "",
                        turn_page = BuildTurnPageHtml(0, 0, pageSize),
                        productsList = new List<int>()
                    }
                });
            }

            // 按照源码逻辑，前端已经传递了当前页的产品ID切片，后端不需要再次分页
            // 直接使用前端传递的where参数作为当前页的产品ID列表
            var productIds = where.ToList();
            _logger.LogInformation($"当前页产品ID: [{string.Join(",", productIds)}], 页码: {page}");

            var products = await GetProductsByIds(productIds, order);

            if (!products.Any())
            {
                return Ok(new
                {
                    ret = 1,
                    msg = new
                    {
                        html = "",
                        turn_page = BuildTurnPageHtml(page - 1, totalCount, pageSize),
                        productsList = where
                    }
                });
            }

            // 构建HTML
            var html = BuildProductsHtml(products);
            var turnPageHtml = BuildTurnPageHtml(page - 1, totalCount, pageSize);

            return Ok(new
            {
                ret = 1,
                msg = new
                {
                    html = html,
                    turn_page = turnPageHtml,
                    productsList = where
                }
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取分类产品列表时出错");
            return Ok(new
            {
                ret = 0,
                msg = "获取产品列表失败"
            });
        }
    }

    /// <summary>
    /// 根据产品ID列表获取产品信息
    /// </summary>
    private async Task<List<products>> GetProductsByIds(List<int> productIds, string order)
    {
        var queryRequest = new ProductQueryRequest
        {
            PageIndex = 1,
            PageSize = productIds.Count,
            IsPaging = false
        };

        // 设置排序
        switch (order.ToLower())
        {
            case "price_desc":
                queryRequest.OrderByField = "Price_1 DESC";
                break;
            case "price_asc":
                queryRequest.OrderByField = "Price_1 ASC";
                break;
            case "time_desc":
                queryRequest.OrderByField = "AccTime DESC";
                break;
            case "time_asc":
                queryRequest.OrderByField = "AccTime ASC";
                break;
            case "custom_sort":
                // 保持原有顺序
                queryRequest.OrderByField = "ProId";
                break;
            default:
                queryRequest.OrderByField = "ProId";
                break;
        }

        // 获取产品列表
        var result =
            await _productService.GetProductIdsByIdsList(productIds, 1, productIds.Count, queryRequest.OrderByField);

        if (order == "custom_sort" && result.data.Any())
        {
            // 对于自定义排序，按照传入的ID顺序排列
            var orderedProducts = new List<products>();
            foreach (var id in productIds)
            {
                var product = result.data.FirstOrDefault(p => p.ProId == id);
                if (product != null)
                {
                    orderedProducts.Add(product);
                }
            }

            return orderedProducts;
        }

        return result.data;
    }

    /// <summary>
    /// 构建产品列表HTML
    /// </summary>
    private string BuildProductsHtml(List<products> products)
    {
        var html = new StringBuilder();

        foreach (var product in products)
        {
            // 处理产品图片URL
            var imageUrl = !string.IsNullOrEmpty(product.PicPath_0)
                ? product.PicPath_0
                : "/assets/images/no-image.png";

            // 添加OSS处理参数
            if (!imageUrl.Contains("?") && !imageUrl.StartsWith("/assets/"))
            {
                imageUrl += "?x-oss-process=image/format,webp/resize,m_lfit,,h_80,w_80";
            }

            // 处理产品名称
            var productName = !string.IsNullOrEmpty(product.Name_en)
                ? product.Name_en
                : "未命名产品";

            // 限制产品名称长度
            // if (productName.Length > 30)
            // {
            //     productName = productName.Substring(0, 30);
            // }

            // 处理价格显示
            var priceDisplay = product.Price_1.HasValue
                ? $"${product.Price_1.Value:F2}"
                : "$0.00";

            // 处理库存显示
            var stockDisplay = product.Stock ?? 0;

            html.AppendLine("<tr class=\"item\">");
            html.AppendLine("    <td nowrap>");
            html.AppendLine("        <div class=\"btn_checkbox \">");
            html.AppendLine("            <em class=\"button\"></em>");
            html.AppendLine($"            <input type=\"checkbox\" name=\"select\" value=\"{product.ProId}\"/>");
            html.AppendLine("        </div>");
            html.AppendLine("    </td>");
            html.AppendLine("    <td class=\"box_move\">");
            html.AppendLine("        <b class=\"btn_move order_move\"></b>");
            html.AppendLine("    </td>");
            html.AppendLine("    <td class=\"img\">");
            html.AppendLine("        <div class=\"pic_box\">");
            html.AppendLine($"            <img src=\"{imageUrl}\" class=\"loading_img\">");
            html.AppendLine("            <span></span>");
            html.AppendLine("        </div>");
            html.AppendLine("    </td>");
            html.AppendLine("    <td class=\"info\">");
            html.AppendLine($"        <div>{productName}</div>");
            html.AppendLine("    </td>");
            html.AppendLine($"    <td>{priceDisplay}</td>");
            html.AppendLine($"    <td>{stockDisplay}</td>");
            html.AppendLine("    <td class=\"operation\">");
            html.AppendLine("        <a href=\"javascript:;\" class=\"button_tips oper_icon icon_top p_top\">置顶</a>");
            html.AppendLine("        <a href=\"javascript:;\" class=\"button_tips oper_icon icon_del p_del\">删除</a>");
            html.AppendLine($"        <input type=\"hidden\" name=\"ProId[]\" value=\"{product.ProId}\"/>");
            html.AppendLine("    </td>");
            html.AppendLine("</tr>");
        }

        return html.ToString();
    }

    /// <summary>
    /// 构建分页HTML
    /// </summary>
    private string BuildTurnPageHtml(int currentPage, int totalCount, int pageSize)
    {
        var html = new StringBuilder();
        var totalPages = (int)Math.Ceiling((double)totalCount / pageSize);

        html.AppendLine($"<div id=\"turn_page\" data-current=\"{currentPage}\" data-count=\"{totalPages}\">");
        html.AppendLine($"    <div class=\"total_page\">共 {totalCount} 条</div>");

        // 只有多于1页时才显示分页按钮
        if (totalPages > 1)
        {
            html.AppendLine("    <ul class=\"pagination\">");

            // 首页和上一页
            if (currentPage > 0)
            {
                html.AppendLine(
                    "        <li class=\"first\"><a href=\"javascript:void(0)\" data-page=\"0\">首页</a></li>");
                html.AppendLine(
                    $"        <li class=\"prev\"><a href=\"javascript:void(0)\" data-page=\"{currentPage - 1}\"><span></span></a></li>");
            }
            else
            {
                html.AppendLine("        <li class=\"first disabled\"><span>首页</span></li>");
                html.AppendLine("        <li class=\"prev disabled\"><span></span></li>");
            }

            // 页码按钮
            var startPage = Math.Max(0, currentPage - 2);
            var endPage = Math.Min(totalPages - 1, currentPage + 2);

            for (var i = startPage; i <= endPage; i++)
            {
                var pageNum = i + 1;
                var activeClass = i == currentPage ? " class=\"active\"" : "";
                html.AppendLine(
                    $"        <li{activeClass}><a href=\"javascript:void(0)\" data-page=\"{i}\">{pageNum}</a></li>");
            }

            // 下一页和尾页
            if (currentPage < totalPages - 1)
            {
                html.AppendLine(
                    $"        <li class=\"next\"><a href=\"javascript:void(0)\" data-page=\"{currentPage + 1}\"></a></li>");
                html.AppendLine(
                    $"        <li class=\"last\"><a href=\"javascript:void(0)\" data-page=\"{totalPages - 1}\">尾页</a></li>");
            }
            else
            {
                html.AppendLine("        <li class=\"next disabled\"><span></span></li>");
                html.AppendLine("        <li class=\"last disabled\"><span>尾页</span></li>");
            }

            html.AppendLine("    </ul>");
        }

        html.AppendLine("</div>");

        return html.ToString();
    }

    /// <summary>
    /// 智能添加产品 - 根据条件获取符合的产品列表
    /// </summary>
    /// <param name="filterCondition">筛选条件类型：all-满足全部条件，one-满足其中一个条件</param>
    /// <param name="filterConditionValue">筛选条件值的JSON字符串</param>
    /// <returns>符合条件的产品ID列表</returns>
    [HttpPost("get-condition-products")]
    public async Task<IActionResult> GetConditionProducts(
        [FromForm] string filterCondition = "all",
        [FromForm] Dictionary<string, Dictionary<string, string>> filterConditionValue = null)
    {
        try
        {
            // _logger.LogInformation($"智能添加产品请求 - FilterCondition: {filterCondition}");

            // 验证参数
            if (filterConditionValue == null || !filterConditionValue.Any())
            {
                return Ok(new
                {
                    ret = 1,
                    msg = new List<int>()
                });
            }

            // 获取符合条件的产品ID列表
            var productIds = await GetProductIdsByConditions(filterCondition, filterConditionValue);

            // _logger.LogInformation($"智能添加产品结果 - 找到 {productIds.Count} 个符合条件的产品");

            if (!productIds.Any())
            {
                return Ok(new
                {
                    ret = 1,
                    msg = new List<int>()
                });
            }

            return Ok(new
            {
                ret = 1,
                msg = productIds
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "智能添加产品时出错");
            return Ok(new
            {
                ret = 0,
                msg = "获取产品列表失败"
            });
        }
    }

    /// <summary>
    /// 根据筛选条件获取产品ID列表
    /// </summary>
    /// <param name="filterCondition">筛选条件类型</param>
    /// <param name="filterConditionValue">筛选条件值</param>
    /// <returns>符合条件的产品ID列表</returns>
    private async Task<List<int>> GetProductIdsByConditions(
        string filterCondition,
        Dictionary<string, Dictionary<string, string>> filterConditionValue)
    {
        try
        {
            // 创建基础查询
            var query = _productService.Db.Queryable<products>()
                .Where(p => p.SoldOut != true); // 只查询未下架的产品

            // 如果是满足全部条件
            if (filterCondition == "all")
            {
                // 遍历所有筛选条件，使用 AND 连接
                foreach (var condition in filterConditionValue.Values)
                {
                    if (!condition.ContainsKey("Type") || !condition.ContainsKey("Calc") ||
                        !condition.ContainsKey("Value"))
                        continue;

                    var type = condition["Type"];
                    var calc = condition["Calc"];
                    var value = condition["Value"];

                    if (string.IsNullOrEmpty(value))
                        continue;

                    // 根据条件类型和计算方式构建查询条件
                    query = ApplyConditionToQuery(query, type, calc, value);
                }
            }
            else
            {
                // 满足其中一个条件 - 使用 OR 连接
                query = BuildOrConditionQuery(filterConditionValue);
            }

            // 执行查询并返回产品ID列表
            var productIds = await query.Select(p => p.ProId).ToListAsync();

            return productIds;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "根据条件查询产品时出错");
            return new List<int>();
        }
    }

    /// <summary>
    /// 将单个条件应用到查询中
    /// </summary>
    /// <param name="query">查询对象</param>
    /// <param name="type">条件类型</param>
    /// <param name="calc">计算方式</param>
    /// <param name="value">条件值</param>
    /// <returns>应用条件后的查询对象</returns>
    private SqlSugar.ISugarQueryable<products> ApplyConditionToQuery(
        SqlSugar.ISugarQueryable<products> query,
        string type,
        string calc,
        string value)
    {
        switch (type.ToLower())
        {
            case "name":
                return ApplyNameCondition(query, calc, value);
            case "tags":
                return ApplyTagsCondition(query, calc, value);
            case "stock":
                return ApplyStockCondition(query, calc, value);
            case "sales":
                return ApplySalesCondition(query, calc, value);
            default:
                return query;
        }
    }

    /// <summary>
    /// 应用产品名称条件
    /// </summary>
    private SqlSugar.ISugarQueryable<products> ApplyNameCondition(
        SqlSugar.ISugarQueryable<products> query,
        string calc,
        string value)
    {
        switch (calc.ToLower())
        {
            case "equal":
                return query.Where(p => p.Name_en == value);
            case "unequal":
                return query.Where(p => p.Name_en != value);
            case "starts":
                return query.Where(p => p.Name_en.StartsWith(value));
            case "ends":
                return query.Where(p => p.Name_en.EndsWith(value));
            case "include":
                return query.Where(p => p.Name_en.Contains(value));
            case "uninclude":
                return query.Where(p => !p.Name_en.Contains(value));
            default:
                return query;
        }
    }

    /// <summary>
    /// 应用标签条件
    /// </summary>
    private SqlSugar.ISugarQueryable<products> ApplyTagsCondition(
        SqlSugar.ISugarQueryable<products> query,
        string calc,
        string value)
    {
        if (calc.ToLower() == "equal")
        {
            return query.Where(p => p.Tags.Contains(value));
        }

        return query;
    }

    /// <summary>
    /// 应用库存条件
    /// </summary>
    private SqlSugar.ISugarQueryable<products> ApplyStockCondition(
        SqlSugar.ISugarQueryable<products> query,
        string calc,
        string value)
    {
        if (int.TryParse(value, out int stockValue))
        {
            switch (calc.ToLower())
            {
                case "equal":
                    return query.Where(p => SqlFunc.Subqueryable<products_selected_attribute_combination>()
                        .Where(c => c.ProId == p.ProId).Sum(c => c.Stock) == stockValue);
                case "more":
                    return query.Where(p => SqlFunc.Subqueryable<products_selected_attribute_combination>()
                        .Where(c => c.ProId == p.ProId).Sum(c => c.Stock) > stockValue);
                case "less":
                    return query.Where(p => SqlFunc.Subqueryable<products_selected_attribute_combination>()
                        .Where(c => c.ProId == p.ProId).Sum(c => c.Stock) < stockValue);
            }
        }

        return query;
    }

    /// <summary>
    /// 应用销量条件
    /// </summary>
    private SqlSugar.ISugarQueryable<products> ApplySalesCondition(
        SqlSugar.ISugarQueryable<products> query,
        string calc,
        string value)
    {
        if (int.TryParse(value, out int salesValue) && calc.ToLower() == "more")
        {
            return query.Where(p => p.Sales != null && p.Sales > salesValue);
        }

        return query;
    }

    /// <summary>
    /// 构建OR条件查询（满足其中一个条件）
    /// </summary>
    private SqlSugar.ISugarQueryable<products> BuildOrConditionQuery(
        Dictionary<string, Dictionary<string, string>> filterConditionValue)
    {
        var baseQuery = _productService.Db.Queryable<products>()
            .Where(p => p.SoldOut != true);

        // 收集所有符合条件的产品ID
        var allProductIds = new HashSet<int>();

        // 为每个条件单独查询并收集产品ID
        foreach (var condition in filterConditionValue.Values)
        {
            if (!condition.ContainsKey("Type") || !condition.ContainsKey("Calc") || !condition.ContainsKey("Value"))
                continue;

            var type = condition["Type"];
            var calc = condition["Calc"];
            var value = condition["Value"];

            if (string.IsNullOrEmpty(value))
                continue;

            var conditionQuery = _productService.Db.Queryable<products>()
                .Where(p => p.SoldOut != true);

            conditionQuery = ApplyConditionToQuery(conditionQuery, type, calc, value);

            // 获取符合当前条件的产品ID
            var conditionProductIds = conditionQuery.Select(p => p.ProId).ToList();
            foreach (var id in conditionProductIds)
            {
                allProductIds.Add(id);
            }
        }

        // 如果有符合条件的产品ID，返回这些产品的查询
        if (allProductIds.Any())
        {
            return baseQuery.Where(p => allProductIds.Contains(p.ProId));
        }

        return baseQuery.Where(p => false); // 没有条件时返回空结果
    }

    /// <summary>
    /// 产品分类保存/更新接口
    /// </summary>
    /// <param name="request">分类保存请求参数</param>
    /// <returns>操作结果</returns>
    [HttpPost("update")]
    public async Task<IActionResult> UpdateCategory([FromBody] SaveCategoryRequest request)
    {
        try
        {
            // 处理UId，用于构建分类树
            string uid = "0,"; // 默认值为"0,"

            // 优先使用前端传入的UId值
            if (!string.IsNullOrWhiteSpace(request.UId))
            {
                uid = request.UId;
            }
            // 如果前端没有传入UId，但有父级ID，则根据父级计算UId
            else if (request.SupCateId > 0)
            {
                uid = request.Level == 1
                    ? $"0,{request.SupCateId}"
                    : await GetParentUid(request.SupCateId);
            }

            // 如果是编辑现有分类，且UId为空（有可能是前端没有传递或设置为空），则检索数据库中存储的UId
            if (request.CateId > 0 && string.IsNullOrWhiteSpace(request.UId))
            {
                var existingCategory = await _productCategoryService.GetProductCategoryById(request.CateId);
                if (existingCategory.category != null && !string.IsNullOrWhiteSpace(existingCategory.category.UId))
                {
                    uid = existingCategory.category.UId;
                }
            }

            // 构建产品分类实体
            var category = new products_category
            {
                CateId = request.CateId,
                Source = "manual",
                UId = uid,
                Category_en = request.Category_en ?? "",
                PicPath = request.PicPath ?? "",
                SeoTitle_en = request.SeoTitle_en ?? "",
                SeoDescription_en = request.SeoDescription_en ?? "",
                SeoKeyword_en = request.keysName != null && request.keysName.Any()
                    ? string.Join(",", request.keysName.Where(k => !string.IsNullOrWhiteSpace(k)))
                    : "",
                AddMethod = request.AddMethod ?? "manual",
                OrderType = request.OrderType ?? "time_desc",
                FilterCondition = request.FilterCondition ?? "all",
                FilterConditionValue = request.FilterConditionValue ?? "",
                PageUrl = request.PageUrl ?? "",
                EditTime = DateTimeHelper.ConvertToUnixTimestamp(DateTime.Now)
            };

            // 构建产品分类描述实体列表
            var descriptions = new List<products_category_description>
            {
                new products_category_description
                {
                    CateId = request.CateId,
                    PositionType = 0,
                    Description_en = request.Description_en ?? ""
                },
                new products_category_description
                {
                    CateId = request.CateId,
                    PositionType = 1,
                    Core_top = request.Core_top ?? ""
                },
                new products_category_description
                {
                    CateId = request.CateId,
                    PositionType = 2,
                    Core_bottom = request.Core_bottom ?? ""
                },
                new products_category_description
                {
                    CateId = request.CateId,
                    PositionType = 3,
                    Description_en_bottom = request.Description_en_bottom ?? ""
                }
            };

            // 保存产品分类
            var cateId = await _productCategoryService.SaveProductCategory(category, descriptions);

            if (cateId > 0)
            {
                // 处理产品分类关联
                bool relationResult = true;

                // 根据排序方式决定使用哪个字段
                List<int> productIdsUpdated = new List<int>();

                // 只有当手动排序时才使用UpdatedProId字段
                if (request.OrderType == "custom_sort" && request.UpdatedProId != null && request.UpdatedProId.Any())
                {
                    productIdsUpdated = request.UpdatedProId;
                    _logger.LogInformation($"手动排序模式：使用UpdatedProId数组，包含{productIdsUpdated.Count}个产品ID");
                }

                // 非手动排序或UpdatedProId为空时，使用原来的ProId数组
                if (!productIdsUpdated.Any() && request.ProId != null && request.ProId.Any())
                {
                    productIdsUpdated = request.ProId;
                    _logger.LogInformation($"使用ProId数组，包含{productIdsUpdated.Count}个产品ID");
                }

                if (productIdsUpdated.Any())
                {
                    relationResult = await _productCategoryService.SaveProductCategoryRelations(
                        cateId,
                        productIdsUpdated);
                }
                else
                {
                    // 如果没有产品ID，清空该分类的所有产品关联
                    relationResult = await _productCategoryService.ClearCategoryProductRelations(cateId);
                }

                if (relationResult)
                {
                    // 获取保存后的完整分类数据用于回显
                    var savedData = await _productCategoryService.GetProductCategoryById(cateId);
                    var productIds = await _productCategoryService.GetProductIdsByCategoryId(cateId);

                    _logger.LogInformation($"分类保存成功: CateId={cateId}");

                    return Ok(new
                    {
                        success = true,
                        message = "保存成功",
                        data = new
                        {
                            id = cateId,
                            category = savedData.category,
                            descriptions = savedData.descriptions,
                            productIds = productIds
                        }
                    });
                }
                else
                {
                    _logger.LogWarning($"分类保存成功但产品关联保存失败: CateId={cateId}");
                    return Ok(new { success = false, message = "分类保存成功，但产品关联保存失败" });
                }
            }
            else
            {
                _logger.LogError("分类保存失败");
                return Ok(new { success = false, message = "保存失败" });
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "保存分类时出错");
            return Ok(new { success = false, message = $"保存失败: {ex.Message}" });
        }
    }

    /// <summary>
    /// 获取父级分类的UId
    /// </summary>
    /// <param name="parentId">父级分类ID</param>
    /// <returns>父级UId</returns>
    private async Task<string> GetParentUid(int parentId)
    {
        try
        {
            var parentCategory = await _productCategoryService.GetProductCategoryById(parentId);
            if (parentCategory.category != null && !string.IsNullOrWhiteSpace(parentCategory.category.UId))
            {
                return $"{parentCategory.category.UId},{parentId}";
            }

            return $"0,{parentId}";
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"获取父级分类UId时出错: ParentId={parentId}");
            return $"0,{parentId}";
        }
    }

    /// <summary>
    /// 产品转移接口
    /// </summary>
    /// <param name="request">产品转移请求参数</param>
    /// <returns>操作结果</returns>
    [HttpPost("transfer")]
    public async Task<IActionResult> TransferProducts([FromForm] ProductTransferRequest request)
    {
        try
        {
            _logger.LogInformation($"收到产品转移请求: CateId={request.CateId}, TransferCateId={request.TransferCateId}");

            // 参数验证
            if (request.CateId <= 0)
            {
                return Ok(new { ret = 0, msg = "当前分类ID无效" });
            }

            if (request.TransferCateId < -1)
            {
                return Ok(new { ret = 0, msg = "目标分类ID无效" });
            }

            // 如果目标分类和当前分类相同，直接返回成功
            if (request.CateId == request.TransferCateId)
            {
                return Ok(new { ret = 1, msg = "转移成功" });
            }

            // 调用服务层执行产品转移
            var result = await _productCategoryService.TransferProducts(request);

            if (result)
            {
                _logger.LogInformation($"产品转移成功: 从分类{request.CateId}转移到分类{request.TransferCateId}");
                return Ok(new { ret = 1, msg = "产品转移成功" });
            }
            else
            {
                _logger.LogWarning($"产品转移失败: 从分类{request.CateId}转移到分类{request.TransferCateId}");
                return Ok(new { ret = 0, msg = "产品转移失败" });
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "产品转移时出错");
            return Ok(new { ret = 0, msg = $"产品转移失败: {ex.Message}" });
        }
    }

    /// <summary>
    /// 批量下架/上架分类
    /// </summary>
    /// <returns>操作结果</returns>
    [HttpPost("sold-out-batch")]
    public async Task<IActionResult> SoldOutBatch()
    {
        try
        {
            // 从表单获取参数
            var id = Request.Form["id"].ToString();
            var soldOut = Request.Form["soldOut"].ToString();
            var soldProduct = Request.Form["soldProduct"].ToString();
            var productRange = Request.Form["productRange"].ToString();
            var soldNav = Request.Form["soldNav"].ToString();
            var outWithNavRadio = Request.Form["outWithNavRadio"].ToString();

            _logger.LogInformation(
                $"分类下架请求参数: id={id}, soldOut={soldOut}, soldProduct={soldProduct}, productRange={productRange}, soldNav={soldNav}, outWithNavRadio={outWithNavRadio}");

            // 验证参数
            if (!int.TryParse(id, out int categoryId) || categoryId <= 0)
            {
                return Ok(new { ret = 0, msg = "无效的分类ID" });
            }

            // 构建请求对象
            var request = new ProductCategorySoldOutRequest
            {
                Id = categoryId,
                SoldOut = soldOut == "1",
                SoldProduct = soldProduct == "1",
                ProductRange = string.IsNullOrEmpty(productRange) ? "all" : productRange,
                SoldNav = soldNav == "1",
                OutWithNavRadio = int.TryParse(outWithNavRadio, out int navRadio) ? navRadio : 0
            };
            // 执行正常的上下架操作
            var result = await _productCategoryService.BatchSoldOutCategory(request);

            if (result)
            {
                string message = request.SoldOut ? "下架成功" : "上架成功";
                return Ok(new { ret = 1, msg = message });
            }
            else
            {
                string message = request.SoldOut ? "下架失败" : "上架失败";
                return Ok(new { ret = 0, msg = message });
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "分类下架操作时出错");
            return Ok(new { ret = 0, msg = $"操作失败: {ex.Message}" });
        }
    }

    /// <summary>
    /// 设置后台是否显示已下架分类
    /// </summary>
    /// <returns>操作结果</returns>
    [HttpPost("soldout-category-show")]
    public async Task<IActionResult> SoldOutCategoryShow()
    {
        try
        {
            // 从表单获取参数
            var showSoldOutCategory = Request.Form["SoldOutCategoryShow"].ToString() == "1";

            _logger.LogInformation($"设置后台显示已下架分类: {showSoldOutCategory}");

            // 设置Cookie
            var cookieOptions = new CookieOptions
            {
                Expires = DateTime.Now.AddYears(1), // Cookie有效期为1年
                HttpOnly = false,
                IsEssential = true,
                SameSite = SameSiteMode.Lax
            };

            // 将设置存储到Cookie中
            Response.Cookies.Delete("SoldOutCategoryShow");
            Response.Cookies.Append("SoldOutCategoryShow", showSoldOutCategory.ToString(), cookieOptions);

            return Ok(new { ret = 1 });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "保存显示已下架分类设置时出错");
            return Ok(new { ret = 0, msg = $"保存设置失败: {ex.Message}" });
        }
    }
}