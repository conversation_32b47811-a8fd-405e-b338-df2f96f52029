using Microsoft.AspNetCore.Mvc;
using YseStore.IService.Products;
using YseStore.Model.RequestModels.Products;
using YseStore.Model.Utils;
using System.Web;
using Entitys;
using System.Text;
using YseStore.Common.Helper;
using YseStore.Model.VM.Sales;
using YseStore.IService.Sales;
using Elasticsearch.Net;
using Microsoft.AspNetCore.Mvc.ViewFeatures;
using YseStore.IService.Order;
using YseStore.IService.Store;
using SqlSugar;

namespace YseStoreAdmin.Controllers.Products
{
    /// <summary>
    /// 管理操作控制器
    /// </summary>
    [Route("manage/action")]
    [ApiController]
    public class ManageController : ControllerBase
    {
        private readonly IProductCategoryService _productCategoryService;
        private readonly ILogger<ManageController> _logger;
        private readonly IProductService _productService;
        private readonly ISalesCouponService _salesCouponService;
        private readonly IOrderListService _orderListService;
        private readonly IArticleService _articleService;
        private readonly IMenuService _menuService;
        private readonly ITkdLableService _tkdLableService;
        private readonly ISqlSugarClient _context;

        public ManageController(IProductCategoryService productCategoryService, ILogger<ManageController> logger,
            IProductService productService,
            ISalesCouponService salesCouponService, IOrderListService orderListService, IArticleService articleService,
            IMenuService menuService, ITkdLableService tkdLableService, ISqlSugarClient context)
        {
            _productCategoryService = productCategoryService;
            _logger = logger;
            _productService = productService;
            _salesCouponService = salesCouponService;
            _orderListService = orderListService;
            _articleService = articleService ?? throw new ArgumentNullException(nameof(articleService));
            _menuService = menuService ?? throw new ArgumentNullException(nameof(menuService));
            _tkdLableService = tkdLableService;
            _context = context ?? throw new ArgumentNullException(nameof(context));
        }

        /// <summary>
        /// 全局搜索分类
        /// </summary>
        /// <returns>分类搜索结果</returns>
        [HttpPost("global-search-category")]
        public async Task<IActionResult> GlobalSearchCategory()
        {
            try
            {
                string keyword = Request.Form["keyword"].ToString();
                string type = Request.Form["type"].ToString();

                // 创建查询请求
                var request = new ProductCategoryQueryRequest
                {
                    Keyword = keyword,
                    IncludeSoldOut = true,
                    PageSize = 100 // 设置一个合理的数量限制
                };

                // 调用服务获取分类数据
                var pageModel = await _productCategoryService.GetAllProductCategories(request);

                if (pageModel == null || !pageModel.data.Any())
                {
                    return Ok(new { ret = 1, msg = "" });
                }

                // 构建HTML结果
                var htmlResult = BuildCategoryHtml(pageModel.data, keyword, type);

                return Ok(new { ret = 1, msg = htmlResult });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "搜索分类时发生异常");
                return Ok(new { ret = 0, msg = "搜索分类时发生错误" });
            }
        }

        /// <summary>
        /// 构建分类HTML结果
        /// </summary>
        /// <param name="categories">分类列表</param>
        /// <param name="keyword">搜索关键词</param>
        /// <param name="type">分类类型</param>
        /// <returns>HTML字符串</returns>
        private string BuildCategoryHtml(List<products_category> categories, string keyword, string type)
        {
            var html = new System.Text.StringBuilder();

            // 预处理类别名称，构建完整路径显示名称
            var categoryPathMap = new Dictionary<int, string>();
            foreach (var category in categories)
            {
                string displayName = category.Category_en;

                // 如果有父级ID，构建层级路径
                if (!string.IsNullOrEmpty(category.UId) && category.UId != "0,")
                {
                    string[] parentIds = category.UId.Split(',', StringSplitOptions.RemoveEmptyEntries);
                    string parentPath = "";

                    foreach (string parentIdStr in parentIds)
                    {
                        if (int.TryParse(parentIdStr, out int parentId))
                        {
                            var parent = categories.FirstOrDefault(c => c.CateId == parentId);
                            if (parent != null)
                            {
                                if (!string.IsNullOrEmpty(parentPath))
                                {
                                    parentPath += " > ";
                                }

                                parentPath += parent.Category_en;
                            }
                        }
                    }

                    if (!string.IsNullOrEmpty(parentPath))
                    {
                        displayName = parentPath + " > " + displayName;
                    }
                }

                categoryPathMap[category.CateId] = displayName;
            }

            // 确定哪些分类应该被禁用
            var disabledCategories = new HashSet<int>();
            if (!string.IsNullOrEmpty(type))
            {
                // 根据type参数确定哪些分类应该被禁用
                if (type == "manual")
                {
                    // manual类型下禁用某些特定分类，如非手动添加的分类
                    disabledCategories = new HashSet<int>(
                        categories.Where(c => c.AddMethod != "manual").Select(c => c.CateId)
                    );
                }
                else if (type == "fromSubcategory")
                {
                    // fromSubcategory类型下禁用某些特定分类
                    disabledCategories = new HashSet<int>(
                        categories.Where(c => c.AddMethod != "fromSubcategory").Select(c => c.CateId)
                    );
                }
            }

            // 为每个分类生成HTML
            foreach (var category in categories.OrderBy(c => c.Dept).ThenBy(c => c.MyOrder))
            {
                string displayName = categoryPathMap[category.CateId];
                string highlightedName = HighlightKeyword(displayName, keyword);

                // 判断分类是否应该被禁用
                bool isDisabled = disabledCategories.Contains(category.CateId);
                string disabledClass = isDisabled ? " disabled" : "";
                string disabledAttr = isDisabled ? " disabled=\"disabled\"" : "";

                // 注意：前端JS要求特定的HTML结构才能正常工作
                // 重要：确保HTML结构与ProductEdit.cshtml中完全一致，但添加更明确的点击行为
                html.Append($@"
<div class=""tr"">
    <div class=""td c_name"">
        <span
            class=""input_checkbox_box{disabledClass}""
                onclick=""if(!$(this).hasClass('disabled')){{$(this).toggleClass('checked');var input=$(this).find('input');input.prop('checked', !input.prop('checked')).trigger('change');}}"">
            <span class=""input_checkbox"">
                <input type=""checkbox""
                       {disabledAttr}
                       name=""GlobalSelectCateId""
                       data-alias=""{HttpUtility.HtmlEncode(displayName)}""
                       value=""{category.CateId}"">
            </span>{highlightedName}
        </span>
    </div>
</div>");
            }

            return html.ToString();
        }

        /// <summary>
        /// 高亮显示搜索关键词
        /// </summary>
        /// <param name="text">原始文本</param>
        /// <param name="keyword">关键词</param>
        /// <returns>高亮后的HTML</returns>
        private string HighlightKeyword(string text, string keyword)
        {
            if (string.IsNullOrEmpty(keyword))
            {
                return text;
            }

            // 获取需要高亮的字符集合
            var highlightChars = new HashSet<char>(keyword.ToLower().Distinct());

            // 将文本分成多个部分处理，而不是直接使用正则替换整个字符串
            var result = new System.Text.StringBuilder();
            string[] parts = text.Split(' ', StringSplitOptions.RemoveEmptyEntries);

            for (int i = 0; i < parts.Length; i++)
            {
                string part = parts[i];

                // 处理 ">" 符号
                if (part == ">")
                {
                    result.Append(" > ");
                    continue;
                }

                string processedPart = "";

                // 检查此部分是否包含关键词
                bool containsKeyword = !string.IsNullOrEmpty(keyword) &&
                                       part.IndexOf(keyword, StringComparison.OrdinalIgnoreCase) >= 0;

                if (containsKeyword)
                {
                    // 按字符处理带关键词的部分
                    for (int j = 0; j < part.Length; j++)
                    {
                        char c = part[j];
                        bool isInKeyword = false;

                        // 检查当前位置是否是关键词的开始
                        if (j + keyword.Length <= part.Length)
                        {
                            isInKeyword = true;
                            for (int k = 0; k < keyword.Length; k++)
                            {
                                if (char.ToLower(part[j + k]) != char.ToLower(keyword[k]))
                                {
                                    isInKeyword = false;
                                    break;
                                }
                            }
                        }

                        if (isInKeyword)
                        {
                            // 高亮关键词中的每个字符
                            for (int k = 0; k < keyword.Length && j + k < part.Length; k++)
                            {
                                processedPart += $"<span class=\"primarycolor\">{part[j + k]}</span>";
                            }

                            // 跳过已处理的字符
                            j += keyword.Length - 1;
                        }
                        else if (highlightChars.Contains(char.ToLower(c)))
                        {
                            // 高亮与关键词中字符相匹配的字符
                            processedPart += $"<span class=\"primarycolor\">{c}</span>";
                        }
                        else
                        {
                            processedPart += c;
                        }
                    }
                }
                else
                {
                    // 如果不包含关键词，高亮与关键词相关的字符
                    for (int j = 0; j < part.Length; j++)
                    {
                        char c = part[j];
                        if (highlightChars.Contains(char.ToLower(c)))
                        {
                            processedPart += $"<span class=\"primarycolor\">{c}</span>";
                        }
                        else
                        {
                            processedPart += c;
                        }
                    }
                }

                result.Append(processedPart);

                // 添加空格，除非是最后一个部分
                if (i < parts.Length - 1 && parts[i + 1] != ">")
                {
                    result.Append(" ");
                }
            }

            return result.ToString();
        }

        /// <summary>
        /// 处理下拉菜单Ajax请求，用于加载子分类数据
        /// </summary>
        /// <returns>子分类数据及返回上级按钮信息</returns>
        [HttpPost("box-drop-double")]
        public async Task<IActionResult> BoxDropDouble()
        {
            try
            {
                // 获取请求参数
                string value = Request.Form["Value"].ToString();
                string type = Request.Form["Type"].ToString();
                string table = Request.Form["Table"].ToString();
                string top = Request.Form["Top"].ToString();
                string all = Request.Form["All"].ToString();
                string checkbox = Request.Form["Checkbox"].ToString();
                string topType = Request.Form["TopType"].ToString();
                string customData = Request.Form.ContainsKey("CustomData")
                    ? Request.Form["CustomData"].ToString()
                    : string.Empty;
                string keyword = Request.Form.ContainsKey("Keyword")
                    ? Request.Form["Keyword"].ToString()
                    : string.Empty;
                string start = Request.Form.ContainsKey("Start") ? Request.Form["Start"].ToString() : "0";
                string exclude = Request.Form.ContainsKey("exclude")
                    ? Request.Form["exclude"].ToString()
                    : string.Empty;

                _logger.LogInformation($"BoxDropDouble参数: Value={value}, Type={type}, Table={table}, Top={top}");

                // 如果是返回顶级
                if (top == "1")
                {
                    // 返回顶级数据
                    return await GetTopLevelData(type, table, checkbox);
                }

                // 处理不同类型的下拉菜单数据请求
                switch (type.ToLower())
                {
                    case "products_category":
                        // 获取子分类
                        return await GetChildCategories(value, type, table, checkbox);
                    case "article":
                        return await GetArticleDropDownData(value, type, table, checkbox, start, keyword);
                    case "category":
                        return await GetCategoryDropDownData(value, type, table, checkbox, start, keyword);
                    case "products":
                        return await GetProductsDropDownData(value, type, table, checkbox, start, keyword);
                    case "products_tags":
                        return await GetProductTagsDropDownData(value, type, table, checkbox, start, keyword);
                    case "customer":
                        return await GetCustomerDropDownData(value, type, table, checkbox, start, keyword);
                    default:
                        // 默认返回空结果
                        return Ok(new
                        {
                            ret = 1,
                            msg = new
                            {
                                Html = "",
                                Count = 0,
                                Start = 0,
                                Back = new { Value = "0", Type = type, Table = table, Top = 1 }
                            }
                        });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "处理下拉菜单请求时出错");
                return Ok(new { ret = 0, msg = "处理请求时出错" });
            }
        }

        /// <summary>
        /// 获取顶级分类
        /// </summary>
        private async Task<IActionResult> GetTopLevelCategories(string type, string table, string checkbox)
        {
            // 创建查询请求
            var request = new ProductCategoryQueryRequest
            {
                PageIndex = 1,
                PageSize = 1000, // 设置足够大的值以获取所有分类
                OrderByFileds = "MyOrder ASC" // 按排序字段正序获取
            };

            // 获取所有分类
            var pageModel = await _productCategoryService.GetAllProductCategories(request);
            if (pageModel == null || !pageModel.data.Any())
            {
                return Ok(new
                {
                    ret = 1,
                    msg = new
                    {
                        Html = "",
                        Count = 0,
                        Start = 0,
                        Back = new { Value = "0", Type = type, Table = table, Top = 1 }
                    }
                });
            }

            // 构建分类树
            var categoryTree = CategoryTreeBuilder.BuildCategoryTreeGeneric(pageModel.data);

            // 获取顶级分类
            var topCategories = pageModel.data.Where(c => c.UId == "0," || c.UId == "0" || string.IsNullOrEmpty(c.UId))
                .ToList();

            // 构建HTML
            var html = new System.Text.StringBuilder();
            foreach (var category in topCategories.OrderBy(c => c.MyOrder))
            {
                bool hasChildren = categoryTree.ContainsKey(category.CateId) && categoryTree[category.CateId].Count > 0;
                string childrenClass = hasChildren ? " children" : "";
                string itemName = HttpUtility.HtmlEncode(category.Category_en);

                if (checkbox == "1") // 多选
                {
                    html.Append($@"
<div class=""item{childrenClass}"" data-name=""{itemName}"" data-value=""{category.CateId}"" data-type=""{type}"" data-table=""{table}"">
    <span class=""input_checkbox_box"">
        <span class=""input_checkbox"">
            <input type=""checkbox"" name=""list[]"" value=""{category.CateId}"">
        </span>
    </span>
    <span class=""item_name"">{itemName}</span>
    {(hasChildren ? "<em></em>" : "")}
</div>");
                }
                else // 单选
                {
                    html.Append($@"
<div class=""item{childrenClass}"" data-name=""{itemName}"" data-value=""{category.CateId}"" data-type=""{type}"" data-table=""{table}"">
    <span class=""input_radio_box"">
        <span class=""input_radio"">
            <input type=""radio"" name=""list[]"" value=""{category.CateId}"">
        </span>
    </span>
    <span class=""item_name"">{itemName}</span>
    {(hasChildren ? "<em></em>" : "")}
</div>");
                }
            }

            return Ok(new
            {
                ret = 1,
                msg = new
                {
                    Html = html.ToString(),
                    Count = topCategories.Count,
                    Start = 0,
                    Back = new { Value = "0", Type = type, Table = table, Top = 1 }
                }
            });
        }

        /// <summary>
        /// 获取子分类
        /// </summary>
        private async Task<IActionResult> GetChildCategories(string parentId, string type, string table,
            string checkbox)
        {
            if (!int.TryParse(parentId, out int parentIdInt))
            {
                return Ok(new { ret = 0, msg = "无效的分类ID" });
            }

            // 创建查询请求
            var request = new ProductCategoryQueryRequest
            {
                PageIndex = 1,
                PageSize = 1000, // 设置足够大的值以获取所有分类
                OrderByFileds = "MyOrder ASC" // 按排序字段正序获取
            };

            // 获取所有分类
            var pageModel = await _productCategoryService.GetAllProductCategories(request);
            if (pageModel == null || !pageModel.data.Any())
            {
                return Ok(new
                {
                    ret = 1,
                    msg = new
                    {
                        Html = "",
                        Count = 0,
                        Start = 0,
                        Back = new { Value = "0", Type = type, Table = table, Top = 1 }
                    }
                });
            }

            // 构建分类树
            var categoryTree = CategoryTreeBuilder.BuildCategoryTreeGeneric(pageModel.data);

            // 获取指定父分类ID的子分类
            if (!categoryTree.ContainsKey(parentIdInt))
            {
                return Ok(new
                {
                    ret = 1,
                    msg = new
                    {
                        Html = "",
                        Count = 0,
                        Start = 0,
                        Back = new { Value = "0", Type = type, Table = table, Top = 1 }
                    }
                });
            }

            var childCategories = categoryTree[parentIdInt];

            // 构建HTML
            var html = new System.Text.StringBuilder();
            foreach (var category in childCategories.OrderBy(c => c.MyOrder))
            {
                bool hasChildren = categoryTree.ContainsKey(category.CateId) && categoryTree[category.CateId].Count > 0;
                string childrenClass = hasChildren ? " children" : "";
                string itemName = HttpUtility.HtmlEncode(category.Category_en);

                if (checkbox == "1") // 多选
                {
                    html.Append($@"
<div class=""item{childrenClass}"" data-name=""{itemName}"" data-value=""{category.CateId}"" data-type=""{type}"" data-table=""{table}"">
    <span class=""input_checkbox_box"">
        <span class=""input_checkbox"">
            <input type=""checkbox"" name=""list[]"" value=""{category.CateId}"">
        </span>
    </span>
    <span class=""item_name"">{itemName}</span>
    {(hasChildren ? "<em></em>" : "")}
</div>");
                }
                else // 单选
                {
                    html.Append($@"
<div class=""item{childrenClass}"" data-name=""{itemName}"" data-value=""{category.CateId}"" data-type=""{type}"" data-table=""{table}"">
    <span class=""input_radio_box"">
        <span class=""input_radio"">
            <input type=""radio"" name=""list[]"" value=""{category.CateId}"">
        </span>
    </span>
    <span class=""item_name"">{itemName}</span>
    {(hasChildren ? "<em></em>" : "")}
</div>");
                }
            }

            // 获取父分类信息，用于构建返回按钮
            var parentCategory = pageModel.data.FirstOrDefault(c => c.CateId == parentIdInt);
            string returnValue = "0";
            if (parentCategory != null && !string.IsNullOrEmpty(parentCategory.UId))
            {
                var uidParts = parentCategory.UId.Split(',', StringSplitOptions.RemoveEmptyEntries);
                if (uidParts.Length > 0)
                {
                    returnValue = uidParts[uidParts.Length - 1];
                }
            }

            return Ok(new
            {
                ret = 1,
                msg = new
                {
                    Html = html.ToString(),
                    Count = childCategories.Count,
                    Start = 0,
                    Back = new { Value = returnValue, Type = type, Table = table, Top = returnValue == "0" ? 1 : 0 }
                }
            });
        }

        /// <summary>
        /// 获取客户下拉数据
        /// </summary>
        private async Task<IActionResult> GetCustomerDropDownData(string value, string type, string table,
            string checkbox, string start, string keyword)
        {
            try
            {
                StringBuilder html = new StringBuilder();
                var UserDataList = await _orderListService.GetNextPageUserBookData(Convert.ToInt32(start), keyword);
                if (UserDataList.Count > 0)
                {
                    foreach (var item in UserDataList)
                    {
                        html.AppendFormat(
                            @"<div class=""item"" data-name=""{0}"" data-value=""{1}"" data-type=""customer"" data-table=""customer"">
                        <span>{2}</span>
                    </div>", item.Name, item.Value, item.Name);
                    }
                }

                return Ok(new
                {
                    ret = 1,
                    msg = new
                    {
                        Html = html.ToString(),
                        Count = UserDataList.Count,
                        Start = start,
                        Back = new { Value = value, Type = type, Table = table, Top = 0 }
                    }
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取客户下拉数据失败");
                return Ok(new { ret = 0, msg = "获取客户下拉数据失败" });
            }
        }

        /// <summary>
        /// 获取文章下拉数据
        /// </summary>
        private async Task<IActionResult> GetArticleDropDownData(string value, string type, string table,
            string checkbox, string start, string keyword)
        {
            try
            {
                var html = new System.Text.StringBuilder();
                int startIndex = int.Parse(start);
                int pageSize = 20;

                var query = _context.Queryable<article>().Where(a => a.AId > 0);

                if (!string.IsNullOrEmpty(keyword))
                {
                    query = query.Where(a => a.Title_en.Contains(keyword));
                }

                var articles = await query
                    .OrderBy(a => a.AId)
                    .Skip(startIndex)
                    .Take(pageSize)
                    .ToListAsync();

                foreach (var article in articles)
                {
                    string itemName = System.Web.HttpUtility.HtmlEncode(article.Title_en ?? "");
                    html.Append(BuildDropDownItem(itemName, article.AId.ToString(), type, table, checkbox));
                }

                return Ok(new
                {
                    ret = 1,
                    msg = new
                    {
                        Html = html.ToString(),
                        Count = articles.Count,
                        Start = start,
                        Back = new { Value = value, Type = 1, Table = table, Top = 0 }
                    }
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取文章下拉数据失败");
                return Ok(new { ret = 0, msg = "获取文章下拉数据失败" });
            }
        }

        /// <summary>
        /// 获取分类下拉数据
        /// </summary>
        private async Task<IActionResult> GetCategoryDropDownData(string value, string type, string table,
            string checkbox, string start, string keyword)
        {
            try
            {
                var html = new System.Text.StringBuilder();
                int startIndex = int.Parse(start);
                int pageSize = 20;

                var query = _context.Queryable<products_category>().Where(c => c.CateId > 0);

                if (!string.IsNullOrEmpty(keyword))
                {
                    query = query.Where(c => c.Category_en.Contains(keyword));
                }

                // 如果指定了父分类值，则查询子分类
                if (!string.IsNullOrEmpty(value) && value != "0")
                {
                    query = query.Where(c => c.UId.Contains($"{value},"));
                }
                else
                {
                    // 否则查询顶级分类
                    query = query.Where(c => c.UId == "0," || c.UId == "0" || string.IsNullOrEmpty(c.UId));
                }

                var categories = await query
                    .OrderBy(c => c.MyOrder)
                    .Skip(startIndex)
                    .Take(pageSize)
                    .ToListAsync();

                foreach (var category in categories)
                {
                    string itemName = System.Web.HttpUtility.HtmlEncode(category.Category_en ?? "");
                    bool hasChildren = await _context.Queryable<products_category>()
                        .Where(c => c.UId.Contains($"{category.CateId},"))
                        .AnyAsync();
                    html.Append(BuildDropDownItem(itemName, category.CateId.ToString(), type, table, checkbox,
                        hasChildren));
                }

                return Ok(new
                {
                    ret = 1,
                    msg = new
                    {
                        Html = html.ToString(),
                        Count = categories.Count,
                        Start = start,
                        Back = new { Value = value, Type = 1, Table = table, Top = 0 }
                    }
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取分类下拉数据失败");
                return Ok(new { ret = 0, msg = "获取分类下拉数据失败" });
            }
        }

        /// <summary>
        /// 获取产品下拉数据
        /// </summary>
        private async Task<IActionResult> GetProductsDropDownData(string value, string type, string table,
            string checkbox, string start, string keyword)
        {
            try
            {
                var html = new System.Text.StringBuilder();
                int startIndex = int.Parse(start);
                int pageSize = 20;

                // 如果是第一页，添加 "All Products" 选项
                if (startIndex == 0)
                {
                    html.Append(BuildProductDropDownItem("All Products", "0", "all_products", "", checkbox));
                }

                var query = _context.Queryable<products>().Where(p => p.ProId > 0);

                if (!string.IsNullOrEmpty(keyword))
                {
                    query = query.Where(p => p.Name_en.Contains(keyword));
                }

                var products = await query
                    .OrderByDescending(p => p.ProId) // 按ID降序排列，匹配原始接口
                    .Skip(startIndex)
                    .Take(pageSize)
                    .ToListAsync();

                foreach (var product in products)
                {
                    string itemName = System.Web.HttpUtility.HtmlEncode(product.Name_en ?? "");
                    string iconUrl = !string.IsNullOrEmpty(product.PicPath_0) ? product.PicPath_0 : "";
                    html.Append(
                        BuildProductDropDownItem(itemName, product.ProId.ToString(), type, table, checkbox, iconUrl));
                }

                return Ok(new
                {
                    ret = 1,
                    msg = new
                    {
                        Html = html.ToString(),
                        Count = products.Count,
                        Start = startIndex,
                        Back = new
                        {
                            Value = value, Type = 1, Table = table,
                            Top = value == "0" || string.IsNullOrEmpty(value) ? 1 : 0
                        }
                    }
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取产品下拉数据失败");
                return Ok(new { ret = 0, msg = "获取产品下拉数据失败" });
            }
        }

        /// <summary>
        /// 获取顶级数据
        /// </summary>
        private async Task<IActionResult> GetTopLevelData(string type, string table, string checkbox)
        {
            var html = new System.Text.StringBuilder();
            int count = 0;

            switch (type.ToLower())
            {
                case "article":
                    var articles = await _context.Queryable<article>()
                        .Where(a => a.AId > 0)
                        .OrderBy(a => a.AId)
                        .Take(20)
                        .ToListAsync();

                    foreach (var article in articles)
                    {
                        string itemName = System.Web.HttpUtility.HtmlEncode(article.Title_en ?? "");
                        html.Append(BuildDropDownItem(itemName, article.AId.ToString(), type, table, checkbox));
                    }

                    count = articles.Count;
                    break;

                case "category":
                    var categories = await _context.Queryable<products_category>()
                        .Where(c => c.UId == "0," || c.UId == "0" || string.IsNullOrEmpty(c.UId))
                        .OrderBy(c => c.MyOrder)
                        .Take(20)
                        .ToListAsync();

                    foreach (var category in categories)
                    {
                        string itemName = System.Web.HttpUtility.HtmlEncode(category.Category_en ?? "");
                        bool hasChildren = await _context.Queryable<products_category>()
                            .Where(c => c.UId.Contains($"{category.CateId},"))
                            .AnyAsync();
                        html.Append(BuildDropDownItem(itemName, category.CateId.ToString(), type, table, checkbox,
                            hasChildren));
                    }

                    count = categories.Count;
                    break;

                case "products":
                    // 添加 "All Products" 选项
                    html.Append(BuildProductDropDownItem("All Products", "0", "all_products", "", checkbox));

                    var products = await _context.Queryable<products>()
                        .Where(p => p.ProId > 0)
                        .OrderByDescending(p => p.ProId)
                        .Take(20)
                        .ToListAsync();

                    foreach (var product in products)
                    {
                        string itemName = System.Web.HttpUtility.HtmlEncode(product.Name_en ?? "");
                        string iconUrl = !string.IsNullOrEmpty(product.PicPath_0) ? product.PicPath_0 : "";
                        html.Append(BuildProductDropDownItem(itemName, product.ProId.ToString(), type, table, checkbox,
                            iconUrl));
                    }

                    count = products.Count + 1; // +1 for "All Products"
                    break;

                case "products_category":
                    // 使用现有的GetTopLevelCategories逻辑
                    return await GetTopLevelCategories(type, table, checkbox);

                case "products_tags":
                    var tags = await _productService.GetAllProductTags();
                    if (tags != null && tags.Any())
                    {
                        var topTags = tags.Take(20).ToList();
                        foreach (var tag in topTags)
                        {
                            string tagName = !string.IsNullOrEmpty(tag.Name_en) ? tag.Name_en : $"Tag_{tag.TId}";
                            html.Append(BuildDropDownItem(tagName, tag.TId.ToString(), type, table, checkbox));
                        }

                        count = topTags.Count;
                    }

                    break;
            }

            return Ok(new
            {
                ret = 1,
                msg = new
                {
                    Html = html.ToString(),
                    Count = count,
                    Start = 0,
                    Back = new { Value = "0", Type = type, Table = table, Top = 1 }
                }
            });
        }

        /// <summary>
        /// 构建下拉选项HTML
        /// </summary>
        private string BuildDropDownItem(string name, string value, string type, string table, string checkbox,
            bool hasChildren = false)
        {
            string childrenClass = hasChildren ? " children" : "";
            string itemName = System.Web.HttpUtility.HtmlEncode(name);

            if (checkbox == "1") // 多选
            {
                return $@"
<div class=""item{childrenClass}"" data-name=""{itemName}"" data-value=""{value}"" data-type=""{type}"" data-table=""{table}"">
    <span class=""input_checkbox_box"">
        <span class=""input_checkbox"">
            <input type=""checkbox"" name=""_DoubleOption"" value=""{value}"">
        </span>
    </span>
    <span class=""item_name"">{itemName}</span>
    {(hasChildren ? "<em></em>" : "")}
</div>";
            }
            else // 单选
            {
                return $@"
<div class=""item{childrenClass}"" data-name=""{itemName}"" data-value=""{value}"" data-type=""{type}"" data-table=""{table}"">
    <span class=""input_radio_box"">
        <span class=""input_radio"">
            <input type=""radio"" name=""_DoubleOption"" value=""{value}"">
        </span>
    </span>
    <span class=""item_name"">{itemName}</span>
    {(hasChildren ? "<em></em>" : "")}
</div>";
            }
        }

        /// <summary>
        /// 构建产品下拉选项HTML（包含图片）
        /// </summary>
        private string BuildProductDropDownItem(string name, string value, string type, string table, string checkbox,
            string iconUrl = "")
        {
            string itemName = System.Web.HttpUtility.HtmlEncode(name);
            string iconHtml = "";

            if (type == "products")
            {
                iconHtml = $@"
        <em class=""icon icon_products pic_box"">
            <img src=""{iconUrl}""/>
            <span></span>
        </em>
        ";
            }

            if (checkbox == "1") // 多选
            {
                return $@"
<div class=""item"" data-name=""{itemName}"" data-value=""{value}"" data-type=""{type}"" data-table=""{table}"">
    <span class=""input_checkbox_box"">
        <span class=""input_checkbox"">
            <input type=""checkbox"" name=""_DoubleOption"" value=""{value}"">
        </span>
    </span>
    <span class=""item_name"">{iconHtml}{itemName}</span>
</div>";
            }
            else // 单选
            {
                return $@"
<div class=""item"" data-name=""{itemName}"" data-value=""{value}"" data-type=""{type}"" data-table=""{table}"">
    <span class=""input_radio_box"">
        <span class=""input_radio"">
            <input type=""radio"" name=""_DoubleOption"" value=""{value}"">
        </span>
    </span>
    <span class=""item_name"">{iconHtml}{itemName}</span>
</div>";
            }
        }

        /// <summary>
        /// 获取产品标签下拉数据
        /// </summary>
        /// <summary>
        /// 获取产品标签下拉数据
        /// </summary>
        private async Task<IActionResult> GetProductTagsDropDownData(string value, string type, string table,
            string checkbox, string start, string keyword)
        {
            try
            {
                var html = new StringBuilder();
                int pageNumber = int.TryParse(start, out int s) ? s : 1; // start参数表示页码，从1开始
                int pageSize = 20; // 每页显示20条

                // 计算要跳过的数据量
                int skipCount = pageNumber * pageSize;

                // 获取所有产品标签
                var allTags = await _productService.GetAllProductTags();
                if (allTags == null || !allTags.Any())
                {
                    return Ok(new
                    {
                        ret = 1,
                        msg = new
                        {
                            Html = "",
                            Count = 0,
                            Start = pageNumber, // 返回当前页码而不是跳过的数量
                            Back = new { Value = "", Type = type, Table = table, Top = 1 }
                        }
                    });
                }

                // 如果有关键词，进行过滤
                var filteredTags = allTags.AsQueryable();
                if (!string.IsNullOrEmpty(keyword))
                {
                    filteredTags = filteredTags.Where(t =>
                        (!string.IsNullOrEmpty(t.Name_en) &&
                         t.Name_en.Contains(keyword, StringComparison.OrdinalIgnoreCase)));
                }

                var tagsList = filteredTags.ToList();

                // 分页处理 - 使用skipCount来跳过已加载的数据
                var pagedTags = tagsList.Skip(skipCount).Take(pageSize).ToList();

                // 生成HTML
                foreach (var tag in pagedTags)
                {
                    string tagName = !string.IsNullOrEmpty(tag.Name_en) ? tag.Name_en : $"Tag_{tag.TId}";
                    html.Append(BuildDropDownItem(tagName, tag.TId.ToString(), type, table, checkbox));
                }

                // 计算下一页页码
                int nextPage = pageNumber + 1;

                return Ok(new
                {
                    ret = 1,
                    msg = new
                    {
                        Html = html.ToString(),
                        Count = pagedTags.Count,
                        Start = nextPage, // 返回下一页的页码，而不是跳过的数据量
                        Back = new { Value = "", Type = type, Table = table, Top = 1 }
                    }
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取产品标签下拉数据时出错");
                return Ok(new
                {
                    ret = 0,
                    msg = new
                    {
                        Html = "",
                        Count = 0,
                        Start = int.TryParse(start, out int s) ? s : 1, // 保持原始页码
                        Back = new { Value = "", Type = type, Table = table, Top = 1 }
                    }
                });
            }
        }
    }
}