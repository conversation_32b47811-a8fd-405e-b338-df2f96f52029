using Microsoft.AspNetCore.Mvc;
using YseStore.IService.Products;
using YseStore.Model.RequestModels.Products;
using System.Web;
using Newtonsoft.Json;
using Entitys;

namespace YseStoreAdmin.Controllers.Products
{
    /// <summary>
    /// 推荐产品控制器
    /// </summary>
    [ApiController]
    [Route("manage/plugins/products-recommend/")]
    public class ProductRecommendController : ControllerBase
    {
        private readonly IProductRecommendService _productRecommendService;
        private readonly IProductService _productService;
        private readonly ILogger<ProductRecommendController> _logger;

        public ProductRecommendController(
            IProductRecommendService productRecommendService,
            IProductService productService,
            ILogger<ProductRecommendController> logger)
        {
            _productRecommendService = productRecommendService ??
                                       throw new ArgumentNullException(nameof(productRecommendService));
            _productService = productService ?? throw new ArgumentNullException(nameof(productService));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        /// <summary>
        /// 获取推荐产品列表
        /// </summary>
        /// <param name="request">查询请求参数</param>
        /// <returns>推荐产品列表</returns>
        [HttpPost("list")]
        public async Task<IActionResult> GetRecommendList([FromBody] ProductRecommendQueryRequest request)
        {
            if (request == null)
            {
                request = new ProductRecommendQueryRequest();
            }

            try
            {
                var result = await _productRecommendService.GetRecommendList(request);

                // 构建包含筛选条件的URL
                var queryParams = new System.Collections.Specialized.NameValueCollection();
                if (!string.IsNullOrEmpty(request.Keyword))
                    queryParams.Add("keyword", request.Keyword);
                if (!string.IsNullOrEmpty(request.ProductsType))
                    queryParams.Add("productsType", request.ProductsType);
                if (!string.IsNullOrEmpty(request.ProductsScope))
                    queryParams.Add("productsScope", request.ProductsScope);

                // 构建URL
                var url = "/Products/Recommend";
                if (queryParams.Count > 0)
                {
                    var queryString = string.Join("&",
                        queryParams.AllKeys.Select(key => $"{key}={HttpUtility.UrlEncode(queryParams[key])}"));
                    url += "?" + queryString;
                }

                return Ok(new { success = true, data = result, url });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取推荐产品列表时出错");
                return StatusCode(500, new { success = false, message = $"获取推荐产品列表时出错: {ex.Message}" });
            }
        }
        
        /// <summary>
        /// 删除推荐产品
        /// </summary>
        /// <param name="ids">推荐产品ID列表</param>
        /// <returns>删除结果</returns>
        [HttpGet("delete-batch")]
        public async Task<IActionResult> DeleteRecommends([FromQuery] string id)
        {
            try
            {
                var ids = id?.Split('-').Select(int.Parse).ToList() ?? new List<int>();
                if (ids == null || !ids.Any())
                {
                    return Ok(new { ret = 0, msg = "请选择要删除的推荐产品" });
                }

                var result = await _productRecommendService.DeleteRecommends(ids);
                if (result)
                {
                    return Ok(new { ret = 1, msg = "删除成功" });
                }
                else
                {
                    return Ok(new { ret = 0, msg = "删除失败" });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "删除推荐产品时出错，ID: {Id}", id);
                return Ok(new { ret = 0, msg = $"删除推荐产品时出错: {ex.Message}" });
            }
        }

        /// <summary>
        /// 获取推荐产品信息（用于点击产品数量时显示详情）
        /// </summary>
        /// <param name="request">包含RId的请求</param>
        /// <returns>推荐产品信息</returns>
        [HttpPost("get-recommend-products-info")]
        public async Task<IActionResult> GetRecommendProductsInfo([FromForm] GetRecommendProductsInfoRequest request)
        {
            if (request.RId <= 0)
            {
                return Ok(new { ret = 0, msg = "无效的推荐ID" });
            }

            try
            {
                var recommend = await _productRecommendService.GetRecommendById(request.RId);
                if (recommend == null)
                {
                    return Ok(new { ret = 0, msg = "推荐不存在" });
                }

                object productInfo;

                // 根据推荐类型决定如何解析产品信息
                if (recommend.ProductsType == "recommended_set")
                {
                    // 推荐集：从ProductsValue字段解析产品ID，然后查询产品详情
                    productInfo = await ParseProductsFromProductsValue(recommend.ProductsValue);
                }
                else
                {
                    // 其他类型：从Data字段解析产品信息
                    productInfo = ParseProductsFromData(recommend.Data);
                }

                return Ok(new { ret = 1, msg = productInfo });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取推荐产品信息时发生错误");
                return Ok(new { ret = 0, msg = "获取信息失败" });
            }
        }

        /// <summary>
        /// 添加产品到推荐集
        /// </summary>
        /// <returns>操作结果</returns>
        [HttpPost("add-products")]
        public async Task<IActionResult> AddProducts()
        {
            try
            {
                // 获取选中的产品ID列表
                var productIds = Request.Form["ProId[]"].ToList();

                if (productIds == null || !productIds.Any())
                {
                    return Ok(new { ret = 0, msg = "请选择要添加的产品" });
                }

                // 解析产品ID并获取产品信息
                var validProductIds = new List<int>();
                foreach (var productIdStr in productIds)
                {
                    if (int.TryParse(productIdStr, out int productId))
                    {
                        validProductIds.Add(productId);
                    }
                }

                if (!validProductIds.Any())
                {
                    return Ok(new { ret = 0, msg = "无效的产品ID" });
                }

                // 获取产品信息
                var products = await GetProductsByIds(validProductIds);

                // 构建产品HTML
                var html = "";
                foreach (var product in products)
                {
                    var imagePath = !string.IsNullOrEmpty(product.PicPath_0)
                        ? product.PicPath_0
                        : "/assets/images/default-product.jpg";

                    // 为非默认图片添加OSS处理参数
                    if (!string.IsNullOrEmpty(imagePath) && !imagePath.StartsWith("/assets/"))
                    {
                        imagePath += "?x-oss-process=image/format,webp/resize,m_lfit,h_120,w_120/quality,q_90";
                    }

                    var productName = !string.IsNullOrEmpty(product.Name_en)
                        ? product.Name_en
                        : $"产品 {product.ProId}";

                    html += $@"
                        <div class='item' data-proid='{product.ProId}'>
                            <div class='img'>
                                <img src='{imagePath}' />
                                <span></span>
                            </div>
                            <div class='name'>{productName}</div>
                            <div class='del p_del'><i></i></div>
                            <input type='hidden' name='ProId[]' value='{product.ProId}' />
                        </div>";
                }

                return Ok(new { ret = 1, msg = html });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "添加产品到推荐集时发生错误");
                return Ok(new { ret = 0, msg = "添加产品失败" });
            }
        }

        /// <summary>
        /// 创建或更新推荐产品
        /// </summary>
        /// <param name="request">推荐产品更新请求</param>
        /// <param name="id">推荐产品ID（从URL获取）</param>
        /// <returns>操作结果</returns>
        [HttpPost("update")]
        public async Task<IActionResult> UpdateRecommend([FromQuery] int id = 0)
        {
            try
            {
                // 手动从表单中获取数据
                var showType = Request.Form["ShowType"].FirstOrDefault() ?? "auto";
                var productsScope = Request.Form["ProductsScope"].FirstOrDefault() ?? "";

                // 根据ProductsScope决定从哪个字段获取ProductsValue
                string productsValue = "";
                switch (productsScope)
                {
                    case "category":
                        productsValue = ConvertArrayToPipeFormat(Request.Form["products_categoryCurrent[]"]);
                        break;
                    case "tags":
                        productsValue = ConvertArrayToPipeFormat(Request.Form["products_tagsCurrent[]"]);
                        break;
                    case "products":
                    default:
                        productsValue = ConvertArrayToPipeFormat(Request.Form["productsCurrent[]"]);
                        break;
                }

                // 处理ProductsValue字段
                string finalProductsValue = productsValue;

                // 如果是推荐集类型，使用ProId[]字段的数据
                if (Request.Form["Type"].FirstOrDefault() == "recommended_set")
                {
                    var recommendSetProductIds = Request.Form["ProId[]"].ToList();
                    if (recommendSetProductIds.Any())
                    {
                        // 转换为管道分隔格式：|1|2|3|
                        finalProductsValue = "|" + string.Join("|", recommendSetProductIds) + "|";
                    }
                    else
                    {
                        finalProductsValue = "";
                    }
                }

                var request = new ProductRecommendUpdateRequest
                {
                    Id = id,
                    ShowType = showType,
                    // 如果是手动添加模式，清空Pages字段
                    Page = showType == "manual" ? new string[0] : Request.Form["Page[]"].ToArray(),
                    Type = Request.Form["Type"].FirstOrDefault() ?? "",
                    ProductsScope = productsScope,
                    ProductsValue = finalProductsValue,
                    Products = Request.Form["Products"].FirstOrDefault() ?? "",
                    ShowPosition = Request.Form["ShowPosition"].FirstOrDefault() ?? "",
                    Mode = Request.Form["Mode"].FirstOrDefault() ?? "",
                    ProductsType = Request.Form["ProductsType"].FirstOrDefault() ?? "",
                    ExcludeValue = ConvertArrayToPipeFormat(Request.Form["products_excludeCurrent[]"]),
                    Exclude = Request.Form["Exclude"].FirstOrDefault() ?? "",
                    ExcludeType = Request.Form["ExcludeType"].FirstOrDefault() ?? "",
                    Data = new ProductRecommendDataRequest
                    {
                        Title = Request.Form["Data[Title]"].FirstOrDefault() ?? "",
                        SubTitle = Request.Form["Data[SubTitle]"].FirstOrDefault() ?? ""
                    }
                };
                
                // 获取数量，如果没有提供则使用默认值
                if (int.TryParse(Request.Form["Quantity"].FirstOrDefault(), out int quantity))
                {
                    request.Quantity = quantity;
                }
                else
                {
                    request.Quantity = 10;
                }
                
                var resultId = await _productRecommendService.CreateOrUpdateRecommend(request);

                if (resultId > 0)
                {
                    return Ok(new { ret = 1, msg = resultId });
                }
                else
                {
                    return Ok(new { ret = 0, msg = "保存失败" });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "创建或更新推荐产品时出错");
                return Ok(new { ret = 0, msg = $"保存失败: {ex.Message}" });
            }
        }

        /// <summary>
        /// 将表单数组转换为管道符分割格式
        /// </summary>
        /// <param name="formValues">表单值数组</param>
        /// <returns>管道符分割的字符串，格式如 |1|2|3|</returns>
        private string ConvertArrayToPipeFormat(Microsoft.Extensions.Primitives.StringValues formValues)
        {
            try
            {
                if (!formValues.Any() || formValues.All(string.IsNullOrWhiteSpace))
                {
                    return "";
                }

                // 过滤空值并转换为管道符格式
                var validValues = formValues.Where(v => !string.IsNullOrWhiteSpace(v)).ToArray();
                if (validValues.Length == 0)
                {
                    return "";
                }

                return "|" + string.Join("|", validValues) + "|";
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "转换数组为管道符格式时发生错误");
                return "";
            }
        }

        /// <summary>
        /// 从ProductsValue字段解析产品信息（用于推荐集）
        /// </summary>
        /// <param name="productsValue">ProductsValue字段的值，格式如"|48|47|"</param>
        /// <returns>产品信息列表</returns>
        private async Task<List<object>> ParseProductsFromProductsValue(string productsValue)
        {
            var result = new List<object>();

            try
            {
                if (string.IsNullOrEmpty(productsValue))
                {
                    return result;
                }

                // 解析ProductsValue字段，格式如"|48|47|"
                var productIds = productsValue.Split('|', StringSplitOptions.RemoveEmptyEntries)
                    .Select(s => int.TryParse(s, out int id) ? id : 0)
                    .Where(id => id > 0)
                    .ToList();

                if (!productIds.Any())
                {
                    return result;
                }

                // 查询产品详细信息
                var products = await GetProductsByIds(productIds);

                // 构建前端期望的数据结构
                foreach (var product in products)
                {
                    var imagePath = !string.IsNullOrEmpty(product.PicPath_0)
                        ? product.PicPath_0
                        : "";

                    var productName = !string.IsNullOrEmpty(product.Name_en)
                        ? product.Name_en
                        : $"产品 {product.ProId}";

                    // 构建产品URL
                    string productUrl = !string.IsNullOrEmpty(product.PageUrl)
                        ? $"/products/{product.PageUrl}"
                        : $"/products/{product.ProId}";

                    // 按照示例格式构建数据结构
                    result.Add(new
                    {
                        Url = productUrl,
                        Name = productName,
                        PicPath = imagePath
                    });
                }

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "解析ProductsValue字段时发生错误");
                return result;
            }
        }

        /// <summary>
        /// 解析Data字段中的产品信息
        /// </summary>
        /// <param name="data">JSON数据</param>
        /// <returns>产品信息字典</returns>
        private Dictionary<string, object> ParseProductsFromData(string data)
        {
            var result = new Dictionary<string, object>();

            try
            {
                if (string.IsNullOrEmpty(data))
                {
                    return result;
                }

                var dataObj = JsonConvert.DeserializeObject<dynamic>(data);

                if (dataObj?.Products != null)
                {
                    int index = 0;
                    foreach (var product in dataObj.Products)
                    {
                        string productId = product.id?.ToString() ?? "";
                        string productName = product.name?.ToString() ?? "未知产品";
                        string productImage = product.image?.ToString() ?? "";
                        string productPrice = product.price?.ToString() ?? "0";

                        // 构建产品URL
                        string productUrl = !string.IsNullOrEmpty(productId)
                            ? $"/products/{productId}"
                            : "#";

                        // 前端期望的数据结构，使用索引作为key
                        result[$"product_{index}"] = new
                        {
                            Url = productUrl,
                            PicPath = productImage,
                            Name = productName,
                            Price = productPrice,
                            Id = productId
                        };
                        index++;
                    }
                }

                return result;
            }
            catch
            {
                return result;
            }
        }

        /// <summary>
        /// 根据产品ID列表获取产品基本信息
        /// </summary>
        /// <param name="productIds">产品ID列表</param>
        /// <returns>产品信息列表</returns>
        private async Task<List<products>> GetProductsByIds(List<int> productIds)
        {
            try
            {
                return await _productService.Query(p => productIds.Contains(p.ProId));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "根据产品ID获取产品信息时发生错误");
                return new List<products>();
            }
        }
    }

    /// <summary>
    /// 获取推荐产品信息请求模型
    /// </summary>
    public class GetRecommendProductsInfoRequest
    {
        public int RId { get; set; }
    }
}