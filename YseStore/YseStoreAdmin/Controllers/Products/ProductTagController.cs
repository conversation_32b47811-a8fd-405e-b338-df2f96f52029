using Microsoft.AspNetCore.Mvc;
using YseStore.IService.Products;
using System.Text.Json;
using Entitys;

namespace YseStoreAdmin.Controllers.Products
{
    /// <summary>
    /// 产品标签控制器
    /// </summary>
    [Route("manage")]
    [ApiController]
    public class ProductTagController : ControllerBase
    {
        private readonly IProductService _productService;
        private readonly ILogger<ProductTagController> _logger;

        public ProductTagController(IProductService productService, ILogger<ProductTagController> logger)
        {
            _productService = productService;
            _logger = logger;
        }

        // 创建一个用于解析标签更新请求的类
        public class TagUpdateRequest
        {
            public List<string> insertCurrent { get; set; }
            public List<string> insertOption { get; set; }
            public List<string> insertName { get; set; }
            public string PId { get; set; }
            public string Type { get; set; }
            public string do_action { get; set; }
            public string Option { get; set; } // 单一标签值，兼容简单情况
        }

        /// <summary>
        /// 获取产品标签列表
        /// </summary>
        /// <param name="Type">标签类型，默认为"tags"</param>
        /// <returns>标签列表HTML</returns>
        [HttpGet("products/products/tags")]
        public async Task<IActionResult> GetTags(string Type = "tags")
        {
            try
            {
                // 获取所有产品标签
                var allTags = await _productService.GetAllProductTags();

                // 过滤出通用标签（ProId=0）
                var commonTags = allTags.Where(t => t.ProId == 0).OrderBy(t => t.MyOrder).ToList();

                // 构建HTML响应
                var html = BuildTagsHtml(commonTags, Type);

                return Ok(new { ret = 1, msg = html });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取产品标签列表时出错");
                return Ok(new { ret = 0, msg = "获取标签列表失败" });
            }
        }

        /// <summary>
        /// 构建标签HTML
        /// </summary>
        /// <param name="tags">标签列表</param>
        /// <param name="type">标签类型</param>
        /// <returns>HTML字符串</returns>
        private string BuildTagsHtml(List<products_tags> tags, string type)
        {
            var html = new System.Text.StringBuilder();

            // 构建选项已选择区域
            html.Append("<div class=\"option_selected\">");
            html.Append("<div class=\"select_list\"></div>");
            html.Append(
                "<input type=\"text\" class=\"box_input\" name=\"_Option\" value=\"\" size=\"30\" maxlength=\"255\" />");
            html.Append("<span class=\"placeholder insert_delete\" data-" + type +
                        "=\"填写好内容，按回车键或者输入英文逗号保存\">填写好内容，按回车键或者输入英文逗号保存</span>");
            html.Append("</div>");

            // 构建选项未选择区域
            html.Append("<div class=\"option_not_yet\">");
            html.Append("<div class=\"select_list\" data-type=\"" + type + "\">");

            // 添加现有标签选项
            foreach (var tag in tags)
            {
                html.Append("<span class=\"btn_attr_choice\" data-type=\"" + type + "\">");
                html.Append("<b>" + tag.Name_en + "</b>");
                html.Append("<input type=\"checkbox\" name=\"" + type + "Current[]\" value=\"" + tag.TId +
                            "\" class=\"option_current\" />");
                html.Append("<input type=\"hidden\" name=\"" + type + "Option[]\" value=\"" + tag.TId + "\" />");
                html.Append("<input type=\"hidden\" name=\"" + type + "Name[]\" value=\"" + tag.Name_en + "\" />");
                html.Append("<i></i>");
                html.Append("</span>");
            }

            html.Append("</div>");
            html.Append("</div>");

            // 构建选项按钮区域
            html.Append("<div class=\"option_button\">");
            html.Append("<a href=\"javascript:;\" class=\"select_all\">全选</a>");
            html.Append("<div class=\"option_button_menu\" style=\"display:none;\">");
            html.Append("<a href=\"javascript:;\" data-type=\"" + type + "\">标签</a>");
            html.Append("</div>");
            html.Append("</div>");

            // 添加最大数量限制（0表示无限制）
            html.Append("<input type=\"hidden\" class=\"option_max_number\" value=\"0\" />");

            return html.ToString();
        }

        /// <summary>
        /// 获取可删除的标签列表
        /// </summary>
        /// <returns>可删除标签列表HTML</returns>
        [HttpGet("products/products/tags-del-list")]
        public async Task<IActionResult> GetTagsDelList()
        {
            try
            {
                // 获取所有产品标签
                var allTags = await _productService.GetAllProductTags();

                // 过滤出通用标签（ProId=0）
                var commonTags = allTags.Where(t => t.ProId == 0).OrderBy(t => t.MyOrder).ToList();

                // 构建HTML响应
                var html = new System.Text.StringBuilder();

                // 简单的checkbox列表
                foreach (var tag in commonTags)
                {
                    html.Append(
                        $"<span class=\"input_checkbox_box\"><span class=\"input_checkbox\"><input type=\"checkbox\" name=\"TId[]\" value=\"{tag.TId}\"></span>{tag.Name_en}</span>");
                }

                return Ok(new { ret = 1, msg = html.ToString() });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取可删除标签列表时出错");
                return Ok(new { ret = 0, msg = "获取可删除标签列表失败" });
            }
        }

        /// <summary>
        /// 添加或更新标签
        /// </summary>
        /// <returns>操作结果</returns>
        [HttpPost("products/products/tags-update")]
        [Consumes("application/x-www-form-urlencoded", "multipart/form-data", "application/json")]
        public async Task<IActionResult> UpdateTags()
        {
            try
            {
                // 用于保存标签值的列表
                List<string> tagNames = new List<string>();
                string type = "";
                string productIds = ""; // 存储产品ID列表

                // 检查请求内容类型，获取参数
                if (Request.ContentType != null && Request.ContentType.Contains("application/json"))
                {
                    // 尝试从JSON请求体中读取数据
                    using var reader = new System.IO.StreamReader(Request.Body);
                    string requestBody = await reader.ReadToEndAsync();

                    try
                    {
                        // 尝试解析为复杂对象
                        var jsonData = JsonSerializer.Deserialize<TagUpdateRequest>(requestBody,
                            new JsonSerializerOptions
                            {
                                PropertyNameCaseInsensitive = true
                            });

                        if (jsonData != null)
                        {
                            // 设置标签类型
                            type = jsonData.Type;
                            // 获取产品ID
                            productIds = jsonData.PId;

                            // 处理多种可能的标签值来源
                            if (jsonData.insertName != null && jsonData.insertName.Any())
                            {
                                // 使用insertName数组
                                tagNames.AddRange(jsonData.insertName.Where(n => !string.IsNullOrWhiteSpace(n)));
                            }
                            else if (!string.IsNullOrWhiteSpace(jsonData.Option))
                            {
                                // 使用Option字段
                                tagNames.AddRange(jsonData.Option
                                    .Split(new[] { ',' }, StringSplitOptions.RemoveEmptyEntries)
                                    .Select(t => t.Trim())
                                    .Where(t => !string.IsNullOrEmpty(t)));
                            }
                        }
                    }
                    catch (JsonException ex)
                    {
                        _logger.LogError(ex, "解析JSON请求体时出错");
                        return Ok(new { ret = 0, msg = "解析请求数据时出错" });
                    }
                }
                else if (Request.HasFormContentType)
                {
                    // 从表单中获取参数
                    type = Request.Form["Type"].ToString();
                    productIds = Request.Form["PId"].ToString();
                    var options = Request.Form["Option"].ToString();

                    // 分割标签值
                    tagNames.AddRange(options.Split(new[] { ',' }, StringSplitOptions.RemoveEmptyEntries)
                        .Select(t => t.Trim())
                        .Where(t => !string.IsNullOrEmpty(t)));
                }
                else
                {
                    // 不支持的内容类型
                    return Ok(new { ret = 0, msg = "不支持的请求格式" });
                }

                if (tagNames.Count == 0)
                {
                    return Ok(new { ret = 0, msg = "标签名称不能为空" });
                }

                // 获取现有标签
                var existingTags = await _productService.GetAllProductTags();
                var existingTagNames = existingTags.Where(t => t.ProId == 0)
                    .Select(t => t.Name_en.ToLower())
                    .ToHashSet();

                // 准备需要添加的标签
                var tagsToAdd = new List<products_tags>();
                foreach (var tagName in tagNames)
                {
                    // 如果标签已存在，则跳过添加通用标签
                    if (existingTagNames.Contains(tagName.ToLower()))
                    {
                        continue;
                    }

                    // 添加新标签(ProId=0表示通用标签)
                    tagsToAdd.Add(new products_tags
                    {
                        ProId = 0, // 通用标签
                        Name_en = tagName,
                        MyOrder = 0,
                        Source = "manual",
                        SourceId = "0"
                    });
                }

                // 如果有通用标签要添加，先添加它们
                int insertCount = 0;
                if (tagsToAdd.Count > 0)
                {
                    try
                    {
                        // 使用SqlSugar批量插入标签
                        insertCount = await _productService.AddProductTags(tagsToAdd);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "添加通用标签时出错");
                        return Ok(new { ret = 0, msg = $"添加标签失败: {ex.Message}" });
                    }
                }

                // 如果有产品ID，则更新产品的标签关联
                if (!string.IsNullOrEmpty(productIds))
                {
                    try
                    {
                        // 分割产品ID
                        var productIdList = productIds
                            .Split(new[] { '-', ',', '|' }, StringSplitOptions.RemoveEmptyEntries)
                            .Select(id => id.Trim())
                            .Where(id => !string.IsNullOrEmpty(id) && int.TryParse(id, out _))
                            .ToList();

                        if (productIdList.Count > 0)
                        {
                            // 重新获取所有标签（包含可能刚添加的标签）
                            existingTags = await _productService.GetAllProductTags();

                            // 获取标签名称对应的标签ID
                            var tagIdsToAssociate = new List<int>();
                            foreach (var tagName in tagNames)
                            {
                                var matchingTag = existingTags.FirstOrDefault(t =>
                                    t.Name_en.Equals(tagName, StringComparison.OrdinalIgnoreCase) && t.ProId == 0);

                                if (matchingTag != null)
                                {
                                    tagIdsToAssociate.Add(matchingTag.TId);
                                }
                            }

                            if (tagIdsToAssociate.Count > 0)
                            {
                                // 构建标签字符串 "|1|2|3|" 格式
                                var tagString = "|" + string.Join("|", tagIdsToAssociate) + "|";

                                // 更新每个产品的Tags字段
                                int updatedProductCount = 0;
                                foreach (var productId in productIdList)
                                {
                                    if (int.TryParse(productId, out int productIdInt))
                                    {
                                        var success = await _productService.UpdateProductTags(productIdInt, tagString);
                                        if (success)
                                        {
                                            updatedProductCount++;
                                        }
                                    }
                                }

                                return Ok(new
                                {
                                    ret = 1,
                                    msg = $"已添加{insertCount}个新标签，已更新{updatedProductCount}个产品的标签关联"
                                });
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "更新产品标签关联时出错");
                        return Ok(new { ret = 0, msg = $"更新产品标签关联失败: {ex.Message}" });
                    }
                }

                return Ok(new { ret = 1, msg = $"成功添加{insertCount}个标签" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "处理添加标签请求时出错");
                return Ok(new { ret = 0, msg = $"添加标签失败: {ex.Message}" });
            }
        }

        /// <summary>
        /// 删除产品标签
        /// </summary>
        /// <returns>操作结果</returns>
        [HttpPost("products/products/tags-del")]
        [Consumes("application/x-www-form-urlencoded", "multipart/form-data", "application/json")]
        public async Task<IActionResult> DeleteTags()
        {
            try
            {
                // 用于存储标签ID的列表
                List<int> tagIds = new List<int>();
                string productIds = ""; // 存储产品ID列表

                // 检查请求内容类型，获取参数
                if (Request.ContentType != null && Request.ContentType.Contains("application/json"))
                {
                    // 从JSON请求体中读取数据
                    using var reader = new StreamReader(Request.Body);
                    string requestBody = await reader.ReadToEndAsync();

                    try
                    {
                        // 尝试解析为对象
                        var jsonData = JsonSerializer.Deserialize<Dictionary<string, object>>(requestBody,
                            new JsonSerializerOptions
                            {
                                PropertyNameCaseInsensitive = true
                            });

                        if (jsonData != null)
                        {
                            // 获取产品ID
                            if (jsonData.TryGetValue("PId", out var pidObj))
                            {
                                productIds = pidObj.ToString();
                            }

                            // 获取标签ID列表
                            if (jsonData.TryGetValue("TId", out var tagIdsObj) &&
                                tagIdsObj is JsonElement tagIdsElement)
                            {
                                if (tagIdsElement.ValueKind == JsonValueKind.Array)
                                {
                                    foreach (var element in tagIdsElement.EnumerateArray())
                                    {
                                        if (int.TryParse(element.ToString(), out int tagId))
                                        {
                                            tagIds.Add(tagId);
                                        }
                                    }
                                }
                                else if (tagIdsElement.ValueKind == JsonValueKind.String)
                                {
                                    // 处理逗号分隔的标签ID字符串
                                    var tagIdStrs = tagIdsElement.GetString().Split(',');
                                    foreach (var tagIdStr in tagIdStrs)
                                    {
                                        if (int.TryParse(tagIdStr.Trim(), out int tagId))
                                        {
                                            tagIds.Add(tagId);
                                        }
                                    }
                                }
                            }
                        }
                    }
                    catch (JsonException ex)
                    {
                        _logger.LogError(ex, "解析JSON请求体时出错");
                        return Ok(new { ret = 0, msg = "解析请求数据时出错" });
                    }
                }
                else if (Request.HasFormContentType)
                {
                    // 从表单中获取参数
                    productIds = Request.Form["PId"].ToString();
                    var tagIdArray = Request.Form["TId[]"];

                    // 解析标签ID
                    foreach (var tagIdStr in tagIdArray)
                    {
                        if (int.TryParse(tagIdStr, out int tagId))
                        {
                            tagIds.Add(tagId);
                        }
                    }
                }
                else
                {
                    // 不支持的内容类型
                    return Ok(new { ret = 0, msg = "不支持的请求格式" });
                }

                if (tagIds.Count == 0)
                {
                    return Ok(new { ret = 0, msg = "请选择要删除的标签" });
                }

                // 分割产品ID，使用与UpdateTags相同的方式
                var productIdList = new List<int>();
                if (!string.IsNullOrEmpty(productIds))
                {
                    var productIdStrs =
                        productIds.Split(new[] { '-', ',', '|' }, StringSplitOptions.RemoveEmptyEntries);
                    foreach (var idStr in productIdStrs)
                    {
                        if (int.TryParse(idStr.Trim(), out int productId))
                        {
                            productIdList.Add(productId);
                        }
                    }
                }

                if (productIdList.Count == 0)
                {
                    return Ok(new { ret = 0, msg = "未找到有效的产品ID" });
                }

                // 处理每个产品
                int updatedCount = 0;

                foreach (var productId in productIdList)
                {
                    try
                    {
                        // 1. 获取产品详情，包含Tags字段
                        var product = await _productService.QueryById(productId);
                        if (product == null || string.IsNullOrEmpty(product.Tags))
                        {
                            continue; // 跳过不存在或没有标签的产品
                        }

                        // 2. 解析当前的标签ID
                        var currentTagIds = new HashSet<int>();
                        var tagParts = product.Tags.Trim('|').Split('|', StringSplitOptions.RemoveEmptyEntries);
                        foreach (var part in tagParts)
                        {
                            if (int.TryParse(part, out int id))
                            {
                                currentTagIds.Add(id);
                            }
                        }

                        // 3. 移除要删除的标签ID
                        bool hasChanges = false;
                        foreach (var tagId in tagIds)
                        {
                            if (currentTagIds.Remove(tagId))
                            {
                                hasChanges = true;
                            }
                        }

                        // 4. 如果有变化，更新产品的Tags字段
                        if (hasChanges)
                        {
                            // 构建新的Tags字符串
                            string newTagsString = currentTagIds.Count > 0
                                ? "|" + string.Join("|", currentTagIds) + "|"
                                : string.Empty;

                            // 更新产品的Tags字段
                            var success = await _productService.UpdateProductTags(productId, newTagsString);
                            if (success)
                            {
                                updatedCount++;
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, $"处理产品ID {productId} 时出错");
                        // 继续处理下一个产品
                    }
                }

                if (updatedCount > 0)
                {
                    return Ok(new { ret = 1, msg = $"已从{updatedCount}个产品中删除选中的标签" });
                }
                else
                {
                    return Ok(new { ret = 0, msg = "没有产品的标签被更新" });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "处理删除标签请求时出错");
                return Ok(new { ret = 0, msg = $"删除标签失败: {ex.Message}" });
            }
        }

        /// <summary>
        /// 批量编辑产品价格
        /// </summary>
        [HttpPost("products/products/edit-batch-price")]
        public async Task<IActionResult> EditBatchPrice()
        {
            try
            {
                // 1. 从表单中获取产品价格调整数据
                var priceAdjustments = new Dictionary<int, decimal>();
                var oldPriceAdjustments = new Dictionary<int, decimal>();

                // 解析表单中的 Price[_XXX] 和 OldPrice[_XXX] 字段
                foreach (var key in Request.Form.Keys)
                {
                    if (key.StartsWith("Price[_") && key.EndsWith("]"))
                    {
                        // 提取产品ID和价格值
                        var idStr = key.Substring(7, key.Length - 8);
                        if (int.TryParse(idStr, out int productId) &&
                            decimal.TryParse(Request.Form[key], out decimal price))
                        {
                            priceAdjustments[productId] = price;
                        }
                    }
                    else if (key.StartsWith("OldPrice[_") && key.EndsWith("]"))
                    {
                        // 提取产品ID和原价值
                        var idStr = key.Substring(10, key.Length - 11);
                        if (int.TryParse(idStr, out int productId) &&
                            decimal.TryParse(Request.Form[key], out decimal oldPrice))
                        {
                            oldPriceAdjustments[productId] = oldPrice;
                        }
                    }
                }

                // 2. 调用服务层方法执行批量更新
                bool success = await _productService.UpdateProductPricesAsync(priceAdjustments, oldPriceAdjustments);

                // 3. 返回结果
                if (success)
                {
                    return Ok(new { ret = 1, msg = "批量更新产品价格成功" });
                }
                else
                {
                    return Ok(new { ret = 0, msg = "批量更新产品价格失败" });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "批量更新产品价格时出错");
                return Ok(new { ret = 0, msg = $"批量更新产品价格时出错: {ex.Message}" });
            }
        }
    }
}