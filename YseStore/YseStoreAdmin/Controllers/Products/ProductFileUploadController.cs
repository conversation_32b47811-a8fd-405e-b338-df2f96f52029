using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Serilog.Sinks.File;
using YseStore.IService;
using YseStore.Model.VM;

namespace YseStoreAdmin.Controllers
{

    [ApiController]
    [Route("api/[controller]")]
    public class ProductFileUploadController : ControllerBase
    {
        private readonly ILogger<ProductFileUploadController> _logger;
        private readonly ISettingBasisService _settingBasisService;
        private readonly IToolService _toolService;
        public ProductFileUploadController(ILogger<ProductFileUploadController> logger, ISettingBasisService settingBasisService, IToolService toolService)
        {
            _logger = logger;
            _settingBasisService = settingBasisService;
            _toolService = toolService;
        }

        /// <summary>
        /// 产品管理 上传图片
        /// </summary>
        /// <param name="Filedata"></param>
        /// <returns></returns>
        [HttpPost("uploadImages")]
        public async Task<IActionResult> UploadImages([FromForm] List<IFormFile> Filedata)
        {
            try
            {
                var filesStorageOptions = await _settingBasisService.GetFilesStorageOptions();
                //初始化上传参数
                var maxSize = 1024 * 1024 * filesStorageOptions.MaxSize; //上传大小20M
                if (Filedata == null || Filedata.Count == 0)
                    return BadRequest("未选择任何文件");
                var file = Filedata[0];
                var fileName = file.FileName;
                var fileExt = Path.GetExtension(fileName).ToLowerInvariant();
                //检查大小
                if (file.Length > maxSize)
                {
                    var msg = "上传文件大小超过限制，最大允许上传" + filesStorageOptions.MaxSize + "M";
                    return BadRequest(msg);
                }
                //检查文件扩展名
                if (string.IsNullOrEmpty(fileExt) ||
                    Array.IndexOf(filesStorageOptions.FileTypes.Split(','), fileExt.Substring(1).ToLower()) == -1)
                {
                    var msg = "上传文件扩展名是不允许的扩展名,请上传后缀名为：" + filesStorageOptions.FileTypes;
                    return BadRequest(msg);
                }
                var fileObj = new VM_UploadObject();
                if (filesStorageOptions.StorageType == Consts.FilesStorageType_LocalStorage)
                {
                    fileObj = await _toolService.UpLoadFileForLocalStorage(filesStorageOptions, fileExt, file);
                }
                else if (filesStorageOptions.StorageType == Consts.FilesStorageType_AliYunOSS)
                {
                    fileObj = await _toolService.UpLoadFileForAliYunOSS(filesStorageOptions, fileExt, file);
                }
                // 返回结果 - 同时兼容新旧两种格式
                return Ok(new { 
                    filePath = fileObj.filePath,
                    name = fileObj.oriname,
                    surplus = fileObj.surplus,
                    files = new[] {
                        new {
                            name = Path.GetFileNameWithoutExtension(file.FileName),
                            fileName = Path.GetFileName(file.FileName),
                            size = file.Length,
                            type = file.ContentType,
                            url = fileObj.filePath,
                            thumbnailUrl = fileObj.filePath,
                            deleteUrl = "",
                            deleteType = "DELETE"
                        }
                    }
                });
            

            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "服务器错误", error = ex.Message });
            }
        }

        /// <summary>
        /// 上传文件
        /// </summary>
        /// <param name="Filedata"></param>
        /// <returns></returns>
        [HttpPost("uploadFiles")]
        public async Task<IActionResult> UploadFiles([FromForm] List<IFormFile> Filedata)
        {
            try
            {
                if (Filedata == null || Filedata.Count == 0)
                    return BadRequest("未选择任何文件");

                var file = Filedata[0];

                // 验证文件类型（扩展支持类型）
                var allowedMimeTypes = new HashSet<string>(StringComparer.OrdinalIgnoreCase)
        {
            // 文档类型
            "application/pdf",
            "application/msword",          // .doc
            "application/vnd.openxmlformats-officedocument.wordprocessingml.document", // .docx
            "application/vnd.ms-excel",    // .xls
            "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",      // .xlsx
            "application/vnd.ms-powerpoint", // .ppt
            "application/vnd.openxmlformats-officedocument.presentationml.presentation", // .pptx
            "text/plain",                  // .txt
            "text/csv",                    // .csv
            
            // 压缩文件
            "application/zip",
            "application/x-zip-compressed",
            "application/rar",
            "application/x-rar",
            "application/octet-stream",    // 通用二进制流
            
            // 视频类型
            "video/mp4"
        };

                if (!allowedMimeTypes.Contains(file.ContentType))
                    return BadRequest("不支持的文件类型");

                // 验证文件大小（保持10MB限制）
                if (file.Length > 10 * 1024 * 1024)
                    return BadRequest("文件大小不能超过10MB");

                // 生成存储路径
                var uploadFolder = Path.Combine(
					"wwwroot",
					"u_file",
                    DateTime.Now.ToString("yyyyMM"),
                    DateTime.Now.ToString("dd"),
                    "product_file"  // 原为"photo"，现改为通用文件目录
                );

                // 创建目录（如果不存在）
                Directory.CreateDirectory(uploadFolder);

                // 生成唯一文件名（保留扩展名）
                var fileExtension = Path.GetExtension(file.FileName).ToLower();
                var sanitizedFileName = $"{Guid.NewGuid().ToString("N")}{fileExtension}";
                var filePath = Path.Combine(uploadFolder, sanitizedFileName);

                // 保存文件
                using (var stream = new FileStream(filePath, FileMode.Create))
                {
                    await file.CopyToAsync(stream);
                }

                // 构建 URL - 简化路径处理
                string relativePath = "/u_file/" + 
                    DateTime.Now.ToString("yyyyMM") + "/" +
                    DateTime.Now.ToString("dd") + "/product_file/" +
                    sanitizedFileName;
                    
                return Ok(new
                {
                    filePath = relativePath,
                    name = sanitizedFileName,
                    surplus = "",
                    files = new[] {
                        new {
                            name = Path.GetFileNameWithoutExtension(file.FileName),
                            fileName = Path.GetFileName(file.FileName),
                            size = file.Length,
                            type = file.ContentType,
                            url = relativePath,
                            thumbnailUrl = relativePath,
                            deleteUrl = "",
                            deleteType = "DELETE"
                        }
                    }
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "服务器错误", error = ex.Message });
            }
        }
        
        /// <summary>
        /// 获取文件封面
        /// </summary>
        /// <param name="path">文件路径</param>
        /// <returns></returns>
        [HttpPost("getFileCover")]
        public IActionResult GetFileCover([FromForm] string path)
        {
            try
            {
                if (string.IsNullOrEmpty(path))
                    return BadRequest("文件路径不能为空");
                
                // 通常对视频文件，需要返回一个缩略图或第一帧作为封面
                // 这里我们假设视频文件路径已经正确，直接返回
                
                return Ok(new { 
                    ret = 1, 
                    msg = path  // 直接返回原路径，实际应用中可能需要生成缩略图
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取文件封面失败");
                return StatusCode(500, new { ret = 0, msg = "服务器错误" });
            }
        }
        
        /// <summary>
        /// 保存文件封面
        /// </summary>
        /// <param name="path">文件路径</param>
        /// <returns></returns>
        [HttpPost("saveFileCover")]
        public IActionResult SaveFileCover([FromForm] string path)
        {
            try
            {
                if (string.IsNullOrEmpty(path))
                    return BadRequest("文件路径不能为空");
                
                // 实际应用中可能需要将文件封面信息保存到数据库
                // 这里简单返回成功
                
                return Ok(new { 
                    ret = 1, 
                    msg = "封面保存成功"
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "保存文件封面失败");
                return StatusCode(500, new { ret = 0, msg = "服务器错误" });
            }
        }

        /// <summary>
        /// 检查文件是否存在
        /// </summary>
        /// <param name="File">文件路径</param>
        /// <returns></returns>
        [HttpGet("has-file")]
        public IActionResult HasFile([FromQuery] string File)
        {
            try
            {
                if (string.IsNullOrEmpty(File))
                    return Ok(new { ret = 0, msg = "文件路径不能为空" });
                
                // 检查文件是否存在于服务器
                string filePath = Path.Combine(Directory.GetCurrentDirectory(), "wwwroot", File.TrimStart('/'));
                bool fileExists = System.IO.File.Exists(filePath);
                
                if (fileExists)
                {
                    return Ok(new { ret = 1, msg = "文件存在" });
                }
                else
                {
                    return Ok(new { ret = 0, msg = "文件不存在" });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "检查文件是否存在时发生错误");
                return StatusCode(500, new { ret = 0, msg = "服务器错误" });
            }
        }
    }
    
}
