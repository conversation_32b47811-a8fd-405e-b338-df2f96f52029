using Castle.Components.DictionaryAdapter;
using Entitys;
using MailKit.Search;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Options;
using MiniExcelLibs.Utils;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using OfficeOpenXml.FormulaParsing.Excel.Functions.Text;
using OfficeOpenXml.Style;
using Org.BouncyCastle.Asn1.Pkcs;
using Org.BouncyCastle.Utilities;
using SqlSugar;
using StackExchange.Redis;
using System.Drawing;
using System.Dynamic;
using System.Globalization;
using System.Reflection.Emit;
using System.Runtime.InteropServices;
using System.Security.AccessControl;
using System.Text;
using System.Text.Json;
using System.Text.RegularExpressions;
using YseStore.Common;
using YseStore.IService;
using YseStore.IService.Customer;
using YseStore.IService.Order;
using YseStore.Model;
using YseStore.Model.Enums;
using YseStore.Model.Event;
using YseStore.Model.Response;
using YseStore.Service.Customer;
using YseStore.Service.HelpsOrder;
using YseStore.Service.Order;
using YseStore.Service.Products;
using YseStoreAdmin.Pages.Components.Orders;
using YseStoreAdmin.Pages.Components.Products;
using YseStoreAdmin.Pages.Components.Setting;
using YseStoreAdmin.Pages.Customer;
using YseStoreAdmin.Pages.Mail.Template;
using YseStoreAdmin.Pages.Setting;
using ZXing;
using System.Linq;

using static YseStoreAdmin.Pages.Components.Orders.OrderCreate;
using static YseStore.Service.Order.OrderListService;

using YseStore.Model.VM.Country;
using System.Threading.Channels;
using YseStore.IService.HelpsOrder;
using YseStore.Common.Helper;
using YseStore.IService.Sales;
using Org.BouncyCastle.Ocsp;
using YseStore.Model.VM.Setting;
using YseStore.Service;
using YseStore.Common.Cache;
using YseStore.IService.Email;
using Aop.Api.Domain;



namespace YseStoreAdmin.Controllers.Order
{
    [ApiController]
    [Route("api/[controller]")]
    public class OrderListController : ControllerBase
    {
        private readonly IOrderListService _orderListService;
        private readonly ILogger<CodeEditController> _logger;
        private readonly IHttpContextAccessor _httpContextAccessor;
        private readonly IAppConfigService _configService;
        private readonly ICountryService _countryService;
        public ISettingBasisService _settingBasisService;
        public IHelpsCartService _helpsCartService;
        public IHelpsUserService _helpsUserService;
        public IHelpsManageService _helpsManageService;
        public IHelpsProductsService _helpsProductsService;
        private readonly ISalesCouponService _salesCouponService;
        private readonly IToolService _toolService;

        private readonly ISystemEmailTplServices _emailTplService;
        private readonly ICaching _caching;


        public OrderListController(IOrderListService orderListService, ILogger<CodeEditController> logger,
            IHttpContextAccessor httpContextAccessor, IAppConfigService configService, ICountryService countryService,
            ISettingBasisService settingBasisService, IHelpsCartService helpsCartService, IHelpsUserService helpsUserService,
            IHelpsManageService helpsManageService, IHelpsProductsService helpsProductsService, ISalesCouponService salesCouponService, 
            IToolService toolService,
            ISystemEmailTplServices systemEmailTplServices, ICaching caching)
        {
            _orderListService = orderListService;
            _logger = logger;
            _httpContextAccessor = httpContextAccessor;
            _configService = configService;
            _countryService = countryService;
            _settingBasisService = settingBasisService;
            _helpsCartService = helpsCartService;
            _helpsUserService = helpsUserService;
            _helpsManageService = helpsManageService;
            _helpsProductsService = helpsProductsService;
            _salesCouponService = salesCouponService;
            _toolService = toolService;
            _emailTplService = systemEmailTplServices;
            _caching = caching;
        }
        [HttpPost("OrdersGetOrderUserInfo")]
        public async Task<IActionResult> OrdersGetOrderUserInfo([FromForm] int UserId)
        {
            try
            {
                var user = await _orderListService.OrdersGetOrderUserInfo(UserId);
                var orderslist = await _orderListService.GetOrderByUserId(UserId);
                var user_label = await _orderListService.Getuser_label_collectionByUserId(UserId);
                //var two = "";
                //var three = "";
                //if (Convert.ToBoolean(user.IsRegistered))
                //{
                //    two = "直接访问";
                //}
                //else
                //{
                //    two = "游客";
                //}
                //if (Convert.ToBoolean(user.IsNewsletter))
                //{
                //    three = "已订阅";
                //}
                //else
                //{
                //    three = "未订阅";
                //}
                int zero = 0;
                int one = 0;
                if (orderslist != null)
                {
                    zero = orderslist.Count;
                    one = orderslist.Where(p => p.OrderStatus == (int)OrderStatusEnum.已付款).ToList().Count;
                }
                StringBuilder content = new StringBuilder();
                content.AppendFormat(@"<div class='user_info_title'>共{0}个订单，{1}次付款</div>
                <div class='user_tags clean'>", zero, one);
                if (user.Locked)
                {
                    content.AppendFormat(@"<div class='non_user'>{0}</div>", "已屏蔽");
                }
                if (!user.IsRegistered.Value)
                {
                    content.AppendFormat(@"<div class='non_user'>{0}</div>", "游客");
                }
                if (Convert.ToBoolean(user.Status))
                {
                    content.AppendFormat(@"<div class='non_user'>{0}</div>", "待验证邮箱");
                }
                if (user.RefererId == 0)
                {
                    content.AppendFormat(@"<div class='non_user'>{0}</div>", "搜索引擎");
                }
                else if (user.RefererId == 1)
                {
                    content.AppendFormat(@"<div class='non_user'>{0}</div>", "分享平台");
                }
                else if (user.RefererId == 99)
                {
                    content.AppendFormat(@"<div class='non_user'>{0}</div>", "直接输入");
                }
                else if (user.RefererId == 100)
                {
                    content.AppendFormat(@"<div class='non_user'>{0}</div>", "其他");
                }

                //content.AppendFormat(@"<div class='non_user'>{0}</div>",  two);
                //content.AppendFormat(@"<div class='non_user'>{0}</div>", three);



                if (user_label != null)
                {
                    foreach (var item in user_label)
                    {
                        content.AppendFormat(@"
                            <div class='non_user'>{0}</div>
                        ", item.Value);
                    }
                }
                content.AppendFormat(@"</div>");



                return Ok(new { ret = true, msg = content.ToString() });

            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "根据发生未处理异常");
                return StatusCode(500, "服务器内部错误");
            }

        }

        [HttpGet("OrdersExportMenu")]
        public async Task<IActionResult> GetOrdersExportMenu()
        {
            try
            {

                var menuParams = HttpContext.Request.Query
                .Where(q => q.Key.StartsWith("Menu["))
                .ToDictionary(
                    q => q.Key.Substring("Menu[".Length).TrimEnd(']'),
                    q => q.Value.ToString()
                );

                var mergedConfig = OrderExportMenuHelper.AllMenuItems.ToDictionary(
                    kvp => kvp.Key,
                    kvp => menuParams.ContainsKey(kvp.Key) ? "1" : kvp.Value
                );
                string jsonConfig = JsonConvert.SerializeObject(mergedConfig);
                var b = await _orderListService.UpdetOrder_ExportAry(jsonConfig);
                return Ok(new { ret = true });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "根据发生未处理异常");
                return StatusCode(500, "服务器内部错误");
            }
        }
        [HttpPost("Orderdel")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> Orderdel()
        {
            try
            {
                var OId = Request.Form["OrderId"].ObjToInt();
                var b = await _orderListService.OrderdelByOId(OId);
                return Ok(new { ret = 1, msg = "" });

            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "根据发生未处理异常");
                return StatusCode(500, "服务器内部错误");
            }
        }
        [HttpPost("MessageOrdersView")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> MessageOrdersView()
        {
            try
            {
                var OId = Request.Form["OId"];
                var messageOrdersResponseData = await _orderListService.GetordersLogListByOId(OId);
                return Ok(new { ret = 1, msg = messageOrdersResponseData });

            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "根据发生未处理异常");
                return StatusCode(500, "服务器内部错误");
            }
        }
        [HttpPost("MessageOrdersReply")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> MessageOrdersReply()
        {
            try
            {

                var IsSendMail = Request.Form["IsSendMail"];//还没写
                var Message = Request.Form["Message"];
                var MsgPicPath = Request.Form["MsgPicPath"];
                var MsgVideoPath = Request.Form["MsgVideoPath"];
                var fileCover = Request.Form["fileCover"];
                var MId = Request.Form["MId"].ObjToInt();
                var resData = await _orderListService.AddordersLogList(Message, MsgPicPath, MsgVideoPath, MId);
                return Ok(new { ret = 1, msg = resData });

            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "根据发生未处理异常");
                return StatusCode(500, "服务器内部错误");
            }
        }
        [HttpGet("OrdersTrack")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> OrdersTrack(string number,string carrier)
        {
            try
            {

                //var number = Request.Form["number"];
                //var carrier = Request.Form["carrier"];
                if (string.IsNullOrWhiteSpace(number))
                {
                    return Ok(new { msg = "No tracking number provided", ret = 0 });
                }
                var result = await _orderListService.OrdersTrack(number, carrier);
                
                return Ok(new { ret = result.Item2, msg = result.Item1 });

            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "根据发生未处理异常");
                return StatusCode(500, "服务器内部错误");
            }
        }


        [HttpPost("GetOrderExplode")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> GetOrderExplode()
        {
            try
            {
                var filesStorageOptions = await _settingBasisService.GetFilesStorageOptions();
                var Type = Request.Form["Type"];
                var Email = Request.Form["Email"];
                var CurrencyUnit = Request.Form["CurrencyUnit"];
                var keyword = Request.Form["keyword"];
                var OrderStatus = Request.Form["OrderStatus"];
                var PaymentStatus = Request.Form["PaymentStatus"];
                var ShippingStatus = Request.Form["ShippingStatus"];
                var OrderTime = Request.Form["OrderTime"];
                var PaymentTime = Request.Form["PaymentTime"];
                var PId = Request.Form["PId"];
                var SId = Request.Form["SId"];
                var TId = Request.Form["TId"];
                var CreateType = Request.Form["CreateType"];
                var StoreSource = Request.Form["StoreSource"];

                var currentPageCount = Request.Form["currentPageCount"].ObjToInt();
                var currentPage = Request.Form["currentPage"].ObjToInt();
                var id_list = Request.Form["id_list"];
                var MenuSort = Request.Form["MenuSort"];

                //Type    "0"
                //Email   ""
                //CurrencyUnit    "manage"
                //keyword ""
                //OrderStatus ""
                //PaymentStatus   ""
                //ShippingStatus  ""
                //OrderTime   ""
                //PaymentTime ""
                //PId ""
                //SId ""
                //TId ""
                //CreateType  ""
                //StoreSource ""
                //currentPageCount    "25"
                //currentPage "1"
                //id_list "47-46"
                //do_action   "/manage/orders/orders/export"
                //exportService   "website"
                //Exclude[refunded]1
                //Exclude[shipped]1
                //Exclude[outOfStock]1
                //Exclude[preSale]1

                var refunded = Request.Form["Exclude[refunded]"];//已退款的产品
                var shipped = Request.Form["Exclude[shipped]"];//已发货的产品
                var outOfStock = Request.Form["Exclude[outOfStock]"];//库存<=0的产品
                var preSale = Request.Form["Exclude[preSale]"];//预售产品

                var OrderShippingStatus = await _orderListService.GetOrderExplode(Type, Email, CurrencyUnit, keyword, OrderStatus, PaymentStatus, ShippingStatus,
                    OrderTime, PaymentTime, PId, SId, TId, CreateType, StoreSource, currentPageCount, currentPage, id_list, refunded, shipped, outOfStock, preSale, MenuSort);
                var jsonString = await _orderListService.GetOrder_ExportMenuStr();
                // 生成保存路径
                var uploadFolder = Path.Combine(
                    "wwwroot",
                    "u_file",
                    DateTime.Now.ToString("yyyyMM"),
                    DateTime.Now.ToString("dd"),
                    "csv"
                );
                // 创建目录（如果不存在）
                Directory.CreateDirectory(uploadFolder);
                var random = new Random();
                string csvFileName = $"{DateTime.Now:yyyyMMddHHmmssfff}{random.Next(1000, 9999)}.csv";

                // 生成唯一文件名
                var sanitizedFileName = $"order_{csvFileName}";
                var filePath = Path.Combine(uploadFolder, sanitizedFileName);

                //OrderShippingStatus = testOrders();
               var dt=  _orderListService.ExportToCsvTwo(jsonString, OrderShippingStatus, filePath);
                var relativePath = "";

                if (filesStorageOptions.StorageType == Consts.FilesStorageType_LocalStorage)
                {
                    _orderListService.UpLoadFileForLocalStorage(dt, filePath);
                    relativePath = filePath
                        .Replace("wwwroot", "")
                        .Replace(Directory.GetCurrentDirectory(), "")
                        .Replace("\\", "/")
                        .TrimStart('/');
                    relativePath = $"wwwroot/{relativePath}";
                }
                else if (filesStorageOptions.StorageType == Consts.FilesStorageType_AliYunOSS)
                {
                    var result = await _toolService.ExportToOss(jsonString, dt, filesStorageOptions);
                    relativePath = result.filePath;
                }


               
                var tId = await _orderListService.SaveTemporaryStorageFile(csvFileName, relativePath);
                return Ok(new { ret = true, msg = new { name = csvFileName, count = OrderShippingStatus.Count, tid = tId } });

            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "根据发生未处理异常");
                return StatusCode(500, "服务器内部错误");
            }
        }
        #region
        //public List<OrderExplodeResponse> testOrders()
        //{
        //    return new List<OrderExplodeResponse>
        //        {
        //            new OrderExplodeResponse
        //            {
        //                OrderId = 27,
        //                PaymentStatus = "未付款",
        //                OId = "0506371",
        //                Email = "<EMAIL>",
        //                ShippingPrice = 8.05m,
        //                OrderSymbol = "$",
        //                OrderSum = 154.03m,
        //                TotalWeight = 2.5m,
        //                ShippingStatus = "未发货",
        //                OrderStatus = 1,
        //                OrderTime = new DateTime(2025, 2, 11, 14, 24, 0).ToString(),
        //                CouponPrice = 0m,
        //                TaxCost = 0m,
        //                Commission = 0m,
        //                DiscountPrice = 0m,
        //                Points = 0,
        //                ordersListExplodes = new List<ordersListExplode>
        //                {
        //                    new ordersListExplode
        //                    {
        //                        ShippingExpress = "配送信息",
        //                        TrackingNumber="运单号",
        //                         Qty = "2",
        //                        Status = "未发货",
        //                        Carrier = "物流商",
        //                        Name = "RA79 50-600MHz RX Aviation Band 5W Ham Radio (US Ver.)",
        //                       Price="33",
        //                        SKU = "US-A9267AX2-C9034AX2",
        //                        Property = "Bundle Sale: 2Packs",
        //                        PropertyPrice = "11"
        //                    },
        //                    new ordersListExplode
        //                    {
        //                        ShippingExpress = "配送信息1",
        //                        TrackingNumber="运单号1",
        //                         Qty = "3",
        //                        Status = "未发货1",
        //                        Carrier = "物流商1",
        //                        Name = "1RA79 50-600MHz RX Aviation Band 5W Ham Radio (US Ver.)",
        //                       Price="1",
        //                        SKU = "1US-A9267AX2-C9034AX2",
        //                        Property = "1Bundle Sale: 2Packs",
        //                        PropertyPrice = "1"
        //                    },
        //                    new ordersListExplode
        //                    {
        //                        ShippingExpress = "2配送信息",
        //                        TrackingNumber="2运单号",
        //                         Qty = "22",
        //                        Status = "2未发货",
        //                        Carrier = "2物流商",
        //                        Name = "2RA79 50-600MHz RX Aviation Band 5W Ham Radio (US Ver.)",
        //                       Price="2",
        //                        SKU = "2US-A9267AX2-C9034AX2",
        //                        Property = "2Bundle Sale: 2Packs",
        //                        PropertyPrice = "2"
        //                    }
        //                },
        //                PaymentMethod = "",
        //                PayTime = 1,
        //                PaymentId = "",
        //                ShippingFirstName = "CC",
        //                ShippingLastName = "adc",
        //                ShippingAddressLine1 = "22",
        //                ShippingAddressLine2 = "",
        //                ShippingCountry = "United States",
        //                ShippingState = "Alabama",
        //                ShippingCity = "22",
        //                ShippingZipCode = "91724",
        //                ShippingPhoneNumber = "1 12212111",
        //                BillFirstName = "CC",
        //                BillLastName = "adc",
        //                BillAddressLine1 = "22",
        //                BillAddressLine2 = "",
        //                BillCountry = "United States",
        //                BillState = "Alabama",
        //                BillCity = "22",
        //                BillZipCode = "91724",
        //                BillPhoneNumber = "1 12212111",
        //                ordersRemarkLog = "",
        //                userRemarkLog = "",
        //                CancelReason = "",
        //                ordersReason = ""
        //            },
        //            new OrderExplodeResponse
        //            {
        //                OrderId = 26,
        //                PaymentStatus = "已付款",
        //                OId = "5179749",
        //                Email = "<EMAIL>",
        //                ShippingPrice = 2.09m,
        //                OrderSymbol = "$",
        //                OrderSum = 76m,
        //                TotalWeight = 0.65m,
        //                ShippingStatus = "已发货",
        //                OrderStatus = 1,
        //                OrderTime = new DateTime(2025, 2, 11, 10, 43, 0).ToString(),
        //                CouponPrice = 0m,
        //                TaxCost = 0m,
        //                Commission = 2.93m,
        //                DiscountPrice = 0m,
        //                Points = 0,
        //                ordersListExplodes = new List<ordersListExplode>
        //                {
        //                    new ordersListExplode
        //                    {
        //                        ShippingExpress = "橙联",
        //                        TrackingNumber="#111",
        //                         Qty = "2",
        //                        Status = "未发货",
        //                        Carrier = "物流商",
        //                        Name = "RA79 50-600MHz RX Aviation Band 5W Ham Radio (US Ver.)",
        //                       Price="33",
        //                        SKU = "US-A9267AX2-C9034AX2",
        //                        Property = "Bundle Sale: 2Packs",
        //                        PropertyPrice = "11"
        //                    },new ordersListExplode
        //                    {
        //                        ShippingExpress = "橙联",
        //                        TrackingNumber="#1122",
        //                         Qty = "2",
        //                        Status = "未发货",
        //                        Carrier = "物流商",
        //                        Name = "6666RA79 50-600MHz RX Aviation Band 5W Ham Radio (US Ver.)",
        //                       Price="33",
        //                        SKU = "US-A9267AX2-C9034AX2",
        //                        Property = "Bundle Sale: 2Packs",
        //                        PropertyPrice = "11"
        //                    }
        //                },
        //                PaymentMethod = "PayPal",
        //                PayTime = 1,
        //                PaymentId = "220",
        //                ShippingFirstName = "CC",
        //                ShippingLastName = "adc",
        //                ShippingAddressLine1 = "111",
        //                ShippingAddressLine2 = "",
        //                ShippingCountry = "United States",
        //                ShippingState = "Alabama",
        //                ShippingCity = "111",
        //                ShippingZipCode = "100000",
        //                ShippingPhoneNumber = "1 12345678910",
        //                BillFirstName = "CC",
        //                BillLastName = "adc",
        //                BillAddressLine1 = "111",
        //                BillAddressLine2 = "",
        //                BillCountry = "United States",
        //                BillState = "Alabama",
        //                BillCity = "111",
        //                BillZipCode = "100000",
        //                BillPhoneNumber = "1 12345678910",
        //                ordersRemarkLog = "",
        //                userRemarkLog = "",
        //                CancelReason = "",
        //                ordersReason = ""
        //            }
        //        };

        //}
        #endregion



        [HttpGet("GetOrderExportDown")]
        public async Task<IActionResult> GetOrderExportDown(string name)
        {
            try
            {
                var temporary_storage_file = await _orderListService.GetOrderExportDown(name);

                // 方案一：直接重定向到远程URL（推荐）
                if (temporary_storage_file.Path.StartsWith("http"))
                {
                    return Redirect(temporary_storage_file.Path);
                }

                // 方案二：处理本地文件路径（需确保路径正确）
                var filePath = Path.Combine(Directory.GetCurrentDirectory(), "wwwroot", temporary_storage_file.Path.TrimStart('/'));

                if (!System.IO.File.Exists(filePath))
                {
                    return Ok(new { ret = false, msg = "文件不存在" });
                }

                var fileStream = System.IO.File.OpenRead(filePath);
                return File(fileStream, "application/octet-stream", Path.GetFileName(filePath));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "下载文件时发生未处理异常");
                return StatusCode(500, "服务器内部错误");
            }
        }



        //[HttpGet("GetOrderExportDown")]
        //public async Task<IActionResult> GetOrderExportDown(string name)
        //{
        //    try
        //    {
        //        var temporary_storage_file = await _orderListService.GetOrderExportDown(name);
        //        var filePath = $"wwwroot{temporary_storage_file.Path}";
        //        var file = new FileInfo(filePath);
        //        if (!file.Exists)
        //        {
        //            return Ok(new { ret = false, msg = "文件不存在" });
        //        }
        //        var stream = file.OpenRead();
        //        return File(stream, "application/octet-stream", file.Name);
        //        //return Ok(new { ret = true });
        //    }
        //    catch (Exception ex)
        //    {
        //        _logger.LogError(ex, "根据发生未处理异常");
        //        return StatusCode(500, "服务器内部错误");
        //    }
        //}
        [HttpGet("OrdersStatusSuccess")]
        public async Task<IActionResult> OrdersStatusSuccess(string id)
        {
            var b = _orderListService.OrdersStatusSuccess(id);
            return Ok(new { ret = true, msg = new { content = "修改成功", jump = "/Orders/List" } });
        }



        [HttpPost("OrdersModTrack")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> OrdersModTrack()
        {
            try
            {

                var WId = Request.Form["WId"].ObjToInt();
                var OrderId = Request.Form["OrderId"].ObjToInt();
                var TrackingNumber = Request.Form["TrackingNumber[" + WId + "]"];
                var Carrier = Request.Form["Carrier"].ToString();
                var CarrierKey = Request.Form["CarrierKey"].ToString();
                var CarrierKeyType = Request.Form["CarrierKeyType"].ToString();
                var ShippingTime = Request.Form["ShippingTime[" + WId + "]"];
                var Remarks = Request.Form["Remarks[" + WId + "]"];



                // 2. 创建有序字典存储动态参数
                var qtyDict = new SortedDictionary<string, string>(StringComparer.Ordinal);

                // 3. 遍历所有表单键
                foreach (var key in Request.Form.Keys)
                {
                    // 4. 正则匹配动态参数格式
                    var match = System.Text.RegularExpressions.Regex.Match(
                        key,
                        @"^Qty\[(?<index>\d+)\]$",
                        System.Text.RegularExpressions.RegexOptions.IgnoreCase
                    );

                    if (match.Success)
                    {
                        // 5. 提取并存储参数（保留原始顺序）
                        var index = match.Groups["index"].Value;
                        qtyDict[index] = Request.Form[key].ToString();
                    }
                }



                JObject CarrierKeys = JObject.Parse(CarrierKey);

                // 构建最终JSON对象
                JObject result = new JObject
                {
                    ["key"] = CarrierKeys,
                    ["type"] = CarrierKeyType,
                    ["name"] = Carrier
                };

                var CarrierRes = result.ToString(Newtonsoft.Json.Formatting.None);
                //{ "key":{ "17track":3013},"type":"carrier","name":"China EMS"}

                var Status = await _orderListService.OrdersModTrack(WId, OrderId, TrackingNumber, ShippingTime, Remarks, CarrierRes, qtyDict);
                //var msg = new Dictionary<string, object> { { OrderId.ToString(), Status } };
                return Ok(new { ret = 1, msg = new { content = "提交成功", jump = "/Orders/Detail?id=" + OrderId + "&query_string=" } });

            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "根据发生未处理异常");
                return StatusCode(500, "服务器内部错误");
            }
        }

        [HttpGet("OrdersCancelDelivery")]
        public async Task<IActionResult> OrdersCancelDelivery(int OrderId, int WId)
        {
            var b = _orderListService.OrdersCancelDelivery(OrderId, WId);
            return Ok(new { ret = true, msg = new { content = "取消发货", jump = "/Orders/Detail?id=" + OrderId + "&query_string=" } });
        }

        [HttpPost("OrdersRemarkLog")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> OrdersRemarkLog()
        {
            try
            {

                var OrderId = Request.Form["OrderId"].ObjToInt();
                var Log = Request.Form["Log"];

                var orders_Remark_Log = await _orderListService.OrdersRemarkLog(OrderId, Log);
                return Ok(new
                {
                    ret = 1,
                    msg = new
                    {
                        OrderId = orders_Remark_Log.OrderId,
                        Log = orders_Remark_Log.Log,
                        AccTime = YseStore.Common.Helper.DateTimeHelper.ConvertUnixTimestampToBeijingTime((long)orders_Remark_Log.AccTime, "yyyy-MM-dd") + "<br/>" + YseStore.Common.Helper.DateTimeHelper.ConvertUnixTimestampToBeijingTime((long)orders_Remark_Log.AccTime, "HH:mm:ss"),
                        UserId = orders_Remark_Log.UserId,
                        UserName = orders_Remark_Log.UserName
                    }
                });


                //return Ok(new { ret = 1, msg = new { content = "发送成功", jump = "/Orders/Detail?id=" + OrderId + "&query_string=" } });
                //{ "msg":{ "OrderId":31,"Log":"2323234","AccTime":"2025-05-21<br/>11:20:26","UserId":85903,"UserName":"yseadmin"},"ret":1}

            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "根据发生未处理异常");
                return StatusCode(500, "服务器内部错误");
            }
        }
        [HttpPost("OrdersModTags")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> OrdersModTags([FromForm] int OrderId, [FromForm] List<string> tagsName)
        {
            var b = _orderListService.UpdateOrders_tags(OrderId, tagsName);
            return Ok(new { ret = true, msg = "更改成功" });
        }
        [HttpPost("OrdersGetAddress")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> OrdersGetAddress([FromForm] int OrderId, [FromForm] string Type)
        {
            var OrdersFirst = await _orderListService.OrdersGetAddress(OrderId, Type);
            return Ok(new { ret = 1, msg = OrdersFirst });
        }
        [HttpPost("OrdersModAddress")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> OrdersModAddress()
        {
            var FirstName = Request.Form["FirstName"];
            var LastName = Request.Form["LastName"];
            var AddressLine1 = Request.Form["AddressLine1"];
            var AddressLine2 = Request.Form["AddressLine2"];
            var City = Request.Form["City"];
            var country_id_input = Request.Form["country_id_input"];
            var country_id = Request.Form["country_id"].ObjToInt();
            var country_idType = Request.Form["country_idType"];
            var _DoubleOption = Request.Form["_DoubleOption[]"];
            var Province = Request.Form["Province"].ObjToInt();
            var ZipCode = Request.Form["ZipCode"];
            var CountryCode = Request.Form["CountryCode"];
            var PhoneNumber = Request.Form["PhoneNumber"];
            var tax_code_type = Request.Form["tax_code_type"].ObjToInt();
            var tax_code_value = Request.Form["tax_code_value"];
            var OrderId = Request.Form["OrderId"].ObjToInt();
            var Type = Request.Form["Type"];


            var OrdersModAddress = await _orderListService.OrdersModAddress(FirstName, LastName, AddressLine1, AddressLine2, City, country_id_input, country_id, country_idType
                , _DoubleOption, Province, ZipCode, CountryCode, PhoneNumber, tax_code_type, tax_code_value, OrderId, Type);
            return Ok(new { ret = 1, msg = OrdersModAddress });
        }

        [HttpPost("OrdersModStatus")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> OrdersModStatus()
        {
            try
            {
                var OrderId = Request.Form["OrderId"].ObjToInt();
                var OrderStatus = Request.Form["OrderStatus"].ObjToInt();
                var b = await _orderListService.GetOrdersModStatus(OrderId, OrderStatus);
                return Ok(new { ret = b });
            }
            catch (Exception ex)
            {
                return Ok(new { ret = false });
                throw;
            }

        }


        [HttpPost("OrdersSavePrintConfig")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> OrdersSavePrintConfig()
        {
            var Type = Request.Form["Type"];
            var currency = Request.Form["currency"];
            var OrderPrintLogo = Request.Form["OrderPrintLogo"];
            //,,proserial,propic,proname,pronumber,prosku,proattr,proqty,item_price,

            string printconfig = "";
            var store_logo = Request.Form["printMenu[store_logo]"];
            if (store_logo == "1")
            {
                printconfig += "store_logo,";
            }
            var store_name = Request.Form["printMenu[store_name]"];
            if (store_name == "1")
            {
                printconfig += "store_name,";
            }

            var proserial = Request.Form["printMenu[proserial]"];
            if (proserial == "1")
            {
                printconfig += "proserial,";
            }
            var propic = Request.Form["printMenu[propic]"];
            if (propic == "1")
            {
                printconfig += "propic,";
            }
            var proname = Request.Form["printMenu[proname]"];
            if (proname == "1")
            {
                printconfig += "proname,";
            }
            var pronumber = Request.Form["printMenu[pronumber]"];
            if (pronumber == "1")
            {
                printconfig += "pronumber,";
            }
            var prosku = Request.Form["printMenu[prosku]"];
            if (prosku == "1")
            {
                printconfig += "prosku,";
            }
            var proattr = Request.Form["printMenu[proattr]"];
            if (proattr == "1")
            {
                printconfig += "proattr,";
            }
            var proqty = Request.Form["printMenu[proqty]"];
            if (proqty == "1")
            {
                printconfig += "proqty,";
            }
            var item_price = Request.Form["printMenu[item_price]"];
            if (item_price == "1")
            {
                printconfig += "item_price,";
            }
            //sub_price,total_price,discount,freight,taxes,fee_price,order_total
            var sub_price = Request.Form["printMenu[sub_price]"];
            if (sub_price == "1")
            {
                printconfig += "sub_price,";
            }
            var total_price = Request.Form["printMenu[total_price]"];
            if (total_price == "1")
            {
                printconfig += "total_price,";
            }
            var discount = Request.Form["printMenu[discount]"];
            if (discount == "1")
            {
                printconfig += "discount,";
            }
            var freight = Request.Form["printMenu[freight]"];
            if (freight == "1")
            {
                printconfig += "freight,";
            }
            var taxes = Request.Form["printMenu[taxes]"];
            if (taxes == "1")
            {
                printconfig += "taxes,";
            }
            var fee_price = Request.Form["printMenu[fee_price]"];
            if (fee_price == "1")
            {
                printconfig += "fee_price,";
            }
            var order_total = Request.Form["printMenu[order_total]"];
            if (order_total == "1")
            {
                printconfig += "order_total,";
            }
            if (printconfig.Length > 0)
            {
                printconfig = printconfig.Substring(0, printconfig.Length - 1);
            }
            return Ok(new { ret = 1, msg = printconfig });
        }


        [HttpPost("OrdersAddProducts")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> OrdersAddProducts()
        {

            var contentType = Request.ContentType;
            if (string.IsNullOrEmpty(contentType))
            {
                return Ok(new { ret = 1, msg = new { Html = new List<dynamic>(), ProId = new List<dynamic>(), IsFirst = 1 } });
            }
            var HtmlList = new List<dynamic>();
            var ProIdList = new List<dynamic>();
            var form = Request.Form;
            var proIds = new List<int>();
            if (form.ContainsKey("ProId[]"))
            {
                var standardArrayValues = form["ProId[]"].ToString().Split(',');
                foreach (var value in standardArrayValues)
                {
                    if (int.TryParse(value, out var id))
                    {
                        proIds.Add(id);
                        ProIdList.Add(id.ToString());
                    }
                }
            }

            //var (html_ary, html_id_ary,  IsFirst) =await _helpsProductsService.AddProducts(proIds);


            //var sdfasdf = await _orderListService.GetCreateProducts(proIds);

            var usedCurrencyRow = await _orderListService.getCurrencyList();
            var manageCurrency = usedCurrencyRow.Where(x=>x.ManageDefault==true).FirstOrDefault();
            
            var productsList = await _orderListService.GetproductsByProId(proIds);
            if (productsList != null && productsList.Count > 0)
            {
                foreach (var item in productsList)
                {
                    var rawData = await _orderListService.getAttributeByProId(item.ProId);
                    var price = item.Price_1;
                    if (rawData!=null&& rawData.Count>0)
                    {
                        price = rawData.First().Price;
                    }
                    StringBuilder html = new StringBuilder();
                    StringBuilder htmls = new StringBuilder();
                    var Stock = await _orderListService.getQtyByProId(item.ProId);
                    html.AppendFormat(@"<tr data-id=""{0}"" data-price=""{1}"" data-fixed-price=""{2}"" data-fixed-stock=""{3}"" data-single=""{4}"" data-variants="""">", item.ProId, price, item.Price_1, Stock, item.IsCombination);
                    html.AppendFormat(@"<td class=""img"">");
                    html.AppendFormat(@"    <div class=""img_box"">");
                    html.AppendFormat(@"        <a href ="""" target=""_blank"" class=""pic_box"">");
                    html.AppendFormat(@"            <img src =""{0}"" data-src=""{1}"" class=""loading_img""/>", item.PicPath_0, item.PicPath_0);
                    html.AppendFormat(@" <span></span>");
                    html.AppendFormat(@"        </a>");
                    html.AppendFormat(@"        <div class=""info"">");
                    html.AppendFormat(@"            <a href ="""" target=""_blank"" class=""name"">{0}</a>", item.Name_en);
                    html.AppendFormat(@" <p class=""pro_sku""></p>");
                    html.AppendFormat(@"            <div class=""attr_data""> ");
                    if (Stock == 0)
                    {
                        html.AppendFormat(@"            <p>没有规格可选</p>");
                    }
                    html.AppendFormat(@"             </div>");
                    html.AppendFormat(@"        </div>");
                    html.AppendFormat(@"    </div>");
                    html.AppendFormat(@"</td>");
                    html.AppendFormat(@"<td nowrap =""nowrap"">");
                    html.AppendFormat(@"    <div class=""h54"">");
                    html.AppendFormat(@"        <span class=""unit_input Price_1"">");
                    html.AppendFormat(@"            <b>{0}</b>", manageCurrency.Symbol);
                    html.AppendFormat(@"            <input type =""text"" class=""box_input left_radius"" autocomplete=""off"" name=""Price[]"" value=""{0}"" size=""3"" maxlength=""10"" rel=""amount""/>", item.Price_1);
                    html.AppendFormat(@" </span>");
                    html.AppendFormat(@"        <div class=""rate_price""></div>");
                    html.AppendFormat(@"    </div>");
                    html.AppendFormat(@"</td>");
                    html.AppendFormat(@"<td nowrap =""nowrap"">");
                    html.AppendFormat(@"    <div class=""qty_box"">");
                    html.AppendFormat(@"        <input type =""text"" class=""box_input qty"" autocomplete=""off"" name=""Qty[]"" value=""1"" size=""4"" maxlength=""7"" rel=""int"">");
                    html.AppendFormat(@"  /  <span class=""stock"">{0}</span>", Stock);
                    html.AppendFormat(@"    </div>");
                    html.AppendFormat(@"    <div class=""sold_status"">库存为0 不允许购买</div>");
                    html.AppendFormat(@"</td>");
                    html.AppendFormat(@"<td nowrap =""nowrap"">");
                    html.AppendFormat(@"    <span class=""itemTotal"">");
                    html.AppendFormat(@"        {0}<span>{1}</span>", manageCurrency.Symbol, item.Price_1);
                    html.AppendFormat(@"    </span>");
                    html.AppendFormat(@"    <div class=""rate_total_price""></div>");
                    html.AppendFormat(@"</td>");
                    html.AppendFormat(@"<td nowrap =""nowrap"" class=""operation"">");
                    html.AppendFormat(@"    <a href =""javascript:;"" class=""oper_icon btn_attr_revise icon_revise"">修改</a>");
                    html.AppendFormat(@"    <a href =""javascript:;"" class=""oper_icon icon_del icon_delete del"">删除</a>");
                    html.AppendFormat(@"    <div class=""fixed_attribute"">");
                    html.AppendFormat(@"        <div class=""option_list"">");

                    if (rawData.Count > 1)
                    {
                        var products_AttributesList = await _orderListService.getproducts_attributeByProId(item.ProId);
                        //var specsList = products_AttributesList.Where(x => x.Type == "text").ToList();
                        var specsList = products_AttributesList;
                        if (specsList != null)
                        {
                            foreach (var specs in specsList)
                            {
                                html.AppendFormat(@" <div class=""rows"">");
                                html.AppendFormat(@"      <label>{0}</label>   ", specs.Name_en);
                                html.AppendFormat(@"      <div class=""input"">                                                                                                                        ");
                                html.AppendFormat(@"          <div class=""box_select"" parent_null="""">                                                                                              ");
                                html.AppendFormat(@"              <select name=""Attr_{0}[{1}]"" parent_null=""1"" data-attr=""{2}"" data-title=""{3}"" data-title-value=""{4}"" notnull=""""> ", item.ProId, specs.AttrId, specs.AttrId, specs.Name_en, specs.Name_en);
                                html.AppendFormat(@"                  <option value="""">--请选择--</option>  ");
                                JArray jsonArray = JArray.Parse(specs.Options);
                                foreach (JToken items in jsonArray)
                                {
                                    html.AppendFormat(@"                  <option value=""{0}"">{1}</option>", items.ToString(), items.ToString());
                                }
                                html.AppendFormat(@"              </select>   ");
                                html.AppendFormat(@"          </div>                                                                                                                                   ");
                                html.AppendFormat(@"      </div>                                                                                                                                       ");
                                html.AppendFormat(@"  </div>  ");
                            }


                        }

                        //var colorList = products_AttributesList.Where(x => x.Type == "color").ToList();
                        //if (colorList != null)
                        //{
                        //    foreach (var color in colorList)
                        //    {
                        //        html.AppendFormat(@" <div class=""rows"">                                                                                                                           ");
                        //        html.AppendFormat(@"     <label>{0}</label>   ", color.Name_en);
                        //        html.AppendFormat(@"     <div class=""input"">                                                                                                                        ");
                        //        html.AppendFormat(@"         <div class=""box_select"" parent_null="""">                                                                                              ");
                        //        html.AppendFormat(@"             <select name=""Attr_{0}[{1}]"" parent_null=""1"" data-attr=""{2}"" data-title=""{3}"" data-title-value=""{4}"" notnull="""">", item.ProId, color.AttrId, color.AttrId, color.Name_en, color.Name_en);
                        //        html.AppendFormat(@"                 <option value="""">--请选择--</option> ");
                        //        JArray jsonArray = JArray.Parse(color.Options);
                        //        foreach (JToken items in jsonArray)
                        //        {
                        //            html.AppendFormat(@"                  <option value=""{0}"">{1}</option>", items.ToString(), items.ToString());
                        //        }
                        //        html.AppendFormat(@"             </select> ");
                        //        html.AppendFormat(@"         </div>  ");
                        //        html.AppendFormat(@"     </div>   ");
                        //        html.AppendFormat(@" </div>   ");
                        //    }

                        //}


                        var OvIds = rawData.Where(x => x.OvId > 0).Select(x => Convert.ToInt32(x.OvId)).ToList();



                        //if (item.IsCombination > 0)
                        //{
                            var shippingOverseasList = await _orderListService.getshipping_overseas();
                            var OverseasList = shippingOverseasList.Where(x => OvIds.Contains(x.OvId)).ToList();

                            //var OvList = await _orderListService.getAttributeShipByProId(item.ProId);

                            //var OvList = await _orderListService.Getshipping_overseasByProId(item.ProId);
                            if (OverseasList != null && OverseasList.Count > 0)
                            {
                                html.AppendFormat(@" <div class=""rows"">                                                                                                                            ");
                                html.AppendFormat(@"    <label>发货地</label>                                                                                                                         ");
                                html.AppendFormat(@"    <div class=""input"">										                                                                                ");
                                html.AppendFormat(@"        <div class=""box_select"" parent_null="""">                                                                                              ");
                                html.AppendFormat(@"            <select name=""Attr_{0}[0]"" parent_null=""1"" data-attr=""0"" data-title=""发货地"" data-title-value=""Ship From"" notnull="""">", item.ProId);
                                html.AppendFormat(@"                <option value="""">--请选择--</option>");
                                foreach (var items in OverseasList)
                                {
                                    html.AppendFormat(@"                  <option value=""{0}"">{1}</option>", items.OvId, items.Name);
                                }
                                html.AppendFormat(@"            </select>  ");
                                html.AppendFormat(@"        </div> ");
                                html.AppendFormat(@"    </div>                                                                                                                                       ");
                                html.AppendFormat(@"  </div>        ");
                            }
                        //}
                    }
                    html.AppendFormat(@"  </div>        ");
                    var Totalvalue = _orderListService.GetProAttribute(rawData, item);

                    html.AppendFormat(@"        <div class=""no_attribute"">没有规格可选</div>");
                    html.AppendFormat(@"        <input type =""hidden"" name=""VariantsId[]"" value="""" class=""variants_id""/>");
                    html.AppendFormat(@"        <input type =""hidden"" class=""ext_attr"" value='{0}' data-combination=""{1}"" data-soldstatus=""{2}"" disabled/>", Totalvalue, Convert.ToInt32(item.IsCombination), item.SoldStatus);
                    html.AppendFormat(@"        <input type =""hidden"" name=""ProId[]"" value=""{0}"">    ", item.ProId);
                    html.AppendFormat(@"    </div>");
                    html.AppendFormat(@"</td>");
                    html.AppendFormat(@"</tr>");
                    HtmlList.Add(html.ToString());
                    //html += $@"<tr data-id=""{item.Id}"" data-price=""{item.Price}"" data-fixed-price=""{item.FixedPrice}"" data-fixed-stock=""{item.FixedStock}"" data-single=""1"" data-variants="""">";
                }
            }

            //{ "msg":{ "Html":[],"ProId":[],"IsFirst":1},"ret":1}
            return Ok(new { ret = 1, msg = new { Html = HtmlList, ProId = ProIdList, IsFirst = 0 } });
        }







        //public string GetProAttribute(List<products_selected_attribute_combination> rawData)
        //{
        //    var cleanedData = rawData.Select(item => new
        //    {
        //        item.Data,
        //        item.Price,
        //        item.Stock,
        //        item.Weight,
        //        item.SKU,
        //        item.OldPrice,
        //        item.ImageId,
        //        item.VariantsId,
        //        item.OvId
        //    }).ToList();

        //    var result = new
        //    {
        //        Total = cleanedData
        //            .GroupBy(item => new
        //            {
        //                // 组合VariantsId和清洗后的Data作为外层键
        //                Key = $"{SanitizeInput(item.Data)}"
        //            })
        //            .ToDictionary(
        //                g => g.Key.Key,
        //                g => g.GroupBy(item => item.OvId)  // 按OvId进行二次分组
        //                    .ToDictionary(
        //                        sg => sg.Key.ToString(),  // 使用OvId作为内层键
        //                        sg => sg.Select(item => new object[]
        //                        {
        //                    // 统一转换为字符串类型（除数字ID外）
        //                    item.Price.ToString(),
        //                    item.Stock,
        //                    item.Weight,
        //                    SanitizeString(item.SKU),
        //                    item.OldPrice.ToString("0.00"),
        //                    item.ImageId,
        //                    item.VariantsId,
        //                    item.OvId
        //                        }).FirstOrDefault()  // 确保每个OvId只取第一个匹配项
        //                    )
        //            )
        //    };

        //    var settings = new Newtonsoft.Json.JsonSerializerSettings
        //    {
        //        ContractResolver = new Newtonsoft.Json.Serialization.DefaultContractResolver
        //        {
        //            NamingStrategy = new Newtonsoft.Json.Serialization.DefaultNamingStrategy()
        //        },
        //        //StringEscapeHandling = StringEscapeHandling.EscapeNonAscii,
        //        StringEscapeHandling = StringEscapeHandling.EscapeHtml,
        //        Formatting = Formatting.None  // 保持紧凑格式（HTML中需要时可手动换行）
        //    };

        //    return JsonConvert.SerializeObject(result, settings)
        //        //.Replace("\"", "")  // 手动转义HTML中的双引号
        //        .Replace("\r\n", "")      // 移除换行符（根据需求调整）
        //        .Replace(" ", "");        // 移除空格（根据需求调整）
        //}

        //// 保持原有Sanitize方法不变（根据实际数据可能需要调整）
        //private static string SanitizeInput(string input)
        //{
        //    if (string.IsNullOrEmpty(input)) return "";
        //    return input.Trim()
        //        .Replace("[", "")
        //        .Replace("]", "")
        //        .Replace("\"", "")
        //        .Replace("=", "")
        //        .Replace(" ", "_")
        //        .Replace("#", "")
        //        .Replace("$", "")
        //        .Replace("^", "")
        //        .Replace("&", "")
        //        .Replace("*", "")
        //        .Replace("(", "")
        //        .Replace(")", "")
        //        .Replace("+", "")
        //        .Replace("`", "")
        //        .Replace("~", "")
        //        .Replace("!", "")
        //        .Replace("@", "")
        //        .Replace(",", "_")
        //        .Replace("%", "");

        //}

        //private static string SanitizeString(string input)
        //{
        //    return string.IsNullOrEmpty(input) ? "" : input.Trim();
        //}








        [HttpPost("products-choice-search-v2")]
        public async Task<IActionResult> ProductsChoiceSearchV2()
        {
            try
            {
                var products_Categories = await _orderListService.GetProducts_Categories();
                var products_Tags = await _orderListService.GetProducts_tags();
                var cateids = new Dictionary<string, string>();
                if (products_Categories != null && products_Categories.Count > 0)
                {
                    foreach (var item in products_Categories)
                    {
                        cateids.Add(item.CateId.ToString(), item.Category_en);
                    }
                }
                var tagids = new Dictionary<string, string>();
                if (products_Tags != null && products_Tags.Count > 0)
                {
                    foreach (var item in products_Tags)
                    {
                        tagids.Add(item.TId.ToString(), item.Name_en);
                    }
                }

                //{

                //    { "1", "Amateur Radios / HAM Radios" },
                //    { "7", "Amateur Radios / HAM Radios > Analog Radios" },
                //    // 其他分类数据...
                //    { "25", "test" }
                //};

                var html =
                    @"<div class=""box_drop_down_menu"">
                            <div class=""more"">
                                <span>产品添加方式</span>
                            </div>
                            <div class=""box_type_menu"">
                                <span class=""item type_item checked"" data-type=""manual"">
                                    <input type=""radio"" name=""add_type"" value=""manual"" checked=""checked"">手动添加
                                </span>
                                <span class=""item type_item"" data-type=""auto"">
                                    <input type=""radio"" name=""add_type"" value=""auto"">智能添加
                                </span>
                            </div>
                        </div>
                        <span class=""input_checkbox_box menu_all_checkbox disabled"">
                            <span class=""input_checkbox"">
                                <input type=""checkbox"" name="""" value="""">
                            </span>
                            全选
                        </span>
                        <div class=""products_limit""></div>
                        <div class=""search_form"">
                            <div class=""k_input"">
                                <input type=""text"" name=""Keyword"" value="""" class=""form_input"" size=""15"" autocomplete=""off"" placeholder=""请输入产品名称、产品编号或SKU""/>
                                <input type=""button"" value="""" class=""more more_up"">
                            </div>
                            <input type=""button"" value=""搜索"" class=""search_btn""/>
                            <div class=""clear""></div>
                            <div class=""ext drop_down""></div>
                            <input type=""hidden"" name=""cateId"" value=""""/>
                            <input type=""hidden"" name=""tagId"" value=""""/>
                            <input type=""hidden"" name=""moreId"" value=""""/>
                        </div>";


                var response = new OrderProductsChoiceSearchResponse
                {


                    filter = new FilterModel
                    {
                        cateid = cateids,
                        tagid = tagids,
                        more = new Dictionary<string, string>
                        {
                            { "new", "新品" },
                            { "sales", "畅销产品" }
                        }
                    },
                    html = html.Replace("\r\n", "").ToString()

                };
                return Ok(new { ret = 1, msg = response });


            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "根据发生未处理异常");
                return StatusCode(500, "服务器内部错误");
            }

        }






        //[HttpPost("products-choice-search-action-v2")]
        //[ProducesResponseType(StatusCodes.Status200OK)]
        //[ProducesResponseType(StatusCodes.Status400BadRequest)]
        //[ProducesResponseType(StatusCodes.Status500InternalServerError)]
        //public async Task<IActionResult> ProductsChoiceSearchActionV2()
        //{
        //    var Keyword = Request.Form["Keyword"];
        //    var CateId = Request.Form["CateId"];
        //    var TagId = Request.Form["TagId"];
        //    var MoreId = Request.Form["MoreId"];
        //    var Page = Request.Form["Page"].ObjToInt();
        //    var Count = Request.Form["Count"].ObjToInt();
        //    var exclude = Request.Form["exclude"];

        //    var productsList = await _orderListService.GetProductsChoiceSearchActionV2(Keyword, CateId, TagId, MoreId, exclude, Page, Count);

        //    var products = productsList;

        //    //var products = new List<Product>
        //    //{
        //    //    new Product
        //    //    {
        //    //        ProId = 45,
        //    //        Name_en = "MyTestData",
        //    //        PicPath_0 = "/u_file/2505/08/products/8ac6823ba5.jpg"
        //    //    },
        //    //    new Product
        //    //    {
        //    //        ProId = 38,
        //    //        Name_en = "Retevis EZTalk 62 Dual Band VHF/UHF Waterproof 2 Way Radio for Hunting",
        //    //        PicPath_0 = "/u_file/2502/11/products/da7fe9c5dd.jpg"
        //    //    }
        //    //    // 添加更多产品数据...
        //    //};
        //    var response = new OrderProductsRowResponse
        //    {
        //        products_row = new object[]{
        //        products,       // 产品列表
        //                productsList.TotalCount,             // 总条数
        //                1,              // 当前页
        //                1,              // 每页条数
        //                0               // 其他参数（根据实际业务逻辑）
        //        },
        //        turnPageHtml = "<div id=\"turn_page\" data-current=\"0\" data-count=\"1\"><div class=\"total_page\">共 " + productsList.TotalCount + " 条</div></div>"
        //    };
        //    return Ok(new { ret = true, msg = response });

        //}


        [HttpPost("products-choice-search-action-v2")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> ProductsChoiceSearchActionV2()
        {
            var Keyword = Request.Form["Keyword"];
            var CateId = Request.Form["CateId"];
            var TagId = Request.Form["TagId"];
            var MoreId = Request.Form["MoreId"];
            var Page = Request.Form["Page"].ObjToInt();
            var Count = Request.Form["Count"].ObjToInt();
            var exclude = Request.Form["exclude"];

            var productsList = await _orderListService.GetProductsChoiceSearchActionV2(Keyword, CateId, TagId, MoreId, exclude, Page, Count);

            var products = productsList;

            //var products = new List<Product>
            //{
            //    new Product
            //    {
            //        ProId = 45,
            //        Name_en = "MyTestData",
            //        PicPath_0 = "/u_file/2505/08/products/8ac6823ba5.jpg"
            //    },
            //    new Product
            //    {
            //        ProId = 38,
            //        Name_en = "Retevis EZTalk 62 Dual Band VHF/UHF Waterproof 2 Way Radio for Hunting",
            //        PicPath_0 = "/u_file/2502/11/products/da7fe9c5dd.jpg"
            //    }
            //    // 添加更多产品数据...
            //};
            

            var response = new OrderProductsRowResponse
            {
                products_row = new object[]{
                products,
                productsList.TotalCount,
                Page, // 修正当前页显示
                Count,
                0
                },
                turnPageHtml = GeneratePaginationHtml(productsList.TotalCount, Page - 1, Count)
            };
            return Ok(new { ret = true, msg = response });

        }



        // 新增分页HTML生成方法
        private string GeneratePaginationHtml(int totalItems, int currentPage, int itemsPerPage)
        {
            int totalPages = (int)Math.Ceiling((double)totalItems / itemsPerPage);
            int visiblePages = 5; // 可见页码数

            var sb = new StringBuilder();
            sb.Append($@"<div id=""W9E549831A32933077636770B7286DDB0"" m-name=""MxPager"" class=""pagination-container"">
        <div id=""turn_page"" data-current=""{currentPage}"" data-count=""{totalPages}"">
            <div class=""total_page"">共 {totalItems} 条</div>
            <ul class=""pagination"">");

        //    // 首页和上一页
        //    sb.Append($@"<li class=""first {(currentPage == 0 ? "disabled" : "")}"">
        //<a href=""javascript:void(0)"" data-page=""0"" x-on:click=""invoke($event, {{'name':'TurnPage','parameters':{{'page':1}}}})"" {(currentPage == 0 ? "disabled" : "")}>首页</a></li>
        //<li class=""prev {(currentPage == 0 ? "disabled" : "")}"">
        //<a href=""javascript:void(0)"" data-page=""{currentPage - 1}"" x-on:click=""invoke($event, {{'name':'TurnPage','parameters':{{'page':{currentPage}}}}})"" {(currentPage == 0 ? "disabled" : "")}></a></li>");
          
            // 修改首页和上一页的参数传递
            sb.Append($@"<li class=""first {(currentPage == 0 ? "disabled" : "")}"">
        <a href=""javascript:void(0)"" data-page=""0"" x-on:click=""invoke($event, {{'name':'TurnPage','parameters':{{'page':1}}}})"" {(currentPage == 0 ? "disabled" : "")}>首页</a></li>
        <li class=""prev {(currentPage == 0 ? "disabled" : "")}"">
        <a href=""javascript:void(0)"" data-page=""{currentPage - 1}"" x-on:click=""invoke($event, {{'name':'TurnPage','parameters':{{'page':{currentPage}}}}})"" {(currentPage == 0 ? "disabled" : "")}></a></li>"); // 这里保持currentPage原始值

            // 页码生成逻辑
            int startPage = Math.Max(0, currentPage - 2);
            int endPage = Math.Min(totalPages - 1, startPage + visiblePages - 1);

            if (startPage > 0)
            {
                sb.Append("<li class=\"ellipsis\"><span>...</span></li>");
            }

            //for (int i = startPage; i <= endPage; i++)
            //{
            //    sb.Append($@"<li class=""{(i == currentPage ? "active" : "")}"">
            //<a href=""javascript:void(0)"" data-page=""{i}"" x-on:click=""invoke($event, {{'name':'TurnPage','parameters':{{'page':{i + 1}}}}})"">{i + 1}</a></li>");
            //}
            // 修改页码循环中的参数传递
            for (int i = startPage; i <= endPage; i++)
            {
                sb.Append($@"<li class=""{(i == currentPage ? "active" : "")}"">
            <a href=""javascript:void(0)"" data-page=""{i}"" x-on:click=""invoke($event, {{'name':'TurnPage','parameters':{{'page':{i + 1}}}}})"">{i + 1}</a></li>"); // 保持i+1显示，但参数传递正确
            }
            if (endPage < totalPages - 1)
            {
                sb.Append("<li class=\"ellipsis\"><span>...</span></li>");
            }

            //    // 下一页和尾页
            //    sb.Append($@"<li class=""next {(currentPage == totalPages - 1 ? "disabled" : "")}"">
            //<a href=""javascript:void(0)"" data-page=""{currentPage + 1}"" x-on:click=""invoke($event, {{'name':'TurnPage','parameters':{{'page':{currentPage + 2}}}}})"" {(currentPage == totalPages - 1 ? "disabled" : "")}></a></li>
            //<li class=""last {(currentPage == totalPages - 1 ? "disabled" : "")}"">
            //<a href=""javascript:void(0)"" data-page=""{totalPages - 1}"" x-on:click=""invoke($event, {{'name':'TurnPage','parameters':{{'page':{totalPages}}}}})"" {(currentPage == totalPages - 1 ? "disabled" : "")}>尾页</a></li>");
            // 修改下一页和尾页的参数传递
            sb.Append($@"<li class=""next {(currentPage == totalPages - 1 ? "disabled" : "")}"">
        <a href=""javascript:void(0)"" data-page=""{currentPage + 1}"" x-on:click=""invoke($event, {{'name':'TurnPage','parameters':{{'page':{currentPage + 2}}}}})"" {(currentPage == totalPages - 1 ? "disabled" : "")}></a></li>
        <li class=""last {(currentPage == totalPages - 1 ? "disabled" : "")}"">
        <a href=""javascript:void(0)"" data-page=""{totalPages - 1}"" x-on:click=""invoke($event, {{'name':'TurnPage','parameters':{{'page':{totalPages}}}}})"" {(currentPage == totalPages - 1 ? "disabled" : "")}>尾页</a></li>"); // 保持totalPages原始值


            // 跳转部分
            sb.Append($@"</ul>
            <div class=""goto-page"">
                <span>前往</span>
                <input type=""number"" id=""gotoPageInput_manlog"" name=""gotoPage"" class=""goto-page-input"" 
                    min=""1"" max=""{totalPages}"" value=""{currentPage + 1}"">
                <span>页</span>
                <button type=""button"" class=""goto-page-button"" 
                    x-on:click=""invoke($event, {{'name':'GotoPage'}})"">确定</button>
            </div>
        </div>
    </div>");

            return sb.ToString();
        }


        [HttpPost("SelectProductsFilter")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> SelectProductsFilter()
        {
            var CateId = Request.Form["CateId"];
            var TagId = Request.Form["TagId"];
            var MoreId = Request.Form["MoreId"];

            var htmls = await _orderListService.GetSelectProductsFilter(CateId, TagId, MoreId);

            //var products_Categories = await _orderListService.GetProducts_Categories();
            //var products_Tags = await _orderListService.GetProducts_tags();
            //var cateids = new Dictionary<string, string>();
            //if (products_Categories != null && products_Categories.Count > 0)
            //{
            //    foreach (var item in products_Categories)
            //    {
            //        cateids.Add(item.CateId.ToString(), item.Category_en);
            //    }
            //}
            //var tagids = new Dictionary<string, string>();
            //if (products_Tags != null && products_Tags.Count > 0)
            //{
            //    foreach (var item in products_Tags)
            //    {
            //        tagids.Add(item.TId.ToString(), item.Name_en);
            //    }
            //}
            //var cateidsStr = System.Text.Json.JsonSerializer.Serialize(cateids);
            //var tagidsStr = System.Text.Json.JsonSerializer.Serialize(tagids);

            //var cateidsStr = JsonConvert.SerializeObject(cateids);
            //var tagidsStr = JsonConvert.SerializeObject(tagids);
            string html =
                    @"
<div class=""rows i_select_category item clean"">
    <label>分类</label>
    <div class=""input"">
        <dl class=""box_drop_double edit_box"" data-checkbox=""0"" data-showadd=""0"">
            <dt>
                <input type=""text"" class=""box_input"" name=""Select"" placeholder=""请选择或填写"" value="""" autocomplete=""off""/>
                <input type=""hidden"" name=""cateId"" value="""" class=""hidden_value""/>
                <input type=""hidden"" name=""cateIdType"" value="""" class=""hidden_type""/>
            </dt>
            <dd class=""drop_down"">
                <div class=""drop_menu"" data-type=""Select"">
                    <a href=""javascript:;"" class=""btn_back"" data-value="""" data-type="""" data-table="""" data-top=""0"" data-all=""0"" style=""display:none;"">返回</a>
                    <div class=""drop_skin"" style=""display:none;""></div>
                    <div class=""drop_list"" data=""[{Name:Amateur Radios / HAM Radios,Type:products_category,Value:1,Disabled:false,Table:products_category},{Name:Professional DMR radios,Type:products_category,Value:15,Disabled:false,Table:products_category},{Name:On-site business radios,Type:products_category,Value:2,Disabled:false,Table:products_category},{Name:Outdoor Two Way Radios,Type:products_category,Value:3,Disabled:false,Table:products_category},{Name:Mobile Two-Way Radios,Type:products_category,Value:4,Disabled:false},{Name:Radio Repeaters,Type:products_category,Value:5,Disabled:false},{Name:Radio Accessories,Type:products_category,Value:6,Disabled:false},{Name:Deals,Type:products_category,Value:12,Disabled:false},{Name:Activity,Type:products_category,Value:13,Disabled:false},{Name:test,Type:products_category,Value:25,Disabled:false}]"" data-more=""none""></div>
                    <a href=""javascript:;"" class=""btn_load_more"" data-value=""products_category"" data-type=""products_category"" data-table=""products_category"" data-top=""0"" data-all=""1"" data-check-all=""0"" data-start=""1"" style=""display:none;"">加载更多</a>
                </div>
            </dd>
        </dl>
    </div>
</div>
<div class=""rows i_select_tags item clean"">
    <label>标签</label>
    <div class=""input"">
        <dl class=""box_drop_double"" data-checkbox=""1"" data-showadd=""0"">
            <dt class=""box_checkbox_list"">
                <div class=""select_placeholder"">请选择</div>
                <div class=""select_list""></div>
                <input type=""text"" class=""box_input check_input"" name=""Select"" value="""" placeholder="""" size=""30"" maxlength=""255"" autocomplete=""off""/>
                <input type=""hidden"" name=""curtagId"" value="""" class=""hidden_value""/>
                <input type=""hidden"" name=""curtagIdType"" value="""" class=""hisdden_type""/>
            </dt>
            <dd class=""drop_down"">
                <div class=""drop_menu"" data-type=""Select"">
                    <a href=""javascript:;"" class=""btn_back"" data-value="""" data-type="""" data-table="""" data-top=""0"" data-all=""0"" style=""display:none;"">返回</a>
                    <div class=""drop_skin"" style=""display:none;""></div>
                    <div class=""drop_list"" data=""[{Name:33333333,Value:11,Type:products_tags},{Name:2222,Value:10,Type:products_tags},{Name:11,Value:9,Type:products_tags},{Name:Family,Value:7,Type:products_tags},{Name:Off-Road,Value:6,Type:products_tags},{Name:Camping1,Value:5,Type:products_tags},{Name:Motorcycling,Value:4,Type:products_tags},{Name:Sking,Value:3,Type:products_tags},{Name:Kayak,Value:2,Type:products_tags},{Name:Prepper,Value:1,Type:products_tags}]"" data-more=""none""></div>
                    <a href=""javascript:;"" class=""btn_load_more"" data-value=""products_tags"" data-type=""products_tags"" data-table=""products_tags"" data-top=""0"" data-all=""1"" data-check-all=""0"" data-start=""1"" style=""display:none;"">加载更多</a>
                </div>
            </dd>
        </dl>
    </div>
</div>
<div class=""rows i_select_more item clean"">
    <label>更多筛选</label>
    <div class=""input"">
        <dl class=""box_drop_double"" data-checkbox=""1"" data-showadd=""0"">
            <dt class=""box_checkbox_list"">
                <div class=""select_placeholder"">请选择</div>
                <div class=""select_list""></div>
                <input type=""text"" class=""box_input check_input"" name=""Select"" value="""" placeholder="""" size=""30"" maxlength=""255"" autocomplete=""off""/>
                <input type=""hidden"" name=""curmoreId"" value="""" class=""hidden_value""/>
                <input type=""hidden"" name=""curmoreIdType"" value="""" class=""hisdden_type""/>
            </dt>
            <dd class=""drop_down"">
                <div class=""drop_menu"" data-type=""Select"">
                    <a href=""javascript:;"" class=""btn_back"" data-value="""" data-type="""" data-table="""" data-top=""0"" data-all=""0"" style=""display:none;"">返回</a>
                    <div class=""drop_skin"" style=""display:none;""></div>
                    <div class=""drop_list"" data=""[{Name:新品,Value:new,Type:more_filter},{Name:畅销产品,Value:sales,Type:more_filter}]"" data-more=""none""></div>
                    <a href=""javascript:;"" class=""btn_load_more"" data-value=""more_filter"" data-type=""more_filter"" data-table=""more_filter"" data-top=""0"" data-all=""1"" data-check-all=""0"" data-start=""1"" style=""display:none;"">加载更多</a>
                </div>
            </dd>
        </dl>
    </div>
</div>
";

            //Json(new
            //{
            //    category = cateHtml,
            //    tags = tagsHtml
            //}, new JsonSerializerSettings { Formatting = Formatting.None });
            return Ok(new { ret = 1, msg = htmls });
        }



        /// <summary>
        /// 第一步中的数据放到session
        /// </summary>
        /// <returns></returns>
        [HttpPost("OrdersEnterSecond")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> OrdersEnterSecond()
        {
            try
            {


                var request = HttpContext.Request;
                var form = request.Form;
                var currency = form["Currency"].FirstOrDefault();
                var currencyLabel = "";

                // 处理货币信息
                if (!string.IsNullOrEmpty(currency))
                {
                    var currencyObj = await _orderListService.getCurrency(Convert.ToInt32(currency));

                    if (currencyObj != null)
                    {
                        currencyLabel = currencyObj.Currency;
                    }
                }

                // 收集所有产品ID
                var proIds = form["ProId[]"].ToList();
                var products = new List<Dictionary<string, object>>();
                int i = 0;
                foreach (var proId in proIds)
                {
                    //if (proId.Contains("Add_"))
                    //{

                    //}
                    //else
                    //{

                    //}

                    // 获取当前产品的所有属性键
                    var attrKeys = form.Keys
                    .Where(k => k.StartsWith($"Weight[{proId}]") || k.StartsWith($"Name[{proId}]") || k.StartsWith($"PicPath_0[{proId}]") || k.StartsWith($"Attr_{proId}[") || k == $"ProId[]" || k == $"Price[]" || k == $"Qty[]" || k == $"VariantsId[]")
                    .ToList();

                    // 提取产品数据
                    var productData = new Dictionary<string, object>
                {
                        { "ProId", proId },
                    { "Price",_helpsCartService.CeilPrice(decimal.Parse(form[$"Price[]"][i] ?? "0")) },
                    { "Qty", int.Parse(form[$"Qty[]"][i] ?? "0") },
                    { "VariantsId", form[$"VariantsId[]"][i] ?? "" }
                };

                    // 处理货币转换
                    if (!string.IsNullOrEmpty(currencyLabel))
                    {
                        productData["Price"] =
                            (decimal)productData["Price"];
                        //productData["Price"] = _helpsCartService.CurrencyFloatPrice(
                        //    (decimal)productData["Price"],
                        //    currencyLabel
                        //);
                    }

                    // 处理其他属性
                    foreach (var key in attrKeys)
                    {
                        if (key.StartsWith($"Attr_{proId}["))
                        {
                            // 提取属性索引和值
                            var match = System.Text.RegularExpressions.Regex.Match(key, $@"Attr_{proId}\[(\d+)\]");
                            if (match.Success)
                            {
                                var attrIndex = match.Groups[1].Value;
                                var attrValue = form[key].FirstOrDefault();

                                if (!string.IsNullOrEmpty(attrValue))
                                {
                                    productData[$"Attr_{proId}_{attrIndex}"] = attrValue;
                                }
                            }
                        }
                        else if (key == $"Weight[{proId}]")
                        {
                            var weightValue = form[key].FirstOrDefault();
                            if (!string.IsNullOrEmpty(weightValue))
                            {
                                productData["Weight"] = weightValue;
                            }
                        }
                        else if (key == $"Name[{proId}]")
                        {
                            var nameValue = form[key].FirstOrDefault();
                            if (!string.IsNullOrEmpty(nameValue))
                            {
                                productData["Name"] = nameValue;
                            }
                        }
                        else if (key == $"PicPath_0[{proId}]")
                        {
                            var picValue = form[key].FirstOrDefault();
                            if (!string.IsNullOrEmpty(picValue))
                            {
                                productData["PicPath_0"] = picValue;
                            }
                        }
                    }

                    products.Add(productData);
                    i++;
                }

                // 更新Session
                var orderCreate = HttpContext.Session.GetObject<Dictionary<string, object>>("OrderCreate")
                               ?? new Dictionary<string, object>();

                orderCreate["first"] = products;
                if (!string.IsNullOrEmpty(currency))
                {
                    orderCreate["currency"] = currency;
                }

                HttpContext.Session.SetObject("OrderCreate", orderCreate);

                return new JsonResult(new { Success = 1 });

            }
            catch (Exception e)
            {

                throw;
            }

        }

        #region


        //// 初始化结果容器
        //var products = new List<dynamic>();

        //// 获取所有产品ID（确保按顺序处理）
        //string[] proIds = Request.Form["ProId[]"].ToArray();
        //string[] prices = Request.Form["Price[]"].ToArray();
        //string[] qtys = Request.Form["Qty[]"].ToArray();
        //string[] variantsIds = Request.Form["VariantsId[]"].ToArray();

        //// 创建属性分组字典
        //var attrDict = new Dictionary<string, Dictionary<string, string>>();

        //foreach (string key in Request.Form.Keys)
        //{
        //    // 匹配属性参数（如 Attr_47[42]）
        //    if (key.StartsWith("Attr_"))
        //    {
        //        // 提取ProId和属性键（正则表达式解析）
        //        var match = Regex.Match(key, @"Attr_(\d+)\[(\d+)\]");
        //        if (match.Success)
        //        {
        //            string proId = match.Groups[1].Value;
        //            string attrKey = match.Groups[2].Value;
        //            string attrValue = Request.Form[key].ToString(); // 获取值

        //            // 初始化产品属性容器
        //            if (!attrDict.ContainsKey(proId))
        //            {
        //                attrDict[proId] = new Dictionary<string, string>();
        //            }

        //            // 存储属性键值对
        //            attrDict[proId][attrKey] = attrValue;
        //        }
        //    }
        //}

        //// 组合产品数据
        //for (int i = 0; i < proIds.Length; i++)
        //{
        //    string proId = proIds[i];
        //    string price = prices.ElementAtOrDefault(i) ?? "0";
        //    string qty = qtys.ElementAtOrDefault(i) ?? "0";
        //    string variantId = variantsIds.ElementAtOrDefault(i) ?? "";

        //    // 获取产品属性
        //    var attributes = attrDict.ContainsKey(proId)
        //        ? attrDict[proId]
        //        : new Dictionary<string, string>();

        //    // 构建产品对象
        //    products.Add(new
        //    {
        //        ProId = proId,
        //        Price = price,
        //        Quantity = qty,
        //        VariantId = variantId,
        //        Attributes = attributes
        //    });
        //}






        ////{ "msg":{ "Html":[],"ProId":[],"IsFirst":1},"ret":1}
        //return Ok(new { ret = 1, msg = "" });
        #endregion

        [HttpPost("OrdersGetCustomerAddress")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> OrdersGetCustomerAddress()
        {
            var id = Request.Form["id"].ObjToInt();
            var type = Request.Form["type"];
            List<user_address_book> user_Address_Books = new List<user_address_book>();
            if (id == 0)
            {
                return Ok(new { ret = 1, msg = new { data = user_Address_Books } });
            }
            user_Address_Books = await _orderListService.getuser_address_bookList(id);
            StringBuilder html = new StringBuilder();
            if (user_Address_Books != null && user_Address_Books.Count > 0)
            {
                int i = 0;
                foreach (var item in user_Address_Books)
                {
                    var countryData = await _orderListService.GetcountryBy(Convert.ToInt32(item.CId));
                    var Countrys = "";
                    if (countryData != null)
                    {
                        Countrys = countryData.Country;
                    }
                    html.AppendFormat(@" <span class=""input_radio_box input_radio_side_box tab_option {0}"" data-id=""{1}"">", i == 0 ? "checked" : "", item.AId);
                    html.AppendFormat(@"    <span class=""input_radio"">                                ");
                    html.AppendFormat(@"        <input type=""radio"" name=""Type"" value=""{0}""/>  ", item.AId);
                    html.AppendFormat(@"    </span>                                                       ");
                    html.AppendFormat(@"    <ul class=""fs14"">                                         ");
                    html.AppendFormat(@"        <li>                                                      ");
                    html.AppendFormat(@"            <strong>姓名:</strong>                                ");
                    html.AppendFormat(@"            <span>{0} {1}</span>                                      ", item.FirstName, item.LastName);
                    html.AppendFormat(@"        </li>                                                     ");
                    html.AppendFormat(@"        <li>                                                      ");
                    html.AppendFormat(@"            <strong>手机:</strong>                                ");
                    html.AppendFormat(@"            <span>{0}-{1}</span>                                ", item.CountryCode, item.PhoneNumber);
                    html.AppendFormat(@"        </li>                                                     ");
                    html.AppendFormat(@"        <li>                                                      ");
                    html.AppendFormat(@"            <strong>国家/地区:</strong>                            ");
                    html.AppendFormat(@"            <span>{0}</span>                                   ", Countrys);
                    html.AppendFormat(@"        </li>                                                     ");
                    html.AppendFormat(@"        <li>                                                      ");
                    html.AppendFormat(@"            <strong>州/省/地区:</strong>                           ");
                    html.AppendFormat(@"            <span>{0}</span>                                  ", item.State);
                    html.AppendFormat(@"        </li>                                                     ");
                    html.AppendFormat(@"        <li>                                                      ");
                    html.AppendFormat(@"            <strong>城市:</strong>                                ");
                    html.AppendFormat(@"            <span>{0}</span>                                        ", item.City);
                    html.AppendFormat(@"        </li>                                                     ");
                    html.AppendFormat(@"        <li>                                                      ");
                    html.AppendFormat(@"            <strong>街道:</strong>                                ");
                    html.AppendFormat(@"            <span>{0}</span>                                        ", item.AddressLine1);
                    html.AppendFormat(@"        </li>                                                     ");
                    html.AppendFormat(@"        <li>                                                      ");
                    html.AppendFormat(@"            <strong>寓所:</strong>                                ");
                    html.AppendFormat(@"            <span>{0}</span>                                    ", item.AddressLine2);
                    html.AppendFormat(@"        </li>                                                     ");
                    html.AppendFormat(@"        <li>                                                      ");
                    html.AppendFormat(@"            <strong>个人或增值税号:</strong>                        ");
                    html.AppendFormat(@"            <span>#Tax ID: {0}</span>                         ", item.TaxCode);
                    html.AppendFormat(@"        <li>                                                      ");
                    html.AppendFormat(@"            <strong>邮政编码:</strong>                             ");
                    html.AppendFormat(@"            <span>{0}</span>                                   ", item.ZipCode);
                    html.AppendFormat(@"        </li>                                                     ");
                    html.AppendFormat(@"    </ul>                                                         ");
                    html.AppendFormat(@"</span> ");
                    i++;
                }
            }

            return Ok(new { ret = 1, msg = new { data = user_Address_Books, html = html.ToString() } });
        }
        /// <summary>
        /// 第二部数据放到session中
        /// </summary>
        /// <returns></returns>
        [HttpPost("OrdersEnterThird")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> OrdersEnterThird()
        {
            try
            {


                // 获取表单数据
                var form = Request.Form;


                // 解析表单数据
                var countryId = int.TryParse(form["country_id"], out int countryIdValue) ? countryIdValue : 0;
                var province = int.TryParse(form["Province"], out int provinceValue) ? provinceValue : 0;
                var saveAddress = int.TryParse(form["SaveAddress"], out int saveAddressValue) ? saveAddressValue : 0;
                var pAdditionalInfoData = form.ContainsKey("AdditionalInfoData") ? form["AdditionalInfoData"].ToString() : "[]";

                var taxData = new
                {
                    CodeOption = int.TryParse(form["tax_code_type"], out int codeOptionValue) ? codeOptionValue : 0,
                    TaxCode = form["tax_code_value"].ToString().Trim()
                };

                var taxDataDict = new Dictionary<string, object>
                {
                    { "CodeOption", taxData.CodeOption },
                    { "TaxCode", taxData.TaxCode }
                };

                var taxAry = GetTaxInfo(taxDataDict);


                // 处理AdditionalInfoData
                var (additionalInfoData, additionalInfoName) = SaveAdditionalInfo(pAdditionalInfoData);



                // 构建数据对象
                var data = new
                {
                    Email = form["CustomerValue"].ToString().Trim(),
                    UserId = int.TryParse(form["Customer"], out int userIdValue) ? userIdValue : 0,
                    AddressId = int.TryParse(form["AddressId"], out int addressIdValue) ? addressIdValue : 0,
                    LastName = form["LastName"].ToString().Trim(),
                    FirstName = form["FirstName"].ToString().Trim(),
                    AddressLine1 = form["AddressLine1"].ToString().Trim(),
                    AddressLine2 = form["AddressLine2"].ToString().Trim(),
                    City = form["City"].ToString().Trim(),
                    Country = await GetCountry(countryId), // 假设有GetCountry方法
                    CountryCode = form["CountryCode"].ToString().Trim('+'),
                    CountryId = countryId,
                    Province = province,
                    State = form.ContainsKey("State") ? form["State"].ToString().Trim() : await GetState(province, countryId), // 假设有GetState方法
                    ZipCode = form["ZipCode"].ToString().Trim(),
                    PhoneNumber = form["PhoneNumber"].ToString().Trim(),
                    CodeOption = taxAry.CodeOption,
                    CodeOptionId = taxAry.CodeOptionId,
                    TaxCode = taxAry.TaxCode,
                    SaveAddress = saveAddress,
                    AdditionalInfoData = JsonConvert.SerializeObject(additionalInfoData),
                    AdditionalInfoName = JsonConvert.SerializeObject(additionalInfoName)
                };

                // 存储到Session
                var orderCreate = HttpContext.Session.GetString("OrderCreate") != null
                    ? JsonConvert.DeserializeObject<Dictionary<string, object>>(HttpContext.Session.GetString("OrderCreate"))
                    : new Dictionary<string, object>();

                orderCreate["second"] = data;
                HttpContext.Session.SetString("OrderCreate", JsonConvert.SerializeObject(orderCreate));

                return new JsonResult(new { Success = 1 });
            }
            catch (Exception e)
            {

                throw;
            }
        }

        #region
        //var CustomerValue = Request.Form["CustomerValue"];
        //var Customer = Request.Form["Customer"];
        //var CustomerType = Request.Form["CustomerType"];
        //var FirstName = Request.Form["FirstName"];
        //var LastName = Request.Form["LastName"];
        //var AddressLine1 = Request.Form["AddressLine1"];
        //var AddressLine2 = Request.Form["AddressLine2"];
        //var City = Request.Form["City"];
        //var country_id_input = Request.Form["country_id_input"];
        //var country_id = Request.Form["country_id"];
        //var country_idType = Request.Form["country_idType"];
        //var _DoubleOption = Request.Form["_DoubleOption[]"];
        //var Province = Request.Form["Province"];
        //var ZipCode = Request.Form["ZipCode"];
        //var CountryCode = Request.Form["CountryCode"];
        //var PhoneNumber = Request.Form["PhoneNumber"];
        //var tax_code_type = Request.Form["tax_code_type"];
        //var tax_code_value = Request.Form["tax_code_value"];
        //var SaveAddress = Request.Form["SaveAddress"];
        //var AddressId = Request.Form["AddressId"];

        ////CustomerValue   "<EMAIL>"
        ////Customer    "13"
        ////CustomerType    "customer"
        ////FirstName   "cheng"
        ////LastName    "kevin"
        ////AddressLine1    "aaa+222"
        ////AddressLine2    ""
        ////City    "aaa"
        ////country_id_input    "France"
        ////country_id  "74"
        ////country_idType  "country"
        ////_DoubleOption[] "75"
        ////Province    "1239"
        ////ZipCode "1103"
        ////CountryCode "+33"
        ////PhoneNumber "123456"
        ////tax_code_type   "6"
        ////tax_code_value  "1"
        ////SaveAddress "1"
        ////AddressId   ""


        //var b=await _orderListService.UpdateUser_address_book(CustomerValue,Customer,CustomerType,FirstName,LastName,
        //            AddressLine1,AddressLine2,City,country_id_input,country_id,country_idType,_DoubleOption,
        //            Province,ZipCode,CountryCode,PhoneNumber,tax_code_type,tax_code_value,SaveAddress,AddressId);
        //return Ok(new { ret = 1, msg ="" });
        #endregion
        public class TaxInfoResult
        {
            public int CodeOptionId { get; set; }
            public string CodeOption { get; set; }
            public string TaxCode { get; set; }
        }
        public static TaxInfoResult GetTaxInfo(Dictionary<string, object> row)
        {
            // 解析CodeOption（处理null并转为int）
            int codeOption = row.TryGetValue("CodeOption", out object codeOptionValue)
                             && codeOptionValue != null
                             ? Convert.ToInt32(codeOptionValue)
                             : 0;

            // 初始化返回对象
            var result = new TaxInfoResult
            {
                CodeOptionId = codeOption,
                CodeOption = "0",  // 默认值与PHP逻辑一致
                TaxCode = ""
            };

            // 仅当有效CodeOption时处理映射
            if (codeOption != 0)
            {
                // 税务代码映射表
                var taxCodeOptions = new Dictionary<int, string>
        {
            { 1, "CPF" },
            { 2, "CNPJ" },
            { 3, "ID" },
            { 4, "VAT ID" },
            { 5, "RUT" },
            { 6, "Tax ID" }
        };

                // 安全获取映射值
                result.CodeOption = taxCodeOptions.TryGetValue(codeOption, out string optionName)
                                    ? optionName
                                    : "0";

                // 安全获取税务代码
                result.TaxCode = row.TryGetValue("TaxCode", out object taxCodeValue)
                                 && taxCodeValue != null
                                 ? taxCodeValue.ToString().Trim()
                                 : "";
            }

            return result;
        }

        private (object, object) SaveAdditionalInfo(string pAdditionalInfoData)
        {
            // 这里实现保存附加信息的逻辑
            // 返回类似PHP中的list($AdditionalInfoData, $AdditionalInfoName)
            return (new { }, new { });
        }

        private async Task<string> GetCountry(int countryId)
        {
            var CountryData = await _countryService.GetCountryAsync(countryId);
            // 这里实现获取国家名称的逻辑
            return CountryData.Country; // 示例值
        }

        private async Task<string> GetState(int province, int countryId)
        {
            if (province == 0)
            {
                return "0";
            }
            var CountryData = await _countryService.GetProvinceAsync(province);
            // 这里实现获取州/省名称的逻辑
            if (CountryData == null)
            {
                return "0"; // 示例值
            }
            else
            {
                return CountryData.States; // 示例值
            }

        }


        [HttpPost("OrdersThirdShipping")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> OrdersThirdShipping()
        {
            // 1. 获取请求参数
            var packageId = HttpContext.Request.Form["packageId"].FirstOrDefault();

            // 2. 从Session获取订单创建数据
            var orderCreate = HttpContext.Session.GetString("OrderCreate") != null
               ? JsonConvert.DeserializeObject<Dictionary<string, object>>(HttpContext.Session.GetString("OrderCreate"))
               : new Dictionary<string, object>();

            var productsList = orderCreate.ContainsKey("first")
                ? JsonConvert.DeserializeObject<List<Dictionary<string, object>>>(orderCreate["first"].ToString())
                : new List<Dictionary<string, object>>();

            var orderAddress = orderCreate.ContainsKey("second")
                ? JsonConvert.DeserializeObject<Dictionary<string, object>>(orderCreate["second"].ToString())
                : new Dictionary<string, object>();
            var thirdData = orderCreate.ContainsKey("third")
                ? JsonConvert.DeserializeObject<Dictionary<string, object>>(orderCreate["third"].ToString())
                : new Dictionary<string, object>();
            var packageAry = JsonConvert.DeserializeObject<Dictionary<string, object>>(thirdData["package"].ToString());




            #region
            //// 3. 检查是否启用物流模板插件
            //var isShippingTemplate = _configService.IsPluginEnabled("shipping_template");

            //// 4. 获取地址信息
            //var countryId = orderAddress.GetValueOrDefault("CountryId")?.ToString().ObjToInt(0);
            //var statesSId = orderAddress.GetValueOrDefault("Province")?.ToString().ObjToInt(0);

            //// 5. 获取包裹产品信息
            //var productsInfo = packages.GetValueOrDefault(packageId.ToString()) as List<Dictionary<string, object>> ?? new List<Dictionary<string, object>>();
            //var proIdAry = productsInfo.Select(p => p.GetValueOrDefault("ProId")?.ToString().ObjToInt(0)).Where(id => id > 0).ToList();

            //// 6. 获取产品详细信息
            //var productsDataAry = new Dictionary<int, Product>();
            //if (proIdAry.Any())
            //{
            //    var products = await _orderListService.GetProductsByIds(proIdAry);
            //    productsDataAry = products?.ToDictionary(p => p.ProId);
            //}

            //// 7. 整合产品数据
            //var finalProducts = new List<OrderProduct>();
            //foreach (var productInfo in productsInfo)
            //{
            //    if (int.TryParse(productInfo["ProId"]?.ToString(), out var proId) &&
            //        productsDataAry.TryGetValue(proId, out var product))
            //    {
            //        finalProducts.Add(new OrderProduct
            //        {
            //            ProId = product.ProId,
            //            Name = product.Name,
            //            Qty = productInfo.GetValueOrDefault("Qty")?.ToString().ObjToInt(1),
            //            IsFreeShipping = product.IsFreeShipping,
            //            TId = product.TId,
            //            IsCombination = product.IsCombination
            //        });
            //    }
            //}

            //// 8. 获取用户免运费权益
            //var userId = orderAddress.GetValueOrDefault("UserId")?.ToString().ObjToInt(0);
            //var freeShipping = _userService.GetUserFreeShipping(userId);

            //// 9. 获取配送方式
            //var shippingResult = GetShippingMethod(new ShippingRequest
            //{
            //    CountryId = countryId,
            //    StateId = statesSId,
            //    Products = finalProducts,
            //    IsShippingTemplate = isShippingTemplate,
            //    FreeShipping = freeShipping
            //});

            //return Json(shippingResult);
            #endregion


            bool isShippingTemplate = (HttpContext.Session.GetString("plugins")?.Split(',') ?? new string[0]).Contains("shipping_template");
            // 获取国家省份信息
            var countryId = orderAddress["CountryId"]?.ObjToInt(0) ?? 0;
            var statesSId = orderAddress["Province"]?.ObjToInt(0) ?? 0;






            var ret = _orderListService.actionThirdShipping(packageId, orderCreate,
                  orderAddress, packageAry, isShippingTemplate, countryId, statesSId);

            return Ok(new { ret = true, msg = ret });








            //var shippingMethods = new Dictionary<string, object>
            //    {
            //        {
            //            "0-0", new[]
            //            {
            //                new
            //                {
            //                    SId = 35,
            //                    Name = "橙联",
            //                    Brief = "",
            //                    IsAPI = 0,
            //                    type = "",
            //                    weight = 0,
            //                    ShippingPrice = "8.05",
            //                    ApiType = "0"
            //                }
            //            }
            //        }
            //    };


            //return Ok(new { ret = true, msg = new { info = shippingMethods, shipping_template = 1 } });
        }


        [HttpPost("OrdersCreateOrder")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> OrdersCreateOrder()
        {

            try
            {
                // 获取表单数据
                var form = Request.Form;

                var shippingData = new Dictionary<int, (string Method, string Name, decimal Price)>();

                // 解析表单参数（增强版）
                foreach (var key in form.Keys)
                {
                    // 匹配三种可能的键格式（兼容不同表单生成方式）
                    var methodMatch = Regex.Match(key, @"ShippingMethod\[?(\d+)-(\d+)\]");
                    if (methodMatch.Success)
                    {
                        int row = int.Parse(methodMatch.Groups[1].Value);

                        // 优先使用严格格式参数
                        string methodValue = form[$"ShippingMethod[{row}-0]"];
                        string name = form[$"ShippingName[{row}-0]"];
                        string priceStr = form[$"ShippingPrice[{row}-0]"];

                        if (!decimal.TryParse(priceStr, out decimal price))
                            throw new Exception($"Invalid price format at row {row}: {priceStr}");

                        shippingData[row] = ($"{row}-{methodValue}", name, price);
                    }
                }

                // 按行号排序并构建数组
                var sortedRows = shippingData.OrderBy(kvp => kvp.Key).ToList();
                dynamic shippingAry = new ExpandoObject();
                shippingAry.ShippingMethod = sortedRows.Select(kvp => kvp.Value.Method).ToArray();
                shippingAry.ShippingName = sortedRows.Select(kvp => kvp.Value.Name).ToArray();
                shippingAry.ShippingPrice = sortedRows.Select(kvp => kvp.Value.Price).ToArray();
                //dynamic shippingAry = new ExpandoObject();
                #region
                //// 临时存储容器（使用字典保证顺序）
                //var methodDict = new Dictionary<int, string>();
                //var nameDict = new Dictionary<int, string>();
                //var priceDict = new Dictionary<int, decimal>();

                //// 正则表达式匹配索引模式
                //var pattern = @"Shipping(Method|Name|Price)\[(\d+)-(\d+)\]";
                //var regex = new Regex(pattern);

                //foreach (var key in form.Keys)
                //{
                //    var match = regex.Match(key);
                //    if (!match.Success) continue;

                //    int row = int.Parse(match.Groups[2].Value);
                //    int col = int.Parse(match.Groups[3].Value);
                //    string fieldType = match.Groups[1].Value.ToLower();

                //    // 保证二维数组结构（按行优先填充）
                //    while (row >= methodDict.Count)
                //    {
                //        methodDict.Add(row, "");
                //        nameDict.Add(row, "");
                //        priceDict.Add(row, 0);
                //    }

                //    // 根据字段类型赋值
                //    switch (fieldType)
                //    {
                //        case "method":
                //            methodDict[row] = form[key];
                //            break;
                //        case "name":
                //            nameDict[row] = form[key];
                //            break;
                //        case "price":
                //            priceDict[row] = decimal.Parse(form[key], CultureInfo.InvariantCulture);
                //            break;
                //    }
                //}

                //// 转换为数组并赋值给dynamic对象
                //shippingAry.ShippingMethod = methodDict.Values.ToArray();
                //shippingAry.ShippingName = nameDict.Values.ToArray();
                //shippingAry.ShippingPrice = priceDict.Values.ToArray();
                #endregion

                #region
                //var shipping = 0;
                //foreach (string key in Request.Form.Keys)
                //{
                //    if (key.StartsWith("ShippingMethod["))
                //    {
                //        shipping = shipping + 1;
                //    }
                //}


                //var shippingList = new List<Dictionary<string, object>>();
                //for (int i = 0; i < shipping; i++)
                //{
                //    var attrKeys = form.Keys
                //        .Where(k => k.StartsWith($"ShippingMethod[") || k.StartsWith($"ShippingName[") || k.StartsWith($"ShippingPrice["))
                //        .ToList();

                //    // 提取产品数据
                //    var shippingData = new Dictionary<string, object>
                //    {
                //        //{ "ProId", proId },
                //        //{ "Price", HelpsCart.CeilPrice(float.Parse(form[$"Price[]"][i] ?? "0")) },
                //        //{ "Qty", int.Parse(form[$"Qty[]"][i] ?? "0") },
                //        //{ "VariantsId", form[$"VariantsId[]"][i] ?? "" }
                //    };

                //    // 处理其他属性
                //    foreach (var key in attrKeys)
                //    {
                //        if (key.StartsWith("ShippingMethod["))
                //        {
                //            // 提取属性索引和值
                //            var match = System.Text.RegularExpressions.Regex.Match(key, $@"ShippingMethod\[(\d+)\]");
                //            if (match.Success)
                //            {
                //                var attrIndex = match.Groups[1].Value;
                //                var attrValue = form[key].FirstOrDefault();

                //                if (!string.IsNullOrEmpty(attrValue))
                //                {
                //                    shippingData[$"{attrIndex}"] = attrValue;
                //                }
                //            }
                //        }
                //        else if (key.StartsWith("ShippingName["))
                //        {
                //            // 提取属性索引和值
                //            var match = System.Text.RegularExpressions.Regex.Match(key, $@"ShippingName\[(\d+)\]");
                //            if (match.Success)
                //            {
                //                var attrIndex = match.Groups[1].Value;
                //                var attrValue = form[key].FirstOrDefault();

                //                if (!string.IsNullOrEmpty(attrValue))
                //                {
                //                    shippingData[$"{attrIndex}"] = attrValue;
                //                }
                //            }
                //        }
                //        else if (key.StartsWith("ShippingPrice["))
                //        {
                //            // 提取属性索引和值
                //            var match = System.Text.RegularExpressions.Regex.Match(key, $@"ShippingPrice\[(\d+)\]");
                //            if (match.Success)
                //            {
                //                var attrIndex = match.Groups[1].Value;
                //                var attrValue = form[key].FirstOrDefault();

                //                if (!string.IsNullOrEmpty(attrValue))
                //                {
                //                    shippingData[$"{attrIndex}"] = attrValue;
                //                }
                //            }
                //        }

                //    }
                //    shippingList.Add(shippingData);
                //}
                #endregion

                var shippingMethodTwo = new List<string>();
                var ShippingNameTwo = new List<string>();
                var ShippingPriceTwo = new List<string>();
                foreach (string key in Request.Form.Keys)
                {
                    if (key.StartsWith("ShippingMethod["))
                    {
                        string value = Request.Form[key];
                        shippingMethodTwo.Add(value);
                    }
                    if (key.StartsWith("ShippingName["))
                    {
                        string value = Request.Form[key];
                        ShippingNameTwo.Add(value);
                    }
                    if (key.StartsWith("ShippingPrice["))
                    {
                        string value = Request.Form[key];
                        ShippingPriceTwo.Add(value);
                    }
                }


                // 订单信息
                var orderCreate = HttpContext.Session.GetString("OrderCreate") != null
                  ? JsonConvert.DeserializeObject<Dictionary<string, object>>(HttpContext.Session.GetString("OrderCreate"))
                  : new Dictionary<string, object>();

                var productsList = orderCreate.ContainsKey("first")
                    ? JsonConvert.DeserializeObject<List<Dictionary<string, object>>>(orderCreate["first"].ToString())
                    : new List<Dictionary<string, object>>();

                var orderAddress = orderCreate.ContainsKey("second")
                    ? JsonConvert.DeserializeObject<Dictionary<string, object>>(orderCreate["second"].ToString())
                    : new Dictionary<string, object>();
                var thirdData = orderCreate.ContainsKey("third")
                    ? JsonConvert.DeserializeObject<Dictionary<string, object>>(orderCreate["third"].ToString())
                    : new Dictionary<string, object>();
                var ProductsQty = Convert.ToInt32(thirdData["totalQty"]);
                var ProductsPrice = Convert.ToDecimal(thirdData["totalPrice"]);
                var RateProductsPrice = Convert.ToDecimal(thirdData["rateTotalPrice"]);
                var packageAry = JsonConvert.DeserializeObject<Dictionary<string, object>>(thirdData["package"].ToString());

                //var orderAddress = orderCreate.ContainsKey("second") ? (orderCreate["second"] as Dictionary<string, object>) ?? new Dictionary<string, object>() : new Dictionary<string, object>();
                //var packageAry = orderCreate.ContainsKey("third") ? (Convert.ToBoolean((orderCreate["third"] as Dictionary<string, object>)?.ContainsKey("package")) ?
                //(orderCreate["third"] as Dictionary<string, object>)["package"] as List<Dictionary<string, object>> : new List<Dictionary<string, object>>()) : new List<Dictionary<string, object>>();

                //var thirdData = orderCreate.ContainsKey("third")
                //   ? JsonConvert.DeserializeObject<Dictionary<string, object>>(orderCreate["third"].ToString())
                //   : new Dictionary<string, object>();

                //// 创建订单号
                //string OId = HelpsCart.CreateOrderNumber("orders");

                // 会员信息
                var userEmail = orderAddress["Email"].ToString();
                var dataUser = await _orderListService.GetUserByEmailAsync(userEmail);

                //var dataUser = CreateOrderUserInfo();
                //if (dataUser == null)
                //{
                //    return Ok(new { ret = false, msg = new { content = "" } });
                //    //return Json(new { success = false, message = "" });
                //}

                // 订单折扣
                decimal Discount = 0, DiscountPrice = 0;
                int DiscountType = int.TryParse(form["DiscountType"], out int discountType) ? discountType : 0;

                if (DiscountType == 1)
                {
                    Discount = decimal.TryParse(form["Discount"], out decimal discount) ? discount : 0;
                }
                else
                {
                    DiscountPrice = decimal.TryParse(form["DiscountPrice"], out decimal discountPrice) ? discountPrice : 0;
                }

                // 产品信息
                decimal ProductPrice = 0;
                decimal TotalWeight = 0;

                foreach (var package in packageAry)
                {
                    //if (package is Dictionary<string, object> packageDict)
                    //{
                    //    foreach (var item in packageDict.Values)
                    //    {
                    //        if (item is Dictionary<string, object> itemDict)
                    //        {
                    //            if (decimal.TryParse(itemDict["Price"]?.ToString(), out decimal price) &&
                    //                int.TryParse(itemDict["Qty"]?.ToString(), out int qty))
                    //            {
                    //                ProductPrice += HelpsCart.CeilPrice(price * qty);
                    //            }
                    //            if (decimal.TryParse(itemDict["Weight"]?.ToString(), out decimal weight) &&
                    //                int.TryParse(itemDict["Qty"]?.ToString(), out qty))
                    //            {
                    //                ProductPrice += weight * qty;
                    //            }
                    //        }
                    //    }
                    //}
                    var items = JsonConvert.DeserializeObject<List<ProductItemTwo>>(package.Value.ToString());
                    foreach (var item in items)
                    {
                        decimal price = decimal.TryParse(item.Price.ToString(), out decimal p) ? p : 0;
                        int qty = int.TryParse(item.Qty.ToString(), out int quantity) ? quantity : 0;
                        ProductPrice += _helpsCartService.CeilPrice(price * qty);
                        TotalWeight += (decimal.TryParse(item.Weight.ToString(), out decimal weight) ? weight : 0) * qty;
                    }


                    //foreach (var item in package.Value)
                    //{
                    //    decimal price = decimal.TryParse(item["Price"].ToString(), out decimal p) ? p : 0;
                    //    int qty = int.TryParse(item["Qty"].ToString(), out int quantity) ? quantity : 0;
                    //    ProductPrice += HelpsCart.CeilPrice(price * qty);
                    //    TotalWeight += (decimal.TryParse(item["Weight"].ToString(), out decimal weight) ? weight : 0) * qty;
                    //}
                }

                // 运费
                decimal ShippingPrice = 0;
                var shippingPrices = ShippingPriceTwo;
                foreach (var price in shippingPrices)
                {
                    ShippingPrice += float.TryParse(price, out float shippingPrice) ? (decimal)shippingPrice : 0;
                }
                ShippingPrice = _helpsCartService.CeilPrice(ShippingPrice);

                // 当前货币
                //var currencyManage = Currency.GetValue(new Dictionary<string, object>
                //{
                //    { "IsUsed", 1 },
                //    { "ManageDefault", 1 }
                //}, "Currency");




                var usedCurrencyRow = await _orderListService.getCurrencyList();
                //var currencyResult = OrderCurrency1();
                //var currency = (orderCreate as Dictionary<string, object>).TryGetValue("currency", out object currencyValue) ? (int?)currencyValue : null;

                var currency = orderCreate.ContainsKey("currency")
                    ? Convert.ToInt32(orderCreate["currency"])
                    : 1;
                var currencyManage = await _settingBasisService.GetCurrencyManageDefaultAry();
                var usedCurrencyAry = OrderCurrency(usedCurrencyRow, currency);
                var currentCurrecy = OrderCurrencyTwo(usedCurrencyRow, currency);
                var manageDefaultCurrency = OrderCurrencyThree(usedCurrencyRow, currency);

                //var usedCurrencyAry = currencyResult.Item1;
                //var currentCurrecy = currencyResult.Item2;
                //var manageDefaultCurrency = currencyResult.Item3;

                var rateCurrency = usedCurrencyAry.ContainsKey(currentCurrecy) ? usedCurrencyAry[currentCurrecy].Currency?.ToString() : currencyManage?.Currency;
                var rate = usedCurrencyAry.ContainsKey(currentCurrecy) ? (decimal?)usedCurrencyAry[currentCurrecy].Rate : 1;
                rate= Math.Round(rate.Value / currencyManage.Rate.Value, 4);
                // 运费模板
                bool isShippingTemplate = (HttpContext.Session.GetString("plugins")?.Split(',') ?? new string[0]).Contains("shipping_template");

                // 税率
                int CountryId = int.TryParse(orderAddress["CountryId"]?.ToString(), out int countryId) ? countryId : 0;
                var countryRow = await _orderListService.GetcountryBy(CountryId);
                int Province = int.TryParse(orderAddress["Province"]?.ToString(), out int province) ? province : 0;
                var provinceRow = await _orderListService.GetCountryStatesByid(Province);


                var taxAry = new CreatetaxAry
                {
                    taxType = int.TryParse(HttpContext.Session.GetString("config_global_TaxType"), out int taxType) ? taxType : 0,
                    taxThreshold = countryRow.TaxThreshold,
                    tax = decimal.TryParse(provinceRow?.Tax.ToString() ?? countryRow.Tax.ToString(), out decimal tax) ? tax : 0
                };

                int orderUserId = 0;
                int SalesId = 0;
                if (dataUser != null)
                {
                    orderUserId = dataUser.UserId;
                    SalesId = Convert.ToInt32(dataUser.SalesId);
                }
                int userIsTaxExempt = orderUserId > 0 ? _helpsUserService.CountCustomerLabel(orderUserId, "system", "taxExempt") : 0;

                if (userIsTaxExempt > 0)
                {
                    taxAry.tax = 0;
                }

                if (taxAry.tax > 0)
                {
                    if (taxAry.taxType > 0)
                    {
                        if ((ProductPrice + ShippingPrice) < taxAry.taxThreshold)
                        {
                            taxAry.tax = 0;
                        }
                    }
                    else
                    {
                        if (ProductPrice < taxAry.taxThreshold)
                        {
                            taxAry.tax = 0;
                        }
                    }
                }

                // 支付方式
                int PId = int.TryParse(form["PId"], out int pId) ? pId : 0;

                var paymentRow = await _orderListService.GetpaymentByid(PId);
                string paymentName = paymentRow.Name_en?.ToString() ?? "";

                if (new[] { "PaypalCreditCard", "PaypalApplePay", "PaypalGooglePay" }.Contains(paymentRow.Method?.ToString()))
                {
                    paymentRow = await _orderListService.GetpaymentByid(1);
                }

                // 支付凭证图片
                string paymentStatus = form["PaymentStatus"].FirstOrDefault() ?? "unpaid";
                string picPath = form["PicPath_0"].FirstOrDefault() ?? "";
                string type = form["Type"].FirstOrDefault() ?? "picture";

                if (paymentStatus == "paid" && paymentRow.IsOnline.ToString() == "0" && PId != 9 && type == "picture" && string.IsNullOrEmpty(picPath))
                {
                    return Ok(new { ret = false, msg = new { content = "" } });
                }
                //DateTime now = DateTime.Now;
                //string dateTimeFormat = now.ToString("yyyyMMddHHmmss");
                //Random random = new Random();
                //int randomNumber = random.Next(0, 100); 
                //string randomNumberStr = randomNumber.ToString("D2"); 
                //string result = dateTimeFormat + randomNumberStr;
                string result = YseStore.Common.Helper.OrderNumberGenerator.Generate();
                var orderPrefix = await _orderListService.GetOrderPrefix();

                 // 订单数据
                 var orderData = new orders
                {
                    OId = orderPrefix+result,
                    UserId = orderUserId,
                    SalesId = SalesId,
                    Source = 0,
                    RefererId = 0,
                    RefererName = "手动创建",
                    ChannelId = 0,
                    IsNewCustom = false,
                    Email = orderAddress["Email"].ToString(),
                    Discount = Discount,
                    DiscountPrice = DiscountPrice,
                    UserDiscount = 0,
                    DiscountType = "common",
                    ProductPrice = decimal.Parse(Math.Round(ProductPrice, 2).ToString("0.00")),
                    ShippingInsurancePrice = 0,
                    Currency = rateCurrency == "" ? "USD" : rateCurrency,
                    ManageCurrency = currencyManage.Currency,
                    //Currency = "USD" ,
                    //ManageCurrency = "USD",
                    Rate = Convert.ToDecimal(rate),
                    Tax = taxAry.tax,
                    TaxType = (sbyte)taxAry.taxType,
                    TotalWeight = TotalWeight,
                    TotalVolume = 0,
                    OrderTime = YseStore.Common.Helper.DateTimeHelper.ConvertToUnixTimestamp(DateTime.Now),
                    UpdateTime = YseStore.Common.Helper.DateTimeHelper.ConvertToUnixTimestamp(DateTime.Now),
                    Note = "",
                    DISTInfo = "",
                    IP = HttpContext.Connection.RemoteIpAddress?.ToString(),
                    CouponCode = "",
                    CouponPrice = 0m,
                    CouponDiscount = 0m,
                    ShippingFirstName = orderAddress["FirstName"].ToString(),
                    ShippingLastName = orderAddress["LastName"].ToString(),
                    ShippingAddressLine1 = orderAddress["AddressLine1"].ToString(),
                    ShippingAddressLine2 = orderAddress["AddressLine2"].ToString(),
                    ShippingCountryCode = "+" + orderAddress["CountryCode"].ToString(),
                    ShippingPhoneNumber = orderAddress["PhoneNumber"].ToString(),
                    ShippingCity = orderAddress["City"].ToString(),
                    ShippingState = orderAddress["State"].ToString(),
                    ShippingSId = Convert.ToInt32(shippingMethodTwo.FirstOrDefault()),
                    ShippingCountry = orderAddress["Country"].ToString(),
                    ShippingCId = Province,
                    ShippingZipCode = orderAddress["ZipCode"].ToString(),
                    ShippingCodeOption = orderAddress["CodeOption"].ToString(),
                    ShippingCodeOptionId = Convert.ToInt32(orderAddress["CodeOptionId"]),
                    ShippingTaxCode = orderAddress["TaxCode"].ToString(),
                    BillFirstName = orderAddress["FirstName"].ToString(),
                    BillLastName = orderAddress["LastName"].ToString(),
                    BillAddressLine1 = orderAddress["AddressLine1"].ToString(),
                    BillAddressLine2 = orderAddress["AddressLine2"].ToString(),
                    BillCountryCode = "+" + orderAddress["CountryCode"].ToString(),
                    BillPhoneNumber = orderAddress["PhoneNumber"].ToString(),
                    BillCity = orderAddress["City"].ToString(),
                    BillState = orderAddress["State"].ToString(),
                    BillSId = Convert.ToInt32(shippingMethodTwo.FirstOrDefault()),
                    BillCountry = orderAddress["Country"].ToString(),
                    BillCId = Province,
                    BillZipCode = orderAddress["ZipCode"].ToString(),
                    BillCodeOption = orderAddress["CodeOption"].ToString(),
                    BillCodeOptionId = Convert.ToSByte(orderAddress["CodeOptionId"]),
                    BillTaxCode = orderAddress["TaxCode"].ToString(),
                    PaymentId = paymentStatus == "paid" ? PId.ToString() : "0",

                    OrderStatus = paymentStatus == "paid" ? (int)OrderStatusEnum.已付款 : (int)OrderStatusEnum.等待付款,
                    PaymentStatus = paymentStatus,
                    ShippingStatus = ShippingStatusEnum.未发货.GetDescription(),
                    //TradeStatus = paymentStatus == "paid" ? TradeStatusEnum.已完成.GetDescription() : TradeStatusEnum.待处理.GetDescription(),
                    TradeStatus = "",
                    CutPay = false,
                    CutStock = true,
                    CutUser = false,
                    CutSuccess = false,
                    AsiaflyStatus = false,
                    CutCancel = false,
                    CutDIST = false,
                    IsTaxExempt = false,
                    isVirtual = false,
                    IsPerformance = true,
                    StoreSource = "",
                    Points = 0,
                    PointsPrice = 0,
                    CouponSource = "system",
                    CouponSourceId = 0,

                    ShippingPrice = ShippingPrice,
                    //shipping_template = isShippingTemplate ? (sbyte)1 : (sbyte)0,
                    shipping_template = (sbyte)1,
                    PId = paymentStatus == "paid" ? Convert.ToInt16(PId) : Convert.ToInt16(1),

                    PaymentMethod = paymentName,
                    PayAdditionalFee = decimal.TryParse(paymentRow.AdditionalFee.ToString(), out decimal additionalFee) ? additionalFee : 0,
                    PayAdditionalAffix = 0,
                    Type = "custom",
                    AdditionalInfoData = orderAddress["AdditionalInfoData"].ToString(),
                    AdditionalInfoName = orderAddress["AdditionalInfoName"].ToString(),
                    OrderOrPayTime= YseStore.Common.Helper.DateTimeHelper.ConvertToUnixTimestamp(DateTime.Now),
                    SyncRedisStatus=0
                };

                #region
                //var data = new
                //{
                //    Email = form["CustomerValue"].ToString().Trim(),
                //    UserId = int.TryParse(form["Customer"], out int userIdValue) ? userIdValue : 0,
                //    AddressId = int.TryParse(form["AddressId"], out int addressIdValue) ? addressIdValue : 0,
                //    LastName = form["LastName"].ToString().Trim(),
                //    FirstName = form["FirstName"].ToString().Trim(),
                //    AddressLine1 = form["AddressLine1"].ToString().Trim(),
                //    AddressLine2 = form["AddressLine2"].ToString().Trim(),
                //    City = form["City"].ToString().Trim(),
                //    Country = GetCountry(countryId), // 假设有GetCountry方法
                //    CountryCode = form["CountryCode"].ToString().Trim('+'),
                //    CountryId = countryId,
                //    Province = province,
                //    State = form.ContainsKey("State") ? form["State"].ToString().Trim() : GetState(province, countryId), // 假设有GetState方法
                //    ZipCode = form["ZipCode"].ToString().Trim(),
                //    PhoneNumber = form["PhoneNumber"].ToString().Trim(),
                //    //CodeOption = taxAry.CodeOption,
                //    //CodeOptionId = taxAry.CodeOptionId,
                //    //TaxCode = taxAry.TaxCode,
                //    SaveAddress = saveAddress,
                //    AdditionalInfoData = JsonConvert.SerializeObject(additionalInfoData),
                //    AdditionalInfoName = JsonConvert.SerializeObject(additionalInfoName)
                //};
                #endregion
                // 保存订单
                //var order = new Orders();
                // 这里需要实现将匿名对象映射到Orders实体类的逻辑
                // 可以使用AutoMapper等映射工具
                // MapToEntity(orderData, order);



                // 假设有保存方法
                // _orderRepository.Save(order);


                int OrderId = await _orderListService.AddOrder(orderData); // 假设保存后返回了OrderId

                await _orderListService.CreateOrdersPackageAsync(OrderId, packageAry, isShippingTemplate,
                    shippingMethodTwo, ShippingNameTwo, ShippingPriceTwo, shippingAry);


                //        Task CreateOrdersPackageAsync(int orderId, dynamic packageAry, bool isShippingTemplate,
                //string ShippingMethod, string ShippingName, string ShippingPrice);


                // 分配会员
                _orderListService.AssignMember(orderData, orderAddress);

                // 创建包裹
                //CreateOrdersPackage(OrderId, packageAry, isShippingTemplate);

                // 记录日志、发送邮件、减库存
                _orderListService.CreateOrderLog(OrderId, orderData);


                HttpContext.Session.Remove("OrderCreate");

                _helpsManageService.OperationLog("手动创建订单", result);
                return Ok(new { ret = true, msg = new { content = "创建成功", jump = "/Orders/Detail?id=" + OrderId + "" } });
            }
            catch (Exception ex)
            {
                return Ok(new { ret = false, msg = new { content = ex.Message } });
            }

        }




        [HttpPost("OrdersAddCustomizeProducts")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> OrdersAddCustomizeProducts()
        {
            var Name = Request.Form["Name"];
            var ordersPicPath = Request.Form["ordersPicPath"];
            var Price = Request.Form["Price"];
            var Qty = Request.Form["Qty"];
            var Weight = Request.Form["Weight"];
            var ProId = Request.Form["ProId"];

            //Name     产品名称
            //ordersPicPath  u_file / 2503 / 17 / photo / blogabout.jpg
            //Price    2.00
            //Qty      12
            //Weight   3
            //ProId    Add_1748911451976
            //do_action
            /// manage / orders / orders / add - customize - products
            StringBuilder html = new StringBuilder();

            html.AppendFormat(@"<tr data-id=""{0}"" data-price=""{1}"" data-fixed-price=""{2}"" data-fixed-stock=""1"" data-single=""1"" data-variants="""">", ProId, Price, Price);
            html.AppendFormat(@" <td class=""img"">  ");
            html.AppendFormat(@"        <div class=""img_box"">        ");
            html.AppendFormat(@"            <a href=""javascript:;"" class=""pic_box"">");
            html.AppendFormat(@"                <img src=""{0}"" data-src=""{1}"" class=""loading_img""/>", ordersPicPath, ordersPicPath);
            html.AppendFormat(@"                <span></span>");
            html.AppendFormat(@"            </a>       ");
            html.AppendFormat(@"            <div class=""info"">");
            html.AppendFormat(@"                                <a href=""javascript:;"" class=""name"">{0}</a>         ", Name);
            html.AppendFormat(@"            </div> ");
            html.AppendFormat(@"        </div>");
            html.AppendFormat(@"    </td>");
            html.AppendFormat(@"    <td nowrap=""nowrap"">");
            html.AppendFormat(@"        <div class=""h54"">    ");
            html.AppendFormat(@"            <span class=""unit_input Price_1"">");
            html.AppendFormat(@"                <b>$</b>");
            html.AppendFormat(@"                <input type=""text"" class=""box_input left_radius"" autocomplete=""off"" name=""Price[]"" value=""{0}"" size=""3"" maxlength=""10"" rel=""amount""/>", Price);
            html.AppendFormat(@"            </span>");
            html.AppendFormat(@"                        <div class=""rate_price""></div>");
            html.AppendFormat(@"        </div>");
            html.AppendFormat(@"    </td>");
            html.AppendFormat(@"    <td nowrap=""nowrap"">");
            html.AppendFormat(@"        <div class=""h54"">  ");
            html.AppendFormat(@"            <div class=""qty_box"">");
            html.AppendFormat(@"                <input type=""text"" class=""box_input qty"" autocomplete=""off"" name=""Qty[]"" value=""{0}"" size=""4"" maxlength=""7"" rel=""int"">", Qty);
            html.AppendFormat(@"            </div>       ");
            html.AppendFormat(@"        </div>");
            html.AppendFormat(@"    </td>");
            html.AppendFormat(@"    <td nowrap=""nowrap"">     ");
            html.AppendFormat(@"        <span class=""itemTotal"">");
            html.AppendFormat(@"            $<span>{0}</span>", Convert.ToDecimal(Price) * Convert.ToInt32(Qty));
            html.AppendFormat(@"        </span>");
            html.AppendFormat(@"                <div class=""rate_total_price""></div>");
            html.AppendFormat(@"    </td>");
            html.AppendFormat(@"    <td nowrap=""nowrap"" class=""operation"">");
            html.AppendFormat(@"                <a href=""javascript:;"" class=""oper_icon btn_edit_customize_products icon_revise"">修改</a>");
            html.AppendFormat(@"                <a href=""javascript:;"" class=""oper_icon icon_del icon_delete del"">删除</a>");
            html.AppendFormat(@"        <div class=""fixed_attribute"">");
            html.AppendFormat(@"                        <div class=""option_list""></div>");
            html.AppendFormat(@"                        <div class=""no_attribute"">没有规格可选</div>");
            html.AppendFormat(@"                        <input type=""hidden"" name=""VariantsId[]"" value="""" class=""variants_id""/>");
            html.AppendFormat(@"                        <input type=""hidden"" class=""ext_attr"" value="""" data-combination=""0"" data-soldstatus=""1"" disabled/>");
            html.AppendFormat(@"                        <input type=""hidden"" name=""ProId[]"" value=""{0}"">", ProId);
            html.AppendFormat(@"                        <input type=""hidden"" name=""Weight[{0}]"" value=""{1}"">", ProId, Weight);
            html.AppendFormat(@"                        <input type=""hidden"" name=""Name[{0}]"" value=""{1}"">", ProId, Name);
            html.AppendFormat(@"                        <input type=""hidden"" name=""PicPath_0[{0}]"" value=""{1}""> ", ProId, ordersPicPath);
            html.AppendFormat(@"        </div>");
            html.AppendFormat(@"    </td>");
            html.AppendFormat(@"</tr>");


            //var OrdersModAddress = await _orderListService.OrdersModAddress(FirstName, LastName, AddressLine1, AddressLine2, City, country_id_input, country_id, country_idType
            //    , _DoubleOption, Province, ZipCode, CountryCode, PhoneNumber, tax_code_type, tax_code_value, OrderId, Type);
            return Ok(new { ret = 1, msg = new { Html = html.ToString(), ProId = ProId } });
        }










        public Dictionary<int, currency> OrderCurrency(List<currency> usedCurrencyRow, int? currency, string queryCurrency = "")
        {
            // 选中的货币
            //var orderCreate = SessionManager.Get("OrderCreate", new Dictionary<string, object>());
            //var currency = (orderCreate as Dictionary<string, object>).TryGetValue("currency", out object currencyValue) ? (int?)currencyValue : null;

            // 数据库查询

            var usedCIdAry = usedCurrencyRow.Select(c => c.CId).ToList();
            var usedCurrencyAry = usedCurrencyRow.ToDictionary(c => c.CId);

            int manageDefaultCurrency = 0;
            int currentCurrency = 0;

            foreach (var v in usedCurrencyRow)
            {
                if (Convert.ToBoolean(v.ManageDefault)) manageDefaultCurrency = v.CId; // 默认后台货币
                if (Convert.ToBoolean(v.IsDefault)) currentCurrency = v.CId; // 默认前台货币
                if (!string.IsNullOrEmpty(queryCurrency) && v.Currency == queryCurrency) currency = v.CId;
            }

            currentCurrency = currentCurrency != 0 ? currentCurrency : manageDefaultCurrency; // 没有前台默认就使用后台默认货币
            if (currency.HasValue && usedCIdAry.Contains(currency.Value)) currentCurrency = currency.Value; // 优先用选中的

            return (usedCurrencyAry);
        }
        public int OrderCurrencyTwo(List<currency> usedCurrencyRow, int? currency, string queryCurrency = "")
        {
            // 选中的货币
            //var orderCreate = SessionManager.Get("OrderCreate", new Dictionary<string, object>());
            //var currency = (orderCreate as Dictionary<string, object>).TryGetValue("currency", out object currencyValue) ? (int?)currencyValue : null;

            // 数据库查询

            var usedCIdAry = usedCurrencyRow.Select(c => c.CId).ToList();
            var usedCurrencyAry = usedCurrencyRow.ToDictionary(c => c.CId);

            int manageDefaultCurrency = 0;
            int currentCurrency = 0;

            foreach (var v in usedCurrencyRow)
            {
                if (Convert.ToBoolean(v.ManageDefault)) manageDefaultCurrency = v.CId; // 默认后台货币
                if (Convert.ToBoolean(v.IsDefault)) currentCurrency = v.CId; // 默认前台货币
                if (!string.IsNullOrEmpty(queryCurrency) && v.Currency == queryCurrency) currency = v.CId;
            }

            currentCurrency = currentCurrency != 0 ? currentCurrency : manageDefaultCurrency; // 没有前台默认就使用后台默认货币
            if (currency.HasValue && usedCIdAry.Contains(currency.Value)) currentCurrency = currency.Value; // 优先用选中的

            return currentCurrency;
        }
        public int OrderCurrencyThree(List<currency> usedCurrencyRow, int? currency, string queryCurrency = "")
        {
            // 选中的货币
            //var orderCreate = SessionManager.Get("OrderCreate", new Dictionary<string, object>());
            //var currency = (orderCreate as Dictionary<string, object>).TryGetValue("currency", out object currencyValue) ? (int?)currencyValue : null;

            // 模拟数据库查询

            var usedCIdAry = usedCurrencyRow.Select(c => c.CId).ToList();
            var usedCurrencyAry = usedCurrencyRow.ToDictionary(c => c.CId);

            int manageDefaultCurrency = 0;
            int currentCurrency = 0;

            foreach (var v in usedCurrencyRow)
            {
                if (Convert.ToBoolean(v.ManageDefault)) manageDefaultCurrency = v.CId; // 默认后台货币
                if (Convert.ToBoolean(v.IsDefault)) currentCurrency = v.CId; // 默认前台货币
                if (!string.IsNullOrEmpty(queryCurrency) && v.Currency == queryCurrency) currency = v.CId;
            }

            currentCurrency = currentCurrency != 0 ? currentCurrency : manageDefaultCurrency; // 没有前台默认就使用后台默认货币
            if (currency.HasValue && usedCIdAry.Contains(currency.Value)) currentCurrency = currency.Value; // 优先用选中的

            return manageDefaultCurrency;
        }


        [HttpPost("CheckDropDouble")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> CheckDropDouble()
        {
            try
            {
                // 1. 读取原始请求体
                using var reader = new StreamReader(Request.Body);
                var body = await reader.ReadToEndAsync();

                // 2. 反序列化为模型对象
                var model = System.Text.Json.JsonSerializer.Deserialize<CheckDropDoubleModel>(body);
                //var dataItems = System.Text.Json.JsonSerializer.Deserialize<List<DataItem>>(model.Data);


                var keyword = model.Keyword;

                //var form = Request.Form;
                //var checkbox = form["Checkbox"];
                //var data = form["Data"]; // 需要自行解析数组
                //var action = form["do_action"];
                //var isShowAdd = form["isShowAdd"];
                //var keyword = form["Keyword"];
                //var position = form["position"];

                StringBuilder html = new StringBuilder();
                if (model.Data[0].Type== "products")
                {
                    var productArr = await _salesCouponService.GetProductsRowAsync("products", page: 1, keyword: keyword);
                    foreach (var product in productArr)
                    {

                        var iconHtml = $"<em class='icon icon_products pic_box'><img src='{product.Icon}' /><span></span></em>";

                        html.Append(HtmlCategoryBuilder.BoxDropDoubleOption(
                            name: product.Name,
                            value: product.Value.ToString(),
                            type: "products",
                            table: "products",
                            checkbox: int.Parse(model.Checkbox),
                            topType: "",
                            iconTxt: iconHtml
                        ));
                    }
                    return Ok(new { ret = 1, msg = new { Html = html.ToString(), Count = 1, dataCount = productArr.Count } });
                }
                

                var userList = await _orderListService.GetUserByEmail(keyword);
                if (userList != null && userList.Count > 0)
                {
                    foreach (var item in userList)
                    {
                        html.AppendFormat(@" <div class=""item"" data-name=""{0}"" data-value=""{1}"" data-type=""user"" data-table=""customer"">", item.Email, item.UserId);
                        html.AppendFormat(@"    <span>{0}</span>     ", item.Email);
                        html.AppendFormat(@"</div>        ");
                    }
                }

                html.AppendFormat(@"<div class=""item drop_add"" data-name=""添加 {0}"" data-value=""{1}"" data-type=""add"" data-table="""">  ", keyword, keyword);
                html.AppendFormat(@"    <span>添加 {0}</span>             ", keyword);
                html.AppendFormat(@"</div>");



                //var OrdersModAddress = await _orderListService.OrdersModAddress(FirstName, LastName, AddressLine1, AddressLine2, City, country_id_input, country_id, country_idType
                //    , _DoubleOption, Province, ZipCode, CountryCode, PhoneNumber, tax_code_type, tax_code_value, OrderId, Type);
                return Ok(new { ret = 1, msg = new { Html = html.ToString(), Count = 1, dataCount = userList.Count } });
            }
            catch (Exception ex)
            {

                throw;
            }

        }

        [HttpPost("OrdersSuggestedRefund")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> OrdersSuggestedRefund()
        {
            try
            {


                var form = Request.Form;

                // 基础字段
                var orderModel = new OrderModelRefundRequest
                {
                    orderId = int.Parse(form["orderId"]),
                    shippingAmount = decimal.Parse(form["shippingAmount"]),
                    totalAmount = decimal.Parse(form["totalAmount"]),
                    Reason = form["Reason"],
                    refundType = form["refundType"]
                };

                //orderId  28
                //shippingAmount 0
                //lineItems[0][itemId] 31
                //lineItems[0][quantity]0
                //lineItems[0][status]Shipped
                //lineItems[0][packageId]            45
                //lineItems[0][type]true
                //lineItems[1][itemId]29
                //lineItems[1][quantity]0
                //lineItems[1][status]Shipped
                //lineItems[1][packageId]           45
                //lineItems[1][type]true
                //lineItems[2][itemId]30
                //lineItems[2][quantity]0
                //lineItems[2][status]UnShipped
                //lineItems[2][packageId]            35
                //lineItems[2][type]true
                //totalAmount  0
                //Reason
                //refundType   original

                int quantityCount = 0;
                // 手动解析LineItems
                var lineItems = new List<LineItem>();
                foreach (var key in form.Keys)
                {
                    if (key.StartsWith("lineItems["))
                    {
                        var match = Regex.Match(key, @"lineItems\[(\d+)\]\[([a-zA-Z]+)\]");
                        if (match.Success)
                        {
                            var index = int.Parse(match.Groups[1].Value);
                            var propName = match.Groups[2].Value;
                            var value = form[key];

                            // 扩展列表容量
                            while (lineItems.Count <= index)
                            {
                                lineItems.Add(new LineItem());
                            }

                            var item = lineItems[index];
                            switch (propName)
                            {
                                case "itemId":
                                    item.itemId = int.Parse(value);
                                    break;
                                case "quantity":
                                    item.quantity = int.Parse(value);
                                    if (int.Parse(value) > 0)
                                    {
                                        quantityCount += int.Parse(value);
                                    }
                                    break;
                                case "status":
                                    item.status = value;
                                    break;
                                case "packageId":
                                    item.packageId = int.Parse(value);
                                    break;
                                case "type":
                                    item.type = bool.Parse(value);
                                    break;
                            }
                        }
                    }
                }
                orderModel.lineItems = lineItems;

                //"ProductPrice": "0.00",
                //"DiscountPrice": "0.00",
                //"CouponPrice": "0.00",
                //"PointsPrice": "0.00",
                //"ShippingPrice": "0.00",
                //"FeePrice": "0.00",
                //"TaxPrice": "0.00",
                //"TotalPrice": "0.00",
                //"Currency": "USD",
                //"RefundTotalPrice": "199.12",
                //"Tax": "0.00000",
                //"itemCount": "0.00"

                //          "ProductPrice": "49.99",
                //"DiscountPrice": "0.00",
                //"CouponPrice": "0.00",
                //"PointsPrice": "0.27",
                //"ShippingPrice": "0.00",
                //"FeePrice": "1.99",
                //"TaxPrice": "0.00",
                //"TotalPrice": "51.71",
                //"Currency": "USD",
                //"RefundTotalPrice": "199.12",
                //"Tax": "0.00000",
                //"itemCount": "1.00"

                var OrdersSuggestedRefundData = new OrdersSuggestedRefundResponse();
                if (quantityCount > 0)
                {
                    OrdersSuggestedRefundData = await _orderListService.GetOrdersSuggestedRefund(orderModel);

                    return Ok(new { ret = 1, msg = OrdersSuggestedRefundData });
                }
                else
                {
                    var OrdersAmount = await _orderListService.GetOrdersAmount(orderModel.orderId);
                    //ProductCount = OrdersAmount.Item1;
                    //TotalProductPrice = OrdersAmount.Item2;
                    //points = OrdersAmount.Item3;
                    //shippingFee = OrdersAmount.Item4;
                    //commission = OrdersAmount.Item5;
                    //OrderSum = OrdersAmount.Item6;
                    //OrderSymbol = OrdersAmount.Item7;
                    //paymentMethod = OrdersAmount.Item8;
                    //var OrderRefund = await _orderListService.GetOrderRefundByOrderIdAsync(orderModel.orderId);
                    var OrderDetailModel = await _orderListService.GetOrderDetailByOrderIdAsync(orderModel.orderId);
                    //OrderRefundAmount = OrderRefund?.Amount ?? 0;
                    //Model.OrderSum - Model.OrderRefundAmount

                    //"ProductPrice": "0.00",
                    //"DiscountPrice": "0.00",
                    //"CouponPrice": "0.00",
                    //"PointsPrice": "0.00",
                    //"ShippingPrice": "0.00",
                    //"FeePrice": "0.00",
                    //"TaxPrice": "0.00",
                    //"TotalPrice": "0.00",
                    //"Currency": "USD",
                    //"RefundTotalPrice": "199.12",
                    //"Tax": "0.00000",
                    //"itemCount": "0.00"
               
                    var paymentPrice = await _orderListService.actualPayment(OrderDetailModel, 0);
                    var OrderSum = paymentPrice;
                    OrdersSuggestedRefundData.ProductPrice = 0.00m;
                    OrdersSuggestedRefundData.DiscountPrice = 0.00m;
                    OrdersSuggestedRefundData.CouponPrice = 0.00m;
                    OrdersSuggestedRefundData.PointsPrice = 0.00m;
                    OrdersSuggestedRefundData.ShippingPrice = 0.00m;
                    OrdersSuggestedRefundData.FeePrice = 0.00m;
                    OrdersSuggestedRefundData.TaxPrice = 0.00m;
                    OrdersSuggestedRefundData.TotalPrice = 0.00m;
                    OrdersSuggestedRefundData.Currency = OrderDetailModel.Currency;
                    OrdersSuggestedRefundData.RefundTotalPrice = OrderSum;
                    OrdersSuggestedRefundData.Tax = 0.00m;
                    OrdersSuggestedRefundData.itemCount = 0.00m;
                    return Ok(new { ret = 0, msg = OrdersSuggestedRefundData });
                }
                //var OrdersModAddress = await _orderListService.OrdersModAddress(FirstName, LastName, AddressLine1, AddressLine2, City, country_id_input, country_id, country_idType
                //    , _DoubleOption, Province, ZipCode, CountryCode, PhoneNumber, tax_code_type, tax_code_value, OrderId, Type);
            }
            catch (Exception e)
            {
                return Ok(new { ret = 0, msg = new OrdersSuggestedRefundResponse() });
                throw;
            }
        }


        [HttpPost("OrdersRefund")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> OrdersRefund()
        {
            var form = Request.Form;

            // 基础字段
            var orderModel = new OrderModelRefundRequest
            {
                orderId = int.Parse(form["orderId"]),
                shippingAmount = decimal.Parse(form["shippingAmount"]),
                totalAmount = decimal.Parse(form["totalAmount"]),
                Reason = form["Reason"],
                refundType = form["refundType"]
            };

            // 优化后的LineItems解析逻辑
            var lineItems = new List<LineItem>();
            var lineItemKeys = form.Keys
                .Where(k => k.StartsWith("lineItems["))
                .OrderBy(k =>
                {
                    // 提取数组索引并确保排序正确
                    var match = Regex.Match(k, @"lineItems\[(\d+)\]");
                    return match.Success ? int.Parse(match.Groups[1].Value) : int.MaxValue;
                })
                .ToList();
            int quantityCount = 0;
            foreach (var key in lineItemKeys)
            {
                var match = Regex.Match(key, @"lineItems\[(\d+)\]\[([a-zA-Z]+)\]");
                if (!match.Success) continue;

                var index = int.Parse(match.Groups[1].Value);
                var propName = match.Groups[2].Value;
                var value = form[key];

                // 确保列表容量足够
                while (lineItems.Count <= index)
                {
                    lineItems.Add(new LineItem());
                }

                var item = lineItems[index];
                try
                {
                    switch (propName)
                    {
                        case "itemId":
                            item.itemId = int.Parse(value);
                            break;
                        case "quantity":
                            item.quantity = int.Parse(value);
                            if (int.Parse(value) > 0)
                            {
                                quantityCount += int.Parse(value);
                            }
                            break;
                        case "status":
                            item.status = value;
                            break;
                        case "packageId":
                            item.packageId = int.Parse(value);
                            break;
                        case "type":
                            item.type = bool.Parse(value);
                            break;
                    }
                }
                catch (Exception ex)
                {

                }
            }

            // 移除未使用的默认项（当索引不连续时）
            lineItems.RemoveAll(item =>
                item.itemId == 0 &&
                item.quantity == 0 &&
                string.IsNullOrEmpty(item.status) &&
                item.packageId == 0 &&
                !item.type);

            orderModel.lineItems = lineItems;
            if (quantityCount > 0 && orderModel.totalAmount > 0)
            {
                var res = await _orderListService.AddRefund(orderModel);

                #region 发送订单退款
                if (res == "success")
                {
                    var order = await _orderListService.GetOrderDetailByOrderIdAsync(orderModel.orderId);
                string tokenKey = Guid.NewGuid().ToUUID();
                //设置缓存 保存到redis  1个小时过期
                DateTime expTime = DateTime.Now.AddHours(1);
                TimeSpan ts = new TimeSpan(1, 0, 0); // 小时, 分钟, 秒

                string email = order.Email;
                string userName = order.ShippingFirstName + order.ShippingLastName;
                //设置缓存 1小时有效 
                _caching.Set(GlobalConstVars.user_order_refund.FormatWith(tokenKey), email, ts);
                //_caching.HashSet(GlobalConstVars.user_validate_email, tokenKey, email);

                //发邮件
                string userLang = AppSettingsConstVars.LanguageDefault; //默认语言

                var OrderNum = order.OId;//订单号
                var OrderDetail = "";//订单详情表格，包含订单价格，产品等信息
                var OrderUrl = "";//查看订单链接，适用于超链接URL设置
                var OrderStatus = ((OrderStatusEnum)order.OrderStatus).ToString();//订单状态
                var OrderPrice = orderModel.totalAmount.ToString();//订单价格
                var OrderFefundMethod = orderModel.refundType;//订单退款方式
                var OrderRefundPrice = orderModel.totalAmount.ToString();//订单退款价格
                var OrderRefundDetail = "";//订单退款详情

                var resent = await _emailTplService.SendOrderRefund(email, userName, userLang, OrderNum, OrderDetail
                , OrderUrl, OrderStatus, OrderPrice, OrderFefundMethod, OrderRefundPrice, OrderRefundDetail);

                    #endregion
                }
                return Ok(new { ret = 1, msg = new { reason = res, description = "" } });
            }
            else
            {
                return Ok(new { ret = 0, msg = new { reason = "", description = "" } });
            }



        }



        [HttpPost("OrdersCancelAction")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> OrdersCancelAction(int id)
        {
            try
            {
                //restock 1
                //reason 111
                //amount 0.00
                //refundType offline
                //PaymentMethod

                var restock = Request.Form["restock"].ObjToInt();
                var reason = Request.Form["reason"];
                var amount = Convert.ToDecimal(Request.Form["amount"]);
                var refundType = Request.Form["refundType"];
                var PaymentMethod = Request.Form["PaymentMethod"];
                //订单ID、是否重新入库、原因、退款金额、退款方式、支付方式


                //(int id, decimal totalAmount, string reason, int restock, string actionRefundType, string paymentMethod)


                var reasonStr = await _orderListService.OrdersCancelAction(id, amount, reason, restock, refundType, PaymentMethod);
                return Ok(new
                {
                    ret = reasonStr == "已成功取消订单" ? 1 : 0,
                    msg = reasonStr
                });


                //return Ok(new { ret = 1, msg = new { content = "发送成功", jump = "/Orders/Detail?id=" + OrderId + "&query_string=" } });
                //{ "msg":{ "OrderId":31,"Log":"2323234","AccTime":"2025-05-21<br/>11:20:26","UserId":85903,"UserName":"yseadmin"},"ret":1}

            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "根据发生未处理异常");
                return StatusCode(500, "服务器内部错误");
            }
        }






















    }

   
    public class DataItem
    {
        public string Name { get; set; }
        public string Value { get; set; }
        public string Type { get; set; }
    }

    public class CheckDropDoubleModel
    {
        public string do_action { get; set; }
        public string Keyword { get; set; }
        public List<DataItem> Data { get; set; } // 注意：这个字段对应前端JSON中的Data字符串
        public string Checkbox { get; set; }
        public string isShowAdd { get; set; }
        public string position { get; set; }
    }

    public class CreatetaxAry
    {
        public int taxType { get; set; }
        public decimal taxThreshold { get; set; }
        public decimal tax { get; set; }
    }

    // 辅助扩展方法（需要添加到静态类）
    public static class SessionExtensions
    {
        public static void SetObject(this ISession session, string key, object value)
        {
            session.SetString(key, System.Text.Json.JsonSerializer.Serialize(value));
        }

        public static T GetObject<T>(this ISession session, string key)
        {
            var value = session.GetString(key);
            return value == null ? default(T) :
                System.Text.Json.JsonSerializer.Deserialize<T>(value);
        }
    }




   

}
