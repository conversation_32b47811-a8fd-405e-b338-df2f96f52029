using Microsoft.AspNetCore.Authentication.Cookies;
using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using YseStore.Common.Helper;
using YseStore.Service;
using YseStore.IService.SiteSystem;

namespace YseStoreAdmin.Controllers;

public class AccountController : Controller
{
    private readonly IManageServices _manageServices;

    public AccountController(IManageServices manageServices)
    {
        _manageServices = manageServices;
    }
    [Route("/Account/Logout")]
    [HttpGet]
    public async Task<IActionResult> Logout()
    {
        await HttpContext.SignOutAsync(CookieAuthenticationDefaults.AuthenticationScheme);

        return new RedirectResult("/");
    }

    [Route("/Account/ChangePassword")]
    [HttpPost]
    public async Task<IActionResult> ChangePassword()
    {
        var userId = HttpContext.User.Claims.FirstOrDefault(c => c.Type == "UserId")?.Value.ObjToInt();
        var psd = Request.Form["Password"];
        var cfpsd = Request.Form["ConfirmPassword"];
        var data = await _manageServices.ManagerChangePassword(userId, psd, cfpsd);
        var ret= new MessageModel<string>
        {
            ret = data.Item1?1:0,
            msg = data.Item2
        };
        if (data.Item1)
        {
            await HttpContext.SignOutAsync(CookieAuthenticationDefaults.AuthenticationScheme);
        }
        return Ok(ret);
    }



}
