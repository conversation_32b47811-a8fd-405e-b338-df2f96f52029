using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using YseStore.IService.Sales;
using YseStore.IService;
using Entitys;
using SqlSugar;
using YseStore.Model.Enums;
using YseStore.Service.Sales;
using YseStoreAdmin.Parameters.Sales;
using YseStore.Model.Response.Sales;
using StackExchange.Profiling.Internal;

namespace YseStoreAdmin.Controllers.Sales
{
    [Route("api/[controller]")]
    //[ApiController]
    public class SlaeStickerController : ControllerBase
    {
       
        private readonly ILogger<DiscountController> _logger;
        private readonly IConfigService _configService;
        private readonly ISaleStickerService _saleStickerService;
        public SlaeStickerController( ILogger<DiscountController> logger, IConfigService configService, ISaleStickerService saleStickerService)
        {
            _logger = logger;
            _configService = configService;
            _saleStickerService = saleStickerService;
        }

        #region 保存促销标签
        [HttpPost("savesalesticker")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> SaveSaleSticker([FromForm] SlaeStickerRequest formData)
        {
            try
            {
                // 1. 基础校验
                if (!ModelState.IsValid)
                {
                    var errorMessages = ModelState.Values
                       .SelectMany(v => v.Errors)
                       .Select(e => e.ErrorMessage)
                       .Where(m => !string.IsNullOrEmpty(m)) // 过滤空错误信息
                       .ToList();
                    return Ok(new { ret = 0, msg = string.Join(',', errorMessages) });

                }
                YseStore.Model.ContentData contentData = new ContentData()
                {
                    BgColor = formData.BgColor ?? "",
                    Style = formData.Style ?? "",
                    TextColor = formData.TextColor ?? "",
                    BgImgPath= formData.ImgPath??""
                };
                app_sale_sticker app_Sale_Sticker  = new app_sale_sticker() { 
                SId=  formData.SId,Name=formData.Name,Position=formData.Position??"",ContentData=contentData.ToJson()
                };
                StickerScopeData stickerScopeData=new StickerScopeData()
                {
                    Data = new StickerSpecifyData()
                    {
                        specifyType = formData.specifyType??  "",
                        StartTime=0,
                        EndTime=0,
                        specifyData=""
                    },
                    Type = formData.ScopeDataType?? "new"
                };
                if (stickerScopeData.Type=="new")//适用范围是新品时才需要时间
                {
                    if (formData.StartTime != null)//适用范围新增
                    {
                        stickerScopeData.Data.StartTime= (new DateTimeOffset(formData.StartTime.Value).ToUnixTimeSeconds());
                    }
                    if (formData.EndTime != null)//适用范围新增
                    {
                        stickerScopeData.Data.EndTime = (new DateTimeOffset(formData.EndTime.Value).ToUnixTimeSeconds());
                    }
                }
                string specifyData = "";
                if (formData.specifyType == "category" && formData.ProductsCategoryCurrent != null)
                {
                    specifyData =$"|{string.Join("|", formData.ProductsCategoryCurrent ?? new List<int>())}|";
                }
                else if (formData.specifyType  == "products" && formData.ProductsCurrent != null)
                {
                    specifyData = $"|{string.Join("|", formData.ProductsCurrent ?? new List<int>())}|";
                }
                stickerScopeData.Data.specifyData = specifyData;
               app_Sale_Sticker.ScopeData= stickerScopeData.ToJson();
                var res = await _saleStickerService.SaveSticker(app_Sale_Sticker);

                return Ok(new { ret = res.Item1, msg = "" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "保存促销标签发生未处理异常");
                return StatusCode(500, "服务器内部错误");
            }
        }

        #endregion

        #region
        [HttpGet("delete")]
        public async Task<IActionResult> ActionDelete([FromQuery] string id)
        {
            try
            {
                var groupCIdAry = id?.Split('-').Select(int.Parse).ToList() ?? new List<int>();
                if (!groupCIdAry.Any())
                {
                    return Ok(new { ret = false, msg = "请选择要删除的营销标签" });
                }

                var res = await _saleStickerService.BatchDeleteStickerAsync(groupCIdAry);

                return Ok(new { ret = res.Item1, msg = res.Item2 });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "删除营销标签时发生未处理异常");
                return StatusCode(500, "服务器内部错误");
            }

        }
        #endregion
    }
}
