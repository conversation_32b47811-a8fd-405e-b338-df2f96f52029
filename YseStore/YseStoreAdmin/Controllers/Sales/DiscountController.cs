using Entitys;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using YseStore.IService.Sales;
using YseStore.IService;
using YseStoreAdmin.Pages.Components.Setting;
using YseStore.Service.Sales;
using OfficeOpenXml.FormulaParsing.Excel.Functions.Text;
using static System.Runtime.InteropServices.JavaScript.JSType;
using YseStore.Common.Helper;
using Magicodes.IE.Core;
using System.Text;
using YseStoreAdmin.Parameters.Sales;
using YseStore.Model.Enums;
using SqlSugar;
using System.Text.RegularExpressions;
using OfficeOpenXml.FormulaParsing.Excel.Functions.Information;

namespace YseStoreAdmin.Controllers.Sales
{
    [Route("api/[controller]")]
    //[ApiController]
    public class DiscountController : ControllerBase
    {
        public readonly ISalesCouponService _salesCouponService;
        private readonly ILogger<DiscountController> _logger;
        private readonly IConfigService _configService;
        private readonly IOpreationActivitiesService _opreationActivitiesService;
        private readonly ISalesDiscountService _salesDiscountService;
        public DiscountController(ISalesCouponService salesCouponService, ILogger<DiscountController> logger, IConfigService configService, IOpreationActivitiesService opreationActivities,ISalesDiscountService salesDiscountService)
        {
            _salesCouponService = salesCouponService;
            _logger = logger;
            _configService = configService;
            _opreationActivitiesService = opreationActivities;
            _salesDiscountService = salesDiscountService;
        }
        [HttpPost("FixedRightProductsChoice")]
        public async Task<IActionResult> FixedRightProductsChoice([FromForm] string keyword,string a,string filterProId,int filterCateId=0, int page = 1,string filterTagId="")
        {
            try
            {
                
                var res = await _salesDiscountService.GetProductSelectList(keyword, page,filterProId: filterProId,cateId:filterCateId, filterTagId: filterTagId);
               // res.Item1=new List<YseStore.Model.Response.Sales.DiscountProductSelect>();
                var html = "<div>";
                html += $"<div class='fixed_right_products_choice_list' data-page='{page}' data-total-pages='{res.Item2}'>";
                if (res.Item1.Count > 0)
                {
                    foreach (var product in res.Item1)
                    {
                        var @class = "";
                        var error = 0;
                        var error_text = new List<string>();
                        if (a== "discount")
                        {
                            if (product.Specification > 1)
                            {
                                error = 1;
                                @class += " disabled";
                                error_text.Add("多规格属性产品不能成为赠品");
                            }
                            else if (product.Stock == 0)
                            {
                                error = 1;
                                @class += " disabled";
                                error_text.Add("没库存");
                            }
                            else if (product.Specification == 0 && product.IsShipFrom > 0)
                            {
                                error = 1;
                                @class += " disabled";
                                error_text.Add("单规格仓库产品不能成为赠品");
                            }

                            if (error == 1)
                            {
                                @class += " error";
                            }
                        }
                        

                        html += $"<div class='fixed_right_products_choice_list_item{@class}' data-proid='{product.ProId}'>";
                        html += HtmlBuildHelper.BtnCheckbox("ProId[]", product.ProId.ToString());

                        html += $"<div class='item_img fl pic_box'><img src='{product.PicPath_0}' /><span></span></div>";
                        html += $"<div class='item_name fl'><span>{product.Name_en}</span>";
                        if (error == 1)
                        {
                            html += $"<div class='error_text'>{string.Join(',', error_text)}</div>";
                        }
                        html += "</div>";
                        html += "<div class='clear'></div>";
                        html += "</div>";
                    }
                }
                else
                {
                    html += HtmlBuildHelper.NoTableData(0, "/Products/Index", "<span class=\"wholesale_pro_no_data\">上架产品已全部参与批发，请前往产品管理 <a href=\"/Products/Edit?id=0\" target=\"_blank\">添加新产品</a></span>", "", "target='_blank'", "暂无产品");
                }
                html += "<div class='clear'></div>";
                if (page < res.Item2)
                {
                    html += $"<a class='fixed_right_products_choice_list_load_more' href='javascript:;'>加载更多</a>";
                }
                html += "</div>";
                html += "</div>";
                return Content(html, "text/html");
            }
            catch (Exception ex)
            {

                _logger.LogError(ex, "FixedRightProductsChoice方法发生未处理异常");
                return Content("", "text/html");
            }

          
        }


        #region 选中赠品
        [HttpPost("AddProducts")]
        public async Task<IActionResult> AddProducts([FromForm] DiscountAddProductRequest discountAddProductRequest)
        {

            try
            {
                //DiscountAddProductRequest discountAddProductRequest=new DiscountAddProductRequest();
                if (discountAddProductRequest.ProIds == null || discountAddProductRequest.ProIds.Count == 0)
                {
                    return Ok(new { ret = false, msg = "请选择要赠送的产品" });
                }
                int item = discountAddProductRequest.Item??0;
                var productsRow = await _salesDiscountService.GetProductSelectedList(discountAddProductRequest.ProIds);

                string html = "";
                foreach (var v in productsRow)
                {
                    string img = "";
                    string name = v.Name_en;
                    html += "<div class=\"p_item\">";
                    html += "<div class=\"img\">";
                    html += $"<img src=\"{img}\" alt=\"\" />";
                    html += "</div>";
                    html += "<div class=\"info\">";
                    html += $"<div class=\"name\">{name}</div>";
                    html += "</div>";
                    html += $"<div class=\"qty_box\"><span class=\"unit_input\"><input type=\"text\" name=\"Qty[{item}][]\" value=\"1\" class=\"box_input right_radius qty\" rel=\"int\" notnull><b class=\"last\">件</b></span></div>";
                    html += "<div class=\"del_box\"><a href=\"javascript:;\" class=\"p_del\"></a></div>";
                    html += $"<input type=\"hidden\" class=\"proid\" name=\"ProId[{item}][]\" value=\"{v.ProId}\" />";
                    html += "</div>";
                }

                return Ok(new { ret = true, msg = html });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "AddProducts方法发生未处理异常");
                return StatusCode(500, "服务器内部错误");
            }
        }

        #endregion

        #region 保存满减活动
        [HttpPost("savediscount")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> SaveDiscount([FromForm] DiscountRequest formData)
        {
            try
            {
                // 1. 基础校验
                if (!ModelState.IsValid)
                {
                    var errorMessages = ModelState.Values
                       .SelectMany(v => v.Errors)
                       .Select(e => e.ErrorMessage)
                       .Where(m => !string.IsNullOrEmpty(m)) // 过滤空错误信息
                       .ToList();
                    return Ok(new { ret = 0, msg = string.Join(',', errorMessages) });

                }

                // 校对类型
                var ruleTypeAry =  Consts.RuleTypeAry;
                var ruleType = $"{formData.ActCondition}-{formData.ActType}-{formData.ActRule}";
                if (!ruleTypeAry.ContainsValue(ruleType))
                {
                    return Ok(new { ret = false, msg = "无效的满减活动类型" });
                }

                // 处理时间
                if (formData.EndTime <= formData.StartTime)
                {
                   return Ok(new { ret = false, msg = "结束时间必须大于开始时间" });
                }
                // 判断是否有活动在相交时间内
                var upenum = (UseProductsEnum)formData.UseProducts;
                int startTime = (int)(new DateTimeOffset(formData.StartTime.Value).ToUnixTimeSeconds());
                int endTime = (int)(new DateTimeOffset(formData.EndTime.Value).ToUnixTimeSeconds());
                var sameStartWhere = await _salesDiscountService.QueryAsyncByCondition(Expressionable.Create<full_reduction>().And(r => r.UseProducts == upenum && r.StartTime <= startTime && r.EndTime >= startTime));
                var sameMidWhere = await _salesDiscountService.QueryAsyncByCondition(Expressionable.Create<full_reduction>().And(r => r.UseProducts == upenum && r.StartTime > startTime && r.StartTime < endTime));
                var sameEndWhere = await _salesDiscountService.QueryAsyncByCondition(Expressionable.Create<full_reduction>().And(r => r.UseProducts == upenum && r.StartTime <= endTime && r.EndTime >= endTime));

                if (upenum == UseProductsEnum.SpecificProducts && Request.Form.ContainsKey("productsCurrent[]"))
                {
                    var productsCurrent =formData.ProductsOption;
                    sameStartWhere = sameStartWhere.Where(r => productsCurrent.Any(p => r.UseProductsValue.Contains($"|{p}|"))).ToList();
                    sameMidWhere = sameMidWhere.Where(r => productsCurrent.Any(p => r.UseProductsValue.Contains($"|{p}|"))).ToList();
                    sameEndWhere = sameEndWhere.Where(r => productsCurrent.Any(p => r.UseProductsValue.Contains($"|{p}|"))).ToList();
                }
                else if (upenum == UseProductsEnum.SpecificCategory && Request.Form.ContainsKey("products_categoryCurrent[]"))
                {
                    var categoryCurrent = formData.ProductsCategoryCurrent;
                    sameStartWhere = sameStartWhere.Where(r => categoryCurrent.Any(p => r.UseProductsValue.Contains($"|{p}|"))).ToList();
                    sameMidWhere = sameMidWhere.Where(r => categoryCurrent.Any(p => r.UseProductsValue.Contains($"|{p}|"))).ToList();
                    sameEndWhere = sameEndWhere.Where(r => categoryCurrent.Any(p => r.UseProductsValue.Contains($"|{p}|"))).ToList();
                }

                if (formData.FId > 0)
                {
                    sameStartWhere = sameStartWhere.Where(r => r.FId != formData.FId).ToList();
                    sameMidWhere = sameMidWhere.Where(r => r.FId != formData.FId).ToList();
                    sameEndWhere = sameEndWhere.Where(r => r.FId != formData.FId).ToList();
                }
                int currenttime = (int)(new DateTimeOffset(DateTime.Now).ToUnixTimeSeconds());
                var sameStartTimeRow = sameStartWhere.Where(r => r.StartTime <= currenttime && r.EndTime >= currenttime).ToList();
                var sameMidTimeRow = sameMidWhere.Where(r => r.StartTime <= currenttime && r.EndTime >= currenttime).ToList();
                var sameEndTimeRow = sameEndWhere.Where(r => r.StartTime <= currenttime && r.EndTime >= currenttime).ToList();
                var sameRow = sameStartTimeRow.Concat(sameMidTimeRow).Concat(sameEndTimeRow).ToList();

                if (sameRow.Count > 0)
                {
                    string errorTips;
                    if (upenum == UseProductsEnum.SpecificProducts)
                    {
                        var useProductsValueAry = sameRow.SelectMany(r => r.UseProductsValue.Split('|').Where(v => !string.IsNullOrEmpty(v))).ToList();
                        var proidAry = formData.ProductsOption!.Where(p => useProductsValueAry.Contains(p)).ToList();
                        var seletProList = await _salesDiscountService.GetProductSelectedList(proidAry.Select(c => int.Parse(c)).ToList());
                        var productsRow = seletProList.Select(p => p.Name_en).ToList();
                        var productsTips = string.Join(",", productsRow);
                        errorTips = $"指定产品 {productsTips} 已有参加其他满减活动";
                    }
                    else if (upenum == UseProductsEnum.SpecificCategory)
                    {
                        var useProductsValueAry = sameRow.SelectMany(r => r.UseProductsValue.Split('|').Where(v => !string.IsNullOrEmpty(v))).ToList();
                        var cateAry = formData.ProductsCategoryCurrent!.Where(c => useProductsValueAry.Contains(c)).ToList();
                        var seletCatList = await _salesDiscountService.GetProductCategorySelectedList(cateAry.Select(c => int.Parse(c)).ToList());
                        var categoryRow = seletCatList.Select(c => c.Category_en).ToList();
                        var categoryTips = string.Join(",", categoryRow);
                        errorTips =$"指定分类 {categoryTips} 已有参加其他满减活动";
                    }
                    else
                    {
                        errorTips = "已有此产品在这个时段里设置促销";
                    }
                    return Ok(new { ret = false, msg = errorTips });
                }

                // 处理优惠规则(目前只能整数)
                var rule = new Dictionary<string, object>();
                if (new[] { 6, 7, 8, 9,10,11,12,13,14,15 }.Contains(formData.Type))
                {
                    var p_RulePro = Request.Form["RulePro[]"].ToString().Split(',');
                    var p_Rule1 = Request.Form["Rule[2][]"].ToString().Split(',');//减的金额
                    for (var k = 0; k < p_RulePro.Length; k++)
                    {
                        if (!float.TryParse(p_RulePro[k], out var v) || v == 0) continue;
                        if ((formData.Type == 8 || formData.Type == 9) && k > 0) continue;

                        var proAry = Request.Form[$"ProId[{k}][]"].ToString().Split(',',StringSplitOptions.RemoveEmptyEntries);
                        var qtyPro = new List<Dictionary<string, object>>();
                        var qtyValues = Request.Form[$"Qty[{k}][]"].ToString().Split(',', StringSplitOptions.RemoveEmptyEntries);
                        for (var k1 = 0; k1 < proAry.Length; k1++)
                        {
                            var qty = k1 < qtyValues.Length && int.TryParse(qtyValues[k1], out int q) ? q : 1;
                            qtyPro.Add(new Dictionary<string, object> { { proAry[k1], qty } });
                        }

                        if (proAry.Length <= 0)
                        {
                            return Ok(new { ret = false, msg = "赠品数大于0" });
                        }
                        if ((new[] { 10, 11, 12, 13, 14, 15 }.Contains(formData.Type)))//满减+赠品
                        {
                            if (!float.TryParse(p_Rule1[k], out var p_Rule1K)) p_Rule1K = 0;
                            var expRedu = p_Rule1K.ToString().Split('.');
                            var full = (int)(v * 100) / 100.0;
                            var redu = (int)(p_Rule1K * 100) / 100.0;
                            if ((new[] { 14, 15 }.Contains(formData.Type)) && expRedu.Length > 1)
                            {
                                return Ok(new { ret = false, msg = "折扣不能是小数" });
                            }
                            if ((new[] { 14, 15 }.Contains(formData.Type)) && (redu <= 0 || redu >= 100))
                            {
                                return Ok(new { ret = false, msg = "折扣必须大于0且小于100" });
                            }
                            if ((new[] { 10, 11,12,13 }.Contains(formData.Type)) && redu >= full)
                            {
                                return Ok(new { ret = false, msg = "减钱额度必须小于满额额度" });
                            }
                            qtyPro.Add(new Dictionary<string, object> { { "-1", redu } });
                        }
                        rule[v.ToString()] = qtyPro;
                    }
                    rule = rule.OrderBy(r => float.Parse(r.Key)).ToDictionary(r => r.Key, r => r.Value);
                }
                else
                {
                    var p_Rule0 = Request.Form["Rule[0][]"].ToString().Split(',');
                    var p_Rule1 = Request.Form["Rule[1][]"].ToString().Split(',');
                    for (var k = 0; k < p_Rule0.Length; k++)
                    {
                        if ((formData.Type == 4 || formData.Type == 5) && k != 0) continue;

                        if (!float.TryParse(p_Rule0[k], out var v)) v = 0;
                        if (!float.TryParse(p_Rule1[k], out var p_Rule1K)) p_Rule1K = 0;

                        var expFull = v.ToString().Split('.');
                        var expRedu = p_Rule1K.ToString().Split('.');

                        if ((new[] { 2, 3, 5 }.Contains(formData.Type)) && expFull.Length > 1)
                        {
                            return Ok(new { ret = false, msg = "件数不能是小数" });
                        }
                        if ((new[] { 1, 3 }.Contains(formData.Type)) && expRedu.Length > 1)
                        {
                            return Ok(new { ret = false, msg = "折扣不能是小数" });
                        }

                        var full = (int)(v * 100) / 100.0;
                        var redu = (int)(p_Rule1K * 100) / 100.0;

                        if (new[] { 2, 3, 5 }.Contains(formData.Type)) full = (int)full;
                        if (new[] { 1, 3 }.Contains(formData.Type)) redu = (int)redu;

                        if ((new[] { 0, 1, 4 }.Contains(formData.Type)) && full <= 0)
                        {
                            return Ok(new { ret = false, msg = "满额额度必须大于0" });
                        }
                        if ((new[] { 0, 4 }.Contains(formData.Type)) && redu >= full)
                        {
                            return Ok(new { ret = false, msg = "减钱额度必须小于满额额度" });
                        }
                        if ((new[] { 2, 3, 5 }.Contains(formData.Type)) && full <= 0)
                        {
                            return Ok(new { ret = false, msg = "件数必须大于0" });
                        }
                        if ((new[] { 1, 3 }.Contains(formData.Type)) && (redu <= 0 || redu >= 100))
                        {
                            return Ok(new { ret = false, msg = "折扣必须大于0且小于100" });
                        }
                        if ((new[] { 0, 2, 4, 5 }.Contains(formData.Type)) && redu <= 0)
                        {
                            return Ok(new { ret = false, msg = "减钱额度必须大于0" });
                        }

                        rule[full.ToString()] = redu;
                    }

                    rule = rule.Where(r => r.Value != null).ToDictionary(r => r.Key, r => r.Value);
                    rule = rule.OrderBy(r => float.Parse(r.Key)).ToDictionary(r => r.Key, r => r.Value);

                    var prev = 0.0;
                    foreach (var item in rule)
                    {
                        if (prev != 0 && (double)item.Value < (double)rule[prev.ToString()])
                        {
                            return Ok(new { ret = false, msg = "优惠梯度不能少于上一级梯度" });
                        }
                        prev = float.Parse(item.Key);
                    }
                }

                var ruleJson = rule.ToJson();

                // 处理指定类型
                var useProductsValue = "";
                if (formData.UseProducts > 0)
                {
                  
                    if (formData.UseProducts == 1)
                    {
                        if (!Request.Form.ContainsKey("productsOption[]"))
                        {
                           
                            return Ok(new { ret = false, msg = "请选择指定产品" });
                        }
                        useProductsValue = $"|{string.Join("|", formData.ProductsOption)}|" ;
                    }
                    else if (formData.UseProducts == 2)
                    {
                        if (!Request.Form.ContainsKey("products_categoryCurrent[]"))
                        {
                            return Ok(new { ret = false, msg = "请选择指定分类" });
                        }
                        useProductsValue = $"|{string.Join("|", formData.ProductsCategoryCurrent)}|";
                    }

                   
                }
                full_reduction full_Reduction = new full_reduction { 
                FId=  formData.FId,
                EndTime=  formData.EndTime==null?0: (int)(new DateTimeOffset(formData.EndTime.Value).ToUnixTimeSeconds()),
                StartTime=  formData.StartTime==null?0: (int)(new DateTimeOffset(formData.StartTime.Value).ToUnixTimeSeconds()),
                Name=  formData.Name,
                Rule=  ruleJson,
                Type=  (DiscountTypeEnum)formData.Type,
                UseProducts= (UseProductsEnum)formData.UseProducts,
                UseProductsValue= useProductsValue
                };
                var res = await _salesDiscountService.SaveDiscount(full_Reduction);

                return Ok(new { ret = res.Item1, msg = "" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "保存满减活动发生未处理异常");
                return StatusCode(500, "服务器内部错误");
            }
        }

        #endregion

        #region
        [HttpGet("delete")]
        public async Task<IActionResult> ActionDelete([FromQuery] string id)
        {
            try
            {
                var groupCIdAry = id?.Split('-').Select(int.Parse).ToList() ?? new List<int>();
                if (!groupCIdAry.Any())
                {
                    return Ok(new { ret = false, msg = "请选择要删除的满减活动" });
                }

                var res = await _salesDiscountService.BatchDeleteDiscountAsync(groupCIdAry);

                return Ok(new { ret = res.Item1, msg = res.Item2 });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "删除满减活动时发生未处理异常");
                return StatusCode(500, "服务器内部错误");
            }

        }
        #endregion

    }
}
