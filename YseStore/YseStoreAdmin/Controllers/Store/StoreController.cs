using Microsoft.AspNetCore.Mvc;
using YseStore.IService.Store;
using YseStore.Model.Response.Store;
using Entitys;
using YseStore.Model.RequestModels.Store;
using SqlSugar;
using YseStore.Model.Utils;

namespace YseStoreAdmin.Controllers.Store
{
    /// <summary>
    /// 单页文章控制器
    /// </summary>
    public class StoreController : Controller
    {
        private readonly IArticleService _articleService;
        private readonly ILogger<StoreController> _logger;
        private readonly IMenuService _menuService;
        private readonly ITkdLableService _tkdLableService;
        private readonly ISqlSugarClient _context;

        public StoreController(IArticleService articleService, ILogger<StoreController> logger,
            IMenuService menuService, ITkdLableService tkdLableService, ISqlSugarClient context)
        {
            _articleService = articleService ?? throw new ArgumentNullException(nameof(articleService));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _menuService = menuService ?? throw new ArgumentNullException(nameof(menuService));
            _tkdLableService = tkdLableService;
            _context = context ?? throw new ArgumentNullException(nameof(context));
        }

        /// <summary>
        /// 获取导航菜单列表
        /// </summary>
        /// <returns>导航菜单列表</returns>
        [HttpGet]
        [Route("/manage/view/nav/list")]
        public async Task<IActionResult> GetNavMenus()
        {
            try
            {
                var menus = await _menuService.GetNavMenusAsync();
                return new JsonResult(new { success = true, data = menus });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取导航菜单时发生错误: {ErrorMessage}", ex.Message);
                return new JsonResult(new { success = false, message = "获取导航菜单时发生错误: " + ex.Message });
            }
        }

        /// <summary>
        /// 获取导航菜单编辑表单
        /// </summary>
        /// <param name="MId">菜单ID</param>
        /// <param name="ParentId">父级菜单ID</param>
        /// <returns>编辑表单HTML</returns>
        [HttpGet]
        [Route("/manage/view/nav/edit")]
        public async Task<IActionResult> GetNavEdit(int? MId, int? ParentId)
        {
            try
            {
                menu menuData = null;

                // 如果提供了MId，获取现有菜单数据
                if (MId.HasValue && MId.Value > 0)
                {
                    menuData = await _menuService.GetMenuByIdAsync(MId.Value);
                    if (menuData == null)
                    {
                        return NotFound("菜单不存在");
                    }
                }
                else
                {
                    // 创建新菜单的默认数据
                    menuData = new menu
                    {
                        MId = 0,
                        Type = "nav",
                        NewTarget = false,
                        MyOrder = 1,
                        IsHidden = false
                    };

                    // 设置父级关系
                    if (ParentId.HasValue && ParentId.Value > 0)
                    {
                        var parentMenu = await _menuService.GetMenuByIdAsync(ParentId.Value);
                        if (parentMenu != null)
                        {
                            menuData.UId = string.IsNullOrEmpty(parentMenu.UId)
                                ? $"0,{ParentId.Value}"
                                : $"{parentMenu.UId},{ParentId.Value}";
                        }
                        else
                        {
                            menuData.UId = $"0,{ParentId.Value}";
                        }
                    }
                    else
                    {
                        menuData.UId = "0";
                    }
                }

                // 构建编辑表单HTML
                var formHtml = BuildNavEditFormHtml(menuData, ParentId ?? 0);

                return Content(formHtml, "text/html");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取导航菜单编辑表单时发生错误: {ErrorMessage}", ex.Message);
                return StatusCode(500, "获取编辑表单时发生错误: " + ex.Message);
            }
        }

        /// <summary>
        ///导航菜单编辑
        /// </summary>
        /// <returns>操作结果</returns>
        [HttpPost]
        [Route("/manage/view/nav/edit")]
        public async Task<IActionResult> PostNavEdit()
        {
            try
            {
                // 获取表单参数
                string name = Request.Form["Name"].ToString();
                string newTargetStr = Request.Form["NewTarget"].ToString();
                string unit = Request.Form["Unit"].ToString();
                string unitType = Request.Form["UnitType"].ToString();
                string unitValue = Request.Form["UnitValue"].ToString();
                string link = Request.Form["Link"].ToString();
                string mIdStr = Request.Form["MId"].ToString();
                string parentIdStr = Request.Form["ParentId"].ToString();
                string type = Request.Form["Type"].ToString();

                _logger.LogInformation(
                    $"PostNavEdit参数: Name={name}, NewTarget={newTargetStr}, Unit={unit}, UnitType={unitType}, UnitValue={unitValue}, Link={link}, MId={mIdStr}, ParentId={parentIdStr}, Type={type}");

                // 验证必填参数
                if (string.IsNullOrEmpty(name))
                {
                    return Json(new { ret = 0, msg = "菜单名称不能为空" });
                }

                // 解析参数
                if (!int.TryParse(mIdStr, out int mId))
                {
                    mId = 0;
                }

                if (!int.TryParse(parentIdStr, out int parentId))
                {
                    parentId = 0;
                }

                if (!int.TryParse(newTargetStr, out int newTargetInt))
                {
                    newTargetInt = 0;
                }

                // 确定链接URL
                string url = "/";
                if (unitType == "add" && !string.IsNullOrEmpty(link))
                {
                    url = link;
                }
                else if (!string.IsNullOrEmpty(unit))
                {
                    url = unit;
                }
                else if (!string.IsNullOrEmpty(unitValue))
                {
                    url = unitValue;
                }

                // 获取原始菜单的MyOrder值（如果是编辑现有菜单）
                sbyte myOrder = 1; // 默认值，用于新建菜单
                if (mId > 0)
                {
                    // 编辑现有菜单，获取原始的MyOrder值
                    var existingMenu = await _menuService.GetMenuByIdAsync(mId);
                    if (existingMenu != null)
                    {
                        myOrder = existingMenu.MyOrder ?? 0;
                    }
                }

                // 构建菜单实体
                var menuEntity = new menu
                {
                    MId = mId,
                    Name = BuildMultiLanguageMenuName(name), // 使用多语言格式
                    Url = url,
                    NewTarget = newTargetInt == 1,
                    Type = string.IsNullOrEmpty(type) ? "nav" : type,
                    MyOrder = myOrder,
                    IsHidden = false
                };

                // 设置父级关系
                if (parentId > 0)
                {
                    // 获取父级菜单的UId
                    var parentMenu = await _menuService.GetMenuByIdAsync(parentId);
                    if (parentMenu != null)
                    {
                        menuEntity.UId = string.IsNullOrEmpty(parentMenu.UId)
                            ? $"0,{parentId}"
                            : $"{parentMenu.UId},{parentId}";
                    }
                    else
                    {
                        menuEntity.UId = $"0,{parentId}";
                    }
                }
                else
                {
                    menuEntity.UId = "0";
                }

                // 保存菜单
                bool result = await _menuService.SaveMenuAsync(menuEntity);

                if (result)
                {
                    return Json(new { ret = 1, msg = "保存成功", data = menuEntity.MId });
                }
                else
                {
                    return Json(new { ret = 0, msg = "保存失败" });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "保存导航菜单时发生错误: {ErrorMessage}", ex.Message);
                return Json(new { ret = 0, msg = "保存菜单时发生错误: " + ex.Message });
            }
        }

        [HttpPost]
        [Route("/manage/view/visual/change-tmp-drafts")]
        public async Task<IActionResult> ChangeTemDrafts()
        {
            var ret = MessageModel<string>.Success("ok");
            return Ok(ret);
        }

        /// <summary>
        /// 获取底部导航菜单列表
        /// </summary>
        /// <returns>底部导航菜单列表</returns>
        [HttpGet]
        [Route("/manage/view/nav/footer-list")]
        public async Task<IActionResult> GetFooterNavMenus()
        {
            try
            {
                var menus = await _menuService.GetFooterNavMenusAsync();
                return new JsonResult(new { success = true, data = menus });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取底部导航菜单时发生错误: {ErrorMessage}", ex.Message);
                return new JsonResult(new { success = false, message = "获取底部导航菜单时发生错误: " + ex.Message });
            }
        }

        /// <summary>
        /// 获取菜单详情
        /// </summary>
        /// <param name="mId">菜单ID</param>
        /// <returns>菜单详情</returns>
        [HttpGet]
        [Route("/manage/view/nav/detail")]
        public async Task<IActionResult> GetMenuDetail(int mId)
        {
            try
            {
                var menu = await _menuService.GetMenuByIdAsync(mId);
                if (menu == null)
                {
                    return new JsonResult(new { success = false, message = "菜单不存在" });
                }

                return new JsonResult(new { success = true, data = menu });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取菜单详情时发生错误: {ErrorMessage}", ex.Message);
                return new JsonResult(new { success = false, message = "获取菜单详情时发生错误: " + ex.Message });
            }
        }

        /// <summary>
        /// 保存菜单
        /// </summary>
        /// <param name="menuEntity">菜单实体</param>
        /// <returns>操作结果</returns>
        [HttpPost]
        [Route("/manage/view/nav/update")]
        public async Task<IActionResult> SaveMenu([FromBody] menu menuEntity)
        {
            try
            {
                if (menuEntity == null)
                {
                    return new JsonResult(new { success = false, message = "菜单数据不能为空" });
                }

                bool result = await _menuService.SaveMenuAsync(menuEntity);

                if (result)
                {
                    return new JsonResult(new { success = true, message = "保存成功", data = menuEntity.MId });
                }
                else
                {
                    return new JsonResult(new { success = false, message = "保存失败" });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "保存菜单时发生错误: {ErrorMessage}", ex.Message);
                return new JsonResult(new { success = false, message = "保存菜单时发生错误: " + ex.Message });
            }
        }

        /// <summary>
        /// 删除菜单
        /// </summary>
        /// <param name="mId">菜单ID</param>
        /// <param name="type">菜单类型</param>
        /// <returns>操作结果</returns>
        [HttpGet]
        [Route("/manage/view/nav/delete")]
        public async Task<IActionResult> DeleteMenu(int mId, string type)
        {
            try
            {
                if (mId <= 0)
                {
                    return new JsonResult(new { ret = 0, msg = "菜单ID不能为空" });
                }

                bool result = await _menuService.DeleteMenuAsync(mId);

                if (result)
                {
                    return new JsonResult(new { ret = 1, msg = "删除成功" });
                }
                else
                {
                    return new JsonResult(new { ret = 0, msg = "删除失败" });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "删除菜单时发生错误: {ErrorMessage}", ex.Message);
                return new JsonResult(new { ret = 0, msg = "删除菜单时发生错误: " + ex.Message });
            }
        }

        /// <summary>
        /// 更新单页文章
        /// </summary>
        /// <param name="articleDetail">文章详情</param>
        /// <returns>操作结果</returns>
        [HttpPost]
        [Route("/manage/view/page/update/")]
        public async Task<IActionResult> UpdateArticle([FromBody] ArticleDetailResponse articleDetail)
        {
            try
            {
                if (articleDetail == null || articleDetail.Article == null)
                {
                    return new JsonResult(new { success = false, message = "文章数据不能为空" });
                }

                // 确保Content的AId与Article的AId一致
                if (articleDetail.Content != null)
                {
                    articleDetail.Content.AId = articleDetail.Article.AId;
                }

                // 调用服务更新文章
                bool result = await _articleService.UpdateArticleAsync(articleDetail);

                if (result)
                {
                    // 返回成功结果，如果是新增，则返回文章ID
                    return new JsonResult(new
                    {
                        success = true,
                        message = "保存成功",
                        data = articleDetail.Article.AId
                    });
                }
                else
                {
                    return new JsonResult(new { success = false, message = "保存失败" });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "更新文章时发生错误: {ErrorMessage}", ex.Message);
                return new JsonResult(new { success = false, message = "更新文章时发生错误: " + ex.Message });
            }
        }

        /// <summary>
        /// 删除单页文章
        /// </summary>
        /// <param name="id">文章ID，格式为"1-2-3-4"</param>
        /// <returns>操作结果</returns>
        [HttpGet]
        [Route("/manage/view/page/delete")]
        public async Task<IActionResult> DeleteArticle(string id)
        {
            try
            {
                if (string.IsNullOrEmpty(id))
                {
                    return new JsonResult(new { success = false, message = "文章ID不能为空" });
                }

                // 解析ID，格式可能是 "1-2-3-4" 这样的形式
                var idParts = id.Split('-');
                if (idParts.Length == 0)
                {
                    return new JsonResult(new { success = false, message = "无效的文章ID格式" });
                }

                // 解析所有ID
                List<object> idList = new List<object>();
                foreach (var idPart in idParts)
                {
                    if (int.TryParse(idPart, out int articleId))
                    {
                        idList.Add(articleId);
                    }
                    else
                    {
                        _logger.LogWarning("无法解析文章ID: {IdPart}", idPart);
                    }
                }

                // 检查是否有有效的ID
                if (idList.Count == 0)
                {
                    return new JsonResult(new { success = false, message = "没有有效的文章ID" });
                }

                // 调用服务删除文章
                bool result = await _articleService.DeleteByIds(idList.ToArray());

                if (result)
                {
                    return new JsonResult(new { success = true, message = "删除成功" });
                }
                else
                {
                    return new JsonResult(new { success = false, message = "删除失败" });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "删除文章时发生错误: {ErrorMessage}", ex.Message);
                return new JsonResult(new { success = false, message = "删除文章时发生错误: " + ex.Message });
            }
        }

        #region 保存TKD标签

        [HttpPost("/manage/view/seo/tkd-edit")]
        public async Task<IActionResult> TkdEdit([FromForm] TkdLableRequest tkdLableRequest)
        {
            try
            {
                if (tkdLableRequest.Ids == null || tkdLableRequest.Ids.Count == 0)
                {
                    return Ok(new { ret = 0, msg = "修改数据为空" });
                }

                List<TkdLableResponse> tkdLableResponses = new List<TkdLableResponse>();
                foreach (var id in tkdLableRequest.Ids)
                {
                    if (id <= 0)
                    {
                        return Ok(new { ret = 0, msg = "ID无效" });
                    }

                    Request.Form.TryGetValue($"SeoTitle[{id}]", out var title);
                    Request.Form.TryGetValue($"keysName[{id}][]", out var keyValues);
                    Request.Form.TryGetValue($"SeoDescription[{id}]", out var description);
                    tkdLableResponses.Add(new TkdLableResponse()
                    {
                        Id = id,
                        Page = title.ToString(),
                        Keyword = keyValues.ToString(),
                        Description = description.ToString(),
                    });
                }

                var res = await _tkdLableService.SaveTkdLable(tkdLableResponses, tkdLableRequest.Table);
                return Ok(new { ret = res.Item1 ? 1 : 0, msg = res.Item2 });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "保存TKD标签发生错误: {ErrorMessage}", ex.Message);
                return Ok(new { ret = 0, msg = "保存TKD标签失败" });
            }
        }

        /// <summary>
        /// 更新菜单排序
        /// </summary>
        /// <returns>操作结果</returns>
        [HttpPost]
        [Route("/manage/view/nav/order/")]
        public async Task<IActionResult> UpdateMenuOrder()
        {
            try
            {
                // 获取菜单类型
                Request.Form.TryGetValue("Type", out var menuType);

                if (string.IsNullOrEmpty(menuType))
                {
                    return new JsonResult(new { ret = 0, msg = "菜单类型不能为空" });
                }

                // 解析排序数据 - 处理 sort_order[id]=value 格式
                var sortOrders = new Dictionary<int, int>();

                foreach (var formKey in Request.Form.Keys)
                {
                    // 检查是否是 sort_order[数字] 格式
                    if (formKey.StartsWith("sort_order[") && formKey.EndsWith("]"))
                    {
                        // 提取菜单ID：sort_order[43] -> 43
                        var idStr = formKey.Substring(11, formKey.Length - 12); // 去掉 "sort_order[" 和 "]"

                        if (int.TryParse(idStr, out int menuId))
                        {
                            // 获取排序值
                            Request.Form.TryGetValue(formKey, out var orderValueStr);

                            if (int.TryParse(orderValueStr, out int orderValue))
                            {
                                sortOrders[menuId] = orderValue;
                            }
                        }
                    }
                }

                if (!sortOrders.Any())
                {
                    return new JsonResult(new { ret = 0, msg = "没有有效的排序数据" });
                }

                // 验证菜单类型
                string type = menuType.ToString().ToLower();
                if (type != "nav" && type != "footer_nav")
                {
                    return new JsonResult(new { ret = 0, msg = "无效的菜单类型" });
                }

                // 调用服务更新排序
                bool success = await _menuService.UpdateMenuOrdersBatchAsync(sortOrders, type);

                if (success)
                {
                    _logger.LogInformation("菜单排序更新成功，类型: {MenuType}, 更新数量: {Count}", type, sortOrders.Count);
                    return new JsonResult(new { ret = 1, msg = "排序更新成功" });
                }
                else
                {
                    return new JsonResult(new { ret = 0, msg = "排序更新失败" });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "更新菜单排序时发生错误: {ErrorMessage}", ex.Message);
                return new JsonResult(new { ret = 0, msg = "更新菜单排序失败: " + ex.Message });
            }
        }


        /// <summary>
        /// 更新菜单隐藏状态
        /// </summary>
        /// <returns>操作结果</returns>
        [HttpPost]
        [Route("/manage/view/nav/menu-hidden-status")]
        public async Task<IActionResult> UpdateMenuHiddenStatus()
        {
            try
            {
                // 获取请求参数
                string mIdStr = Request.Form["MId"].ToString();
                string isHiddenStr = Request.Form["IsHidden"].ToString();

                _logger.LogInformation($"UpdateMenuHiddenStatus参数: MId={mIdStr}, IsHidden={isHiddenStr}");

                // 验证参数
                if (string.IsNullOrEmpty(mIdStr) || !int.TryParse(mIdStr, out int mId))
                {
                    return Ok(new { ret = 0, msg = "菜单ID参数无效" });
                }

                if (string.IsNullOrEmpty(isHiddenStr) || !int.TryParse(isHiddenStr, out int isHiddenInt))
                {
                    return Ok(new { ret = 0, msg = "隐藏状态参数无效" });
                }

                bool isHidden = isHiddenInt == 1;

                // 获取菜单信息
                var menu = await _menuService.GetMenuByIdAsync(mId);
                if (menu == null)
                {
                    return Ok(new { ret = 0, msg = "菜单不存在" });
                }

                // 更新隐藏状态
                menu.IsHidden = isHidden;
                bool result = await _menuService.SaveMenuAsync(menu);

                if (result)
                {
                    string message = isHidden ? "菜单已隐藏" : "菜单已显示";
                    return Ok(new { ret = 1, msg = message });
                }
                else
                {
                    return Ok(new { ret = 0, msg = "更新菜单状态失败" });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "更新菜单隐藏状态时出错");
                return Ok(new { ret = 0, msg = "更新菜单状态时出错" });
            }
        }

        /// <summary>
        /// 更新底部导航菜单隐藏状态
        /// </summary>
        /// <returns>操作结果</returns>
        [HttpPost]
        [Route("/manage/view/footer-nav/menu-hidden-status")]
        public async Task<IActionResult> UpdateFooterNavMenuHiddenStatus()
        {
            try
            {
                // 获取请求参数
                string mIdStr = Request.Form["MId"].ToString();
                string isHiddenStr = Request.Form["IsHidden"].ToString();

                _logger.LogInformation($"UpdateFooterNavMenuHiddenStatus参数: MId={mIdStr}, IsHidden={isHiddenStr}");

                // 验证参数
                if (string.IsNullOrEmpty(mIdStr) || !int.TryParse(mIdStr, out int mId))
                {
                    return Ok(new { ret = 0, msg = "菜单ID参数无效" });
                }

                if (string.IsNullOrEmpty(isHiddenStr) || !int.TryParse(isHiddenStr, out int isHiddenInt))
                {
                    return Ok(new { ret = 0, msg = "隐藏状态参数无效" });
                }

                bool isHidden = isHiddenInt == 1;

                // 获取菜单信息
                var menu = await _menuService.GetMenuByIdAsync(mId);
                if (menu == null)
                {
                    return Ok(new { ret = 0, msg = "菜单不存在" });
                }

                // 验证是否为底部导航菜单
                if (menu.Type != "footer_nav")
                {
                    return Ok(new { ret = 0, msg = "该菜单不是底部导航菜单" });
                }

                // 更新隐藏状态
                menu.IsHidden = isHidden;
                bool result = await _menuService.SaveMenuAsync(menu);

                if (result)
                {
                    string message = isHidden ? "底部导航菜单已隐藏" : "底部导航菜单已显示";
                    return Ok(new { ret = 1, msg = message });
                }
                else
                {
                    return Ok(new { ret = 0, msg = "更新底部导航菜单状态失败" });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "更新底部导航菜单隐藏状态时出错");
                return Ok(new { ret = 0, msg = "更新底部导航菜单状态时出错" });
            }
        }

        /// <summary>
        /// 创建导航菜单
        /// </summary>
        /// <returns>操作结果</returns>
        [HttpPost]
        [Route("/manage/view/nav/create")]
        public async Task<IActionResult> CreateNavMenu()
        {
            try
            {
                // 获取基本参数
                string parentIdStr = Request.Form["ParentId"].ToString();
                string levelStr = Request.Form["Level"].ToString();
                string navType = Request.Form["NavType"].ToString();
                string type = Request.Form["Type"].ToString();

                _logger.LogInformation(
                    $"CreateNavMenu参数: ParentId={parentIdStr}, Level={levelStr}, NavType={navType}, Type={type}");

                // 验证基本参数
                if (!int.TryParse(parentIdStr, out int parentId))
                {
                    parentId = 0;
                }

                if (!int.TryParse(levelStr, out int level))
                {
                    level = 1;
                }

                if (string.IsNullOrEmpty(type))
                {
                    type = "nav";
                }

                // 获取数组参数
                var names = Request.Form["Name[]"].ToArray();
                var newTargets = Request.Form["NewTarget[]"].ToArray();
                var links = Request.Form["Link[]"].ToArray();
                var icons = Request.Form["Icon[]"].ToArray();
                var subCates = Request.Form["SubCate[]"].ToArray();
                var types = Request.Form["type[]"].ToArray();
                var ids = Request.Form["id[]"].ToArray();

                if (names.Length == 0)
                {
                    return new JsonResult(new { ret = 0, msg = "请至少添加一个菜单项" });
                }

                // 获取父级菜单的UId
                string parentUId = "0,";
                if (parentId > 0)
                {
                    var parentMenu = await _menuService.GetMenuByIdAsync(parentId);
                    if (parentMenu != null)
                    {
                        parentUId = $"{parentMenu.UId}{parentMenu.MId},";
                    }
                }

                // 获取当前最大排序值
                var maxOrder = await GetMaxMenuOrder(type, parentUId);

                var createdMenus = new List<menu>();

                // 批量创建菜单
                for (int i = 0; i < names.Length; i++)
                {
                    if (string.IsNullOrEmpty(names[i])) continue;

                    var menuEntity = new menu
                    {
                        Name = BuildMultiLanguageMenuName(names[i]), // 使用多语言格式
                        Type = type,
                        UId = parentUId,
                        MyOrder = (sbyte)(maxOrder + i + 1),
                        IsHidden = false,
                        Sub = false
                    };

                    // 设置页面打开方式
                    if (i < newTargets.Length && int.TryParse(newTargets[i], out int newTarget))
                    {
                        menuEntity.NewTarget = newTarget == 1;
                    }

                    // 设置链接
                    if (i < links.Length)
                    {
                        menuEntity.Url = links[i];
                    }

                    // 设置图标
                    if (i < icons.Length)
                    {
                        menuEntity.Icon = icons[i];
                    }

                    // 设置导航类型（根据type[]参数）
                    if (i < types.Length)
                    {
                        string typeValue = types[i];
                        switch (typeValue)
                        {
                            case "index":
                                menuEntity.Nav = 1;
                                break;
                            case "article":
                                menuEntity.Nav = 2;
                                break;
                            case "category":
                                menuEntity.Nav = 3;
                                break;
                            case "products":
                                menuEntity.Nav = 4;
                                break;
                            case "special_offer":
                                menuEntity.Nav = 5;
                                break;
                            case "blog":
                                menuEntity.Nav = 6;
                                break;
                            case "add":
                            default:
                                menuEntity.Nav = 0; // 自定义
                                break;
                        }
                    }

                    // 设置关联ID
                    if (i < ids.Length && int.TryParse(ids[i], out int keyId))
                    {
                        menuEntity.KeyId = keyId;
                    }
                    

                    bool result = await _menuService.SaveMenuAsync(menuEntity);
                    if (result)
                    {
                        createdMenus.Add(menuEntity);
                    }
                }

                if (createdMenus.Count > 0)
                {
                    return new JsonResult(new
                    {
                        ret = 1,
                        msg = $"成功创建 {createdMenus.Count} 个菜单项"
                    });
                }
                else
                {
                    return new JsonResult(new { ret = 0, msg = "创建菜单失败" });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "创建导航菜单时出错");
                return new JsonResult(new { ret = 0, msg = "创建菜单时出错" });
            }
        }

        /// <summary>
        /// 获取系统菜单列表数据
        /// </summary>
        /// <returns>菜单数据</returns>
        [HttpPost]
        [Route("/manage/action/system-menu-list/")]
        public async Task<IActionResult> GetSystemMenuList()
        {
            try
            {
                // 获取请求参数
                Request.Form.TryGetValue("type", out var typeValue);
                Request.Form.TryGetValue("table", out var tableValue);
                Request.Form.TryGetValue("id", out var idValue);
                Request.Form.TryGetValue("start", out var startValue);

                string type = typeValue.ToString();
                string table = tableValue.ToString();
                int.TryParse(idValue.ToString(), out int id);
                int.TryParse(startValue.ToString(), out int start);

                // 每页显示数量
                int pageSize = 20;

                object result = new
                {
                    ret = 1,
                    msg = new
                    {
                        type = type,
                        table = table,
                        data = new List<object>(),
                        isMore = false,
                        next = start + pageSize,
                        id = id
                    }
                };

                // 根据不同类型获取数据
                switch (type.ToLower())
                {
                    case "article":
                        result = await GetArticleMenuData(type, table, id, start, pageSize);
                        break;
                    case "category":
                        result = await GetCategoryMenuData(type, table, id, start, pageSize);
                        break;
                    case "products":
                        result = await GetProductsMenuData(type, table, id, start, pageSize);
                        break;
                    default:
                        // 返回空数据
                        break;
                }

                return new JsonResult(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取系统菜单列表数据时发生错误: {ErrorMessage}", ex.Message);
                return new JsonResult(new { ret = 0, msg = "获取菜单数据失败: " + ex.Message });
            }
        }

        /// <summary>
        /// 获取文章菜单数据
        /// </summary>
        private async Task<object> GetArticleMenuData(string type, string table, int id, int start, int pageSize)
        {
            try
            {
                // 查询article表获取文章数据
                var query = _context.Queryable<article>()
                    .Where(a => a.AId > 0); // 基本条件

                // 获取总记录数
                var totalCount = await query.CountAsync();

                // 分页查询
                var articles = await query
                    .OrderBy(a => a.AId)
                    .Skip(start)
                    .Take(pageSize)
                    .Select(a => new
                    {
                        id = a.AId,
                        name = a.Title_en ?? "",
                        crumb = a.Title_en ?? "",
                        link = !string.IsNullOrEmpty(a.PageUrl) ? $"/pages/{a.PageUrl}" : $"/article/{a.AId}",
                        sub = 0
                    })
                    .ToListAsync();

                bool hasMore = totalCount > start + pageSize;

                return new
                {
                    ret = 1,
                    msg = new
                    {
                        type = type,
                        table = table,
                        data = articles,
                        isMore = hasMore,
                        next = start + pageSize,
                        id = id
                    }
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取文章菜单数据失败: {ErrorMessage}", ex.Message);
                return new
                {
                    ret = 0,
                    msg = new
                    {
                        type = type,
                        table = table,
                        data = new List<object>(),
                        isMore = false,
                        next = start + pageSize,
                        id = id
                    }
                };
            }
        }

        /// <summary>
        /// 获取分类菜单数据
        /// </summary>
        private async Task<object> GetCategoryMenuData(string type, string table, int id, int start, int pageSize)
        {
            try
            {
                // 查询products_category表获取分类数据
                var query = _context.Queryable<products_category>()
                    .Where(c => c.CateId > 0); // 基本条件

                // 如果指定了分类ID，则查询该分类的直接子分类
                if (id > 0)
                {
                    // 先获取父分类信息
                    var parentCategory = await _context.Queryable<products_category>()
                        .Where(c => c.CateId == id)
                        .FirstAsync();

                    if (parentCategory != null)
                    {
                        // 构建子分类的UId模式：父分类的UId + 父分类的CateId
                        string parentUid = parentCategory.UId ?? "";
                        string childUidPattern = $"{parentUid}{parentCategory.CateId}";

                        // 查询UId等于该模式的直接子分类
                        // 例如：父分类UId="0,", CateId=1000020，则查找UId="0,1000020"的分类
                        query = query.Where(c => !string.IsNullOrEmpty(c.UId) && c.UId == childUidPattern);
                    }
                    else
                    {
                        // 如果父分类不存在，返回空结果
                        query = query.Where(c => false);
                    }
                }
                else
                {
                    // id = 0 时，查询根分类（UId为"0,"或空或"0"的分类）
                    query = query.Where(c => string.IsNullOrEmpty(c.UId) || c.UId == "0," || c.UId == "0");
                }

                // 获取总记录数
                var totalCount = await query.CountAsync();

                // 分页查询
                var categoriesData = await query
                    .OrderBy(c => c.MyOrder)
                    .OrderBy(c => c.CateId)
                    .Skip(start)
                    .Take(pageSize)
                    .ToListAsync();

                // 获取所有分类数据用于构建树形结构
                var allCategories = await _context.Queryable<products_category>()
                    .Where(c => c.CateId > 0)
                    .ToListAsync();

                // 使用CategoryTreeBuilder构建分类树
                var categoryTree = CategoryTreeBuilder.BuildCategoryTreeGeneric(allCategories);

                // 构建返回数据
                var categories = new List<object>();
                foreach (var category in categoriesData)
                {
                    // 检查是否有子分类：查看分类树中是否存在以当前分类CateId为键的子分类列表
                    var hasChildren = categoryTree.ContainsKey(category.CateId) &&
                                      categoryTree[category.CateId].Count > 0;

                    categories.Add(new
                    {
                        id = category.CateId,
                        name = category.Category_en ?? "",
                        crumb = category.Category_en ?? "",
                        link = !string.IsNullOrEmpty(category.PageUrl)
                            ? $"/collections/{category.PageUrl}"
                            : $"/category/{category.CateId}",
                        sub = hasChildren ? 1 : 0
                    });
                }

                bool hasMore = totalCount > start + pageSize;

                return new
                {
                    ret = 1,
                    msg = new
                    {
                        type = type,
                        table = table,
                        data = categories,
                        isMore = hasMore,
                        next = start + pageSize,
                        id = id
                    }
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取分类菜单数据失败: {ErrorMessage}", ex.Message);
                return new
                {
                    ret = 0,
                    msg = new
                    {
                        type = type,
                        table = table,
                        data = new List<object>(),
                        isMore = false,
                        next = start + pageSize,
                        id = id
                    }
                };
            }
        }

        /// <summary>
        /// 获取产品菜单数据
        /// </summary>
        private async Task<object> GetProductsMenuData(string type, string table, int id, int start, int pageSize)
        {
            try
            {
                // 查询products表获取产品数据
                var query = _context.Queryable<products>()
                    .Where(p => p.ProId > 0); // 基本条件

                // 获取总记录数
                var totalCount = await query.CountAsync();

                // 分页查询
                var products = await query
                    .OrderBy(p => p.ProId)
                    .Skip(start)
                    .Take(pageSize)
                    .Select(p => new
                    {
                        id = p.ProId,
                        name = p.Name_en ?? "",
                        crumb = p.Name_en ?? "",
                        link = !string.IsNullOrEmpty(p.PageUrl) ? $"/products/{p.PageUrl}" : $"/product/{p.ProId}",
                        sub = 0
                    })
                    .ToListAsync();

                bool hasMore = totalCount > start + pageSize;

                return new
                {
                    ret = 1,
                    msg = new
                    {
                        type = type,
                        table = table,
                        data = products,
                        isMore = hasMore,
                        next = start + pageSize,
                        id = id
                    }
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取产品菜单数据失败: {ErrorMessage}", ex.Message);
                return new
                {
                    ret = 0,
                    msg = new
                    {
                        type = type,
                        table = table,
                        data = new List<object>(),
                        isMore = false,
                        next = start + pageSize,
                        id = id
                    }
                };
            }
        }

        /// <summary>
        /// 创建底部导航菜单
        /// </summary>
        /// <returns>操作结果</returns>
        // [HttpPost]
        // [Route("/manage/view/footer-nav")]
        // public async Task<IActionResult> CreateFooterNavMenu()
        // {
        //     try
        //     {
        //         // 获取基本参数
        //         string parentIdStr = Request.Form["ParentId"].ToString();
        //         string levelStr = Request.Form["Level"].ToString();
        //         string navType = Request.Form["NavType"].ToString();
        //         string type = "footer_nav"; // 固定为底部导航
        //
        //         _logger.LogInformation(
        //             $"CreateFooterNavMenu参数: ParentId={parentIdStr}, Level={levelStr}, NavType={navType}");
        //
        //         // 验证基本参数
        //         if (!int.TryParse(parentIdStr, out int parentId))
        //         {
        //             parentId = 0;
        //         }
        //
        //         if (!int.TryParse(levelStr, out int level))
        //         {
        //             level = 1;
        //         }
        //
        //         // 获取数组参数
        //         var names = Request.Form["Name[]"].ToArray();
        //         var newTargets = Request.Form["NewTarget[]"].ToArray();
        //         var links = Request.Form["Link[]"].ToArray();
        //         var icons = Request.Form["Icon[]"].ToArray();
        //         var subCates = Request.Form["SubCate[]"].ToArray();
        //         var types = Request.Form["type[]"].ToArray();
        //         var ids = Request.Form["id[]"].ToArray();
        //
        //         if (names.Length == 0)
        //         {
        //             return new JsonResult(new { ret = 0, msg = "请至少添加一个菜单项" });
        //         }
        //
        //         // 获取父级菜单的UId
        //         string parentUId = "0,";
        //         if (parentId > 0)
        //         {
        //             var parentMenu = await _menuService.GetMenuByIdAsync(parentId);
        //             if (parentMenu != null)
        //             {
        //                 parentUId = $"{parentMenu.UId}{parentMenu.MId},";
        //             }
        //         }
        //
        //         // 获取当前最大排序值
        //         var maxOrder = await GetMaxMenuOrder(type, parentUId);
        //
        //         var createdMenus = new List<menu>();
        //
        //         // 批量创建菜单
        //         for (int i = 0; i < names.Length; i++)
        //         {
        //             if (string.IsNullOrEmpty(names[i])) continue;
        //
        //             var menuEntity = new menu
        //             {
        //                 Name = BuildMultiLanguageMenuName(names[i]), // 使用多语言格式
        //                 Type = type,
        //                 UId = parentUId,
        //                 MyOrder = (sbyte)(maxOrder + i + 1),
        //                 IsHidden = false,
        //                 Sub = false
        //             };
        //
        //             // 设置页面打开方式
        //             if (i < newTargets.Length && int.TryParse(newTargets[i], out int newTarget))
        //             {
        //                 menuEntity.NewTarget = newTarget == 1;
        //             }
        //
        //             // 设置链接
        //             if (i < links.Length)
        //             {
        //                 menuEntity.Url = links[i];
        //             }
        //
        //             // 设置图标
        //             if (i < icons.Length)
        //             {
        //                 menuEntity.Icon = icons[i];
        //             }
        //
        //             // 设置导航类型（根据type[]参数）
        //             if (i < types.Length)
        //             {
        //                 string typeValue = types[i];
        //                 switch (typeValue)
        //                 {
        //                     case "index":
        //                         menuEntity.Nav = 1;
        //                         break;
        //                     case "article":
        //                         menuEntity.Nav = 2;
        //                         break;
        //                     case "category":
        //                         menuEntity.Nav = 3;
        //                         break;
        //                     case "products":
        //                         menuEntity.Nav = 4;
        //                         break;
        //                     case "special_offer":
        //                         menuEntity.Nav = 5;
        //                         break;
        //                     case "blog":
        //                         menuEntity.Nav = 6;
        //                         break;
        //                     case "add":
        //                     default:
        //                         menuEntity.Nav = 0; // 自定义
        //                         break;
        //                 }
        //             }
        //
        //             // 设置关联ID
        //             if (i < ids.Length && int.TryParse(ids[i], out int keyId))
        //             {
        //                 menuEntity.KeyId = keyId;
        //             }
        //
        //             // 移除Sub字段依赖 - 菜单父子关系完全通过UId构建
        //             // if (i < subCates.Length && int.TryParse(subCates[i], out int subCate))
        //             // {
        //             //     menuEntity.Sub = subCate == 1;
        //             // }
        //
        //             bool result = await _menuService.SaveMenuAsync(menuEntity);
        //             if (result)
        //             {
        //                 createdMenus.Add(menuEntity);
        //             }
        //         }
        //
        //         if (createdMenus.Count > 0)
        //         {
        //             return new JsonResult(new
        //             {
        //                 ret = 1,
        //                 msg = new
        //                 {
        //                     content = $"成功创建 {createdMenus.Count} 个底部导航菜单项",
        //                     jump = "/Store/FooterNav" // 强制刷新页面
        //                 }
        //             });
        //         }
        //         else
        //         {
        //             return new JsonResult(new { ret = 0, msg = "创建底部导航菜单失败" });
        //         }
        //     }
        //     catch (Exception ex)
        //     {
        //         _logger.LogError(ex, "创建底部导航菜单时出错");
        //         return new JsonResult(new { ret = 0, msg = "创建底部导航菜单时出错" });
        //     }
        // }

        /// <summary>
        /// 创建底部导航菜单
        /// </summary>
        /// <returns>操作结果</returns>
        [HttpPost]
        [Route("/manage/view/footer-nav/create")]
        public async Task<IActionResult> CreateFooterNavMenuSpecific()
        {
            try
            {
                // 获取基本参数
                string parentIdStr = Request.Form["ParentId"].ToString();
                string levelStr = Request.Form["Level"].ToString();
                string navType = Request.Form["NavType"].ToString();
                string type = "footer_nav"; // 固定为底部导航

                _logger.LogInformation(
                    $"CreateFooterNavMenuSpecific参数: ParentId={parentIdStr}, Level={levelStr}, NavType={navType}");

                // 验证基本参数
                if (!int.TryParse(parentIdStr, out int parentId))
                {
                    parentId = 0;
                }

                if (!int.TryParse(levelStr, out int level))
                {
                    level = 1;
                }

                // 获取数组参数
                var names = Request.Form["Name[]"].ToArray();
                var newTargets = Request.Form["NewTarget[]"].ToArray();
                var links = Request.Form["Link[]"].ToArray();
                var icons = Request.Form["Icon[]"].ToArray();
                var subCates = Request.Form["SubCate[]"].ToArray();
                var types = Request.Form["type[]"].ToArray();
                var ids = Request.Form["id[]"].ToArray();

                if (names.Length == 0)
                {
                    return new JsonResult(new { ret = 0, msg = "请至少添加一个菜单项" });
                }

                // 获取父级菜单的UId
                string parentUId = "0,";
                if (parentId > 0)
                {
                    var parentMenu = await _menuService.GetMenuByIdAsync(parentId);
                    if (parentMenu != null)
                    {
                        parentUId = $"{parentMenu.UId}{parentMenu.MId},";
                    }
                }

                // 获取当前最大排序值
                var maxOrder = await GetMaxMenuOrder(type, parentUId);

                var createdMenus = new List<menu>();

                // 批量创建菜单
                for (int i = 0; i < names.Length; i++)
                {
                    if (string.IsNullOrEmpty(names[i])) continue;

                    var menuEntity = new menu
                    {
                        Name = BuildMultiLanguageMenuName(names[i]), // 使用多语言格式
                        Type = type,
                        UId = parentUId,
                        MyOrder = (sbyte)(maxOrder + i + 1),
                        IsHidden = false,
                        Sub = false
                    };

                    // 设置页面打开方式
                    if (i < newTargets.Length && int.TryParse(newTargets[i], out int newTarget))
                    {
                        menuEntity.NewTarget = newTarget == 1;
                    }

                    // 设置链接
                    if (i < links.Length)
                    {
                        menuEntity.Url = links[i];
                    }

                    // 设置图标
                    if (i < icons.Length)
                    {
                        menuEntity.Icon = icons[i];
                    }

                    // 设置导航类型（根据type[]参数）
                    if (i < types.Length)
                    {
                        string typeValue = types[i];
                        switch (typeValue)
                        {
                            case "index":
                                menuEntity.Nav = 1;
                                break;
                            case "article":
                                menuEntity.Nav = 2;
                                break;
                            case "category":
                                menuEntity.Nav = 3;
                                break;
                            case "products":
                                menuEntity.Nav = 4;
                                break;
                            case "special_offer":
                                menuEntity.Nav = 5;
                                break;
                            case "blog":
                                menuEntity.Nav = 6;
                                break;
                            case "add":
                            default:
                                menuEntity.Nav = 0; // 自定义
                                break;
                        }
                    }

                    // 设置关联ID
                    if (i < ids.Length && int.TryParse(ids[i], out int keyId))
                    {
                        menuEntity.KeyId = keyId;
                    }


                    bool result = await _menuService.SaveMenuAsync(menuEntity);
                    if (result)
                    {
                        createdMenus.Add(menuEntity);
                    }
                }

                if (createdMenus.Count > 0)
                {
                    return new JsonResult(new
                    {
                        ret = 1,
                        msg = new
                        {
                            content = $"成功创建 {createdMenus.Count} 个底部导航菜单项",
                            jump = "/Store/FooterNav" // 强制刷新页面
                        }
                    });
                }
                else
                {
                    return new JsonResult(new { ret = 0, msg = "创建底部导航菜单失败" });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "创建底部导航菜单时出错");
                return new JsonResult(new { ret = 0, msg = "创建底部导航菜单时出错" });
            }
        }

        /// <summary>
        /// 获取指定类型和父级下的最大排序值
        /// </summary>
        /// <param name="type">菜单类型</param>
        /// <param name="parentUId">父级UId</param>
        /// <returns>最大排序值</returns>
        private async Task<int> GetMaxMenuOrder(string type, string parentUId)
        {
            try
            {
                var maxOrder = await _context.Queryable<menu>()
                    .Where(m => m.Type == type && m.UId == parentUId)
                    .MaxAsync(m => (int?)m.MyOrder);

                return maxOrder ?? 0;
            }
            catch
            {
                return 0;
            }
        }


        /// <summary>
        /// 构建导航下拉框HTML
        /// </summary>
        /// <param name="unitValue">当前选中值</param>
        /// <param name="unitType">当前选中类型</param>
        /// <returns>下拉框HTML</returns>
        private string BuildNavDropDoubleHtml(string unitValue, string unitType)
        {
            // 构建导航选项数据 - 使用与正确结构一致的数据
            var navOptions = new Dictionary<string, object>
            {
                ["index"] = new { Name = "首页", Type = "index", Table = "", Disabled = false },
                ["article"] = new { Name = "单页", Type = "article", Table = "article", Disabled = true },
                ["category"] = new { Name = "产品分类", Type = "category", Table = "products_category", Disabled = true },
                ["special_offer"] = new { Name = "促销产品", Type = "special_offer", Table = "", Disabled = false },
                ["products"] = new { Name = "产品", Type = "products", Table = "products", Disabled = true },
                ["blog"] = new { Name = "博客", Type = "blog", Table = "", Disabled = false },
                ["custom"] = new
                    { Name = "自定义", Value = "自定义", Type = "add", Table = "", KeyId = 0, PageId = 0, Disabled = false }
            };

            // 构建JSON数据 - 使用Newtonsoft.Json来保持一致性
            var jsonData = Newtonsoft.Json.JsonConvert.SerializeObject(navOptions);

            // HTML编码JSON数据
            var encodedJsonData = System.Web.HttpUtility.HtmlEncode(jsonData);

            return
                $@"<dl class=""box_drop_double edit_box"" data-checkbox=""0"" data-showadd=""1""><dt><input type=""text"" class=""box_input"" name=""UnitValue"" placeholder=""可选择下拉页面链接"" value=""{System.Web.HttpUtility.HtmlEncode(unitValue)}"" autocomplete=""off"" /><input type=""hidden"" name=""Unit"" value=""{System.Web.HttpUtility.HtmlEncode(unitValue)}"" class=""hidden_value"" /><input type=""hidden"" name=""UnitType"" value=""{System.Web.HttpUtility.HtmlEncode(unitType)}"" class=""hidden_type"" /></dt><dd class=""drop_down""><div class=""drop_menu"" data-type=""nav""><a href=""javascript:;"" class=""btn_back"" data-value="""" data-type="""" data-table="""" data-top=""0"" data-all=""0"" style=""display:none;"">返回</a><div class=""drop_skin"" style=""display:none;""></div><div class=""drop_list"" data=""{encodedJsonData}"" data-more=""none""></div><a href=""javascript:;"" class=""btn_load_more"" data-value="""" data-type="""" data-table="""" data-top=""0"" data-all=""0"" data-check-all="""" data-start=""0"" style=""display:none;"">加载更多</a></div></dd></dl>";
        }

        /// <summary>
        /// 构建多语言菜单名称JSON字符串
        /// </summary>
        /// <param name="name">菜单名称</param>
        /// <param name="defaultLanguage">默认语言，默认为"en"</param>
        /// <returns>多语言JSON字符串</returns>
        private string BuildMultiLanguageMenuName(string name, string defaultLanguage = "en")
        {
            if (string.IsNullOrEmpty(name))
            {
                return "{}";
            }

            // 如果已经是JSON格式，直接返回
            if (name.TrimStart().StartsWith("{"))
            {
                return name;
            }

            // 构建多语言JSON对象
            var multiLangName = new Dictionary<string, string>
            {
                { defaultLanguage, name }
            };

            return System.Text.Json.JsonSerializer.Serialize(multiLangName);
        }

        /// <summary>
        /// 构建导航菜单编辑表单HTML
        /// </summary>
        /// <param name="menuData">菜单数据</param>
        /// <param name="parentId">父级菜单ID</param>
        /// <returns>表单HTML</returns>
        private string BuildNavEditFormHtml(menu menuData, int parentId = 0)
        {
            // 确定表单标题
            string title = menuData.MId > 0 ? "修改菜单" : "添加菜单";

            // 解析菜单名称（处理多语言JSON格式）
            string displayName = ParseMenuName(menuData.Name);

            // 确定链接类型和值
            string unitValue = menuData.Url ?? "/";
            string unitType = "add";
            string customLink = menuData.Url ?? "/";

            // 根据URL判断链接类型
            if (!string.IsNullOrEmpty(menuData.Url))
            {
                switch (menuData.Url)
                {
                    case "/":
                        unitValue = "index";
                        unitType = "index";
                        break;
                    case "/special-offer":
                        unitValue = "special_offer";
                        unitType = "special_offer";
                        break;
                    case "/blog":
                        unitValue = "blog";
                        unitType = "blog";
                        break;
                    default:
                        unitValue = menuData.Url;
                        unitType = "add";
                        break;
                }
            }

            // 注意：这里需要包装在 .nav_edit 容器中，因为 load_edit_form 函数会查找这个选择器
            var html = $@"
<div class=""nav_edit"">
    <div class=""global_container nav_edit"" data-width=""350"">
        <div class=""top_title"">{title} <a href=""javascript:;"" class=""close""></a></div>
        <form id=""nav_edit_form"" class=""global_form nav_edit_box"" action=""/manage/view/nav/edit"" method=""post"">
            <div class=""rows clean"">
                <label>名称<span>（必填）</span></label>
                <div class=""input"">
                    <input type=""text"" name=""Name"" value=""{displayName}"" class=""box_input"" size=""46"" maxlength=""255"" notnull="""">
                </div>
            </div>
            <div class=""rows clean"">
                <label>页面打开方式</label>
                <div class=""input"">
                    <div class=""box_type_menu"">
                        <span class=""item {(menuData.NewTarget == false ? "checked" : "")}"">
                            <input type=""radio"" name=""NewTarget"" value=""0"" {(menuData.NewTarget == false ? "checked=\"\"" : "")}>
                            当前窗口打开页面
                        </span>
                        <span class=""item {(menuData.NewTarget == true ? "checked" : "")}"">
                            <input type=""radio"" name=""NewTarget"" value=""1"" {(menuData.NewTarget == true ? "checked=\"\"" : "")}>
                            新窗口打开页面
                        </span>
                        <span class=""item"">
                            <input type=""radio"" name=""NewTarget"" value=""2"">
                            不打开页面，仅展示子菜单
                        </span>
                    </div>
                </div>
            </div>
        <div class=""rows clean"" id=""box_url"">
            <label>链接</label>
            <div class=""input"">
                {BuildNavDropDoubleHtml(unitValue, unitType)}
            </div>
        </div>
        <div class=""rows clean"" id=""custom_link"" style=""{(unitType == "add" ? "" : "display: none;")}"">
            <label>自定义链接</label>
            <div class=""input"">
                <textarea name=""Link"" class=""box_textarea full_textarea"" maxlength=""255"" {(unitType == "add" ? "notnull=\"\"" : "")}>{customLink}</textarea>
            </div>
        </div>
        <div class=""rows clean box_button box_submit"">
            <div class=""input"">
                <input type=""button"" class=""btn_global btn_submit"" value=""保存"">
                <input type=""button"" class=""btn_global btn_cancel"" value=""取消"">
            </div>
        </div>
            <input type=""hidden"" name=""MId"" value=""{menuData.MId}"">
            <input type=""hidden"" name=""ParentId"" value=""{parentId}"">
            <input type=""hidden"" name=""Type"" value=""{menuData.Type ?? "nav"}"">
            <input type=""hidden"" name=""do_action"" value=""/manage/view/nav/edit"">
        </form>
    </div>
</div>";

            return html;
        }

        /// <summary>
        /// 解析菜单名称（处理多语言JSON格式）
        /// </summary>
        /// <param name="name">菜单名称</param>
        /// <param name="preferredLanguage">首选语言，默认为"en"</param>
        /// <returns>解析后的显示名称</returns>
        private string ParseMenuName(string name, string preferredLanguage = "en")
        {
            if (string.IsNullOrEmpty(name))
            {
                return string.Empty;
            }

            // 如果不是JSON格式，直接返回原始文本
            if (!name.TrimStart().StartsWith("{"))
            {
                return name;
            }

            try
            {
                // 尝试解析JSON
                var jsonDoc = System.Text.Json.JsonDocument.Parse(name);
                var root = jsonDoc.RootElement;

                // 首先尝试获取首选语言
                if (root.TryGetProperty(preferredLanguage, out var preferredElement))
                {
                    return preferredElement.GetString() ?? name;
                }

                // 如果首选语言不存在，尝试获取英文
                if (preferredLanguage != "en" && root.TryGetProperty("en", out var enElement))
                {
                    return enElement.GetString() ?? name;
                }

                // 如果英文也不存在，获取第一个可用的语言
                foreach (var property in root.EnumerateObject())
                {
                    if (!string.IsNullOrEmpty(property.Value.GetString()))
                    {
                        return property.Value.GetString();
                    }
                }
            }
            catch (System.Text.Json.JsonException)
            {
                // JSON解析失败，返回原始文本
                return name;
            }

            return name;
        }

        #endregion
    }
}