using Microsoft.AspNetCore.Mvc;
using YseStore.IService;
using YseStore.Service;
using Entitys;

namespace YseStoreAdmin.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class SettingPhotosController : ControllerBase
    {
        private readonly ISettingPhotosService _settingPhotosService;
        public readonly IConfigService _configService;
        private readonly ILogger<CodeEditController> _logger;

        public SettingPhotosController(ISettingPhotosService settingPhotosService, ILogger<CodeEditController> logger, IConfigService configService)
        {
            _settingPhotosService = settingPhotosService;
            _logger = logger;
            _configService = configService;
        }
        [HttpPost("FileImage")]
        public async Task<IActionResult> FileImage([FromForm] List<string> PicPath, [FromForm] List<string> FileSize, [FromForm] List<string> FileOriname, [FromForm] List<string> tagsName)
        {
            var showType = Request.Query["showtype"].ToString();
            var b = await _settingPhotosService.AddImageList(PicPath, FileSize, FileOriname, tagsName);
            var ret = MessageModel<string>.Message(b, "", null, $"/Setting/Photos?showType={showType}");
            return Ok(ret);
        }

        [HttpPost("TagsUpdate")]
        public async Task<IActionResult> TagsUpdate([FromForm] string PId, [FromForm] List<string> tagsName)
        {

            var b = _settingPhotosService.Updatephoto_tagsId(PId, tagsName);
            return Ok(new { ret = true, msg = new { content = "更改成功", jump = "/Setting/Photos" } });
        }

        [HttpGet("DelPhoto")]
        public async Task<IActionResult> DelPhoto(string id)
        {
            var b = _settingPhotosService.Delphoto(id);
            return Ok(new { ret = true, msg = new { content = "删除成功", jump = "/Setting/Photos" } });
        }
        [HttpGet("HasImg")]
        public async Task<IActionResult> HasImg(string PicPath)
        {

            return Ok(new { ret = true, msg = "" });
        }
        [HttpPost("CheckOption")]
        public async Task<IActionResult> CheckOption()
        {
            var Keyword = Request.Form["Keyword"];
            var Type = Request.Form["Type"];
            var b = _settingPhotosService.getPhotoTagsContains(Keyword, Type);
            return Ok(new { ret = 1, msg = new { Fixed = new { IsIndex = "首页显示", IsNew = "新品", IsHot = "热卖", IsBestDeals = "畅销" } } });
        }


    }
}
