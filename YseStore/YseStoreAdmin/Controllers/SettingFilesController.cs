using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using YseStore.IService;

namespace YseStoreAdmin.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class SettingFilesController : ControllerBase
    {
        private readonly ISettingFilesService _settingFilesService;
        public readonly IConfigService _configService;
        private readonly ILogger<CodeEditController> _logger;

        public SettingFilesController(ISettingFilesService settingFilesService, ILogger<CodeEditController> logger, IConfigService configService)
        {
            _settingFilesService = settingFilesService;
            _logger = logger;
            _configService = configService;
        }
        [HttpPost("FileImage")]
        public async Task<IActionResult> FileImage([FromForm] List<string> FilePath, [FromForm] List<string> FileSize, [FromForm] List<string> FileOriname, [FromForm] List<string> tagsName)
        {

            var b =await _settingFilesService.AddFileList(FilePath, FileSize, FileOriname,tagsName);
            return Ok(new { ret = true, msg = new { content = "保存成功", jump = "/Setting/Files" } });
        }

        [HttpPost("TagsUpdate")]
        public async Task<IActionResult> TagsUpdate([FromForm] string id, [FromForm] List<string> tagsName)
        {

            var b = _settingFilesService.Updatephoto_tagsId(id, tagsName);
            return Ok(new { ret = true, msg = new { content = "保存成功", jump = "/Setting/Files" } });
        }

        [HttpGet("DelPhoto")]
        public async Task<IActionResult> DelPhoto(string id)
        {
            var b = _settingFilesService.Delphoto(id);
            return Ok(new { ret = true, msg = new { content = "保存成功", jump = "/Setting/Files" } });
        }
    }
}
