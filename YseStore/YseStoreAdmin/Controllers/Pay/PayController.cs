using Entitys;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Localization;
using Newtonsoft.Json;
using System.Xml.Linq;
using YseStore.Common;
using YseStore.Common.Cache;
using YseStore.Common.Helper;
using YseStore.IService;
using YseStore.IService.Order;
using YseStore.IService.Pay;
using YseStore.Model.FromBody;
using YseStore.Model.VM;
using YseStore.Model.VM.Payment.OceanPayment;
using YseStore.Service.Pay;
using IPaymentService = YseStore.IService.Pay.IPaymentService;


namespace YseStoreAdmin.Controllers.Pay
{
    /// <summary>
    /// 支付方式控制器
    /// </summary>
    [Route("api/[controller]")]
    [ApiController]
    public class PayController : ControllerBase
    {
        public readonly IPaymentService _paymentService;
        public readonly IConfigService _configService;
        public readonly ICaching _caching;
        public readonly IPluginsService _pluginsService;
        public readonly IOrderService _orderServices;
        public readonly IStringLocalizer<PayController> T;
        public readonly IOrderPaymentInfoService _orderPaymentInfoService;
        public readonly IOrderRefundService _orderRefundService;
        public readonly IPayPalServices _payPalServices;
        public readonly IPayoneerServices _payoneerServices;
        private readonly IOceanPaymentService _oceanPaymentService;
        public readonly IOrderLogService _orderLogServices;

        public PayController(IPaymentService paymentService, IConfigService configService, ICaching caching, IPluginsService pluginsService, IOrderService orderService, IStringLocalizer<PayController> t, IPayPalServices payPalServices, IPayoneerServices payoneerServices, IOrderLogService orderLogServices, IOceanPaymentService oceanPaymentService)
        {
            _paymentService = paymentService;
            _configService = configService;
            _caching = caching;
            _pluginsService = pluginsService;
            _orderServices = orderService;
            T = t;
            _payPalServices = payPalServices;
            _payoneerServices = payoneerServices;
            _orderLogServices = orderLogServices;
            _oceanPaymentService = oceanPaymentService;
        }

        /// <summary>
        /// 编辑支付方式
        /// </summary>
        /// <returns></returns>
        [Route("/manage/set/payment/edit/")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> PaymentEdit()
        {
            var Value = Request.Form["Value[]"].ToString();
            var Name = Request.Form["Name[]"].ToString();
            var AdditionalFee = Request.GetFormDecimal("AdditionalFee");
            var AffixPrice = Request.GetFormDecimal("AffixPrice");
            var MinPrice = Request.GetFormDecimal("MinPrice");
            var MaxPrice = Request.GetFormDecimal("MaxPrice");
            var NoMaxLimit = Request.GetFormByte("NoMaxLimit");
            //var IsDescription = Request.Form["IsDescription"].ToString();

            var Description_en = Request.Form["Description_en"].ToString();
            var PId = Request.GetFormInt("PId");
            var showType = Request.GetFormInt("showType");

            var paymentCacheKey = GlobalConstVars.UsedPaymentList;

            //  // 处理
            var payment_row = await _paymentService.QueryById(PId);
            if (payment_row == null)
            {
                payment_row = new Entitys.payment();
            }
            var isGet = payment_row.IsGet;

            payment_row.IsGet = true;
            payment_row.IsUsed = true;
            payment_row.MinPrice = MinPrice;
            payment_row.MaxPrice = MaxPrice;
            payment_row.AdditionalFee = AdditionalFee;
            payment_row.AffixPrice = AffixPrice;
            payment_row.UpdateTime = DateTimeHelper.ConvertToUnixTimestamp(DateTime.Now);
            payment_row.Description_en = Description_en;
            payment_row.NoMaxLimit = (sbyte)NoMaxLimit;


            if (showType == 2)
            {
                //线下支付


            }
            else
            {
                Dictionary<string, string> data = new Dictionary<string, string>();
                // 非线下支付
                var nameList = Name.Split(',').ToList();
                var valueList = Value.Split(',').ToList();
                foreach (var name in nameList)
                {
                    var index = nameList.IndexOf(name);
                    data[name] = valueList[index];
                }

                if (payment_row.Method == "Paypal")
                {
                    var IsExpressCheckout = Request.GetFormInt("IsExpressCheckout"); // 是否开启 快捷支付
                    var IsPayLater = Request.GetFormInt("IsPayLater");// 是否开启 Pay Later 消息
                    var IsPayLaterButton = Request.GetFormInt("IsPayLaterButton"); // 是否开启 Pay Later 按钮
                    var IsCreditCard = Request.GetFormInt("IsCreditCard");// 是否开启 信用卡按钮
                    var IsExpressCreditCardGoods = Request.GetFormInt("IsExpressCreditCardGoods");// 是否开启 信用卡按钮(快捷支付)(产品详情页)
                    var IsExpressCreditCardCart = Request.GetFormInt("IsExpressCreditCardCart");// 是否开启 信用卡按钮(快捷支付)(购物车页)
                    var IsExpressCreditCardCheckout = Request.GetFormInt("IsExpressCreditCardCheckout");// 是否开启 信用卡按钮(快捷支付)(结账页)
                    var IsCreditCardPay = Request.GetFormInt("IsCreditCardPay");// 是否开启 信用卡付款
                    var CreditCardPayMethod = Request.GetFormString("CreditCardPayMethod", "independent");// 信用卡付款类型(独立信用卡付款按钮 or 内嵌信用卡付款)
                    var CreditCard3DS = Request.GetFormInt("CreditCard3DS");// 3DS验证
                    var IsLocalPayment = Request.GetFormInt("IsLocalPayment");// 是否开启 本地支付
                    var IsDescription = Request.GetFormInt("IsDescription");// 是否开启 付款说明
                    var IsSyncTrack = Request.GetFormInt("IsSyncTrack"); // 是否开启 同步物流信息
                    var IsLanguage = Request.GetFormInt("IsLanguage"); // 是否开启 根据访客浏览器切换对应语言
                    var IsPending = Request.GetFormInt("IsPending");// 是否开启 待处理的订单改为已付款
                    var IsVault = Request.GetFormInt("IsVault");// 是否开启 快捷存储支付信息
                    var IsGooglepayButton = Request.GetFormInt("IsGooglepayButton");// 是否开启 Google Pay
                    var IsGooglepayExpressButton = Request.GetFormInt("IsGooglepayExpressButton");// 是否开启 Google Pay快捷支付
                    var IsApplepayButton = Request.GetFormInt("IsApplepayButton");// 是否开启 Apple Pay
                    var IsApplepayExpressButton = Request.GetFormInt("IsApplepayExpressButton"); // 是否开启 Apple Pay快捷支付

                    var IsAttr = 1;
                    var MerchantData = await _configService.GetConfigByGroup("paypal", "Onboarding");
                    if (MerchantData != null)
                    {
                        var MerchantJSON = MerchantData.Value.JsonToObj<Dictionary<string, string>>();
                        if (MerchantJSON.ContainsKey("merchantId") && MerchantJSON["merchantId"] != "")
                        {
                            IsAttr = 0; //PayPal商家账号已绑定
                        }
                    }

                    if (IsAttr == 1)
                    {
                        payment_row.NewAttribute = data.ToJson();
                    }


                    //更新paypal
                    var updatePaypal = await _paymentService.Update(payment_row);

                    // 更新PayPal快捷支付
                    payment data_excheckout = await _paymentService.QueryByClauseAsync(it => it.Method == "Excheckout");
                    data_excheckout.IsGet = true;
                    data_excheckout.IsUsed = IsExpressCheckout == 1 ? true : false;
                    data_excheckout.MinPrice = MinPrice;
                    data_excheckout.MaxPrice = MaxPrice;
                    data_excheckout.AdditionalFee = AdditionalFee;
                    data_excheckout.AffixPrice = AffixPrice;
                    data_excheckout.UpdateTime = DateTimeHelper.ConvertToUnixTimestamp(DateTime.Now);
                    data_excheckout.Description_en = Description_en;
                    data_excheckout.NoMaxLimit = (sbyte)NoMaxLimit;
                    if (IsAttr == 1)
                    {
                        data_excheckout.NewAttribute = data.ToJson();
                    }
                    var updatedata_excheckout = await _paymentService.Update(data_excheckout);

                    // CreditCard
                    var creditCardData = await _paymentService.QueryByClauseAsync(it => it.Method == "PaypalCreditCard");
                    creditCardData.MinPrice = MinPrice;
                    creditCardData.MaxPrice = MaxPrice;
                    creditCardData.AdditionalFee = AdditionalFee;
                    creditCardData.AffixPrice = AffixPrice;
                    creditCardData.UpdateTime = DateTimeHelper.ConvertToUnixTimestamp(DateTime.Now);
                    creditCardData.Description_en = Description_en;
                    creditCardData.NoMaxLimit = (sbyte)NoMaxLimit;
                    var updatecreditCardData = await _paymentService.Update(creditCardData);

                    //Google Pay
                    var googlePayData = await _paymentService.QueryByClauseAsync(it => it.Method == "PaypalGooglePay");
                    googlePayData.MinPrice = MinPrice;
                    googlePayData.MaxPrice = MaxPrice;
                    googlePayData.AdditionalFee = AdditionalFee;
                    googlePayData.AffixPrice = AffixPrice;
                    googlePayData.UpdateTime = DateTimeHelper.ConvertToUnixTimestamp(DateTime.Now);
                    googlePayData.Description_en = Description_en;
                    googlePayData.NoMaxLimit = (sbyte)NoMaxLimit;
                    if (IsGooglepayButton != 1)
                    {
                        IsGooglepayExpressButton = 0;
                    }
                    googlePayData.IsGet = IsGooglepayButton == 1;
                    googlePayData.IsUsed = IsGooglepayButton == 1;
                    var updategooglePayData = await _paymentService.Update(googlePayData);

                    // Apple Pay
                    var applePayData = await _paymentService.QueryByClauseAsync(it => it.Method == "PaypalApplePay");
                    applePayData.MinPrice = MinPrice;
                    applePayData.MaxPrice = MaxPrice;
                    applePayData.AdditionalFee = AdditionalFee;
                    applePayData.AffixPrice = AffixPrice;
                    applePayData.UpdateTime = DateTimeHelper.ConvertToUnixTimestamp(DateTime.Now);
                    applePayData.Description_en = Description_en;
                    applePayData.NoMaxLimit = (sbyte)NoMaxLimit;
                    if (IsApplepayButton != 1)
                    {
                        IsApplepayExpressButton = 0;
                    }
                    applePayData.IsGet = IsApplepayButton == 1;
                    applePayData.IsUsed = IsApplepayButton == 1;
                    var updateapplePayData = await _paymentService.Update(applePayData);


                    Dictionary<string, object> paypalDict = new Dictionary<string, object>
                        {
                            { "IsPayLater", IsPayLater },
                            { "IsPayLaterButton", IsPayLaterButton },
                            { "IsCreditCard", IsCreditCard },
                            { "IsExpressCreditCardGoods", IsExpressCreditCardGoods },
                            { "IsExpressCreditCardCart", IsExpressCreditCardCart },
                            { "IsExpressCreditCardCheckout", IsExpressCreditCardCheckout },
                            { "IsCreditCardPay", IsCreditCardPay },
                            { "CreditCardPayMethod", CreditCardPayMethod },
                            { "CreditCard3DS", CreditCard3DS },
                            { "IsLocalPayment", IsLocalPayment },
                            { "IsDescription", IsDescription },
                            { "IsSyncTrack", IsSyncTrack },
                            { "IsLanguage", IsLanguage },
                            { "IsPending", IsPending },
                            { "IsVault", IsVault },
                            { "IsGooglepayButton", IsGooglepayButton },
                            { "IsGooglepayExpressButton", IsGooglepayExpressButton },
                            { "IsApplepayButton", IsApplepayButton },
                            { "IsApplepayExpressButton", IsApplepayExpressButton }
                        };

                    var setConfig = await _configService.SetConfig("paypal", "Config", paypalDict.ToJson());



                }
                else if (payment_row.SystemType == "custom")
                {
                    // 自定义付款 (信用卡、本地支付)
                    Dictionary<string, object> json_ary = new Dictionary<string, object>
                    {
                        {"ClientId", Request.GetFormString("ClientId") },
                        {"Key", Request.GetFormString("Key") },
                        {"PaymentUrl", Request.GetFormString("PaymentUrl") },
                        {"RefundUrl", Request.GetFormString("RefundUrl") },
                        {"ClientId", Request.GetFormString("ClientId") },
                        {"ShippedUrl", Request.GetFormString("ShippedUrl") },
                    };

                    payment_row.Attribute = json_ary.ToJson();
                    payment_row.LogoPath = Request.GetFormString("LogoPath");
                    string Name_en = Request.GetFormString("Name_en");
                    if (!string.IsNullOrEmpty(Name_en))
                    {
                        payment_row.Name_en = Name_en;
                    }
                    if (payment_row.ShowType == 1)
                    {
                        payment_row.Card = Request.GetFormString("Card");
                    }

                    //更新
                    var update = await _paymentService.Update(payment_row);

                    Dictionary<string, string> nameAry = nameAry = new Dictionary<string, string>
                    {
                        { "cn", Request.GetFormString("Name_en") },
                        { "en", Request.GetFormString("Name_en") }
                    };

                    string pName = $"API{PId}";
                    var plugins = await _pluginsService.QueryByClauseAsync(it => it.ClassName == pName && it.Category == "payment");
                    if (plugins != null)
                    {
                        plugins.Name = nameAry.ToJson();

                        var updatePlugins = await _pluginsService.Update(plugins);
                    }
                }
                else
                {
                    payment_row.Attribute = data.ToJson();
                    var ActionMethod = Request.GetFormString("ActionMethod");
                    if (!ActionMethod.IsNullOrEmpty())
                    {
                        payment_row.ActionMethod = ActionMethod;
                    }
                    if (payment_row.ShowType == 1)
                    {
                        string cart = Request.GetFormString("Card[]");
                        var cartList = cart.Split(',').ToList();

                        payment_row.Card = cartList.ToJson();
                    }

                    //更新
                    var update = await _paymentService.Update(payment_row);
                }


            }

            //操作日志 		HelpsManage::operationLog('编辑支付渠道', Payment::getValue(['PId' => $p_PId], 'Name_en'));



            //移除缓存
            await _caching.DelByPatternAsync(paymentCacheKey);
            await _caching.DelByPatternAsync(GlobalConstVars.UsedPaymentHash);

            return Ok(new { ret = true, msg = GlobalConstVars.EditSuccess });


        }



        /// <summary>
        /// 禁用支付方式
        /// </summary>
        /// <param name="PID"></param>
        /// <returns></returns>
        [Route("/manage/set/payment/del")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> PaymentDel(int PId)
        {
            var paymentCacheKey = GlobalConstVars.UsedPaymentList;

            var payment_row = await _paymentService.QueryById(PId);

            if (payment_row == null)
            {
                return Ok(new { ret = false, msg = GlobalConstVars.DataisNo });
            }

            if (payment_row.Method == "Custom")
            {
                // 线下自定义付款,删除直接删掉
                var del = await _paymentService.DeleteById(PId);

            }
            else if (payment_row.Method == "API")
            {
                // 自定义付款,删除直接删掉
                var del = await _paymentService.DeleteById(PId);


                string pName = $"API{PId}";
                var plugins = await _pluginsService.QueryByClauseAsync(it => it.ClassName == pName && it.Category == "payment");
                if (plugins != null)
                {
                    var delPlugin = await _pluginsService.DeleteById(plugins.PluginId);
                }
            }
            else
            {
                // 非线下自定义付款,删掉只是关闭按钮
                payment_row.IsGet = false;
                payment_row.IsUsed = false;
                var update = await _paymentService.Update(payment_row);
                if (PId == 1)
                {
                    //PayPal，直接关联快捷支付，快捷支付也关了
                    var excheckout = await _paymentService.QueryByClauseAsync(it => it.PId == 2);
                    if (excheckout != null)
                    {
                        excheckout.IsGet = false;
                        excheckout.IsUsed = false;
                        await _paymentService.Update(excheckout);
                    }

                    //PayPal，解除授权绑定
                    var paypalConfig = await _configService.QueryByClauseAsync(it => it.GroupId == "paypal" && it.Variable == "Onboarding" && it.Variable != "");
                    if (paypalConfig != null)
                    {
                        var setConfig = await _configService.SetConfig("paypal", "Onboarding", "");
                    }

                    var paypalConfig2 = await _configService.QueryByClauseAsync(it => it.GroupId == "paypal" && it.Variable == "MerchantInfo" && it.Variable != "");
                    if (paypalConfig2 != null)
                    {
                        var setConfig2 = await _configService.SetConfig("paypal", "MerchantInfo", "");
                    }

                }
            }

            // PayPal Apple Pay注册域名
            if (PId == 1)
            {
                var setConfig = await _configService.SetConfig("paypal", "SdkToken", "");
                var setConfig1 = await _configService.SetConfig("paypal", "PaymentToken", "");

                //PaypalApplePay
                //PaypalGooglePay
                var applePayData = await _paymentService.QueryByClauseAsync(it => it.Method == "PaypalApplePay");
                if (applePayData != null)
                {
                    applePayData.IsGet = false;
                    applePayData.IsUsed = false;
                    await _paymentService.Update(applePayData);
                }

                var googlePayData = await _paymentService.QueryByClauseAsync(it => it.Method == "PaypalGooglePay");
                if (googlePayData != null)
                {
                    googlePayData.IsGet = false;
                    googlePayData.IsUsed = false;
                    await _paymentService.Update(googlePayData);
                }

            }

            //      HelpsManage::operationLog('删除支付渠道', $payment_row->Name_en);

            //移除缓存
            await _caching.DelByPatternAsync(paymentCacheKey);
            await _caching.DelByPatternAsync(GlobalConstVars.UsedPaymentHash);

            return Ok(new { ret = true, msg = GlobalConstVars.EditSuccess });
        }


        /// <summary>
        /// 前台排序
        /// </summary>
        /// <returns></returns>
        [Route("/manage/set/payment/order/")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> PaymentOrder()
        {
            var paymentCacheKey = GlobalConstVars.UsedPaymentList;

            string sort_order = Request.GetQueryString("sort_order"); //1,9,154

            if (sort_order.IsNullOrEmpty())
            {
                return Ok(new { ret = false, msg = GlobalConstVars.DataisNo });
            }

            //支付方式id
            List<int> payId = sort_order.Split(',').Select(it => int.Parse(it)).ToList();

            var paymentList = await _paymentService.Query(it => payId.Contains(it.PId));

            //需要更新的支付方式列表   
            List<payment> paymentAry = new List<payment>();
            foreach (var pId in payId)
            {
                var item = paymentList.FirstOrDefault(it => it.PId == pId);

                int index = payId.IndexOf(pId);
                item.MyOrder = (short)(index + 1);

                paymentAry.Add(item);
            }

            var update = await _paymentService.Update(paymentAry);
            if (update)
            {
                //移除缓存
                await _caching.DelByPatternAsync(paymentCacheKey);
                await _caching.DelByPatternAsync(GlobalConstVars.UsedPaymentHash);


                return Ok(new { ret = true, msg = GlobalConstVars.EditSuccess });
            }
            else
            {
                return Ok(new { ret = false, msg = GlobalConstVars.EditFailure });
            }

        }





        #region paypal退款
        /// <summary>
        /// paypal退款
        /// </summary>
        /// <param name="entity"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("/api/payments/Paypal/Refund")]
        public async Task<IActionResult> PaypalRefunds([FromBody] FMStringId entity)
        {
            var jm = new WebApiCallBack<YseStore.Model.VM.Payment.Paypal.RefundResp>();
            var obj = JsonConvert.DeserializeAnonymousType(entity.data.ToString(), new
            {
                refundId = "",

            });

            var order = await _orderServices.QueryByClauseAsync(it => it.OId == entity.id);//获取订单详情
            if (order == null)
            {
                jm.status = false;
                jm.msg = T["web.global.orderNotFind"];
                return Ok(jm);
            }

            if (order.PaymentStatus == "refunded")
            {
                jm.status = false;
                jm.msg = T["订单已退款，不能重复退款"];
                return Ok(jm);
            }

            var refund = await _orderRefundService.QueryByClauseAsync(it => it.RefundId == obj.refundId);
            if (refund == null)
            {
                jm.status = false;
                jm.msg = "退款单不存在";
                return Ok(jm);
            }

            var r = await _payPalServices.Refunds(order, refund);
            if (r.status)
            {
                jm.data = r.data;

                //创建退款单
                //退款成功 订单记录
                var logOption = _orderServices.WriteOrderLog("refund", order);
                //订单记录
                var orderLog = new orders_log
                {
                    IsAdmin = true,
                    UserId = order.UserId,
                    UserName = "",
                    OrderId = order.OrderId,
                    Log = $"{entity.id}|退款成功",
                    LogManage = logOption.LogTitle,
                    LogData = logOption.LogData,
                    PaymentStatus = "refunded",
                    ShippingStatus = order.ShippingStatus,
                    AccTime = DateTimeHelper.ConvertToUnixTimestamp(DateTime.Now),
                    Ip = Request.GetIP(),
                    OrderStatus = order.OrderStatus,
                    TId = 0,
                };
                await _orderLogServices.AddWithIntId(orderLog);

                ////订单记录
                //var orderLog = new OrderLog
                //{
                //    orderId = order.orderId,
                //    userId = order.userId,
                //    type = (int)GlobalEnumVars.OrderLogTypes.LOG_TYPE_TO_PAY,
                //    msg = $"{entity.data?.ToString()}|退款",
                //    data = JsonConvert.SerializeObject(jm),
                //    createTime = DateTime.Now,
                //    creater = "",
                //};
                //await _orderLogServices.InsertAsync(orderLog);


            }
            else
            {
                jm.msg = r.msg;
            }
            jm.status = r.status;

            return Ok(jm);
        }
        #endregion


        #region payoneer退款

        /// <summary>
        /// Payoneer 退款
        /// </summary>
        /// <returns></returns>
        [AllowAnonymous]
        [HttpPost]
        [Route("/api/payments/Payoneer/Refund")]
        public async Task<IActionResult> PayoneerRefunds([FromBody] FMStringId entity)
        {
            var jm = new WebApiCallBack<YseStore.Model.VM.Payment.Payoneer.RefundResp>();

            try
            {
                var obj = JsonConvert.DeserializeAnonymousType(entity.data.ToString(), new
                {
                    refundId = "",

                });

                var order = await _orderServices.QueryByClauseAsync(it => it.OId == entity.id);//获取订单详情
                if (order == null)
                {
                    jm.status = false;
                    jm.msg = T["web.global.orderNotFind"];
                    return Ok(jm);
                }

                if (order.PaymentStatus == "refunded")
                {
                    jm.status = false;
                    jm.msg = "订单已退款，不能重复退款";
                    return Ok(jm);
                }

                var refund = await _orderRefundService.QueryByClauseAsync(it => it.RefundId == obj.refundId);
                if (refund == null)
                {
                    jm.status = false;
                    jm.msg = "退款单不存在";
                    return Ok(jm);
                }

                //调用退款接口
                var r = await _payoneerServices.Refunds(order, refund);
                if (r.status)
                {

                    //退款成功 订单记录
                    var logOption = _orderServices.WriteOrderLog("refund", order);
                    //订单记录
                    var orderLog = new orders_log
                    {
                        IsAdmin = true,
                        UserId = order.UserId,
                        UserName = "",
                        OrderId = order.OrderId,
                        Log = $"{entity.id}|退款成功",
                        LogManage = logOption.LogTitle,
                        LogData = logOption.LogData,
                        PaymentStatus = "refunded",
                        ShippingStatus = order.ShippingStatus,
                        AccTime = DateTimeHelper.ConvertToUnixTimestamp(DateTime.Now),
                        Ip = Request.GetIP(),
                        OrderStatus = order.OrderStatus,
                        TId = 0,
                    };
                    await _orderLogServices.AddWithIntId(orderLog);

                    //订单记录
                    //var orderLog = new OrderLog
                    //{
                    //    orderId = order.orderId,
                    //    userId = order.userId,
                    //    type = (int)GlobalEnumVars.OrderLogTypes.LOG_TYPE_TO_PAY,
                    //    msg = $"{entity.data.ToString()}|退款",
                    //    data = JsonConvert.SerializeObject(jm),
                    //    createTime = DateTime.Now,
                    //    creater = "",
                    //};
                    //await _orderLogServices.InsertAsync(orderLog);

                    jm.status = true;
                    jm.data = r.data;
                }
                else
                {
                    jm.msg = r.msg;
                }
                jm.status = r.status;

                return Ok(jm);
            }
            catch (Exception ex)
            {
                jm.status = false;
                jm.msg = ex.Message;

                return Ok(jm);
            }


        }

        #endregion

        #region 钱海退款



        /// <summary>
        /// 钱海退款
        /// </summary>
        /// <param name="entity"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("/api/payments/OceanPayment/Refund")]
        public async Task<IActionResult> OceanPaymentRefund([FromBody] FMStringId entity)
        {
            var jm = new WebApiCallBack<XElement>();

            try
            {
                var obj = JsonConvert.DeserializeAnonymousType(entity.data.ToString(), new
                {
                    refundId = "",

                });

                var order = await _orderServices.QueryByClauseAsync(it => it.OId == entity.id);//获取订单详情
                if (order == null)
                {
                    jm.status = false;
                    jm.msg = T["web.global.orderNotFind"];
                    return Ok(jm);
                }

                if (order.PaymentStatus == "refunded")
                {
                    jm.status = false;
                    jm.msg = "订单已退款，不能重复退款";
                    return Ok(jm);
                }

                var refund = await _orderRefundService.QueryByClauseAsync(it => it.RefundId == obj.refundId);
                if (refund == null)
                {
                    jm.status = false;
                    jm.msg = "退款单不存在";
                    return Ok(jm);
                }

                //调用退款接口
                var r = await _oceanPaymentService.Refunds(order, refund);
                if (r.status)
                {

                    //退款成功 订单记录
                    var logOption = _orderServices.WriteOrderLog("refund", order);
                    //订单记录
                    var orderLog = new orders_log
                    {
                        IsAdmin = true,
                        UserId = order.UserId,
                        UserName = "",
                        OrderId = order.OrderId,
                        Log = $"{entity.id}|退款成功",
                        LogManage = logOption.LogTitle,
                        LogData = logOption.LogData,
                        PaymentStatus = "refunded",
                        ShippingStatus = order.ShippingStatus,
                        AccTime = DateTimeHelper.ConvertToUnixTimestamp(DateTime.Now),
                        Ip = Request.GetIP(),
                        OrderStatus = order.OrderStatus,
                        TId = 0,
                    };
                    await _orderLogServices.AddWithIntId(orderLog);


                    jm.status = true;
                    jm.data = r.data;
                }
                else
                {
                    jm.msg = r.msg;
                }
                jm.status = r.status;

                return Ok(jm);
            }
            catch (Exception ex)
            {
                jm.status = false;
                jm.msg = ex.Message;

                return Ok(jm);
            }


        }

        #endregion
    }
}
