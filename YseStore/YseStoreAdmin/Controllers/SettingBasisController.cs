using Aop.Api.Domain;
using Entitys;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using SqlSugar.Extensions;
using System;
using System.Text.Json;
using System.Text.RegularExpressions;
using YseStore.Common;
using YseStore.Common.Cache;
using YseStore.IService;
using YseStore.Model.Response;
using YseStore.Service;

namespace YseStoreAdmin.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class SettingBasisController : ControllerBase
    {
        public readonly ISettingBasisService _settingBasisService;
        private readonly ILogger<CodeEditController> _logger;
        private readonly ICaching _caching;
        public SettingBasisController(ISettingBasisService settingBasisService, ILogger<CodeEditController> logger, ICaching caching)
        {
            _settingBasisService = settingBasisService;
            _logger = logger;
            _caching = caching;
        }
        [HttpPost("EditConfigs")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> SaveConfigs()
        {
            try
            {


                var Configs = await _settingBasisService.GetConfigsAry();
                List<config> configList = new List<config>();

                var SiteName = Request.Form["SiteName"];
                configList.Add(new config
                {
                    GroupId = "global",
                    Variable = "SiteName",
                    Value = SiteName
                });

                //icon图标
                var IcoPath = Request.Form["IcoPath"];
                configList.Add(new config
                {
                    GroupId = "global",
                    Variable = "IcoPath",
                    Value = IcoPath
                });
                var Blog = Request.Form["CopyRight_en"];
                configList.Add(new config
                {
                    GroupId = "global",
                    Variable = "Blog",
                    Value = Blog
                });
                var FreeShippingPrice = Request.Form["FreeShippingPrice"];
                configList.Add(new config
                {
                    GroupId = "global",
                    Variable = "FreeShippingPrice",
                    Value = FreeShippingPrice
                });

                //博客右侧图片
                var BlogPath = Request.Form["BlogPath"];
                configList.Add(new config
                {
                    GroupId = "global",
                    Variable = "BlogPath",
                    Value = BlogPath
                });
                var BlogTitle = Request.Form["BlogTitle"];
                configList.Add(new config
                {
                    GroupId = "global",
                    Variable = "BlogTitle",
                    Value = BlogTitle
                });
                var BlogBrief = Request.Form["BlogBrief"];
                configList.Add(new config
                {
                    GroupId = "global",
                    Variable = "BlogBrief",
                    Value = BlogBrief
                });
                var GoodsFooterModeTopTitle1 = Request.Form["GoodsFooterModeTopTitle1"];
                configList.Add(new config
                {
                    GroupId = "global",
                    Variable = "GoodsFooterModeTopTitle1",
                    Value = GoodsFooterModeTopTitle1
                });
                var GoodsFooterModeTopTitle2 = Request.Form["GoodsFooterModeTopTitle2"];
                configList.Add(new config
                {
                    GroupId = "global",
                    Variable = "GoodsFooterModeTopTitle2",
                    Value = GoodsFooterModeTopTitle2
                });
                if (Configs != null)
                {
                    for (int i = 1; i <= Configs.Where(c => c.GroupId == "global" && c.Variable.Contains("GoodsFooterModeTitle")).Count(); i++)
                    {
                        var GoodsFooterModeTitle = Request.Form["GoodsFooterModeTitle" + i + ""];
                        configList.Add(new config
                        {
                            GroupId = "global",
                            Variable = "GoodsFooterModeTitle" + i,
                            Value = GoodsFooterModeTitle
                        });
                        var GoodsFooterModePath = Request.Form["GoodsFooterModePath" + i + ""];
                        configList.Add(new config
                        {
                            GroupId = "global",
                            Variable = "FooterModePath" + i,
                            Value = GoodsFooterModePath
                        });


                        var GoodsFooterModeBrief = Request.Form["GoodsFooterModeBrief" + i + ""];
                        configList.Add(new config
                        {
                            GroupId = "global",
                            Variable = "GoodsFooterModeBrief" + i,
                            Value = GoodsFooterModeBrief
                        });
                    }
                    for (int i = 1; i <= Configs.Where(c => c.GroupId == "global" && c.Variable.StartsWith("FooterModeTitle")).Count(); i++)
                    {
                        var FooterModeTitle = Request.Form["FooterModeTitle" + i + ""];
                        configList.Add(new config
                        {
                            GroupId = "global",
                            Variable = "FooterModeTitle" + i,
                            Value = FooterModeTitle
                        });
                        var FooterModePath = Request.Form["FooterModePath" + i + ""];
                        configList.Add(new config
                        {
                            GroupId = "global",
                            Variable = "FooterModePath" + i,
                            Value = FooterModePath
                        });
                        var FooterModeBrief = Request.Form["FooterModeBrief" + i + ""];
                        configList.Add(new config
                        {
                            GroupId = "global",
                            Variable = "FooterModeBrief" + i,
                            Value = FooterModeBrief
                        });
                    }
                }
                //底部Logo
                var FooterPath = Request.Form["FooterPath"];
                configList.Add(new config
                {
                    GroupId = "global",
                    Variable = "FooterPath",
                    Value = FooterPath
                });
                var FooterBrief = Request.Form["FooterBrief"];
                configList.Add(new config
                {
                    GroupId = "global",
                    Variable = "FooterBrief",
                    Value = FooterBrief
                });
                var FooterAddress = Request.Form["FooterAddress"];
                configList.Add(new config
                {
                    GroupId = "global",
                    Variable = "FooterAddress",
                    Value = FooterAddress
                });
                var FooterEmail = Request.Form["FooterEmail"];
                configList.Add(new config
                {
                    GroupId = "global",
                    Variable = "FooterEmail",
                    Value = FooterEmail
                });
                var FooterTelephone = Request.Form["FooterTelephone"];
                configList.Add(new config
                {
                    GroupId = "global",
                    Variable = "FooterTelephone",
                    Value = FooterTelephone
                });
                var TimeZone = Request.Form["TimeZone"];
                configList.Add(new config
                {
                    GroupId = "global",
                    Variable = "TimeZone",
                    Value = TimeZone
                });

                var Compeny = Request.Form["Compeny"];
                configList.Add(new config
                {
                    GroupId = "print",
                    Variable = "Compeny",
                    Value = Compeny
                });
                var AdminEmail = Request.Form["AdminEmail"];
                configList.Add(new config
                {
                    GroupId = "global",
                    Variable = "AdminEmail",
                    Value = AdminEmail
                });
                var Address = Request.Form["Address"];
                configList.Add(new config
                {
                    GroupId = "print",
                    Variable = "Address",
                    Value = Address
                });
                var City = Request.Form["City"];
                configList.Add(new config
                {
                    GroupId = "print",
                    Variable = "City",
                    Value = City
                });
                var State = Request.Form["State"];
                configList.Add(new config
                {
                    GroupId = "print",
                    Variable = "State",
                    Value = State
                });
                var ZipCode = Request.Form["ZipCode"];
                configList.Add(new config
                {
                    GroupId = "print",
                    Variable = "ZipCode",
                    Value = ZipCode
                });
                var Telephone = Request.Form["Telephone"];
                configList.Add(new config
                {
                    GroupId = "print",
                    Variable = "Telephone",
                    Value = Telephone
                });
                var range = Request.Form["range"];
                var display = Request.Form["display"];
                ConfigReview configReview = new ConfigReview();
                configReview.range = Convert.ToInt32(range);
                configReview.display = Convert.ToInt32(display);
                string configReviewJson = JsonSerializer.Serialize(configReview);
                configList.Add(new config
                {
                    GroupId = "products_show",
                    Variable = "review",
                    Value = configReviewJson
                });

                await _settingBasisService.UpdetConfigAry(configList);
                await _caching.DelByPatternAsync(GlobalConstVars.config_key);//删除缓存

                List<currency> currencyList = new List<currency>();
                // 获取所有需要更新的索引
                var indexes = new List<int>();
                foreach (string key in Request.Form.Keys)
                {
                    if (key.StartsWith("CurrencyRate["))
                    {
                        var match = Regex.Match(key, @"^CurrencyRate\[(\d+)\]$");
                        if (match.Success)
                        {
                            int index = int.Parse(match.Groups[1].Value);
                            indexes.Add(index);
                        }
                    }
                }
                // 遍历每个索引处理数据
                foreach (int index in indexes.Distinct())
                {
                    // 提取表单数据
                    bool isWebShow = Request.Form[$"IsWebShow[{index}]"] == "1";
                    string exchangeRate = Request.Form[$"CurrencyRate[{index}]"];
                    string method = Request.Form[$"CurrencyMethod[{index}]"];
                    sbyte proportion =  (sbyte)Convert.ToInt32(Request.Form[$"CurrencyProportion[{index}]"]);
                    bool proportionStatus = Request.Form[$"CurrencyProportionStatus[{index}]"] == "1";

                    // 构建更新对象
                    currencyList.Add(new currency {
                        CId = index,
                        IsWebShow = isWebShow,
                        ExchangeRate = exchangeRate,
                        Method = method,
                        Proportion = proportion,
                        ProportionStatus = proportionStatus,
                        IsUsed=true
                    });
                   
                }
                int ManageDefaultCurrency = Request.Form["ManageDefaultCurrency"].ObjToInt();
                int DefaultCurrency = Request.Form["DefaultCurrency"].ObjToInt();

                await _settingBasisService.UpdetCurrencyAry(currencyList, ManageDefaultCurrency, DefaultCurrency);

               



                return Ok(new { ret = true, msg = new { content = "保存成功", jump = "/Setting/Index" } });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "保存协议时发生未处理异常");
                return StatusCode(500, "服务器内部错误");
            }
        }
    }
}
