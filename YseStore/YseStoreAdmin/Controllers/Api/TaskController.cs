using Entitys;
using Flurl.Http;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using OfficeOpenXml.FormulaParsing.Excel.Functions.Math;
using YseStore.Common;
using YseStore.Common.Cache;
using YseStore.Common.Helper;
using YseStore.IService;
using YseStore.IService.Order;
using YseStore.IService.Sales;
using YseStore.IService.Shopping;
using YseStore.Model.VM;
using YseStore.Model.VM.Payment.Paypal;
using YseStore.Model.VM.Sales;
using YseStoreAdmin.Controllers.Sales;

namespace YseStoreAdmin.Controllers.Api
{
    [Route("api/[controller]/[action]")]
    [ApiController]
    [AllowAnonymous]
    [LocalNetworkOnly]
    public class TaskController : ControllerBase
    {

        private readonly IEmailService _emailService;
        private readonly IOrderService _orderService;
        private readonly IShoppingCartServices _cartServices;
        private readonly ICaching _caching;
        private readonly ILogger<TaskController> _logger;
        private readonly IFlashSaleService _flashSaleService;
        private readonly ISyncV3DataService _v3Service;
        private readonly IRecallListService _recallListService;
        public TaskController(IEmailService emailService, IOrderService orderService, IShoppingCartServices cartServices,
            ICaching caching, ILogger<TaskController> logger, IFlashSaleService flashSaleService, ISyncV3DataService v3Service,
            IRecallListService recallListService)
        {
            _emailService = emailService;
            _orderService = orderService;
            _cartServices = cartServices;
            _caching = caching;
            _logger = logger;
            _flashSaleService = flashSaleService;
            _v3Service = v3Service;
            _recallListService = recallListService;
        }


        /// <summary>
        /// 定时邮件同步服务
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public IActionResult EmailRedisSyncService()
        {

            _emailService.EmailRedisSyncService();
            return Ok(new { message = "执行邮件同步" });
        }


        /// <summary>
        /// 定时发送邮件服务
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public async Task<IActionResult> SendEmailService()
        {
            var result = await _emailService.SendEmailService();
            if (result.status)
            {
                return Ok(new { message = "执行邮件发送" });
            }
            else
            {
                return Ok(new { message = result.msg });
            }
        }

        /// <summary>
        /// 添加弃单日志里的记录
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public async Task<IActionResult> ShoppingCartRecallLog()
        {

            var result = await _recallListService.AddShoppingCartRecallLog();
            if (result.status)
            {
                return Ok(new { message = result.msg });
            }
            else
            {
                return Ok(new { message = "执行弃单日志召回失败"+ result.msg });
            }

        }

        /// <summary>
        /// 购物车召回邮件
        /// 加购1小时触发
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public async Task<IActionResult> RecordCartProducts()
        {

            var result = await _cartServices.RecallCar(1);
            if (result.status)
            {
                return Ok(new { message = "执行购物车召回" });
            }
            else
            {
                return Ok(new { message = result.msg });
            }

        }

        /// <summary>
        /// 订单召回邮件
        /// 创建订单多长时间后触发
        /// 一分钟执行一次
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public async Task<IActionResult> RecordOrder()
        {

            var result = await _orderService.RecordOrder(1);
            if (result.status)
            {
                return Ok(new { message = "执行订单召回" });
            }
            else
            {
                return Ok(new { message = result.msg });
            }
        }



        #region 订单任务
        /// <summary>
        /// 获取订单
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public async Task<IActionResult> GetOrderApi()
        {
            var updstart = Request.GetQueryLong("updstart");
            var updend = Request.GetQueryLong("updend");

            DateTime stime = DateTimeHelper.ConvertToBeijingTime(updstart);
            DateTime etime = DateTimeHelper.ConvertToBeijingTime(updend);


            var r = await _orderService.GetApiOrdersList(stime, etime);
            if (r.status)
            {
                return Ok(r.data);
            }

            return Ok("");
        }


        /// <summary>
        /// 获取订单
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public async Task<IActionResult> GetOrderByOIdApi()
        {
            string OIdList = Request.GetQueryString("OId");

            List<string> list = OIdList.Split(',').ToList();

            var r = await _orderService.GetApiOrdersList(list);
            if (r.status)
            {
                return Ok(r.data);
            }

            return Ok("");
        }

        #endregion


        #region 单次执行，同步数据
        /// <summary>
        /// 同步用户
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public async Task<WebApiCallBack> GetV3UserList()
        {
            int IsClear = Request.GetQueryInt("IsClear");


            var r = await _v3Service.GetV3UserList(IsClear == 1);

            return r;
        }

        /// <summary>
        /// 同步收货地址列表
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public async Task<WebApiCallBack> GetV3ShipAddressList()
        {
            int IsClear = Request.GetQueryInt("IsClear");
            var r = await _v3Service.GetV3ShipAddressList(IsClear == 1);


            return r;
        }

        /// <summary>
        /// 同步产品评论
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public async Task<WebApiCallBack> GetV3ProductFeedbackList()
        {
            int IsClear = Request.GetQueryInt("IsClear");

            var r = await _v3Service.GetV3ProductFeedbackList(IsClear == 1);
            return r;
        }

        /// <summary>
        /// 同步产品分类
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public async Task<WebApiCallBack> GetV3CagegoryList()
        {
            int IsClear = Request.GetQueryInt("IsClear");
            string oldKey = Request.GetQueryString("oldKey"); //旧的关键词
            string newKey = Request.GetQueryString("newKey");//新的关键词

            var r = await _v3Service.GetV3CagegoryList(IsClear == 1, oldKey, newKey);
            return r;
        }

        /// <summary>
        /// 同步产品
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public async Task<WebApiCallBack> GetV3ProductList()
        {
            int IsClear = Request.GetQueryInt("IsClear");
            string oldKey = Request.GetQueryString("oldKey"); //旧的关键词
            string newKey = Request.GetQueryString("newKey");//新的关键词
            string imageDomain = Request.GetQueryString("imageDomain");//图片域名  ：v3.retekess.es

            var r = await _v3Service.GetV3ProductList(IsClear == 1, oldKey, newKey, imageDomain);
            return r;

        }


        /// <summary>
        /// 同步博客分类
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public async Task<WebApiCallBack> GetV3BlogCagegoryList()
        {
            int IsClear = Request.GetQueryInt("IsClear");
            string oldKey = Request.GetQueryString("oldKey"); //旧的关键词
            string newKey = Request.GetQueryString("newKey");//新的关键词

            var r = await _v3Service.GetV3BlogCateList(IsClear == 1, oldKey, newKey);
            return r;
        }


        /// <summary>
        /// 同步博客
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public async Task<WebApiCallBack> GetV3BlogList()
        {
            int IsClear = Request.GetQueryInt("IsClear");
            string oldKey = Request.GetQueryString("oldKey"); //旧的关键词
            string newKey = Request.GetQueryString("newKey");//新的关键词
            string imageDomain = Request.GetQueryString("imageDomain");//图片域名  ：v3.retekess.es

            var r = await _v3Service.GetV3BlogList(IsClear == 1, oldKey, newKey, imageDomain);
            return r;
        }

        /// <summary>
        /// 同步博客评论
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public async Task<WebApiCallBack> GetV3BlogReviewList()
        {
            int IsClear = Request.GetQueryInt("IsClear");

            var r = await _v3Service.GetV3BlogReview(IsClear == 1);
            return r;
        }

        /// <summary>
        /// 同步页面
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public async Task<WebApiCallBack> GetV3PageList()
        {
            int IsClear = Request.GetQueryInt("IsClear");
            string oldKey = Request.GetQueryString("oldKey"); //旧的关键词
            string newKey = Request.GetQueryString("newKey");//新的关键词
            string imageDomain = Request.GetQueryString("imageDomain");//图片域名  ：v3.retekess.es

            var r = await _v3Service.GetV3PageList(IsClear == 1, oldKey, newKey, imageDomain);
            return r;
        }

        /// <summary>
        /// url重定向
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public async Task<WebApiCallBack> GetUrlRedirectList()
        {
            int IsClear = Request.GetQueryInt("IsClear");

            string proOldKey = Request.GetQueryString("proOldKey"); //旧的关键词
            string proNewKey = Request.GetQueryString("proNewKey", "products");//新的关键词
            ValueTuple<string, string> proKey = new(proOldKey, proNewKey);

            string proCateOldKey = Request.GetQueryString("proCateOldKey"); //旧的关键词
            string proCateNewKey = Request.GetQueryString("proCateNewKey", "collections");//新的关键词
            ValueTuple<string, string> proCateKey = new(proCateOldKey, proCateNewKey);

            string blogOldKey = Request.GetQueryString("blogOldKey", "blog"); //旧的关键词
            string blogNewKey = Request.GetQueryString("blogNewKey", "blog");//新的关键词
            ValueTuple<string, string> blogKey = new(blogOldKey, blogNewKey);

            string blogCateOldKey = Request.GetQueryString("blogCateOldKey", "blog/category"); //旧的关键词
            string blogCateNewKey = Request.GetQueryString("blogCateNewKey", "blog/category");//新的关键词
            ValueTuple<string, string> blogCateKey = new(blogCateOldKey, blogCateNewKey);

            string pageOldKey = Request.GetQueryString("pageOldKey"); //旧的关键词
            string pageNewKey = Request.GetQueryString("pageNewKey", "pages");//新的关键词
            ValueTuple<string, string> pageKey = new(pageOldKey, pageNewKey);

            //重定向
            var r = await _v3Service.GetV3UrlRedirectList(productCate: proCateKey, product: proKey, blogCate: blogCateKey, blog: blogKey, page: pageKey, IsClear == 1);
            return r;

        }

        /// <summary>
        /// 订单同步
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public async Task<WebApiCallBack> GetV3OrderList()
        {
            int IsClear = Request.GetQueryInt("IsClear");

            var r = await _v3Service.GetV3OrderList(IsClear == 1);
            return r;
        }


        #endregion

        #region test
        public async Task<IActionResult> testOrder()
        {
            //后台执行
            string url = AppSettingsConstVars.AdminSiteUrl + $"/common/mail/order_detail/?orderId=1000985&culture=en";
            //邮件模板内容
            string OrderDetails = await url.GetAsync().ReceiveString();


            return Content(OrderDetails);
        }


        #endregion



        #region 定时更新商品促销价格

        [HttpGet]
        public async Task<IActionResult> UpdatePromotionToProduct()
        {
            try
            {
                bool status = false;
                var res = _caching.EqueuePop<VM_EqueuFlashSalesProduct>(GlobalConstVars.key_EqueuFlashSalesProduct, out status);
                var currentTime = DateTimeOffset.UtcNow.ToUnixTimeSeconds();
                if (status && res != null)
                {
                    if (res.StartTime < currentTime)//促销活动开始了
                    {
                        await _flashSaleService.UpdatePromotionToProductAsync((int)res.UseProductsType, res.CryptUseProducts, res.ConditionPrice ?? 0);
                    }
                    else
                    {
                        _caching.EqueuePush<VM_EqueuFlashSalesProduct>(GlobalConstVars.key_EqueuFlashSalesProduct, res);
                    }
                }
                return Ok(new { ret = res, msg = "定时更新商品促销价格" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "UpdatePromotionToProductAsync发生未处理异常");
                return StatusCode(500, "服务器内部错误");
            }
        }


        #endregion


    }
}
