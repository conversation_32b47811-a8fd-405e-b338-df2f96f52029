{
  "global": {
    "n_y": [ "否", "<font class=\"fc_red\">是</font>" ],
    "n_y_ary": [ "否", "是" ],
    "my_order_ary": [ "默认" ],
    "add": "添加",
    "del": "删除",
    "del_bat": "批量删除",
    "edit": "修改",
    "edit_bat": "批量修改",
    "mod": "编辑",
    "disable": "禁用",
    "copy": "复制",
    "export": "导出",
    "import": "导入",
    "batch": "批量",
    "set": "设置",
    "my_order": "排序",
    "date": "日期",
    "time": "时间",
    "submit": "提交",
    "confirm": "确认",
    "confirm_sure": "确定",
    "save": "保存",
    "reset": "重置",
    "return": "返回",
    "cancel": "取消",
    "close": "关闭",
    "all": "所有",
    "all_to": "全部",
    "serial": "序号",
    "name": "名称",
    "email": "邮箱",
    "operation": "操作",
    "option": "选项",
    "search": "搜索",
    "default": "默认",
    "last": "最后",
    "authorization": "授权",
    "print": "打印",
    "category": "分类",
    "sub_category": "子分类",
    "subjection": "隶属",
    "implode": "导入",
    "explode": "导出",
    "view": "查看",
    "move": "移动",
    "move_to": "移动到",
    "turn_on": "开启",
    "used": "启用",
    "use": "使用",
    "in_use": "已使用",
    "no_use": "不使用",
    "required": "必填",
    "select_all": "全选",
    "anti": "反选",
    "open": "展开",
    "pack_up": "收起",
    "pic": "图片",
    "file": "文件",
    "preview": "预览",
    "release": "发布",
    "status": "状态",
    "synchronize": "同步",
    "key_sync": "一键同步",
    "logo": "Logo",
    "custom_column": "自定义列",
    "seo": "标题与标签",
    "title": "标题",
    "subtitle": "副标题",
    "buttontxt": "按钮文案",
    "keyword": "关键词",
    "depict": "描述",
    "url": "链接",
    "brief": "简短介绍",
    "description": "详细介绍",
    "myorder": "排序优先级",
    "pre_page": "<<上一页",
    "next_page": "下一页>>",
    "upload_pic": "上传图片",
    "select": "选择",
    "select_index": "请选择",
    "select_write": "请选择或填写",
    "do_error": "操作无效",
    "isindex": "首页显示",
    "type": "类型",
    "domain": "域名",
    "reference": "参考文档",
    "excel_format": "Excel导出",
    "please_enter": "请输入",
    "placeholder": "请输入关键词",
    "old_version": "旧版",
    "new_version": "新版",
    "other": "其他",
    "total": "共",
    "item": "件",
    "more": "更多",
    "clear": "清除",
    "file_upload": "选择文件",
    "editor": "编辑器",
    "send": "发送",
    "management": "管理",
    "message": "消息",
    "amount": "金额",
    "content": "内容",
    "translation": "翻译",
    "translation_chars": "剩余%s字符",
    "language": "语言",
    "public": "公共",
    "help": "帮助中心",
    "help_doc": "帮助文档",
    "welfare": "营销视频",
    "upload_file": "上传文件",
    "down_table": "下载表格",
    "click_here": "请<a href=\"%url%\" class=\"no_load\">点击此处</a>下载表格,并按照要求填写好表格。",
    "upload_table": "上传表格并提交",
    "submit_table": "上传填写好的表格，然后提交。",
    "add_new": "新增",
    "load_more": "加载更多",
    "not_limit": "不限",
    "how_to_get": "如何获取",
    "support": [ "企业QQ", "上班时间", "电话号码", "微信号", "投诉电话", "网站到期时间" ],
    "tmp_domain_alert": "能更快访问网站管理后台，解决国内访问国外的网站常常会被屏蔽的问题，前台使用%domain%访问",
    "expired_tips": "会员<span>%days%</span>天后到期, <i>点此延期</i>",
    "expired_tips1": "网站已过期,请联系客服",
    "inquiry": "查询",
    "wechat_ser": "微信服务",
    "scan_code": "扫描代码添加微信服务",
    "first": "最前",
    "app_download": "APP下载：",
    "iso_QR": "IOS系统二维码",
    "android_QR": "安卓系统二维码",
    "scan_browser": "使用浏览器扫描",
    "website_space": "网站容量",
    "rename": "重命名",
    "wechat_contact": "微信联系",
    "online_chat": "在线沟通",
    "online_service": "在线客服",
    "free_service": "获取一对一免费客户服务",
    "wechat_chuang": "客服微信",
    "free_ver": "免费版",
    "free_upgrade": "升级商业版",
    "my_": "店铺续费",
    "shop_expired": "店铺已过期",
    "shop_over_day": "店铺剩余 %day% 天",
    "paid_ver": "商业版",
    "conatact_ser": "联系您的客服",
    "invoice": "* 购买商业版可提供合同与正规发票",
    "guide": "使用教程",
    "know": "知道了",
    "learn_more": "了解详情",
    "understand_more": "了解更多",
    "next": "查看下一条",
    "only_bg": "仅后台可见",
    "phone": "手机号",
    "filter": "筛选",
    "more_filter": "更多筛选",
    "copy_url": "复制链接",
    "selected": "已选择<span></span>个",
    "all_cate": "全部分类",
    "variable_tips": "{OrderNum}表示订单号, {OrderPrice}表示订单金额, {OrderCurrency}表示订单货币, 用来替换需要实时更新的内容",
    "i_have_read": "我已阅读",
    "manage_language": "后台语言",
    "shop_language": "店铺语言",
    "application": "申请",
    "statistics": "统计",
    "free_gift": "赠品",
    "ship_from": "发货地",
    "finish": "提前结束",
    "activate": "激活",
    "date_range": "日期范围",
    "start_time": "开始时间",
    "end_time": "结束时间",
    "time_zone": "当前时区",
    "set_time_zone": "设置时区",
    "detail": "详细",
    "qty": "数量",
    "update": "更新",
    "custom": "自定义",
    "save_continue": "保存并继续添加",
    "download": "下载",
    "less_ie_11": "不支持 Internet Explorer 11 及以下版本的浏览器打开，建议使用其他浏览器!",
    "enter": "进入",
    "prev_btn": "上一步",
    "next_btn": "下一步",
    "install": "安装",
    "pages": {
      "index": "首页",
      "list": "产品列表页",
      "goods": "产品详细页",
      "search": "搜索结果页",
      "article": "单页",
      "account": "会员中心",
      "cart": "购物车",
      "success": "支付成功页",
      "blog-list": "博客列表页",
      "blog-detail": "博客详细页"
    },
    "none": "无",
    "placeholder_sku": "请输入关键词/SKU",
    "noAuthority": "没有操作权限",
    "upload_tips": "或拖入本地图片",
    "pc_pic": "PC端图片",
    "mobile_pic": "移动端图片",
    "miniprogram": "小程序",
    "scan_wechat": "微信扫一扫<br>即可查看店铺数据",
    "goHelp": "前往{{link}}了解详细",
    "new_message": "有 <span>{{num}}</span> 条新消息，请及时查看"
  },
  "module": {
    "account": {
      "module_name": "概览",
      "index": "概览",
      "password": "修改密码",
      "login": "管理员登录"
    },
    "orders": {
      "module_name": "订单",
      "orders": "订单管理",
      "recall": "弃单管理",
      "waybill": "运单管理",
      "import_delivery": "标记发运",
      "asiafly_sheet": "申通取件"
    },
    "products": {
      "module_name": "产品",
      "products": "产品管理",
      "stock-list": "库存管理",
      "category": "分类",
      "review": "评论",
      "switch": "切换卡",
      "salesticker": "促销标签",
      "business": {
        "module_name": "供应商"
      },
      "sync": "数据同步",
      "inquiry": "询盘",
      "product_collection": "产品采集"
    },
    "user": {
      "module_name": "客户",
      "user": "客户",
      "inbox": "站内信",
      "formtool": "表单工具"
    },
    "sales": {
      "module_name": "营销",
      "sales": "限时促销",
      "package": "组合购买",
      "promotion": "组合促销",
      "coupon": "优惠券",
      "discount": "满减活动",
      "operation-activities": "弹窗公告"
    },
    "operation": {
      "module_name": "运营",
      "operation-activities": "弹窗公告",
      "email": {
        "module_name": "邮件",
        "send": "邮件发送",
        "config": "邮件设置",
        "system": "系统邮件设置",
        "email_logs": "邮件发送日志"
      },
      "arrival": "到货通知",
      "newsletter": "订阅管理",
      "chat": {
        "module_name": "在线客服",
        "set": "设置",
        "chat": "帐号管理"
      },
      "googlefeed": "Google Feed",
      "gallery": "买家秀",
      "distribution": "分销",
      "blog": {
        "module_name": "博客管理",
        "set": "博客设置",
        "blog": "博客管理",
        "category": "博客分类",
        "review": "博客评论"
      },
      "translate": "Google翻译",
      "google_verification": "Google站长工具",
      "partner": "友情链接",
      "mailchimp": {
        "module_name": "邮件群发",
        "send": "邮件发送",
        "email_logs": "邮件发送日志"
      },
      "paypal_dispute": "PayPal纠纷"
    },
    "mta": {
      "module_name": "分析",
      "visits": "流量统计",
      "visits-conversion": "转化率",
      "orders-mta": "订单统计",
      "orders_repurchase": "复购率",
      "products-sales": "产品概况",
      "user-mta": "客户概况",
      "search-logs": "搜索统计",
      "real-time": "实时数据",
      "not-found-logs": "404链接"
    },
    "plugins": {
      "module_name": "应用",
      "app": "全部应用",
      "my-app": "我的应用",
      "google": "Google",
      "third-market": "第三方服务市场"
    },
    "set": {
      "module_name": "设置",
      "test": "样板",
      "config": "基本设置",
      "themes": {
        "module_name": "风格模板",
        "index_set": "首页设置",
        "themes": "风格",
        "nav": "头部导航",
        "footer_nav": "底部导航",
        "products_list": "产品列表页",
        "products_detail": "产品详细页",
        "style": "色调",
        "advanced": "其它"
      },
      "orders_print": "打印设置",
      "exchange": "汇率设置",
      "oauth": "平台设置",
      "payment": "收款",
      "taxes": "税费",
      "country": "国家管理",
      "email": "邮件通知&设置",
      "agreement": "协议",
      "shopping-set": "购物设置",
      "basis": "基本设置",
      "photo": "文件管理",
      "shipping": "物流&仓库",
      "third": "自定义代码",
      "manager": "管理员&权限",
      "logs": "系统日志",
      "language": "店铺语言",
      "user-set": "会员设置"
    },
    "interact": {
      "module_name": "互动",
      "blogApp": "博客"
    },
    "view": {
      "module_name": "店铺",
      "visual": "店铺装修",
      "visual-v2": "店铺装修(新版)",
      "page": "单页",
      "editcode": "代码编辑",
      "nav": "导航",
      "footer-nav": "底部导航",
      "domain-binding": "域名绑定",
      "url-redirect": "重定向",
      "seo": "TKD标签",
      "customcode": "自定义代码",
      "all_categories": "All Categories"
    },
    "skin": {
      "module_name": "网站风格管理",
      "index": "首页风格管理",
      "page": "内页风格管理",
      "prodcuts_list": "产品列表页风格管理",
      "prodcuts_detail": "产品详细页风格管理",
      "news_list": "文章列表页风格管理",
      "news_detail": "文章详细页风格管理"
    },
    "mobile": {
      "module_name": "移动端",
      "themes": "风格",
      "index_set": "首页设置"
    },
    "extend": {
      "module_name": "扩展",
      "salesman": "仅查看自己的订单数据"
    }
  },
  "language": {
    "zh-cn": "简体中文",
    "en": "英文",
    "jp": "日语",
    "de": "德语",
    "fr": "法语",
    "es": "西班牙语",
    "ru": "俄语",
    "pt": "葡萄牙语",
    "zh_tw": "繁体中文",
    "cn": "简体中文",
    "it": "意大利语",
    "ko": "韩语",
    "my": "缅甸语",
    "vi": "越南语",
    "th": "泰语",
    "ms": "马来语",
    "pl": "波兰语",
    "ar": "阿拉伯语"
  },
  "continent": {
    "1": "亚洲",
    "2": "欧洲",
    "3": "非洲",
    "4": "北美洲",
    "5": "南美洲",
    "6": "大洋洲",
    "7": "南极洲"
  },
  "frame": {
    "system_name": "官网后台管理",
    "pc": "网站端",
    "mobile": "移动端",
    "email": "发送邮件",
    "mta": "数据统计",
    "course": "使用教程",
    "clear_cache": "清空缓存",
    "home": "店铺首页",
    "feedback": "意见反馈"
  },
  "ckeditor": {
    "file_type_err": "文件上传失败，文件类型错误！",
    "upload_success": "文件上传成功！",
    "upload_fail": "文件上传失败！"
  },
  "notes": {
    "discount_rule": "（填写范围：0至100，0为不打折，20等同于8折，100为免费）",
    "pic_tips": "每次可同时上传%s张图片",
    "png_tips": "建议上传png透明图片，",
    "jpg_tips": "建议上传jpg格式图片，",
    "picture_tips": "仅支持jpg、jpeg、png、gif、ico、webp，建议2MB，最大不超过10MB",
    "pic_size_tips": "图片大小建议: %s像素",
    "ico_tips": "必须上传.ico格式的透明图片，",
    "new_pic_tips": "不超过%count%张，建议上传%format%格式图片，图片大小建议: %size%像素",
    "new_pic_tips_1": "格式图片支持%format%，图片大小建议: %size%像素",
    "page_tips": "共 %num% 条",
    "unread_message": "你有未读消息，请及时处理！",
    "disk_space_tips": "站点容量不足，请联系客服",
    "page_header": "首页",
    "page_footer": "尾页",
    "file_tips": "仅支持上传mp4格式的视频文件，为了保证页面流畅，建议视频大小2MB，最大不超过20MB",
    "file_size_tips": "文件大小建议: %sMB",
    "file_tips_1": "仅支持pdf, rar, zip, doc, xls, ppt, txt, csv，建议2MB，最大不超过20MB"
  },
  "msg": {
    "open_success": "开启成功！",
    "close_success": "关闭成功！",
    "add_success": "添加成功！",
    "edit_success": "修改成功！",
    "delete_success": "删除成功！",
    "import_success": "导入成功！",
    "reset_success": "重置成功！",
    "send_success": "发送成功！",
    "add_error": "添加失败！",
    "edit_error": "修改失败！",
    "send_error": "发送失败！",
    "operating_success": "操作成功！",
    "operating_error": "操作失败！",
    "install_success": "安装成功！",
    "install_error": "安装失败！",
    "save_success": "保存成功！",
    "save_error": "保存失败！",
    "switch_on": "已开启",
    "switch_off": "已关闭",
    "params_error": "参数错误"
  },
  "error": {
    "no_data": "暂无数据",
    "no_table_data": "当前暂时没有数据",
    "add_now": "马上添加",
    "supplement_lang": "请填写其他语言版资料！",
    "trial_limit": "试用版不能进行此操作!",
    "pending_five_min": "当前暂无数据，请稍后5分钟查看"
  },
  "account": {
    "welcome": "用户登录",
    "username": "用户名",
    "password": "密码",
    "login_btn": "登 录",
    "login_title": "做全球网络生意的<br />自建站平台",
    "wel_txt": "",
    "locking": "账号已被锁定，请联系管理员",
    "error": "用户名或密码错误，请重新登录",
    "timeout": "登录连接超时，请重试",
    "logout": "退出",
    "personal_center": "个人中心",
    "domain": "域名",
    "server": "服务器",
    "open_time": "开通时间",
    "trial_time": "试用时间",
    "customer_service": "客服专员",
    "hotline": "客服热线",
    "qq": "客服QQ",
    "proposal": "投诉与建议",
    "new_review": "最新评论",
    "system_messages": "系统消息",
    "orders_messages": "订单消息",
    "review_member": "会员注册审核",
    "form_message": "表单消息",
    "change_password": "更改密码",
    "month_quantity": "订单、流量统计",
    "today_statistics": "今天统计",
    "order_statistics": "订单统计",
    "today_income": "今天收入",
    "income_statistics": "收入统计",
    "today_user": "今天新增",
    "user_statistics": "会员统计",
    "today_review": "今天评论",
    "review_statistics": "评论统计",
    "wait_payment_order": "待付款订单",
    "wait_delivery_order": "待发货订单",
    "stock_warning": "产品库存警告",
    "return": "退货",
    "backup_time": "备份时间",
    "traffic_time": [ "最近30天", "今天", "昨天", "最近7天", "最近15天", "全部（全部只是针对订单）" ],
    "traffic_statistics": "流量统计",
    "statistics_name": [ "总订单数", "总销售额", "访客", "浏览量" ],
    "source": "访客来源",
    "new_order": "最新订单",
    "from": "访客分布",
    "keyword_ranking": "Google关键词排名监控",
    "error_tips": "没有符合条件的结果！",
    "support": [ "企业QQ", "上班时间", "电话号码", "微信号", "上级投诉人", "网站到期时间" ],
    "data_backup": "数据备份",
    "backup": [ "数据库", "文件夹" ],
    "day_ary": [ "今天", "昨天", "过去7天", "过去30天" ],
    "compare_day_ary": [ "对比前一天", "对比前一天", "对比前7天", "对比前30天" ],
    "open_store": "快速开启店铺",
    "exit_guide": "退出引导模式",
    "add_now": "马上添加",
    "set_themes": "选择风格",
    "add_pro": "添加产品",
    "add_pro_free": "添加或导入产品",
    "set_shipping": "设置物流",
    "set_payment": "设置收款方式",
    "go_set": "去设置",
    "course": "推荐视频",
    "upload": "产品导入",
    "suggest": "建议",
    "analysis": "掌握使用数据分析",
    "analysis_desc": "收集访客访问数据并汇总各个方面的关键指标，快速掌握网站运营情况",
    "view_more": "查看更多",
    "total_sales": "总销售额",
    "total_orders": "总订单数",
    "order_item": "单",
    "detail": "详细",
    "ad": {
      "global": {
        "do_it": "现在使用"
      },
      "facebook": {
        "title": "Facebook广告",
        "links": [ "广告开通", "广告常见问题", "在线实操课程" ],
        "note_title": "插件功能包括:",
        "note_list": [
          [ "Facebook店铺", "一键发布到Facebook" ],
          [ "Facebook专页", "在Facebook上创建主页" ],
          [ "Facebook Messenger", "浏览网站时可以联系你" ],
          [ "Facebook 像素", "应用于网站的追踪代码" ]
        ]
      },
      "google": {
        "title": "Google广告",
        "links": [ "Google Analytics对接说明", "营销常见问题", "在线实操课程", "代理商开户推荐" ],
        "note_list": [
          [ "GA追踪代码", "直接观察分析各项数据" ],
          [ "Google验证文件", "验证网站的所有权" ],
          [ "Google Feed", "把产品提交到google" ]
        ]
      },
      "bing": {
        "title": "Bing广告"
      }
    },
    "add_pro_free_tips": "支持Shopify、速卖通等平台同步产品",
    "domain_binding_tips": "把您注册好的域名提交绑定，让您的客户通过您的专属域名访问您的店铺",
    "set_shipping_tips": "添加设置您合作的物流、快递方式，并可根据它们的计算方式设置运费计算公式",
    "set_payment_tips": "已接入PayPal、Stripe等数十种外贸常用信用卡收款平台，注册账号并在后台填写账号信息即可实现收款",
    "web_site_info": "站点信息",
    "member_more_tips": "商业版用户",
    "member_more_tips_1": "独享更丰富的功能",
    "web_site_cer": "证书",
    "web_site_pay": "收款",
    "web_site_line": "线路",
    "web_site_speed": "速度",
    "web_site_space": "容量",
    "web_site_shipping": "物流",
    "web_site_seo": "SEO",
    "web_site_domain_check": "已经绑定正式域名",
    "web_site_domain_fail": "未绑定域名",
    "web_site_cer_check": "已配置SSL证书",
    "web_site_cer_fail": "未配置SSL证书，请先绑定域<br/>名,系统自动为你配置SSL证书",
    "web_site_pay_check": "已绑定PayPal账号",
    "web_site_pay_fail": "未绑定PayPal支付账号",
    "web_site_space_check": "还剩%space%G空间",
    "web_site_space_fail": "存储空间已满",
    "web_site_space_await": "存储空间还剩%space%G,即将达到上限",
    "web_site_lang_free": "英语",
    "web_site_lang_check": "八大语种",
    "web_site_lang_count": "个语种",
    "web_site_seo_check": "60分",
    "web_site_domain_btn": ">>绑定域名",
    "web_site_cer_btn": ">>配置证书",
    "web_site_pay_btn": ">>绑定账号",
    "web_site_shipping_btn": ">>物流",
    "web_site_lang_btn": ">>前去设置",
    "web_site_seo_btn": ">>提升全站SEO",
    "cdx_statistics": "站内漏斗",
    "cdx_access": "访问人数",
    "cdx_see_pro": "查看产品",
    "cdx_orders": "历史订单",
    "cdx_date": "购买日期",
    "cdx_content": "购买内容",
    "cdx_invoice": "下载发票",
    "cdx_win_alert_error_title": "出错了！",
    "cdx_win_alert_error_brief": "您的店铺内容涉嫌违反平台《用户协议》中的<span>【内容管制】</span>相关条款，故此本站点已停止提供访问服务。如有异议，请联系客服人员进行处理",
    "cdx_win_alert_offline_pay_title": "线下支付",
    "cdx_win_alert_offline_pay_brief": "如需线下支付，请联系客服为您办理。",
    "cdx_deer_customer": "尊敬的用户:",
    "cdx_confirm": "确定",
    "cdx_see_later": "再用用看",
    "cdx_no_alert": "残忍拒绝",
    "cdx_no_tips": "不再提示",
    "cdx_method": "支付方式",
    "cdx_pay_status_ary": [ "未支付", "支付成功", "处理中", "支付失败", "已退款" ],
    "cdx_payment": {
      "alipay": "支付宝",
      "wechatpay": "微信支付"
    },
    "cdx_version": {
      "1": "标准版",
      "2": "专业版",
      "10": "免费版",
      "11": "标准版",
      "12": "高级版",
      "13": "专业版"
    },
    "cdx_version_date": {
      "30": "1个月",
      "90": "3个月",
      "180": "6个月",
      "365": "1年"
    },
    "cdx_cur_ver": "当前版本",
    "cdx_commission_fee": "平台手续费",
    "cdx_ava_space": "可用空间",
    "cdx_used_space": "已用空间",
    "cdx_syn_time": "同步时间",
    "cdx_monthly_fee": "月费",
    "cdx_annual_fee": "年费",
    "cdx_valid_period": "有效期至",
    "cdx_expired": "%time%到期",
    "cdx_account": "账号",
    "cdx_renew": "续费",
    "cdx_themes": "模板",
    "system_notification": "系统更新通知",
    "first_orders": "距离你首个订单",
    "for_you": "推荐应用",
    "number": "次数",
    "show_version_tips": "您的店铺当前版本是展示版，无法进行下单，<br />如需要下单，请升级为商业版。",
    "upgrade_now": "立即升级",
    "invite": {
      "invite": "邀请",
      "create_invite": "创建邀请链接",
      "view_rules": "查看邀请规则",
      "get_privilege": "邀请好友试用，获得免佣特权",
      "get_priv_desc": "邀请好友试用7天，后续好友购买套餐，即享免月租福利。",
      "store_points": "店铺点数",
      "point": "点",
      "you_link": "您的邀请链接",
      "user_invited": "已邀请用户",
      "reg_time": "注册时间",
      "username": "用户名",
      "phone": "注册手机号码",
      "user_status": "账户状态",
      "in_trial": "试用中",
      "bought": "已购买",
      "not_user": "暂无邀请用户，加油！",
      "invite_rules": "邀请规则",
      "invite_process": "邀请流程：",
      "process_text": "1、点击“创建邀请链接”，复制您的专属邀请链接给好友；<br>2、好友通过链接注册新店铺，开始7天免费试用；<br>3、被邀请人后续购买套餐时，系统自动获得本店铺点数 399点。",
      "invite_rules_t": "邀请规则：",
      "invite_text": "1.店铺点数仅用于抵扣本店铺内产生的月租费用，每1点抵扣¥ 1.00元，不支持提现、转移，转售等其他用途；<br>2.使用店铺点数抵扣月租费用时，不可与其他优惠方案叠加使用；<br>3.邀请奖励仅限新用户生效，即未曾在系统上注册的手机号；每个手机号仅可视为一个被邀请人；<br>4.每个店铺只计算前五个成功购买套餐的被邀请人名额，即每个店铺最多可获得1995点；<br>5.生成的邀请链接仅限于邀请注册使用，使用期限和次数没有限制；<br>6.若被邀请人产生退款，则奖励取消；若所邀请的新用户退款次数过多，有权终止此店铺的邀请权限；<br>7.拥有活动最终解释权。"
    },
    "my_account": {
      "expires": "已过期",
      "expires_time": "%num%天后到期",
      "acc_period": "账期",
      "to": "至",
      "pay": "支付",
      "method": "请选择支付方式",
      "wechat": "微信支付",
      "alipay": "支付宝支付",
      "amount": "应付金额：",
      "pay_now": "立即支付",
      "pay_succ": "支付成功",
      "auto_close": "%num%秒后自动关闭",
      "deduction": "到期自动划扣",
      "deduction_tips": "（如有点数，优先划扣点数），<a href=\"/manage/account/invite\">了解点数来源</a>",
      "point": "点数",
      "com_price": "您的店铺本期平台手续费已出账，金额为 ￥%amount%，请在出帐后，7天内完成支付。",
      "now_pay": "现在支付",
      "reque_invoice": "申请发票",
      "total_amount": "总金额：￥",
      "order_number": "订单号",
      "pay_plan": "支付方案",
      "pay_time": "支付时间",
      "pay_status": "支付状态",
      "invoice_status": "发票状态",
      "issued": "已开具",
      "not_issued": "未开具",
      "invoice_details": "发票详细",
      "over_price": "申请发票的总金额须达到￥300及以上",
      "only_issue": "只开具电子发票",
      "update_tips": "如果需要修改抬头名称和纳税人识别号，<br>请联系客服",
      "invoice_type": "发票类型",
      "prise_invoice": "企业发票",
      "personal_invoice": "个人发票",
      "header_name": "抬头名称",
      "taxpayer": "纳税人识别号",
      "mailbox": "收件人邮箱",
      "already_applied": "已申请",
      "invoice_tips": "发票将在3个工作日内发送到收件人邮箱，请留意查收",
      "ok": "好的",
      "check_mailbox": "请前往收件人邮箱查收",
      "reque_time": "申请时间"
    },
    "real_name": {
      "real_reg": "实名登记",
      "personal_reg": "个人登记",
      "business_reg": "企业登记",
      "name": "真实姓名",
      "id_card": "身份证号码",
      "upload_card": "证件正反面照片",
      "corporate_upload_card": "上传法人身份证正反面",
      "business_name": "企业全称",
      "business_name_tips": "工商营业执照上的企业全称。只支持中国大陆工商局或市场监督管理局登记的企业。",
      "corporate_name": "法人代表/企业负责人姓名",
      "corporate_name_tips": "如果属于分公司则填写工商营业执照上明确的负责人，个体工商请填写经营者姓名，合伙企业请填写投资人姓名，企业法人的非法人分支机构请填写负责人姓名。",
      "upload_licenes": "工商营业执照",
      "real_tips": "请完成<a class=\"real_tips_btn\" href=\"./?m=account&a=credential&d=type\">实名登记</a>, 开启域名绑定与支付功能",
      "real_now": "马上认证",
      "real_btn": "认证",
      "complete_tips": "您好，用户：<br /> <br />您已经成功提交了实名信息。 <br />域名绑定和支付功能已成功解锁！",
      "go_real": "去登记",
      "go_real_tips": "请完成实名登记，解锁%type%。",
      "front_pic": "证件正面照片",
      "back_pic": "证件反面照片",
      "corporate_front_pic": "法人身份证正面照片",
      "corporate_back_pic": "法人身份证反面照片",
      "license_pic": "营业执照照片",
      "corporate_code": "统一社会信用代码",
      "corporate_code_tips": "请确认18位统一社会信用代码或15位营业执照注册号。",
      "upload_pic": "请正确上传图片",
      "done_real_tips": "进入域名绑定",
      "status_type": "选择认证类型",
      "status_message": "填写认证信息",
      "status_submit": "提交认证",
      "type_personal": "个人认证",
      "type_company": "企业认证",
      "type_tips_personal": "限制最多开启不超过3间店铺",
      "type_tips_company": "没有店铺数量限制",
      "prev_btn": "上一步",
      "next_btn": "下一步",
      "personal_info": "个人认证资料",
      "company_info": "企业认证资料",
      "name_tips": "与您本人证件姓名保持一致",
      "submit_certification": "提交认证",
      "hand_pic": "手持证件正面照片",
      "example": "示例",
      "hand_tips": "<span class=\"tips_red\">为提高您认证的成功率，请务必注意如下要求：</span><br />1、确保是本人手持证件正面照，面部全部露出且可确认；<br />2、证件信息（姓名、号码、免冠照、住址等），必须无遮挡且清晰不模糊；<br />3、手持证件的照片请勿上下，左右倒置或翻转；",
      "idpic_tips": "1、证件正反面信息必须清晰且无遮挡；<br />2、图片勿上下、左右倒置或翻转；",
      "promise_tips": "本人承诺：所提供的个人信息和材料真实准确，对因提供有关信息、证件不实或违反有关规定造成的后果，责任自负。"
    }
  },
  "set": {
    "module_name": "设置",
    "test": "样板",
    "config": "基本设置",
    "themes": {
      "module_name": "风格模板",
      "index_set": "首页设置",
      "themes": "风格",
      "nav": "头部导航",
      "footer_nav": "底部导航",
      "products_list": "产品列表页",
      "products_detail": "产品详细页",
      "style": "色调",
      "advanced": "其它"
    },
    "orders_print": "打印设置",
    "exchange": "汇率设置",
    "oauth": "平台设置",
    "payment": "收款",
    "taxes": "税费",
    "country": "国家管理",
    "email": "邮件通知&设置",
    "agreement": "协议",
    "shopping-set": "购物设置",
    "basis": "基本设置",
    "photo": "文件管理",
    "shipping": "物流",
    "third": "自定义代码",
    "manager": "管理员&权限",
    "logs": "系统日志",
    "language": "店铺语言",
    "user-set": "会员设置"
  },
  "module_brief_ary": {
    "basis": "设置店主邮箱、网站icon图标，时区，语言，联系方式等",
    "shopping-set": "设置购物条件，订单前缀，弃单召回时效等",
    "user-set": "设置客户注册时需要填写的信息以及是否需要验证邮箱",
    "share": "设置公司的海外主流社交媒体主页",
    "review": "设置客户产品评论条件是否需要审核、是否成交才能评论",
    "email": "设置所有通知邮件进行内容的编辑和开关处理",
    "manager": "设置后台管理员或业务员添加及对应的后台操作权限",
    "print": "填写用于订单打印功能内的公司信息",
    "logs": "查阅后台的登录及操作日志",
    "photo": "统一管理后台上传的所有图片和文件，方便查询和二次使用",
    "country": "国家及地区使用设置物流配送地区，客户收货地址",
    "agreement": "设置Cookies协议、服务条款、退款条款、隐私政策",
    "payment": "设置交易的收款方式，Paypal，信用卡，货到付款等",
    "shipping": "根据不同国家地区设置物流方式和费用",
    "taxes": "根据国家或者地区设置税费",
    "language": "设置前端店铺内容的默认语言和设置店铺其他语种"
  },
  "email_template": {
    /* global */
    "home": "Home",
    "my_account": "My Account",
    "contactUs": "Contact Us",
    "footer_0": "You have received this email because you are a registered member of the {{domain}} website.",
    "footer_1": "For further information, log in to your account at: {{domain}} and submit your request or use live chat.",
    "dearTo": "Dear Valued Customer",
    "sincerely": "Yours sincerely",
    "customer": "{{domain}} Customer Care Team",
    "queries": "If you have any queries, please email our Customer Care Team.",
    "copyRight": "Copyright © {{SYSTEM_NAME}} <a href=\"https://www.{{SYSTEM_NAME}}.com/\">https://www.{{SYSTEM_NAME}}.com/</a>",

    /* create account */
    "createTitle_1": "Thank you for registering",
    "createTitle_2": "We will release the latest product updates, news and special offers to you at any time.",
    "shop_now": "Shop Now",

    /* forgot password */
    "not_reply_pwd": "This is an automated email from {{domain}} in response to your request to reset your password. Please do not reply to this email.",
    "steps": "To reset your password and access your {{domain}} account, follow these steps",
    "pwdInfo_0": "Click the following link or copy and paste the link into your address bar of your web browser.",
    "pwdInfo_1": "The above link will take you to our \"Reset password\" page. Fill in the appropriate fields and click \"Submit\", you will then be able to access your account now.",

    /* order create */
    "orderCreateTitle": "You have a new order!",
    "orderCreateAction": "Process order",
    "orderCreateProduct": "Product list",

    /* order detail */
    "paymentMethod": "Payment Method",
    "shippingMethod": "Shipping Method",
    "trackingNumber": "Tracking Number",
    "coupon": "Coupon Savings",
    "grandTotal": "Grand Total",
    "special": "Special Instructions or Comments",
    "product": "Product",
    "qty": "Quantity",
    "refunded": "Refunded",
    "refundedInfo_1": "If you have any queries, please email our Customer Care Team <a style=\"color:#1e5494;text-decoration:none;\" href=\"mailto:{{AdminEmail}}\">{{AdminEmail}}</a>",

    /* validate mail */
    "validateInfo_3": "Verify your email",
    "validateInfo_4": "You have successfully created an account.Please click on the link below to verify your email address and complete your registration.",
    "validateInfo_5": "Or copy this link and paste in your web browser :",
    "validateBtn": "Verify Email",

    /* user invitation */
    "invitationTitle": "Customer account activation",
    "invitationInfo_1": "You have created a customer account in <span style=\"color:#e53935;\">{{SiteName}}</span>. <br /> Do you want to activate it now?",
    "invitationInfoBtn": "Activate Your Account",
    "invitation_coupon_txt": "Coupon",
    "invitation_tips": "Once done, you'll receive a special gift :",

    /* shopping cart recall */
    "recall_0": "Your shopping cart misses you.",
    "recall_1": "Get you shopping items before they're gone!",
    "bigSale": "Big Sale!",
    "couponInfo": "You've got a {{CouponSuffix}} coupon!",
    "items": "Items",
    "recallQty": "Qty",
    "recallPrice": "Price",
    "recallTotal": "Total",
    "totalSavings": "Your Total Savings",
    "viewMyOrder": "View my order",
    "recallFoot": "Questions? Please refer to our <a href=\"{{Domain}}\" target=\"_blank\" style=\"text-decoration:underline; font-size:16px; font-weight:bold; color:#333;\">FAQS</a> or emails us at <a href=\"mailto:{{Email}}\" target=\"_blank\" style=\"text-decoration:underline; font-size:16px; font-weight:bold; color:#333;\">{{Email}}</a>.",
    "shipsFrom": "Ships From",

    /* order recall */
    "o_recall_0": "Forgot Something ?",
    "o_recall_1": "Get ",
    "o_recall_2": "on your entire order.",
    "o_recall_3": "But hurry! Your cart and this offer are expiring soon!",
    "o_recall_btn_0": "Finish Checking out",
    "o_recall_btn_1": "or Visit our store",
    "o_recall_cart": "Your cart",
    "unsubscribe_0": "If you wish to no longer receive our news and offers,",
    "unsubscribe_1": "simply <a href=\"{{UnsubscribeUrl}}\" style=\"font-family:OpenSans-Bold; color:#555;\">CLICK HERE TO UNSUBSCRIBE</a>.",

    /* inbox */
    "userInboxInfo": "Customer <span style=\"color:#1e5494;\">{{UserName}}</span> sent you a station letter in {{Time}}, the content is as follows, please reply in time:",
    "userOrdersInfo": "Customer <span style=\"color:#1e5494;\">{{UserName}}</span> sent you an order message at {{Time}}, the content is as follows, please reply in time:",
    "userInboxReply": "Reply",
    "userInboxRecord": "Recent communication records",
    "or": "or",
    "order": "Order #{{OrderNum}}",
    "orderPaymentTitle": "Thank you for your purchase!",
    "viewYourOrder": "View your order",
    "visitOurStore": "Visit our store",
    "orderSummary": "Order summary",
    "orderShippedTitle": "Your order is on the way.",
    "orderShippedInfo": "Hi {{UserName}}, Your order is on the way. Track your shipment to see the delivery status.",
    "shippingChargesInsurance": "Shipping",
    "handlingFee": "Handling Fee",
    "customerInformation": "Customer information",
    "billingAddress": "Billing address",
    "itemsInThisShipment": "Items in this shipment",
    "orderChangeTitle": "Your order has been delivered.",
    "orderChangeInfo": "Hi {{UserName}}, haven’t received your package yet?",
    "LetUsKnow": "Let us know",
    "orderCancelTitle": "Your order has been canceled.",
    "orderCancelInfo": "Sorry {{UserName}}, your order was canceled at your request and your payment has been voided.",
    "Shipment": "Shipment",

    /* email-subscriptions */
    "subscriptTit": "Thanks for subscribing!",
    "subscriptDesc": "You have been successfully subscribed to {{SiteName}} newsletter.",
    "subscriptBtn": "Start shopping",
    "subscriptCouTit": "Here is a special gift for you:",
    "subscriptCouTxt": "Coupon",

    /* bill-notification */
    "purchaseTitle": "Complete your purchase",
    "purchaseDesc0": "Hi {{UserName}},",
    "purchaseDesc1": "We noticed that you have an unpaid order, click on the link below to take them home!",
    "payNow": "Pay now",
    "shiipingAddress": "Shipping address",

    /* arrival notice */
    "arrivalTitle": "Hi {{UserName}}. {{ProductName}} is available now. As you have subscribed to be notified when the product is back, we made sure you are first to know. Get it now before it goes out of stock again!",
    "buyItNow": "Buy it Now",

    /* Distribution Notice Start */
    "disInfoTitle1": "Hello, {{UserName}}",
    "disApprovedInfo1": "Congratulations! You have become a distributor.",
    "disApprovedInfo2": "Congratulations! You have successfully become a distributor , and you will have below permissions:",
    "disApprovedInfo3": "1. Earn commissions from our products",
    "disApprovedInfo4": "2. Check the distribution data in your account.",
    "viewNow": "View Now",
    "viewMore": "View more",
    "disInvitationInfo1": "Invite you to become a distributor.",
    "disInvitationInfo2": "We invite you to become a distributor, click the button below and get your commissions right now!",
    "disRejectedInfo1": "Your application for distributor has been rejected.",
    "disRejectedInfo2": "Sorry, due to the following reason, your application for the distributor is rejected. <br /> You can reapply it after modifying your information.",
    "disFailureReason": "Failure reason",
    "applyAgain": "Apply Again",

    /* Distribution Notice END */

    /* post_coupon */
    "post_coupon_title": "You've got a gift!",
    "post_coupon_tips_one": "Hi,",
    "post_coupon_tips_two": "Congratulations! You've got a coupon!",
    "post_coupon_tips_three": "To redeem your discount, use following coupon code during checkout.",

    /* inbox_message / order_message Start */
    "inboxMessageTitle": "You have a new message",
    "inboxMessageDesc": "Hi {{UserName}}, I sent you a message at {{Time}}. The content is as follows, please check it in time",
    "orderMessageTitle": "You have a new order message No.# {{OrderNum}}",
    "orderMessageDesc": "Hi {{UserName}}, I sent you a order message at {{Time}}. The content is as follows, please check it in time",
    /* inbox_message / order_message END */

    /* review_rejected */
    "rejectedTitle1": "Hello, {{CustomName}}",
    "rejectedInfo1": "Your registration has been rejected.",
    "rejectedInfo2": "Sorry, due to the following reason, your registration is rejected. You can register again after modifying your information.",
    "rejectedReason": "Reject reason",
    "registerAgain": "Register Again",
    "points": "Points",
    "tax": "Taxes",
    "user_discount": "Member discount",
    "full_discount": "Full discount",
    "recall_discount": "Recall discount"

  },
  "translate": {
    "de_0": "德语",
    "de_1": "German",
    "ru_0": "俄语",
    "ru_1": "Russian",
    "fr_0": "法语",
    "fr_1": "French",
    "es_0": "西班牙语",
    "es_1": "Spanish",
    "pt_0": "葡萄牙语",
    "pt_1": "Portuguese",
    "it_0": "意大利语",
    "it_1": "Italian",
    "jp_0": "日语",
    "jp_1": "Japanese",
    "cn_0": "中文(简体)",
    "cn_1": "Chinese (Simplified)",
    "zh_tw_0": "中文(繁体)",
    "zh_tw_1": "Chinese (Traditional)",
    "sq_0": "阿尔巴尼亚语",
    "sq_1": "Albanian",
    "ar_0": "阿拉伯语",
    "ar_1": "Arabic",
    "am_0": "阿姆哈拉语",
    "am_1": "Amharic",
    "az_0": "阿塞拜疆语",
    "az_1": "Azerbaijani",
    "ga_0": "爱尔兰语",
    "ga_1": "Irish",
    "et_0": "爱沙尼亚语",
    "et_1": "Estonian",
    "or_0": "奥利亚语",
    "or_1": "Oriya",
    "eu_0": "巴斯克语",
    "eu_1": "Basque",
    "be_0": "白俄罗斯语",
    "be_1": "Belarusian",
    "bg_0": "保加利亚语",
    "bg_1": "Bulgarian",
    "is_0": "冰岛语",
    "is_1": "Icelandic",
    "pl_0": "波兰语",
    "pl_1": "Polish",
    "bs_0": "波斯尼亚语",
    "bs_1": "Bosnian",
    "fa_0": "波斯语",
    "fa_1": "Persian",
    "af_0": "布尔语(南非荷兰语)",
    "af_1": "Afrikaans",
    "tt_0": "鞑靼语",
    "tt_1": "Tatar",
    "da_0": "丹麦语",
    "da_1": "Danish",
    "tl_0": "菲律宾语",
    "tl_1": "Filipino",
    "fi_0": "芬兰语",
    "fi_1": "Finnish",
    "fy_0": "弗里西语",
    "fy_1": "Frisian",
    "km_0": "高棉语",
    "km_1": "Khmer",
    "ka_0": "格鲁吉亚语",
    "ka_1": "Georgian",
    "gu_0": "古吉拉特语",
    "gu_1": "Gujarati",
    "kk_0": "哈萨克语",
    "kk_1": "Kazakh",
    "ht_0": "海地克里奥尔语",
    "ht_1": "Haitian Creole",
    "ko_0": "韩语",
    "ko_1": "Korean",
    "ha_0": "豪萨语",
    "ha_1": "Hausa",
    "nl_0": "荷兰语",
    "nl_1": "Dutch",
    "ky_0": "吉尔吉斯语",
    "ky_1": "Kyrgyz",
    "gl_0": "加利西亚语",
    "gl_1": "Galician",
    "ca_0": "加泰罗尼亚语",
    "ca_1": "Catalan",
    "cs_0": "捷克语",
    "cs_1": "Czech",
    "kn_0": "卡纳达语",
    "kn_1": "Kannada",
    "co_0": "科西嘉语",
    "co_1": "Corsican",
    "hr_0": "克罗地亚语",
    "hr_1": "Croatian",
    "ku_0": "库尔德语",
    "ku_1": "Kurdish",
    "la_0": "拉丁语",
    "la_1": "Latin",
    "lv_0": "拉脱维亚语",
    "lv_1": "Latvian",
    "lo_0": "老挝语",
    "lo_1": "Lao",
    "lt_0": "立陶宛语",
    "lt_1": "Lithuanian",
    "lb_0": "卢森堡语",
    "lb_1": "Luxembourgish",
    "rw_0": "卢旺达语",
    "rw_1": "Kinyarwanda",
    "ro_0": "罗马尼亚语",
    "ro_1": "Romanian",
    "mg_0": "马尔加什语",
    "mg_1": "Malagasy",
    "mt_0": "马耳他语",
    "mt_1": "Maltese",
    "mr_0": "马拉地语",
    "mr_1": "Marathi",
    "ml_0": "马拉雅拉姆语",
    "ml_1": "Malayalam",
    "ms_0": "马来语",
    "ms_1": "Malay",
    "mk_0": "马其顿语",
    "mk_1": "Macedonian",
    "mi_0": "毛利语",
    "mi_1": "Maori",
    "mn_0": "蒙古语",
    "mn_1": "Mongolian",
    "bn_0": "孟加拉语",
    "bn_1": "Bengali",
    "my_0": "缅甸语",
    "my_1": "Myanmar (Burmese)",
    "hmn_0": "苗语",
    "hmn_1": "Hmong",
    "xh_0": "南非科萨语",
    "xh_1": "IsiXhosa saseMzantsi Afrika",
    "zu_0": "南非祖鲁语",
    "zu_1": "Zulu",
    "ne_0": "尼泊尔语",
    "ne_1": "Nepali",
    "no_0": "挪威语",
    "no_1": "Norwegian",
    "pa_0": "旁遮普语",
    "pa_1": "Punjabi",
    "ps_0": "普什图语",
    "ps_1": "Pashto",
    "ny_0": "齐切瓦语",
    "ny_1": "Chichewa",
    "sv_0": "瑞典语",
    "sv_1": "Swedish",
    "sm_0": "萨摩亚语",
    "sm_1": "Samoan",
    "sr_0": "塞尔维亚语",
    "sr_1": "Serbian",
    "st_0": "塞索托语",
    "st_1": "Sesotho",
    "si_0": "僧伽罗语",
    "si_1": "Sinhala",
    "eo_0": "世界语",
    "eo_1": "Esperanto",
    "sk_0": "斯洛伐克语",
    "sk_1": "Slovak",
    "sl_0": "斯洛文尼亚语",
    "sl_1": "Slovenian",
    "sw_0": "斯瓦希里语",
    "sw_1": "Swahili",
    "gd_0": "苏格兰盖尔语",
    "gd_1": "Scottish Gaelic",
    "ceb_0": "宿务语",
    "ceb_1": "Cebuano",
    "so_0": "索马里语",
    "so_1": "Somali",
    "tg_0": "塔吉克语",
    "tg_1": "Tajik",
    "te_0": "泰卢固语",
    "te_1": "Telugu",
    "ta_0": "泰米尔语",
    "ta_1": "Tamil",
    "th_0": "泰语",
    "th_1": "Thai",
    "tr_0": "土耳其语",
    "tr_1": "Turkish",
    "tk_0": "土库曼语",
    "tk_1": "Turkmen",
    "cy_0": "威尔士语",
    "cy_1": "Welsh",
    "ug_0": "维吾尔语",
    "ug_1": "Uyghur",
    "ur_0": "乌尔都语",
    "ur_1": "Urdu",
    "uk_0": "乌克兰语",
    "uk_1": "Ukrainian",
    "uz_0": "乌兹别克语",
    "uz_1": "Uzbek",
    "iw_0": "希伯来语",
    "iw_1": "Hebrew",
    "el_0": "希腊语",
    "el_1": "Greek",
    "haw_0": "夏威夷语",
    "haw_1": "Hawaiian",
    "sd_0": "信德语",
    "sd_1": "Sindhi",
    "hu_0": "匈牙利语",
    "hu_1": "Hungarian",
    "sn_0": "修纳语",
    "sn_1": "Shona",
    "hy_0": "亚美尼亚语",
    "hy_1": "Armenian",
    "ig_0": "伊博语",
    "ig_1": "Igbo",
    "yi_0": "意第绪语",
    "yi_1": "Yiddish",
    "hi_0": "印地语",
    "hi_1": "Hindi",
    "su_0": "印尼巽他语",
    "su_1": "Sundanese",
    "id_0": "印尼语",
    "id_1": "Indonesian",
    "jw_0": "印尼爪哇语",
    "jw_1": "Javanese",
    "en_0": "英语",
    "en_1": "English",
    "yo_0": "约鲁巴语",
    "yo_1": "Yoruba",
    "vi_0": "越南语",
    "vi_1": "Vietnamese"
  },
  "variable": {
    "ThemesBodyColor": {
      "name": "Home Background"
    },
    "ThemesBoxTitleColor": {
      "name": "Title"
    },
    "ThemesBoxSubTitleColor": {
      "name": "Tubtitle"
    },
    "ThemesProductsTitleColor": {
      "name": "Product name"
    },
    "ThemesProductsPriceColor": {
      "name": "Current price"
    },
    "ThemesProductsDelPriceColor": {
      "name": "Original price"
    },
    "ThemesReviewsStarColor": {
      "name": "Comment stars"
    },
    "ThemesAttrOptionColor": {
      "name": "Check the option"
    },
    "ThemesTextContentColor": {
      "name": "Text"
    },
    "ThemesCheckoutButtonBgColor": {
      "name": "Checkout button background"
    },
    "ThemesCheckoutButtonTextColor": {
      "name": "Checkout button text"
    },
    "ThemesHeaderBgColor": {
      "name": "Background"
    },
    "ThemesBoardColor": {
      "name": "Bulletin Board"
    },
    "ThemesBoardBgColor": {
      "name": "Bulletin board background"
    },
    "ThemesHeaderTextColor": {
      "name": "Word"
    },
    "ThemesHeaderLineColor": {
      "name": "Line"
    },
    "ThemesHeaderTipsColor": {
      "name": "Hint"
    },
    "ThemesHeaderIconColor": {
      "name": "Icon"
    },
    "ThemesHeaderIconBgColor": {
      "name": "Icon background"
    },
    "ThemesHeaderIconUserColor": {
      "name": "Member icon"
    },
    "ThemesHeaderIconUserBgColor": {
      "name": "Member icon background"
    },
    "ThemesHeaderIconCartColor": {
      "name": "Shopping cart icon"
    },
    "ThemesHeaderIconCartBgColor": {
      "name": "Shopping cart icon background"
    },
    "ThemesHeaderIconSearchColor": {
      "name": "Search icon"
    },
    "ThemesHeaderIconSearchBgColor": {
      "name": "Search icon background"
    },
    "ThemesHeaderIconFavoColor": {
      "name": "Favorite Icon Color"
    },
    "ThemesHeaderIconFavorBgColor": {
      "name": "Favorite icon background color"
    },
    "ThemesHeaderIconMenuColor": {
      "name": "Menu icon"
    },
    "ThemesHeaderIconMenuBgColor": {
      "name": "Menu icon background"
    },
    "ThemesHeaderInputBgColor": {
      "name": "Input box background"
    },
    "ThemesHeaderInputBorderColor": {
      "name": "Input box border"
    },
    "ThemesHeaderProductsCategoryColor": {
      "name": "Products  Category"
    },
    "ThemesNavBgColor": {
      "name": "Navigation"
    },
    "ThemesNavTextColor": {
      "name": "Level 1 Navigation text"
    },
    "ThemesNavTextHoverColor": {
      "name": "Level 1 Navigation text mouse over"
    },
    "ThemesNavLevel2TextColor": {
      "name": "Level 2 Navigation text"
    },
    "ThemesNavLevel2TextHoverColor": {
      "name": "Level 2 Navigation text mouse over"
    },
    "ThemesNavLevel3TextColor": {
      "name": "Level 3 Navigation text"
    },
    "ThemesNavLevel3TextHoverColor": {
      "name": "Level 3 Navigation text mouse over"
    },
    "ThemesNavLevel4TextColor": {
      "name": "Level 4 Navigation text"
    },
    "ThemesNavLevel4TextHoverColor": {
      "name": "Level 4 Navigation text mouse over"
    },
    "ThemesFooterBgColor": {
      "name": "Background"
    },
    "ThemesFooterLineColor": {
      "name": "Line"
    },
    "ThemesFooterTitleColor": {
      "name": "Title"
    },
    "ThemesFooterTextColor": {
      "name": "Word"
    },
    "ThemesFooterTextHoverColor": {
      "name": "Text mouse over"
    },
    "ThemesFooterIconColor": {
      "name": "Icon"
    },
    "ThemesFooterInputColor": {
      "name": "Input box"
    },
    "ThemesFooterInputBgColor": {
      "name": "Input box background"
    },
    "ThemesFooterInputBorderColor": {
      "name": "Input box border"
    },
    "ThemesFooterButtonColor": {
      "name": "Button"
    },
    "ThemesFooterButtonHoverColor": {
      "name": "Button mouse over"
    },
    "ThemesFooterButtonBgColor": {
      "name": "Button background"
    },
    "ThemesFooterButtonBgHoverColor": {
      "name": "Button background mouse over"
    },
    "ThemesCopyrightColor": {
      "name": "Copyright Information"
    },
    "ThemesCopyrightBgColor": {
      "name": "Copyright Information Background"
    },
    "ThemesNavFont": {
      "name": "Navigation"
    },
    "ThemesFooterNavFont": {
      "name": "Bottom navigation"
    },
    "ThemesBoxTitleFont": {
      "name": "Title"
    },
    "ThemesBoxSubTitleFont": {
      "name": "Subtitle"
    },
    "ThemesTextContentFont": {
      "name": "Text"
    },
    "ThemesButtonFont": {
      "name": "Button Text"
    },
    "ThemesProductsTitleFont": {
      "name": "Product name"
    },
    "ThemesProductsPriceFont": {
      "name": "Product current price"
    },
    "ThemesProductsDelPriceFont": {
      "name": "Product original price"
    },
    "ProductsPicScale": {
      "name": "Product map scale",
      "options": {
        "adapt": "Fit the picture",
        "1_1": "1:1",
        "4_3": "4:3",
        "2_3": "2:3"
      }
    },
    "ProductImage": {
      "name": "Product map scale",
      "options": {
        "adapt": "Fit the picture",
        "1_1": "1:1",
        "4_3": "4:3",
        "2_3": "2:3"
      },
      "tips": "Suitable for product listing pages and product detail pages"
    },
    "ProductVideoAutoPlay": {
      "name": "Product video automatically plays",
      "hint": "Only effective for local videos and PC-side Youtube videos"
    },
    "ProductsNumber": {
      "name": "Product Number"
    },
    "ProductsSku": {
      "name": "Product SKUs"
    },
    "ProductsPicMagnifier": {
      "name": "Product main image magnifying glass"
    },
    "ProductsMainPicScale": {
      "name": "Product main image scale",
      "options": {
        "adapt": "Fit the picture",
        "1_1": "1:1",
        "4_3": "4:3",
        "2_3": "2:3"
      }
    },
    "ProductsAttrShowType": {
      "name": "Multi-standard display mode on PC side",
      "options": {
        "normal": "Tile",
        "select": "Drop down box"
      }
    },
    "ProductsAttrShowTypeMobile": {
      "name": "Multi-standard display mode on mobile terminal",
      "options": {
        "normal": "Tile",
        "select": "Drop down box",
        "fold": "Fold"
      }
    },
    "SwipeViewMobile": {
      "name": "Swipe left and right to view"
    },
    "ProductsAddtocartButton": {
      "name": "Add to Cart"
    },
    "ProductsBuynowButton": {
      "name": "Buy Now"
    },
    "ProductsPaypalQuickButton": {
      "name": "Paypal fast payment"
    },
    "ProductsShareFacebook": {
      "name": "Facebook"
    },
    "ProductsShareLine": {
      "name": "Line"
    },
    "ProductsShareTwitter": {
      "name": "Twitter"
    },
    "ProductsShareWhatsapp": {
      "name": "Whatsapp"
    },
    "ProductsSharePinterest": {
      "name": "Pinterest"
    },
    "ProductsShareTumblr": {
      "name": "Tumblr"
    },
    "ProductsShareLinkedin": {
      "name": "Linkedin"
    },
    "ProductsReviewStar": {
      "name": "Star"
    },
    "ProductsReviewScore": {
      "name": "Score"
    },
    "ProductsReviewQuantity": {
      "name": "Number of comments"
    },
    "ProductsFavoButton": {
      "name": "Add to Favorites button"
    },
    "ProductsInventory": {
      "name": "Products Inventory"
    },
    "PlusPurchaseMethod": {
      "name": "Plus purchase method",
      "options": {
        "popup_right": "Right popup",
        "popup_center": "Centered popup",
        "drop_down": "Pull down notification",
        "enter": "Go to the shopping cart page"
      },
      "example": "The effect after clicking the add to cart button"
    },
    "ProductsFavoQuantity": {
      "name": "Number of favorites"
    },
    "ProductsSales": {
      "name": "Sales"
    },
    "ShareFacebook": {
      "name": "Facebook",
      "example": "例:https://facebook.com/ysair"
    },
    "ShareTwitter": {
      "name": "Twitter",
      "example": "Example:https://twitter.com/ysair"
    },
    "SharePinterest": {
      "name": "Pinterest",
      "example": "Example:https://pinterest.com/ysair"
    },
    "ShareYouTube": {
      "name": "YouTube",
      "example": "Example:https://youtube.com/ysair"
    },
    "ShareGoogle": {
      "name": "Google",
      "example": "Example:https://google.com/ysair"
    },
    "ShareVK": {
      "name": "VK",
      "example": "Example:https://vk.com/ysair"
    },
    "ShareLinkedIn": {
      "name": "LinkedIn",
      "example": "Example:https://linkedin.com/ysair"
    },
    "ShareInstagram": {
      "name": "Instagram",
      "example": "Example:https://instagram.com/ysair"
    },
    "ShareTiktok": {
      "name": "Tiktok",
      "example": "Example:https://tiktok.com/ysair"
    },
    "PreLogoGray": {
      "name": "LOGO grayscale display"
    },
    "PreBackgroundColor": {
      "name": "Background"
    },
    "Logo": {
      "name": "LOGO"
    },
    "LogoWidth": {
      "name": "LOGO width on PC side"
    },
    "MobileLogoWidth": {
      "name": "Mobile logo width"
    },
    "Menu": {
      "name": "Navigation",
      "tips": "The content comes from the navigation of the store module"
    },
    "MenuStyle": {
      "name": "Navigation style",
      "tips": "Go to the <a target=\"_blank\" href=\"/manage/plugins/nav-themes/index\">Navigation Style</a> app to set it up"
    },
    "Search": {
      "name": "Search"
    },
    "SearchPlaceholder": {
      "name": "Preset prompts in the search box",
      "placeholder": "Search..."
    },
    "LanguageDisplayMethod": {
      "name": "language display",
      "options": {
        "simple": "short name",
        "full": "full name"
      }
    },
    "User": {
      "name": "Member"
    },
    "ShoppingCart": {
      "name": "Shopping cart"
    },
    "LanguageSwitch": {
      "name": "Language switch",
      "goset": "To add a language, go to the <a href=\"/manage/set/language\" target=\"_blank\">store language</a>"
    },
    "CurrencySwitch": {
      "name": "Currency switch",
      "goset": "To add a currency, go to the <a href=\"/manage/set/basis\" target=\"_blank\">basic settings</a>"
    },
    "HeaderFixedPc": {
      "name": "PC end header is fixed at the top"
    },
    "HeaderFixedMobile": {
      "name": "Mobile header is fixed at the top"
    },
    "PicPc": {
      "name": "PC side"
    },
    "PicMobile": {
      "name": "Mobile"
    },
    "HeightPc": {
      "name": "PC side height",
      "options": {
        "adapt": "Fit the picture",
        "firstpic": "Fit to the first picture",
        "fixed": "Original image height",
        "826": "826px",
        "248": "248px",
        "670": "670px",
        "240": "240px",
        "360": "360px",
        "690": "690px",
        "740": "740px",
        "757": "757px",
        "813": "813px",
        "380": "380px",
        "565": "565px",
        "600": "600px",
        "612": "612px",
        "430": "430px",
        "432": "432px",
        "440": "440px",
        "512": "512px",
        "520": "520px",
        "666": "666px",
        "650": "650px",
        "345": "345px",
        "478": "478px",
        "635": "635px",
        "645": "645px",
        "800": "800px",
        "725": "725px",
        "475": "475px",
        "1080": "1080px",
        "460": "460px",
        "610": "610px",
        "207": "207px",
        "640": "640px",
        "700": "700px",
        "575": "575px",
        "560": "560px",
        "465": "465px",
        "330": "330px",
        "316": "316px",
        "400": "400px",
        "312": "312px",
        "250": "250px",
        "170": "170px",
        "480": "480px",
        "160": "160px",
        "1100": "1100px",
        "243": "243px",
        "420": "420px",
        "270": "270px",
        "970": "970px",
        "488": "488px",
        "546": "546px",
        "660": "660px",
        "680": "680px",
        "730": "730px"
      }
    },
    "HeightMobile": {
      "name": "Mobile height",
      "options": {
        "adapt": "Fit the picture",
        "firstpic": "Fit to the first picture",
        "fixed": "Original image height",
        "230": "230px",
        "323": "323px",
        "160": "160px",
        "240": "240px",
        "248": "248px",
        "320": "320px",
        "252": "252px",
        "580": "580px",
        "565": "565px",
        "606": "606px",
        "300": "300px",
        "330": "330px",
        "420": "420px",
        "432": "432px",
        "183": "183px",
        "430": "430px",
        "482": "482px",
        "234": "234px",
        "520": "520px",
        "650": "650px",
        "478": "478px",
        "345": "345px",
        "372": "372px",
        "380": "380px",
        "284": "284px",
        "126": "126px",
        "422": "422px",
        "445": "445px",
        "273": "273px",
        "287": "287px",
        "350": "350px",
        "186": "186px",
        "152": "152px",
        "530": "530px",
        "224": "224px",
        "250": "250px",
        "71": "71px",
        "480": "480px",
        "200": "200px",
        "400": "400px",
        "305": "305px",
        "600": "600px",
        "140": "140px",
        "845": "845px",
        "412": "412px",
        "210": "210px",
        "1350": "1350px",
        "360": "360px",
        "346": "346px",
        "235": "235px",
        "205": "205px",
        "232": "232px",
        "365": "365px",
        "130": "130px",
        "1000": "1000px",
        "230": "230px",
        "290": "290px",
        "280": "280px",
        "190": "190px",
        "400": "400px",
        "260": "260px",
        "330": "330px",
        "350": "350px",
        "370": "370px",
        "420": "420px",
        "310": "310px",
        "210": "210px",
        "450": "450px",
        "300": "300px"
      }
    },
    "ScrollView": {
      "name": "Scrolling parallax"
    },
    "Pic": {
      "name": "Picture"
    },
    "PicHeight": {
      "name": "Image height"
    },
    "PosterPicHeight": {
      "name": "Image height",
      "options": {
        "firstpic": "Fit to the first picture",
        "74": "74px",
        "108": "108px",
        "232": "232px",
        "290": "290px",
        "394": "394px",
        "372": "372px",
        "585": "585px",
        "200": "200px",
        "300": "300px",
        "347": "347px",
        "350": "350px",
        "447": "447px",
        "464": "464px",
        "480": "480px",
        "418": "418px",
        "280": "280px",
        "432": "432px",
        "395": "395px",
        "820": "820px",
        "338": "338px",
        "166": "166px",
        "425": "425px",
        "620": "620px",
        "493": "493px",
        "471": "471px",
        "570": "570px",
        "340": "340px",
        "405": "405px",
        "500": "500px",
        "100": "100px",
        "260": "260px",
        "374": "374px",
        "113": "113px",
        "960": "960px",
        "592": "592px",
        "330": "330px",
        "730": "730px",
        "140": "140px",
        "700": "700px",
        "460": "460px",
        "450": "450px",
        "398": "398px",
        "560": "560px",
        "900": "900px",
        "508": "508px",
        "416": "416px",
        "170": "170px",
        "415": "415px",
        "320": "320px",
        "110": "110px",
        "440": "440px",
        "65": "65px",
        "510": "510px",
        "580": "580px",
        "520": "520px",
        "58": "58px",
        "509": "509px",
        "690": "690px",
        "380": "380px",
        "770": "770px",
        "376": "376px",
        "164": "164px",
        "490": "490px",
        "494": "494px",
        "342": "342px"
      }
    },
    "FillingMethod": {
      "name": "Filling method",
      "options": {
        "ratio": "Equal ratio",
        "tiled": "Tile"
      }
    },
    "PicDisplayArea": {
      "name": "Picture display area",
      "options": {
        "top": "Above",
        "center": "Middle",
        "bottom": "Below"
      }
    },
    "Link": {
      "name": "Link"
    },
    "Link1": {
      "name": "link 1"
    },
    "Link2": {
      "name": "link 2"
    },
    "LinkText": {
      "name": "Link copy",
      "proHint": "Click to enter the product details page"
    },
    "Title": {
      "name": "Title",
      "bannertips": "After filling in the content, the mouse can be dragged to adjust the position displayed on the advertisement map"
    },
    "TitleManage": {
      "name": "Title <span style=\"color:#7b8e9f;\">(Only visible in the background)</span>"
    },
    "TitleBak": {
      "name": "Title"
    },
    "SubTitle": {
      "name": "Subtitle",
      "bannertips": "After filling in the content, the mouse can be dragged to adjust the position displayed on the advertisement map"
    },
    "IconAlt": {
      "name": "Icon Alt",
      "tips": "Default to title content when empty"
    },
    "ImageAlt": {
      "name": "Image Alt",
      "tips": "Default to title content when empty"
    },
    "ImageAltPc": {
      "name": "Pc Image Alt",
      "tips": "Default to title content when empty"
    },
    "ImageAltMobile": {
      "name": "Mobile Image Alt",
      "tips": "Default to title content when empty"
    },
    "Content": {
      "name": "Text"
    },
    "Content1": {
      "name": "Text 1"
    },
    "Content2": {
      "name": "Text 2"
    },
    "TextPosition": {
      "name": "Text position",
      "options": {
        "lefttop": "top left",
        "top": "Directly above",
        "righttop": "Top right",
        "left": "Left",
        "center": "Middle",
        "right": "Right",
        "leftbottom": "Bottom left",
        "bottom": "Directly below",
        "rightbottom": "Bottom right"
      }
    },
    "TextPositionPc": {
      "name": "PC Text position",
      "options": {
        "lefttop": "top left",
        "top": "Directly above",
        "righttop": "Top right",
        "left": "Left",
        "center": "Middle",
        "right": "Right",
        "leftbottom": "Bottom left",
        "bottom": "Directly below",
        "rightbottom": "Bottom right"
      }
    },
    "TextPositionMobile": {
      "name": "Mobile Text position",
      "options": {
        "lefttop": "top left",
        "top": "Directly above",
        "righttop": "Top right",
        "left": "Left",
        "center": "Middle",
        "right": "Right",
        "leftbottom": "Bottom left",
        "bottom": "Directly below",
        "rightbottom": "Bottom right"
      }
    },
    "TextAlign": {
      "name": "Text alignment",
      "options": {
        "left": "Align left",
        "center": "Center align",
        "right": "Align right"
      }
    },
    "TextAlignPc": {
      "name": "PC text alignment",
      "options": {
        "left": "Align left",
        "center": "Center align",
        "right": "Align right"
      }
    },
    "TextAlignMobile": {
      "name": "Mobile text alignment",
      "options": {
        "left": "Align left",
        "center": "Center align",
        "right": "Align right"
      }
    },
    "PicAlign": {
      "name": "Image alignment",
      "options": {
        "left": "Align left",
        "center": "Center align",
        "right": "Align right"
      }
    },
    "PicAlignPc": {
      "name": "PC image alignment",
      "options": {
        "left": "Align left",
        "center": "Center align",
        "right": "Align right"
      }
    },
    "PicAlignMobile": {
      "name": "Mobile image alignment",
      "options": {
        "left": "Align left",
        "center": "Center align",
        "right": "Align right"
      }
    },
    "ContentTextAlign": {
      "name": "Content text alignment"
    },
    "TitleFont": {
      "name": "Title font"
    },
    "TitleFontSize": {
      "name": "Title size"
    },
    "TitleFontSizePc": {
      "name": "PC title size"
    },
    "TitleFontSizeMobile": {
      "name": "Mobile title size"
    },
    "TitleColor": {
      "name": "Title color"
    },
    "SubTitleFont": {
      "name": "Subtitle font"
    },
    "SubTitleFontSize": {
      "name": "Subtitle size"
    },
    "SubTitleFontSizePc": {
      "name": "PC side subtitle size"
    },
    "SubTitleFontSizeMobile": {
      "name": "Mobile subtitle size"
    },
    "SubTitleColor": {
      "name": "Subtitle color"
    },
    "ContentFontSize": {
      "name": "Body size"
    },
    "ContentFontSize1": {
      "name": "Body size 1"
    },
    "ContentFontSize2": {
      "name": "Body size 2"
    },
    "ContentFontSizePc": {
      "name": "PC side text size"
    },
    "ContentFontSizePc1": {
      "name": "PC side text size 1"
    },
    "ContentFontSizePc2": {
      "name": "PC side text size 2"
    },
    "ContentColor": {
      "name": "Body color"
    },
    "ContentColor1": {
      "name": "Body color 1"
    },
    "ContentColor2": {
      "name": "Body Color 2"
    },
    "BgColor": {
      "name": "Background color"
    },
    "BgBorderColor": {
      "name": "Background border color"
    },
    "MaskColor": {
      "name": "Overlay color"
    },
    "MaskColorOpacity": {
      "name": "Overlay Color Opacity"
    },
    "Products": {
      "name": "Product"
    },
    "ProductsName": {
      "name": "Product name"
    },
    "ProductsBrief": {
      "name": "Product brief introduction"
    },
    "ProductsPrice": {
      "name": "Current price"
    },
    "ProductsDelPrice": {
      "name": "Original price"
    },
    "ReviewStar": {
      "name": "Review stars"
    },
    "ReviewCount": {
      "name": "Number of comments"
    },
    "Share": {
      "name": "Share"
    },
    "ColorFont": {
      "name": "",
      "hint": "More fonts and colors can be modified by going to the <a href=\"javascript:;\" data-fixed-plugins=\"global-set\">global settings</a>"
    },
    "Copyright": {
      "name": "Copyright Information",
      "goset": "To add copyright information, go to <a href=\"/manage/set/basis\" target=\"_blank\">Basic Settings</a>"
    },
    "PaymentMethodIcons": {
      "name": "Payment method icons",
      "goset": "To add a payment method icon, go to <a href=\"/manage/set/payment\" target=\"_blank\">Payment Settings</a>"
    },
    "FooterMenu": {
      "name": "Bottom navigation",
      "tips": "The content comes from the bottom navigation of the store module"
    },
    "Contact": {
      "name": "Contact information",
      "tips": "The content comes from the contact information of the setting module"
    },
    "Social": {
      "name": "Official social media",
      "goset": "To add official social media, go to <a href=\"javascript:;\" data-fixed-plugins=\"global-set\">global settings</a>"
    },
    "Columns": {
      "name": "Number of columns",
      "options": {
        "2": "2",
        "3": "3",
        "4": "4",
        "5": "5",
        "6": "6"
      }
    },
    "ColumnsPc": {
      "name": "Number of columns on the PC side",
      "options": {
        "1": "1",
        "2": "2",
        "3": "3",
        "4": "4",
        "5": "5",
        "6": "6",
        "7": "7",
        "8": "8",
        "9": "9"
      }
    },
    "ColumnsMobile": {
      "name": "Number of columns on mobile",
      "options": {
        "1": "1",
        "2": "2",
        "3": "3"
      }
    },
    "Row": {
      "name": "Rows"
    },
    "RowPc": {
      "name": "Number of lines on PC"
    },
    "RowMobile": {
      "name": "Mobile line count"
    },
    "QuickButton": {
      "name": "Quick buy button"
    },
    "ButtonText": {
      "name": "Button copy",
      "proHint": "Quick purchase popup appears after clicking"
    },
    "ButtonTextColor": {
      "name": "Button text color"
    },
    "ButtonHoverTextColor": {
      "name": "Button copy mouse over color"
    },
    "ButtonColor": {
      "name": "Button color"
    },
    "ButtonHoverColor": {
      "name": "Button mouseover color"
    },
    "ButtonText1": {
      "name": "Button copy 1"
    },
    "ButtonTextColor1": {
      "name": "Button Copy Color 1"
    },
    "ButtonHoverTextColor1": {
      "name": "Button copy mouse over color 1"
    },
    "ButtonColor1": {
      "name": "Button color 1"
    },
    "ButtonHoverColor1": {
      "name": "Button mouseover color 1"
    },
    "ButtonText2": {
      "name": "Button copy 2"
    },
    "ButtonTextColor2": {
      "name": "Button Copy Color 2"
    },
    "ButtonHoverTextColor2": {
      "name": "Button copy mouse over color 2"
    },
    "ButtonColor2": {
      "name": "Button color 2"
    },
    "ButtonHoverColor2": {
      "name": "Button mouseover color 2"
    },
    "InputPlaceholder": {
      "name": "Input box prompt text"
    },
    "Newsletter": {
      "name": "E-mail Subscriptions"
    },
    "Favorites": {
      "name": "Favorites"
    },
    "Category": {
      "name": "Classification"
    },
    "ProductsCategory": {
      "name": "Product category",
      "tips": "The content comes from the All Categories module"
    },
    "LinkColor": {
      "name": "Link color"
    },
    "LinkHoverColor": {
      "name": "Link mouseover color"
    },
    "Article": {
      "name": "Content",
      "tips": "Content from custom pages"
    },
    "PageName": {
      "name": "Page name"
    },
    "ContentFontSizeMobile": {
      "name": "Mobile text size"
    },
    "ContentFontSizeMobile1": {
      "name": "Mobile text size 1"
    },
    "ContentFontSizeMobile2": {
      "name": "Mobile body size 2"
    },
    "PicPc": {
      "name": "PC picture"
    },
    "PicMobile": {
      "name": "Mobile picture"
    },
    "CardShow": {
      "name": "Show card"
    },
    "CardColor": {
      "name": "Card color"
    },
    "CardOpacity": {
      "name": "Card Opacity"
    },
    "Icon": {
      "name": "Icon"
    },
    "Gallery": {
      "name": "Buyers show",
      "tips": "Content from App Buyer Show"
    },
    "Blog": {
      "name": "Blog",
      "tips": "Content comes from the application blog"
    },
    "News": {
      "name": "News",
      "link": "Management news",
      "tips": "Content comes from App News"
    },
    "Cases": {
      "name": "Case",
      "link": "Management case",
      "tips": "Content comes from application cases"
    },

    "ShowStarReviews": {
      "name": "Show star reviews",
      "options": {
        "5": "5 star review",
        "4": "4 star review",
        "3": "3 star review",
        "2": "2 star review",
        "1": "1 star review"
      }
    },
    "Seo": {
      "name": "SEO",
      "options": {
        "VideoTitleSeo": "Video title",
        "VideoDescriptionSeo": "Video Description",
        "VideoPulishTimeSeo": "Video release time"
      },
      "split": "content-global"
    },
    "VideoCode": {
      "name": "Paste video link or mosaic video code"
    },
    "VideoAutoPlay": {
      "name": "Autoplay video",
      "hint": "Only valid for local video and PC-side Youtube video"
    },
    "VideoAutoPlayMute": {
      "name": "Whether autoplay is muted",
      "hint": "Only valid for local video and PC-side Youtube video"
    },
    "Avatar": {
      "name": "Avatar"
    },
    "Name": {
      "name": "Name"
    },
    "NameFontSize": {
      "name": "PC side name size"
    },
    "NameFontSizeMobile": {
      "name": "Mobile name size"
    },
    "NameColor": {
      "name": "Name color"
    },
    "Career": {
      "name": "Position"
    },
    "CareerFontSize": {
      "name": "PC side job size"
    },
    "CareerFontSizeMobile": {
      "name": "Mobile job size"
    },
    "CareerColor": {
      "name": "Job color"
    },
    "Brief": {
      "name": "Introduction"
    },
    "BriefFontSize": {
      "name": "PC side profile size"
    },
    "BriefFontSizeMobile": {
      "name": "Mobile profile size"
    },
    "BriefColor": {
      "name": "Profile Color"
    },
    "ArrowColor": {
      "name": "Arrow color"
    },
    "ArrowHoverColor": {
      "name": "Mouse over arrow color"
    },
    "Banner": {
      "name": "Main image"
    },
    "CoverPic": {
      "name": "PC cover image"
    },
    "CoverPicMobile": {
      "name": "Mobile cover image"
    },
    "CoverHeightPc": {
      "name": "PC side cover image height",
      "options": {
        "adapt": "Fit the picture",
        "fixed": "Original image height",
        "910": "910px",
        "915": "915px",
        "800": "800px"
      }
    },
    "CoverHeightMobile": {
      "name": "Mobile cover image height",
      "options": {
        "adapt": "Fit the picture",
        "fixed": "Original image height",
        "360": "360px",
        "565": "565px"
      }
    },
    "Effect": {
      "name": "Effect",
      "options": {
        "0": "Fade in",
        "1": "Fly left",
        "2": "Fly in",
        "3": "Horizontal advancement",
        "4": "Text rise",
        "5": "Combination cut",
        "6": "Pan Fade",
        "7": "Gradually shrink"
      }
    },
    "CostomCode": {
      "name": "custom code",
      "tips": "The parameters in the code need to be replaced with the content of the corresponding product"
    },
    "ContainerWidth": {
      "name": "Component Width",
      "options": {
        "100%": "100%",
        "80%": "80%",
        "60%": "60%",
        "40%": "40%",
        "1200": "1200px",
        "standard": "Standard",
        "full": "Full screen",
        "right_screen": "Right narrow screen"
      }
    },
    "BelongCategory": {
      "name": "Category"
    },
    "ButtonType": {
      "name": "Button type",
      "options": {
        "quick": "Quick buy",
        "details": "Check the details"
      }
    },
    "LinkType": {
      "name": "Link type",
      "options": {
        "quick": "Quick buy",
        "details": "Check the details"
      }
    },
    "QuickText": {
      "name": "Quick Buy Copy",
      "hint": "Quick purchase popup appears after clicking"
    },
    "Details": {
      "name": "View details copy",
      "hint": "Click to enter the product details page"
    },
    "GapHeightPc": {
      "name": "PC side pitch height",
      "options": {
        "0px": "0px",
        "5px": "5px",
        "10px": "10px",
        "15px": "15px",
        "20px": "20px",
        "25px": "25px",
        "30px": "30px",
        "35px": "35px",
        "40px": "40px",
        "45px": "45px",
        "50px": "50px"
      }
    },
    "GapHeightMobile": {
      "name": "Mobile terminal height",
      "options": {
        "0px": "0px",
        "5px": "5px",
        "10px": "10px",
        "15px": "15px",
        "20px": "20px",
        "25px": "25px",
        "30px": "30px",
        "35px": "35px",
        "40px": "40px",
        "45px": "45px",
        "50px": "50px"
      }
    },
    "FlipType": {
      "name": "Page flip style",
      "options": {
        "dots": "Dots",
        "line": "Line",
        "arrow": "Arrow"
      }
    },
    "CarouselInterval": {
      "name": "Carousel interval"
    },
    "CarouselAuto": {
      "name": "Automatic carousel"
    },
    "SwitchInterval": {
      "name": "Switch interval"
    },
    "SwitchAuto": {
      "name": "Automatic switch"
    },
    "UpperSpacing": {
      "name": "Spacing on PC side"
    },
    "LowerSpacing": {
      "name": "PC side lower spacing"
    },
    "UpperSpacingMobile": {
      "name": "Spacing on mobile"
    },
    "LowerSpacingMobile": {
      "name": "Mobile terminal lower spacing"
    },
    "Filter": {
      "name": "Filter",
      "link": "Manage Filtering",
      "tips": "Content comes from applying condition filtering"
    },
    "ProductsSort": {
      "name": "Product sorting"
    },
    "ProductsMainAttr": {
      "name": "Main attribute of the product"
    },
    "BackgroundBorderColor": {
      "name": "Background wireframe color"
    },
    "ProductsPriceFontSizePc": {
      "name": "PC current price font size"
    },
    "ProductsPriceFontSizeMobile": {
      "name": "Mobile terminal current price font size"
    },
    "ProductsDelPriceFontSizePc": {
      "name": "Original font size on PC"
    },
    "ProductsDelPriceFontSizeMobile": {
      "name": "Mobile original font size"
    },
    "ProductsTitleFontSizePc": {
      "name": "Product title size on PC"
    },
    "ProductsTitleFontSizeMobile": {
      "name": "Mobile product title size"
    },
    "ProductsBriefFontSizePc": {
      "name": "PC-side product brief introduction size"
    },
    "ProductsBriefFontSizeMobile": {
      "name": "Mobile product brief introduction size"
    },
    "NavFontSizePc": {
      "name": "Navigation font size"
    },
    "CountdownStart": {
      "name": "Start the countdown"
    },
    "ChooseTime": {
      "timezone": ""
    },
    "StartTime": {
      "name": "Starting time"
    },
    "EndTime": {
      "name": "End Time"
    },
    "TimeSizePc": {
      "name": "Time on PC"
    },
    "TimeSizeMobile": {
      "name": "Mobile time size"
    },
    "TimeBackgroundRadiusSize": {
      "name": "Time background rounded corner size"
    },
    "TimeColor": {
      "name": "Time color"
    },
    "TimeBackgroundColor": {
      "name": "Time background color"
    },
    "TimeBackgroundHeightPc": {
      "name": "PC side time background height"
    },
    "TimeBackgroundWidthPc": {
      "name": "PC side time background width"
    },
    "TimeBackgroundHeightMobile": {
      "name": "Mobile time background height"
    },
    "TimeBackgroundWidthMobile": {
      "name": "Mobile time background width"
    },
    "ButtonTextSizePc": {
      "name": "PC side button copy size"
    },
    "ButtonTextSizeMobile": {
      "name": "Mobile button text size"
    },
    "ButtonBgColor": {
      "name": "button background color"
    },
    "ButtonBorderColor": {
      "name": "button border color"
    },
    "ButtonBgHoverColor": {
      "name": "button background mouseover color"
    },
    "ButtonBorderHoverColor": {
      "name": "button border mouseover color"
    },
    "ButtonRadiusSize": {
      "name": "button corner size"
    },
    "ButtonHeightPc": {
      "name": "PC side button height"
    },
    "ButtonWidthPc": {
      "name": "PC side button width"
    },
    "ButtonHeightMobile": {
      "name": "mobile button height"
    },
    "ButtonWidthMobile": {
      "name": "mobile button width"
    },
    "FixedOpenPc": {
      "name": "Fixed expansion on PC"
    },
    "ProductsSalesBgStyle": {
      "name": "background style",
      "options": {
        "oval": "oval",
        "round": "round",
        "rectangle": "rectangle",
        "text": "text"
      }
    },
    "ProductsSalesTextStyle": {
      "name": "Offer text style",
      "options": {
        "number": "Discount 1 (-20%)",
        "word": "Discount 2 (20% off)"
      }
    },
    "ProductsSalesPosition": {
      "name": "placement",
      "options": {
        "lefttop": "top left",
        "righttop": "top right"
      }
    },
    "ProductsSalesBgStyleColor": {
      "name": "background style color"
    },
    "ProductsSalesTextColor": {
      "name": "Offer text color"
    },
    "QuickMenu": {
      "name": "Quick navigation",
      "link": "Manage Navigation"
    },
    "ThemesAddToCartTextColor": {
      "name": "Word"
    },
    "ThemesAddToCartTextHoverColor": {
      "name": "Text mouseover effect"
    },
    "ThemesAddToCartBgColor": {
      "name": "Background"
    },
    "ThemesAddToCartBgHoverColor": {
      "name": "background mouseover effect"
    },
    "ThemesAddToCartBorderColor": {
      "name": "Frame"
    },
    "ThemesAddToCartBorderHoverColor": {
      "name": "Border mouseover effect"
    },
    "ThemesBuyNowTextColor": {
      "name": "Word"
    },
    "ThemesBuyNowBgColor": {
      "name": "Background"
    },
    "PageStyleMobile": {
      "name": "Mobile page style",
      "options": {
        "number": "Digital page turn",
        "dropdown": "Drop down loading"
      }
    },
    "DisplayMode": {
      "name": "Display Mode",
      "options": {
        "tab": "Switch Card",
        "tiled": "Tile"
      }
    },
    "ProductImgLayoutPc": {
      "name": "PC-side product map layout",
      "options": {
        "left": "Thumbnails are displayed on the left side of the product image",
        "bottom": "Thumbnails are displayed below the product image",
        "bottom_tiled": "Thumbnails are tiled below the product image"
      }
    },
    "ProductImgLayoutMobile": {
      "name": "Mobile product map layout",
      "options": {
        "show": "Show thumbnails",
        "hide": "Hide Thumbnails"
      }
    },
    "AddButtonOrder": {
      "name": "Add-on button arrangement",
      "options": {
        "horizontal": "Horizontal",
        "vertical": "Vertical"
      }
    },
    "ProductDetailsShow": {
      "name": "Show product details"
    },
    "RecentlySearch": {
      "name": "Recently Searched",
      "goset": "Displays the five most recent keywords searched by the current customer."
    },
    "HotSearch": {
      "name": "Hot Searches",
      "goset": "Displays the top 5 searched terms."
    },
    "ContentType": {
      "name": "Content Type",
      "options": {
        "third": "Third Party Video",
        "local": "Local Video",
        "images": "Picture"
      }
    },
    "ModeType": {
      "name": "Mode Type",
      "options": {
        "blog": "Blog",
        "news": "News",
        "cases": "Cases"
      }
    },
    "LocalVideo": {
      "name": "Local Video"
    },
    "PopUpPlay": {
      "name": "Pop-up play",
      "hint": "It is recommended to turn it on when using tiktok videos"
    },
    "LoopPlay": {
      "name": "Loop",
      "hint": "Only valid for local video and PC-side Youtube video"
    },
    "ProductsNameSizePc": {
      "name": "PC-side product name size"
    },
    "ProductsNameSizeMobile": {
      "name": "Mobile product name size"
    },
    "HotBlog": {
      "name": "Popular blog",
      "split": "content-global"
    },
    "HotBlogSelect": {
      "name": "Blog",
      "link": "/manage/plugins/blog/blog-v2",
      "split": "content-global",
      "tips": "Content comes from the application blog"
    },
    "Month": {
      "name": "Month",
      "split": "content-global"
    },
    "Author": {
      "name": "Author",
      "split": "content-global"
    },
    "PublishTime": {
      "name": "Publish time",
      "split": "content-global"
    },
    "ViewCount": {
      "name": "View count",
      "split": "content-global"
    },
    "PreviousNextBlog": {
      "name": "Previous and next blog",
      "split": "content-global"
    },
    "Coupon": {
      "name": "Coupon",
      "hint": "Only supports \"Manual Drop - All\" coupons",
      "link": "/manage/sales/coupon"
    },
    "CategoryShowLeft": {
      "name": " Category",
      "split": "set-text-global"
    },
    "CategoryTitleColor": {
      "name": "Category Title Hover",
      "split": "set-text-global"
    },
    "CategoryTitleHoverColor": {
      "name": "Category Title Hover Hover",
      "split": "set-text-global"
    },
    "DateColor": {
      "name": "Date Color",
      "split": "set-other-global"
    },
    "DateBgColor": {
      "name": "Date Background Color",
      "split": "set-other-global"
    },
    "Price": {
      "name": "Price"
    },
    "QtySelect": {
      "name": "Quantity selection"
    },
    "TextContentSetMobile": {
      "name": "Mobile text is set separately"
    },
    "TitleMobile": {
      "name": "Title on mobile"
    },
    "SubTitleMobile": {
      "name": "SubTitle on mobile"
    },
    "ContentMobile": {
      "name": "Text on mobile"
    },
    "DividerColor": {
      "name": "Divider color"
    },
    "Download": {
      "name": "File",
      "tips": "Content Source Application Download Management"
    },
    "FileCategory": {
      "name": "File Category"
    },
    "FileCover": {
      "name": "File Cover"
    },
    "FilePreview": {
      "name": "File Preview",
      "hint": "Only supports pdf, txt, jpg, jpeg, png, gif, webp formats"
    },
    "IconColor": {
      "name": "Icon color",
      "split": "set-button-global"
    },
    "IconHoverColor": {
      "name": "Icon mouse over color",
      "split": "set-button-global"
    },
    "ColumnSpacingPc": {
      "name": "PC side column spacing"
    },
    "NavTextAlignPc": {
      "name": "PC navigation alignment"
    },
    "DisplayStyleMobile": {
      "name": "Mobile display style",
      "options": {
        "single": "Single line scrolling",
        "multi": "Expand multiple lines"
      }
    },
    "BriefIntroduction": {
      "name": "Brief introduction"
    },
    "BlogCover": {
      "name": "Blog cover"
    },
    "ProductImageBgColorPc": {
      "name": "PC product image background color"
    },
    "ProductImageBgColorMobile": {
      "name": "Mobile product image background color"
    }
  },
  "type_menu": {
    "header": "Top of page",
    "banner": "Main picture",
    "footer": "Footer",
    "poster": "Graphic window",
    "service": "Service",
    "special": "Special advantages",
    "about": "About us",
    "review": "Review",
    "team": "Team",
    "gallery": "Buyer Show",
    "description": "Product details",
    "products": "Product display",
    "blog": "Dynamic",
    "product_purchase": "Product purchase",
    "newsletter": "Newsletter",
    "product_list": "Product List",
    "product_description": "product description",
    "combination_purchase": "Combination purchase",
    "product_maylike": "you may also like",
    "product_reviews": "Product reviews",
    "article": "Content",
    "video": "Video",
    "customize": "Custom Code",
    "brands": "brands",
    "order_tracking": "Order Tracking",
    "other": "Other",
    "carousel": "Carousel",
    "countdown": "Countdown Product",
    "blog_list": "blog list",
    "blog_detail": "Blog Details",
    "news_list": "New List",
    "news_detail": "New Details",
    "cases_list": "Case List",
    "cases_detail": "Case Details",
    "cases_description": "Case Description",
    "coupon": "Coupon",
    "download": "Download Document",
    "download_list": "Download List"
  },
  "themes": {
    "title": "title",
    "manage_title": "Title (only visible in the background)",
    "big_title": "Headline",
    "subtitle": "subtitle",
    "buttontxt": "Button Txt",
    "link": "link",
    "pic": "image",
    "pic_ary": [ "Image1", "Image2", "Image3", "Image4" ],
    "mainpic": "Main Picture",
    "background": "Background",
    "backgroundPic": "Background image",
    "web_side": "PC",
    "mweb_side": "Mobile",
    "footnav": "Bottom navigation",
    "foot_content": "The content comes from the bottom navigation of the shop module",
    "foot_content_url": "/manage/view/footer-nav/",
    "contactbind": "contact details",
    "contact_content": "The content comes from the contact information of the setting module",
    "contact_content_url": "/manage/set/basis/#company_info",
    "share_menu": "Official Social Media",
    "share_menu_content": "Official Social Media",
    "logo": "Shop LOGO",
    "search": "Search",
    "search_title": "Preset prompts in the search box",
    "top_nav": "navigation",
    "top_nav_content": "The content comes from the navigation of the shop module",
    "top_nav_content_url": "/manage/view/nav/",
    "all_cate": "All Categories",
    "all_cate_tips": "The content comes from the All Categories module",
    "all_cate_tips_url": "/manage/view/categories/",
    "module": "Module",
    "plate": [
      "Image-text1",
      "Image-text2",
      "Image-text3",
      "Image-text4",
      "Image-text5",
      "Image-text6",
      "Image-text7",
      "Image-text8",
      "Image-text9",
      "Image-text10",
      "Image-text11",
      "Image-text12"
    ],
    "layout": "effect",
    "pic_tips": "Image Size Recommendations: %width%*%height% pixels",
    "format_pic_tips": "Image Size Recommendations: %width%*%height% pixels, %format% format",
    "facebook": "Facebook",
    "facebook_tips": "Example: https://facebook.com/ysair",
    "twitter": "Twitter",
    "twitter_tips": "Example: https://twitter.com/ysair",
    "pinterest": "Pinterest",
    "pinterest_tips": "Example: https://pinterest.com/ysair",
    "youtube": "YouTube",
    "youtube_tips": "Example: https://youtube.com/ysair",
    "google": "Google",
    "google_tips": "Example: https://google.com/ysair",
    "vk": "VK",
    "vk_tips": "Example: https://vk.com/ysair",
    "linkedin": "LinkedIn",
    "linkedin_tips": "Example: https://linkedin.com/ysair",
    "instagram": "Instagram",
    "instagram_tips": "Example: https://instagram.com/ysair",
    "banner_tip": "After filling in the content, the mouse can be dragged to adjust the position displayed on the advertising image",
    "video_link": "Paste video link or embed video code",
    "name": "Name",
    "button": "button",
    "position": "Position",
    "products_list": {
      "products_list_module": "merchandise quantity",
      "products_list_title": "Each page shows the number of products",
      "products_line_number": "Each line shows the number of products",
      "products_page_line_number": "Number of rows displayed per page",
      "num_ary": [ 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10 ]
    },
    "hide": "Hide",
    "company_profile": "Company Profile",
    "product_purchase": {
      "button_module": "Button",
      "review_module": "Comment",
      "add_to_cart": "Add to Cart",
      "buy_now": "Buy Now",
      "paypal": "PayPal",
      "share_button": "share it",
      "rating_button": "Star rating",
      "score_button": "score",
      "review_num_button": "Number of comments"
    },
    "company_profile": "Company Profile",
    "brief": "Introduction",
    "avatar": "Avatar",
    "board": "Bulletin Board",
    "no_data": "No content can be set",
    "blank_tip": "There are currently no products",
    "blank_add": "Add product",
    "blank_load": "Added",
    "pages_limit_tips": "The current page does not support this style",
    "review_star_title": "Show star reviews",
    "review_option": [
      "5 star reviews",
      "Reviews with 4 stars and above",
      "Reviews with 3 stars and above",
      "Reviews with 2 stars and above",
      "Reviews with 1 stars and above"
    ],
    "article_title": "content",
    "article_tip": "The content comes from a custom page",
    "article_tip_url": "/manage/view/page/edit?AId=",
    "review_desc_only": "Individual product reviews",
    "review_desc_all": "All product reviews",
    "gallery_tip": "The content comes from a \"Buyer Show\" app",
    "gallery_tip_url": "/manage/plugins/gallery/index/#",
    "custom_code": "Custom Code",
    "style_box": {
      "width": "Container width"
    },
    "GlobalConfig": {
      "ProAttrShow": {
        "name": "Specification attribute display method",
        "type": [ "Options", "Drop down" ]
      }
    },
    "TipsAry": {
      "customize": "The parameters in the code need to be replaced with the content of the corresponding product. Go to the <a href=\"https://help.ueeshop.com/doc/public-variable.html\" target=\"_blank\">Help Center</a> to view"
    },
    "mainpic_magnifying": "Product main image magnifying glass",
    "show_first_attr": "Display the main attributes of the product",
    "show_first_attr_tips": "Each option of the main attribute needs to be associated with the main image"
  }


}
