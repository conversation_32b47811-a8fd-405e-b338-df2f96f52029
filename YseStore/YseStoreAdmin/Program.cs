using Autofac;
using Autofac.Core;
using Autofac.Extensions.DependencyInjection;
using GZY.Quartz.MUI.Extensions;
using I18Next.Net.AspNetCore;
using I18Next.Net.Backends;
using I18Next.Net.Extensions;
using Microsoft.AspNetCore.Authentication.Cookies;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Authorization;
using Microsoft.AspNetCore.Mvc.Razor;
using Microsoft.AspNetCore.ResponseCompression;
using Mxweb.Configuration;
using Serilog;
using System.IdentityModel.Tokens.Jwt;
using System.IO.Compression;
using System.Reflection;
using System.Text;
using YseStore.Common;
using YseStore.Common.Helper;
using YseStore.Extensions;
using YseStore.Extensions.ServiceExtensions;
using YseStore.IService;
using YseStore.IService.Email.SendCloud;
using YseStore.Service;
using YseStore.Service.Email.SendCloud;
using YseStoreAdmin.Ext;
using YseStoreControls;

var builder = WebApplication.CreateBuilder(args);
// 配置 Serilog - 从配置文件读取设置
builder.Host.UseSerilog((context, configuration) =>
    configuration.ReadFrom.Configuration(context.Configuration));
var services = builder.Services;
 
builder.AddPlugins();

// 添加控制器和API Explorer服务
services.AddControllers().AddJsonOptions(options =>
{
    options.JsonSerializerOptions.PropertyNamingPolicy = null;
    
});
services.AddEndpointsApiExplorer();

// 添加HttpContextAccessor服务
services.AddHttpContextAccessor();

// 添加Session服务配置
services.AddDistributedMemoryCache(); // 使用内存缓存作为Session存储
services.AddSession(options =>
{
    options.Cookie.Name = "YseStoreAdmin"; // 设置Cookie名称
    options.IdleTimeout = TimeSpan.FromMinutes(30); // Session超时时间
    options.Cookie.HttpOnly = true; // 设置为HttpOnly以提高安全性
    options.Cookie.IsEssential = true; // 标记为必要Cookie以符合GDPR要求
});

services.RegisterMxwebComponents();
services.AddResponseCompression(options => options.EnableForHttps = true);
services.Configure<BrotliCompressionProviderOptions>(options => options.Level = CompressionLevel.SmallestSize);
services.Configure<GzipCompressionProviderOptions>(options => options.Level = CompressionLevel.SmallestSize);
services.AddAuthentication(CookieAuthenticationDefaults.AuthenticationScheme)
.AddCookie(options =>
{
    options.Cookie.Name= "YseStoreAdminAuth"; // 设置Cookie名称
    options.LoginPath = "/Account/Login";
    options.AccessDeniedPath = "/Error/AccessDenied";
});

services.AddAuthorization(options =>
{
    options.AddPolicy("AdminManager", policy =>
        policy.RequireRole("Admin", "Manager", "Editor"));
});
services.AddControllersWithViews(options =>
{
    options.Filters.Add(new AuthorizeFilter("AdminManager"));
}).AddJsonOptions(options =>
{
    options.JsonSerializerOptions.PropertyNamingPolicy = null;
    options.JsonSerializerOptions.WriteIndented = true;
});
services.AddRazorPages(options =>
{

    // 应用全局授权筛选器
    options.Conventions.AllowAnonymousToFolder("/assets");
    options.Conventions.AllowAnonymousToFolder("/css");
    options.Conventions.AllowAnonymousToFolder("/businessJs");
    options.Conventions.AllowAnonymousToFolder("/template");
    options.Conventions.AllowAnonymousToPage("/Account/Login");
    options.Conventions.AuthorizeFolder("/", "AdminManager");
    options.Conventions.ConfigureFilter(new IgnoreAntiforgeryTokenAttribute());
});
//.AddRazorOptions(options =>
//{
//    // 添加自定义视图位置
//    options.PageViewLocationFormats.Add("/Pages/StoreVisual/{0}" + RazorViewEngine.ViewExtension); 
//});
//services.AddScoped<ISendCloudServices, SendCloudServices>();

#region 配置

// 添加Swagger服务
//services.AddSwaggerServices("YseStoreAdmin API", "v1", "YseStoreAdmin API接口文档");

// 配置日志输出
//builder.Logging.AddConsole();
//builder.Logging.AddDebug();
//builder.Logging.SetMinimumLevel(LogLevel.Debug);

// 配置host与容器
builder.Host
    .UseServiceProviderFactory(new AutofacServiceProviderFactory())
    .ConfigureContainer<ContainerBuilder>(builder =>
    {
        builder.RegisterModule(new AutofacModuleRegister());
        builder.RegisterModule<AutofacPropertityModuleReg>();
    })
    .ConfigureAppConfiguration((hostingContext, config) =>
    {
        hostingContext.Configuration.ConfigureApplication();
        config.Sources.Clear();
        config.AddJsonFile("appsettings.json", optional: true, reloadOnChange: false);
    });
builder.ConfigureApplication();


// 2、配置服务
builder.Services.AddSingleton(new AppSettings(builder.Configuration));
builder.Services.AddAllOptionRegister();

//builder.Services.AddUiFilesZipSetup(builder.Environment);

Permissions.IsUseIds4 = AppSettings.app(new string[] { "Startup", "IdentityServer4", "Enabled" }).ObjToBool();
Permissions.IsUseAuthing = AppSettings.app(new string[] { "Startup", "Authing", "Enabled" }).ObjToBool();
RoutePrefix.Name = AppSettings.app(new string[] { "AppSettings", "SvcName" }).ObjToString();

JwtSecurityTokenHandler.DefaultInboundClaimTypeMap.Clear();
builder.Services.AddSingleton(new AppSettings(builder.Configuration));
builder.Services.AddCacheSetup();
builder.Services.AddSqlsugarSetup();

// 注册应用服务
builder.Services.AddApplicationServices();


// 指定语言资源文件位于 "wwwroot/locales" 目录 设置默认语言为英语 ("en")
services.AddI18NextLocalization(i18N =>
    i18N.IntegrateToAspNetCore().AddBackend(new JsonFileBackend("Lang/locales")).UseDefaultLanguage("cn"));

//quartz
services.RegisterQuartz(AppSettings.app(new string[] { "Quartz", "Connection" }).ObjToString());
//注入
services.AddScoped<Toast>();
services.AddScoped<IViewRenderService, ViewRenderService>();
// 添加应用服务
builder.Services.AddApplicationServices();
#endregion

var app = builder.Build();
// 确保在使用Session之前调用UseSession()
app.UseSession(); // 添加Session中间件

//Configure the HTTP request pipeline.
//if (!app.Environment.IsDevelopment())
//{
//    app.UseExceptionHandler("/Home/Error");
//}
//else
//{
//    // 在开发环境显示更详细的错误
//    app.UseDeveloperExceptionPage();

//    // 在开发环境中启用Swagger
//   app.UseSwaggerMiddleware("YseStoreAdmin API", "v1");
//}

//添加全局异常处理中间件
//app.Use(async (context, next) =>
//{
//    try
//    {
//        await next();
//    }
//    catch (Exception ex)
//    {
//        var logger = app.Services.GetRequiredService<ILogger<Program>>();
//        logger.LogError(ex, "处理请求时发生未处理的异常");

//        if (!context.Response.HasStarted)
//        {
//            context.Response.StatusCode = 500;
//            context.Response.ContentType = "text/html";
//            await context.Response.WriteAsync("<h1>服务器内部错误</h1><p>请稍后再试</p>");
//        }
//    }
//});
//app.InitializeDatabase();
if (!app.Environment.IsDevelopment())
{
  //  app.UseExceptionHandler("/Error");
   
    //app.Use(async (context, next) => {
    //    context.Request.EnableBuffering();
    //    // Leave the body open so the next middleware can read it.
    //    using (var reader = new StreamReader(
    //        context.Request.Body,
    //        encoding: Encoding.UTF8,
    //        detectEncodingFromByteOrderMarks: false,
    //        bufferSize: 1000000,
    //        leaveOpen: true))
    //    {
    //        var body = await reader.ReadToEndAsync();

    //        // Do some processing with body…

    //        // Reset the request body stream position so the next middleware can read it
    //        context.Request.Body.Position = 0;
    //    }
    //    //You could find the contentlength is null
    //    var re2 = context.Request.ContentLength;
    //    await next();
    //});
}
//else
//{
//    // 在生产环境中也启用Swagger，但可能需要额外的安全措施
//    //  app.UseSwaggerMiddleware("YseStoreAdmin API", "v1");
//}
app.UseResponseCompression();
//app.UseHttpsRedirection();

//app.UseMultiLanguageSupport(app.Services, new string[] { "cn", "en" });
var cacheMaxAgeOneWeek = (60 * 60 * 24 * 7).ToString();
app.UseStaticFiles(new StaticFileOptions
{
    OnPrepareResponse = ctx =>
    {
        ctx.Context.Response.Headers.Append(
             "Cache-Control", $"public, max-age={cacheMaxAgeOneWeek}");
    }
});
app.UseRouting();

 
app.UseAuthentication();
app.UseAuthorization();

// 添加控制器路由映射
app.MapControllers();
app.UsePlugins();
app.MapRazorPages();

app.UseMxweb(builder.Environment, app.Services, new string[] { "cn", "en", "fr", "it", "jp", "ru","de","es","pt" });
app.UseMxweb_YseStoreControls(builder.Environment);
app.UseQuartz();

try
{
    Log.Information("Starting web application");
    app.Run();
}
catch (Exception ex)
{
    Log.Fatal(ex, "Application terminated unexpectedly");
}
finally
{
    Log.CloseAndFlush();
}
